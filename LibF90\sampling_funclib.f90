  module sampling_funclib

  use kinddefs,           only : dp
  use lmpi,               only : lmpi_master, lmpi_bcast, lmpi_die, lmpi_id,   &
                                 lmpi_nproc,               lmpi_allgather,     &
                                 lmpi_reduce, lmpi_conditional_stop
  use info_depr,          only : skeleton
  use sampling_types,     only : sample_type
  use sampling_headers,   only : edge_count_master, edge_master, q_master      &
                               , data_p
  use geometry_utils, only :                                                   &
                          line_plane_point2, get_area_normal                   &
                        , triangle_bary, is_inside_triangle, line_face_int_tri &
                        , line_face_int_quad, tet_volume                       &
                        , get_sphere_radius

  implicit none

  private

  public :: is_inside_quad
  public :: interior_circle
  public :: is_quad_flat
  public :: is_opposite
  public :: is_tri_in_window
  public :: plane_line
  public :: sphere_line
  public :: tet_vol_value
  public :: tet_edge_cut_map
  public :: hex_edge_cut_map
  public :: pyr_edge_cut_map
  public :: prz_edge_cut_map
  public :: intersection
  public :: hex_face_parameter
  public :: split_hex_edge_map
  public :: split_pyr_edge_map
  public :: split_prz_edge_map

  public :: hex_4edge_sort
  public :: prz_4edge_sort
  public :: elem_5edge_sort
  public :: elem_6edge_sort

  public ::  getNodeNumberTri
  public ::  getNodeNumberQuad

  public :: analyse_volume_statistics
  public :: analyse_mesh_statistics
  public :: collect_edges
  public :: e2nt, e2nq
  public :: edge_stats
  public :: edge_zero_parameter
  public :: face_edge_cuts
  public :: face_gradrho
  public :: face_stats
  public :: find_closet_element
  public :: get_avg_data
  public :: get_span
  public :: get_window_index
  public :: hex_vol_value
  public :: line_survey_blanking
  public :: isInsideBlock
  public :: is_face_here_or_there
  public :: is_inside_box_status
  public :: is_inside_element
  public :: is_inside_cylinder
  public :: is_inside_triangle2
  public :: iso_stats
  public :: iso_crinkle
  public :: iso_nodal_values
  public :: get_edge_vector
  public :: intersects_cyl
  public :: intersects_cone
  public :: triangle_stats
  public :: triangle_area
  public :: triangle_area_method2
  public :: triangle_area_method3
  public :: rotate
  public :: stats
  public :: vmag
  public :: getMass
  public :: cell2face
  public :: det_a
  public :: trace_a
  public :: make_a
  public :: stddev
  public :: get_taul


    integer, parameter, dimension ( 3, 2 ) :: e2nt =                           &
    reshape((/ 1, 2, 3,                                                        &
               2, 3, 1 /),(/3, 2/))

    integer, parameter, dimension ( 4, 2 ) :: e2nq =                           &
    reshape((/ 1, 2, 3, 4,                                                     &
               2, 3, 4, 1 /),(/4, 2/))

    real(dp), parameter :: gamma_argon = 1.667_dp
    real(dp), parameter :: mol_weight  = 39.848_dp    ! Argon
    real(dp), parameter :: k_boltzman  = 1.38e-23_dp  ! [J/K]
    real(dp), parameter :: avogadro    = 6.022e+26_dp ! [molecules/kg*mole]

contains

!-------------------------------CONNECT_EDGES --------------------------------80
!
!-----------------------------------------------------------------------------80
  subroutine collect_edges( total_survey_edges )

    integer,                   intent(in) :: total_survey_edges

    integer                               :: edge

  continue

       edge_count_master = 0

       do edge = 1, total_survey_edges
           edge_count_master             = edge_count_master + 1
           edge_master(edge)             = edge_count_master
           q_master(:,edge_count_master) = data_p(:,edge)
       enddo

  end subroutine collect_edges

!============================ IS_TRI_IN_WINDOW ===============================80
!
!   Determine if the triangle is within the defined quadrilateral/circle
!
!=============================================================================80

  subroutine is_tri_in_window( xyz_dim, x, y, z                                &
                             , c2n                                             &
                             , cut_edge_map                                    &
                             , type_cell                                       &
                             , edge_per_cell                                   &
                             , geo_name                                        &
                             , geom_def                                        &
                             , inside                                          &
                              )

    use element_defs,    only : local_e2n_tet, local_e2n_hex, local_e2n_prz,   &
                                local_e2n_pyr

    integer ,                      intent(in ) :: xyz_dim
    real(dp), dimension(xyz_dim),  intent(in ) :: x,y,z
    integer , dimension(:),        intent(in ) :: c2n
    integer , dimension(:),        intent(in ) :: cut_edge_map
    character(len= 3),             intent(in ) :: type_cell
    integer ,                      intent(in ) :: edge_per_cell
    character(len=80),             intent(in ) :: geo_name
    type(sample_type),             intent(in ) :: geom_def
    logical ,                      intent(out) :: inside

    integer :: node, edge, node1, node2
    integer :: cut_edges
    integer :: inside_edges

    real(dp)                 :: weight1, weight2
    real(dp), dimension(3  ) :: p1, p2, p3, p4, po
    real(dp), dimension(3  ) :: pa, pb, pc
    real(dp)                 :: r0

  continue

    node1 = 0
    node2 = 0

    po(:) = geom_def%po(:)
    p1(:) = geom_def%p1(:)
    p2(:) = geom_def%p2(:)
    p3(:) = geom_def%p3(:)
    p4(:) = geom_def%p4(:)
    r0    = geom_def%r0

    node         = 0
    edge         = 0
    inside_edges = 0
    cut_edges    = sum(cut_edge_map)
    inside       = .false.
    increment_edges : do edge = 1, edge_per_cell
      if (1==cut_edge_map(edge)) then
        node = node + 1
        if ( type_cell == 'tet') then
          node1 = local_e2n_tet(edge,1) ! node number within cell
          node2 = local_e2n_tet(edge,2)
        else if ( type_cell == 'hex') then
          node1 = local_e2n_hex(edge,1) ! node number within cell
          node2 = local_e2n_hex(edge,2)
        else if ( type_cell == 'prz') then
          node1 = local_e2n_prz(edge,1) ! node number within cell
          node2 = local_e2n_prz(edge,2)
        else if ( type_cell == 'pyr') then
          node1 = local_e2n_pyr(edge,1) ! node number within cell
          node2 = local_e2n_pyr(edge,2)
        endif

        pa(1) = x(c2n(node1))
        pa(2) = y(c2n(node1))
        pa(3) = z(c2n(node1))

        pb(1) = x(c2n(node2))
        pb(2) = y(c2n(node2))
        pb(3) = z(c2n(node2))

        call edge_zero_parameter( geo_name, geom_def, weight1, weight2, pa, pb )

        pc(1) = weight1*pa(1) + weight2*pb(1)      ! Location of vertices
        pc(2) = weight1*pa(2) + weight2*pb(2)      ! that make up cut
        pc(3) = weight1*pa(3) + weight2*pb(3)      ! triangle

        select case ( geo_name )
        case ( 'quad' )
          inside = is_inside_quad ( pc, p1, p2, p3, p4 )
        case ( 'circle' )
          call interior_circle(r0, po, pc, inside)
        end select
        if  ( inside ) inside_edges = inside_edges + 1
      end if
    end do increment_edges
! This will flag is *all* of the cut edges are inside the window
    if ( (inside_edges >= 2 ) .and. (cut_edges /= 0) ) then
      inside = .true.
    else
      inside = .false.
    endif

  end subroutine is_tri_in_window

!============================= INTERIOR_CIRCLE ===============================80
!
!  Determine the soto/uchi status of node w/rt to a circular cut in a
!  plane
!
!=============================================================================80

  subroutine interior_circle(r0, po, pa, inside)

    real(dp),               intent(in   ) :: r0
    real(dp), dimension(3), intent(in   ) :: po
    real(dp), dimension(3), intent(in   ) :: pa
    logical,                intent(  out) :: inside

    real(dp)               :: ra

  continue

    inside = .false.

    ra = sqrt( (po(1)-pa(1))**2 + (po(2)-pa(2))**2 + (po(3)-pa(3))**2 )

!   point is interior to defined circle
    if ( ra <= r0 ) inside = .true.

  end subroutine interior_circle


!=================================== TRIANGLE_STATS ==========================80
!
! Determine the interpolation donor nodes and weights within the partition
! for each node of a cut triangle
!
!=============================================================================80

  subroutine triangle_stats( cut_edge_map, cell, edge_per_cell,                &
                             geo_name, geom_def,                               &
                             x, y, z, c2n, c2e,                                &
                             n_tria, l2g,                                      &
                             local_e2n, local_trinode_map, n_edges, e2ln,      &
                             edge_donor_list, edge_donor_weight )

    use allocations,     only : my_realloc_ptr

    integer,  dimension(:),     intent(in   ) :: cut_edge_map
    integer,                    intent(in   ) :: cell
    integer,                    intent(in   ) :: edge_per_cell
    character(len=80),          intent(in   ) :: geo_name
    type(sample_type),          intent(in   ) :: geom_def
    real(dp), dimension(:  ),   intent(in   ) :: x,y,z
    integer,  dimension(:,:),   intent(in   ) :: c2n
    integer,  dimension(:,:),   intent(in   ) :: c2e
    integer,  dimension(:),     intent(in   ) :: l2g
    integer,                    intent(inout) :: n_tria

    integer, dimension(edge_per_cell,2), intent(in) :: local_e2n
    integer,                    intent(inout) :: n_edges
    integer,  dimension(:),     intent(inout) :: e2ln
    integer,  dimension(:,:),   pointer       :: local_trinode_map
    integer,  dimension(:,:),   intent(inout) :: edge_donor_list
    real(dp), dimension(:),     intent(inout) :: edge_donor_weight

    integer                                   :: node, edge, node1, node2
    integer                                   :: test_dim, new_dim

    real(dp)                                  :: weight1, weight2
    real(dp), dimension(3)                    :: pa, pb

  continue

    node1 = 0
    node2 = 0

    n_tria = n_tria + 1

!   test to make sure we have enough memory for all triangles
!   if not, bump it up by 25%

    test_dim  = size(local_trinode_map,2)

    if ( n_tria > test_dim ) then

      new_dim = test_dim*1.25_dp

      call my_realloc_ptr(local_trinode_map, 4, new_dim)

    end if

    node = 0

    fill_triangle_nodes : do edge = 1, edge_per_cell

      edge_is_cut:  if (1==cut_edge_map(edge)) then

        node  = node + 1
        node1 = c2n(local_e2n(edge,1),cell)   ! parition node number
        node2 = c2n(local_e2n(edge,2),cell)

        pa(1) = x(node1)
        pa(2) = y(node1)
        pa(3) = z(node1)

        pb(1) = x(node2)
        pb(2) = y(node2)
        pb(3) = z(node2)

        call edge_zero_parameter( geo_name, geom_def, weight1, weight2, pa, pb )

        if ( e2ln(c2e(edge,cell)) == 0 ) then
          n_edges                    = n_edges + 1
          local_trinode_map(node,n_tria)   = n_edges
          e2ln(c2e(edge,cell))       = n_edges

          if ( l2g(node1) < l2g(node2) ) then
          edge_donor_weight(n_edges)     = weight1
          edge_donor_list  (1,n_edges)   = node1
          edge_donor_list  (2,n_edges)   = node2
          else
          edge_donor_weight(n_edges)     = 1.0_dp - weight1
          edge_donor_list  (1,n_edges)   = node2
          edge_donor_list  (2,n_edges)   = node1
          endif

          if ( weight1 <= 0.5_dp  ) then
          edge_donor_weight(n_edges)     = weight1
          edge_donor_list  (1,n_edges)   = node1
          edge_donor_list  (2,n_edges)   = node2
          else
          edge_donor_weight(n_edges)     = 1.0_dp - weight1
          edge_donor_list  (1,n_edges)   = node2
          edge_donor_list  (2,n_edges)   = node1
          endif
          if ( n_edges > size(edge_donor_list) ) then
            write(*,*) 'n_edges greater than edge_donor_list dimension'
            write(*,*) n_edges, size(edge_donor_list)
            call lmpi_conditional_stop(1, &
              'sampling_funclib::triangle_stats')
          endif
          if ( c2e(edge,cell) > size(e2ln) ) then
            write(*,*) 'c2e too big', c2e(edge,cell), edge, cell
            call lmpi_conditional_stop(1, &
              'sampling_funclib::triangle_stats')
          endif
        else
          local_trinode_map(node,n_tria)  = e2ln(c2e(edge,cell))
        endif

      end if edge_is_cut

    end do fill_triangle_nodes

    local_trinode_map(4,n_tria)   = local_trinode_map(3,n_tria)

  end subroutine triangle_stats

!========================== HEX_4EDGE_SORT ===================================80
!
! Calculate an untangled set of 2 triangles for a set of 4 points
!
!=============================================================================80

  subroutine prz_4edge_sort( cut_edge_map,edge_per_cell, geo_name, geom_def    &
                           , x, y, z, c2n                                      &
                           , cut_edge_map1, cut_edge_map2                      &
                           )

    use element_defs,    only : local_e2n_prz

    integer,  dimension(:),             intent(in   ) :: cut_edge_map
    integer,                            intent(in   ) :: edge_per_cell
    character(len=80),                  intent(in   ) :: geo_name
    type(sample_type),                  intent(in   ) :: geom_def
    real(dp), dimension(:  ),           intent(in   ) :: x,y,z
    integer,  dimension(:),             intent(in   ) :: c2n
    integer,  dimension(edge_per_cell), intent(out  ) :: cut_edge_map1
    integer,  dimension(edge_per_cell), intent(out  ) :: cut_edge_map2

    integer                            :: node, node1, node2
    integer                            :: edge

    real(dp)                           :: weight1, weight2
    real(dp), dimension(3)             :: pa, pb, pc
    real(dp), dimension(4,3)           :: p
    integer,  dimension(4)             :: n2e
    real(dp), dimension(4,4)           :: p2p_dist
    integer                            :: n1, n2
    integer, dimension(1)              :: p1, p2, p3, p4
    integer                            :: i1, i2, i3, i4
    real(dp)                           :: min_dist

  continue

    node1         = 0
    node2         = 0
    node          = 0
    pc            = 0.0_dp
    cut_edge_map1 = 0
    cut_edge_map2 = 0
    p1(:)         = 0
    p2(:)         = 0
    p3(:)         = 0
    p4(:)         = 0

! increment through all the edges and find the points of the cuts

    edge_loop:  do edge = 1, edge_per_cell

      edge_is_cut:  if (1==cut_edge_map(edge)) then

        node  = node + 1
        n2e(node) = edge

        node1 = local_e2n_prz(edge,1)      ! node number within cell
        node2 = local_e2n_prz(edge,2)

        pa(1) = x(c2n(node1)); pa(2) = y(c2n(node1)); pa(3) = z(c2n(node1))
        pb(1) = x(c2n(node2)); pb(2) = y(c2n(node2)); pb(3) = z(c2n(node2))

        call edge_zero_parameter( geo_name, geom_def, weight1, weight2, pa, pb )

        p(node,1:3) = pa(1:3)*weight1 + pb(1:3)*weight2
        pc(1:3)     = pc(1:3) + p(node,1:3) / 5.0_dp

      end if edge_is_cut

    end do edge_loop

! calculate the distance between all the points

    p2p_dist      = huge(1.0_dp)
    do n1 = 1, 4
      do n2 = 1, 4
        if ( n1 /= n2 ) p2p_dist(n1,n2) = dist(p(n1,:),p(n2,:))
      enddo
    enddo

    min_dist = huge(1.0_dp)
    i1 = 1
    do i2 = 1, 4
      if ( i2 == i1 ) cycle
        do i3 = 1, 4
          if ( i3==i1 .or. i3 == i2 ) cycle
          do i4 = 1, 4
            if ( i4 ==i1 .or. i4 == i2 .or. i4==i3 ) cycle
            if ( total_dist4( p2p_dist, i1, i2, i3, i4 ) < min_dist ) then
              min_dist = total_dist4( p2p_dist, i1, i2, i3, i4 )
              p1(1) = i1
              p2(1) = i2
              p3(1) = i3
              p4(1) = i4
            endif
          enddo
        enddo
    enddo

    cut_edge_map1(n2e(p1(1))) = 1
    cut_edge_map1(n2e(p2(1))) = 1
    cut_edge_map1(n2e(p3(1))) = 1

    cut_edge_map2(n2e(p1(1))) = 1
    cut_edge_map2(n2e(p3(1))) = 1
    cut_edge_map2(n2e(p4(1))) = 1

  end subroutine prz_4edge_sort

!========================== HEX_4EDGE_SORT ===================================80
!
! Calculate an untangled set of 2 triangles for a set of 4 points
!
!=============================================================================80

  subroutine hex_4edge_sort( cut_edge_map,edge_per_cell, geo_name, geom_def    &
                           , x, y, z, c2n                                      &
                           , cut_edge_map1, cut_edge_map2                      &
                           )

    use element_defs,    only : local_e2n_hex

    integer,  dimension(:),             intent(in   ) :: cut_edge_map
    integer,                            intent(in   ) :: edge_per_cell
    character(len=80),                  intent(in   ) :: geo_name
    type(sample_type),                  intent(in   ) :: geom_def
    real(dp), dimension(:  ),           intent(in   ) :: x,y,z
    integer,  dimension(:),             intent(in   ) :: c2n
    integer,  dimension(edge_per_cell), intent(out  ) :: cut_edge_map1
    integer,  dimension(edge_per_cell), intent(out  ) :: cut_edge_map2

    integer                            :: node, node1, node2
    integer                            :: edge

    real(dp)                           :: weight1, weight2
    real(dp), dimension(3)             :: pa, pb, pc
    real(dp), dimension(4,3)           :: p
    integer,  dimension(4)             :: n2e
    real(dp), dimension(4,4)           :: p2p_dist
    integer                            :: n1, n2
    integer, dimension(1)              :: p1, p2, p3, p4
    integer                            :: i1, i2, i3, i4
    real(dp)                           :: min_dist

  continue

    node1         = 0
    node2         = 0
    node          = 0
    pc            = 0.0_dp
    cut_edge_map1 = 0
    cut_edge_map2 = 0
    p1(:)         = 0
    p2(:)         = 0
    p3(:)         = 0
    p4(:)         = 0

! increment through all the edges and find the points of the cuts

    edge_loop:  do edge = 1, edge_per_cell

      edge_is_cut:  if (1==cut_edge_map(edge)) then

        node  = node + 1
        n2e(node) = edge

        node1 = local_e2n_hex(edge,1)      ! node number within cell
        node2 = local_e2n_hex(edge,2)

        pa(1) = x(c2n(node1)); pa(2) = y(c2n(node1)); pa(3) = z(c2n(node1))
        pb(1) = x(c2n(node2)); pb(2) = y(c2n(node2)); pb(3) = z(c2n(node2))

        call edge_zero_parameter( geo_name, geom_def, weight1, weight2, pa, pb )

        p(node,1:3) = pa(1:3)*weight1 + pb(1:3)*weight2
        pc(1:3)     = pc(1:3) + p(node,1:3) / 5.0_dp

      end if edge_is_cut

    end do edge_loop

! calculate the distance between all the points

    p2p_dist      = huge(1.0_dp)
    do n1 = 1, 4
      do n2 = 1, 4
        if ( n1 /= n2 ) p2p_dist(n1,n2) = dist(p(n1,:),p(n2,:))
      enddo
    enddo

    min_dist = huge(1.0_dp)
    i1 = 1
    do i2 = 1, 4
      if ( i2 == i1 ) cycle
        do i3 = 1, 4
          if ( i3==i1 .or. i3 == i2 ) cycle
          do i4 = 1, 4
            if ( i4 ==i1 .or. i4 == i2 .or. i4==i3 ) cycle
            if ( total_dist4( p2p_dist, i1, i2, i3, i4 ) < min_dist ) then
              min_dist = total_dist4( p2p_dist, i1, i2, i3, i4 )
              p1(1) = i1
              p2(1) = i2
              p3(1) = i3
              p4(1) = i4
            endif
          enddo
        enddo
    enddo

    cut_edge_map1(n2e(p1(1))) = 1
    cut_edge_map1(n2e(p2(1))) = 1
    cut_edge_map1(n2e(p3(1))) = 1

    cut_edge_map2(n2e(p1(1))) = 1
    cut_edge_map2(n2e(p3(1))) = 1
    cut_edge_map2(n2e(p4(1))) = 1

  end subroutine hex_4edge_sort

!========================== TOTAL_DIST =======================================80
!
! Return total distance between 5 points
!
!=============================================================================80

  function total_dist4( dist, i1, i2, i3, i4 )

    real(dp)                             :: total_dist4
    real(dp), dimension(4,4), intent(in) :: dist
    integer,                  intent(in) :: i1, i2, i3, i4

  continue

    total_dist4 =       &
          dist(i1,i2)   &
        + dist(i2,i3)   &
        + dist(i3,i4)   &
        + dist(i4,i1)

  end function total_dist4


!========================== TOTAL_DIST =======================================80
!
! Return total distance between 5 points
!
!=============================================================================80

  function total_dist( dist, i1, i2, i3, i4, i5 )

    real(dp)                             :: total_dist
    real(dp), dimension(5,5), intent(in) :: dist
    integer,                  intent(in) :: i1, i2, i3, i4, i5

  continue

    total_dist =       &
          dist(i1,i2)  &
        + dist(i2,i3)  &
        + dist(i3,i4)  &
        + dist(i4,i5)  &
        + dist(i5,i1)

  end function total_dist


!========================== TOTAL_DIST6 ======================================80
!
! Return total distance between 6 points
!
!=============================================================================80

  function total_dist6( dist, i1, i2, i3, i4, i5, i6 )

    real(dp)                             :: total_dist6
    real(dp), dimension(6,6), intent(in) :: dist
    integer,                  intent(in) :: i1, i2, i3, i4, i5, i6

  continue

    total_dist6 =       &
          dist(i1,i2)   &
        + dist(i2,i3)   &
        + dist(i3,i4)   &
        + dist(i4,i5)   &
        + dist(i5,i6)   &
        + dist(i6,i1)

  end function total_dist6

!================================ DIST  ======================================80
!
!  Calcuate distance betweens two points
!
!=============================================================================80

  pure function dist( p1, p2 )

    real(dp)                           :: dist
    real(dp), dimension(3), intent(in) :: p1
    real(dp), dimension(3), intent(in) :: p2

  continue

    dist = sqrt( (p1(1)-p2(1))**2 + (p1(2)-p2(2))**2 + (p1(3)-p2(3))**2)

  end function dist


!================================ EDGE_IS_CUT ================================80
!
!  Function to determine whether an edge is cut
!
!=============================================================================80

  pure function edge_is_cut(a,b)

    logical :: edge_is_cut

    real(dp), intent(in) :: a, b
    real(dp), parameter  :: zero = 0.0_dp

  continue

    edge_is_cut = ( (a>zero .and. b<=zero) .or. (b>zero .and. a<=zero) )

  end function edge_is_cut


!=============================== FACE_EDGE_CUTS ==============================80
!
!  Determine the cut edges for a boundary face element
!
!=============================================================================80

  pure function face_edge_cuts(n_nodes, local_e2n, nodal_values )              &
                          result (cut_edges)

    integer,  dimension(2)                      :: cut_edges

    integer,                        intent(in)  :: n_nodes
    integer,  dimension(n_nodes,2), intent(in)  :: local_e2n
    real(dp), dimension(4),         intent(in)  :: nodal_values

    integer                                     :: edge, n_edge

  continue

    cut_edges = 0
      n_edge  = 1

    do edge = 1, n_nodes
      if ( edge_is_cut(nodal_values(local_e2n(edge,1)),    &
                       nodal_values(local_e2n(edge,2)))) then
        cut_edges(n_edge) = edge
        n_edge = n_edge + 1
      endif
    end do

  end function face_edge_cuts

!================================ TET_EDGE_CUT_MAP ===========================80
!
!  Determine the edge-cut map for a tet
!
!=============================================================================80

  subroutine tet_edge_cut_map(nodal_values, cut_edge_map)

    use element_defs,    only : local_e2n_tet

    real(dp), dimension(4), intent(in)  :: nodal_values

    integer,  dimension(6), intent(out) :: cut_edge_map

    integer :: edge

  continue

    cut_edge_map = 0

    do edge = 1, 6
      if ( edge_is_cut( nodal_values(local_e2n_tet(edge,1)),                   &
                        nodal_values(local_e2n_tet(edge,2)) ) )                &
        cut_edge_map(edge) = 1
    end do

  end subroutine tet_edge_cut_map


!================================ HEX_EDGE_CUT_MAP ===========================80
!
!  Determine an edge-cut map for a hex
!
!=============================================================================80

  subroutine hex_edge_cut_map(nodal_values, cut_edge_map)

    use element_defs,    only : local_e2n_hex

    real(dp), dimension(8), intent(in)  :: nodal_values

    integer,  dimension(12), intent(out) :: cut_edge_map

    integer :: edge

  continue

    cut_edge_map = 0

    do edge = 1, 12
      if ( edge_is_cut( nodal_values(local_e2n_hex(edge,1)),                   &
                        nodal_values(local_e2n_hex(edge,2)) ) )                &
        cut_edge_map(edge) = 1
    end do

  end subroutine hex_edge_cut_map


!================================ PRZ_EDGE_CUT_MAP ===========================80
!
!  Determine an edge-cut map for a prism
!
!=============================================================================80

  subroutine prz_edge_cut_map(nodal_values, cut_edge_map)

    use element_defs,    only : local_e2n_prz

    real(dp), dimension(6), intent(in) :: nodal_values

    integer,  dimension(9), intent(out) :: cut_edge_map

    integer :: edge

  continue

    cut_edge_map = 0

    do edge = 1, 9
      if ( edge_is_cut( nodal_values(local_e2n_prz(edge,1)),                   &
                        nodal_values(local_e2n_prz(edge,2)) ) )                &
        cut_edge_map(edge) = 1
    end do

  end subroutine prz_edge_cut_map


!================================ PYR_EDGE_CUT_MAP ===========================80
!
!  Determine the edge-cut map for a pyramid
!
!=============================================================================80

  subroutine pyr_edge_cut_map(nodal_values, cut_edge_map)

    use element_defs,    only : local_e2n_pyr

    real(dp), dimension(5), intent(in)  :: nodal_values

    integer,  dimension(8), intent(out) :: cut_edge_map

    integer :: edge

  continue

    cut_edge_map = 0

    do edge = 1, 8
      if ( edge_is_cut( nodal_values(local_e2n_pyr(edge,1)),                   &
                        nodal_values(local_e2n_pyr(edge,2)) ) )                &
        cut_edge_map(edge) = 1
    end do

  end subroutine pyr_edge_cut_map


!================================ EDGE_ZERO_PARAMETER ========================80
!
!  Set edge parameter of cylinder intersection
!
!=============================================================================80

  subroutine edge_zero_parameter( geo_name, geom_def, w1, w2, pa, pb )

    character(len=80),         intent(in ) :: geo_name
    type(sample_type),         intent(in ) :: geom_def
    real(dp), dimension(3),    intent(in ) :: pa, pb
    real(dp),                  intent(out) :: w1, w2

    real(dp), dimension(3 ) :: c0, c1, c2, n, pint
    real(dp), dimension(3 ) :: p_in
    real(dp), dimension(3 ) :: p_out
    real(dp), dimension(3 ) :: p1
    real(dp), dimension(3 ) :: p2
    real(dp), dimension(3 ) :: p3
    real(dp), dimension(3 ) :: p4
    real(dp), dimension(3 ) :: p5
    real(dp), dimension(3 ) :: p6
    real(dp), dimension(3 ) :: p7
    real(dp), dimension(3 ) :: p8
    real(dp)                :: r0
    integer                 :: hex_face

    real(dp)                :: t1, t2, t
    real(dp), dimension(3 ) :: ea, da, v, a
    real(dp)                :: a2, a1, a0, radical
    real(dp)                :: cossq

    real(dp), dimension(3 ) :: ab, ap, ap_x_ab, v_x_ab
    real(dp)                :: ab2, radius
    real(dp), parameter  :: zero = 0.0_dp
    real(dp), parameter  :: one  = 1.0_dp

  continue

!
! Compute weights as the intersection of the parametric line that constitutes
! the Edge and the analytic cylindrical surface.  Shape derivatives of the
! weights are computed analytically from the parametric eqn of the line that
! forms the edge, r = (1-t)r(node1) + t*r(node2).
!
    select case ( geo_name )

    case ( 'filledbox' )

      t = 0.5_dp

    case ( 'box' )
       p1 = geom_def%p1
       p2 = geom_def%p2
       p3 = geom_def%p3
       p4 = geom_def%p4
       p5 = geom_def%p5
       p6 = geom_def%p6
       p7 = geom_def%p7
       p8 = geom_def%p8
! ensure p_out is outside the box, p_in is inside the box
       if (                                                                    &
          ( tet_vol_value( p2, p1, p3, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p2, p3, p4, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p2, p4, p8, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p2, p8, p6, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p2, p5, p1, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p2, p6, p5, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p7, p6, p8, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p7, p5, p6, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p7, p1, p5, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p7, p3, p1, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p7, p8, p4, pa ) >= zero ) .and.                    &
          ( tet_vol_value( p7, p4, p3, pa ) >= zero )                          &
          ) then
        p_in  = pa
        p_out = pb
      else
        p_out = pa
        p_in  = pb
      endif

      call hex_face_parameter( &
       p1, p2, p3, p4, p5, p6, p7, p8, p_out, p_in, hex_face, c0, c1, c2 )
      call intersection( c0, c1, c2, pa, pb, pint, t )

    case ( 'cylinder' )

      radius  = geom_def%r_cylinder
!     o       = pa
!     v       = pb - pa
!     ab      = geom_def%p2_cylinder - geom_def%p1_cylinder
!     ap      =      o - geom_def%p1_cylinder
!     ap_x_ab = cross_product(ap,ab)
!      v_x_ab = cross_product(v,ab)
!      ab2    = dot_product(ab,ab)

!     a2      = dot_product( v_x_ab, v_x_ab)
!     a1      = 2.0_dp * dot_product( v_x_ab, ap_x_ab) ! v_x_ab \dot ap_x_ab
!     a0      = dot_product( ap_x_ab, ap_x_ab) - (radius * radius * ab2)
!     radical = a1*a1 - 4.0_dp*a2*a0

      p1      = geom_def%p1_cylinder
      p2      = geom_def%p2_cylinder
      v       = pb - pa
      ab      = p2 - p1
      ap      = pa - p1
      ap_x_ab = cross_product(ap,ab)
      v_x_ab  = cross_product(v,ab)
      ab2     = dot_product(ab,ab)
      a2      = dot_product(v_x_ab,v_x_ab)
      a1      = 2.0_dp * dot_product(v_x_ab,ap_x_ab)
      a0      = dot_product(ap_x_ab,ap_x_ab) - (radius*radius * ab2)
      radical = a1 * a1 - 4.0_dp * a2 * a0

      t   = -999.0d+0
      if ( radical > 0.0d+0 ) then
        t1 = (-a1 + sqrt(radical)) / ( 2.0_dp*a2 )
        t2 = (-a1 - sqrt(radical)) / ( 2.0_dp*a2 )
        if ( ( t1 >= zero ) .and. ( t1 <= one ) ) then
          t = t1
        else if ( ( t2 >= zero ) .and. ( t2 <= one ) ) then
          t = t2
        endif
      else if ( radical < 0.0d+0 ) then ! no intersection
        write(*,'(a,4(1x,es12.5))') &
           'No cylinder-segment intersection found',a2,a1,a0,radical
      else ! singular point
        if ( a2 > tiny(0.0d+0) ) t = a1/(2.0d+0*a2)
      endif

    case ( 'cone' )

      a     = geom_def%n
      v     = geom_def%vertex
      cossq = geom_def%cossq
      ea    = pb - pa
      da    = pa - v
      a2    =          dot_product(a,ea)*dot_product(a,ea)                     &
                     - cossq*dot_product(ea,ea)
      a1    = 2.0_dp*( dot_product(a,ea)*dot_product(a,da)                     &
                     - cossq*dot_product(ea,da) )
      a0    =          dot_product(a,da)*dot_product(a,da)                     &
                     - cossq*dot_product(da,da)

      radical = a1*a1 - 4.0d+0*a2*a0
      t   = 0.0d+0

      if ( radical > 0.0d+0 ) then
        t1 = (-a1 - sqrt(radical) )/ ( 2.0d+0*a2 )
        t2 = (-a1 + sqrt(radical) )/ ( 2.0d+0*a2 )
        if ( ( t1 >= zero ) .and. ( t1 <= one ) ) then
          t = t1
        else if ( ( t2 >= zero ) .and. ( t2 <= one ) ) then
          t = t2
        endif
      else if ( radical < 0.0d+0 ) then ! no intersection
        write(*,'(a,3(1x,es12.5))') &
           'No cone-segment intersection found',a2,a1,a0,radical
        call lmpi_die
      else ! singular point
        if ( a2 > tiny(0.0d+0) ) t = a1/(2.0d+0*a2)
      endif

    case ( 'sphere' )

      c0 = geom_def%center
      r0 = geom_def%radius
      call sphere_line(c0,r0,pa,pb,t)

    case ( 'plane', 'quad', 'circle' )

      c0 = geom_def%po
      n  = geom_def%n
      call plane_line(c0,n,pa,pb,t)

    case default

      t = 0.5_dp

    end select

    w1 = 1.0_dp - t
    w2 = t

  end subroutine edge_zero_parameter


!================================ HEX_FACE_PARAMETER +========================80
!
!  Determine which face of hex is pierced by vector ab
!
!=============================================================================80

  subroutine hex_face_parameter( p1, p2, p3, p4, p5, p6, p7, p8,               &
                                 pa, pb, hex_face, c0, c1, c2 )

    real(dp), dimension(3),    intent(in ) :: pa, pb
    real(dp), dimension(3),    intent(out) :: c0, c1, c2

    real(dp), parameter                    :: zero = 0.0_dp

    real(dp), dimension(3 ), intent(in) :: p1
    real(dp), dimension(3 ), intent(in) :: p2
    real(dp), dimension(3 ), intent(in) :: p3
    real(dp), dimension(3 ), intent(in) :: p4
    real(dp), dimension(3 ), intent(in) :: p5
    real(dp), dimension(3 ), intent(in) :: p6
    real(dp), dimension(3 ), intent(in) :: p7
    real(dp), dimension(3 ), intent(in) :: p8
    integer,                 intent(out) :: hex_face

  continue

     hex_face = 0

     if ( ( tet_vol_value( pa, p2, p1, pb ) > zero   &
.and.       tet_vol_value( pa, p1, p3, pb ) > zero   &
.and.       tet_vol_value( pa, p3, p2, pb ) > zero ) &
) then
       hex_face = 1
         c0 = p2
         c1 = p1
         c2 = p3

else if ( ( tet_vol_value( pa, p2, p3, pb ) > zero  &
.and.       tet_vol_value( pa, p3, p4, pb ) > zero  &
.and.       tet_vol_value( pa, p4, p2, pb ) > zero )&
) then
       hex_face = 2
         c0 = p2
         c1 = p3
         c2 = p4

else if ( ( tet_vol_value( pa, p2, p4, pb ) > zero  &
.and.       tet_vol_value( pa, p4, p8, pb ) > zero  &
.and.       tet_vol_value( pa, p8, p2, pb ) > zero )&
 ) then
       hex_face = 3
         c0 = p2
         c1 = p4
         c2 = p8

else if ( ( tet_vol_value( pa, p2, p8, pb ) > zero  &
.and.       tet_vol_value( pa, p8, p6, pb ) > zero  &
.and.       tet_vol_value( pa, p6, p2, pb ) > zero )&
 ) then
       hex_face = 4
         c0 = p2
         c1 = p8
         c2 = p6

else if ( ( tet_vol_value( pa, p2, p5, pb ) > zero  &
.and.       tet_vol_value( pa, p5, p1, pb ) > zero  &
.and.       tet_vol_value( pa, p1, p2, pb ) > zero )&
 ) then
       hex_face = 5
         c0 = p2
         c1 = p5
         c2 = p1

else if ( ( tet_vol_value( pa, p2, p6, pb ) > zero  &
.and.       tet_vol_value( pa, p6, p5, pb ) > zero  &
.and.       tet_vol_value( pa, p5, p2, pb ) > zero )&
 ) then
       hex_face = 6
         c0 = p2
         c1 = p6
         c2 = p5

else if ( ( tet_vol_value( pa, p7, p6, pb ) > zero  &
.and.       tet_vol_value( pa, p6, p8, pb ) > zero  &
.and.       tet_vol_value( pa, p8, p7, pb ) > zero )&
 ) then
       hex_face = 7
         c0 = p7
         c1 = p6
         c2 = p8

else if ( ( tet_vol_value( pa, p7, p5, pb ) > zero  &
.and.       tet_vol_value( pa, p5, p6, pb ) > zero  &
.and.       tet_vol_value( pa, p6, p7, pb ) > zero )&
 ) then
       hex_face = 8
         c0 = p7
         c1 = p5
         c2 = p6

else if ( ( tet_vol_value( pa, p7, p1, pb ) > zero  &
.and.       tet_vol_value( pa, p1, p5, pb ) > zero  &
.and.       tet_vol_value( pa, p5, p7, pb ) > zero )&
 ) then
       hex_face = 9
         c0 = p7
         c1 = p1
         c2 = p5

else if ( ( tet_vol_value( pa, p7, p3, pb ) > zero  &
.and.       tet_vol_value( pa, p3, p1, pb ) > zero  &
.and.       tet_vol_value( pa, p1, p7, pb ) > zero )&
 ) then
       hex_face = 10
         c0 = p7
         c1 = p3
         c2 = p1

else if ( ( tet_vol_value( pa, p7, p8, pb ) > zero  &
.and.       tet_vol_value( pa, p8, p4, pb ) > zero  &
.and.       tet_vol_value( pa, p4, p7, pb ) > zero )&
 ) then
       hex_face = 11
         c0 = p7
         c1 = p8
         c2 = p4

else if ( ( tet_vol_value( pa, p7, p4, pb ) > zero  &
.and.       tet_vol_value( pa, p4, p3, pb ) > zero  &
.and.       tet_vol_value( pa, p3, p7, pb ) > zero )&
 ) then

       hex_face = 12
         c0 = p7
         c1 = p4
         c2 = p3
endif

  end subroutine hex_face_parameter

!================================ TRIANGLE_AREA ==============================80
!
!  Get the area of a triangle
!
!=============================================================================80

  subroutine triangle_area(trix, triy, triz, n, area )

    real(dp), dimension(3),   intent(in)  :: trix, triy, triz
    real(dp),                 intent(out) :: area
    real(dp), dimension(3),   intent(out) :: n

    real(dp) :: ax, ay, az, bx, by, bz

  continue

      ax = trix(1) - trix(2)
      ay = triy(1) - triy(2)
      az = triz(1) - triz(2)

      bx = -(trix(3) - trix(1))
      by = -(triy(3) - triy(1))
      bz = -(triz(3) - triz(1))

      n(1) = 0.5_dp*(ay*bz - by*az)
      n(2) = 0.5_dp*(bx*az - ax*bz)
      n(3) = 0.5_dp*(ax*by - bx*ay)
      area = sqrt (n(1)*n(1)+ n(2)*n(2)+ n(3)*n(3))

  end subroutine triangle_area


!================================ TRIANGLE_AREA_METHOD2 ======================80
!
!  Get the area of a triangle using Heron's formula
!
!=============================================================================80

  subroutine triangle_area_method2(trix, triy, triz, n, area )

    real(dp), dimension(3),   intent(in)  :: trix, triy, triz
    real(dp),                 intent(out) :: area
    real(dp), dimension(3),   intent(out) :: n

    real(dp), parameter    :: my_tiny = tiny(0.0_dp)

    real(dp) :: ax, ay, az, bx, by, bz, cx, cy, cz
    real(dp) :: a, b, c, r, s, t

  continue

      n = 0.0_dp

      ax = trix(1) - trix(2)
      ay = triy(1) - triy(2)
      az = triz(1) - triz(2)
      a  = sqrt(ax*ax+ay*ay+az*az)
      if ( a <= my_tiny ) a = 0.0_dp

      bx = -(trix(3) - trix(1))
      by = -(triy(3) - triy(1))
      bz = -(triz(3) - triz(1))
      b  = sqrt(bx*bx+by*by+bz*bz)
      if ( b <= my_tiny ) b = 0.0_dp

      cx = trix(2) - trix(3)
      cy = triy(2) - triy(3)
      cz = triz(2) - triz(3)
      c  = sqrt(cx*cx+cy*cy+cz*cz)
      if ( c <= my_tiny ) c = 0.0_dp

      r  = ay*bz - by*az
      s  = ax*bz - bx*az
      t  = ax*by - bx*ay

      if ( r /= 0.0_dp )                                                       &
             n(1) = 0.5_dp * sqrt(1.0_dp/(1.0_dp + (s/r)*(s/r) + (t/r)*(t/r)))
      if ( s /= 0.0_dp )                                                       &
             n(2) = 0.5_dp * sqrt(1.0_dp/(1.0_dp + (r/s)*(r/s) + (t/s)*(t/s)))
      if ( t /= 0.0_dp )                                                       &
             n(3) = 0.5_dp * sqrt(1.0_dp/(1.0_dp + (s/t)*(s/t) + (r/t)*(r/t)))
! normalize
      t = sqrt (n(1)*n(1)+ n(2)*n(2)+ n(3)*n(3))
      if ( t <= my_tiny ) then
        n = 0.0_dp
      else
        n = n / t
      endif

      area = ( a + b + c ) * ( b + c - a ) * ( c + a - b ) * ( a + b - c )
      if ( area <= my_tiny ) area = 0.0_dp
      area = 0.25_dp * sqrt( area )

  end subroutine triangle_area_method2


!================================ TRIANGLE_AREA_METHOD3 ======================80
!
!  Get 3 areas for a triangle using Heron's formula
!
!=============================================================================80

  subroutine triangle_area_method3(trix, triy, triz, n, area1, area2, area3    &
                                   , area )

    real(dp), dimension(3),   intent(in)  :: trix, triy, triz
    real(dp),                 intent(out) :: area, area1, area2, area3
    real(dp), dimension(3),   intent(out) :: n

    real(dp) :: area_half1, area_half2
    real(dp), dimension(3)  :: p1, p2, p3, pc, p12, p23, p31, nn
    real(dp), dimension(3)  :: tx, ty, tz

  continue

      n = 0.0_dp

      call triangle_area_method2( trix, triy, triz, n, area )

      p1(1) = trix(1); p1(2) = triy(1); p1(3) = triz(1)
      p2(1) = trix(2); p2(2) = triy(2); p2(3) = triz(2)
      p3(1) = trix(3); p3(2) = triy(3); p3(3) = triz(3)
      pc    = ( p1 + p2 + p3 ) / 3.0_dp
      p12   = 0.5_dp * ( p1 + p2 )
      p23   = 0.5_dp * ( p2 + p3 )
      p31   = 0.5_dp * ( p3 + p1 )

! construct area1
      tx(1) = p1(1); tx(2) = p12(1); tx(3) =  pc(1)
      ty(1) = p1(2); ty(2) = p12(2); ty(3) =  pc(2)
      tz(1) = p1(3); tz(2) = p12(3); tz(3) =  pc(3)
      call triangle_area_method2( tx, ty, tz, nn, area_half1 )
      tx(1) = p1(1); tx(2) =  pc(1); tx(3) = p31(1)
      ty(1) = p1(2); ty(2) =  pc(2); ty(3) = p31(2)
      tz(1) = p1(3); tz(2) =  pc(3); tz(3) = p31(3)
      call triangle_area_method2( tx, ty, tz, nn, area_half2 )
      area1 = area_half1 + area_half2

! construct area2
      tx(1) = p2(1); tx(2) = p23(1); tx(3) =  pc(1)
      ty(1) = p2(2); ty(2) = p23(2); ty(3) =  pc(2)
      tz(1) = p2(3); tz(2) = p23(3); tz(3) =  pc(3)
      call triangle_area_method2( tx, ty, tz, nn, area_half1 )
      tx(1) = p2(1); tx(2) =  pc(1); tx(3) = p12(1)
      ty(1) = p2(2); ty(2) =  pc(2); ty(3) = p12(2)
      tz(1) = p2(3); tz(2) =  pc(3); tz(3) = p12(3)
      call triangle_area_method2( tx, ty, tz, nn, area_half2 )
      area2 = area_half1 + area_half2

! construct area3
      tx(1) = p3(1); tx(2) = p31(1); tx(3) =  pc(1)
      ty(1) = p3(2); ty(2) = p31(2); ty(3) =  pc(2)
      tz(1) = p3(3); tz(2) = p31(3); tz(3) =  pc(3)
      call triangle_area_method2( tx, ty, tz, nn, area_half1 )
      tx(1) = p3(1); tx(2) =  pc(1); tx(3) = p23(1)
      ty(1) = p3(2); ty(2) =  pc(2); ty(3) = p23(2)
      tz(1) = p3(3); tz(2) =  pc(3); tz(3) = p23(3)
      call triangle_area_method2( tx, ty, tz, nn, area_half2 )
      area3 = area_half1 + area_half2

      area = area1 + area2 + area3

  end subroutine triangle_area_method3


!============================= SPHERE_LINE ===================================80
!
!  Intersection between the survey surface sphere with radius, r0,  centered at
!  po and the parametric line r = (1-t) r1 + t r2 determined by the points
!  pa and pb as endpoints
!
!=============================================================================80

  subroutine sphere_line(po,r0,pa,pb,t)

    real(dp), dimension(3), intent(in ) :: po, pa, pb
    real(dp),               intent(in ) :: r0
    real(dp),               intent(out) :: t

!   local

    real(dp) :: a, b, c, d, e, f, r1
    real(dp) :: ap, bp, cp, radical, t_plus, t_minus

  continue

    t_plus  = 0.0_dp
    t_minus = 0.0_dp

    a = pb(1) - pa(1)
    b = pb(2) - pa(2)
    c = pb(3) - pa(3)

    d = pa(1) - po(1)
    e = pa(2) - po(2)
    f = pa(3) - po(3)

    r1 = sqrt( (pa(1)-po(1)) * (pa(1)-po(1))                                   &
             + (pa(2)-po(2)) * (pa(2)-po(2))                                   &
             + (pa(3)-po(3)) * (pa(3)-po(3)) )

    ap = 0.5_dp * ( a*a + b*b + c* c )
    bp = d*a + e*b + f*c
    cp = -0.5_dp * ( r0*r0 - r1*r1 )
    radical = bp*bp - 4.0_dp*ap*cp

    if ( radical < 0.0_dp ) then
      radical = 0.0_dp
      write(*,*) 'Strange - no sphere-line solution'
      call lmpi_die
    else
      t_plus  = ( -bp + sqrt( radical ) ) / ( 2.0_dp * ap )
      t_minus = ( -bp - sqrt( radical ) ) / ( 2.0_dp * ap )
    endif

         if ( ( t_plus  >= 0.0_dp ) .and. ( t_plus  <= 1.0_dp ) ) then
      t = t_plus
    else if ( ( t_minus >= 0.0_dp ) .and. ( t_minus <= 1.0_dp ) ) then
      t = t_minus
    endif

  end subroutine sphere_line


!============================= PLANE_LINE ====================================80
!
!  Intersection between the survey surface plane defined by P0 and direction
!  ap, bp, cp and the parametric line r = (1-t) r1 + t r2
!
!=============================================================================80

  subroutine plane_line(po,n,pa,pb,t)

    real(dp), dimension(3), intent(in ) :: po, n, pa, pb
    real(dp),               intent(out) :: t

!   local

    real(dp) :: a21, b21, c21, d, e, f
    real(dp) :: ap, bp

  continue

    a21 = pb(1) - pa(1)
    b21 = pb(2) - pa(2)
    c21 = pb(3) - pa(3)

    d   = pa(1) - po(1)
    e   = pa(2) - po(2)
    f   = pa(3) - po(3)

    ap = n(1)*a21 + n(2)*b21 + n(3)*c21
    bp = d*n(1)   + e*n(2)   + f*n(3)

    if ( ap == 0.0_dp ) then
!      write(*,*) 'Strange - no plane-line solution'
      t  = 0.0_dp
    else
      t  = - ( bp  / ap )
    endif

  end subroutine plane_line

!================================ CHECK_QUAD =================================80
!
!  Check for non-planar or zero area quadrilateral input
!
!=============================================================================80

  subroutine is_quad_flat(quad_slice,flat)

    type(sample_type),    intent(in ) :: quad_slice
    logical,              intent(out) :: flat

    real(dp), dimension(3) :: p1, p2, p3, p4
    real(dp)               :: ax, ay, az
    real(dp)               :: bx, by, bz
    real(dp)               :: nx1, ny1, nz1, nx2, ny2, nz2
    real(dp)               :: area1, area2

    real(dp), parameter :: eps = 1.0e-12_dp

  continue

! initialize

    p1    = 0.0_dp
    p2    = 0.0_dp
    p3    = 0.0_dp
    p4    = 0.0_dp
    ax    = 0.0_dp
    ay    = 0.0_dp
    az    = 0.0_dp
    bx    = 0.0_dp
    by    = 0.0_dp
    bz    = 0.0_dp
    nx1   = 0.0_dp
    ny1   = 0.0_dp
    nz1   = 0.0_dp
    nx2   = 0.0_dp
    ny2   = 0.0_dp
    nz2   = 0.0_dp
    area1 = 0.0_dp
    area2 = 0.0_dp
    flat  = .false.

    p1(1) = quad_slice%p1(1)
    p1(2) = quad_slice%p1(2)
    p1(3) = quad_slice%p1(3)

    p2(1) = quad_slice%p2(1)
    p2(2) = quad_slice%p2(2)
    p2(3) = quad_slice%p2(3)

    p3(1) = quad_slice%p3(1)
    p3(2) = quad_slice%p3(2)
    p3(3) = quad_slice%p3(3)

    p4(1) = quad_slice%p4(1)
    p4(2) = quad_slice%p4(2)
    p4(3) = quad_slice%p4(3)

! normal 1
    ax = p2(1) - p1(1)
    ay = p2(2) - p1(2)
    az = p2(3) - p1(3)

    bx = p3(1) - p1(1)
    by = p3(2) - p1(2)
    bz = p3(3) - p1(3)

!       Normal points away from grid interior.

    nx1 = -0.5_dp * ( ay*bz - az*by )
    ny1 =  0.5_dp * ( ax*bz - az*bx )
    nz1 = -0.5_dp * ( ax*by - ay*bx )

    area1  =  sqrt(nx1*nx1 + ny1*ny1 + nz1*nz1)

    nx1 = nx1 / area1
    ny1 = ny1 / area1
    nz1 = nz1 / area1

! normal 2
    ax = p3(1) - p1(1)
    ay = p3(2) - p1(2)
    az = p3(3) - p1(3)

    bx = p4(1) - p1(1)
    by = p4(2) - p1(2)
    bz = p4(3) - p1(3)

!       Normal points away from grid interior.

    nx2 = -0.5_dp * ( ay*bz - az*by )
    ny2 =  0.5_dp * ( ax*bz - az*bx )
    nz2 = -0.5_dp * ( ax*by - ay*bx )

    area2  =  sqrt(nx2*nx2 + ny2*ny2 + nz2*nz2)

    nx2 = nx2 / area2
    ny2 = ny2 / area2
    nz2 = nz2 / area2

    if (      ( abs(nx1-nx2) < eps )                                           &
        .and. ( abs(ny1-ny2) < eps )                                           &
        .and. ( abs(nz1-nz2) < eps )                                           &
        .and. ( ( area1 + area2 ) > eps ) ) then
       flat = .true.
     else
       flat = .false.
     endif

  end subroutine is_quad_flat


!================================ TET_VOL_VALUE ==============================80
!
!  Return a postive ( or negative ) volume given 4 nodes
!
!=============================================================================80

  pure function tet_vol_value( po, p1, p2, pa )

    real(dp) :: tet_vol_value

    real(dp), dimension(3), intent(in ) :: po
    real(dp), dimension(3), intent(in ) :: p1
    real(dp), dimension(3), intent(in ) :: p2
    real(dp), dimension(3), intent(in ) :: pa

    real(dp) :: a, b, c, d, e, f, g, h, i

  continue

    a = po(1) - pa(1)
    b = po(2) - pa(2)
    c = po(3) - pa(3)

    d = p1(1) - pa(1)
    e = p1(2) - pa(2)
    f = p1(3) - pa(3)

    g = p2(1) - pa(1)
    h = p2(2) - pa(2)
    i = p2(3) - pa(3)

    tet_vol_value = a * ( e*i - h*f )                                          &
                  + b * ( g*f - d*i )                                          &
                  + c * ( d*h - g*e )

  end function tet_vol_value

!================================ HEX_VOL_VALUE ==============================80
!
!  Return a postive ( or negative ) volume given 8 nodes
!
!=============================================================================80

  pure function hex_vol_value( po, p1, p2, p3, p4, p5, p6, p7  )

    real(dp) :: hex_vol_value

    real(dp), dimension(3), intent(in ) :: po
    real(dp), dimension(3), intent(in ) :: p1
    real(dp), dimension(3), intent(in ) :: p2
    real(dp), dimension(3), intent(in ) :: p3
    real(dp), dimension(3), intent(in ) :: p4
    real(dp), dimension(3), intent(in ) :: p5
    real(dp), dimension(3), intent(in ) :: p6
    real(dp), dimension(3), intent(in ) :: p7

    real(dp), dimension(3,3) :: a, b, c

  continue

    a(1,1:3) = p7 - po
    a(2,1:3) = p1 - po
    a(3,1:3) = p3 - p5

    b(1,1:3) = p7 - po
    b(2,1:3) = p4 - po
    b(3,1:3) = p5 - p6

    c(1,1:3) = p7 - po
    c(2,1:3) = p2 - po
    c(3,1:3) = p6 - p3

    hex_vol_value = ( det_a(a) + det_a(b) + det_a(c) ) / 6.0_dp

  end function hex_vol_value

!=============================================================================80
!
! Compute determinant
!
!=============================================================================80

  real(dp) pure function det_a( a ) result( determinant )

!   real(dp)                              :: determinant
    real(dp), dimension(3,3), intent(in)  :: a

    real(dp)    :: m1, m2, m3

    continue

!   error = 0

    !...find determinant of transformation matrix
    !...factor by columns
    m1 = a(2,2)*a(3,3) - a(3,2)*a(2,3)
    m2 = a(1,2)*a(3,3) - a(3,2)*a(1,3)
    m3 = a(1,2)*a(2,3) - a(2,2)*a(1,3)

    determinant = a(1,1)*m1 - a(2,1)*m2 + a(3,1)*m3

!   if(abs(deti) < 1.0e-12_dp) error = 1

!   if(error /= 0) return

  end function det_a

!=============================================================================80
!
! Compute determinant
!
!=============================================================================80

  real(dp) pure function trace_a( a ) result( trace )

    real(dp), dimension(3,3), intent(in)  :: a

    continue

    trace = a(1,1) + a(2,2) + a(3,3)

  end function trace_a


!=============================== MAKE_A ======================================80
!
!  Create 3x3 matrix of velocity gradients for notational convenience
!
!=============================================================================80
  pure function make_a ( gradx2, grady2, gradz2                                &
                       , gradx3, grady3, gradz3                                &
                       , gradx4, grady4, gradz4 ) result ( a )

    real(dp), dimension(3,3) :: a

    real(dp), intent(in) :: gradx2, grady2, gradz2
    real(dp), intent(in) :: gradx3, grady3, gradz3
    real(dp), intent(in) :: gradx4, grady4, gradz4

  continue

    a(1,1) = gradx2
    a(1,2) = grady2
    a(1,3) = gradz2
    a(2,1) = gradx3
    a(2,2) = grady3
    a(2,3) = gradz3
    a(3,1) = gradx4
    a(3,2) = grady4
    a(3,3) = gradz4

  end function make_a

!=============================== INTERSECTION ================================80
!
!  More general triangle-line intersection determination
!
!=============================================================================80

  subroutine intersection( pa, pb, pc, pe, pd, pint, s )

    real(dp), dimension(3), intent(in ) :: pa
    real(dp), dimension(3), intent(in ) :: pb
    real(dp), dimension(3), intent(in ) :: pc
    real(dp), dimension(3), intent(in ) :: pd
    real(dp), dimension(3), intent(in ) :: pe
    real(dp), dimension(3), intent(out) :: pint
    real(dp),               intent(out) :: s

    real(dp), parameter                 :: tol = 1.0e-08_dp
    real(dp), dimension(3)              :: vcb
    real(dp), dimension(3)              :: vcd
    real(dp), dimension(3)              :: vac
    real(dp), dimension(3)              :: ved
    real(dp), dimension(3)              :: vc1
!   real(dp), dimension(3)              :: vc2
    real(dp), dimension(3)              :: n1
    real(dp), dimension(3)              :: n2
!   real(dp), dimension(3)              :: n3
    real(dp)                            :: gamma
    real(dp)                            :: area1, area2

    real(dp), parameter :: eps = 1.0e-12_dp

  continue

    vcb = pc - pb
    vcd = pc - pd
    vac = pa - pc
    ved = pe - pd

    vc1 = pe - pc
!   vc2 = pd - pc

    gamma =                                                                    &
     ved(1) * ( vac(3) * vcb(2) - vac(2) * vcb(3) )                            &
   - ved(2) * ( vac(3) * vcb(1) - vac(1) * vcb(3) )                            &
   + ved(3) * ( vac(2) * vcb(1) - vac(1) * vcb(2) )

! test for degenerate case of point in plane
    if ( abs(gamma) < tol ) then
      n1 = cross_product( vac, -vcb )
      n2 = cross_product( vac,  vc1 )
!     n3 = cross_product( vac,  vc2 )
      area1 =  sqrt(n1(1)*n1(1) + n1(2)*n1(2) + n1(3)*n1(3))
      area2 =  sqrt(n2(1)*n2(1) + n2(2)*n2(2) + n2(3)*n2(3))
      n1 = n1 / area1
      n2 = n2 / area2
! if point e is in plane with pa, pb and pc, then parameter s = 1.0
      if (      abs(n1(1)-n2(1)) < eps                                         &
          .and. abs(n1(2)-n2(2)) < eps                                         &
          .and. abs(n1(3)-n2(3)) < eps                                         &
          .and. ( area1 + area2 ) > eps                                        &
         ) then
       s = 1.0_dp
      else
       s = 0.0_dp
      endif

    else

      s     = ( 1.0_dp / gamma ) *                                             &
     ( vcd(1) * ( vac(3) * vcb(2) - vac(2) * vcb(3) )                          &
     - vcd(2) * ( vac(3) * vcb(1) - vac(1) * vcb(3) )                          &
     + vcd(3) * ( vac(2) * vcb(1) - vac(1) * vcb(2) ) )

    endif

    pint(1:3) = pd(1:3) + s * ved(1:3)

    s = 1.0_dp - s

  end subroutine intersection


!======================== SPLIT_PYR_EDGE_MAP =================================80
!
! Determine 2 3-point maps for a 4-point cut through a pyramid
!
!=============================================================================80

  subroutine split_pyr_edge_map( cut_edge_map, cut_edge_map1, cut_edge_map2 )

    use sampling_headers, only : pyr_cut_edge_map1, pyr_cut_edge_map2
    implicit none

    integer, dimension(8), intent(in ) :: cut_edge_map
    integer, dimension(8), intent(out) :: cut_edge_map1
    integer, dimension(8), intent(out) :: cut_edge_map2

    integer :: i, cuts
    integer :: dec
    integer, parameter :: number_of_edges = 8

  continue

!   calculate a decimal equivalent of the 4 point cut map
    dec = 0
    do i = 1, number_of_edges
      if ( cut_edge_map(i) == 1 ) dec = dec + 2**i
    enddo
!   pick out the appropriate split from pyr_cut_edge_map defined
!   in the module header
    do cuts = 1, 16
      if ( dec  == pyr_cut_edge_map1(1,cuts) ) then
         cut_edge_map1 = pyr_cut_edge_map1(2:9,cuts)
         cut_edge_map2 = pyr_cut_edge_map2(2:9,cuts)
      endif
    enddo

  end subroutine split_pyr_edge_map


!======================== SPLIT_PRZ_EDGE_MAP =================================80
!
! Determine 2 3-point maps for a 4-point cut through a prism
!
!=============================================================================80

  subroutine split_prz_edge_map( cut_edge_map, cut_edge_map1, cut_edge_map2 )

    use sampling_headers, only : prz_cut_4edge_map1, prz_cut_4edge_map2

    implicit none

    integer, dimension(8), intent(in ) :: cut_edge_map
    integer, dimension(8), intent(out) :: cut_edge_map1
    integer, dimension(8), intent(out) :: cut_edge_map2

    integer :: i, cuts
    integer :: dec
    integer, parameter :: number_of_edges = 9

  continue

!   calculate a decimal equivalent of the 4 point cut map
    dec = 0
    do i = 1, number_of_edges
      if ( cut_edge_map(i) == 1 ) dec = dec + 2**i
    enddo
!   pick out the appropriate split from pyr_cut_edge_map defined
!   in the module header
    do cuts = 1, 11
      if ( dec  == prz_cut_4edge_map1(1,cuts) ) then
         cut_edge_map1 = prz_cut_4edge_map1(2:9,cuts)
         cut_edge_map2 = prz_cut_4edge_map2(2:9,cuts)
      endif
    enddo

  end subroutine split_prz_edge_map


!======================== SPLIT_HEX_EDGE_MAP =================================80
!
! Determine 2 3-point maps for a 4-point cut through a hexahedral
!
!=============================================================================80

  subroutine split_hex_edge_map( cut_edge_map, cut_edge_map1, cut_edge_map2 )

    use sampling_headers, only : hex_cut_edge_map1, hex_cut_edge_map2

    implicit none

    integer, dimension(12), intent(in ) :: cut_edge_map
    integer, dimension(12), intent(out) :: cut_edge_map1
    integer, dimension(12), intent(out) :: cut_edge_map2

    integer :: i, cuts
    integer :: dec
    integer, parameter :: number_of_edges = 12
    logical :: cut

  continue

!   calculate a decimal equivalent of the 4 point cut map
    dec = 0
    do i = 1, number_of_edges
      if ( cut_edge_map(i) == 1 ) dec = dec + 2**i
    enddo
!   pick out the appropriate split from hex_cut_edge_map defined
!   in the module header
    cuts = 1
    cut = .false.
    do while ( .not.cut .and. cuts <= 36 )
      if ( dec  == hex_cut_edge_map1(1,cuts) ) then
        cut           = .true.
        cut_edge_map1 = hex_cut_edge_map1(2:13,cuts)
        cut_edge_map2 = hex_cut_edge_map2(2:13,cuts)
      endif
      cuts = cuts + 1
    enddo

  end subroutine split_hex_edge_map

!========================== IS_OPPOSITE ======================================80
!
!  To detect if an element has been cut by the sampling geometry
!
!=============================================================================80

  logical function is_opposite ( type_cell , nodal_values )

    implicit none

    character(len=3),       intent(in) :: type_cell
    real(dp), dimension(:), intent(in) :: nodal_values

    real(dp), parameter                :: zero = 0.0_dp

  continue

    is_opposite = .true.

    select case ( type_cell )

      case ( 'tet' )

        if((nodal_values(1)>zero).and.                                 &
           (nodal_values(2)>zero).and.                                 &
           (nodal_values(3)>zero).and.                                 &
           (nodal_values(4)>zero)) is_opposite = .false.

        if((nodal_values(1)<zero).and.                                 &
           (nodal_values(2)<zero).and.                                 &
           (nodal_values(3)<zero).and.                                 &
           (nodal_values(4)<zero)) is_opposite = .false.

      case ( 'hex' )

        if((nodal_values(1)>zero).and.                                 &
           (nodal_values(2)>zero).and.                                 &
           (nodal_values(3)>zero).and.                                 &
           (nodal_values(4)>zero).and.                                 &
           (nodal_values(5)>zero).and.                                 &
           (nodal_values(6)>zero).and.                                 &
           (nodal_values(7)>zero).and.                                 &
           (nodal_values(8)>zero)) is_opposite = .false.

        if((nodal_values(1)<zero).and.                                 &
           (nodal_values(2)<zero).and.                                 &
           (nodal_values(3)<zero).and.                                 &
           (nodal_values(4)<zero).and.                                 &
           (nodal_values(5)<zero).and.                                 &
           (nodal_values(6)<zero).and.                                 &
           (nodal_values(7)<zero).and.                                 &
           (nodal_values(8)<zero)) is_opposite = .false.

      case ( 'prz' )

        if((nodal_values(1)>zero).and.                                 &
           (nodal_values(2)>zero).and.                                 &
           (nodal_values(3)>zero).and.                                 &
           (nodal_values(4)>zero).and.                                 &
           (nodal_values(5)>zero).and.                                 &
           (nodal_values(6)>zero)) is_opposite = .false.

        if((nodal_values(1)<zero).and.                                 &
           (nodal_values(2)<zero).and.                                 &
           (nodal_values(3)<zero).and.                                 &
           (nodal_values(4)<zero).and.                                 &
           (nodal_values(5)<zero).and.                                 &
           (nodal_values(6)<zero)) is_opposite = .false.

      case ( 'pyr' )

        if((nodal_values(1)>zero).and.                                 &
           (nodal_values(2)>zero).and.                                 &
           (nodal_values(3)>zero).and.                                 &
           (nodal_values(4)>zero).and.                                 &
           (nodal_values(5)>zero)) is_opposite = .false.

        if((nodal_values(1)<zero).and.                                 &
           (nodal_values(2)<zero).and.                                 &
           (nodal_values(3)<zero).and.                                 &
           (nodal_values(4)<zero).and.                                 &
           (nodal_values(5)<zero)) is_opposite = .false.

      case default

    end select

  end function is_opposite

!========================== POINT_TO_CENTROID ================================80
!
!  Calculate distance from point to center of element
!
!=============================================================================80

  real function point_to_centroid ( node_per_cell, px, py, pz, pc )

    implicit none

    integer,                              intent(in ) :: node_per_cell
    real(dp), dimension(0:8            ), intent(in ) :: px, py, pz
    real(dp), dimension(1:3)            , intent(out) :: pc

    real(dp), dimension(3) :: p1, p2, p3, p4, p5, p6, p7, p8, p0

  continue

    p0 = (/px(0), py(0), pz(0)/)
    p1 = 0.0_dp
    p2 = 0.0_dp
    p3 = 0.0_dp
    p4 = 0.0_dp
    p5 = 0.0_dp
    p6 = 0.0_dp
    p7 = 0.0_dp
    p8 = 0.0_dp

    if ( node_per_cell == 4 ) then ! tet
      p1 = (/px(1), py(1), pz(1)/)
      p2 = (/px(2), py(2), pz(2)/)
      p3 = (/px(3), py(3), pz(3)/)
      p4 = (/px(4), py(4), pz(4)/)
    endif

    if ( node_per_cell == 5 ) then ! pyr
      p1 = (/px(1), py(1), pz(1)/)
      p2 = (/px(2), py(2), pz(2)/)
      p3 = (/px(3), py(3), pz(3)/)
      p4 = (/px(4), py(4), pz(4)/)
      p5 = (/px(5), py(5), pz(5)/)
    endif

    if ( node_per_cell == 6 ) then ! prz
      p1 = (/px(1), py(1), pz(1)/)
      p2 = (/px(2), py(2), pz(2)/)
      p3 = (/px(3), py(3), pz(3)/)
      p4 = (/px(4), py(4), pz(4)/)
      p5 = (/px(5), py(5), pz(5)/)
      p6 = (/px(6), py(6), pz(6)/)
    endif

    if ( node_per_cell == 8 ) then ! hex
      p1 = (/px(1), py(1), pz(1)/)
      p2 = (/px(2), py(2), pz(2)/)
      p3 = (/px(3), py(3), pz(3)/)
      p4 = (/px(4), py(4), pz(4)/)
      p5 = (/px(5), py(5), pz(5)/)
      p6 = (/px(6), py(6), pz(6)/)
      p7 = (/px(7), py(7), pz(7)/)
      p8 = (/px(8), py(8), pz(8)/)
    endif

    pc = (1.0_dp/float(node_per_cell))*( p1 + p2 + p3 + p4 + p5 + p6 + p7 + p8 )

    point_to_centroid = sqrt( (p0(1)-pc(1))**2                                 &
                            + (p0(2)-pc(2))**2                                 &
                            + (p0(3)-pc(3))**2 )

  end function point_to_centroid

!========================== IS_INSIDE_ELEMENT ================================80
!
!  To detect if a point is inside an element
!
!=============================================================================80

  logical function is_inside_element ( node_per_cell, px, py, pz )

    implicit none

    integer,                              intent(in) :: node_per_cell
    real(dp), dimension(0:8            ), intent(in) :: px, py, pz

    real(dp), dimension(3) :: p1, p2, p3, p4, p5, p6, p7, p8, p0

    real(dp), parameter :: zero = 0.0_dp

  continue

    is_inside_element = .false.
    p0 = (/px(0), py(0), pz(0)/)

    if ( node_per_cell == 4 ) then ! tet
      p1 = (/px(1), py(1), pz(1)/)
      p2 = (/px(2), py(2), pz(2)/)
      p3 = (/px(3), py(3), pz(3)/)
      p4 = (/px(4), py(4), pz(4)/)
      if (                                                                     &
         ( tet_vol_value( p1, p2, p4, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p2, p3, p4, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p3, p1, p4, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p2, p1, p3, p0 ) >= zero )                           &
         ) is_inside_element = .true.
    endif

    if ( node_per_cell == 5 ) then ! pyr
      p1 = (/px(1), py(1), pz(1)/)
      p2 = (/px(2), py(2), pz(2)/)
      p3 = (/px(3), py(3), pz(3)/)
      p4 = (/px(4), py(4), pz(4)/)
      p5 = (/px(5), py(5), pz(5)/)
      if (                                                                     &
         ( tet_vol_value( p1, p4, p3, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p1, p3, p2, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p3, p5, p2, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p1, p2, p5, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p1, p5, p4, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p3, p4, p5, p0 ) >= zero )                           &
         ) is_inside_element = .true.
    endif

    if ( node_per_cell == 6 ) then ! prz
      p1 = (/px(1), py(1), pz(1)/)
      p2 = (/px(2), py(2), pz(2)/)
      p3 = (/px(3), py(3), pz(3)/)
      p4 = (/px(4), py(4), pz(4)/)
      p5 = (/px(5), py(5), pz(5)/)
      p6 = (/px(6), py(6), pz(6)/)
      if (                                                                     &
         ( tet_vol_value( p1, p4, p2, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p2, p4, p3, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p1, p5, p6, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p1, p2, p6, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p2, p3, p5, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p6, p4, p1, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p5, p4, p6, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p5, p3, p4, p0 ) >= zero )                           &
         ) is_inside_element = .true.
    endif

    if ( node_per_cell == 8 ) then ! hex
      p1 = (/px(1), py(1), pz(1)/)
      p2 = (/px(2), py(2), pz(2)/)
      p3 = (/px(3), py(3), pz(3)/)
      p4 = (/px(4), py(4), pz(4)/)
      p5 = (/px(5), py(5), pz(5)/)
      p6 = (/px(6), py(6), pz(6)/)
      p7 = (/px(7), py(7), pz(7)/)
      p8 = (/px(8), py(8), pz(8)/)
      if (                                                                     &
         ( tet_vol_value( p2, p1, p3, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p2, p3, p4, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p2, p4, p8, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p2, p8, p6, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p2, p5, p1, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p2, p6, p5, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p7, p6, p8, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p7, p5, p6, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p7, p1, p5, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p7, p3, p1, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p7, p8, p4, p0 ) >= zero ) .and.                     &
         ( tet_vol_value( p7, p4, p3, p0 ) >= zero )                           &
         ) is_inside_element = .true.
    endif

  end function is_inside_element

!========================== IS_INSIDE_CONE ===================================80
!
!  Return true if point is contained by ( semi-infinite ) cone
!
!=============================================================================80
  pure function is_inside_cone( pa, a, v, cossq )

    logical                            :: is_inside_cone
    real(dp), dimension(3), intent(in) :: pa
    real(dp), dimension(3), intent(in) :: a
    real(dp), dimension(3), intent(in) :: v
    real(dp),               intent(in) :: cossq

    real(dp), dimension(3) :: d
    real(dp)               :: adotd
    real(dp)               :: ddotd
    real(dp), parameter    :: zero = 0.0e+0_dp

  continue

    is_inside_cone = .false.

    d     = pa - v
    adotd = dot_product(a,d)
    ddotd = dot_product(d,d)

    if ( adotd >= zero ) then
      if ( adotd*adotd >= cossq*ddotd ) then
        is_inside_cone = .true.
      endif
    endif

  end function is_inside_cone

!========================== INTERSECTS_CONE ==================================80
!
!  Determine if the edge of an element intersects the define cone
!
!=============================================================================80
       subroutine intersects_cone( type_cell, node_per_cell, edge_per_cell     &
                  , p1_end, p2_end, corners, a, v, cossq , nodal_values )

  use element_defs, only : local_e2n_tet, local_e2n_hex                        &
                         , local_e2n_prz, local_e2n_pyr

  character(len=3),                   intent(in)    :: type_cell
  integer ,                           intent(in)    :: node_per_cell
  integer ,                           intent(in)    :: edge_per_cell
  real(dp), dimension(3),             intent(in)    :: p1_end
  real(dp), dimension(3),             intent(in)    :: p2_end
  real(dp), dimension(3,8),           intent(in)    :: corners
  real(dp), dimension(3),             intent(in)    :: a
  real(dp), dimension(3),             intent(in)    :: v
  real(dp),                           intent(in)    :: cossq
  real(dp), dimension(node_per_cell), intent(inout) :: nodal_values

    logical                :: throw_cell
    integer                :: edge
    integer                :: node, node1, node2
    real(dp), parameter    :: zero = 0.0e+0_dp
    real(dp), dimension(3) :: ea
    real(dp), dimension(3) :: da
    real(dp)               :: a2
    real(dp)               :: a1
    real(dp)               :: a0
    real(dp)               :: t1, t2, radical
    real(dp)               :: adotd
    real(dp), dimension(3) :: d1
    real(dp), dimension(3) :: d2
    real(dp)               :: adotd1, d1dotd1
    real(dp)               :: adotd2, d2dotd2

  continue

    throw_cell = .false.
    node1      = 0
    node2      = 0

! if any of the element nodes are on the negative side
! of the cone plane, throw out the whole element.  This
! keeps elements fron stradling the plane.
!
!     da    = corners(:,node) - v
!
! Substitue p1_end and p2_end for v to create a frustum sample
!
    do node = 1, node_per_cell
      da    = corners(:,node) - p1_end
      adotd = dot_product(a,da)
      if ( adotd < zero ) then
        throw_cell = .true.
      endif
      da    = corners(:,node) - p2_end
      adotd = dot_product(a,da)
      if ( adotd > zero ) then
        throw_cell = .true.
      endif
    enddo

    if ( throw_cell ) then
      nodal_values = -1.0_dp
    else

! test whether the point is inside the cone or not
nodes:  do node = 1, node_per_cell
      if ( is_inside_cone( corners(:,node), a, v, cossq ) ) &
           nodal_values(node) = 1.0_dp
      end do nodes

! got through the edges to assure an intersecting point
edges:  do edge = 1, edge_per_cell
        if ( type_cell == 'tet' ) then
          node1 = local_e2n_tet(edge,1)
          node2 = local_e2n_tet(edge,2)
        else if ( type_cell == 'hex' ) then
          node1 = local_e2n_hex(edge,1)
          node2 = local_e2n_hex(edge,2)
        else if ( type_cell == 'pyr' ) then
          node1 = local_e2n_pyr(edge,1)
          node2 = local_e2n_pyr(edge,2)
        else if ( type_cell == 'prz' ) then
          node1 = local_e2n_prz(edge,1)
          node2 = local_e2n_prz(edge,2)
        endif

        if ( nodal_values(node1)*nodal_values(node2) < zero ) then
          ea    = corners(:,node2) - corners(:,node1)
          da    = corners(:,node1) - v
          a2    =          dot_product(a,ea)*dot_product(a,ea)                 &
                         - cossq*dot_product(ea,ea)
          a1    = 2.0_dp*( dot_product(a,ea)*dot_product(a,da)                 &
                         - cossq*dot_product(ea,da) )
          a0    =          dot_product(a,da)*dot_product(a,da)                 &
                         - cossq*dot_product(da,da)
          if ( ( a1*a1 >= 4.0*a2*a0 ) ) then
            if ( skeleton > 14 ) then
              radical = a1*a1 - 4.0*a2*a0
              t1      = (-a1 + sqrt(radical)) / ( 2.0d+0*a2 )
              t2      = (-a1 - sqrt(radical)) / ( 2.0d+0*a2 )
              write(*,*) 'cone-line roots=', t1, t2
            endif
          else
            write(*,*) 'No line-cone intersection'
            if ( skeleton > 2 ) then
              d1    = corners(:,node1) - v
              adotd1 = dot_product(a,d1)
              d1dotd1 = dot_product(d1,d1)
              d2    = corners(:,node2) - v
              adotd2 = dot_product(a,d2)
              d2dotd2 = dot_product(d2,d2)
              write(6,'(a,8(1x,es12.5))') 'point 1' &
              ,corners(:,node1), adotd1, adotd1*adotd1 , cossq*d1dotd1
              write(6,'(a,8(1x,es12.5))') 'point 2' &
              ,corners(:,node2), adotd2, adotd2*adotd2 , cossq*d2dotd2
            endif
            call lmpi_die
          endif
        endif
      enddo edges

    endif

  end subroutine intersects_cone

!========================== IS_INSIDE_CYLINDER ===============================80
!
!  Return true if point is contained by an infinite cylinder
!
!=============================================================================80
  pure function is_inside_cylinder( p, p1, p2, radius )

    logical                            :: is_inside_cylinder
    real(dp), dimension(3), intent(in) :: p
    real(dp), dimension(3), intent(in) :: p1
    real(dp), dimension(3), intent(in) :: p2
    real(dp),               intent(in) :: radius

    real(dp), dimension(3) :: p01, p02, p21, v
    real(dp)               :: dist

  continue

    is_inside_cylinder = .false.

    p01  = p  - p1
    p02  = p  - p2
    p21  = p2 - p1
    v    = cross_product(p01,p02)
    dist = sqrt(dot_product(v,v))/sqrt(dot_product(p21,p21))
    if ( dist <= radius ) is_inside_cylinder = .true.

  end function is_inside_cylinder

!========================== INTERSECTS_CONE ==================================80
!
!  Determine if the edge of an element intersects the define cone
!
!=============================================================================80
       subroutine intersects_cyl ( type_cell, node_per_cell, edge_per_cell     &
                  , p1_end, p2_end, r, corners, a, nodal_values )

  use element_defs, only : local_e2n_tet, local_e2n_hex                        &
                         , local_e2n_prz, local_e2n_pyr

  character(len=3),                   intent(in)    :: type_cell
  integer ,                           intent(in)    :: node_per_cell
  integer ,                           intent(in)    :: edge_per_cell
  real(dp), dimension(3),             intent(in)    :: p1_end
  real(dp), dimension(3),             intent(in)    :: p2_end
  real(dp),                           intent(in)    :: r
  real(dp), dimension(3,8),           intent(in)    :: corners
  real(dp), dimension(3),             intent(in)    :: a
  real(dp), dimension(node_per_cell), intent(inout) :: nodal_values

    logical                :: throw_cell
    integer                :: edge
    integer                :: node, node1, node2
    real(dp), parameter    :: zero = 0.0e+0_dp
    real(dp), dimension(3) :: da
    real(dp), dimension(3) :: o
    real(dp), dimension(3) :: v
    real(dp), dimension(3) :: ap
    real(dp), dimension(3) :: ab
    real(dp), dimension(3) :: ap_x_ab
    real(dp), dimension(3) :: v_x_ab
    real(dp)               :: a2
    real(dp)               :: a1
    real(dp)               :: a0
    real(dp)               :: t1, t2, radical
    real(dp)               :: adotd
    real(dp)               :: ab2

  continue

    throw_cell = .false.
    node1      = 0
    node2      = 0

! if any of the element nodes are on the negative side
! of the cone plane, throw out the whole element.  This
! keeps elements fron stradling the plane.
!
!     da    = corners(:,node) - v
!
! Substitue p1_end and p2_end for v to create a frustum sample
!
    do node = 1, node_per_cell
      da    = corners(:,node) - p1_end
      adotd = dot_product(a,da)
      if ( adotd < zero ) then
        throw_cell = .true.
      endif
      da    = corners(:,node) - p2_end
      adotd = dot_product(a,da)
      if ( adotd > zero ) then
        throw_cell = .true.
      endif
    enddo

    if ( throw_cell ) then
      nodal_values = -1.0_dp
    else

! test whether the point is inside the cone or not
nodes:  do node = 1, node_per_cell
      if ( is_inside_cylinder( corners(:,node), p1_end, p2_end, r ) ) &
           nodal_values(node) = 1.0_dp
      end do nodes

! got through the edges to assure an intersecting point
edges:  do edge = 1, edge_per_cell
        if ( type_cell == 'tet' ) then
          node1 = local_e2n_tet(edge,1)
          node2 = local_e2n_tet(edge,2)
        else if ( type_cell == 'hex' ) then
          node1 = local_e2n_hex(edge,1)
          node2 = local_e2n_hex(edge,2)
        else if ( type_cell == 'pyr' ) then
          node1 = local_e2n_pyr(edge,1)
          node2 = local_e2n_pyr(edge,2)
        else if ( type_cell == 'prz' ) then
          node1 = local_e2n_prz(edge,1)
          node2 = local_e2n_prz(edge,2)
        endif

        if ( nodal_values(node1)*nodal_values(node2) < zero ) then
           o    = corners(:,node1)
           v    = corners(:,node2) - corners(:,node1)

          ab      = p2_end - p1_end
          ap      =      o - p1_end
          ap_x_ab = cross_product(ap,ab)
           v_x_ab = cross_product(v,ab)
           ab2    = dot_product(ab,ab)

          a2      = dot_product( v_x_ab, v_x_ab)
          a1      = 2.0_dp * dot_product( v_x_ab, ap_x_ab) ! v_x_ab \dot ap_x_ab
          a0      = dot_product( ap_x_ab, ap_x_ab) - (r*r * ab2)
          radical = a1*a1 - 4.0_dp*a2*a0

          if ( ( radical > zero ) ) then
            if ( skeleton > 14 ) then
              radical = a1*a1 - 4.0_dp*a2*a0
              t1      = (-a1 + sqrt(radical)) / ( 2.0_dp*a2 )
              t2      = (-a1 - sqrt(radical)) / ( 2.0_dp*a2 )
              write(*,*) 'cylinder-line roots=', t1, t2
            endif
          else if ( ( radical < zero ) ) then
            write(*,*) 'No line-cylinder intersection--intersects_cyl'
              write(6,'(a,8(1x,es12.5))') ' p1_end=', p1_end
              write(6,'(a,8(1x,es12.5))') ' p2_end=', p2_end
              write(6,'(a,8(1x,es12.5))') '      r=', r
              write(6,'(a,8(1x,es12.5))') '      o=', o
              write(6,'(a,8(1x,es12.5))') '      v=', v
              write(6,'(a,8(1x,es12.5))') ' node1 =', corners(:,node1)
              write(6,'(a,8(1x,es12.5))') ' node2 =', corners(:,node2)
              write(6,'(a,8(1x,es12.5))') 'radical=', radical
            call lmpi_conditional_stop(1,'sampling_funclib::intersects_cyl')
          else
          endif
        endif
      enddo edges

    endif

  end subroutine intersects_cyl

!================================ EDGE_STATS =================================80
!
!  Return edge weighted value
!
!=============================================================================80

  pure function edge_stats ( vec_size, edge_weight, qp )

    implicit none

    integer,                         intent(in) :: vec_size
    real(dp),                        intent(in) :: edge_weight
    real(dp), dimension(vec_size,2), intent(in) :: qp

    real(dp), dimension(vec_size)               :: edge_stats

    integer             :: i
    real(dp)            :: weight1, weight2
    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter ::  one = 1.0_dp

  continue

    weight1    = edge_weight
    weight2    = one - weight1
    edge_stats = zero

    do i = 1, vec_size
      edge_stats(i) = qp(i,1)*weight1 + qp(i,2)*weight2
    enddo

  end function edge_stats

!========================== GET_WINDOW_INDEX =================================79
!
!  Return schlieren window indicies for element intersect search
!
!=============================================================================80
  subroutine get_window_index( ivol, span, i_start, i_fin, j_start, j_fin )

    use sampling_headers, only : schlieren_aspect, sample

    implicit none

    integer,                         intent(in ) :: ivol
    real(dp),        dimension(2,3), intent(in ) :: span
    integer,                         intent(out) :: i_start
    integer,                         intent(out) :: i_fin
    integer,                         intent(out) :: j_start
    integer,                         intent(out) :: j_fin

    real(dp), dimension(3,3)              :: a_rot
    real(dp), dimension(3,3)              :: a_inv
    real(dp), dimension(3)                :: spans1, spans2
    real(dp), dimension(3)                :: spanp1, spanp2
    real(dp)                              :: small_x
    real(dp)                              :: small_y

    integer                               :: i1, i2, j1, j2

    real(dp), dimension(2,3)              :: spanp
    real(dp), dimension(3)                :: shft
    real(dp)                              :: half_width, half_height
    real(dp), parameter                   :: zero = 0.0_dp

  continue

      small_x = 10.0_dp*sample(ivol)%window_width*epsilon(real(small_x,dp))
      small_y = 10.0_dp*sample(ivol)%window_height*epsilon(real(small_y,dp))
      half_height = 0.5_dp*sample(ivol)%window_height
      half_width  = 0.5_dp*sample(ivol)%window_width

      i1 = 0
      i2 = 0
      j1 = 0
      j2 = 0

      select case ( schlieren_aspect(ivol) )
        case ( 'y' )
! reference span ( the cell limits ) to the windown "lower"corner in 2D
! this shifts the point
          shft = (/-half_width,zero,-half_height/)
          spanp(1,:) = NewView( span(1,:), sample(ivol)%window_center, shft )
          spanp(2,:) = NewView( span(2,:), sample(ivol)%window_center, shft )

          i1 = max(int((spanp(1,1)-small_x)/sample(ivol)%delta_x)+1,1)
          i2 = min(int((spanp(2,1)+small_x)/sample(ivol)%delta_x)+1, &
                        sample(ivol)%number_of_columns )
          j1 = max(int((spanp(1,3)-small_y)/sample(ivol)%delta_y)+1,1)
          j2 = min(int((spanp(2,3)+small_y)/sample(ivol)%delta_y)+1, &
                            sample(ivol)%number_of_rows )

        case ( 'z' )

          shft = (/-half_width,-half_height,zero/)
          spanp(1,:) = NewView( span(1,:), sample(ivol)%window_center, shft )
          spanp(2,:) = NewView( span(2,:), sample(ivol)%window_center, shft )
          i1 = max(int((spanp(1,1)-small_x)/sample(ivol)%delta_x)+1,1)
          i2 = min(int((spanp(2,1)+small_x)/sample(ivol)%delta_x)+1, &
                        sample(ivol)%number_of_columns )
          j1 = max(int((spanp(1,2)-small_y)/sample(ivol)%delta_y)+1,1)
          j2 = min(int((spanp(2,2)+small_y)/sample(ivol)%delta_y)+1, &
                        sample(ivol)%number_of_rows )

        case default

        a_rot     = reshape((/                                                 &
                              sample(ivol)%phi_hat,                            &
                              sample(ivol)%theta_hat,                          &
                              sample(ivol)%r_hat/),(/3,3/))

        a_inv     = tinverse( a_rot )

        spans1 = span(1,:) - sample(ivol)%window_center(:)
        spanp1 = rotate( spans1, a_inv )

        spans2 = span(2,:) - sample(ivol)%window_center(:)
        spanp2 = rotate( spans2, a_inv )

        i1= max(int(0.5_dp*real(sample(ivol)%number_of_columns-1,dp)           &
          *(1.0_dp+(spanp1(1)-small_x)*2.0_dp/                                 &
                                    sample(ivol)%window_width))+1,1)
        i2= min(int(0.5_dp*real(sample(ivol)%number_of_columns-1,dp)           &
          *(1.0_dp+(spanp2(1)+small_x)*2.0_dp/sample(ivol)%window_width))+1,   &
                                    sample(ivol)%number_of_columns )

        j1= max(int(0.5_dp*real(sample(ivol)%number_of_rows-1,dp)              &
          *(1.0_dp+(spanp1(2)-small_y)*2.0_dp/                                 &
                                    sample(ivol)%window_height))+1,1)
        j2= min(int(0.5_dp*real(sample(ivol)%number_of_rows-1,dp)              &
          *(1.0_dp+(spanp2(2)+small_y)*2.0_dp/sample(ivol)%window_height))+1,  &
                                    sample(ivol)%number_of_rows )

      end select

      i_start = min(i1,i2)
      i_start = max(1,i_start)
      i_fin   = max(i1,i2)
      i_fin   = min(sample(ivol)%number_of_columns,i_fin)
      j_start = min(j1,j2)
      j_start = max(1,j_start)
      j_fin   = max(j1,j2)
      j_fin   = min(sample(ivol)%number_of_rows,j_fin)

  end subroutine get_window_index

!========================== LINE_SURVEY_BLANKING =============================80
!
! Search for element intersected by line
!
!=============================================================================80

  subroutine line_survey_blanking( grid, ivol )

    use allocations,       only : my_alloc_ptr
    use grid_types,        only : grid_type
    use string_utils,      only : list_to_array
    use sampling_headers,  only : init_blanking_list, blanking_list_count      &
                                , verbose, blanking_list, number_of_rows       &
                                , sample

    type(grid_type),                 intent(in)    :: grid
    integer,                         intent(in)    :: ivol

    integer                               :: triangle_index
    integer                               :: quad_index
    integer,  dimension(4)                :: node
    real(dp), dimension(3)                :: po, p1, p2, p3, p4, p
    real(dp), dimension(3)                :: n
    real(dp), dimension(2,3)              :: span
    real(dp)                              :: dst

    integer                               :: ilines, jlines, line_index
    integer                               :: i_start, i_fin, j_start, j_fin
    integer                               :: j, ib
    integer                               :: wall_count
    integer                               :: istop
    logical                               :: intersects_face
    integer, dimension(:), pointer        :: boundary_found
    integer, dimension(:), allocatable    :: local_blanking
    integer, dimension(:), allocatable    :: global_blanking

    logical                               :: is_inside_box

  continue

    if ( sample(ivol)%geo_type_name /= 'schlieren') return

    istop = 0
    wall_count = 0
    nullify ( sample(ivol)%blanking_list )
!-----------------------------------------------------------------------------
init_master: if ( init_blanking_list(ivol) .and. lmpi_master ) then
!
!        allocate ( soln%component( number_of_components ) )
!!-----------------------------------------------------------------------------
blanking_list_conditional: if ( blanking_list_count(ivol) == 0 ) then
        do ib = 1, grid%nbound
          if ( grid%bc(ib)%ibc == 4000 &
          ) wall_count = wall_count + 1
        enddo


        if ( wall_count > 0 ) then
          blanking_list_count(ivol) = wall_count
          if ( .not.associated( sample(ivol)%blanking_list ) )              &
              call my_alloc_ptr( sample(ivol)%blanking_list,                &
                                              blanking_list_count(ivol) )

          wall_count = 0
          do ib = 1, grid%nbound
            if ( grid%bc(ib)%ibc == 4000 &
            ) then
              wall_count = wall_count + 1
              sample(ivol)%blanking_list(wall_count) = ib
            endif
          enddo

          if ( verbose ) then
          write(*,'(a,i10)') 'Number of wall boundaries = ', wall_count
          write(*,'(128(i5))') &
          (sample(ivol)%blanking_list(j),j = 1, blanking_list_count(ivol) )
          endif
        else
          if ( .not.associated( sample(ivol)%blanking_list ) )                 &
              call my_alloc_ptr( sample(ivol)%blanking_list, 1 )
          sample(ivol)%blanking_list = 0
        endif

      else

          if ( verbose ) then
          write(*,'(a,i10)') 'Boundaries input for blanking= ',                &
                                blanking_list_count(ivol)
          endif

        call list_to_array( blanking_list(ivol), boundary_found )

            if ( size(boundary_found) /= blanking_list_count(ivol) ) then
             write(*,'(3a,i0)') 'Character string blanking_list too short ',   &
                   'in namelist file read for requested number of ',           &
                    'boundaries to blank, geometry= ', ivol
            istop = 1
            end if

            nullify ( sample(ivol)%blanking_list )
            if ( .not.associated( sample(ivol)%blanking_list ) ) &
            call my_alloc_ptr( sample(ivol)%blanking_list,       &
                                            blanking_list_count(ivol) )

            boundaries: do j = 1, blanking_list_count(ivol)
              ib = boundary_found(j)
              if ( ib >= 1 .and. ib <= grid%nbound ) then
                   sample(ivol)%blanking_list(j) = ib
               else
                 istop = istop + 1
                 write(*,'(a,i0)') 'For blanking geometry ', ivol
                 write(*,'(2a,i0)') 'Out-of-bounds boundary number specified ',&
                          'in blanking list : ', ib
                 if (istop == 1) then
                   write(*,'(a,i0)') 'Note: valid range is 1 to', grid%nbound
                   write(*,'(2a)') 'Perhaps you lumped boundaries and',        &
                                   'did not account for the change in numbering'
                   write(*,'(2a)') 'Or perhaps you are using mirroring and ',  &
                                  'did not account for the change in numbering.'
                 end if
               end if
             end do boundaries

             deallocate(boundary_found)

       endif blanking_list_conditional

       init_blanking_list(ivol) = .false.

    endif init_master

    call lmpi_conditional_stop( istop, &
                                'sampling_funclib::line_survey_blanking' )

    call lmpi_bcast( blanking_list_count(ivol) )

    if ( .not.lmpi_master ) call my_alloc_ptr( sample(ivol)%blanking_list, &
                             max( 1, blanking_list_count(ivol) ) )
    call lmpi_bcast( sample(ivol)%blanking_list )

    if ( verbose ) then
      write(*,'(a,50i0)') 'Blanking list = ', sample(ivol)%blanking_list
    endif
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
! Loop over boundaries to detect possible blanking by solid surfaces
!-----------------------------------------------------------------------------80
skip_blanking:  if ( blanking_list_count(ivol) > 0 ) then

    allocate( local_blanking ( sample(ivol)%number_of_lines ) )
    allocate( global_blanking ( sample(ivol)%number_of_lines ) )
    local_blanking = 0

    blanking_boundaries: do j = 1, blanking_list_count(ivol)
!----------Triangular Faces----------
      ib = sample(ivol)%blanking_list(j)

      loop_tris : do triangle_index = 1,grid%bc(ib)%nbfacet

! oriented nodes of the triangle
        node(1) = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(triangle_index,1))
        node(2) = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(triangle_index,2))
        node(3) = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(triangle_index,3))
 ! xmin, xmax
        span(1,1) = min( grid%x(node(1)), grid%x(node(2)), grid%x(node(3)) )
        span(2,1) = max( grid%x(node(1)), grid%x(node(2)), grid%x(node(3)) )
 ! ymin, ymax
        span(1,2) = min( grid%y(node(1)), grid%y(node(2)), grid%y(node(3)) )
        span(2,2) = max( grid%y(node(1)), grid%y(node(2)), grid%y(node(3)) )
 ! zmin, zmax
        span(1,3) = min( grid%z(node(1)), grid%z(node(2)), grid%z(node(3)) )
        span(2,3) = max( grid%z(node(1)), grid%z(node(2)), grid%z(node(3)) )

        is_inside_box = is_inside_box_status( ivol, span )
        if ( is_inside_box ) then
! reference span ( the cell limits ) to the windown "lower"corner in 2D
        call get_window_index( ivol, span, i_start, i_fin, j_start, j_fin )
        row_loopt:    do ilines = i_start, i_fin
          column_loopt: do jlines = j_start, j_fin
            intersects_face = .false.
            line_index = (ilines-1)*sample(ivol)%number_of_rows + jlines
            po(1:3)    = sample(ivol)%line_list(1:3,line_index)
            n(1:3)     = sample(ivol)%window_normal(1:3)
            p1(1)      = grid%x(node(1))
            p1(2)      = grid%y(node(1))
            p1(3)      = grid%z(node(1))
            p2(1)      = grid%x(node(2))
            p2(2)      = grid%y(node(2))
            p2(3)      = grid%z(node(2))
            p3(1)      = grid%x(node(3))
            p3(2)      = grid%y(node(3))
            p3(3)      = grid%z(node(3))

        call line_face_int_tri ( p1, p2, p3, po, n, p, dst, intersects_face )

            if ( verbose .and. intersects_face ) then
!              write(6,'(i4,a,2i8,1x,1l1,2i8)')                             &
!              lmpi_id,' line_face_int_tri',ilines,jlines,intersects_face,  &
!              triangle_index, grid%bc(ib)%face_bit(triangle_index)
            endif

            if ( intersects_face ) then
              if ( grid%bc(ib)%face_bit(triangle_index) == 1 ) then
                local_blanking(line_index) = 1
              endif
            endif
          enddo column_loopt
        enddo row_loopt
        endif

      enddo loop_tris

!----------Quadrilateral Faces----------

      loop_quads : do quad_index = 1,grid%bc(ib)%nbfaceq

! oriented nodes of the quad
        node(1) = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(quad_index,1))
        node(2) = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(quad_index,2))
        node(3) = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(quad_index,3))
        node(4) = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(quad_index,4))
 ! xmin, xmax
        span(1,1) = min( grid%x(node(1)), grid%x(node(2)), grid%x(node(3)) )
        span(2,1) = max( grid%x(node(1)), grid%x(node(2)), grid%x(node(3)) )
 ! ymin, ymax
        span(1,2) = min( grid%y(node(1)), grid%y(node(2)), grid%y(node(3)) )
        span(2,2) = max( grid%y(node(1)), grid%y(node(2)), grid%y(node(3)) )
 ! zmin, zmax
        span(1,3) = min( grid%z(node(1)), grid%z(node(2)), grid%z(node(3)) )
        span(2,3) = max( grid%z(node(1)), grid%z(node(2)), grid%z(node(3)) )

        is_inside_box = is_inside_box_status( ivol, span )
        if ( is_inside_box ) then
! reference span ( the cell limits ) to the windown "lower"corner in 2D
        call get_window_index( ivol, span, i_start, i_fin, j_start, j_fin )

        row_loopq: do ilines = i_start, i_fin
          column_loopq: do jlines = j_start, j_fin
            intersects_face = .false.
            line_index = (ilines-1)*number_of_rows(ivol) + jlines
            po(1:3)             = sample(ivol)%line_list(1:3,line_index)
            n(1:3)              = sample(ivol)%window_normal(1:3)
            p1(1) = grid%x(node(1))
            p1(2) = grid%y(node(1))
            p1(3) = grid%z(node(1))
            p2(1) = grid%x(node(2))
            p2(2) = grid%y(node(2))
            p2(3) = grid%z(node(2))
            p3(1) = grid%x(node(3))
            p3(2) = grid%y(node(3))
            p3(3) = grid%z(node(3))
            p4(1) = grid%x(node(4))
            p4(2) = grid%y(node(4))
            p4(3) = grid%z(node(4))

     call line_face_int_quad( p1, p4, p3, p2, po, n, p, dst, intersects_face )

          if ( skeleton > 4 .and. intersects_face ) then
          write(6,'(i4,a,2i8,1x,1l1,2i8)')                     &
                lmpi_id,' line_face_int_quad',ilines,jlines,   &
                intersects_face,                               &
                quad_index, grid%bc(ib)%face_bitq(quad_index)
           endif


            if ( intersects_face ) then
              if ( grid%bc(ib)%face_bitq(quad_index) == 1 ) then
                local_blanking(line_index) = 1
              endif
            endif
          enddo column_loopq
        enddo row_loopq
        endif
      enddo loop_quads

      enddo blanking_boundaries

      call lmpi_reduce( local_blanking, global_blanking )
      call lmpi_bcast( global_blanking )

      sample(ivol)%l2c_local_image = global_blanking

      deallocate( local_blanking )
      deallocate( global_blanking )

    else

      sample(ivol)%l2c_local_image = 0

    endif skip_blanking

  end subroutine line_survey_blanking

!============================== IS_INSIDE_TRIANGLE ===========================80
!
!  Determines if a point is inside a triangle using barycentric coordinates
!
!=============================================================================80
  pure function is_inside_triangle2 ( p, p1, p2, p3 ) result ( is_inside )

    real(dp), dimension(3),   intent(in)  :: p, p1, p2, p3
    logical                               :: is_inside

    real(dp), dimension(3)                :: n
    real(dp), dimension(3)                :: p_in_plane
    real(dp), dimension(3)                :: t1, t2

    continue

    is_inside = .false.

    t1          = p2 - p1
    t2          = p3 - p1
    n           = cross_product(t1,t2) ! plane of triangle
    n           = n / sqrt(dot_product(n,n))
    p_in_plane  = line_plane_point2( p1, p2, p3, p, n )

    is_inside   = is_inside_triangle ( p_in_plane, p1, p2, p3 )

  end function is_inside_triangle2

!============================== IS_INSIDE_QUAD ===============================80
!
!  Determines if a point is inside a quad using barycentric coordinates
!
!=============================================================================80
  pure function is_inside_quad( p, p1, p2, p3, p4 ) result ( inside_quad )

    logical                                 :: inside_quad

    real(dp), dimension(3),   intent(in)    :: p, p1, p2, p3, p4

    logical :: inside_tri1
    logical :: inside_tri2

    continue

    inside_quad = .false.
    inside_tri1 = .false.
    inside_tri2 = .false.

    inside_tri1 = is_inside_triangle2( p, p1, p2, p3 )
    inside_tri2 = is_inside_triangle2( p, p1, p3, p4 )

    if ( inside_tri1 .or. inside_tri2 ) inside_quad = .true.

  end function is_inside_quad

!============================= POINT_STATS ===================================80
!
!  Return barycentric averaged of data
!
!=============================================================================80
  pure function point_stats ( p, p1, p2, p3, f )

    implicit none

    real(dp)                             :: point_stats
    real(dp), dimension(3),   intent(in) :: p, p1, p2, p3
    real(dp), dimension(3),   intent(in) :: f

    real(dp), dimension(3) :: weight

    continue

    weight       = 0.0_dp
    point_stats  = 0.0_dp

    weight      = triangle_bary ( p, p1, p2, p3 )
    point_stats = dot_product( f, weight )

  end function point_stats

!========================== FACE_STATS =======================================80
!
!  Return weighted value for face element
!
!=============================================================================80
  pure function face_stats ( vec_size, node_per_face, px, py, pz, qp )

    use sampling_headers, only : max_node

    implicit none

    integer,                                 intent(in) :: vec_size
    integer,                                 intent(in) :: node_per_face
    real(dp), dimension(0:max_node),         intent(in) :: px, py, pz
    real(dp), dimension(vec_size,max_node),  intent(in) :: qp

    real(dp), dimension(vec_size)                       :: face_stats

    real(dp), dimension(3)                              :: p, p1, p2, p3, p4
    real(dp), dimension(3)                              :: q

    integer                                             :: i

    continue

    face_stats  = 0.0_dp

    p           = (/px(0),py(0),pz(0)/)
    p1          = (/px(1),py(1),pz(1)/)
    p2          = (/px(2),py(2),pz(2)/)
    p3          = (/px(3),py(3),pz(3)/)
    p4          = (/px(4),py(4),pz(4)/)

    select case ( node_per_face )

    case ( 3 )

      do i = 1, vec_size
        q(1:3) = qp(i,1:3)
        face_stats(i) = point_stats( p, p1, p2, p3, q )
      enddo

    case ( 4 )

      if ( is_inside_triangle ( p, p1, p2, p3 ) ) then
        do i = 1, vec_size
          q(1:3) = qp(i,1:3)
          face_stats(i) = point_stats( p, p1, p2, p3, q )
        enddo
      else if ( is_inside_triangle ( p, p1, p3, p4 ) ) then
        do i = 1, vec_size
          q(1) = qp(i,1)
          q(2) = qp(i,3)
          q(3) = qp(i,4)
          face_stats(i) = point_stats( p, p1, p3, p4, q )
        enddo
      else if ( is_inside_triangle ( p, p1, p2, p4 ) ) then
        do i = 1, vec_size
          q(1) = qp(i,1)
          q(2) = qp(i,2)
          q(3) = qp(i,4)
          face_stats(i) = point_stats( p, p1, p2, p4, q )
        enddo
      else if ( is_inside_triangle ( p, p2, p3, p4 ) ) then
        do i = 1, vec_size
          q(1) = qp(i,2)
          q(2) = qp(i,3)
          q(3) = qp(i,4)
          face_stats(i) = point_stats( p, p2, p3, p4, q )
        enddo
      endif

    end select

  end function face_stats


!========================== FACE_STATS =======================================80
!
!  Return face centroid
!  Locations 1,2,3 -- x, y, z
!  Location 5      -- rho
!  Locattions 6,7,8 -- gradients of rho
!
!=============================================================================80

  pure function face_gradrho ( px, py, pz, pf, qp, grad_rho )

    implicit none

    real(dp), dimension(8)               :: face_gradrho
    real(dp), dimension(3),   intent(in) :: px, py, pz
    real(dp), dimension(3),   intent(in) :: pf
    real(dp), dimension(5,3), intent(in) :: qp
    real(dp), dimension(3,3), intent(in) :: grad_rho

    real(dp), dimension(3) :: p1, p2, p3, p, weight
    real(dp), dimension(3) :: v0, v1, v2

    integer                :: node
    integer                :: node_per_face
    real(dp)               :: l1, l2, l3, weight_sum, denominator
    real(dp)               :: rho, gradx, grady, gradz

    real(dp), parameter :: zero = 0.0_dp

    continue

    node_per_face = 3

    face_gradrho  = 0.0_dp
    weight        = 0.0_dp
    weight_sum    = 0.0_dp
    denominator   = 0.0_dp
    l1            = 0.0_dp
    l2            = 0.0_dp
    l3            = 0.0_dp

    p1            = (/px(1),py(1),pz(1)/)
    p2            = (/px(2),py(2),pz(2)/)
    p3            = (/px(3),py(3),pz(3)/)
    p             = (/pf(1),pf(2),pf(3)/)
    v0            = p2 - p1
    v1            = p3 - p1
    v2            = p  - p1
    denominator   = dot_product(v0,v0)*dot_product(v1,v1)                      &
                  - dot_product(v0,v1)*dot_product(v1,v0)
    if ( denominator /= zero ) then
      l1 = ( dot_product(v1,v1)*dot_product(v2,v0)                             &
            -dot_product(v1,v0)*dot_product(v2,v1) ) / denominator
      l2 = ( dot_product(v0,v0)*dot_product(v2,v1)                             &
            -dot_product(v0,v1)*dot_product(v2,v0) ) / denominator
      l3 = 1.0_dp - l2 - l1
    endif
    weight(1) = l1
    weight(2) = l2
    weight(3) = l3
    weight_sum = l1 + l2 + l3

    face_gradrho(1) = pf(1)
    face_gradrho(2) = pf(2)
    face_gradrho(3) = pf(3)
    rho   = 0.0_dp
    gradx = 0.0_dp
    grady = 0.0_dp
    gradz = 0.0_dp
    do node = 1, node_per_face
      rho   = rho   +       qp(1,node) * weight(node)
      gradx = gradx + grad_rho(1,node) * weight(node)
      grady = grady + grad_rho(2,node) * weight(node)
      gradz = gradz + grad_rho(3,node) * weight(node)
    enddo

    face_gradrho(5)   = rho
    face_gradrho(6)   = gradx
    face_gradrho(7)   = grady
    face_gradrho(8)   = gradz

    face_gradrho(5:8) = face_gradrho(5:8) / weight_sum

  end function face_gradrho

!========================== IS_FACE_HERE_OR_THERE ============================80
!
!  Sets min and max distances and face indicies
!
!=============================================================================80
  subroutine is_face_here_or_there( face_index, dist, max_dist,          &
                                  min_dist, max_face, min_face)

    integer,  intent(in)    :: face_index
    real(dp), intent(in)    :: dist
    real(dp), intent(inout) :: max_dist
    real(dp), intent(inout) :: min_dist
    integer,  intent(inout) :: max_face
    integer,  intent(inout) :: min_face

    real(dp), parameter     :: big_one   = huge(1.0)

  continue

    if ( dist < big_one ) then

      if ( dist > max_dist ) then
        max_dist = dist
        max_face = face_index
      endif

      if ( dist < min_dist ) then
        min_dist = dist
        min_face = face_index
      endif

    endif

  end subroutine is_face_here_or_there

!=============================================================================80
!
!  Return cell min and max extents (x,y,z)
!
!=============================================================================80
  function get_span ( grid, element_set, cell, node_per_cell ) result ( span )

    use grid_types,        only : grid_type

    real(dp), dimension(2,3)      :: span
    type(grid_type), intent(in)   :: grid
    integer,         intent(in)   :: element_set
    integer,         intent(in)   :: cell
    integer,         intent(in)   :: node_per_cell

    integer                  :: n1, n2, n3, n4, n5, n6, n7, n8

  continue

    span = 0.0_dp

    n1   = grid%elem(element_set)%c2n(1,cell)
    n2   = grid%elem(element_set)%c2n(2,cell)
    n3   = grid%elem(element_set)%c2n(3,cell)
    n4   = grid%elem(element_set)%c2n(4,cell)
! xmin, xmax
    span(1,1) = min( grid%x(n1), grid%x(n2), grid%x(n3), grid%x(n4))
    span(2,1) = max( grid%x(n1), grid%x(n2), grid%x(n3), grid%x(n4))
! ymin, ymax
    span(1,2) = min( grid%y(n1), grid%y(n2), grid%y(n3), grid%y(n4))
    span(2,2) = max( grid%y(n1), grid%y(n2), grid%y(n3), grid%y(n4))
! zmin, zmax
    span(1,3) = min( grid%z(n1), grid%z(n2), grid%z(n3), grid%z(n4))
    span(2,3) = max( grid%z(n1), grid%z(n2), grid%z(n3), grid%z(n4))

    if ( node_per_cell > 4 ) then

      n5   = grid%elem(element_set)%c2n(5,cell)
      span(1,1) = min( span(1,1), grid%x(n5) )
      span(2,1) = max( span(2,1), grid%x(n5) )
      span(1,2) = min( span(1,2), grid%y(n5) )
      span(2,2) = max( span(2,2), grid%y(n5) )
      span(1,3) = min( span(1,3), grid%z(n5) )
      span(2,3) = max( span(2,3), grid%z(n5) )

    endif

    if ( node_per_cell > 5 ) then

      n6   = grid%elem(element_set)%c2n(6,cell)
      span(1,1) = min( span(1,1), grid%x(n6) )
      span(2,1) = max( span(2,1), grid%x(n6) )
      span(1,2) = min( span(1,2), grid%y(n6) )
      span(2,2) = max( span(2,2), grid%y(n6) )
      span(1,3) = min( span(1,3), grid%z(n6) )
      span(2,3) = max( span(2,3), grid%z(n6) )

    endif

    if ( node_per_cell > 6 ) then

      n7   = grid%elem(element_set)%c2n(7,cell)
      n8   = grid%elem(element_set)%c2n(8,cell)
      span(1,1) = min( span(1,1), grid%x(n7), grid%x(n8) )
      span(2,1) = max( span(2,1), grid%x(n7), grid%x(n8) )
      span(1,2) = min( span(1,2), grid%y(n7), grid%y(n8) )
      span(2,2) = max( span(2,2), grid%y(n7), grid%y(n8) )
      span(1,3) = min( span(1,3), grid%z(n7), grid%z(n8) )
      span(2,3) = max( span(2,3), grid%z(n7), grid%z(n8) )

    endif

  end function get_span

!========================== IS_INSIDE_BOX_STATUS =============================80
!
!  Determines if a cell is inside the search box
!
!=============================================================================80
  logical function is_inside_box_status ( ivol, span )

    use sampling_headers, only : sample

    integer,                         intent(in) :: ivol
    real(dp),        dimension(2,3), intent(in) :: span

    real(dp), dimension(1:3) :: p1, p8
    real(dp)                 :: xmin, xmax
    real(dp)                 :: ymin, ymax
    real(dp)                 :: zmin, zmax

  continue

    is_inside_box_status = .false.

    p1 = sample(ivol)%window_box(:,1) ! xmin, ymin, zmin
!   p2 = sample(ivol)%window_box(:,2)
!   p3 = sample(ivol)%window_box(:,3)
!   p4 = sample(ivol)%window_box(:,4)
!   p5 = sample(ivol)%window_box(:,5)
!   p6 = sample(ivol)%window_box(:,6)
!   p7 = sample(ivol)%window_box(:,7)
    p8 = sample(ivol)%window_box(:,8) ! xmax, ymax, zmax

    xmin = min(p1(1),p8(1))
    xmax = max(p1(1),p8(1))
    ymin = min(p1(2),p8(2))
    ymax = max(p1(2),p8(2))
    zmin = min(p1(3),p8(3))
    zmax = max(p1(3),p8(3))

      if ( ( span(1,1) > xmin .and. span(2,1) < xmax )                   &
         .and.                                                           &
           ( span(1,2) > ymin .and. span(2,2) < ymax )                   &
         .and.                                                           &
           ( span(1,3) > zmin .and. span(2,3) < zmax )                   &
         ) is_inside_box_status = .true.

  end function is_inside_box_status

!================================ ISO_STATS ==================================80
!
! Determine the interpolation donor nodes and weights within the partition
! for each node of an isosurface cut triangle
!
!=============================================================================80
  subroutine iso_stats( iso_val, iso_variable, cut_edge_map,                   &
                        cell, edge_per_cell,                                   &
                        grid, soln, c2n, c2e, n_tria,                          &
                        local_e2n,                                             &
                        local_trinode_map, n_edges, e2ln,                      &
                        edge_donor_list, edge_donor_weight, sadj )

    use allocations,     only : my_realloc_ptr
    use grid_types,      only : grid_type
    use solution_types,  only : soln_type
    use solution_adj,    only : sadj_type

    real(dp),                   intent(in   ) :: iso_val
    character(len=80),          intent(in   ) :: iso_variable
    integer,  dimension(:),     intent(in   ) :: cut_edge_map
    integer,                    intent(in   ) :: cell
    integer,                    intent(in   ) :: edge_per_cell
    type(grid_type),            intent(in   ) :: grid
    type(soln_type),            intent(in   ) :: soln
    type(sadj_type), optional,  intent(in   ) :: sadj
    integer,  dimension(:,:),   intent(in   ) :: c2n
    integer,  dimension(:,:),   intent(in   ) :: c2e
    integer,                    intent(inout) :: n_tria


    integer, dimension(edge_per_cell,2), intent(in) :: local_e2n
    integer,                    intent(inout) :: n_edges
    integer,  dimension(:),     intent(inout) :: e2ln
    integer,  dimension(:,:),   pointer       :: local_trinode_map
    integer,  dimension(:,:),   intent(inout) :: edge_donor_list
    real(dp), dimension(:),     intent(inout) :: edge_donor_weight

    integer                                   :: node, edge
    integer                                   :: node1, node2
    integer                                   :: test_dim, new_dim

    real(dp)                                  :: iso1, iso2
    real(dp)                                  :: t
    real(dp), dimension(3)                    :: edge_vector

  continue

    node1 = 0
    node2 = 0

    n_tria = n_tria + 1

!   test to make sure we have enough memory for all triangles
!   if not, bump it up by 25%

    test_dim  = size(local_trinode_map,2)

    if ( n_tria > test_dim ) then

      new_dim = test_dim*1.25_dp

      call my_realloc_ptr(local_trinode_map, 4, new_dim)

    end if

    node = 0

    fill_triangle_nodes : do edge = 1, edge_per_cell

      edge_is_cut:  if (1==cut_edge_map(edge)) then

        node  = node + 1
        node1 = c2n(local_e2n(edge,1),cell)   ! parition node number
        node2 = c2n(local_e2n(edge,2),cell)

        edge_vector = get_edge_vector ( grid, node1, node2 )
        call iso_nodal_values(iso_variable, node1, node2, edge_vector          &
                         , soln, iso1, iso2,sadj)

        t = (iso1 - iso_val) / (iso1 - iso2)

        if ( e2ln(c2e(edge,cell)) == 0 ) then
          n_edges                        = n_edges + 1
          local_trinode_map(node,n_tria) = n_edges
          e2ln(c2e(edge,cell))           = n_edges
          edge_donor_weight(n_edges)     = 1.0_dp - t
          edge_donor_list  (1,n_edges)   = node1
          edge_donor_list  (2,n_edges)   = node2

          if ( n_edges > size(edge_donor_list) ) then
            write(*,*) 'Stopping, n_edges great than edge_donor_list dimension'
            call lmpi_conditional_stop(1,'funclib90::iso_stats')
          endif
          if ( c2e(edge,cell) > size(e2ln) ) then
            write(*,*) 'Stopping, c2e too big'
            call lmpi_conditional_stop(1,'funclib90::iso_stats')
          endif
        else
          local_trinode_map(node,n_tria)  = e2ln(c2e(edge,cell))
        endif

      end if edge_is_cut

    end do fill_triangle_nodes

    local_trinode_map(4,n_tria)   = local_trinode_map(3,n_tria)

  end subroutine iso_stats

!================================ ISO_CRINKLE ================================80
!
! Determine the interpolation donor nodes and weights within the partition
! for each node of an isosurface cut triangle
!
!=============================================================================80
  subroutine iso_crinkle( iso_val, iso_variable,                               &
                        cut_edges, cut_edge_map,                               &
                        cell, edge_per_cell, face_per_cell,                    &
                        soln, c2n, n_tria, local_f2n,                          &
                        c2e, local_e2n, e2ln,                                  &
                        local_trinode_map, n_edges, p2ln,                      &
                        edge_donor_list, edge_donor_weight, grid, sadj )

    use allocations,     only : my_realloc_ptr
    use solution_types,  only : soln_type
    use grid_types,      only : grid_type
    use solution_adj,    only : sadj_type

    real(dp),                          intent(in   ) :: iso_val
    character(len=80),                 intent(in   ) :: iso_variable
    integer,                           intent(in   ) :: edge_per_cell
    integer,                           intent(in   ) :: cut_edges
    integer, dimension(edge_per_cell), intent(in   ) :: cut_edge_map
    integer,                           intent(in   ) :: cell
    integer,                           intent(in   ) :: face_per_cell
    type(soln_type),                   intent(in   ) :: soln
    type(sadj_type), optional,         intent(in   ) :: sadj
    type(grid_type),                   intent(in   ) :: grid
    integer, dimension(:,:),           intent(in   ) :: c2n
    integer,                           intent(inout) :: n_tria
    integer, dimension(:,:),   intent(in)    :: local_f2n
    integer, dimension(:,:),   pointer       :: local_trinode_map
    integer,                           intent(inout) :: n_edges
    integer,  dimension(:),    intent(inout) :: p2ln
    integer,  dimension(:,:),  intent(inout) :: edge_donor_list
    real(dp), dimension(:),     intent(inout) :: edge_donor_weight

    integer, dimension(edge_per_cell,2), intent(in) :: local_e2n
    integer,  dimension(:,:),   intent(in   ) :: c2e
    integer,  dimension(:),     intent(inout) :: e2ln

    integer                                   :: corner, face
    integer                                   :: total_faces
    integer                                   :: test_dim, new_dim
    integer                                   :: lnode
    integer                                   :: number_of_nodes
    integer                                   :: node
    integer                                   :: node1, node2
    integer                                   :: edge
    integer, dimension(face_per_cell)         :: faces
    real(dp)                                  :: iso1, iso2, avg_val
    real(dp), dimension(3)                    :: edge_vector
    logical :: write_cells

  continue
    write_cells = .false.
! Re-using variable n_edges for number of nodes in the face-crinkle map

!    if ( cut_edges >= 4 ) return
!    if ( edge_per_cell > 6 ) return

! Find the faces "opposite" the cut edges of the element
    total_faces = 0
    faces       = opposite_faces ( cut_edge_map, face_per_cell, edge_per_cell )
    do face = 1, face_per_cell
      if (faces(face)==cut_edges) total_faces = total_faces + 1
    enddo

    if ( lmpi_id ==3 .and. write_cells ) then
       if ( cell >= 7585 .and. cell <= 7610 ) then
    write(1010,*) 'variables=x,y,z,mach'
    write(1010,*) 'zone,i=4,j=1,f=fepoint,et=tetrahedron'
      do node = 1, 4
        lnode = c2n(node,cell)
        edge_vector = get_edge_vector ( grid, lnode, lnode )
        call iso_nodal_values(iso_variable, lnode, lnode, edge_vector, soln  &
                            ,  iso1, iso2,sadj)
      write(1010,' (10(1x,es12.5))' ) &
grid%x(c2n(node,cell)), grid%y(c2n(node,cell)),grid%z(c2n(node,cell)) ,iso1
      enddo
      write(1010,* ) '1,2,3,4'
    endif
    endif
! If there are no faces opposite the cut edges, just return
! This will happen, for example, with a tet having 4 cut edges
    if ( total_faces == 0 ) return

!   test to make sure we have enough memory for all triangles
!   if not, bump it up by 25%
    test_dim  = size(local_trinode_map,2)
    if ( n_tria > test_dim ) then
      new_dim = test_dim*1.25_dp
      call my_realloc_ptr(local_trinode_map, 4, new_dim)
    end if

    faces_loop: do face = 1, face_per_cell

      number_of_nodes = 4
      if ( local_f2n(face,1) == local_f2n(face,4) ) number_of_nodes = 3

      if ( face_per_cell == 4 ) then
        if ( face == 1 ) node = 4
        if ( face == 2 ) node = 1
        if ( face == 3 ) node = 2
        if ( face == 4 ) node = 3
      endif

      path1:  if ( faces(face) /= cut_edges ) then
      cycle
!----------------------------------------------------------------------
! work through each face and save the un-cut edges
!----------------------------------------------------------------------
!----------------------------------------------------------------------
      tets: if ( face_per_cell == 4 ) then
        select case ( face )
        case ( 1 )
               if (      cut_edge_map(1)==1 .and. cut_edge_map(2)==1 ) then
                 edge = 4
               else if ( cut_edge_map(1)==1 .and. cut_edge_map(4)==1 ) then
                 edge = 2
               else if ( cut_edge_map(2)==1 .and. cut_edge_map(4)==1 ) then
                 edge = 1
               endif
        case ( 2 )
               if (      cut_edge_map(4)==1 .and. cut_edge_map(6)==1 ) then
                 edge = 5
               else if ( cut_edge_map(6)==1 .and. cut_edge_map(5)==1 ) then
                 edge = 4
               else if ( cut_edge_map(5)==1 .and. cut_edge_map(4)==1 ) then
                 edge = 6
               endif
        case ( 3 )
               if (      cut_edge_map(3)==1 .and. cut_edge_map(6)==1 ) then
                 edge = 2
               else if ( cut_edge_map(6)==1 .and. cut_edge_map(2)==1 ) then
                 edge = 3
               else if ( cut_edge_map(2)==1 .and. cut_edge_map(3)==1 ) then
                 edge = 6
               endif
        case ( 4 )
               if (      cut_edge_map(1)==1 .and. cut_edge_map(5)==1 ) then
                 edge = 3
               else if ( cut_edge_map(5)==1 .and. cut_edge_map(3)==1 ) then
                 edge = 1
               else if ( cut_edge_map(3)==1 .and. cut_edge_map(1)==1 ) then
                 edge = 5
               endif
        end select

        node1 = c2n(local_e2n(edge,1),cell)
        node2 = c2n(local_e2n(edge,2),cell)
        edge_vector = get_edge_vector ( grid, node1, node2 )
        call iso_nodal_values(iso_variable, node1, node2, edge_vector, soln  &
                             , iso1, iso2,sadj)
! Determine, before incrementing the triangle/quad counter, if the element
! face sits "below" the iso-surface value or "above" the iso-surface value
        if ( iso1 < iso_val .and. iso2 < iso_val ) then
          n_tria = n_tria + 1
          if ( p2ln(node1) == 0 ) then
            n_edges                          = n_edges + 1
            p2ln(node1)                      = n_edges
            local_trinode_map(1,n_tria) = n_edges
            edge_donor_list(1,n_edges)       = node1
            edge_donor_list(2,n_edges)       = node1
          else
            local_trinode_map(1,n_tria) = p2ln(node1)
          endif
          if ( p2ln(node2) == 0 ) then
            n_edges                          = n_edges + 1
            p2ln(node2)                      = n_edges
            local_trinode_map(2,n_tria) = n_edges
            edge_donor_list(1,n_edges)       = node2
            edge_donor_list(2,n_edges)       = node2
          else
            local_trinode_map(2,n_tria) = p2ln(node2)
          endif
          local_trinode_map(3,n_tria)   = local_trinode_map(2,n_tria)
          local_trinode_map(4,n_tria)   = local_trinode_map(3,n_tria)

          if ( e2ln(c2e(edge,cell)) == 0 ) then
            n_edges                        = n_edges + 1
            local_trinode_map(node,n_tria) = n_edges
            e2ln(c2e(edge,cell))           = n_edges
            edge_donor_weight(n_edges)     = 0.5
            edge_donor_list  (1,n_edges)   = node1
            edge_donor_list  (2,n_edges)   = node2
          else
            local_trinode_map(node,n_tria)  = e2ln(c2e(edge,cell))
          endif
        endif
        endif tets

      else path1
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------

      avg_val = 0.0_dp
      corner_loop_0:  do corner = 1, number_of_nodes
        lnode = c2n(local_f2n(face,corner),cell)   ! parition node number
        edge_vector = get_edge_vector ( grid, lnode, lnode )
        call iso_nodal_values(iso_variable, lnode, lnode, edge_vector, soln  &
                             , iso1, iso2,sadj)
        avg_val = avg_val + iso1/real(number_of_nodes,dp)
      enddo corner_loop_0
! This way only one of the two face-based crinkles will get picked up
! Removing test_iso_side conditional will give both the inner and outer
! face-based crinkles
      test_iso_side: if ( avg_val < iso_val ) then
! proceed with constructing a new face to the crinkle
        n_tria = n_tria + 1
        corner_loop:  do corner = 1, number_of_nodes
          lnode = c2n(local_f2n(face,corner),cell)   ! parition node number
          if ( p2ln(lnode) == 0 ) then
            n_edges                          = n_edges + 1
            p2ln(lnode)                      = n_edges
            local_trinode_map(corner,n_tria) = n_edges
            edge_donor_list(1,n_edges)       = lnode
            edge_donor_list(2,n_edges)       = lnode
          else
            local_trinode_map(corner,n_tria) = p2ln(lnode)
         endif


        enddo corner_loop

        if ( number_of_nodes == 3 ) then
           local_trinode_map(4,n_tria)   = local_trinode_map(3,n_tria)
        endif

      endif test_iso_side
      endif path1
    enddo faces_loop

  end subroutine iso_crinkle

!=============================== GET_OPPOSITE_FACES ==========================80
!
!
!
!
!=============================================================================80
  function opposite_faces( cut_edge_map, face_per_cell, edge_per_cell ) &
                  result ( cut_faces )

    integer,                intent(in) :: face_per_cell
    integer,                intent(in) :: edge_per_cell
    integer,  dimension(face_per_cell) :: cut_faces
    integer,  dimension(edge_per_cell), intent(in) :: cut_edge_map

    integer, parameter, dimension ( 9, 5 ) :: e2f_prz =                     &
    reshape((/                                                              &
          1, 1, 1, 0, 0, 1, 0, 1, 1                                         &
        , 1, 0, 0, 1, 1, 1, 1, 0, 1                                         &
        , 0, 0, 1, 0, 1, 0, 1, 1, 1                                         &
        , 0, 1, 0, 1, 0, 1, 1, 1, 0                                         &
        , 1, 1, 1, 1, 1, 0, 0, 0, 0                                         &
          /),(/9, 5/))

    integer, parameter, dimension ( 6, 4 ) :: e2f_tet =                     &
    reshape((/                                                              &
          0, 0, 1, 0, 1, 1                                                  &
        , 1, 1, 1, 0, 0, 0                                                  &
        , 1, 0, 0, 1, 1, 0                                                  &
        , 0, 1, 0, 1, 0, 1                                                  &
          /),(/6, 4/))

    integer                           :: edge
!    integer                           :: total_cuts

  continue

!    total_cuts = sum(cut_edge_map)
    cut_faces  = 0
! For tets
! node 1 connects edges 1,2,3 and is opposite face 2
!      2 connects edges 1,4,5 and is opposite face 3
!      3 connects edges 2,4,6 and is opposite face 4
!      4 connects edges 3,5,6 and is opposite face 1
    select case ( edge_per_cell )
    case ( 6 )
      do edge = 1, edge_per_cell
        if ( cut_edge_map(edge) == 1 ) then
          cut_faces = cut_faces + e2f_tet(edge,1:4)
        endif
      enddo
    case ( 9 )
      do edge = 1, edge_per_cell
        if ( cut_edge_map(edge) == 1 ) then
          cut_faces = cut_faces + e2f_prz(edge,1:5)
        endif
      enddo
    case default
    end select

  end function opposite_faces

!======================= GET_EDGE_VECTOR =====================================80
!
! Caculate the edge vector determined by two points
!
!=============================================================================80
  function get_edge_vector ( grid, node1, node2 ) result ( edge_vector )

    use grid_types,  only : grid_type

    real(dp), dimension(3)      :: edge_vector

    type(grid_type), intent(in) :: grid
    integer,         intent(in) :: node1
    integer,         intent(in) :: node2

    continue

    edge_vector(1) = grid%x(node2) - grid%x(node1)
    edge_vector(2) = grid%y(node2) - grid%y(node1)
    edge_vector(3) = grid%z(node2) - grid%z(node1)

  end function get_edge_vector

!=============================================================================80
!
! Determine the interpolation donor nodes and weights within the partition
! for each node of an isosurface cut triangle
!
!=============================================================================80
  function edge_zero_iso ( iso_val, iso_variable, cell, edge, edge_per_cell    &
                         , local_e2n, c2n, grid, soln, sadj ) result ( t )

    use solution_types,  only : soln_type
    use grid_types,      only : grid_type
    use solution_adj,    only : sadj_type

    real(dp)                               :: t

    real(dp),                            intent(in) :: iso_val
    character(len=80),                   intent(in) :: iso_variable
    integer,                             intent(in) :: cell
    integer,                             intent(in) :: edge
    integer,                             intent(in) :: edge_per_cell
    integer, dimension(edge_per_cell,2), intent(in) :: local_e2n
    integer, dimension(:,:),             intent(in) :: c2n
    type(soln_type),                     intent(in) :: soln
    type(grid_type),                     intent(in) :: grid
    type(sadj_type), optional,           intent(in) :: sadj

    integer                                   :: node1, node2
    real(dp)                                  :: iso1, iso2
    real(dp), dimension(3)                    :: edge_vector

  continue

    node1 = c2n(local_e2n(edge,1),cell)    ! parition node number
    node2 = c2n(local_e2n(edge,2),cell)

    edge_vector = get_edge_vector ( grid, node1, node2 )
    call iso_nodal_values( iso_variable, node1, node2, edge_vector, soln       &
                         , iso1, iso2, sadj)

    t = ( iso1 - iso_val ) / ( iso1 - iso2 )

  end function edge_zero_iso

!=============================== ISO_NODAL_VALUES ============================80
!
! Determine the isosurface parameter solution at node1 and node2
!
!=============================================================================80
  subroutine iso_nodal_values( iso_variable,                                   &
                               node1,                                          &
                               node2,                                          &
                               edge_vector,                                    &
                               soln,                                           &
                               iso1,                                           &
                               iso2,                                           &
                               sadj )

    use solution_types,       only : soln_type
    use fluid,                only : gamma
    use nml_time_avg_params,  only : itime_avg
    use solution_adj,         only : sadj_type

    character(len=80),       intent(in   ) :: iso_variable
    integer,                 intent(in   ) :: node1
    integer,                 intent(in   ) :: node2
    type(soln_type),         intent(in   ) :: soln
    real(dp), dimension(3),  intent(in   ) :: edge_vector
    real(dp),                intent(inout) :: iso1
    real(dp),                intent(inout) :: iso2
    type(sadj_type), optional, intent(in)  :: sadj

    real(dp) :: rho1, rho2
    real(dp) :: u1, u2
    real(dp) :: v1, v2
    real(dp) :: w1, w2
    real(dp) :: pr1, pr2
    real(dp) :: vort_x1, vort_x2
    real(dp) :: vort_y1, vort_y2
    real(dp) :: vort_z1, vort_z2
    real(dp) :: rho_tavg1, rho_tavg2
    real(dp) :: u_tavg1, u_tavg2
    real(dp) :: v_tavg1, v_tavg2
    real(dp) :: w_tavg1, w_tavg2
    real(dp) :: p_tavg1, p_tavg2
    real(dp) :: rho_trms1, rho_trms2
    real(dp) :: u_trms1, u_trms2
    real(dp) :: v_trms1, v_trms2
    real(dp) :: w_trms1, w_trms2
    real(dp) :: p_trms1, p_trms2
    real(dp) :: bird_b1, bird_b2
    real(dp) :: rlam11, rlam12, rlam13, rlam14, rlam15, rlam16, rlam17
    real(dp) :: rlam21, rlam22, rlam23, rlam24, rlam25, rlam26, rlam27
    real(dp) :: r11, r2r1, r2i1, r3r1, r3i1
    real(dp) :: r12, r2r2, r2i2, r3r2, r3i2
    real(dp) :: proc_id1, proc_id2
    real(dp) :: vort_mag_avg1, vort_mag_avg2
    real(dp) :: vort_mag_rms1, vort_mag_rms2

    real(dp), dimension(3,3) :: gradv1, gradv2, asq_1, asq_2
    real(dp), dimension(2)   :: pf, qf, rf
    real(dp)                 :: d1, d2
    real(dp)                 :: conversion, d, mass, vgradrho, coll_freq
    real(dp)                 :: rho, pressure, temperature
    real(dp), dimension(3)   :: delta_velocity
    real(dp), dimension(3,3) :: vel_grad
    real(dp), dimension(3)   :: vort_avg
    real(dp), dimension(3)   :: vort_rms
    real(dp)                 :: disi

    real(dp), parameter  :: zero = 0.0_dp
    real(dp), parameter  :: half = 0.5_dp
    real(dp), parameter  :: one  = 1.0_dp
    real(dp)             :: pi

    continue

    pi = acos(-one)

    rlam11 = zero
    rlam12 = zero
    rlam13 = zero
    rlam14 = zero
    rlam15 = zero
    rlam16 = zero
    rlam17 = zero

    rlam21 = zero
    rlam22 = zero
    rlam23 = zero
    rlam24 = zero
    rlam25 = zero
    rlam26 = zero
    rlam27 = zero
    vort_mag_avg1 = zero
    vort_mag_avg2 = zero
    vort_mag_rms1 = zero
    vort_mag_rms2 = zero
    p_trms2       = zero

    disi    = sqrt(dot_product(edge_vector,edge_vector))
    if ( disi > epsilon(0.0_dp) ) disi = one / disi

    rho1    = soln%q_dof(1,node1)
    u1      = soln%q_dof(2,node1)
    v1      = soln%q_dof(3,node1)
    w1      = soln%q_dof(4,node1)
    pr1     = soln%q_dof(5,node1)

    rho2    = soln%q_dof(1,node2)
    u2      = soln%q_dof(2,node2)
    v2      = soln%q_dof(3,node2)
    w2      = soln%q_dof(4,node2)
    pr2     = soln%q_dof(5,node2)

    vort_x1 = soln%grady(4,node1)-soln%gradz(3,node1)
    vort_y1 = soln%gradz(2,node1)-soln%gradx(4,node1)
    vort_z1 = soln%gradx(3,node1)-soln%grady(2,node1)

    vort_x2 = soln%grady(4,node2)-soln%gradz(3,node2)
    vort_y2 = soln%gradz(2,node2)-soln%gradx(4,node2)
    vort_z2 = soln%gradx(3,node2)-soln%grady(2,node2)

    if ( present(sadj) ) then
      rlam11 = sadj%rlam(1,node1,1)
      rlam12 = sadj%rlam(2,node1,1)
      rlam13 = sadj%rlam(3,node1,1)
      if ( soln%adim >= 4 ) rlam14 = sadj%rlam(4,node1,1)
      if ( soln%adim >= 5 ) rlam15 = sadj%rlam(5,node1,1)
      if ( soln%adim >= 6 ) rlam16 = sadj%rlam(6,node1,1)
      if ( soln%adim >= 7 ) rlam17 = sadj%rlam(7,node1,1)
      rlam21 = sadj%rlam(1,node2,1)
      rlam22 = sadj%rlam(2,node2,1)
      rlam23 = sadj%rlam(3,node2,1)
      if ( soln%adim >= 4 ) rlam24 = sadj%rlam(4,node2,1)
      if ( soln%adim >= 5 ) rlam25 = sadj%rlam(5,node2,1)
      if ( soln%adim >= 6 ) rlam26 = sadj%rlam(6,node2,1)
      if ( soln%adim >= 7 ) rlam27 = sadj%rlam(7,node2,1)
    endif

    proc_id1 = lmpi_id
    proc_id2 = lmpi_id

!             time average and time rms data only for compressible path
        rho_tavg1 = 0.0_dp
        u_tavg1   = 0.0_dp
        v_tavg1   = 0.0_dp
        w_tavg1   = 0.0_dp
        p_tavg1   = 0.0_dp
        rho_trms1 = 0.0_dp
        u_trms1   = 0.0_dp
        v_trms1   = 0.0_dp
        w_trms1   = 0.0_dp
        p_trms1   = 0.0_dp
        bird_b1   = 0.0_dp

        rho_tavg2 = 0.0_dp
        u_tavg2   = 0.0_dp
        v_tavg2   = 0.0_dp
        w_tavg2   = 0.0_dp
        p_tavg2   = 0.0_dp
        rho_trms2 = 0.0_dp
        u_trms2   = 0.0_dp
        v_trms2   = 0.0_dp
        w_trms2   = 0.0_dp
        bird_b2   = 0.0_dp


              if (itime_avg /= 0) then

    rho_tavg1  = soln%q_time_avg(1,node1)
    u_tavg1    = soln%q_time_avg(2,node1)
    v_tavg1    = soln%q_time_avg(3,node1)
    w_tavg1    = soln%q_time_avg(4,node1)
    p_tavg1    = soln%q_time_avg(5,node1)
    rho_trms1  = sqrt(abs(soln%q_time_avg(6,node1)  - rho_tavg1**2))
    u_trms1    = sqrt(abs(soln%q_time_avg(7,node1)  - u_tavg1**2))
    v_trms1    = sqrt(abs(soln%q_time_avg(8,node1)  - v_tavg1**2))
    w_trms1    = sqrt(abs(soln%q_time_avg(9,node1)  - w_tavg1**2))
    p_trms1    = sqrt(abs(soln%q_time_avg(10,node1) - p_tavg1**2))

    rho_tavg2  = soln%q_time_avg(1,node2)
    u_tavg2    = soln%q_time_avg(2,node2)
    v_tavg2    = soln%q_time_avg(3,node2)
    w_tavg2    = soln%q_time_avg(4,node2)
    p_tavg2    = soln%q_time_avg(5,node2)
    rho_trms2  = sqrt(abs(soln%q_time_avg(6,node2)  - rho_tavg2**2))
    u_trms2    = sqrt(abs(soln%q_time_avg(7,node2)  - u_tavg2**2))
    v_trms2    = sqrt(abs(soln%q_time_avg(8,node2)  - v_tavg2**2))
    w_trms2    = sqrt(abs(soln%q_time_avg(9,node2)  - w_tavg2**2))
    p_trms2    = sqrt(abs(soln%q_time_avg(10,node2) - p_tavg2**2))

    delta_velocity(1)   = u_tavg2 - u_tavg1
    delta_velocity(2)   = v_tavg2 - v_tavg1
    delta_velocity(3)   = w_tavg2 - w_tavg1
    vel_grad(1,1:3)     = delta_velocity(1) * edge_vector(1:3)
    vel_grad(2,1:3)     = delta_velocity(2) * edge_vector(1:3)
    vel_grad(3,1:3)     = delta_velocity(3) * edge_vector(1:3)
    vort_avg(1)         = vel_grad(3,2) - vel_grad(2,3)
    vort_avg(2)         = vel_grad(1,3) - vel_grad(3,1)
    vort_avg(3)         = vel_grad(2,1) - vel_grad(1,2)
    vort_mag_avg1       = sqrt( vort_avg(1) * vort_avg(1)   &
                              + vort_avg(2) * vort_avg(2)   &
                              + vort_avg(3) * vort_avg(3) )
    vort_mag_avg2       = 0.995 * vort_mag_avg1 ! FIXME
    vort_mag_avg1       = 1.005 * vort_mag_avg1 ! FIXME

    delta_velocity(1)   = u_trms2 - u_trms1
    delta_velocity(2)   = v_trms2 - v_trms1
    delta_velocity(3)   = w_trms2 - w_trms1
    vel_grad(1,1:3)     = delta_velocity(1) * edge_vector(1:3)
    vel_grad(2,1:3)     = delta_velocity(2) * edge_vector(1:3)
    vel_grad(3,1:3)     = delta_velocity(3) * edge_vector(1:3)
    vort_rms(1)         = vel_grad(3,2) - vel_grad(2,3)
    vort_rms(2)         = vel_grad(1,3) - vel_grad(3,1)
    vort_rms(3)         = vel_grad(2,1) - vel_grad(1,2)
    vort_mag_rms1       = sqrt( vort_rms(1) * vort_rms(1)   &
                              + vort_rms(2) * vort_rms(2)   &
                              + vort_rms(3) * vort_rms(3) )
    vort_mag_rms2       = 0.995 * vort_mag_rms1 ! FIXME
    vort_mag_rms1       = 1.005 * vort_mag_rms1 ! FIXME

    endif

! critical point determination
    gradv1= make_a( soln%gradx(2,node1)                                        &
                  , soln%grady(2,node1)                                        &
                  , soln%gradz(2,node1)                                        &
                  , soln%gradx(3,node1)                                        &
                  , soln%grady(3,node1)                                        &
                  , soln%gradz(3,node1)                                        &
                  , soln%gradx(4,node1)                                        &
                  , soln%grady(4,node1)                                        &
                  , soln%gradz(4,node1) )
    gradv2= make_a( soln%gradx(2,node2)                                        &
                  , soln%grady(2,node2)                                        &
                  , soln%gradz(2,node2)                                        &
                  , soln%gradx(3,node2)                                        &
                  , soln%grady(3,node2)                                        &
                  , soln%gradz(3,node2)                                        &
                  , soln%gradx(4,node2)                                        &
                  , soln%grady(4,node2)                                        &
                  , soln%gradz(4,node2) )
      asq_1 = matmul( gradv1, gradv1 )
      asq_2 = matmul( gradv2, gradv2 )
      pf(1) = -trace_a( gradv1 )
      pf(2) = -trace_a( gradv2 )
      qf(1) = half * ( pf(1)*pf(1) - trace_a( asq_1 ) )
      qf(2) = half * ( pf(2)*pf(2) - trace_a( asq_2 ) )
      rf(1) = -det_a( gradv1 )
      rf(2) = -det_a( gradv2 )
      call cubic (pf(1), qf(1), rf(1), d1, r11, r2r1, r2i1, r3r1, r3i1)
      call cubic (pf(2), qf(2), rf(2), d2, r12, r2r2, r2i2, r3r2, r3i2)

      conversion  = sqrt(gamma_argon*208._dp*180._dp) ! [m/s]???
      d           = 4.040e-10_dp                      ! [m]
      mass        = mol_weight/avogadro               ! kg
      vgradrho    = soln%q_dof(2,node1)*soln%gradx(1,node1)                    &
                  + soln%q_dof(3,node1)*soln%grady(1,node1)                    &
                  + soln%q_dof(4,node1)*soln%gradz(1,node1)
      rho         = soln%q_dof(1,node1) *1e-6_dp               ! [kg/m^3]
      pressure    = soln%q_dof(5,node1) *1e-6*208._dp*180._dp  ! [Pa]
      temperature = pressure/rho*208.0_dp             ! [K]
      coll_freq   = (pi*d*d*rho/mass)                                          &
                  * sqrt(16.0_dp*k_boltzman*temperature/(pi*mass))
      bird_b1     = abs(vgradrho) * 1e-6_dp * conversion / ( coll_freq * rho )
      vgradrho    = soln%q_dof(2,node2)*soln%gradx(1,node2)                    &
                  + soln%q_dof(3,node2)*soln%grady(1,node2)                    &
                  + soln%q_dof(4,node2)*soln%gradz(1,node2)
      rho         = soln%q_dof(1,node2) *1e-6_dp               ! [kg/m^3]
      pressure    = soln%q_dof(5,node2) *1e-6*208._dp*180._dp  ! [Pa]
      temperature = pressure/rho*208.0_dp             ! [K]
      coll_freq   = (pi*d*d*rho/mass)                                          &
                  * sqrt(16.0_dp*k_boltzman*temperature/(pi*mass))
      bird_b2     = abs(vgradrho) * 1e-6_dp * conversion / ( coll_freq * rho )

    select_iso_type: select case(iso_variable)
      case('p')
        iso1 = pr1
        iso2 = pr2
      case('rho')
        iso1 = rho1
        iso2 = rho2
      case('u')
        iso1 = u1
        iso2 = u2
      case('v')
        iso1 = v1
        iso2 = v2
      case('w')
        iso1 = w1
        iso2 = w2
      case('vort_x')
        iso1 = vort_x1
        iso2 = vort_x2
      case('vort_y')
        iso1 = vort_y1
        iso2 = vort_y2
      case('vort_z')
        iso1 = vort_z1
        iso2 = vort_z2
      case('vort_mag')
        iso1 = sqrt(vort_x1*vort_x1+vort_y1*vort_y1+vort_z1*vort_z1)
        iso2 = sqrt(vort_x2*vort_x2+vort_y2*vort_y2+vort_z2*vort_z2)
      case('vort_mag_avg')
        iso1 = vort_mag_avg1
        iso2 = vort_mag_avg2
      case('vort_mag_rms')
        iso1 = vort_mag_rms1
        iso2 = vort_mag_rms2
      case('mach')
        iso1 = sqrt((u1**2+v1**2+w1**2)/(gamma*pr1/rho1))
        iso2 = sqrt((u2**2+v2**2+w2**2)/(gamma*pr2/rho2))
      case('temperature')
        iso1 = gamma*pr1/rho1
        iso2 = gamma*pr2/rho2
      case('q_criterion')
        iso1 = q_criterion(soln%eqn_set, soln%n_grd,                           &
                           soln%gradx(:,node1),                                &
                           soln%grady(:,node1),                                &
                           soln%gradz(:,node1))
        iso2 = q_criterion(soln%eqn_set, soln%n_grd,                           &
                           soln%gradx(:,node2),                                &
                           soln%grady(:,node2),                                &
                           soln%gradz(:,node2))
      case('critical_d')

          iso1    = d1 ! q1*q1*q1 + r1*r1
          iso2    = d2 ! q2*q2*q2 + r2*r2
      case('s1a')
        iso1 = (1.0_dp/3.0_dp)*pf(1)*(qf(1)-(2.0_dp/9.0_dp)*pf(1)*pf(1))       &
              -(2.0_dp/27.0_dp)*(-3.0_dp*qf(1)+pf(1)*pf(1))**(3.0_dp/2.0_dp)   &
              -rf(1)
        iso2 = (1.0_dp/3.0_dp)*pf(2)*(qf(2)-(2.0_dp/9.0_dp)*pf(2)*pf(2))       &
              -(2.0_dp/27.0_dp)*(-3.0_dp*qf(2)+pf(2)*pf(2))**(3.0_dp/2.0_dp)   &
              -rf(2)

      case('s1b')
        iso1 = (1.0_dp/3.0_dp)*pf(1)*(qf(1)-(2.0_dp/9.0_dp)*pf(1)*pf(1))       &
              +(2.0_dp/27.0_dp)*(-3.0_dp*qf(1)+pf(1)*pf(1))**(3.0_dp/2.0_dp)   &
              -rf(1)
        iso2 = (1.0_dp/3.0_dp)*pf(2)*(qf(2)-(2.0_dp/9.0_dp)*pf(2)*pf(2))       &
              +(2.0_dp/27.0_dp)*(-3.0_dp*qf(2)+pf(2)*pf(2))**(3.0_dp/2.0_dp)   &
              -rf(2)

      case('s1')
        iso1 = 27.0_dp*rf(1)*rf(1)                                             &
             + (4.0_dp*pf(1)*pf(1)*pf(1)-18.0_dp*pf(1)*qf(1))*rf(1)            &
             + (4.0_dp*qf(1)*qf(1)*qf(1)-pf(1)*pf(1)*qf(1)*qf(1))
        iso2 = 27.0_dp*rf(2)*rf(2)                                             &
             + (4.0_dp*pf(2)*pf(2)*pf(1)-18.0_dp*pf(2)*qf(2))*rf(2)            &
             + (4.0_dp*qf(2)*qf(2)*qf(1)-pf(2)*pf(2)*qf(2)*qf(2))

      case('s2')
        if ( r11 /= r2r1 .and. r11 /= r3r1 ) then
          pf(1) = -( r11 + r2r1 + r3r1 )
          qf(1) =  r11*r2r1 + r11*r3r1 + r2r1*r3r1
          rf(1) = -r11*r2r1*r3r1
          pf(2) = -( r12 + r2r2 + r3r2 )
          qf(2) =  r12*r2r2 + r12*r3r2 + r2r2*r3r2
          rf(2) = -r12*r2r2*r3r2
        else
          pf(1) = -( r11 + r2r1 + r3r1 )
          qf(1) =  r11*r11 + r2i1*r2i1 + 2.0_dp*r11*r2r1
          rf(1) = -r3r1 * (r11*r11 + r2i1*r2i1 )
          pf(2) = -( r12 + r2r2 + r3r2 )
          qf(2) =  r12*r12 + r2i2*r2i2 + 2.0_dp*r12*r2r2
          rf(2) = -r3r2 * (r12*r12 + r2i2*r2i2 )
        endif
        iso1 = pf(1)*qf(1)-rf(1)
        iso2 = pf(2)*qf(2)-rf(2)

      case('rho_tavg')
        iso1 = rho_tavg1
        iso2 = rho_tavg2
      case('u_tavg')
        iso1 = u_tavg1
        iso2 = u_tavg2
      case('v_tavg')
        iso1 = v_tavg1
        iso2 = v_tavg2
      case('w_tavg')
        iso1 = w_tavg1
        iso2 = w_tavg2
      case('p_tavg')
        iso1 = p_tavg1
        iso2 = p_tavg2
      case('rho_trms')
        iso1 = rho_trms1
        iso2 = rho_trms2
      case('u_trms')
        iso1 = u_trms1
        iso2 = u_trms2
      case('v_trms')
        iso1 = v_trms1
        iso2 = v_trms2
      case('w_trms')
        iso1 = w_trms1
        iso2 = w_trms2
      case('p_trms')
        iso1 = p_trms1
        iso2 = p_trms2
      case('lambda1')
        iso1 = rlam11
        iso2 = rlam21
      case('lambda2')
        iso1 = rlam12
        iso2 = rlam22
      case('lambda3')
        iso1 = rlam13
        iso2 = rlam23
      case('lambda4')
        iso1 = rlam14
        iso2 = rlam24
      case('lambda5')
        iso1 = rlam15
        iso2 = rlam25
      case('lambda6')
        iso1 = rlam16
        iso2 = rlam26
      case('lambda7')
        iso1 = rlam17
        iso2 = rlam27
      case('processor_id')
        iso1 = proc_id1
        iso2 = proc_id2
      case('bird_breakdown')
        iso1 = bird_b1
        iso2 = bird_b2
! add future functions here
      case default

      end select select_iso_type

  end subroutine iso_nodal_values

  include 'q_criterion.f90'
!========================= ELEM_5EDGE_SORT ===================================80
!
! Calculate an untangled set of 3 triangles for a set of 5 points
!
!=============================================================================80

  subroutine elem_5edge_sort( cut_edge_map, cell, edge_per_cell                &
                           , geo_name, geom_def                                &
                           , grid,    soln, c2n, local_e2n                     &
                           , iso_val, iso_variable                             &
                           , cut_edge_map1, cut_edge_map2, cut_edge_map3       &
                           , sadj)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use solution_adj,   only : sadj_type

    integer,  dimension(:),              intent(in ) :: cut_edge_map
    integer,                             intent(in ) :: cell
    integer,                             intent(in ) :: edge_per_cell
    character(len=80),                   intent(in ) :: geo_name
    type(sample_type),                   intent(in ) :: geom_def
    type(grid_type),                     intent(in ) :: grid
    type(soln_type),                     intent(in ) :: soln
    type(sadj_type), optional,           intent(in ) :: sadj
    integer, dimension(:,:),             intent(in ) :: c2n
    integer, dimension(edge_per_cell,2), intent(in ) :: local_e2n
    real(dp),                            intent(in ) :: iso_val
    character(len=80),                   intent(in ) :: iso_variable

    integer,  dimension(edge_per_cell),  intent(out) :: cut_edge_map1
    integer,  dimension(edge_per_cell),  intent(out) :: cut_edge_map2
    integer,  dimension(edge_per_cell),  intent(out) :: cut_edge_map3

    integer                            :: node, node1, node2
    integer                            :: node1p, node2p
    integer                            :: edge

    real(dp)                           :: weight1, weight2
    real(dp), dimension(3)             :: pa, pb, pc
    real(dp), dimension(5,3)           :: p
    integer,  dimension(5)             :: n2e
    real(dp), dimension(5,5)           :: p2p_dist
    integer                            :: n1, n2
    integer                            :: i1, i2, i3, i4, i5
    integer, dimension(1)              :: p1, p2, p3, p4, p5
    real(dp)                           :: min_dist

  continue

    node1         = 0
    node2         = 0
    node          = 0
    n2e           = 0
    pc            = 0.0_dp
    cut_edge_map1 = 0
    cut_edge_map2 = 0
    cut_edge_map3 = 0

! increment through all the edges and find the points of the cuts

    edge_loop:  do edge = 1, edge_per_cell

      edge_is_cut:  if (1==cut_edge_map(edge)) then

        node  = node + 1
        n2e(node) = edge

        node1  = local_e2n(edge,1)      ! node number within cell
        node2  = local_e2n(edge,2)
        node1p = c2n(node1,cell)
        node2p = c2n(node2,cell)

        pa(1) = grid%x(node1p); pa(2) = grid%y(node1p); pa(3) = grid%z(node1p)
        pb(1) = grid%x(node2p); pb(2) = grid%y(node2p); pb(3) = grid%z(node2p)

        if ( geo_name == 'isosurface' ) then

           weight2 = edge_zero_iso ( iso_val, iso_variable, cell, edge         &
                         , edge_per_cell, local_e2n, c2n, grid, soln, sadj )
           weight1 = 1.0_dp - weight2

        else

        call edge_zero_parameter( geo_name, geom_def, weight1, weight2, pa, pb )

        endif

        p(node,1:3) = pa(1:3)*weight1 + pb(1:3)*weight2
        pc(1:3)     = pc(1:3) + p(node,1:3) / 5.0_dp

      end if edge_is_cut

    end do edge_loop

! calculate the distance between all the points

    p2p_dist      = huge(1.0_dp)
    do n1 = 1, 5
      do n2 = 1, 5
        p2p_dist(n1,n2) = dist(p(n1,:),p(n2,:))
      enddo
    enddo

    min_dist = huge(1.0_dp)
    i1 = 1
    do i2 = 2, 5
      do i3 = 2, 5
        if ( i2 == i3 ) cycle
        do i4 = 2, 5
          if ( i4 == i2 .or. i4 == i3 ) cycle
          do i5 = 2, 5
            if ( i5 == i2 .or. i5 == i3 .or. i5 == i4 ) cycle
            if ( total_dist( p2p_dist, i1, i2, i3, i4, i5 ) < min_dist ) then
              min_dist = total_dist( p2p_dist, i1, i2, i3, i4, i5 )
              p1(1) = i1
              p2(1) = i2
              p3(1) = i3
              p4(1) = i4
              p5(1) = i5
            endif
          enddo
        enddo
      enddo
    end do

    cut_edge_map1(n2e(p1(1))) = 1
    cut_edge_map1(n2e(p2(1))) = 1
    cut_edge_map1(n2e(p3(1))) = 1

    cut_edge_map2(n2e(p1(1))) = 1
    cut_edge_map2(n2e(p3(1))) = 1
    cut_edge_map2(n2e(p4(1))) = 1

    cut_edge_map3(n2e(p1(1))) = 1
    cut_edge_map3(n2e(p4(1))) = 1
    cut_edge_map3(n2e(p5(1))) = 1

    if ( skeleton > 2 ) then
      write(6,'(a,5i3,1x,es12.5)') 'min_dist',&
           p1(1),p2(1),p3(1),p4(1),p5(1),min_dist
      write(6,'(a,12i3)') 'map   ', cut_edge_map
      write(6,'(a,12i3)') 'map1  ', cut_edge_map1
      write(6,'(a,12i3)') 'map2  ', cut_edge_map2
      write(6,'(a,12i3)') 'map3  ', cut_edge_map3
    endif

  end subroutine elem_5edge_sort

!=============================================================================80
!
! Calculate an untangled set of 4 triangles for a set of 6 points
!
!=============================================================================80
  subroutine elem_6edge_sort( cut_edge_map, cell, edge_per_cell                &
                           , geo_name, geom_def                                &
                           , grid,    soln, c2n, local_e2n                     &
                           , iso_val, iso_variable                             &
                           , cut_edge_map1, cut_edge_map2, cut_edge_map3       &
                           , cut_edge_map4, sadj                               &
                           )

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use solution_adj,   only : sadj_type

    integer,  dimension(:),              intent(in ) :: cut_edge_map
    integer,                             intent(in ) :: cell
    integer,                             intent(in ) :: edge_per_cell
    character(len=80),                   intent(in ) :: geo_name
    type(sample_type),                   intent(in ) :: geom_def
    type(grid_type),                     intent(in ) :: grid
    type(soln_type),                     intent(in ) :: soln
    type(sadj_type), optional,           intent(in ) :: sadj
    integer, dimension(:,:),             intent(in ) :: c2n
    integer, dimension(edge_per_cell,2), intent(in ) :: local_e2n
    real(dp),                            intent(in ) :: iso_val
    character(len=80),                   intent(in ) :: iso_variable

    integer,  dimension(edge_per_cell),  intent(out) :: cut_edge_map1
    integer,  dimension(edge_per_cell),  intent(out) :: cut_edge_map2
    integer,  dimension(edge_per_cell),  intent(out) :: cut_edge_map3
    integer,  dimension(edge_per_cell),  intent(out) :: cut_edge_map4

    integer                            :: node, node1, node2
    integer                            :: node1p, node2p
    integer                            :: edge

    real(dp)                           :: weight1, weight2
    real(dp), dimension(3)             :: pa, pb, pc
    real(dp), dimension(6,3)           :: p
    integer,  dimension(6)             :: n2e
    real(dp), dimension(6,6)           :: p2p_dist
    integer                            :: n1, n2
    integer                            :: i1, i2, i3, i4, i5, i6
    integer, dimension(1)              :: p1, p2, p3, p4, p5, p6
    real(dp)                           :: min_dist

  continue

    node1         = 0
    node2         = 0
    node          = 0
    n2e           = 0
    pc            = 0.0_dp
    cut_edge_map1 = 0
    cut_edge_map2 = 0
    cut_edge_map3 = 0
    cut_edge_map4 = 0
    p1=0; p2=0; p3=0; p4=0; p5=0; p6=0

! increment through all the edges and find the points of the cuts

    edge_loop:  do edge = 1, edge_per_cell

      edge_is_cut:  if (1==cut_edge_map(edge)) then

        node  = node + 1
        n2e(node) = edge

        node1  = local_e2n(edge,1)      ! node number within cell
        node2  = local_e2n(edge,2)
        node1p = c2n(node1,cell)
        node2p = c2n(node2,cell)

        pa(1) = grid%x(node1p); pa(2) = grid%y(node1p); pa(3) = grid%z(node1p)
        pb(1) = grid%x(node2p); pb(2) = grid%y(node2p); pb(3) = grid%z(node2p)

        if ( geo_name == 'isosurface' ) then

           weight2 = edge_zero_iso ( iso_val, iso_variable, cell, edge         &
                          , edge_per_cell, local_e2n, c2n, grid, soln, sadj )
           weight1 = 1.0_dp - weight2

        else

        call edge_zero_parameter( geo_name, geom_def, weight1, weight2, pa, pb )

        endif

        p(node,1:3) = pa(1:3)*weight1 + pb(1:3)*weight2
        pc(1:3)     = pc(1:3) + p(node,1:3) / 5.0_dp

      end if edge_is_cut

    end do edge_loop

! calculate the distance between all the points


    p2p_dist      = huge(1.0_dp)
    do n1 = 1, 6
      do n2 = 1, 6
        if ( n1 /= n2 ) p2p_dist(n1,n2) = dist(p(n1,:),p(n2,:))
      enddo
    enddo

    min_dist = huge(1.0_dp)
    i1 = 1
    do i2 = 2, 6
      do i3 = 2, 6
        if ( i2 == i3 ) cycle
        do i4 = 2, 6
          if ( i4 == i2 .or. i4 == i3 ) cycle
          do i5 = 2, 6
            if ( i5 == i2 .or. i5 == i3 .or. i5 == i4 ) cycle
            do i6 = 2, 6
              if ( i6 == i2 .or. i6 == i3 .or. i6 == i4 .or. i6 == i5 ) cycle
              if (total_dist6(p2p_dist, i1, i2, i3, i4, i5, i6) < min_dist) then
                min_dist = total_dist6(p2p_dist, i1, i2, i3, i4, i5, i6)
                p1(1) = i1
                p2(1) = i2
                p3(1) = i3
                p4(1) = i4
                p5(1) = i5
                p6(1) = i6
              endif
            enddo
          enddo
        enddo
      enddo
    end do

    cut_edge_map1(n2e(p1(1))) = 1
    cut_edge_map1(n2e(p2(1))) = 1
    cut_edge_map1(n2e(p3(1))) = 1

    cut_edge_map2(n2e(p1(1))) = 1
    cut_edge_map2(n2e(p3(1))) = 1
    cut_edge_map2(n2e(p5(1))) = 1

    cut_edge_map3(n2e(p3(1))) = 1
    cut_edge_map3(n2e(p4(1))) = 1
    cut_edge_map3(n2e(p5(1))) = 1

    cut_edge_map4(n2e(p1(1))) = 1
    cut_edge_map4(n2e(p5(1))) = 1
    cut_edge_map4(n2e(p6(1))) = 1

    if ( skeleton > 2 ) then
      write(6,'(a,6i3,1x,es12.5,1x,f10.3,a)') 'min_dist',&
           p1(1),p2(1),p3(1),p4(1),p5(1),p6(1),min_dist
      write(6,'(a,12i3)') 'map  6', cut_edge_map
      write(6,'(a,12i3)') 'map1  ', cut_edge_map1
      write(6,'(a,12i3)') 'map2  ', cut_edge_map2
      write(6,'(a,12i3)') 'map3  ', cut_edge_map3
      write(6,'(a,12i3)') 'map4  ', cut_edge_map4
    endif

  end subroutine elem_6edge_sort

!========================= FIND_CLOSEST_ELEMENT ==============================80
!
! Search for element closest to any missing points
!
!=============================================================================80

  subroutine find_closet_element( grid, ivol, ipts )

    use allocations,        only : my_alloc_ptr
    use grid_types,         only : grid_type
    use sampling_headers,   only : lost_point_cell, lost_point_elem, sample

    type(grid_type),                 intent(in)   :: grid
    integer,                         intent(in)   :: ivol
    integer,                         intent(in)   :: ipts

    integer                               :: element_set
    integer                               :: node_per_cell
    integer,  dimension(:,:), pointer     :: c2n
    integer                               :: cell
    integer                               :: i, min_id
    real(dp), dimension(0:8)              :: px, py, pz
    real(dp), dimension(3)                :: po
    real(dp), dimension(3)                :: pc
    real(dp)                              :: dist, min_dist

    real(dp),  dimension(:  ), pointer    :: mindist
    real(dp), parameter                   :: zero = 0.0_dp

  continue

    if ( sample(ivol)%geo_type_name /= 'points') return

    po(1:3)                   = sample(ivol)%point_list(1:3,ipts)
    dist                      = zero
    min_dist                  = huge(1.0_dp)

    element_type_count:  do element_set = 1, grid%nelem

      node_per_cell =  grid%elem(element_set)%node_per_cell
      c2n           => grid%elem(element_set)%c2n

      element_loop : do cell = 1, grid%elem(element_set)%ncell

        px(0)                = po(1)
        py(0)                = po(2)
        pz(0)                = po(3)
        px(1:node_per_cell)  = grid%x(c2n(1:node_per_cell,cell))
        py(1:node_per_cell)  = grid%y(c2n(1:node_per_cell,cell))
        pz(1:node_per_cell)  = grid%z(c2n(1:node_per_cell,cell))
        dist = point_to_centroid ( node_per_cell, px, py, pz, pc )

        if ( dist < min_dist ) then
          min_dist                   = dist
          lost_point_elem(ivol,ipts) = element_set
          lost_point_cell(ivol,ipts) = cell
        endif

      end do element_loop

    end do element_type_count

    element_set   = lost_point_elem(ivol, ipts)
    cell          = lost_point_cell(ivol, ipts)
    node_per_cell =  grid%elem(element_set)%node_per_cell
    c2n           => grid%elem(element_set)%c2n

    call my_alloc_ptr( mindist, lmpi_nproc )
    mindist = 0
    call lmpi_allgather(min_dist, mindist)
    call lmpi_bcast(mindist)
    if ( lmpi_master ) then
      min_dist = huge(1.0_dp)
      do i = 1, lmpi_nproc
        if ( mindist(i) < min_dist ) then
          min_dist = mindist(i)
          min_id = i-1
        endif
      enddo
    endif
    call lmpi_bcast(min_id)
    call lmpi_bcast(min_dist)
    if ( lmpi_id == min_id ) then
      element_set          = lost_point_elem(ivol, ipts)
      cell                 = lost_point_cell(ivol, ipts)
      node_per_cell        =  grid%elem(element_set)%node_per_cell
      c2n                  => grid%elem(element_set)%c2n
      px(1:node_per_cell)  = grid%x(c2n(1:node_per_cell,cell))
      py(1:node_per_cell)  = grid%y(c2n(1:node_per_cell,cell))
      pz(1:node_per_cell)  = grid%z(c2n(1:node_per_cell,cell))
      write(*,'(a)') 'Closest element for sampling point found'
      write(*,'(a,1x,es12.5,a,i6)') 'Minimum distance = ', min_dist &
      , ' on processor ', min_id
      write(*,'(a,4i10,5(1x,f12.5))') 'Volume number ',ivol
      write(*,'(a,4i10,5(1x,f12.5))') 'Point number  ',ipts
      write(*,'(a,9(1x,es12.5))'    ) '  px=',(px(i),i=1,node_per_cell)
      write(*,'(a,9(1x,es12.5))'    ) '  py=',(py(i),i=1,node_per_cell)
      write(*,'(a,9(1x,es12.5))'    ) '  pz=',(pz(i),i=1,node_per_cell)
    endif

    deallocate( mindist )

  end subroutine find_closet_element


!-----------------------------------------------------------------------------80
!
! Routine to calculate statistics of cells inside a sphere
!
!-----------------------------------------------------------------------------80
  subroutine analyse_volume_statistics( grid, ivol )

    use grid_types,         only : grid_type
    use sampling_headers,   only : sample

    type(grid_type),                 intent(in) :: grid
    integer,                         intent(in) :: ivol

    integer                  :: node
    real(dp), dimension(1:3) :: po, pc
    real(dp)                 :: r0
    real(dp)                 :: dist
    real(dp)                 :: volume_local, volume_global
    integer                  :: count_local, count_global
    real(dp), parameter      :: third = 1.0_dp/3.0_dp

  continue

    volume_local  = 0.0_dp
    volume_global = 0.0_dp
    count_local   = 0
    count_global  = 0

    element_loop : do node = 1, grid%nnodes0

!         element_loop_geo_case : select case ( geo_name )

!           case ('sphere')

              ! nodal_values() == 0.0 on integration surface

        po(1:3)     = sample(ivol)%center(1:3) ! sphere center
        r0          = sample(ivol)%radius    ! sphere radius
        pc(1) = grid%x(node)
        pc(2) = grid%y(node)
        pc(3) = grid%z(node)

        dist = sqrt( (po(1)-pc(1))**2+ (po(2)-pc(2))**2+ (po(3)-pc(3))**2)

        if ( dist < r0 ) then

           volume_local = volume_local + grid%vol(node)
           count_local = count_local + 1

        endif

!     case default

!     end select

      enddo element_loop

    call lmpi_reduce( volume_local, volume_global )
    call lmpi_reduce(  count_local,  count_global )

    if ( lmpi_master ) then
      write(*,'(a,1x,es15.5,1x,i15)') 'total sampled volume/nodes=' &
             , volume_global, count_global
      write(*,'(a,2(1x,es15.5))')     'average volume/node,length=' &
             , volume_global/float(count_global)                    &
             ,(volume_global/float(count_global))**third
    endif

  end subroutine analyse_volume_statistics

!-----------------------------------------------------------------------------80
!
! Routine to calculate mesh statistics
!
!-----------------------------------------------------------------------------80
  subroutine analyse_mesh_statistics( grid, ivol )

    use grid_types,         only : grid_type

    type(grid_type),                 intent(in) :: grid
    integer,                         intent(in) :: ivol

   integer          :: element_set
   integer          :: ntet
   character(len=3) :: type_cell

    integer,  dimension(:,:), allocatable :: c2f
    integer                               :: number_of_faces

  continue

    element_type_count: do element_set = 1, grid%nelem

      type_cell     =  grid%elem(element_set)%type_cell

      if ( type_cell == 'tet') then
        ntet = grid%elem(element_set)%ncellg
        number_of_faces = 4
        allocate( c2f(number_of_faces, ntet) )

!write(*,*)'Calculate cell-to-face, ntet=', element_set &
!        , size(grid%elem(element_set)%c2n)
        call cell2face( grid%elem(element_set)%c2n, c2f, ntet, grid%nnodesg )

!        write(*,*)'Calculate stats.'
        call stats( grid%x, grid%y, grid%z, grid%elem(element_set)%c2n         &
                  , c2f, grid%nnodesg, ntet, grid%slen, ivol )
        deallocate( c2f )
      endif

    enddo element_type_count

  end subroutine analyse_mesh_statistics

!================================== STATS ====================================80
!
! Mesh statistics generated for quality histogram tecplot file
!
!=============================================================================80
  subroutine stats( x, y, z, c2n, c2f, nnodesg, ntet, slen, ivol )

    use file_utils,       only : available_unit
    use sampling_headers, only : sample

    integer, parameter :: number_of_nodes = 4
    integer, parameter :: number_of_edges = 6
    integer, parameter :: number_of_faces = 4

    integer                                  , intent(in) :: nnodesg
    integer                                  , intent(in) :: ntet
    real(dp), dimension(nnodesg)             , intent(in) :: x, y, z
    integer,  dimension(number_of_nodes,ntet), intent(in) :: c2n
    integer,  dimension(number_of_faces,ntet), intent(in) :: c2f
    real(dp),  dimension(:),                   intent(in) :: slen
    integer,                                   intent(in) :: ivol

    integer :: i, edge

    real(dp), dimension(3)                 :: p1, p2, p3, p4
    real(dp), dimension(3)                 :: po
    real(dp)                               :: r0
    real(dp)                               :: dst
    real(dp), dimension(number_of_faces,3) :: faces
    real(dp), dimension(number_of_faces)   :: area
    real(dp)                               :: h_min, h_max, h_avg
    real(dp)                               :: volume_min, volume_max, volume_avg
    real(dp)                               :: volume_total
    real(dp)                               :: x_vol_min, y_vol_min, z_vol_min
    real(dp)                               :: x_vol_max, y_vol_max, z_vol_max
    real(dp), dimension(6)                 :: angles
    real(dp), dimension(3,3)               :: m
    real(dp), dimension(3)                 :: lambda
    real(dp), dimension(3)                 :: eigen_vector1
    real(dp), dimension(3)                 :: eigen_vector2
    real(dp), dimension(3)                 :: eigen_vector3
    real(dp)                               :: volumes
    real(dp)                               :: eta
    real(dp)                               :: k
    real(dp)                               :: max_skew
    real(dp)                               :: q1, q2
    real(dp)                               :: angle_max, angle_min
    real(dp), dimension(3)                 :: centers
    real(dp), dimension(number_of_edges)   :: l
    real(dp)                               :: circumradius

    integer, dimension(0:10) :: eta_bin
    integer, dimension(0:10) :: k_bin
    integer, dimension(0:10) :: skew_bin
    integer, dimension(0:10) :: q1_bin
    integer, dimension(0:10) :: q2_bin
    integer, dimension(0:18) :: angle_bin
    integer, dimension(0:18,0:10) :: angle_k_bin
    integer, dimension(0:18,0:10) :: angle_sliver_bin
    integer, dimension(0:180,0:180) :: angle_angle_bin
    integer, dimension(0:18,0:10,0:10,0:10) :: cross_bin

    integer :: iostat
    integer :: i_cell, cell, n1, n2, n3, n4, j
    integer :: cell_tag_dim
    integer :: sliver, viscous, needle, spindle, regular, wedge, other

    integer , dimension(:,:), allocatable :: cell_tag
    integer :: ishape, iopt
    integer :: ii, jj
    integer :: iu_1, iu_2

  continue

    volume_avg = 0.0_dp
    volume_total = 0.0_dp
    volume_min = huge(1.0_dp)
    volume_max = tiny(1.0_dp)
    angle_max  = tiny(1.0_dp)
    angle_min  = huge(1.0_dp)
    cell_tag_dim = ntet!  / 100

!-----------------------------------------------------------------------------80
    sliver = 0 ; viscous = 0 ; needle = 0 ; spindle = 0 ; regular = 0
    wedge = 0
    other = 0
    angle_k_bin = 0
    angle_sliver_bin = 0
    cross_bin = 0
      eta_bin = 0
        k_bin = 0
     skew_bin = 0
       q1_bin = 0
       q2_bin = 0
    angle_bin = 0
    angle_angle_bin = 0
       i_cell = 0
!-----------------------------------------------------------------------------80
open(921,file='mesh_tets.dat',form='formatted',status='unknown',iostat=iostat)
        iu_1 = available_unit()
open(iu_1,file='mesh_angles.dat',form='formatted',status='unknown',            &
        iostat=iostat)
write(iu_1,*) 'variables= angle<sub>min</sub>, angle<sub>max</sub>,dist, ',&
        'k, <greek>h</greek>, Sliver, Skew'
    write(iu_1,*) 'zone'

    allocate(cell_tag(0:5,cell_tag_dim),stat=iostat)
    if ( iostat /= 0 ) stop 'Allocate error cell_tag'

    po(1:3)     = sample(ivol)%center(1:3) ! sphere center
    r0          = sample(ivol)%radius    ! sphere radius

    do i = 1, ntet

      p1 = (/x(c2n(1,i)), y(c2n(1,i)), z(c2n(1,i))/)
      p2 = (/x(c2n(2,i)), y(c2n(2,i)), z(c2n(2,i))/)
      p3 = (/x(c2n(3,i)), y(c2n(3,i)), z(c2n(3,i))/)
      p4 = (/x(c2n(4,i)), y(c2n(4,i)), z(c2n(4,i))/)
      centers(:)             = tet_cen_value( p1, p2, p3, p4 )
      dst = sqrt( (po(1)-centers(1))**2 &
                + (po(2)-centers(2))**2 &
                + (po(3)-centers(3))**2)

inside_sphere:   if ( dst < r0 ) then

! cell-to-cell skewness
      max_skew = skewness ( i, number_of_faces, number_of_nodes  &
        , x, y, z, c2n, c2f, ntet, nnodesg )
      skew_bin(int(10.*max_skew+.5)) = skew_bin(int(10.*max_skew+.5)) + 1

      call metrics ( p1, p2, p3, p4                                            &
          , m, lambda, eigen_vector1, eigen_vector2, eigen_vector3 )

! condition number
      k = min(lambda(1),lambda(2),lambda(3))/max(lambda(1),lambda(2),lambda(3))
      k_bin(int(10.*k+.5)) = k_bin(int(10.*k+.5)) + 1

      l(:)  = edge_length ( p1, p2, p3, p4 )
      h_min = huge(1.0d+0)
      h_max = tiny(0.0d+0)
      h_avg = 0.0d+0
      do edge = 1, number_of_edges
        h_avg = h_avg + l(edge)/6.0d+0
        if ( l(edge) < h_min ) h_min = l(edge)
        if ( l(edge) > h_max ) h_max = l(edge)
      enddo

      call triangle_areas (p1, p2, p3, p4, faces, area )
      angles(:)             = face_angles ( faces )
      angle_max             = maxval( angles )
      angle_min             = minval( angles )
      angle_bin(int(0.1*angle_max+.5)) = angle_bin(int(0.1*angle_max+.5)) + 1

      volumes                = tet_volume( p1, p2, p3, p4 )
! sliver
      q1                     = 6.0d+0*sqrt(2.0d+0)*volumes/h_avg**3
      q1_bin(int(10.*q1+.5)) = q1_bin(int(10.*q1+.5)) + 1

      volume_total          = volume_total + volumes
      circumradius          = get_sphere_radius( p1, p2, p3, p4, volumes )
! skew
      q2                    = (9.0d+0*sqrt(3.0d+0) &
                              /8.0d+0)*volumes/circumradius**3
      q2_bin(int(10.*q2+.5)) = q2_bin(int(10.*q2+.5)) + 1
! cross-cut data bin
      angle_k_bin( (int(0.1*angle_max+.5)), (int(10.*k+.5)) ) &
    = angle_k_bin( (int(0.1*angle_max+.5)), (int(10.*k+.5)) ) + 1
      angle_sliver_bin( (int(0.1*angle_max+.5)), (int(10.*q1+.5)) ) &
    = angle_sliver_bin( (int(0.1*angle_max+.5)), (int(10.*q1+.5)) ) + 1
      angle_angle_bin( (int(angle_max+.5)), (int(angle_min+.5)) ) &
    = angle_angle_bin( (int(angle_max+.5)), (int(angle_min+.5)) ) + 1

      cross_bin( (int(0.1*angle_max+.5))   &
               , (int(10.*k+.5))           &
               , (int(10.*q1+.5))          &
               , (int(10.*q2+.5)) )        &
    = cross_bin( (int(0.1*angle_max+.5))   &
               , (int(10.*k+.5))           &
               , (int(10.*q1+.5))          &
               , (int(10.*q2+.5)) ) + 1


      if ( volumes < volume_min ) then
        volume_min = volumes
        x_vol_min  = centers(1)
        y_vol_min  = centers(2)
        z_vol_min  = centers(3)
      endif
      if ( volumes > volume_max ) then
        volume_max = volumes
        x_vol_max  = centers(1)
        y_vol_max  = centers(2)
        z_vol_max  = centers(3)
      endif

      volume_avg = volume_avg + volumes/float(ntet)

! mean ratio
      eta = ( 12.0d+0 * (3.0d+0*abs(volumes))**(2.0_dp/3.0_dp) )         &
           / ( l(1)**2 + l(2)**2 + l(3)**2                               &
             + l(4)**2 + l(5)**2 + l(6)**2 )
      eta_bin(int(10.*eta+.5)) = eta_bin(int(10.*eta+.5)) + 1
      write(iu_1,'(10(1x,es13.6))') angle_min, angle_max &
       , slen(c2n(1,i)), k, eta, q1, q2

iopt=0
    if ( iopt==0 ) then
      if ( ( k >= 0.3d+0 ) ) then
        regular = regular + 1
            ishape = 0
            cell_tag(ishape,regular) = i
      else if ( ( k < .3d+0 ) .and. ( k >= 1.0d-4 ) ) then

        if  ( angle_max <= 140.0d+0 ) then

          if ( angle_max/angle_min < 2.0d+0  ) then
            needle = needle + 1
            ishape = 4
            cell_tag(ishape,needle) = i
          else if ( angle_max/angle_min < 100.0d+0  ) then
            wedge = wedge + 1
            ishape = 3
            cell_tag(ishape,wedge) = i
          else
            viscous = viscous + 1
            ishape = 1
            cell_tag(ishape,viscous) = i
          endif
        else if  ( angle_max > 140.0d+0 ) then
          if ( angle_max/angle_min < 10.0d+0  ) then
            spindle = spindle + 1
            ishape = 5
            cell_tag(ishape,spindle) = i
          else
            sliver = sliver + 1
            ishape = 2
            cell_tag(ishape,sliver) = i
          endif

        endif

      else if ( ( k < 1.0d-4 ) ) then

        if  ( angle_max <= 140.0d+0 ) then
          if ( angle_max/angle_min < 100.0d+0  ) then
            wedge = wedge + 1
            ishape = 3
            cell_tag(ishape,wedge) = i
          else
            viscous = viscous + 1
            ishape = 1
            cell_tag(ishape,viscous) = i
          endif
        else
          sliver = sliver + 1
            ishape = 2
            cell_tag(ishape,sliver) = i
        endif

      endif
!---------------------------------------------------------------------
!---------------------------------------------------------------------
!---------------------------------------------------------------------
    else if ( iopt==1 ) then

      if (                                       &
          ( abs(angle_max-115.0d+0) < 20.0d+0 )  &
     .and.( abs(angle_min-  0.0d+0) < 15.0d+0 )  &
         ) then
            viscous = viscous + 1
            ishape = 1
            cell_tag(ishape,viscous) = i
       else if (                                 &
          ( abs(angle_max-90.0d+0) < 15.0d+0 )   &
     .and.( abs(angle_min-55.0d+0) < 15.0d+0 )   &
         ) then
            regular = regular + 1
            ishape = 0
            cell_tag(ishape,regular) = i
       else if (                                 &
          ( abs(angle_max-180.0d+0) < 15.0d+0 )  &
     .and.( abs(angle_min-  0.0d+0) < 15.0d+0 )  &
         ) then
            sliver = sliver + 1
            ishape = 2
            cell_tag(ishape,sliver) = i
       else
         if  ( angle_max > 140.0d+0 ) then
            if ( angle_max/angle_min < 2.0d+0  ) then
            needle = needle + 1
            ishape = 4
            cell_tag(ishape,needle) = i
         else if ( angle_max/angle_min < 100.0d+0  ) then
            wedge = wedge + 1
            ishape = 3
            cell_tag(ishape,wedge) = i
!         else
!           viscous = viscous + 1
!           ishape = 1
!           cell_tag(ishape,viscous) = i
          endif
        else if  ( angle_max > 140.0d+0 ) then
          if ( angle_max/angle_min < 10.0d+0  ) then
            spindle = spindle + 1
            ishape = 5
            cell_tag(ishape,spindle) = i
!         else
!           sliver = sliver + 1
!           ishape = 2
!           cell_tag(ishape,sliver) = i
          endif

        endif


        endif
      endif
      i_cell  = i_cell + 1

      endif inside_sphere

    enddo

!----------------------------------------------------------------------------80
!
!   Write out map of mesh statistics
!
!----------------------------------------------------------------------------80
        iu_2 = available_unit()
    open(iu_2,file='mesh_stats.dat',form='formatted',status='unknown'         &
                ,iostat=iostat)
    write(iu_2,*) 'variables= var1, var2, Count'
    write(iu_2,*) 'zone, t="Angle-k stats",i=19,j=11,f=block'
    write(iu_2,*) ((float(ii)*10.,ii=0,18),jj=0,10)
    write(iu_2,*) ((float(jj)/10.,ii=0,18),jj=0,10)
    write(iu_2,*) ((float(angle_k_bin(ii,jj)),ii=0,18),jj=0,10)
    write(iu_2,*) 'zone, t="Angle-sliver stats",i=19,j=11,f=block'
    write(iu_2,*) ((float(ii)*10.,ii=0,18),jj=0,10)
    write(iu_2,*) ((float(jj)/10.,ii=0,18),jj=0,10)
    write(iu_2,*) ((float(angle_sliver_bin(ii,jj)),ii=0,18),jj=0,10)
    write(iu_2,*) 'zone, t="Angle-angle stats",i=181,j=181,f=block'
    write(iu_2,*) ((float(ii),ii=0,180),jj=0,180)
    write(iu_2,*) ((float(jj),ii=0,180),jj=0,180)
    write(iu_2,*) ((float(angle_angle_bin(ii,jj)),ii=0,180),jj=0,180)
    close(iu_2)

    write(*,*) 'Regular          =',regular
    write(*,*) 'Viscous          =',Viscous
    write(*,*) 'Sliver           =',sliver
    write(*,*) 'Wedge            =',wedge
    write(*,*) 'Needle           =',needle
    write(*,*) 'Spindle          =',spindle
    write(*,*) 'other            =',other
    write(*,*) 'Total of types   =', &
              regular+needle+wedge+spindle+sliver+viscous+other
    write(*,*)
    write(*,*) 'Total cells      =',ntet
    write(*,*)
    write(921,*) 'variables=x,y,z'

sequence_shapes:    do j = 0, 5 ! shapes

      if ( j == 0 ) i_cell = regular
      if ( j == 1 ) i_cell = viscous
      if ( j == 2 ) i_cell = sliver
      if ( j == 3 ) i_cell = wedge
      if ( j == 4 ) i_cell = needle
      if ( j == 5 ) i_cell = spindle

    write(6,*) 'zone, n=',4*i_cell,',e=',i_cell, &
               ',zonetype=fetetrahedron, datapacking=point'
    write(921,*) 'zone, n=',4*i_cell,',e=',i_cell, &
               ',zonetype=fetetrahedron, datapacking=point'


sequence_tets:    do i = 1, i_cell

        cell = cell_tag(j,i)
        n1 = c2n(1,cell); n2 = c2n(2,cell); n3 = c2n(3,cell); n4 = c2n(4,cell)

        write(921,'(3(1x,es15.7))') x(n1), y(n1), z(n1)
        write(921,'(3(1x,es15.7))') x(n2), y(n2), z(n2)
        write(921,'(3(1x,es15.7))') x(n3), y(n3), z(n3)
        write(921,'(3(1x,es15.7))') x(n4), y(n4), z(n4)

      enddo sequence_tets

      do i = 1, i_cell
        write(921,*) (i-1)*4+1, (i-1)*4+2, (i-1)*4+3, (i-1)*4+4
      enddo

      enddo sequence_shapes



    write(6,'(a,1x,es11.4,a,3(1x,es12.3),a)') 'mininum volume='&
          ,volume_min, ', position=(',x_vol_min ,y_vol_min ,z_vol_min,')'
    write(6,'(a,1x,es11.4,a,3(1x,es12.3),a)') 'maximum volume='&
          ,volume_max, ', position=(',x_vol_max ,y_vol_max ,z_vol_max,')'
    write(6,'(a,12(1x,es11.4))') 'average volume=',volume_avg
    write(6,'(a,i12)') 'Number of out-of-spec cells=',i_cell


!-------------------------------------------------------------------------------
!  Make up some histograms of mesh statistics
!-------------------------------------------------------------------------------
    open(200,file='mesh_histogram.dat'&
            ,iostat=iostat)
    write(200,*) 'variables=var, number'
    write(200,*) 'zone, t="Mean ratio"'
    write(200,*) (float(i)/10.,float(eta_bin(i))/float(1),i=0,10)
    write(200,*) 'zone, t="Condition number"'
    write(200,*) (float(i)/10.,float(k_bin(i))/float(1),i=0,10)
    write(200,*) 'zone, t="Skew"'
    write(200,*) (float(i)/10.,float(skew_bin(i))/float(1),i=0,10)
    write(200,*) 'zone, t="Sliver quality"'
    write(200,*) (float(i)/10.,float(q1_bin(i))/float(1),i=0,10)
    write(200,*) 'zone, t="Skew2"'
    write(200,*) (float(i)/10.,float(q2_bin(i))/float(1),i=0,10)
    write(200,*) 'zone, t="Max angle"'
    write(200,*) (float(i)*10.,float(angle_bin(i))/float(1),i=0,18)
    close(200)

  end subroutine stats

!================================ ANGLES =====================================80
!
!  Calculate the angles for the tet
!
!=============================================================================80

  pure function face_angles ( faces )

    real(dp), dimension(6)               :: face_angles
    real(dp), dimension(4,3), intent(in) :: faces

    real(dp), dimension(3) :: v1, v2, v3, v4
    real(dp)               :: magv1, magv2, magv3, magv4
    real(dp)               :: pi

  continue

    pi      = acos(-1.0d+0)
    v1      = faces(1,:)
    v2      = faces(2,:)
    v3      = faces(3,:)
    v4      = faces(4,:)
    magv1   = sqrt(v1(1)*v1(1)+v1(2)*v1(2)+v1(3)*v1(3))
    magv2   = sqrt(v2(1)*v2(1)+v2(2)*v2(2)+v2(3)*v2(3))
    magv3   = sqrt(v3(1)*v3(1)+v3(2)*v3(2)+v3(3)*v3(3))
    magv4   = sqrt(v4(1)*v4(1)+v4(2)*v4(2)+v4(3)*v4(3))
    v1      = v1/magv1
    v2      = v2/magv2
    v3      = v3/magv3
    v4      = v4/magv4

    face_angles(1) = 180.0d+0 - acos(dot_product(v1, v2))*180.0d+0/pi
    face_angles(2) = 180.0d+0 - acos(dot_product(v1, v3))*180.0d+0/pi
    face_angles(3) = 180.0d+0 - acos(dot_product(v1, v4))*180.0d+0/pi
    face_angles(4) = 180.0d+0 - acos(dot_product(v2, v3))*180.0d+0/pi
    face_angles(5) = 180.0d+0 - acos(dot_product(v2, v4))*180.0d+0/pi
    face_angles(6) = 180.0d+0 - acos(dot_product(v3, v4))*180.0d+0/pi

  end function face_angles

!================================ EDGE_LENGTH ================================80
!
!  Calculate the edge lengths for a tet
!
!=============================================================================80
  pure function edge_length ( p1, p2, p3, p4 )

    real(dp), dimension(6)             :: edge_length
    real(dp), dimension(3), intent(in) :: p1
    real(dp), dimension(3), intent(in) :: p2
    real(dp), dimension(3), intent(in) :: p3
    real(dp), dimension(3), intent(in) :: p4

  continue

    edge_length(1) = tet_edge_value( p2, p1 )
    edge_length(2) = tet_edge_value( p3, p1 )
    edge_length(3) = tet_edge_value( p4, p1 )
    edge_length(4) = tet_edge_value( p3, p2 )
    edge_length(5) = tet_edge_value( p4, p2 )
    edge_length(6) = tet_edge_value( p4, p3 )

  end function edge_length

!================================ TRIANGLE_AREAS =============================80
!
!  Get the triangle areaa and normals for tet
!
!=============================================================================80
  subroutine triangle_areas (p1, p2, p3, p4, faces, area )

    real(dp), dimension(3)  , intent(in)  ::  p1
    real(dp), dimension(3)  , intent(in)  ::  p2
    real(dp), dimension(3)  , intent(in)  ::  p3
    real(dp), dimension(3)  , intent(in)  ::  p4
    real(dp), dimension(4,3), intent(out) ::  faces
    real(dp), dimension(4)  , intent(out) ::  area

    real(dp), dimension(3) :: px, py, pz

  continue

    px = (/p1(1), p3(1), p2(1)/)
    py = (/p1(2), p3(2), p2(2)/)
    pz = (/p1(3), p3(3), p2(3)/)
    call triangle_area_method2(px, py, pz, faces(1,:), area(1) )
    px = (/p2(1), p3(1), p4(1)/)
    py = (/p2(2), p3(2), p4(2)/)
    pz = (/p2(3), p3(3), p4(3)/)
    call triangle_area_method2(px, py, pz, faces(2,:), area(2) )
    px = (/p3(1), p1(1), p4(1)/)
    py = (/p3(2), p1(2), p4(2)/)
    pz = (/p3(3), p1(3), p4(3)/)
    call triangle_area_method2(px, py, pz, faces(3,:), area(3) )
    px = (/p2(1), p4(1), p1(1)/)
    py = (/p2(2), p4(2), p1(2)/)
    pz = (/p2(3), p4(3), p1(3)/)
    call triangle_area_method2(px, py, pz, faces(4,:), area(4) )

  end subroutine triangle_areas

!================================ FACE_NORMAL ================================80
!
!  Calculate normal vector given cell and face
!
!=============================================================================80
  function face_normal ( ntet, nnodesg, c2n, x, y, z, cell, face  ) result ( n )

    real(dp), dimension(3)                   :: n

    integer,                      intent(in) :: ntet
    integer,                      intent(in) :: nnodesg
    integer,  dimension(4, ntet), intent(in) :: c2n
    real(dp), dimension(nnodesg), intent(in) :: x, y, z
    integer,                      intent(in) :: cell
    integer,                      intent(in) :: face

    real(dp)               :: area
    integer,  dimension(3) :: nodes
    real(dp), dimension(3) :: px, py, pz

    integer, parameter, dimension (4,4) :: local_f2n =                         &
    reshape((/  1, 2, 1, 1,                                                    &
                3, 3, 4, 2,                                                    &
                2, 4, 3, 4,                                                    &
                1, 2, 1, 1 /), (/ 4, 4/))

  continue

    nodes(1:3) = c2n(local_f2n(face,1:3),cell)

    px = (/x(nodes(1)), x(nodes(2)), x(nodes(3))/)
    py = (/y(nodes(1)), y(nodes(2)), y(nodes(3))/)
    pz = (/z(nodes(1)), z(nodes(2)), z(nodes(3))/)

    call triangle_area_method2(px, py, pz, n, area )

  end function face_normal

!================================ TET_CEN_VALUE ==============================80
!
!  Return the geometric center of the tet given 4 nodes
!
!=============================================================================80
  pure function tet_cen_value( p1, p2, p3, p4 )

    real(dp), dimension(3)              :: tet_cen_value

    real(dp), dimension(3), intent(in ) :: p1
    real(dp), dimension(3), intent(in ) :: p2
    real(dp), dimension(3), intent(in ) :: p3
    real(dp), dimension(3), intent(in ) :: p4

    tet_cen_value(:) = 0.25_dp * ( p1(:) + p2(:) + p3(:) + p4(:) )

  end function tet_cen_value

!================================ TET_EDGE_VALUE =============================80
!
!  Return the length of an edge
!
!=============================================================================80
  pure function tet_edge_value( p1, p2 )

    real(dp)                            :: tet_edge_value
    real(dp), dimension(3), intent(in ) :: p1
    real(dp), dimension(3), intent(in ) :: p2

  continue

    tet_edge_value = sqrt( (p2(1)-p1(1))**2+(p2(2)-p1(2))**2+(p2(3)-p1(3))**2 )

  end function tet_edge_value

!-------------------------- DETERMINANT --------------------------------------80
!
! Recursive function to calculation of a square matrix determinant
!
!-----------------------------------------------------------------------------80
  recursive function determinant( n, a ) result ( det )

    real(dp)                                 :: det
    integer,                      intent(in) :: n
    real(dp), dimension(n  ,n  ), intent(in) :: a
    real(dp), dimension(n-1,n-1)             :: m

    integer :: i
    integer :: j
    integer :: j1
    integer :: j2

  continue

    det = huge(1._dp)
    if ( n < 2 ) return

    if ( n == 2 ) then
      det = a(1,1)*a(2,2) - a(2,1)*a(1,2)
    else
      det = 0.0_dp
      do j1 = 1, n
! create minor
        do i = 2, n
          j2 = 1
          do j = 1, n
            if ( j == j1 ) cycle
            m(i-1,j2) = a(i,j)
            j2 = j2 + 1
          end do
        end do
! calculate determinant
        det = det + (-1)**(1+j1) * a(1,j1) * determinant( n-1, m )
      end do
    end if

  end function determinant

!=========================== METRICS =========================================80
!
! Calculate the transformation of a tentrahedron to a regular tetrahedron
!
!=============================================================================80
  subroutine metrics ( p1, p2, p3, p4                                          &
      , m, eigen_values, eigen_vector1, eigen_vector2, eigen_vector3 )

    use linear_algebra, only : eigenvalueandvect3sym

    real(dp), dimension(3)  , intent(in)  :: p1, p2, p3, p4
    real(dp), dimension(3,3), intent(out) :: m
    real(dp), dimension(3)  , intent(out) :: eigen_values
    real(dp), dimension(3)  , intent(out) :: eigen_vector1
    real(dp), dimension(3)  , intent(out) :: eigen_vector2
    real(dp), dimension(3)  , intent(out) :: eigen_vector3

    integer :: ierr

  continue

    call infer_tetrahedral_metric(p1,p2,p3,p4,m)
    call eigenvalueandvect3sym( m,                                           &
      eigen_values(1), eigen_values(2), eigen_values(3),                     &
      eigen_vector1, eigen_vector2, eigen_vector3, ierr )

  end subroutine metrics


!========================= INFER_TETRAHEDRAL_METRIC ==========================80
!
!=============================================================================80

  subroutine infer_tetrahedral_metric( xyz1, xyz2, xyz3, xyz4, m )

    use linear_algebra, only : singlePivotLU, backSolveLU

    real(dp), dimension(3),   intent(in)  :: xyz1, xyz2, xyz3, xyz4
    real(dp), dimension(3,3), intent(out) :: m

    real(dp), dimension(3)   :: edge
    real(dp), dimension(6,6) :: a, p
    real(dp), dimension(6)   :: ones, entries

    integer :: row
    real(dp), parameter :: one = 1.0_dp
    real(dp), parameter :: two = 2.0_dp

    continue

    row = 1
    edge = xyz2-xyz1
    a(row,1) =     edge(1)*edge(1)
    a(row,2) = two*edge(1)*edge(2)
    a(row,3) = two*edge(1)*edge(3)
    a(row,4) =     edge(2)*edge(2)
    a(row,5) = two*edge(2)*edge(3)
    a(row,6) =     edge(3)*edge(3)

    row = 2
    edge = xyz3-xyz1
    a(row,1) =     edge(1)*edge(1)
    a(row,2) = two*edge(1)*edge(2)
    a(row,3) = two*edge(1)*edge(3)
    a(row,4) =     edge(2)*edge(2)
    a(row,5) = two*edge(2)*edge(3)
    a(row,6) =     edge(3)*edge(3)

    row = 3
    edge = xyz4-xyz1
    a(row,1) =     edge(1)*edge(1)
    a(row,2) = two*edge(1)*edge(2)
    a(row,3) = two*edge(1)*edge(3)
    a(row,4) =     edge(2)*edge(2)
    a(row,5) = two*edge(2)*edge(3)
    a(row,6) =     edge(3)*edge(3)

    row = 4
    edge = xyz3-xyz2
    a(row,1) =     edge(1)*edge(1)
    a(row,2) = two*edge(1)*edge(2)
    a(row,3) = two*edge(1)*edge(3)
    a(row,4) =     edge(2)*edge(2)
    a(row,5) = two*edge(2)*edge(3)
    a(row,6) =     edge(3)*edge(3)

    row = 5
    edge = xyz4-xyz2
    a(row,1) =     edge(1)*edge(1)
    a(row,2) = two*edge(1)*edge(2)
    a(row,3) = two*edge(1)*edge(3)
    a(row,4) =     edge(2)*edge(2)
    a(row,5) = two*edge(2)*edge(3)
    a(row,6) =     edge(3)*edge(3)

    row = 6
    edge = xyz4-xyz3
    a(row,1) =     edge(1)*edge(1)
    a(row,2) = two*edge(1)*edge(2)
    a(row,3) = two*edge(1)*edge(3)
    a(row,4) =     edge(2)*edge(2)
    a(row,5) = two*edge(2)*edge(3)
    a(row,6) =     edge(3)*edge(3)

    call singlePivotLU(a,p)
    ones = one
    call backSolveLU(a,entries,ones)

    m(1,1) = entries(1)
    m(1,2) = entries(2)
    m(1,3) = entries(3)
    m(2,1) = entries(2)
    m(2,2) = entries(4)
    m(2,3) = entries(5)
    m(3,1) = entries(3)
    m(3,2) = entries(5)
    m(3,3) = entries(6)

  end subroutine infer_tetrahedral_metric

!========================== CELL2FACE ========================================80
!
! Find adjacent cell for each tet face
!
!=============================================================================80
  subroutine cell2face( c2n, c2f, ntet, nnodesg )

    integer, parameter :: number_of_nodes = 4
    integer, parameter :: number_of_faces = 4

    integer                                  , intent(in)  :: ntet
    integer                                  , intent(in)  :: nnodesg
    integer, dimension(number_of_nodes, ntet), intent(in)  :: c2n
    integer, dimension(number_of_faces, ntet), intent(out) :: c2f

    integer, dimension(2, 4*ntet)                          :: n2c
    integer, dimension(nnodesg  )                          :: firstn2c

    integer :: cell
    integer :: cellm
    integer :: face
    integer :: n2cindex
    integer, dimension(3) :: nodes

    integer, parameter, dimension (4,4) :: local_f2n =                         &
    reshape((/  1, 2, 1, 1,                                                    &
                3, 3, 4, 2,                                                    &
                2, 4, 3, 4,                                                    &
                1, 2, 1, 1 /), (/ 4, 4/))

  continue

    c2f = 0

    call node2cell( c2n, n2c, firstn2c, ntet, nnodesg )

outer_cell_loop:  do cell = 1, ntet

outer_face_loop:  do face = 1, 4

          nodes(1:3) = c2n(local_f2n(face,1:3),cell)

          cellm = gridfindcellwithface( firstn2c, c2n, n2c                     &
               , number_of_faces, number_of_nodes                              &
                  , local_f2n, nodes, n2cindex, nnodesg, ntet )

          c2f(face,cell)= cellm

      enddo outer_face_loop

    enddo outer_cell_loop

  end subroutine cell2face

!============================== gridfindcellwithface =========================80
!
!=============================================================================80

  function gridfindcellwithface( firstn2c, c2n, n2c                            &
           , face_per_cell, node_per_cell, local_f2n                           &
           , nodes, n2cindex, nnodesg, ntet )

    integer                                    :: gridfindcellwithface

    integer                                  , intent(in)  :: ntet
    integer                                  , intent(in)  :: nnodesg
    integer                                  , intent(in)  :: node_per_cell
    integer                                  , intent(in)  :: face_per_cell
    integer, dimension(nnodesg  )            , intent(in)  :: firstn2c
    integer, dimension(node_per_cell, ntet  ), intent(in)  :: c2n
    integer, dimension(2, 4*ntet)            , intent(in)  :: n2c
    integer, dimension(4,4)                  , intent(in)  :: local_f2n
    integer, dimension(3)                    , intent(in)  :: nodes
    integer                                  , intent(out) :: n2cindex

    integer :: cell, face, n1, n2, n3

    continue

    gridfindcellwithface = 0

    if (nodes(1) <= 0) return

    n2cindex = firstn2c(nodes(1))
    search_orbit :do while ( n2cindex > 0 )
      cell = n2c(1,n2cindex)
      do face = 1, face_per_cell
! reverse face for inward pointing normal
        n1 = c2n(local_f2n(face,1),cell)
        n2 = c2n(local_f2n(face,3),cell)
        n3 = c2n(local_f2n(face,2),cell)
        if ( ( n1 == nodes(1) .and. n2 == nodes(2) .and. n3 == nodes(3) ) .or. &
             ( n2 == nodes(1) .and. n3 == nodes(2) .and. n1 == nodes(3) ) .or. &
             ( n3 == nodes(1) .and. n1 == nodes(2) .and. n2 == nodes(3) ) ) then
          gridfindcellwithface = cell
          return
        end if
      end do
      n2cindex = n2c(2,n2cindex)
    end do search_orbit

  end function gridfindcellwithface


!========================== NODE2CELL ========================================80
!
! Find adjacent cell for each node
!
!=============================================================================80
  subroutine node2cell( c2n, n2c, firstn2c, ntet, nnodesg )

    integer, parameter :: number_of_nodes = 4

    integer                                  , intent(in)  :: ntet
    integer                                  , intent(in)  :: nnodesg
    integer, dimension(number_of_nodes, ntet), intent(in)  :: c2n
    integer, dimension(2, 4*ntet            ), intent(out) :: n2c
    integer, dimension(nnodesg)              , intent(out) :: firstn2c

    integer :: cell
    integer :: j
    integer :: cellnode, node, firstemptyn2c
    integer :: nextempty

  continue

    n2c      = 0
    firstn2c = 0

    do j = 1, 4*ntet
      n2c(1,j) = 0
      n2c(2,j) = j+1
    end do
    n2c(2,4*ntet) = 0
    firstemptyn2c = 1

outer_cell_loop:  do cell = 1, ntet

      do cellnode = 1, 4

        node                 = c2n(cellnode,cell)
        nextempty            = n2c(2,firstemptyn2c)
        n2c(1,firstemptyn2c) = cell
        n2c(2,firstemptyn2c) = firstn2c(node)
        firstn2c(node)       = firstemptyn2c
        firstemptyn2c        = nextempty

      end do

    enddo outer_cell_loop

  end subroutine node2cell

!=========================== POINT2POINT =====================================80
!
! Return normalized vector given end points
!
!=============================================================================80
  pure function point2point ( p0, p1 )  result ( p2p )

    real(dp), dimension(3)             :: p2p
    real(dp), dimension(3), intent(in) :: p0
    real(dp), dimension(3), intent(in) :: p1

    real(dp) :: magnitude

  continue

    magnitude = sqrt((p1(1)-p0(1))**2 + (p1(2)-p0(2))**2 + (p1(3)-p0(3))**2 )
    p2p = ( p1 - p0 ) / magnitude

  end function point2point

!=========================== SKEWNESS ========================================80
!
! Calculate largest skew factor for a cell
!
!=============================================================================80
  function skewness ( cell, number_of_faces, number_of_nodes &
       , x, y, z, c2n, c2f, ntet, nnodesg ) result ( skew_max )

    real(dp) :: skew, skew_max, skew_min
    integer                                  , intent(in) :: cell
    integer                                  , intent(in) :: number_of_faces
    integer                                  , intent(in) :: number_of_nodes
    integer                                  , intent(in) :: ntet
    integer                                  , intent(in) :: nnodesg
    real(dp), dimension(nnodesg)             , intent(in) :: x, y, z
    integer, dimension(number_of_nodes, ntet), intent(in) :: c2n
    integer, dimension(number_of_faces, ntet), intent(in) :: c2f

    integer :: face
    integer :: cell2
    real(dp), dimension(3) :: p1 , p2 , p3 , p4, center
    real(dp), dimension(3) :: p1m, p2m, p3m, p4m, centerm
    real(dp), dimension(3) :: n_face
    real(dp), dimension(3) :: n_c2c

  continue

    skew_max    = tiny(0.0_dp)
    skew_min    = huge(1.0_dp)
    center      = 0.0_dp
    centerm     = 0.0_dp

    p1 = (/x(c2n(1,cell)), y(c2n(1,cell)), z(c2n(1,cell))/)
    p2 = (/x(c2n(2,cell)), y(c2n(2,cell)), z(c2n(2,cell))/)
    p3 = (/x(c2n(3,cell)), y(c2n(3,cell)), z(c2n(3,cell))/)
    p4 = (/x(c2n(4,cell)), y(c2n(4,cell)), z(c2n(4,cell))/)

    center(:) = tet_cen_value( p1, p2, p3, p4 )

    do face = 1, number_of_faces

      n_face(1:3) = face_normal ( ntet, nnodesg, c2n, x, y, z, cell, face )

      cell2 = c2f(cell, face)
      if ( cell2 /= 0 .and. cell2 /= cell ) then
        p1m = (/x(c2n(1,cell2)), y(c2n(1,cell2)), z(c2n(1,cell2))/)
        p2m = (/x(c2n(2,cell2)), y(c2n(2,cell2)), z(c2n(2,cell2))/)
        p3m = (/x(c2n(3,cell2)), y(c2n(3,cell2)), z(c2n(3,cell2))/)
        p4m = (/x(c2n(4,cell2)), y(c2n(4,cell2)), z(c2n(4,cell2))/)
        centerm(:) = tet_cen_value( p1m, p2m, p3m, p4m )
        n_c2c = point2point ( center, centerm )
        skew = abs(dot_product(n_face,n_c2c))
      !...this step is to avoid NaN in acos routine.

        skew = min( 1.0_dp , skew )
        skew = max(-1.0_dp , skew )

        skew_max = max( skew_max , skew )
        skew_min = min( skew_min , skew )

      else
        skew = -1
      endif

    enddo

  end function skewness

!============================ ROTATE =========================================80
!
! Rotate a 3-D vector by A
!
!=============================================================================80
  pure function rotate ( v, a ) result ( tilde_v )

    real(dp), dimension(3)               :: tilde_v
    real(dp), dimension(3),   intent(in) :: v
    real(dp), dimension(3,3), intent(in) :: a

  continue

    tilde_v(1) = a(1,1)*v(1) + a(1,2)*v(2) + a(1,3)*v(3)
    tilde_v(2) = a(2,1)*v(1) + a(2,2)*v(2) + a(2,3)*v(3)
    tilde_v(3) = a(3,1)*v(1) + a(3,2)*v(2) + a(3,3)*v(3)

  end function rotate

!====================================== VMAG =================================80
!
!  Computes the magnitude
!
!=============================================================================80

  pure function vmag(v)

    real(dp) :: vmag
    real(dp), dimension(3), intent(in) :: v

  continue

    vmag = sqrt(v(1)*v(1) + v(2)*v(2) + v(3)*v(3))

  end function vmag

!=============================== GET_AVG_DATA ================================80
!
!  Creates array of single-precision averaged data from node-based array
!
!=============================================================================80
  function get_avg_data( n_tria, nnodes, edge_data, trinode_map, quad_normal ) &
    result ( data_avg )

    use sampling_headers, only : n_output_variables, output_variables
    use kinddefs,         only : r4

    integer,                                        intent(in) :: n_tria
    integer,                                        intent(in) :: nnodes
    real(dp), dimension(n_output_variables,nnodes), intent(in) :: edge_data
    integer,  dimension(                 4,n_tria), intent(in) :: trinode_map
    real(dp), dimension(                        3), intent(in) :: quad_normal

    real(r4), dimension(n_output_variables,n_tria)             :: data_avg

    integer                :: nn, tria, node1, node2, node3
    real(dp), dimension(3) :: local_data
    real(dp), dimension(3) :: p, p1, p2, p3
    real(dp), dimension(3) :: n

    real(dp), parameter    :: zero      = 0.0_dp
    real(dp), parameter    :: one_third = 1.0_dp/3.0_dp

  continue

    do tria = 1, n_tria

      node1   = trinode_map(1,tria)
      node2   = trinode_map(2,tria)
      node3   = trinode_map(3,tria)
      p1(1:3) = edge_data(1:3,node1)
      p2(1:3) = edge_data(1:3,node2)
      p3(1:3) = edge_data(1:3,node3)
      p       = one_third * ( p1 + p2 + p3 )
      n       = get_area_normal( p1, p2, p3 )
      if ( dot_product(n,quad_normal) < zero ) n = -n
      do nn = 1, n_output_variables
        local_data = 0.0_dp
        if ( trim(adjustl(output_variables(nn))) == 'x' ) then
          data_avg(nn,tria)  = p(1)
        else if ( trim(adjustl(output_variables(nn))) == 'y' ) then
          data_avg(nn,tria)  = p(2)
        else if ( trim(adjustl(output_variables(nn))) == 'z' ) then
          data_avg(nn,tria)  = p(3)
        else if ( trim(adjustl(output_variables(nn))) == 'nx' ) then
          data_avg(nn,tria)  = n(1)
        else if ( trim(adjustl(output_variables(nn))) == 'ny' ) then
          data_avg(nn,tria)  = n(2)
        else if ( trim(adjustl(output_variables(nn))) == 'nz' ) then
          data_avg(nn,tria)  = n(3)
        else
          local_data(1)      = edge_data(nn,node1)
          local_data(2)      = edge_data(nn,node2)
          local_data(3)      = edge_data(nn,node3)
          data_avg(nn,tria)  = point_stats(p, p1, p2, p3, local_data )
        endif
      enddo
    enddo

  end function get_avg_data

!====================================== NewView ==============================80
!
!  Shifts point to be referenced to the window "lower"corner
!
!=============================================================================80

  pure function NewView( p, origin, shft ) result ( newP )

    real(dp), dimension(3)               :: newP
    real(dp), dimension(3),   intent(in) :: p
    real(dp), dimension(3),   intent(in) :: origin
    real(dp), dimension(3),   intent(in) :: shft

    real(dp), dimension(3)               :: PShift

  continue

! shift to window center reference
    PShift = p - origin
! now move window corner reference
    newP = PShift - shft

  end function NewView

!========================= GETNODENUMBERTRI ==================================80
!
!  Returns the global node number to see if it is an overlap point
!
!=============================================================================80

  pure function getNodeNumberTri( grid, corner ) result ( node )

    use grid_types,         only : grid_type

    type(grid_type),         intent(in) :: grid
    integer, dimension(3),   intent(in) :: corner
    integer :: node

    integer :: globalnode

  continue

    globalnode = grid%l2g(corner(1))
    node       = corner(1)
    if ( grid%l2g(corner(2)) < globalnode ) then
      globalnode = grid%l2g(corner(2))
      node       = corner(2)
    endif
    if ( grid%l2g(corner(3)) < globalnode ) then
      globalnode = grid%l2g(corner(3))
      node       = corner(3)
    endif

  end function getNodeNumberTri

!========================= GETNODENUMBERQUAD =================================80
!
!  Returns the global node number to see if it is an overlap point
!
!=============================================================================80

  pure function getNodeNumberQuad( grid, corner ) result ( node )

    use grid_types,         only : grid_type

    type(grid_type),         intent(in) :: grid
    integer, dimension(4),   intent(in) :: corner
    integer :: node

    integer :: globalnode

  continue

    globalnode = grid%l2g(corner(1))
    node       = corner(1)
    if ( grid%l2g(corner(2)) < globalnode ) then
      globalnode = grid%l2g(corner(2))
      node       = corner(2)
    endif
    if ( grid%l2g(corner(3)) < globalnode ) then
      globalnode = grid%l2g(corner(3))
      node       = corner(3)
    endif
    if ( grid%l2g(corner(4)) < globalnode ) then
      globalnode = grid%l2g(corner(4))
      node       = corner(4)
    endif

  end function getNodeNumberQuad

!---------------------------------ISINSIDEBLOCK-------------------------------80
!
! Routine to determine if an edge is inside a box
!
!-----------------------------------------------------------------------------80
  pure function isInsideBlock ( ivol, pa ) &
       result ( inside )

    use sampling_headers, only : sample

    logical                                     :: inside
    integer,                         intent(in) :: ivol
    real(dp), dimension(3),          intent(in) :: pa

    real(dp), parameter     :: zero = 0.0_dp
    real(dp), dimension (3) :: p1, p2, p3, p4, p5, p6, p7, p8

  continue

    inside = .false.

    p1 = sample(ivol)%p1
    p2 = sample(ivol)%p2
    p3 = sample(ivol)%p3
    p4 = sample(ivol)%p4
    p5 = sample(ivol)%p5
    p6 = sample(ivol)%p6
    p7 = sample(ivol)%p7
    p8 = sample(ivol)%p8

    if (                                                           &
       ( tet_vol_value( p2, p1, p3, pa ) >= zero ) .and.           &
       ( tet_vol_value( p2, p3, p4, pa ) >= zero ) .and.           &
       ( tet_vol_value( p2, p4, p8, pa ) >= zero ) .and.           &
       ( tet_vol_value( p2, p8, p6, pa ) >= zero ) .and.           &
       ( tet_vol_value( p2, p5, p1, pa ) >= zero ) .and.           &
       ( tet_vol_value( p2, p6, p5, pa ) >= zero ) .and.           &
       ( tet_vol_value( p7, p6, p8, pa ) >= zero ) .and.           &
       ( tet_vol_value( p7, p5, p6, pa ) >= zero ) .and.           &
       ( tet_vol_value( p7, p1, p5, pa ) >= zero ) .and.           &
       ( tet_vol_value( p7, p3, p1, pa ) >= zero ) .and.           &
       ( tet_vol_value( p7, p8, p4, pa ) >= zero ) .and.           &
       ( tet_vol_value( p7, p4, p3, pa ) >= zero )                 &
       ) inside = .true.

  end function isInsideBlock

!=========================== GETMASS =========================================80
!
! Calculate massflux through each element face
!
!=============================================================================80
  function getMass ( face_per_cell, node_per_cell                              &
       , q_dof, x, y, z, c2n_single, n_tot, nnodesg ) result ( mass )

    use element_defs,     only : local_f2n_tet, node_per_hex
    use sampling_headers, only : verbose

    integer                                   , intent(in) :: face_per_cell
    integer                                   , intent(in) :: node_per_cell
    integer                                   , intent(in) :: n_tot
    integer                                   , intent(in) :: nnodesg
    real(dp), dimension(n_tot, nnodesg)       , intent(in) :: q_dof
    real(dp), dimension(nnodesg)              , intent(in) :: x, y, z
    integer,  dimension(node_per_hex)         , intent(in) :: c2n_single

    real(dp), dimension(face_per_cell)                     :: mass

    integer                    :: nodes_per_face, face
    integer                    :: corner
    integer, dimension(4)      :: node
!   integer                    :: node1p, node2p, node3p
    real(dp)                   :: area
    real(dp)                   :: xc, yc, zc
    real(dp)                   :: xm, ym, zm
    real(dp)                   :: xr, yr, zr
    real(dp)                   :: xl, yl, zl
    real(dp)                   :: axn, ayn, azn
    real(dp)                   :: xnorm, ynorm, znorm
    real(dp)                   :: unorm
    real(dp), dimension(3)     :: area_net
    real(dp)                   :: mass_net

    real(dp), parameter        :: one_half  = 1.0_dp / 2.0_dp
    real(dp), parameter        :: one_third = 1.0_dp / 3.0_dp
    real(dp), parameter        :: one_forth = 1.0_dp / 4.0_dp

    integer, dimension (4,4)   :: local_f2n
    real(dp), parameter :: orientation = -1.0_dp

  continue

    area_net = 0.0_dp
    mass_net = 0.0_dp
    mass     = 0.0_dp

    if ( node_per_cell == 4 ) then
      nodes_per_face = 3
      local_f2n      = local_f2n_tet
    endif

    face_index: do face = 1, face_per_cell

      corner_index: do corner = 1, nodes_per_face

        select case( nodes_per_face )
        case( 3 )
! oriented nodes of the triangle
          node(1) = c2n_single(local_f2n(face,mod(corner+0-1,3)+1))
          node(2) = c2n_single(local_f2n(face,mod(corner****,3)+1))
          node(3) = c2n_single(local_f2n(face,mod(corner****,3)+1))

          xc = (x(node(1)) + x(node(2)) + x(node(3)))*one_third
          yc = (y(node(1)) + y(node(2)) + y(node(3)))*one_third
          zc = (z(node(1)) + z(node(2)) + z(node(3)))*one_third

        case( 4 )

! oriented nodes of the quad
          node(1) = c2n_single(local_f2n(face,mod(corner+0-1,4)+1))
          node(2) = c2n_single(local_f2n(face,mod(corner****,4)+1))
          node(3) = c2n_single(local_f2n(face,mod(corner****,4)+1))
          node(4) = c2n_single(local_f2n(face,mod(corner****,4)+1))

          xc = ( x(node(1)) + x(node(2)) &
               + x(node(3)) + x(node(4)) ) * one_forth
          yc = ( y(node(1)) + y(node(2)) &
               + y(node(3)) + y(node(4)) ) * one_forth
          zc = ( z(node(1)) + z(node(2)) &
               + z(node(3)) + z(node(4)) ) * one_forth

        case default
          write(*,*) 'getMass--only 3 or 4 nodes_per_face allowed, ',&
                       'but is',nodes_per_face
          return
        end select

! Compute metric terms
        xm = x(node(1))
        ym = y(node(1))
        zm = z(node(1))

! left edge midpoint

        xl = (x(node(1)) + x(node(nodes_per_face)))*one_half
        yl = (y(node(1)) + y(node(nodes_per_face)))*one_half
        zl = (z(node(1)) + z(node(nodes_per_face)))*one_half

! right edge midpoint

        xr = (x(node(1)) + x(node(2)))*one_half
        yr = (y(node(1)) + y(node(2)))*one_half
        zr = (z(node(1)) + z(node(2)))*one_half

! average area : average of two diagonal choices:
!                triangles[ m-r-c + m-c-l ]
!                triangles[ l-m-r + l-r-c ]
! note: reverse normal to ensure outward pointing normal

        axn = orientation*one_forth*              &
              ( (yc-ym)*(zl-zr) - (zc-zm)*(yl-yr) &
              + (yr-yl)*(zc-zm) - (zr-zl)*(yc-ym) )
        ayn = orientation*one_forth*              &
              ( (zc-zm)*(xl-xr) - (xc-xm)*(zl-zr) &
              + (zr-zl)*(xc-xm) - (xr-xl)*(zc-zm) )
        azn = orientation*one_forth*              &
              ( (xc-xm)*(yl-yr) - (yc-ym)*(xl-xr) &
              + (xr-xl)*(yc-ym) - (yr-yl)*(xc-xm) )

        area = sqrt(axn*axn+ayn*ayn+azn*azn)

        area_net(1) = area_net(1) + axn
        area_net(2) = area_net(2) + ayn
        area_net(3) = area_net(3) + azn

        xnorm = axn/area
        ynorm = ayn/area
        znorm = azn/area

        unorm =   q_dof(2,node(1))*xnorm  &
                + q_dof(3,node(1))*ynorm  &
                + q_dof(4,node(1))*znorm

        mass(face)  = mass(face) + q_dof(1,node(1)) * unorm * area
        mass_net    = mass_net + mass(face)

      enddo corner_index
      if ( verbose) write(6,'(a,i5,12(1x,f20.10))') 'face',face, axn, ayn, azn

    enddo face_index

  end function getMass

!=========================== CUBIC ===========================================80
!
! Calculate roots of cubic equation
!
!=============================================================================80
  subroutine cubic (p,q,r,d,r1,r2r,r2i,r3r,r3i)

    real(dp), intent(in)  :: p
    real(dp), intent(in)  :: q
    real(dp), intent(in)  :: r
    real(dp), intent(out) :: d
    real(dp), intent(out) :: r1
    real(dp), intent(out) :: r2r
    real(dp), intent(out) :: r2i
    real(dp), intent(out) :: r3r
    real(dp), intent(out) :: r3i

    real(dp)            :: pp, qq
!   real(dp)            :: smalla, smallb, t
    real(dp)            :: pi, twopi3
    real(dp)            :: tempa, biga, tempb, bigb
    real(dp)            :: theta, coeff, bounded_theta
    real(dp), parameter :: zero     = 0.0_dp
    real(dp), parameter :: one      = 1.0_dp
    real(dp), parameter :: third = 1.0_dp/3.0_dp

    integer :: k

    continue

      r1     = zero
      r2r    = zero
      r2i    = zero
      r3r    = zero
      r3i    = zero

      pi     = acos(-1.0_dp)
      twopi3 = 2.0_dp*pi/3.0_dp

! THIS SUBROUTINE (originally) WRITTEN BY STEVEN F. YAROS, JULY 1982. SOLVES
! CUBIC EQNS. GIVEN COEFFICIENTS (P,Q,R), WITH EQUATION OF THE
! FORM  Y**3 + P*Y**2 + Q*Y + R = 0.
!
      pp = q - third*p*p
      qq = ( 2.0_dp*p*p*p - 9.0_dp*p*q + 27.0_dp*r ) / 27.0_dp
!     smalla = q - p*p/3.0_dp
!     smallb = (2.0_dp/27.0_dp)*p*p*p - (9.0_dp/27.0_dp)*p*q + r
      d   = qq*qq/4.0_dp + pp*pp*pp/27.0_dp
!     t      = 0.25_dp*smallb*smallb + smalla*smalla*smalla/27.0_dp

      if ( d < zero ) then
        coeff = 2.0*sqrt(-third*pp)
        theta = (3.0_dp/2.0_dp)*(qq/pp)*sqrt(-3.0_dp/pp)
        k     = 0
        bounded_theta = max(-one,min(one,theta-twopi3*k))
        r1    = coeff*cos(third*acos(bounded_theta))
        k     = 1
        bounded_theta = max(-one,min(one,theta-twopi3*k))
        r2r   = coeff*cos(third*acos(bounded_theta))
        k     = 2
        bounded_theta = max(-one,min(one,theta-twopi3*k))
        r3r   = coeff*cos(third*acos(bounded_theta))

!       phi  = acos(-smalla/2.0_dp/sqrt(-smalla**3.0_dp/27.0_dp))
!       rad  = 2.0_dp*sqrt(-smalla/3.0_dp)
!       r1   = rad*cos(phi/3.)-p/3.0_dp
!       r2r  = rad*cos(phi/3.0_dp+twpio3)-p/3.0_dp
        r2i  = 0.0_dp
!       r3r  = rad*cos(phi/3.0_dp+2.0_dp*twpio3)-p/3.0_dp
        r3i  = 0.0_dp

      else if ( d >= zero ) then

        tempa = -pp/2.0_dp + sqrt(d)
        biga  = (abs(tempa))**third
        biga  = sign(biga,tempa)

        tempb = -pp/2.0_dp - sqrt(d)
        bigb  = (abs(tempb))**third
        bigb  = sign(bigb,tempb)

        r1    =  biga + bigb - third*p
        r2r   = -(biga + bigb)/2.0_dp - third*p
        r2i   =  (biga - bigb)*sqrt(3.0_dp)/2.0_dp
        r3r   =  r2r
        r3i   = -r2i
       endif

  end subroutine cubic

!=========================== STDDEV ==========================================80
!
! Standard deviation of distribution
!
!=============================================================================80
  subroutine stddev ( n_lines, image, mean, dev )

    integer,                      intent(in)  :: n_lines
    real(dp), dimension(n_lines), intent(in)  :: image
    real(dp),                     intent(out) :: mean
    real(dp),                     intent(out) :: dev

    real(dp) :: var

    integer  :: i

    real(dp), parameter :: zero = 0.0_dp

    continue

      mean   = zero
      var    = zero
      dev    = zero

      do i = 1, n_lines
        mean = mean + image(i)/float(n_lines)
      enddo

      do i = 1, n_lines
        var = var + (image(i)-mean)*(image(i)-mean)/float(n_lines)
      enddo

      dev = sqrt(var)

  end subroutine stddev

!=========================== GET_TAU =========================================80
!
! Return a stress tensor given velocity gradients
!
!=============================================================================80
  pure function get_taul ( gradx, grady, gradz ) result ( tau )

    real(dp), dimension(3,3)              :: tau

    real(dp), dimension(3),   intent(in)  :: gradx
    real(dp), dimension(3),   intent(in)  :: grady
    real(dp), dimension(3),   intent(in)  :: gradz

    real(dp), parameter :: my_2   = 2.0_dp
    real(dp), parameter :: my_3   = 3.0_dp
    real(dp), parameter :: my_4   = 4.0_dp
    real(dp), parameter :: c43    = my_4/my_3
    real(dp), parameter :: c23    = my_2/my_3

    real(dp) :: ux, uy, uz
    real(dp) :: vx, vy, vz
    real(dp) :: wx, wy, wz

  continue

    ux = gradx(1)
    vx = gradx(2)
    wx = gradx(3)

    uy = grady(1)
    vy = grady(2)
    wy = grady(3)

    uz = gradz(1)
    vz = gradz(2)
    wz = gradz(3)
!       now compute components of stress vector acting on the face
    tau(1,1) = c43*ux - c23*(vy + wz)
    tau(1,2) = uy + vx
    tau(1,3) = uz + wx
    tau(2,1) = uy + vx
    tau(2,2) = c43*vy - c23*(ux + wz)
    tau(2,3) = vz + wy
    tau(3,1) = uz + wx
    tau(3,2) = vz + wy
    tau(3,3) = c43*wz - c23*(ux + vy)

  end function get_taul

  include 'tinverse.f90'
  include 'cross_product.f90'

end module sampling_funclib

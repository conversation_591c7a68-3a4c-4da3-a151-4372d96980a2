module trig_utils

  use kinddefs,        only : dp
  use fun3d_constants, only : deg_from_rad

  implicit none

  private

  public :: skew_degrees, skew_radians

contains

!===========================  SKEW_DEGREES ===================================80
!
! Degrees of skew.
!
!=============================================================================80
  pure function skew_degrees( acos_argument )

    real(dp), intent(in) :: acos_argument

    real(dp) :: skew_degrees, skew

  continue

    skew = acos_argument

    skew = min( 1.0_dp , skew ) !...avoid NaN in acos routine.
    skew = max(-1.0_dp , skew ) !...avoid NaN in acos routine.
    skew_degrees = deg_from_rad*acos( skew )

  end function skew_degrees

!===========================  SKEW_RADIANS ===================================80
!
! Degrees of skew.
!
!=============================================================================80
  pure function skew_radians( acos_argument )

    real(dp), intent(in) :: acos_argument

    real(dp) :: skew_radians, skew

  continue

    skew = acos_argument

    skew = min( 1.0_dp , skew ) !...avoid NaN in acos routine.
    skew = max(-1.0_dp , skew ) !...avoid NaN in acos routine.
    skew_radians = acos( skew )

  end function skew_radians

end module trig_utils

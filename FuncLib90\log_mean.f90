!=================================== DQ ======================================80
!
!  Compute the logarithmic mean ala <PERSON>
!
!=============================================================================80

 pure function log_mean(a, b)

   use kinddefs,        only : dp
   use fun3d_constants, only : my_half, my_1

   real(dp), intent(in) :: a, b
   real(dp)             :: log_mean

   real(dp)             :: x, fs, p

   real(dp), parameter  :: eps = 1.0e-02_dp

 continue

   x = my_half*(a+b)

!  If left state is close to right state, use an approximation

   if (abs(a-b) < eps) then
     p  = (a-b) / x
     fs = my_1 + p*p/12_dp + p*p*p*p/80_dp
   else
     fs = (a-b) / x / log(a/b)
   end if

   log_mean = x * fs

 end function log_mean

module split_comm

  implicit none

  private

  public :: split_communicator

  contains

!============================ SPLIT_COMMUNICATOR =============================80
!
! Splits the communicator for various tasks
!
!=============================================================================80
  subroutine split_communicator()

    use nml_overset_data, only : dci_on_the_fly, dci_io, dci_io_nproc
    use lmpi,             only : lmpi_comm, lmpi_id_world, lmpi_set_comm,      &
                                 lmpi_die, lmpi_nproc, lmpi_create_new_comm,   &
                                 lmpi_id, lmpi_bcast
    use suggar_info,     only : world_comm, world_nproc, fun3d_comm, is_fun3d, &
                                suggar_nproc, is_suggar, suggar_comm,          &
                                is_dci_io, fun3d_id_l2g, dci_io_comm,          &
                                fun3d_nproc, suggar_id_l2g, dci_io_id_l2g

    integer :: requested_ranks, offset, i

  continue

! First figure out basic stuff about solver ranks and sanity checks

    world_comm  = lmpi_comm
    world_nproc = lmpi_nproc

! Make sure requested number of ranks does not exceed ranks available

    requested_ranks = 1 ! minimum for solver itself
    if (dci_on_the_fly) requested_ranks = requested_ranks + suggar_nproc
    if (dci_io)         requested_ranks = requested_ranks + dci_io_nproc

    if ( requested_ranks > world_nproc ) then
      write(*,*) 'Error: too many ranks requested by various options.'
      call lmpi_die
      stop
    endif

! Keep track of an offset in the rank listing to help in placing later processes

    offset = 0

! Determine the number of ranks available to solver

    fun3d_nproc = world_nproc
    if (dci_on_the_fly) fun3d_nproc = fun3d_nproc - suggar_nproc
    if (dci_io) fun3d_nproc = fun3d_nproc - dci_io_nproc

! Determine if I belong to the solver

    is_fun3d = (lmpi_id_world <= fun3d_nproc-1)

! Create a comm for the solver if needed

    fun3d_comm = world_comm

    if ( fun3d_nproc /= world_nproc ) then
      call lmpi_create_new_comm(world_comm,fun3d_nproc,offset,fun3d_comm)
    endif

! Set the comm in the MPI module for all solver ranks

    if (is_fun3d) call lmpi_set_comm(fun3d_comm)

    allocate(fun3d_id_l2g(0:fun3d_nproc-1))

    if (is_fun3d) fun3d_id_l2g(lmpi_id) = lmpi_id + offset

    do i = 0, fun3d_nproc-1
      call lmpi_bcast(fun3d_id_l2g(i),i,world_comm)
    end do

    offset = offset + fun3d_nproc

! Stuff specific to a split libsuggar rank comes next if requested

    place_suggar : if (dci_on_the_fly) then

      is_suggar = (lmpi_id_world >  offset-1 .and.                             &
                   lmpi_id_world <= offset+suggar_nproc-1)

      call lmpi_create_new_comm(world_comm,suggar_nproc,offset,suggar_comm)

      if (is_suggar) call lmpi_set_comm(suggar_comm)

      allocate(suggar_id_l2g(0:suggar_nproc-1))

      if (is_suggar) suggar_id_l2g(lmpi_id) = lmpi_id + offset

      do i = 0, suggar_nproc-1
        call lmpi_bcast(suggar_id_l2g(i),i+offset,world_comm)
      end do

      offset = offset + suggar_nproc

    end if place_suggar

! Stuff specific to a split dci_io rank comes next if requested

    place_dci_io : if (dci_io) then

      is_dci_io = (lmpi_id_world >  offset-1 .and.                             &
                   lmpi_id_world <= offset+dci_io_nproc-1)

      call lmpi_create_new_comm(world_comm,dci_io_nproc,offset,dci_io_comm)

      if (is_dci_io) call lmpi_set_comm(dci_io_comm)

      allocate(dci_io_id_l2g(0:dci_io_nproc-1))

      if (is_dci_io) dci_io_id_l2g(lmpi_id) = lmpi_id + offset

      do i = 0, dci_io_nproc-1
        call lmpi_bcast(dci_io_id_l2g(i),i+offset,world_comm)
      end do

      offset = offset + dci_io_nproc

    end if place_dci_io

  end subroutine split_communicator

end module split_comm

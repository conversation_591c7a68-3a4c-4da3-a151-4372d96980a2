module turb_ras_2011

  use kinddefs,       only : dp, odp
  use lmpi,           only : lmpi_master, lmpi_conditional_stop
  use info_depr,      only : grad_x_y_z_contents, skeleton
  use grid_types,      only : grid_type
  use solution_types,  only : soln_type
  use comprow_types,   only : crow_flow

  implicit none

  private

  public :: residual_ras_2011
  public :: jacobian_ras_2011

  public :: ras_2011_resid
  public :: ras_2011_jacob
  public :: bc_ras_2011_setturb

  public :: edge_assembly_res_conv_diff
  public :: edge_assembly_jac_conv_diff

  real(dp), parameter    :: zero   = 0.0_dp

  real(dp), dimension(:), allocatable :: s11deriv, s12deriv
  real(dp), dimension(:), allocatable :: s13deriv, s22deriv
  real(dp), dimension(:), allocatable :: s23deriv, s33deriv

  logical :: sderivs_allocated = .false.

  ! sa diffusion treatment with --mixed
  ! = 0 element-assembly of sa diffusion (no positivity)
  ! = 1 edge-assembly with positivity
  ! = 2 edge-assembly without positivity
  ! (note parameters 0 and 2 give identical results)
  integer, parameter :: sa_diffusion_edge_tets  = 0

  logical :: positive_turb_diffusion = .true. ! Enforce positivity of
             ! turbulent diffusion in tetrahedral weakly coupled paths :


contains

!=============================== RESIDUAL_SA =================================80
!
! Driver routine for residual evaluation for Spalart-Allmaras model
!
!=============================================================================80

  subroutine residual_ras_2011(grid, soln)

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln

  continue

    call ras_2011_resid(soln%eqn_set, grid%nnodes0,      grid%nnodes01,        &
                  grid%nedgeloc,     grid%eptr,                                &
                  soln%turb,         soln%q_dof,                               &
                  soln%turbres,      grid%slen,                                &
                  soln%gradx,        soln%grady,                               &
                  soln%gradz,        grid%vol,                                 &
                  grid%xn,           grid%yn,                                  &
                  grid%zn,           grid%ra,                                  &
                  soln%dft1,         soln%dft2,                                &
                  grid%des_slen,     grid%iflagslen,                           &
                  grid%x,            grid%y,                                   &
                  grid%z,            grid%facespeed,                           &
                  soln%n_turb,       soln%n_tot,        soln%n_grd, 1,         &
                  grid%nelem, grid%elem, grid%nedgeloc_2d, grid%nnodes0_2d,    &
                  grid%node_pairs_2d, soln%viscous_method,                     &
                  grid%dxdt, grid%dydt, grid%dzdt,                             &
                  grid%nbound, grid%bc )

  end subroutine residual_ras_2011


!================================= JACOBIAN_SA ===============================80
!
! Driver routine for jacobians of Spalart-Allmaras model.
!
!=============================================================================80

  subroutine jacobian_ras_2011(grid, soln, crow )

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    continue

    soln%a_turb_diag = 0._dp
    soln%a_turb_off  = 0._odp

      call ras_2011_jacob(soln%eqn_set, soln%viscous_method, grid%nnodes0,     &
                  grid%nnodes01, grid%nedgeloc, soln%max_nnz,                  &
                  grid%eptr, soln%turb, soln%q_dof, grid%slen, soln%gradx,     &
                  soln%grady, soln%gradz, grid%vol, grid%xn, grid%yn,          &
                  grid%zn, grid%ra, soln%dft1, soln%dft2, grid%des_slen,       &
                  soln%a_turb_diag, soln%a_turb_off, crow%fhelp,               &
                  grid%iflagslen, grid%facespeed, soln%n_turb, soln%n_tot,     &
                  soln%n_grd, crow%g2m, grid%nedgeloc_2d, grid%nelem,          &
                  grid%elem, grid%x, grid%y, grid%z, crow%ia, crow%ja,         &
                  crow%nzg2m, grid%nnodes0_2d, grid%node_pairs_2d,             &
                  grid%dxdt, grid%dydt, grid%dzdt, grid%nbound, grid%bc)

  end subroutine jacobian_ras_2011


!============================ BC_RAS_2011_SETTURB ============================80
!
! This routine sets turbulence quantities on viscous boundaries
!
!=============================================================================80

  subroutine bc_ras_2011_setturb(nnodes01, nbnode, ibnode, turb, amut, n_turb  &
                       , ibc, k_wf, mu_t_wf )

    use bc_names, only : viscous_wall_function, viscous_wf_trs

    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: nbnode
    integer,  dimension(nbnode),          intent(in)    :: ibnode
    integer,                              intent(in)    :: n_turb
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turb
    real(dp), dimension(nnodes01),        intent(inout) :: amut
    integer,                              intent(in)    :: ibc
    real(dp), dimension(:),               intent(in)    :: k_wf
    real(dp), dimension(:),               intent(in)    :: mu_t_wf

    integer :: i, inode

    continue

    select case ( ibc )

      case ( viscous_wall_function, viscous_wf_trs )

        do i = 1,nbnode
          inode         = ibnode(i)
          turb(1,inode) = k_wf(i)
          amut(inode)   = mu_t_wf(i)
        end do

      case default

        do i = 1,nbnode
          inode         = ibnode(i)
          turb(1,inode) = zero
          amut(inode)   = zero
        end do

    end select

  end subroutine bc_ras_2011_setturb


!================================= SA_RESID ==================================80
!
! Calculates the residual for Spalart's model
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine ras_2011_resid(eqn_set, nnodes0, nnodes01, nedgeloc, eptr, turb,  &
                      qnode, res, slen, gradx, grady, gradz, vol, xn, yn, zn,  &
                      ra, dft1, dft2, des_slen, iflagslen, x, y, z, facespeed, &
                      n_turb, n_tot, n_grd, eq_loc, nelem, elem, nedgeloc_2d,  &
                      nnodes0_2d, node_pairs_2d, viscous_method, dxdt, dydt,   &
                      dzdt, nbound, bc)

    use info_depr,       only : tref, xmach, re, twod, mixed
    use nml_two_d_trans, only : turb_transition
    use debug_defs,      only : test_freestream
    use turb_sa_const,   only : cdes, cv1, vkar, cw2, cw3, ct3, ct4, cb1,      &
                                cw1, dacles_mariani, ddes, ddes_mod1, cddes,   &
                                use_edwards_mod, sarc, sarc_cr3
    use turb_kw_const,   only : turb_compress_model
    use fluid,           only : gamma, sutherland_constant
    use element_types,   only : elem_type
    use turb_parameters, only : t_prod
    use turb_util,       only : edget, strain_tensor_deriv
    use solution_types,  only : compressible, incompressible
    use bc_types,        only : bcgrid_type
    use turbulence_info, only : turbulence_model

    integer, intent(in) :: nnodes0, nnodes01, n_tot, n_grd, eq_loc
    integer, intent(in) :: eqn_set, nedgeloc, n_turb, nelem, nedgeloc_2d
    integer, intent(in) :: nnodes0_2d, viscous_method, nbound

    integer,      dimension(nnodes01),       intent(in)    :: iflagslen
    integer,      dimension(2,nedgeloc),     intent(in)    :: eptr
    integer,      dimension(2,nnodes0_2d),   intent(in)    :: node_pairs_2d

    real(dp),  dimension(n_turb,nnodes01),   intent(in) :: turb
    real(dp),  dimension(n_tot,nnodes01),    intent(inout) :: qnode
    real(dp),  dimension(:,:),               intent(inout) :: res
    real(dp),  dimension(nnodes01),          intent(in)    :: slen
    real(dp),  dimension(nnodes01),          intent(in)    :: des_slen
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: gradx,grady,gradz
    real(dp),  dimension(nnodes01),          intent(in)    :: x, y, z, vol
    real(dp),  dimension(nedgeloc),          intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),          intent(in)    :: facespeed
    real(dp),  dimension(nedgeloc),          intent(inout) :: dft1, dft2
    real(dp),  dimension(nnodes01),          intent(in)    :: dxdt, dydt, dzdt

    type(elem_type), dimension(nelem), intent(in) :: elem
    type(bcgrid_type), dimension(nbound),  intent(in)  :: bc

    integer :: i,ielem,node_src_eval,ii

    real(dp)    :: dest, gg, prod, rr, s, sij_mag, sw, bot
    real(dp)    :: chi, cstar
    real(dp)    :: ft2, fv1, fv2, fw
    real(dp)    :: a, p, rho, rnu, source, temp
    real(dp)    :: uy, uz, vkar2
    real(dp)    :: vx, vz, wx, wy, distance
    real(dp)    :: ux, vy, wz
    real(dp)    :: my_xmach
    real(dp)    :: velterm, rd, fd, rhoinv, botinv

    real(dp)    :: arg, xmr
    real(dp)    :: comp_term
    real(dp),  dimension(3,3) :: sij
    real(dp)    :: strace, dudxsij
    real(dp)    :: xis, xisabs, rstar, rtilde, fr1
    real(dp)    :: w12, w13, w23, s11, s22, s33, s12, s13, s23

    real(dp), parameter    :: my_0     =     0.0_dp
    real(dp), parameter    :: my_1m12  =  1.e-12_dp
    real(dp), parameter    :: my_00001 = 0.00001_dp
    real(dp), parameter    :: my_6th   =     1.0_dp/6.0_dp
    real(dp), parameter    :: onethird =     1.0_dp/3.0_dp
    real(dp), parameter    :: my_haf   =     0.5_dp
    real(dp), parameter    :: my_1     =     1.0_dp
    real(dp), parameter    :: two      =     2.0_dp

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp
    fd = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'sa_resid: only in/compress. perfect gas')
    end select

    xmr  = my_xmach / re

!   Evaluate the weights for the diffusion terms on tets

    if ( viscous_method == 0 ) then
      if ( .not.mixed .or. ( mixed .and. sa_diffusion_edge_tets > 0 ) ) then
        do ielem = 1, nelem
          if ( elem(ielem)%type_cell /= 'tet' ) cycle
          call edget(eqn_set, nnodes01, elem(ielem)%ncell, nedgeloc,           &
                     elem(ielem)%c2n, elem(ielem)%c2e, x, y, z, qnode, turb,   &
                     dft1, dft2, n_turb, n_tot, 0)
        end do
      endif
    endif

! Now lets compute the convective and diffusion terms.  Note the diffusion
! terms here are only for tets.  dft1 and dft2 are zero everywhere else.  We
! will pick up the diffusion contributions from other element types later on.

    call edge_assembly_res_conv_diff(                                          &
                      eqn_set, nnodes0, nnodes01, nedgeloc, eptr, turb,        &
                      qnode, res, xn, yn, zn,                                  &
                      ra, dft1, dft2, facespeed,                               &
                      n_turb, n_tot, eq_loc, nedgeloc_2d, viscous_method )

    node_src_eval   = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

!   For SARC only, need to get D(Sij)/Dt terms (ignoring time deriv for now)
!   = u_k*d(Sij)/dx_k (summing over k)

    if (sarc) then
      if ( .not. sderivs_allocated ) then
        allocate(s11deriv(nnodes01))
        allocate(s12deriv(nnodes01))
        allocate(s13deriv(nnodes01))
        allocate(s22deriv(nnodes01))
        allocate(s23deriv(nnodes01))
        allocate(s33deriv(nnodes01))
        sderivs_allocated = .true.
      endif
      call strain_tensor_deriv(eqn_set,                                        &
                   nnodes0, nnodes01, nedgeloc, eptr, qnode,                   &
                   x, y, z, gradx, grady, gradz,                               &
                   xn, yn, zn, ra, vol,                                        &
                   nedgeloc_2d, node_pairs_2d, nnodes0_2d, nelem, elem,        &
                   n_tot, n_grd, dxdt, dydt, dzdt, nbound, bc,                 &
                   s11deriv, s12deriv, s13deriv, s22deriv, s23deriv, s33deriv)
    end if

!   Now lets compute the source term

    regular_source_term : if (.not. use_edwards_mod) then

      source1 : do ii = 1, node_src_eval

        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if

        if ( slen(i) < epsilon(1._dp) ) cycle !skip source on wall

        ux   = gradx(2,i)
        uy   = grady(2,i)
        uz   = gradz(2,i)
        vx   = gradx(3,i)
        vy   = grady(3,i)
        vz   = gradz(3,i)
        wx   = gradx(4,i)
        wy   = grady(4,i)
        wz   = gradz(4,i)

        a      = my_1
        rho    = my_1
        rhoinv = my_1
        if ( eqn_set == compressible ) then
          rho  = qnode(1,i)
          rhoinv = my_1/rho
          p    = qnode(5,i)
          temp = gamma * p * rhoinv
          a    = sqrt( temp )
          rnu  = viscosity_law( cstar, temp ) * rhoinv
        else
          rnu  = my_1
        end if

!       Determine s(i,j)
        sij(1,1) = my_haf*(ux + ux)
        sij(1,2) = my_haf*(uy + vx)
        sij(1,3) = my_haf*(uz + wx)

        sij(2,1) = sij(1,2)
        sij(2,2) = my_haf*(vy + vy)
        sij(2,3) = my_haf*(vz + wy)

        sij(3,1) = sij(1,3)
        sij(3,2) = sij(2,3)
        sij(3,3) = my_haf*(wz + wz)

!       Make s(i,j) traceless
        strace = zero
        if ( twod ) then
          strace   = sij(1,1) + sij(3,3)
          sij(1,1) = sij(1,1) - my_haf*strace
          sij(3,3) = sij(3,3) - my_haf*strace
        else
          strace   = sij(1,1) + sij(2,2) + sij(3,3)
          sij(1,1) = sij(1,1) - onethird*strace
          sij(2,2) = sij(2,2) - onethird*strace
          sij(3,3) = sij(3,3) - onethird*strace
        endif

        dudxsij = two*(ux*sij(1,1) +uy*sij(1,2) +uz*sij(1,3)                   &
                      +vx*sij(2,1) +vy*sij(2,2) +vz*sij(2,3)                   &
                      +wx*sij(3,1) +wy*sij(3,2) +wz*sij(3,3))
        velterm = ux*ux + uy*uy + uz*uz + vx*vx + vy*vy + vz*vz +              &
                  wx*wx + wy*wy + wz*wz

!       Old way of getting Sw

        distance = slen(i)
        if(turbulence_model == 'des') then
          if(ddes) then  ! Delayed DES (DDES) (TCFD 20:181-195 2006)
            rd = turb(1,i)*my_xmach/                                           &
                 (sqrt(velterm)*vkar*vkar*slen(i)*slen(i)*re)
            fd = my_1 - tanh((8.0_dp*rd)*(8.0_dp*rd)*(8.0_dp*rd))
            distance = slen(i) - fd*max(my_0, slen(i)-cdes*des_slen(i))
          else  ! Standard DES
            distance = min(slen(i),cdes*des_slen(i))
          end if
        end if

        chi    = turb(1,i) / rnu

        fv1    = chi**3 / (chi**3+cv1**3)
        fv2    = my_1 - chi/(my_1+chi*fv1)
        bot    = vkar * vkar * distance * distance
        s      = sqrt((wy-vz)**2+(uz-wx)**2+(vx-uy)**2)

        if ( s <= 1.0e-8_dp) s = 1.0e-8_dp

        if (dacles_mariani) then
          sij_mag   = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                    &
                            + 2._dp*(ux**2+vy**2+wz**2))
          s = s + 2._dp*min(my_0,sij_mag-s)
        end if

        if(turbulence_model == 'des') then
          if(ddes .and. ddes_mod1) then  ! Delayed DES (DDES_MOD1)
            if (fd > cddes) then
              s    = s* (my_1 - fd) / (my_1 - cddes)
            end if
          end if
        end if

        botinv = my_1/bot

        sw     = s + xmr*turb(1,i)*botinv*fv2
        sw     = max(sw,my_00001)

        rr     = xmr * turb(1,i) * botinv / sw
        if (rr > 10.0_dp) rr = 10.0_dp

        gg = rr + cw2*(rr**6-rr)
        fw = gg * ((my_1+cw3**6)/(gg**6+cw3**6))**(my_6th)

!       Avoid computing exp(-ct4*chi*chi) which may cause underflow.
!       It is not needed anyway when t_prod = 0.

        if ( abs(t_prod) >= 1.0e-12_dp ) then
          ft2 = ct3 * exp(-ct4*chi*chi)
        else
          ft2 = 0.0_dp
        endif

        vkar2  = vkar * vkar

        fr1 = my_1
!       SARC term:
        if (sarc) then
          s11 = my_haf*(ux + ux)
          s12 = my_haf*(uy + vx)
          s13 = my_haf*(uz + wx)
          s22 = my_haf*(vy + vy)
          s23 = my_haf*(vz + wy)
          s33 = my_haf*(wz + wz)
          w12 = my_haf*(uy - vx)
          w13 = my_haf*(uz - wx)
          w23 = my_haf*(vz - wy)
          xis = s11*s11 + s22*s22 + s33*s33 +                                 &
                2._dp*s12*s12 + 2._dp*s13*s13 + 2._dp*s23*s23
          xisabs = sqrt(2._dp*xis)
          rstar=xisabs/s
          rtilde=2._dp/(0.5_dp*(s**2+xisabs**2))**2*                          &
                    ( -w12*s12deriv(i)*(s11-s22)                              &
                      -w13*s13deriv(i)*(s11-s33)                              &
                      -w23*s23deriv(i)*(s22-s33)                              &
                      +s12*(-w12*(s22deriv(i)-s11deriv(i))                    &
                                 -w13*s23deriv(i)-w23*s13deriv(i))            &
                      +s13*(-w13*(s33deriv(i)-s11deriv(i))                    &
                                 -w12*s23deriv(i)+w23*s12deriv(i))            &
                      +s23*(-w23*(s33deriv(i)-s22deriv(i))                    &
                                 +w12*s13deriv(i)+w13*s12deriv(i)) )
          fr1=4._dp*rstar/(1._dp+rstar)*(1._dp-sarc_cr3*                      &
              atan(12._dp*rtilde))-1._dp
        end if

        prod   = (cb1*(fr1-ft2)*sw*turb(1,i))

!       test for laminar node
        if (turb_transition) then
          if (iflagslen(i) < 0) prod=my_0
        end if

        dest   = xmr * (cw1*fw-cb1/vkar2*ft2)*(turb(1,i)/distance)**2

        comp_term = 0.0_dp
        if ( turb_compress_model == 'ssz' ) then
! compressibility - aiaa 95-0863 Shur et al.
          comp_term = 3.5_dp*xmr*rho*turb(1,i)*turb(1,i)*dudxsij/(a*a)
          comp_term = 3.5_dp*xmr*rho*turb(1,i)*turb(1,i)*velterm/(a*a)
          dest   = dest + comp_term
        endif

        source = vol(i) * (prod-dest)

        if (test_freestream) then
          source = 0._dp
        end if

        res(eq_loc,i) = res(eq_loc,i) - t_prod*source

      end do source1

    else regular_source_term

     source2 : do ii = 1, node_src_eval

        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if

        if ( slen(i) < epsilon(1._dp) ) cycle !skip source on wall

        ux   = gradx(2,i)
        uy   = grady(2,i)
        uz   = gradz(2,i)
        vx   = gradx(3,i)
        vy   = grady(3,i)
        vz   = gradz(3,i)
        wx   = gradx(4,i)
        wy   = grady(4,i)
        wz   = gradz(4,i)

        if ( eqn_set == compressible ) then
          rho  = qnode(1,i)
          rhoinv = my_1/rho
          p    = qnode(5,i)
          temp = gamma * p * rhoinv
          rnu  = viscosity_law( cstar, temp ) * rhoinv
        else
          rnu  = my_1
        end if

!       Determine s(i,j)
        sij(1,1) = my_haf*(ux + ux)
        sij(1,2) = my_haf*(uy + vx)
        sij(1,3) = my_haf*(uz + wx)

        sij(2,1) = sij(1,2)
        sij(2,2) = my_haf*(vy + vy)
        sij(2,3) = my_haf*(vz + wy)

        sij(3,1) = sij(1,3)
        sij(3,2) = sij(2,3)
        sij(3,3) = my_haf*(wz + wz)

!       Make s(i,j) traceless
        strace = zero
        if ( twod ) then
          strace   = sij(1,1) + sij(3,3)
          sij(1,1) = sij(1,1) - my_haf*strace
          sij(3,3) = sij(3,3) - my_haf*strace
        else
          strace   = sij(1,1) + sij(2,2) + sij(3,3)
          sij(1,1) = sij(1,1) - onethird*strace
          sij(2,2) = sij(2,2) - onethird*strace
          sij(3,3) = sij(3,3) - onethird*strace
        endif

!       Old way of getting Sw

        distance = slen(i)
        if(turbulence_model == 'des') then
          if(ddes) then  ! Delayed DES (DDES) (TCFD 20:181-195 2006)
            velterm = ux*ux + uy*uy + uz*uz + vx*vx + vy*vy + vz*vz +          &
                      wx*wx + wy*wy + wz*wz
            rd = turb(1,i)*my_xmach/                                           &
                 (sqrt(velterm)*vkar*vkar*slen(i)*slen(i)*re)
            fd = my_1 - tanh((8.0_dp*rd)*(8.0_dp*rd)*(8.0_dp*rd))
            distance = slen(i) - fd*max(my_0, slen(i)-cdes*des_slen(i))
          else  ! Standard DES
            distance = min(slen(i),cdes*des_slen(i))
          end if
        end if

        chi    = turb(1,i) / rnu
        chi    = (turb(1,i)+my_1m12) / rnu      ! Adjust turb for Edwards
        fv1    = chi**3 / (chi**3+cv1**3)
        fv2    = my_1 - chi/(my_1+chi*fv1)
        bot    = vkar * vkar * distance * distance
        s      = sqrt((wy-vz)**2+(uz-wx)**2+(vx-uy)**2)
        if ( s <= 1.0e-8_dp) s = 1.0e-8_dp

        if(turbulence_model == 'des') then
          if(ddes .and. ddes_mod1) then  ! Delayed DES (DDES_MOD1)
            if (fd > cddes) then
              s    = s* (my_1 - fd) / (my_1 - cddes)
            end if
          end if
        end if

        sw     = s + xmr*turb(1,i)/bot*fv2
        sw     = max(sw,my_00001)

        rr     = xmr * turb(1,i) / bot / sw
        if (rr > 10.0_dp) rr = 10.0_dp

!       Edwards' mod

        sw     = s*(1.0_dp/chi + fv1)
        arg    = vkar*vkar*distance*distance
        rr     = tanh(xmr*turb(1,i)/arg/sw) / tanh(1.0_dp)

!       End of Edwards' mod

        gg     = rr + cw2*(rr**6-rr)
        fw     = gg * ((my_1+cw3**6)/(gg**6+cw3**6))**(my_6th)
        ft2    = ct3 * exp(-ct4*chi*chi)
        vkar2  = vkar * vkar

        fr1 = my_1
!       SARC term:
        if (sarc) then
          s11 = my_haf*(ux + ux)
          s12 = my_haf*(uy + vx)
          s13 = my_haf*(uz + wx)
          s22 = my_haf*(vy + vy)
          s23 = my_haf*(vz + wy)
          s33 = my_haf*(wz + wz)
          w12 = my_haf*(uy - vx)
          w13 = my_haf*(uz - wx)
          w23 = my_haf*(vz - wy)
          xis = s11*s11 + s22*s22 + s33*s33 +                                 &
                2._dp*s12*s12 + 2._dp*s13*s13 + 2._dp*s23*s23
          xisabs = sqrt(2._dp*xis)
          rstar=xisabs/s
          rtilde=2._dp/(0.5_dp*(s**2+xisabs**2))**2*                          &
                    ( -w12*s12deriv(i)*(s11-s22)                              &
                      -w13*s13deriv(i)*(s11-s33)                              &
                      -w23*s23deriv(i)*(s22-s33)                              &
                      +s12*(-w12*(s22deriv(i)-s11deriv(i))                    &
                                 -w13*s23deriv(i)-w23*s13deriv(i))            &
                      +s13*(-w13*(s33deriv(i)-s11deriv(i))                    &
                                 -w12*s23deriv(i)+w23*s12deriv(i))            &
                      +s23*(-w23*(s33deriv(i)-s22deriv(i))                    &
                                 +w12*s13deriv(i)+w13*s12deriv(i)) )
          fr1=4._dp*rstar/(1._dp+rstar)*(1._dp-sarc_cr3*                      &
              atan(12._dp*rtilde))-1._dp
        end if

        prod   = (cb1*(fr1-ft2)*sw*turb(1,i))

!       test for laminar node
        if (turb_transition) then
          if (iflagslen(i) < 0) prod=my_0
        end if

        dest   = xmr * (cw1*fw-cb1/vkar2*ft2)*(turb(1,i)/distance)**2

        source = vol(i) * (prod-dest)

        if (test_freestream) then
          source = 0._dp
        end if

        res(eq_loc,i) = res(eq_loc,i) - t_prod*source

      end do source2

    end if regular_source_term

! Diffusion terms on non-tetrahedral element types

    if ( viscous_method == 0 .and. mixed ) then

      do ielem = 1, nelem
        if ( sa_diffusion_edge_tets > 0 .and.     &
             elem(ielem)%type_cell == 'tet' ) cycle
        call sa_resid_diff_element(                                            &
                             eqn_set, nnodes0, nnodes01,                       &
                             turb, qnode, res,                                 &
                             elem(ielem)%ncell,                                &
                             elem(ielem)%c2n, x, y, z,                         &
                             elem(ielem)%local_f2n, elem(ielem)%local_e2n,     &
                             elem(ielem)%local_f2e, elem(ielem)%e2n_2d,        &
                             elem(ielem)%face_per_cell,                        &
                             elem(ielem)%node_per_cell,                        &
                             elem(ielem)%edge_per_cell, elem(ielem)%type_cell, &
                             n_turb, n_tot, elem(ielem)%face_2d,               &
                             elem(ielem)%chk_norm )
      enddo

    endif

! Diffusion terms via edge-based integration.

    if ( viscous_method == 1 ) then

      call sa_resid_diff_avglsq(                                               &
                             eqn_set, nnodes0, nnodes01,                       &
                             nedgeloc, eptr, turb, qnode, res, gradx, grady,   &
                             gradz, xn, yn, zn, ra, x, y, z,                   &
                             nedgeloc_2d, n_turb, n_tot, n_grd )
    endif

  end subroutine ras_2011_resid


!================================= SA_JACOB ==================================80
!
! Calculates jacobians of the loosely coupled Spalart model
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine ras_2011_jacob(eqn_set, viscous_method, nnodes0, nnodes01,        &
                      nedgeloc, max_nnz, eptr, turb, qnode, slen, gradx, grady,&
                      gradz, vol, xn, yn, zn, ra, dft1, dft2, des_slen, a_diag,&
                      a_off, fhelp, iflagslen, facespeed, n_turb, n_tot, n_grd,&
                      g2m, nedgeloc_2d, nelem, elem, x, y, z, ia, ja, nzg2m,   &
                      nnodes0_2d, node_pairs_2d, dxdt, dydt, dzdt, nbound, bc)

    use info_depr,       only : tref, xmach, re, twod, mixed
    use nml_two_d_trans, only : turb_transition
    use debug_defs,      only : test_freestream
    use turb_kw_const,   only : turb_compress_model
    use turb_sa_const,   only : cdes, cv1, vkar, cw2, cw3, ct3, ct4,           &
                                cb1, cw1, dacles_mariani, ddes,                &
                                ddes_mod1, cddes, use_edwards_mod, sarc,       &
                                sarc_cr3
    use fluid,           only : gamma, sutherland_constant
    use element_types,   only : elem_type
    use turb_parameters, only : t_prod
    use solution_types,  only : compressible, incompressible
    use bc_types,        only : bcgrid_type
    use turb_util,       only : strain_tensor_deriv
    use turbulence_info, only : turbulence_model

    integer, intent(in) :: eqn_set, viscous_method, nnodes0_2d
    integer, intent(in) :: nnodes0, nnodes01, n_tot, n_grd
    integer, intent(in) :: nedgeloc, n_turb
    integer, intent(in) :: max_nnz, nelem
    integer, intent(in) :: nedgeloc_2d, nbound

    integer, dimension(2,nedgeloc),   intent(in) :: eptr
    integer, dimension(2,nedgeloc),   intent(in) :: fhelp
    integer, dimension(nnodes01),     intent(in) :: iflagslen
    integer, dimension(:),            intent(in) :: g2m,ia,ja,nzg2m
    integer, dimension(2,nnodes0_2d), intent(in) :: node_pairs_2d

    real(dp),  dimension(n_turb,nnodes01),       intent(inout) :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(inout) :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off
    real(dp),  dimension(nnodes01),              intent(in)    :: slen
    real(dp),  dimension(nnodes01),              intent(in)    :: des_slen
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradz
    real(dp),  dimension(nnodes01),              intent(in)    :: vol
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),              intent(in)    :: facespeed
    real(dp),  dimension(nedgeloc),              intent(in)    :: dft1, dft2
    real(dp),  dimension(nnodes01),              intent(in)    :: dxdt, dydt
    real(dp),  dimension(nnodes01),              intent(in)    :: dzdt

    type(elem_type), dimension(nelem), intent(in) :: elem
    type(bcgrid_type), dimension(nbound),  intent(in)  :: bc

    integer :: i, row, node_src_eval, ii, ielem

    real(dp) :: d, d1, d1p, gg, p1, pp, rr, s, sij_mag, sw, bot
    real(dp) :: chi, const, cstar
    real(dp) :: dswdnu, ft2, fv1, fv2, fw
    real(dp) :: p, rho, rnu, temp
    real(dp) :: term1, term2
    real(dp) :: vkar2, distance
    real(dp) :: uy, uz, vx, vz, wx, wy, ux, vy, wz
    real(dp) :: arg,dchidturb,dft2dturb,dfv1dturb,dswdturb
    real(dp) :: baseterm,drdturb,dgdturb,dfwdturb,ddestdturb
    real(dp) :: cosharg, xmr, xvkar2, rhoinv, botinv
    real(dp) :: my_xmach
    real(dp) :: velterm, rd, fd
    real(dp) :: a, comp_term, term3
    real(dp),  dimension(3,3) :: sij
    real(dp)    :: strace, dudxsij
    real(dp)    :: xis, xisabs, rstar, rtilde, fr1
    real(dp)    :: w12, w13, w23, s11, s22, s33, s12, s13, s23

    real(dp), parameter    :: my_0     =     0.0_dp
    real(dp), parameter    :: my_1m12  =  1.e-12_dp
    real(dp), parameter    :: my_00001 = 0.00001_dp
    real(dp), parameter    :: my_6th   =     1.0_dp/6.0_dp
    real(dp), parameter    :: onethird =     1.0_dp/3.0_dp
    real(dp), parameter    :: my_76th  =     7.0_dp/6.0_dp
    real(dp), parameter    :: my_haf   =     0.5_dp
    real(dp), parameter    :: my_1     =     1.0_dp
    real(dp), parameter    :: two      =     2.0_dp

  continue

    my_xmach = 0.0_dp
    fd = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'ras_2011_jacob: only in perfect gas')
    end select

    xmr    = my_xmach / re
    vkar2  = vkar*vkar
    xvkar2 = my_1/vkar2

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    node_src_eval  = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

!   Now lets compute the convective and second order terms
!   This assumes that the dft1 and dft2 terms have previously been computed
!   for the residual computation!

    call edge_assembly_jac_conv_diff(                                          &
                      eqn_set, viscous_method, nnodes0, nnodes01,              &
                      nedgeloc, max_nnz, eptr, turb, qnode,                    &
                      xn, yn, zn, ra, dft1, dft2, a_diag,                      &
                      a_off, fhelp, facespeed, n_turb, n_tot,                  &
                      g2m, nedgeloc_2d )

    cstar = sutherland_constant / tref

!     For SARC only, need to get D(Sij)/Dt terms (ignoring time deriv for now)
!     = u_k*d(Sij)/dx_k (summing over k)

      if (sarc) then
        if ( .not. sderivs_allocated ) then
          allocate(s11deriv(nnodes01))
          allocate(s12deriv(nnodes01))
          allocate(s13deriv(nnodes01))
          allocate(s22deriv(nnodes01))
          allocate(s23deriv(nnodes01))
          allocate(s33deriv(nnodes01))
          sderivs_allocated = .true.
        endif
        call strain_tensor_deriv(eqn_set,                                      &
                   nnodes0, nnodes01, nedgeloc, eptr, qnode,                   &
                   x, y, z, gradx, grady, gradz,                               &
                   xn, yn, zn, ra, vol,                                        &
                   nedgeloc_2d, node_pairs_2d, nnodes0_2d, nelem, elem,        &
                   n_tot, n_grd, dxdt, dydt, dzdt, nbound, bc,                 &
                   s11deriv, s12deriv, s13deriv, s22deriv, s23deriv, s33deriv)
      end if

      regular_source_term : if (.not. use_edwards_mod) then

        source1 : do ii = 1, node_src_eval

          if (twod) then
            i = node_pairs_2d(1,ii)
          else
            i = ii
          end if

        if ( slen(i) < epsilon(1._dp) ) cycle !skip source on wall

          ux   = gradx(2,i)
          uy   = grady(2,i)
          uz   = gradz(2,i)
          vx   = gradx(3,i)
          vy   = grady(3,i)
          vz   = gradz(3,i)
          wx   = gradx(4,i)
          wy   = grady(4,i)
          wz   = gradz(4,i)

!         Determine s(i,j)
          sij(1,1) = my_haf*(ux + ux)
          sij(1,2) = my_haf*(uy + vx)
          sij(1,3) = my_haf*(uz + wx)

          sij(2,1) = sij(1,2)
          sij(2,2) = my_haf*(vy + vy)
          sij(2,3) = my_haf*(vz + wy)

          sij(3,1) = sij(1,3)
          sij(3,2) = sij(2,3)
          sij(3,3) = my_haf*(wz + wz)

          strace = zero
          if ( twod ) then
            strace   = sij(1,1) + sij(3,3)
            sij(1,1) = sij(1,1) - my_haf*strace
            sij(3,3) = sij(3,3) - my_haf*strace
          else
            strace   = sij(1,1) + sij(2,2) + sij(3,3)
            sij(1,1) = sij(1,1) - onethird*strace
            sij(2,2) = sij(2,2) - onethird*strace
            sij(3,3) = sij(3,3) - onethird*strace
          endif

          dudxsij = two*(ux*sij(1,1) +uy*sij(1,2) +uz*sij(1,3)                 &
                        +vx*sij(2,1) +vy*sij(2,2) +vz*sij(2,3)                 &
                        +wx*sij(3,1) +wy*sij(3,2) +wz*sij(3,3))
          velterm = ux*ux + uy*uy + uz*uz + vx*vx + vy*vy + vz*vz +            &
                    wx*wx + wy*wy + wz*wz

          a      = my_1
          rho    = my_1
          rhoinv = my_1
          if ( eqn_set == compressible ) then
            rho  = qnode(1,i)
            rhoinv = my_1/rho
            p    = qnode(5,i)
            temp = gamma * p * rhoinv
            a    = sqrt( temp )
            rnu  = viscosity_law( cstar, temp ) * rhoinv
          else
            rnu  = my_1
          end if

!         Old way of getting Sw

          distance = slen(i)
          if(turbulence_model == 'des') then
            if(ddes) then  ! Delayed DES (DDES) (TCFD 20:181-195 2006)
              rd = turb(1,i)*my_xmach/                                         &
                   (sqrt(velterm)*vkar2*slen(i)*slen(i)*re)
              fd = my_1 - tanh((8.0_dp*rd)*(8.0_dp*rd)*(8.0_dp*rd))
              distance = slen(i) - fd*max(my_0, slen(i)-cdes*des_slen(i))
            else  ! Standard DES
              distance = min(slen(i),cdes*des_slen(i))
            end if
          end if

          chi    = turb(1,i) / rnu

          fv1    = chi**3 / (chi**3+cv1**3)
          fv2    = my_1 - chi/(my_1+chi*fv1)
          bot    = vkar2 * distance * distance
          s      = sqrt((wy-vz)**2+(uz-wx)**2+(vx-uy)**2)
          if ( s <= 1.0e-8_dp) s = 1.0e-8_dp

          if (dacles_mariani) then
            sij_mag   = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                  &
                              +2._dp*(ux**2+vy**2+wz**2))
            s = s + 2._dp*min(my_0,sij_mag-s)
          end if

          if(turbulence_model == 'des') then
            if(ddes .and. ddes_mod1) then  ! Delayed DES (DDES_MOD1)
              if (fd > cddes) then
                s    = s* (my_1 - fd) / (my_1 - cddes)
              end if
            end if
          end if

          botinv = my_1/bot

          sw     = s + xmr*turb(1,i)*botinv*fv2
          sw     = max(sw,my_00001)

          rr     = xmr * turb(1,i) * botinv / sw
          if (rr > 10.0_dp) rr = 10.0_dp

          gg     = rr + cw2*(rr**6-rr)
          fw     = gg * ((my_1+cw3**6)/(gg**6+cw3**6))**(my_6th)

!       Avoid computing exp(-ct4*chi*chi) which may cause underflow.
!       It is not needed anyway when t_prod = 0.

          if ( abs(t_prod) >= 1.0e-12_dp ) then
           ft2    = ct3 * exp(-ct4*chi*chi)
          else
           ft2    = 0.0_dp
          endif

          fr1 = my_1
!         SARC term:
          if (sarc) then
            s11 = my_haf*(ux + ux)
            s12 = my_haf*(uy + vx)
            s13 = my_haf*(uz + wx)
            s22 = my_haf*(vy + vy)
            s23 = my_haf*(vz + wy)
            s33 = my_haf*(wz + wz)
            w12 = my_haf*(uy - vx)
            w13 = my_haf*(uz - wx)
            w23 = my_haf*(vz - wy)
            xis = s11*s11 + s22*s22 + s33*s33 + &
                2._dp*s12*s12 + 2._dp*s13*s13 + 2._dp*s23*s23
            xisabs = sqrt(2._dp*xis)
            rstar=xisabs/s
            rtilde=2._dp/(0.5_dp*(s**2+xisabs**2))**2*                        &
                    ( -w12*s12deriv(i)*(s11-s22)                              &
                      -w13*s13deriv(i)*(s11-s33)                              &
                      -w23*s23deriv(i)*(s22-s33)                              &
                      +s12*(-w12*(s22deriv(i)-s11deriv(i))                    &
                                 -w13*s23deriv(i)-w23*s13deriv(i))            &
                      +s13*(-w13*(s33deriv(i)-s11deriv(i))                    &
                                 -w12*s23deriv(i)+w23*s12deriv(i))            &
                      +s23*(-w23*(s33deriv(i)-s22deriv(i))                    &
                                 +w12*s13deriv(i)+w13*s12deriv(i)) )
            fr1=4._dp*rstar/(1._dp+rstar)*(1._dp-sarc_cr3*                    &
                atan(12._dp*rtilde))-1._dp
          end if

!         Left hand side

          p1        = cb1 * (fr1-ft2)

!         test for laminar node
          if (turb_transition) then
            if (iflagslen(i) < 0) p1 = my_0
          end if

          const     = xmr / (distance*distance)
          d1        = const * (cw1*fw-cb1*xvkar2*ft2)
          dswdnu    = const * xvkar2 * fv2
          p         = p1 * sw
          d         = d1 * turb(1,i)
          pp        = p1 * dswdnu
          d1p       = d1

          comp_term = 0.0_dp
          term3     = 0.0_dp
          if ( turb_compress_model == 'ssz' ) then
            comp_term = 2.0_dp * (3.5_dp*xmr*xmr*rho*dudxsij/(a*a))
            comp_term = 2.0_dp * (3.5_dp*xmr*xmr*rho*velterm/(a*a))
            term3     = t_prod*max(my_0,comp_term)
            term3     = -term3*turb(1,i)
!write(6,'(a,5(1x,es12.5))') 'jac-comp_term',d-p,d1p-pp,comp_term
          endif

          term1     = t_prod*max(my_0,d-p)
          term2     = t_prod*max(my_0,d1p-pp)

          if (test_freestream) then
            term1 = 0._dp
            term2 = 0._dp
            term3 = 0._dp
          end if

          row = g2m(i)
          a_diag(1,1,row) = a_diag(1,1,row) + vol(i)                           &
                          *(term1+term2*turb(1,i)+term3)
        end do source1

      else regular_source_term

       source2 : do ii = 1, node_src_eval

          if (twod) then
            i = node_pairs_2d(1,ii)
          else
            i = ii
          end if

        if ( slen(i) < epsilon(1._dp) ) cycle !skip source on wall

          ux   = gradx(2,i)
          uy   = grady(2,i)
          uz   = gradz(2,i)
          vx   = gradx(3,i)
          vy   = grady(3,i)
          vz   = gradz(3,i)
          wx   = gradx(4,i)
          wy   = grady(4,i)
          wz   = gradz(4,i)

          if ( eqn_set == compressible ) then
            rho  = qnode(1,i)
            rhoinv = my_1/rho
            p    = qnode(5,i)
            temp = gamma * p * rhoinv
            rnu  = viscosity_law( cstar, temp ) * rhoinv
          else
            rnu  = my_1
          end if

!         Old way of getting Sw

          distance = slen(i)
          if(turbulence_model == 'des') then
            if(ddes) then  ! Delayed DES (DDES) (TCFD 20:181-195 2006)
              velterm = ux*ux + uy*uy + uz*uz + vx*vx + vy*vy + vz*vz +        &
                        wx*wx + wy*wy + wz*wz
              rd = turb(1,i)*my_xmach/                                         &
                   (sqrt(velterm)*vkar2*slen(i)*slen(i)*re)
              fd = my_1 - tanh((8.0_dp*rd)*(8.0_dp*rd)*(8.0_dp*rd))
              distance = slen(i) - fd*max(my_0, slen(i)-cdes*des_slen(i))
            else  ! Standard DES
              distance = min(slen(i),cdes*des_slen(i))
            end if
          end if

          chi    = turb(1,i) / rnu
          chi    = (turb(1,i)+my_1m12) / rnu      ! Adjust turb for Edwards
          fv1    = chi**3 / (chi**3+cv1**3)
          fv2    = my_1 - chi/(my_1+chi*fv1)
          bot    = vkar2 * distance * distance
          s      = sqrt((wy-vz)**2+(uz-wx)**2+(vx-uy)**2)
          if ( s <= 1.0e-8_dp) s = 1.0e-8_dp

          if(turbulence_model == 'des') then
            if(ddes .and. ddes_mod1) then  ! Delayed DES (DDES_MOD1)
              if (fd > cddes) then
                s    = s* (my_1 - fd) / (my_1 - cddes)
              end if
            end if
          end if

          sw     = s + xmr*turb(1,i)/bot*fv2
          sw     = max(sw,my_00001)

          rr     = xmr * turb(1,i) / bot / sw
          if (rr > 10.0_dp) rr = 10.0_dp

!         Edwards' mod

          sw     = s*(1.0_dp/chi + fv1)
          arg    = vkar2*distance*distance
          rr     = tanh(xmr*turb(1,i)/arg/sw) / tanh(1.0_dp)

!         End of Edwards' mod

          gg     = rr + cw2*(rr**6-rr)
          fw     = gg * ((my_1+cw3**6)/(gg**6+cw3**6))**(my_6th)
          ft2    = ct3 * exp(-ct4*chi*chi)

!         Edwards LHS

          dchidturb = 1.0_dp / rnu
          dft2dturb = ct3*exp(-ct4*chi*chi)*(-2.0_dp)*ct4*chi*dchidturb

          dfv1dturb = 3.0_dp*chi*chi / (chi**3 + cv1**3) * dchidturb           &
                    - (3.0_dp*chi**5)/(chi**3 + cv1**3)**2 * dchidturb

!         dfv2dturb = dchidturb / (1.0_dp + chi*fv1)                           &
!                   - chi/(1.0_dp + chi*fv1)**2 * (chi*dfv1dturb+fv1*dchidturb)

          dswdturb  = s*(dfv1dturb - dchidturb/chi/chi)

!         dproddturb = cb1*(1.0_dp - ft2)*sw                                   &
!                    + cb1*(1.0_dp - ft2)*turb(1,i)*dswdturb                   &
!                    - cb1*sw*turb(1,i)*dft2dturb

          baseterm = xmr*turb(1,i)                                             &
                   * (-1.0_dp)*xvkar2/distance/distance/sw/sw * dswdturb       &
                   + xmr*xvkar2/distance/distance/sw

!         Put a limit on the argument to cosh()
!         As the minimum spacing gets very small, cosh() grows without bound
!         Hence drdturb goes to zero very fast

          cosharg = xmr*turb(1,i)*xvkar2/distance/distance/sw

          if(abs(cosharg) > 15.0_dp) then
            drdturb = my_0
          else
            drdturb = 1.0_dp/cosh(cosharg)/cosh(cosharg)/                      &
                      tanh(1.0_dp)*baseterm
          endif

          dgdturb = drdturb + cw2*(6.0_dp*rr**5*drdturb - drdturb)

          baseterm = dgdturb / (gg**7 * (1.0_dp + cw3**(-6)))
          dfwdturb = ((gg**(-6) + cw3**(-6)) /                                 &
                      (1.0_dp + cw3**(-6)))**(-1.0_dp*my_76th)                 &
                     * baseterm

          ddestdturb = xmr*(cw1*fw - cb1*xvkar2*ft2)                           &
                     * (2.0_dp/distance/distance*turb(1,i))                    &
                     + xmr*(turb(1,i)/distance)**2                             &
                     * (cw1*dfwdturb - cb1*xvkar2*dft2dturb)

          if (test_freestream) then
!           ddestdturb = 0._dp
            ddestdturb = 0._dp
          end if

!         a_diag(1,1,i) = a_diag(1,1,i) - vol(i)*(dproddturb - ddestdturb)
          row = g2m(i)
          a_diag(1,1,row) = a_diag(1,1,row) + t_prod*vol(i)*(ddestdturb)

        end do source2

      end if regular_source_term

! Diffusion terms on non-tetrahedral element types

    if ( viscous_method == 0 .and. mixed ) then
      do ielem = 1, nelem
      if ( sa_diffusion_edge_tets > 0 .and.     &
           elem(ielem)%type_cell == 'tet' ) cycle
      call sa_jacob_diff_element(eqn_set, nnodes0, nnodes01, max_nnz, turb,    &
                                 qnode, a_diag, a_off, elem(ielem)%ncell,      &
                                 elem(ielem)%c2n, x, y, z,                     &
                                 elem(ielem)%type_cell, elem(ielem)%local_f2n, &
                                 elem(ielem)%local_e2n, elem(ielem)%local_f2e, &
                                 elem(ielem)%e2n_2d, elem(ielem)%face_per_cell,&
                                 elem(ielem)%node_per_cell,                    &
                                 elem(ielem)%edge_per_cell, ia, ja, n_turb,    &
                                 n_tot, elem(ielem)%face_2d, nzg2m, g2m,       &
                                 elem(ielem)%chk_norm)
      enddo
    endif

  end subroutine ras_2011_jacob


!================================= SA_RESID_DIFF_ELEMENT =====================80
!
! Diffusion residual for mixed element via cell-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine sa_resid_diff_element( eqn_set, nnodes0, nnodes01,                &
                               turb, qnode, res,                               &
                               ncell, c2n,                                     &
                               x, y, z, local_f2n, local_e2n, local_f2e,       &
                               e2n_2d, face_per_cell,                          &
                               node_per_cell, edge_per_cell, type_cell, n_turb,&
                               n_tot, face_2d, chk_norm )

    use kinddefs,       only : dp
    use info_depr,      only : twod, use_edge_gradients
    use debug_defs,     only : gradient_construction_rhs
    use solution_types, only : compressible, incompressible
    use debug_defs,     only : composite_jacobian_lhs

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_turb, n_tot
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: face_2d

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(inout) :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    character(len=3), intent(in) :: type_cell
    integer :: path_opt

  continue

    if ( eqn_set /= incompressible .and. eqn_set /= compressible ) then
      call lmpi_conditional_stop(1,'sa_resid_diff_element: only in/compress pg')
    end if

    path_opt = 1
    if ( twod                    .or. &
         .not.use_edge_gradients .or. &
         composite_jacobian_lhs ) path_opt = 0
    if ( path_opt /= 0 .and. gradient_construction_rhs == 1 ) path_opt = 2

    if ( skeleton > 0 ) then
      write(*,"(1x,a,i3,a,a)") ' Cell-based residuals of decoupled &
      &turbulent diffusion ...path_opt=',path_opt,' element=',type_cell
      write(*,*) ' gradx,... via ', trim(grad_x_y_z_contents)
    endif

    if ( trim(grad_x_y_z_contents) /= 'turbgrad' ) then
      if ( lmpi_master ) then
        write(*,*) ' Failure in cell-based SA residuals...stopping.'
        write(*,*) ' gradx,... via ', trim(grad_x_y_z_contents)
        write(*,*) ' ........should be via ','turbgrad'
      endif
      call lmpi_conditional_stop(1,'gradient:sa_resid_diff_element')
    endif

    if ( path_opt == 0 ) then
      call cell_based_diff_res(  eqn_set, nnodes0, nnodes01, turb,             &
                                 qnode, res, ncell, c2n, x, y, z,              &
                                 local_f2n, local_e2n, local_f2e, e2n_2d,      &
                                 face_per_cell, node_per_cell,                 &
                                 edge_per_cell, type_cell, n_turb, n_tot,      &
                                 face_2d, chk_norm)
    elseif ( path_opt == 1 ) then
      call cell_based_diff_res_o1( eqn_set, nnodes0, nnodes01, turb, qnode,  &
                                   res, ncell, c2n, x, y, z, local_f2n,      &
                                   local_e2n, face_per_cell, node_per_cell,  &
                                   edge_per_cell, type_cell, n_turb, n_tot,  &
                                   chk_norm )
    elseif ( path_opt == 2 ) then
      call cell_based_diff_res_o2( eqn_set, nnodes0, nnodes01, turb, qnode,  &
                                   res, ncell, c2n, x, y, z, local_f2n,      &
                                   local_e2n, face_per_cell, node_per_cell,  &
                                   edge_per_cell, type_cell, n_turb, n_tot,  &
                                   chk_norm )
    endif

  end subroutine sa_resid_diff_element

!================================= SA_RESID_DIFF_AVGLSQ ======================80
!
! Diffusion residual for edge-based formulation.
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine sa_resid_diff_avglsq( eqn_set, nnodes0, nnodes01,                 &
                               nedgeloc, eptr, turb, qnode, res, gradx, grady, &
                               gradz, xn, yn, zn, ra, x, y, z,                 &
                               nedgeloc_2d, n_turb, n_tot, n_grd )

    use kinddefs,        only : dp
    use info_depr,       only : tref, xmach, re, twod
    use turb_sa_const,   only : sig, cb2
    use turb_parameters, only : t_diff1, t_diff2, t_diff3
    use fluid,           only : gamma, sutherland_constant
    use utilities,       only : tangents
    use debug_defs,      only : gradient_construction_rhs
    use solution_types,  only : compressible, incompressible

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_turb, n_tot, n_grd
    integer, intent(in) :: nedgeloc, nedgeloc_2d
    integer, intent(in) :: nnodes0, nnodes01

    integer,  dimension(2,nedgeloc),      intent(in)    :: eptr
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),        intent(in)    :: ra
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(inout) :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    integer :: n, nedge_flux_eval, node1, node2, nn

    real(dp) :: xmre, xmre_s
    real(dp) :: aturb1, aturb2
    real(dp) :: cb21, cb20, turb1, turb2
    real(dp) :: cstar
    real(dp) :: phi, rnu1, rnu2, rho1, rho2
    real(dp) :: lx, ly, lz, mx, my, mz, deti
    real(dp) :: my_xmach
    real(dp) :: txavg, tyavg, tzavg
    real(dp) :: ex, ey, ez, disi
    real(dp) :: tx, ty, tz, egradt, gradt_xi
    real(dp) :: lgradt, mgradt

    real(dp), dimension(3,3)    :: b
    real(dp), dimension(n_turb) :: ngradt

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'sa_resid_diff_avglsq: only in/compress pg')
    end select

    !...to characterize three types of turbulent diffusion.
    cb20 = t_diff3*cb2
    cb21 = t_diff2 + cb20

    xmre   = my_xmach / re
    xmre_s = xmre / sig

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    endif

!   Check method for computing gradx,... (diffusion + source terms)

    if ( skeleton > 0 ) then
      write(*,"(1x,3(a))") ' Edge-based (avg_lsq) turbulent diffusion', &
                           ' ...gradients via ',trim(grad_x_y_z_contents)
    endif

    edge_loop: do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      nn = n_turb

      ! rnu = laminar viscosity / density

      rho1   = 1._dp
      rho2   = 1._dp
      rnu1   = 1._dp
      rnu2   = 1._dp
      if ( eqn_set == compressible ) then
        rho1 = qnode(1,node1)
        rho2 = qnode(1,node2)
        rnu1 = viscosity_law( cstar, gamma*qnode(5,node1)/rho1 ) / rho1
        rnu2 = viscosity_law( cstar, gamma*qnode(5,node2)/rho2 ) / rho2
      end if

      turb1 = turb(nn,node1)
      turb2 = turb(nn,node2)

      aturb1 = turb1
      aturb2 = turb2

      phi = t_diff1*0.5_dp*(rnu1 + rnu2) + cb21*0.5_dp*(aturb1 + aturb2)

      ! average node-based gradients : gradx, grady, gradz

      txavg = my_half*( gradx(n_grd-n_turb+nn,node1) +                     &
                        gradx(n_grd-n_turb+nn,node2) )
      tyavg = my_half*( grady(n_grd-n_turb+nn,node1) +                     &
                        grady(n_grd-n_turb+nn,node2) )
      tzavg = my_half*( gradz(n_grd-n_turb+nn,node1) +                     &
                        gradz(n_grd-n_turb+nn,node2) )


      ! ex, ey, ez is unit vector along edge direction

      ex   = x(node2) - x(node1)
      ey   = y(node2) - y(node1)
      ez   = z(node2) - z(node1)
      disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

      ex   = ex*disi
      ey   = ey*disi
      ez   = ez*disi

      if ( ex*xn(n) + ey*yn(n) + ez*zn(n) < 1.0e-10_dp ) cycle edge_loop

      ! directional gradient along edge

      egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

      if ( gradient_construction_rhs == 1 ) then

        !...find tangent vectors in the dual face
        call tangents(xn(n), yn(n), zn(n), lx, ly, lz, mx, my, mz)

        !...find inverse elements of transformation matrix.
        deti =  my_1/ ( ex*( ly*mz - lz*my ) &
                      + ey*( lz*mx - lx*mz ) &
                      + ez*( lx*my - ly*mx ) )

        b(1,1) =  deti*( ly*mz - lz*my )
        b(1,2) = -deti*( ey*mz - ez*my )
        b(1,3) =  deti*( ey*lz - ez*ly )

        b(2,1) = -deti*( lx*mz - lz*mx )
        b(2,2) =  deti*( ex*mz - ez*mx )
        b(2,3) = -deti*( ex*lz - ez*lx )

        b(3,1) =  deti*( lx*my - ly*mx )
        b(3,2) = -deti*( ex*my - ey*mx )
        b(3,3) =  deti*( ex*ly - ey*lx )

        lgradt = txavg*lx + tyavg*ly + tzavg*lz

        mgradt = txavg*mx + tyavg*my + tzavg*mz

        ! resolve gradient contributions from edge and dual face

        tx = b(1,1)*egradt + b(1,2)*lgradt + b(1,3)*mgradt
        ty = b(2,1)*egradt + b(2,2)*lgradt + b(2,3)*mgradt
        tz = b(3,1)*egradt + b(3,2)*lgradt + b(3,3)*mgradt

      else

        ! average Green-Gauss gradient in edge direction

        gradt_xi = txavg*ex + tyavg*ey + tzavg*ez

        ! combine gradient contributions from edge and primal cell

        tx = txavg + ( egradt - gradt_xi )*ex
        ty = tyavg + ( egradt - gradt_xi )*ey
        tz = tzavg + ( egradt - gradt_xi )*ez

      endif

      ! turbulent diffusion contribution at dual face [ two terms ]

      ! [area]*[nondimensionalization factor : Mach / Re / sigma ]*
      ! [normal gradient] at dual face

      ngradt(nn) = xmre_s*ra(n)*( tx*xn(n) + ty*yn(n) + tz*zn(n) )

      if ( node1 <= nnodes0 ) then
        res(nn,node1) = res(nn,node1) - ( phi - cb20*aturb1 )*ngradt(nn)
      end if

      if ( node2 <= nnodes0 ) then
        res(nn,node2) = res(nn,node2) + ( phi - cb20*aturb2 )*ngradt(nn)
      end if

    end do edge_loop

  end subroutine sa_resid_diff_avglsq


!============================ CELL_BASED_DIFF_RES ============================80
!
! Cell-based mixed element diffusion for SA model with all bells and whistles
!
!=============================================================================80
  subroutine cell_based_diff_res(eqn_set, nnodes0, nnodes01, turb,             &
                                 qnode, res, ncell, c2n, x, y, z,              &
                                 local_f2n, local_e2n, local_f2e, e2n_2d,      &
                                 face_per_cell, node_per_cell,                 &
                                 edge_per_cell, type_cell, n_turb, n_tot,      &
                                 face_2d, chk_norm)

    use kinddefs,        only : dp
    use info_depr,       only : tref, xmach, re, twod, use_edge_gradients, ivgrd
    use turb_sa_const,   only : sig, cb2
    use turb_parameters, only : t_diff1, t_diff2, t_diff3
    use fluid,           only : gamma, sutherland_constant
    use element_defs,    only : max_node_per_cell, max_face_per_cell,          &
                                max_edge_per_cell
    use utilities,       only : tangents, cell_gradients, big_angle
    use debug_defs,      only : gradient_construction_rhs
    use solution_types,  only : compressible, incompressible

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: face_2d

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(inout) :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    character(len=3), intent(in) :: type_cell

    integer :: n
    integer :: ie, i, ie_local, i_local
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, node
    integer :: n1, n2, n3, n4, n5, n6

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: trbre, atrbre
    real(dp) :: xmre, xmre_s
    real(dp) :: aturb1, aturb2
    real(dp) :: cb21, cb20, turb1, turb2
    real(dp) :: cstar
    real(dp) :: rho, rnu, rhoinv
    real(dp) :: phi
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: my_xmach
    real(dp) :: ex, ey, ez, disi, areai, xnf, ynf, znf
    real(dp) :: egradt, lgradt, mgradt, gradt_xi
    real(dp) :: lx, ly, lz, mx, my, mz, deti

    real(dp), dimension(3,3)                      :: b
    real(dp), dimension(max_face_per_cell)        :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)        :: nu_node, t_node
    real(dp), dimension(max_node_per_cell)        :: x_node, y_node, z_node
    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(n_turb)                   :: gradx_cell, grady_cell
    real(dp), dimension(n_turb)                   :: gradz_cell
    real(dp), dimension(n_turb)                   :: trbrex, trbrey, trbrez
    real(dp), dimension(n_turb)                   :: trbrexavg, trbreyavg
    real(dp), dimension(n_turb)                   :: trbrezavg, ngradt

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp

    logical :: edge_gradients, skip_viscous_terms

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp
    nodes_local = 0

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'cell_based_diff_res: only in/compress pg')
    end select

    !...to characterize three types of turbulent diffusion.
    cb20 = t_diff3*cb2
    cb21 = t_diff2 + cb20

    xmre   = my_xmach / re
    xmre_s = xmre / sig

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    edge_map = 0
    node_map = 0

    if ( twod ) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    else

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

    diffusion_term_cell : do n = 1, ncell

!     initialization

      cell_vol = 0.0_dp
      rnu      = 0._dp
      trbre    = 0._dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

      trbrex(:)       = 0._dp
      trbrey(:)       = 0._dp
      trbrez(:)       = 0._dp
      x_node(:)       = 0._dp
      y_node(:)       = 0._dp
      z_node(:)       = 0._dp
      t_node(:)       = 0._dp
      trbre_node(:,:) = 0._dp
      nu_node(:)      = 0._dp

!     compute cell averages and set up some local solution arrays

      node_loop : do i_local = 1, nodes_local

!       local node number

        i = node_map(i_local)

!       global node number

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        trbre_node(1,i) = turb(1,node)

        if ( eqn_set == compressible ) then
          rho        = qnode(1,node)
          rhoinv     = my_1/rho
          t_node(i)  = gamma*qnode(5,node)*rhoinv
          nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv
        else
          rho        = my_1
          nu_node(i) = my_1
        end if

        rnu   = rnu   + nu_node(i)
        atrbre = turb(1,node)
        trbre = trbre + atrbre

      end do node_loop

!     get cell averages by dividing by the number of nodes that contributed

      fact = 1._dp / real(nodes_local, dp)

      rnu    = rnu*fact
      atrbre = trbre*fact

      phi = t_diff1*rnu + cb21*atrbre

!     compute the cell center (must loop over node_per_cell even in 2D)

      do i = 1, node_per_cell

!       global node number

        node = c2n(i,n)

        xc  =  xc + x(node)
        yc  =  yc + y(node)
        zc  =  zc + z(node)

      end do

      fact = 1._dp / real(node_per_cell, dp)

      xc  =  xc*fact
      yc  =  yc*fact
      zc  =  zc*fact

!     get the gradients in the primal cell via Green-Gauss

      call cell_gradients(edges_local, max_node_per_cell,face_per_cell,      &
                          x_node, y_node, z_node, n_turb, trbre_node,        &
                          local_f2n, e2n_2d, gradx_cell, grady_cell,         &
                          gradz_cell, cell_vol, nx, ny, nz)

      skip_viscous_terms = .false.
      if ( ivgrd == 1 .and. .not.twod ) &
      skip_viscous_terms = big_angle( face_per_cell, nx, ny, nz, chk_norm )
      if ( skip_viscous_terms ) cycle diffusion_term_cell

      trbrexavg(1) = gradx_cell(1)
      trbreyavg(1) = grady_cell(1)
      trbrezavg(1) = gradz_cell(1)

!     next loop over the edges in the cell and get each ones
!     contribution to the residual

      edge_loop : do ie_local = 1,edges_local

!       local edge number

        ie = edge_map(ie_local)

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)

!       global node numbers of edge endpoints

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

!       edge midpoint

        xm = (x(n1) + x(n2))*my_half
        ym = (y(n1) + y(n2))*my_half
        zm = (z(n1) + z(n2))*my_half

!       compute left face centroid

        n3 = c2n(local_e2n(ie,3),n)

        if (local_e2n(ie,4) /= 0) then

!         quad cell face

          n4 = c2n(local_e2n(ie,4),n)

          xl = (x(n1) + x(n2) + x(n3) + x(n4))*my_4th
          yl = (y(n1) + y(n2) + y(n3) + y(n4))*my_4th
          zl = (z(n1) + z(n2) + z(n3) + z(n4))*my_4th

        else

!         tria cell face

          xl = (x(n1) + x(n2) + x(n3))*my_3rd
          yl = (y(n1) + y(n2) + y(n3))*my_3rd
          zl = (z(n1) + z(n2) + z(n3))*my_3rd

        end if

!       compute right face centroid

        n5 = c2n(local_e2n(ie,5),n)

        if (local_e2n(ie,6) /= 0) then

!         quad cell face

          n6 = c2n(local_e2n(ie,6),n)

          xr = (x(n1) + x(n2) + x(n5) + x(n6))*my_4th
          yr = (y(n1) + y(n2) + y(n5) + y(n6))*my_4th
          zr = (z(n1) + z(n2) + z(n5) + z(n6))*my_4th

        else

!         tria cell face

          xr = (x(n1) + x(n2) + x(n5))*my_3rd
          yr = (y(n1) + y(n2) + y(n5))*my_3rd
          zr = (z(n1) + z(n2) + z(n5))*my_3rd

        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

!       get gradients at the dual face; either take gradients for this
!       piece of the dual face to be the same as the cell-average gradient
!       computed above  (which is what the legacy FUN3D solver does for tets),
!       or combine with the edge-gradient to increase h-ellipticity on
!       non-simplicial meshes.

!       for tets in 3D or prisms in 2D, edge gradients add no new info
!       so there is no need to do the extra work

        edge_gradients = use_edge_gradients

        if (type_cell == 'tet') edge_gradients = .false.
        if (twod .and. type_cell == 'prz') edge_gradients = .false.

        include_edge_gradients : if (edge_gradients) then

!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          areai = my_1/sqrt( areax**2 + areay**2 + areaz**2 )
          xnf = areax*areai
          ynf = areay*areai
          znf = areaz*areai

          if ( ex*xnf + ey*ynf + ez*znf < 1.0e-10_dp ) cycle edge_loop

!         directional gradients along edge

          egradt = ( turb(1,n2) - turb(1,n1) )*disi

          if ( gradient_construction_rhs == 0 ) then

!           average Green-Gauss gradient in edge direction

            gradt_xi = trbrexavg(1)*ex + trbreyavg(1)*ey + trbrezavg(1)*ez

!           combine gradient contributions from edge and primal cell

            trbrex(1) = trbrexavg(1) + ( egradt - gradt_xi )*ex
            trbrey(1) = trbreyavg(1) + ( egradt - gradt_xi )*ey
            trbrez(1) = trbrezavg(1) + ( egradt - gradt_xi )*ez

          else

            !...find tangent vectors in the dual face
            call tangents(xnf, ynf, znf, lx, ly, lz, mx, my, mz)

            !...find inverse elements of transformation matrix.
            deti =  my_1/ ( ex*( ly*mz - lz*my ) &
                          + ey*( lz*mx - lx*mz ) &
                          + ez*( lx*my - ly*mx ) )

            b(1,1) =  deti*( ly*mz - lz*my )
            b(1,2) = -deti*( ey*mz - ez*my )
            b(1,3) =  deti*( ey*lz - ez*ly )

            b(2,1) = -deti*( lx*mz - lz*mx )
            b(2,2) =  deti*( ex*mz - ez*mx )
            b(2,3) = -deti*( ex*lz - ez*lx )

            b(3,1) =  deti*( lx*my - ly*mx )
            b(3,2) = -deti*( ex*my - ey*mx )
            b(3,3) =  deti*( ex*ly - ey*lx )

            lgradt = trbrexavg(1)*lx + trbreyavg(1)*ly             &
                   + trbrezavg(1)*lz

            mgradt = trbrexavg(1)*mx + trbreyavg(1)*my             &
                   + trbrezavg(1)*mz

            ! resolve gradient contributions from edge and dual face

            trbrex(1) = b(1,1)*egradt + b(1,2)*lgradt + b(1,3)*mgradt
            trbrey(1) = b(2,1)*egradt + b(2,2)*lgradt + b(2,3)*mgradt
            trbrez(1) = b(3,1)*egradt + b(3,2)*lgradt + b(3,3)*mgradt

          end if

        else include_edge_gradients

!         just use Green-Gauss cell-average gradients (this
!         is what the baseline code does for tets)

          trbrex(1) = trbrexavg(1)
          trbrey(1) = trbreyavg(1)
          trbrez(1) = trbrezavg(1)

        end if include_edge_gradients

!       turbulent diffusion contribution at dual face

!       [nondimensionalization factor : Mach / Re / sigma ]*
!       [normal gradient * area] at dual face

!       Note no positivity required since cb2 > -1

        ngradt(1) = xmre_s*( trbrex(1)*areax + trbrey(1)*areay               &
                           + trbrez(1)*areaz )

        turb1 = turb(1,n1)
        turb2 = turb(1,n2)

        aturb1 = turb1
        aturb2 = turb2

        if ( n1 <= nnodes0 ) then
          res(1,n1) = res(1,n1) - ( phi - cb20*aturb1 )*ngradt(1)
        end if

        if ( n2 <= nnodes0 ) then
          res(1,n2) = res(1,n2) + ( phi - cb20*aturb2 )*ngradt(1)
        end if

      end do edge_loop

    end do diffusion_term_cell

  end subroutine cell_based_diff_res


!============================ CELL_BASED_DIFF_RES_O1 =========================80
!
! Cell-based mixed element diffusion for SA model optimized for speed but
! does not have all of the bells and whistles that the cell_based_diff_res
! routine has
!
!=============================================================================80
  subroutine cell_based_diff_res_o1( eqn_set, nnodes0, nnodes01, turb, qnode,  &
                                     res, ncell, c2n, x, y, z, local_f2n,      &
                                     local_e2n, face_per_cell, node_per_cell,  &
                                     edge_per_cell, type_cell, n_turb, n_tot,  &
                                     chk_norm)

    use kinddefs,        only : dp
    use info_depr,       only : tref, xmach, re, ivgrd
    use turb_sa_const,   only : sig, cb2
    use turb_parameters, only : t_diff1, t_diff2, t_diff3
    use fluid,           only : gamma, sutherland_constant
    use element_defs,    only : max_node_per_cell, max_face_per_cell
    use solution_types,  only : compressible, incompressible
    use utilities,       only : big_angle

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(inout) :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    character(len=3), intent(in) :: type_cell

    integer :: n, iface, nn1, nn2, nn3, nn4
    integer :: ie, i
    integer :: n1_loc, n2_loc, node, n3_loc, n4_loc, n5_loc, n6_loc
    integer :: n1, n2

    real(dp) :: trbre, atrbre, xavg, yavg, zavg, xavg1, yavg1, zavg1
    real(dp) :: xmre, xmre_s, xavg2, yavg2, zavg2, nx1, ny1, nz1
    real(dp) :: cb21, cb20, turb1, turb2, nx, ny, nz, qavg, nx2, ny2, nz2
    real(dp) :: cstar, term1, term2, termx, termy, termz, cell_vol_inv
    real(dp) :: rho, rnu, rhoinv, termx1, termy1, termz1
    real(dp) :: phi, termx2, termy2, termz2, qavg1, qavg2
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: my_xmach
    real(dp) :: ex, ey, ez, disi
    real(dp) :: egradt, gradt_xi

    real(dp), dimension(max_node_per_cell)        :: nu_node, t_node
    real(dp), dimension(max_node_per_cell)        :: x_node, y_node, z_node
    real(dp), dimension(max_face_per_cell)        :: nxf, nyf, nzf
    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(n_turb)                   :: trbrex, trbrey, trbrez
    real(dp), dimension(n_turb)                   :: trbrexavg, trbreyavg
    real(dp), dimension(n_turb)                   :: trbrezavg, ngradt

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp
    real(dp), parameter :: my_6th  = 1.0_dp/6.0_dp
    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp

    logical :: skip_viscous_terms

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'cell_based_diff_res_o1: only in/comprss pg')
    end select

    !...to characterize three types of turbulent diffusion.
    cb20 = t_diff3*cb2
    cb21 = t_diff2 + cb20

    xmre   = my_xmach / re
    xmre_s = xmre / sig

    fact = 1._dp / real(node_per_cell, dp)

    diffusion_term_cell : do n = 1, ncell

      rnu   = 0._dp
      trbre = 0._dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

!     compute cell averages and set up some local solution arrays

      node_loop : do i = 1, node_per_cell

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        xc  =  xc + x_node(i)
        yc  =  yc + y_node(i)
        zc  =  zc + z_node(i)

        trbre_node(1,i) = turb(1,node)

        if ( eqn_set == compressible ) then
          rho        = qnode(1,node)
          rhoinv     = my_1/rho
          t_node(i)  = gamma*qnode(5,node)*rhoinv
          nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv
        else
          rho        = my_1
          nu_node(i) = my_1
        end if

        rnu    = rnu + nu_node(i)
        trbre  = trbre + turb(1,node)

      end do node_loop

!     get cell averages by dividing by the number of nodes that contributed

      rnu    = rnu*fact
      atrbre = trbre*fact
      phi    = t_diff1*rnu + cb21*atrbre
      xc     =  xc*fact
      yc     =  yc*fact
      zc     =  zc*fact

!     get the gradients in the primal cell via Green-Gauss

      trbrexavg = 0.0_dp
      trbreyavg = 0.0_dp
      trbrezavg = 0.0_dp

      cell_vol = my_0

      threed_faces : do iface = 1, face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!       triangular faces of the cell

!       face normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))       &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))       &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))       &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

!       gradient contributions

          qavg = trbre_node(1,nn1) + trbre_node(1,nn2) + trbre_node(1,nn3)

          trbrexavg(1) = trbrexavg(1) + termx*qavg
          trbreyavg(1) = trbreyavg(1) + termy*qavg
          trbrezavg(1) = trbrezavg(1) + termz*qavg

        else

!       quadrilateral faces of the cell

!       break face up into triangles 1-2-3 and 1-3-4 and add together

!       triangle 1: 1-2-3

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       triangle 1 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))      &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))      &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))      &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!       triangle 2: 1-3-4

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)

!       triangle 2 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))      &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))
          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))      &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))
          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))      &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!       cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th

!       gradient contributions

          termx1 = nx1*my_6th
          termy1 = ny1*my_6th
          termz1 = nz1*my_6th

          termx2 = nx2*my_6th
          termy2 = ny2*my_6th
          termz2 = nz2*my_6th

          qavg1 = trbre_node(1,nn1) + trbre_node(1,nn2) + trbre_node(1,nn3)
          qavg2 = trbre_node(1,nn1) + trbre_node(1,nn3) + trbre_node(1,nn4)

          trbrexavg(1) = trbrexavg(1) + termx1*qavg1 + termx2*qavg2
          trbreyavg(1) = trbreyavg(1) + termy1*qavg1 + termy2*qavg2
          trbrezavg(1) = trbrezavg(1) + termz1*qavg1 + termz2*qavg2

          nx = nx1 + nx2
          ny = ny1 + ny2
          nz = nz1 + nz2

        end if

        nxf(iface) = nx
        nyf(iface) = ny
        nzf(iface) = nz

      end do threed_faces

      skip_viscous_terms = .false.
      if ( ivgrd == 1 ) &
      skip_viscous_terms = big_angle( face_per_cell, nxf, nyf, nzf, chk_norm )
      if ( skip_viscous_terms ) cycle diffusion_term_cell

!   Need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

      cell_vol_inv = my_1/cell_vol

      trbrexavg(:) = trbrexavg(:) * cell_vol_inv
      trbreyavg(:) = trbreyavg(:) * cell_vol_inv
      trbrezavg(:) = trbrezavg(:) * cell_vol_inv

!     next loop over the edges in the cell and get each ones
!     contribution to the residual

      edge_loop : do ie = 1, edge_per_cell

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)
        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

!       global node numbers of edge endpoints

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

!       edge midpoint

        xm = (x_node(n1_loc) + x_node(n2_loc))*my_half
        ym = (y_node(n1_loc) + y_node(n2_loc))*my_half
        zm = (z_node(n1_loc) + z_node(n2_loc))*my_half

!       compute left face centroid

        if (n4_loc /= 0) then
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc)             &
              + x_node(n4_loc))*my_4th
          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc)             &
              + y_node(n4_loc))*my_4th
          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc)             &
              + z_node(n4_loc))*my_4th
        else
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc))*my_3rd
          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc))*my_3rd
          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc))*my_3rd
        end if

!       compute right face centroid

        if (n6_loc /= 0) then
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc)             &
              + x_node(n6_loc))*my_4th
          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc)             &
              + y_node(n6_loc))*my_4th
          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc)             &
              + z_node(n6_loc))*my_4th
        else
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc))*my_3rd
          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc))*my_3rd
          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc))*my_3rd
        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

!       get gradients at the dual face; either take gradients for this
!       piece of the dual face to be the same as the cell-average gradient
!       computed above  (which is what the legacy FUN3D solver does for tets),
!       or combine with the edge-gradient to increase h-ellipticity on
!       non-simplicial meshes.

!       for tets in 3D or prisms in 2D, edge gradients add no new info
!       so there is no need to do the extra work

        if ( type_cell /= 'tet' ) then

!         ex, ey, ez is unit vector along edge direction

          ex   = x_node(n2_loc) - x_node(n1_loc)
          ey   = y_node(n2_loc) - y_node(n1_loc)
          ez   = z_node(n2_loc) - z_node(n1_loc)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )
          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

!         directional gradients along edge

          egradt = ( turb(1,n2) - turb(1,n1) )*disi

!         average Green-Gauss gradient in edge direction

          gradt_xi = trbrexavg(1)*ex + trbreyavg(1)*ey + trbrezavg(1)*ez

!         combine gradient contributions from edge and primal cell

          trbrex(1) = trbrexavg(1) + ( egradt - gradt_xi )*ex
          trbrey(1) = trbreyavg(1) + ( egradt - gradt_xi )*ey
          trbrez(1) = trbrezavg(1) + ( egradt - gradt_xi )*ez

        else

          trbrex(:) = trbrexavg(:)
          trbrey(:) = trbreyavg(:)
          trbrez(:) = trbrezavg(:)

        endif

!       turbulent diffusion contribution at dual face

!       [nondimensionalization factor : Mach / Re / sigma ]*
!       [normal gradient * area] at dual face

!       Note no positivity required since cb2 > -1

        ngradt(1) = xmre_s*( trbrex(1)*areax + trbrey(1)*areay               &
                           + trbrez(1)*areaz )

        turb1 = trbre_node(1,n1_loc)
        turb2 = trbre_node(1,n2_loc)

        if ( n1 <= nnodes0 ) then
          res(1,n1) = res(1,n1) - ( phi - cb20*turb1 )*ngradt(1)
        end if

        if ( n2 <= nnodes0 ) then
          res(1,n2) = res(1,n2) + ( phi - cb20*turb2 )*ngradt(1)
        end if

      end do edge_loop

    end do diffusion_term_cell

  end subroutine cell_based_diff_res_o1

!============================ CELL_BASED_DIFF_RES_O2 =========================80
!
! Cell-based mixed element diffusion for SA model optimized for speed but
! does not have all of the bells and whistles that the cell_based_diff_res
! routine has
!
!=============================================================================80
  subroutine cell_based_diff_res_o2( eqn_set, nnodes0, nnodes01, turb, qnode,  &
                                     res, ncell, c2n, x, y, z, local_f2n,      &
                                     local_e2n, face_per_cell, node_per_cell,  &
                                     edge_per_cell, type_cell, n_turb, n_tot,  &
                                     chk_norm )

    use kinddefs,        only : dp
    use info_depr,       only : tref, xmach, re, ivgrd
    use turb_sa_const,   only : sig, cb2
    use turb_parameters, only : t_diff1, t_diff2, t_diff3
    use fluid,           only : gamma, sutherland_constant
    use element_defs,    only : max_node_per_cell, max_face_per_cell
    use utilities,       only : tangents, big_angle
    use solution_types,  only : compressible, incompressible

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(inout) :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    character(len=3), intent(in) :: type_cell

    integer :: n, iface, nn1, nn2, nn3, nn4
    integer :: ie, i
    integer :: n1_loc, n2_loc, node, n3_loc, n4_loc, n5_loc, n6_loc
    integer :: n1, n2

    real(dp) :: trbre, atrbre, xavg, yavg, zavg, xavg1, yavg1, zavg1
    real(dp) :: xmre, xmre_s, xavg2, yavg2, zavg2, nx1, ny1, nz1
    real(dp) :: cb21, cb20, turb1, turb2, nx, ny, nz, qavg, nx2, ny2, nz2
    real(dp) :: cstar, term1, term2, termx, termy, termz, cell_vol_inv
    real(dp) :: rho, rnu, rhoinv, termx1, termy1, termz1
    real(dp) :: phi, termx2, termy2, termz2, qavg1, qavg2
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: my_xmach
    real(dp) :: ex, ey, ez, disi, areai, xnf, ynf, znf
    real(dp) :: egradt, lgradt, mgradt
    real(dp) :: lx, ly, lz, mx, my, mz, deti

    real(dp), dimension(3,3) :: b

    real(dp), dimension(max_node_per_cell)        :: nu_node, t_node
    real(dp), dimension(max_node_per_cell)        :: x_node, y_node, z_node
    real(dp), dimension(max_face_per_cell)        :: nxf, nyf, nzf
    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(n_turb)                   :: trbrex, trbrey, trbrez
    real(dp), dimension(n_turb)                   :: trbrexavg, trbreyavg
    real(dp), dimension(n_turb)                   :: trbrezavg, ngradt

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp
    real(dp), parameter :: my_6th  = 1.0_dp/6.0_dp
    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp

    logical :: skip_viscous_terms

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'cell_based_diff_res_o2: only in/comprss pg')
    end select

    !...to characterize three types of turbulent diffusion.
    cb20 = t_diff3*cb2
    cb21 = t_diff2 + cb20

    xmre   = my_xmach / re
    xmre_s = xmre / sig

    fact = 1._dp / real(node_per_cell, dp)

    diffusion_term_cell : do n = 1, ncell

      rnu   = 0._dp
      trbre = 0._dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

!     compute cell averages and set up some local solution arrays

      node_loop : do i = 1, node_per_cell

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        xc  =  xc + x_node(i)
        yc  =  yc + y_node(i)
        zc  =  zc + z_node(i)

        trbre_node(1,i) = turb(1,node)

        if ( eqn_set == compressible ) then
          rho        = qnode(1,node)
          rhoinv     = my_1/rho
          t_node(i)  = gamma*qnode(5,node)*rhoinv
          nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv
        else
          rho        = my_1
          nu_node(i) = my_1
        end if

        rnu    = rnu + nu_node(i)
        trbre  = trbre + turb(1,node)

      end do node_loop

!     get cell averages by dividing by the number of nodes that contributed

      rnu    = rnu*fact
      atrbre = trbre*fact
      phi    = t_diff1*rnu + cb21*atrbre
      xc     =  xc*fact
      yc     =  yc*fact
      zc     =  zc*fact

!     get the gradients in the primal cell via Green-Gauss

      trbrexavg = 0.0_dp
      trbreyavg = 0.0_dp
      trbrezavg = 0.0_dp

      cell_vol = my_0

      threed_faces : do iface = 1, face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!       triangular faces of the cell

!       face normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))       &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))       &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))       &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

!       gradient contributions

          qavg = trbre_node(1,nn1) + trbre_node(1,nn2) + trbre_node(1,nn3)

          trbrexavg(1) = trbrexavg(1) + termx*qavg
          trbreyavg(1) = trbreyavg(1) + termy*qavg
          trbrezavg(1) = trbrezavg(1) + termz*qavg

        else

!       quadrilateral faces of the cell

!       break face up into triangles 1-2-3 and 1-3-4 and add together

!       triangle 1: 1-2-3

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       triangle 1 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))      &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))      &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))      &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!       triangle 2: 1-3-4

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)

!       triangle 2 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))      &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))
          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))      &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))
          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))      &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!       cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th

!       gradient contributions

          termx1 = nx1*my_6th
          termy1 = ny1*my_6th
          termz1 = nz1*my_6th

          termx2 = nx2*my_6th
          termy2 = ny2*my_6th
          termz2 = nz2*my_6th

          qavg1 = trbre_node(1,nn1) + trbre_node(1,nn2) + trbre_node(1,nn3)
          qavg2 = trbre_node(1,nn1) + trbre_node(1,nn3) + trbre_node(1,nn4)

          trbrexavg(1) = trbrexavg(1) + termx1*qavg1 + termx2*qavg2
          trbreyavg(1) = trbreyavg(1) + termy1*qavg1 + termy2*qavg2
          trbrezavg(1) = trbrezavg(1) + termz1*qavg1 + termz2*qavg2

          nx = nx1 + nx2
          ny = ny1 + ny2
          nz = nz1 + nz2

        end if

        nxf(iface) = nx
        nyf(iface) = ny
        nzf(iface) = nz

      end do threed_faces

      skip_viscous_terms = .false.
      if ( ivgrd == 1 ) &
      skip_viscous_terms = big_angle( face_per_cell, nxf, nyf, nzf, chk_norm )
      if ( skip_viscous_terms ) cycle diffusion_term_cell

!   ed to divide the gradient sums by the grid cell volume to give the
!   ll-average Green-Gauss gradients

      cell_vol_inv = my_1/cell_vol

      trbrexavg(:) = trbrexavg(:) * cell_vol_inv
      trbreyavg(:) = trbreyavg(:) * cell_vol_inv
      trbrezavg(:) = trbrezavg(:) * cell_vol_inv

!     next loop over the edges in the cell and get each ones
!     contribution to the residual

      edge_loop : do ie = 1, edge_per_cell

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)
        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

!       global node numbers of edge endpoints

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

!       edge midpoint

        xm = (x_node(n1_loc) + x_node(n2_loc))*my_half
        ym = (y_node(n1_loc) + y_node(n2_loc))*my_half
        zm = (z_node(n1_loc) + z_node(n2_loc))*my_half

!       compute left face centroid

        if (n4_loc /= 0) then
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc)             &
              + x_node(n4_loc))*my_4th
          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc)             &
              + y_node(n4_loc))*my_4th
          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc)             &
              + z_node(n4_loc))*my_4th
        else
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc))*my_3rd
          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc))*my_3rd
          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc))*my_3rd
        end if

!       compute right face centroid

        if (n6_loc /= 0) then
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc)             &
              + x_node(n6_loc))*my_4th
          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc)             &
              + y_node(n6_loc))*my_4th
          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc)             &
              + z_node(n6_loc))*my_4th
        else
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc))*my_3rd
          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc))*my_3rd
          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc))*my_3rd
        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

!       get gradients at the dual face; either take gradients for this
!       piece of the dual face to be the same as the cell-average gradient
!       computed above  (which is what the legacy FUN3D solver does for tets),
!       or combine with the edge-gradient to increase h-ellipticity on
!       non-simplicial meshes.

!       for tets in 3D or prisms in 2D, edge gradients add no new info
!       so there is no need to do the extra work

        if ( type_cell /= 'tet' ) then

!         ex, ey, ez is unit vector along edge direction

          ex   = x_node(n2_loc) - x_node(n1_loc)
          ey   = y_node(n2_loc) - y_node(n1_loc)
          ez   = z_node(n2_loc) - z_node(n1_loc)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )
          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          areai = my_1/sqrt( areax**2 + areay**2 + areaz**2 )
          xnf = areax*areai
          ynf = areay*areai
          znf = areaz*areai

          if ( ex*xnf + ey*ynf + ez*znf < 1.0e-10_dp ) cycle edge_loop

!         directional gradients along edge

          egradt = ( turb(1,n2) - turb(1,n1) )*disi

            !...find tangent vectors in the dual face
            call tangents(xnf, ynf, znf, lx, ly, lz, mx, my, mz)

            !...find inverse elements of transformation matrix.
            deti =  my_1/ ( ex*( ly*mz - lz*my ) &
                          + ey*( lz*mx - lx*mz ) &
                          + ez*( lx*my - ly*mx ) )

            b(1,1) =  deti*( ly*mz - lz*my )
            b(1,2) = -deti*( ey*mz - ez*my )
            b(1,3) =  deti*( ey*lz - ez*ly )

            b(2,1) = -deti*( lx*mz - lz*mx )
            b(2,2) =  deti*( ex*mz - ez*mx )
            b(2,3) = -deti*( ex*lz - ez*lx )

            b(3,1) =  deti*( lx*my - ly*mx )
            b(3,2) = -deti*( ex*my - ey*mx )
            b(3,3) =  deti*( ex*ly - ey*lx )

            lgradt = trbrexavg(1)*lx + trbreyavg(1)*ly             &
                   + trbrezavg(1)*lz

            mgradt = trbrexavg(1)*mx + trbreyavg(1)*my             &
                   + trbrezavg(1)*mz

            ! resolve gradient contributions from edge and dual face

            trbrex(1) = b(1,1)*egradt + b(1,2)*lgradt + b(1,3)*mgradt
            trbrey(1) = b(2,1)*egradt + b(2,2)*lgradt + b(2,3)*mgradt
            trbrez(1) = b(3,1)*egradt + b(3,2)*lgradt + b(3,3)*mgradt

        else

          trbrex(:) = trbrexavg(:)
          trbrey(:) = trbreyavg(:)
          trbrez(:) = trbrezavg(:)

        endif

!       turbulent diffusion contribution at dual face

!       [nondimensionalization factor : Mach / Re / sigma ]*
!       [normal gradient * area] at dual face

!       Note no positivity required since cb2 > -1

        ngradt(1) = xmre_s*( trbrex(1)*areax + trbrey(1)*areay               &
                           + trbrez(1)*areaz )

        turb1 = trbre_node(1,n1_loc)
        turb2 = trbre_node(1,n2_loc)

        if ( n1 <= nnodes0 ) then
          res(1,n1) = res(1,n1) - ( phi - cb20*turb1 )*ngradt(1)
        end if

        if ( n2 <= nnodes0 ) then
          res(1,n2) = res(1,n2) + ( phi - cb20*turb2 )*ngradt(1)
        end if

      end do edge_loop

    end do diffusion_term_cell

  end subroutine cell_based_diff_res_o2


!============================= SA_JACOB_DIFF_ELEMENT =========================80
!
! Diffusion LHS for mixed element formulation
!
! Calculates the diffusion jacobians for Spalart's model (loosely coupled) on
! via cell-based integration.
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine sa_jacob_diff_element(eqn_set, nnodes0, nnodes01, max_nnz, turb,  &
                                   qnode, a_diag, a_off, ncell, c2n, x, y, z,  &
                                   type_cell, local_f2n, local_e2n, local_f2e, &
                                   e2n_2d, face_per_cell, node_per_cell,       &
                                   edge_per_cell, ia, ja, n_turb, n_tot,       &
                                   face_2d, nzg2m, g2m, chk_norm)

    use kinddefs,              only : dp, odp
    use info_depr,             only : twod, use_edge_gradients
    use debug_defs,            only : gradient_construction_lhs
    use solution_types,  only : compressible, incompressible
    use debug_defs,      only : composite_jacobian_lhs

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: face_2d
    integer, intent(in) :: nnodes0, nnodes01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(:),                   intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp),  dimension(n_turb,nnodes01),       intent(inout) :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(inout) :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    character(len=3), intent(in) :: type_cell
    integer :: path_opt

  continue

    if ( eqn_set /= incompressible .and. eqn_set /= compressible ) then
      call lmpi_conditional_stop(1,'sa_jacob_diff_element: only in/comprs pg')
    end if

    path_opt = 1
    if ( twod                    .or. &
         .not.use_edge_gradients .or. &
         composite_jacobian_lhs  ) path_opt = 0
    if ( path_opt /= 0 .and. gradient_construction_lhs == 1 ) path_opt = 2

    if ( skeleton > 0 ) then
      write(*,"(1x,a,i3,a,a)") ' Cell-based jacobians of decoupled &
      &turbulent diffusion ...path_opt=',path_opt,' element=',type_cell
    endif

    if ( path_opt == 0 ) then
      call cell_based_diff_jac(eqn_set, nnodes0, nnodes01,                   &
                               max_nnz, turb, qnode, a_diag, a_off,          &
                               ncell, c2n, x, y, z,                          &
                               type_cell, local_f2n, local_e2n, local_f2e,   &
                               e2n_2d, face_per_cell, node_per_cell,         &
                               edge_per_cell, ia, ja, n_turb, n_tot,         &
                               face_2d, nzg2m, g2m, chk_norm )
    elseif ( path_opt == 1 ) then
      call cell_based_diff_jac_o1( eqn_set, nnodes0, nnodes01, max_nnz, turb,&
                                   qnode, a_diag, a_off, ncell, c2n, x,      &
                                   y, z, type_cell, local_f2n, local_e2n,    &
                                   face_per_cell, node_per_cell,             &
                                   edge_per_cell, ia, ja, n_turb, n_tot,     &
                                   nzg2m, g2m, chk_norm )
    elseif ( path_opt == 2 ) then
      call cell_based_diff_jac_o2( eqn_set, nnodes0, nnodes01, max_nnz, turb,&
                                   qnode, a_diag, a_off, ncell, c2n, x,      &
                                   y, z, type_cell, local_f2n, local_e2n,    &
                                   face_per_cell, node_per_cell,             &
                                   edge_per_cell, ia, ja, n_turb, n_tot,     &
                                   nzg2m, g2m, chk_norm )
    endif

  end subroutine sa_jacob_diff_element

!============================= CELL_BASED_DIFF_JAC ===========================80
!
! Diffusion LHS for mixed element formulation on element basis
!
! Diffusion jacobians for Spalart's model (loosely coupled) on
! mixed-element grids via cell-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine cell_based_diff_jac(eqn_set, nnodes0, nnodes01,                   &
                                 max_nnz, turb, qnode, a_diag, a_off,          &
                                 ncell, c2n, x, y, z,                          &
                                 type_cell, local_f2n, local_e2n, local_f2e,   &
                                 e2n_2d, face_per_cell, node_per_cell,         &
                                 edge_per_cell, ia, ja, n_turb, n_tot,         &
                                 face_2d, nzg2m, g2m, chk_norm )

    use kinddefs,        only : dp, odp
    use info_depr,       only : tref, xmach, re, twod, use_edge_gradients, ivgrd
    use turb_sa_const,   only : sig, cb2
    use turb_parameters, only : t_diff1, t_diff2, t_diff3
    use fluid,           only : gamma, sutherland_constant
    use element_defs,    only : max_node_per_cell, max_face_per_cell,          &
                                max_edge_per_cell
    use utilities,       only : tangents, cell_jacobians, big_angle
    use debug_defs,      only : gradient_construction_lhs
    use solution_types,  only : compressible, incompressible

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: face_2d
    integer, intent(in) :: nnodes0, nnodes01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(:),                   intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp),  dimension(n_turb,nnodes01),       intent(inout) :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(inout) :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    character(len=3), intent(in) :: type_cell

    integer :: n, row, ierr
    integer :: ie, i, ii, ie_local, i_local, ioff
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, node, nodec
    integer :: n1, n2, n3, n4, n5, n6
    integer :: k, column

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: terma, termb, termc, trbre, atrbre
    real(dp) :: xmre, xmre_s, phi, cb21, cb20
    real(dp) :: cstar, rho, rnu, rhoinv
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: xc, yc, zc, cell_vol, fact, cell_avg_factor
    real(dp) :: factor, my_xmach
    real(dp) :: ex, ey, ez, disi, areai, xnf, ynf, znf
    real(dp) :: dlgradt, dmgradt, dgradt_xi
    real(dp) :: lx, ly, lz, mx, my, mz, deti

    real(dp), dimension(3,3) :: b

    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(max_node_per_cell)        :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell)        :: dgradx_celldq
    real(dp), dimension(max_node_per_cell)        :: dgrady_celldq
    real(dp), dimension(max_node_per_cell)        :: dgradz_celldq
    real(dp), dimension(max_node_per_cell)        :: dtrbrex, dtrbrey, dtrbrez
    real(dp), dimension(max_node_per_cell)        :: dtrbrexavg, dtrbreyavg
    real(dp), dimension(max_node_per_cell)        :: dtrbrezavg, dngradt
    real(dp), dimension(max_face_per_cell)        :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)        :: nu_node, t_node

    real(dp), parameter :: my_4th   = 1.0_dp/4.0_dp
    real(dp), parameter :: my_3rd   = 1.0_dp/3.0_dp

    logical :: edge_gradients, skip_viscous_terms

  continue

    ierr  = 0
    termb = 0._dp
    termc = 0._dp

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = 1._dp
    case default
      call lmpi_conditional_stop(1,'cell_based_diff_jac: only in/compress pg')
    end select

    !...to characterize three types of turbulent diffusion.
    cb20 = t_diff3*cb2
    cb21 = t_diff2 + cb20

    xmre   = my_xmach / re
    xmre_s = xmre / sig

    nodes_local = 0

    ! Set some loop indicies and local mapping arrays depending
    ! on whether we are doing a 2D case or a 3D case

    edge_map     = 0
    node_map     = 0

    nodes_local = 0
    nodes_local = 0
    if ( twod ) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    else

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

    diffusion_term_cell : do n = 1, ncell

      ! initialization

      cell_vol = 0.0_dp

      dtrbrex(:) = 0._dp
      dtrbrey(:) = 0._dp
      dtrbrez(:) = 0._dp

      rnu      = 0._dp
      trbre    = 0._dp
      trbre_node(:,:) = 0._dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

      x_node(:)       = 0._dp
      y_node(:)       = 0._dp
      z_node(:)       = 0._dp
      t_node(:)       = 0._dp
      nu_node(:)      = 0._dp
      dtrbrexavg(:) = 0._dp
      dtrbreyavg(:) = 0._dp
      dtrbrezavg(:) = 0._dp

      ! cell averages and set up some local solution arrays

      node_loop : do i_local = 1, nodes_local

        i = node_map(i_local) ! local node number

        node = c2n(i,n)       ! global node number

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        if ( eqn_set == compressible ) then
          rho        = qnode(1,node)
          rhoinv     = 1._dp/rho
          t_node(i)  = gamma*qnode(5,node)*rhoinv
          nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv
        else
          rho        = 1._dp
          nu_node(i) = 1._dp
        end if

        rnu             = rnu   + nu_node(i)
        trbre_node(1,i) = turb(1,node)
        trbre           = trbre + trbre_node(1,i)

      end do node_loop

      ! averages over contributing nodes

      cell_avg_factor = 1._dp / real(nodes_local, dp)

      rnu   = cell_avg_factor*rnu
      trbre = cell_avg_factor*trbre


      atrbre  = trbre

      phi  = t_diff1*rnu + atrbre*cb21

      ! cell center (must loop over node_per_cell even in 2D)

      do i = 1, node_per_cell

        node = c2n(i,n)       ! global node number

        xc  =  xc + x(node)
        yc  =  yc + y(node)
        zc  =  zc + z(node)

      end do

      fact = 1._dp / real(node_per_cell, dp)

      xc  =  xc*fact
      yc  =  yc*fact
      zc  =  zc*fact

      ! jacobians of the gradients in the primal cell via Green-Gauss

      call cell_jacobians(edges_local, max_node_per_cell, face_per_cell,     &
                          x_node, y_node, z_node, local_f2n, e2n_2d,         &
                          dgradx_celldq, dgrady_celldq, dgradz_celldq,       &
                          cell_vol, nx, ny, nz)

      skip_viscous_terms = .false.
      if ( ivgrd == 1 .and. .not.twod ) &
      skip_viscous_terms = big_angle( face_per_cell, nx, ny, nz, chk_norm )
      if ( skip_viscous_terms ) cycle diffusion_term_cell

      ! average gradients

      if (twod) then

        do i_local = 1, nodes_local

          i = node_map(i_local) ! local node number

          dtrbrexavg(i) = dgradx_celldq(i)
          dtrbrezavg(i) = dgradz_celldq(i)

        end do

      else

        do i_local = 1, nodes_local

          i = node_map(i_local) ! local node number

          dtrbrexavg(i) = dgradx_celldq(i)
          dtrbreyavg(i) = dgrady_celldq(i)
          dtrbrezavg(i) = dgradz_celldq(i)

        end do

      end if

      ! next loop over the edges in the cell for each one's
      ! contribution to the jacobian

      edge_loop : do ie_local = 1,edges_local

        ie = edge_map(ie_local)  ! local edge number

        n1_loc = local_e2n(ie,1) ! local node numbers of edge endpoints
        n2_loc = local_e2n(ie,2) ! local node numbers of edge endpoints

        n1 = c2n(n1_loc,n) ! global node number of edge endpoints
        n2 = c2n(n2_loc,n) ! global node number of edge endpoints

        ! edges contribution to the dual normal and area

        ! edge midpoint

        xm = (x(n1) + x(n2))*0.5_dp
        ym = (y(n1) + y(n2))*0.5_dp
        zm = (z(n1) + z(n2))*0.5_dp

        ! left face centroid

        n3 = c2n(local_e2n(ie,3),n)       ! global node number

        if (local_e2n(ie,4) /= 0) then

          ! quad cell face

          n4 = c2n(local_e2n(ie,4),n)     ! global node number

          xl = (x(n1) + x(n2) + x(n3) + x(n4))*my_4th
          yl = (y(n1) + y(n2) + y(n3) + y(n4))*my_4th
          zl = (z(n1) + z(n2) + z(n3) + z(n4))*my_4th

        else

          ! tria cell face

          xl = (x(n1) + x(n2) + x(n3))*my_3rd
          yl = (y(n1) + y(n2) + y(n3))*my_3rd
          zl = (z(n1) + z(n2) + z(n3))*my_3rd

        end if

        ! right face centroid

        n5 = c2n(local_e2n(ie,5),n)       ! global node number

        if (local_e2n(ie,6) /= 0) then

          ! quad cell face

          n6 = c2n(local_e2n(ie,6),n)     ! global node number

          xr = (x(n1) + x(n2) + x(n5) + x(n6))*my_4th
          yr = (y(n1) + y(n2) + y(n5) + y(n6))*my_4th
          zr = (z(n1) + z(n2) + z(n5) + z(n6))*my_4th

        else

          ! tria cell face

          xr = (x(n1) + x(n2) + x(n5))*my_3rd
          yr = (y(n1) + y(n2) + y(n5))*my_3rd
          zr = (z(n1) + z(n2) + z(n5))*my_3rd

        end if

        ! contributions to dual normals from the two triangles
        ! that form part of the dual-cell surface

        ! area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*0.5_dp
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*0.5_dp
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*0.5_dp

        !jacobians of gradients at the dual face; either take gradients
        !for this piece of the dual face to be the same as the cell-average
        !gradient computed above  (which is what the legacy FUN3D solver does
        !for tets), or combine with the edge-gradient to increase h-ellipticity
        !non-simplicial meshes.

        !for tets in 3D or prisms in 2D, edge gradients add no new info
        !so there is no need to do the extra work

        edge_gradients = use_edge_gradients

        if (type_cell == 'tet') edge_gradients = .false.
        if (twod .and. type_cell == 'prz') edge_gradients = .false.

        include_edge_gradients : if (edge_gradients) then

!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = 1._dp/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          areai = 1._dp/sqrt( areax**2 + areay**2 + areaz**2 )
          xnf = areax*areai
          ynf = areay*areai
          znf = areaz*areai

          if ( ex*xnf + ey*ynf + ez*znf < 1.0e-10_dp ) cycle edge_loop

          !directional gradients along edge

          !egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

          if ( gradient_construction_lhs == 0 ) then

            ! avg_term pieces; all active cell nodes contribute.

            do i_local = 1, nodes_local

              !local node number

              i = node_map(i_local) ! local node number

              dgradt_xi = dtrbrexavg(i)*ex+dtrbreyavg(i)*ey+dtrbrezavg(i)*ez

              dtrbrex(i) = dtrbrexavg(i) - dgradt_xi*ex
              dtrbrey(i) = dtrbreyavg(i) - dgradt_xi*ey
              dtrbrez(i) = dtrbrezavg(i) - dgradt_xi*ez

            end do

            ! edge_term pieces; only the two edge nodes contribute.

            dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*ex
            dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*ex
            dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*ey
            dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*ey
            dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*ez
            dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*ez

          else

            !...find tangent vectors in the dual face.
            call tangents(xnf, ynf, znf, lx, ly, lz, mx, my, mz)

            !...find inverse elements of transformation matrix.
            deti =  1._dp/ ( ex*( ly*mz - lz*my ) &
                           + ey*( lz*mx - lx*mz ) &
                           + ez*( lx*my - ly*mx ) )

            b(1,1) =  deti*( ly*mz - lz*my )
            b(1,2) = -deti*( ey*mz - ez*my )
            b(1,3) =  deti*( ey*lz - ez*ly )

            b(2,1) = -deti*( lx*mz - lz*mx )
            b(2,2) =  deti*( ex*mz - ez*mx )
            b(2,3) = -deti*( ex*lz - ez*lx )

            b(3,1) =  deti*( lx*my - ly*mx )
            b(3,2) = -deti*( ex*my - ey*mx )
            b(3,3) =  deti*( ex*ly - ey*lx )

            !lgradt = trbrexavg(1)*lx + trbreyavg(1)*ly             &
            !       + trbrezavg(1)*lz

            !mgradt = trbrexavg(1)*mx + trbreyavg(1)*my             &
            !       + trbrezavg(1)*mz

            !resolve gradient contributions from edge and dual face

            !trbrex(1) = b(1,1)*egradt + b(1,2)*lgradt + b(1,3)*mgradt
            !trbrey(1) = b(2,1)*egradt + b(2,2)*lgradt + b(2,3)*mgradt
            !trbrez(1) = b(3,1)*egradt + b(3,2)*lgradt + b(3,3)*mgradt

            ! avg_term pieces; all active cell nodes contribute.

            do i_local = 1, nodes_local

              i = node_map(i_local) ! local node number

              dlgradt = dtrbrexavg(i)*lx + dtrbreyavg(i)*ly          &
                      + dtrbrezavg(i)*lz

              dmgradt = dtrbrexavg(i)*mx + dtrbreyavg(i)*my          &
                      + dtrbrezavg(i)*mz

              dtrbrex(i) = b(1,2)*dlgradt + b(1,3)*dmgradt
              dtrbrey(i) = b(2,2)*dlgradt + b(2,3)*dmgradt
              dtrbrez(i) = b(3,2)*dlgradt + b(3,3)*dmgradt

            end do

            ! edge_term pieces; only the two edge nodes contribute.

            dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*b(1,1)
            dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*b(1,1)
            dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*b(2,1)
            dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*b(2,1)
            dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*b(3,1)
            dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*b(3,1)

          end if

        else include_edge_gradients

          ! only have the unaltered, average green-gauss contributions;
          ! all active nodes in the cell contribute

            do i_local = 1, nodes_local

              i = node_map(i_local) ! local node number

              dtrbrex(i) = dtrbrexavg(i)
              dtrbrey(i) = dtrbreyavg(i)
              dtrbrez(i) = dtrbrezavg(i)

            end do

        end if include_edge_gradients

        ! Jacobian of normal gradient oriented from n1 to n2.

        dngradt(:) = xmre_s*( dtrbrex(:)*areax + dtrbrey(:)*areay            &
                   +          dtrbrez(:)*areaz )

        ! assemble final Jacobian matrices into sparse matrix form

        factor = -1._dp

        edge_node_loop : do ii = 1,2

          ! diagonal contributions

          if (ii == 1) then
            i = n1_loc
            node = c2n(n1_loc,n) ! global node number
          else
            i = n2_loc
            node = c2n(n2_loc,n) ! global node number
          end if

          factor = -1._dp*factor

          terma  = phi - cb20*turb(1,node)

          if ( node <= nnodes0 ) then
            row = g2m(node)
            a_diag(1,1,row) = a_diag(1,1,row) - factor*terma*dngradt(i) &
                                              + termb
          end if

          ! off-diagonal contributions

          node_loop_2 : do i_local = 1, nodes_local

            i = node_map(i_local) ! local node number

            nodec = c2n(i,n)      ! global node number

            if (nodec == node) cycle node_loop_2

            if ( node <= nnodes0 ) then

              ! avoid unused entries in a_off
              if (node > nnodes0 .and. nodec > nnodes0) cycle node_loop_2

              ! location of nonzero contribution in comp row storage

              ioff = 0

              do k = ia(node), ia(node+1) - 1
                column = ja(k)
                if (column == nodec) then
                  ioff = nzg2m(k)
                  exit
                endif
              end do

              if (ioff == 0) then
                write(6,*)'error: no place to put contribution from node ',  &
                           nodec,' to the off diagonal of node ',node
                ierr = 1 ; exit node_loop_2
              end if

              ! res(n1) = res(n1) - ( phi - cb20*turb1 )*ngradt
              ! res(n2) = res(n2) + ( phi - cb20*turb2 )*ngradt

              a_off(1,1,ioff) = a_off(1,1,ioff) - factor*terma*dngradt(i) &
                                                + termc

            end if

          end do node_loop_2

        end do edge_node_loop

      end do edge_loop

    end do diffusion_term_cell

    call lmpi_conditional_stop(ierr,'off-diagonal_site:cell_based_diff_jac')

  end subroutine cell_based_diff_jac


!============================= CELL_BASED_DIFF_JAC_O1 ========================80
!
! Diffusion LHS for mixed element formulation on element basis
!
! Calculates the diffusion jacobians for Spalart's model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-nased integration
!
! Both compressible and incompressible perfect gas
!
! optimized for speed but does not contain all of the bells and whistles
! of cell_based_diff
!
!=============================================================================80
  subroutine cell_based_diff_jac_o1( eqn_set, nnodes0, nnodes01, max_nnz, turb,&
                                     qnode, a_diag, a_off, ncell, c2n, x,      &
                                     y, z, type_cell, local_f2n, local_e2n,    &
                                     face_per_cell, node_per_cell,             &
                                     edge_per_cell, ia, ja, n_turb, n_tot,     &
                                     nzg2m, g2m, chk_norm )

    use kinddefs,        only : dp, odp
    use info_depr,       only : tref, xmach, re, ivgrd
    use turb_sa_const,   only : sig, cb2
    use turb_parameters, only : t_diff1, t_diff2, t_diff3
    use fluid,           only : gamma, sutherland_constant
    use element_defs,    only : max_node_per_cell, max_face_per_cell
    use solution_types,  only : compressible, incompressible
    use utilities,       only : big_angle

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0, nnodes01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(:),                   intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp),  dimension(n_turb,nnodes01),       intent(inout) :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(inout) :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    character(len=3), intent(in) :: type_cell

    integer :: n, row, iface
    integer :: ie, i, ioff, j
    integer :: n1_loc, n2_loc, node, n3_loc, n4_loc, n5_loc, n6_loc
    integer :: nodec, nn1, nn2, nn3, nn4
    integer :: k, column

    real(dp) :: terma, trbre, xavg, yavg, zavg, xavg1, yavg1, zavg1
    real(dp) :: xmre, xmre_s, xavg2, yavg2, zavg2, term1, term2
    real(dp) :: cb21, cb20
    real(dp) :: cstar
    real(dp) :: rho, rnu, rhoinv
    real(dp) :: phi
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: nx, ny, nz, nx1, ny1, nz1, nx2, ny2, nz2
    real(dp) :: cell_vol_inv, my_xmach
    real(dp) :: ex, ey, ez, disi
    real(dp) :: dgradt_xi

    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(max_node_per_cell)        :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell)        :: dtrbrex, dtrbrey, dtrbrez
    real(dp), dimension(max_node_per_cell)        :: dtrbrexavg, dtrbreyavg
    real(dp), dimension(max_node_per_cell)        :: dtrbrezavg, dngradt
    real(dp), dimension(max_node_per_cell)        :: nu_node, t_node
    real(dp), dimension(max_face_per_cell)        :: nxf, nyf, nzf
    real(dp), dimension(node_per_cell,node_per_cell) :: a

    real(dp), parameter :: my_half  = 0.5_dp
    real(dp), parameter :: my_1     = 1.0_dp
    real(dp), parameter :: my_4th   = 1.0_dp/4.0_dp
    real(dp), parameter :: my_6th   = 1.0_dp/6.0_dp
    real(dp), parameter :: my_3rd   = 1.0_dp/3.0_dp
    real(dp), parameter :: my_18th  = 1.0_dp/18.0_dp

    logical :: skip_viscous_terms

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'cell_based_diff_jac_o1: only in/comprss pg')
    end select

    !...to characterize three types of turbulent diffusion.
    cb20 = t_diff3*cb2
    cb21 = t_diff2 + cb20

    xmre   = my_xmach / re
    xmre_s = xmre / sig

    fact = 1._dp / real(node_per_cell, dp)

    diffusion_term_cell : do n = 1, ncell

      a        = 0._dp
      rnu      = 0._dp
      trbre    = 0._dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

!     compute cell averages and set up some local solution arrays

      node_loop : do i = 1, node_per_cell

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        xc  =  xc + x_node(i)
        yc  =  yc + y_node(i)
        zc  =  zc + z_node(i)

        if ( eqn_set == compressible ) then
          rho        = qnode(1,node)
          rhoinv     = my_1/rho
          t_node(i)  = gamma*qnode(5,node)*rhoinv
          nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv
        else
          rho        = my_1
          nu_node(i) = my_1
        end if

        rnu   = rnu   + nu_node(i)
        trbre_node(1,i) = turb(1,node)
        trbre = trbre + trbre_node(1,i)

      end do node_loop

      rnu    = rnu*fact
      trbre  = trbre*fact
      phi    = t_diff1*rnu + cb21*trbre
      xc     =  xc*fact
      yc     =  yc*fact
      zc     =  zc*fact

!     get the jacobians of the gradients in the primal cell via Green-Gauss

      dtrbrexavg(:) = 0.0_dp
      dtrbreyavg(:) = 0.0_dp
      dtrbrezavg(:) = 0.0_dp
      cell_vol      = 0.0_dp

      threed_faces : do iface = 1,face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!         triangular faces of the cell

!         face normals (factor of 1/2 deferred till cell_vol
!         and jacobian terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))         &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))         &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))         &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)

!         jacobian contributions

          dtrbrexavg(nn1) = dtrbrexavg(nn1) + nx
          dtrbreyavg(nn1) = dtrbreyavg(nn1) + ny
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + nz

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + nx
          dtrbreyavg(nn2) = dtrbreyavg(nn2) + ny
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + nz

          dtrbrexavg(nn3) = dtrbrexavg(nn3) + nx
          dtrbreyavg(nn3) = dtrbreyavg(nn3) + ny
          dtrbrezavg(nn3) = dtrbrezavg(nn3) + nz

        else

!         quadrilateral faces of the cell

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))        &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))        &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))        &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))        &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))
          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))        &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))
          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))        &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!         cell volume contributions

          cell_vol = cell_vol + (term1 + term2)

!         jacobian contributions

          dtrbrexavg(nn1) = dtrbrexavg(nn1) + nx1 + nx2
          dtrbreyavg(nn1) = dtrbreyavg(nn1) + ny1 + ny2
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + nz1 + nz2

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + nx1
          dtrbreyavg(nn2) = dtrbreyavg(nn2) + ny1
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + nz1

          dtrbrexavg(nn3) = dtrbrexavg(nn3) + nx1 + nx2
          dtrbreyavg(nn3) = dtrbreyavg(nn3) + ny1 + ny2
          dtrbrezavg(nn3) = dtrbrezavg(nn3) + nz1 + nz2

          dtrbrexavg(nn4) = dtrbrexavg(nn4) + nx2
          dtrbreyavg(nn4) = dtrbreyavg(nn4) + ny2
          dtrbrezavg(nn4) = dtrbrezavg(nn4) + nz2

          nx = nx1 + nx2
          ny = ny1 + ny2
          nz = nz1 + nz2

        end if

        nxf(iface) = nx
        nyf(iface) = ny
        nzf(iface) = nz

      end do threed_faces

      skip_viscous_terms = .false.
      if ( ivgrd == 1 ) &
      skip_viscous_terms = big_angle( face_per_cell, nxf, nyf, nzf, chk_norm )
      if ( skip_viscous_terms ) cycle diffusion_term_cell

      cell_vol = cell_vol * my_18th
      cell_vol_inv = my_6th/cell_vol

      dtrbrexavg(:) = dtrbrexavg(:) * cell_vol_inv
      dtrbreyavg(:) = dtrbreyavg(:) * cell_vol_inv
      dtrbrezavg(:) = dtrbrezavg(:) * cell_vol_inv

!     next loop over the edges in the cell and get each one's
!     contribution to the jacobian

      edge_loop : do ie = 1, edge_per_cell

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)
        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

!       get this edges' contributiuon to the dual normal and area

!       edge midpoint

        xm = (x_node(n1_loc) + x_node(n2_loc))*my_half
        ym = (y_node(n1_loc) + y_node(n2_loc))*my_half
        zm = (z_node(n1_loc) + z_node(n2_loc))*my_half

!       compute left face centroid

        if (n4_loc /= 0) then
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc)             &
              + x_node(n4_loc))*my_4th
          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc)             &
              + y_node(n4_loc))*my_4th
          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc)             &
              + z_node(n4_loc))*my_4th
        else
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc))*my_3rd
          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc))*my_3rd
          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc))*my_3rd
        end if

!       compute right face centroid

        if (n6_loc /= 0) then
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc)             &
              + x_node(n6_loc))*my_4th
          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc)             &
              + y_node(n6_loc))*my_4th
          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc)             &
              + z_node(n6_loc))*my_4th
        else
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc))*my_3rd
          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc))*my_3rd
          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc))*my_3rd
        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

!       get (jacobians of) gradients at the dual face; either take gradients
!       for this piece of the dual face to be the same as the cell-average
!       gradient computed above  (which is what the legacy FUN3D solver does
!       for tets), or combine with the edge-gradient to increase h-ellipticity
!       non-simplicial meshes.

!       for tets in 3D or prisms in 2D, edge gradients add no new info
!       so there is no need to do the extra work

!       ex, ey, ez is unit vector along edge direction

        include_edge_gradients : if ( type_cell /= 'tet' ) then

          !ex, ey, ez is unit vector along edge direction

          ex   = x_node(n2_loc) - x_node(n1_loc)
          ey   = y_node(n2_loc) - y_node(n1_loc)
          ez   = z_node(n2_loc) - z_node(n1_loc)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          !directional gradients along edge

          !egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

            ! avg_term pieces; all active cell nodes contribute.

            do i = 1, node_per_cell

              dgradt_xi = dtrbrexavg(i)*ex+dtrbreyavg(i)*ey+dtrbrezavg(i)*ez

              dtrbrex(i) = dtrbrexavg(i) - dgradt_xi*ex
              dtrbrey(i) = dtrbreyavg(i) - dgradt_xi*ey
              dtrbrez(i) = dtrbrezavg(i) - dgradt_xi*ez

            end do

            ! edge_term pieces; only the two edge nodes contribute.

            dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*ex
            dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*ex
            dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*ey
            dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*ey
            dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*ez
            dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*ez

        else include_edge_gradients

          dtrbrex(:) = dtrbrexavg(:)
          dtrbrey(:) = dtrbreyavg(:)
          dtrbrez(:) = dtrbrezavg(:)

        endif include_edge_gradients

!       form some intermediate Jacobians at all nodes

        dngradt(:) = xmre_s*( dtrbrex(:)*areax + dtrbrey(:)*areay            &
                   +          dtrbrez(:)*areaz )

!       assemble final Jacobian matrices into sparse matrix form
!       map the local entries into compact space and unpack later

        terma  = phi - cb20*trbre_node(1,n1_loc)

        node_loop_3 : do j = 1, node_per_cell
          a(n1_loc,j) = a(n1_loc,j) - terma*dngradt(j)
        end do node_loop_3

        terma  = phi - cb20*trbre_node(1,n2_loc)

        node_loop_4 : do j = 1, node_per_cell
          a(n2_loc,j) = a(n2_loc,j) + terma*dngradt(j)
        end do node_loop_4

      end do edge_loop

      do i = 1, node_per_cell
        node = c2n(i,n)
        if ( node <= nnodes0 ) then
          do j = 1, node_per_cell
            if ( i == j ) then
              row = g2m(node)
              a_diag(1,1,row) = a_diag(1,1,row) + a(i,i)
            else
              nodec = c2n(j,n)
              ioff = 0
              search : do k = ia(node), ia(node+1) - 1
                column = ja(k)
                if (column == nodec) then
                  ioff = nzg2m(k)
                  exit search
                endif
              end do search
              a_off(1,1,ioff) = a_off(1,1,ioff) + real(a(i,j),odp)
            endif
          end do
        end if
      end do

    end do diffusion_term_cell

  end subroutine cell_based_diff_jac_o1

!============================= CELL_BASED_DIFF_JAC_O2 ========================80
!
! Diffusion LHS for mixed element formulation on element basis
!
! Calculates the diffusion jacobians for Spalart's model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-nased integration
!
! Both compressible and incompressible perfect gas
!
! optimized for speed but does not contain all of the bells and whistles
! of cell_based_diff
!
!=============================================================================80
  subroutine cell_based_diff_jac_o2( eqn_set, nnodes0, nnodes01, max_nnz, turb,&
                                     qnode, a_diag, a_off, ncell, c2n, x,      &
                                     y, z, type_cell, local_f2n, local_e2n,    &
                                     face_per_cell, node_per_cell,             &
                                     edge_per_cell, ia, ja, n_turb, n_tot,     &
                                     nzg2m, g2m, chk_norm )

    use kinddefs,        only : dp, odp
    use info_depr,       only : tref, xmach, re, ivgrd
    use turb_sa_const,   only : sig, cb2
    use turb_parameters, only : t_diff1, t_diff2, t_diff3
    use fluid,           only : gamma, sutherland_constant
    use element_defs,    only : max_node_per_cell, max_face_per_cell
    use utilities,       only : tangents, big_angle
    use solution_types,  only : compressible, incompressible

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0, nnodes01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(:),                   intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp),  dimension(n_turb,nnodes01),       intent(inout) :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(inout) :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    character(len=3), intent(in) :: type_cell

    integer :: n, row, iface
    integer :: ie, i, ioff, j
    integer :: n1_loc, n2_loc, node, n3_loc, n4_loc, n5_loc, n6_loc
    integer :: nodec, nn1, nn2, nn3, nn4
    integer :: k, column

    real(dp) :: terma, trbre, xavg, yavg, zavg, xavg1, yavg1, zavg1
    real(dp) :: xmre, xmre_s, xavg2, yavg2, zavg2, term1, term2
    real(dp) :: cb21, cb20
    real(dp) :: cstar
    real(dp) :: rho, rnu, rhoinv
    real(dp) :: phi
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: nx, ny, nz, nx1, ny1, nz1, nx2, ny2, nz2
    real(dp) :: cell_vol_inv, my_xmach
    real(dp) :: ex, ey, ez, disi, areai, xnf, ynf, znf
    real(dp) :: dlgradt, dmgradt
    real(dp) :: lx, ly, lz, mx, my, mz, deti

    real(dp), dimension(3,3) :: b

    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(max_node_per_cell)        :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell)        :: dtrbrex, dtrbrey, dtrbrez
    real(dp), dimension(max_node_per_cell)        :: dtrbrexavg, dtrbreyavg
    real(dp), dimension(max_node_per_cell)        :: dtrbrezavg, dngradt
    real(dp), dimension(max_node_per_cell)        :: nu_node, t_node
    real(dp), dimension(max_face_per_cell)        :: nxf, nyf, nzf
    real(dp), dimension(node_per_cell,node_per_cell) :: a

    real(dp), parameter :: my_half  = 0.5_dp
    real(dp), parameter :: my_1     = 1.0_dp
    real(dp), parameter :: my_4th   = 1.0_dp/4.0_dp
    real(dp), parameter :: my_6th   = 1.0_dp/6.0_dp
    real(dp), parameter :: my_3rd   = 1.0_dp/3.0_dp
    real(dp), parameter :: my_18th  = 1.0_dp/18.0_dp

    logical :: skip_viscous_terms

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'cell_based_diff_jac_o2: only in/comprss pg')
    end select

    !...to characterize three types of turbulent diffusion.
    cb20 = t_diff3*cb2
    cb21 = t_diff2 + cb20

    xmre   = my_xmach / re
    xmre_s = xmre / sig

    fact = 1._dp / real(node_per_cell, dp)

    diffusion_term_cell : do n = 1, ncell

      a        = 0._dp
      rnu      = 0._dp
      trbre    = 0._dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

!     compute cell averages and set up some local solution arrays

      node_loop : do i = 1, node_per_cell

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        xc  =  xc + x_node(i)
        yc  =  yc + y_node(i)
        zc  =  zc + z_node(i)

        if ( eqn_set == compressible ) then
          rho        = qnode(1,node)
          rhoinv     = my_1/rho
          t_node(i)  = gamma*qnode(5,node)*rhoinv
          nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv
        else
          rho        = my_1
          nu_node(i) = my_1
        end if

        rnu   = rnu   + nu_node(i)
        trbre_node(1,i) = turb(1,node)
        trbre = trbre + trbre_node(1,i)

      end do node_loop

      rnu    = rnu*fact
      trbre  = trbre*fact
      phi    = t_diff1*rnu + cb21*trbre
      xc     =  xc*fact
      yc     =  yc*fact
      zc     =  zc*fact

!     get the jacobians of the gradients in the primal cell via Green-Gauss

      dtrbrexavg(:) = 0.0_dp
      dtrbreyavg(:) = 0.0_dp
      dtrbrezavg(:) = 0.0_dp
      cell_vol      = 0.0_dp

      threed_faces : do iface = 1,face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!         triangular faces of the cell

!         face normals (factor of 1/2 deferred till cell_vol
!         and jacobian terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))         &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))         &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))         &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)

!         jacobian contributions

          dtrbrexavg(nn1) = dtrbrexavg(nn1) + nx
          dtrbreyavg(nn1) = dtrbreyavg(nn1) + ny
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + nz

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + nx
          dtrbreyavg(nn2) = dtrbreyavg(nn2) + ny
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + nz

          dtrbrexavg(nn3) = dtrbrexavg(nn3) + nx
          dtrbreyavg(nn3) = dtrbreyavg(nn3) + ny
          dtrbrezavg(nn3) = dtrbrezavg(nn3) + nz

        else

!         quadrilateral faces of the cell

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))        &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))        &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))        &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))        &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))
          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))        &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))
          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))        &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!         cell volume contributions

          cell_vol = cell_vol + (term1 + term2)

!         jacobian contributions

          dtrbrexavg(nn1) = dtrbrexavg(nn1) + nx1 + nx2
          dtrbreyavg(nn1) = dtrbreyavg(nn1) + ny1 + ny2
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + nz1 + nz2

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + nx1
          dtrbreyavg(nn2) = dtrbreyavg(nn2) + ny1
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + nz1

          dtrbrexavg(nn3) = dtrbrexavg(nn3) + nx1 + nx2
          dtrbreyavg(nn3) = dtrbreyavg(nn3) + ny1 + ny2
          dtrbrezavg(nn3) = dtrbrezavg(nn3) + nz1 + nz2

          dtrbrexavg(nn4) = dtrbrexavg(nn4) + nx2
          dtrbreyavg(nn4) = dtrbreyavg(nn4) + ny2
          dtrbrezavg(nn4) = dtrbrezavg(nn4) + nz2

          nx = nx1 + nx2
          ny = ny1 + ny2
          nz = nz1 + nz2

        end if

        nxf(iface) = nx
        nyf(iface) = ny
        nzf(iface) = nz

      end do threed_faces

      skip_viscous_terms = .false.
      if ( ivgrd == 1 ) &
      skip_viscous_terms = big_angle( face_per_cell, nxf, nyf, nzf, chk_norm )
      if ( skip_viscous_terms ) cycle diffusion_term_cell

      cell_vol = cell_vol * my_18th
      cell_vol_inv = my_6th/cell_vol

      dtrbrexavg(:) = dtrbrexavg(:) * cell_vol_inv
      dtrbreyavg(:) = dtrbreyavg(:) * cell_vol_inv
      dtrbrezavg(:) = dtrbrezavg(:) * cell_vol_inv

!     next loop over the edges in the cell and get each one's
!     contribution to the jacobian

      edge_loop : do ie = 1, edge_per_cell

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)
        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

!       get this edges' contributiuon to the dual normal and area

!       edge midpoint

        xm = (x_node(n1_loc) + x_node(n2_loc))*my_half
        ym = (y_node(n1_loc) + y_node(n2_loc))*my_half
        zm = (z_node(n1_loc) + z_node(n2_loc))*my_half

!       compute left face centroid

        if (n4_loc /= 0) then
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc)             &
              + x_node(n4_loc))*my_4th
          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc)             &
              + y_node(n4_loc))*my_4th
          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc)             &
              + z_node(n4_loc))*my_4th
        else
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc))*my_3rd
          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc))*my_3rd
          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc))*my_3rd
        end if

!       compute right face centroid

        if (n6_loc /= 0) then
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc)             &
              + x_node(n6_loc))*my_4th
          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc)             &
              + y_node(n6_loc))*my_4th
          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc)             &
              + z_node(n6_loc))*my_4th
        else
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc))*my_3rd
          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc))*my_3rd
          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc))*my_3rd
        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

!       get (jacobians of) gradients at the dual face; either take gradients
!       for this piece of the dual face to be the same as the cell-average
!       gradient computed above  (which is what the legacy FUN3D solver does
!       for tets), or combine with the edge-gradient to increase h-ellipticity
!       non-simplicial meshes.

!       for tets in 3D or prisms in 2D, edge gradients add no new info
!       so there is no need to do the extra work

!       ex, ey, ez is unit vector along edge direction

        include_edge_gradients : if ( type_cell /= 'tet' ) then

          !ex, ey, ez is unit vector along edge direction

          ex   = x_node(n2_loc) - x_node(n1_loc)
          ey   = y_node(n2_loc) - y_node(n1_loc)
          ez   = z_node(n2_loc) - z_node(n1_loc)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          areai = my_1/sqrt( areax**2 + areay**2 + areaz**2 )
          xnf = areax*areai
          ynf = areay*areai
          znf = areaz*areai

          if ( ex*xnf + ey*ynf + ez*znf < 1.0e-10_dp ) cycle edge_loop

          !directional gradients along edge

          !egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

            !...find tangent vectors in the dual face.
            call tangents(xnf, ynf, znf, lx, ly, lz, mx, my, mz)

            !...find inverse elements of transformation matrix.
            deti =  my_1/ ( ex*( ly*mz - lz*my ) &
                          + ey*( lz*mx - lx*mz ) &
                          + ez*( lx*my - ly*mx ) )

            b(1,1) =  deti*( ly*mz - lz*my )
            b(1,2) = -deti*( ey*mz - ez*my )
            b(1,3) =  deti*( ey*lz - ez*ly )

            b(2,1) = -deti*( lx*mz - lz*mx )
            b(2,2) =  deti*( ex*mz - ez*mx )
            b(2,3) = -deti*( ex*lz - ez*lx )

            b(3,1) =  deti*( lx*my - ly*mx )
            b(3,2) = -deti*( ex*my - ey*mx )
            b(3,3) =  deti*( ex*ly - ey*lx )

            !lgradt = trbrexavg(1)*lx + trbreyavg(1)*ly             &
            !       + trbrezavg(1)*lz

            !mgradt = trbrexavg(1)*mx + trbreyavg(1)*my             &
            !       + trbrezavg(1)*mz

            !resolve gradient contributions from edge and dual face

            !trbrex(1) = b(1,1)*egradt + b(1,2)*lgradt + b(1,3)*mgradt
            !trbrey(1) = b(2,1)*egradt + b(2,2)*lgradt + b(2,3)*mgradt
            !trbrez(1) = b(3,1)*egradt + b(3,2)*lgradt + b(3,3)*mgradt

            ! avg_term pieces; all active cell nodes contribute.

            do i = 1, node_per_cell

              dlgradt = dtrbrexavg(i)*lx + dtrbreyavg(i)*ly          &
                      + dtrbrezavg(i)*lz

              dmgradt = dtrbrexavg(i)*mx + dtrbreyavg(i)*my          &
                      + dtrbrezavg(i)*mz

              dtrbrex(i) = b(1,2)*dlgradt + b(1,3)*dmgradt
              dtrbrey(i) = b(2,2)*dlgradt + b(2,3)*dmgradt
              dtrbrez(i) = b(3,2)*dlgradt + b(3,3)*dmgradt

            end do

            ! edge_term pieces; only the two edge nodes contribute.

            dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*b(1,1)
            dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*b(1,1)
            dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*b(2,1)
            dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*b(2,1)
            dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*b(3,1)
            dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*b(3,1)

        else include_edge_gradients

          dtrbrex(:) = dtrbrexavg(:)
          dtrbrey(:) = dtrbreyavg(:)
          dtrbrez(:) = dtrbrezavg(:)

        endif include_edge_gradients

!       form some intermediate Jacobians at all nodes

        dngradt(:) = xmre_s*( dtrbrex(:)*areax + dtrbrey(:)*areay            &
                   +          dtrbrez(:)*areaz )

!       assemble final Jacobian matrices into sparse matrix form
!       map the local entries into compact space and unpack later

        terma  = phi - cb20*trbre_node(1,n1_loc)

        a(n1_loc,n1_loc) = a(n1_loc,n1_loc) - terma*dngradt(n1_loc)

        node_loop_3 : do j = 1, node_per_cell
          if ( j == n1_loc ) cycle node_loop_3
          a(n1_loc,j) = a(n1_loc,j) - terma*dngradt(j)
        end do node_loop_3

        terma  = phi - cb20*trbre_node(1,n2_loc)

        a(n2_loc,n2_loc) = a(n2_loc,n2_loc) + terma*dngradt(n2_loc)

        node_loop_4 : do j = 1, node_per_cell
          if ( j == n2_loc ) cycle node_loop_4
          a(n2_loc,j) = a(n2_loc,j) + terma*dngradt(j)
        end do node_loop_4

      end do edge_loop

      do i = 1, node_per_cell
        node = c2n(i,n)
        if ( node <= nnodes0 ) then
          do j = 1, node_per_cell
            if ( i == j ) then
              row = g2m(node)
              a_diag(1,1,row) = a_diag(1,1,row) + a(i,i)
            else
              nodec = c2n(j,n)
              ioff = 0
              search : do k = ia(node), ia(node+1) - 1
                column = ja(k)
                if (column == nodec) then
                  ioff = nzg2m(k)
                  exit search
                endif
              end do search
              a_off(1,1,ioff) = a_off(1,1,ioff) + a(i,j)
            endif
          end do
        end if
      end do

    end do diffusion_term_cell

  end subroutine cell_based_diff_jac_o2

!========================== EDGE_ASSEMBLY_RES_CONV_DIFF ======================80
!
! Edge-assembled convection and diffuson terms.
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine edge_assembly_res_conv_diff(                                      &
                      eqn_set, nnodes0, nnodes01, nedgeloc, eptr, turb,        &
                      qnode, res, xn, yn, zn,                                  &
                      ra, dft1, dft2, facespeed,                               &
                      n_turb, n_tot, eq_loc, nedgeloc_2d, viscous_method )


    use info_depr,           only : xmach, re, twod, mixed
    use grid_motion_helpers, only : need_grid_velocity
    use turb_sa_const,       only : sig, cb2
    use turb_parameters,     only : t_conv, t_diff3, ubar_eps
    use solution_types,      only : compressible, incompressible

    integer, intent(in) :: nnodes0, nnodes01, n_tot, eq_loc
    integer, intent(in) :: eqn_set, nedgeloc, n_turb, nedgeloc_2d
    integer, intent(in) :: viscous_method

    integer,      dimension(2,nedgeloc),     intent(in)    :: eptr

    real(dp),  dimension(n_turb,nnodes01),   intent(in)    :: turb
    real(dp),  dimension(n_tot,nnodes01),    intent(in)    :: qnode
    real(dp),  dimension(:,:),               intent(inout) :: res
    real(dp),  dimension(nedgeloc),          intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),          intent(in)    :: facespeed
    real(dp),  dimension(nedgeloc),          intent(inout) :: dft1, dft2

    integer :: n, node1, node2, nedge_flux_eval

    real(dp)    :: area, cb20, rmr
    real(dp)    :: term1, term2, terma, termb
    real(dp)    :: u, v, w, ubar, uminus, uplus
    real(dp)    :: xnorm, ynorm, znorm, face_speed
    real(dp)    :: my_xmach, positivity_term
    real(dp)    :: turb_node1, turb_node2

  continue

    if ( mixed ) then
      positivity_term = -huge(1.0_dp)
      if ( sa_diffusion_edge_tets == 1 ) positivity_term = 0._dp
    else
      positivity_term = 0._dp
      if(.not.positive_turb_diffusion) positivity_term = -huge(1.0_dp)
    endif

    cb20 = t_diff3*cb2

    my_xmach = 0._dp
    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = 1._dp
    case default
      call lmpi_conditional_stop(1,'sa_resid: only in/compress pg')
    end select

    rmr  = my_xmach / re / sig

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    endif

! Now lets compute the convective and diffusion terms.  Note the diffusion
! terms here are only for tets.  dft1 and dft2 are zero everywhere else.  We
! will pick up the diffusion contributions from other element types later on.

    do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

!     Unit normal to dual face and area

      xnorm = xn(n)
      ynorm = yn(n)
      znorm = zn(n)
      area  = ra(n)

!     Dual face speed

      face_speed = 0._dp

      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

!     First node
!     Convective part

      u = qnode(2,node1)
      v = qnode(3,node1)
      w = qnode(4,node1)
      ubar   = xnorm*u + ynorm*v + znorm*w - face_speed

        uplus  = 0.5_dp * (ubar+aharten(ubar,ubar_eps))
        uminus = 0.5_dp * (ubar-aharten(ubar,ubar_eps))

!     Dissipative part
!           (term1 = phi*(wxx + wyy + wzz))
!           (term2 = cb2*turb*(wxx + wyy + wzz))

      terma = 0.0_dp
      termb = 0.0_dp
      if ( viscous_method == 0 ) then
        if ( .not.mixed .or. ( mixed .and. sa_diffusion_edge_tets > 0 ) ) then
          turb_node1 = turb(1,node1)
          term1 = dft2(n)
          term2 = cb20 * turb_node1 * dft1(n)
          terma = max( term1-term2, positivity_term)
          termb = min(-term1+term2,-positivity_term)
        endif
      endif

      if (node1 <= nnodes0)  res(eq_loc,node1) = res(eq_loc,node1)             &
                                        + (uplus*turb(1,node1)                 &
                                        +  uminus*turb(1,node2))*area*t_conv   &
                                        + rmr*terma*turb(1,node1)              &
                                        + rmr*termb*turb(1,node2)

!     Now do the other node (note that ubar pts in other direction. This
!     is because I'm too stupid to work out all the sign changes but
!     I need an outward pointing normal for the convective stuff

      u = qnode(2,node2)
      v = qnode(3,node2)
      w = qnode(4,node2)
      ubar   = -(xnorm*u + ynorm*v + znorm*w - face_speed)

        uplus  = 0.5_dp * (ubar+aharten(ubar,ubar_eps))
        uminus = 0.5_dp * (ubar-aharten(ubar,ubar_eps))

      terma = 0.0_dp
      termb = 0.0_dp
      if ( viscous_method == 0 ) then
        if ( .not.mixed .or. ( mixed .and. sa_diffusion_edge_tets > 0 )  ) then
          turb_node2 = turb(1,node2)
          term1 = dft2(n)
          term2 = cb20 * turb_node2 * dft1(n)
          terma = max( term1-term2, positivity_term)
          termb = min(-term1+term2,-positivity_term)
        endif
      endif

      if (node2 <= nnodes0)  res(eq_loc,node2) = res(eq_loc,node2)             &
                                        + (uplus*turb(1,node2)                 &
                                        +  uminus*turb(1,node1))*area*t_conv   &
                                        + rmr*terma*turb(1,node2)              &
                                        + rmr*termb*turb(1,node1)
    end do

  end subroutine edge_assembly_res_conv_diff

!======================= EDGE_ASSEMBLY_JAC_CONV_DIFF =========================80
!
! Edge-assembled jacobians of convection and diffuson terms.
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine edge_assembly_jac_conv_diff(                                      &
                      eqn_set, viscous_method, nnodes0, nnodes01,              &
                      nedgeloc, max_nnz, eptr, turb, qnode,                    &
                      xn, yn, zn, ra, dft1, dft2, a_diag,                      &
                      a_off, fhelp, facespeed, n_turb, n_tot,                  &
                      g2m, nedgeloc_2d )

    use info_depr,           only : xmach, re, twod, mixed
    use grid_motion_helpers, only : need_grid_velocity
    use turb_sa_const,       only : sig, cb2
    use turb_parameters,     only : t_conv, t_diff3, ubar_eps
    use solution_types,      only : compressible, incompressible

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: nnodes0, nnodes01, n_tot
    integer, intent(in) :: nedgeloc, n_turb, max_nnz
    integer, intent(in) :: nedgeloc_2d

    integer, dimension(2,nedgeloc),   intent(in) :: eptr
    integer, dimension(2,nedgeloc),   intent(in) :: fhelp
    integer, dimension(:),            intent(in) :: g2m

    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),              intent(in)    :: facespeed
    real(dp),  dimension(nedgeloc),              intent(in)    :: dft1, dft2

    integer :: n, node1, node2, ioff, row, nedge_jac_eval

    real(dp) :: area
    real(dp) :: cb20
    real(dp) :: diag1, diag2, off1, off2, rmr
    real(dp) :: term1, term2, terma, termb, positivity_term
    real(dp) :: ubar, uminus, uplus, u, v, w
    real(dp) :: xnorm, ynorm, znorm
    real(dp) :: turb_node1, turb_node2
    real(dp) :: face_speed, my_xmach

  continue

    if ( mixed ) then
      positivity_term = -huge(1.0_dp)
      if ( sa_diffusion_edge_tets == 1 ) positivity_term = 0._dp
    else
      positivity_term = 0._dp
      if(.not.positive_turb_diffusion) positivity_term = -huge(1.0_dp)
    endif

    my_xmach = 0.0_dp
    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = 1._dp
    case default
      call lmpi_conditional_stop(1,'sa_jacob: only in/compress pg')
    end select

    cb20 = t_diff3*cb2

    rmr    = my_xmach / re / sig

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    nedge_jac_eval = nedgeloc
    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    endif

!   Now lets compute the convective and second order terms
!   This assumes that the dft1 and dft2 terms have previously been computed
!   for the residual computation!

      do n = 1, nedge_jac_eval
        node1 = eptr(1,n)
        node2 = eptr(2,n)

!       Unit normal to dual face and area

        xnorm = xn(n)
        ynorm = yn(n)
        znorm = zn(n)
        area  = ra(n)

!       Dual face speed

        face_speed = 0._dp

        if (need_grid_velocity) then
          face_speed = facespeed(n)
        end if

!       First node
!       Convective part

        u = qnode(2,node1)
        v = qnode(3,node1)
        w = qnode(4,node1)
        ubar   = xnorm*u + ynorm*v + znorm*w - face_speed
        uplus  = t_conv*0.5_dp * (ubar+aharten(ubar,ubar_eps))
        uminus = t_conv*0.5_dp * (ubar-aharten(ubar,ubar_eps))

!       Dissipative part
!             (term1 = phi*(wxx + wyy + wzz))
!             (term2 = cb2*turb*(wxx + wyy + wzz))

!       Jacobian type terms

        diag1 = 0._dp
        off1  = 0._dp

        diag1 = diag1 + uplus  * area
        off1  = off1  + uminus * area

        if ( viscous_method == 0 ) then
          if( .not.mixed .or. ( mixed .and. sa_diffusion_edge_tets > 0 ) ) then
            turb_node1 = turb(1,node1)
            term1 = dft2(n)
            term2 = cb20 * turb_node1 * dft1(n)
            terma = max( term1-term2, positivity_term)
            termb = min(-term1+term2,-positivity_term)
            diag1 = diag1 + rmr * terma
            off1  = off1  + rmr * termb
          endif
        endif

!       Now do the other node (note that ubar pts in other direction. This
!       is because I'm too stupid to work out all the sign changes but
!       I need an outward pointing normal for the convective stuff

        u = qnode(2,node2)
        v = qnode(3,node2)
        w = qnode(4,node2)
        ubar   = -(xnorm*u + ynorm*v + znorm*w - face_speed)
        uplus  = t_conv*0.5_dp * (ubar+aharten(ubar,ubar_eps))
        uminus = t_conv*0.5_dp * (ubar-aharten(ubar,ubar_eps))

!       Jacobian type terms

        diag2 = 0._dp
        off2  = 0._dp

        diag2 = diag2 + uplus  * area
        off2  = off2  + uminus * area

        if ( viscous_method == 0 ) then
          if( .not.mixed .or. ( mixed .and. sa_diffusion_edge_tets > 0 ) ) then
            turb_node2 = turb(1,node2)
            term1 = dft2(n)
            term2 = cb20 * turb_node2 * dft1(n)
            terma = max( term1-term2, positivity_term)
            termb = min(-term1+term2,-positivity_term)
            diag2 = diag2 + rmr * terma
            off2  = off2  + rmr * termb
          endif
        endif

!       Now add the Jacobian terms to the diagonal and off diagonal

        if (node1 <= nnodes0) then
          row = g2m(node1)
          a_diag(1,1,row) = a_diag(1,1,row) + diag1
          ioff        = fhelp(1,n)
          a_off(1,1,ioff) = a_off(1,1,ioff) + real(off1,odp)
        end if

        if (node2 <= nnodes0) then
          row = g2m(node2)
          a_diag(1,1,row) = a_diag(1,1,row) + diag2
          ioff        = fhelp(2,n)
          a_off(1,1,ioff) = a_off(1,1,ioff) + real(off2,odp)
        end if

      end do

  end subroutine edge_assembly_jac_conv_diff

  include 'viscosity_law.f90'
  include 'aharten.f90'

end module turb_ras_2011

module code_status

  implicit none

  private

  public :: code_id, number_of_codes, code_name
  public :: flow_code_id, adjoint_code_id
  public :: design_code_id, rad_code_id

  integer            :: code_id
  integer, parameter :: number_of_codes = 4

  character(len=13), dimension(number_of_codes), parameter ::                  &
                                                code_name = (/ 'Flow    ',     &
                                                               'Adjoint ',     &
                                                               'Design  ',     &
                                                               'Rad     '/)

  integer, parameter :: flow_code_id = 1
  integer, parameter :: adjoint_code_id = 2
  integer, parameter :: design_code_id = 3
  integer, parameter :: rad_code_id = 4

end module code_status

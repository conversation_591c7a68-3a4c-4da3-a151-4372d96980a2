module move_mixed

  use kinddefs,       only : dp, odp
  use lmpi,           only : lmpi_master, lmpi_die, lmpi_id, lmpi_reduce
  use info_depr,      only : skeleton
  use turb_util,      only : set_slen_wall_nonzero, set_slen_wall_zero

  implicit none

  private

  public :: fill_jacobian_elasticity
  public :: res_elasticity
  public :: init_elasticity
  public :: move_nbodies
  public :: vol_divide, null_boundary_move
  public :: unit_info

  integer          :: move_nbodies, unit_info

  logical :: vol_divide, null_boundary_move

  real(dp), parameter    :: my_0     = 0.0_dp
  real(dp), parameter    :: my_1     = 1.0_dp
  real(dp), parameter    :: my_0_odp = 0.0_odp

  real(dp), parameter    :: poisson  = 0.0_dp

contains


!=============================== EXACT_ELASTICITY ============================80
!
! Exact solution for elasticity equations
!
!=============================================================================80

  subroutine exact_elasticity(x, y, z, q_exact, f_exact, init, print_flag )

    use moves,       only : elasticity
    use exact_defs,  only : u_polyf, u_sinef

    integer,               intent(in)  :: init
    real(dp),              intent(in)  :: x, y, z
    real(dp),              intent(out) :: q_exact(3)
    real(dp),              intent(out) :: f_exact(3)
    integer,     optional, intent(in)  :: print_flag

    real(dp)    :: c, cx,   cy,   cz,  &
                      cxx,  cyy,  czz, &
                      cxxx, cyyy, czzz,&
                      cxxxx,cyyyy,czzzz

    real(dp)    :: qx_exact(3)
    real(dp)    :: qy_exact(3)
    real(dp)    :: qz_exact(3)

    real(dp)    :: sx, sy, sz

    real(dp)    :: pi
    real(dp)    :: modulus_of_elasticity, mu, mux, muy, muz
    real(dp)    :: slen, r, rx, ry, rz

    real(dp), parameter    :: my_half = 0.5_dp
    real(dp), parameter    :: my_6th  = 1._dp/6.0_dp
    real(dp), parameter    :: my_3rd  = 1._dp/3.0_dp
    real(dp), parameter    :: my_12th = 1._dp/12.0_dp

  continue

    pi   = acos(-my_1)

    c     = my_0
    cx    = my_0
    cy    = my_0
    cz    = my_0
    cxx   = my_0
    cyy   = my_0
    czz   = my_0
    cxxx  = my_0
    cyyy  = my_0
    czzz  = my_0
    cxxxx = my_0
    cyyyy = my_0
    czzzz = my_0

    sx    = my_0
    sy    = my_0
    sz    = my_0

    if(init == -1) then

!     Use input coefficients

      c     = u_polyf( 1)
      cx    = u_polyf( 2)
      cy    = u_polyf( 3)
      cz    = u_polyf( 4)
      cxx   = u_polyf( 5)
      cyy   = u_polyf( 6)
      czz   = u_polyf( 7)
      cxxx  = u_polyf( 8)
      cyyy  = u_polyf( 9)
      czzz  = u_polyf(10)
      cxxxx = u_polyf(11)
      cyyyy = u_polyf(12)
      czzzz = u_polyf(13)

      sx    = u_sinef( 1)
      sy    = u_sinef( 2)
      sz    = u_sinef( 3)

    elseif(init == 1) then

!     Linear function

      cx    = 1.0_dp
      cy    = 0.0_dp
      cz    = 1.0_dp

    elseif(init == 2) then

!     Quadratic function

      cxx    = 1.0_dp
      cyy    = 0.0_dp
      czz    = 1.0_dp

    endif

    if(present(print_flag) .and. skeleton > 0) then

      write(*,*) '  Computing exact solution via polynomial functions&
                 &...elasticity equations.'
      write(*,*) '  c                =', c
      write(*,*) '  cx,    cy,    cz   =',  cx, cy, cz
      write(*,*) '  cxx,   cyy,   czz  =',  cxx, cyy, czz
      write(*,*) '  cxxx,  cyyy,  czzz =',  cxxx, cyyy, czzz
      write(*,*) '  cxxxx, cyyyy, czzzz =', cxxxx, cyyyy, czzzz

      write(*,*) '  Computing exact solution via sine functions&
                 &...elasticity equations.'
      write(*,*) '  sx,    sy,    sz   =',  sx, sy, sz

    endif

!   Set q_exact

    q_exact = c + cx*x + my_half*cxx*(x**2) + my_6th*cxxx*(x**3) &
                + cy*y + my_half*cyy*(y**2) + my_6th*cyyy*(y**3) &
                + cz*z + my_half*czz*(z**2) + my_6th*czzz*(z**3) &
                + my_12th*cxxxx*(x**4)                           &
                + my_12th*cyyyy*(y**4)                           &
                + my_12th*czzzz*(z**4)

    qx_exact =  cx + cxx*x + my_half*cxxx*(x**2) + my_3rd*cxxxx*(x**3)
    qy_exact =  cy + cyy*y + my_half*cyyy*(y**2) + my_3rd*cyyyy*(y**3)
    qz_exact =  cz + czz*z + my_half*czzz*(z**2) + my_3rd*czzzz*(z**3)



    q_exact = q_exact + my_half*sx*( sin(pi*x)**2 ) &
                      + my_half*sy*( sin(pi*y)**2 ) &
                      + my_half*sz*( sin(pi*z)**2 )

    qx_exact = qx_exact + sx*( sin(pi*x)*cos(pi*x)*pi )
    qy_exact = qy_exact + sy*( sin(pi*y)*cos(pi*y)*pi )
    qz_exact = qz_exact + sz*( sin(pi*z)*cos(pi*z)*pi )


    f_exact = 0.0_dp
    if(0 == 1) then

!   Compute forcing terms based on elasticity variation
!   [either constant or e=f(1/s) for cylinder of unit radius]
!   Assume Poisson's ratio is zero

!   First elliptic terms

    f_exact = cxx       + cyy       + czz      &
            + cxxx*x    + cyyy*y    + czzz*z   &
            + cxxxx*x*x + cyyyy*y*y + czzzz*z*z

    f_exact = f_exact + sx*pi*pi*( -sin(pi*x)**2 + cos(pi*x)**2 ) &
                      + sy*pi*pi*( -sin(pi*y)**2 + cos(pi*y)**2 ) &
                      + sz*pi*pi*( -sin(pi*z)**2 + cos(pi*z)**2 )

    if (elasticity == 1) then

      r = sqrt( x**2 + z**2 )
      rx = x/r
      ry = 0.0_dp
      rz = z/r

      slen   = r - 1.0_dp

      modulus_of_elasticity = my_1 / slen

      mu   =  my_half*modulus_of_elasticity
      mux  = -my_half*rx*(modulus_of_elasticity**2)
      muy  = -my_half*ry*(modulus_of_elasticity**2)
      muz  = -my_half*rz*(modulus_of_elasticity**2)

    elseif(elasticity == 4) then

      mu   = my_1
      mux  = my_0
      muy  = my_0
      muz  = my_0

    elseif(elasticity == 5) then

      r = sqrt( x**2 + z**2 )
      rx = x/r
      ry = 0.0_dp
      rz = z/r

      slen   = r - 1.0_dp

      modulus_of_elasticity = my_1 / slen**2

      mu   =  my_half*modulus_of_elasticity
      mux  = -my_half*rx*(modulus_of_elasticity**2)
      muy  = -my_half*ry*(modulus_of_elasticity**2)
      muz  = -my_half*rz*(modulus_of_elasticity**2)

    else

      write(*,*) ' forcing function not available...stopping'
      write(*,*) ' elasticity=',elasticity
      call lmpi_die

    endif

    f_exact = mu*f_exact + mux*qx_exact + muy*qy_exact + muz*qz_exact

    endif


  end subroutine exact_elasticity


!============================== RES_ELASTICITY ===============================80
!
! Driver routine to compute residuals of the elasticity equations
!
!=============================================================================80

  subroutine res_elasticity( grid, soln, mass )

    use grid_types,     only : grid_type, mass_type
    use solution_types, only : soln_type
    use info_depr,      only : ntt
    use linear_systems, only : dgmres_frechet
    use array_check,    only : csize

    type(grid_type),                          intent(inout) :: grid
    type(soln_type),                          intent(inout) :: soln
    type(mass_type), dimension(move_nbodies), intent(in)    :: mass

    integer :: i, init, ntt_hold, skeleton_hold

    real(dp)    :: xc, yc, zc
    real(dp), dimension(3)    :: q_exact, f_exact

    continue

    skeleton_hold = skeleton
    ntt_hold      = ntt

    if ( dgmres_frechet ) then
      ntt           = 0
      skeleton      = 0
    endif

    if(skeleton > 1) then
      write(*,*)
      write(*,*) '  Compute elasticity residuals.'
      write(*,*) '  ......soln%njac =',soln%njac
    endif

    call csize( 3, grid%nnodes01, size(soln%res,1),         &
                                  size(soln%res,2), 'res')
    call csize( 3, grid%nnodes01, size(soln%q_dof,1),       &
                                  size(soln%q_dof,2),'qnode')

!   Zero residuals

    soln%res(:,:) = my_0

    if (skeleton > 1) write(*,*) '  Compute interior residuals&
              & for elasticity equations with with mixed element path.'

    call set_slen_wall_nonzero( grid%nbound, grid%bc, grid%slen )

    do i = 1, grid%nelem

      call elasticity_rhs(                                                     &
           grid%nnodes0,               grid%nnodes01,                          &
           grid%elem(i)%ncell,                                                 &
           grid%elem(i)%c2n,           grid%elem(i)%c2e,                       &
           grid%x,                     grid%y,                    grid%z,      &
           soln%q_dof,                 soln%res,                               &
           grid%nedgeloc,              grid%nedgeloc_2d,                       &
           grid%elem(i)%local_f2n,     grid%elem(i)%local_e2n,                 &
                                       grid%elem(i)%local_f2e,                 &
           grid%elem(i)%e2n_2d,        grid%elem(i)%face_per_cell,             &
           grid%elem(i)%node_per_cell, grid%elem(i)%edge_per_cell,             &
           grid%slen,                  grid%elem(i)%type_cell,                 &
           grid%elem(i)%face_2d )

    end do

    !...divide by volume for consistency
    if(vol_divide) then

      do i=1,grid%nnodes0
        soln%res(1,i) = soln%res(1,i)/grid%vol(i)
        soln%res(2,i) = soln%res(2,i)/grid%vol(i)
        soln%res(3,i) = soln%res(3,i)/grid%vol(i)
      enddo

    endif

    if(.not.null_boundary_move) then

      if (skeleton > 1) write(*,*) ' Add forcing function&
                                   & of the manufactured solution.'

!     note if we stored f this operation could be done far simpler

      do i=1,grid%nnodes0

        xc = grid%x(i)
        yc = grid%y(i)
        zc = grid%z(i)

        init = -1
        call exact_elasticity( xc, yc, zc, q_exact, f_exact, init )

        if(vol_divide) then
          soln%res(1,i) = soln%res(1,i) - f_exact(1)
          soln%res(2,i) = soln%res(2,i) - f_exact(2)
          soln%res(3,i) = soln%res(3,i) - f_exact(3)
        else
          soln%res(1,i) = soln%res(1,i) - f_exact(1)*grid%vol(i)
          soln%res(2,i) = soln%res(2,i) - f_exact(2)*grid%vol(i)
          soln%res(3,i) = soln%res(3,i) - f_exact(3)*grid%vol(i)
        endif

      enddo
    endif

    if(skeleton > 0) then
      write(*,*) '  Compute boundary residuals for variable elastic modulii'
    endif

    call move_bc_mixed_rhs(                                                  &
         grid%nnodes0, grid%nnodes01,                                        &
         soln%res,     soln%q_dof,   grid%nbound,   grid%bc,                 &
         grid%x,       grid%y,       grid%z,        mass )

    if(skeleton > 0) write(*,*) '  Elasticity residuals complete.'

    call set_slen_wall_zero( grid%nbound, grid%bc, grid%slen )

    if ( dgmres_frechet ) then
      ntt      = ntt_hold
      skeleton = skeleton_hold
    endif

  end subroutine res_elasticity


!============================== INIT_ELASTICITY =============================80
!
! Set initial conditions for elasticity equations
!
!=============================================================================80

  subroutine init_elasticity(grid, soln, init, print_flag )

    use grid_types,     only : grid_type
    use solution_types, only : soln_type

    type(grid_type),           intent(in)    :: grid
    type(soln_type),           intent(inout) :: soln
    integer,                   intent(in)    :: init
    integer,         optional, intent(in)    :: print_flag

    integer :: i

    real(dp)                  :: xc = my_0, yc = my_0, zc = my_0
    real(dp), dimension(3)    :: q_exact, f_exact

    continue

    if (present(print_flag)) then

      call exact_elasticity( xc, yc, zc, q_exact, f_exact, init, 1 )

    else

      do i = 1, grid%nnodes01

        xc = grid%x(i)
        yc = grid%y(i)
        zc = grid%z(i)

        call exact_elasticity( xc, yc, zc, q_exact, f_exact, init )

        soln%q_dof(1,i) = q_exact(1)
        soln%q_dof(2,i) = q_exact(2)
        soln%q_dof(3,i) = q_exact(3)

      end do
    endif

  end subroutine init_elasticity


!========================== FILL_JACOBIAN_ELASTICITY =========================80
!
! Fills up the LHS for the elasticity equations
!
!=============================================================================80

  subroutine fill_jacobian_elasticity(grid, soln, crow)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

  continue

!   Calculate the elasticity Jacobians

    if(skeleton > 1) then
      write(6,*)
      write(6,*) '  Compute elasticity Jacobians.'
      write(6,*) '  ...kind(    diagonal_entries)=',kind(0.0_dp)
      write(6,*) '  ...kind(off_diagonal_entries)=',kind(0.0_odp)
    endif

    soln%a_diag = my_0
    soln%a_off  = my_0_odp

    call set_slen_wall_nonzero( grid%nbound, grid%bc, grid%slen )

    call elasticity_jacobian(grid, soln, crow)

    call set_slen_wall_zero( grid%nbound, grid%bc, grid%slen )

  end subroutine fill_jacobian_elasticity


!=============================== ELASTICITY_JACOBIAN =========================80
!
! Driver routine to compute elasticity jacobians
!
!=============================================================================80

  subroutine elasticity_jacobian(grid, soln, crow)

    use info_depr,         only : twod
    use grid_types,        only : grid_type
    use solution_types,    only : soln_type
    use comprow_types,     only : crow_flow
    use lmpi,              only : lmpi_bcast, lmpi_synchronize
    use lmpi_app,          only : lmpi_collect_res
    use system_extensions, only : se_flush
    use array_check,       only : csize
    use twod_util,         only : set_jac_twod

    type(grid_type),       intent(inout) :: grid
    type(soln_type),       intent(inout) :: soln
    type(crow_flow),       intent(in)    :: crow

    integer :: i
    integer :: eq, print_flag, npts
    integer :: imx(1)

    real(dp), dimension(soln%njac)     :: pmaxsum, pmax, xmax, ymax, zmax

    real(dp)    :: r_dum, t_dum, x_dum, y_dum, z_dum
    real(dp)    :: l2norm_factor, fact1, fact2

    real(dp)    :: gxmax, gymax, gzmax, gpmax
    real(dp)    :: rxmax, rymax, rzmax, grmax

    integer, parameter :: n_profile = 21

    real(dp), dimension(n_profile,soln%njac)    :: p_profile
    real(dp), dimension(n_profile,soln%njac)    :: m_profile
    real(dp), dimension(n_profile,soln%njac)    :: x_profile
    real(dp), dimension(n_profile,soln%njac)    :: y_profile
    real(dp), dimension(n_profile,soln%njac)    :: z_profile

    continue

    if(skeleton > 1) then
      write(6,*) '  Compute Jacobians&
                 & of elasticity equations...interior terms.'
      write(6,*) ' number of element types=',grid%nelem
      do i = 1, grid%nelem
        write(6,*) ' element number=',i
        write(6,*) '          ncell=',grid%elem(i)%ncell
        write(6,*) '      type_cell=',grid%elem(i)%type_cell
        write(6,*) '  face_per_cell=',grid%elem(i)%face_per_cell
        write(6,*) '  edge_per_cell=',grid%elem(i)%edge_per_cell
        write(6,*) '  node_per_cell=',grid%elem(i)%node_per_cell
      end do
    endif

!   Check size of some arrays

    call csize( grid%nnodes01, size(grid%vol),  'vol')
    call csize( grid%nnodes01, size(grid%x),    'x')
    call csize( grid%nnodes01, size(grid%y),    'y')
    call csize( grid%nnodes01, size(grid%z),    'z')

    call csize( grid%nnodes01+1, size(crow%ia),  'ia')
    call csize( crow%nnz01,      size(crow%ja),  'ja')

    call csize( grid%nnodes0, size(soln%a_diag,3), 'a_diag')
    call csize( crow%nnz0,    size(soln%a_off,3),  'a_off')

    if(skeleton > 2) then
      write(6,*) '        nnodes0=',grid%nnodes0
      write(6,*) '      nnnodes01=',grid%nnodes01
      write(6,*) '       nedgeloc=',grid%nedgeloc
      write(6,*) '          nedge=',grid%nedge
      write(6,*) '    nedgeloc_2d=',grid%nedgeloc_2d
    endif

    do i = 1, grid%nelem
      call elasticity_lhs(                                                     &
           grid%nnodes0,               grid%nnodes01,                          &
           grid%elem(i)%ncell,                                                 &
           grid%nedgeloc,              grid%nedgeloc_2d,                       &
           crow%nnz0,                  grid%elem(i)%c2n,                       &
           grid%elem(i)%c2e,                                                   &
           grid%x,                     grid%y,                     grid%z,     &
           soln%a_diag,                soln%a_off,                             &
           grid%elem(i)%local_f2n,                                             &
           grid%elem(i)%local_e2n,                                             &
           grid%elem(i)%local_f2e,     grid%elem(i)%e2n_2d,                    &
           grid%elem(i)%face_per_cell, grid%elem(i)%node_per_cell,             &
           grid%elem(i)%edge_per_cell,                                         &
           crow%nnz01,                 crow%ia,                                &
           crow%ja,                    grid%vol,                               &
           grid%slen,                  grid%elem(i)%type_cell,                 &
           grid%elem(i)%face_2d,       crow%nzg2m, crow%g2m )
    end do

    if(skeleton > 1) write(6,*) '  Compute Jacobians&
                         & of elasticity equations...boundary terms.'

    call move_bc_mixed_lhs(                                                    &
         grid%nnodes0, grid%nnodes01,                                          &
         soln%a_diag,  soln%a_off,                                             &
         crow%nnz0,    crow%iam,                                               &
         grid%nbound,  grid%bc, crow%g2m)

    if ( twod ) call set_jac_twod( soln%eqn_set, soln%a_diag, soln%a_off )

!   Set a global printing flag here

    call lmpi_reduce(skeleton,print_flag)
    call lmpi_bcast(print_flag)

    check : if((print_flag > 10) .and. .not.null_boundary_move) then

      !...check for positivity of coefficient matrix
      call matrix_lhsp( pmaxsum, pmax, xmax, ymax, zmax,     &
               n_profile, p_profile, m_profile,              &
               x_profile, y_profile, z_profile,              &
               grid%nnodes0, grid%nnodes01,                  &
               crow%nnz0,                                    &
               grid%x,         grid%y,       grid%z,         &
               soln%a_diag,    soln%a_off,                   &
               crow%iam, soln%njac, crow%g2m )

      eq_loop_1 : do eq = 1,soln%njac
        r_dum = my_0
        t_dum = my_0
        x_dum = my_0
        y_dum = my_0
        z_dum = my_0
        call lmpi_collect_res( r_dum,     t_dum,   pmax(eq), &
                               xmax(eq), ymax(eq), zmax(eq) )

        call lmpi_collect_res( r_dum, t_dum, pmaxsum(eq), &
                               x_dum, y_dum, z_dum )

        do i=1,n_profile
          call lmpi_collect_res(                                  &
               p_profile(i,eq), t_dum,           m_profile(i,eq), &
               x_profile(i,eq), y_profile(i,eq), z_profile(i,eq) )
        enddo

        if(lmpi_master) then
          write(6,*)
          write(6,*) ' Statistics for equation',eq
          write(6,*) ' Maximum sum non-positive off-diagonals to diagonal=', &
                                                                pmaxsum(eq)
          write(6,*) '      Maximum non-positive off-diagonal to diagonal=', &
                                                               pmax(eq)
          write(6,*) ' ....at x,y,z=',xmax(eq),ymax(eq),zmax(eq)
          write(6,*) ' '

          write(6,*) ' Variation of non-positive off-diagonal terms.'
          write(6,*) ' Values ratioed to diagonal....total number of nodes=', &
                                                                grid%nnodesg
          write(6,*)
          l2norm_factor = real(grid%nnodesg,dp)
          write(6,"(5x,'bin',4x,'np:min',3x,'<np:max',3x,'percent',&
                   &5x,'nodes',5x,'value',12x,'x',12x,'y',12x,'z')")
          profile_loop : do i=n_profile,1,-1
            fact1 = real(2*(i-1),dp)/real(n_profile-1,dp)
            fact2 = real(2*(  i),dp)/real(n_profile-1,dp)
            !...avoid complexification issues with this construct
            npts  = nint( real( p_profile(i,eq),dp ) )
            if(i == n_profile) then
              fact2 = fact1
              write(6,"(i8,f10.2,2x,'infinity',f10.4,i10)") i,fact1,           &
                   100._dp*p_profile(i,eq)/l2norm_factor,npts
            elseif(npts > 0) then
              write(6,"(i8,2f10.2,f10.4,i10,f10.4,3e13.5)") i,fact1,fact2,     &
                   100._dp*p_profile(i,eq)/l2norm_factor,npts,                 &
                   m_profile(i,eq),x_profile(i,eq),y_profile(i,eq),            &
                   z_profile(i,eq)
            else
              write(6,"(i8,2f10.2,f10.4,i10)") i,fact1,fact2,&
                   100._dp*p_profile(i,eq)/l2norm_factor,npts
            endif
          enddo profile_loop
        endif

      enddo eq_loop_1

!     Find the location of the worst positivity

      gpmax = -huge(1.0_dp)
      eq_loop_2 : do eq = 1,soln%njac
        if(pmax(eq) > gpmax) then
          gpmax = pmax(eq)
          gxmax = xmax(eq)
          gymax = ymax(eq)
          gzmax = zmax(eq)
        endif
      enddo eq_loop_2

      call lmpi_bcast(gxmax)
      call lmpi_bcast(gymax)
      call lmpi_bcast(gzmax)

      call se_flush(6)
      call lmpi_synchronize

      !...print some information at  gxmax, gymax, gzmax
      if(lmpi_master) then
        write(6,*)
        write(6,*) ' Elements of linearization for worst positivity location.'
        write(6,*)
      endif
      call matrix_lhsc( gxmax,          gymax,             gzmax,            &
                        grid%nnodes0,   grid%nnodes01,                       &
                        crow%nnz0,      grid%vol,                            &
                        soln%q_dof,     soln%res,                            &
                        grid%x,         grid%y,            grid%z,           &
                        soln%a_diag,    soln%a_off,                          &
                        crow%nnz01,     crow%iam,          crow%jam,         &
                        soln%njac,      soln%n_tot, crow%g2m, crow%m2g)

      call se_flush(6)
      call lmpi_synchronize

      grmax = -1.0_dp

      eq_loop_3 : do eq=1,soln%njac
      r_dum = 0.0_dp
      t_dum = 0.0_dp
      !...find location of maximum residuals
      imx  = maxloc( abs( soln%res(eq,1:grid%nnodes0) ) )
      gpmax = abs( soln%res(eq,imx(1)) )
      gxmax =      grid%x(     imx(1))
      gymax =      grid%y(     imx(1))
      gzmax =      grid%z(     imx(1))

      call lmpi_collect_res( r_dum, t_dum, gpmax, gxmax, gymax, gzmax )

      if(lmpi_master) then
        write(6,*) ' For equation=',eq
        write(6,*) ' Residual maximum =',gpmax
        write(6,*) ' ...at x,y,z=',gxmax,gymax,gzmax
      endif

      !...broadcast these results
      call lmpi_bcast(gpmax)
      call lmpi_bcast(gxmax)
      call lmpi_bcast(gymax)
      call lmpi_bcast(gzmax)

      !...find location of global maximum
      if(gpmax > grmax) then
        grmax = gpmax
        rxmax = gxmax
        rymax = gymax
        rzmax = gzmax
      endif

      end do eq_loop_3

      call se_flush(6)
      call lmpi_synchronize

      !...print some information at rxmax, rymax, rzmax
      if(lmpi_master) then
        write(6,*)
        write(6,*) ' Elements of linearization at highest residual location.'
        write(6,*) ' ....x=',rxmax,rymax,rzmax
        write(6,*)
      endif
      call matrix_lhsc( rxmax,          rymax,             rzmax,            &
                        grid%nnodes0,   grid%nnodes01,                       &
                        crow%nnz0,      grid%vol,                            &
                        soln%q_dof,     soln%res,                            &
                        grid%x,         grid%y,            grid%z,           &
                        soln%a_diag,    soln%a_off,                          &
                        crow%nnz01,     crow%iam,          crow%jam,         &
                        soln%n_tot,     soln%njac, crow%g2m, crow%m2g )

      call se_flush(6)
      call lmpi_synchronize


      !...print some information at x=0, y=0, z=0
      gxmax = 0.0_dp
      gymax = 0.0_dp
      gzmax = 0.0_dp

      !...broadcast these locations
      call lmpi_bcast(gxmax)
      call lmpi_bcast(gymax)
      call lmpi_bcast(gzmax)

      !...print some information at gxmax, gymax, gzmax
      if(lmpi_master) then
        write(6,*)
        write(6,*) ' Elements of linearization at specified x,y,z.'
        write(6,*)
      endif
      call matrix_lhsc( gxmax,          gymax,             gzmax,            &
                        grid%nnodes0,   grid%nnodes01,                       &
                        crow%nnz0,      grid%vol,                            &
                        soln%q_dof,     soln%res,                            &
                        grid%x,         grid%y,            grid%z,           &
                        soln%a_diag,    soln%a_off,                          &
                        crow%nnz01,     crow%iam,          crow%jam,         &
                        soln%n_tot,     soln%njac, crow%g2m, crow%m2g )

      call se_flush(6)
      call lmpi_synchronize

    endif check

    if(skeleton > 1) write(6,*) '  Elasticity Jacobians complete.'

  end subroutine elasticity_jacobian


!================================ MOVE_BC_MIXED_RHS ==========================80
!
! Fill the boundary conditions for the matrix associated with the linear system.
!
!=============================================================================80

  subroutine move_bc_mixed_rhs(nnodes0, nnodes01, res, qnode,                  &
                               nbound, bc, x, y, z, mass)

    use info_depr,               only : ntt, reduce_shape_change, reduce_factor
    use moves,                   only : elasticity
    use bc_types,                only : bcgrid_type
    use nml_boundary_conditions, only : x_constant_boundary,                   &
                                        y_constant_boundary,                   &
                                        z_constant_boundary
    use system_extensions,       only : se_flush
    use grid_types,              only : mass_type
    use array_check,             only : csize

    integer,                                    intent(in)    :: nnodes0
    integer,                                    intent(in)    :: nnodes01
    integer,                                    intent(in)    :: nbound
    real(dp),       dimension(3,nnodes01),      intent(inout) :: res
    real(dp),       dimension(3,nnodes01),      intent(in)    :: qnode
    type(bcgrid_type), dimension(nbound),       intent(in)    :: bc
    real(dp),       dimension(nnodes01),        intent(in)    :: x, y, z
    type(mass_type),   dimension(move_nbodies), intent(in)    :: mass

    integer :: i, inode, ib, ibody, rhs_total, rhs_local, init

    real(dp)    :: rhs_x_abs_sum, rhs_y_abs_sum, rhs_z_abs_sum
    real(dp)    :: delta_x_abs_sum, delta_y_abs_sum, delta_z_abs_sum
    real(dp)    :: rhs_x, rhs_y, rhs_z
    real(dp)    :: delta_x, delta_y, delta_z
    real(dp)    :: f, xc, yc, zc

    real(dp), dimension(3)         :: q_exact, f_exact

  continue

!   Check size of some arrays

    call csize( nbound, size(bc), 'bc')

    f = my_1
    if(null_boundary_move) f = my_0

! Set the matrix so the boundaries will be set correctly. Note that on all but
! "special" boundaries we have dirchlet boundary conditions. "Special" implies
! we allow movement in a x/yz=constant plane, but not across it.

    bound_loop : do ib = 1, nbound

!     Take care of "special" boundaries first

      special_bc : if ( y_constant_boundary(ib) ) then

        if(skeleton > 0) write(*,*) '   Special boundary ib=',ib,' of',nbound

        special_nodes_y : do i = 1, bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)
          local_special_bc_y : if (inode <= nnodes0) then

!           Enforce prescribed y-value of form [ q(current) - q(exact) ]

            xc = x(inode)
            yc = y(inode)
            zc = z(inode)

            init = -1
            call exact_elasticity( xc, yc, zc, q_exact, f_exact, init )

            res(2,inode) = f*( qnode(2,inode) - q_exact(2) )

          endif local_special_bc_y

        end do special_nodes_y

      else if ( x_constant_boundary(ib) ) then

        if(skeleton > 0) write(*,*) '   Special boundary ib=',ib,' of',nbound

        special_nodes_x : do i = 1, bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)
          local_special_bc_x : if (inode <= nnodes0) then

!           Enforce prescribed x-value of form [ q(current) - q(exact) ]

            xc = x(inode)
            yc = y(inode)
            zc = z(inode)

            init = -1
            call exact_elasticity( xc, yc, zc, q_exact, f_exact, init )

            res(1,inode) = f*( qnode(1,inode) - q_exact(1) )

          endif local_special_bc_x

        end do special_nodes_x

      else if ( z_constant_boundary(ib) ) then

        if(skeleton > 0) write(*,*) '   Special boundary ib=',ib,' of',nbound

        special_nodes_z : do i = 1, bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)
          local_special_bc_z : if (inode <= nnodes0) then

!           Enforce prescribed z-value of form [ q(current) - q(exact) ]

            xc = x(inode)
            yc = y(inode)
            zc = z(inode)

            init = -1
            call exact_elasticity( xc, yc, zc, q_exact, f_exact, init )

            res(3,inode) = f*( qnode(3,inode) - q_exact(3) )

          endif local_special_bc_z

        end do special_nodes_z

      else special_bc

!       All the ordinary boundaries

        if(skeleton > 0) write(*,*) '   Ordinary boundary ib=',ib,&
                                      ' of',nbound

        nodes : do i = 1,bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)

          local_ordinary_bc : if (inode <= nnodes0) then

!           Enforce prescribed values of form [ q(current) - q(exact) ]

            xc = x(inode)
            yc = y(inode)
            zc = z(inode)

            init = -1
            call exact_elasticity( xc, yc, zc, q_exact, f_exact, init )

            res(1,inode) = f*( qnode(1,inode) - q_exact(1) )
            res(2,inode) = f*( qnode(2,inode) - q_exact(2) )
            res(3,inode) = f*( qnode(3,inode) - q_exact(3) )

          end if local_ordinary_bc

        end do nodes

      end if special_bc

    end do bound_loop

!   Modify residual for massoud points

    rhs_x_abs_sum = 0.0_dp
    rhs_y_abs_sum = 0.0_dp
    rhs_z_abs_sum = 0.0_dp
    delta_x_abs_sum = 0.0_dp
    delta_y_abs_sum = 0.0_dp
    delta_z_abs_sum = 0.0_dp

    if(elasticity /= 4) then

      rhs_local = 0

      body_loop : do ibody = 1, move_nbodies

        if(skeleton > 0) write(*,*) 'Modifying res for body=',ibody, &
                                    'of',move_nbodies

        do i = 1, mass(ibody)%itotal

          inode = mass(ibody)%inodemt(i)

          if(inode <= nnodes0) then

            xc = x(inode)
            yc = y(inode)
            zc = z(inode)

            init = -1
            call exact_elasticity( xc, yc, zc, q_exact, f_exact, init )

            q_exact(1) = q_exact(1) + mass(ibody)%xmt(i)
            q_exact(2) = q_exact(2) + mass(ibody)%ymt(i)
            q_exact(3) = q_exact(3) + mass(ibody)%zmt(i)

!           x(new) = x(old) + qnode = x(mass) + dx(manufactured)

            res(1,inode) = f*( xc + qnode(1,inode) - q_exact(1) )
            res(2,inode) = f*( yc + qnode(2,inode) - q_exact(2) )
            res(3,inode) = f*( zc + qnode(3,inode) - q_exact(3) )

            if ( reduce_shape_change ) then
              res = res / reduce_factor
            endif

!           Accumulate averages for on-processor nodes

            rhs_x_abs_sum = rhs_x_abs_sum + abs( res(1,inode) )
            rhs_y_abs_sum = rhs_y_abs_sum + abs( res(2,inode) )
            rhs_z_abs_sum = rhs_z_abs_sum + abs( res(3,inode) )
            delta_x_abs_sum = delta_x_abs_sum + abs( f*qnode(1,inode) )
            delta_y_abs_sum = delta_y_abs_sum + abs( f*qnode(2,inode) )
            delta_z_abs_sum = delta_z_abs_sum + abs( f*qnode(3,inode) )
            rhs_local = rhs_local + 1

          endif

        end do

      end do body_loop

      call lmpi_reduce( rhs_x_abs_sum, rhs_x )
      call lmpi_reduce( rhs_y_abs_sum, rhs_y )
      call lmpi_reduce( rhs_z_abs_sum, rhs_z )
      call lmpi_reduce( delta_x_abs_sum, delta_x )
      call lmpi_reduce( delta_y_abs_sum, delta_y )
      call lmpi_reduce( delta_z_abs_sum, delta_z )
      call lmpi_reduce( rhs_local,     rhs_total )

      if(lmpi_master .and. ntt > 0) then

        rhs_x = rhs_x/real( rhs_total, dp )
        rhs_y = rhs_y/real( rhs_total, dp )
        rhs_z = rhs_z/real( rhs_total, dp )
        delta_x = delta_x/real( rhs_total, dp )
        delta_y = delta_y/real( rhs_total, dp )
        delta_z = delta_z/real( rhs_total, dp )

        if(skeleton > 0) then
          write(*,*) 'average |rhs-x|,|delta_x| =',real(   rhs_x, dp ),   &
                                                   real( delta_x, dp )
          write(*,*) 'average |rhs-y|,|delta_y| =',real(   rhs_y, dp ),   &
                                                   real( delta_y, dp )
          write(*,*) 'average |rhs-z|,|delta_z| =',real(   rhs_z, dp ),   &
                                                   real( delta_z, dp )
        endif

        if(.not.null_boundary_move  .and. ntt <= 5) then
          write(unit_info,*) '...rhs_total=',rhs_total,'   ntt=',ntt
          write(unit_info,*) 'average |rhs-x|,|delta_x| =',real(   rhs_x, dp ),&
                                                           real( delta_x, dp )
          write(unit_info,*) 'average |rhs-y|,|delta_y| =',real(   rhs_y, dp ),&
                                                           real( delta_y, dp )
          write(unit_info,*) 'average |rhs-z|,|delta_z| =',real(   rhs_z, dp ),&
                                                           real( delta_z, dp )


          call se_flush(unit_info)
        endif

      endif

    endif

  end subroutine move_bc_mixed_rhs


!================================ MOVE_BC_MIXED_LHS ==========================80
!
! Fill the boundary conditions for the matrix associated with the linear system.
!
!=============================================================================80

  subroutine move_bc_mixed_lhs(nnodes0, nnodes01, a_diag, a_off, nnz0, iam,    &
                               nbound, bc, g2m)

    use bc_types,    only : bcgrid_type
    use nml_boundary_conditions, only : x_constant_boundary,                   &
                                        y_constant_boundary,                   &
                                        z_constant_boundary

    integer,                                   intent(in)    :: nnodes0
    integer,                                   intent(in)    :: nnodes01
    integer,                                   intent(in)    :: nnz0
    integer,                                   intent(in)    :: nbound
    integer,           dimension(nnodes01+1),  intent(in)    :: iam
    integer,           dimension(:),           intent(in)    :: g2m
    real(dp),          dimension(3,3,nnodes0), intent(inout) :: a_diag
    real(odp),         dimension(3,3,nnz0),    intent(inout) :: a_off
    type(bcgrid_type), dimension(nbound),      intent(in)     :: bc

    integer :: i, ii, ib
    integer :: inode, row

    real(dp)    :: diagonal

  continue

! Set the matrix so the boundaries will be set correctly. Note that on all but
! "special" boundaries we have dirchlet boundary conditions. "Special" implies
! we allow movement in a x/y/z=constant plane, but not across it.

    diagonal = my_1
    if(null_boundary_move) diagonal = my_0

    bound_loop : do ib = 1, nbound

!     Take care of "special" boundaries first

      special_bc : if ( y_constant_boundary(ib) ) then

        if(skeleton > 0) write(*,*) '   Special boundary ib=',ib,' of',nbound

        special_nodes_y : do i = 1, bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)
          local_special_bc_y : if (inode <= nnodes0) then

!           Zero out entries in the row of interest

            row = g2m(inode)
            a_diag(2,:,row) = my_0
            do ii = iam(row),iam(row+1) - 1
              a_off(2,:,ii) = my_0_odp
            end do

!           Now fill the diagonals with the identity matrix

            a_diag(2,2,row) = diagonal

          endif local_special_bc_y

        end do special_nodes_y

      else if ( x_constant_boundary(ib) ) then

        if(skeleton > 0) write(*,*) '   Special boundary ib=',ib,' of',nbound

        special_nodes_x : do i = 1, bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)
          local_special_bc_x : if (inode <= nnodes0) then

!           Zero out entries in the row of interest

            row = g2m(inode)
            a_diag(1,:,row) = my_0
            do ii = iam(row),iam(row+1) - 1
              a_off(1,:,ii) = my_0_odp
            end do

!           Now fill the diagonals with the identity matrix

            a_diag(1,1,row) = diagonal

          endif local_special_bc_x

        end do special_nodes_x

      else if ( z_constant_boundary(ib) ) then

        if(skeleton > 0) write(*,*) '   Special boundary ib=',ib,' of',nbound

        special_nodes_z : do i = 1, bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)
          local_special_bc_z : if (inode <= nnodes0) then

!           Zero out entries in the row of interest

            row = g2m(inode)
            a_diag(3,:,row) = my_0
            do ii = iam(row),iam(row+1) - 1
              a_off(3,:,ii) = my_0_odp
            end do

!           Now fill the diagonals with the identity matrix

            a_diag(3,3,row) = diagonal

          endif local_special_bc_z

        end do special_nodes_z

      else special_bc

!       All the ordinary boundaries

          if(skeleton > 0) write(*,*) '   Ordinary boundary ib=',ib,&
                                      ' of',nbound

          nodes : do i = 1,bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)

          local_ordinary_bc : if (inode <= nnodes0) then

!           Zero out all entries

            row = g2m(inode)
            a_diag(:,:,row) = my_0
            do ii = iam(row),iam(row+1) - 1
              a_off(:,:,ii) = my_0_odp
            end do

!           Now fill diagonal with the identity matrix

            a_diag(1,1,row) = diagonal
            a_diag(2,2,row) = diagonal
            a_diag(3,3,row) = diagonal

          end if local_ordinary_bc

        end do nodes

      end if special_bc

    end do bound_loop

  end subroutine move_bc_mixed_lhs

!=========================== ELASTICITY_RHS ==================================80
!
! This routine computes the elasticity fluxes for general (mixed) elements
!
!=============================================================================80

  subroutine elasticity_rhs(nnodes0, nnodes01, ncell,                          &
                        c2n, c2e,                                              &
                        x, y, z, qnode, res, nedgeloc, nedgeloc_2d,local_f2n,  &
                        local_e2n, local_f2e, e2n_2d,                          &
                        face_per_cell, node_per_cell, edge_per_cell,           &
                        slen, type_cell, face_2d)

    use info_depr,        only : twod, use_edge_gradients
    use debug_defs,       only : gradient_construction_rhs
    use moves,            only : elasticity
    use element_defs,     only : max_node_per_cell, max_face_per_cell,    &
                                 max_edge_per_cell
    use utilities,        only : tangents, tinverse, cell_gradients
    use lmpi,             only : lmpi_bcast

    character(len=3),                             intent(in) :: type_cell

    integer,                                      intent(in) :: nnodes0
    integer,                                      intent(in) :: nnodes01
    integer,                                      intent(in) :: ncell
    integer,                                      intent(in) :: nedgeloc
    integer,                                      intent(in) :: nedgeloc_2d
    integer,                                      intent(in) :: face_per_cell
    integer,                                      intent(in) :: node_per_cell
    integer,                                      intent(in) :: edge_per_cell
    integer,                                      intent(in) :: face_2d

    integer,     dimension(node_per_cell,ncell),         intent(in) :: c2n
    integer,     dimension(edge_per_cell,ncell),         intent(in) :: c2e
    integer,     dimension(face_per_cell,4),             intent(in) :: local_f2n
    integer,     dimension(edge_per_cell,6),             intent(in) :: local_e2n
    integer,     dimension(face_per_cell,4),             intent(in) :: local_f2e
    integer,     dimension(4,2),                         intent(in) :: e2n_2d

    real(dp), dimension(nnodes01),                       intent(in)    :: x,y,z
    real(dp), dimension(nnodes01),                       intent(in)    :: slen
    real(dp), dimension(3,nnodes01),                     intent(in)    :: qnode
    real(dp), dimension(3,nnodes01),                     intent(inout) :: res

    integer     :: n, nedge_flux_eval
    integer     :: ie, i, ie_local, i_local
    integer     :: nodes_local, edges_local
    integer     :: n1_loc, n2_loc, edge, node
    integer     :: n1, n2, n3, n4, n5, n6
    integer     :: size_slen, slen_weight, slen_ierr, ierr

    real(dp)    :: ux, uy, uz
    real(dp)    :: vx, vy, vz
    real(dp)    :: wx, wy, wz
    real(dp)    :: uxavg, uyavg, uzavg
    real(dp)    :: vxavg, vyavg, vzavg
    real(dp)    :: wxavg, wyavg, wzavg
    real(dp)    :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp)    :: xc, yc, zc, cell_vol, fact
    real(dp)    :: ex, ey, ez, disi
    real(dp)    :: uf, vf, wf
    real(dp)    :: u1, u2, v1, v2, w1, w2
    real(dp)    :: egradu, gradu_xi
    real(dp)    :: egradv, gradv_xi
    real(dp)    :: egradw, gradw_xi
    real(dp)    :: rax, ray, raz
    real(dp)    :: xnf, ynf, znf, rai
    real(dp)    :: lx, ly, lz
    real(dp)    :: lgradu, mgradu
    real(dp)    :: lgradv, mgradv
    real(dp)    :: lgradw, mgradw
    real(dp)    :: mx, my, mz

    real(dp), dimension(3,3)                    :: b, t
    real(dp), dimension(max_node_per_cell)      :: x_node, y_node, z_node
    real(dp), dimension(3,max_node_per_cell)    :: q_node
    real(dp), dimension(3)                      :: gradx_cell
    real(dp), dimension(3)                      :: grady_cell
    real(dp), dimension(3)                      :: gradz_cell

!   dimension these work arrays by the max nodes or faces per cell in any cell,
!   rather than the number in the current cell type to avoid repetitive
!   alloc/dealloc; these are tiny arrays

    integer,     dimension(max_node_per_cell)   :: node_map
    integer,     dimension(max_edge_per_cell)   :: edge_map

    real(dp), dimension(max_face_per_cell)      :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)      :: u_node
    real(dp), dimension(max_node_per_cell)      :: v_node
    real(dp), dimension(max_node_per_cell)      :: w_node

    real(dp)    :: mu, lambda, theta, tinclude
    real(dp)    :: modulus_of_elasticity, slen_average
    real(dp)    :: xmu, xlambda
    real(dp)    :: ymu, ylambda
    real(dp)    :: zmu, zlambda

    logical     :: edge_gradients
    logical     :: slen_needed

    real(dp), parameter    :: my_half = 0.50_dp
    real(dp), parameter    :: my_3rd  = 1.0_dp/3.0_dp
    real(dp), parameter    :: my_4th  = 0.25_dp

  continue

    slen_needed = .false.
    if ((elasticity == 1) .or. (elasticity == 3) .or. (elasticity == 5)) then
      slen_needed = .true.
    end if

!   Determine size of slen array

    size_slen = size(slen,1)
    slen_ierr = 0

    edge_gradients = use_edge_gradients

!   For tets in 3D or prisms in 2D, edge gradients add no new info
!   so there is no need to do the extra work

    if (type_cell == 'tet') edge_gradients = .false.
    if (twod .and. type_cell == 'prz') edge_gradients = .false.

    tinclude = my_1

    u_node(:) = my_0
    v_node(:) = my_0
    w_node(:) = my_0

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

    edge_map(:) = 0
    node_map(:) = 0

    if (twod) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

      nedge_flux_eval = nedgeloc_2d

    else

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

      nedge_flux_eval = nedgeloc

    end if

! Loop over the cells

    cell_loop: do n = 1, ncell

! Initialization

      ux = my_0
      uy = my_0
      uz = my_0

      vx = my_0
      vy = my_0
      vz = my_0

      wx = my_0
      wy = my_0
      wz = my_0

      xc = my_0
      yc = my_0
      zc = my_0

      x_node(:)   = my_0
      y_node(:)   = my_0
      z_node(:)   = my_0
      u_node(:)   = my_0
      v_node(:)   = my_0
      w_node(:)   = my_0
      q_node(:,:) = my_0

! Compute cell averages, cell center, and set up some local solution arrays

      node_loop : do i_local = 1, nodes_local

!       local node number

        i = node_map(i_local)

!       global node number

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        u_node(i) = qnode(1,node)
        v_node(i) = qnode(2,node)
        w_node(i) = qnode(3,node)

      end do node_loop

!     compute the cell center (must loop over node_per_cell even in 2D)

      slen_weight  = 0
      slen_average = 0.0_dp

      do i = 1, node_per_cell

!       global node number

        node = c2n(i,n)

        xc  =  xc + x(node)
        yc  =  yc + y(node)
        zc  =  zc + z(node)

        if (node <= size_slen .and. slen_needed) then
          slen_average = slen_average + slen(node)
          slen_weight  = slen_weight + 1
        end if

      end do

      fact = 1._dp / real(node_per_cell, dp)

      xc  =  xc*fact
      yc  =  yc*fact
      zc  =  zc*fact

      if (slen_weight > 0) then

        slen_average = slen_average/real(slen_weight, dp)
        slen_average = max(slen_average, epsilon(1.0_dp))

      else

!       no nodes in this cell that have slen...possible problem

        slen_ierr = slen_ierr + 1
        slen_average = 1._dp

      endif

! Get the gradients in the primal cell via Green-Gauss

      q_node(1,:) = u_node(:)
      q_node(2,:) = v_node(:)
      q_node(3,:) = w_node(:)

      call cell_gradients(edges_local, max_node_per_cell,face_per_cell,        &
                          x_node, y_node, z_node, 3, q_node, local_f2n,        &
                          e2n_2d, gradx_cell, grady_cell, gradz_cell,          &
                          cell_vol, nx, ny, nz)

      uxavg = gradx_cell(1)
      uyavg = grady_cell(1)
      uzavg = gradz_cell(1)

      vxavg = gradx_cell(2)
      vyavg = grady_cell(2)
      vzavg = gradz_cell(2)

      wxavg = gradx_cell(3)
      wyavg = grady_cell(3)
      wzavg = gradz_cell(3)

      modulus_of_elasticity = elasticity_modulus(cell_vol, slen_average)

      if(elasticity == 4) then

        mu       = my_1
        lambda   = my_0
        tinclude = my_0

      else

        mu     = modulus_of_elasticity / 2.0_dp / (my_1 + poisson)
        lambda = poisson * modulus_of_elasticity / (my_1 + poisson)       &
                                             / (my_1 - 2.0_dp*poisson)

      endif

! Next loop over the edges in the cell and get each ones
! contribution to the residual

      edge_loop : do ie_local = 1,edges_local

!       local edge number

        ie = edge_map(ie_local)

!       global edge number

        edge = c2e(ie,n)

!       check edge to make sure it is not off-processor - if it is
!       we don't need its contribution anyway

        if (edge > nedge_flux_eval) cycle edge_loop

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)

!       global node numbers of edge endpoints

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

! Get this edges' contributiuon to the dual normal and area

!       edge midpoint

        xm = (x(n1) + x(n2))*my_half
        ym = (y(n1) + y(n2))*my_half
        zm = (z(n1) + z(n2))*my_half

!       compute left face centroid

        n3 = c2n(local_e2n(ie,3),n)

        if (local_e2n(ie,4) /= 0) then

!         quad cell face

          n4 = c2n(local_e2n(ie,4),n)

          xl = (x(n1) + x(n2) + x(n3) + x(n4))*my_4th
          yl = (y(n1) + y(n2) + y(n3) + y(n4))*my_4th
          zl = (z(n1) + z(n2) + z(n3) + z(n4))*my_4th

        else

!         tria cell face

          xl = (x(n1) + x(n2) + x(n3))*my_3rd
          yl = (y(n1) + y(n2) + y(n3))*my_3rd
          zl = (z(n1) + z(n2) + z(n3))*my_3rd

        end if

!       compute right face centroid

        n5 = c2n(local_e2n(ie,5),n)

        if (local_e2n(ie,6) /= 0) then

!         quad cell face

          n6 = c2n(local_e2n(ie,6),n)

          xr = (x(n1) + x(n2) + x(n5) + x(n6))*my_4th
          yr = (y(n1) + y(n2) + y(n5) + y(n6))*my_4th
          zr = (z(n1) + z(n2) + z(n5) + z(n6))*my_4th

        else

!         tria cell face

          xr = (x(n1) + x(n2) + x(n5))*my_3rd
          yr = (y(n1) + y(n2) + y(n5))*my_3rd
          zr = (z(n1) + z(n2) + z(n5))*my_3rd

        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        rax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        ray = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        raz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

! Get gradients at the dual face; either take gradients for
! this piece of the dual face to be the same as the cell-average gradient
! computed above  (which is what the legacy FUN3D solver does for tets),
! or combine with the edge-gradient to increase h-ellipticity.

        include_edge_terms : if(edge_gradients .and. &
                                gradient_construction_rhs == 0 ) then

!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

!         primitive variables at nodes 1 and 2

          u1 = qnode(1,n1)
          u2 = qnode(1,n2)
          v1 = qnode(2,n1)
          v2 = qnode(2,n2)
          w1 = qnode(3,n1)
          w2 = qnode(3,n2)

!         directional gradients along edge

          egradu = ( u2 - u1 )*disi
          egradv = ( v2 - v1 )*disi
          egradw = ( w2 - w1 )*disi

!         average Green-Gauss gradient in edge direction

          gradu_xi = uxavg*ex + uyavg*ey + uzavg*ez
          gradv_xi = vxavg*ex + vyavg*ey + vzavg*ez
          gradw_xi = wxavg*ex + wyavg*ey + wzavg*ez

!         combine gradient contributions from edge and primal cell

          ux = uxavg + ( egradu - gradu_xi )*ex
          uy = uyavg + ( egradu - gradu_xi )*ey
          uz = uzavg + ( egradu - gradu_xi )*ez

          vx = vxavg + ( egradv - gradv_xi )*ex
          vy = vyavg + ( egradv - gradv_xi )*ey
          vz = vzavg + ( egradv - gradv_xi )*ez

          wx = wxavg + ( egradw - gradw_xi )*ex
          wy = wyavg + ( egradw - gradw_xi )*ey
          wz = wzavg + ( egradw - gradw_xi )*ez

        elseif(edge_gradients .and. &
               gradient_construction_rhs == 1 ) then include_edge_terms

!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

!         primitive variables at nodes 1 and 2

          u1 = qnode(1,n1)
          u2 = qnode(1,n2)
          v1 = qnode(2,n1)
          v2 = qnode(2,n2)
          w1 = qnode(3,n1)
          w2 = qnode(3,n2)

          !...find tangent vectors in the dual face
          rai = my_1/sqrt( rax**2 + ray**2 + raz**2 )
          xnf = rax*rai
          ynf = ray*rai
          znf = raz*rai
          call tangents(xnf, ynf, znf, lx, ly, lz, mx, my, mz)

          !...form transformation matrix
          !...edge direction
          b(1,1) = ex
          b(1,2) = ey
          b(1,3) = ez

          !...l direction
          b(2,1) = lx
          b(2,2) = ly
          b(2,3) = lz

          !...m direction
          b(3,1) = mx
          b(3,2) = my
          b(3,3) = mz

          call tinverse( b , t )

!         directional gradients

          egradu = ( u2 - u1 )*disi
          egradv = ( v2 - v1 )*disi
          egradw = ( w2 - w1 )*disi

          lgradu = uxavg*b(2,1) + uyavg*b(2,2) + uzavg*b(2,3)
          lgradv = vxavg*b(2,1) + vyavg*b(2,2) + vzavg*b(2,3)
          lgradw = wxavg*b(2,1) + wyavg*b(2,2) + wzavg*b(2,3)

          mgradu = uxavg*b(3,1) + uyavg*b(3,2) + uzavg*b(3,3)
          mgradv = vxavg*b(3,1) + vyavg*b(3,2) + vzavg*b(3,3)
          mgradw = wxavg*b(3,1) + wyavg*b(3,2) + wzavg*b(3,3)

!         resolve gradient contributions from edge and dual face

          ux = t(1,1)*egradu + t(1,2)*lgradu + t(1,3)*mgradu
          uy = t(2,1)*egradu + t(2,2)*lgradu + t(2,3)*mgradu
          uz = t(3,1)*egradu + t(3,2)*lgradu + t(3,3)*mgradu

          vx = t(1,1)*egradv + t(1,2)*lgradv + t(1,3)*mgradv
          vy = t(2,1)*egradv + t(2,2)*lgradv + t(2,3)*mgradv
          vz = t(3,1)*egradv + t(3,2)*lgradv + t(3,3)*mgradv

          wx = t(1,1)*egradw + t(1,2)*lgradw + t(1,3)*mgradw
          wy = t(2,1)*egradw + t(2,2)*lgradw + t(2,3)*mgradw
          wz = t(3,1)*egradw + t(3,2)*lgradw + t(3,3)*mgradw

        else include_edge_terms

!         just use Green-Gauss cell-average gradients (this
!         is what the baseline code does for tets)

          ux = uxavg
          uy = uyavg
          uz = uzavg

          vx = vxavg
          vy = vyavg
          vz = vzavg

          wx = wxavg
          wy = wyavg
          wz = wzavg

        end if include_edge_terms

!       Laplacian-type terms

        uf = mu*( rax*ux + ray*uy + raz*uz )
        vf = mu*( rax*vx + ray*vy + raz*vz )
        wf = mu*( rax*wx + ray*wy + raz*wz )

        xlambda = rax*lambda
        ylambda = ray*lambda
        zlambda = raz*lambda

        xmu = rax*mu
        ymu = ray*mu
        zmu = raz*mu

!       Lambda-type terms

        theta = ux + vy + wz
        uf    = uf + xlambda*theta
        vf    = vf + ylambda*theta
        wf    = wf + zlambda*theta

!       Mu-type terms

        uf = uf + ( xmu*ux + ymu*vx + zmu*wx )*tinclude
        vf = vf + ( xmu*uy + ymu*vy + zmu*wy )*tinclude
        wf = wf + ( xmu*uz + ymu*vz + zmu*wz )*tinclude

! Finally, add the contribution of this piece of the dual face to the residual

        if ( n1 <= nnodes0 ) then
          res(1,n1) = res(1,n1) + uf
          res(2,n1) = res(2,n1) + vf
          res(3,n1) = res(3,n1) + wf
        end if

        if ( n2 <= nnodes0 ) then
          res(1,n2) = res(1,n2) - uf
          res(2,n2) = res(2,n2) - vf
          res(3,n2) = res(3,n2) - wf
        end if

      end do edge_loop

    end do cell_loop

!   Check for possible problems with slen encountered earlier

    if((elasticity == 1) .or. (elasticity == 3)) then

      call lmpi_reduce(slen_ierr,ierr)
      call lmpi_bcast(ierr)

      if(ierr > 0) then
        if(lmpi_master) then
          write(*,*) ' Stopping in elasticity_rhs...'
          write(*,*) ' No stored values of slen available'
          write(*,*) ' Occurences = ',ierr
        end if
        call lmpi_die
      endif
    endif

  end subroutine elasticity_rhs

!=========================== ELASTICITY_LHS ==================================80
!
! This routine computes the elasticity jacobians for general (mixed) elements
!
!=============================================================================80

  subroutine elasticity_lhs(nnodes0, nnodes01, ncell,                          &
                            nedgeloc, nedgeloc_2d, nnz0,                       &
                            c2n, c2e, x, y, z,                                 &
                            a_diag, a_off, local_f2n, local_e2n,               &
                            local_f2e, e2n_2d, face_per_cell, node_per_cell,   &
                            edge_per_cell,                                     &
                            nnz01, ia, ja, vol, slen, type_cell, face_2d,      &
                            nzg2m, g2m)

    use info_depr,        only : twod, use_edge_gradients
    use debug_defs,       only : gradient_construction_lhs
    use moves,            only : elasticity
    use element_defs,     only : max_node_per_cell, max_face_per_cell,         &
                                 max_edge_per_cell
    use utilities,        only : tangents, tinverse, cell_jacobians
    use lmpi,             only : lmpi_conditional_stop, lmpi_bcast

    character(len=3),                            intent(in)    :: type_cell
    integer,                                     intent(in)    :: nnodes0
    integer,                                     intent(in)    :: nnodes01
    integer,                                     intent(in)    :: ncell
    integer,                                     intent(in)    :: nedgeloc
    integer,                                     intent(in)    :: nedgeloc_2d
    integer,                                     intent(in)    :: nnz0, nnz01
    integer,                                     intent(in)    :: face_per_cell
    integer,                                     intent(in)    :: node_per_cell
    integer,                                     intent(in)    :: edge_per_cell
    integer,                                     intent(in)    :: face_2d
    integer,  dimension(node_per_cell,ncell),    intent(in)    :: c2n
    integer,  dimension(edge_per_cell,ncell),    intent(in)    :: c2e
    integer,  dimension(face_per_cell,4),        intent(in)    :: local_f2n
    integer,  dimension(edge_per_cell,6),        intent(in)    :: local_e2n
    integer,  dimension(face_per_cell,4),        intent(in)    :: local_f2e
    integer,  dimension(4,2),                    intent(in)    :: e2n_2d
    integer,  dimension(nnodes01+1),             intent(in)    :: ia
    integer,  dimension(nnz01),                  intent(in)    :: ja
    integer,  dimension(:),                      intent(in)    :: nzg2m, g2m
    real(dp), dimension(nnodes01),               intent(in)    :: vol
    real(dp), dimension(nnodes01),               intent(in)    :: x,y,z
    real(dp), dimension(nnodes01),               intent(in)    :: slen
    real(dp), dimension(3,3,nnodes0),            intent(inout) :: a_diag
    real(odp), dimension(3,3,nnz0),              intent(inout) :: a_off

    integer :: n, nodec, k, ierr, row
    integer :: ie, i, ii, ie_local, i_local
    integer :: nodes_local, edges_local
    integer :: ioff, nedge_jac_eval
    integer :: n1_loc, n2_loc, edge, node
    integer :: n1, n2, n3, n4, n5, n6
    integer :: size_slen, slen_weight, slen_ierr

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp)    :: disi
    real(dp)    :: ex, ey, ez, factor
    real(dp)    :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp)    :: xc, yc, zc, cell_vol, fact
    real(dp)    :: rax, ray, raz
    real(dp)    :: xnf, ynf, znf, rai
    real(dp)    :: lx, ly, lz
    real(dp)    :: dlgraddu, dmgraddu
    real(dp)    :: dlgraddv, dmgraddv
    real(dp)    :: dlgraddw, dmgraddw
    real(dp)    :: mx, my, mz, normal_voli

    real(dp), dimension(3,3)    :: b, t

    real(dp), dimension(max_node_per_cell)    :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell)    :: dgradx_celldq, dgrady_celldq
    real(dp), dimension(max_node_per_cell)    :: dgradz_celldq

    real(dp)                                  :: dgradu_xidu
    real(dp), dimension(max_node_per_cell)    :: duxavgdu
    real(dp), dimension(max_node_per_cell)    :: duyavgdu
    real(dp), dimension(max_node_per_cell)    :: duzavgdu

    real(dp), dimension(max_node_per_cell)    :: duxdu
    real(dp), dimension(max_node_per_cell)    :: duydu
    real(dp), dimension(max_node_per_cell)    :: duzdu

    real(dp)                                  :: dgradv_xidv
    real(dp), dimension(max_node_per_cell)    :: dvxavgdv
    real(dp), dimension(max_node_per_cell)    :: dvyavgdv
    real(dp), dimension(max_node_per_cell)    :: dvzavgdv

    real(dp), dimension(max_node_per_cell)    :: dvxdv
    real(dp), dimension(max_node_per_cell)    :: dvydv
    real(dp), dimension(max_node_per_cell)    :: dvzdv

    real(dp)                                  :: dgradw_xidw
    real(dp), dimension(max_node_per_cell)    :: dwxavgdw
    real(dp), dimension(max_node_per_cell)    :: dwyavgdw
    real(dp), dimension(max_node_per_cell)    :: dwzavgdw

    real(dp), dimension(max_node_per_cell)    :: dwxdw
    real(dp), dimension(max_node_per_cell)    :: dwydw
    real(dp), dimension(max_node_per_cell)    :: dwzdw

    real(dp), dimension(max_node_per_cell)    :: dufdu
    real(dp), dimension(max_node_per_cell)    :: dufdv
    real(dp), dimension(max_node_per_cell)    :: dufdw

    real(dp), dimension(max_node_per_cell)    :: dvfdu
    real(dp), dimension(max_node_per_cell)    :: dvfdv
    real(dp), dimension(max_node_per_cell)    :: dvfdw

    real(dp), dimension(max_node_per_cell)    :: dwfdu
    real(dp), dimension(max_node_per_cell)    :: dwfdv
    real(dp), dimension(max_node_per_cell)    :: dwfdw

    real(dp), dimension(max_face_per_cell)    :: nx, ny, nz

    real(dp)    :: mu, lambda, tinclude
    real(dp)    :: modulus_of_elasticity, slen_average
    real(dp)    :: xmu, xlambda
    real(dp)    :: ymu, ylambda
    real(dp)    :: zmu, zlambda

    logical     :: edge_gradients
    logical     :: slen_needed

    logical :: info_written = .false.

    real(dp), parameter    :: my_half = 0.50_dp
    real(dp), parameter    :: my_3rd  = 1.0_dp/3.0_dp
    real(dp), parameter    :: my_4th  = 0.25_dp

  continue

    slen_needed = .false.
    if ((elasticity == 1) .or. (elasticity == 3) .or. (elasticity == 5)) then
      slen_needed = .true.
    end if

!   Determine size of slen array

    size_slen = size(slen,1)
    slen_ierr = 0

    ierr = 0

    tinclude = my_1

!   write information to file (only once)

    if(lmpi_master .and. .not.null_boundary_move &
                   .and. .not.info_written) then
      modulus_of_elasticity = elasticity_modulus(my_1, my_1, write_info=.true.)
      info_written = .true.
    endif

!   Initialize edge_gradients as default value

    edge_gradients = use_edge_gradients

! For tets in 3D or prisms in 2D, edge gradients add no new info
! so there is no need to do the extra work

    if (type_cell == 'tet') edge_gradients = .false.
    if (twod .and. type_cell == 'prz') edge_gradients = .false.

    if(skeleton > 0) then
      edge_terms_print : if(edge_gradients .and. &
                            gradient_construction_lhs == 0 ) then

        write(*,*) ' Original formulation of edge terms included to &
                   &increase h-ellipticity.'

      elseif(edge_gradients .and. &
             gradient_construction_lhs == 1 ) then edge_terms_print

        write(*,*) ' Second formulation of edge terms included to &
                   &increase h-ellipticity.'

      elseif(use_edge_gradients) then edge_terms_print

        write(*,*) ' No edge terms needed to increase h-ellipticity&
                   & for this element type.'

      else edge_terms_print

        write(*,*) ' No edge terms included to increase h-ellipticity.'

      endif edge_terms_print
    endif


! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

    edge_map(:) = 0
    node_map(:) = 0

    if (twod) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

      nedge_jac_eval = nedgeloc_2d

    else

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

      nedge_jac_eval = nedgeloc

    end if

! Loop over the cells

    cell_loop: do n = 1, ncell

! Initialization

      cell_vol = my_0

      xc = my_0
      yc = my_0
      zc = my_0

      slen_average = my_0

      x_node(:) = my_0
      y_node(:) = my_0
      z_node(:) = my_0

      duxdu(:) = my_0
      duydu(:) = my_0
      duzdu(:) = my_0

      dvxdv(:) = my_0
      dvydv(:) = my_0
      dvzdv(:) = my_0

      dwxdw(:) = my_0
      dwydw(:) = my_0
      dwzdw(:) = my_0

! Compute cell averages, cell center, and set up some local solution arrays

      node_loop_1 : do i_local = 1, nodes_local

!       local node number

        i = node_map(i_local)

!       global node number

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

      end do node_loop_1

!     compute the cell center (must loop over node_per_cell even in 2D)

      slen_weight = 0

      do i = 1, node_per_cell

!       global node number

        node = c2n(i,n)

        xc  =  xc + x(node)
        yc  =  yc + y(node)
        zc  =  zc + z(node)

        if ((node <= size_slen) .and. slen_needed) then
          slen_average = slen_average + slen(node)
          slen_weight  = slen_weight + 1
        end if

      end do

      fact = 1._dp / real(node_per_cell, dp)

      xc  =  xc*fact
      yc  =  yc*fact
      zc  =  zc*fact

      if (slen_weight > 0) then

        slen_average = slen_average/real(slen_weight, dp)
        slen_average = max(slen_average, epsilon(1.0_dp))

      else

!       we have no nodes in this cell that have slen...possible problem

        slen_ierr = slen_ierr + 1
        slen_average = 1._dp

      endif

! Get the jacobians of the gradients in the primal cell via Green-Gauss
! Note gradients initialized in routine

      call cell_jacobians(edges_local, max_node_per_cell, face_per_cell,       &
                          x_node, y_node, z_node, local_f2n, e2n_2d,           &
                          dgradx_celldq, dgrady_celldq, dgradz_celldq,         &
                          cell_vol, nx, ny, nz)

!     Store these average gradients (2D path same as 3D path)

      do i_local = 1, nodes_local

!       local node number

        i = node_map(i_local)

        duxavgdu(i) = dgradx_celldq(i)
        duyavgdu(i) = dgrady_celldq(i)
        duzavgdu(i) = dgradz_celldq(i)

        dvxavgdv(i) = dgradx_celldq(i)
        dvyavgdv(i) = dgrady_celldq(i)
        dvzavgdv(i) = dgradz_celldq(i)

        dwxavgdw(i) = dgradx_celldq(i)
        dwyavgdw(i) = dgrady_celldq(i)
        dwzavgdw(i) = dgradz_celldq(i)

      end do

      modulus_of_elasticity = elasticity_modulus(cell_vol, slen_average)

      if(elasticity == 4) then

        mu       = my_1
        lambda   = my_0
        tinclude = my_0

      else

        mu     = modulus_of_elasticity / 2.0_dp / (my_1 + poisson)
        lambda = poisson * modulus_of_elasticity / (my_1 + poisson)       &
                                             / (my_1 - 2.0_dp*poisson)

      endif

! Next loop over the edges in the cell and get each one's
! contribution to the jacobian

      edge_loop : do ie_local = 1,edges_local

!       local edge number

        ie = edge_map(ie_local)

!       global edge number

        edge = c2e(ie,n)

!       check edge to make sure it is not off-processor - if it is
!       we don't need its contribution anyway

        if (edge > nedge_jac_eval) cycle edge_loop

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)

!       global node numbers of edge endpoints

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

! Get this edges' contributiuon to the dual normal and area

!       edge midpoint

        xm = (x(n1) + x(n2))*my_half
        ym = (y(n1) + y(n2))*my_half
        zm = (z(n1) + z(n2))*my_half

!       compute left face centroid

        n3 = c2n(local_e2n(ie,3),n)

        if (local_e2n(ie,4) /= 0) then

!         quad cell face

          n4 = c2n(local_e2n(ie,4),n)

          xl = (x(n1) + x(n2) + x(n3) + x(n4))*my_4th
          yl = (y(n1) + y(n2) + y(n3) + y(n4))*my_4th
          zl = (z(n1) + z(n2) + z(n3) + z(n4))*my_4th

        else

!         tria cell face

          xl = (x(n1) + x(n2) + x(n3))*my_3rd
          yl = (y(n1) + y(n2) + y(n3))*my_3rd
          zl = (z(n1) + z(n2) + z(n3))*my_3rd

        end if

!       compute right face centroid

        n5 = c2n(local_e2n(ie,5),n)

        if (local_e2n(ie,6) /= 0) then

!         quad cell face

          n6 = c2n(local_e2n(ie,6),n)

          xr = (x(n1) + x(n2) + x(n5) + x(n6))*my_4th
          yr = (y(n1) + y(n2) + y(n5) + y(n6))*my_4th
          zr = (z(n1) + z(n2) + z(n5) + z(n6))*my_4th

        else

!         tria cell face

          xr = (x(n1) + x(n2) + x(n5))*my_3rd
          yr = (y(n1) + y(n2) + y(n5))*my_3rd
          zr = (z(n1) + z(n2) + z(n5))*my_3rd

        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        rax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        ray = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        raz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

! Get gradients at the dual face; either take gradients for
! this piece of the dual face to be the same as the cell-average gradient
! computed above  (which is what the legacy FUN3D solver does for tets),
! or combine with the edge-gradient to increase h-ellipticity.

!       initialize the gradient contributions (recall we stored off
!       the green-gauss average gradient contributions above)

        duxdu(:) = my_0
        duydu(:) = my_0
        duzdu(:) = my_0

        dvxdv(:) = my_0
        dvydv(:) = my_0
        dvzdv(:) = my_0

        dwxdw(:) = my_0
        dwydw(:) = my_0
        dwzdw(:) = my_0

        include_edge_terms : if(edge_gradients .and. &
                                gradient_construction_lhs == 0 ) then

!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

!         first get the avg_term pieces; all active nodes in the cell contribute

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           u-gradient avg terms

            dgradu_xidu = duxavgdu(i)*ex + duyavgdu(i)*ey +  duzavgdu(i)*ez

            duxdu(i) = duxavgdu(i) - dgradu_xidu*ex
            duydu(i) = duyavgdu(i) - dgradu_xidu*ey
            duzdu(i) = duzavgdu(i) - dgradu_xidu*ez

!           v-gradient avg terms

            dgradv_xidv = dvxavgdv(i)*ex + dvyavgdv(i)*ey +  dvzavgdv(i)*ez

            dvxdv(i) = dvxavgdv(i) - dgradv_xidv*ex
            dvydv(i) = dvyavgdv(i) - dgradv_xidv*ey
            dvzdv(i) = dvzavgdv(i) - dgradv_xidv*ez

!           w-gradient avg terms
            dgradw_xidw = dwxavgdw(i)*ex + dwyavgdw(i)*ey +  dwzavgdw(i)*ez

            dwxdw(i) = dwxavgdw(i) - dgradw_xidw*ex
            dwydw(i) = dwyavgdw(i) - dgradw_xidw*ey
            dwzdw(i) = dwzavgdw(i) - dgradw_xidw*ez

          end do

!         next get the edge_term pieces; only the two edge nodes contribute

!         u-gradient edge terms

          duxdu(n1_loc) = duxdu(n1_loc) - disi*ex
          duydu(n1_loc) = duydu(n1_loc) - disi*ey
          duzdu(n1_loc) = duzdu(n1_loc) - disi*ez

          duxdu(n2_loc) = duxdu(n2_loc) + disi*ex
          duydu(n2_loc) = duydu(n2_loc) + disi*ey
          duzdu(n2_loc) = duzdu(n2_loc) + disi*ez

!         v-gradient edge terms

          dvxdv(n1_loc) = dvxdv(n1_loc) - disi*ex
          dvydv(n1_loc) = dvydv(n1_loc) - disi*ey
          dvzdv(n1_loc) = dvzdv(n1_loc) - disi*ez

          dvxdv(n2_loc) = dvxdv(n2_loc) + disi*ex
          dvydv(n2_loc) = dvydv(n2_loc) + disi*ey
          dvzdv(n2_loc) = dvzdv(n2_loc) + disi*ez

!         w-gradient edge terms

          dwxdw(n1_loc) = dwxdw(n1_loc) - disi*ex
          dwydw(n1_loc) = dwydw(n1_loc) - disi*ey
          dwzdw(n1_loc) = dwzdw(n1_loc) - disi*ez

          dwxdw(n2_loc) = dwxdw(n2_loc) + disi*ex
          dwydw(n2_loc) = dwydw(n2_loc) + disi*ey
          dwzdw(n2_loc) = dwzdw(n2_loc) + disi*ez

        elseif(edge_gradients .and. &
               gradient_construction_lhs == 1 ) then include_edge_terms

!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          !...find tangent vectors in the dual face
          rai = my_1/sqrt( rax**2 + ray**2 + raz**2 )
          xnf = rax*rai
          ynf = ray*rai
          znf = raz*rai
          call tangents(xnf, ynf, znf, lx, ly, lz, mx, my, mz)

          !...form transformation matrix
          !...edge direction
          b(1,1) = ex
          b(1,2) = ey
          b(1,3) = ez

          !...l direction
          b(2,1) = lx
          b(2,2) = ly
          b(2,3) = lz

          !...m direction
          b(3,1) = mx
          b(3,2) = my
          b(3,3) = mz

          call tinverse( b , t )

!         directional gradients

!         resolve gradient contributions from edge and dual face

!         first get the avg_term pieces; all active nodes in the cell contribute

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           u-gradient avg terms

            dlgraddu = duxavgdu(i)*b(2,1) + &
                       duyavgdu(i)*b(2,2) + &
                       duzavgdu(i)*b(2,3)
            dmgraddu = duxavgdu(i)*b(3,1) + &
                       duyavgdu(i)*b(3,2) + &
                       duzavgdu(i)*b(3,3)

            duxdu(i) = t(1,2)*dlgraddu + t(1,3)*dmgraddu
            duydu(i) = t(2,2)*dlgraddu + t(2,3)*dmgraddu
            duzdu(i) = t(3,2)*dlgraddu + t(3,3)*dmgraddu

!           v-gradient avg terms

            dlgraddv = dvxavgdv(i)*b(2,1) + &
                       dvyavgdv(i)*b(2,2) + &
                       dvzavgdv(i)*b(2,3)
            dmgraddv = dvxavgdv(i)*b(3,1) + &
                       dvyavgdv(i)*b(3,2) + &
                       dvzavgdv(i)*b(3,3)

            dvxdv(i) = t(1,2)*dlgraddv + t(1,3)*dmgraddv
            dvydv(i) = t(2,2)*dlgraddv + t(2,3)*dmgraddv
            dvzdv(i) = t(3,2)*dlgraddv + t(3,3)*dmgraddv

!           w-gradient avg terms

            dlgraddw = dwxavgdw(i)*b(2,1) + &
                       dwyavgdw(i)*b(2,2) + &
                       dwzavgdw(i)*b(2,3)
            dmgraddw = dwxavgdw(i)*b(3,1) + &
                       dwyavgdw(i)*b(3,2) + &
                       dwzavgdw(i)*b(3,3)

            dwxdw(i) = t(1,2)*dlgraddw + t(1,3)*dmgraddw
            dwydw(i) = t(2,2)*dlgraddw + t(2,3)*dmgraddw
            dwzdw(i) = t(3,2)*dlgraddw + t(3,3)*dmgraddw

          end do

!         next get the edge_term pieces; only the two edge nodes contribute

!         u-gradient edge terms

          duxdu(n1_loc) = duxdu(n1_loc) - t(1,1)*disi
          duydu(n1_loc) = duydu(n1_loc) - t(2,1)*disi
          duzdu(n1_loc) = duzdu(n1_loc) - t(3,1)*disi

          duxdu(n2_loc) = duxdu(n2_loc) + t(1,1)*disi
          duydu(n2_loc) = duydu(n2_loc) + t(2,1)*disi
          duzdu(n2_loc) = duzdu(n2_loc) + t(3,1)*disi

!         v-gradient edge terms

          dvxdv(n1_loc) = dvxdv(n1_loc) - t(1,1)*disi
          dvydv(n1_loc) = dvydv(n1_loc) - t(2,1)*disi
          dvzdv(n1_loc) = dvzdv(n1_loc) - t(3,1)*disi

          dvxdv(n2_loc) = dvxdv(n2_loc) + t(1,1)*disi
          dvydv(n2_loc) = dvydv(n2_loc) + t(2,1)*disi
          dvzdv(n2_loc) = dvzdv(n2_loc) + t(3,1)*disi

!         w-gradient edge terms

          dwxdw(n1_loc) = dwxdw(n1_loc) - t(1,1)*disi
          dwydw(n1_loc) = dwydw(n1_loc) - t(2,1)*disi
          dwzdw(n1_loc) = dwzdw(n1_loc) - t(3,1)*disi

          dwxdw(n2_loc) = dwxdw(n2_loc) + t(1,1)*disi
          dwydw(n2_loc) = dwydw(n2_loc) + t(2,1)*disi
          dwzdw(n2_loc) = dwzdw(n2_loc) + t(3,1)*disi

        else include_edge_terms

!         only have the unaltered, average green-gauss contributions;
!         all active nodes in the cell contribute

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           u-gradient avg terms

            duxdu(i) = duxavgdu(i)
            duydu(i) = duyavgdu(i)
            duzdu(i) = duzavgdu(i)

            dvxdv(i) = dvxavgdv(i)
            dvydv(i) = dvyavgdv(i)
            dvzdv(i) = dvzavgdv(i)

            dwxdw(i) = dwxavgdw(i)
            dwydw(i) = dwyavgdw(i)
            dwzdw(i) = dwzavgdw(i)

          end do

        end if include_edge_terms

!       form some more intermediate Jacobians at all nodes

        do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

!         Laplacian-type terms

!         uf = mu*( rax*ux + ray*uy + raz*uz )
!         vf = mu*( rax*vx + ray*vy + raz*vz )
!         wf = mu*( rax*wx + ray*wy + raz*wz )

          dufdu(i) = mu*( rax*duxdu(i) + ray*duydu(i) + raz*duzdu(i) )
          dvfdv(i) = mu*( rax*dvxdv(i) + ray*dvydv(i) + raz*dvzdv(i) )
          dwfdw(i) = mu*( rax*dwxdw(i) + ray*dwydw(i) + raz*dwzdw(i) )

          xlambda = rax*lambda
          ylambda = ray*lambda
          zlambda = raz*lambda

          xmu = rax*mu
          ymu = ray*mu
          zmu = raz*mu

!         Lambda-type terms

!         theta = ux + vy + wz
!         uf    = uf + xlambda*theta
!         vf    = vf + ylambda*theta
!         wf    = wf + zlambda*theta


          dufdu(i) = dufdu(i) + xlambda*duxdu(i)
          dufdv(i) =            xlambda*dvydv(i)
          dufdw(i) =            xlambda*dwzdw(i)

          dvfdu(i) =            ylambda*duxdu(i)
          dvfdv(i) = dvfdv(i) + ylambda*dvydv(i)
          dvfdw(i) =            ylambda*dwzdw(i)

          dwfdu(i) =            zlambda*duxdu(i)
          dwfdv(i) =            zlambda*dvydv(i)
          dwfdw(i) = dwfdw(i) + zlambda*dwzdw(i)

!         Mu-type terms

!         uf = uf + ( xmu*ux + ymu*vx + zmu*wx )*tinclude
!         vf = vf + ( xmu*uy + ymu*vy + zmu*wy )*tinclude
!         wf = wf + ( xmu*uz + ymu*vz + zmu*wz )*tinclude

          dufdu(i) = dufdu(i) + xmu*duxdu(i)*tinclude
          dufdv(i) = dufdv(i) + ymu*dvxdv(i)*tinclude
          dufdw(i) = dufdw(i) + zmu*dwxdw(i)*tinclude

          dvfdu(i) = dvfdu(i) + xmu*duydu(i)*tinclude
          dvfdv(i) = dvfdv(i) + ymu*dvydv(i)*tinclude
          dvfdw(i) = dvfdw(i) + zmu*dwydw(i)*tinclude

          dwfdu(i) = dwfdu(i) + xmu*duzdu(i)*tinclude
          dwfdv(i) = dwfdv(i) + ymu*dvzdv(i)*tinclude
          dwfdw(i) = dwfdw(i) + zmu*dwzdw(i)*tinclude

        end do

! Assemble final Jacobian matrices into sparse matrix form

        factor = -my_1

        edge_node_loop : do ii = 1,2

!         diagonal contributions

!         local (i) and global (node) numbers

          if (ii == 1) then
            i = n1_loc
            node = c2n(n1_loc,n)
          else
            i = n2_loc
            node = c2n(n2_loc,n)
          end if

          factor = -factor

          !...divide by volume
          if(vol_divide) then
            normal_voli   = factor/vol(node)
          else
            normal_voli   = factor
          endif

          if ( node <= nnodes0 ) then

            row = g2m(node)
            a_diag(1,1,row) = a_diag(1,1,row) + normal_voli*dufdu(i)
            a_diag(1,2,row) = a_diag(1,2,row) + normal_voli*dufdv(i)
            a_diag(1,3,row) = a_diag(1,3,row) + normal_voli*dufdw(i)

            a_diag(2,1,row) = a_diag(2,1,row) + normal_voli*dvfdu(i)
            a_diag(2,2,row) = a_diag(2,2,row) + normal_voli*dvfdv(i)
            a_diag(2,3,row) = a_diag(2,3,row) + normal_voli*dvfdw(i)

            a_diag(3,1,row) = a_diag(3,1,row) + normal_voli*dwfdu(i)
            a_diag(3,2,row) = a_diag(3,2,row) + normal_voli*dwfdv(i)
            a_diag(3,3,row) = a_diag(3,3,row) + normal_voli*dwfdw(i)

          end if

!         off-diagonal contributions

          node_loop_3 : do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           global node number

            nodec = c2n(i,n)

            if (nodec == node) cycle node_loop_3

            if ( node <= nnodes0 ) then

!             Determine location of nonzero contribution in comp row storage

              ioff   = 0
              do k = ia(node), ia(node+1) - 1
                if (ja(k) == nodec) then
                  ioff = nzg2m(k)
                endif
              end do

              if (ioff <= 0 .or. ioff > nnz0) then

                ierr       = 1
                write(*,*) ' Error assembling LHS for elasticity equations'
                write(*,*) ' Node=',node,'  proc=',lmpi_id
                write(*,*) ' No place for off-diagonal node',nodec
                write(*,*) ' nnodes0,nnodes01=',nnodes0,nnodes01
                write(*,*) ' nnz0,nnz01=',nnz0,nnz01
                write(*,*) ' ia(node),ia(node+1),ioff=',ia(node),ia(node+1),ioff
                do k = ia(node), ia(node+1) - 1
                  write(*,*) '  k,ja(k)=',k,ja(k)
                end do
                write(*,*) ' edge,nedge_jac_eval = ',edge,nedge_jac_eval

              else

                a_off(1,1,ioff) = a_off(1,1,ioff) + normal_voli*dufdu(i)
                a_off(1,2,ioff) = a_off(1,2,ioff) + normal_voli*dufdv(i)
                a_off(1,3,ioff) = a_off(1,3,ioff) + normal_voli*dufdw(i)

                a_off(2,1,ioff) = a_off(2,1,ioff) + normal_voli*dvfdu(i)
                a_off(2,2,ioff) = a_off(2,2,ioff) + normal_voli*dvfdv(i)
                a_off(2,3,ioff) = a_off(2,3,ioff) + normal_voli*dvfdw(i)

                a_off(3,1,ioff) = a_off(3,1,ioff) + normal_voli*dwfdu(i)
                a_off(3,2,ioff) = a_off(3,2,ioff) + normal_voli*dwfdv(i)
                a_off(3,3,ioff) = a_off(3,3,ioff) + normal_voli*dwfdw(i)

              endif

            endif

          enddo node_loop_3

        enddo edge_node_loop

      enddo edge_loop

    enddo cell_loop

!   check for problems in allocation of variables

    call lmpi_conditional_stop(ierr)

!   Check for possible problems with slen encountered earlier

    if((elasticity == 1) .or. (elasticity == 3) .or. (elasticity == 5)) then

      call lmpi_reduce(slen_ierr,ierr)
      call lmpi_bcast(ierr)

      if(ierr > 0) then
        if(lmpi_master) then
          write(*,*) ' Stopping in elasticity_lhs...'
          write(*,*) ' No stored values of slen available'
          write(*,*) ' Occurences = ',ierr
        end if
        call lmpi_die
      endif
    endif

  end subroutine elasticity_lhs

!================================ MATRIX_LHSP ================================80
!
! This routine checks positivity for a general block matrix
!
!=============================================================================80

  subroutine matrix_lhsp(pmaxsum, pmax, xmax,ymax,zmax,                        &
                         n_profile, p_profile, m_profile,                      &
                         x_profile, y_profile, z_profile,                      &
                         nnodes0, nnodes01, nnz0,                              &
                         x, y, z,                                              &
                         a_diag, a_off, iam, njac, g2m )

    integer,                                       intent(in) :: njac
    integer,                                       intent(in) :: nnodes0
    integer,                                       intent(in) :: nnodes01
    integer,                                       intent(in) :: nnz0
    integer,     dimension(nnodes01+1),            intent(in) :: iam
    integer,     dimension(:),                     intent(in) :: g2m
    integer,                                       intent(in) :: n_profile

    real(dp), dimension(njac),                     intent(out) :: pmax
    real(dp), dimension(njac),                     intent(out) :: pmaxsum
    real(dp), dimension(njac),                     intent(out) :: xmax
    real(dp), dimension(njac),                     intent(out) :: ymax
    real(dp), dimension(njac),                     intent(out) :: zmax

    real(dp), dimension(nnodes01),                 intent(in)  :: x, y, z
    real(dp), dimension(njac,njac,nnodes0),        intent(in)  :: a_diag
    real(odp),dimension(njac,njac,nnz0),        intent(in)  :: a_off

    real(dp), dimension(n_profile,njac),           intent(out) :: p_profile
    real(dp), dimension(n_profile,njac),           intent(out) :: m_profile
    real(dp), dimension(n_profile,njac),           intent(out) :: x_profile
    real(dp), dimension(n_profile,njac),           intent(out) :: y_profile
    real(dp), dimension(n_profile,njac),           intent(out) :: z_profile

    integer :: node, i, eq, row
    integer :: connect_node

    real(dp)    :: diag, off, offmax, offsum
    real(dp)    :: ptrial

    logical :: profile_set

    continue

    p_profile(1:n_profile,1:njac) =  my_0
    m_profile(1:n_profile,1:njac) = -huge(1.0_dp)
    x_profile(1:n_profile,1:njac) =  my_0
    y_profile(1:n_profile,1:njac) =  my_0
    z_profile(1:n_profile,1:njac) =  my_0

    eq_loop : do eq = 1, njac

!     Loop over all the nodes within this partition

      pmax(eq)    = -huge(1.0_dp)
      xmax(eq)    = -huge(1.0_dp)
      ymax(eq)    = -huge(1.0_dp)
      zmax(eq)    = -huge(1.0_dp)
      pmaxsum(eq) = -huge(1.0_dp)
      node_loop : do node = 1,nnodes0

        if(node > nnodes0) cycle node_loop

        offsum  = my_0
        offmax  = my_0
        row = g2m(node)
        diag    = a_diag(eq,eq,row)
        do connect_node = iam(row),iam(row+1)-1
          off = a_off(eq,eq,connect_node)
          if(sign( my_1 , off )*sign( my_1 , diag ) > my_0) then
            offsum = offsum + abs( off )
            offmax = max( offmax , abs( off ) )
          endif
        enddo

!       Track maximum off diagonal terms (with location) for each equation

        ptrial = abs( offmax / diag )
        if(ptrial > pmax(eq)) then
          pmax(eq) = ptrial
          xmax(eq) = x(node)
          ymax(eq) = y(node)
          zmax(eq) = z(node)
        endif

!       Track maximum off diagonal terms within brackets

        profile_set = .false.
        do i=n_profile,1,-1
          if(ptrial >= real(2*(i-1),dp)/real(n_profile-1,dp)) then
            p_profile(i,eq) = p_profile(i,eq) + my_1
            profile_set = .true.
            if(ptrial > m_profile(i,eq)) then
              m_profile(i,eq) = ptrial
              x_profile(i,eq) = x(node)
              y_profile(i,eq) = y(node)
              z_profile(i,eq) = z(node)
            endif
          endif
          if(profile_set) exit
        enddo

!       Track maximum off diagonal sum terms for each equation

        ptrial = abs( offsum / diag )
        if(ptrial > pmaxsum(eq)) then
          pmaxsum(eq) = ptrial
        endif

      end do node_loop

    end do eq_loop

  end subroutine matrix_lhsp


!================================ MATRIX_LHSC ================================80
!
! This routine prints out info related to jacobians for general block matrix
!
!=============================================================================80

  subroutine matrix_lhsc(xp, yp, zp,                                           &
                         nnodes0, nnodes01, nnz0, vol,                         &
                         qnode, res, x, y, z,                                  &
                         a_diag, a_off, nnz01, iam, jam, n_tot, njac, g2m, m2g )

    use system_extensions, only : se_flush

    integer,                                   intent(in) :: nnodes0
    integer,                                   intent(in) :: n_tot
    integer,                                   intent(in) :: njac
    integer,                                   intent(in) :: nnodes01
    integer,                                   intent(in) :: nnz0,nnz01
    integer,     dimension(nnodes01+1),        intent(in) :: iam
    integer,     dimension(nnz01),             intent(in) :: jam
    integer,     dimension(:),                 intent(in) :: g2m, m2g
    real(dp),                                  intent(in) :: xp, yp, zp
    real(dp), dimension(nnodes01),             intent(in) :: vol
    real(dp), dimension(n_tot,nnodes01),       intent(in) :: qnode
    real(dp), dimension(njac,nnodes01),        intent(in) :: res
    real(dp), dimension(nnodes01),             intent(in) :: x, y, z
    real(dp), dimension(njac,njac,nnodes0),    intent(in) :: a_diag
    real(odp),dimension(njac,njac,nnz0),    intent(in) :: a_off

    integer :: node, n_count, eq, eq2, row
    integer :: connect_node, nodec, pass, n_offp

    real(dp), dimension(3)    :: check
    real(dp)                  :: diagi

! beginNeverComplex
    real(dp)    :: xc, yc, zc, tol
! endNeverComplex

    continue

    tol = 1.0e-05_dp

!   Loop over all the nodes within this partition
    node_loop : do node = 1,nnodes0

      if(node > nnodes0) cycle node_loop

      xc = xp
      yc = yp
      zc = zp

      check(1) = abs( x(node)- xc )
      check(2) = abs( y(node)- yc )
      check(3) = abs( z(node)- zc )
      if(sum(check) > tol) cycle node_loop

      write(6,*)
      write(6,*) ' Elements of the linearization for point'
      write(6,*) ' ........x,y,z=',x(node),y(node),z(node)
      write(6,*) ' .........node=',node,'     processor=',lmpi_id
      write(6,*) ' .........njac=',njac
      write(6,*)

      write(6,*) ' ......volume=',vol(node)
      write(6,*) ' .......qnode=',(qnode(eq,node),eq=1,njac)
      write(6,*) ' ....residual=',(res(eq,node),eq=1,njac)

      n_offp = 0
      row = g2m(node)
      do connect_node = iam(row),iam(row+1)-1
        if(m2g(jam(connect_node)) > nnodes0) n_offp = n_offp + 1
      enddo
      write(6,*)
      write(6,"(3x,'Total number of off-diagonal contributions = ',i10)") &
                                                iam(row+1)-iam(row)

      write(6,"(3x,'     Number of off-processor contributions = ',i10)") &
                                               n_offp

      eq_loop : do eq=1,njac
        n_count = 0
        diagi = 1.0_dp/abs( a_diag(eq,eq,g2m(node)) )
        write(6,*)
        write(6,"(3x,'|diagonal|=',e20.10)") abs( a_diag(eq,eq,g2m(node)) )
        write(6,*)
        write(6,"(3x,'nodec',14x,'x',14x,'y',14x,'z',&
                &6x,'contribution/|diagonal|')")

        write(6,"(i8,3e15.4,2x,'d',e20.10)") node,x(node),y(node),z(node),  &
                  a_diag(eq,eq,g2m(node))*diagi
        write(6,*)

!       Make 4 passes : on-processor
!                     : off-processor

          pass_loop : do pass = 1,2
          row = g2m(node)
          ia_loop : do connect_node = iam(row),iam(row+1)-1

          nodec = m2g(jam(connect_node))

          if((pass == 1) .and. (nodec >= nnodes0)) cycle
          if((pass == 2) .and. (nodec <  nnodes0)) cycle

          if((abs( y(nodec)-yc ) <= tol) .and. (x(nodec) < x(node)) ) then

            do eq2=1,eq-1
              write(6,"(8x,45x,i3,e20.10)")                                &
                       eq2,a_off(eq,eq2,connect_node)*diagi
            enddo
            write(6,"(i8,3e15.4,i3,e20.10)")                               &
                    m2g(jam(connect_node)), x(nodec),y(nodec),z(nodec),    &
                    eq,a_off(eq,eq,connect_node)*diagi
            do eq2=eq+1,njac
              write(6,"(8x,45x,i3,e20.10)")                                &
                      eq2,a_off(eq,eq2,connect_node)*diagi
            enddo
            write(6,*)
            n_count = n_count + 1

          elseif((abs( y(nodec)-yc ) <= tol) .and.   &
           (abs( x(nodec) - x(node)) ) <= tol ) then

            do eq2=1,eq-1
              write(6,"(8x,45x,i3,e20.10)")                                &
                       eq2,a_off(eq,eq2,connect_node)*diagi
            enddo
            write(6,"(i8,3e15.4,i3,e20.10)")                               &
                    m2g(jam(connect_node)), x(nodec),y(nodec),z(nodec),    &
                    eq,a_off(eq,eq,connect_node)*diagi
            do eq2=eq+1,njac
              write(6,"(8x,45x,i3,e20.10)")                                &
                      eq2,a_off(eq,eq2,connect_node)*diagi
            enddo
            write(6,*)
            n_count = n_count + 1

          elseif((abs( y(nodec)-yc ) <= tol) .and. &
            (x(nodec) > x(node)) ) then

            do eq2=1,eq-1
              write(6,"(8x,45x,i3,e20.10)")                                &
                       eq2,a_off(eq,eq2,connect_node)*diagi
            enddo
            write(6,"(i8,3e15.4,i3,e20.10)")                               &
                    m2g(jam(connect_node)), x(nodec),y(nodec),z(nodec),    &
                    eq,a_off(eq,eq,connect_node)*diagi
            do eq2=eq+1,njac
              write(6,"(8x,45x,i3,e20.10)")                                &
                      eq2,a_off(eq,eq2,connect_node)*diagi
            enddo
            write(6,*)
            n_count = n_count + 1

          else

            do eq2=1,eq-1
              write(6,"(8x,45x,i3,e20.10)")                                &
                       eq2,a_off(eq,eq2,connect_node)*diagi
            enddo
            write(6,"(i8,3e15.4,i3,e20.10)")                               &
                    m2g(jam(connect_node)), x(nodec),y(nodec),z(nodec),    &
                    eq,a_off(eq,eq,connect_node)*diagi
            do eq2=eq+1,njac
              write(6,"(8x,45x,i3,e20.10)")                                &
                      eq2,a_off(eq,eq2,connect_node)*diagi
            enddo
            write(6,*)
            n_count = n_count + 1

          endif

          enddo ia_loop

          write(6,*)

        enddo pass_loop

        row = g2m(node)
        if(n_count /= iam(row+1)-iam(row)) then
          write(6,*) ' Caution...some nodes missed in printout above....'
        endif

      end do eq_loop

    end do node_loop

    call se_flush()

  end subroutine matrix_lhsc

!=============================== ELASTICITY_MODULUS ==========================80
!
! Sets the elasticity modulus as a function of distance from the surface or
! the cell volume
!
!=============================================================================80

  function elasticity_modulus(volume, distance, write_info)

    use moves, only : elasticity

    real(dp)             :: elasticity_modulus

    real(dp), intent(in) :: volume, distance

    logical,  intent(in), optional :: write_info

    logical :: writeinfo

  continue

    writeinfo = .false.
    if (present(write_info)) writeinfo = write_info

    select case (elasticity)
      case (1)
        elasticity_modulus = my_1 / distance
      case (2)
        elasticity_modulus = my_1 / (volume)
      case (3)
        elasticity_modulus = my_1 / (my_1 - exp(-distance))
      case (5)
        elasticity_modulus = my_1 / distance**2
      case default
        elasticity_modulus = my_1 / (volume)
    end select

    if (writeinfo) then

      if (elasticity == 1) then

        write(unit_info,*) ' Using modulus_of_elasticity = 1/slen'
        write(unit_info,*) ' Poisson ratio =',poisson

      elseif(elasticity == 2) then

        write(unit_info,*) ' Using modulus_of_elasticity = 1/vol'
        write(unit_info,*) ' Poisson ratio =',poisson

      elseif(elasticity == 3) then

        write(unit_info,*) ' Using modulus_of_elasticity = 1/[1-exp(-slen)]'
        write(unit_info,*) ' Poisson ratio =',poisson

      elseif(elasticity == 4) then

        write(unit_info,*)                                                     &
              ' CHECK CASE ONLY: mu = 1 lambda = 0 => Elliptic equation'

      elseif(elasticity == 5) then

        write(unit_info,*) ' Using modulus_of_elasticity = 1/slen**2'
        write(unit_info,*) ' Poisson ratio =',poisson

      endif

    end if

  end function elasticity_modulus

end module move_mixed

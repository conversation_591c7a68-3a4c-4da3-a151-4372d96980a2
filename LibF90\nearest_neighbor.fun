!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite nearest_neighbor

  integer    :: maxNumOfNodes
  type(Tree) :: aTree
  integer, parameter :: dp = selected_real_kind(15)

setup
  maxNumOfNodes = 8
  call initializeTree( aTree, maxNumOfNodes  )
end setup

teardown
  call destroyTree( aTree )
end teardown

test CorrectAllocation
  assert_equal( size( aTree%pair ), maxNumOfNodes-1 )
end test

test nodeSubtractionOperatorOverload
  type(Node) :: node1, node2

  node1%xCoordinate = 1.0_dp
  node2%xCoordinate = 0.0_dp

  assert_real_equal( 1.0_dp, node1 - node2 )
  assert_real_equal( 1.0_dp, node2 - node1 )

  node1%xCoordinate =  1.0_dp
  node2%xCoordinate = -1.0_dp

  assert_real_equal( 2.0_dp, node1 - node2 )
  assert_real_equal( 2.0_dp, node2 - node1 )

end test

test storageOfLargestChildDistance
  type(Pair) :: aPair

  assert_real_equal( -1.0_dp, aPair%rightMaxDistance )
  assert_real_equal( -1.0_dp, aPair%leftMaxDistance )

  aPair%rightMaxDistance = 1.0_dp
  aPair%leftMaxDistance  = 2.0_dp

  assert_real_equal( 1.0_dp, aPair%rightMaxDistance )
  assert_real_equal( 2.0_dp, aPair%leftMaxDistance )

end test

test nodeAddition
  integer                  :: i
  type(Node), dimension(8) :: aNode

  aNode%xCoordinate = (/ 0.1, 5.9, 1.0, -2.5, -5.0, 6.0, 6.2, 8.0 /)

  do i = 1, 8
    aNode(i)%id = i
    call addNodeTo( aTree, aNode(i) )
  end do

  assert_true( aTree%pair(1)%leftNode  == aNode(1) )
  assert_equal( aTree%pair(1)%leftNode%id, 1 )
  assert_true( aTree%pair(1)%rightNode == aNode(2) )
  assert_equal( aTree%pair(1)%rightNode%id, 2 )
  assert_true( aTree%pair(2)%leftNode  == aNode(3) )
  assert_equal( aTree%pair(2)%leftNode%id, 3 )
  assert_true( aTree%pair(2)%rightNode == aNode(4) )
  assert_equal( aTree%pair(2)%rightNode%id, 4 )
  assert_true( aTree%pair(3)%leftNode  == aNode(5) )
  assert_equal( aTree%pair(3)%leftNode%id, 5 )
  assert_true( aTree%pair(4)%leftNode  == aNode(6) )
  assert_equal( aTree%pair(4)%leftNode%id, 6 )
  assert_true( aTree%pair(4)%rightNode == aNode(7) )
  assert_equal( aTree%pair(4)%rightNode%id, 7 )
  assert_true( aTree%pair(5)%leftNode  == aNode(8) )
  assert_equal( aTree%pair(5)%leftNode%id, 8 )

end test

test updateMaxChildDistanceOnInsertion

  type(Node) :: aNode

  aNode%xCoordinate =  0.0_dp
  aNode%id = 1
  call addNodeTo( aTree, aNode )

  aNode%xCoordinate =  1.0_dp
  aNode%id = 2
  call addNodeTo( aTree, aNode )

  aNode%xCoordinate = -1.0_dp
  aNode%id = 3
  call addNodeTo( aTree, aNode )
  assert_real_equal( 1.0_dp, aTree%pair(1)%leftMaxDistance )

  aNode%xCoordinate =  2.5_dp
  aNode%id = 4
  call addNodeTo( aTree, aNode )
  assert_real_equal( 1.0_dp, aTree%pair(1)%leftMaxDistance )
  assert_real_equal( 1.5_dp, aTree%pair(1)%rightMaxDistance )

  assert_equal( 1, aTree%pair(1)%leftNode%id )
  assert_equal( 2, aTree%pair(1)%RightNode%id )
  assert_equal( 3, aTree%pair(2)%leftNode%id )
  assert_equal( 4, aTree%pair(3)%leftNode%id )

end test

test pruningFunction

  assert_true( descendTree( distanceBetweenNodes=1.0_dp, bestDistance= 0.5_dp, childDistance=2.0_dp) )

  assert_true( descendTree(1.0_dp, 0.5_dp, 0.5_dp ) )
  assert_false( descendTree(1.0_dp, 0.5_dp, 0.2_dp ) )

  assert_false( descendTree(1.0_dp, 2.0_dp, -1.0_dp ) )

end test

test oneNodeTree

  type(Node) :: aNode, nearestNode
  real(dp)       :: distance

  aNode%xCoordinate =  0.0_dp
  aNode%id = 89346
  call addNodeTo( aTree, aNode )

  aNode%xCoordinate =  1.0_dp
  distance = 29028346.357
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )

  assert_equal( 89346, nearestNode%id )
  assert_real_equal( 1.0_dp, distance  )

end test

test TwoNodeTree

  type(Node) :: aNode, nearestNode
  real(dp)       :: distance

  aNode%xCoordinate =  0.0_dp
  aNode%id = 89346
  call addNodeTo( aTree, aNode )
  aNode%xCoordinate =  1.0_dp
  aNode%id = 43
  call addNodeTo( aTree, aNode )

  aNode%xCoordinate =  0.1_dp
  distance = 29028346.357
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )

  assert_equal( 89346, nearestNode%id )
  assert_real_equal( 0.1_dp, distance  )

  aNode%xCoordinate =  0.8_dp
  distance = 29028346.357
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )

  assert_equal( 43, nearestNode%id )
  assert_real_equal( 0.2_dp, distance  )

end test

test ThreeNodeTreeLeft

  type(Node) :: aNode, nearestNode
  real(dp)       :: distance

  aNode%xCoordinate =  0.0_dp
  aNode%id = 89346
  call addNodeTo( aTree, aNode )
  aNode%xCoordinate =  1.0_dp
  aNode%id = 43
  call addNodeTo( aTree, aNode )
  aNode%xCoordinate =  0.4_dp
  aNode%id = 57
  call addNodeTo( aTree, aNode )

  aNode%xCoordinate =  0.3_dp
  distance = 29028346.357
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )

  assert_equal( 57, nearestNode%id )
  assert_real_equal( 0.1_dp, distance  )

end test

test ThreeNodeTreeRight

  type(Node) :: aNode, nearestNode
  real(dp)       :: distance

  aNode%xCoordinate =  0.0_dp
  aNode%id = 89346
  call addNodeTo( aTree, aNode )
  aNode%xCoordinate =  1.0_dp
  aNode%id = 43
  call addNodeTo( aTree, aNode )
  aNode%xCoordinate =  0.6_dp
  aNode%id = 57
  call addNodeTo( aTree, aNode )

  aNode%xCoordinate =  0.5_dp
  distance = 29028346.357
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )

  assert_equal( 57, nearestNode%id )
  assert_real_equal( 0.1_dp, distance  )

end test

test FourNodeTree

  type(Node) :: aNode, nearestNode
  real(dp)       :: distance

  aNode%xCoordinate =  0.0_dp
  aNode%id = 34
  call addNodeTo( aTree, aNode )

  aNode%xCoordinate =  1.0_dp
  aNode%id = 43
  call addNodeTo( aTree, aNode )

  aNode%xCoordinate =  0.1_dp
  aNode%id = 57
  call addNodeTo( aTree, aNode )

  aNode%xCoordinate =  0.6_dp
  aNode%id = 65
  call addNodeTo( aTree, aNode )

  aNode%xCoordinate =  0.4_dp
  distance = 29028346.357
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )

  assert_equal( 65, nearestNode%id )
  assert_real_equal( 0.2_dp, distance  )

end test



test oneDimNearest
  type(Node) :: aNode, nearestNode
  real(dp)       :: distance

  integer                  :: i
  type(Node), dimension(8) :: nodes
  nodes%xCoordinate = (/ 0.1, 5.9, 1.0, -2.5, -5.0, 6.0, 6.2, 8.0 /)

  do i = 1, 8
    nodes(i)%id = i
    call addNodeTo( aTree, nodes(i) )
  end do

  aNode%xCoordinate = 6.15
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(7) )
  assert_equal( nearestNode%id, 7 )

  aNode%xCoordinate = -2.6
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance)
  assert_true( nearestNode==nodes(4) )

  aNode%xCoordinate = 5.95  ! right between two postitive nodes
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(2) ) ! picks lower value

  aNode%xCoordinate = -3.75 ! right between two negative nodes
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(4) ) ! picks lower abs value

  aNode%xCoordinate = 1.0   ! coincident with existing node
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(3) )

  aNode%xCoordinate = 10.   ! on the high side, out of range
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(8) )

  aNode%xCoordinate = -90.  ! on the low side, out of range
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(5) )
end test

test twoDimsNearest
  type(Node) :: aNode, nearestNode
  real(dp)       :: distance

  integer                  :: i
  type(Node), dimension(5) :: nodes
  nodes%xCoordinate = (/ 0.1,  5.9,  1.0, -2.5, -5.0 /)
  nodes%yCoordinate = (/ 6.5, -2.3,  0.2, -4.7,  5.0 /)

  do i = 1, 5
    call addNodeTo( aTree, nodes(i) )
  end do

  aNode%xCoordinate = 0.9
  aNode%yCoordinate = 0.3
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(3) )

  aNode%xCoordinate = -10.
  aNode%yCoordinate = -10.
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(4) )

  aNode%xCoordinate = 10.
  aNode%yCoordinate = 10.
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(1) )
end test

test threeDimsNearest
  type(Node) :: aNode, nearestNode
  real(dp)       :: distance

  integer                  :: i
  type(Node), dimension(5) :: nodes
  nodes%xCoordinate = (/ 0.1,  5.9,  1.0, -2.5, -5.0 /)
  nodes%yCoordinate = (/ 6.5, -2.3,  0.2, -4.7,  5.0 /)
  nodes%zCoordinate = (/ 2.1,  7.7, -6.8, -1.2,  2.1 /)

  do i = 1, 5
    call addNodeTo( aTree, nodes(i) )
  end do

  aNode = Node( -2.1, -3.8, 0.2, 1 )
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(4) )

  aNode = Node( -10., -10., -10., 1 )
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(4) )

  aNode = Node(  10.,  10.,  10., 1 )
  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
  assert_true( nearestNode==nodes(1) )
end test

test SimpleCloud

 integer, parameter :: nvol  = 8
 integer, parameter :: nsurf = 4

 real(dp), dimension(nvol) :: x_vol = (/ 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0 /)
 real(dp), dimension(nvol) :: y_vol = (/ 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0 /)
 real(dp), dimension(nvol) :: z_vol = (/ 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0 /)

 real(dp), dimension(nsurf) :: x_surf = (/ 0.0, 1.0, 0.0, 1.0 /)
 real(dp), dimension(nsurf) :: y_surf = (/ 0.0, 0.0, 1.0, 1.0 /)
 real(dp), dimension(nsurf) :: z_surf = (/ 0.0, 0.0, 0.0, 0.0 /)

 real(dp), dimension(nvol)    :: slen
 integer, dimension(nvol) :: nodeID

 integer :: i, j, k

 call findNearestSurfaceNodes (nvol, x_vol, y_vol, z_vol, &
                nsurf, x_surf, y_surf, z_surf, slen, nodeID)

 do i = 1, 2
    do j = 1, 4
       k = (i-1)*4 + j
       assert_equal(j, nodeID(k) )
    end do
 end do

end test

!!$test WrongBranchDecision
!!$
!!$  type(Node) :: aNode, nearestNode
!!$  real(dp)       :: distance
!!$
!!$  aNode%xCoordinate = -1.0_dp
!!$  call addNodeTo( aTree, aNode )
!!$  aNode%xCoordinate = 1.0_dp
!!$  call addNodeTo( aTree, aNode )
!!$  aNode%xCoordinate = 0.1_dp
!!$  call addNodeTo( aTree, aNode )
!!$
!!$  aNode%xCoordinate = -0.1_dp
!!$
!!$  call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
!!$  aNode%xCoordinate = 0.1_dp
!!$  assert_true( nearestNode==aNode )
!!$
!!$end test

end test_suite

!================================= DFVF_I ====================================80
!
! Linearization of the incompressible viscous fluxes.
! Note: tx,ty,tz : unit normal to cell face.
!       qt       : T-vector (var1,u,v,w) at face.
!       dugrad   : d(u) w/r u at cell.
!       dvgrad   : d(v) w/r v at cell.
!       dwgrad   : d(w) w/r w at cell.
!
!=============================================================================80

  pure function dfvf_i( tx, ty, tz, area, amutf,                               &
                        dugrad, dvgrad, dwgrad, n )

    integer, intent(in) :: n

    real(dp),                 intent(in) :: tx, ty, tz, area, amutf
    real(dp), dimension(3,n), intent(in) :: dugrad, dvgrad, dwgrad

    integer :: ii

    real(dp) :: muf, muta, mut

    real(dp), dimension(4,4) :: dfvt

    real(dp), dimension(4,4,n) :: dfvf_i

  continue

    muf    = 1._dp
    mut    = ( muf + amutf )
    muta   =   mut*area

    dfvt(1,:) = 0._dp
    dfvt(:,1) = 0._dp
    df_loop : do ii=1,n

      dfvt(2:4,2:4) = dfv_mom_dgrad( muta, tx, ty, tz,                        &
                                     dugrad(1,ii), dugrad(2,ii), dugrad(3,ii),&
                                     dvgrad(1,ii), dvgrad(2,ii), dvgrad(3,ii),&
                                     dwgrad(1,ii), dwgrad(2,ii), dwgrad(3,ii) )

      dfvf_i(1:4,1:4,ii) = dfvt(1:4,1:4)

    enddo df_loop

  end function dfvf_i

!================================= FLUX_TURB_ADVECTION =======================80
!
! This routine computes the turbulent fluxes using upwinded scalar advection.
! Note : this is a nonconservative formulation.
!
!=============================================================================80

  pure function flux_turb_advection( xnorm, ynorm, znorm, area, face_speed,    &
                                     velocity_vector, turbl, turbr, n_turb )

    integer, intent(in) :: n_turb

    real(dp), intent(in) :: xnorm, ynorm, znorm, area, face_speed

    real(dp), dimension(3), intent(in) :: velocity_vector

    real(dp), dimension(n_turb), intent(in) :: turbl, turbr

    real(dp), dimension(n_turb) :: flux_turb_advection

    real(dp) :: ubar, uplus, uminus

  continue

    ubar  = area*( xnorm*velocity_vector(1)              &
                 + ynorm*velocity_vector(2)              &
                 + znorm*velocity_vector(3) - face_speed )

    uplus  = ubar
    if ( uplus < 0.0_dp) then
      uplus = 0.0_dp
    endif
    uminus = ubar - uplus

    flux_turb_advection(1:n_turb) =  uplus*turbl(1:n_turb) &
                                  + uminus*turbr(1:n_turb)

  end function flux_turb_advection

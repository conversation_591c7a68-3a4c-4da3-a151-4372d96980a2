  module sampling_gather

  use kinddefs, only : dp
  use lmpi,     only : lmpi_id

  implicit none

  private

  public :: gather_node_data
  public :: gather_edge_data
  public :: gather_point_data

    real(dp), parameter :: gamma_argon = 1.667_dp
    real(dp), parameter :: mol_weight  = 39.848_dp    ! Argon
    real(dp), parameter :: k_boltzman  = 1.38e-23_dp  ! [J/K]
    real(dp), parameter :: avogadro    = 6.022e+26_dp ! [molecules/kg*mole]

contains

!---------------------- GATHER_NODE_DATA -------------------------------------80
!
! Take interpolation node data and create output variable arrays for plotting
!
!-----------------------------------------------------------------------------80
  subroutine gather_node_data(grid, soln, n_edges, n_output_variables,         &
                                output_variables,                              &
                                edge_list,                                     &
                                edge_weight,                                   &
                                edge_data, sadj )

    use cgamma_util,         only : compute_aspect_ratio
    use lsq_defs,            only : nc_mapped_lsq
    use allocations,         only : my_alloc_ptr
    use solution_types,      only : soln_type
    use solution_adj,        only : sadj_type
    use grid_types,          only : grid_type
    use nml_sampling_output, only : max_smp_vars
    use nml_overset_data,    only : overset_flag
    use pundit,              only : pundit_flag
    use info_depr,           only : cc_primal
    use nml_global,          only : moving_grid
    use nml_time_avg_params, only : itime_avg, tavg_header_version
    use fun3d_constants,     only : my_1
    use sampling_headers,    only : need_gradients, need_turb_variables,       &
                                    need_slen
    use sampling_funclib,    only : edge_stats, det_a, trace_a, make_a
    use generic_gas_map,     only : n_momx, n_momy, n_momz, n_density, n_etot, &
                                    n_temperature_j, n_energy, n_energy_last,  &
                                    n_pressure_k, n_sonic_k,                   &
                                    n_molecular_weight, n_species, n_turb_g
    use solution_types,      only : generic_gas, compressible, incompressible
    use turb_gen,            only : n_amu_t, n_turb_eq

    type(grid_type),                         intent(in ) :: grid
    type(soln_type),                         intent(in ) :: soln
    integer,                                 intent(in ) :: n_edges
    integer,                                 intent(in ) :: n_output_variables
    character(80), dimension(max_smp_vars),  intent(in ) :: output_variables
    integer,       dimension(:,:),           intent(in ) :: edge_list
    real(dp),      dimension(:),             intent(in ) :: edge_weight
    real(dp),      dimension(:,:),           intent(out) :: edge_data
    type(sadj_type),               optional, intent(in ) :: sadj

    integer  :: n_turb
    integer  :: ns
!   integer  :: n_grd
    integer  :: nn, n, n_q_dof
!   real(dp) :: p_inf, cp_factor
    real(dp) :: qcriterion1, qcriterion2
    real(dp) :: slen1, slen2
    real(dp) :: hrles_blend1, hrles_blend2
!   real(dp) :: cstar

    real(dp), dimension(6)             :: turbfluctuations1, turbfluctuations2
    real(dp), dimension(3)             :: vel
    real(dp), dimension(3)             :: dw
    real(dp)                           :: velmag
    real(dp), dimension(3,3)           :: gradv1, gradv2
    real(dp), dimension(3,3)           :: asq_1, asq_2
    real(dp)                           :: conversion
    real(dp)                           :: rho, pressure, temperature
    real(dp)                           :: d, mass, coll_freq

    real(dp), dimension(:), allocatable ::  temp_vec
    character(len=80)                   ::  var

    integer :: edge, node1, node2, j

    logical :: aspect_ratio_needed = .false.
    logical :: adjoint_mode

    real(dp), dimension(:), pointer :: ar

! nodal values
    real(dp), dimension(3,2)          :: p_f
    real(dp), dimension(1)            :: px, py, pz
    real(dp), dimension(5,2)          :: qp
    real(dp), dimension(soln%n_turb,2):: qt
    real(dp), dimension(5,2)          :: qtavg
    real(dp), dimension(5,2)          :: qtrms
    real(dp), dimension(soln%n_turb,2):: turres
    real(dp), dimension(3,2)          :: gradrho_f
    real(dp), dimension(  2)          :: mut
    real(dp), dimension(6,2)          :: turb_fluct_f
    real(dp), dimension(  2)          :: s_len_f
    real(dp), dimension(  2)          :: volume_f
    real(dp), dimension(  2)          :: iflagslen
    real(dp), dimension(  2)          :: q_crit_f
    real(dp), dimension(  2)          :: blank_f
    real(dp), dimension(  2)          :: mesh_f
    real(dp), dimension(  2)          :: hrles_f
    real(dp), dimension(  2)          :: vortx_f
    real(dp), dimension(  2)          :: vorty_f
    real(dp), dimension(  2)          :: vortz_f
    real(dp), dimension(  2)          :: divvel_f
    real(dp), dimension(5,2)          :: qres
    real(dp), dimension(  2)          :: res_gcl_f
    real(dp), dimension(  2)          :: c_gamma_f
    real(dp), dimension(  2)          :: aspect_f
    real(dp), dimension(  2)          :: skip_q_f
    real(dp), dimension(soln%adim,2)  :: lambda_f
    real(dp), dimension(n_species,2)  :: rho_i_f
    real(dp), dimension(7,2)          :: q_rg_f
    real(dp), dimension(  2)          :: sw_f
    real(dp), dimension(  2)          :: pw_f
    real(dp), dimension(  2)          :: pv_f
    real(dp), dimension(  2)          :: qv_f
    real(dp), dimension(  2)          :: rv_f
    real(dp), dimension(  2)          :: global_node_f
    real(dp), dimension(  2)          :: vort_mag_tavg_f
    real(dp), dimension(  2)          :: mu_t_tavg_f
    real(dp), dimension(  2)          :: vort_x_tavg_f
    real(dp), dimension(  2)          :: vort_y_tavg_f
    real(dp), dimension(  2)          :: vort_z_tavg_f
    real(dp), dimension(soln%n_grd,2) :: reconstruction_limiter_phi_f

! averaged values
    real(dp), dimension(1  )        :: s_len
    real(dp), dimension(1  )        :: volume
    real(dp), dimension(5  )        :: q_point
    real(dp), dimension(soln%n_turb):: q_turb
    real(dp), dimension(5  )        :: q_tavg
    real(dp), dimension(5  )        :: q_trms
    real(dp), dimension(soln%n_turb):: turb_res
    real(dp), dimension(3  )        :: gradrho
    real(dp), dimension(1  )        :: mu_t
    real(dp), dimension(6  )        :: turb_fluct
    real(dp), dimension(1  )        :: iflag_s_len
    real(dp), dimension(1  )        :: q_crit
    real(dp), dimension(1  )        :: hrles_bf
    real(dp), dimension(1  )        :: vort_x
    real(dp), dimension(1  )        :: vort_y
    real(dp), dimension(1  )        :: vort_z
    real(dp), dimension(1  )        :: div_vel
    real(dp), dimension(5  )        :: q_res
    real(dp), dimension(1  )        :: res_gcl
    real(dp), dimension(1  )        :: c_gamma
    real(dp), dimension(1  )        :: aspect_r_f
    real(dp), dimension(1  )        :: skip_q
    real(dp), dimension(1  )        :: c_q
    real(dp), dimension(1  )        :: u_avg
    real(dp), dimension(1  )        :: v_avg
    real(dp), dimension(1  )        :: w_avg
    real(dp), dimension(1  )        :: c_f
    real(dp), dimension(1  )        :: c_fx
    real(dp), dimension(1  )        :: c_fy
    real(dp), dimension(1  )        :: c_fz
    real(dp), dimension(1  )        :: y_plus
    real(dp), dimension(soln%adim)  :: lambda
    real(dp), dimension(n_species)  :: rho_i
    real(dp), dimension(7)          :: q_rg
    real(dp), dimension(1 )         :: sw
    real(dp), dimension(1 )         :: pw
    real(dp), dimension(1 )         :: pv
    real(dp), dimension(1 )         :: qv
    real(dp), dimension(1 )         :: rv
!   real(dp), dimension(1 )         :: global_node
    real(dp), dimension(1  )        :: u_plus
    real(dp), dimension(1  )        :: k_plus
    real(dp), dimension(1  )        :: w_plus
    real(dp), dimension(1  )        :: t11_plus
    real(dp), dimension(1  )        :: t12_plus
    real(dp), dimension(1  )        :: t13_plus
    real(dp), dimension(1  )        :: t22_plus
    real(dp), dimension(1  )        :: t23_plus
    real(dp), dimension(1  )        :: t33_plus
    real(dp), dimension(6  )        :: tau_ij
    real(dp), dimension(6  )        :: b_ij
    real(dp), dimension(6  )        :: pk_ij
    real(dp), dimension(6  )        :: tau_sgs
    real(dp)                        :: re_tau
    real(dp), dimension(1  )        :: cmu_star
    real(dp), dimension(1  )        :: eta1s
    real(dp), dimension(1  )        :: eta2s
    real(dp), dimension(1  )        :: tr_bs
    real(dp), dimension(1  )        :: iib
    real(dp), dimension(1  )        :: iiib
    real(dp), dimension(1  )        :: f_r1
    real(dp), dimension(1  )        :: sst_f1
    real(dp), dimension(1  )        :: xi_k
    real(dp), dimension(soln%n_grd) :: reconstruction_limiter_phi
    real(dp), dimension(1  )        :: production_k
    real(dp), dimension(1  )        :: destruction_k
    real(dp), dimension(1  )        :: diffusion_k
    real(dp), dimension(1  )        :: transport_k
    real(dp), dimension(1  )        :: vgradrho
    real(dp), dimension(1  )        :: bird_breakdown
    real(dp), dimension(1  )        :: mu_t_tavg
    real(dp), dimension(1  )        :: vort_mag_tavg
    real(dp), dimension(1  )        :: vort_x_tavg
    real(dp), dimension(1  )        :: vort_y_tavg
    real(dp), dimension(1  )        :: vort_z_tavg
    real(dp), dimension(4  )        :: vort_rms
    real(dp), dimension(9  )        :: gradients

    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: half = 0.5_dp
    real(dp), parameter :: one  = 1.0_dp
    real(dp)            :: pi

    real(dp), dimension(3  )        :: edge_vector
    real(dp)                        :: disi

  continue

    pi           = acos(-one)
    adjoint_mode = .false.
    if ( present(sadj) ) adjoint_mode = .true.

    c_q      = zero
    u_avg    = zero
    v_avg    = zero
    w_avg    = zero
    c_f      = zero
    c_fx     = zero
    c_fy     = zero
    c_fz     = zero
    y_plus   = zero
    q_rg_f   = zero
    q_rg     = zero
    rho_i_f  = zero
    rho_i    = zero
    sw_f     = zero
    pw_f     = zero
!   n_grd  = soln%n_grd
    n_turb = soln%n_turb
    u_plus   = zero
    k_plus   = zero
    w_plus   = zero
    t11_plus = zero
    t12_plus = zero
    t13_plus = zero
    t22_plus = zero
    t23_plus = zero
    t33_plus = zero
    re_tau   = zero
    tau_ij   = zero
    b_ij     = zero
    pk_ij    = zero
    tau_sgs  = zero
    cmu_star = zero
    eta1s    = zero
    eta2s    = zero
    tr_bs    = zero
    iib      = zero
    iiib     = zero
    f_r1     = zero
    sst_f1   = zero
    xi_k     = zero
    reconstruction_limiter_phi = zero
    production_k  = zero
    destruction_k = zero
    diffusion_k   = zero
    transport_k   = zero
    vgradrho = zero
    bird_breakdown = zero
    gradients      = zero

    n_q_dof = grid%nnodes01

    if (cc_primal) then
      n_q_dof = grid%ncell01
    end if

    allocate(temp_vec(n_output_variables))

    ! Compute auxiliary variables

    aspect_ratio_needed = .false.
    do n = 1,n_output_variables
      if ( trim(adjustl(output_variables(n))) /= 'aspect_ratio') cycle
      aspect_ratio_needed = .true.
      call my_alloc_ptr(ar,n_q_dof)
      exit
    enddo

    if ( aspect_ratio_needed .and. .not.cc_primal ) then
      call compute_aspect_ratio( grid%nedgeloc, grid%ra, 2, grid%eptr, &
                                 grid%x, grid%y, grid%z, ar )
    elseif ( aspect_ratio_needed ) then
      call compute_aspect_ratio( grid%nface, grid%area_face, 6, grid%fptr, &
                                 grid%xc, grid%yc, grid%zc, ar )
    endif

!   aspect_ratio1 = my_1
!   aspect_ratio2 = my_1

    loop_edges:  do edge = 1, n_edges

        node1 = edge_list(1,edge)
        node2 = edge_list(2,edge)

       ! ex, ey, ez is unit vector along edge direction.


        global_node_f(1) = grid%l2g(node1)
        global_node_f(2) = grid%l2g(node2)
!       set eqn_set-independent data

        if (present(sadj)) then
          do j = 1, soln%adim
            lambda_f(j,1) = sadj%rlam(j,node1,1)
            lambda_f(j,2) = sadj%rlam(j,node2,1)
          end do
        end if

        p_f(1,1) = grid%x(node1)
        p_f(2,1) = grid%y(node1)
        p_f(3,1) = grid%z(node1)
        p_f(1,2) = grid%x(node2)
        p_f(2,2) = grid%y(node2)
        p_f(3,2) = grid%z(node2)

        edge_vector = p_f(:,2) - p_f(:,1)
        disi        = 1.0_dp/sqrt(dot_product(edge_vector,edge_vector))
        edge_vector = edge_vector * disi

        slen1   = 0._dp
        slen2   = 0._dp
        s_len_f = 0.0_dp
        if (grid%idistfcn /= 0 .and. .not.cc_primal) then
          slen1  = grid%slen(node1)
          slen2  = grid%slen(node2)
          s_len_f(1) = grid%slen(node1)
          s_len_f(2) = grid%slen(node2)
        end if

        iflagslen   = 0.0_dp
        if (grid%idistfcn /= 0 .and. .not.cc_primal) then
          iflagslen(1) = real(grid%iflagslen(node1),dp)
          iflagslen(2) = real(grid%iflagslen(node2),dp)
        end if

        c_gamma_f  = 0.0_dp
        if ( .not.cc_primal .and. nc_mapped_lsq ) then
          c_gamma_f(1) = grid%cgamma(node1)
          c_gamma_f(2) = grid%cgamma(node2)
        endif

        aspect_f      = my_1
        if ( aspect_ratio_needed ) then
          aspect_f(1) = ar(node1)
          aspect_f(2) = ar(node2)
        endif

        skip_q_f   = 0.0_dp
        if ( grid%skip_q(node1) > 0 ) skip_q_f(1) = my_1
        if ( grid%skip_q(node2) > 0 ) skip_q_f(2) = my_1

        blank_f = my_1
        mesh_f  = 0._dp

        if (overset_flag.or.pundit_flag) then
          blank_f(1) = real(grid%iblank(node1), dp)
          blank_f(2) = real(grid%iblank(node2), dp)
          mesh_f(1) = real(grid%imesh(node1), dp)
          mesh_f(2) = real(grid%imesh(node2), dp)
        end if

        volume_f(1) = grid%vol(node1)
        volume_f(2) = grid%vol(node2)

        res_gcl_f  = 0.0_dp
        if (moving_grid) then
          res_gcl_f(1) = grid%res_gcl(1,node1)
          res_gcl_f(2) = grid%res_gcl(1,node2)
        end if

!       default all time avg/rms data to zero

        qtavg     = 0.0_dp
        qtrms     = 0.0_dp

        vortx_f    = zero
        vorty_f    = zero
        vortz_f    = zero
        mu_t_tavg_f = zero
        vort_mag_tavg_f = zero
        vort_x_tavg_f = zero
        vort_y_tavg_f = zero
        vort_z_tavg_f = zero
        vort_rms   = zero

!=============================================================================80
!=============================================================================80
!=============================================================================80
!                               cell_centered
!=============================================================================80
!=============================================================================80
!=============================================================================80
        node_or_cell_centered : if (cc_primal) then          ! cell-centered

!=============================================================================80
!                            node_centered
!=============================================================================80
        else node_or_cell_centered                     ! node-centered

          if(soln%eqn_set == generic_gas .and. n_species > 1)then
            gradrho_f(1,1) = sum(soln%gradx(1:n_species,node1))
            gradrho_f(2,1) = sum(soln%grady(1:n_species,node1))
            gradrho_f(3,1) = sum(soln%gradz(1:n_species,node1))
            gradrho_f(1,2) = sum(soln%gradx(1:n_species,node2))
            gradrho_f(2,2) = sum(soln%grady(1:n_species,node2))
            gradrho_f(3,2) = sum(soln%gradz(1:n_species,node2))
          else
            gradrho_f(1,1) = soln%gradx(1,node1)
            gradrho_f(2,1) = soln%grady(1,node1)
            gradrho_f(3,1) = soln%gradz(1,node1)
            gradrho_f(1,2) = soln%gradx(1,node2)
            gradrho_f(2,2) = soln%grady(1,node2)
            gradrho_f(3,2) = soln%gradz(1,node2)
          end if

          vortx_f(1) = soln%grady(n_momz,node1)-soln%gradz(n_momy,node1)
          vorty_f(1) = soln%gradz(n_momx,node1)-soln%gradx(n_momz,node1)
          vortz_f(1) = soln%gradx(n_momy,node1)-soln%grady(n_momx,node1)
          vortx_f(2) = soln%grady(n_momz,node2)-soln%gradz(n_momy,node2)
          vorty_f(2) = soln%gradz(n_momx,node2)-soln%gradx(n_momz,node2)
          vortz_f(2) = soln%gradx(n_momy,node2)-soln%grady(n_momx,node2)

          divvel_f(1) = soln%gradx(n_momx,node1) + soln%grady(n_momy,node1)    &
                      + soln%gradz(n_momz,node1)
          divvel_f(2) = soln%gradx(n_momx,node2) + soln%grady(n_momy,node2)    &
                      + soln%gradz(n_momz,node2)

! soln%phi(n_grd,global_node)
          reconstruction_limiter_phi_f(1:soln%n_grd,1) = &
            soln%phi(1:soln%n_grd,node1)
          reconstruction_limiter_phi_f(1:soln%n_grd,2) = &
            soln%phi(1:soln%n_grd,node2)
!------------------------------------------------------------------------------
! vortex tracking
!
! critical point determination
        gradv1= make_a( soln%gradx(n_momx,node1)                               &
                      , soln%grady(n_momx,node1)                               &
                      , soln%gradz(n_momx,node1)                               &
                      , soln%gradx(n_momy,node1)                               &
                      , soln%grady(n_momy,node1)                               &
                      , soln%gradz(n_momy,node1)                               &
                      , soln%gradx(n_momz,node1)                               &
                      , soln%grady(n_momz,node1)                               &
                      , soln%gradz(n_momz,node1) )
        gradv2= make_a( soln%gradx(n_momx,node2)                               &
                      , soln%grady(n_momx,node2)                               &
                      , soln%gradz(n_momx,node2)                               &
                      , soln%gradx(n_momy,node2)                               &
                      , soln%grady(n_momy,node2)                               &
                      , soln%gradz(n_momy,node2)                               &
                      , soln%gradx(n_momz,node2)                               &
                      , soln%grady(n_momz,node2)                               &
                      , soln%gradz(n_momz,node2) )
          asq_1 = matmul( gradv1, gradv1 )
          asq_2 = matmul( gradv2, gradv2 )
          pv_f(1) = -trace_a( gradv1 )
          pv_f(2) = -trace_a( gradv2 )
          qv_f(1) = half * ( pv_f(1)*pv_f(1) - trace_a( asq_1 ) )
          qv_f(2) = half * ( pv_f(2)*pv_f(2) - trace_a( asq_2 ) )
          rv_f(1) = -det_a( gradv1 )
          rv_f(2) = -det_a( gradv2 )
! critical point determination

          vel(1:3) = soln%q_dof(n_momx:n_momz,node1)
          velmag   = dot_product(vel,vel)
          dw(1)    = det_a ( (/vel,                                            &
                               soln%grady(n_momx:n_momz,node1),                &
                               soln%gradz(n_momx:n_momz,node1)/) )
          dw(2)    = det_a ( (/soln%gradx(n_momx:n_momz,node1),                &
                               vel,                                            &
                               soln%gradz(n_momx:n_momz,node1)/) )
          dw(3)    = det_a ( (/soln%gradx(n_momx:n_momz,node1),                &
                               soln%grady(n_momx:n_momz,node1),                &
                               vel/) )
          if ( abs(velmag**2) > epsilon( real(dot_product(vel,dw),dp) ) ) then
            pw_f(1)  = dot_product(vel,dw)/velmag**2
          else
            pw_f(1)  = dot_product(vel,dw)
          end if
          if ( abs(sqrt(velmag)) > epsilon( real(divvel_f(1),dp) ) ) then
            sw_f(1)  = divvel_f(1)/sqrt(velmag)
          else
            sw_f(1)  = divvel_f(1)
          end if

          vel(1:3) = soln%q_dof(n_momx:n_momz,node2)
          velmag   = dot_product(vel,vel)
          dw(1)    = det_a ( (/vel,                                            &
                               soln%grady(n_momx:n_momz,node2),                &
                               soln%gradz(n_momx:n_momz,node2)/) )
          dw(2)    = det_a ( (/soln%gradx(n_momx:n_momz,node2),                &
                               vel,                                            &
                               soln%gradz(n_momx:n_momz,node2)/) )
          dw(3)    = det_a ( (/soln%gradx(n_momx:n_momz,node2),                &
                               soln%grady(n_momx:n_momz,node2),                &
                               vel/) )
          if ( abs(velmag**2) > epsilon( real(dot_product(vel,dw),dp) ) ) then
            pw_f(2)  = dot_product(vel,dw)/velmag**2
          else
            pw_f(2)  = dot_product(vel,dw)
          end if
          if ( abs(sqrt(velmag)) > epsilon( real(divvel_f(2),dp) ) ) then
            sw_f(2)  = divvel_f(2)/sqrt(velmag)
          else
            sw_f(2)  = divvel_f(2)
          end if
!
! vortex tracking
!------------------------------------------------------------------------------

          if ( .not. adjoint_mode ) then
            if(soln%eqn_set == generic_gas .and. n_species > 1)then
              qres(1,1) = sum(soln%res(1:n_species,node1))
              qres(1,2) = sum(soln%res(1:n_species,node2))
              qres(2:4,1) = soln%res(n_momx:n_momz,node1)
              qres(2:4,2) = soln%res(n_momx:n_momz,node2)
            else
              qres(1:4,1) = soln%res(1:4,node1)
              qres(1:4,2) = soln%res(1:4,node2)
            end if
          endif

          mut    = 0.0_dp
          qt     = 0.0_dp
          turres = 0.0_dp

          if(soln%eqn_set == generic_gas)then
            if(n_turb_g /= 0)then
              mut(1)             = soln%q_dof(n_amu_t,node1)
              mut(2)             = soln%q_dof(n_amu_t,node2)
            end if
            if(n_turb_g > 0)then
              qt(1:n_turb_g,1)   = soln%q_dof(n_turb_eq(1):n_turb_eq(n_turb_g),&
                                              node1)
              qt(1:n_turb_g,2)   = soln%q_dof(n_turb_eq(1):n_turb_eq(n_turb_g),&
                                              node2)
              turres(1:n_turb_g,1) = soln%res(n_turb_eq(1):n_turb_eq(n_turb_g),&
                                              node1)
              turres(1:n_turb_g,2) = soln%res(n_turb_eq(1):n_turb_eq(n_turb_g),&
                                              node2)
            end if
          else
            if ( n_turb /= 0 ) then
              mut(1)             = soln%amut(node1)
              mut(2)             = soln%amut(node2)
              qt(1:n_turb,1)     = soln%turb(1:n_turb,node1)
              qt(1:n_turb,2)     = soln%turb(1:n_turb,node2)
              turres(1:n_turb,1) = soln%turbres(1:n_turb,node1)
              turres(1:n_turb,2) = soln%turbres(1:n_turb,node2)
            end if
          endif

          select case(soln%eqn_set)

            case (compressible)

              qp(1:5,1)    = soln%q_dof(1:5,node1)
              qp(1:5,2)    = soln%q_dof(1:5,node2)
              if ( .not. adjoint_mode ) then
                qres(5,1)    = soln%res(5,node1)
                qres(5,2)    = soln%res(5,node2)
              endif

!             time average and time rms data only for compressible path

              if (itime_avg /= 0) then

                qtavg(1:5,1)  = soln%q_time_avg(1:5,node1)
                qtavg(1:5,2)  = soln%q_time_avg(1:5,node2)
                qtrms(1:5,1) = &
                      sqrt(abs(soln%q_time_avg(6:10,node1)  - qtavg(1:5,1)**2))
                qtrms(1:5,2) = &
                      sqrt(abs(soln%q_time_avg(6:10,node2)  - qtavg(1:5,2)**2))
                if ( tavg_header_version == 2 ) then
                  mu_t_tavg_f(1) = soln%qq_time_avg(4,node1)
                  mu_t_tavg_f(2) = soln%qq_time_avg(4,node2)
                  vort_mag_tavg_f(1) = soln%qq_time_avg(5,node1)
                  vort_mag_tavg_f(2) = soln%qq_time_avg(5,node2)
                else if ( tavg_header_version == 3 ) then
                  vort_x_tavg_f(1)   = soln%qq_time_avg(1,node1)
                  vort_x_tavg_f(2)   = soln%qq_time_avg(1,node2)
                  vort_y_tavg_f(1)   = soln%qq_time_avg(2,node1)
                  vort_y_tavg_f(2)   = soln%qq_time_avg(2,node2)
                  vort_z_tavg_f(1)   = soln%qq_time_avg(3,node1)
                  vort_z_tavg_f(2)   = soln%qq_time_avg(3,node2)
                  mu_t_tavg_f(1)     = soln%qq_time_avg(4,node1)
                  mu_t_tavg_f(2)     = soln%qq_time_avg(4,node2)
                  vort_mag_tavg_f(1) = soln%qq_time_avg(5,node1)
                  vort_mag_tavg_f(2) = soln%qq_time_avg(5,node2)
                end if

              end if

            case (incompressible)

              qp(1:4,1) = soln%q_dof(1:4,node1)
              qp(1:4,2) = soln%q_dof(1:4,node2)

            case (generic_gas)

              do ns = 1,n_species
                 rho_i_f(ns,1) = soln%q_dof(ns,node1)
                 rho_i_f(ns,2) = soln%q_dof(ns,node2)
              end do
              qp(1,1)     = soln%q_dof(n_density,node1)             ! rho
              qp(2,1)     = soln%q_dof(n_momx,node1) / qp(1,1)      ! u
              qp(3,1)     = soln%q_dof(n_momy,node1) / qp(1,1)      ! v
              qp(4,1)     = soln%q_dof(n_momz,node1) / qp(1,1)      ! w
              qp(5,1)     = soln%q_dof(n_pressure_k(1),node1)       ! p
              if (n_energy > 1) then
                q_rg_f(1,1) = soln%q_dof(n_energy_last,node1)       ! ev
                q_rg_f(2,1) = soln%q_dof(n_temperature_j(2),node1)  ! tv
              end if
              q_rg_f(3,1) = soln%q_dof(n_temperature_j(1),node1)    ! tt
              q_rg_f(4,1) = soln%q_dof(n_molecular_weight,node1)
              q_rg_f(5,1) = soln%q_dof(n_sonic_k(1),node1)
              q_rg_f(6,1) = ( soln%q_dof(n_etot,node1) + qp(5,1) ) / qp(1,1)
              q_rg_f(7,1) = soln%q_dof(n_etot,node1) / qp(1,1)       ! etot

              qp(1,2)     = soln%q_dof(n_density,node2)
              qp(2,2)     = soln%q_dof(n_momx,node1) / qp(1,2)
              qp(3,2)     = soln%q_dof(n_momy,node1) / qp(1,2)
              qp(4,2)     = soln%q_dof(n_momz,node1) / qp(1,2)
              qp(5,2)     = soln%q_dof(n_pressure_k(1),node1)
              if (n_energy > 1) then
                q_rg_f(1,2) = soln%q_dof(n_energy_last,node2) ! ev
                q_rg_f(2,2) = soln%q_dof(n_temperature_j(2),node2) ! tv
              end if
              q_rg_f(3,2) = soln%q_dof(n_temperature_j(1),node2) ! tt
              q_rg_f(4,2) = soln%q_dof(n_molecular_weight,node2)
              q_rg_f(5,2) = soln%q_dof(n_sonic_k(1),node2)
              q_rg_f(6,2) = ( soln%q_dof(n_etot,node2) + qp(5,2) ) / qp(1,2)
              q_rg_f(7,2) = soln%q_dof(n_etot,node2) / qp(1,2)       ! etot

            case default

          end select

          q_crit_f     = zero
          turb_fluct_f = zero
          hrles_f      = my_1 ! pure RANS

          if (need_gradients) then
            qcriterion1 = q_criterion(soln%eqn_set, soln%n_grd,                &
                                      soln%gradx(:,node1),                     &
                                      soln%grady(:,node1),                     &
                                      soln%gradz(:,node1))
            qcriterion2 = q_criterion(soln%eqn_set, soln%n_grd,                &
                                      soln%gradx(:,node2),                     &
                                      soln%grady(:,node2),                     &
                                      soln%gradz(:,node2))
            q_crit_f(1) = qcriterion1
            q_crit_f(2) = qcriterion2
          end if

          if (need_gradients .and. need_slen) then
            hrles_blend1 = hrles_blend(soln%eqn_set, soln%n_tot, soln%n_grd,   &
                                       slen1,                                  &
                                       soln%q_dof(:,node1),                    &
                                       soln%turb(:,node1),                     &
                                       soln%gradx(:,node1),                    &
                                       soln%grady(:,node1),                    &
                                       soln%gradz(:,node1))
            hrles_blend2 = hrles_blend(soln%eqn_set, soln%n_tot, soln%n_grd,   &
                                       slen2,                                  &
                                       soln%q_dof(:,node2),                    &
                                       soln%turb(:,node2),                     &
                                       soln%gradx(:,node2),                    &
                                       soln%grady(:,node2),                    &
                                       soln%gradz(:,node2))
            hrles_f(1) = hrles_blend1
            hrles_f(2) = hrles_blend2
          end if

          if (need_turb_variables) then
              turbfluctuations1 =                                              &
              turb_fluctuations(soln%n_grd, soln%n_tot, soln%n_turb,           &
                                soln%eqn_set,                                  &
                                soln%q_dof(:,node1), soln%amut(node1),         &
                                soln%gradx(:,node1), soln%grady(:,node1),      &
                                soln%gradz(:,node1), soln%turb(:,node1))
              turbfluctuations2 =                                              &
              turb_fluctuations(soln%n_grd, soln%n_tot, soln%n_turb,           &
                                soln%eqn_set,                                  &
                                soln%q_dof(:,node2), soln%amut(node2),         &
                                soln%gradx(:,node2), soln%grady(:,node2),      &
                                soln%gradz(:,node2), soln%turb(:,node2))
            turb_fluct_f(1:6,1) = turbfluctuations1
            turb_fluct_f(1:6,2) = turbfluctuations2
          end if

        end if node_or_cell_centered
!=============================================================================80
!=============================================================================80
!=============================================================================80
!                       end node_or_cell_centered
!=============================================================================80
!=============================================================================80
!=============================================================================80

          px          = edge_stats(1, edge_weight(edge), p_f(1,:) )
          py          = edge_stats(1, edge_weight(edge), p_f(2,:) )
          pz          = edge_stats(1, edge_weight(edge), p_f(3,:) )
          q_point     = edge_stats(5, edge_weight(edge), qp )
          q_turb      = edge_stats(n_turb, edge_weight(edge), qt )
          q_tavg      = edge_stats(5, edge_weight(edge), qtavg )
          q_trms      = edge_stats(5, edge_weight(edge), qtrms )
          gradrho     = edge_stats(3, edge_weight(edge), gradrho_f )
          turb_res    = edge_stats(n_turb, edge_weight(edge), turres )
          mu_t        = edge_stats(1, edge_weight(edge), mut )
          turb_fluct  = edge_stats(6, edge_weight(edge), turb_fluct_f )
          s_len       = edge_stats(1, edge_weight(edge), s_len_f )
          volume      = edge_stats(1, edge_weight(edge), volume_f )
          iflag_s_len = edge_stats(1, edge_weight(edge), iflagslen )
          q_crit      = edge_stats(1, edge_weight(edge), q_crit_f )
          aspect_r_f  = edge_stats(1, edge_weight(edge), aspect_f )
          vort_x      = edge_stats(1, edge_weight(edge), vortx_f )
          vort_y      = edge_stats(1, edge_weight(edge), vorty_f )
          vort_z      = edge_stats(1, edge_weight(edge), vortz_f )
          div_vel     = edge_stats(1, edge_weight(edge), divvel_f )
          q_res       = edge_stats(5, edge_weight(edge), qres )
          res_gcl     = edge_stats(1, edge_weight(edge), res_gcl_f )
          c_gamma     = edge_stats(1, edge_weight(edge), c_gamma_f )
          skip_q      = edge_stats(1, edge_weight(edge), skip_q_f )
          hrles_bf    = edge_stats(1, edge_weight(edge), hrles_f )
          lambda      = edge_stats(soln%adim, edge_weight(edge), lambda_f )
          rho_i       = edge_stats(n_species, edge_weight(edge), rho_i_f )
          q_rg        = edge_stats(7, edge_weight(edge), q_rg_f )
          sw          = edge_stats(1, edge_weight(edge), sw_f )
          pw          = edge_stats(1, edge_weight(edge), pw_f )
          pv          = edge_stats(1, edge_weight(edge), pv_f )
          qv          = edge_stats(1, edge_weight(edge), qv_f )
          rv          = edge_stats(1, edge_weight(edge), rv_f )
          mu_t_tavg   = edge_stats(1, edge_weight(edge), mu_t_tavg_f )
          vort_mag_tavg    = edge_stats(1, edge_weight(edge), vort_mag_tavg_f )
          vort_x_tavg    = edge_stats(1, edge_weight(edge), vort_x_tavg_f )
          vort_y_tavg    = edge_stats(1, edge_weight(edge), vort_y_tavg_f )
          vort_z_tavg    = edge_stats(1, edge_weight(edge), vort_z_tavg_f )
          reconstruction_limiter_phi  = edge_stats(soln%n_grd                 &
                       , edge_weight(edge), reconstruction_limiter_phi_f )


          vgradrho(1) = q_point(2)*gradrho(1)                                 &
                      + q_point(3)*gradrho(2)                                 &
                      + q_point(4)*gradrho(3)

          conversion  = sqrt(gamma_argon*208._dp*180._dp) ! [m/s]???
          rho         = q_point(1) *1e-6_dp               ! [kg/m^3]
!         u           = q_point(2) *conversion            ! [m/s]
!         v           = q_point(3) *conversion            ! [m/s]
!         w           = q_point(4) *conversion            ! [m/s]
!         temperature = gamma*q_point(5)/q_point(1)
          pressure    = q_point(5) *1e-6*208._dp*180._dp  ! [Pa]
          temperature = pressure/rho*208.0_dp             ! [K]
          d           = 4.040e-10_dp                      ! [m]
          mass        = mol_weight/avogadro               ! kg
          coll_freq   = (pi*d*d*rho/mass)                                     &
                      * sqrt(16.0_dp*k_boltzman*temperature/(pi*mass))
          bird_breakdown(1) = abs(vgradrho(1)) * 1e-6_dp * conversion         &
                         / ( coll_freq * rho )


          output_var_loop:    do nn = 1,n_output_variables

            var          = trim(adjustl(output_variables(nn)))
            edge_data(nn,edge) = get_var( var, soln%eqn_set                   &
                      , px(1), py(1), pz(1), s_len, volume                    &
                      , q_point, q_turb, q_tavg, q_trms, turb_res, mu_t       &
                      , turb_fluct, iflag_s_len, q_crit                       &
                      , vort_x, vort_y, vort_z, div_vel                       &
                      , q_res, res_gcl, c_gamma, aspect_r_f                   &
                      , skip_q, blank_f, mesh_f                               &
                      , hrles_bf, c_q, c_fx, c_fy, c_fz, c_f, y_plus          &
                      , u_avg, v_avg, w_avg, rho_i, q_rg, lambda              &
                      , sw, pw, pv, qv, rv, global_node_f                     &
                      , u_plus, k_plus, w_plus, t11_plus, t12_plus, t13_plus  &
                      , t22_plus, t23_plus, t33_plus, re_tau, tau_ij, b_ij    &
                      , cmu_star, eta1s, eta2s, tr_bs, iib, iiib              &
                      , vgradrho, bird_breakdown, mu_t_tavg, vort_mag_tavg    &
                      , vort_x_tavg, vort_y_tavg, vort_z_tavg                 &
                      , vort_rms, n_turb, pk_ij, tau_sgs, gradients, f_r1     &
                      , production_k, destruction_k, diffusion_k, transport_k &
                      , sst_f1, xi_k , soln%n_grd, reconstruction_limiter_phi &
                      )

          end do output_var_loop

    end do loop_edges

    deallocate ( temp_vec )

    if ( aspect_ratio_needed ) deallocate(ar)

  end subroutine gather_node_data

!----------------------GATHER_EDGE_DATA --------------------------------------80
!
! Take interpolation node data and create output variable arrays for plotting
! Called by subroutine write_boundary_data for boundary sampling
!
!-----------------------------------------------------------------------------80

  subroutine gather_edge_data(grid, soln, n_edges, n_output_variables,         &
                                output_variables, edge_donor_node1,            &
                                edge_donor_node2, edge_donor_weight1,          &
                                edge_donor_weight2,                            &
                                edge_data, local_node1, local_node2, sadj )

    use cgamma_util,         only : compute_aspect_ratio
    use lsq_defs,            only : nc_mapped_lsq
    use allocations,         only : my_alloc_ptr
    use solution_types,      only : soln_type, generic_gas,                    &
                                    compressible, incompressible
    use solution_adj,        only : sadj_type
    use fluid,               only : gamma, sutherland_constant, xgm1
    use grid_types,          only : grid_type
    use nml_sampling_output, only : max_smp_vars
    use nml_overset_data,    only : overset_flag
    use pundit,              only : pundit_flag
    use info_depr,           only : xmach, cc_primal, tref
    use nml_global,          only : moving_grid
    use nml_time_avg_params, only : itime_avg
    use fun3d_constants,     only : my_1
    use sampling_headers,    only : need_gradients, need_turb_variables        &
                                  , need_slen
    use generic_gas_map,     only : n_momx, n_momy, n_momz, n_density, n_etot, &
                                    n_temperature_j, n_energy, n_energy_last,  &
                                    n_pressure_k, n_sonic_k,                   &
                                    n_molecular_weight, n_species
    use shared_gas_variables, only: spec_propv

    type(grid_type),                            intent(in) :: grid
    type(soln_type),                            intent(in) :: soln
    integer,                                    intent(in) :: n_edges
    integer,                                    intent(in) :: n_output_variables
    character(len=80), dimension(max_smp_vars), intent(in) :: output_variables
    integer,  dimension(:),                     intent(in) :: edge_donor_node1
    integer,  dimension(:),                     intent(in) :: edge_donor_node2
    real(dp), dimension(:),                     intent(in) :: edge_donor_weight1
    real(dp), dimension(:),                     intent(in) :: edge_donor_weight2
    type(sadj_type), optional,                  intent(in) :: sadj

    real(dp), dimension(:,:), pointer                           :: edge_data
    integer , dimension(:,:), pointer                           :: local_node1
    integer , dimension(:,:), pointer                           :: local_node2

    integer  :: n_turb
    integer  :: nn, n, n_q_dof
    real(dp) :: p_inf, cp_factor
    real(dp) :: weight1, weight2
    real(dp) :: x1, x2
    real(dp) :: y1, y2
    real(dp) :: z1, z2
    real(dp) :: rho1, rho2
    real(dp) :: u1, u2
    real(dp) :: v1, v2
    real(dp) :: w1, w2
    real(dp) :: velsq1, velsq2
    real(dp) :: pr1, pr2
    real(dp) :: cp1, cp2
    real(dp) :: vort_x1, vort_x2
    real(dp) :: vort_y1, vort_y2
    real(dp) :: vort_z1, vort_z2
    real(dp) :: vort_mag1, vort_mag2
    real(dp) :: div_vel1, div_vel2
    real(dp) :: qcriterion1, qcriterion2
    real(dp) :: mach1, mach2
    real(dp) :: temp1, temp2
    real(dp) :: mu_lam1, mu_lam2
    real(dp) :: blank1, blank2
    real(dp) :: vol1, vol2
    real(dp) :: slen1, slen2
    real(dp) :: iflagslen1, iflagslen2
    real(dp) :: cgamma1, cgamma2, aspect_ratio1, aspect_ratio2, skip_q1, skip_q2
    real(dp) :: mu_t1, mu_t2
    real(dp) :: entropy1, entropy2
    real(dp) :: turb1_1, turb1_2
    real(dp) :: turb2_1, turb2_2
    real(dp) :: turb3_1, turb3_2
    real(dp) :: turb4_1, turb4_2
    real(dp) :: turb5_1, turb5_2
    real(dp) :: turb6_1, turb6_2
    real(dp) :: turb7_1, turb7_2
    real(dp) :: res_gcl1, res_gcl2
    real(dp) :: turres1_1, turres1_2
    real(dp) :: turres2_1, turres2_2
    real(dp) :: turres3_1, turres3_2
    real(dp) :: turres4_1, turres4_2
    real(dp) :: turres5_1, turres5_2
    real(dp) :: turres6_1, turres6_2
    real(dp) :: turres7_1, turres7_2
    real(dp) :: res1_1, res1_2
    real(dp) :: res2_1, res2_2
    real(dp) :: res3_1, res3_2
    real(dp) :: res4_1, res4_2
    real(dp) :: res5_1, res5_2
    real(dp) :: mesh1, mesh2
    real(dp) :: rho_tavg1, u_tavg1, v_tavg1, w_tavg1, p_tavg1
    real(dp) :: rho_tavg2, u_tavg2, v_tavg2, w_tavg2, p_tavg2
    real(dp) :: rho_trms1, u_trms1, v_trms1, w_trms1, p_trms1
    real(dp) :: rho_trms2, u_trms2, v_trms2, w_trms2, p_trms2
    real(dp) :: hrles_blend1, hrles_blend2
    real(dp) :: cstar
    real(dp) :: ev1, tv1, tt1, mol_wt1, sonic1, htot1, etot1
    real(dp) :: ev2, tv2, tt2, mol_wt2, sonic2, htot2, etot2
    real(dp) :: proc_id1, proc_id2

    real(dp), dimension(n_species)     :: rho_i1, rho_i2
    real(dp), dimension(6)             :: turbfluctuations1, turbfluctuations2
    real(dp), dimension(soln%adim,2)   :: lambda

    real(dp), dimension(:), allocatable ::  temp_vec

    integer :: edge, node1, node2, ns

    character(80)                :: species_name

    logical :: aspect_ratio_needed = .false.

    real(dp), dimension(:), pointer :: ar

  continue

    cstar = sutherland_constant / tref

    res5_1    = 0.0_dp
    res5_2    = 0.0_dp
    turres2_1 = 0.0_dp
    turres2_2 = 0.0_dp
    turres3_1 = 0.0_dp
    turres3_2 = 0.0_dp
    turres4_1 = 0.0_dp
    turres4_2 = 0.0_dp
    turres5_1 = 0.0_dp
    turres5_2 = 0.0_dp
    turres6_1 = 0.0_dp
    turres6_2 = 0.0_dp
    turres7_1 = 0.0_dp
    turres7_2 = 0.0_dp
    turb1_1   = 0.0_dp
    turb2_1   = 0.0_dp
    turb3_1   = 0.0_dp
    turb4_1   = 0.0_dp
    turb5_1   = 0.0_dp
    turb6_1   = 0.0_dp
    turb7_1   = 0.0_dp
    turb1_2   = 0.0_dp
    turb2_2   = 0.0_dp
    turb3_2   = 0.0_dp
    turb4_2   = 0.0_dp
    turb5_2   = 0.0_dp
    turb6_2   = 0.0_dp
    turb7_2   = 0.0_dp
    velsq2    = 0.0_dp

    if ( soln%eqn_set == compressible ) then
      p_inf     = 1._dp/gamma
      cp_factor = gamma*xmach**2
    else if ( soln%eqn_set == generic_gas ) then
      p_inf     = 1._dp/gamma
      cp_factor = my_1
    else
      p_inf     = my_1
      cp_factor = my_1
    end if

    n_turb = soln%n_turb

    n_q_dof = grid%nnodes01

    if (cc_primal) then
      n_q_dof = grid%ncell01
    end if

    allocate(temp_vec(n_output_variables))

    ! Compute auxiliary variables

    aspect_ratio_needed = .false.
    do n = 1,n_output_variables
      if ( trim(adjustl(output_variables(n))) /= 'aspect_ratio') cycle
      aspect_ratio_needed = .true.
      call my_alloc_ptr(ar,n_q_dof)
      exit
    enddo

    if ( aspect_ratio_needed .and. .not.cc_primal ) then
      call compute_aspect_ratio( grid%nedgeloc, grid%ra, 2, grid%eptr, &
                                 grid%x, grid%y, grid%z, ar )
    elseif ( aspect_ratio_needed ) then
      call compute_aspect_ratio( grid%nface, grid%area_face, 6, grid%fptr, &
                                 grid%xc, grid%yc, grid%zc, ar )
    endif

    aspect_ratio1 = my_1
    aspect_ratio2 = my_1

    proc_id1 = lmpi_id
    proc_id2 = lmpi_id

    loop_edges:  do edge = 1, n_edges

        node1 = edge_donor_node1(edge)
        node2 = edge_donor_node2(edge)

        weight1 = edge_donor_weight1(edge)
        weight2 = edge_donor_weight2(edge)

!       set eqn_set-independent data

        if (present(sadj)) then
          lambda(:,1) = sadj%rlam(1:soln%adim,node1,1)
          lambda(:,2) = sadj%rlam(1:soln%adim,node2,1)
        end if

        x1     = grid%x(node1)
        y1     = grid%y(node1)
        z1     = grid%z(node1)

        x2     = grid%x(node2)
        y2     = grid%y(node2)
        z2     = grid%z(node2)

        slen1  = 0._dp
        slen2  = 0._dp
        if (grid%idistfcn /= 0 .and. .not.cc_primal) then
          slen1  = grid%slen(node1)
          slen2  = grid%slen(node2)
        end if

        iflagslen1  = 0._dp
        iflagslen2  = 0._dp
        if (grid%idistfcn /= 0 .and. .not.cc_primal) then
          iflagslen1  = real(grid%iflagslen(node1),dp)
          iflagslen2  = real(grid%iflagslen(node2),dp)
        end if

        cgamma1  = 0._dp
        cgamma2  = 0._dp
        if ( .not.cc_primal .and. nc_mapped_lsq ) then
          cgamma1  = grid%cgamma(node1)
          cgamma2  = grid%cgamma(node2)
        endif

        if ( aspect_ratio_needed ) then
          aspect_ratio1 = ar(node1)
          aspect_ratio2 = ar(node2)
        endif

        skip_q1  = 0._dp
        skip_q2  = 0._dp
        if ( grid%skip_q(node1) > 0 ) skip_q1 = my_1
        if ( grid%skip_q(node2) > 0 ) skip_q2 = my_1

        blank1 = my_1
        mesh1  = 0._dp
        blank2 = my_1
        mesh2  = 0._dp

        if (overset_flag.or.pundit_flag) then
          blank1 = real(grid%iblank(node1), dp)
          blank2 = real(grid%iblank(node2), dp)
          mesh1  = real(grid%imesh(node1), dp)
          mesh2  = real(grid%imesh(node2), dp)
        end if

        vol1       = grid%vol(node1)
        vol2       = grid%vol(node2)

        res_gcl1 = 0._dp
        res_gcl2 = 0._dp
        if (moving_grid) then
          res_gcl1 = grid%res_gcl(1,node1)
          res_gcl2 = grid%res_gcl(1,node2)
        end if

!       default all time avg/rms data to zero

        rho_tavg1 = 0.0_dp
        u_tavg1   = 0.0_dp
        v_tavg1   = 0.0_dp
        w_tavg1   = 0.0_dp
        p_tavg1   = 0.0_dp
        rho_trms1 = 0.0_dp
        u_trms1   = 0.0_dp
        v_trms1   = 0.0_dp
        w_trms1   = 0.0_dp
        p_trms1   = 0.0_dp

        rho_tavg2 = 0.0_dp
        u_tavg2   = 0.0_dp
        v_tavg2   = 0.0_dp
        w_tavg2   = 0.0_dp
        p_tavg2   = 0.0_dp
        rho_trms2 = 0.0_dp
        u_trms2   = 0.0_dp
        v_trms2   = 0.0_dp
        w_trms2   = 0.0_dp
        p_trms2   = 0.0_dp

!       default gen gas data to zero

        ev1       = 0._dp
        tv1       = 0._dp
        tt1       = 0._dp
        mol_wt1   = 0._dp
        sonic1    = 0._dp
        htot1     = 0._dp
        etot1     = 0._dp
        rho_i1(:) = 0._dp

        ev2       = 0._dp
        tv2       = 0._dp
        tt2       = 0._dp
        mol_wt2   = 0._dp
        sonic2    = 0._dp
        htot2     = 0._dp
        etot2     = 0._dp
        rho_i2(:) = 0._dp

!       Initialize the rest
        rho1       = 0._dp
        u1         = 0._dp
        v1         = 0._dp
        w1         = 0._dp
        pr1        = 0._dp
        cp1        = 0._dp
        temp1      = 0._dp
        mach1      = 0._dp
        entropy1   = 0._dp

        rho2       = 0._dp
        u2         = 0._dp
        v2         = 0._dp
        w2         = 0._dp
        pr2        = 0._dp
        cp2        = 0._dp
        temp2      = 0._dp
        mach2      = 0._dp
        entropy2   = 0._dp

        node_or_cell_centered : if (cc_primal) then          ! cell-centered

          vort_x1    = 0._dp
          vort_y1    = 0._dp
          vort_z1    = 0._dp
          vort_mag1  = 0._dp

          vort_x2    = 0._dp
          vort_y2    = 0._dp
          vort_z2    = 0._dp
          vort_mag2  = 0._dp

          res1_1     = 0._dp
          res2_1     = 0._dp
          res3_1     = 0._dp
          res4_1     = 0._dp
          res5_1     = 0._dp

          res1_2     = 0._dp
          res2_2     = 0._dp
          res3_2     = 0._dp
          res4_2     = 0._dp
          res5_2     = 0._dp

          turres1_1  = 0._dp
          turres1_2  = 0._dp
          turres2_1  = 0._dp
          turres2_2  = 0._dp
          turres3_1  = 0._dp
          turres3_2  = 0._dp
          turres4_1  = 0._dp
          turres4_2  = 0._dp
          turres5_1  = 0._dp
          turres5_2  = 0._dp
          turres6_1  = 0._dp
          turres6_2  = 0._dp
          turres7_1  = 0._dp
          turres7_2  = 0._dp

          mu_t1      = 0._dp
          mu_t2      = 0._dp

          div_vel1   = 0._dp
          div_vel2   = 0._dp

          select case (n_turb)

            case(1)

              turb1_1   = soln%qtavg(soln%n_q,node1)
              turb2_1   = 0.0_dp

              turb1_2   = soln%qtavg(soln%n_q,node2)
              turb2_2   = 0.0_dp

            case(2)

              turb1_1   = soln%qtavg(soln%n_q-1,node1)
              turb2_1   = soln%qtavg(soln%n_q,node1)

              turb1_2   = soln%qtavg(soln%n_q-1,node2)
              turb2_2   = soln%qtavg(soln%n_q,node2)

            case(4)

              turb1_1   = soln%qtavg(soln%n_q-3,node1)
              turb2_1   = soln%qtavg(soln%n_q-2,node1)
              turb3_1   = soln%qtavg(soln%n_q-1,node1)
              turb4_1   = soln%qtavg(soln%n_q,node1)

              turb1_2   = soln%qtavg(soln%n_q-3,node2)
              turb2_2   = soln%qtavg(soln%n_q-2,node2)
              turb3_2   = soln%qtavg(soln%n_q-1,node2)
              turb4_2   = soln%qtavg(soln%n_q,node2)

            case(7)

              turb1_1   = soln%qtavg(soln%n_q-6,node1)
              turb2_1   = soln%qtavg(soln%n_q-5,node1)
              turb3_1   = soln%qtavg(soln%n_q-4,node1)
              turb4_1   = soln%qtavg(soln%n_q-3,node1)
              turb5_1   = soln%qtavg(soln%n_q-2,node1)
              turb6_1   = soln%qtavg(soln%n_q-1,node1)
              turb7_1   = soln%qtavg(soln%n_q,node1)

              turb1_2   = soln%qtavg(soln%n_q-6,node2)
              turb2_2   = soln%qtavg(soln%n_q-5,node2)
              turb3_2   = soln%qtavg(soln%n_q-4,node2)
              turb4_2   = soln%qtavg(soln%n_q-3,node2)
              turb5_2   = soln%qtavg(soln%n_q-2,node2)
              turb6_2   = soln%qtavg(soln%n_q-1,node2)
              turb7_2   = soln%qtavg(soln%n_q,node2)

            case default

              turb1_1   = 0.0_dp
              turb2_1   = 0.0_dp
              turb3_1   = 0.0_dp
              turb4_1   = 0.0_dp
              turb5_1   = 0.0_dp
              turb6_1   = 0.0_dp
              turb7_1   = 0.0_dp
              turb1_2   = 0.0_dp
              turb2_2   = 0.0_dp
              turb3_2   = 0.0_dp
              turb4_2   = 0.0_dp
              turb5_2   = 0.0_dp
              turb6_2   = 0.0_dp
              turb7_2   = 0.0_dp

          end select

          select case(soln%eqn_set)

            case (compressible)

              rho1       = soln%qtavg(1,node1)
              u1         = soln%qtavg(2,node1)
              v1         = soln%qtavg(3,node1)
              w1         = soln%qtavg(4,node1)
              pr1        = soln%qtavg(5,node1)
              velsq1     = u1**2 + v1**2 + w1**2
              cp1        = 2._dp*(pr1/p_inf - 1._dp)/cp_factor
              temp1      = gamma*pr1/rho1
              mach1      = sqrt((velsq1)/temp1)
              entropy1   = log((pr1/p_inf)/rho1**gamma)
              htot1      = pr1/rho1*gamma*xgm1 + 0.5_dp*(velsq1)
              etot1      = pr1/rho1*xgm1       + 0.5_dp*(velsq1)

              rho2       = soln%qtavg(1,node2)
              u2         = soln%qtavg(2,node2)
              v2         = soln%qtavg(3,node2)
              w2         = soln%qtavg(4,node2)
              pr2        = soln%qtavg(5,node2)
              velsq2     = u2**2 + v2**2 + w2**2
              cp2        = 2._dp*(pr2/p_inf - 1._dp)/cp_factor
              temp2      = gamma*pr2/rho2
              mach2      = sqrt((velsq2)/temp2)
              entropy2   = log((pr2/p_inf)/rho2**gamma)
              htot2      = pr2/rho2*gamma*xgm1 + 0.5_dp*(velsq2)
              etot2      = pr2/rho2*xgm1       + 0.5_dp*(velsq2)

            case (incompressible)

              rho1       = 1.0_dp
              u1         = soln%qtavg(2,node1)
              v1         = soln%qtavg(3,node1)
              w1         = soln%qtavg(4,node1)
              pr1        = soln%qtavg(1,node1)
              cp1        = 2._dp*(pr1/p_inf - 1._dp)/cp_factor
              temp1      = 0._dp
              mach1      = 0._dp
              entropy1   = log(pr1/p_inf)

              rho2       = 1.0_dp
              u2         = soln%qtavg(2,node2)
              v2         = soln%qtavg(3,node2)
              w2         = soln%qtavg(4,node2)
              pr2        = soln%qtavg(1,node2)
              cp2        = 2._dp*(pr2/p_inf - 1._dp)/cp_factor
              temp2      = 0._dp
              mach2      = 0._dp
              entropy2   = log(pr2/p_inf)

            case (generic_gas)

!             no cc gen gas yet (?)

            case default
              ! It is already iitialized
          end select

          qcriterion1       = 0.0_dp
          qcriterion2       = 0.0_dp

          hrles_blend1      = my_1  ! pure RANS
          hrles_blend2      = my_1

          turbfluctuations1 = 0.0_dp
          turbfluctuations2 = 0.0_dp

        else node_or_cell_centered                     ! node-centered

          vort_x1    = soln%grady(4,node1)-soln%gradz(3,node1)
          vort_y1    = soln%gradz(2,node1)-soln%gradx(4,node1)
          vort_z1    = soln%gradx(3,node1)-soln%grady(2,node1)
          vort_mag1  = sqrt(vort_x1*vort_x1+vort_y1*vort_y1+vort_z1*vort_z1)

          vort_x2    = soln%grady(4,node2)-soln%gradz(3,node2)
          vort_y2    = soln%gradz(2,node2)-soln%gradx(4,node2)
          vort_z2    = soln%gradx(3,node2)-soln%grady(2,node2)
          vort_mag2  = sqrt(vort_x2*vort_x2+vort_y2*vort_y2+vort_z2*vort_z2)

          div_vel1   = soln%gradx(2,node1) + soln%grady(3,node1)               &
                     + soln%gradz(4,node1)

          div_vel2   = soln%gradx(2,node2) + soln%grady(3,node2)               &
                     + soln%gradz(4,node2)

          res1_1     = soln%res(1,node1)
          res2_1     = soln%res(2,node1)
          res3_1     = soln%res(3,node1)
          res4_1     = soln%res(4,node1)

          res1_2     = soln%res(1,node2)
          res2_2     = soln%res(2,node2)
          res3_2     = soln%res(3,node2)
          res4_2     = soln%res(4,node2)

          select case (n_turb)

            case(1)

              turb1_1   = soln%turb(1,node1)
              turb2_1   = 0.0_dp
              mu_t1     = soln%amut(node1)
              turres1_1 = soln%turbres(1,node1)

              turb1_2   = soln%turb(1,node2)
              turb2_2   = 0.0_dp
              mu_t2     = soln%amut(node2)
              turres1_2 = soln%turbres(1,node2)

            case(2)

              turb1_1   = soln%turb(1,node1)
              turb2_1   = soln%turb(2,node1)
              mu_t1     = soln%amut(node1)
              turres1_1 = soln%turbres(1,node1)
              turres2_1 = soln%turbres(2,node1)

              turb1_2   = soln%turb(1,node2)
              turb2_2   = soln%turb(2,node2)
              mu_t2     = soln%amut(node2)
              turres1_2 = soln%turbres(1,node2)
              turres2_2 = soln%turbres(2,node2)

            case(3)

              turb1_1   = soln%turb(1,node1)
              turb2_1   = soln%turb(2,node1)
              turb3_1   = soln%turb(3,node1)
              mu_t1     = soln%amut(node1)
              turres1_1 = soln%turbres(1,node1)
              turres2_1 = soln%turbres(2,node1)
              turres3_1 = soln%turbres(3,node1)

              turb1_2   = soln%turb(1,node2)
              turb2_2   = soln%turb(2,node2)
              turb3_2   = soln%turb(3,node2)
              mu_t2     = soln%amut(node2)
              turres1_2 = soln%turbres(1,node2)
              turres2_2 = soln%turbres(2,node2)
              turres3_2 = soln%turbres(3,node2)

            case(4)

              turb1_1   = soln%turb(1,node1)
              turb2_1   = soln%turb(2,node1)
              turb3_1   = soln%turb(3,node1)
              turb4_1   = soln%turb(4,node1)
              mu_t1     = soln%amut(node1)
              turres1_1 = soln%turbres(1,node1)
              turres2_1 = soln%turbres(2,node1)
              turres3_1 = soln%turbres(3,node1)
              turres4_1 = soln%turbres(4,node1)

              turb1_2   = soln%turb(1,node2)
              turb2_2   = soln%turb(2,node2)
              turb3_2   = soln%turb(3,node2)
              turb4_2   = soln%turb(4,node2)
              mu_t2     = soln%amut(node2)
              turres1_2 = soln%turbres(1,node2)
              turres2_2 = soln%turbres(2,node2)
              turres3_2 = soln%turbres(3,node2)
              turres4_2 = soln%turbres(4,node2)

            case(7)

              turb1_1   = soln%turb(1,node1)
              turb2_1   = soln%turb(2,node1)
              turb3_1   = soln%turb(3,node1)
              turb4_1   = soln%turb(4,node1)
              turb5_1   = soln%turb(5,node1)
              turb6_1   = soln%turb(6,node1)
              turb7_1   = soln%turb(7,node1)
              mu_t1     = soln%amut(node1)
              turres1_1 = soln%turbres(1,node1)
              turres2_1 = soln%turbres(2,node1)
              turres3_1 = soln%turbres(3,node1)
              turres4_1 = soln%turbres(4,node1)
              turres5_1 = soln%turbres(5,node1)
              turres6_1 = soln%turbres(6,node1)
              turres7_1 = soln%turbres(7,node1)

              turb1_2   = soln%turb(1,node2)
              turb2_2   = soln%turb(2,node2)
              turb3_2   = soln%turb(3,node2)
              turb4_2   = soln%turb(4,node2)
              turb5_2   = soln%turb(5,node2)
              turb6_2   = soln%turb(6,node2)
              turb7_2   = soln%turb(7,node2)
              mu_t2     = soln%amut(node2)
              turres1_2 = soln%turbres(1,node2)
              turres2_2 = soln%turbres(2,node2)
              turres3_2 = soln%turbres(3,node2)
              turres4_2 = soln%turbres(4,node2)
              turres5_2 = soln%turbres(5,node2)
              turres6_2 = soln%turbres(6,node2)
              turres7_2 = soln%turbres(7,node2)

            case default

              turb1_1   = 0.0_dp
              turb2_1   = 0.0_dp
              turb3_1   = 0.0_dp
              turb4_1   = 0.0_dp
              turb5_1   = 0.0_dp
              turb6_1   = 0.0_dp
              turb7_1   = 0.0_dp
              mu_t1     = 0.0_dp
              turres1_1 = 0.0_dp
              turres2_1 = 0.0_dp
              turres3_1 = 0.0_dp
              turres4_1 = 0.0_dp
              turres5_1 = 0.0_dp
              turres6_1 = 0.0_dp
              turres7_1 = 0.0_dp

              turb1_2   = 0.0_dp
              turb2_2   = 0.0_dp
              turb3_2   = 0.0_dp
              turb4_2   = 0.0_dp
              turb5_2   = 0.0_dp
              turb6_2   = 0.0_dp
              turb7_2   = 0.0_dp
              mu_t2     = 0.0_dp
              turres1_2 = 0.0_dp
              turres2_2 = 0.0_dp
              turres3_2 = 0.0_dp
              turres4_2 = 0.0_dp
              turres5_2 = 0.0_dp
              turres6_2 = 0.0_dp
              turres7_2 = 0.0_dp

          end select

          select case(soln%eqn_set)

            case (compressible)

              rho1         = soln%q_dof(1,node1)
              u1           = soln%q_dof(2,node1)
              v1           = soln%q_dof(3,node1)
              w1           = soln%q_dof(4,node1)
              pr1          = soln%q_dof(5,node1)
              velsq1       = u1**2 + v1**2 + w1**2
              cp1          = 2._dp*(pr1/p_inf - 1._dp)/cp_factor
              temp1        = gamma*pr1/rho1
              mach1        = sqrt((u1**2+v1**2+w1**2)/temp1)
              entropy1     = log((pr1/p_inf)/rho1**gamma)
              res5_1       = soln%res(5,node1)
              htot1        = pr1/rho1*gamma*xgm1 + 0.5_dp*(velsq1)
              etot1        = pr1/rho1*xgm1       + 0.5_dp*(velsq1)

              rho2         = soln%q_dof(1,node2)
              u2           = soln%q_dof(2,node2)
              v2           = soln%q_dof(3,node2)
              w2           = soln%q_dof(4,node2)
              pr2          = soln%q_dof(5,node2)
              velsq1       = u2**2 + v2**2 + w2**2
              cp2          = 2._dp*(pr2/p_inf - 1._dp)/cp_factor
              temp2        = gamma*pr2/rho2
              mach2        = sqrt((u2**2+v2**2+w2**2)/temp2)
              entropy2     = log((pr2/p_inf)/rho1**gamma)
              res5_2       = soln%res(5,node2)
              htot2        = pr2/rho2*gamma*xgm1 + 0.5_dp*(velsq2)
              etot2        = pr2/rho2*xgm1       + 0.5_dp*(velsq2)

!             time average and time rms data only for compressible path

              if (itime_avg /= 0) then

                rho_tavg1  = soln%q_time_avg(1,node1)
                u_tavg1    = soln%q_time_avg(2,node1)
                v_tavg1    = soln%q_time_avg(3,node1)
                w_tavg1    = soln%q_time_avg(4,node1)
                p_tavg1    = soln%q_time_avg(5,node1)
                rho_trms1  = sqrt(abs(soln%q_time_avg(6,node1)  - rho_tavg1**2))
                u_trms1    = sqrt(abs(soln%q_time_avg(7,node1)  - u_tavg1**2))
                v_trms1    = sqrt(abs(soln%q_time_avg(8,node1)  - v_tavg1**2))
                w_trms1    = sqrt(abs(soln%q_time_avg(9,node1)  - w_tavg1**2))
                p_trms1    = sqrt(abs(soln%q_time_avg(10,node1) - p_tavg1**2))

                rho_tavg2  = soln%q_time_avg(1,node2)
                u_tavg2    = soln%q_time_avg(2,node2)
                v_tavg2    = soln%q_time_avg(3,node2)
                w_tavg2    = soln%q_time_avg(4,node2)
                p_tavg2    = soln%q_time_avg(5,node2)
                rho_trms2  = sqrt(abs(soln%q_time_avg(6,node2)  - rho_tavg2**2))
                u_trms2    = sqrt(abs(soln%q_time_avg(7,node2)  - u_tavg2**2))
                v_trms2    = sqrt(abs(soln%q_time_avg(8,node2)  - v_tavg2**2))
                w_trms2    = sqrt(abs(soln%q_time_avg(9,node2)  - w_tavg2**2))
                p_trms2    = sqrt(abs(soln%q_time_avg(10,node2) - p_tavg2**2))

              end if

            case (incompressible)

              rho1         = 1._dp
              u1           = soln%q_dof(2,node1)
              v1           = soln%q_dof(3,node1)
              w1           = soln%q_dof(4,node1)
              pr1          = soln%q_dof(1,node1)
              cp1          = 2._dp*(pr1/p_inf - 1._dp)/cp_factor
              temp1        = 0._dp
              mach1        = 0._dp
              entropy1     = log(pr1/p_inf)
              res5_1       = 0._dp

              rho2         = 1._dp
              u2           = soln%q_dof(2,node2)
              v2           = soln%q_dof(3,node2)
              w2           = soln%q_dof(4,node2)
              pr2          = soln%q_dof(1,node2)
              cp2          = 2._dp*(pr2/p_inf - 1._dp)/cp_factor
              temp2        = 0._dp
              mach2        = 0._dp
              entropy2     = log(pr2/p_inf)
              res5_2       = 0._dp

            case (generic_gas)

              do ns = 1,n_species
                 rho_i1(ns) = soln%q_dof(ns,node1)
              end do
              rho1        = soln%q_dof(n_density,node1)
              u1          = soln%q_dof(n_momx,node1) / rho1
              v1          = soln%q_dof(n_momy,node1) / rho1
              w1          = soln%q_dof(n_momz,node1) / rho1
              pr1         = soln%q_dof(n_pressure_k(1),node1)
              tt1         = soln%q_dof(n_temperature_j(1),node1)
              if (n_energy > 1) then
                tv1       = soln%q_dof(n_temperature_j(2),node1)
                ev1       = soln%q_dof(n_energy_last,node1)
              end if
              etot1       = soln%q_dof(n_etot,node1) / rho1
              htot1       = ( soln%q_dof(n_etot,node1) + pr1 ) / rho1
              sonic1      = soln%q_dof(n_sonic_k(1),node1)
              mol_wt1     = soln%q_dof(n_molecular_weight,node1)
              cp1         = 2.0_dp*(pr1-1.0_dp)
              temp1       = 0._dp
              mach1       = sqrt( soln%q_dof(n_momx,node1)**2                  &
                                 +soln%q_dof(n_momy,node1)**2                  &
                                 +soln%q_dof(n_momz,node1)**2 )                &
                          / ( soln%q_dof(n_density,node1)                      &
                             *soln%q_dof(n_sonic_k(1),node1) )

              entropy1    = 0._dp
              res5_1      = 0._dp  ! to set a default; meaningless, really

              do ns = 1,n_species
                 rho_i2(ns) = soln%q_dof(ns,node2)
              end do
              rho2        = soln%q_dof(n_density,node2)
              u2          = soln%q_dof(n_momx,node2) / rho2
              v2          = soln%q_dof(n_momy,node2) / rho2
              w2          = soln%q_dof(n_momz,node2) / rho2
              pr2         = soln%q_dof(n_pressure_k(1),node2)
              tt2         = soln%q_dof(n_temperature_j(1),node2)
              if (n_energy > 1) then
                tv2       = soln%q_dof(n_temperature_j(2),node2)
                ev2       = soln%q_dof(n_energy_last,node2)
              end if
              etot2       = soln%q_dof(n_etot,node2) / rho2
              htot2       = ( soln%q_dof(n_etot,node2) + pr2 ) / rho2
              sonic2      = soln%q_dof(n_sonic_k(1),node2)
              mol_wt2     = soln%q_dof(n_molecular_weight,node2)
              cp2         = 2.0_dp*(pr2-1.0_dp)
              temp2       = 0._dp
              mach2       = sqrt( soln%q_dof(n_momx,node2)**2                  &
                                 +soln%q_dof(n_momy,node2)**2                  &
                                 +soln%q_dof(n_momz,node2)**2 )                &
                          / ( soln%q_dof(n_density,node2)                      &
                             *soln%q_dof(n_sonic_k(1),node2) )
              entropy2    = 0._dp
              res5_2      = 0._dp  ! to set a default; meaningless, really

            case default
              ! It is already initialized
          end select

          qcriterion1       = 0.0_dp
          qcriterion2       = 0.0_dp

          turbfluctuations1 = 0.0_dp
          turbfluctuations2 = 0.0_dp

          hrles_blend1      = my_1  ! pure RANS
          hrles_blend2      = my_1

          if (need_gradients) then
            qcriterion1 = q_criterion(soln%eqn_set, soln%n_grd,                &
                                      soln%gradx(:,node1),                     &
                                      soln%grady(:,node1),                     &
                                      soln%gradz(:,node1))
            qcriterion2 = q_criterion(soln%eqn_set, soln%n_grd,                &
                                      soln%gradx(:,node2),                     &
                                      soln%grady(:,node2),                     &
                                      soln%gradz(:,node2))
          end if

          if (need_gradients .and. need_slen) then
            hrles_blend1 = hrles_blend(soln%eqn_set, soln%n_tot, soln%n_grd,   &
                                       slen1,                                  &
                                       soln%q_dof(:,node1),                    &
                                       soln%turb(:,node1),                     &
                                       soln%gradx(:,node1),                    &
                                       soln%grady(:,node1),                    &
                                       soln%gradz(:,node1))
            hrles_blend2 = hrles_blend(soln%eqn_set, soln%n_tot, soln%n_grd,   &
                                       slen2,                                  &
                                       soln%q_dof(:,node2),                    &
                                       soln%turb(:,node2),                     &
                                       soln%gradx(:,node2),                    &
                                       soln%grady(:,node2),                    &
                                       soln%gradz(:,node2))
          end if

          if (need_turb_variables) then
              turbfluctuations1 =                                              &
                turb_fluctuations(soln%n_grd, soln%n_tot, soln%n_turb,         &
                                  soln%eqn_set,                                &
                                  soln%q_dof(:,node1), soln%amut(node1),       &
                                  soln%gradx(:,node1), soln%grady(:,node1),    &
                                  soln%gradz(:,node1), soln%turb(:,node1))
              turbfluctuations2 =                                              &
                turb_fluctuations(soln%n_grd, soln%n_tot, soln%n_turb,         &
                                  soln%eqn_set,                                &
                                  soln%q_dof(:,node2), soln%amut(node2),       &
                                  soln%gradx(:,node2), soln%grady(:,node2),    &
                                  soln%gradz(:,node2), soln%turb(:,node2))
          end if

        end if node_or_cell_centered

        output_var_loop:    do nn = 1,n_output_variables

          if ( soln%eqn_set == generic_gas ) then
            if (n_species > 1) then
              nspecies: do ns = 1,n_species
                species_name = 'rho_'//trim(spec_propv(ns)%species)
                if (output_variables(nn) == species_name) then
                  temp_vec(nn) = rho_i1(ns)*weight1 + rho_i2(ns)*weight2
                end if
              end do nspecies
            end if
          end if

          select case(trim(adjustl(output_variables(nn))))

            case('x')
              temp_vec(nn) = x1*weight1 + x2*weight2

            case('y')
              temp_vec(nn) = y1*weight1 + y2*weight2

            case('z')
              temp_vec(nn) = z1*weight1 + z2*weight2

            case('processor_id')
              temp_vec(nn) = proc_id1*weight1 + proc_id2*weight2

            case('rho')
              temp_vec(nn) = rho1*weight1 + rho2*weight2

            case('u')
              temp_vec(nn) = u1*weight1 + u2*weight2

            case('v')
              temp_vec(nn) = v1*weight1 + v2*weight2

            case('w')
              temp_vec(nn) = w1*weight1 + w2*weight2

            case('p')
              temp_vec(nn) = pr1*weight1 + pr2*weight2

            case('lambda1')
              temp_vec(nn) = lambda(1,1)*weight1 + lambda(1,2)*weight2

            case('lambda2')
              temp_vec(nn) = lambda(2,1)*weight1 + lambda(2,2)*weight2

            case('lambda3')
              temp_vec(nn) = lambda(3,1)*weight1 + lambda(3,2)*weight2

            case('lambda4')
              temp_vec(nn) = lambda(4,1)*weight1 + lambda(4,2)*weight2

            case('lambda5')
              temp_vec(nn) = lambda(5,1)*weight1 + lambda(5,2)*weight2

            case('lambda6')
              temp_vec(nn) = lambda(6,1)*weight1 + lambda(6,2)*weight2

            case('lambda7')
              temp_vec(nn) = lambda(7,1)*weight1 + lambda(7,2)*weight2

            case('cp')
              temp_vec(nn) = cp1*weight1 + cp2*weight2

            case('dp_pinf')
              temp_vec(nn) = (pr1*weight1 + pr2*weight2 - p_inf)/p_inf

            case('tt')
              temp_vec(nn) = tt1*weight1 + tt2*weight2

            case('tv')
              if (n_energy > 1) then
                temp_vec(nn) = tv1*weight1 + tv2*weight2
              end if

            case('etot')
              temp_vec(nn) = etot1*weight1 + etot2*weight2

            case('htot')
              temp_vec(nn) = htot1*weight1 + htot2*weight2

            case('ev')
              if (n_energy > 1) then
                temp_vec(nn) = ev1*weight1 + ev2*weight2
              end if

            case('mixture_mol_weight')
              temp_vec(nn) = mol_wt1*weight1 + mol_wt2*weight2

            case('sonic')
              temp_vec(nn) = sonic1*weight1 + sonic2*weight2

            case('vort_x')
              temp_vec(nn) = vort_x1*weight1 + vort_x2*weight2

            case('vort_y')
              temp_vec(nn) = vort_y1*weight1 + vort_y2*weight2

            case('vort_z')
              temp_vec(nn) = vort_z1*weight1 + vort_z2*weight2

            case('vort_mag')
              temp_vec(nn) = vort_mag1*weight1 + vort_mag2*weight2

            case('div_vel')
              temp_vec(nn) = div_vel1*weight1 + div_vel2*weight2

            case('q_criterion')
              temp_vec(nn) = qcriterion1*weight1 + qcriterion2*weight2

            case('mach')
              temp_vec(nn) = mach1*weight1 + mach2*weight2

            case('temperature')
              temp_vec(nn) = temp1*weight1 + temp2*weight2

            case('iblank')
!             blank the interpolated node if either donor node is blanked
              temp_vec(nn) = real(min(blank1, blank2), dp)

            case('imesh')
              temp_vec(nn) = real(min(mesh1, mesh2), dp)

            case('entropy')
              temp_vec(nn) = entropy1*weight1 + entropy2*weight2

            case('mu_t')
              temp_vec(nn) = mu_t1*weight1 + mu_t2*weight2

            case('mu_t_ratio')
              temp1        = gamma*pr1/rho1
              temp2        = gamma*pr2/rho2
              mu_lam1      = viscosity_law( cstar, temp1 )
              mu_lam2      = viscosity_law( cstar, temp2 )
              temp_vec(nn) = (mu_t1/mu_lam1)*weight1 + (mu_t2/mu_lam2)*weight2

            case ('uuprime')
              temp_vec(nn) = turbfluctuations1(1)*weight1                      &
                           + turbfluctuations2(1)*weight2

            case ('vvprime')
              temp_vec(nn) = turbfluctuations1(2)*weight1                      &
                           + turbfluctuations2(2)*weight2

            case ('wwprime')
              temp_vec(nn) = turbfluctuations1(3)*weight1                      &
                           + turbfluctuations2(3)*weight2

            case ('uvprime')
              temp_vec(nn) = turbfluctuations1(4)*weight1                      &
                           + turbfluctuations2(4)*weight2

            case ('uwprime')
              temp_vec(nn) = turbfluctuations1(5)*weight1                      &
                           + turbfluctuations2(5)*weight2

            case ('vwprime')
              temp_vec(nn) = turbfluctuations1(6)*weight1                      &
                           + turbfluctuations2(6)*weight2

            case ('slen')
              temp_vec(nn) = slen1*weight1 + slen2*weight2

            case ('turb1')
              temp_vec(nn) = turb1_1*weight1 + turb1_2*weight2

            case ('turb2')
              temp_vec(nn) = turb2_1*weight1 + turb2_2*weight2

            case ('turb3')
              temp_vec(nn) = turb3_1*weight1 + turb3_2*weight2

            case ('turb4')
              temp_vec(nn) = turb4_1*weight1 + turb4_2*weight2

            case ('turb5')
              temp_vec(nn) = turb5_1*weight1 + turb5_2*weight2

            case ('turb6')
              temp_vec(nn) = turb6_1*weight1 + turb6_2*weight2

            case ('turb7')
              temp_vec(nn) = turb7_1*weight1 + turb7_2*weight2

            case ('volume')
              temp_vec(nn) = vol1*weight1 + vol2*weight2

            case ('res1')
              temp_vec(nn) = res1_1*weight1 + res1_2*weight2

            case ('res2')
              temp_vec(nn) = res2_1*weight1 + res2_2*weight2

            case ('res3')
              temp_vec(nn) = res3_1*weight1 + res3_2*weight2

            case ('res4')
              temp_vec(nn) = res4_1*weight1 + res4_2*weight2

            case ('res5')
              temp_vec(nn) = res5_1*weight1 + res5_2*weight2

            case ('turres1')
              temp_vec(nn) = turres1_1*weight1 + turres1_2*weight2

            case ('turres2')
              temp_vec(nn) = turres2_1*weight1 + turres2_2*weight2

            case ('turres3')
              temp_vec(nn) = turres3_1*weight1 + turres3_2*weight2

            case ('turres4')
              temp_vec(nn) = turres4_1*weight1 + turres4_2*weight2

            case ('turres5')
              temp_vec(nn) = turres5_1*weight1 + turres5_2*weight2

            case ('turres6')
              temp_vec(nn) = turres6_1*weight1 + turres6_2*weight2

            case ('turres7')
              temp_vec(nn) = turres7_1*weight1 + turres7_2*weight2

            case ('res_gcl')
              temp_vec(nn) = res_gcl1*weight1 + res_gcl2*weight2

            case ('rho_tavg')
              temp_vec(nn) = rho_tavg1*weight1 + rho_tavg2*weight2

            case ('u_tavg')
              temp_vec(nn) = u_tavg1*weight1 + u_tavg2*weight2

            case ('v_tavg')
              temp_vec(nn) = v_tavg1*weight1 + v_tavg2*weight2

            case ('w_tavg')
              temp_vec(nn) = w_tavg1*weight1 + w_tavg2*weight2

            case ('p_tavg')
              temp_vec(nn) = p_tavg1*weight1 + p_tavg2*weight2

            case ('rho_trms')
              temp_vec(nn) = rho_trms1*weight1 + rho_trms2*weight2

            case ('u_trms')
              temp_vec(nn) = u_trms1*weight1 + u_trms2*weight2

            case ('v_trms')
              temp_vec(nn) = v_trms1*weight1 + v_trms2*weight2

            case ('w_trms')
              temp_vec(nn) = w_trms1*weight1 + w_trms2*weight2

            case ('p_trms')
              temp_vec(nn) = p_trms1*weight1 + p_trms2*weight2

            case ('iflagslen')
              temp_vec(nn) = iflagslen1*weight1 + iflagslen2*weight2

            case ('cgamma')
              temp_vec(nn) = cgamma1*weight1 + cgamma2*weight2

            case ('aspect_ratio')
              temp_vec(nn) = aspect_ratio1*weight1 + aspect_ratio2*weight2

            case ('skip_q')
              temp_vec(nn) = skip_q1*weight1 + skip_q2*weight2

            case('hrles_blend')
              temp_vec(nn) = hrles_blend1*weight1 + hrles_blend2*weight2

            case default

              write(*,*) 'unknown tecplot volume variable (gather edge): ',    &
                         trim(adjustl(output_variables(nn)))

          end select

        end do output_var_loop

        do nn = 1,n_output_variables
          edge_data(nn,edge) = temp_vec(nn)
        end do
        local_node1( 1, edge ) = grid%l2g(node1)
        local_node2( 1, edge ) = grid%l2g(node2)

    end do loop_edges

    deallocate ( temp_vec )

    if ( aspect_ratio_needed ) deallocate(ar)

  end subroutine gather_edge_data


!----------------------------GATHER_POINT_DATA--------------------------------80
!
!  locate point sampling in mesh and calculate data
!
!-----------------------------------------------------------------------------80
  subroutine gather_point_data( grid, soln, ivol, n_output_variables,          &
              output_variables, point_data, sadj )

    use fluid,            only : gamma, sutherland_constant
    use grid_types,       only : grid_type
    use info_depr,        only : cc_primal, tref, xmach, re, alpha, yaw, twod
    use lmpi,             only : lmpi_bcast, lmpi_max, lmpi_die
    use solution_types,   only : soln_type, compressible, incompressible,      &
                                 generic_gas
    use turb_kw_const,    only : betastar, ctime, cmu_0, sig_w2
    use turbulence_info,  only : model_strain_form_int, prodk_form_int

    use turb_kw_const,    only : angular_velocity, betastar_0_w98
    use geometry_utils,   only : get_normal, element_stats
    use sampling_funclib, only : face_stats, trace_a
    use sampling_headers, only : have_points, number_of_points,                &
                                 p2c_local_cells, p2c_local_bdy,               &
                                 p2c_local_elem, p2c_local_face,               &
                                 verbose, max_node, point_is_found, sample,    &
                                 need_turb_variables, u_tau, rnuw,             &
                                 need_wall_data, boundary_point, boundary,     &
                                 sampling_final_write, re_tau, cf
    use solution_adj,     only : sadj_type
    use compute_stress,   only : get_bij, get_rhotauij
    use turb_functions,   only : get_cmu_star
    use turb_functions,   only : smirnov_cc_term
    use turb_functions,   only : get_production_k, get_destruction_k           &
                               , cross_diffusion_kw98
!   use twod_util,        only : yplane_2d, y_coplanar_tol
    use twod_util,        only : copy_array_2d
    use turb_util,        only : kloc, wloc
    use cell_stats,       only : get_cell_dsdt, get_cell_transport             &
                               , get_cell_diffusion                            &
                               , get_time_averaged_vorticity
    use turbulence_info,  only : turbulence_model_int,                         &
                                 wilcox_asm, easm_ddes, EASMko2003_S,          &
                                 wilcox_kw88, wilcox_kw98, wilcox_kw06,        &
                                         kw_lag, abid_linear,                  &
                                 k_kL_MEAH2013, WilcoxRSM_w2006,               &
                                 WilcoxRSM_w2006c,SSGLRR_RSM_w2012_SD,         &
                                 SSGLRR_RSM_w2012, sst_kkl
    use generic_gas_map,  only : n_density, n_temperature_j, n_momx, n_momy,   &
                                 n_momz, n_species, n_pressure_k, n_energy,    &
                                 n_energy_last, n_molecular_weight, n_sonic_k, &
                                 n_etot, n_amu_k
    use turb_gen,         only : n_amu_t, n_turb_ke, n_dis_nutl, n_turb_eq
    use nml_time_avg_params, only : itime_avg, tavg_header_version

    type(grid_type),               intent(in ) :: grid
    type(soln_type),               intent(inout ) :: soln
    type(sadj_type),     optional, intent(in ) :: sadj
    integer,                       intent(in ) :: ivol
    integer,                       intent(in ) :: n_output_variables
    character(80), dimension(:),   intent(in ) :: output_variables
    real(dp),      dimension(:,:), intent(out) :: point_data

    character(80) :: geo_name

    integer :: ipts
    integer :: ielem
    integer :: cell
    integer :: face
    integer :: node
    integer :: cell_node
    integer :: face_node
    integer :: element_set
    integer :: node_per_cf
    integer :: node_per_cell
    integer :: node_per_face
    integer :: cf_index
    integer :: corner
    integer :: ib
    integer :: nn

    real(dp), dimension(6)   :: turbfluctuations
    real(dp), dimension(3,3) :: gradv
    real(dp), dimension(3,3) :: sij
    real(dp), dimension(3,3) :: wij
    real(dp), dimension(3,3) :: wji
    real(dp), dimension(3,3) :: s2
    real(dp), dimension(3,3) :: w2
    real(dp), dimension(3,3) :: bij
    real(dp), dimension(3,3) :: bs
    real(dp), dimension(3,3) :: bb
    real(dp), dimension(3,3) :: bbb
    real(dp), dimension(3,3) :: tauij
!   real(dp)                 :: eta1s, eta2s
!   real(dp), dimension(3,3) :: tau_lam

! node data
    real(dp), dimension(0:max_node) :: px, py, pz
    real(dp), dimension(5,max_node) :: qp
    real(dp), dimension(:,:), allocatable  :: rho_i_p
    real(dp), dimension(7,max_node) :: q_rg_p
    real(dp), dimension(soln%n_turb,max_node) :: qt
    real(dp), dimension(5,max_node) :: qtavg
    real(dp), dimension(6,max_node) :: qqtavg
    real(dp), dimension(5,max_node) :: qtrms
    real(dp), dimension(soln%n_turb,max_node) :: turres
    real(dp), dimension(1,max_node) :: mut
    real(dp), dimension(6,max_node) :: turb_fluct_f
    real(dp), dimension(3,max_node) :: gradrho_f
    real(dp), dimension(9,max_node) :: gradients_f
    real(dp), dimension(3,max_node) :: gradk_f
    real(dp), dimension(3,max_node) :: gradw_f
    real(dp), dimension(1,max_node) :: s_len_f
    real(dp), dimension(1,max_node) :: volume_f
    real(dp), dimension(1,max_node) :: iflagslen
    real(dp), dimension(1,max_node) :: q_crit_f
    real(dp), dimension(1,max_node) :: blank_f
    real(dp), dimension(1,max_node) :: mesh_f
    real(dp), dimension(1,max_node) :: hrles_f
    real(dp), dimension(1,max_node) :: vortx_f
    real(dp), dimension(1,max_node) :: vorty_f
    real(dp), dimension(1,max_node) :: vortz_f
    real(dp), dimension(1,max_node) :: divvel_f
    real(dp), dimension(5,max_node) :: qres
    real(dp), dimension(1,max_node) :: res_gcl_f
    real(dp), dimension(1,max_node) :: c_gamma_f
    real(dp), dimension(1,max_node) :: aspect_f
    real(dp), dimension(1,max_node) :: skip_q_f
    real(dp), dimension(soln%adim,max_node) :: lambdap
    real(dp), dimension(1,max_node) :: global_node_f
    real(dp), dimension(6,max_node) :: tau_sgs_f
    real(dp), dimension(1,max_node) :: mu_t_tavg_f
    real(dp), dimension(1,max_node) :: vort_mag_tavg_f
    real(dp), dimension(1,max_node) :: vort_x_tavg_f
    real(dp), dimension(1,max_node) :: vort_y_tavg_f
    real(dp), dimension(1,max_node) :: vort_z_tavg_f
    real(dp), dimension(soln%n_grd,max_node) :: reconstruction_limiter_phi_f

! interpolated data
    real(dp), dimension(5 )        :: q_point
    real(dp), dimension(soln%n_turb)  :: q_turb
    real(dp), dimension(5 )        :: q_tavg
    real(dp), dimension(5 )        :: q_trms
    real(dp), dimension(soln%n_turb)  :: turb_res
    real(dp), dimension(1 )        :: mu_t
    real(dp), dimension(6 )        :: turb_fluct
    real(dp), dimension(3 )        :: gradrho
    real(dp), dimension(9 )        :: gradients
    real(dp), dimension(3 )        :: gradk
    real(dp), dimension(3 )        :: gradw
    real(dp), dimension(6 )        :: tau_ij
    real(dp), dimension(6 )        :: b_ij
    real(dp), dimension(6 )        :: pk_ij
    real(dp), dimension(6 )        :: tau_sgs
    real(dp), dimension(1 )        :: s_len
    real(dp), dimension(1 )        :: volume
    real(dp), dimension(1 )        :: iflag_s_len
    real(dp), dimension(1 )        :: q_crit
    real(dp), dimension(1 )        :: hrles_bf
    real(dp), dimension(1 )        :: vort_x
    real(dp), dimension(1 )        :: vort_y
    real(dp), dimension(1 )        :: vort_z
    real(dp), dimension(1 )        :: div_vel
    real(dp), dimension(5 )        :: q_res
    real(dp), dimension(1 )        :: res_gcl
    real(dp), dimension(1 )        :: c_gamma
    real(dp), dimension(1 )        :: aspect_r_f
    real(dp), dimension(1 )        :: skip_q
    real(dp), dimension(1 )        :: c_q
    real(dp), dimension(1 )        :: u_avg
    real(dp), dimension(1 )        :: v_avg
    real(dp), dimension(1 )        :: w_avg
    real(dp), dimension(1 )        :: c_f
    real(dp), dimension(1 )        :: c_fx
    real(dp), dimension(1 )        :: c_fy
    real(dp), dimension(1 )        :: c_fz
    real(dp), dimension(1 )        :: y_plus
    real(dp), dimension(soln%adim) :: lambda_point
    real(dp), dimension(:), allocatable  :: rho_i
    real(dp), dimension(7 )        :: q_rg
    real(dp), dimension(1 )        :: sw_f
    real(dp), dimension(1 )        :: pw_f
    real(dp), dimension(1 )        :: pv_f
    real(dp), dimension(1 )        :: qv_f
    real(dp), dimension(1 )        :: rv_f
    real(dp), dimension(1 )        :: u_plus
    real(dp), dimension(1 )        :: k_plus
    real(dp), dimension(1 )        :: w_plus
    real(dp), dimension(1 )        :: t11_plus
    real(dp), dimension(1 )        :: t12_plus
    real(dp), dimension(1 )        :: t13_plus
    real(dp), dimension(1 )        :: t22_plus
    real(dp), dimension(1 )        :: t23_plus
    real(dp), dimension(1 )        :: t33_plus
    real(dp), dimension(1 )        :: cmu_star
    real(dp), dimension(1 )        :: eta1s
    real(dp), dimension(1 )        :: eta2s
    real(dp), dimension(1 )        :: tr_bs
    real(dp), dimension(1 )        :: iib
    real(dp), dimension(1 )        :: iiib
    real(dp), dimension(1 )        :: f_r1
    real(dp), dimension(1 )        :: sst_f1
    real(dp), dimension(1 )        :: xi_k
    real(dp), dimension(soln%n_grd):: reconstruction_limiter_phi
    real(dp), dimension(1 )        :: production_k
    real(dp), dimension(1 )        :: destruction_k
    real(dp), dimension(1 )        :: diffusion_k
    real(dp), dimension(1 )        :: transport_k
    real(dp), dimension(1 )        :: vgradrho
    real(dp), dimension(1 )        :: bird_breakdown
    real(dp), dimension(1 )        :: mu_t_tavg
    real(dp), dimension(1 )        :: vort_mag_tavg
    real(dp), dimension(1 )        :: vort_x_tavg
    real(dp), dimension(1 )        :: vort_y_tavg
    real(dp), dimension(1 )        :: vort_z_tavg
    real(dp), dimension(4 )        :: vort_rms


    real(dp), dimension(:), allocatable :: temp_vec
    character(len=80)                   :: var
    integer,  dimension(:), allocatable :: node_flag
    real(dp), dimension(3)              :: p1, p2, p3, p4
    real(dp), dimension(3)              :: n, n1
    integer                             :: node1, node2, node3, node4
    integer                             :: ncell, n_turb
    integer                             :: ns

    real(dp) :: uavg, vavg, wavg
    real(dp) :: uave, vave, wave, fact
    real(dp) :: uave_on,  vave_on,  wave_on
    real(dp) :: uave_off, vave_off, wave_off
    real(dp) :: total_weight_on
    real(dp) :: total_weight_off
    real(dp) :: xave_off
    real(dp) :: yave_off
    real(dp) :: zave_off
    real(dp) :: xave_on
    real(dp) :: yave_on
    real(dp) :: zave_on
    real(dp) :: tave_on
    real(dp) :: tave_off
    real(dp) :: temperature
    real(dp) :: heat_flux, rhow, rmu, cstar, u_tau_w, rnu_w
    real(dp) :: rmuw, amuw
    real(dp) :: dx, dy, dz
    real(dp) :: du, dv, dw, dun, dvn, dwn, dut, dvt, dwt
    real(dp) :: dtx, dty, dtz
    real(dp) :: dist, dudn, tau_wall
    real(dp) :: conv, uinf, vinf, winf
    real(dp) :: skin_fric, skin_fricx, skin_fricy, skin_fricz
    real(dp) :: uplus, yplus
    real(dp) :: temp
    real(dp) :: u_ratio
    real(dp) :: timescale
    real(dp) :: inner_timescale
    real(dp) :: outer_timescale
    real(dp) :: mu
    real(dp) :: nu
    real(dp) :: xmr
    real(dp) :: vort
    real(dp) :: enorm
    real(dp) :: ap, aq, ar
    real(dp) :: xlam1, xlam2, xlam3
    real(dp) :: tempxlam1, tempxlam2, tempxlam3
    real(dp) :: term, crossterm
    real(dp) :: arg, arg1, arg2, arg3, argt, arga
!   real(dp) :: c1c, c2c, c3c

!   real(dp) :: length_rans, length_wall, length_min, f2, factor, arg
    real(dp), dimension(3,3)  :: dsdt = 0.0_dp
!   real(dp), dimension(3,3)  :: w_total

    real(dp), parameter :: zero      = 0.0_dp
    real(dp), parameter :: limit_cd  = 1.0e-20_dp
    real(dp), parameter :: one       = 1.0_dp
    real(dp), parameter :: two       = 2.0_dp
    real(dp), parameter :: three     = 3.0_dp

    logical  :: use_tet

  continue

    n_turb           = soln%n_turb

    qtavg            = zero
    qqtavg           = zero
    qtrms            = zero
    turb_fluct_f     = zero
    volume_f         = zero
    blank_f          = zero
    mesh_f           = zero
    qres             = zero
    res_gcl_f        = zero
    c_gamma_f        = zero
    aspect_r_f       = zero
    skip_q_f         = zero
    vortx_f          = zero
    vorty_f          = zero
    vortz_f          = zero
    divvel_f         = zero
! FIXME
    aspect_f         = zero
    s_len_f          = zero

    uavg             = zero
    vavg             = zero
    wavg             = zero
    skin_fric        = zero
    skin_fricx       = zero
    skin_fricy       = zero
    skin_fricz       = zero
    yplus            = zero
    uplus            = zero
    u_tau            = -999.0d+0
    allocate(rho_i_p(n_species, max_node))
    allocate(rho_i  (n_species))
    rho_i_p          = zero
    rho_i            = zero
    q_rg             = zero
    outer_timescale  = zero
    inner_timescale  = zero

    xave_off         = zero
    yave_off         = zero
    zave_off         = zero
    xave_on          = zero
    yave_on          = zero
    zave_on          = zero

    rhow     = zero ! to satisfy a g95 compiler gripe
    rmuw     = zero ! to satisfy a g95 compiler gripe
    amuw     = zero ! to satisfy a g95 compiler gripe
    uave_on  = zero ! to satisfy a g95 compiler gripe
    vave_on  = zero ! to satisfy a g95 compiler gripe
    wave_on  = zero ! to satisfy a g95 compiler gripe
    uave_off = zero ! to satisfy a g95 compiler gripe
    vave_off = zero ! to satisfy a g95 compiler gripe
    wave_off = zero ! to satisfy a g95 compiler gripe
    vgradrho = zero
    bird_breakdown = zero
    mu_t_tavg   = zero ! to satisfy a g95 compiler gripe
    vort_mag_tavg   = zero ! to satisfy a g95 compiler gripe
    vort_x_tavg   = zero ! to satisfy a g95 compiler gripe
    vort_y_tavg   = zero ! to satisfy a g95 compiler gripe
    vort_z_tavg   = zero ! to satisfy a g95 compiler gripe
    vort_rms   = zero ! to satisfy a g95 compiler gripe
    reconstruction_limiter_phi = zero ! to satisfy a g95 compiler gripe
    production_k  = zero ! to satisfy a g95 compiler gripe
    destruction_k = zero ! to satisfy a g95 compiler gripe
    diffusion_k   = zero ! to satisfy a g95 compiler gripe
    transport_k   = zero ! to satisfy a g95 compiler gripe


    node          = 0 ! to satisfy a g95 compile gripe
    cell          = 0 ! to satisfy a g95 compile gripe
    element_set   = 0 ! to satisfy a g95 compile gripe
    ib            = 0 ! to satisfy a g95 compile gripe
    face          = 0 ! to satisfy a g95 compile gripe
    node_per_face = 0 ! to satisfy a g95 compile gripe
    ielem         = 0 ! to satisfy a g95 compile gripe


!   get freestream velocity components
    conv = 180.0_dp/acos(-1.0_dp)

    if ( soln%eqn_set == compressible ) then
      uinf =   xmach * cos(alpha/conv) * cos(yaw/conv)
      vinf = - xmach * sin(yaw/conv)
      winf =   xmach * sin(alpha/conv) * cos(yaw/conv)
      xmr = one
      if ( re > tiny(1._dp) ) xmr  = xmach / re
    else if ( soln%eqn_set == generic_gas ) then
      uinf =   cos(alpha/conv) * cos(yaw/conv)
      vinf = - sin(yaw/conv)
      winf =   sin(alpha/conv) * cos(yaw/conv)
      xmr = one
      if ( re > tiny(1._dp) ) xmr       = one / re
    else
      uinf =   cos(alpha/conv) * cos(yaw/conv)
      vinf = - sin(yaw/conv)
      winf =   sin(alpha/conv) * cos(yaw/conv)
      xmr = one
      if ( re > tiny(1._dp) ) xmr       = one / re
     end if

    if ( twod ) then
      call copy_array_2d( grid%nnodes01, grid%nnodes0_2d   &
           , grid%node_pairs_2d, soln%q_dof, size(soln%q_dof,1) )
      call copy_array_2d( grid%nnodes01, grid%nnodes0_2d   &
           , grid%node_pairs_2d, soln%turb, size(soln%turb,1) )
      call copy_array_2d( grid%nnodes01, grid%nnodes0_2d   &
           , grid%node_pairs_2d, soln%gradx, size(soln%gradx,1) )
      call copy_array_2d( grid%nnodes01, grid%nnodes0_2d   &
           , grid%node_pairs_2d, soln%grady, size(soln%grady,1) )
      call copy_array_2d( grid%nnodes01, grid%nnodes0_2d   &
           , grid%node_pairs_2d, soln%gradz, size(soln%gradz,1) )
      call copy_array_2d( grid%nnodes01, grid%nnodes0_2d   &
           , grid%node_pairs_2d, soln%phi, size(soln%phi,1) )

    endif

    compressible_temp : if ( soln%eqn_set == compressible ) then
      cstar = sutherland_constant/tref
    else
      cstar = 1.0_dp
    end if compressible_temp

!   n_grd        = soln%n_grd

    allocate(temp_vec(n_output_variables))

    geo_name = sample(ivol)%geo_type_name

    have_points(ivol) = .true.
    use_tet           = .false.

    if ( need_wall_data(ivol) ) then
      ib            = boundary(ivol)
      call get_face_index( grid, ib, ivol, face, node_per_face )

      if ( node_per_face ==  3 ) then
        ielem = grid%bc(ib)%f2ntb(face,5)
        if ( grid%elem(ielem)%type_cell == 'tet' ) use_tet = .true.
        write(6,'(a,5i5,1x,a,l2)') 'types  '                                   &
        , ib,face,node_per_face,grid%bc(ib)%nbfacet                            &
        , grid%bc(ib)%f2ntb(face,5) , grid%elem(ielem)%type_cell, use_tet
      endif

      if ( use_tet ) then
        ncell = size(grid%elem(ielem)%c2n,2)
        call u_tau_tet(soln%eqn_set, face, node_per_face                       &
                    , grid%bc(ib)%ibnode, grid%bc(ib)%f2ntb                    &
                    , grid%elem(ielem)%c2n                                     &
                    , grid%nnodes01, grid%x, grid%y, grid%z, soln%q_dof        &
                    , grid%bc(ib)%ibc,      soln%bcsoln(ib)%u_tau              &
                    , grid%bc(ib)%nbnode, grid%bc(ib)%nbfacet, ncell           &
                    , soln%n_tot, u_tau(ivol), rnuw(ivol)                      &
                    , sampling_final_write(ivol), re_tau(ivol), cf(ivol) )

      else

      call u_tau_mixed( soln%eqn_set, face, node_per_face,                     &
                   grid%bc(ib)%nbnode,   grid%bc(ib)%ibnode,                   &
                   grid%bc(ib)%nbfacet,  grid%bc(ib)%f2ntb,                    &
                   grid%bc(ib)%nbfaceq,  grid%bc(ib)%f2nqb,                    &
                   grid%nelem,           grid%elem,           grid%nnodes01,   &
                   grid%x,               grid%y,              grid%z,          &
                   soln%q_dof,                                                 &
                   grid%bc(ib)%ibc,      soln%bcsoln(ib)%u_tau,                &
                   soln%bcsoln(ib)%mu_t_wf,                                    &
                   soln%bcsoln(ib)%yplus_wf, soln%bcsoln(ib)%uplus_wf,         &
                   soln%bcsoln(ib)%omega_wf, soln%bcsoln(ib)%k_wf,             &
                   soln%n_tot, u_tau(ivol), rnuw(ivol),                        &
                   sampling_final_write(ivol), re_tau(ivol), cf(ivol) )
      endif




!     call get_u_tau( grid, soln, ivol, u_tau(ivol), rnuw(ivol) )
      call lmpi_max(u_tau(ivol),temp)
      call lmpi_bcast(temp)
       u_tau(ivol) = temp
      temp = zero
      call lmpi_max(rnuw(ivol),temp)
      call lmpi_bcast(temp)
        rnuw(ivol) = temp
      temp = zero
      call lmpi_max(re_tau(ivol),temp)
      call lmpi_bcast(temp)
      re_tau(ivol) = temp
    endif

    loop_ipts:  do ipts = 1, number_of_points(ivol)

      temp_vec   = 0.0_dp
      cf_index =  p2c_local_cells(ivol,ipts) ! cell/face index

      loop_cell_faces:  if ( cf_index /= 0 ) then

        px(0) = sample(ivol)%print_list(1,ipts)
        py(0) = sample(ivol)%print_list(2,ipts)
        pz(0) = sample(ivol)%print_list(3,ipts)

        !----------------- point type ----------------------------------------80
        !
        ! Set up node and cell information for data gathering
        !
        !---------------------------------------------------------------------80
        select case ( geo_name )
        case ( 'boundary_points' )
!  cell and element type associated the boundary face
          element_set = p2c_local_elem(ivol,ipts)
          face        = cf_index
          ib          = p2c_local_bdy(ivol,ipts)
          if      ( p2c_local_face(ivol,ipts) == 1 ) then
            node_per_cf = 3
            node_per_face = 3
            cell = grid%bc(ib)%f2ntb(face,node_per_face+1)
          else if ( p2c_local_face(ivol,ipts) == 2 ) then
            node_per_cf = 4
            node_per_face = 4
            cell = grid%bc(ib)%f2nqb(face,node_per_face+1)
          endif

        case ( 'volume_points', 'line' )
          cell        = cf_index
          element_set = p2c_local_elem(ivol,ipts)
          node_per_cf = grid%elem(element_set)%node_per_cell
        end select

        hrles_f = one
        qt      = zero
        mut     = zero
        turres  = zero
        s_len_f  = 0._dp
        iflagslen  = 0._dp
        skip_q_f   = 0.0_dp

        !--------------------- fill the nodal information --------------------80
        !
        !
        !
        !---------------------------------------------------------------------80
        fill_nodes: do corner = 1, node_per_cf

          select case ( geo_name )
          case ( 'boundary_points' )
            if ( node_per_cf == 3 ) then
              node = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face,corner))
            else if ( node_per_cf == 4 ) then
              node = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face,corner))
            endif
          case ( 'volume_points', 'line' )
            node   = grid%elem(element_set)%c2n(corner,cell)
          end select

          reconstruction_limiter_phi_f(1:soln%n_grd,corner) = &
            soln%phi(1:soln%n_grd,node)

          if (.not. cc_primal) then
            if (need_turb_variables) then
              if(soln%eqn_set == generic_gas)then
                write(6,*)"This option requiring turbfluctuations is not"
                write(6,*)"engaged for the generic gas path."
                write(6,*)"Stop in LibF90/sampling_gather/gather_point_data."
                call lmpi_die
              end if
                turbfluctuations =                                             &
                  turb_fluctuations(soln%n_grd, soln%n_tot, soln%n_turb,       &
                                    soln%eqn_set,                              &
                                    soln%q_dof(:,node), soln%amut(node),       &
                                    soln%gradx(:,node), soln%grady(:,node),    &
                                    soln%gradz(:,node), soln%turb(:,node))
              turb_fluct_f(1:6,corner) = turbfluctuations
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!            skip = skip_plane( twod, grid%y(node) )
!            if ( .not.skip ) then
             if( soln%eqn_set == generic_gas)then
               gradrho_f(1,corner) = sum(soln%gradx(1:n_species,node))
               gradrho_f(2,corner) = sum(soln%grady(1:n_species,node))
               gradrho_f(3,corner) = sum(soln%gradz(1:n_species,node))
             else
               gradrho_f(1,corner) = soln%gradx(1,node)
               gradrho_f(2,corner) = soln%grady(1,node)
               gradrho_f(3,corner) = soln%gradz(1,node)
             end if
             gradients_f(1,corner) = soln%gradx(2,node)
             gradients_f(2,corner) = soln%grady(2,node)
             gradients_f(3,corner) = soln%gradz(2,node)
             gradients_f(4,corner) = soln%gradx(3,node)
             gradients_f(5,corner) = soln%grady(3,node)
             gradients_f(6,corner) = soln%gradz(3,node)
             gradients_f(7,corner) = soln%gradx(4,node)
             gradients_f(8,corner) = soln%grady(4,node)
             gradients_f(9,corner) = soln%gradz(4,node)
               if ( n_turb == 2 ) then
                 gradk_f(1,corner)     = soln%gradx(kloc,node)
                 gradk_f(2,corner)     = soln%grady(kloc,node)
                 gradk_f(3,corner)     = soln%gradz(kloc,node)
                 gradw_f(1,corner)     = soln%gradx(wloc,node)
                 gradw_f(2,corner)     = soln%grady(wloc,node)
                 gradw_f(3,corner)     = soln%gradz(wloc,node)
               else
                 gradk_f  = zero
                 gradw_f  = zero
               endif
             endif
!           end if
          end if
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80

          px(corner)     = grid%x(node)
          py(corner)     = grid%y(node)
          pz(corner)     = grid%z(node)

          global_node_f(1,corner) = grid%l2g(node)
!         gradx(1:soln%n_grd,corner) = soln%gradx(1:soln%n_grd,node)
!         grady(1:soln%n_grd,corner) = soln%grady(1:soln%n_grd,node)
!         gradz(1:soln%n_grd,corner) = soln%gradz(1:soln%n_grd,node)

!  initialize to boundary face value

          if(soln%eqn_set == generic_gas)then
            uavg = soln%q_dof(n_momx,node)/soln%q_dof(n_density,node)
            vavg = soln%q_dof(n_momy,node)/soln%q_dof(n_density,node)
            wavg = soln%q_dof(n_momz,node)/soln%q_dof(n_density,node)
          else
            uavg               = soln%q_dof(2,node)
            vavg               = soln%q_dof(3,node)
            wavg               = soln%q_dof(4,node)
          end if

          vortx_f(1,corner)  = soln%grady(n_momz,node)-soln%gradz(n_momy,node)
          vorty_f(1,corner)  = soln%gradz(n_momx,node)-soln%gradx(n_momz,node)
          vortz_f(1,corner)  = soln%gradx(n_momy,node)-soln%grady(n_momx,node)
          divvel_f(1,corner) = soln%gradx(n_momx,node)+soln%grady(n_momy,node) &
                             + soln%gradz(n_momz,node)
          q_crit_f(1,corner) = q_criterion(soln%eqn_set, soln%n_grd,         &
                                          soln%gradx(:,node),                &
                                          soln%grady(:,node),                &
                                          soln%gradz(:,node) )

          select case (soln%eqn_set)
          case (compressible)
            qp(1:5,corner) = soln%q_dof(1:5,node)
          case (incompressible)
            qp(1,corner)   = 1.0_dp
            qp(2:4,corner) = soln%q_dof(2:4,node)
            qp(5,corner)   = soln%q_dof(1,node)
          case (generic_gas)

            do ns = 1,n_species
               rho_i_p(ns,corner) = soln%q_dof(ns,node)
            end do
            qp(1,corner)     = soln%q_dof(n_density,node)             ! rho
            qp(2,corner)     = soln%q_dof(n_momx,node) / qp(1,corner) ! u
            qp(3,corner)     = soln%q_dof(n_momy,node) / qp(1,corner) ! v
            qp(4,corner)     = soln%q_dof(n_momz,node) / qp(1,corner) ! w
            qp(5,corner)     = soln%q_dof(n_pressure_k(1),node)       ! p
            if (n_energy > 1) then
              q_rg_p(1,corner) = soln%q_dof(n_energy_last,node)       ! ev
              q_rg_p(2,corner) = soln%q_dof(n_temperature_j(2),node)  ! tv
            end if
            q_rg_p(3,corner) = soln%q_dof(n_temperature_j(1),node)    ! tt
            q_rg_p(4,corner) = soln%q_dof(n_molecular_weight,node)
            q_rg_p(5,corner) = soln%q_dof(n_sonic_k(1),node)
            q_rg_p(6,corner) = ( soln%q_dof(n_etot,node) + qp(5,corner) ) /    &
                                 qp(1,corner)
            q_rg_p(7,corner) = soln%q_dof(n_etot,node) / qp(1,corner)  ! etot
          case default
            qp(1:5,corner)             = zero
            q_crit_f(1,corner)         = zero
          end select

          if ( present(sadj) ) then
            lambdap(1:soln%adim,corner) = sadj%rlam(1:soln%adim,node,1)
          endif

          select case (n_turb)

          case(1)

            if(soln%eqn_set == generic_gas)then
              qt(1,corner)     = soln%q_dof(n_dis_nutl,node)
              mut(1,corner)    = soln%q_dof(n_amu_t,node)
              turres(1,corner) = soln%res(n_dis_nutl,node)
            else
              qt(1,corner)     = soln%turb(1,node)
              mut(1,corner)    = soln%amut(node)
              turres(1,corner) = soln%turbres(1,node)
            end if

          case(2)

            select case ( turbulence_model_int )
            case ( wilcox_asm, easm_ddes, EASMko2003_S )
              if(soln%eqn_set == generic_gas)then
                qt(1,corner)   = soln%q_dof(n_turb_ke,node)/                   &
                                 soln%q_dof(n_density,node)
                qt(2,corner)   = soln%q_dof(n_dis_nutl,node)/                  &
                                 soln%q_dof(n_density,node)
              else
                qt(1,corner)   = soln%turb(1,node)/soln%q_dof(1,node)
                qt(2,corner)   = soln%turb(2,node)/soln%q_dof(1,node)
              end if
            case ( wilcox_kw88, wilcox_kw98, wilcox_kw06                       &
                 , kw_lag )
              if(soln%eqn_set == generic_gas)then
                qt(1,corner)   = soln%q_dof(n_turb_ke,node)/                   &
                                 soln%q_dof(n_density,node)
                qt(2,corner)   = soln%q_dof(n_dis_nutl,node)/                  &
                                 soln%q_dof(n_density,node)
              else
                qt(1,corner)   = soln%turb(1,node)/soln%q_dof(1,node)
                qt(2,corner)   = soln%turb(2,node)/soln%q_dof(1,node)
              end if
            case default
              if(soln%eqn_set == generic_gas)then
                qt(1,corner)   = soln%q_dof(n_turb_ke,node)
                qt(2,corner)   = soln%q_dof(n_dis_nutl,node)
              else
                qt(1:2,corner)   = soln%turb(1:2,node)
              end if
            end select

            if(soln%eqn_set == generic_gas)then
              mut(1,corner)    = soln%q_dof(n_amu_t,node)
              turres(1,corner) = soln%res(n_turb_ke,node)
              turres(2,corner) = soln%res(n_dis_nutl,node)
            else
              mut(1,corner)          = soln%amut(node)
              turres(1:2,corner)     = soln%turbres(1:2,node)
            end if

          case(3)

            if(soln%eqn_set == generic_gas)then
              qt(1:2,corner) = soln%q_dof(n_turb_eq(1:2),node)                 &
                             / soln%q_dof(n_density,node)
              qt(  3,corner) = soln%q_dof(n_turb_eq(  3),node)
              mut(1,corner)    = soln%q_dof(n_amu_t,node)
              turres(1:2,corner) = soln%res(n_turb_eq(1:2),node)
            else
              qt(1,corner)       = soln%turb(1,node)/soln%q_dof(1,node)
              qt(2,corner)       = soln%turb(2,node)/soln%q_dof(1,node)
              qt(3,corner)       = soln%turb(3,node)
              mut(1,corner)      = soln%amut(node)
              turres(1:2,corner) = soln%turbres(1:2,node)
            end if

          case(4, 7)

            if(soln%eqn_set == generic_gas)then
              qt(1:n_turb,corner)  = soln%q_dof(n_turb_eq(1:n_turb),node)
              mut(1,corner)    = soln%q_dof(n_amu_t,node)
              turres(1:n_turb,corner) = soln%res(n_turb_eq(1:n_turb),node)
            else
              qt(1:n_turb,corner)   = soln%turb(1:n_turb,node)
              mut(1,corner)      = soln%amut(node)
              turres(1:n_turb,corner) = soln%turbres(1:n_turb,node)
            end if

          end select

          if (grid%idistfcn /= 0 .and. .not.cc_primal) then
            s_len_f(1,corner)  = grid%slen(node)
          end if

          if (grid%idistfcn /= 0 .and. .not.cc_primal) then
            iflagslen(1,corner) = real(grid%iflagslen(node),dp)
          end if

          if ( grid%skip_q(node) > 0 ) skip_q_f(1,corner) = one

!             time average and time rms data only for compressible path

          if (itime_avg /= 0) then

            qtavg(1:5,corner)  = soln%q_time_avg(1:5,node)
            qqtavg(1,corner) = soln%q_time_avg(7,node)
            qqtavg(2,corner) = soln%q_time_avg(8,node)
            qqtavg(3,corner) = soln%q_time_avg(9,node)
            qqtavg(4,corner) = soln%qq_time_avg(1,node)
            qqtavg(5,corner) = soln%qq_time_avg(2,node)
            qqtavg(6,corner) = soln%qq_time_avg(3,node)
            qtrms(1,corner) = sqrt(abs(soln%q_time_avg(6,node)  &
                              - qtavg(1,corner)**2))
            qtrms(2,corner) = sqrt(abs(soln%q_time_avg(7,node)  &
                              - qtavg(2,corner)**2))
            qtrms(3,corner) = sqrt(abs(soln%q_time_avg(8,node)  &
                              - qtavg(3,corner)**2))
            qtrms(4,corner) = sqrt(abs(soln%q_time_avg(9,node)  &
                              - qtavg(4,corner)**2))
            qtrms(5,corner) = sqrt(abs(soln%q_time_avg(10,node) &
                              - qtavg(5,corner)**2))
            if ( tavg_header_version == 2 ) then
              tau_sgs_f(1,corner) = qqtavg(1,corner) - qtavg(2,corner)**2
              tau_sgs_f(2,corner) = qqtavg(2,corner) - qtavg(3,corner)**2
              tau_sgs_f(3,corner) = qqtavg(3,corner) - qtavg(4,corner)**2
              tau_sgs_f(4,corner) = qqtavg(4,corner) &
                                   - qtavg(2,corner)*qtavg(3,corner)
              tau_sgs_f(5,corner) = qqtavg(5,corner) &
                                   - qtavg(2,corner)*qtavg(4,corner)
              tau_sgs_f(6,corner) = qqtavg(6,corner) &
                                   - qtavg(3,corner)*qtavg(4,corner)
              mu_t_tavg_f(1,corner) = qqtavg(4,corner)
            vort_mag_tavg_f(1,corner) = qqtavg(5,corner)
            else if ( tavg_header_version == 3 ) then
              vort_x_tavg_f(1,corner)   = qqtavg(1,corner)
              vort_y_tavg_f(1,corner)   = qqtavg(2,corner)
              vort_z_tavg_f(1,corner)   = qqtavg(3,corner)
              mu_t_tavg_f(1,corner)     = qqtavg(4,corner)
              vort_mag_tavg_f(1,corner) = qqtavg(5,corner)
            endif


          end if


        enddo fill_nodes

!------------------------------ dsdt -----------------------------------------80
!------------------------------ dsdt -----------------------------------------80
!------------------------------ dsdt -----------------------------------------80

       ielem  = element_set
       dsdt = get_cell_dsdt ( cell,                                            &
              grid%elem(ielem)%ncell,         grid%elem(ielem)%c2n,            &
              grid%x,                         grid%y,                          &
              grid%z,                         soln%q_dof,                      &
              grid%elem(ielem)%local_f2n,                                      &
              grid%elem(ielem)%local_f2e,     grid%elem(ielem)%e2n_2d,         &
              grid%elem(ielem)%face_per_cell, grid%elem(ielem)%node_per_cell,  &
              grid%elem(ielem)%edge_per_cell, grid%elem(ielem)%face_2d,        &
              soln%gradx,                     soln%grady,                      &
              soln%gradz                             )

!------------------------------ dsdt -----------------------------------------80
!------------------------------ dsdt -----------------------------------------80
!------------------------------ dsdt -----------------------------------------80
       if ( n_turb > 0 ) then
       diffusion_k(1) = get_cell_diffusion ( cell,                             &
              grid%elem(ielem)%ncell,         grid%elem(ielem)%c2n,            &
              grid%x,                         grid%y,                          &
              grid%z,                         soln%q_dof,                      &
              grid%elem(ielem)%local_f2n,                                      &
              grid%elem(ielem)%local_f2e,     grid%elem(ielem)%e2n_2d,         &
              grid%elem(ielem)%face_per_cell, grid%elem(ielem)%node_per_cell,  &
              grid%elem(ielem)%edge_per_cell, grid%elem(ielem)%face_2d,        &
              soln%gradx,                     soln%grady,                      &
              soln%gradz,                     kloc   )
       transport_k(1) = get_cell_transport ( cell,                             &
              grid%elem(ielem)%ncell,         grid%elem(ielem)%c2n,            &
              grid%x,                         grid%y,                          &
              grid%z,                                                          &
              grid%elem(ielem)%local_f2n,                                      &
              grid%elem(ielem)%local_f2e,     grid%elem(ielem)%e2n_2d,         &
              grid%elem(ielem)%face_per_cell, grid%elem(ielem)%node_per_cell,  &
              grid%elem(ielem)%edge_per_cell, grid%elem(ielem)%face_2d,        &
              soln%gradx,                     soln%grady,                      &
              soln%gradz,                     soln%amut,                       &
              kloc )
        else
          diffusion_k(1) = 0.0_dp
          transport_k(1) = 0.0_dp
        endif
!-----------------------------------------------------------------------------80
!----------- CLEAN ME UP -- duplicate data structures, different paths
!-----------------------------------------------------------------------------80
        if (itime_avg /= 0) then
          vort_mag_tavg = get_time_averaged_vorticity ( cell,                  &
            grid%elem(ielem)%ncell,         grid%elem(ielem)%c2n,              &
            grid%x,                         grid%y,                            &
            grid%z,                         qtavg(2:4,:),                      &
            grid%elem(ielem)%local_f2n,                                        &
            grid%elem(ielem)%local_f2e,     grid%elem(ielem)%e2n_2d,           &
            grid%elem(ielem)%face_per_cell, grid%elem(ielem)%node_per_cell,    &
            grid%elem(ielem)%edge_per_cell, grid%elem(ielem)%face_2d           &
             )
        end if
!-----------------------------------------------------------------------------80
!       start boundary points
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
! Much of this comes from post_party/wall_data, except it's reduced to just
! obtaining individual face data rather than complete boundaries
      boundary_data:  if ( geo_name == 'boundary_points' ) then
 !   allocate space for node_flag array
        ielem             = element_set
        node_per_cell     = grid%elem(ielem)%node_per_cell

        allocate( node_flag(node_per_cell) )

        node_flag = 1
!---heat transfer
        do cell_node = 1, node_per_cell
          do face_node = 1, node_per_face
              if ( node_per_face == 3 ) then
                node = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face,face_node))
              else if ( node_per_cf == 4 ) then
                node = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face,face_node))
              endif
!           if a cell node lies on the surface, set its flag to zero
            if (grid%elem(ielem)%c2n(cell_node,cell) == node) then
               node_flag(cell_node) = 0
            end if
          end do
        end do


!  get average values
        uave  = zero
        vave  = zero
        wave  = zero

        fact  = one/real(node_per_cell,dp)

        cell_nodes : do cell_node = 1, node_per_cell
          node = grid%elem(ielem)%c2n(cell_node,cell)
!         just sum here, convert to average below
          if(soln%eqn_set == generic_gas)then
            uave  = uave + soln%q_dof(n_momx,node)/soln%q_dof(n_density,node)
            vave  = vave + soln%q_dof(n_momy,node)/soln%q_dof(n_density,node)
            wave  = wave + soln%q_dof(n_momz,node)/soln%q_dof(n_density,node)
          else
            uave  = uave + soln%q_dof(2,node)
            vave  = vave + soln%q_dof(3,node)
            wave  = wave + soln%q_dof(4,node)
          end if
        end do cell_nodes

        face_nodes : do face_node = 1, node_per_face
          uavg = uave * fact
          vavg = vave * fact
          wavg = wave * fact
        end do face_nodes

! calculate normal of face element
        if ( node_per_face == 3 ) then
          node1 = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face,1))
          node2 = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face,2))
          node3 = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face,3))
          p1(1) = grid%x(node1); p1(2) = grid%y(node1); p1(3) = grid%z(node1)
          p2(1) = grid%x(node2); p2(2) = grid%y(node2); p2(3) = grid%z(node2)
          p3(1) = grid%x(node3); p3(2) = grid%y(node3); p3(3) = grid%z(node3)
          n     = get_normal(p1, p2, p3)
        else if ( node_per_face == 4 ) then
          node1 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face,1))
          node2 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face,2))
          node3 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face,3))
          node4 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face,4))
          p1(1) = grid%x(node1); p1(2) = grid%y(node1); p1(3) = grid%z(node1)
          p2(1) = grid%x(node2); p2(2) = grid%y(node2); p2(3) = grid%z(node2)
          p3(1) = grid%x(node3); p3(2) = grid%y(node3); p3(3) = grid%z(node3)
          p4(1) = grid%x(node4); p4(2) = grid%y(node4); p4(3) = grid%z(node4)
          n1    = get_normal(p1, p2, p3)
          n     = n1
          n1    = get_normal(p1, p3, p4)
          n     = n + n1
          n1    = get_normal(p1, p2, p4)
          n     = n + n1
          n1    = get_normal(p2, p3, p4)
          n     = n + n1
          n     = n / dot_product(n,n)
        endif

!--------------------------------------------------
!
!     get values of skin friction coefficient and
!     yplus for each viscous boundary face
!
!--------------------------------------------------

!     get the average velocity of 1) all off-surface points in the cell
!                                 2) all surface points in the cell

!     we will form a velocity gradient from these two average velocities,
!     and then get the portion of that velocity gradient tangent to the
!     wall to use to compute skin friction

!     average temp and density needed only at surface

!     _off denotes off-surface values
!     _on  denotes on-surface  values

        total_weight_on  = 0._dp
        total_weight_off = 0._dp
        xave_off         = 0._dp
        yave_off         = 0._dp
        zave_off         = 0._dp
        xave_on          = 0._dp
        yave_on          = 0._dp
        zave_on          = 0._dp
        tave_on          = 0._dp
        tave_off         = 0._dp
        rhow             = 0._dp
        amuw             = 0._dp

        do cell_node = 1, node_per_cell
          node = grid%elem(ielem)%c2n(cell_node,cell)
          if (node_flag(cell_node) == 0) then     ! surface nodes
            if(soln%eqn_set == generic_gas)then
              uave_on  = uave_on                                               &
                       + soln%q_dof(n_momx,node)/soln%q_dof(n_density,node)
              vave_on  = vave_on                                               &
                       + soln%q_dof(n_momy,node)/soln%q_dof(n_density,node)
              wave_on  = wave_on                                               &
                       + soln%q_dof(n_momz,node)/soln%q_dof(n_density,node)
              tave_on  = tave_on                                               &
                       + soln%q_dof(n_temperature_j(1),node)
              amuw     = amuw + soln%q_dof(n_amu_k(1),node)
              rhow     = rhow + soln%q_dof(n_density,node)
            else
              uave_on  = uave_on + soln%q_dof(2,node)
              vave_on  = vave_on + soln%q_dof(3,node)
              wave_on  = wave_on + soln%q_dof(4,node)
            end if
            xave_on = xave_on + grid%x(node)
            yave_on = yave_on + grid%y(node)
            zave_on = zave_on + grid%z(node)
            if ( soln%eqn_set == compressible ) then
              tave_on = tave_on + gamma*soln%q_dof(5,node)/soln%q_dof(1,node)
              rhow = rhow + soln%q_dof(1,node)
            end if
            total_weight_on = total_weight_on + 1.0_dp
          else                                    ! off-surface nodes
            if(soln%eqn_set == generic_gas)then
              uave_off  = uave_off                                             &
                        + soln%q_dof(n_momx,node)/soln%q_dof(n_density,node)
              vave_off  = vave_off                                             &
                        + soln%q_dof(n_momy,node)/soln%q_dof(n_density,node)
              wave_off  = wave_off                                             &
                        + soln%q_dof(n_momz,node)/soln%q_dof(n_density,node)
              tave_off  = tave_off                                             &
                        + soln%q_dof(n_temperature_j(1),node)
            else
              uave_off  = uave_off + soln%q_dof(2,node)
              vave_off  = vave_off + soln%q_dof(3,node)
              wave_off  = wave_off + soln%q_dof(4,node)
            end if
            xave_off = xave_off + grid%x(node)
            yave_off = yave_off + grid%y(node)
            zave_off = zave_off + grid%z(node)
            if ( soln%eqn_set == compressible ) then
              tave_off = tave_off + gamma*soln%q_dof(5,node)/soln%q_dof(1,node)
            end if
            total_weight_off = total_weight_off + 1.0_dp
          end if
        end do

        uave_on = uave_on/total_weight_on
        vave_on = vave_on/total_weight_on
        wave_on = wave_on/total_weight_on
        tave_on = tave_on/total_weight_on
        xave_on = xave_on/total_weight_on
        yave_on = yave_on/total_weight_on
        zave_on = zave_on/total_weight_on

        uave_off = uave_off/total_weight_off
        vave_off = vave_off/total_weight_off
        wave_off = wave_off/total_weight_off
        tave_off = tave_off/total_weight_off
        xave_off = xave_off/total_weight_off
        yave_off = yave_off/total_weight_off
        zave_off = zave_off/total_weight_off

!     wall-average values below for density and viscosity

        compressible1 : if ( soln%eqn_set == compressible ) then
          rhow    = rhow/total_weight_on
          rmu     = viscosity_law( cstar, tave_on )
          rnu_w   = rmu / rhow
        else if ( soln%eqn_set == generic_gas ) then
          rhow    = rhow/total_weight_on
          rmuw    = rmuw/total_weight_on
          rnu_w   = rmuw / rhow
        else
          rmu   = 1._dp
          rnu_w = 1._dp
          rhow  = 1._dp
        end if compressible1

        dx   = xave_off - xave_on
        dy   = yave_off - yave_on
        dz   = zave_off - zave_on
        dist = (dx*n(1) + dy*n(2) + dz*n(3))

!     note that the average velocity of the on-surface points may be
!     non-zero in the case of suction, blowing, or moving walls.

!     note that the average velocities _may_ not be parallel to the wall;
!     to compute skin friction, we need to take the difference in the
!     tangential components of these two velocity vectors. below, the
!     equivalent process of taking the tangential component of the
!     difference in the average velocity between off-surface point(s)
!     and on-surface points is used instead

!     components of velocity difference between off-surface and on-surface

        du = uave_off - uave_on
        dv = vave_off - vave_on
        dw = wave_off - wave_on

!     components of this velocity difference normal to the surface;

        dun   = du*n(1)
        dvn   = dv*n(2)
        dwn   = dw*n(3)

!     components of this velocity difference tangential to the surface
!     (this is simply the vector minus the normal component)

        dut   = du - (dun + dvn + dwn)*n(1)
        dvt   = dv - (dun + dvn + dwn)*n(2)
        dwt   = dw - (dun + dvn + dwn)*n(3)

!     skin friction

        dudn  = sqrt(dut*dut + dvt*dvt + dwt*dwt)/dist

!     there is no perfect way to set the sign for skin friction coefficient;
!     we choose to define negative skin friction when the inner product of
!     the local tangent velocity and the frestream velocity is negative

        if( (dut*uinf + dvt*vinf + dwt*winf) < 0._dp ) dudn = -dudn

        compressible2 : if ( soln%eqn_set == compressible ) then

        skin_fricx = 2._dp*rmu*dut/dist/xmach/re
        skin_fricy = 2._dp*rmu*dvt/dist/xmach/re
        skin_fricz = 2._dp*rmu*dwt/dist/xmach/re
        skin_fric  = 2._dp*rmu*dudn/xmach/re
        if (rmu <= 1e-10_dp) rmu=1e-10_dp
        tau_wall = rmu*abs(dudn)
        u_tau_w  = sqrt(tau_wall/rhow)*sqrt(xmach/re)
        yplus    = ( dist * u_tau_w / rnu_w ) * (re/xmach)
        if ( abs(u_tau_w) > epsilon(1.0_dp) ) &
          uplus    = sqrt(uavg*uavg+vavg*vavg+wavg*wavg) / u_tau_w
!       yplus= dist*sqrt(rhow/rmu*abs(dudn)*re/xmach)

        else if ( soln%eqn_set == generic_gas ) then

        skin_fricx = 2._dp*rmu*dut/dist
        skin_fricy = 2._dp*rmu*dvt/dist
        skin_fricz = 2._dp*rmu*dwt/dist
        skin_fric  = 2._dp*rmu*dudn
        if (rmu <= 1e-10_dp) rmu=1e-10_dp
        tau_wall = rmu*abs(dudn)
        u_tau_w  = sqrt(tau_wall/rhow)
        yplus    = ( dist * u_tau_w / rnu_w )
        if ( abs(u_tau_w) > epsilon(1.0_dp) ) &
          uplus    = sqrt(uavg*uavg+vavg*vavg+wavg*wavg) / u_tau_w

        else

        skin_fricx = 2._dp*rmu*dut/dist/re
        skin_fricy = 2._dp*rmu*dvt/dist/re
        skin_fricz = 2._dp*rmu*dwt/dist/re
        skin_fric  = 2._dp*rmu*dudn/re
        if (rmu <= 1e-10_dp) rmu=1e-10_dp
!       yplus = dist*sqrt(rhow/rmu*abs(dudn)*re)

        tau_wall = rmu*abs(dudn)
        u_tau_w  = sqrt(tau_wall/rhow)*sqrt(xmach/re)
        yplus    = ( dist * u_tau_w / rnu_w ) * (re/xmach)
        if ( abs(u_tau_w) > epsilon(1.0_dp) ) &
          uplus    = sqrt(uavg*uavg+vavg*vavg+wavg*wavg) / u_tau_w

        end if compressible2

 !     store temperature gradient for aerothermoelastics
        dtx = (tave_off - tave_on)*n(1)/dist
        dty = (tave_off - tave_on)*n(2)/dist
        dtz = (tave_off - tave_on)*n(3)/dist

!     Note: there is generally a lot more to compute heating in the generic gas
!           path. This is a reasonable approximation in absence of chemical
!           reactions and radiation. One could compute intersection with bcsoln
!           surface for better accuracy. This involves an infrastructure change
!           that may not be warranted at this time.
        heat_flux = (dtx*n(1) + dty*n(2) + dtz*n(3))

        deallocate( node_flag )

      else

        heat_flux = 0.0_dp

      endif boundary_data

!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!     end boundary points
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80

select_geo_name:        select case ( geo_name )
        case ( 'boundary_points' )

          q_point    = face_stats(5, node_per_cf, px, py, pz, qp )
          q_rg       = face_stats(7, node_per_cf, px, py, pz, q_rg_p )
          rho_i      = face_stats(n_species, node_per_cf, px, py, pz,          &
                                     rho_i_p )
          q_turb     = face_stats(n_turb, node_per_cf, px, py, pz, qt )
          q_tavg     = face_stats(5, node_per_cf, px, py, pz, qtavg )
          q_trms     = face_stats(5, node_per_cf, px, py, pz, qtrms )
          gradrho    = face_stats(3, node_per_cf, px, py, pz, gradrho_f )
          gradients  = face_stats(9, node_per_cf, px, py, pz, gradients_f )
          gradk      = face_stats(3, node_per_cf, px, py, pz, gradk_f )
          gradw      = face_stats(3, node_per_cf, px, py, pz, gradw_f )
          mu_t       = face_stats(1, node_per_cf, px, py, pz, mut )
          turb_fluct = face_stats(6, node_per_cf, px, py, pz, turb_fluct_f )
          turb_res   = face_stats(n_turb, node_per_cf, px, py, pz, turres )
          s_len      = face_stats(1, node_per_cf, px, py, pz, s_len_f )
          iflag_s_len= face_stats(1, node_per_cf, px, py, pz, iflagslen )
          q_crit     = face_stats(1, node_per_cf, px, py, pz, q_crit_f )
          aspect_r_f = face_stats(1, node_per_cf, px, py, pz, aspect_f )
          volume     = face_stats(1, node_per_cf, px, py, pz, volume_f )
          vort_x     = face_stats(1, node_per_cf, px, py, pz, vortx_f )
          vort_y     = face_stats(1, node_per_cf, px, py, pz, vorty_f )
          vort_z     = face_stats(1, node_per_cf, px, py, pz, vortz_f )
          div_vel    = face_stats(1, node_per_cf, px, py, pz, divvel_f )
          q_res      = face_stats(5, node_per_cf, px, py, pz, qres )
          res_gcl    = face_stats(1, node_per_cf, px, py, pz, res_gcl_f )
          c_gamma    = face_stats(1, node_per_cf, px, py, pz, c_gamma_f)
          skip_q     = face_stats(1, node_per_cf, px, py, pz, skip_q_f)
          lambda_point = face_stats(soln%adim, node_per_cf, px, py, pz, lambdap)
          hrles_bf   = hrles_f(1,1)
          u_avg      = uavg
          v_avg      = vavg
          w_avg      = wavg
          c_q        = heat_flux
          c_fx       = skin_fricx
          c_fy       = skin_fricy
          c_fz       = skin_fricz
          c_f        = skin_fric
          y_plus     = yplus
          u_plus     = uplus
          k_plus     = zero
          w_plus     = zero
          t11_plus   = zero
          t12_plus   = zero
          t13_plus   = zero
          t22_plus   = zero
          t23_plus   = zero
          t33_plus   = zero
          b_ij       = zero
          tau_ij     = zero
          tau_sgs    = zero

        case ( 'volume_points', 'line' )

          q_point    = element_stats(5, node_per_cf, px, py, pz, qp )
          q_rg       = element_stats(7, node_per_cf, px, py, pz, q_rg_p )
          rho_i      = element_stats(n_species, node_per_cf, px, py, pz,       &
                                     rho_i_p )
          q_turb     = element_stats(n_turb, node_per_cf, px, py, pz, qt )
          q_tavg     = element_stats(5, node_per_cf, px, py, pz, qtavg )
          q_trms     = element_stats(5, node_per_cf, px, py, pz, qtrms )
          mu_t       = element_stats(1, node_per_cf, px, py, pz, mut )
          gradrho    = element_stats(3, node_per_cf, px, py, pz, gradrho_f )
          gradients  = element_stats(9, node_per_cf, px, py, pz, gradients_f )
          gradk      = element_stats(3, node_per_cf, px, py, pz, gradk_f )
          gradw      = element_stats(3, node_per_cf, px, py, pz, gradw_f )
          turb_fluct = element_stats(6, node_per_cf, px, py, pz, turb_fluct_f )
          tau_sgs    = element_stats(6, node_per_cf, px, py, pz, tau_sgs_f )
          turb_res   = element_stats(n_turb, node_per_cf, px, py, pz, turres )
          s_len      = element_stats(1, node_per_cf, px, py, pz, s_len_f )
          iflag_s_len= element_stats(1, node_per_cf, px, py, pz, iflagslen )
          q_crit     = element_stats(1, node_per_cf, px, py, pz, q_crit_f )
          aspect_r_f = element_stats(1, node_per_cf, px, py, pz, aspect_f )
          volume     = element_stats(1, node_per_cf, px, py, pz, volume_f )
          vort_x     = element_stats(1, node_per_cf, px, py, pz, vortx_f )
          vort_y     = element_stats(1, node_per_cf, px, py, pz, vorty_f )
          vort_z     = element_stats(1, node_per_cf, px, py, pz, vortz_f )
          div_vel    = element_stats(1, node_per_cf, px, py, pz, divvel_f )
          q_res      = element_stats(5, node_per_cf, px, py, pz, qres )
          res_gcl    = element_stats(1, node_per_cf, px, py, pz, res_gcl_f )
          c_gamma    = element_stats(1, node_per_cf, px, py, pz, c_gamma_f)
          skip_q     = element_stats(1, node_per_cf, px, py, pz, skip_q_f)
          mu_t_tavg  = element_stats(1, node_per_cf, px, py, pz, mu_t_tavg_f)
          vort_mag_tavg = &
                     element_stats(1, node_per_cf, px, py, pz, vort_mag_tavg_f)
          vort_x_tavg = &
                     element_stats(1, node_per_cf, px, py, pz, vort_x_tavg_f)
          vort_y_tavg = &
                     element_stats(1, node_per_cf, px, py, pz, vort_y_tavg_f)
          vort_z_tavg = &
                     element_stats(1, node_per_cf, px, py, pz, vort_z_tavg_f)
          reconstruction_limiter_phi = element_stats(soln%n_grd                &
                     , node_per_cf, px, py, pz, reconstruction_limiter_phi_f )

          lambda_point = element_stats(soln%adim, node_per_cf,px,py,pz,lambdap)
          hrles_bf   = hrles_f(1,1)

          vgradrho   = q_point(2)*gradrho(1)                           &
                     + q_point(3)*gradrho(2)                           &
                     + q_point(4)*gradrho(3)

          dist = sqrt(                                                  &
          (boundary_point(1,ivol)-px(0))*(boundary_point(1,ivol)-px(0)) &
         +(boundary_point(2,ivol)-py(0))*(boundary_point(2,ivol)-py(0)) &
         +(boundary_point(3,ivol)-pz(0))*(boundary_point(3,ivol)-pz(0)) &
                             )
          if ( rnuw(ivol) > spacing(real( (dist * u_tau(ivol)), dp) ) ) then
            y_plus(1)   = ( dist * u_tau(ivol) / rnuw(ivol) ) * (re/xmach)
          else
            y_plus(1)   = zero
          end if
          if ( u_tau(ivol) > zero ) then
            u_plus(1)   = sqrt( q_point(2)*q_point(2) &
                              + q_point(3)*q_point(3) &
                              + q_point(4)*q_point(4) ) / u_tau(ivol)
          end if

          k_plus(1)   = zero
          w_plus(1)   = zero
!=============================================================================80
!
!                   conditional for number of turbulence variables
!
!=============================================================================80
          gradv = set_gradv(                                                   &
          gradients(1), gradients(2), gradients(3),                            &
          gradients(4), gradients(5), gradients(6),                            &
          gradients(7), gradients(8), gradients(9))
          sij  = get_sij(gradv)
          wij  = get_wij(gradv)
          vort = get_vort(gradv)
          wji = -wij
          s2 = matmul( sij, sij )
          w2 = matmul( wij, wji )

n_turb_gt_0: if ( n_turb == 1 ) then
            k_plus(1)   = zero
            w_plus(1)   = zero

          else if ( n_turb >= 2 ) then
            temperature = gamma * q_point(5) / q_point(1)
            mu   = viscosity_law( cstar, temperature )
            nu   = mu / q_point(1)
            if ( u_tau(ivol) > tiny(0.0_dp) ) then
              k_plus(1)   = q_turb(1) / ( u_tau(ivol)*u_tau(ivol) )
              w_plus(1)   = q_turb(2) * nu / ( u_tau(ivol)*u_tau(ivol) )
!             u_ratio = xmach * xmach / ( u_tau(ivol)*u_tau(ivol) )
              u_ratio = 1.0d+0 / ( u_tau(ivol)*u_tau(ivol) )
            else
              k_plus(1)   = zero
              u_ratio = zero
            endif
            bij = zero

            kw_select_2: select case ( turbulence_model_int )

!           case ( 'kw-lag' )
!             mu_3 = q_turb(3)
!write(6,'(a,10(1x,es15.5))') 'sampling:  ', q_turb
            case ( abid_linear )
              outer_timescale = 1.0e+15
              if ( q_turb(1) > epsilon(1.0_dp) )  &
                                         outer_timescale = q_turb(2) / q_turb(1)

              if ( q_turb(2) < epsilon(1.0_dp) ) then
                inner_timescale = 1.0e+20_dp
              else
                inner_timescale = ctime * sqrt(xmr) * sqrt( nu / q_turb(2) )
              endif
              timescale = max( outer_timescale, inner_timescale )

            case ( k_kL_MEAH2013, sst_kkl )
              if(q_turb(1) > tiny(1.0_dp) ) then
               timescale = cmu_0**0.25_dp*q_turb(2) / q_turb(1)**1.5_dp
              else
               timescale = 0.0_dp
              end if

            case ( WilcoxRSM_w2006,WilcoxRSM_w2006c,SSGLRR_RSM_w2012_SD,       &
                   SSGLRR_RSM_w2012 )
              outer_timescale = 1.0e+15
            if ( q_turb(7) > epsilon(1.0_dp) ) outer_timescale = one / q_turb(7)
              timescale = outer_timescale

            case default

              outer_timescale = one / q_turb(2)
              if ( q_turb(1)*q_turb(2) < epsilon(1.0_dp) ) then
                inner_timescale = 1.0e+20_dp
              else
                inner_timescale = ctime * sqrt(xmr) &
                   * sqrt( nu / ( q_turb(1) * q_turb(2) ) )
              endif
              timescale = outer_timescale

            end select kw_select_2

            eta1s = trace_a( s2 )*timescale*timescale*xmr*xmr
            eta2s = trace_a( w2 )*timescale*timescale*xmr*xmr

            cmu_star(1) = get_cmu_star( sij, wij, timescale, xmr )

            bij   = get_bij( turbulence_model_int                              &
                         , model_strain_form_int, sij, wij                     &
                         , angular_velocity, dsdt, vort, s_len(1)              &
                         , q_turb(1), timescale, nu, xmr, cmu_0, q_turb )
            tauij = get_rhotauij( turbulence_model_int                         &
                  , model_strain_form_int, sij, wij                            &
                  , angular_velocity, dsdt, vort                               &
                  , s_len(1), q_point(1), q_turb(1), timescale                 &
                  , nu, xmr, cmu_0, q_turb )

            bs = matmul( bij, sij )
            bb = matmul( bij, bij )
            bbb = matmul( bb, bij )
            tr_bs(1) =  bs(1,1) +  bs(2,2) +  bs(3,3)
            iib(1)   =  -trace_a ( bb  ) / two
            iiib(1)  =   trace_a ( bbb ) / three

!write(6,'(a,12(1x,es15.5))') 'sampling:...', &
!bij(1,1),bij(2,2),bij(3,3),                  &
!tauij(1,1), tauij(2,2), tauij(3,3),iib(1),iiib(1)
if ( turbulence_model_int == WilcoxRSM_w2006 .or.                              &
     turbulence_model_int == WilcoxRSM_w2006c.or.                              &
     turbulence_model_int == SSGLRR_RSM_w2012_SD.or.                           &
     turbulence_model_int == SSGLRR_RSM_w2012 ) then
else
            ap = - trace_a( bij )
            aq = -( bij(1,2)**2 + bij(1,3)**2 + bij(2,3)**2             &
                  - bij(1,1)*bij(2,2) - bij(1,1)*bij(3,3) - bij(2,2)*bij(3,3) )
            ar = -( bij(1,1)*bij(2,2)*bij(3,3) - bij(1,1)*bij(2,3)**2       &
                 -bij(3,3)*bij(1,2)**2 - bij(2,2)*bij(1,3)**2               &
                 + 2.0_dp*bij(1,2)*bij(2,3)*bij(1,3))
            call cubic_lam(ap,aq,ar,xlam1,xlam2,xlam3)
!       sort eigenvalues:
            tempxlam1 = xlam1
            tempxlam2 = xlam2
            tempxlam3 = xlam3
            if (     xlam1 >=   xlam2 .and. xlam2 >=   xlam3) then
              xlam1=tempxlam1
              xlam2=tempxlam2
              xlam3=tempxlam3
            else if (xlam1 >=   xlam3 .and. xlam3 >=   xlam2) then
              xlam1=tempxlam1
              xlam2=tempxlam3
              xlam3=tempxlam2
            else if (xlam2 >=   xlam1 .and. xlam1 >=   xlam3) then
              xlam1=tempxlam2
              xlam2=tempxlam1
              xlam3=tempxlam3
            else if (xlam2 >=   xlam3 .and. xlam3 >=   xlam1) then
              xlam1=tempxlam2
              xlam2=tempxlam3
              xlam3=tempxlam1
            else if (xlam3 >=   xlam1 .and. xlam1 >=   xlam2) then
              xlam1=tempxlam3
              xlam2=tempxlam1
              xlam3=tempxlam2
            else if (xlam3 >=   xlam2 .and. xlam2 >=   xlam1) then
              xlam1=tempxlam3
              xlam2=tempxlam2
              xlam3=tempxlam1
            else
              write(6,'(''error in sort!'')')
!             stop
            end if
end if
! get Cs
! c1c= xlam1 - xlam2
! c2c= 2.0_dp * ( xlam2 - xlam3 )
! c3c= 3.0_dp * xlam3 + 1.0_dp
! get x,y coords of map pts
!(note map bdy corners are 0,0 -> 1,0 -> 0.5,0.8660254)
! xiib and xiiib are the traditional IIb and IIIb from Lumley triangle
! xxc=c1c*1.0_dp+c2c*0.0_dp+c3c*0.50_dp
! yyc=c1c*0.0_dp+c2c*0.0_dp+c3c*0.8660254_dp
! write(4,'(7e17.7)') c1c,c2c,c3c,xxc,yyc,xiib,xiiib
            term = gradk(1)*gradw(1) + gradk(2)*gradw(2) + gradk(3)*gradw(3)
            crossterm = 2.0_dp*xmr*(1.0_dp/sig_w2)*term/q_turb(2)

!           Blending function

            arg1 = xmr*sqrt(abs(q_turb(1)))/(betastar*q_turb(2)*dist)
            arg2 = 500.0_dp*xmr*xmr*nu/(dist*dist*q_turb(2))
            argt = max(crossterm/xmr,limit_cd)
            arg3 = (4.0_dp/sig_w2)*q_turb(1)/(argt*dist*dist)
            arga = max(arg1,arg2)
            arg  = min(arga,arg3)

            sst_f1(1) = tanh(arg*arg*arg*arg)

            f_r1 = smirnov_cc_term(q_turb(2), sij, wij, angular_velocity, dsdt )

            xi_k  = cross_diffusion_kw98( q_turb(2), gradk, gradw              &
                  , xmr, betastar_0_w98, turbulence_model_int )

            production_k(1) = get_production_k ( q_point(1), tauij, gradv      &
                          , turbulence_model_int, prodk_form_int               &
                          , mu_t(1), xmr, f_r1(1) )
            destruction_k(1) = get_destruction_k ( turbulence_model_int        &
                          , betastar, q_point(1), q_turb(1), q_turb(2)         &
                          , xmr, one, xi_k(1) )

            enorm         = u_tau(ivol)**4 * ( mu/xmr)
! normalize
            if ( enorm > tiny(1.0_dp) ) then
              production_k  = production_k /enorm
              destruction_k = -destruction_k /enorm
              transport_k   = transport_k*xmr /enorm
              diffusion_k   = diffusion_k*xmr /enorm
            else
              production_k  = zero
              destruction_k = zero
              transport_k   = zero
              diffusion_k   = zero
            endif
!         if ( verbose ) then
!         write(6,'(15(1x,f20.10))') y_plus(1), production_k, &
!         destruction_k, transport_k, diffusion_k &
!         , production_k + destruction_k+ transport_k+ diffusion_k
!          endif

            tau_ij(1)   = -tauij(1,1)
            tau_ij(2)   = -tauij(2,2)
            tau_ij(3)   = -tauij(3,3)
            tau_ij(4)   =  tauij(1,2)
            tau_ij(5)   =  tauij(1,3)
            tau_ij(6)   =  tauij(2,3)
            b_ij(1)     = bij(1,1)
            b_ij(2)     = bij(2,2)
            b_ij(3)     = bij(3,3)
            b_ij(4)     = bij(1,2)
            b_ij(5)     = bij(1,3)
            b_ij(6)     = bij(2,3)
            t11_plus(1) = -tauij(1,1) * u_ratio
            t22_plus(1) = -tauij(2,2) * u_ratio
            t33_plus(1) = -tauij(3,3) * u_ratio
            t12_plus(1) = tauij(1,2) * u_ratio
            t13_plus(1) = tauij(1,3) * u_ratio
            t23_plus(1) = tauij(2,3) * u_ratio
            pk_ij(1)    = -tauij(1,1)*gradv(1,1)
            pk_ij(2)    = -tauij(2,2)*gradv(2,2)
            pk_ij(3)    = -tauij(3,3)*gradv(3,3)
            pk_ij(4)    = -tauij(1,2)*gradv(1,2)
            pk_ij(5)    = -tauij(1,3)*gradv(1,3)
            pk_ij(6)    = -tauij(2,3)*gradv(2,3)

          endif n_turb_gt_0
!=============================================================================80
!
!               ENDIF conditional for number of turbulence variables
!
!=============================================================================80
        end select select_geo_name

          sw_f = one
          pw_f = one
          pv_f = one
          qv_f = one
          rv_f = one

        if ( verbose .and. (point_is_found(ivol,ipts)==1) ) then
          write(6,'(a,6i5,8(1x,es15.5))') 'gather_point_data'           &
           ,lmpi_id, ivol,ipts,                                         &
           p2c_local_cells(ivol,ipts), p2c_local_elem(ivol,ipts),       &
           p2c_local_bdy(ivol,ipts), px(0),py(0),pz(0),q_point
        endif
!-----------------------------------------------------------------------------80
      output_var_loop:    do nn = 1,n_output_variables

        var          = trim(adjustl(output_variables(nn)))
        temp_vec(nn) = get_var( var, soln%eqn_set                            &
                     , px(0), py(0), pz(0), s_len, volume                    &
                     , q_point, q_turb, q_tavg, q_trms, turb_res, mu_t       &
                     , turb_fluct, iflag_s_len, q_crit                       &
                     , vort_x, vort_y, vort_z, div_vel                       &
                     , q_res, res_gcl, c_gamma, aspect_r_f                   &
                     , skip_q, blank_f, mesh_f                               &
                     , hrles_bf, c_q, c_fx, c_fy, c_fz, c_f, y_plus          &
                     , u_avg, v_avg, w_avg, rho_i, q_rg, lambda_point        &
                     , sw_f, pw_f, pv_f, qv_f, rv_f, global_node_f           &
                     , u_plus, k_plus, w_plus, t11_plus, t12_plus, t13_plus  &
                     , t22_plus, t23_plus, t33_plus, re_tau(ivol), tau_ij    &
                     , b_ij, cmu_star, eta1s, eta2s, tr_bs, iib, iiib        &
                     , vgradrho, bird_breakdown, mu_t_tavg, vort_mag_tavg    &
                     , vort_x_tavg, vort_y_tavg, vort_z_tavg                 &
                     , vort_rms, n_turb, pk_ij, tau_sgs, gradients, f_r1     &
                     , production_k, destruction_k, diffusion_k, transport_k &
                     , sst_f1, xi_k , soln%n_grd, reconstruction_limiter_phi &
                     )

      end do output_var_loop
!-----------------------------------------------------------------------------80

      do nn = 1,n_output_variables
        point_data(nn,ipts) = temp_vec(nn)
      end do


    end if loop_cell_faces

  end do loop_ipts

  deallocate ( rho_i_p )
  deallocate ( rho_i )
  deallocate ( temp_vec )

  end subroutine gather_point_data

!---------------------------- GET_FACE_INDEX ---------------------------------80
!
!  find face index for requested boundary point
!
!-----------------------------------------------------------------------------80
  subroutine get_face_index( grid, ib, ivol, face, node_per_face)

    use grid_types,         only : grid_type

    use sampling_funclib,   only : is_inside_triangle2                         &
                                 , is_inside_quad, getNodeNumberTri            &
                                 , getNodeNumberQuad

    use sampling_headers,   only : verbose, point_is_found                     &
                                 , dist_tolerance, boundary_point

    type(grid_type),                 intent(in)    :: grid
    integer,                         intent(in)    :: ib
    integer,                         intent(in)    :: ivol
    integer,                         intent(out)   :: face
    integer,                         intent(out)   :: node_per_face

    integer                      :: ipts

    integer                               :: node
    integer                               :: face_index
    integer, dimension(4)                 :: corner
    real(dp), dimension(3)                :: pc, po, p1, p2, p3, p4
    real(dp), dimension(3)                :: t0

    real(dp), parameter                   :: one_forth = 1.0_dp/4.0_dp
    real(dp), parameter                   :: one_third = 1.0_dp/3.0_dp

    real(dp)                              :: dist, dist_tri, dist_quad
    real(dp)                              :: d1, d2, d3, d4, max_edge

    logical                               :: intersects_face

  continue

    node          = 0 ! to satisfy a g95 compile gripe
    face          = 0 ! to satisfy a g95 compile gripe
    node_per_face = 0 ! to satisfy a g95 compile gripe
    dist_tri      = 0.0d+0 ! to satisfy a g95 compile gripe
    dist_quad     = 0.0d+0 ! to satisfy a g95 compile gripe

    ipts                      = 1
    po(1:3)                   = boundary_point(1:3,ivol)
    point_is_found(ivol,ipts) = 0
    if ( grid%bc(ib)%nbfacet == 0 .and. grid%bc(ib)%nbfaceq == 0 ) return

! find the boundary face index
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    tris:  if ( grid%bc(ib)%nbfacet > 0 ) then

      loop_tris : do face_index = 1,grid%bc(ib)%nbfacet

        if ( point_is_found(ivol,ipts) == 1 ) cycle loop_tris

        corner(1:3) = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,1:3))
! global node
        node = getNodeNumberTri( grid, corner )
! elminate ghost overlap cells on partition boundaries
        if ( node > grid%nnodes0 ) cycle loop_tris

        dist            = huge(0.0_dp)
        intersects_face = .false.
        p1(1)  = grid%x(corner(1))
        p1(2)  = grid%y(corner(1))
        p1(3)  = grid%z(corner(1))
        p2(1)  = grid%x(corner(2))
        p2(2)  = grid%y(corner(2))
        p2(3)  = grid%z(corner(2))
        p3(1)  = grid%x(corner(3))
        p3(2)  = grid%y(corner(3))
        p3(3)  = grid%z(corner(3))
! test to see if the point is in reasonable proximity to the boundary
! distance from point to boundary face center
        pc     = one_third*(p1+p2+p3)
        t0     = pc-po
        dist     = sqrt(dot_product(t0,t0))
! longest local boundary face edge length
        d1 = sqrt(dot_product(p2-p1,p2-p1))
        d2 = sqrt(dot_product(p3-p2,p3-p2))
        d3 = sqrt(dot_product(p1-p3,p1-p3))
        max_edge = max( d1, d2, d3 )

        if ( dist > dist_tolerance .and. dist > max_edge ) cycle

        intersects_face = is_inside_triangle2 ( po, p1, p2, p3 )

        if ( intersects_face  ) then
            node_per_face = 3
                                face = face_index
          point_is_found(ivol,ipts)  = 1
        else
          if ( dist < dist_tri ) then
            dist_tri = dist
          endif
        endif

        if ( intersects_face .and. verbose ) then
        write(6,'(i6,3(a,i6),3(1x,f15.5))')  lmpi_id,                   &
          ', Point',ipts,'is in tri face',face_index,'of boundary', ib  &
          , po
        endif

      end do loop_tris

    endif tris
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!                finish tris
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    quads:  if ( grid%bc(ib)%nbfaceq > 0 ) then

      loop_quads : do face_index = 1,grid%bc(ib)%nbfaceq

        if ( point_is_found(ivol,ipts) == 1 ) cycle loop_quads

        corner(1:4) = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,1:4))
! elminate ghost overlap cells on partition boundaries
        node = getNodeNumberQuad( grid, corner )
        if ( node > grid%nnodes0 ) cycle loop_quads

        dist            = huge(0.0_dp)
        intersects_face = .false.
        p1(1)  = grid%x(corner(1))
        p1(2)  = grid%y(corner(1))
        p1(3)  = grid%z(corner(1))
        p2(1)  = grid%x(corner(2))
        p2(2)  = grid%y(corner(2))
        p2(3)  = grid%z(corner(2))
        p3(1)  = grid%x(corner(3))
        p3(2)  = grid%y(corner(3))
        p3(3)  = grid%z(corner(3))
        p4(1)  = grid%x(corner(4))
        p4(2)  = grid%y(corner(4))
        p4(3)  = grid%z(corner(4))
! test to see if the point is in reasonable proximity to the boundary
! 10 is just an arbitrary choice
        pc     = one_forth*(p1+p2+p3+p4)
        t0     = pc-po
        dist   = sqrt(dot_product(t0,t0))
! longest local boundary face edge length
        d1 = sqrt(dot_product(p2-p1,p2-p1))
        d2 = sqrt(dot_product(p3-p2,p3-p2))
        d3 = sqrt(dot_product(p3-p4,p3-p4))
        d4 = sqrt(dot_product(p4-p1,p4-p1))
        max_edge = max( d1, d2, d3, d4 )

        if ( dist > dist_tolerance .and. dist > max_edge ) cycle

        intersects_face = is_inside_quad( po, p1, p2, p3, p4 )

        if ( intersects_face ) then
            node_per_face = 4
                               face = face_index
          point_is_found(ivol,ipts) = 1
        else
          if ( dist < dist_quad ) then
            dist_quad = dist
          endif
        endif

        if ( intersects_face ) then
           if ( verbose ) write(6,'(i4,4(a,i6),a,1x,f12.4,2(a,3(1x,f10.3)))') &
            lmpi_id, ', Point',ipts,'is in quad face',face_index,             &
           'of boundary ', ib, 'for geometry ',ivol,', dist=',dist,', p=', po,&
           ', pc=', pc
        endif

      end do loop_quads

    end if quads
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!                         fininsh quads
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
  end subroutine get_face_index

!     if (mixed) then

!     else

!       call u_tau_tet(ibnode,f2ntb,elem(1)%c2n,                               &
!                     nnodes01,x,y,z,qnode,amut,nbnode,                        &
!                     nbfacet,elem(1)%ncell,force,                             &
!                     n_tot, xmc, ymc, zmc)

!     end if

!   end if

!================================= U_TAU_TET =================================80
!
! This gets friction velocity for a tet viscous boundary
!
!=============================================================================80

  subroutine u_tau_tet(eqn_set, face, node_per_face, ibnode, f2ntb, c2n        &
                    , nnodes01, x, y, z, qnode, ibc, u_tau_wf                  &
                    , nbnode, nbfacet, ncell                                   &
                    , n_tot, u_tau, rnuw, sampling_final_write, re_tau, cf )

    use info_depr,  only : re, xmach, tref
    use fluid, only : gm1, gamma, sutherland_constant
    use sampling_funclib,   only : get_taul
    use sampling_headers,   only : verbose, reference_length
    use thermo,           only : primitive_q_type, conserved_q_type, q_type
    use solution_types, only : compressible
    use bc_names,       only : viscous_wall_function, viscous_wf_trs !         &
!                            , viscous_weak_trs, viscous_weal_wall

    integer,                             intent(in)    :: eqn_set
    integer,                             intent(in)    :: face
    integer,                             intent(in)    :: node_per_face
    integer,                             intent(in)    :: nnodes01,ncell, n_tot
    integer,                             intent(in)    :: nbnode
    integer,                             intent(in)    :: nbfacet
    integer,  dimension(nbnode),         intent(in)    :: ibnode
    integer,  dimension(nbfacet,5),      intent(in)    :: f2ntb
    integer,  dimension(4,ncell),        intent(in)    :: c2n
    real(dp), dimension(nnodes01),       intent(in)    :: x,y,z
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    integer,                             intent(in)    :: ibc
    real(dp), dimension(:),              intent(in)    :: u_tau_wf
    real(dp),                            intent(out)   :: u_tau
    real(dp),                            intent(out)   :: rnuw
    logical,                             intent(in)    :: sampling_final_write
    real(dp),                            intent(out)   :: re_tau
    real(dp),                            intent(out)   :: cf

    integer :: n, node1, node2, node3, node4, icell
    integer :: bnode1, bnode2, bnode3

    real(dp) :: nx1, nx2, nx3, nx4
    real(dp) :: ny1, ny2, ny3, ny4
    real(dp) :: nz1, nz2, nz3, nz4
!   real(dp) :: conv
    real(dp) :: cstar, const
    real(dp) :: rho1, u1, v1, w1, p1, t1
    real(dp) :: rho2, u2, v2, w2, p2, t2
    real(dp) :: rho3, u3, v3, w3, p3, t3
    real(dp) :: rho4, u4, v4, w4, p4, t4
    real(dp) :: rmu, rmu1, rmu2, rmu3, rmu4
!   real(dp) :: pi
    real(dp) :: x1, y1, z1, x2, y2, z2, x3, y3, z3, x4, y4, z4, vol

    real(dp), dimension(3)                   :: gradx_cell
    real(dp), dimension(3)                   :: grady_cell
    real(dp), dimension(3)                   :: gradz_cell
    real(dp), dimension(3,3)                 :: tau
    real(dp), dimension(3)                   :: components
    real(dp), dimension(3)                   :: vel
    real(dp), dimension(3)                   :: vel_norm
    real(dp), dimension(3)                   :: vel_slip
    real(dp), dimension(3)                   :: slip_dir
    real(dp)                                 :: slip_sq
    real(dp), dimension(3)                   :: normal
    real(dp) :: xave_off
    real(dp) :: yave_off
    real(dp) :: zave_off
    real(dp) :: uave_off
    real(dp) :: vave_off
    real(dp) :: wave_off
    real(dp) :: xave_on
    real(dp) :: yave_on
    real(dp) :: zave_on
    real(dp) :: dist
    real(dp) :: area
    real(dp) :: temperature
    real(dp) :: rho_w
    real(dp) :: p_w
    real(dp) :: rnu_w
    real(dp) :: u_tau_w
    real(dp) :: tau_wall
    real(dp) :: yplus
    real(dp) :: uplus
    real(dp) :: dx, dy, dz

    real(dp), parameter :: zero   = 0.0_dp
    real(dp), parameter :: my_haf = 0.5_dp
    real(dp), parameter :: my_1   = 1.0_dp
!   real(dp), parameter :: my_2   = 2.0_dp
    real(dp), parameter :: my_3   = 3.0_dp
!   real(dp), parameter :: my_4   = 4.0_dp
    real(dp), parameter :: my_6   = 6.0_dp
!   real(dp), parameter :: my_180 = 180.0_dp
    integer,  parameter :: nodes_local = 4

  continue

    u1 = zero
    v1 = zero
    w1 = zero
    u2 = zero
    v2 = zero
    w2 = zero
    u3 = zero
    v3 = zero
    w3 = zero
    u4 = zero
    v4 = zero
    w4 = zero
    temperature = zero

! kick out if this is still zero
    if ( node_per_face == 0 ) return

! initialize
    u_tau = 0.0
    rnuw  = 0.0
!   Some constants

!   pi    = acos(-1.0_dp)
!   conv  = my_180/pi
    cstar = sutherland_constant/tref

    n = face

      node1 = ibnode(f2ntb(n,1))
      node2 = ibnode(f2ntb(n,2))
      node3 = ibnode(f2ntb(n,3))

      icell = f2ntb(n,4)

      node4 = c2n(1,icell) + c2n(2,icell) + c2n(3,icell)                   &
        + c2n(4,icell) - node1 - node2 - node3

      x1 = x(node1)
      y1 = y(node1)
      z1 = z(node1)

      x2 = x(node2)
      y2 = y(node2)
      z2 = z(node2)

      x3 = x(node3)
      y3 = y(node3)
      z3 = z(node3)

      x4 = x(node4)
      y4 = y(node4)
      z4 = z(node4)

      xave_on = (x1 + x2 + x3 ) / real(node_per_face, dp)
      yave_on = (y1 + y2 + y3 ) / real(node_per_face, dp)
      zave_on = (z1 + z2 + z3 ) / real(node_per_face, dp)
      xave_off = x4
      yave_off = y4
      zave_off = z4

!       Lets get outward normals (nx_i is for the face opposite node i)

      nx1 = my_haf*((y2 - y4)*(z3 - z4) - (y3 - y4)*(z2 - z4))
      ny1 = my_haf*((z2 - z4)*(x3 - x4) - (z3 - z4)*(x2 - x4))
      nz1 = my_haf*((x2 - x4)*(y3 - y4) - (x3 - x4)*(y2 - y4))

      nx2 = my_haf*((y3 - y4)*(z1 - z4) - (y1 - y4)*(z3 - z4))
      ny2 = my_haf*((z3 - z4)*(x1 - x4) - (z1 - z4)*(x3 - x4))
      nz2 = my_haf*((x3 - x4)*(y1 - y4) - (x1 - x4)*(y3 - y4))

      nx3 = my_haf*((y1 - y4)*(z2 - z4) - (y2 - y4)*(z1 - z4))
      ny3 = my_haf*((z1 - z4)*(x2 - x4) - (z2 - z4)*(x1 - x4))
      nz3 = my_haf*((x1 - x4)*(y2 - y4) - (x2 - x4)*(y1 - y4))

      nx4 = -nx1 -nx2 -nx3
      ny4 = -ny1 -ny2 -ny3
      nz4 = -nz1 -nz2 -nz3

!       Compute cell volume

      vol = (((y2-y1)*(z3-z1) - (y3-y1)*(z2-z1))*(x4-x1)                   &
            -((x2-x1)*(z3-z1) - (x3-x1)*(z2-z1))*(y4-y1)                   &
            +((x2-x1)*(y3-y1) - (x3-x1)*(y2-y1))*(z4-z1))/my_6

    if ( eqn_set == compressible .and. q_type == primitive_q_type ) then
      rho1  = qnode(1,node1)
      u1    = qnode(2,node1)
      v1    = qnode(3,node1)
      w1    = qnode(4,node1)
      p1    = qnode(5,node1)
      rho2  = qnode(1,node2)
      u2    = qnode(2,node2)
      v2    = qnode(3,node2)
      w2    = qnode(4,node2)
      p2    = qnode(5,node2)
      rho3  = qnode(1,node3)
      u3    = qnode(2,node3)
      v3    = qnode(3,node3)
      w3    = qnode(4,node3)
      p3    = qnode(5,node3)
      rho4  = qnode(1,node4)
      u4    = qnode(2,node4)
      v4    = qnode(3,node4)
      w4    = qnode(4,node4)
      p4    = qnode(5,node4)
      t1 = gamma*p1/rho1
      t2 = gamma*p2/rho2
      t3 = gamma*p3/rho3
      t4 = gamma*p4/rho4
    elseif ( eqn_set == compressible .and. q_type == conserved_q_type ) then
      rho1  = qnode(1,node1)
      u1    = qnode(2,node1)/rho1
      v1    = qnode(3,node1)/rho1
      w1    = qnode(4,node1)/rho1
      t1    = gamma * get_p( qnode(1:5,node1) ) / rho1
      p1    = gm1*(qnode(5,node1) - my_haf*rho1*(u1*u1 + v1*v1 + w1*w1))
      rho2  = qnode(1,node2)
      u2    = qnode(2,node2)/rho2
      v2    = qnode(3,node2)/rho2
      w2    = qnode(4,node2)/rho2
      p2    = gm1*(qnode(5,node2) - my_haf*rho2*(u2*u2 + v2*v2 + w2*w2))
      t2    = gamma * get_p( qnode(1:5,node2) ) / rho2
      rho3  = qnode(1,node3)
      u3    = qnode(2,node3)/rho3
      v3    = qnode(3,node3)/rho3
      w3    = qnode(4,node3)/rho3
      p3    = gm1*(qnode(5,node3) - my_haf*rho3*(u3*u3 + v3*v3 + w3*w3))
      t3    = gamma * get_p( qnode(1:5,node3) ) / rho3
      rho4  = qnode(1,node4)
      u4    = qnode(2,node4)/rho4
      v4    = qnode(3,node4)/rho4
      w4    = qnode(4,node4)/rho4
      p4    = gm1*(qnode(5,node4) - my_haf*rho4*(u4*u4 + v4*v4 + w4*w4))
      t4    = gamma * get_p( qnode(1:5,node4) ) / rho4
    else
      rho1 = 1._dp
    endif

      rho_w    = ( rho1 + rho2 + rho3 + rho4 ) / real(nodes_local, dp)
      p_w      = ( p1 + p2 + p3 + p4 ) / real(nodes_local, dp)
      temperature  = ( t1 + t2 + t3 + t4 ) / real(nodes_local, dp)
      uave_off = u4
      vave_off = v4
      wave_off = w4
!       Compute viscosity for the cell

      rmu1 = viscosity_law( cstar, t1 )
      rmu2 = viscosity_law( cstar, t2 )
      rmu3 = viscosity_law( cstar, t3 )
      rmu4 = viscosity_law( cstar, t4 )

      rmu = (rmu1 + rmu2 + rmu3 + rmu4) / real(nodes_local, dp)
      rnu_w  = rmu  / rho_w

      write(6,'(a,8(1x,f20.8))') 'rho1234 ', rho1,rho2,rho3,rho4,rho_w
      write(6,'(a,8(1x,f20.8))') '  p1234 ',   p1,  p2,  p3,  p4
      write(6,'(a,8(1x,f20.8))') '  t1234 ',   t1,  t2,  t3,  t4
      write(6,'(a,8(1x,f20.8))') ' mu1234 ', rmu1, rmu2, rmu3, rmu4,rmu

!       Now form gradients of velocity

      const = -my_1/(my_3*vol)

      gradx_cell(1) = const*((u1-u4)*nx1 + (u2-u4)*nx2 + (u3-u4)*nx3)
      grady_cell(1) = const*((u1-u4)*ny1 + (u2-u4)*ny2 + (u3-u4)*ny3)
      gradz_cell(1) = const*((u1-u4)*nz1 + (u2-u4)*nz2 + (u3-u4)*nz3)

      gradx_cell(2) = const*((v1-v4)*nx1 + (v2-v4)*nx2 + (v3-v4)*nx3)
      grady_cell(2) = const*((v1-v4)*ny1 + (v2-v4)*ny2 + (v3-v4)*ny3)
      gradz_cell(2) = const*((v1-v4)*nz1 + (v2-v4)*nz2 + (v3-v4)*nz3)

      gradx_cell(3) = const*((w1-w4)*nx1 + (w2-w4)*nx2 + (w3-w4)*nx3)
      grady_cell(3) = const*((w1-w4)*ny1 + (w2-w4)*ny2 + (w3-w4)*ny3)
      gradz_cell(3) = const*((w1-w4)*nz1 + (w2-w4)*nz2 + (w3-w4)*nz3)

      normal(1) = nx4
      normal(2) = ny4
      normal(3) = nz4
      area      = sqrt(dot_product(normal,normal))
      normal    = normal / area
      dx        = xave_off - xave_on
      dy        = yave_off - yave_on
      dz        = zave_off - zave_on
      dist      = abs(dx*normal(1) + dy*normal(2) + dz*normal(3))

      tau = get_taul ( gradx_cell(1:3), grady_cell(1:3), gradz_cell(1:3) )
      components = matmul( tau, normal )
!     tau_wall = rmu*abs(components(1))

! pick out local flow direction and normal to determine the
! components of the slip velocity.
! tau_wall for skin friction and wall units is thought
! to be the dot product of the direction of the slip velocity
! with the Reynolds stresses at the surface.
        vel      = (/uave_off,vave_off,wave_off/)
        vel_norm = dot_product(vel,normal)
        vel_slip = vel - vel_norm*normal
        slip_sq  = dot_product(vel_slip,vel_slip)
        slip_dir = zero
        if ( abs(slip_sq) > zero ) slip_dir = vel_slip / sqrt(slip_sq)
        tau_wall = dot_product(slip_dir,components)

      u_tau_w  = sqrt(abs(tau_wall)/rho_w)*sqrt(xmach/re)

    wall_function_flag:  if ( ibc == viscous_wall_function &
                      .or.    ibc == viscous_wf_trs        &
!                     .or.    ibc == viscous_weak_trs      &
!                     .or.    ibc == viscous_weak_wall     &
                            ) then

        bnode1 = f2ntb(face,1)
        bnode2 = f2ntb(face,2)
        bnode3 = f2ntb(face,3)
        u_tau_w = ( u_tau_wf(bnode1)          &
                  + u_tau_wf(bnode2)          &
                  + u_tau_wf(bnode3) ) / 3.0_dp
        write(6,'(a,8(1x,f15.5))') ' u_tau_tet:   wall_function: tri  face'
        write(6,'(a,i8,8(1x,f15.5))') ' u_tau_w', bnode1, u_tau_wf(bnode1)

      tau_wall = rho_w * u_tau_w * u_tau_w * ( re / xmach )

    endif wall_function_flag



      yplus    = ( dist * u_tau_w / rnu_w ) * (re/xmach)
      uplus    = sqrt(slip_sq) / u_tau_w
      re_tau   = ( u_tau_w * reference_length / rnu_w ) * ( re / xmach )
      cf       = 2.0_dp * (u_tau_w/xmach)**2

      if ( verbose .or. sampling_final_write ) then
        write(6,'(a,8(1x,f20.8))') 'tets    '
        write(6,'(a,8(1x,f20.8))') 'normals ', normal
        write(6,'(a,8(1x,f20.8))') 'delta   ', dx, dy, dz
        write(6,'(a,8(1x,f20.8))') 'dist    ', dist
        write(6,'(a,8(1x,es20.8))') 'rmu     ', rmu
        write(6,'(a,8(1x,f20.8))') 'dudy    ', abs(components(1))
        write(6,'(a,8(1x,f20.8))') 'comps   ', rmu*components
        write(6,'(a,8(1x,f20.8))') 'tau(1,:)', tau(1,1:3)
        write(6,'(a,8(1x,f20.8))') 'tau(2,:)', tau(2,1:3)
        write(6,'(a,8(1x,f20.8))') 'tau(3,:)', tau(3,1:3)
        write(6,'(a,8(1x,f20.8))') 'tau_wall', tau_wall
        write(6,'(a,8(1x,f20.8))') 'rho_wall', rho_w
        write(6,'(a,8(1x,f20.8))') 'p_wall  ', p_w
        write(6,'(a,8(1x,f20.8))') 'T_wall  ', temperature
        write(6,'(a,8(1x,f20.8))') 'recovery', &
                   (temperature - 1.0_dp) / (0.5_dp*gm1*xmach**2)
        write(6,'(a,8(1x,f20.8))') 'rnu_w   ', rnu_w
        write(6,'(a,8(1x,f20.8))') 'u_tau_w ', u_tau_w
        write(6,'(a,8(1x,f20.8))') 'yplus   ', yplus
        write(6,'(a,8(1x,f20.8))') 'uplus   ', uplus
        write(6,'(a,8(1x,f20.8))') 'ref.len.', reference_length
        write(6,'(a,8(1x,f15.5))') ' cf         = ', cf
        write(6,'(a,8(1x,f15.5))') ' Re_tau     = ', re_tau
        write(6,'(a,8(1x,f15.5))') ' Re (ref)   = ', re_tau*xmach/u_tau_w
        write(6,'(a,8(1x,f15.5))') ' Re (2*ref) = ', 2.*re_tau*xmach/u_tau_w
        write(*,*)
      endif

    u_tau = u_tau_w
    rnuw  = rnu_w

  end subroutine u_tau_tet


!=============================== U_TAU_MIXED =================================80
!
! This gets friction velocity for a
! viscous boundary for the general (mixed) element case
!
!=============================================================================80

  subroutine u_tau_mixed( eqn_set, face, node_per_face                         &
                        , nbnode, ibnode                                       &
                        , nbfacet, f2ntb, nbfaceq, f2nqb                       &
                        , nelem, elem, nnodes01,x,y,z,qnode, ibc, u_tau_wf     &
                        , mu_t_wf, yplus_wf, uplus_wf, omega_wf, k_wf          &
                        , n_tot, u_tau, rnuw, sampling_final_write, re_tau, cf )

    use info_depr,         only : re, xmach, tref, twod
    use file_utils,        only : available_unit
    use fluid,             only : gamma, gm1, sutherland_constant
    use element_types,     only : elem_type
    use element_defs,      only : max_face_per_cell, max_node_per_cell
    use utilities,         only : cell_gradients
    use sampling_funclib,  only : get_taul
    use sampling_headers,  only : verbose, reference_length, project_name
    use system_extensions, only : se_open
    use thermo,            only : primitive_q_type, conserved_q_type, q_type
    use solution_types,    only : compressible
    use bc_names,          only : viscous_wall_function, viscous_wf_trs !      &
!                            , viscous_weak_trs, viscous_weak_wall

    integer,                             intent(in)    :: eqn_set
    integer,                             intent(in)    :: face
    integer,                             intent(in)    :: node_per_face
    integer,                             intent(in)    :: nnodes01, n_tot
    integer,                             intent(in)    :: nbnode
    integer,                             intent(in)    :: nbfacet
    integer,                             intent(in)    :: nbfaceq
    integer,                             intent(in)    :: nelem
    integer,  dimension(nbnode),         intent(in)    :: ibnode
    integer,  dimension(nbfacet,5),      intent(in)    :: f2ntb
    integer,  dimension(nbfaceq,6),      intent(in)    :: f2nqb
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(nnodes01),       intent(in)    :: x, y, z
    integer,                             intent(in)    :: ibc
    real(dp), dimension(:),              intent(in)    :: u_tau_wf
    real(dp), dimension(:),              intent(in)    :: uplus_wf
    real(dp), dimension(:),              intent(in)    :: mu_t_wf
    real(dp), dimension(:),              intent(in)    :: yplus_wf
    real(dp), dimension(:),              intent(in)    :: omega_wf
    real(dp), dimension(:),              intent(in)    :: k_wf
    type(elem_type),  dimension(nelem),  intent(in)    :: elem
    real(dp),                            intent(out)   :: u_tau
    real(dp),                            intent(out)   :: rnuw
    logical,                             intent(in)    :: sampling_final_write
    real(dp),                            intent(out)   :: re_tau
    real(dp),                            intent(out)   :: cf

    integer, dimension(max_node_per_cell) :: c2n_cell, node_map
    character(len=80) :: filename

    integer :: iunit
    integer :: face_2d, i, n, icell, node, ielem
    integer :: bnode1, bnode2, bnode3, bnode4
    integer :: i_local, edges_local, nodes_local
    integer :: node_per_cell
    integer :: face_node
    integer :: cell_node

    real(dp) :: cstar, rmu, rho
    real(dp) :: x1, x2, x3, x4
    real(dp) :: y1, y2, y3, y4
    real(dp) :: z1, z2, z3, z4
    real(dp) :: cell_vol
    real(dp) :: rnu_w
    real(dp) :: rho_w, p_w, tau_wall, dist, u_tau_w
    real(dp) :: uplus, yplus, mut_wf
    real(dp) :: omega_wf_avg, k_wf_avg
    real(dp) :: uplus_wf_avg, yplus_wf_avg
    real(dp) :: temperature
    real(dp) :: area
    real(dp) :: factor

    real(dp), dimension(max_face_per_cell)   :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)   :: u_node, v_node, w_node
    real(dp), dimension(max_node_per_cell)   :: t_node, p_node, mu_node
    real(dp), dimension(max_node_per_cell)   :: x_node, y_node, z_node
    real(dp), dimension(4,max_node_per_cell) :: q_node
    real(dp), dimension(3,3)                 :: tau
    real(dp), dimension(3)                   :: components
    real(dp), dimension(3)                   :: vel
    real(dp), dimension(3)                   :: vel_norm
    real(dp), dimension(3)                   :: vel_slip
    real(dp), dimension(3)                   :: slip_dir
    real(dp)                                 :: slip_sq
    real(dp)                                 :: dx, dy, dz

    real(dp), dimension(4) :: gradx_cell, grady_cell, gradz_cell

    real(dp), parameter :: my_haf = 0.5_dp

    integer, dimension(:), allocatable :: node_flag

    real(dp) :: uave_off, vave_off, wave_off
    real(dp) :: total_weight_on
    real(dp) :: total_weight_off
    real(dp) :: xave_off
    real(dp) :: yave_off
    real(dp) :: zave_off
    real(dp) :: xave_on
    real(dp) :: yave_on
    real(dp) :: zave_on

    real(dp), dimension(3) :: normal
    real(dp), parameter    :: zero = 0.0_dp

  continue

    node        = 0      ! to satisfy g95 gripes
    xave_on     = 0.0d+0 ! to satisfy g95 gripes
    yave_on     = 0.0d+0 ! to satisfy g95 gripes
    zave_on     = 0.0d+0 ! to satisfy g95 gripes
    uave_off    = 0.0d+0 ! to satisfy g95 gripes
    rho         = 0.0d+0 ! to satisfy g95 gripes
    p_w         = 0.0d+0 ! to satisfy g95 gripes
    temperature = 0.0d+0 ! to satisfy g95 gripes
    rmu         = 0.0d+0 ! to satisfy g95 gripes
    u_tau_w      = 0.0d+0 ! to satisfy g95 gripes
    k_wf_avg     = 0.0d+0 ! to satisfy g95 gripes
    omega_wf_avg = 0.0d+0 ! to satisfy g95 gripes
    yplus_wf_avg = 0.0d+0 ! to satisfy g95 gripes
    uplus_wf_avg = 0.0d+0 ! to satisfy g95 gripes
    mut_wf       = 0.0d+0 ! to satisfy g95 gripes

    if ( .not.allocated(node_flag)) allocate( node_flag(max_node_per_cell) )

    u_tau = 0.0
    rnuw  = 0.0

!   define some constants

    cstar = sutherland_constant/tref

    n = face

! kick out if this is still zero
    if ( node_per_face == 0 ) return

    wall_boundary: select case ( node_per_face )

! Contributions from tria boundary faces
    case ( 3 )

!   surface_trias : do n = 1, nface_eval

        bnode1 = ibnode(f2ntb(n,1))
        bnode2 = ibnode(f2ntb(n,2))
        bnode3 = ibnode(f2ntb(n,3))

        icell = f2ntb(n,4)
        ielem = f2ntb(n,5)
        node_per_cell = elem(ielem)%node_per_cell

! spot the surface nodes

        node_flag = 1
        do cell_node = 1, node_per_cell
          do face_node = 1, node_per_face
              if ( node_per_face == 3 ) then
                node = ibnode(f2ntb(face,face_node))
              else if ( node_per_face == 4 ) then
                node = ibnode(f2nqb(face,face_node))
              endif
!           if a cell node lies on the surface, set its flag to zero
            if (elem(ielem)%c2n(cell_node,icell) == node) then
               node_flag(cell_node) = 0
            end if
          end do
        end do
!       write(6,'(a,8(i3))') 'node_flag   ', node_flag
!------------------------------------------------------------------
        uave_off         = 0.0_dp
        vave_off         = 0.0_dp
        wave_off         = 0.0_dp
        xave_off         = 0.0_dp
        yave_off         = 0.0_dp
        zave_off         = 0.0_dp
        total_weight_on  = 0.0_dp
        total_weight_off = 0.0_dp
        do cell_node = 1, node_per_cell
          node = elem(ielem)%c2n(cell_node,icell)
          if (node_flag(cell_node) == 0) then     ! surface nodes
            xave_on = xave_on + x(node)
            yave_on = yave_on + y(node)
            zave_on = zave_on + z(node)
            total_weight_on = total_weight_on + 1.0_dp
          else                                    ! off-surface nodes
        if ( eqn_set == compressible .and. q_type == primitive_q_type ) then
            uave_off = uave_off + qnode(2,node)
            vave_off = vave_off + qnode(3,node)
            wave_off = wave_off + qnode(4,node)
        elseif ( eqn_set == compressible .and. q_type == conserved_q_type ) then
            uave_off = uave_off + qnode(2,node)/qnode(1,node)
            vave_off = vave_off + qnode(3,node)/qnode(1,node)
            wave_off = wave_off + qnode(4,node)/qnode(1,node)
        endif
            xave_off = xave_off + x(node)
            yave_off = yave_off + y(node)
            zave_off = zave_off + z(node)
            total_weight_off = total_weight_off + 1.0_dp
          end if
        end do

        xave_on  = xave_on /total_weight_on
        yave_on  = yave_on /total_weight_on
        zave_on  = zave_on /total_weight_on

        uave_off = uave_off/total_weight_off
        vave_off = vave_off/total_weight_off
        wave_off = wave_off/total_weight_off
        xave_off = xave_off/total_weight_off
        yave_off = yave_off/total_weight_off
        zave_off = zave_off/total_weight_off


!       set some loop indicies and local mapping arrays depending on whether
!       we are doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (elem(ielem)%local_f2e(face_2d,1) /=                              &
              elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = elem(ielem)%edge_per_cell

        end if

!       copy c2n and local_f2n arrays from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

!       compute cell averaged viscosity by looping over the nodes in the
!       element and gathering their contributions, then average at the end

        cell_vol = 0.0_dp

        rmu = 0.0_dp

        x_node(:)   = 0.0_dp
        y_node(:)   = 0.0_dp
        z_node(:)   = 0.0_dp
        u_node(:)   = 0.0_dp
        v_node(:)   = 0.0_dp
        w_node(:)   = 0.0_dp
        p_node(:)   = 0.0_dp
        t_node(:)   = 0.0_dp
        mu_node(:)  = 0.0_dp
        q_node(:,:) = 0.0_dp

        node_loop1 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

        if ( eqn_set == compressible .and. q_type == primitive_q_type ) then
          u_node(i) = qnode(2,node)
          v_node(i) = qnode(3,node)
          w_node(i) = qnode(4,node)
          p_node(i) = qnode(5,node)
          t_node(i) = gamma * p_node(i) / qnode(1,node)
        elseif ( eqn_set == compressible .and. q_type == conserved_q_type ) then
          u_node(i) = qnode(2,node)/qnode(1,node)
          v_node(i) = qnode(3,node)/qnode(1,node)
          w_node(i) = qnode(4,node)/qnode(1,node)
          p_node(i) = get_p( qnode(1:5,node) )
          t_node(i) = gamma * p_node(i) / qnode(1,node)
        endif

          mu_node(i) = viscosity_law( cstar, t_node(i) )

          rho         = rho + qnode(1,node)
          p_w         = p_w + p_node(i)
          temperature = temperature + t_node(i)
          rmu         = rmu + mu_node(i)

        end do node_loop1

!       now compute cell average by dividing by the number of nodes
!       that contributed

        rho         = rho / real(nodes_local, dp)
        temperature = temperature / real(nodes_local, dp)
        rho_w       = rho
        p_w         = p_w / real(nodes_local, dp)

        rmu         = rmu / real(nodes_local, dp)
        rnu_w       = rmu / rho_w


!       now we get this boundary face's normal

        x1 = x(bnode1)
        y1 = y(bnode1)
        z1 = z(bnode1)

        x2 = x(bnode2)
        y2 = y(bnode2)
        z2 = z(bnode2)

        x3 = x(bnode3)
        y3 = y(bnode3)
        z3 = z(bnode3)

!       - sign for outward facing normal

        normal(1) = -my_haf*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
        normal(2) = -my_haf*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
        normal(3) = -my_haf*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )
        area      = sqrt(dot_product(normal,normal))
        normal    = normal / area
        dx        = xave_off - xave_on
        dy        = yave_off - yave_on
        dz        = zave_off - zave_on
        dist      = abs(dx*normal(1) + dy*normal(2) + dz*normal(3))
!-----------------------------------------------------------------------------80
        q_node(1,:) = u_node(:)
        q_node(2,:) = v_node(:)
        q_node(3,:) = w_node(:)
        q_node(4,:) = t_node(:)

        call cell_gradients(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            4, q_node, elem(ielem)%local_f2n,                  &
                            elem(ielem)%e2n_2d, gradx_cell, grady_cell,        &
                            gradz_cell, cell_vol, nx, ny, nz)

        tau = get_taul ( gradx_cell(1:3), grady_cell(1:3), gradz_cell(1:3) )
        components = matmul( tau, normal )
!       tau_wall = rmu*abs(components(1))

! pick out local flow direction and normal to determine the
! components of the slip velocity.
! tau_wall for skin friction and wall units is thought
! to be the dot product of the direction of the slip velocity
! with the Reynolds stresses at the surface.
        vel      = (/uave_off,vave_off,wave_off/)
        vel_norm = dot_product(vel,normal)
        vel_slip = vel - vel_norm*normal
        slip_sq  = dot_product(vel_slip,vel_slip)
        slip_dir = zero
        if ( abs(slip_sq) > zero ) slip_dir = vel_slip / sqrt(slip_sq)
        tau_wall = rmu*dot_product(slip_dir,components)

        u_tau_w  = sqrt(abs(tau_wall)/rho_w)*sqrt(xmach/re)

    case ( 4 )
! Contributions from quad boundary faces

!   nface_eval = nbfaceq

!   surface_quads : do n = 1, nface_eval

        bnode1 = ibnode(f2nqb(n,1))
        bnode2 = ibnode(f2nqb(n,2))
        bnode3 = ibnode(f2nqb(n,3))
        bnode4 = ibnode(f2nqb(n,4))

        icell         = f2nqb(n,5)
        ielem         = f2nqb(n,6)
        node_per_cell = elem(ielem)%node_per_cell

! spot the surface nodes

        node_flag = 1
        do cell_node = 1, node_per_cell
          do face_node = 1, node_per_face
              if ( node_per_face == 3 ) then
                node = ibnode(f2ntb(face,face_node))
              else if ( node_per_face == 4 ) then
                node = ibnode(f2nqb(face,face_node))
              endif
!           if a cell node lies on the surface, set its flag to zero
            if (elem(ielem)%c2n(cell_node,icell) == node) then
               node_flag(cell_node) = 0
            end if
          end do
        end do
!       write(6,'(a,8(i3))') 'node_flag   ', node_flag
!------------------------------------------------------------------
        uave_off         = 0.0_dp
        vave_off         = 0.0_dp
        wave_off         = 0.0_dp
        xave_off         = 0.0_dp
        yave_off         = 0.0_dp
        zave_off         = 0.0_dp
        total_weight_on  = 0.0_dp
        total_weight_off = 0.0_dp
        do cell_node = 1, node_per_cell
          node = elem(ielem)%c2n(cell_node,icell)
          if (node_flag(cell_node) == 0) then     ! surface nodes
            xave_on = xave_on + x(node)
            yave_on = yave_on + y(node)
            zave_on = zave_on + z(node)
            total_weight_on = total_weight_on + 1.0_dp
          else                                    ! off-surface nodes
        if ( eqn_set == compressible .and. q_type == primitive_q_type ) then
            uave_off = uave_off + qnode(2,node)
            vave_off = vave_off + qnode(3,node)
            wave_off = wave_off + qnode(4,node)
        elseif ( eqn_set == compressible .and. q_type == conserved_q_type ) then
            uave_off = uave_off + qnode(2,node)/qnode(1,node)
            vave_off = vave_off + qnode(3,node)/qnode(1,node)
            wave_off = wave_off + qnode(4,node)/qnode(1,node)
        endif
            xave_off = xave_off + x(node)
            yave_off = yave_off + y(node)
            zave_off = zave_off + z(node)
            total_weight_off = total_weight_off + 1.0_dp
          end if
        end do

        xave_on  = xave_on /total_weight_on
        yave_on  = yave_on /total_weight_on
        zave_on  = zave_on /total_weight_on

        uave_off = uave_off/total_weight_off
        vave_off = vave_off/total_weight_off
        wave_off = wave_off/total_weight_off
        xave_off = xave_off/total_weight_off
        yave_off = yave_off/total_weight_off
        zave_off = zave_off/total_weight_off
!------------------------------------------------------------------
!       set some loop indicies and local mapping arrays depending on whether
!       we are doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (elem(ielem)%local_f2e(face_2d,1) /=                              &
              elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = elem(ielem)%edge_per_cell

        end if

!       write(6,'(a,8(i3))') 'node_map    ', node_map
!       copy c2n and local_f2n arrays from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

!       compute cell averaged viscosity by looping over the nodes in the
!       element and gathering their contributions, then average at the end

        cell_vol = 0.0_dp

        rmu = 0.0_dp

        x_node(:)   = 0.0_dp
        y_node(:)   = 0.0_dp
        z_node(:)   = 0.0_dp
        u_node(:)   = 0.0_dp
        v_node(:)   = 0.0_dp
        w_node(:)   = 0.0_dp
        p_node(:)   = 0.0_dp
        t_node(:)   = 0.0_dp
        mu_node(:)  = 0.0_dp
        q_node(:,:) = 0.0_dp

        node_loop2 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

        if ( eqn_set == compressible .and. q_type == primitive_q_type ) then
          u_node(i) = qnode(2,node)
          v_node(i) = qnode(3,node)
          w_node(i) = qnode(4,node)
          p_node(i) = qnode(5,node)
          t_node(i) = gamma * p_node(i) / qnode(1,node)
        elseif ( eqn_set == compressible .and. q_type == conserved_q_type ) then
          u_node(i) = qnode(2,node)/qnode(1,node)
          v_node(i) = qnode(3,node)/qnode(1,node)
          w_node(i) = qnode(4,node)/qnode(1,node)
          p_node(i) = get_p( qnode(1:5,node) )
          t_node(i) = gamma * p_node(i) / qnode(1,node)
        endif

          mu_node(i) = viscosity_law( cstar, t_node(i) )

          rho         = rho + qnode(1,node)
          p_w         = p_w / real(nodes_local, dp)
          temperature = temperature + t_node(i)
          rmu         = rmu + mu_node(i)

        end do node_loop2

!       now compute cell average by dividing by the number of nodes
!       that contributed

        rho         = rho / real(nodes_local, dp)
        p_w         = p_w / real(nodes_local, dp)
        temperature = temperature / real(nodes_local, dp)
        rho_w       = rho

        rmu         = rmu / real(nodes_local, dp)
        rnu_w       = rmu / rho_w

!       now we get this boundary face's normal

        x1 = x(bnode1)
        y1 = y(bnode1)
        z1 = z(bnode1)

        x2 = x(bnode2)
        y2 = y(bnode2)
        z2 = z(bnode2)

        x3 = x(bnode3)
        y3 = y(bnode3)
        z3 = z(bnode3)

        x4 = x(bnode4)
        y4 = y(bnode4)
        z4 = z(bnode4)

!       - sign for outward facing normal

        normal(1) = -my_haf*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
        normal(2) = -my_haf*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
        normal(3) = -my_haf*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )
        area      = sqrt(dot_product(normal,normal))
        normal    = normal / area
        dx        = xave_off - xave_on
        dy        = yave_off - yave_on
        dz        = zave_off - zave_on
        dist      = abs(dx*normal(1) + dy*normal(2) + dz*normal(3))
!-----------------------------------------------------------------------------80
        q_node(1,:) = u_node(:)
        q_node(2,:) = v_node(:)
        q_node(3,:) = w_node(:)
        q_node(4,:) = t_node(:)

        call cell_gradients(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            4, q_node, elem(ielem)%local_f2n,                  &
                            elem(ielem)%e2n_2d, gradx_cell, grady_cell,        &
                            gradz_cell, cell_vol, nx, ny, nz)

        tau = get_taul ( gradx_cell(1:3), grady_cell(1:3), gradz_cell(1:3) )
        components = matmul( tau, normal )
!       tau_wall = rmu*abs(components(1))

        vel      = (/uave_off,vave_off,wave_off/)
        vel_norm = dot_product(vel,normal)
        vel_slip = vel - vel_norm*normal
        slip_sq  = dot_product(vel_slip,vel_slip)
        slip_dir = zero
        if ( abs(slip_sq) > zero ) slip_dir = vel_slip / sqrt(slip_sq)
        tau_wall = rmu*dot_product(slip_dir,components)

        u_tau_w  = sqrt(abs(tau_wall)/rho_w)*sqrt(xmach/re)

    end select wall_boundary

    write(6,'(a,i10)') ' ibc               = ', ibc
    wall_function_flag:  if ( ibc == viscous_wall_function &
                       .or.   ibc == viscous_wf_trs    ) then

        write(6,'(a,i10)') ' node_per_face = ', node_per_face
      select case ( node_per_face )
      case ( 3 )
        bnode1 = f2ntb(face,1)
        bnode2 = f2ntb(face,2)
        bnode3 = f2ntb(face,3)
        u_tau_w = ( u_tau_wf(bnode1)          &
                  + u_tau_wf(bnode2)          &
                  + u_tau_wf(bnode3) ) / 3.0_dp
        write(6,'(a,8(1x,f15.5))') ' u_tau_mixed: wall_function: tri  face'
        write(6,'(a,i8,8(1x,f15.5))') ' u_tau_w', bnode1, u_tau_wf(bnode1)
      case ( 4 )
        factor = 4.0_dp
        if ( twod ) factor = 2.0_dp
        bnode1 = f2nqb(face,1)
        bnode2 = f2nqb(face,2)
        bnode3 = f2nqb(face,3)
        bnode4 = f2nqb(face,4)
        u_tau_w  = ( u_tau_wf(bnode1)          &
                   + u_tau_wf(bnode2)          &
                   + u_tau_wf(bnode3)          &
                   + u_tau_wf(bnode4) ) / factor
        k_wf_avg = ( k_wf(bnode1)          &
                   + k_wf(bnode2)          &
                   + k_wf(bnode3)          &
                   + k_wf(bnode4) ) / factor
        omega_wf_avg = ( omega_wf(bnode1)          &
                       + omega_wf(bnode2)          &
                       + omega_wf(bnode3)          &
                       + omega_wf(bnode4) ) / factor
        yplus_wf_avg  = ( yplus_wf(bnode1)          &
                        + yplus_wf(bnode2)          &
                        + yplus_wf(bnode3)          &
                        + yplus_wf(bnode4) ) / factor
        uplus_wf_avg  = ( uplus_wf(bnode1)          &
                        + uplus_wf(bnode2)          &
                        + uplus_wf(bnode3)          &
                        + uplus_wf(bnode4) ) / factor
        mut_wf  = ( mu_t_wf(bnode1)                 &
                    + mu_t_wf(bnode2)               &
                    + mu_t_wf(bnode3)               &
                   + mu_t_wf(bnode4) ) / factor
        write(6,'(a,8(1x,f15.5))') ' u_tau_mixed: wall_function: quad face'
        write(6,'(a,i8,8(1x,f15.5))') ' u_tau_w', bnode1, &
        u_tau_wf(bnode1), mut_wf

      end select

      tau_wall = rho_w * u_tau_w * u_tau_w * ( re / xmach )

    endif wall_function_flag

!write(6,'(a,8(1x,f20.8))') 'tau     '
!write(6,'(3(1x,f20.8))') tau
!-----------------------------------------------------------------------------80
!       components with respect to face normal
        yplus    = ( dist * u_tau_w / rnu_w ) * (re/xmach)
        uplus    = sqrt(slip_sq) / u_tau_w
        re_tau   = ( u_tau_w * reference_length / rnu_w ) * ( re / xmach )
        cf       = 2.0_dp * (u_tau_w/xmach)**2
        if ( verbose .or. sampling_final_write ) then
          write(6,'(a,1x,i10     )') 'mixed, ibc =', ibc
          write(6,'(a,8(1x,f20.8))') 'slip    ', vel_slip
          write(6,'(a,8(1x,f20.8))') 'normals ', normal
          write(6,'(a,8(1x,f20.8))') 'delta   ', dx, dy, dz
          write(6,'(a,8(1x,f20.8))') 'dist    ', dist
          write(6,'(a,8(1x,es20.8))') 'rmu     ', rmu
          write(6,'(a,8(1x,f20.8))') 'dudy    ', abs(components(1))
          write(6,'(a,8(1x,f20.8))') 'comps   ', rmu*components
          write(6,'(a,8(1x,f20.8))') 'tau(1,:)', tau(1,1:3)
          write(6,'(a,8(1x,f20.8))') 'tau(2,:)', tau(2,1:3)
          write(6,'(a,8(1x,f20.8))') 'tau(3,:)', tau(3,1:3)
          write(6,'(a,8(1x,f20.8))') 'tau_wall', tau_wall
          write(6,'(a,8(1x,f20.8))') 'rho_wall', rho_w
          write(6,'(a,8(1x,f20.8))') 'p_wall  ', p_w
          write(6,'(a,8(1x,f20.8))') 'T_wall  ', temperature
          write(6,'(a,8(1x,f20.8))') 'recovery', &
                     (temperature - 1.0_dp) / (0.5_dp*gm1*xmach**2)
          write(6,'(a,8(1x,f20.8))') 'rnu_w   ', rnu_w
          write(6,'(a,8(1x,f20.8))') 'u_tau_w ', u_tau_w
          write(6,'(a,8(1x,f20.8))') 'mut_wf  ', mut_wf
          write(6,'(a,8(1x,f20.8))') 'yplus   ', yplus
          write(6,'(a,8(1x,f20.8))') 'uplus   ', uplus
        write(6,'(a,8(1x,f20.8))') 'ref.len.', reference_length
        write(6,'(a,8(1x,f15.5))') ' cf         = ', cf
        write(6,'(a,8(1x,f15.5))') ' Re_tau     = ', re_tau
        write(6,'(a,8(1x,f15.5))') ' Re (ref)   = ', re_tau*xmach/u_tau_w
        write(6,'(a,8(1x,f15.5))') ' Re (2*ref) = ', 2.*re_tau*xmach/u_tau_w
        write(6,'(a,8(1x,f20.8))') 'plotting', yplus, u_tau_w, uplus
          write(*,*)

        iunit = available_unit()
        filename = trim(project_name)//'_sampling_wall_stats.dat'
        call se_open(iunit,file=filename,form='formatted')
        write(iunit,'(20(1x,f20.10))') yplus, uplus, u_tau_w &
        , yplus_wf_avg, uplus_wf_avg                         &
        , mut_wf, omega_wf_avg, k_wf_avg, dist

        endif



    deallocate( node_flag )

    u_tau = u_tau_w
    rnuw  = rnu_w

  end subroutine u_tau_mixed

!============================== CUBIC_LAM ====================================80
!
!  Solves a cubic equation for the eigenvalues of the anisotropy tensor
!
!=============================================================================80
  subroutine cubic_lam( ap,aq,ar,xlam1,xlam2,xlam3)

    implicit none

    real(dp), intent(in) :: ap
    real(dp), intent(in) :: aq
    real(dp), intent(in) :: ar
    real(dp), intent(out) :: xlam1
    real(dp), intent(out) :: xlam2
    real(dp), intent(out) :: xlam3

    real(dp) :: pi, tpi3, aa, ab, ad
    real(dp) :: raat, rbbt, raa, rbb, coss, theta
  continue

    pi    = acos(-1.0_dp)
    tpi3  = 2.0_dp*pi/3.0_dp
    xlam1 = 0.0_dp
    xlam2 = 0.0_dp
    xlam3 = 0.0_dp
!   *******-------------------------------------------*******
!   Solve cubic eqn now
!   (xlam)**3 + ap(xlam)**2 + aq(xlam) + ar = 0
!   *******-------------------------------------------*******
    aa = (aq-ap*ap/3.0_dp)
    ab = (2.0_dp*ap*ap*ap-9.0_dp*ap*aq+27.0_dp*ar)/27.0_dp
    ad = (ab*ab/4.0_dp)+(aa*aa*aa)/27.0_dp
!   if ad > 0,  get one real and 2 imaginary roots:

    if ( ad > 0.0_dp ) then

      write(6,'(''rutrow... imag roots!'')')
      raat =-0.50_dp*ab+sqrt(ad)
      rbbt =-0.50_dp*ab-sqrt(ad)
      raa =(abs(raat))**(1.0_dp/3.0_dp)
      raa =sign(raa,raat)
      rbb =(abs(rbbt))**(1.0_dp/3.0_dp)
      rbb =sign(rbb,rbbt)
      xlam1 =-ap/3.+raa+rbb
!   real part of imaj roots:
      xlam2 =-ap/3.0_dp-.50_dp*raa-.50_dp*rbb
      xlam3 =-ap/3.0_dp-.50_dp*raa-.50_dp*rbb

    else
!   if ad < 0, get 3 real roots:
      coss = 0.0_dp
      if ( aa < 0.0_dp ) &
      coss = -ab/2.0_dp/sqrt(-(aa*aa*aa)/27.0_dp)
      theta = acos(coss)
      xlam1 = -ap/3.0_dp+2.0_dp*sqrt(-aa/3.0_dp)*cos(theta/3.0_dp)
      xlam2 = -ap/3.0_dp+2.0_dp*sqrt(-aa/3.0_dp)*cos(tpi3+theta/3.0_dp)
      xlam3 = -ap/3.0_dp+2.0_dp*sqrt(-aa/3.0_dp)*cos(2.0_dp*tpi3+theta/3.0_dp)

    end if

  end subroutine cubic_lam

!-------------------------------- GET_VAR ------------------------------------80
!
!  Retrieve output variable data
!
!-----------------------------------------------------------------------------80
    function get_var( var, eqn_set                                             &
                    , px, py, pz, slen, volume                                 &
                    , qp, qt, qtavg, qtrms, turres, mut                        &
                    , turbfluctuations, iflagslen, qcriterion                  &
                    , vortx, vorty, vortz, div_vel                             &
                    , res, res_gcl, cgamma, aspect_ratio                       &
                    , skip_q, iblank, imesh                                    &
                    , hrles_blend, c_q, c_fx, c_fy, c_fz, c_f, y_plus          &
                    , u_avg, v_avg, w_avg, rho_i, q_rg, lambda                 &
                    , sw, pw, pv, qv, rv, global_node, u_plus, k_plus, w_plus  &
                    , t11_plus, t12_plus, t13_plus                             &
                    , t22_plus, t23_plus, t33_plus, re_tau, tau_ij, b_ij       &
                    , cmu_star, eta1s, eta2s, tr_bs, iib, iiib, vgradrho       &
                    , bird_breakdown, mu_t_tavg, vort_mag_tavg                 &
                    , vort_x_tavg, vort_y_tavg, vort_z_tavg, vort_rms          &
                    , n_turb, pk_ij, tau_sgs, gradients, f_r1                  &
                    , production_k, destruction_k, diffusion_k, transport_k    &
                    , sst_f1, xi_k , n_grd, reconstruction_limiter_phi         &
                            ) result ( value )

    use fluid,                only : gamma, gm1, xgm1g, xgm1                   &
                                   , sutherland_constant
    use info_depr,            only : xmach, tref, re
    use fun3d_constants,      only : my_1
    use generic_gas_map,      only : n_species, n_energy, n_mom
    use shared_gas_variables, only : spec_propv
    use lmpi,                 only : lmpi_id
    use turb_kw_const,        only : betastar
    use solution_types,       only : compressible, incompressible, generic_gas
    use thermodynamics,       only : thermo

    real(dp)                              :: value

    character(len=80),        intent(in)  :: var
    integer,                  intent(in)  :: eqn_set, n_turb, n_grd
    real(dp),                 intent(in)  :: px, py, pz
    real(dp), dimension(1  ), intent(in)  :: slen
    real(dp), dimension(1  ), intent(in)  :: volume
    real(dp), dimension(5  ), intent(in)  :: qp
    real(dp), dimension(n_turb), intent(in)  :: qt
    real(dp), dimension(5  ), intent(in)  :: qtavg
    real(dp), dimension(5  ), intent(in)  :: qtrms
    real(dp), dimension(n_turb), intent(in)  :: turres
    real(dp), dimension(1  ), intent(in)  :: mut
    real(dp), dimension(6  ), intent(in)  :: turbfluctuations
    real(dp), dimension(1  ), intent(in)  :: iflagslen
    real(dp), dimension(1  ), intent(in)  :: qcriterion
    real(dp), dimension(1  ), intent(in)  :: vortx
    real(dp), dimension(1  ), intent(in)  :: vorty
    real(dp), dimension(1  ), intent(in)  :: vortz
    real(dp), dimension(1  ), intent(in)  :: div_vel
    real(dp), dimension(5  ), intent(in)  :: res
    real(dp), dimension(1  ), intent(in)  :: res_gcl
    real(dp), dimension(1  ), intent(in)  :: cgamma
    real(dp), dimension(1  ), intent(in)  :: aspect_ratio
    real(dp), dimension(1  ), intent(in)  :: skip_q
    real(dp), dimension(1  ), intent(in)  :: iblank
    real(dp), dimension(1  ), intent(in)  :: imesh
    real(dp), dimension(1  ), intent(in)  :: hrles_blend
    real(dp), dimension(1  ), intent(in)  :: c_q
    real(dp), dimension(1  ), intent(in)  :: c_f
    real(dp), dimension(1  ), intent(in)  :: c_fx
    real(dp), dimension(1  ), intent(in)  :: c_fy
    real(dp), dimension(1  ), intent(in)  :: c_fz
    real(dp), dimension(1  ), intent(in)  :: y_plus
    real(dp), dimension(1  ), intent(in)  :: u_avg
    real(dp), dimension(1  ), intent(in)  :: v_avg
    real(dp), dimension(1  ), intent(in)  :: w_avg
    real(dp), dimension(:  ), intent(in), optional :: lambda
    real(dp), dimension(n_species), intent(in)  :: rho_i
    real(dp), dimension(7  ), intent(in)  :: q_rg
    real(dp), dimension(1  ), intent(in)  :: sw
    real(dp), dimension(1  ), intent(in)  :: pw
    real(dp), dimension(1  ), intent(in)  :: pv
    real(dp), dimension(1  ), intent(in)  :: qv
    real(dp), dimension(1  ), intent(in)  :: rv
    real(dp), dimension(1  ), intent(in)  :: global_node
    real(dp), dimension(1  ), intent(in)  :: u_plus
    real(dp), dimension(1  ), intent(in)  :: k_plus
    real(dp), dimension(1  ), intent(in)  :: w_plus
    real(dp), dimension(1  ), intent(in)  :: t11_plus
    real(dp), dimension(1  ), intent(in)  :: t12_plus
    real(dp), dimension(1  ), intent(in)  :: t13_plus
    real(dp), dimension(1  ), intent(in)  :: t22_plus
    real(dp), dimension(1  ), intent(in)  :: t23_plus
    real(dp), dimension(1  ), intent(in)  :: t33_plus
    real(dp),                 intent(in)  :: re_tau
    real(dp), dimension(6  ), intent(in)  :: tau_ij
    real(dp), dimension(6  ), intent(in)  :: b_ij
    real(dp), dimension(1  ), intent(in)  :: cmu_star
    real(dp), dimension(1  ), intent(in)  :: eta1s
    real(dp), dimension(1  ), intent(in)  :: eta2s
    real(dp), dimension(1  ), intent(in)  :: tr_bs
    real(dp), dimension(1  ), intent(in)  :: iib
    real(dp), dimension(1  ), intent(in)  :: iiib
    real(dp), dimension(1  ), intent(in)  :: f_r1
    real(dp), dimension(1  ), intent(in)  :: sst_f1
    real(dp), dimension(1  ), intent(in)  :: xi_k
    real(dp), dimension(n_grd), intent(in)  :: reconstruction_limiter_phi
    real(dp), dimension(1  ), intent(in)  :: production_k
    real(dp), dimension(1  ), intent(in)  :: destruction_k
    real(dp), dimension(1  ), intent(in)  :: diffusion_k
    real(dp), dimension(1  ), intent(in)  :: transport_k
    real(dp), dimension(1  ), intent(in)  :: vgradrho
    real(dp), dimension(1  ), intent(in)  :: bird_breakdown
    real(dp), dimension(1  ), intent(in)  :: mu_t_tavg
    real(dp), dimension(1  ), intent(in)  :: vort_mag_tavg
    real(dp), dimension(1  ), intent(in)  :: vort_x_tavg
    real(dp), dimension(1  ), intent(in)  :: vort_y_tavg
    real(dp), dimension(1  ), intent(in)  :: vort_z_tavg
    real(dp), dimension(4  ), intent(in)  :: vort_rms
    real(dp), dimension(6  ), intent(in)  :: pk_ij
    real(dp), dimension(6  ), intent(in)  :: tau_sgs
    real(dp), dimension(9  ), intent(in)  :: gradients
    real(dp), dimension(:,:), allocatable :: rhot_i
    real(dp), dimension(:,:), allocatable :: temp_j
    real(dp), dimension(:,:), allocatable :: energy_j
    real(dp), dimension(:,:), allocatable :: pressure_k
    real(dp), dimension(:,:), allocatable :: cv_j
    real(dp), dimension(:,:,:), allocatable :: enthalpy_ij
    real(dp), dimension(:,:), allocatable :: sonic_k
    real(dp), dimension(:,:,:), allocatable :: d_pressure_k_d_q
    real(dp), dimension(:,:), allocatable :: entropy_j
    real(dp), dimension(1) :: density
    real(dp), dimension(1) :: molecular_weight

    integer                               :: ns
    logical                               :: match
    character(len=80)                     :: species_name

    real(dp)                  :: rho, u, v, w, pr, cp, temp, mach, entropy
    real(dp)                  :: p_inf, cp_factor
    real(dp)                  :: cstar, mu_lam, nu_lam
    real(dp)                  :: r1a, i1a
    real(dp)                  :: r2a, i2a
    real(dp)                  :: q1, r1
    real(dp)                  :: a, b, c, rad
    real(dp)                  :: umag_sq, ptot, ttot, htot, etot, xmr
    real(dp), parameter       :: zero = 0.0_dp
    real(dp), parameter       :: half = 0.5_dp
    real(dp), parameter       :: one  = 1.0_dp
    real(dp), parameter       :: two  = 2.0_dp
    real(dp), parameter       :: four = 4.0_dp

    continue

    value = zero
    ptot  = zero
    ttot  = zero
    etot  = zero
    htot  = zero
    match = .false.

    cstar = sutherland_constant / tref

    if ( eqn_set == compressible ) then
      p_inf     = 1._dp/gamma
      cp_factor = gamma*xmach**2
      xmr = my_1
      if ( re > tiny(1._dp) ) xmr       = xmach / re
    else if ( eqn_set == generic_gas ) then
      p_inf     = 1._dp/gamma
      cp_factor = my_1
      xmr = my_1
    else
      p_inf     = my_1
      cp_factor = my_1
      xmr = my_1
      if ( re > tiny(1._dp) ) xmr       = my_1 / re
    end if
!-----------------------------------------------------------------------------80

    select case(eqn_set)

    case (compressible)

      rho          = qp(1)
      u            = qp(2)
      v            = qp(3)
      w            = qp(4)
      pr           = qp(5)
      umag_sq      = u**2 + v**2 + w**2
      cp           = 2._dp*(pr/p_inf - 1._dp)/cp_factor
      temp         = gamma*pr/rho
      mach         = sqrt((u**2+v**2+w**2)/temp)
      entropy      = log((pr/p_inf)/rho**gamma)
      htot         = ( gamma*pr/rho )*xgm1 + umag_sq * half
      etot         = ( pr/rho )*xgm1 + umag_sq * half
      ptot         = pr * ( one + half * gm1 * mach * mach )**(xgm1g)
      ttot         = temp * (pr / ptot)**(-gm1/gamma)

    case (incompressible)

      rho          = 1._dp
      u            = qp(2)
      v            = qp(3)
      w            = qp(4)
      pr           = qp(1)
      cp           = 2._dp*(pr/p_inf - 1._dp)/cp_factor
      temp         = 0._dp
      mach         = 0._dp
      entropy      = log(pr/p_inf)

    case (generic_gas)

      rho          = qp(1)
      u            = qp(2)
      v            = qp(3)
      w            = qp(4)
      pr           = qp(5)
      cp           = 2.0_dp*(pr-p_inf)
      temp         = q_rg(3)
      mach         = sqrt( u**2 + v**2 + w**2 ) /  q_rg(5)
      entropy      = 0.0_dp

      if (n_species > 1) then
        nspecies: do ns = 1,n_species
          species_name = 'rho_'//trim(spec_propv(ns)%species)
          if ( var == species_name)then
            value = rho_i(ns)
            match = .true.
          end if
        end do nspecies
      end if
      allocate(rhot_i(n_species,1))
      allocate(temp_j(n_energy,1))
      allocate(energy_j(n_energy,1))
      allocate(pressure_k(n_mom,1))
      allocate(cv_j(n_energy,1))
      allocate(enthalpy_ij(n_species,n_energy,1))
      allocate(sonic_k(n_mom,1))
      allocate(d_pressure_k_d_q(n_species+n_energy,n_mom,1))
      allocate(entropy_j(n_energy,1))
      htot = q_rg(6)
      energy_j(:,1) = htot
      temp_j(:,1) = temp
      rhot_i(:,1) = rho_i(:)
      call thermo(3,rhot_i,temp_j,energy_j,pressure_k,density,cv_j,enthalpy_ij,&
                  molecular_weight,sonic_k,d_pressure_k_d_q, entropy_j )
      ttot = temp_j(1,1)
      ptot = pressure_k(1,1)
      deallocate(rhot_i)
      deallocate(energy_j)
      deallocate(temp_j)
      deallocate(pressure_k)
      deallocate(cv_j)
      deallocate(enthalpy_ij)
      deallocate(sonic_k)
      deallocate(d_pressure_k_d_q)
      deallocate(entropy_j)

    case default

      rho         = 0._dp
      u           = 0._dp
      v           = 0._dp
      w           = 0._dp
      pr          = 0._dp
      cp          = 0._dp
      temp        = 0._dp
      mach        = 0._dp
      entropy     = 0._dp

    end select

    select case ( var )

    case('x')
      value = px

    case('y')
      value = py

    case('z')
      value = pz

    case('rho')
      value = rho

    case('u')
      value = u

    case('v')
      value = v

    case('w')
      value = w

    case('p')
      value = pr

    case('p_gage')
      value = pr - p_inf

    case('cp')
      value = cp

    case('dp_pinf')
      value = (pr-p_inf)/p_inf

    case('vort_x')
      value = vortx(1)

    case('vort_y')
      value = vorty(1)

    case('vort_z')
      value = vortz(1)

    case('vort_mag')
      value  = sqrt(vortx(1)*vortx(1)+vorty(1)*vorty(1)+vortz(1)*vortz(1))

    case('div_vel')
      value = div_vel(1)

    case('q_criterion')
      value = qcriterion(1)

    case('mach')
      value = mach

    case('temperature')
      value = temp

    case('recovery_temperature')
      value = (temp-one)/(half*gm1*xmach*xmach)

    case('iblank')
!      blank the interpolated node if either donor node is blanked
      value = iblank(1)

    case('imesh')
      value = imesh(1)

    case('entropy')
      value = entropy

    case('mu_t')
      value = mut(1)

    case('mu_t_ratio')
      mu_lam       = viscosity_law( cstar, temp )
      value = mut(1)/mu_lam

    case ('uuprime')
      value = turbfluctuations(1)

    case ('vvprime')
      value = turbfluctuations(2)

    case ('wwprime')
      value = turbfluctuations(3)

    case ('uvprime')
      value = turbfluctuations(4)

    case ('uwprime')
      value = turbfluctuations(5)

    case ('vwprime')
      value = turbfluctuations(6)

    case ('slen')
      value = slen(1)

    case ('turb1')
      value = qt(1)

    case ('turb2')
      value = qt(2)

    case ('turb3')
      value = qt(3)

    case ('turb4')
      value = qt(4)

    case ('turb5')
      value = qt(5)

    case ('turb6')
      value = qt(6)

    case ('turb7')
      value = qt(7)

    case ('volume')
      value = volume(1)

    case ('res1')
      value = res(1)

    case ('res2')
      value = res(2)

    case ('res3')
      value = res(3)

    case ('res4')
      value = res(4)

    case ('res5')
      value = res(5)

    case ('turres1')
      value = turres(1)

    case ('turres2')
      value = turres(2)

    case ('turres3')
      value = turres(3)

    case ('turres4')
      value = turres(4)

    case ('turres5')
      value = turres(5)

    case ('turres6')
      value = turres(6)

    case ('turres7')
      value = turres(7)

    case ('res_gcl')
      value = res_gcl(1)

    case ('rho_tavg')
      value = qtavg(1)

    case ('u_tavg')
      value = qtavg(2)

    case ('v_tavg')
      value = qtavg(3)

    case ('w_tavg')
      value = qtavg(4)

    case ('p_tavg')
      value = qtavg(5)

    case ('rho_trms')
      value = qtrms(1)

    case ('u_trms')
      value = qtrms(2)

    case ('v_trms')
      value = qtrms(3)

    case ('w_trms')
      value = qtrms(4)

    case ('p_trms')
      value = qtrms(5)

    case ('iflagslen')
      value = iflagslen(1)

    case ('cgamma')
      value = cgamma(1)

    case ('aspect_ratio')
      value = aspect_ratio(1)

    case ('skip_q')
      value = skip_q(1)

    case ('hrles_blend')
      value = hrles_blend(1)

    case ('cq')
      value = c_q(1)

    case ('cfx')
      value = c_fx(1)

    case ('cfy')
      value = c_fy(1)

    case ('cfz')
      value = c_fz(1)

    case ('cf')
      value = c_f(1)

    case ('yplus')
      value = y_plus (1)

    case ('uavg')
      value = u_avg(1)

    case ('vavg')
      value = v_avg(1)

    case ('wavg')
      value = w_avg(1)

    case ('lambda1')
      value = lambda(1)

    case ('lambda2')
      value = lambda(2)

    case ('lambda3')
      value = lambda(3)

    case ('lambda4')
      value = lambda(4)

    case ('lambda5')
      value = lambda(5)

    case ('lambda6')
      value = lambda(6)

    case ('lambda7')
      value = lambda(7)

    case ('nx', 'ny', 'nz')

    case('ptot')
      value = ptot

    case('ttot')
      value = ttot

    case('tt')
      value = q_rg(3)

    case('tv')
      value = q_rg(2)

    case('etot')
      value = q_rg(7)
      if ( eqn_set == compressible ) value = etot

    case('htot')
      value = q_rg(6)
      if ( eqn_set == compressible ) value = htot

    case('ev')
      value = q_rg(1)

    case('mixture_mol_weight')
      value = q_rg(4)

    case('sonic')
      value = q_rg(5)

    case('sw')
      value = sw(1)

    case('pw')
      value = pw(1)

    case('pv')
      value = pv(1)

    case('qv')
      value = qv(1)

    case('rv')
      value = rv(1)

    case('dv') ! discriminant for invariants of velocity gradient tensor
! l^3 + P*l^2 + Q*l + R = 0, P=0 for incompressible flow
          q1 = (3.0_dp*qv(1)-pv(1)*pv(1))/9.0_dp
          r1 = (9.0_dp*pv(1)*qv(1)                                         &
             - 27.0_dp*rv(1) - 2.0_dp*pv(1)*pv(1)*pv(1))/54.0_dp
          value    = q1*q1*q1 + r1*r1

!     value = (27.0_dP/4.0_dp)*rv(1)*rv(1) + qv(1)*qv(1)*qv(1)

    case('processor_id')
      value = lmpi_id

    case('global_node')
      value = global_node(1)

    case('critical_point_r1')
      a     = one
      b     = -sw(1)
      c     = pw(1)
      rad   = ( b*b - four*a*c )
      if ( rad >= zero ) then
        r1a   = (-b + sqrt(rad))/(two*a)
        r2a   = sw(1) - r1a
        value = r1a
      endif

    case('critical_point_i1')
! real parts equal
! imaginary parts equal with opposite signs
      a     = one
      b     = -sw(1)
      c     = pw(1)
      rad   = ( b*b - four*a*c )
      if ( rad < zero ) then
        r1a   = -b /(two*a)
        i1a   = (  sqrt(-rad))/(two*a)
        value = i1a
      endif
!     l2a   = sw(1) - l1a
!     l2b   = sw(1) - l1b

    case('critical_point_r2')
!
! imaginary parts zero
      a     = one
      b     = -sw(1)
      c     = pw(1)
      rad   = ( b*b - four*a*c )
      if ( rad >= zero ) then
        r1a   = (-b + sqrt(rad))/(two*a)
        r2a   = sw(1) - r1a
        value = r2a
      endif

    case('critical_point_i2')
! real parts equal
! imaginary parts equal with opposite signs
      a     = one
      b     = -sw(1)
      c     = pw(1)
      rad   = ( b*b - four*a*c )
      if ( rad < zero ) then
        r1a   = -b /(two*a)
        i1a   = (  sqrt(-rad))/(two*a)
        r2a   = sw(1) - r1a
        i2a   = - i1a
        value = i2a
      endif

    case ('uplus')
      value = u_plus (1)

    case ('kplus')
      value = k_plus (1)

    case ('wplus')
      value = w_plus (1)

    case ('rey_turb')
      mu_lam       = viscosity_law( cstar, temp )
      value = qt(1) / ( qt(2) * mu_lam )

    case ('yplusretau')
      if ( re_tau > tiny(1.0_dp) ) value = y_plus (1) / re_tau

    case ('t11')
      value = tau_ij (1)

    case ('t22')
      value = tau_ij (2)

    case ('t33')
      value = tau_ij (3)

    case ('t12')
      value = tau_ij (4)

    case ('t13')
      value = tau_ij (5)

    case ('t23')
      value = tau_ij (6)

    case ('t11plus')
      value = t11_plus (1)

    case ('t12plus')
      value = t12_plus (1)

    case ('t13plus')
      value = t13_plus (1)

    case ('t22plus')
      value = t22_plus (1)

    case ('t23plus')
      value = t23_plus (1)

    case ('t33plus')
      value = t33_plus (1)

    case ('b11')
      value = b_ij (1)

    case ('b22')
      value = b_ij (2)

    case ('b33')
      value = b_ij (3)

    case ('b12')
      value = b_ij (4)

    case ('b13')
      value = b_ij (5)

    case ('b23')
      value = b_ij (6)

    case ('cmu_star')
      value = cmu_star (1)

    case ('eta1s')
      value = eta1s(1)

    case ('eta2s')
      value = eta2s(1)

    case ('pze')
      value = 2.0_dp * eta1s(1) * cmu_star(1)

    case ('tr_bs')
      value = tr_bs(1)

    case ('iib')
      value = iib(1)

    case ('iiib')
      value = iiib(1)

    case ('f_r1')
      value = f_r1(1)

    case ('xi_k')
      value = xi_k(1)

    case ('reconstruction_limiter_phi1')
      value = reconstruction_limiter_phi(1)
    case ('reconstruction_limiter_phi2')
      value = reconstruction_limiter_phi(2)
    case ('reconstruction_limiter_phi3')
      value = reconstruction_limiter_phi(3)
    case ('reconstruction_limiter_phi4')
      value = reconstruction_limiter_phi(4)
    case ('reconstruction_limiter_phi5')
      value = reconstruction_limiter_phi(5)

    case ('production_k')
      value = production_k(1)

    case ('destruction_k')
      value = destruction_k(1)

    case ('diffusion_k')
      value = diffusion_k(1)

    case ('transport_k')
      value = transport_k(1)

    case ('vgradrho')
      value = vgradrho(1)

    case ('bird_breakdown')
      value = bird_breakdown(1)

    case('mu_t_tavg')
      value = mu_t_tavg(1)

    case('vort_mag_tavg')
      value = vort_mag_tavg(1)

    case('vort_x_tavg')
      value = vort_x_tavg(1)

    case('vort_t_tavg')
      value = vort_y_tavg(1)

    case('vort_z_tavg')
      value = vort_z_tavg(1)

    case('vort_x_rms')
      value = vort_rms(1)

    case('vort_y_rms')
      value = vort_rms(2)

    case('vort_z_rms')
      value = vort_rms(3)

    case('vort_mag_rms')
      value = vort_rms(4)

    case('pk_11')
      value = pk_ij(1)

    case('pk_22')
      value = pk_ij(2)

    case('pk_33')
      value = pk_ij(3)

    case('pk_12')
      value = pk_ij(4)

    case('pk_13')
      value = pk_ij(5)

    case('pk_23')
      value = pk_ij(6)

    case('t11_sgs')
      value = tau_sgs(1)

    case('t22_sgs')
      value = tau_sgs(2)

    case('t33_sgs')
      value = tau_sgs(3)

    case('t12_sgs')
      value = tau_sgs(4)

    case('t13_sgs')
      value = tau_sgs(5)

    case('t23_sgs')
      value = tau_sgs(6)

    case('dudx')
      value = gradients(1)

    case('dudy')
      value = gradients(2)

    case('dudz')
      value = gradients(3)

    case('dvdx')
      value = gradients(4)

    case('dvdy')
      value = gradients(5)

    case('dvdz')
      value = gradients(6)

    case('dwdx')
      value = gradients(7)

    case('dwdy')
      value = gradients(8)

    case('dwdz')
      value = gradients(9)

    case ('sst_f1')
      value = sst_f1(1)

    case('sst_f2')
      mu_lam = viscosity_law( cstar, temp )
      nu_lam = mu_lam / rho
      value  = function_sst_f2 ( xmr, qt(1), qt(2), nu_lam, betastar, slen(1) )

    case('re_k')
      mu_lam = viscosity_law( cstar, temp )
      nu_lam = mu_lam / rho
      value  = ( sqrt(qt(1)) * slen(1) / nu_lam ) / xmr

    case('re_t')
! k-w based equation
      mu_lam = viscosity_law( cstar, temp )
      nu_lam = mu_lam / rho
      value  = ( qt(1) / (qt(2) * nu_lam ) ) / xmr

    case default

     if(.not. match)                                                           &
     write(*,'(a,a)') 'unknown sampling tecplot variable in get_var: ', var

    end select

  end function get_var

  include 'q_criterion.f90'
  include 'turb_fluctuations.f90'
  include 'viscosity_law.f90'
  include 'hrles_blend.f90'
  include 'set_gradv.f90'
  include 'get_sij.f90'
  include 'get_wij.f90'
  include 'get_vort.f90'
  include 'get_p.f90'
  include 'function_sst_f2.f90'

end module sampling_gather

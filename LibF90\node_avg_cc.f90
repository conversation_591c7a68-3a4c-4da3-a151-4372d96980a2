module node_avg_cc

  use kinddefs,        only : dp, system_i1
  use exact_defs,      only : ic_exact
  use bc_types,        only : bcc_type
  use allocations,     only : my_alloc_ptr
  use crow_na_types,   only : crow_na
  use thermo,          only : q_type, conserved_q_type, primitive_q_type
  use lmpi,            only : lmpi_die, lmpi_master
  use twod_util,       only : q_2d, y_coplanar_tol
  use lsq_defs,        only : cylindrical_distance, na_mapped_lsq
  use info_depr,       only : skeleton
  use bc_names,        only : bc_null
  use lsq_constants,   only : cg_tol, tf, mlsq
  use lsq_types,       only : lsq_ref_type

  use grid_types,         only : grid_type, na_type
  use nml_global,         only : grids_to_read

  implicit none

  private

  !independent of g
  public :: set_crow_avg_to_nodes
  public :: green_gauss_cc
  public :: n_qavg_max

  ! g passed through grid%
  public :: avg_to_nodes, qt_avg_to_nodes
  public :: set_up_node_averaging

  ! g passed through grid%igrid
  public :: dqavg, dqavg_bc
  !public :: qt_face_node_avg

  type(na_type), dimension(:), allocatable :: na

  type(crow_na), pointer :: crs_na

  real(dp) :: slen_tolerance

  integer :: n_qavg_max = 0

  integer :: g

contains

!============================== AVG_TO_NODES =================================80
!
!  Averages cell-centered data to the nodes using weighting scheme
!  derived as a pseudo-Laplacian by Holmes and Connell (AIAA 89-1932CP) and
!  used subsequently by Russ Rausch and others.  Hasselbacher showed it is
!  equivalent to an unweighted least-square fit.
!
!=============================================================================80
  subroutine avg_to_nodes(grid,soln)

    use grid_types,        only : grid_type
    use solution_types,    only : soln_type, compressible, incompressible
    use kinddefs,          only : dp
    use fun3d_constants,   only : my_0, my_1, my_2
    use info_depr,         only : cc_clip_weights
    use lmpi_app,          only : lmpi_xfer
    use lmpi,              only : lmpi_reduce, lmpi_id
    use system_extensions, only : se_flush
    use bc_cc,             only : update_qbc_na
    use bc_cc_i,           only : update_qbc_na_i

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln

    integer :: ielem, icell, gcell, inode, node, i, nn
    integer :: no_inv_w8ts, local_inv_w8ts
    integer, dimension(4) :: clips

    real(dp) :: xc, yc, zc, sc, xn, yn, zn, weight
    real(dp) :: rniw8ts
    real(dp) :: x_box_min, x_box_max, y_box_min, y_box_max
    real(dp) :: z_box_min, z_box_max

    integer, dimension(grid%nnodes0) :: clip_l, clip_h

    real(dp), dimension(grid%nnodes0)             :: inv_weighted_sum
    real(dp), dimension(soln%n_tot, grid%nnodes0) :: inv_weighted_qavg

    logical :: report_clips = .true.

    character (len=256) :: filename

    type(lsq_ref_type) :: lsq_mrefs

  continue

    g = grid%igrid

    x_box_min = -huge(1._dp)
    y_box_min = -huge(1._dp)
    z_box_min = -huge(1._dp)
    x_box_max = +huge(1._dp)
    y_box_max = +huge(1._dp)
    z_box_max = +huge(1._dp)

! Update the ghost values for cell-centered

    if ( soln%eqn_set == compressible ) then

      if ( q_type /= primitive_q_type ) then
        if ( lmpi_master ) then
          write(*,*) ' Incorrect q_dof type...stopping in avg_to_nodes.'
          write(*,*) ' ..............q_type=',q_type
          write(*,*) ' ....primitive_q_type=',primitive_q_type
        endif
        call lmpi_die
      endif

      call update_qbc_na( soln%n_tot, soln%n_q, size(soln%qbc_na,2),  &
                          grid%ncell01, soln%q_dof,                   &
                          soln%qbc_na, soln%qbc_na_loc, grid%bcc )

    elseif ( soln%eqn_set == incompressible ) then

      call update_qbc_na_i(soln%n_tot, soln%n_q, size(soln%qbc_na,2), &
                           grid%ncell01, soln%q_dof,                  &
                           soln%qbc_na, soln%qbc_na_loc, grid%bcc )

    endif

    soln%qavg         = my_0
    na(g)%weight_sum  = my_0
    inv_weighted_qavg = my_0
    inv_weighted_sum  = my_0

    clip_l(:) = 0
    clip_h(:) = 0
    rniw8ts   = my_0

! Contributions from cells

    sc = 0._dp
    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell

        gcell = grid%elem(ielem)%cell_map(icell)

        xc = grid%xc(gcell) ; yc = grid%yc(gcell) ; zc = grid%zc(gcell)
        if ( na_mapped_lsq ) sc = grid%slen(gcell)

! Loop over the node comprising the current cell

        do inode = 1, grid%elem(ielem)%node_per_cell

          node = grid%elem(ielem)%c2n(inode,icell)

          local_node : if ( node <= grid%nnodes0 ) then
            xn = grid%x(node) ; yn = grid%y(node) ; zn = grid%z(node)

            lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
                  xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
               na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )
            weight = na_weight( lsq_mrefs, node, xc, yc, zc, sc )

! Clip weights as required/neccessary

            if (cc_clip_weights) then
              if (weight < -1.0e-16_dp) clip_l(node) = clip_l(node) + 1
              if (weight > + my_2)      clip_h(node) = clip_h(node) + 1

              weight = min(weight, my_2)
              weight = max(weight, my_0)
            end if

            na(g)%weight_sum(node) = na(g)%weight_sum(node) + weight
            soln%qavg(:,node) = soln%qavg(:,node) + weight*soln%q_dof(:,gcell)

! Inverse-distance weighting as a backup in case we don't succeed.

            weight=my_1/(sqrt((xc-xn)*(xc-xn)+(yc-yn)*(yc-yn)+(zc-zn)*(zc-zn)))
            inv_weighted_sum(node)    = inv_weighted_sum(node) + weight
            inv_weighted_qavg(:,node) = inv_weighted_qavg(:,node)              &
                                      + weight*soln%q_dof(:,gcell)

          endif local_node

        end do

      end do
    end do

    call qavg_bc_cont( grid%nnodes0, grid%x, grid%y, grid%z, grid%bcc,    &
         soln%n_tot, soln%qavg, soln%q_dof, soln%qbc_na, soln%qbc_na_loc, &
         na(g)%weight_sum, inv_weighted_sum, inv_weighted_qavg,           &
         clip_l, clip_h )

! Divide the sums by the weight sums

    do i = 1, grid%nnodes0
      if ( na(g)%weight_sum(i) >= 1.e-8_dp ) then
        soln%qavg(:,i) = soln%qavg(:,i) / na(g)%weight_sum(i)
      else
        soln%qavg(:,i) = inv_weighted_qavg(:,i) / inv_weighted_sum(i)
        rniw8ts        = rniw8ts + my_1
      endif
    end do

! Exchange across processors (with a level-1 argument!)

    call lmpi_xfer(soln%qavg,ghostlevel_arg=1)

!   If weight clipping is enabled, report clipping once

    if ( .not.cc_clip_weights .or. .not.report_clips ) return

    report_clips = .false.
    clips(1:4) = 0
    !clips(4)   = grid%nnodes0
    do node=1,grid%nnodes0
      if ( grid%x(node) - x_box_min <= +1.0e-12_dp ) cycle
      if ( grid%x(node) - x_box_max >= -1.0e-12_dp ) cycle
      if ( grid%y(node) - y_box_min <= +1.0e-12_dp ) cycle
      if ( grid%y(node) - y_box_max >= -1.0e-12_dp ) cycle
      if ( grid%z(node) - z_box_min <= +1.0e-12_dp ) cycle
      if ( grid%z(node) - z_box_max >= -1.0e-12_dp ) cycle
      clips(4) = clips(4) + 1
      if ( clip_l(node) > 0 ) clips(1) = clips(1) + 1
      if ( clip_h(node) > 0 ) clips(2) = clips(2) + 1
      if ( clip_l(node) > 0 .or. clip_h(node) > 0 ) &
                              clips(3) = clips(3) + 1
    enddo

    do nn=1,4
      i = clips(nn) ; call lmpi_reduce(i,clips(nn))
    enddo

    if ( lmpi_master ) then
      write(*,*)
      write(*,"(1x,a,e20.12)") ' ...Node-avg clips: x_box_min=',x_box_min
      write(*,"(1x,a,e20.12)") ' ...Node-avg clips: x_box_min=',x_box_min
      write(*,"(1x,a,e20.12)") ' ...Node-avg clips: y_box_min=',y_box_min
      write(*,"(1x,a,e20.12)") ' ...Node-avg clips: y_box_min=',y_box_min
      write(*,"(1x,a,e20.12)") ' ...Node-avg clips: z_box_min=',z_box_min
      write(*,"(1x,a,e20.12)") ' ...Node-avg clips: z_box_min=',z_box_min
      write(*,"(1x,a,i10,a,f7.2)")                            &
      ' .........Node-avgs (within box) influenced by clips=',&
      clips(3),' : total      %=',                            &
      real(clips(3),dp)/real(clips(4),dp)*100._dp
      write(*,"(1x,a,i10,a,f7.2)")                            &
      ' .....Node-avgs (within box) influenced by low clips=',&
      clips(1),' : total      %=',                            &
      real(clips(1),dp)/real(clips(4),dp)*100._dp
      write(*,"(1x,a,i10,a,f7.2)")                            &
      ' ...Node-avgs (within box) influenced by high clips=', &
      clips(2),' : total      %=',                            &
      real(clips(2),dp)/real(clips(4),dp)*100._dp
    endif

!   Boundary inverse arclength weighting usage

    no_inv_w8ts = int(rniw8ts)
    local_inv_w8ts = no_inv_w8ts
    call lmpi_reduce(local_inv_w8ts,no_inv_w8ts)

    if ( lmpi_master .and. no_inv_w8ts > 0 ) then
      write(*,'(2x,a35,i10,a21)') "Avg_to_nodes used inverse weighting",       &
                      no_inv_w8ts, " times in the domain!"
      call se_flush()
    end if

    if ( lmpi_id == 0 ) then

      if ( q_2d ) then
        do node=1,grid%nnodes0
          if ( abs( grid%y(node) ) > y_coplanar_tol ) then
            clip_h(node) = 0 ; clip_l(node) = 0
          endif
        enddo
      endif

      clips(1) = 0
      !...count the number of clips in the grid
      do node=1,grid%nnodes0
        if ( clip_l(node) > 0 .or. clip_h(node) > 0 ) clips(1) = clips(1) + 1
      enddo

      filename = 'clipped_nodes'
      filename = trim(filename) // '_tec.dat'

      open(unit=56, file=filename,&
           status='unknown', position='append')

      rewind(56)

      write(56,'(a)') 'title="Clipped grid points"'
      write(56,'(2a)') 'variables="node","x", "y", "z","low","high"'
      write(56,*)'ZONE T="Points" I=',clips(1)
      do node=1,grid%nnodes0
        if ( clip_l(node) == 0 .and. clip_h(node) == 0 ) cycle
        write(56,'(i10,3e23.15,2i5)')               &
        node,grid%x(node),grid%y(node),grid%z(node),&
        clip_l(node),clip_h(node)
      enddo

      close(56)

    endif

  end subroutine avg_to_nodes


!============================== QT_AVG_TO_NODES ==============================80
!
!  Averages cell-centered data to the nodes using least-squares.
!  (no clipping and no inverse-distance-weighting)
!
!=============================================================================80
  subroutine qt_avg_to_nodes( grid, soln, pressure_not_temperature )

    use cc_defs,           only : clip_viscous_weights
    use info_depr,         only : ntt
    use grid_types,        only : grid_type
    use solution_types,    only : soln_type
    use fun3d_constants,   only : my_1
    use kinddefs,          only : dp
    use lmpi_app,          only : lmpi_xfer
    use fluid,             only : gamma

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln

    logical, optional, intent(in)    :: pressure_not_temperature

    integer :: ielem, icell, gcell, inode, node, i

    real(dp) :: xc, yc, zc, sc, xn, yn, zn, weight, press

    logical  :: convert_temp_to_press

    real(dp), dimension(soln%n_q) :: qt

    type(lsq_ref_type) :: lsq_mrefs

  continue

    g = grid%igrid

    if ( q_type /= conserved_q_type ) then
      if ( lmpi_master ) then
        write(*,*) ' Incorrect q_dof type...stopping in qt_avg_to_nodes.'
        write(*,*) ' ..............q_type=',q_type
        write(*,*) ' ....conserved_q_type=',conserved_q_type
      endif
      call lmpi_die
    endif

    if ( clip_viscous_weights .and. lmpi_master ) then
      if ( ntt == 1 .or. ( ntt == (ntt/100)*100 ) ) then
        write(*,*) ' WARNING...CLIPPING VISCOUS WEIGHTS..WARNING.'
      endif
    endif

    call qt_face_node_avg( soln%eqn_set, soln%n_q, size(soln%qt_face,2), &
                           soln%q_dof, soln%qt_face, grid%bcc )

    soln%qtavg = 0._dp

! Contributions from cells

    sc = 0._dp
    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell

        gcell = grid%elem(ielem)%cell_map(icell)

        qt(:) = qt_from_qc( soln%eqn_set, soln%n_q, soln%q_dof(:,gcell))

        xc = grid%xc(gcell) ; yc = grid%yc(gcell) ; zc = grid%zc(gcell)
        if ( na_mapped_lsq ) sc = grid%slen(gcell)

! Loop over the node comprising the current cell

        do inode = 1, grid%elem(ielem)%node_per_cell

          node = grid%elem(ielem)%c2n(inode,icell)

          local_node : if ( node <= grid%nnodes0 ) then
            xn = grid%x(node) ; yn = grid%y(node) ; zn = grid%z(node)

            lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
                  xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
               na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )
            weight = na_weight( lsq_mrefs, node, xc, yc, zc, sc )

            if ( clip_viscous_weights ) then
              weight = min(weight, 2._dp)
              weight = max(weight, 0._dp)
            endif

            soln%qtavg(:,node) = soln%qtavg(:,node) + weight*qt(:)

          endif local_node

        end do

      end do
    end do

! Contributions from boundary faces

    call qtavg_bc_cont( grid%nnodes0, grid%x, grid%y, grid%z, soln%qtavg, &
                        soln%n_q, grid%nbface01, soln%qt_face, grid%bcc )

! Divide the sums by the weight sums

    do i = 1, grid%nnodes0
      soln%qtavg(:,i) = soln%qtavg(:,i) * na(g)%weight_sum_inv_qt(i)
    end do

! Convert temperature to pressure if requested

  convert_temp_to_press = .false.

  if (present(pressure_not_temperature)) then
    convert_temp_to_press = pressure_not_temperature
  end if

  if (convert_temp_to_press) then
    do i = 1, grid%nnodes0
      press = soln%qtavg(1,i)*soln%qtavg(5,i)/gamma
      soln%qtavg(5,i) = press
    end do
  end if

! Exchange across processors (with a level-1 argument!)

    call lmpi_xfer(soln%qtavg,ghostlevel_arg=1)

  end subroutine qt_avg_to_nodes

!=============================== QTAVG_BC_CONT ===============================80
!
! Contributions to surrounding nodes comprising the boundary faces.
!
!=============================================================================80
  subroutine qtavg_bc_cont(nnodes0, x, y, z, qtavg, n_q, nbfaces, qt_face, bcc )

    use kinddefs,        only : dp
    use cc_defs,         only : clip_viscous_weights
    use fun3d_constants, only : my_1

    integer, intent(in) :: nnodes0, n_q, nbfaces

    real(dp), dimension(:),           intent(in)    :: x, y, z
    real(dp), dimension(n_q,nbfaces), intent(in)    :: qt_face
    real(dp), dimension(:,:),         intent(inout) :: qtavg
    type(bcc_type),                   intent(in)    :: bcc

    integer :: node, i, n, num_bface_nodes

    real(dp) :: xf, yf, zf, sf, xn, yn, zn, weight

    type(lsq_ref_type) :: lsq_mrefs

  continue

    faces : do n = 1, bcc%n_faces01

      if (bcc%ibc(n) == bc_null) cycle faces

      xf = bcc%xface(n) ; yf = bcc%yface(n) ; zf = bcc%zface(n)
      sf = bcc%slenface(n)

      !...tria or quad.
      num_bface_nodes = 4
      if (bcc%nodes(4,n) == bcc%nodes(1,n)) num_bface_nodes = 3

      !...Loop over the nodes on the boundary face.

      bface_nodes : do i = 1, num_bface_nodes

        node = bcc%nodes(i,n)

        if ( node > nnodes0 ) cycle bface_nodes

        xn = x(node) ; yn = y(node) ; zn = z(node)
        lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
              xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
           na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )
        weight = na_weight( lsq_mrefs, node, xf, yf, zf, sf )

        if ( clip_viscous_weights ) then
          weight = min(weight, 2._dp)
          weight = max(weight, 0._dp)
        endif

        qtavg(:,node) = qtavg(:,node) + weight*qt_face(:,n)

      end do bface_nodes

    end do faces

  end subroutine qtavg_bc_cont

!============================== SET_CROW_AVG_TO_NODES ========================80
!
! Set compressed row storage (crs) for node-averaging.
! SP Note: Nodes are not on-solve-plane-ordered for q_2d.
!
!=============================================================================80
  subroutine set_crow_avg_to_nodes(grid)

    use grid_types,        only : grid_type
    use lmpi,              only : lmpi_bcast, lmpi_max

    type(grid_type), intent(in)    :: grid

    integer :: ielem, icell, inode, node, i, j, n, gcell, ja, ia_min, ia_max
    integer :: num_bface_nodes, can, can_bc, ir
    integer :: n_entries_found_bc, n_entries_found

    integer, dimension(grid%nnodes0) :: n_entries, n_entries_bc

    integer, parameter :: n_entries_max_bc = 50
    integer, parameter :: n_entries_max = 50

    integer, dimension(n_entries_max,grid%nnodes0) :: cell

    integer,  dimension(n_entries_max_bc,grid%nnodes0) :: ibc, ibr, iqt
    real(dp), dimension(n_entries_max_bc,grid%nnodes0) :: xf, yf, zf, sf

    real(dp) :: yn

  continue

    n_entries_found_bc = 0
    n_entries_found = 0

    can    = 0
    can_bc = 0

    n_entries(:) = 0
    n_entries_bc(:) = 0

! Contributions from cells to nodes from interior faces

    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell

        gcell = grid%elem(ielem)%cell_map(icell)

! Loop over the node comprising the current cell

        do inode = 1, grid%elem(ielem)%node_per_cell

          node = grid%elem(ielem)%c2n(inode,icell)

          yn = grid%y(node)
          if ( q_2d .and. abs( yn ) > y_coplanar_tol ) cycle !skip off-plane 2D

          local_node : if ( node <= grid%nnodes0 ) then

            n_entries_found = max( n_entries_found,    &
                                   n_entries(node) + 1 )
            if ( n_entries_found > n_entries_max ) cycle

            can             = can + 1
            n_entries(node) = n_entries(node) + 1
            cell(n_entries(node),node) = gcell

          endif local_node

        end do

      end do
    end do

    ir = n_entries_found
    call lmpi_max(ir,n_entries_found)
    call lmpi_bcast( n_entries_found)
    if ( n_entries_found > n_entries_max ) then
      if ( lmpi_master ) then
        write(*,*) 'Stopping in set_crow_avg_to_nodes...&
        &n_entries_found,n_entries_max=',         &
         n_entries_found,n_entries_max
      endif
      call lmpi_die
    endif

! Contributions from cells to nodes comprising the boundary faces

    faces : do n = 1, grid%bcc%n_faces01

      if (grid%bcc%ibc(n) == bc_null) cycle faces

! Figure out if the current face is a tri or a quad

      if (grid%bcc%nodes(4,n) == grid%bcc%nodes(1,n)) then
        num_bface_nodes = 3
      else
        num_bface_nodes = 4
      end if

! Loop over the nodes on the current boundary face

      bface_node_loop : do i = 1, num_bface_nodes

        node = grid%bcc%nodes(i,n)

        yn = grid%y(node)
        if ( q_2d .and. abs( yn ) > y_coplanar_tol ) cycle !skip off-plane 2D

        loc_node1 : if ( node <= grid%nnodes0 ) then

          n_entries_found_bc = max( n_entries_found_bc,    &
                                    n_entries_bc(node) + 1 )

          if ( n_entries_found_bc > n_entries_max_bc ) cycle

          can_bc             = can_bc + 1
          n_entries_bc(node) = n_entries_bc(node) + 1
          ir = 0
          do ir =1,n_entries(node)
            if ( grid%bcc%cell(n) == cell(ir,node) ) exit
          enddo

          ibr( n_entries_bc(node), node ) = ir
          ibc( n_entries_bc(node), node ) = grid%bcc%ibc(n)
          iqt( n_entries_bc(node), node ) = n
          xf(  n_entries_bc(node), node ) = grid%bcc%xface(n)
          yf(  n_entries_bc(node), node ) = grid%bcc%yface(n)
          zf(  n_entries_bc(node), node ) = grid%bcc%zface(n)
          sf(  n_entries_bc(node), node ) = grid%bcc%slenface(n)

        endif loc_node1

      end do bface_node_loop

    end do faces

    ir = n_entries_found_bc
    call lmpi_max(ir,n_entries_found_bc)
    call lmpi_bcast( n_entries_found_bc)
    if ( n_entries_found_bc > n_entries_max_bc ) then
      if ( lmpi_master ) then
        write(*,*) 'Stopping in set_crow_avg_to_nodes...&
        &n_entries_found_bc,n_entries_max_bc=',         &
         n_entries_found_bc,n_entries_max_bc
      endif
      call lmpi_die
    endif

    allocate(crs_na)
    call my_alloc_ptr(crs_na%ia,   grid%nnodes0+1)
    call my_alloc_ptr(crs_na%cell,            can)

    crs_na%ia(1) = 1
    ja            = 0
    ia_max        = 0
    ia_min        = 1000
    do node=1,grid%nnodes0
      crs_na%ia(node+1) = crs_na%ia(node) + n_entries(node)
      ia_max = max( ia_max, n_entries(node) )
      ia_min = min( ia_min, n_entries(node) )
      do j=1,n_entries(node)
        ja = ja + 1
        crs_na%cell(ja) = cell(j,node)
      enddo
    enddo

    n_qavg_max = max( n_qavg_max, ia_max )

    j = ia_max ; call lmpi_max(j,ia_max)
    j =-ia_min ; call lmpi_max(j,ia_min) ; ia_min = -ia_min
    if ( lmpi_master ) then
      write(*,*) 'CROW_NA:       n_entries:'
      write(*,*) 'CROW_NA:       max found=',n_entries_found
      write(*,*) 'CROW_NA:   max allowable=',n_entries_max
      write(*,*) 'CROW_NA:         nnodes0=',grid%nnodes0
      write(*,*) 'CROW_NA: size (interior)=',can
      write(*,*) 'CROW_NA:    min_per_node=',ia_min
      write(*,*) 'CROW_NA:    max_per_node=',ia_max
    endif

    call my_alloc_ptr(crs_na%iab, grid%nnodes0+1)
    call my_alloc_ptr(crs_na%ibr,         can_bc)
    call my_alloc_ptr(crs_na%ibc,         can_bc)
    call my_alloc_ptr(crs_na%iqt,         can_bc)
    call my_alloc_ptr(crs_na%xf,          can_bc)
    call my_alloc_ptr(crs_na%yf,          can_bc)
    call my_alloc_ptr(crs_na%zf,          can_bc)
    call my_alloc_ptr(crs_na%sf,          can_bc)

    crs_na%iab(1) = 1
    ja            = 0
    ia_max        = 0
    ia_min        = 1000
    do node=1,grid%nnodes0
      crs_na%iab(node+1) = crs_na%iab(node) + n_entries_bc(node)
      ia_max = max( ia_max, n_entries_bc(node) )
      ia_min = min( ia_min, n_entries_bc(node) )
      do j=1,n_entries_bc(node)
        ja = ja + 1
        crs_na%ibr(ja) = ibr(j,node)
        crs_na%ibc(ja) = ibc(j,node)
        crs_na%iqt(ja) = iqt(j,node)
        crs_na%xf(ja)  =  xf(j,node)
        crs_na%yf(ja)  =  yf(j,node)
        crs_na%zf(ja)  =  zf(j,node)
        crs_na%sf(ja)  =  sf(j,node)
      enddo
    enddo

    n_qavg_max = max( n_qavg_max, ia_max )

    j = ia_max ; call lmpi_max(j,ia_max)
    j =-ia_min ; call lmpi_max(j,ia_min) ; ia_min = -ia_min
    if ( lmpi_master ) then
      write(*,*) 'CROW_NA:    n_bc_entries:'
      write(*,*) 'CROW_NA:       max found=',n_entries_found_bc
      write(*,*) 'CROW_NA:   max allowable=',n_entries_max_bc
      write(*,*) 'CROW_NA: size (boundary)=',can_bc
      write(*,*) 'CROW_NA:    min_per_node=',ia_min
      write(*,*) 'CROW_NA:    max_per_node=',ia_max
    endif

  end subroutine set_crow_avg_to_nodes

!============================== DQAVG ========================================80
!
! Weights for averaging cell-centered data to the nodes.
!
!=============================================================================80
  subroutine dqavg( g, xc, yc, zc, sc, xn, yn, zn, node,                       &
                    n_max, n, nb, weights, cells, ws_interior )

    use debug_defs,        only : composite_jacobian_lhs
    use fun3d_constants,   only : my_1

    integer,                    intent(in)  :: g, node, n_max
    integer,                    intent(out) :: n, nb
    integer,  dimension(n_max), intent(out) :: cells
    real(dp), dimension(n_max), intent(out) :: weights

    real(dp),               intent(in) :: xn, yn, zn
    real(dp), dimension(:), intent(in) :: xc, yc, zc, sc

    real(dp), intent(out), optional :: ws_interior

    integer :: gcell, ia, i, nt

    real(dp) :: xp, yp, zp, sp, ws

    logical :: invalid

    type(lsq_ref_type) :: lsq_mrefs

  continue

    if ( composite_jacobian_lhs ) then
      write(*,*)
      write(*,"(1x,a,i10)")     '       dqavg:'
      write(*,"(1x,a,i10)")     '        node=',node
      write(*,"(1x,a,3f20.10)") '    xn,yn,zn=',xn,yn,zn
    endif

    lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
          xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
       na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )

!   Interior face contributions.

    n = 0
    do ia=crs_na%ia(node),crs_na%ia(node+1)-1

      gcell = crs_na%cell(ia)

      n = n + 1
      cells(n)  = gcell

      xp = xc(gcell)
      yp = yc(gcell)
      zp = zc(gcell)
      sp = sc(gcell)

      weights(n) = na_weight( lsq_mrefs, node, xp, yp, zp, sp )

    end do

    ws = 0._dp
    do i=1,n
      weights(i) = weights(i) * na(g)%weight_sum_inv_qt(node)
      ws         = ws + weights(i)
    end do

    nb = crs_na%iab(node+1) - crs_na%iab(node)
    nt = n + nb

    if ( present( ws_interior ) ) ws_interior = ws

    if ( composite_jacobian_lhs ) then
      write(*,"(1x,a,i10)")     '  n(interior)=',n
      write(*,"(1x,a,i10)")     '     n(total)=',nt

      write(*,"(31x,4x,a,6x,a,14x,a)") 'i','cell','weight'
      do i=1,n
        write(*,"(31x,i5,i10,f20.10)") i,cells(i),weights(i)
      enddo
      write(*,"(1x,a,f20.10)") ' normalized na(g)%weight_sum=',ws
      write(*,"(1x,a,f20.10)") '            na(g)%weight_sum=',  &
                               1._dp/na(g)%weight_sum_inv_qt(node)
      write(*,*)
      write(*,"(31x,11x,a,3(18x,a))") 'cell','xc','yc','zc'
      do ia=crs_na%ia(node),crs_na%ia(node+1)-1
        gcell = crs_na%cell(ia)
        write(*,"(31x,i15,3f20.10)") gcell,xc(gcell),yc(gcell),zc(gcell)
      end do
      write(*,*)
    endif

    invalid = .false.
    if ( abs( ws - 1._dp ) > 5.0e-05_dp ) invalid = .true.

    if ( invalid .and. n == nt ) then
      write(*,*) ' Stopping...invalid ws at interior node.'
      write(*,*) ' ..........ws=',ws
      write(*,*) ' .......error=',abs( ws - 1._dp )
      write(*,*) ' ...tolerance=',1.0e-05_dp
      stop ! FIXME: should be lmpi_die or se_exit(1)
    endif

  end subroutine dqavg

!============================== DQAVG_BC =====================================80
!
! Weights for averaging cell-centered data to the nodes from bc.
!
!=============================================================================80
  subroutine dqavg_bc( g, xn, yn, zn, n_q, node, n_max, n, dqt_int, dqt_ext,   &
                       cells, iqt, ws_interior )

    use kinddefs,          only : system_i1
    use fun3d_constants,   only : my_1
    use debug_defs,        only : composite_jacobian_lhs
    use bc_names,          only : bc_name_index

    integer,                         intent(in)  :: g, n_q, node, n_max
    integer,                         intent(out) :: n
    integer,  dimension(n_max),      intent(out) :: cells
    integer,  dimension(n_max),      intent(out) :: iqt
    real(dp),                        intent(in)  :: xn, yn, zn
    real(dp), dimension(n_q, n_max), intent(out) :: dqt_int, dqt_ext

    real(dp), intent(in), optional :: ws_interior

    integer :: ia, ia_cell, ir, ibc, eq

    integer(system_i1), dimension(n_q) :: dqt

    real(dp) :: xf, yf, zf, sf, sw, ws

    character(len=80) :: bc_name

    logical :: invalid

    type(lsq_ref_type) :: lsq_mrefs

  continue

    if ( composite_jacobian_lhs .and. &
         crs_na%iab(node+1) == crs_na%iab(node) ) then
      write(*,*)
      write(*,"(1x,a,i10)")     '    dqavg_bc: No contribution.'
      write(*,*)
    elseif( composite_jacobian_lhs ) then
      write(*,*)
      write(*,"(1x,a,i10)")     '    dqavg_bc:'
      write(*,"(1x,a,i10)")     '        node=',node
      write(*,"(1x,a,3f20.10)") '    xn,yn,zn=',xn,yn,zn
      write(*,*)
    endif

    lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
          xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
       na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )

    n = 0

!   Boundary face contributions.

    ws = 0._dp
    if ( present( ws_interior ) ) ws = ws_interior
    do ia=crs_na%iab(node),crs_na%iab(node+1)-1

      n = n + 1

      ir  = crs_na%ibr(ia)
      ibc = crs_na%ibc(ia) ; if ( ibc == bc_null ) cycle
      xf  = crs_na%xf(ia)
      yf  = crs_na%yf(ia)
      zf  = crs_na%zf(ia)
      sf  = crs_na%sf(ia)

      iqt(n) = crs_na%iqt(ia)

      ia_cell  = crs_na%ia(node) + ir - 1
      cells(n) = crs_na%cell(ia_cell)

      sw = na_weight( lsq_mrefs, node, xf, yf, zf, sf )

      sw = sw * na(g)%weight_sum_inv_qt(node)
      ws = ws + sw

      call dqt_face_node_avg( ibc, n_q, dqt )

      do eq=1,n_q
        dqt_int(eq,n) = sw*real( dqt(eq), dp )
        dqt_ext(eq,n) = sw*( 1._dp - real( dqt(eq), dp ) )
      enddo

      if ( .not.composite_jacobian_lhs ) cycle

      write(*,"(1x,a,i10)")     ' n(boundary)=',n
      write(*,"(1x,a,i10)")     '          ir=',ir
      write(*,"(1x,a,i10)")     '        cell=',cells(n)
      call bc_name_index( ibc, bc_name, integer_to_character=.true. )
      write(*,"(1x,a,i10,2x,a)")'         ibc=',ibc, trim(bc_name)
      write(*,"(1x,a,3f20.10)") '    xf,yf,zf=',xf,yf,zf
      write(*,"(1x,a,3f20.10)") '          sf=',sf
      write(*,"(1x,a,f20.10)")  '          sw=',sw
      write(*,"(11x,4x,a,6x,a,14x,a,3x,a,14x,a)") 'i','cell','weight',&
                                                   'qt_face','weight'
      do eq=1,n_q
        write(*,"(11x,i5,2(i10,f20.10))") eq,cells(n),dqt_int(eq,n),&
                                               iqt(n),dqt_ext(eq,n)
      enddo
      if ( ia == crs_na%iab(node+1)-1 ) then
        write(*,"(1x,a,f20.10)") ' normalized weight_sum=',ws
        write(*,"(1x,a,f20.10)") '            weight_sum=',  &
                                 1._dp/na(g)%weight_sum_inv_qt(node)
        write(*,*)
      endif

    end do

    if ( .not. present( ws_interior ) ) return

    invalid = .false.
    if ( abs( ws - 1._dp ) > 5.0e-05_dp ) invalid = .true.

    if ( invalid ) then
      write(*,*) ' Stopping...invalid ws at boundary node.'
      write(*,*) ' ..........ws=',ws
      write(*,*) ' .......error=',abs( ws - 1._dp )
      write(*,*) ' ...tolerance=',1.0e-05_dp
      stop ! FIXME: should be lmpi_die or se_exit(1)
    endif

  end subroutine dqavg_bc

!============================== SET_UP_NODE_AVERAGING ========================80
!
!  Sets up weights for node averaging.
!
!=============================================================================80
  subroutine set_up_node_averaging( level, grid )

    integer,         intent(in) :: level
    type(grid_type), intent(inout) :: grid

  continue

    if ( level == 1 ) then
      allocate(na(grids_to_read))
    endif

    slen_tolerance = 100._dp*epsilon(1._dp)

    if ( skeleton > 0 ) then
      write(*,*)
      write(*,"(1x,a,L1,a,i3)") 'Node averaging set up...na_mapped_lsq=',&
        na_mapped_lsq,' mlsq=',mlsq
    endif

    g = grid%igrid

    call my_alloc_ptr(na(g)%lambdax,           grid%nnodes0)
    call my_alloc_ptr(na(g)%lambday,           grid%nnodes0)
    call my_alloc_ptr(na(g)%lambdaz,           grid%nnodes0)
    call my_alloc_ptr(na(g)%weight_sum,        grid%nnodes0)
    call my_alloc_ptr(na(g)%weight_sum_inv_qt, grid%nnodes0)
    call my_alloc_ptr(na(g)%n_sum,             grid%nnodes0)

    call my_alloc_ptr(na(g)%slenxn,          grid%nnodes0)
    call my_alloc_ptr(na(g)%slenyn,          grid%nnodes0)
    call my_alloc_ptr(na(g)%slenzn,          grid%nnodes0)
    call my_alloc_ptr(na(g)%slen,            grid%nnodes0)
    call my_alloc_ptr(na(g)%cgamma,          grid%nnodes0)

    if ( na_mapped_lsq .and. .not. cylindrical_distance ) then

      !FIXME JLT
      write(*,*) ' Not operational...problems for mixed elements.' !FIXME JLT
      call lmpi_die
      !FIXME JLT
      !..set slenr0 via cartesian coordinates.
      na_mapped_lsq = .false.
      call set_lambda(grid)
      call slenr0_avg_to_nodes(grid)
      call set_slenr0_bc_strong( grid%nnodes0, grid%x, grid%y, grid%z, &
                                 grid%bcc )
      call set_slen(grid)

      !..set slenr0 using mapped coordinates.
      na_mapped_lsq = .true.
      call set_lambda(grid)
      call slenr0_avg_to_nodes(grid)
      call set_slenr0_bc_strong( grid%nnodes0, grid%x, grid%y, grid%z, &
                                 grid%bcc )
      call set_slen(grid)

    elseif ( na_mapped_lsq ) then

      !..set slenxn/yn/zn and slen to exact values for cylinder.
      na(g)%slen(:)   = sqrt( grid%x(:)**2 + grid%z(:)**2 )
      na(g)%slenxn(:) = grid%x(:)/na(g)%slen(:)
      na(g)%slenzn(:) = grid%z(:)/na(g)%slen(:)
      na(g)%slenyn(:) = 0._dp
      na(g)%slen(:)   = na(g)%slen(:) - 1._dp

    endif

    call set_lambda(grid)

  end subroutine set_up_node_averaging

!============================== SET_SLEN =====================================80
!
!  Set slen for node-averaging from slenr0.
!
!=============================================================================80
  subroutine set_slen( grid )

    use grid_types,  only : grid_type
    use kinddefs,    only : dp
    use lmpi,        only : lmpi_nproc

    type(grid_type), intent(in) :: grid

    integer :: node, node_max

    real(dp) :: dx, dy, dz, prediction, actual
    real(dp) :: rr, tr, slenr, error, error_max, rr_max, tr_max

    logical :: check_cylinder_exact_distance = .false.

  continue

    g=grid%igrid

    error_max = -huge(1._dp)

    if ( skeleton > 0 ) then
      write(*,*) 'Node averaging...setting slen via slenr0.'
    endif

    do node = 1, grid%nnodes0
      dx = ( grid%x(node) - na(g)%slenxn(node) )
      dy = ( grid%y(node) - na(g)%slenyn(node) )*tf
      dz = ( grid%z(node) - na(g)%slenzn(node) )
      na(g)%slen(node) = sqrt( dx**2 + dy**2 + dz**2 )

      if ( .not. check_cylinder_exact_distance ) cycle
      if ( lmpi_nproc > 1 ) cycle
      if ( na(g)%slen(node) < 10._dp*slen_tolerance ) cycle

      rr = sqrt( grid%x(node)**2 + grid%z(node)**2 )
      tr = atan2( real(grid%z(node),dp),real(grid%x(node),dp) )&
      *180._dp/acos(-1._dp)
      slenr = rr - 1.0_dp

      error = abs( na(g)%slen(node) - slenr )/slenr
      if ( error > error_max ) then
        error_max = error
        rr_max    = rr
        tr_max    = tr
        node_max  = node
        prediction= na(g)%slen(node)
        actual    = slenr
      endif

    end do

    if ( lmpi_nproc == 1 .and. check_cylinder_exact_distance ) then
      write(*,*) 'NA: Maximum relative error in distance function for cylinder:'
      write(*,*) 'NA: .....error=',error_max
      write(*,*) 'NA: ......node=',node_max
      write(*,*) 'NA: .....theta=',tr_max
      write(*,*) 'NA: ....radius=',rr_max
      write(*,*) 'NA: ...s-exact=',actual
      write(*,*) 'NA: s-discrete=',prediction
    endif

  end subroutine set_slen

!============================== SET_LAMBDA ===================================80
!
!  Sets up weights for node averaging.
!
!=============================================================================80
  subroutine set_lambda(grid)

    use grid_types,      only : grid_type
    use kinddefs,        only : dp
    use fun3d_constants, only : my_1
    use lmpi,            only : lmpi_bcast, lmpi_reduce, lmpi_nproc
    use debug_defs,      only : debug_q, debug_node_loc

    type(grid_type), intent(in) :: grid

    integer :: ielem, icell, gcell, inode, node, i, warnings, sdet_min_node

    real(dp) :: xc, yc, zc, sc, xn, yn, zn, lx, ly, lz
    real(dp) :: sdet, sdet_min, sdet_tolerance, ws_tolerance

    real(dp), dimension(grid%nnodes0) :: Rx,Ry,Rz,Ixx,Iyy,Izz,Ixy,Ixz,Iyz
    real(dp), dimension(9) :: a
    real(dp), dimension(3) :: sfac

    character (len=256) :: filename

    real(dp), dimension(grid%nnodes0) :: node_with_warning

    type(lsq_ref_type) :: lsq_mrefs

  continue

    sdet_tolerance = 100._dp*( epsilon(1._dp) )
    ws_tolerance   = 100._dp*( epsilon(1._dp) )

    if ( skeleton > 0 ) then
      write(*,*) 'Node averaging...interior scaling factors.'
    endif

    warnings              = 0
    sdet_min             = huge(1._dp)
    node_with_warning(:) = 0

! Gather some information about scale at a node from interior nodes
! so we can check determinants.  The approach is limited in efficacy
! to isotropic grids or anisotropies aligned to the x, y, or z axes.
! A better approach would be to use the distance function to construct
! a local coordinate system more aligned to the anisotropy.

! Store scales in lambdax, lambday, lambdaz temporarily.

    na(g)%lambdax(:) = 0._dp
    na(g)%lambday(:) = 0._dp
    na(g)%lambdaz(:) = 0._dp
    sc = 0._dp
    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell

        gcell = grid%elem(ielem)%cell_map(icell)

        xc = grid%xc(gcell) ; yc = grid%yc(gcell) ; zc = grid%zc(gcell)
        if ( na_mapped_lsq ) sc = grid%slen(gcell)

        do inode = 1, grid%elem(ielem)%node_per_cell

          node = grid%elem(ielem)%c2n(inode,icell)

          local_node_scale : if ( node <= grid%nnodes0 ) then

            xn = grid%x(node) ; yn = grid%y(node) ; zn = grid%z(node)

            lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
                  xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
               na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )

            sfac(1:3) = na_scale(lsq_mrefs, xc, yc, zc, sc )

            na(g)%lambdax(node) = max( na(g)%lambdax(node), sfac(1) )
            na(g)%lambday(node) = max( na(g)%lambday(node), sfac(2) )
            na(g)%lambdaz(node) = max( na(g)%lambdaz(node), sfac(3) )

          endif local_node_scale

        end do

      end do
    end do

    if ( skeleton > 0 ) then
      write(*,*) 'Node averaging...interior lsq terms.'
    endif

! Gather the R's and I's

    Rx = 0.0_dp
    Ry = 0.0_dp
    Rz = 0.0_dp

    Ixx = 0.0_dp
    Iyy = 0.0_dp
    Izz = 0.0_dp

    Ixy = 0.0_dp
    Ixz = 0.0_dp
    Iyz = 0.0_dp

! Pieces from cells

    sc = 0._dp
    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell

        gcell = grid%elem(ielem)%cell_map(icell)

        xc = grid%xc(gcell) ; yc = grid%yc(gcell) ; zc = grid%zc(gcell)
        if ( na_mapped_lsq ) sc = grid%slen(gcell)

        do inode = 1, grid%elem(ielem)%node_per_cell

          node = grid%elem(ielem)%c2n(inode,icell)

          local_node : if ( node <= grid%nnodes0 ) then

            xn = grid%x(node) ; yn = grid%y(node) ; zn = grid%z(node)

            lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
                  xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
               na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )

            a(1:9) = na_lsq_a(lsq_mrefs, node, xc, yc, zc, sc )

            Rx(node) = Rx(node) + a(1)
            Ry(node) = Ry(node) + a(2)
            Rz(node) = Rz(node) + a(3)

            Ixx(node) = Ixx(node) + a(4)
            Iyy(node) = Iyy(node) + a(5)
            Izz(node) = Izz(node) + a(6)

            Ixy(node) = Ixy(node) + a(7)
            Ixz(node) = Ixz(node) + a(8)
            Iyz(node) = Iyz(node) + a(9)

          endif local_node

        end do

      end do
    end do

    if ( skeleton > 0 ) then
      write(*,*) 'Node averaging...boundary lsq terms.'
    endif

    call na_lsq_bc_cont( grid%nnodes0, grid%x, grid%y, grid%z, grid%bcc, &
                         Rx, Ry, Rz, Ixx, Iyy, Izz, Ixy, Ixz, Iyz )

    ! Compute determinant and find lambda terms.  This determinant
    ! is actually a subfactor determinant of the least square system.

    if ( .not. q_2d ) then

      do i = 1, grid%nnodes0

        sdet = Ixx(i)*(Iyy(i)*Izz(i) - Iyz(i)*Iyz(i)) &
             - Ixy(i)*(Ixy(i)*Izz(i) - Ixz(i)*Iyz(i)) &
             + Ixz(i)*(Ixy(i)*Iyz(i) - Iyy(i)*Ixz(i))

        if ( abs( sdet ) < sdet_min ) then
          sdet_min      = abs( sdet )
          sdet_min_node = i
        endif

        if ( abs( sdet ) > sdet_tolerance ) then

          lx = (-Rx(i)*(Iyy(i)*Izz(i) - Iyz(i)*Iyz(i))       &
               + Ry(i)*(Ixy(i)*Izz(i) - Ixz(i)*Iyz(i))       &
               - Rz(i)*(Ixy(i)*Iyz(i) - Iyy(i)*Ixz(i))) / sdet
          ly =  (Rx(i)*(Ixy(i)*Izz(i) - Ixz(i)*Iyz(i))       &
               - Ry(i)*(Ixx(i)*Izz(i) - Ixz(i)*Ixz(i))       &
               + Rz(i)*(Ixx(i)*Iyz(i) - Ixy(i)*Ixz(i))) / sdet
          lz = (-Rx(i)*(Ixy(i)*Iyz(i) - Iyy(i)*Ixz(i))       &
               + Ry(i)*(Ixx(i)*Iyz(i) - Ixy(i)*Ixz(i))       &
               - Rz(i)*(Ixx(i)*Iyy(i) - Ixy(i)*Ixy(i))) / sdet

          !...account for scaling  - from here use unscaled x/y/z.
          na(g)%lambdax(i) = lx/na(g)%lambdax(i)
          na(g)%lambday(i) = ly/na(g)%lambday(i)
          na(g)%lambdaz(i) = lz/na(g)%lambdaz(i)

        else

          !...fall back to arithmetic averages.
          warnings   = warnings + 1
          na(g)%lambdax(i) = 0._dp
          na(g)%lambday(i) = 0._dp
          na(g)%lambdaz(i) = 0._dp

        endif

      end do

    else

      do i = 1, grid%nnodes0

        sdet = Ixx(i)*Izz(i) - Ixz(i)*Ixz(i)

        if ( debug_q .and. i == debug_node_loc ) then
          write(*,*) 'NA: Checking node averaging.'
          write(*,*) 'NA: ......node=',i
          write(*,*) 'NA: ......sdet=',sdet
          write(*,*) 'NA: .......Ixx=',Ixx(i)
          write(*,*) 'NA: .......Izz=',Izz(i)
          write(*,*) 'NA: .......Ixz=',Ixz(i)
          write(*,*) 'NA: .na_scalex=',na(g)%lambdax(i)
          write(*,*) 'NA: .na_scalez=',na(g)%lambdaz(i)
          write(*,*) 'NA: .........x=',grid%x(i)
          write(*,*) 'NA: .........y=',grid%y(i)
          write(*,*) 'NA: .........z=',grid%z(i)
          if ( na_mapped_lsq ) then
            write(*,*) 'NA: ....slenxn=',na(g)%slenxn(i)
            write(*,*) 'NA: ....slenyn=',na(g)%slenyn(i)
            write(*,*) 'NA: ....slenzn=',na(g)%slenzn(i)
            write(*,*) 'NA: ......slen=',na(g)%slen(i)
          endif
        endif

        if ( abs( sdet ) < sdet_min ) then
          sdet_min      = abs( sdet )
          sdet_min_node = i
        endif

        if ( abs( sdet ) > sdet_tolerance ) then

          lx = -( Rx(i)*Izz(i) - Rz(i)*Ixz(i) ) / sdet
          lz = -( Rz(i)*Ixx(i) - Rx(i)*Ixz(i) ) / sdet

          !...account for scaling - from here use unscaled x/y/z.
          na(g)%lambdax(i) = lx/na(g)%lambdax(i)
          na(g)%lambdaz(i) = lz/na(g)%lambdaz(i)

          if ( debug_q .and. i == debug_node_loc ) then
            write(*,*) 'NA: ......node=',i
            write(*,*) 'NA: ........lx=',lx
            write(*,*) 'NA: ........lz=',lz
            write(*,*) 'NA: ...lambdax=',na(g)%lambdax(i)
            write(*,*) 'NA: ...lambdaz=',na(g)%lambdaz(i)
          endif

        else

          !...fall back to arithmetic averages.
          warnings = warnings + 1
          if ( warnings == 1 .and. lmpi_nproc == 1 ) then
            write(*,*) 'NA: Subfactor determinant below tolerance.'
            write(*,*) 'NA: ......node=',i
            write(*,*) 'NA: ......sdet=',sdet
            write(*,*) 'NA: .......Ixx=',Ixx(i)
            write(*,*) 'NA: .......Izz=',Izz(i)
            write(*,*) 'NA: .......Ixz=',Ixz(i)
            write(*,*) 'NA: ...lambdax=',na(g)%lambdax(i)
            write(*,*) 'NA: ...lambdaz=',na(g)%lambdaz(i)
            write(*,*) 'NA: .........x=',grid%x(i)
            write(*,*) 'NA: .........y=',grid%y(i)
            write(*,*) 'NA: .........z=',grid%z(i)
          endif
          na(g)%lambdax(i) = 0._dp
          na(g)%lambdaz(i) = 0._dp
          node_with_warning(warnings) = i
        endif

        na(g)%lambday(i) = 0._dp

      end do

    endif

    i=warnings
    call lmpi_reduce(i,node)
    call lmpi_bcast(node)
    if ( node > 0 ) then
      if ( lmpi_master ) then
        write(*,*) 'NA: Subfactor determinant < tolerance.'
        write(*,*) 'NA: ....sdet_tolerance=',sdet_tolerance
        write(*,*) 'NA: .....sdet_warnings=',node
        write(*,*) 'NA: ...fraction_nodes=',real(node,dp)/&
                                            real(grid%nnodesg,dp)
        write(*,*) 'NA: Falling back to arithmetic averaging position &
                   &at nodes where subfactor determinant below tolerance.'
      endif
    else
      if ( lmpi_master ) then
        write(*,*) 'NA: No subfactor determinants < tolerance.'
        write(*,*) 'NA: ...sdet_tolerance=',sdet_tolerance
        write(*,*) 'NA: .........sdet_min=',sdet_min
        write(*,*) 'NA: .....sdet_min_node',sdet_min_node
      endif
    endif

    ! Accumulate weight_sum into weight_sum_inv_qt (no clipping).

    na(g)%weight_sum_inv_qt(:) = 0._dp
    na(g)%n_sum(:)             = 0

    !..contributions from cells.
    sc = 0._dp
    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell

        gcell = grid%elem(ielem)%cell_map(icell)

        xc = grid%xc(gcell) ; yc = grid%yc(gcell) ; zc = grid%zc(gcell)
        if ( na_mapped_lsq ) sc = grid%slen(gcell)

        do inode = 1, grid%elem(ielem)%node_per_cell

          node = grid%elem(ielem)%c2n(inode,icell)

          local_node_ws : if ( node <= grid%nnodes0 ) then
            xn = grid%x(node) ; yn = grid%y(node) ; zn = grid%z(node)

            lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
                  xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
               na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )
            na(g)%weight_sum_inv_qt(node) = na(g)%weight_sum_inv_qt(node) &
                       + na_weight( lsq_mrefs, node, xc, yc, zc, sc )
            na(g)%n_sum(node) = na(g)%n_sum(node) + 1

            if ( debug_q .and. node == debug_node_loc ) then
              write(*,*)
              write(*,*) 'NA...interior..........node=',node
              write(*,*) 'NA...interior.........x,y,z=',xn, yn, zn
              write(*,*) 'NA...interior........na(g)%lambda=',             &
              na(g)%lambdax(node),na(g)%lambday(node),na(g)%lambdaz(node)
              write(*,*) 'NA...interior...xc,yc,zc,sc=',xc, yc, zc, sc
              write(*,*) 'NA...interior........weight=',             &
              na_weight( lsq_mrefs, node, xc, yc, zc, sc )
            endif
          endif local_node_ws

        end do

      end do
    end do

    if ( debug_q .and. debug_node_loc > 0 ) then
      write(*,*)
      write(*,*) 'NA: ................node=',debug_node_loc
      write(*,*) 'NA: weight_sum(interior)=',&
                  na(g)%weight_sum_inv_qt(debug_node_loc)
    endif

    !..contributions from boundary faces
    call weightsum_bc_cont( grid%nnodes0, grid%x, grid%y, grid%z, grid%bcc )

    if ( debug_q .and. debug_node_loc > 0 ) then
      write(*,*)
      write(*,*) 'NA: ................node=',debug_node_loc
      write(*,*) 'NA: weight_sum(   total)=',&
                 na(g)%weight_sum_inv_qt(debug_node_loc)
    endif

! Store the inverse of the accumulated weight_sums.  The accumulated
! weight sums are a scaled determinant of the least square system.

    do node = 1, grid%nnodes0
      if ( abs( na(g)%weight_sum_inv_qt(node) ) < ws_tolerance ) then

        !...fall back to arithmetic averages.
        na(g)%lambdax(node) = 0._dp
        na(g)%lambday(node) = 0._dp
        na(g)%lambdaz(node) = 0._dp
        na(g)%weight_sum_inv_qt(node) = 1._dp/real(na(g)%n_sum(node),dp)

        warnings = warnings + 1
        node_with_warning(warnings) = node
        cycle
      endif
      na(g)%weight_sum_inv_qt(node) = 1._dp/na(g)%weight_sum_inv_qt(node)
    end do

    if ( warnings > 0 .and. lmpi_nproc == 1 ) then
      filename = 'warnings_node_avg'
      filename = trim(filename) // '_tec.dat'
      open(unit=56, file=filename)
      rewind(56)
      write(56,'(a)') 'title="Determinant warnings node averaging"'
      write(56,'(2a)') 'variables="node", "x", "y", "z"'
      write(56,*)'ZONE T="Points" I=',warnings
      do i=1,warnings
        node = node_with_warning(i)
        write(56,'(i10,5e23.15)') node,grid%x(node),grid%y(node),grid%z(node)
      enddo
      close(56)
    endif

    i=warnings
    call lmpi_reduce(i,warnings)
    call lmpi_bcast(warnings)
    if ( warnings > 0 ) then
      if ( lmpi_master ) then
        write(*,*) 'NA: Weight sums < tolerance in set_up_node_averaging.'
        write(*,*) 'NA: ........tolerance=',ws_tolerance
        write(*,*) 'NA: .........warnings=',warnings
        write(*,*) 'NA: ...fraction_nodes=',real(warnings,dp)/&
                                            real(grid%nnodesg,dp)
      endif
    else
      if ( lmpi_master ) then
        write(*,*) 'NA: No Weight sums < tolerance in set_up_node_averaging.'
        write(*,*) 'NA: ........tolerance=',ws_tolerance
      endif
    endif

  end subroutine set_lambda

!=============================== NA_LSQ_BC_CONT ==============================80
!
! Contributions to node avg lsq matrix terms from boundary faces.
!
!=============================================================================80
  subroutine na_lsq_bc_cont( nnodes0, x, y, z, bcc,                           &
                             Rx, Ry, Rz, Ixx, Iyy, Izz, Ixy, Ixz, Iyz )

    use kinddefs,        only : dp
    use fun3d_constants, only : my_1

    integer, intent(in) :: nnodes0

    real(dp), dimension(:), intent(in)    :: x, y, z
    type(bcc_type),         intent(in)    :: bcc
    real(dp), dimension(:), intent(inout) :: Rx, Ry, Rz
    real(dp), dimension(:), intent(inout) :: Ixx, Iyy, Izz, Ixy, Ixz, Iyz

    integer :: node, i, n, num_bface_nodes

    real(dp) :: xf, yf, zf, sf, xn, yn, zn

    real(dp), dimension(9) :: a

    type(lsq_ref_type) :: lsq_mrefs

  continue

    faces : do n = 1, bcc%n_faces01

      if (bcc%ibc(n) == bc_null) cycle faces

      xf = bcc%xface(n) ; yf = bcc%yface(n) ; zf = bcc%zface(n)
      sf = bcc%slenface(n)

      !...tria or quad.
      num_bface_nodes = 4
      if (bcc%nodes(4,n) == bcc%nodes(1,n)) num_bface_nodes = 3

      !...Loop over the nodes on the boundary face.

      bface_nodes : do i = 1, num_bface_nodes

        node = bcc%nodes(i,n)

        if ( node > nnodes0 ) cycle bface_nodes

        xn = x(node) ; yn = y(node) ; zn = z(node)

        lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
              xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
           na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )
        a(1:9) = na_lsq_a(lsq_mrefs, node, xf, yf, zf, sf )

        Rx(node) = Rx(node) + a(1)
        Ry(node) = Ry(node) + a(2)
        Rz(node) = Rz(node) + a(3)

        Ixx(node) = Ixx(node) + a(4)
        Iyy(node) = Iyy(node) + a(5)
        Izz(node) = Izz(node) + a(6)

        Ixy(node) = Ixy(node) + a(7)
        Ixz(node) = Ixz(node) + a(8)
        Iyz(node) = Iyz(node) + a(9)

      end do bface_nodes

    end do faces

  end subroutine na_lsq_bc_cont

!=============================== WEIGHTSUM_LSQ_BC_CONT =======================80
!
! Contributions to node avg lsq matrix terms from boundary faces.
!
!=============================================================================80
  subroutine weightsum_bc_cont(nnodes0, x, y, z, bcc)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_1

    integer, intent(in) :: nnodes0

    real(dp), dimension(:), intent(in)    :: x, y, z
    type(bcc_type),         intent(in)    :: bcc

    integer :: node, i, n, num_bface_nodes

    real(dp) :: xf, yf, zf, sf, xn, yn, zn

    type(lsq_ref_type) :: lsq_mrefs

  continue

    faces : do n = 1, bcc%n_faces01

      if (bcc%ibc(n) == bc_null) cycle faces

      xf = bcc%xface(n) ; yf = bcc%yface(n) ; zf = bcc%zface(n)
      sf = bcc%slenface(n)

      !...tria or quad.
      num_bface_nodes = 4
      if (bcc%nodes(4,n) == bcc%nodes(1,n)) num_bface_nodes = 3

      !...Loop over the nodes on the boundary face.

      bface_nodes : do i = 1, num_bface_nodes

        node = bcc%nodes(i,n)

        if ( node > nnodes0 ) cycle bface_nodes

        xn = x(node) ; yn = y(node) ; zn = z(node)

        lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                 &
             xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
          na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )
        na(g)%weight_sum_inv_qt(node) = na(g)%weight_sum_inv_qt(node)        &

                                + na_weight( lsq_mrefs, node, xf, yf, zf, sf )
        na(g)%n_sum(node) = na(g)%n_sum(node) + 1
      end do bface_nodes

    end do faces

  end subroutine weightsum_bc_cont

!================================= GREEN_GAUSS_CC ============================80
!
! Calculates gradients using Green-Gauss for cell-centered.
!
!=============================================================================80
  subroutine green_gauss_cc(nnodes01, qavg, x, y, z, gradx, grady, gradz,      &
                            nelem, elem, n_tot, n_grd, ncell01, ncell0)

    use element_types,        only : elem_type
    use element_defs,         only : max_node_per_cell, max_face_per_cell
    use kinddefs,             only : dp
    use utilities,            only : cell_gradients
    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations

    integer, intent(in) :: nnodes01, n_tot, n_grd, nelem, ncell01, ncell0

    real(dp), dimension(n_tot,nnodes01), intent(in)  :: qavg
    real(dp), dimension(nnodes01),       intent(in)  :: x, y, z
    real(dp), dimension(n_grd,ncell01),  intent(out) :: gradx, grady, gradz

    type(elem_type), dimension(nelem), intent(in)  :: elem

    integer :: ielem, icell, gcell, i, inode, my_ntt

    real(dp) :: cell_vol

    real(dp), dimension(max_face_per_cell)       :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)       :: x_node, y_node, z_node
    real(dp), dimension(n_grd,max_node_per_cell) :: q_node

  continue

    gradx(:,:) = 0.0_dp
    grady(:,:) = 0.0_dp
    gradz(:,:) = 0.0_dp

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

    if(my_ntt <= first_order_iterations) return

    elem_loop : do ielem = 1, nelem
      cell_loop : do icell = 1, elem(ielem)%ncell

        gcell = elem(ielem)%cell_map(icell)

        if ( gcell <= ncell0 ) then

          do i = 1, elem(ielem)%node_per_cell
            inode = elem(ielem)%c2n(i,icell)
            x_node(i)         = x(inode)
            y_node(i)         = y(inode)
            z_node(i)         = z(inode)
            q_node(1:n_grd,i) = qavg(1:n_grd,inode)
          end do

          call cell_gradients(elem(ielem)%edge_per_cell, max_node_per_cell,    &
                              elem(ielem)%face_per_cell, x_node, y_node,z_node,&
                              n_grd, q_node, elem(ielem)%local_f2n,            &
                              elem(ielem)%e2n_2d, gradx(1:n_grd,gcell),        &
                              grady(1:n_grd,gcell), gradz(1:n_grd,gcell),      &
                              cell_vol, nx, ny, nz)

        endif

      end do cell_loop
    end do elem_loop

  end subroutine green_gauss_cc

!================================= NA_WEIGHT =================================80
!
! Node-average weighting.
!
!=============================================================================80

pure function na_weight(lsq_mrefs, node, xc, yc, zc, sc )

    use lsq_types,             only : lsq_ref_type

    type(lsq_ref_type), intent(in) :: lsq_mrefs
    integer,            intent(in) :: node
    real(dp),           intent(in) :: xc, yc, zc, sc

    real(dp) :: dx, dy, dz
    real(dp), dimension(4) :: coords

    real(dp) :: na_weight

  continue

    coords = lsq_coords( lsq_mrefs, tf, xc, yc, zc, sc )

    dx = coords(1)
    dy = coords(2)
    dz = coords(3)
    if ( q_2d ) then
      dz = dy
      dy = 0._dp
    endif

    na_weight = 1._dp + na(g)%lambdax(node)*dx &
                      + na(g)%lambday(node)*dy &
                      + na(g)%lambdaz(node)*dz

  end function na_weight

!================================= NA_LSQ_A ==================================80
!
! Matrix terms associated with node-averaging/least squares.
!
!=============================================================================80

  pure function na_lsq_a(lsq_mrefs, node, xc, yc, zc, sc )

    use lsq_types,             only : lsq_ref_type

    type(lsq_ref_type), intent(in) :: lsq_mrefs
    integer,            intent(in) :: node
    real(dp),           intent(in) :: xc, yc, zc, sc

    real(dp), dimension(9) :: na_lsq_a

    real(dp) :: dx, dy, dz

    real(dp), dimension(4) :: coords

  continue

    coords = lsq_coords( lsq_mrefs, tf, xc, yc, zc, sc )

    dx = coords(1)
    dy = coords(2)
    dz = coords(3)
    if ( q_2d ) then
      dz = dy
      dy = 0._dp
    endif

    dx = dx/na(g)%lambdax(node)
    dy = dy/na(g)%lambday(node)
    dz = dz/na(g)%lambdaz(node)

    na_lsq_a(1) = dx !Rx
    na_lsq_a(2) = dy !Ry
    na_lsq_a(3) = dz !Rz

    na_lsq_a(4) = dx*dx !Ixx
    na_lsq_a(5) = dy*dy !Iyy
    na_lsq_a(6) = dz*dz !Izz

    na_lsq_a(7) = dx*dy !Ixy
    na_lsq_a(8) = dx*dz !Ixz
    na_lsq_a(9) = dy*dz !Iyz

  end function na_lsq_a

!================================= NA_SCALE ==================================80
!
! Scale of terms associated with node-averaging/least squares.
!
!=============================================================================80

  pure function na_scale(lsq_mrefs, xc, yc, zc, sc )

    use lsq_types,             only : lsq_ref_type

    type(lsq_ref_type), intent(in) :: lsq_mrefs
    real(dp),           intent(in) :: xc, yc, zc, sc

    real(dp) :: dx, dy, dz
    real(dp), dimension(3) :: na_scale

    real(dp), dimension(4) :: coords

  continue

    coords = lsq_coords( lsq_mrefs, tf, xc, yc, zc, sc )

    dx = coords(1)
    dy = coords(2)
    dz = coords(3)
    if ( q_2d ) then
      dz = dy
      dy = 1._dp
    endif

    na_scale(1) = abs(dx)
    na_scale(2) = abs(dy)
    na_scale(3) = abs(dz)

  end function na_scale

!=============================== QT_FACE_NODE_AVG ============================80
!
! Set bc face values to T variables (var1, u, v, w, T, turb1, turb2, ...)
!
!=============================================================================80
  subroutine qt_face_node_avg(eqn_set, n_q, nbfaces, qcell, qt_face, bcc)

    use info_depr,      only : ntt
    use bc_names,       only : tangency, farfield_riem, twall,                 &
                               viscous_solid, farfield_roe,                    &
                               symmetry_x, symmetry_y, symmetry_z,             &
                               extrapolate, back_pressure,                     &
                               dirichlet, dirichlet_viscous
    use lmpi,           only : lmpi_reduce, lmpi_bcast
    use exact,          only : exact_qt
    use solution_types, only : compressible
    use kinddefs,       only : dp

    integer, intent(in) :: eqn_set, n_q, nbfaces

    real(dp), dimension(:,:),         intent(in)    :: qcell
    real(dp), dimension(n_q,nbfaces), intent(inout) :: qt_face
    type(bcc_type),                   intent(in)    :: bcc

    integer :: ibc, n, icell, ierr

    real(dp) :: u, v, w, ubar, xnorm, ynorm, znorm
    real(dp) :: xface, yface, zface

    real(dp), dimension(n_q) :: qexact, qt

    integer(system_i1), dimension(n_q) :: dqt

  continue

    if ( q_type /= conserved_q_type ) then
      if ( lmpi_master ) then
        write(*,*) ' Incorrect q_dof type...stopping in qt_face_node_avg.'
        write(*,*) ' ..............q_type=',q_type
        write(*,*) ' ....conserved_q_type=',conserved_q_type
      endif
      call lmpi_die
    endif

    ierr = 0

    faces : do n = 1, bcc%n_faces01

      ibc   = bcc%ibc(n) ; if ( ibc == bc_null ) cycle

      icell = bcc%cell(n)

      xnorm = bcc%xn(n)
      ynorm = bcc%yn(n)
      znorm = bcc%zn(n)

      xface = bcc%xface(n)
      yface = bcc%yface(n)
      zface = bcc%zface(n)

      qt(1:n_q) = qt_from_qc( eqn_set, n_q, qcell(1:n_q,icell) )

!   Default to Neumann condition : q(face) = q(interior).

      qt_face(1:n_q,n) = qt(1:n_q)

      select case(ibc)
      case ( viscous_solid )

        qt_face(2:n_q,n) = 0._dp
        if ( eqn_set == compressible ) qt_face(5,n) = twall
        if ( ic_exact ) then
          call exact_qt( eqn_set, n_q, xface , yface, zface, qexact)
          qt_face(2:n_q,n) = qexact(2:n_q)
        endif

      case ( dirichlet, dirichlet_viscous )

        call exact_qt( eqn_set, n_q, xface , yface, zface, qexact)
        qt_face(1:n_q,n) = qexact(1:n_q)

      case ( symmetry_x )

        qt_face(2,n) = 0._dp

      case ( symmetry_y )

        qt_face(3,n) = 0._dp

      case ( symmetry_z )

        qt_face(4,n) = 0._dp

      case ( farfield_riem, farfield_roe, &
             extrapolate, back_pressure )

        if ( ic_exact ) then
          call exact_qt( eqn_set, n_q, xface , yface, zface, qexact)
          qt_face(1:n_q,n) = qexact(1:n_q)
        endif

      case ( tangency )

        u = qt(2)
        v = qt(3)
        w = qt(4)
        if ( ic_exact ) then
          call exact_qt( eqn_set, n_q, xface , yface, zface, qexact)
          u = qexact(2)
          v = qexact(3)
          w = qexact(4)
        endif

        ubar = u*xnorm + v*ynorm + w*znorm

        qt_face(2,n) = u - xnorm*ubar
        qt_face(3,n) = v - ynorm*ubar
        qt_face(4,n) = w - znorm*ubar

      case default
        if ( ierr == 0 )                                               &
        write(*,*) 'BC type not valid for qt_face_node_avg:',bcc%ibc(n)
        ierr = ierr + 1
      end select

      !...Just to ensure the circuit is complete..
      if ( ntt == 1 ) then
        call dqt_face_node_avg( ibc, n_q, dqt )
        if ( dqt(1) == -1 ) ierr = ierr + 1
      endif

    end do faces

    if ( ntt == 1 ) then
      ibc = ierr
      call lmpi_reduce(ibc,ierr)
      call lmpi_bcast(ierr)
      if ( ierr > 0 ) call lmpi_die
    endif

  end subroutine qt_face_node_avg

!=============================== DQT_FACE_NODE_AVG ===========================80
!
! Linearization coefficients for bc face values in terms of
! T variables (var1, u, v, w, T, turb1, turb2, ...)
!
!=============================================================================80
  subroutine dqt_face_node_avg( ibc, n_q, dqt )

    use kinddefs,  only : system_i1
    use bc_names,  only : tangency, farfield_riem,                             &
                          viscous_solid, farfield_roe,                         &
                          symmetry_x, symmetry_y, symmetry_z,                  &
                          extrapolate, back_pressure,                          &
                          dirichlet, dirichlet_viscous

    integer,                            intent(in)    :: ibc, n_q
    integer(system_i1), dimension(n_q), intent(out)   :: dqt

  continue

!   Default weights to Neumann condition : q(face) = q(interior).

    dqt(1:n_q) = 1

    select case(ibc)

    case ( viscous_solid )

      dqt(2:n_q) = 0

    case ( dirichlet, dirichlet_viscous )

      dqt(1:n_q) = 0

    case ( symmetry_x )

      dqt(    2) = 0

    case ( symmetry_y )

      dqt(    3) = 0

    case ( symmetry_z )

      dqt(    4) = 0

    case ( farfield_riem, farfield_roe,  &
           extrapolate, back_pressure )

      if ( ic_exact ) dqt(1:n_q) = 0

    case ( tangency )

      !default

    case default

      write(*,*) 'BC type not valid for dqt_face_node_avg:',ibc
      dqt(1) = -1

    end select

  end subroutine dqt_face_node_avg

!=============================== QAVG_BC_CONT ================================80
!
! Contributions to qavg from boundary faces.
!
!=============================================================================80
  subroutine qavg_bc_cont( nnodes0, x, y, z, bcc,                              &
                           n_tot, qavg, q_dof, qbc_na, qbc_na_loc,             &
                           weight_sum, inv_weighted_sum, inv_weighted_qavg,    &
                           clip_l, clip_h )

    use kinddefs,        only : dp
    use info_depr,       only : cc_clip_weights
    use fun3d_constants, only : my_0, my_half, my_1, my_2

    integer, intent(in) :: nnodes0, n_tot
    integer(system_i1), dimension(:), intent(in) :: qbc_na_loc

    real(dp), dimension(:), intent(in)    :: x, y, z
    type(bcc_type),         intent(in)    :: bcc

    real(dp), dimension(:,:), intent(in)    :: q_dof, qbc_na
    real(dp), dimension(:,:), intent(inout) :: qavg
    real(dp), dimension(:),   intent(inout) :: weight_sum
    real(dp), dimension(:),   intent(inout) :: inv_weighted_sum
    real(dp), dimension(:,:), intent(inout) :: inv_weighted_qavg

    integer, dimension(:),            intent(inout) :: clip_l, clip_h

    integer :: node, i, n, num_bface_nodes, icell

    real(dp) :: xf, yf, zf, sf, xn, yn, zn, weight

    real(dp), dimension(n_tot) :: q_on_face

    type(lsq_ref_type) :: lsq_mrefs

  continue

    faces : do n = 1, bcc%n_faces01

      if (bcc%ibc(n) == bc_null) cycle faces

      icell = bcc%cell(n)

      xf = bcc%xface(n) ; yf = bcc%yface(n) ; zf = bcc%zface(n)
      sf = bcc%slenface(n)

      !...tria or quad.
      num_bface_nodes = 4
      if (bcc%nodes(4,n) == bcc%nodes(1,n)) num_bface_nodes = 3

      if ( qbc_na_loc(n) == 0 ) then
        ! Average interior cell with ghost cell value
        q_on_face(:) = (q_dof(:,icell) + qbc_na(:,n)) * my_half
      else
        q_on_face(:) = qbc_na(:,n)
      endif

! Loop over the nodes on the current boundary face

      bface_nodes: do i = 1, num_bface_nodes

        node = bcc%nodes(i,n)

        if ( node > nnodes0 ) cycle bface_nodes

        xn = x(node) ; yn = y(node) ; zn = z(node)

        lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                   &
               xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
            na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )
        weight = na_weight(lsq_mrefs, node, xf, yf, zf, sf )

! Clip weights as required/neccessary

        if (cc_clip_weights) then
          if (weight < -1.0e-16_dp) clip_l(node) = clip_l(node) + 1
          if (weight > + my_2)      clip_h(node) = clip_h(node) + 1

          weight = min(weight, my_2)
          weight = max(weight, my_0)
        end if

        weight_sum(node) = weight_sum(node)  + weight
        qavg(:,node)     = qavg(:,node) + weight*q_on_face(:)

! Inverse-distance weighting as a backup in case we don't succeed.

        weight=my_1/(sqrt((xf-xn)*(xf-xn)+(yf-yn)*(yf-yn)+(zf-zn)*(zf-zn)))
        inv_weighted_sum(node)    = inv_weighted_sum(node) + weight
        inv_weighted_qavg(:,node) = inv_weighted_qavg(:,node)                &
                                  + weight*q_on_face(:)

      end do bface_nodes

    end do faces

  end subroutine qavg_bc_cont

!============================== SLENR0_AVG_TO_NODES ==========================80
!
!  Averages cell-centered slen origin data to the nodes using least-squares.
!  (no clipping and no inverse-distance-weighting)
!
!=============================================================================80
  subroutine slenr0_avg_to_nodes( grid )

    use grid_types,        only : grid_type
    use kinddefs,          only : dp
    use fun3d_constants,   only : my_1

    type(grid_type), intent(in)    :: grid

    integer :: ielem, icell, gcell, inode, node, i

    real(dp) :: xc, yc, zc, sc, xn, yn, zn, weight

    type(lsq_ref_type) :: lsq_mrefs

  continue

    if ( skeleton > 0 ) then
      write(*,*) 'Node averaging...interpolating for slenr0.'
    endif

    na(g)%slenxn = 0._dp
    na(g)%slenyn = 0._dp
    na(g)%slenzn = 0._dp

    !..contributions from cells
    sc = 0._dp
    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell

        gcell = grid%elem(ielem)%cell_map(icell)

        xc = grid%xc(gcell) ; yc = grid%yc(gcell) ; zc = grid%zc(gcell)
        if ( na_mapped_lsq ) sc = grid%slen(gcell)

! Loop over the node comprising the current cell

        do inode = 1, grid%elem(ielem)%node_per_cell

          node = grid%elem(ielem)%c2n(inode,icell)

          local_node : if ( node <= grid%nnodes0 ) then
            xn = grid%x(node) ; yn = grid%y(node) ; zn = grid%z(node)

            lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
                  xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
               na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )
            weight = na_weight(lsq_mrefs, node, xc, yc, zc, sc )

            na(g)%slenxn(node) = na(g)%slenxn(node) + weight*grid%slenxn(gcell)
            na(g)%slenyn(node) = na(g)%slenyn(node) + weight*grid%slenyn(gcell)
            na(g)%slenzn(node) = na(g)%slenzn(node) + weight*grid%slenzn(gcell)

          endif local_node

        end do

      end do
    end do

    !...contributions from boundary faces
    call slenr0_avg_bc_cont( grid%nnodes0, grid%x, grid%y, grid%z, grid%bcc, &
                             grid%xc, grid%yc, grid%zc,                      &
                             grid%slenxn, grid%slenyn, grid%slenzn )

! Divide the sums by the weight sums

    do i = 1, grid%nnodes0
      na(g)%slenxn(i) = na(g)%slenxn(i) * na(g)%weight_sum_inv_qt(i)
      na(g)%slenyn(i) = na(g)%slenyn(i) * na(g)%weight_sum_inv_qt(i) * tf
      na(g)%slenzn(i) = na(g)%slenzn(i) * na(g)%weight_sum_inv_qt(i)
    end do

  end subroutine slenr0_avg_to_nodes

!=============================== SLENR0_AVG_BC_CONT ==========================80
!
! Contributions to surrounding nodes comprising the boundary faces.
!
!=============================================================================80
  subroutine slenr0_avg_bc_cont( nnodes0, x, y, z, bcc,                        &
                                 xc, yc, zc, slenxnc, slenync, slenznc )

    use kinddefs,        only : dp
    use fun3d_constants, only : my_1

    integer, intent(in) :: nnodes0

    real(dp), dimension(:), intent(in) :: x, y, z, xc, yc, zc
    real(dp), dimension(:), intent(in) :: slenxnc, slenync, slenznc
    type(bcc_type),         intent(in) :: bcc

    integer :: node, i, n, num_bface_nodes, cell

    real(dp) :: xf, yf, zf, sf, xn, yn, zn, weight, sx0, sy0, sz0
    real(dp) :: tx, ty, tz, term

    type(lsq_ref_type) :: lsq_mrefs

  continue

    faces : do n = 1, bcc%n_faces01

      if (bcc%ibc(n) == bc_null) cycle faces

      xf = bcc%xface(n) ; yf = bcc%yface(n) ; zf = bcc%zface(n)
      sf = bcc%slenface(n)

      cell = bcc%cell(n)

      !...tria or quad.
      num_bface_nodes = 4
      if (bcc%nodes(4,n) == bcc%nodes(1,n)) num_bface_nodes = 3

      !...Loop over the nodes on the boundary face.

      bface_nodes : do i = 1, num_bface_nodes

        node = bcc%nodes(i,n)

        if ( node > nnodes0 ) cycle bface_nodes

        xn = x(node) ; yn = y(node) ; zn = z(node)
        lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                  &
              xn, yn, zn, my_1, na(g)%cgamma(node), na(g)%slen(node), &
           na(g)%slenxn(node), na(g)%slenyn(node), na(g)%slenzn(node) )
        weight = na_weight(lsq_mrefs, node, xf, yf, zf, sf )

        !...extrapolate for slenr0 using direction of interior cell.
        tx =   xc(cell) - slenxnc(cell)
        ty = ( yc(cell) - slenync(cell) )*tf
        tz =   zc(cell) - slenznc(cell)
        term = 1._dp/sqrt( tx**2 + ty**2 + tz**2 )
        tx = tx*term
        ty = ty*term
        tz = tz*term

        sx0 = xf - tx*sf
        sy0 = yf - ty*sf
        sz0 = zf - tz*sf

        na(g)%slenxn(node) = na(g)%slenxn(node) + weight*sx0
        na(g)%slenyn(node) = na(g)%slenyn(node) + weight*sy0
        na(g)%slenzn(node) = na(g)%slenzn(node) + weight*sz0

      end do bface_nodes

    end do faces

  end subroutine slenr0_avg_bc_cont

!=============================== SET_SLENR0_BC_STRONG ========================80
!
! Set slenr0 at nodes defining distance-function-zero boundaries.
!
!=============================================================================80
  subroutine set_slenr0_bc_strong( nnodes0, x, y, z, bcc )

    use kinddefs,  only : dp
    use bc_names,  only : viscous_solid, dirichlet_viscous

    integer, intent(in) :: nnodes0

    real(dp), dimension(:), intent(in) :: x, y, z
    type(bcc_type),         intent(in) :: bcc

    integer :: node, i, n, num_bface_nodes, ibc, trip

    integer, dimension(nnodes0) :: weights

    real(dp) :: term

  continue

    trips : do trip =1,3

      faces : do n = 1, bcc%n_faces01

        ibc  = bcc%ibc(n) ; if ( ibc == bc_null ) cycle

        if ( ibc == viscous_solid .or. ibc == dirichlet_viscous ) then

          !...tria or quad.
          num_bface_nodes = 4
          if (bcc%nodes(4,n) == bcc%nodes(1,n)) num_bface_nodes = 3

          !...Loop over the nodes on the boundary face.

          bface_nodes : do i = 1, num_bface_nodes

            node = bcc%nodes(i,n)

            if ( node > nnodes0 ) cycle bface_nodes

            if ( trip == 1 ) then
              na(g)%slenxn(node) = 0._dp
              na(g)%slenyn(node) = 0._dp
              na(g)%slenzn(node) = 0._dp
              weights(node) = 0._dp
            elseif ( trip == 2 ) then
              na(g)%slenxn(node) = na(g)%slenxn(node) - bcc%xn(n)
              na(g)%slenyn(node) = na(g)%slenyn(node) - bcc%yn(n)*tf
              na(g)%slenzn(node) = na(g)%slenzn(node) - bcc%zn(n)
              weights(node) = weights(node) + 1
            elseif ( trip == 3 ) then
              if ( weights(node) < 0 ) cycle
              na(g)%slenxn(node) = na(g)%slenxn(node)/real(weights(node),dp)
              na(g)%slenyn(node) = na(g)%slenyn(node)/real(weights(node),dp)
              na(g)%slenzn(node) = na(g)%slenzn(node)/real(weights(node),dp)
              term = 1._dp/sqrt( na(g)%slenxn(node)**2 &
                               + na(g)%slenyn(node)**2 &
                               + na(g)%slenzn(node)**2 )
              na(g)%slenxn(node) = na(g)%slenxn(node)*term
              na(g)%slenyn(node) = na(g)%slenyn(node)*term
              na(g)%slenzn(node) = na(g)%slenzn(node)*term
              na(g)%slenxn(node) = x(node) - slen_tolerance*na(g)%slenxn(node)
              na(g)%slenyn(node) = y(node) - slen_tolerance*na(g)%slenyn(node)
              na(g)%slenzn(node) = z(node) - slen_tolerance*na(g)%slenzn(node)
              weights(node) = -1
            endif

          end do bface_nodes

        endif

      end do faces
    enddo trips

  end subroutine set_slenr0_bc_strong

!   Statements to include the functions that are to be inlined.
!   This is necessary because not all compilers can inline
!   functions that are in a different module.
!   N.B.: The order of the statements must reflect
!         how they are nested in the routine(s)
!         they are invoked from.

  include 'qt_from_qc.f90'
  include 'mapping_system.f90'
  include 'lsq_map_ref.f90'
  include 'lsq_coords.f90'
    include 'mapping_coords.f90'
    include 'coords_cylindrical_polar.f90'

end module node_avg_cc

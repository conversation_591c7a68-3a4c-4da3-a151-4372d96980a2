!================================= FLUX_LDFSS ================================80
!
! Calculates the fluxes on the face and compute the contribution to the
! flux balance using Edwards low dissipation flux splitting scheme
!
! Blending of the Low Dissipation Flux Split Schemes (LDFSS) no. 0 and 2 of
! to remove the carbuncle that LDFSS(2) can develop for stong shock aligned with
! the grid by switching it with the LDFSS(0) scheme while controlling the higher
! dissipation of LDFSS(0) away from the shocks by causing the scheme to revert
! to the LDFSS(2) scheme. LDFSS(0) and LDFSS(2) schemes  were developed by
<PERSON> <PERSON>,J.R., "A Low-Diffusion Flux-Splitting Scheme for Navier Stokes
! Calculations," AIAA-96-1704-CP.
!
! Note that this function uses primitive variables
!
!=============================================================================80

  pure function flux_ldfss(rx1, ry1, rz1, rx2, ry2, rz2,                       &
                           xnorm, ynorm, znorm, area, vol1, vol2,              &
                           gradx1, grady1, gradz1, phi1,                       &
                           gradx2, grady2, gradz2, phi2,                       &
                           face_speed, mu, ql, qr, second)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_4th, my_half, my_1, my_2, pi

    use info_depr,       only : adptv_entropy_fix, ivisc
    use fluid,           only : gm1
    use inviscid_flux,   only : flux_construction

    real(dp),                 intent(in) :: rx1, ry1, rz1, rx2, ry2, rz2
    real(dp),                 intent(in) :: xnorm, ynorm, znorm, area
    real(dp),                 intent(in) :: vol1, vol2
    real(dp),                 intent(in) :: gradx1, grady1, gradz1,            &
                                            gradx2, grady2, gradz2
    real(dp),                 intent(in) :: phi1, phi2
    real(dp),                 intent(in) :: face_speed, mu
    real(dp),   dimension(5), intent(in) :: ql, qr
    real(dp),   dimension(6)             :: flux_ldfss

    logical,                  intent(in) :: second

    real(dp) :: rhol, ul, vl, wl, q2l, pressl, energyl, Hl, ubarl, cl
    real(dp) :: rhor, ur, vr, wr, q2r, pressr, energyr, Hr, ubarr, cr
    real(dp) :: rho, wat, u, v, w, q2, ubar, h, c, c2
    real(dp) :: chalf, rmbl, rmbr, btl, btr
    real(dp) :: alfl, alfr, xmml, xmmr, xmhalf, xmc
    real(dp) :: delp, psum, xmcl, xmcr
    real(dp) :: mfil, mfir, ppl, ppr, pnet
    real(dp) :: switch, switchv, diss_path
    real(dp) :: xmfunct, btfunct, fact
    real(dp) :: gradpl, gradpr, pgrad, dpx2, dpy2, dpz2, plapc, pcoef
    real(dp) :: utngl, utngr, utot, dutng, unorm

    integer,  parameter :: behavior = 01
    integer,  parameter :: powerv = 4
    real(dp), parameter :: poweri = my_2
    real(dp), parameter :: laplcc = my_2
    real(dp), parameter :: plim_cf2 = 1.0_dp

    real(dp), parameter :: one_e_negative_6 = 0.000001_dp
    real(dp), parameter :: point_005 = 0.005_dp

  continue

!   Get left and right state primitive variables

    rhol   = ql(1)
    ul     = ql(2)
    vl     = ql(3)
    wl     = ql(4)
    pressl = ql(5)

    rhor   = qr(1)
    ur     = qr(2)
    vr     = qr(3)
    wr     = qr(4)
    pressr = qr(5)

!   Compute the remaining needed left and right state variables:

    q2l     = ul*ul + vl*vl + wl*wl
    energyl = pressl/gm1 + my_half*rhol*q2l
    Hl      = (energyl + pressl)/rhol
    ubarl   = xnorm*ul + ynorm*vl + znorm*wl
    cl      = sqrt(gm1*(Hl-my_half*q2l))

    q2r     = ur*ur + vr*vr + wr*wr
    energyr = pressr/gm1 + my_half*rhor*q2r
    Hr      = (energyr + pressr)/rhor
    ubarr   = xnorm*ur + ynorm*vr + znorm*wr
    cr      = sqrt(gm1*(Hr-my_half*q2r))

!   Interface contravariant Mach numbers including face_speed

    chalf = my_half * (cl+cr) ! eq. 48

    rmbl  = (ubarl - face_speed) / chalf ! eq. 49
    rmbr  = (ubarr - face_speed) / chalf ! eq. 49

!   Split contravariant Mach number

    alfl = my_half * (my_1 + sign(my_1, rmbl)) ! eq. 15
    alfr = my_half * (my_1 - sign(my_1, rmbr)) ! eq. 15
!
    btl = -max(my_0, my_1-int(abs(rmbl))) ! eq. 16
    btr = -max(my_0, my_1-int(abs(rmbr))) ! eq. 16
!
    xmml =  my_4th * (rmbl+my_1)**2 ! eq. 14
    xmmr = -my_4th * (rmbr-my_1)**2 ! eq. 14

    xmhalf = sqrt(my_half*(rmbl**2+rmbr**2))

    xmc  = my_4th * btl * btr * (xmhalf-my_1)**2

    delp = pressl - pressr
    psum = pressl + pressr

!   Modified form of eq. 26

    xmcl = btl*btr*xmc * (my_1 - (delp/psum + my_2*abs(delp)/pressl))
    xmcr = btl*btr*xmc * (my_1 + (delp/psum - my_2*abs(delp)/pressr))

    diss_path = my_1

    if ( .not. second ) then

      switch  = my_0
      switchv = my_0
      if ( flux_construction == 'ldfss' ) then
        switch = my_1
      endif

    else if ( flux_construction == 'dldfss' ) then

      diss_path = my_0

!     Blend LDFSS(0) with LDFSS(2) using the local pressure gradient and
!     the ratio deltaU(tangent)/U(normal) such that the flux normal to
!     a stagnation streamline line gets forced to be LDFSS(0) and the flux
!     normal to a slip line gets forced to be LDFSS(2) due to the behavior:
!     parameter       [stag. streamline] [slip line]
!     ----------------------------------------------
!     deltaU(tangent)        = 0            /= 0
!     U(normal)              = 0             = 0
!     gradp                  = 0             = 0

!     Construct the normalized magnitude of the undivided pressure gradient

      gradpl = sqrt(gradx1*gradx1+grady1*grady1+gradz1*gradz1)                 &
                                                  *sqrt(rx1*rx1+ry1*ry1+rz1*rz1)
      gradpr = sqrt(gradx2*gradx2+grady2*grady2+gradz2*gradz2)                 &
                                                  *sqrt(rx2*rx2+ry2*ry2+rz2*rz2)

      pgrad  = max(gradpl, gradpr) / min(pressl, pressr)

!     Construct a crude estimate of the magnitude of the
!     normalized undivided pressure Laplacian

      dpx2 = gradx2*rx2 - gradx1*rx1
      dpy2 = grady2*ry2 - grady1*ry1
      dpz2 = gradz2*rz2 - gradz1*rz1

      plapc = sqrt(dpx2*dpx2+dpy2*dpy2+dpz2*dpz2) / min(pressl, pressr)

      pcoef = max(pgrad, plim_cf2*plapc)

!     Compute deltaU(tangent) and U(normal) to compute the blend coeff.

      utngl  = sqrt(max(my_0, q2l-ubarl*ubarl))
      utngr  = sqrt(max(my_0, q2r-ubarr*ubarr))
      utot   = my_half*(sqrt(q2l)+sqrt(q2r))
      dutng  = abs(utngr-utngl)/(utot+one_e_negative_6)
      unorm  = xmhalf*chalf ! This is more robust then myhalf*(ubarl+ubar) !
      unorm  = max(unorm/(utot+one_e_negative_6), point_005 )

!     Compute the coeff. used to blend LDFSS(0) and LDFSS(2)

      switch = min(my_1-tanh(pcoef), max(my_0, min(my_1, dutng**2/unorm)))
      switch = switch*min(phi1, phi2)

!     Compute the cell face reynolds number to make the switch coeff. one
!     on low Reynolds number cells faces

      if (ivisc >= 2) then
        switchv = vswch_coef_orig(rhol, rhor, q2l, q2r, chalf, vol1, vol2,     &
                                  area, powerv, mu)
        switch = max(switch, switchv)
      else
        switchv = my_0
      end if

    else if (.not. adptv_entropy_fix) then

      diss_path = my_0

      switch  = my_1
      switchv = my_1

    else if (adptv_entropy_fix) then

!     Compute the flux type switch: N.B. switch=0=>LDFSS(0), 1=>LDFSS(2)

!     Compute the Roe averages of the primitive variables

      rho = sqrt(rhol*rhor)
      wat = rho/(rho + rhor)
      u   = ul*wat + ur*(my_1 - wat)
      v   = vl*wat + vr*(my_1 - wat)
      w   = wl*wat + wr*(my_1 - wat)
      q2  = u*u + v*v + w*w
      h   = Hl*wat + Hr*(my_1 - wat)
      c2  = gm1*(h - my_half*q2)
      c   = sqrt(c2)
      ubar = xnorm*u + ynorm*v + znorm*w

      switch = iswch_coef(rx1,ry1,rz1,rx2,ry2,rz2,gradx1,grady1,gradz1,        &
                          gradx2,grady2,gradz2,pressl,pressr,phi1,phi2,        &
                          ubarl,ubarr,q2l,q2r,q2,c2,laplcc,poweri,behavior)

!     Compute the cell face reynolds number to make the switch coeff. one
!     on low Reynolds number cells faces

      if (ivisc >= 2) then
        ubar = xnorm*u + ynorm*v + znorm*w
        switchv = vswch_coef(wat,rho,q2l,ubarl,q2r,ubarr,ubar,c,vol1,vol2,area,&
                             powerv,mu)
        if (switchv > switch) switch = my_1
      else
        switchv = my_0
      end if

    end if

!     ordered conditionals                 diss_path       switch        switchv
!---------------------------------         ---------       ------        -------
! flux_construction= 'ldfss' ; second == F         1.          0.             0.
! flux_construction='dldfss' ; second == F         1.          1.             0.
! flux_construction='dldfss'                       0.      varies         varies
! adptv_entropy_fix= F                             0.          1.             1.
! adptv_entropy_fix= T                             1.      varies         varies

!   Sonic rarefaction treatment (unpublished to the best of my knowledge)

    xmfunct = sin(my_half*pi*min(xmhalf, my_1))
    btfunct = my_4th * (rmbl - rmbr - abs(rmbl-rmbr))
    fact    = max(-my_half, btfunct) * xmfunct

!   Switch between the LDFSS(0) [switch=0] with the LDFSS(2) scheme [switch=1]
!                      [dissipative      ]          [contact-preserving      ]

    xmcl = switch*xmcl + (my_1 - diss_path*switchv)*fact
    xmcr = switch*xmcr + (my_1 - diss_path*switchv)*fact

!   Mass flux splitting (combination of eq. 24 with 47 and 25 with 47)

    mfil = (alfl*(my_1+btl)*rmbl - btl*xmml - xmcl) * (rhol * chalf)
    mfir = (alfr*(my_1+btr)*rmbr - btr*xmmr + xmcr) * (rhor * chalf)

!   Pressure splitting  and interface pressure for the momentum eq.s

    ppl = my_4th * (rmbl+my_1)**2 * (my_2-rmbl) ! eq. 20
    ppr = my_4th * (rmbr-my_1)**2 * (my_2+rmbr) ! eq. 20
    pnet = (alfl*(my_1+btl)-btl*ppl)*pressl +                                  &
           (alfr*(my_1+btr)-btr*ppr)*pressr ! eq. 18 and 19

!   Compute the contribution to the flux balance

    flux_ldfss(1) = area*(mfil    + mfir)
    flux_ldfss(2) = area*(mfil*ul + mfir*ur + pnet*xnorm)
    flux_ldfss(3) = area*(mfil*vl + mfir*vr + pnet*ynorm)
    flux_ldfss(4) = area*(mfil*wl + mfir*wr + pnet*znorm)
    flux_ldfss(5) = area*(mfil*Hl + mfir*Hr + face_speed*pnet)

    flux_ldfss(6) = switch

  end function flux_ldfss

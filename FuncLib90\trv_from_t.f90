
!================================= TRV_FROM_T ================================80
!
! Transform viscous Jacobians from temperature variables to conserved variables.
!
! Incoming variables are 1/rho, u, v, w, and T and Jacobians wrt u,v,w,T.
!
!=============================================================================80
  pure function trv_from_t( ri, u, v, w, t, df_t )

    use kinddefs,       only : dp
    use fluid,          only : ggm1

    real(dp), intent(in) :: ri, u, v, w, t

    real(dp), dimension(5,5), intent(in) :: df_t

    real(dp), dimension(5,5) :: trv_from_t

    real(dp) :: T22, T33, T44, T55, T21, T31, T41, T51, T52, T53, T54

  continue

    T21 = -u*ri
    T31 = -v*ri
    T41 = -w*ri

    T22 = ri
    T33 = ri
    T44 = ri

    T51 = -ri*( t - 0.5_dp*ggm1*(u*u + v*v + w*w) )
    T55 =  ggm1*ri
    T52 = -T55*u
    T53 = -T55*v
    T54 = -T55*w

    trv_from_t(1,:) = 0._dp

    trv_from_t(2,1) = df_t(2,2)*T21 + df_t(2,3)*T31          &
                    + df_t(2,4)*T41 + df_t(2,5)*T51
    trv_from_t(2,2) = df_t(2,2)*T22 + df_t(2,5)*T52
    trv_from_t(2,3) = df_t(2,3)*T33 + df_t(2,5)*T53
    trv_from_t(2,4) = df_t(2,4)*T44 + df_t(2,5)*T54
    trv_from_t(2,5) =                 df_t(2,5)*T55

    trv_from_t(3,1) = df_t(3,2)*T21 + df_t(3,3)*T31          &
                    + df_t(3,4)*T41 + df_t(3,5)*T51
    trv_from_t(3,2) = df_t(3,2)*T22 + df_t(3,5)*T52
    trv_from_t(3,3) = df_t(3,3)*T33 + df_t(3,5)*T53
    trv_from_t(3,4) = df_t(3,4)*T44 + df_t(3,5)*T54
    trv_from_t(3,5) =                 df_t(3,5)*T55

    trv_from_t(4,1) = df_t(4,2)*T21 + df_t(4,3)*T31          &
                    + df_t(4,4)*T41 + df_t(4,5)*T51
    trv_from_t(4,2) = df_t(4,2)*T22 + df_t(4,5)*T52
    trv_from_t(4,3) = df_t(4,3)*T33 + df_t(4,5)*T53
    trv_from_t(4,4) = df_t(4,4)*T44 + df_t(4,5)*T54
    trv_from_t(4,5) =                 df_t(4,5)*T55

    trv_from_t(5,1) = df_t(5,2)*T21 + df_t(5,3)*T31          &
                    + df_t(5,4)*T41 + df_t(5,5)*T51
    trv_from_t(5,2) = df_t(5,2)*T22 + df_t(5,5)*T52
    trv_from_t(5,3) = df_t(5,3)*T33 + df_t(5,5)*T53
    trv_from_t(5,4) = df_t(5,4)*T44 + df_t(5,5)*T54
    trv_from_t(5,5) =                 df_t(5,5)*T55

  end function trv_from_t

!================================= BIG_ANGLE ========================80
!
! Check if cell-face angles greater that a specified value and return
! .true. to skip the contribution from this cell (3D only).
!
!=============================================================================80

  pure function big_angle( face_per_cell, nxf, nyf, nzf, chk_norm )

    use kinddefs, only : dp

    integer,                            intent(in)  :: face_per_cell
    integer,  dimension(face_per_cell,face_per_cell),              &
                                        intent(in)  :: chk_norm
    real(dp), dimension(face_per_cell), intent(in)  :: nxf, nyf, nzf

    logical                                         :: big_angle

    integer :: i, j

    real(dp) :: dot

    real(dp), parameter :: my_mxd = 0.99939_dp

  continue

    big_angle = .false.

    ! SP Note: only need to check upper part of matrix chk_norm since
    ! dot products commute (A*B = B*A) and the diagonal indicates
    ! the dot product of a face with itself; also note that nx, ny, nz
    ! are not unit normals so we must scale the dot product accordingly

    face_loop : do i=1,face_per_cell

      do j=i+1,face_per_cell

        if (chk_norm(i,j) > 0) then
          dot = nxf(i)*nxf(j) + nyf(i)*nyf(j) + nzf(i)*nzf(j)
          !  scale to unit normals
          dot = dot / sqrt(nxf(i)*nxf(i) + nyf(i)*nyf(i) + nzf(i)*nzf(i))
          dot = dot / sqrt(nxf(j)*nxf(j) + nyf(j)*nyf(j) + nzf(j)*nzf(j))
          if ( dot >= my_mxd ) then
            big_angle = .true.
            exit face_loop
          endif
        end if

      end do

    end do face_loop

  end function big_angle

!============================== FLUX_ROE_DC ==================================80
!
! This routine computes the fluxes using Roe's flux difference splitting
! and compute the contribution to the flux balance
!
! This routine is for the decoupling of the generic gas path species and
! vibrational energy equations
!
! Note that this function uses primitive variables
!
!=============================================================================80

  function flux_roe_dc(mass_flux, ql, qr, rhol_new, rhor_new,                  &
                           n_species, n_momx, n_momy, n_momz,                  &
                           ndim, xnorm, ynorm, znorm, area, face_speed)

    use kinddefs,        only : dp

    integer,                     intent(in) :: ndim, n_momx, n_momy,           &
                                               n_momz,  n_species
    real(dp),                    intent(in) :: xnorm, ynorm, znorm, area
    real(dp),                    intent(in) :: face_speed
    real(dp),                    intent(in) :: rhol_new, rhor_new
    real(dp),                    intent(in) :: mass_flux
    real(dp), dimension(ndim),   intent(in) :: ql, qr

    real(dp), dimension(n_species)  :: c_i, cl_i, cr_i
    real(dp), dimension(n_species)  :: flux_roe_dc

    real(dp) :: rho, rhol, rhor, ul, ur, vl, vr, wl, wr
    real(dp) :: u, v, w, ubarl, ubarr, ubar
    real(dp) :: rhol_inv, rhor_inv, eigp, eigm, wat

    real(dp), parameter :: my_half = 0.50_dp
    real(dp), parameter :: my_1    =  1.0_dp
    integer             :: ns

  continue

    ul     = ql(n_momx)
    vl     = ql(n_momy)
    wl     = ql(n_momz)

    ur     = qr(n_momx)
    vr     = qr(n_momy)
    wr     = qr(n_momz)

!   Compute the remaining needed left and right state variables:

    ubarl  = xnorm*ul + ynorm*vl + znorm*wl - face_speed
    ubarr  = xnorm*ur + ynorm*vr + znorm*wr - face_speed

!   Compute Roe averages

    rhol = sum(ql(1:n_species))
      rhol_inv = my_1/rhol
    rhor = sum(qr(1:n_species))
      rhor_inv = my_1/rhor

    rho = sqrt(rhol*rhor)
    wat = rho/(rho + rhor)

    u    = ul*wat + ur*(my_1-wat)
    v    = vl*wat + vr*(my_1-wat)
    w    = wl*wat + wr*(my_1-wat)
    ubar = xnorm*u + ynorm*v + znorm*w - face_speed

    eigp = ubarl+abs(ubar)
    eigm = ubarr-abs(ubar)

    do ns = 1, n_species

      !Compute left, right, and roe state mass fractions
      cl_i(ns) = ql(ns)*rhol_inv
      cr_i(ns) = qr(ns)*rhor_inv
      c_i(ns)  = cl_i(ns)*wat + cr_i(ns)*(my_1-wat)

      flux_roe_dc(ns) = c_i(ns)*mass_flux                                &
                      + area*my_half*((cl_i(ns)-c_i(ns))*rhol_new*eigp   &
                      + (cr_i(ns)-c_i(ns))*rhor_new*eigm)
    end do

end function flux_roe_dc

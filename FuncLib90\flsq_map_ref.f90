!================================= FLSQ_MAP_REF ==============================80
!
! Set references for face-based least squares.
!
!=============================================================================80

  pure function flsq_map_ref( cell1, cell2, scalei, x0, y0, z0, tx, ty, tz,    &
                              xc, yc, zc, cgamma, slen,                        &
                              slenxn, slenyn, slenzn )

    use lsq_types,          only : lsq_ref_type
    use lsq_constants,      only : mlsq, cg_tol

    integer,                intent(in) :: cell1, cell2
    real(dp),               intent(in) :: scalei, x0, y0, z0, tx, ty, tz
    real(dp), dimension(:), intent(in) :: xc, yc, zc, cgamma, slen
    real(dp), dimension(:), intent(in) :: slenxn, slenyn, slenzn

    type(lsq_ref_type)                 :: flsq_map_ref

    real(dp) :: term, txr, tyr, tzr, radiusr, thetar
    real(dp) :: slenx0, sleny0, slenz0

  continue

    flsq_map_ref%xr = x0
    flsq_map_ref%yr = y0
    flsq_map_ref%zr = z0

    flsq_map_ref%scaleir = scalei

    flsq_map_ref%mapr = mlsq + 1
    if ( ( flsq_map_ref%mapr == 2 )   .and.                 &
         ( cgamma(cell1) <= cg_tol )  .and.                 &
         ( cgamma(cell2) <= cg_tol )  ) flsq_map_ref%mapr = 1

    flsq_map_ref%slenr   = 0.5_dp*(   slen(cell1) +   slen(cell2) )

    flsq_map_ref%slenxnr = 0.5_dp*( slenxn(cell1) + slenxn(cell2) )
    flsq_map_ref%slenynr = 0.5_dp*( slenyn(cell1) + slenyn(cell2) )
    flsq_map_ref%slenznr = 0.5_dp*( slenzn(cell1) + slenzn(cell2) )

    flsq_map_ref%tr = mapping_system( tx, ty, tz )

    if ( flsq_map_ref%mapr == 2 ) then

      flsq_map_ref%xr = 0.5_dp*( xc(cell1) + xc(cell2) )
      flsq_map_ref%yr = 0.5_dp*( yc(cell1) + yc(cell2) )
      flsq_map_ref%zr = 0.5_dp*( zc(cell1) + zc(cell2) )

      slenx0 = 0.5_dp*( xc(cell1) - slen(cell1)*slenxn(cell1) &
                      + xc(cell2) - slen(cell2)*slenxn(cell2) )
      sleny0 = 0.5_dp*( yc(cell1) - slen(cell1)*slenyn(cell1) &
                      + yc(cell2) - slen(cell2)*slenyn(cell2) )
      slenz0 = 0.5_dp*( zc(cell1) - slen(cell1)*slenzn(cell1) &
                      + zc(cell2) - slen(cell2)*slenzn(cell2) )

      !...txr, tyr, tzr is unit vector along distance normal direction.

      txr  = ( flsq_map_ref%xr - slenx0 )/flsq_map_ref%slenr
      tyr  = ( flsq_map_ref%yr - sleny0 )/flsq_map_ref%slenr
      tzr  = ( flsq_map_ref%zr - slenz0 )/flsq_map_ref%slenr

      term = 1._dp/sqrt( txr**2 + tyr**2 + tzr**2 )

      txr = txr*term
      tyr = tyr*term
      tzr = tzr*term

      flsq_map_ref%xr = slenx0 + txr*flsq_map_ref%slenr
      flsq_map_ref%yr = sleny0 + tyr*flsq_map_ref%slenr
      flsq_map_ref%zr = slenz0 + tzr*flsq_map_ref%slenr

      !...Set coordinate transformation.
      flsq_map_ref%tr = mapping_system( txr, tyr, tzr)

      flsq_map_ref%slenxnr = txr
      flsq_map_ref%slenynr = tyr
      flsq_map_ref%slenznr = tzr

    else if ( flsq_map_ref%mapr == 3 ) then

!     Forced mapped least squares

      flsq_map_ref%yr = 0.5_dp*( yc(cell1) + yc(cell2) )
      radiusr = sqrt( xc(cell1)**2 + zc(cell1)**2 ) &
              + sqrt( xc(cell2)**2 + zc(cell2)**2 )
      thetar  = atan2( real(zc(cell1),dp),real(xc(cell1),dp) ) &
              + atan2( real(zc(cell2),dp),real(xc(cell2),dp) )
      radiusr = 0.5_dp*radiusr
      thetar  = 0.5_dp*thetar
      flsq_map_ref%xr = radiusr*cos(thetar)
      flsq_map_ref%zr = radiusr*sin(thetar)
      flsq_map_ref%slenxnr = cos(thetar)
      flsq_map_ref%slenznr = sin(thetar)
      flsq_map_ref%slenynr = 0._dp

    end if

  end function flsq_map_ref

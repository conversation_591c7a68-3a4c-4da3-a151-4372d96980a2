module dshape_laminar

  implicit none

  private

  public :: drdgeomv_mix
  public :: bc_viscous_wall, bc_drdgeomv_mix, bc_viscous_walli
  public :: bc_viscous_wall_rates
  public :: drdgeomv_mix_i, bc_drdgeomv_mix_i

contains

!================================= BC_VISCOUS_WALL ===========================80
!
! For moving grids, pick up the linearization of the residual on viscous walls
!         [X          ]
!         [u-uwall    ]         uwall=bdxdt(i)
! Rwall = [v-vwall    ]   where vwall=bdydt(i)
!         [w-wwall    ]         wwall=bdzdt(i)
!         [E/rho-ewall]         ewall=twall/ggm1 + my_half*(uwall**2 + vwall**2
!                                                                    + wwall**2)
!
!=============================================================================80
  subroutine bc_viscous_wall(ntp,nnodes0,nbound,nfunctions,bc,rlam,adim,       &
                             nnodes01,rn,np,pointsum,drdxl,qnode,ndim)

    use info_depr,            only : physical_timestep
    use nml_nonlinear_solves, only : ibdf2opt, itime, dt
    use grid_motion_helpers,  only : need_grid_velocity
    use fun3d_constants,      only : my_half, my_1, my_2, my_1p5, my_3, my_6,  &
                                     my_11, my_0
    use kinddefs,             only : dp
    use bc_names,             only : bc_strong_viscous_adjoint
    use bc_types,             only : bcgrid_type
    use noninertials,         only : xrotrate_ni,yrotrate_ni,zrotrate_ni
    use nml_noninertial_reference_frame, only : noninertial

    integer, intent(in) :: ntp, nnodes0, nbound, nfunctions, adim, nnodes01
    integer, intent(in) :: rn, np, ndim

    real(dp), dimension(adim,nnodes01,nfunctions), intent(in)    :: rlam
    real(dp), dimension(6,3),                      intent(inout) :: pointsum
    real(dp), dimension(3,nnodes01,nfunctions,ntp),intent(inout) :: drdxl
    real(dp), dimension(ndim,nnodes01),            intent(in)    :: qnode

    type(bcgrid_type), dimension(nbound), intent(in) :: bc

    integer :: ib, inode, k, i, j

    real(dp) :: rho, uwall, vwall, wwall, rlam2, rlam3, rlam4, rlam5

    real(dp), dimension(ntp) :: uwallx,uwally,uwallz
    real(dp), dimension(ntp) :: vwallx,vwally,vwallz
    real(dp), dimension(ntp) :: wwallx,wwally,wwallz
    real(dp), dimension(ntp) :: res2x,res2y,res2z
    real(dp), dimension(ntp) :: res3x,res3y,res3z
    real(dp), dimension(ntp) :: res4x,res4y,res4z
    real(dp), dimension(ntp) :: res5x,res5y,res5z
    real(dp), dimension(ntp) :: time_coeff

    logical, dimension(nnodes0) :: moving_wall_piece_added

  continue

    dynamic_grid : if ( need_grid_velocity ) then

! Set the time-derivative coefficient that goes with the node speed terms

      if ( physical_timestep == 1 .or. itime == 1 ) then       !BDF1
        time_coeff(1) =  my_1/dt
        time_coeff(2) = -my_1/dt
      else if ( physical_timestep == 2 .or. itime == 2 ) then  !BDF2
        time_coeff(1) =  my_1p5/dt
        time_coeff(2) = -my_2/dt
        time_coeff(3) =  my_half/dt
      else if ( itime == 3 ) then
        if ( ibdf2opt == 0 ) then                              !BDF3
          time_coeff(1) =  my_11/my_6/dt
          time_coeff(2) = -my_3/dt
          time_coeff(3) =  my_1p5/dt
          time_coeff(4) = -my_1/my_3/dt
        else                                                   !BDF2opt
          time_coeff(1) =  (0.48_dp*my_11/my_6 + 0.52_dp*my_1p5)/dt
          time_coeff(2) = (-0.48_dp*my_3       - 0.52_dp*my_2)/dt
          time_coeff(3) =  (0.48_dp*my_1p5     + 0.52_dp*my_half)/dt
          time_coeff(4) = (-0.48_dp*my_2/my_6)/dt
        endif
      endif

      moving_wall_piece_added = .false.

      boundary_loop : do ib = 1, nbound
        strong_bc : if ( bc_strong_viscous_adjoint(bc(ib)%ibc) ) then
          node_loop : do i = 1, bc(ib)%nbnode
            inode = bc(ib)%ibnode(i)
            local_node : if ( inode <= nnodes0 ) then
              add_contrib : if ( .not. moving_wall_piece_added(inode) ) then

                rho   = qnode(1,inode)
                uwall = bc(ib)%bdxdt(i)
                vwall = bc(ib)%bdydt(i)
                wwall = bc(ib)%bdzdt(i)

                do k = 1, ntp
                  if ( noninertial ) then
                    uwallx(k) = my_0
                    uwally(k) = -zrotrate_ni
                    uwallz(k) =  yrotrate_ni

                    vwallx(k) =  zrotrate_ni
                    vwally(k) = my_0
                    vwallz(k) = -xrotrate_ni

                    wwallx(k) = -yrotrate_ni
                    wwally(k) =  xrotrate_ni
                    wwallz(k) = my_0
                  else
                    uwallx(k) = time_coeff(k)
                    uwally(k) = my_0
                    uwallz(k) = my_0

                    vwallx(k) = my_0
                    vwally(k) = time_coeff(k)
                    vwallz(k) = my_0

                    wwallx(k) = my_0
                    wwally(k) = my_0
                    wwallz(k) = time_coeff(k)
                  endif

                  res2x(k) = -rho*uwallx(k)
                  res2y(k) = -rho*uwally(k)
                  res2z(k) = -rho*uwallz(k)

                  res3x(k) = -rho*vwallx(k)
                  res3y(k) = -rho*vwally(k)
                  res3z(k) = -rho*vwallz(k)

                  res4x(k) = -rho*wwallx(k)
                  res4y(k) = -rho*wwally(k)
                  res4z(k) = -rho*wwallz(k)

                  res5x(k) = -rho*(uwall*uwallx(k) + vwall*vwallx(k) +         &
                                   wwall*wwallx(k))
                  res5y(k) = -rho*(uwall*uwally(k) + vwall*vwally(k) +         &
                                   wwall*wwally(k))
                  res5z(k) = -rho*(uwall*uwallz(k) + vwall*vwallz(k) +         &
                                   wwall*wwallz(k))
                end do

                fcn_loop : do j = 1, nfunctions
                  rlam2 = rlam(2,inode,j)
                  rlam3 = rlam(3,inode,j)
                  rlam4 = rlam(4,inode,j)
                  rlam5 = rlam(5,inode,j)

                  if (inode == rn .and. inode == np) then
                    pointsum(2,1) =pointsum(2,1) + res2x(1)
                    pointsum(3,1) =pointsum(3,1) + res3x(1)
                    pointsum(4,1) =pointsum(4,1) + res4x(1)
                    pointsum(5,1) =pointsum(5,1) + res5x(1)

                    pointsum(2,2) =pointsum(2,2) + res2y(1)
                    pointsum(3,2) =pointsum(3,2) + res3y(1)
                    pointsum(4,2) =pointsum(4,2) + res4y(1)
                    pointsum(5,2) =pointsum(5,2) + res5y(1)

                    pointsum(2,3) =pointsum(2,3) + res2z(1)
                    pointsum(3,3) =pointsum(3,3) + res3z(1)
                    pointsum(4,3) =pointsum(4,3) + res4z(1)
                    pointsum(5,3) =pointsum(5,3) + res5z(1)
                  endif

                  do k = 1, ntp
                    drdxl(1,inode,j,k)=drdxl(1,inode,j,k)                      &
                                            + res2x(k)*rlam2 + res3x(k)*rlam3  &
                                            + res4x(k)*rlam4 + res5x(k)*rlam5
                    drdxl(2,inode,j,k)=drdxl(2,inode,j,k)                      &
                                            + res2y(k)*rlam2 + res3y(k)*rlam3  &
                                            + res4y(k)*rlam4 + res5y(k)*rlam5
                    drdxl(3,inode,j,k)=drdxl(3,inode,j,k)                      &
                                            + res2z(k)*rlam2 + res3z(k)*rlam3  &
                                            + res4z(k)*rlam4 + res5z(k)*rlam5
                  end do
                end do fcn_loop

                moving_wall_piece_added(inode) = .true.

              endif add_contrib
            endif local_node
          end do node_loop
        endif strong_bc
      end do boundary_loop
    endif dynamic_grid

  end subroutine bc_viscous_wall


!================================= BC_VISCOUS_WALLI ==========================80
!
! For moving grids, pick up the linearization of the residual on viscous walls
!         [X          ]
!         [u-uwall    ]         uwall=bdxdt(i)
! Rwall = [v-vwall    ]   where vwall=bdydt(i)
!         [w-wwall    ]         wwall=bdzdt(i)
!         [E/rho-ewall]         ewall=twall/ggm1 + my_half*(uwall**2 + vwall**2
!                                                                    + wwall**2)
!
!=============================================================================80
  subroutine bc_viscous_walli(ntp,nnodes0,nbound,nfunctions,bc,rlam,adim,      &
                              nnodes01,rn,np,pointsum,drdxl)

    use info_depr,            only : physical_timestep
    use nml_nonlinear_solves, only : ibdf2opt, itime, dt
    use grid_motion_helpers,  only : need_grid_velocity
    use fun3d_constants,      only : my_half, my_1, my_2, my_1p5, my_3, my_6,  &
                                     my_11, my_0
    use kinddefs,             only : dp
    use bc_names,             only : bc_strong_viscous_adjoint
    use bc_types,             only : bcgrid_type
    use noninertials,         only : xrotrate_ni,yrotrate_ni,zrotrate_ni
    use nml_noninertial_reference_frame, only : noninertial

    integer, intent(in) :: ntp, nnodes0, nbound, nfunctions, adim, nnodes01
    integer, intent(in) :: rn, np

    real(dp), dimension(adim,nnodes01,nfunctions), intent(in)    :: rlam
    real(dp), dimension(6,3),                      intent(inout) :: pointsum
    real(dp), dimension(3,nnodes01,nfunctions,ntp),intent(inout) :: drdxl

    type(bcgrid_type), dimension(nbound), intent(in) :: bc

    integer :: ib, inode, k, i, j

    real(dp) :: rlam2, rlam3, rlam4

    real(dp), dimension(ntp) :: uwallx,uwally,uwallz
    real(dp), dimension(ntp) :: vwallx,vwally,vwallz
    real(dp), dimension(ntp) :: wwallx,wwally,wwallz
    real(dp), dimension(ntp) :: res2x,res2y,res2z
    real(dp), dimension(ntp) :: res3x,res3y,res3z
    real(dp), dimension(ntp) :: res4x,res4y,res4z
    real(dp), dimension(ntp) :: time_coeff

    logical, dimension(nnodes0) :: moving_wall_piece_added

  continue

    dynamic_grid : if ( need_grid_velocity ) then

! Set the time-derivative coefficient that goes with the node speed terms

      if ( physical_timestep == 1 .or. itime == 1 ) then       !BDF1
        time_coeff(1) =  my_1/dt
        time_coeff(2) = -my_1/dt
      else if ( physical_timestep == 2 .or. itime == 2 ) then  !BDF2
        time_coeff(1) =  my_1p5/dt
        time_coeff(2) = -my_2/dt
        time_coeff(3) =  my_half/dt
      else if ( itime == 3 ) then
        if ( ibdf2opt == 0 ) then                              !BDF3
          time_coeff(1) =  my_11/my_6/dt
          time_coeff(2) = -my_3/dt
          time_coeff(3) =  my_1p5/dt
          time_coeff(4) = -my_1/my_3/dt
        else                                                   !BDF2opt
          time_coeff(1) =  (0.48_dp*my_11/my_6 + 0.52_dp*my_1p5)/dt
          time_coeff(2) = (-0.48_dp*my_3       - 0.52_dp*my_2)/dt
          time_coeff(3) =  (0.48_dp*my_1p5     + 0.52_dp*my_half)/dt
          time_coeff(4) = (-0.48_dp*my_2/my_6)/dt
        endif
      endif

      moving_wall_piece_added = .false.

      boundary_loop : do ib = 1, nbound
        strong_bc : if ( bc_strong_viscous_adjoint(bc(ib)%ibc) ) then
          node_loop : do i = 1, bc(ib)%nbnode
            inode = bc(ib)%ibnode(i)
            local_node : if ( inode <= nnodes0 ) then
              add_contrib : if ( .not. moving_wall_piece_added(inode) ) then

                do k = 1, ntp
                  if ( noninertial ) then
                    uwallx(k) = my_0
                    uwally(k) = -zrotrate_ni
                    uwallz(k) =  yrotrate_ni

                    vwallx(k) =  zrotrate_ni
                    vwally(k) = my_0
                    vwallz(k) = -xrotrate_ni

                    wwallx(k) = -yrotrate_ni
                    wwally(k) =  xrotrate_ni
                    wwallz(k) = my_0
                  else
                    uwallx(k) = time_coeff(k)
                    uwally(k) = my_0
                    uwallz(k) = my_0

                    vwallx(k) = my_0
                    vwally(k) = time_coeff(k)
                    vwallz(k) = my_0

                    wwallx(k) = my_0
                    wwally(k) = my_0
                    wwallz(k) = time_coeff(k)
                  endif

                  res2x(k) = -uwallx(k)
                  res2y(k) = -uwally(k)
                  res2z(k) = -uwallz(k)

                  res3x(k) = -vwallx(k)
                  res3y(k) = -vwally(k)
                  res3z(k) = -vwallz(k)

                  res4x(k) = -wwallx(k)
                  res4y(k) = -wwally(k)
                  res4z(k) = -wwallz(k)
                end do

                fcn_loop : do j = 1, nfunctions

                  rlam2 = rlam(2,inode,j)
                  rlam3 = rlam(3,inode,j)
                  rlam4 = rlam(4,inode,j)

                  if (inode == rn .and. inode == np) then
                    pointsum(2,1) =pointsum(2,1) + res2x(1)
                    pointsum(3,1) =pointsum(3,1) + res3x(1)
                    pointsum(4,1) =pointsum(4,1) + res4x(1)

                    pointsum(2,2) =pointsum(2,2) + res2y(1)
                    pointsum(3,2) =pointsum(3,2) + res3y(1)
                    pointsum(4,2) =pointsum(4,2) + res4y(1)

                    pointsum(2,3) =pointsum(2,3) + res2z(1)
                    pointsum(3,3) =pointsum(3,3) + res3z(1)
                    pointsum(4,3) =pointsum(4,3) + res4z(1)
                  endif

                  do k = 1, ntp
                    drdxl(1,inode,j,k)=drdxl(1,inode,j,k)                      &
                                            + res2x(k)*rlam2 + res3x(k)*rlam3  &
                                            + res4x(k)*rlam4
                    drdxl(2,inode,j,k)=drdxl(2,inode,j,k)                      &
                                            + res2y(k)*rlam2 + res3y(k)*rlam3  &
                                            + res4y(k)*rlam4
                    drdxl(3,inode,j,k)=drdxl(3,inode,j,k)                      &
                                            + res2z(k)*rlam2 + res3z(k)*rlam3  &
                                            + res4z(k)*rlam4
                  end do
                end do fcn_loop

                moving_wall_piece_added(inode) = .true.

              endif add_contrib
            endif local_node
          end do node_loop
        endif strong_bc
      end do boundary_loop
    endif dynamic_grid

  end subroutine bc_viscous_walli


!====================================== DRDGEOMV_MIX =========================80
!
! This routine computes the full viscous fluxes for general (mixed) elements
! using a cell-based formulation to compute gradients. It is the generalization
! of the original tetrahedral formulation to more general elements.
!
! For non-tetrahedral cells, the cell-based gradients can be augmented with
! edge gradients to enhance h-ellipticity.
!
! Note: qnode assumed to contain primitive variables
!
! Optimized version of visrhs_mix_cell but does not contain all of the bells
! and whistles of that routine
!
!=============================================================================80

  subroutine drdgeomv_mix(nnodes0, nnodes01, ncell, c2n, x, y, z, qnode, amut, &
                          local_f2n, local_e2n, chk_norm, face_per_cell,       &
                          node_per_cell, edge_per_cell, n_tot, ndim, adim,     &
                          nfunctions, ntp, rlam, drdxl, coltag, type_cell,     &
                          pointsum, rn, np)

    use info_depr,       only : tref, xmach, re, ivgrd
    use fluid,           only : gamma, gm1, sutherland_constant, prandtl
    use turb_parameters, only : turbulent_prandtl
    use element_defs,    only : max_node_per_cell, max_face_per_cell
    use generic_gas_map, only : n_momx, n_momy, n_momz, n_etot
    use kinddefs,        only : dp
    use debug_defs,      only : gradient_construction_rhs
    use lmpi,            only : lmpi_die

    integer, intent(in) :: n_tot, rn, np
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: ndim, adim, nfunctions, ntp

    integer, dimension(node_per_cell,ncell),         intent(in) :: c2n
    integer, dimension(face_per_cell,4),             intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),             intent(in) :: local_e2n
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp), dimension(nnodes01),       intent(in) :: x,y,z
    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode
    real(dp), dimension(nnodes01),       intent(in) :: amut
    real(dp), dimension(adim,nnodes01),  intent(in) :: coltag
    real(dp), dimension(3,nnodes01,nfunctions,ntp), intent(inout) :: drdxl
    real(dp), dimension(adim,nnodes01,nfunctions),  intent(in)    :: rlam
    real(dp), dimension(6,3),                       intent(inout) :: pointsum

    character(len=*), intent(in) :: type_cell

    integer :: n, j, iface, nn1, nn2, nn3, nn4
    integer :: ie, i, eqn
    integer :: n1_loc, n2_loc, node, n3_loc, n4_loc, n5_loc, n6_loc
    integer :: n1, n2, big_angle
    integer :: eqn0, eqn1, k

    real(dp) :: c23, c43, cgp, cgpt, rmucgp, xmr
    real(dp) :: cstar, dot, xavg, yavg, zavg, termx, termy, termz
    real(dp) :: rmu, umu, vmu, wmu, qavg, xavg1, yavg1, zavg1
    real(dp) :: ux, uy, uz, vx, vy, vz, wx, wy, wz, tx, ty, tz
    real(dp) :: uxavg, uyavg, uzavg, vxavg, vyavg, vzavg
    real(dp) :: wxavg, wyavg, wzavg, txavg, tyavg, tzavg
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz, qavg1, qavg2, xavg2, yavg2, zavg2
    real(dp) :: xc, yc, zc, cell_vol, fact, term1, term2
    real(dp) :: ex, ey, ez, disi, nx, ny, nz, nx1, ny1, nz1, nx2, ny2, nz2
    real(dp) :: termx1, termy1, termz1, ex_new, ey_new, ez_new
    real(dp) :: u1, u2, v1, v2, w1, w2, t1, t2, termx2, termy2, termz2
    real(dp) :: egradu, egradv, egradw, egradt, cell_vol_inv
    real(dp) :: gradu_xi, gradv_xi, gradw_xi, gradt_xi
    real(dp) :: txx, txy, txz, tyy, tyz, tzz, rax, ray, raz
    real(dp) :: tqx, tqy, tqz, stuff, areai, xnf, ynf, znf, e_dot_n
    real(dp) :: augment_weight1, augment_weight2, augment_weight3
    real(dp) :: augment_weight1_new, augment_weight2_new, augment_weight3_new

    real(dp), dimension(max_face_per_cell)      :: nxf, nyf, nzf
    real(dp), dimension(max_node_per_cell)      :: mu_node
    real(dp), dimension(max_node_per_cell)      :: x_node, y_node, z_node
    real(dp), dimension(ndim,max_node_per_cell) :: q_node
    real(dp), dimension(ndim)                   :: gradx_cell, grady_cell
    real(dp), dimension(ndim)                   :: gradz_cell
    real(dp), dimension(ndim)                   :: gradx_cell_new,grady_cell_new
    real(dp), dimension(ndim)                   :: gradz_cell_new

    real(dp), dimension(max_node_per_cell) :: xcdx, ycdy, zcdz
    real(dp), dimension(max_node_per_cell) :: nxdx, nxdy, nxdz
    real(dp), dimension(max_node_per_cell) :: nydx, nydy, nydz
    real(dp), dimension(max_node_per_cell) :: nzdx, nzdy, nzdz
    real(dp), dimension(max_node_per_cell) :: nx1dx, nx1dy, nx1dz
    real(dp), dimension(max_node_per_cell) :: ny1dx, ny1dy, ny1dz
    real(dp), dimension(max_node_per_cell) :: nz1dx, nz1dy, nz1dz
    real(dp), dimension(max_node_per_cell) :: nx2dx, nx2dy, nx2dz
    real(dp), dimension(max_node_per_cell) :: ny2dx, ny2dy, ny2dz
    real(dp), dimension(max_node_per_cell) :: nz2dx, nz2dy, nz2dz
    real(dp), dimension(max_node_per_cell) :: xavgdx, yavgdy, zavgdz
    real(dp), dimension(max_node_per_cell) :: xavg1dx, yavg1dy, zavg1dz
    real(dp), dimension(max_node_per_cell) :: xavg2dx, yavg2dy, zavg2dz
    real(dp), dimension(max_node_per_cell) :: cell_voldx, cell_voldy, cell_voldz
    real(dp), dimension(max_node_per_cell) :: termxdx, termxdy, termxdz
    real(dp), dimension(max_node_per_cell) :: termydx, termydy, termydz
    real(dp), dimension(max_node_per_cell) :: termzdx, termzdy, termzdz
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdz
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdz
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdz
    real(dp), dimension(max_node_per_cell) :: term1dx, term1dy, term1dz
    real(dp), dimension(max_node_per_cell) :: term2dx, term2dy, term2dz
    real(dp), dimension(max_node_per_cell) :: termx1dx, termx1dy, termx1dz
    real(dp), dimension(max_node_per_cell) :: termy1dx, termy1dy, termy1dz
    real(dp), dimension(max_node_per_cell) :: termz1dx, termz1dy, termz1dz
    real(dp), dimension(max_node_per_cell) :: termx2dx, termx2dy, termx2dz
    real(dp), dimension(max_node_per_cell) :: termy2dx, termy2dy, termy2dz
    real(dp), dimension(max_node_per_cell) :: termz2dx, termz2dy, termz2dz
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdx
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdy
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdz
    real(dp), dimension(max_node_per_cell) :: uxavgdx, uxavgdy, uxavgdz
    real(dp), dimension(max_node_per_cell) :: uyavgdx, uyavgdy, uyavgdz
    real(dp), dimension(max_node_per_cell) :: uzavgdx, uzavgdy, uzavgdz
    real(dp), dimension(max_node_per_cell) :: vxavgdx, vxavgdy, vxavgdz
    real(dp), dimension(max_node_per_cell) :: vyavgdx, vyavgdy, vyavgdz
    real(dp), dimension(max_node_per_cell) :: vzavgdx, vzavgdy, vzavgdz
    real(dp), dimension(max_node_per_cell) :: wxavgdx, wxavgdy, wxavgdz
    real(dp), dimension(max_node_per_cell) :: wyavgdx, wyavgdy, wyavgdz
    real(dp), dimension(max_node_per_cell) :: wzavgdx, wzavgdy, wzavgdz
    real(dp), dimension(max_node_per_cell) :: txavgdx, txavgdy, txavgdz
    real(dp), dimension(max_node_per_cell) :: tyavgdx, tyavgdy, tyavgdz
    real(dp), dimension(max_node_per_cell) :: tzavgdx, tzavgdy, tzavgdz
    real(dp), dimension(max_node_per_cell) :: xmdx, ymdy, zmdz
    real(dp), dimension(max_node_per_cell) :: xldx, yldy, zldz
    real(dp), dimension(max_node_per_cell) :: xrdx, yrdy, zrdz
    real(dp), dimension(max_node_per_cell) :: areaxdx, areaxdy, areaxdz
    real(dp), dimension(max_node_per_cell) :: areaydx, areaydy, areaydz
    real(dp), dimension(max_node_per_cell) :: areazdx, areazdy, areazdz
    real(dp), dimension(max_node_per_cell) :: exdx
    real(dp), dimension(max_node_per_cell) :: eydy
    real(dp), dimension(max_node_per_cell) :: ezdz
    real(dp), dimension(max_node_per_cell) :: ex_newdx, ex_newdy, ex_newdz
    real(dp), dimension(max_node_per_cell) :: ey_newdx, ey_newdy, ey_newdz
    real(dp), dimension(max_node_per_cell) :: ez_newdx, ez_newdy, ez_newdz
    real(dp), dimension(max_node_per_cell) :: areaidx, areaidy, areaidz
    real(dp), dimension(max_node_per_cell) :: xnfdx, xnfdy, xnfdz
    real(dp), dimension(max_node_per_cell) :: ynfdx, ynfdy, ynfdz
    real(dp), dimension(max_node_per_cell) :: znfdx, znfdy, znfdz
    real(dp), dimension(max_node_per_cell) :: e_dot_ndx, e_dot_ndy, e_dot_ndz
    real(dp), dimension(max_node_per_cell) :: augment_weight1dx
    real(dp), dimension(max_node_per_cell) :: augment_weight1dy
    real(dp), dimension(max_node_per_cell) :: augment_weight1dz
    real(dp), dimension(max_node_per_cell) :: augment_weight2dx
    real(dp), dimension(max_node_per_cell) :: augment_weight2dy
    real(dp), dimension(max_node_per_cell) :: augment_weight2dz
    real(dp), dimension(max_node_per_cell) :: augment_weight3dx
    real(dp), dimension(max_node_per_cell) :: augment_weight3dy
    real(dp), dimension(max_node_per_cell) :: augment_weight3dz
    real(dp), dimension(max_node_per_cell) :: augment_weight1_newdx
    real(dp), dimension(max_node_per_cell) :: augment_weight1_newdy
    real(dp), dimension(max_node_per_cell) :: augment_weight1_newdz
    real(dp), dimension(max_node_per_cell) :: augment_weight2_newdx
    real(dp), dimension(max_node_per_cell) :: augment_weight2_newdy
    real(dp), dimension(max_node_per_cell) :: augment_weight2_newdz
    real(dp), dimension(max_node_per_cell) :: augment_weight3_newdx
    real(dp), dimension(max_node_per_cell) :: augment_weight3_newdy
    real(dp), dimension(max_node_per_cell) :: augment_weight3_newdz
    real(dp), dimension(max_node_per_cell) :: disidx, disidy, disidz
    real(dp), dimension(max_node_per_cell) :: egradudx, egradudy, egradudz
    real(dp), dimension(max_node_per_cell) :: egradvdx, egradvdy, egradvdz
    real(dp), dimension(max_node_per_cell) :: egradwdx, egradwdy, egradwdz
    real(dp), dimension(max_node_per_cell) :: egradtdx, egradtdy, egradtdz
    real(dp), dimension(max_node_per_cell) :: gradu_xidx, gradu_xidy, gradu_xidz
    real(dp), dimension(max_node_per_cell) :: gradv_xidx, gradv_xidy, gradv_xidz
    real(dp), dimension(max_node_per_cell) :: gradw_xidx, gradw_xidy, gradw_xidz
    real(dp), dimension(max_node_per_cell) :: gradt_xidx, gradt_xidy, gradt_xidz
    real(dp), dimension(max_node_per_cell) :: uxdx, uxdy, uxdz
    real(dp), dimension(max_node_per_cell) :: vxdx, vxdy, vxdz
    real(dp), dimension(max_node_per_cell) :: wxdx, wxdy, wxdz
    real(dp), dimension(max_node_per_cell) :: txdx, txdy, txdz
    real(dp), dimension(max_node_per_cell) :: uydx, uydy, uydz
    real(dp), dimension(max_node_per_cell) :: vydx, vydy, vydz
    real(dp), dimension(max_node_per_cell) :: wydx, wydy, wydz
    real(dp), dimension(max_node_per_cell) :: tydx, tydy, tydz
    real(dp), dimension(max_node_per_cell) :: uzdx, uzdy, uzdz
    real(dp), dimension(max_node_per_cell) :: vzdx, vzdy, vzdz
    real(dp), dimension(max_node_per_cell) :: wzdx, wzdy, wzdz
    real(dp), dimension(max_node_per_cell) :: tzdx, tzdy, tzdz
    real(dp), dimension(max_node_per_cell) :: txxdx, txxdy, txxdz
    real(dp), dimension(max_node_per_cell) :: txydx, txydy, txydz
    real(dp), dimension(max_node_per_cell) :: txzdx, txzdy, txzdz
    real(dp), dimension(max_node_per_cell) :: tyydx, tyydy, tyydz
    real(dp), dimension(max_node_per_cell) :: tyzdx, tyzdy, tyzdz
    real(dp), dimension(max_node_per_cell) :: tzzdx, tzzdy, tzzdz
    real(dp), dimension(max_node_per_cell) :: tqxdx, tqxdy, tqxdz
    real(dp), dimension(max_node_per_cell) :: tqydx, tqydy, tqydz
    real(dp), dimension(max_node_per_cell) :: tqzdx, tqzdy, tqzdz
    real(dp), dimension(max_node_per_cell) :: raxdx, raxdy, raxdz
    real(dp), dimension(max_node_per_cell) :: raydx, raydy, raydz
    real(dp), dimension(max_node_per_cell) :: razdx, razdy, razdz
    real(dp), dimension(max_node_per_cell) :: vf2dx, vf2dy, vf2dz
    real(dp), dimension(max_node_per_cell) :: vf3dx, vf3dy, vf3dz
    real(dp), dimension(max_node_per_cell) :: vf4dx, vf4dy, vf4dz
    real(dp), dimension(max_node_per_cell) :: vf5dx, vf5dy, vf5dz
    real(dp), dimension(max_node_per_cell) :: res12dx, res12dy, res12dz
    real(dp), dimension(max_node_per_cell) :: res13dx, res13dy, res13dz
    real(dp), dimension(max_node_per_cell) :: res14dx, res14dy, res14dz
    real(dp), dimension(max_node_per_cell) :: res15dx, res15dy, res15dz
    real(dp), dimension(max_node_per_cell) :: res22dx, res22dy, res22dz
    real(dp), dimension(max_node_per_cell) :: res23dx, res23dy, res23dz
    real(dp), dimension(max_node_per_cell) :: res24dx, res24dy, res24dz
    real(dp), dimension(max_node_per_cell) :: res25dx, res25dy, res25dz
    real(dp), dimension(adim) :: lambda1, lambda2

    real(dp), parameter :: my_0     = 0.0_dp
    real(dp), parameter :: my_half  = 0.50_dp
    real(dp), parameter :: my_mxd  = 0.99939_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_2    = 2.0_dp
    real(dp), parameter :: my_3    = 3.0_dp
    real(dp), parameter :: my_4    = 4.0_dp
    real(dp), parameter :: my_18th = my_1/18.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp
    real(dp), parameter :: my_6th  = 1.0_dp/6.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp

  continue

    augment_weight1 = 0.0_dp
    augment_weight2 = 0.0_dp
    augment_weight3 = 0.0_dp

    c43   = my_4/my_3
    c23   = my_2/my_3
    fact  = 1.0_dp / real(node_per_cell, dp)
    cstar = sutherland_constant/tref
    xmr   = xmach/re
    cgp   = my_1/(gm1*prandtl) ! xmr factor comes in rax, etc
    cgpt  = my_1/(gm1*turbulent_prandtl) ! xmr factor comes in rax, etc
    eqn0  = 2
    eqn1  = 5

! Loop over the cells

    cell_loop: do n = 1, ncell

! Initialization

      rmu = 0.0_dp
      umu = 0.0_dp
      vmu = 0.0_dp
      wmu = 0.0_dp
      rmucgp = 0.0_dp

      xc  = 0.0_dp
      yc  = 0.0_dp
      zc  = 0.0_dp

! Compute cell averages, cell center, and set up some local solution arrays

      node_loop : do i = 1, node_per_cell

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        xc  =  xc + x_node(i)
        yc  =  yc + y_node(i)
        zc  =  zc + z_node(i)

        q_node(1:ndim,i) = qnode(1:ndim,node)
        q_node(n_etot,i) = gamma*qnode(n_etot,node)/qnode(1,node)
        mu_node(i) = viscosity_law( cstar, q_node(n_etot,i) )
        rmucgp     = rmucgp + mu_node(i)*cgp
        mu_node(i) = mu_node(i) + amut(node)
        rmucgp     = rmucgp + amut(node)*cgpt
        umu = umu + q_node(n_momx,i) * mu_node(i)
        vmu = vmu + q_node(n_momy,i) * mu_node(i)
        wmu = wmu + q_node(n_momz,i) * mu_node(i)
        rmu = rmu + mu_node(i)

      end do node_loop

!     get cell averages by dividing by the number of nodes that contributed

      rmu    = rmu*fact
      umu    = umu*fact/rmu
      vmu    = vmu*fact/rmu
      wmu    = wmu*fact/rmu
      rmucgp = rmucgp*fact
      xc     =  xc*fact
      yc     =  yc*fact
      zc     =  zc*fact

      xcdx(:) = fact
      ycdy(:) = fact
      zcdz(:) = fact

! Get the gradients in the primal cell via Green-Gauss

      cell_vol = 0.0_dp
        cell_voldx = 0.0_dp
        cell_voldy = 0.0_dp
        cell_voldz = 0.0_dp

      gradx_cell(:) = my_0
        gradx_celldx(:,:) = my_0
        gradx_celldy(:,:) = my_0
        gradx_celldz(:,:) = my_0
      grady_cell(:) = my_0
        grady_celldx(:,:) = my_0
        grady_celldy(:,:) = my_0
        grady_celldz(:,:) = my_0
      gradz_cell(:) = my_0
        gradz_celldx(:,:) = my_0
        gradz_celldy(:,:) = my_0
        gradz_celldz(:,:) = my_0

      threed_faces : do iface = 1, face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        nxdx = 0.0_dp
        nxdy = 0.0_dp
        nxdz = 0.0_dp
        nx1dx = 0.0_dp
        nx1dy = 0.0_dp
        nx1dz = 0.0_dp
        nx2dx = 0.0_dp
        nx2dy = 0.0_dp
        nx2dz = 0.0_dp

        nydx = 0.0_dp
        nydy = 0.0_dp
        nydz = 0.0_dp
        ny1dx = 0.0_dp
        ny1dy = 0.0_dp
        ny1dz = 0.0_dp
        ny2dx = 0.0_dp
        ny2dy = 0.0_dp
        ny2dz = 0.0_dp

        nzdx = 0.0_dp
        nzdy = 0.0_dp
        nzdz = 0.0_dp
        nz1dx = 0.0_dp
        nz1dy = 0.0_dp
        nz1dz = 0.0_dp
        nz2dx = 0.0_dp
        nz2dy = 0.0_dp
        nz2dz = 0.0_dp

        if (nn4 == nn1) then

!         triangular faces of the cell

!         face normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))         &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

            nxdy(nn1)=-(z_node(nn3) - z_node(nn1)) + (z_node(nn2) - z_node(nn1))
            nxdy(nn2)=  z_node(nn3) - z_node(nn1)
            nxdy(nn3)=-(z_node(nn2) - z_node(nn1))

            nxdz(nn1)=-(y_node(nn2) - y_node(nn1)) + (y_node(nn3) - y_node(nn1))
            nxdz(nn2)=-(y_node(nn3) - y_node(nn1))
            nxdz(nn3)= (y_node(nn2) - y_node(nn1))

          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))         &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

            nydx(nn1)=-(z_node(nn2) - z_node(nn1)) + (z_node(nn3) - z_node(nn1))
            nydx(nn2)=-(z_node(nn3) - z_node(nn1))
            nydx(nn3)= (z_node(nn2) - z_node(nn1))

            nydz(nn1)=-(x_node(nn3) - x_node(nn1)) + (x_node(nn2) - x_node(nn1))
            nydz(nn2)= (x_node(nn3) - x_node(nn1))
            nydz(nn3)=-(x_node(nn2) - x_node(nn1))

          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))         &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

            nzdx(nn1)=-(y_node(nn3) - y_node(nn1)) + (y_node(nn2) - y_node(nn1))
            nzdx(nn2)= (y_node(nn3) - y_node(nn1))
            nzdx(nn3)=-(y_node(nn2) - y_node(nn1))

            nzdy(nn1)=-(x_node(nn2) - x_node(nn1)) + (x_node(nn3) - x_node(nn1))
            nzdy(nn2)=-(x_node(nn3) - x_node(nn1))
            nzdy(nn3)= (x_node(nn2) - x_node(nn1))

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavgdx = 0.0_dp
          yavgdy = 0.0_dp
          zavgdz = 0.0_dp

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)

            xavgdx(nn1) = 1.0_dp
            xavgdx(nn2) = 1.0_dp
            xavgdx(nn3) = 1.0_dp

          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
            yavgdy(nn1) = 1.0_dp
            yavgdy(nn2) = 1.0_dp
            yavgdy(nn3) = 1.0_dp

          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)
            zavgdz(nn1) = 1.0_dp
            zavgdz(nn2) = 1.0_dp
            zavgdz(nn3) = 1.0_dp

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th
            cell_voldx(:) = cell_voldx(:) + (xavg*nxdx(:) + yavg*nydx(:)       &
                          + zavg*nzdx(:) + xavgdx(:)*nx)*my_18th
            cell_voldy(:) = cell_voldy(:) + (xavg*nxdy(:) + yavg*nydy(:)       &
                          + zavg*nzdy(:) + yavgdy(:)*ny)*my_18th
            cell_voldz(:) = cell_voldz(:) + (xavg*nxdz(:) + yavg*nydz(:)       &
                          + zavg*nzdz(:) + zavgdz(:)*nz)*my_18th

          termx = nx*my_6th
            termxdx(:) = nxdx(:)*my_6th
            termxdy(:) = nxdy(:)*my_6th
            termxdz(:) = nxdz(:)*my_6th
          termy = ny*my_6th
            termydx(:) = nydx(:)*my_6th
            termydy(:) = nydy(:)*my_6th
            termydz(:) = nydz(:)*my_6th
          termz = nz*my_6th
            termzdx(:) = nzdx(:)*my_6th
            termzdy(:) = nzdy(:)*my_6th
            termzdz(:) = nzdz(:)*my_6th

!         gradient contributions

          do eqn = eqn0, eqn1
            qavg = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
            gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
              gradx_celldx(eqn,:) = gradx_celldx(eqn,:) + termxdx(:)*qavg
              gradx_celldy(eqn,:) = gradx_celldy(eqn,:) + termxdy(:)*qavg
              gradx_celldz(eqn,:) = gradx_celldz(eqn,:) + termxdz(:)*qavg
            grady_cell(eqn) = grady_cell(eqn) + termy*qavg
              grady_celldx(eqn,:) = grady_celldx(eqn,:) + termydx(:)*qavg
              grady_celldy(eqn,:) = grady_celldy(eqn,:) + termydy(:)*qavg
              grady_celldz(eqn,:) = grady_celldz(eqn,:) + termydz(:)*qavg
            gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg
              gradz_celldx(eqn,:) = gradz_celldx(eqn,:) + termzdx(:)*qavg
              gradz_celldy(eqn,:) = gradz_celldy(eqn,:) + termzdy(:)*qavg
              gradz_celldz(eqn,:) = gradz_celldz(eqn,:) + termzdz(:)*qavg
          end do

        else

!         quadrilateral faces of the cell

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg1dx = 0.0_dp
          yavg1dy = 0.0_dp
          zavg1dz = 0.0_dp

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
            xavg1dx(nn1) = 1.0_dp
            xavg1dx(nn2) = 1.0_dp
            xavg1dx(nn3) = 1.0_dp
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
            yavg1dy(nn1) = 1.0_dp
            yavg1dy(nn2) = 1.0_dp
            yavg1dy(nn3) = 1.0_dp
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)
            zavg1dz(nn1) = 1.0_dp
            zavg1dz(nn2) = 1.0_dp
            zavg1dz(nn3) = 1.0_dp

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))        &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

            nx1dy(nn1)=-(z_node(nn3)- z_node(nn1)) + (z_node(nn2) - z_node(nn1))
            nx1dy(nn2)=  z_node(nn3)- z_node(nn1)
            nx1dy(nn3)=-(z_node(nn2)- z_node(nn1))

            nx1dz(nn1)=-(y_node(nn2)- y_node(nn1)) + (y_node(nn3) - y_node(nn1))
            nx1dz(nn2)=-(y_node(nn3)- y_node(nn1))
            nx1dz(nn3)= (y_node(nn2)- y_node(nn1))

          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))        &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

            ny1dx(nn1)=-(z_node(nn2)- z_node(nn1)) + (z_node(nn3) - z_node(nn1))
            ny1dx(nn2)=-(z_node(nn3)- z_node(nn1))
            ny1dx(nn3)= (z_node(nn2)- z_node(nn1))

            ny1dz(nn1)=-(x_node(nn3)- x_node(nn1)) + (x_node(nn2) - x_node(nn1))
            ny1dz(nn2)= (x_node(nn3)- x_node(nn1))
            ny1dz(nn3)=-(x_node(nn2)- x_node(nn1))

          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))        &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

            nz1dx(nn1)=-(y_node(nn3)- y_node(nn1)) + (y_node(nn2) - y_node(nn1))
            nz1dx(nn2)= (y_node(nn3)- y_node(nn1))
            nz1dx(nn3)=-(y_node(nn2)- y_node(nn1))

            nz1dy(nn1)=-(x_node(nn2)- x_node(nn1)) + (x_node(nn3) - x_node(nn1))
            nz1dy(nn2)=-(x_node(nn3)- x_node(nn1))
            nz1dy(nn3)= (x_node(nn2)- x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1
            term1dx(:) = xavg1*nx1dx(:) + yavg1*ny1dx(:) + zavg1*nz1dx(:) +    &
                         xavg1dx(:)*nx1
            term1dy(:) = xavg1*nx1dy(:) + yavg1*ny1dy(:) + zavg1*nz1dy(:) +    &
                         yavg1dy(:)*ny1
            term1dz(:) = xavg1*nx1dz(:) + yavg1*ny1dz(:) + zavg1*nz1dz(:) +    &
                         zavg1dz(:)*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg2dx = 0.0_dp
          yavg2dy = 0.0_dp
          zavg2dz = 0.0_dp

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
            xavg2dx(nn1) = 1.0_dp
            xavg2dx(nn3) = 1.0_dp
            xavg2dx(nn4) = 1.0_dp
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
            yavg2dy(nn1) = 1.0_dp
            yavg2dy(nn3) = 1.0_dp
            yavg2dy(nn4) = 1.0_dp
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)
            zavg2dz(nn1) = 1.0_dp
            zavg2dz(nn3) = 1.0_dp
            zavg2dz(nn4) = 1.0_dp

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))        &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))

            nx2dy(nn1)=-(z_node(nn4)- z_node(nn1)) + (z_node(nn3) - z_node(nn1))
            nx2dy(nn3)=  z_node(nn4)- z_node(nn1)
            nx2dy(nn4)=-(z_node(nn3)- z_node(nn1))

            nx2dz(nn1)=-(y_node(nn3)- y_node(nn1)) + (y_node(nn4) - y_node(nn1))
            nx2dz(nn3)=-(y_node(nn4)- y_node(nn1))
            nx2dz(nn4)= (y_node(nn3)- y_node(nn1))

          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))        &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))

            ny2dx(nn1)=-(z_node(nn3)- z_node(nn1)) + (z_node(nn4) - z_node(nn1))
            ny2dx(nn3)=-(z_node(nn4)- z_node(nn1))
            ny2dx(nn4)= (z_node(nn3)- z_node(nn1))

            ny2dz(nn1)=-(x_node(nn4)- x_node(nn1)) + (x_node(nn3) - x_node(nn1))
            ny2dz(nn3)= (x_node(nn4)- x_node(nn1))
            ny2dz(nn4)=-(x_node(nn3)- x_node(nn1))

          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))        &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

            nz2dx(nn1)=-(y_node(nn4)- y_node(nn1)) + (y_node(nn3) - y_node(nn1))
            nz2dx(nn3)= (y_node(nn4)- y_node(nn1))
            nz2dx(nn4)=-(y_node(nn3)- y_node(nn1))

            nz2dy(nn1)=-(x_node(nn3)- x_node(nn1)) + (x_node(nn4) - x_node(nn1))
            nz2dy(nn3)=-(x_node(nn4)- x_node(nn1))
            nz2dy(nn4)= (x_node(nn3)- x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2
            term2dx(:) = xavg2*nx2dx(:) + yavg2*ny2dx(:) + zavg2*nz2dx(:) +    &
                         xavg2dx(:)*nx2
            term2dy(:) = xavg2*nx2dy(:) + yavg2*ny2dy(:) + zavg2*nz2dy(:) +    &
                         yavg2dy(:)*ny2
            term2dz(:) = xavg2*nx2dz(:) + yavg2*ny2dz(:) + zavg2*nz2dz(:) +    &
                         zavg2dz(:)*nz2

!         cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th
            cell_voldx(:) = cell_voldx(:) + (term1dx(:) + term2dx(:))*my_18th
            cell_voldy(:) = cell_voldy(:) + (term1dy(:) + term2dy(:))*my_18th
            cell_voldz(:) = cell_voldz(:) + (term1dz(:) + term2dz(:))*my_18th

!         gradient contributions

          termx1 = nx1*my_6th
            termx1dx(:) = nx1dx(:)*my_6th
            termx1dy(:) = nx1dy(:)*my_6th
            termx1dz(:) = nx1dz(:)*my_6th
          termy1 = ny1*my_6th
            termy1dx(:) = ny1dx(:)*my_6th
            termy1dy(:) = ny1dy(:)*my_6th
            termy1dz(:) = ny1dz(:)*my_6th
          termz1 = nz1*my_6th
            termz1dx(:) = nz1dx(:)*my_6th
            termz1dy(:) = nz1dy(:)*my_6th
            termz1dz(:) = nz1dz(:)*my_6th

          termx2 = nx2*my_6th
            termx2dx(:) = nx2dx(:)*my_6th
            termx2dy(:) = nx2dy(:)*my_6th
            termx2dz(:) = nx2dz(:)*my_6th
          termy2 = ny2*my_6th
            termy2dx(:) = ny2dx(:)*my_6th
            termy2dy(:) = ny2dy(:)*my_6th
            termy2dz(:) = ny2dz(:)*my_6th
          termz2 = nz2*my_6th
            termz2dx(:) = nz2dx(:)*my_6th
            termz2dy(:) = nz2dy(:)*my_6th
            termz2dz(:) = nz2dz(:)*my_6th

          do eqn = eqn0, eqn1
            qavg1 = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
            qavg2 = q_node(eqn,nn1) + q_node(eqn,nn3) + q_node(eqn,nn4)
            gradx_cell(eqn) = gradx_cell(eqn) + termx1*qavg1 + termx2*qavg2
              gradx_celldx(eqn,:) = gradx_celldx(eqn,:) + termx1dx(:)*qavg1    &
                                  + termx2dx(:)*qavg2
              gradx_celldy(eqn,:) = gradx_celldy(eqn,:) + termx1dy(:)*qavg1    &
                                  + termx2dy(:)*qavg2
              gradx_celldz(eqn,:) = gradx_celldz(eqn,:) + termx1dz(:)*qavg1    &
                                  + termx2dz(:)*qavg2
            grady_cell(eqn) = grady_cell(eqn) + termy1*qavg1 + termy2*qavg2
              grady_celldx(eqn,:) = grady_celldx(eqn,:) + termy1dx(:)*qavg1    &
                                  + termy2dx(:)*qavg2
              grady_celldy(eqn,:) = grady_celldy(eqn,:) + termy1dy(:)*qavg1    &
                                  + termy2dy(:)*qavg2
              grady_celldz(eqn,:) = grady_celldz(eqn,:) + termy1dz(:)*qavg1    &
                                  + termy2dz(:)*qavg2
            gradz_cell(eqn) = gradz_cell(eqn) + termz1*qavg1 + termz2*qavg2
              gradz_celldx(eqn,:) = gradz_celldx(eqn,:) + termz1dx(:)*qavg1    &
                                  + termz2dx(:)*qavg2
              gradz_celldy(eqn,:) = gradz_celldy(eqn,:) + termz1dy(:)*qavg1    &
                                  + termz2dy(:)*qavg2
              gradz_celldz(eqn,:) = gradz_celldz(eqn,:) + termz1dz(:)*qavg1    &
                                  + termz2dz(:)*qavg2
          end do

          nx = nx1 + nx2
            nxdx(:) = nx1dx(:) + nx2dx(:)
            nxdy(:) = nx1dy(:) + nx2dy(:)
            nxdz(:) = nx1dz(:) + nx2dz(:)
          ny = ny1 + ny2
            nydx(:) = ny1dx(:) + ny2dx(:)
            nydy(:) = ny1dy(:) + ny2dy(:)
            nydz(:) = ny1dz(:) + ny2dz(:)
          nz = nz1 + nz2
            nzdx(:) = nz1dx(:) + nz2dx(:)
            nzdy(:) = nz1dy(:) + nz2dy(:)
            nzdz(:) = nz1dz(:) + nz2dz(:)

        end if

        nxf(iface) = nx
        nyf(iface) = ny
        nzf(iface) = nz

      end do threed_faces

!   need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

      cell_vol_inv = my_1/cell_vol
        cell_vol_invdx(:) = -my_1/cell_vol/cell_vol*cell_voldx(:)
        cell_vol_invdy(:) = -my_1/cell_vol/cell_vol*cell_voldy(:)
        cell_vol_invdz(:) = -my_1/cell_vol/cell_vol*cell_voldz(:)

      gradx_cell_new(eqn0:eqn1) = gradx_cell(eqn0:eqn1) * cell_vol_inv
        do k = eqn0, eqn1
          gradx_cell_newdx(k,:) = gradx_cell(k)*cell_vol_invdx(:)              &
                                + cell_vol_inv*gradx_celldx(k,:)
          gradx_cell_newdy(k,:) = gradx_cell(k)*cell_vol_invdy(:)              &
                                + cell_vol_inv*gradx_celldy(k,:)
          gradx_cell_newdz(k,:) = gradx_cell(k)*cell_vol_invdz(:)              &
                                + cell_vol_inv*gradx_celldz(k,:)
        end do
      grady_cell_new(eqn0:eqn1) = grady_cell(eqn0:eqn1) * cell_vol_inv
        do k = eqn0, eqn1
          grady_cell_newdx(k,:) = grady_cell(k)*cell_vol_invdx(:)              &
                                + cell_vol_inv*grady_celldx(k,:)
          grady_cell_newdy(k,:) = grady_cell(k)*cell_vol_invdy(:)              &
                                + cell_vol_inv*grady_celldy(k,:)
          grady_cell_newdz(k,:) = grady_cell(k)*cell_vol_invdz(:)              &
                                + cell_vol_inv*grady_celldz(k,:)
        end do
      gradz_cell_new(eqn0:eqn1) = gradz_cell(eqn0:eqn1) * cell_vol_inv
        do k = eqn0, eqn1
          gradz_cell_newdx(k,:) = gradz_cell(k)*cell_vol_invdx(:)              &
                                + cell_vol_inv*gradz_celldx(k,:)
          gradz_cell_newdy(k,:) = gradz_cell(k)*cell_vol_invdy(:)              &
                                + cell_vol_inv*gradz_celldy(k,:)
          gradz_cell_newdz(k,:) = gradz_cell(k)*cell_vol_invdz(:)              &
                                + cell_vol_inv*gradz_celldz(k,:)
        end do

      uxavg = gradx_cell_new(n_momx)
        uxavgdx(:) = gradx_cell_newdx(n_momx,:)
        uxavgdy(:) = gradx_cell_newdy(n_momx,:)
        uxavgdz(:) = gradx_cell_newdz(n_momx,:)
      vxavg = gradx_cell_new(n_momy)
        vxavgdx(:) = gradx_cell_newdx(n_momy,:)
        vxavgdy(:) = gradx_cell_newdy(n_momy,:)
        vxavgdz(:) = gradx_cell_newdz(n_momy,:)
      wxavg = gradx_cell_new(n_momz)
        wxavgdx(:) = gradx_cell_newdx(n_momz,:)
        wxavgdy(:) = gradx_cell_newdy(n_momz,:)
        wxavgdz(:) = gradx_cell_newdz(n_momz,:)
      txavg = gradx_cell_new(n_etot)
        txavgdx(:) = gradx_cell_newdx(n_etot,:)
        txavgdy(:) = gradx_cell_newdy(n_etot,:)
        txavgdz(:) = gradx_cell_newdz(n_etot,:)

      uyavg = grady_cell_new(n_momx)
        uyavgdx(:) = grady_cell_newdx(n_momx,:)
        uyavgdy(:) = grady_cell_newdy(n_momx,:)
        uyavgdz(:) = grady_cell_newdz(n_momx,:)
      vyavg = grady_cell_new(n_momy)
        vyavgdx(:) = grady_cell_newdx(n_momy,:)
        vyavgdy(:) = grady_cell_newdy(n_momy,:)
        vyavgdz(:) = grady_cell_newdz(n_momy,:)
      wyavg = grady_cell_new(n_momz)
        wyavgdx(:) = grady_cell_newdx(n_momz,:)
        wyavgdy(:) = grady_cell_newdy(n_momz,:)
        wyavgdz(:) = grady_cell_newdz(n_momz,:)
      tyavg = grady_cell_new(n_etot)
        tyavgdx(:) = grady_cell_newdx(n_etot,:)
        tyavgdy(:) = grady_cell_newdy(n_etot,:)
        tyavgdz(:) = grady_cell_newdz(n_etot,:)

      uzavg = gradz_cell_new(n_momx)
        uzavgdx(:) = gradz_cell_newdx(n_momx,:)
        uzavgdy(:) = gradz_cell_newdy(n_momx,:)
        uzavgdz(:) = gradz_cell_newdz(n_momx,:)
      vzavg = gradz_cell_new(n_momy)
        vzavgdx(:) = gradz_cell_newdx(n_momy,:)
        vzavgdy(:) = gradz_cell_newdy(n_momy,:)
        vzavgdz(:) = gradz_cell_newdz(n_momy,:)
      wzavg = gradz_cell_new(n_momz)
        wzavgdx(:) = gradz_cell_newdx(n_momz,:)
        wzavgdy(:) = gradz_cell_newdy(n_momz,:)
        wzavgdz(:) = gradz_cell_newdz(n_momz,:)
      tzavg = gradz_cell_new(n_etot)
        tzavgdx(:) = gradz_cell_newdx(n_etot,:)
        tzavgdy(:) = gradz_cell_newdy(n_etot,:)
        tzavgdz(:) = gradz_cell_newdz(n_etot,:)

! Check cell-face angles (dot product) - if greater than a specified value,
! we skip the contribution from this cell (currently for 3D cases only)

!     note: only need to check upper part of matrix chk_norm since
!     dot products commute (A*B = B*A) and the diagonal indicates
!     the dot product of a face with itself; also note that nx, ny, nz
!     are not unit normals so we must scale the dot product accordingly

      if ( ivgrd == 1 ) then
        big_angle = 0
        do i=1,face_per_cell
          do j=i+1,face_per_cell
            if (chk_norm(i,j) > 0) then
              dot = nxf(i)*nxf(j) + nyf(i)*nyf(j) + nzf(i)*nzf(j)
!             scale to unit normals
              dot = dot / sqrt(nxf(i)*nxf(i) + nyf(i)*nyf(i) + nzf(i)*nzf(i))
              dot = dot / sqrt(nxf(j)*nxf(j) + nyf(j)*nyf(j) + nzf(j)*nzf(j))
              if (dot >= my_mxd) big_angle = big_angle + 1
            end if
          end do
        end do
        if ( big_angle > 0 )  cycle cell_loop
      end if

! Next loop over the edges in the cell and get each ones
! contribution to the residual

      edge_loop : do ie = 1, edge_per_cell

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)
        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

! Get this edges' contribution to the dual normal and area

!       edge midpoint

        xmdx(:) = 0.0_dp
        ymdy(:) = 0.0_dp
        zmdz(:) = 0.0_dp

        xm = (x_node(n1_loc) + x_node(n2_loc))*my_half
          xmdx(n1_loc) = my_half
          xmdx(n2_loc) = my_half

        ym = (y_node(n1_loc) + y_node(n2_loc))*my_half
          ymdy(n1_loc) = my_half
          ymdy(n2_loc) = my_half

        zm = (z_node(n1_loc) + z_node(n2_loc))*my_half
          zmdz(n1_loc) = my_half
          zmdz(n2_loc) = my_half

!       compute left face centroid

        xldx(:) = 0.0_dp
        yldy(:) = 0.0_dp
        zldz(:) = 0.0_dp

        if (n4_loc /= 0) then
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc)               &
              + x_node(n4_loc))*my_4th
            xldx(n1_loc) = my_4th
            xldx(n2_loc) = my_4th
            xldx(n3_loc) = my_4th
            xldx(n4_loc) = my_4th

          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc)               &
              + y_node(n4_loc))*my_4th
            yldy(n1_loc) = my_4th
            yldy(n2_loc) = my_4th
            yldy(n3_loc) = my_4th
            yldy(n4_loc) = my_4th

          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc)               &
              + z_node(n4_loc))*my_4th
            zldz(n1_loc) = my_4th
            zldz(n2_loc) = my_4th
            zldz(n3_loc) = my_4th
            zldz(n4_loc) = my_4th
        else
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc))*my_3rd
            xldx(n1_loc) = my_3rd
            xldx(n2_loc) = my_3rd
            xldx(n3_loc) = my_3rd

          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc))*my_3rd
            yldy(n1_loc) = my_3rd
            yldy(n2_loc) = my_3rd
            yldy(n3_loc) = my_3rd

          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc))*my_3rd
            zldz(n1_loc) = my_3rd
            zldz(n2_loc) = my_3rd
            zldz(n3_loc) = my_3rd
        end if

!       compute right face centroid

        xrdx(:) = 0.0_dp
        yrdy(:) = 0.0_dp
        zrdz(:) = 0.0_dp

        if (n6_loc /= 0) then
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc)               &
              + x_node(n6_loc))*my_4th
            xrdx(n1_loc) = my_4th
            xrdx(n2_loc) = my_4th
            xrdx(n5_loc) = my_4th
            xrdx(n6_loc) = my_4th

          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc)               &
              + y_node(n6_loc))*my_4th
            yrdy(n1_loc) = my_4th
            yrdy(n2_loc) = my_4th
            yrdy(n5_loc) = my_4th
            yrdy(n6_loc) = my_4th

          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc)               &
              + z_node(n6_loc))*my_4th
            zrdz(n1_loc) = my_4th
            zrdz(n2_loc) = my_4th
            zrdz(n5_loc) = my_4th
            zrdz(n6_loc) = my_4th
        else
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc))*my_3rd
            xrdx(n1_loc) = my_3rd
            xrdx(n2_loc) = my_3rd
            xrdx(n5_loc) = my_3rd

          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc))*my_3rd
            yrdy(n1_loc) = my_3rd
            yrdy(n2_loc) = my_3rd
            yrdy(n5_loc) = my_3rd

          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc))*my_3rd
            zrdz(n1_loc) = my_3rd
            zrdz(n2_loc) = my_3rd
            zrdz(n5_loc) = my_3rd
        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
          areaxdx(:) = 0.0_dp
          areaxdy(:) = ((ycdy(:)-ymdy(:))*(zl-zr) - (zc-zm)*(yldy(:)-yrdy(:))) &
                                                                        *my_half
          areaxdz(:) = ((yc-ym)*(zldz(:)-zrdz(:)) - (zcdz(:)-zmdz(:))*(yl-yr)) &
                                                                        *my_half

        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
          areaydx(:) = ((zc-zm)*(xldx(:)-xrdx(:)) - (xcdx(:)-xmdx(:))*(zl-zr)) &
                                                                        *my_half
          areaydy(:) = 0.0_dp
          areaydz(:) = ((zcdz(:)-zmdz(:))*(xl-xr) - (xc-xm)*(zldz(:)-zrdz(:))) &
                                                                        *my_half

        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half
          areazdx(:) = ((xcdx(:)-xmdx(:))*(yl-yr) - (yc-ym)*(xldx(:)-xrdx(:))) &
                                                                        *my_half
          areazdy(:) = ((xc-xm)*(yldy(:)-yrdy(:)) - (ycdy(:)-ymdy(:))*(xl-xr)) &
                                                                        *my_half
          areazdz(:) = 0.0_dp

! Get gradients at the dual face; either take gradients for
! this piece of the dual face to be the same as the cell-average gradient
! computed above  (which is what the legacy FUN3D solver does for tets),
! or combine with the edge-gradient to increase h-ellipticity on
! non-simplicial meshes.

        areai = 1._dp/sqrt( areax**2 + areay**2 + areaz**2 )
          areaidx(:) = -0.5_dp/( areax**2 + areay**2 + areaz**2 )**1.5_dp      &
                       *(2.0_dp*areax*areaxdx(:) + 2.0_dp*areay*areaydx(:)     &
                       + 2.0_dp*areaz*areazdx(:))
          areaidy(:) = -0.5_dp/( areax**2 + areay**2 + areaz**2 )**1.5_dp      &
                       *(2.0_dp*areax*areaxdy(:) + 2.0_dp*areay*areaydy(:)     &
                       + 2.0_dp*areaz*areazdy(:))
          areaidz(:) = -0.5_dp/( areax**2 + areay**2 + areaz**2 )**1.5_dp      &
                       *(2.0_dp*areax*areaxdz(:) + 2.0_dp*areay*areaydz(:)     &
                       + 2.0_dp*areaz*areazdz(:))

        xnf = areax*areai
          xnfdx(:) = areax*areaidx(:) + areai*areaxdx(:)
          xnfdy(:) = areax*areaidy(:) + areai*areaxdy(:)
          xnfdz(:) = areax*areaidz(:) + areai*areaxdz(:)

        ynf = areay*areai
          ynfdx(:) = areay*areaidx(:) + areai*areaydx(:)
          ynfdy(:) = areay*areaidy(:) + areai*areaydy(:)
          ynfdz(:) = areay*areaidz(:) + areai*areaydz(:)

        znf = areaz*areai
          znfdx(:) = areaz*areaidx(:) + areai*areazdx(:)
          znfdy(:) = areaz*areaidy(:) + areai*areazdy(:)
          znfdz(:) = areaz*areaidz(:) + areai*areazdz(:)

!       ex, ey, ez is unit vector along edge direction

        exdx(:) = 0.0_dp
        eydy(:) = 0.0_dp
        ezdz(:) = 0.0_dp

        ex   = x_node(n2_loc) - x_node(n1_loc)
          exdx(n1_loc) = -1.0_dp
          exdx(n2_loc) =  1.0_dp

        ey   = y_node(n2_loc) - y_node(n1_loc)
          eydy(n1_loc) = -1.0_dp
          eydy(n2_loc) =  1.0_dp

        ez   = z_node(n2_loc) - z_node(n1_loc)
          ezdz(n1_loc) = -1.0_dp
          ezdz(n2_loc) =  1.0_dp

        disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

        stuff = ex**2 + ey**2 + ez**2

          disidx(:) = -0.5_dp*stuff**(-1.5_dp)*(2.0_dp*ex*exdx(:))
          disidy(:) = -0.5_dp*stuff**(-1.5_dp)*(2.0_dp*ey*eydy(:))
          disidz(:) = -0.5_dp*stuff**(-1.5_dp)*(2.0_dp*ez*ezdz(:))

        ex_new = ex*disi
          ex_newdx(:) = ex*disidx(:) + disi*exdx(:)
          ex_newdy(:) = ex*disidy(:)
          ex_newdz(:) = ex*disidz(:)

        ey_new = ey*disi
          ey_newdx(:) = ey*disidx(:)
          ey_newdy(:) = ey*disidy(:) + disi*eydy(:)
          ey_newdz(:) = ey*disidz(:)

        ez_new = ez*disi
          ez_newdx(:) = ez*disidx(:)
          ez_newdy(:) = ez*disidy(:)
          ez_newdz(:) = ez*disidz(:) + disi*ezdz(:)

        select case( gradient_construction_rhs )
        case (0)

          augment_weight1 = ex_new
            augment_weight1dx(:) = ex_newdx(:)
            augment_weight1dy(:) = ex_newdy(:)
            augment_weight1dz(:) = ex_newdz(:)
          augment_weight2 = ey_new
            augment_weight2dx(:) = ey_newdx(:)
            augment_weight2dy(:) = ey_newdy(:)
            augment_weight2dz(:) = ey_newdz(:)
          augment_weight3 = ez_new
            augment_weight3dx(:) = ez_newdx(:)
            augment_weight3dy(:) = ez_newdy(:)
            augment_weight3dz(:) = ez_newdz(:)

        case (1)

          e_dot_n = ex_new*xnf + ey_new*ynf + ez_new*znf
            e_dot_ndx(:) = ex_new*xnfdx(:) + ey_new*ynfdx(:) + ez_new*znfdx(:) &
                         + ex_newdx(:)*xnf + ey_newdx(:)*ynf + ez_newdx(:)*znf
            e_dot_ndy(:) = ex_new*xnfdy(:) + ey_new*ynfdy(:) + ez_new*znfdy(:) &
                         + ex_newdy(:)*xnf + ey_newdy(:)*ynf + ez_newdy(:)*znf
            e_dot_ndz(:) = ex_new*xnfdz(:) + ey_new*ynfdz(:) + ez_new*znfdz(:) &
                         + ex_newdz(:)*xnf + ey_newdz(:)*ynf + ez_newdz(:)*znf

          augment_weight1 = xnf
            augment_weight1dx(:) = xnfdx(:)
            augment_weight1dy(:) = xnfdy(:)
            augment_weight1dz(:) = xnfdz(:)

          augment_weight2 = ynf
            augment_weight2dx(:) = ynfdx(:)
            augment_weight2dy(:) = ynfdy(:)
            augment_weight2dz(:) = ynfdz(:)

          augment_weight3 = znf
            augment_weight3dx(:) = znfdx(:)
            augment_weight3dy(:) = znfdy(:)
            augment_weight3dz(:) = znfdz(:)

          if ( e_dot_n < 1.0e-10_dp ) then
            augment_weight1 = 0.0_dp
              augment_weight1dx(:) = 0.0_dp
              augment_weight1dy(:) = 0.0_dp
              augment_weight1dz(:) = 0.0_dp
            augment_weight2 = 0.0_dp
              augment_weight2dx(:) = 0.0_dp
              augment_weight2dy(:) = 0.0_dp
              augment_weight2dz(:) = 0.0_dp
            augment_weight3 = 0.0_dp
              augment_weight3dx(:) = 0.0_dp
              augment_weight3dy(:) = 0.0_dp
              augment_weight3dz(:) = 0.0_dp
          else
            augment_weight1_new = augment_weight1/e_dot_n
              augment_weight1_newdx(:) = (e_dot_n*augment_weight1dx(:)         &
                                 - augment_weight1*e_dot_ndx(:))/e_dot_n/e_dot_n
              augment_weight1_newdy(:) = (e_dot_n*augment_weight1dy(:)         &
                                 - augment_weight1*e_dot_ndy(:))/e_dot_n/e_dot_n
              augment_weight1_newdz(:) = (e_dot_n*augment_weight1dz(:)         &
                                 - augment_weight1*e_dot_ndz(:))/e_dot_n/e_dot_n
            augment_weight2_new = augment_weight2/e_dot_n
              augment_weight2_newdx(:) = (e_dot_n*augment_weight2dx(:)         &
                                 - augment_weight2*e_dot_ndx(:))/e_dot_n/e_dot_n
              augment_weight2_newdy(:) = (e_dot_n*augment_weight2dy(:)         &
                                 - augment_weight2*e_dot_ndy(:))/e_dot_n/e_dot_n
              augment_weight2_newdz(:) = (e_dot_n*augment_weight2dz(:)         &
                                 - augment_weight2*e_dot_ndz(:))/e_dot_n/e_dot_n
            augment_weight3_new = augment_weight3/e_dot_n
              augment_weight3_newdx(:) = (e_dot_n*augment_weight3dx(:)         &
                                 - augment_weight3*e_dot_ndx(:))/e_dot_n/e_dot_n
              augment_weight3_newdy(:) = (e_dot_n*augment_weight3dy(:)         &
                                 - augment_weight3*e_dot_ndy(:))/e_dot_n/e_dot_n
              augment_weight3_newdz(:) = (e_dot_n*augment_weight3dz(:)         &
                                 - augment_weight3*e_dot_ndz(:))/e_dot_n/e_dot_n
            augment_weight1 = augment_weight1_new
              augment_weight1dx(:) = augment_weight1_newdx(:)
              augment_weight1dy(:) = augment_weight1_newdy(:)
              augment_weight1dz(:) = augment_weight1_newdz(:)
            augment_weight2 = augment_weight2_new
              augment_weight2dx(:) = augment_weight2_newdx(:)
              augment_weight2dy(:) = augment_weight2_newdy(:)
              augment_weight2dz(:) = augment_weight2_newdz(:)
            augment_weight3 = augment_weight3_new
              augment_weight3dx(:) = augment_weight3_newdx(:)
              augment_weight3dy(:) = augment_weight3_newdy(:)
              augment_weight3dz(:) = augment_weight3_newdz(:)
          endif

        case default

          write(*,*) 'unknown gradient_construction_rhs in drdgeomv_mix'
          call lmpi_die
          stop

        end select

!       primitive variables at nodes 1 and 2

        u1 = q_node(n_momx,n1_loc)
        v1 = q_node(n_momy,n1_loc)
        w1 = q_node(n_momz,n1_loc)
        t1 = q_node(n_etot,n1_loc)

        u2 = q_node(n_momx,n2_loc)
        v2 = q_node(n_momy,n2_loc)
        w2 = q_node(n_momz,n2_loc)
        t2 = q_node(n_etot,n2_loc)

!       directional gradients along edge

        egradu = ( u2 - u1 )*disi
          egradudx(:) = (u2-u1)*disidx(:)
          egradudy(:) = (u2-u1)*disidy(:)
          egradudz(:) = (u2-u1)*disidz(:)

        egradv = ( v2 - v1 )*disi
          egradvdx(:) = (v2-v1)*disidx(:)
          egradvdy(:) = (v2-v1)*disidy(:)
          egradvdz(:) = (v2-v1)*disidz(:)

        egradw = ( w2 - w1 )*disi
          egradwdx(:) = (w2-w1)*disidx(:)
          egradwdy(:) = (w2-w1)*disidy(:)
          egradwdz(:) = (w2-w1)*disidz(:)

        egradt = ( t2 - t1 )*disi
          egradtdx(:) = (t2-t1)*disidx(:)
          egradtdy(:) = (t2-t1)*disidy(:)
          egradtdz(:) = (t2-t1)*disidz(:)

!       average Green-Gauss gradient in edge direction

        gradu_xi = uxavg*ex_new + uyavg*ey_new + uzavg*ez_new
          gradu_xidx(:)=uxavg*ex_newdx(:)+uyavg*ey_newdx(:)+uzavg*ez_newdx(:) +&
                        ex_new*uxavgdx(:)+ey_new*uyavgdx(:)+ez_new*uzavgdx(:)
          gradu_xidy(:)=uxavg*ex_newdy(:)+uyavg*ey_newdy(:)+uzavg*ez_newdy(:) +&
                        ex_new*uxavgdy(:)+ey_new*uyavgdy(:)+ez_new*uzavgdy(:)
          gradu_xidz(:)=uxavg*ex_newdz(:)+uyavg*ey_newdz(:)+uzavg*ez_newdz(:) +&
                        ex_new*uxavgdz(:)+ey_new*uyavgdz(:)+ez_new*uzavgdz(:)

        gradv_xi = vxavg*ex_new + vyavg*ey_new + vzavg*ez_new
          gradv_xidx(:)=vxavg*ex_newdx(:)+vyavg*ey_newdx(:)+vzavg*ez_newdx(:) +&
                        ex_new*vxavgdx(:)+ey_new*vyavgdx(:)+ez_new*vzavgdx(:)
          gradv_xidy(:)=vxavg*ex_newdy(:)+vyavg*ey_newdy(:)+vzavg*ez_newdy(:) +&
                        ex_new*vxavgdy(:)+ey_new*vyavgdy(:)+ez_new*vzavgdy(:)
          gradv_xidz(:)=vxavg*ex_newdz(:)+vyavg*ey_newdz(:)+vzavg*ez_newdz(:) +&
                        ex_new*vxavgdz(:)+ey_new*vyavgdz(:)+ez_new*vzavgdz(:)

        gradw_xi = wxavg*ex_new + wyavg*ey_new + wzavg*ez_new
          gradw_xidx(:)=wxavg*ex_newdx(:)+wyavg*ey_newdx(:)+wzavg*ez_newdx(:) +&
                        ex_new*wxavgdx(:)+ey_new*wyavgdx(:)+ez_new*wzavgdx(:)
          gradw_xidy(:)=wxavg*ex_newdy(:)+wyavg*ey_newdy(:)+wzavg*ez_newdy(:) +&
                        ex_new*wxavgdy(:)+ey_new*wyavgdy(:)+ez_new*wzavgdy(:)
          gradw_xidz(:)=wxavg*ex_newdz(:)+wyavg*ey_newdz(:)+wzavg*ez_newdz(:) +&
                        ex_new*wxavgdz(:)+ey_new*wyavgdz(:)+ez_new*wzavgdz(:)

        gradt_xi = txavg*ex_new + tyavg*ey_new + tzavg*ez_new
          gradt_xidx(:)=txavg*ex_newdx(:)+tyavg*ey_newdx(:)+tzavg*ez_newdx(:) +&
                        ex_new*txavgdx(:)+ey_new*tyavgdx(:)+ez_new*tzavgdx(:)
          gradt_xidy(:)=txavg*ex_newdy(:)+tyavg*ey_newdy(:)+tzavg*ez_newdy(:) +&
                        ex_new*txavgdy(:)+ey_new*tyavgdy(:)+ez_new*tzavgdy(:)
          gradt_xidz(:)=txavg*ex_newdz(:)+tyavg*ey_newdz(:)+tzavg*ez_newdz(:) +&
                        ex_new*txavgdz(:)+ey_new*tyavgdz(:)+ez_new*tzavgdz(:)

!       combine gradient contributions from edge and primal cell if non-tet

        tet_or_not : if ( type_cell == 'tet' ) then

          ux = uxavg
            uxdx(:) = uxavgdx(:)
            uxdy(:) = uxavgdy(:)
            uxdz(:) = uxavgdz(:)

          uy = uyavg
            uydx(:) = uyavgdx(:)
            uydy(:) = uyavgdy(:)
            uydz(:) = uyavgdz(:)

          uz = uzavg
            uzdx(:) = uzavgdx(:)
            uzdy(:) = uzavgdy(:)
            uzdz(:) = uzavgdz(:)

          vx = vxavg
            vxdx(:) = vxavgdx(:)
            vxdy(:) = vxavgdy(:)
            vxdz(:) = vxavgdz(:)

          vy = vyavg
            vydx(:) = vyavgdx(:)
            vydy(:) = vyavgdy(:)
            vydz(:) = vyavgdz(:)

          vz = vzavg
            vzdx(:) = vzavgdx(:)
            vzdy(:) = vzavgdy(:)
            vzdz(:) = vzavgdz(:)

          wx = wxavg
            wxdx(:) = wxavgdx(:)
            wxdy(:) = wxavgdy(:)
            wxdz(:) = wxavgdz(:)

          wy = wyavg
            wydx(:) = wyavgdx(:)
            wydy(:) = wyavgdy(:)
            wydz(:) = wyavgdz(:)

          wz = wzavg
            wzdx(:) = wzavgdx(:)
            wzdy(:) = wzavgdy(:)
            wzdz(:) = wzavgdz(:)

          tx = txavg
            txdx(:) = txavgdx(:)
            txdy(:) = txavgdy(:)
            txdz(:) = txavgdz(:)

          ty = tyavg
            tydx(:) = tyavgdx(:)
            tydy(:) = tyavgdy(:)
            tydz(:) = tyavgdz(:)

          tz = tzavg
            tzdx(:) = tzavgdx(:)
            tzdy(:) = tzavgdy(:)
            tzdz(:) = tzavgdz(:)

        else tet_or_not

          ux = uxavg + ( egradu - gradu_xi )*augment_weight1
            uxdx(:) = uxavgdx(:) + (egradu-gradu_xi)*augment_weight1dx(:)      &
                    + augment_weight1*(egradudx(:)-gradu_xidx(:))
            uxdy(:) = uxavgdy(:) + (egradu-gradu_xi)*augment_weight1dy(:)      &
                    + augment_weight1*(egradudy(:)-gradu_xidy(:))
            uxdz(:) = uxavgdz(:) + (egradu-gradu_xi)*augment_weight1dz(:)      &
                    + augment_weight1*(egradudz(:)-gradu_xidz(:))

          uy = uyavg + ( egradu - gradu_xi )*augment_weight2
            uydx(:) = uyavgdx(:) + (egradu-gradu_xi)*augment_weight2dx(:)      &
                    + augment_weight2*(egradudx(:)-gradu_xidx(:))
            uydy(:) = uyavgdy(:) + (egradu-gradu_xi)*augment_weight2dy(:)      &
                    + augment_weight2*(egradudy(:)-gradu_xidy(:))
            uydz(:) = uyavgdz(:) + (egradu-gradu_xi)*augment_weight2dz(:)      &
                    + augment_weight2*(egradudz(:)-gradu_xidz(:))

          uz = uzavg + ( egradu - gradu_xi )*augment_weight3
            uzdx(:) = uzavgdx(:) + (egradu-gradu_xi)*augment_weight3dx(:)      &
                    + augment_weight3*(egradudx(:)-gradu_xidx(:))
            uzdy(:) = uzavgdy(:) + (egradu-gradu_xi)*augment_weight3dy(:)      &
                    + augment_weight3*(egradudy(:)-gradu_xidy(:))
            uzdz(:) = uzavgdz(:) + (egradu-gradu_xi)*augment_weight3dz(:)      &
                    + augment_weight3*(egradudz(:)-gradu_xidz(:))

          vx = vxavg + ( egradv - gradv_xi )*augment_weight1
            vxdx(:) = vxavgdx(:) + (egradv-gradv_xi)*augment_weight1dx(:)      &
                    + augment_weight1*(egradvdx(:)-gradv_xidx(:))
            vxdy(:) = vxavgdy(:) + (egradv-gradv_xi)*augment_weight1dy(:)      &
                    + augment_weight1*(egradvdy(:)-gradv_xidy(:))
            vxdz(:) = vxavgdz(:) + (egradv-gradv_xi)*augment_weight1dz(:)      &
                    + augment_weight1*(egradvdz(:)-gradv_xidz(:))

          vy = vyavg + ( egradv - gradv_xi )*augment_weight2
            vydx(:) = vyavgdx(:) + (egradv-gradv_xi)*augment_weight2dx(:)      &
                    + augment_weight2*(egradvdx(:)-gradv_xidx(:))
            vydy(:) = vyavgdy(:) + (egradv-gradv_xi)*augment_weight2dy(:)      &
                    + augment_weight2*(egradvdy(:)-gradv_xidy(:))
            vydz(:) = vyavgdz(:) + (egradv-gradv_xi)*augment_weight2dz(:)      &
                    + augment_weight2*(egradvdz(:)-gradv_xidz(:))

          vz = vzavg + ( egradv - gradv_xi )*augment_weight3
            vzdx(:) = vzavgdx(:) + (egradv-gradv_xi)*augment_weight3dx(:)      &
                    + augment_weight3*(egradvdx(:)-gradv_xidx(:))
            vzdy(:) = vzavgdy(:) + (egradv-gradv_xi)*augment_weight3dy(:)      &
                    + augment_weight3*(egradvdy(:)-gradv_xidy(:))
            vzdz(:) = vzavgdz(:) + (egradv-gradv_xi)*augment_weight3dz(:)      &
                    + augment_weight3*(egradvdz(:)-gradv_xidz(:))

          wx = wxavg + ( egradw - gradw_xi )*augment_weight1
            wxdx(:) = wxavgdx(:) + (egradw-gradw_xi)*augment_weight1dx(:)      &
                    + augment_weight1*(egradwdx(:)-gradw_xidx(:))
            wxdy(:) = wxavgdy(:) + (egradw-gradw_xi)*augment_weight1dy(:)      &
                    + augment_weight1*(egradwdy(:)-gradw_xidy(:))
            wxdz(:) = wxavgdz(:) + (egradw-gradw_xi)*augment_weight1dz(:)      &
                    + augment_weight1*(egradwdz(:)-gradw_xidz(:))

          wy = wyavg + ( egradw - gradw_xi )*augment_weight2
            wydx(:) = wyavgdx(:) + (egradw-gradw_xi)*augment_weight2dx(:)      &
                    + augment_weight2*(egradwdx(:)-gradw_xidx(:))
            wydy(:) = wyavgdy(:) + (egradw-gradw_xi)*augment_weight2dy(:)      &
                    + augment_weight2*(egradwdy(:)-gradw_xidy(:))
            wydz(:) = wyavgdz(:) + (egradw-gradw_xi)*augment_weight2dz(:)      &
                    + augment_weight2*(egradwdz(:)-gradw_xidz(:))

          wz = wzavg + ( egradw - gradw_xi )*augment_weight3
            wzdx(:) = wzavgdx(:) + (egradw-gradw_xi)*augment_weight3dx(:)      &
                    + augment_weight3*(egradwdx(:)-gradw_xidx(:))
            wzdy(:) = wzavgdy(:) + (egradw-gradw_xi)*augment_weight3dy(:)      &
                    + augment_weight3*(egradwdy(:)-gradw_xidy(:))
            wzdz(:) = wzavgdz(:) + (egradw-gradw_xi)*augment_weight3dz(:)      &
                    + augment_weight3*(egradwdz(:)-gradw_xidz(:))

          tx = txavg + ( egradt - gradt_xi )*augment_weight1
            txdx(:) = txavgdx(:) + (egradt-gradt_xi)*augment_weight1dx(:)      &
                    + augment_weight1*(egradtdx(:)-gradt_xidx(:))
            txdy(:) = txavgdy(:) + (egradt-gradt_xi)*augment_weight1dy(:)      &
                    + augment_weight1*(egradtdy(:)-gradt_xidy(:))
            txdz(:) = txavgdz(:) + (egradt-gradt_xi)*augment_weight1dz(:)      &
                    + augment_weight1*(egradtdz(:)-gradt_xidz(:))

          ty = tyavg + ( egradt - gradt_xi )*augment_weight2
            tydx(:) = tyavgdx(:) + (egradt-gradt_xi)*augment_weight2dx(:)      &
                    + augment_weight2*(egradtdx(:)-gradt_xidx(:))
            tydy(:) = tyavgdy(:) + (egradt-gradt_xi)*augment_weight2dy(:)      &
                    + augment_weight2*(egradtdy(:)-gradt_xidy(:))
            tydz(:) = tyavgdz(:) + (egradt-gradt_xi)*augment_weight2dz(:)      &
                    + augment_weight2*(egradtdz(:)-gradt_xidz(:))

          tz = tzavg + ( egradt - gradt_xi )*augment_weight3
            tzdx(:) = tzavgdx(:) + (egradt-gradt_xi)*augment_weight3dx(:)      &
                    + augment_weight3*(egradtdx(:)-gradt_xidx(:))
            tzdy(:) = tzavgdy(:) + (egradt-gradt_xi)*augment_weight3dy(:)      &
                    + augment_weight3*(egradtdy(:)-gradt_xidy(:))
            tzdz(:) = tzavgdz(:) + (egradt-gradt_xi)*augment_weight3dz(:)      &
                    + augment_weight3*(egradtdz(:)-gradt_xidz(:))

        endif tet_or_not

! Viscous contributions at dual face [ full Navier-Stokes terms ]

!       components of symmetric stress tensor

        txx = rmu*(c43*ux - c23*vy - c23*wz)
          txxdx(:) = rmu*(c43*uxdx(:) - c23*vydx(:) - c23*wzdx(:))
          txxdy(:) = rmu*(c43*uxdy(:) - c23*vydy(:) - c23*wzdy(:))
          txxdz(:) = rmu*(c43*uxdz(:) - c23*vydz(:) - c23*wzdz(:))

        txy = rmu*(uy + vx)
          txydx(:) = rmu*(uydx(:) + vxdx(:))
          txydy(:) = rmu*(uydy(:) + vxdy(:))
          txydz(:) = rmu*(uydz(:) + vxdz(:))

        txz = rmu*(uz + wx)
          txzdx(:) = rmu*(uzdx(:) + wxdx(:))
          txzdy(:) = rmu*(uzdy(:) + wxdy(:))
          txzdz(:) = rmu*(uzdz(:) + wxdz(:))

        tyy = rmu*(c43*vy - c23*ux - c23*wz)
          tyydx(:) = rmu*(c43*vydx(:) - c23*uxdx(:) - c23*wzdx(:))
          tyydy(:) = rmu*(c43*vydy(:) - c23*uxdy(:) - c23*wzdy(:))
          tyydz(:) = rmu*(c43*vydz(:) - c23*uxdz(:) - c23*wzdz(:))

        tyz = rmu*(vz + wy)
          tyzdx(:) = rmu*(vzdx(:) + wydx(:))
          tyzdy(:) = rmu*(vzdy(:) + wydy(:))
          tyzdz(:) = rmu*(vzdz(:) + wydz(:))

        tzz = rmu*(c43*wz - c23*ux - c23*vy)
          tzzdx(:) = rmu*(c43*wzdx(:) - c23*uxdx(:) - c23*vydx(:))
          tzzdy(:) = rmu*(c43*wzdy(:) - c23*uxdy(:) - c23*vydy(:))
          tzzdz(:) = rmu*(c43*wzdz(:) - c23*uxdz(:) - c23*vydz(:))

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

        tqx = rmucgp*tx
          tqxdx(:) = rmucgp*txdx(:)
          tqxdy(:) = rmucgp*txdy(:)
          tqxdz(:) = rmucgp*txdz(:)

        tqy = rmucgp*ty
          tqydx(:) = rmucgp*tydx(:)
          tqydy(:) = rmucgp*tydy(:)
          tqydz(:) = rmucgp*tydz(:)

        tqz = rmucgp*tz
          tqzdx(:) = rmucgp*tzdx(:)
          tqzdy(:) = rmucgp*tzdy(:)
          tqzdz(:) = rmucgp*tzdz(:)

!       [nondimensionalization factor xmr ] * [ viscosity ]
!       [ unit normal and area ] at dual face

        rax = xmr*areax
          raxdx(:) = xmr*areaxdx(:)
          raxdy(:) = xmr*areaxdy(:)
          raxdz(:) = xmr*areaxdz(:)

        ray = xmr*areay
          raydx(:) = xmr*areaydx(:)
          raydy(:) = xmr*areaydy(:)
          raydz(:) = xmr*areaydz(:)

        raz = xmr*areaz
          razdx(:) = xmr*areazdx(:)
          razdy(:) = xmr*areazdy(:)
          razdz(:) = xmr*areazdz(:)

!       vf2 = rax*txx + ray*txy + raz*txz
          vf2dx(:) = rax*txxdx(:) + ray*txydx(:) + raz*txzdx(:) +              &
                     txx*raxdx(:) + txy*raydx(:) + txz*razdx(:)
          vf2dy(:) = rax*txxdy(:) + ray*txydy(:) + raz*txzdy(:) +              &
                     txx*raxdy(:) + txy*raydy(:) + txz*razdy(:)
          vf2dz(:) = rax*txxdz(:) + ray*txydz(:) + raz*txzdz(:) +              &
                     txx*raxdz(:) + txy*raydz(:) + txz*razdz(:)

!       vf3 = rax*txy + ray*tyy + raz*tyz
          vf3dx(:) = rax*txydx(:) + ray*tyydx(:) + raz*tyzdx(:) +              &
                     txy*raxdx(:) + tyy*raydx(:) + tyz*razdx(:)
          vf3dy(:) = rax*txydy(:) + ray*tyydy(:) + raz*tyzdy(:) +              &
                     txy*raxdy(:) + tyy*raydy(:) + tyz*razdy(:)
          vf3dz(:) = rax*txydz(:) + ray*tyydz(:) + raz*tyzdz(:) +              &
                     txy*raxdz(:) + tyy*raydz(:) + tyz*razdz(:)

!       vf4 = rax*txz + ray*tyz + raz*tzz
          vf4dx(:) = rax*txzdx(:) + ray*tyzdx(:) + raz*tzzdx(:) +              &
                     txz*raxdx(:) + tyz*raydx(:) + tzz*razdx(:)
          vf4dy(:) = rax*txzdy(:) + ray*tyzdy(:) + raz*tzzdy(:) +              &
                     txz*raxdy(:) + tyz*raydy(:) + tzz*razdy(:)
          vf4dz(:) = rax*txzdz(:) + ray*tyzdz(:) + raz*tzzdz(:) +              &
                     txz*raxdz(:) + tyz*raydz(:) + tzz*razdz(:)

!       vf5 = rax*tqx + ray*tqy + raz*tqz + (umu*vf2 + vmu*vf3 + wmu*vf4)
          vf5dx(:) = rax*tqxdx(:) + ray*tqydx(:) + raz*tqzdx(:) +              &
                     tqx*raxdx(:) + tqy*raydx(:) + tqz*razdx(:) +              &
                     umu*vf2dx(:) + vmu*vf3dx(:) + wmu*vf4dx(:)
          vf5dy(:) = rax*tqxdy(:) + ray*tqydy(:) + raz*tqzdy(:) +              &
                     tqx*raxdy(:) + tqy*raydy(:) + tqz*razdy(:) +              &
                     umu*vf2dy(:) + vmu*vf3dy(:) + wmu*vf4dy(:)
          vf5dz(:) = rax*tqxdz(:) + ray*tqydz(:) + raz*tqzdz(:) +              &
                     tqx*raxdz(:) + tqy*raydz(:) + tqz*razdz(:) +              &
                     umu*vf2dz(:) + vmu*vf3dz(:) + wmu*vf4dz(:)

! Finally, add the contribution of this piece of the dual face to the residual

!       if ( n1 <= nnodes0 ) then
!         res(n_momx,n1) = res(n_momx,n1) - vf2
!         res(n_momy,n1) = res(n_momy,n1) - vf3
!         res(n_momz,n1) = res(n_momz,n1) - vf4
!         res(n_etot,n1) = res(n_etot,n1) - vf5
!       end if

!       if ( n2 <= nnodes0 ) then
!         res(n_momx,n2) = res(n_momx,n2) + vf2
!         res(n_momy,n2) = res(n_momy,n2) + vf3
!         res(n_momz,n2) = res(n_momz,n2) + vf4
!         res(n_etot,n2) = res(n_etot,n2) + vf5
!       end if

! derivatives of residual at n1

        res12dx(:) = -vf2dx(:)
        res12dy(:) = -vf2dy(:)
        res12dz(:) = -vf2dz(:)

        res13dx(:) = -vf3dx(:)
        res13dy(:) = -vf3dy(:)
        res13dz(:) = -vf3dz(:)

        res14dx(:) = -vf4dx(:)
        res14dy(:) = -vf4dy(:)
        res14dz(:) = -vf4dz(:)

        res15dx(:) = -vf5dx(:)
        res15dy(:) = -vf5dy(:)
        res15dz(:) = -vf5dz(:)

! derivatives of residual at n2

        res22dx(:) = vf2dx(:)
        res22dy(:) = vf2dy(:)
        res22dz(:) = vf2dz(:)

        res23dx(:) = vf3dx(:)
        res23dy(:) = vf3dy(:)
        res23dz(:) = vf3dz(:)

        res24dx(:) = vf4dx(:)
        res24dy(:) = vf4dy(:)
        res24dz(:) = vf4dz(:)

        res25dx(:) = vf5dx(:)
        res25dy(:) = vf5dy(:)
        res25dz(:) = vf5dz(:)

! finally add up all the pieces

        fcn_loop : do k = 1, nfunctions

          lambda1(:) = coltag(:,n1)*rlam(:,n1,k)
          lambda2(:) = coltag(:,n2)*rlam(:,n2,k)

! place the derivatives of the residual at n1

          if ( n1 <= nnodes0 ) then
            do j = 1, node_per_cell
              node = c2n(j,n)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res12dx(j)*lambda1(2)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res13dx(j)*lambda1(3)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res14dx(j)*lambda1(4)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res15dx(j)*lambda1(5)

              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res12dy(j)*lambda1(2)
              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res13dy(j)*lambda1(3)
              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res14dy(j)*lambda1(4)
              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res15dy(j)*lambda1(5)

              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res12dz(j)*lambda1(2)
              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res13dz(j)*lambda1(3)
              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res14dz(j)*lambda1(4)
              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res15dz(j)*lambda1(5)

              if ( n1 == rn .and. node == np ) then
                pointsum(2,1) = pointsum(2,1) + res12dx(j)*coltag(2,n1)
                pointsum(3,1) = pointsum(3,1) + res13dx(j)*coltag(3,n1)
                pointsum(4,1) = pointsum(4,1) + res14dx(j)*coltag(4,n1)
                pointsum(5,1) = pointsum(5,1) + res15dx(j)*coltag(5,n1)

                pointsum(2,2) = pointsum(2,2) + res12dy(j)*coltag(2,n1)
                pointsum(3,2) = pointsum(3,2) + res13dy(j)*coltag(3,n1)
                pointsum(4,2) = pointsum(4,2) + res14dy(j)*coltag(4,n1)
                pointsum(5,2) = pointsum(5,2) + res15dy(j)*coltag(5,n1)

                pointsum(2,3) = pointsum(2,3) + res12dz(j)*coltag(2,n1)
                pointsum(3,3) = pointsum(3,3) + res13dz(j)*coltag(3,n1)
                pointsum(4,3) = pointsum(4,3) + res14dz(j)*coltag(4,n1)
                pointsum(5,3) = pointsum(5,3) + res15dz(j)*coltag(5,n1)
              endif

            end do
          endif

! place the derivatives of the residual at n2

          if ( n2 <= nnodes0 ) then
            do j = 1, node_per_cell
              node = c2n(j,n)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res22dx(j)*lambda2(2)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res23dx(j)*lambda2(3)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res24dx(j)*lambda2(4)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res25dx(j)*lambda2(5)

              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res22dy(j)*lambda2(2)
              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res23dy(j)*lambda2(3)
              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res24dy(j)*lambda2(4)
              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res25dy(j)*lambda2(5)

              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res22dz(j)*lambda2(2)
              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res23dz(j)*lambda2(3)
              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res24dz(j)*lambda2(4)
              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res25dz(j)*lambda2(5)

              if ( n2 == rn .and. node == np ) then
                pointsum(2,1) = pointsum(2,1) + res22dx(j)*coltag(2,n2)
                pointsum(3,1) = pointsum(3,1) + res23dx(j)*coltag(3,n2)
                pointsum(4,1) = pointsum(4,1) + res24dx(j)*coltag(4,n2)
                pointsum(5,1) = pointsum(5,1) + res25dx(j)*coltag(5,n2)

                pointsum(2,2) = pointsum(2,2) + res22dy(j)*coltag(2,n2)
                pointsum(3,2) = pointsum(3,2) + res23dy(j)*coltag(3,n2)
                pointsum(4,2) = pointsum(4,2) + res24dy(j)*coltag(4,n2)
                pointsum(5,2) = pointsum(5,2) + res25dy(j)*coltag(5,n2)

                pointsum(2,3) = pointsum(2,3) + res22dz(j)*coltag(2,n2)
                pointsum(3,3) = pointsum(3,3) + res23dz(j)*coltag(3,n2)
                pointsum(4,3) = pointsum(4,3) + res24dz(j)*coltag(4,n2)
                pointsum(5,3) = pointsum(5,3) + res25dz(j)*coltag(5,n2)
              endif
            end do
          endif

        end do fcn_loop

      end do edge_loop

    end do cell_loop

  end subroutine drdgeomv_mix


!============================= BC_DRDGEOMV_MIX ===============================80
!
! Closes off viscous flux on boundaries for the general (mixed) element case
! It is the generalization of the original tetrahedral formulation to more
! general elements.
!
! Note: qnode assumed to contain primitive variables
!
!=============================================================================80
  subroutine bc_drdgeomv_mix(nnodes0, nnodes01, nbnode, ibc, ibnode, nbfacet,  &
                             f2ntb, nbfaceq, f2nqb, nelem, elem, x, y, z,      &
                             qnode, amut, n_tot, drdxl, coltag, rlam,          &
                             nfunctions, ntp, adim, pointsum, rn, np)

    use info_depr,       only : twod, tref, re, xmach
    use fluid,           only : gamma, gm1, sutherland_constant, prandtl
    use turb_parameters, only : turbulent_prandtl
    use generic_gas_map, only : n_etot, n_energy, n_momx, n_momy, n_momz, ndim_g
    use bc_names,        only : bc_has_visc_flux_closure
    use element_types,   only : elem_type
    use element_defs,    only : max_node_per_cell
    use grid_metrics,    only : dual_area_quad
    use twod_util,       only : yplane_2d, y_coplanar_tol
    use kinddefs,        only : dp
    use nml_noninertial_reference_frame, only : noninertial

    integer, intent(in) :: n_tot, rn, np
    integer, intent(in) :: nbfacet
    integer, intent(in) :: nbfaceq
    integer, intent(in) :: nbnode
    integer, intent(in) :: ibc
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nelem, nfunctions, ntp, adim

    integer, dimension(nbnode),    intent(in) :: ibnode
    integer, dimension(nbfacet,5), intent(in) :: f2ntb
    integer, dimension(nbfaceq,6), intent(in) :: f2nqb

    real(dp), dimension(nnodes01),       intent(in) :: amut
    real(dp), dimension(nnodes01),       intent(in) :: x, y, z
    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode
    real(dp), dimension(adim,nnodes01),  intent(in) :: coltag
    real(dp), dimension(adim,nnodes01,nfunctions),  intent(in)    :: rlam
    real(dp), dimension(3,nnodes01,nfunctions,ntp), intent(inout) :: drdxl
    real(dp), dimension(6,3),                       intent(inout) :: pointsum

    type(elem_type), dimension(nelem), intent(in)    :: elem

    integer, dimension(max_node_per_cell) :: c2n_cell
    integer, dimension(max_node_per_cell) :: node_map

    integer :: i, icell, ielem, nf, node, iface, nn1, nn2, nn3, nn4
    integer :: in1, i_local, bnode, face_2d, eqn, eqn0, eqn1, k, j
    integer :: bnode1, bnode2, bnode3, bnode4
    integer :: nodes_local
    integer :: face_type, nl, nbface_type

    real(dp) :: tx, ty, tz, qavg, term1, term2, termx1, termy1, termz1
    real(dp) :: c23, c43, cell_vol, fact, termx2, termy2, termz2
    real(dp) :: rmu, qavg1, qavg2, cell_vol_inv
    real(dp) :: txx, txy, txz
    real(dp) :: tyy, tyz
    real(dp) :: tzz

    real(dp), dimension(4) :: xn, yn, zn

    real(dp) :: ux, uy, uz
    real(dp) :: vx, vy, vz
    real(dp) :: wx, wy, wz
    real(dp) :: tqx, tqy, tqz
    real(dp) :: u, v, w
    real(dp) :: x1, x2, x3
    real(dp) :: y1, y2, y3
    real(dp) :: z1, z2, z3
    real(dp) :: areax, areay, areaz
    real(dp) :: xmr, cstar, cgp, cgpt

    real(dp), dimension(4) :: xnorm_q, ynorm_q, znorm_q

    real(dp) :: nx, ny, nz
    real(dp), dimension(max_node_per_cell) :: nxdx, nydx, nzdx
    real(dp), dimension(max_node_per_cell) :: nxdy, nydy, nzdy
    real(dp), dimension(max_node_per_cell) :: nxdz, nydz, nzdz

    real(dp) :: nx1, ny1, nz1
    real(dp), dimension(max_node_per_cell) :: nx1dx, ny1dx, nz1dx
    real(dp), dimension(max_node_per_cell) :: nx1dy, ny1dy, nz1dy
    real(dp), dimension(max_node_per_cell) :: nx1dz, ny1dz, nz1dz

    real(dp) :: nx2, ny2, nz2
    real(dp), dimension(max_node_per_cell) :: nx2dx, ny2dx, nz2dx
    real(dp), dimension(max_node_per_cell) :: nx2dy, ny2dy, nz2dy
    real(dp), dimension(max_node_per_cell) :: nx2dz, ny2dz, nz2dz

    real(dp) :: termx, termy, termz
    real(dp) :: xavg, yavg, zavg
    real(dp), dimension(max_node_per_cell) :: xavgdx, yavgdy, zavgdz
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdx
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdy
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdz
    real(dp), dimension(max_node_per_cell) :: cell_voldx, cell_voldy, cell_voldz
    real(dp), dimension(max_node_per_cell) :: termxdx, termxdy, termxdz
    real(dp), dimension(max_node_per_cell) :: termydx, termydy, termydz
    real(dp), dimension(max_node_per_cell) :: termzdx, termzdy, termzdz
    real(dp) :: xavg1, yavg1, zavg1
    real(dp), dimension(max_node_per_cell) :: xavg1dx, yavg1dy, zavg1dz
    real(dp) :: xavg2, yavg2, zavg2
    real(dp), dimension(max_node_per_cell) :: xavg2dx, yavg2dy, zavg2dz
    real(dp), dimension(max_node_per_cell) :: term1dx, term1dy, term1dz
    real(dp), dimension(max_node_per_cell) :: term2dx, term2dy, term2dz
    real(dp), dimension(max_node_per_cell) :: termx1dx, termx1dy, termx1dz
    real(dp), dimension(max_node_per_cell) :: termx2dx, termx2dy, termx2dz
    real(dp), dimension(max_node_per_cell) :: termy1dx, termy1dy, termy1dz
    real(dp), dimension(max_node_per_cell) :: termy2dx, termy2dy, termy2dz
    real(dp), dimension(max_node_per_cell) :: termz1dx, termz1dy, termz1dz
    real(dp), dimension(max_node_per_cell) :: termz2dx, termz2dy, termz2dz
    real(dp), dimension(max_node_per_cell) :: uxdx, uxdy, uxdz
    real(dp), dimension(max_node_per_cell) :: vxdx, vxdy, vxdz
    real(dp), dimension(max_node_per_cell) :: wxdx, wxdy, wxdz
    real(dp), dimension(max_node_per_cell) :: txdx, txdy, txdz
    real(dp), dimension(max_node_per_cell) :: uydx, uydy, uydz
    real(dp), dimension(max_node_per_cell) :: vydx, vydy, vydz
    real(dp), dimension(max_node_per_cell) :: wydx, wydy, wydz
    real(dp), dimension(max_node_per_cell) :: tydx, tydy, tydz
    real(dp), dimension(max_node_per_cell) :: uzdx, uzdy, uzdz
    real(dp), dimension(max_node_per_cell) :: vzdx, vzdy, vzdz
    real(dp), dimension(max_node_per_cell) :: wzdx, wzdy, wzdz
    real(dp), dimension(max_node_per_cell) :: tzdx, tzdy, tzdz
    real(dp), dimension(max_node_per_cell) :: txxdx, txxdy, txxdz
    real(dp), dimension(max_node_per_cell) :: txydx, txydy, txydz
    real(dp), dimension(max_node_per_cell) :: txzdx, txzdy, txzdz
    real(dp), dimension(max_node_per_cell) :: tyydx, tyydy, tyydz
    real(dp), dimension(max_node_per_cell) :: tyzdx, tyzdy, tyzdz
    real(dp), dimension(max_node_per_cell) :: tzzdx, tzzdy, tzzdz
    real(dp), dimension(max_node_per_cell) :: tqxdx, tqxdy, tqxdz
    real(dp), dimension(max_node_per_cell) :: tqydx, tqydy, tqydz
    real(dp), dimension(max_node_per_cell) :: tqzdx, tqzdy, tqzdz
    real(dp), dimension(max_node_per_cell) :: x1dx, y1dy, z1dz
    real(dp), dimension(max_node_per_cell) :: x2dx, y2dy, z2dz
    real(dp), dimension(max_node_per_cell) :: x3dx, y3dy, z3dz
    real(dp), dimension(max_node_per_cell) :: areaxdx, areaxdy, areaxdz
    real(dp), dimension(max_node_per_cell) :: areaydx, areaydy, areaydz
    real(dp), dimension(max_node_per_cell) :: areazdx, areazdy, areazdz
    real(dp), dimension(4,max_node_per_cell) :: xndx, yndx, zndx
    real(dp), dimension(4,max_node_per_cell) :: xndy, yndy, zndy
    real(dp), dimension(4,max_node_per_cell) :: xndz, yndz, zndz
    real(dp), dimension(4,max_node_per_cell) :: xnorm_qdx, ynorm_qdx, znorm_qdx
    real(dp), dimension(4,max_node_per_cell) :: xnorm_qdy, ynorm_qdy, znorm_qdy
    real(dp), dimension(4,max_node_per_cell) :: xnorm_qdz, ynorm_qdz, znorm_qdz
    real(dp), dimension(4,max_node_per_cell) :: vf2dx, vf3dx, vf4dx, vf5dx
    real(dp), dimension(4,max_node_per_cell) :: vf2dy, vf3dy, vf4dy, vf5dy
    real(dp), dimension(4,max_node_per_cell) :: vf2dz, vf3dz, vf4dz, vf5dz
    real(dp), dimension(max_node_per_cell) :: res2dx, res2dy, res2dz
    real(dp), dimension(max_node_per_cell) :: res3dx, res3dy, res3dz
    real(dp), dimension(max_node_per_cell) :: res4dx, res4dy, res4dz
    real(dp), dimension(max_node_per_cell) :: res5dx, res5dy, res5dz
    real(dp), dimension(4,4) :: dual_areaxdx
    real(dp), dimension(4,4) :: dual_areaxdy
    real(dp), dimension(4,4) :: dual_areaxdz
    real(dp), dimension(4,4) :: dual_areaydx
    real(dp), dimension(4,4) :: dual_areaydy
    real(dp), dimension(4,4) :: dual_areaydz
    real(dp), dimension(4,4) :: dual_areazdx
    real(dp), dimension(4,4) :: dual_areazdy
    real(dp), dimension(4,4) :: dual_areazdz

    real(dp), dimension(max_node_per_cell) :: mu_node
    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node

    real(dp), dimension(n_energy,max_node_per_cell) :: eta_node
    real(dp), dimension(ndim_g,max_node_per_cell)   :: prim_node

    real(dp), dimension(ndim_g) :: gradx_cell
    real(dp), dimension(ndim_g) :: grady_cell
    real(dp), dimension(ndim_g) :: gradz_cell
    real(dp), dimension(ndim_g) :: gradx_cell_new
    real(dp), dimension(ndim_g) :: grady_cell_new
    real(dp), dimension(ndim_g) :: gradz_cell_new

    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_celldx
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_celldy
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_celldz
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_celldx
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_celldy
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_celldz
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_celldx
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_celldy
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_celldz
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_cell_newdx
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_cell_newdy
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_cell_newdz
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_cell_newdx
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_cell_newdy
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_cell_newdz
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_cell_newdx
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_cell_newdy
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_cell_newdz
    real(dp), dimension(adim) :: lambda

    real(dp), dimension(n_energy) :: eta

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_2   = 2.0_dp
    real(dp), parameter :: my_3   = 3.0_dp
    real(dp), parameter :: my_4   = 4.0_dp
    real(dp), parameter :: my_6th = 1.0_dp/6.0_dp
    real(dp), parameter :: my_18th = my_1/18.0_dp

  continue

    c43   = my_4 / my_3
    c23   = my_2 / my_3

    cstar = sutherland_constant / tref
    xmr   = xmach / re
    cgp   = my_1 / (gm1*prandtl)
    cgpt  = my_1 / (gm1*turbulent_prandtl)

    eqn0 = 2
    eqn1 = 5

! For bctype viscous_solid and viscous_solid_trs:
! note that the viscous no-slip strong boundary residuals will be set to zero
! after the all viscous fluxes have been closed off on the boundaries

    close_off_viscous: if ( bc_has_visc_flux_closure(ibc) ) then

      do face_type = 1,2
! First loop over all the tria boundary faces with face_type=1
! Next  loop over all the quad boundary faces with face_type=2
      if(face_type==1)then
        nbface_type = nbfacet
      else
        nbface_type = nbfaceq
      end if

      if(nbface_type==0)cycle

      loop_faces: do nf = 1, nbface_type

!       global node numbers of the cell/face nodes on the boundary

        if(face_type==1)then
          bnode1 = ibnode(f2ntb(nf,1))
          bnode2 = ibnode(f2ntb(nf,2))
          bnode3 = ibnode(f2ntb(nf,3))
          icell = f2ntb(nf,4)         ! global cell number
          ielem = f2ntb(nf,5)         ! cell type indicator
        else
          bnode1 = ibnode(f2nqb(nf,1))
          bnode2 = ibnode(f2nqb(nf,2))
          bnode3 = ibnode(f2nqb(nf,3))
          bnode4 = ibnode(f2nqb(nf,4))
          icell = f2nqb(nf,5)         ! global cell number
          ielem = f2nqb(nf,6)         ! cell type indicator
        end if

!       copy c2n array from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

        end if

! Compute cell averages, cell center, and set up some local solution arrays

        rmu = my_0
        u   = my_0
        v   = my_0
        w   = my_0
        eta(:) = my_0

        node_loop1 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          prim_node(n_momx:n_momz,i) = qnode(n_momx:n_momz,node)

          prim_node(1,i) = my_1
          prim_node(n_etot,i) = (gamma*qnode(n_etot,node)/qnode(1,node))
          mu_node(i)  = viscosity_law( cstar, prim_node(n_etot,i) )
          eta_node(1,i) = mu_node(i)*cgp + amut(node)*cgpt
          eta(1) = eta(1) + eta_node(1,i)
          mu_node(i) = mu_node(i) + amut(node)
          rmu = rmu + mu_node(i)
          u = u + prim_node(n_momx,i)*mu_node(i)
          v = v + prim_node(n_momy,i)*mu_node(i)
          w = w + prim_node(n_momz,i)*mu_node(i)

        end do node_loop1

!       now compute cell averages by dividing by the number of nodes
!       that contributed

        fact = 1._dp / real(nodes_local, dp)

        rmu = rmu*fact
        u = u*fact
        v = v*fact
        w = w*fact
        eta(:) = eta(:)*fact
        u = u/rmu
        v = v/rmu
        w = w/rmu

! Get the gradients in the primal cell via Green-Gauss

        cell_vol = 0.0_dp
          cell_voldx = 0.0_dp
          cell_voldy = 0.0_dp
          cell_voldz = 0.0_dp

        gradx_cell(:) = my_0
          gradx_celldx(:,:) = my_0
          gradx_celldy(:,:) = my_0
          gradx_celldz(:,:) = my_0
        grady_cell(:) = my_0
          grady_celldx(:,:) = my_0
          grady_celldy(:,:) = my_0
          grady_celldz(:,:) = my_0
        gradz_cell(:) = my_0
          gradz_celldx(:,:) = my_0
          gradz_celldy(:,:) = my_0
          gradz_celldz(:,:) = my_0

        threed_faces : do iface = 1, elem(ielem)%face_per_cell

          nn1 = elem(ielem)%local_f2n(iface,1)
          nn2 = elem(ielem)%local_f2n(iface,2)
          nn3 = elem(ielem)%local_f2n(iface,3)
          nn4 = elem(ielem)%local_f2n(iface,4)

          nxdx = 0.0_dp
          nxdy = 0.0_dp
          nxdz = 0.0_dp
          nx1dx = 0.0_dp
          nx1dy = 0.0_dp
          nx1dz = 0.0_dp
          nx2dx = 0.0_dp
          nx2dy = 0.0_dp
          nx2dz = 0.0_dp

          nydx = 0.0_dp
          nydy = 0.0_dp
          nydz = 0.0_dp
          ny1dx = 0.0_dp
          ny1dy = 0.0_dp
          ny1dz = 0.0_dp
          ny2dx = 0.0_dp
          ny2dy = 0.0_dp
          ny2dz = 0.0_dp

          nzdx = 0.0_dp
          nzdy = 0.0_dp
          nzdz = 0.0_dp
          nz1dx = 0.0_dp
          nz1dy = 0.0_dp
          nz1dz = 0.0_dp
          nz2dx = 0.0_dp
          nz2dy = 0.0_dp
          nz2dz = 0.0_dp

          if (nn4 == nn1) then

!         triangular faces of the cell

!         face normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

            nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))       &
               - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

              nxdy(nn1) = -(z_node(nn3) - z_node(nn1))                         &
                         + (z_node(nn2) - z_node(nn1))
              nxdy(nn2) =   z_node(nn3) - z_node(nn1)
              nxdy(nn3) = -(z_node(nn2) - z_node(nn1))

              nxdz(nn1) = -(y_node(nn2) - y_node(nn1))                         &
                         + (y_node(nn3) - y_node(nn1))
              nxdz(nn2) = -(y_node(nn3) - y_node(nn1))
              nxdz(nn3) =  (y_node(nn2) - y_node(nn1))

            ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))       &
               - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

              nydx(nn1) = -(z_node(nn2) - z_node(nn1))                         &
                         + (z_node(nn3) - z_node(nn1))
              nydx(nn2) = -(z_node(nn3) - z_node(nn1))
              nydx(nn3) =  (z_node(nn2) - z_node(nn1))

              nydz(nn1) = -(x_node(nn3) - x_node(nn1))                         &
                         + (x_node(nn2) - x_node(nn1))
              nydz(nn2) =  (x_node(nn3) - x_node(nn1))
              nydz(nn3) = -(x_node(nn2) - x_node(nn1))

            nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))       &
               - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

              nzdx(nn1) = -(y_node(nn3) - y_node(nn1))                         &
                         + (y_node(nn2) - y_node(nn1))
              nzdx(nn2) =  (y_node(nn3) - y_node(nn1))
              nzdx(nn3) = -(y_node(nn2) - y_node(nn1))

              nzdy(nn1) = -(x_node(nn2) - x_node(nn1))                         &
                         + (x_node(nn3) - x_node(nn1))
              nzdy(nn2) = -(x_node(nn3) - x_node(nn1))
              nzdy(nn3) =  (x_node(nn2) - x_node(nn1))

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

            xavgdx = 0.0_dp
            yavgdy = 0.0_dp
            zavgdz = 0.0_dp

            xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
              xavgdx(nn1) = 1.0_dp
              xavgdx(nn2) = 1.0_dp
              xavgdx(nn3) = 1.0_dp

            yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
              yavgdy(nn1) = 1.0_dp
              yavgdy(nn2) = 1.0_dp
              yavgdy(nn3) = 1.0_dp

            zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)
              zavgdz(nn1) = 1.0_dp
              zavgdz(nn2) = 1.0_dp
              zavgdz(nn3) = 1.0_dp

!         cell volume contributions

            cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th
              cell_voldx(:) = cell_voldx(:) + (xavg*nxdx(:) + yavg*nydx(:)     &
                            + zavg*nzdx(:) + xavgdx(:)*nx)*my_18th
              cell_voldy(:) = cell_voldy(:) + (xavg*nxdy(:) + yavg*nydy(:)     &
                            + zavg*nzdy(:) + yavgdy(:)*ny)*my_18th
              cell_voldz(:) = cell_voldz(:) + (xavg*nxdz(:) + yavg*nydz(:)     &
                            + zavg*nzdz(:) + zavgdz(:)*nz)*my_18th

            termx = nx*my_6th
              termxdx(:) = nxdx(:)*my_6th
              termxdy(:) = nxdy(:)*my_6th
              termxdz(:) = nxdz(:)*my_6th
            termy = ny*my_6th
              termydx(:) = nydx(:)*my_6th
              termydy(:) = nydy(:)*my_6th
              termydz(:) = nydz(:)*my_6th
            termz = nz*my_6th
              termzdx(:) = nzdx(:)*my_6th
              termzdy(:) = nzdy(:)*my_6th
              termzdz(:) = nzdz(:)*my_6th

!         gradient contributions

            do eqn = eqn0, eqn1
              qavg= prim_node(eqn,nn1) + prim_node(eqn,nn2) + prim_node(eqn,nn3)
              gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
                gradx_celldx(eqn,:) = gradx_celldx(eqn,:) + termxdx(:)*qavg
                gradx_celldy(eqn,:) = gradx_celldy(eqn,:) + termxdy(:)*qavg
                gradx_celldz(eqn,:) = gradx_celldz(eqn,:) + termxdz(:)*qavg
              grady_cell(eqn) = grady_cell(eqn) + termy*qavg
                grady_celldx(eqn,:) = grady_celldx(eqn,:) + termydx(:)*qavg
                grady_celldy(eqn,:) = grady_celldy(eqn,:) + termydy(:)*qavg
                grady_celldz(eqn,:) = grady_celldz(eqn,:) + termydz(:)*qavg
              gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg
                gradz_celldx(eqn,:) = gradz_celldx(eqn,:) + termzdx(:)*qavg
                gradz_celldy(eqn,:) = gradz_celldy(eqn,:) + termzdy(:)*qavg
                gradz_celldz(eqn,:) = gradz_celldz(eqn,:) + termzdz(:)*qavg
            end do

          else

!         quadrilateral faces of the cell

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

            xavg1dx = 0.0_dp
            yavg1dy = 0.0_dp
            zavg1dz = 0.0_dp

            xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
              xavg1dx(nn1) = 1.0_dp
              xavg1dx(nn2) = 1.0_dp
              xavg1dx(nn3) = 1.0_dp
            yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
              yavg1dy(nn1) = 1.0_dp
              yavg1dy(nn2) = 1.0_dp
              yavg1dy(nn3) = 1.0_dp
            zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)
              zavg1dz(nn1) = 1.0_dp
              zavg1dz(nn2) = 1.0_dp
              zavg1dz(nn3) = 1.0_dp

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

            nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))      &
                - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

              nx1dy(nn1) = -(z_node(nn3) - z_node(nn1))                        &
                          + (z_node(nn2) - z_node(nn1))
              nx1dy(nn2) =   z_node(nn3) - z_node(nn1)
              nx1dy(nn3) = -(z_node(nn2) - z_node(nn1))

              nx1dz(nn1) = -(y_node(nn2) - y_node(nn1))                        &
                          + (y_node(nn3) - y_node(nn1))
              nx1dz(nn2) = -(y_node(nn3) - y_node(nn1))
              nx1dz(nn3) =  (y_node(nn2) - y_node(nn1))

            ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))      &
                - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

              ny1dx(nn1) = -(z_node(nn2) - z_node(nn1))                        &
                          + (z_node(nn3) - z_node(nn1))
              ny1dx(nn2) = -(z_node(nn3) - z_node(nn1))
              ny1dx(nn3) =  (z_node(nn2) - z_node(nn1))

              ny1dz(nn1) = -(x_node(nn3) - x_node(nn1))                        &
                          + (x_node(nn2) - x_node(nn1))
              ny1dz(nn2) =  (x_node(nn3) - x_node(nn1))
              ny1dz(nn3) = -(x_node(nn2) - x_node(nn1))

            nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))      &
                - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

              nz1dx(nn1) = -(y_node(nn3) - y_node(nn1))                        &
                          + (y_node(nn2) - y_node(nn1))
              nz1dx(nn2) =  (y_node(nn3) - y_node(nn1))
              nz1dx(nn3) = -(y_node(nn2) - y_node(nn1))

              nz1dy(nn1) = -(x_node(nn2) - x_node(nn1))                        &
                          + (x_node(nn3) - x_node(nn1))
              nz1dy(nn2) = -(x_node(nn3) - x_node(nn1))
              nz1dy(nn3) =  (x_node(nn2) - x_node(nn1))

            term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1
              term1dx(:) = xavg1*nx1dx(:) + yavg1*ny1dx(:) + zavg1*nz1dx(:) +  &
                           xavg1dx(:)*nx1
              term1dy(:) = xavg1*nx1dy(:) + yavg1*ny1dy(:) + zavg1*nz1dy(:) +  &
                           yavg1dy(:)*ny1
              term1dz(:) = xavg1*nx1dz(:) + yavg1*ny1dz(:) + zavg1*nz1dz(:) +  &
                           zavg1dz(:)*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

            xavg2dx = 0.0_dp
            yavg2dy = 0.0_dp
            zavg2dz = 0.0_dp

            xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
              xavg2dx(nn1) = 1.0_dp
              xavg2dx(nn3) = 1.0_dp
              xavg2dx(nn4) = 1.0_dp
            yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
              yavg2dy(nn1) = 1.0_dp
              yavg2dy(nn3) = 1.0_dp
              yavg2dy(nn4) = 1.0_dp
            zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)
              zavg2dz(nn1) = 1.0_dp
              zavg2dz(nn3) = 1.0_dp
              zavg2dz(nn4) = 1.0_dp

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

            nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))      &
                - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))

              nx2dy(nn1) = -(z_node(nn4) - z_node(nn1))                        &
                          + (z_node(nn3) - z_node(nn1))
              nx2dy(nn3) =   z_node(nn4) - z_node(nn1)
              nx2dy(nn4) = -(z_node(nn3) - z_node(nn1))

              nx2dz(nn1) = -(y_node(nn3) - y_node(nn1))                        &
                          + (y_node(nn4) - y_node(nn1))
              nx2dz(nn3) = -(y_node(nn4) - y_node(nn1))
              nx2dz(nn4) =  (y_node(nn3) - y_node(nn1))

            ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))      &
                - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))

              ny2dx(nn1) = -(z_node(nn3) - z_node(nn1))                        &
                          + (z_node(nn4) - z_node(nn1))
              ny2dx(nn3) = -(z_node(nn4) - z_node(nn1))
              ny2dx(nn4) =  (z_node(nn3) - z_node(nn1))

              ny2dz(nn1) = -(x_node(nn4) - x_node(nn1))                        &
                          + (x_node(nn3) - x_node(nn1))
              ny2dz(nn3) =  (x_node(nn4) - x_node(nn1))
              ny2dz(nn4) = -(x_node(nn3) - x_node(nn1))

            nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))      &
                - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

              nz2dx(nn1) = -(y_node(nn4) - y_node(nn1))                        &
                          + (y_node(nn3) - y_node(nn1))
              nz2dx(nn3) =  (y_node(nn4) - y_node(nn1))
              nz2dx(nn4) = -(y_node(nn3) - y_node(nn1))

              nz2dy(nn1) = -(x_node(nn3) - x_node(nn1))                        &
                          + (x_node(nn4) - x_node(nn1))
              nz2dy(nn3) = -(x_node(nn4) - x_node(nn1))
              nz2dy(nn4) =  (x_node(nn3) - x_node(nn1))

            term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2
              term2dx(:) = xavg2*nx2dx(:) + yavg2*ny2dx(:) + zavg2*nz2dx(:) +  &
                           xavg2dx(:)*nx2
              term2dy(:) = xavg2*nx2dy(:) + yavg2*ny2dy(:) + zavg2*nz2dy(:) +  &
                           yavg2dy(:)*ny2
              term2dz(:) = xavg2*nx2dz(:) + yavg2*ny2dz(:) + zavg2*nz2dz(:) +  &
                           zavg2dz(:)*nz2

!         cell volume contributions

            cell_vol = cell_vol + (term1 + term2)*my_18th
              cell_voldx(:) = cell_voldx(:) + (term1dx(:) + term2dx(:))*my_18th
              cell_voldy(:) = cell_voldy(:) + (term1dy(:) + term2dy(:))*my_18th
              cell_voldz(:) = cell_voldz(:) + (term1dz(:) + term2dz(:))*my_18th

!         gradient contributions

            termx1 = nx1*my_6th
              termx1dx(:) = nx1dx(:)*my_6th
              termx1dy(:) = nx1dy(:)*my_6th
              termx1dz(:) = nx1dz(:)*my_6th
            termy1 = ny1*my_6th
              termy1dx(:) = ny1dx(:)*my_6th
              termy1dy(:) = ny1dy(:)*my_6th
              termy1dz(:) = ny1dz(:)*my_6th
            termz1 = nz1*my_6th
              termz1dx(:) = nz1dx(:)*my_6th
              termz1dy(:) = nz1dy(:)*my_6th
              termz1dz(:) = nz1dz(:)*my_6th

            termx2 = nx2*my_6th
              termx2dx(:) = nx2dx(:)*my_6th
              termx2dy(:) = nx2dy(:)*my_6th
              termx2dz(:) = nx2dz(:)*my_6th
            termy2 = ny2*my_6th
              termy2dx(:) = ny2dx(:)*my_6th
              termy2dy(:) = ny2dy(:)*my_6th
              termy2dz(:) = ny2dz(:)*my_6th
            termz2 = nz2*my_6th
              termz2dx(:) = nz2dx(:)*my_6th
              termz2dy(:) = nz2dy(:)*my_6th
              termz2dz(:) = nz2dz(:)*my_6th

            do eqn = eqn0, eqn1
              qavg1=prim_node(eqn,nn1) + prim_node(eqn,nn2) + prim_node(eqn,nn3)
              qavg2=prim_node(eqn,nn1) + prim_node(eqn,nn3) + prim_node(eqn,nn4)
              gradx_cell(eqn) = gradx_cell(eqn) + termx1*qavg1 + termx2*qavg2
                gradx_celldx(eqn,:) = gradx_celldx(eqn,:) + termx1dx(:)*qavg1  &
                                    + termx2dx(:)*qavg2
                gradx_celldy(eqn,:) = gradx_celldy(eqn,:) + termx1dy(:)*qavg1  &
                                    + termx2dy(:)*qavg2
                gradx_celldz(eqn,:) = gradx_celldz(eqn,:) + termx1dz(:)*qavg1  &
                                    + termx2dz(:)*qavg2
              grady_cell(eqn) = grady_cell(eqn) + termy1*qavg1 + termy2*qavg2
                grady_celldx(eqn,:) = grady_celldx(eqn,:) + termy1dx(:)*qavg1  &
                                    + termy2dx(:)*qavg2
                grady_celldy(eqn,:) = grady_celldy(eqn,:) + termy1dy(:)*qavg1  &
                                    + termy2dy(:)*qavg2
                grady_celldz(eqn,:) = grady_celldz(eqn,:) + termy1dz(:)*qavg1  &
                                    + termy2dz(:)*qavg2
              gradz_cell(eqn) = gradz_cell(eqn) + termz1*qavg1 + termz2*qavg2
                gradz_celldx(eqn,:) = gradz_celldx(eqn,:) + termz1dx(:)*qavg1  &
                                    + termz2dx(:)*qavg2
                gradz_celldy(eqn,:) = gradz_celldy(eqn,:) + termz1dy(:)*qavg1  &
                                    + termz2dy(:)*qavg2
                gradz_celldz(eqn,:) = gradz_celldz(eqn,:) + termz1dz(:)*qavg1  &
                                    + termz2dz(:)*qavg2
            end do

            nx = nx1 + nx2
              nxdx(:) = nx1dx(:) + nx2dx(:)
              nxdy(:) = nx1dy(:) + nx2dy(:)
              nxdz(:) = nx1dz(:) + nx2dz(:)
            ny = ny1 + ny2
              nydx(:) = ny1dx(:) + ny2dx(:)
              nydy(:) = ny1dy(:) + ny2dy(:)
              nydz(:) = ny1dz(:) + ny2dz(:)
            nz = nz1 + nz2
              nzdx(:) = nz1dx(:) + nz2dx(:)
              nzdy(:) = nz1dy(:) + nz2dy(:)
              nzdz(:) = nz1dz(:) + nz2dz(:)

          end if

        end do threed_faces

!   need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

        cell_vol_inv = my_1/cell_vol
          cell_vol_invdx(:) = -my_1/cell_vol/cell_vol*cell_voldx(:)
          cell_vol_invdy(:) = -my_1/cell_vol/cell_vol*cell_voldy(:)
          cell_vol_invdz(:) = -my_1/cell_vol/cell_vol*cell_voldz(:)

        gradx_cell_new(eqn0:eqn1) = gradx_cell(eqn0:eqn1) * cell_vol_inv
          do k = eqn0, eqn1
            gradx_cell_newdx(k,:) = gradx_cell(k)*cell_vol_invdx(:)            &
                                  + cell_vol_inv*gradx_celldx(k,:)
            gradx_cell_newdy(k,:) = gradx_cell(k)*cell_vol_invdy(:)            &
                                  + cell_vol_inv*gradx_celldy(k,:)
            gradx_cell_newdz(k,:) = gradx_cell(k)*cell_vol_invdz(:)            &
                                  + cell_vol_inv*gradx_celldz(k,:)
          end do
        grady_cell_new(eqn0:eqn1) = grady_cell(eqn0:eqn1) * cell_vol_inv
          do k = eqn0, eqn1
            grady_cell_newdx(k,:) = grady_cell(k)*cell_vol_invdx(:)            &
                                  + cell_vol_inv*grady_celldx(k,:)
            grady_cell_newdy(k,:) = grady_cell(k)*cell_vol_invdy(:)            &
                                  + cell_vol_inv*grady_celldy(k,:)
            grady_cell_newdz(k,:) = grady_cell(k)*cell_vol_invdz(:)            &
                                  + cell_vol_inv*grady_celldz(k,:)
          end do
        gradz_cell_new(eqn0:eqn1) = gradz_cell(eqn0:eqn1) * cell_vol_inv
          do k = eqn0, eqn1
            gradz_cell_newdx(k,:) = gradz_cell(k)*cell_vol_invdx(:)            &
                                  + cell_vol_inv*gradz_celldx(k,:)
            gradz_cell_newdy(k,:) = gradz_cell(k)*cell_vol_invdy(:)            &
                                  + cell_vol_inv*gradz_celldy(k,:)
            gradz_cell_newdz(k,:) = gradz_cell(k)*cell_vol_invdz(:)            &
                                  + cell_vol_inv*gradz_celldz(k,:)
          end do

        ux = gradx_cell_new(n_momx)
          uxdx(:) = gradx_cell_newdx(n_momx,:)
          uxdy(:) = gradx_cell_newdy(n_momx,:)
          uxdz(:) = gradx_cell_newdz(n_momx,:)
        vx = gradx_cell_new(n_momy)
          vxdx(:) = gradx_cell_newdx(n_momy,:)
          vxdy(:) = gradx_cell_newdy(n_momy,:)
          vxdz(:) = gradx_cell_newdz(n_momy,:)
        wx = gradx_cell_new(n_momz)
          wxdx(:) = gradx_cell_newdx(n_momz,:)
          wxdy(:) = gradx_cell_newdy(n_momz,:)
          wxdz(:) = gradx_cell_newdz(n_momz,:)
        tx = gradx_cell_new(n_etot)
          txdx(:) = gradx_cell_newdx(n_etot,:)
          txdy(:) = gradx_cell_newdy(n_etot,:)
          txdz(:) = gradx_cell_newdz(n_etot,:)

        uy = grady_cell_new(n_momx)
          uydx(:) = grady_cell_newdx(n_momx,:)
          uydy(:) = grady_cell_newdy(n_momx,:)
          uydz(:) = grady_cell_newdz(n_momx,:)
        vy = grady_cell_new(n_momy)
          vydx(:) = grady_cell_newdx(n_momy,:)
          vydy(:) = grady_cell_newdy(n_momy,:)
          vydz(:) = grady_cell_newdz(n_momy,:)
        wy = grady_cell_new(n_momz)
          wydx(:) = grady_cell_newdx(n_momz,:)
          wydy(:) = grady_cell_newdy(n_momz,:)
          wydz(:) = grady_cell_newdz(n_momz,:)
        ty = grady_cell_new(n_etot)
          tydx(:) = grady_cell_newdx(n_etot,:)
          tydy(:) = grady_cell_newdy(n_etot,:)
          tydz(:) = grady_cell_newdz(n_etot,:)

        uz = gradz_cell_new(n_momx)
          uzdx(:) = gradz_cell_newdx(n_momx,:)
          uzdy(:) = gradz_cell_newdy(n_momx,:)
          uzdz(:) = gradz_cell_newdz(n_momx,:)
        vz = gradz_cell_new(n_momy)
          vzdx(:) = gradz_cell_newdx(n_momy,:)
          vzdy(:) = gradz_cell_newdy(n_momy,:)
          vzdz(:) = gradz_cell_newdz(n_momy,:)
        wz = gradz_cell_new(n_momz)
          wzdx(:) = gradz_cell_newdx(n_momz,:)
          wzdy(:) = gradz_cell_newdy(n_momz,:)
          wzdz(:) = gradz_cell_newdz(n_momz,:)
        tz = gradz_cell_new(n_etot)
          tzdx(:) = gradz_cell_newdx(n_etot,:)
          tzdy(:) = gradz_cell_newdy(n_etot,:)
          tzdz(:) = gradz_cell_newdz(n_etot,:)

! Now we get this boundary faces' contribution to the dual normal at each
! node; note that for a triangle, this is just 1/3 the face normal, and is
! the same at each node

        if(face_type ==1)then

          x1dx(:) = 0.0_dp
          x2dx(:) = 0.0_dp
          x3dx(:) = 0.0_dp

          y1dy(:) = 0.0_dp
          y2dy(:) = 0.0_dp
          y3dy(:) = 0.0_dp

          z1dz(:) = 0.0_dp
          z2dz(:) = 0.0_dp
          z3dz(:) = 0.0_dp

          x1 = x(bnode1)
          y1 = y(bnode1)
          z1 = z(bnode1)

          x2 = x(bnode2)
          y2 = y(bnode2)
          z2 = z(bnode2)

          x3 = x(bnode3)
          y3 = y(bnode3)
          z3 = z(bnode3)

          do k = 1, elem(ielem)%node_per_cell

            if ( bnode1 == c2n_cell(k) ) then
              x1dx(k) = 1.0_dp
              y1dy(k) = 1.0_dp
              z1dz(k) = 1.0_dp
            endif

            if ( bnode2 == c2n_cell(k) ) then
              x2dx(k) = 1.0_dp
              y2dy(k) = 1.0_dp
              z2dz(k) = 1.0_dp
            endif

            if ( bnode3 == c2n_cell(k) ) then
              x3dx(k) = 1.0_dp
              y3dy(k) = 1.0_dp
              z3dz(k) = 1.0_dp
            endif

          end do

!       - sign on normals because Green-Gauss needs outward pointing normals

          areax = -my_6th*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
            areaxdx(:) =  0.0_dp
            areaxdy(:) = -my_6th*( (y2dy(:)-y1dy(:))*(z3-z1)                   &
                       - (z2-z1)*(y3dy(:)-y1dy(:)) )
            areaxdz(:) = -my_6th*( (y2-y1)*(z3dz(:)-z1dz(:))                   &
                       - (z2dz(:)-z1dz(:))*(y3-y1) )

          areay = -my_6th*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
            areaydx(:) = -my_6th*( (z2-z1)*(x3dx(:)-x1dx(:))                   &
                       - (x2dx(:)-x1dx(:))*(z3-z1) )
            areaydy(:) = 0.0_dp
            areaydz(:) = -my_6th*( (z2dz(:)-z1dz(:))*(x3-x1)                   &
                       - (x2-x1)*(z3dz(:)-z1dz(:)) )

          areaz = -my_6th*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )
            areazdx(:) = -my_6th*( (x2dx(:)-x1dx(:))*(y3-y1)                   &
                       - (y2-y1)*(x3dx(:)-x1dx(:)) )
            areazdy(:) = -my_6th*( (x2-x1)*(y3dy(:)-y1dy(:))                   &
                       - (y2dy(:)-y1dy(:))*(x3-x1) )
            areazdz(:) = 0.0_dp

          nl = 3

          xn(1:3) = areax
            do k = 1, 3
              xndx(k,:) = areaxdx(:)
              xndy(k,:) = areaxdy(:)
              xndz(k,:) = areaxdz(:)
            end do

          yn(1:3) = areay
            do k = 1, 3
              yndx(k,:) = areaydx(:)
              yndy(k,:) = areaydy(:)
              yndz(k,:) = areaydz(:)
            end do

          zn(1:3) = areaz
            do k = 1, 3
              zndx(k,:) = areazdx(:)
              zndy(k,:) = areazdy(:)
              zndz(k,:) = areazdz(:)
            end do

        else

          call dual_area_quad(nnodes01,x,y,z,bnode1,bnode2,bnode3,bnode4,      &
                              noninertial,xnorm_q,ynorm_q,znorm_q,             &
                              dual_areaxdx,dual_areaxdy,dual_areaxdz,          &
                              dual_areaydx,dual_areaydy,dual_areaydz,          &
                              dual_areazdx,dual_areazdy,dual_areazdz)

          xnorm_qdx(:,:) = 0.0_dp
          xnorm_qdy(:,:) = 0.0_dp
          xnorm_qdz(:,:) = 0.0_dp

          ynorm_qdx(:,:) = 0.0_dp
          ynorm_qdy(:,:) = 0.0_dp
          ynorm_qdz(:,:) = 0.0_dp

          znorm_qdx(:,:) = 0.0_dp
          znorm_qdy(:,:) = 0.0_dp
          znorm_qdz(:,:) = 0.0_dp

          do k = 1, elem(ielem)%node_per_cell

            if ( bnode1 == c2n_cell(k) ) then
              xnorm_qdx(:,k) = dual_areaxdx(:,1)
              xnorm_qdy(:,k) = dual_areaxdy(:,1)
              xnorm_qdz(:,k) = dual_areaxdz(:,1)

              ynorm_qdx(:,k) = dual_areaydx(:,1)
              ynorm_qdy(:,k) = dual_areaydy(:,1)
              ynorm_qdz(:,k) = dual_areaydz(:,1)

              znorm_qdx(:,k) = dual_areazdx(:,1)
              znorm_qdy(:,k) = dual_areazdy(:,1)
              znorm_qdz(:,k) = dual_areazdz(:,1)
            endif

            if ( bnode2 == c2n_cell(k) ) then
              xnorm_qdx(:,k) = dual_areaxdx(:,2)
              xnorm_qdy(:,k) = dual_areaxdy(:,2)
              xnorm_qdz(:,k) = dual_areaxdz(:,2)

              ynorm_qdx(:,k) = dual_areaydx(:,2)
              ynorm_qdy(:,k) = dual_areaydy(:,2)
              ynorm_qdz(:,k) = dual_areaydz(:,2)

              znorm_qdx(:,k) = dual_areazdx(:,2)
              znorm_qdy(:,k) = dual_areazdy(:,2)
              znorm_qdz(:,k) = dual_areazdz(:,2)
            endif

            if ( bnode3 == c2n_cell(k) ) then
              xnorm_qdx(:,k) = dual_areaxdx(:,3)
              xnorm_qdy(:,k) = dual_areaxdy(:,3)
              xnorm_qdz(:,k) = dual_areaxdz(:,3)

              ynorm_qdx(:,k) = dual_areaydx(:,3)
              ynorm_qdy(:,k) = dual_areaydy(:,3)
              ynorm_qdz(:,k) = dual_areaydz(:,3)

              znorm_qdx(:,k) = dual_areazdx(:,3)
              znorm_qdy(:,k) = dual_areazdy(:,3)
              znorm_qdz(:,k) = dual_areazdz(:,3)
            endif

            if ( bnode4 == c2n_cell(k) ) then
              xnorm_qdx(:,k) = dual_areaxdx(:,4)
              xnorm_qdy(:,k) = dual_areaxdy(:,4)
              xnorm_qdz(:,k) = dual_areaxdz(:,4)

              ynorm_qdx(:,k) = dual_areaydx(:,4)
              ynorm_qdy(:,k) = dual_areaydy(:,4)
              ynorm_qdz(:,k) = dual_areaydz(:,4)

              znorm_qdx(:,k) = dual_areazdx(:,4)
              znorm_qdy(:,k) = dual_areazdy(:,4)
              znorm_qdz(:,k) = dual_areazdz(:,4)
            endif

          end do

          nl = 4

          xn(1:4) = xnorm_q(1:4)
            xndx(1:4,:) = xnorm_qdx(1:4,:)
            xndy(1:4,:) = xnorm_qdy(1:4,:)
            xndz(1:4,:) = xnorm_qdz(1:4,:)

          yn(1:4) = ynorm_q(1:4)
            yndx(1:4,:) = ynorm_qdx(1:4,:)
            yndy(1:4,:) = ynorm_qdy(1:4,:)
            yndz(1:4,:) = ynorm_qdz(1:4,:)

          zn(1:4) = znorm_q(1:4)
            zndx(1:4,:) = znorm_qdx(1:4,:)
            zndy(1:4,:) = znorm_qdy(1:4,:)
            zndz(1:4,:) = znorm_qdz(1:4,:)

        end if

! Viscous contributions at dual face [ full Navier-Stokes terms ]

!       components of symmetric stress tensor

        txx = rmu*(c43*ux - c23*vy - c23*wz)
          txxdx(:) = rmu*(c43*uxdx(:) - c23*vydx(:) - c23*wzdx(:))
          txxdy(:) = rmu*(c43*uxdy(:) - c23*vydy(:) - c23*wzdy(:))
          txxdz(:) = rmu*(c43*uxdz(:) - c23*vydz(:) - c23*wzdz(:))

        txy = rmu*(uy + vx)
          txydx(:) = rmu*(uydx(:) + vxdx(:))
          txydy(:) = rmu*(uydy(:) + vxdy(:))
          txydz(:) = rmu*(uydz(:) + vxdz(:))

        txz = rmu*(uz + wx)
          txzdx(:) = rmu*(uzdx(:) + wxdx(:))
          txzdy(:) = rmu*(uzdy(:) + wxdy(:))
          txzdz(:) = rmu*(uzdz(:) + wxdz(:))

        tyy = rmu*(c43*vy - c23*ux - c23*wz)
          tyydx(:) = rmu*(c43*vydx(:) - c23*uxdx(:) - c23*wzdx(:))
          tyydy(:) = rmu*(c43*vydy(:) - c23*uxdy(:) - c23*wzdy(:))
          tyydz(:) = rmu*(c43*vydz(:) - c23*uxdz(:) - c23*wzdz(:))

        tyz = rmu*(vz + wy)
          tyzdx(:) = rmu*(vzdx(:) + wydx(:))
          tyzdy(:) = rmu*(vzdy(:) + wydy(:))
          tyzdz(:) = rmu*(vzdz(:) + wydz(:))

        tzz = rmu*(c43*wz - c23*ux - c23*vy)
          tzzdx(:) = rmu*(c43*wzdx(:) - c23*uxdx(:) - c23*vydx(:))
          tzzdy(:) = rmu*(c43*wzdy(:) - c23*uxdy(:) - c23*vydy(:))
          tzzdz(:) = rmu*(c43*wzdz(:) - c23*uxdz(:) - c23*vydz(:))

        tqx = eta(1)*tx
          tqxdx(:) = eta(1)*txdx(:)
          tqxdy(:) = eta(1)*txdy(:)
          tqxdz(:) = eta(1)*txdz(:)
        tqy = eta(1)*ty
          tqydx(:) = eta(1)*tydx(:)
          tqydy(:) = eta(1)*tydy(:)
          tqydz(:) = eta(1)*tydz(:)
        tqz = eta(1)*tz
          tqzdx(:) = eta(1)*tzdx(:)
          tqzdy(:) = eta(1)*tzdy(:)
          tqzdz(:) = eta(1)*tzdz(:)

!       [nondimensionalization factor xmr ] * [ viscosity ]
!       [ unit normal and area ] at dual face

!       vf2(1:nl) = xmr*(xn(1:nl)*txx + yn(1:nl)*txy + zn(1:nl)*txz)
          do k = 1, nl
            vf2dx(k,:) = xmr*(xn(k)*txxdx(:) + yn(k)*txydx(:) + zn(k)*txzdx(:) &
                              + txx*xndx(k,:)  + txy*yndx(k,:)  + txz*zndx(k,:))
            vf2dy(k,:) = xmr*(xn(k)*txxdy(:) + yn(k)*txydy(:) + zn(k)*txzdy(:) &
                              + txx*xndy(k,:)  + txy*yndy(k,:)  + txz*zndy(k,:))
            vf2dz(k,:) = xmr*(xn(k)*txxdz(:) + yn(k)*txydz(:) + zn(k)*txzdz(:) &
                              + txx*xndz(k,:)  + txy*yndz(k,:)  + txz*zndz(k,:))
          end do

!       vf3(1:nl) = xmr*(xn(1:nl)*txy + yn(1:nl)*tyy + zn(1:nl)*tyz)
          do k = 1, nl
            vf3dx(k,:) = xmr*(xn(k)*txydx(:) + yn(k)*tyydx(:) + zn(k)*tyzdx(:) &
                              + txy*xndx(k,:)  + tyy*yndx(k,:)  + tyz*zndx(k,:))
            vf3dy(k,:) = xmr*(xn(k)*txydy(:) + yn(k)*tyydy(:) + zn(k)*tyzdy(:) &
                              + txy*xndy(k,:)  + tyy*yndy(k,:)  + tyz*zndy(k,:))
            vf3dz(k,:) = xmr*(xn(k)*txydz(:) + yn(k)*tyydz(:) + zn(k)*tyzdz(:) &
                              + txy*xndz(k,:)  + tyy*yndz(k,:)  + tyz*zndz(k,:))
          end do

!       vf4(1:nl) = xmr*(xn(1:nl)*txz + yn(1:nl)*tyz + zn(1:nl)*tzz)
          do k = 1, nl
            vf4dx(k,:) = xmr*(xn(k)*txzdx(:) + yn(k)*tyzdx(:) + zn(k)*tzzdx(:) &
                              + txz*xndx(k,:)  + tyz*yndx(k,:)  + tzz*zndx(k,:))
            vf4dy(k,:) = xmr*(xn(k)*txzdy(:) + yn(k)*tyzdy(:) + zn(k)*tzzdy(:) &
                              + txz*xndy(k,:)  + tyz*yndy(k,:)  + tzz*zndy(k,:))
            vf4dz(k,:) = xmr*(xn(k)*txzdz(:) + yn(k)*tyzdz(:) + zn(k)*tzzdz(:) &
                              + txz*xndz(k,:)  + tyz*yndz(k,:)  + tzz*zndz(k,:))
          end do

!       vf5(1:nl) = xmr*(xn(1:nl)*tqx + yn(1:nl)*tqy + zn(1:nl)*tqz)           &
!                 + (u*vf2(1:nl) + v*vf3(1:nl) + w*vf4(1:nl))
          do k = 1, nl
            vf5dx(k,:) = xmr*(xn(k)*tqxdx(:) + yn(k)*tqydx(:) + zn(k)*tqzdx(:) &
                           + tqx*xndx(k,:)  + tqy*yndx(k,:)  + tqz*zndx(k,:))  &
                          + (u*vf2dx(k,:) + v*vf3dx(k,:) + w*vf4dx(k,:))
            vf5dy(k,:) = xmr*(xn(k)*tqxdy(:) + yn(k)*tqydy(:) + zn(k)*tqzdy(:) &
                           + tqx*xndy(k,:)  + tqy*yndy(k,:)  + tqz*zndy(k,:))  &
                          + (u*vf2dy(k,:) + v*vf3dy(k,:) + w*vf4dy(k,:))
            vf5dz(k,:) = xmr*(xn(k)*tqxdz(:) + yn(k)*tqydz(:) + zn(k)*tqzdz(:) &
                           + tqx*xndz(k,:)  + tqy*yndz(k,:)  + tqz*zndz(k,:))  &
                          + (u*vf2dz(k,:) + v*vf3dz(k,:) + w*vf4dz(k,:))
          end do

        in1_loop : do in1 = 1, nl

          if (in1 == 1) then
            bnode = bnode1
          else if (in1 == 2) then
            bnode = bnode2
          else if (in1 == 3) then
            bnode = bnode3
          else if (in1 == 4) then
            bnode = bnode4
          end if

          if (twod) then
             if (abs(y(bnode)-yplane_2d) > y_coplanar_tol) cycle
          end if

!         res2 = -vf2(in1)
            res2dx(:) = -vf2dx(in1,:)
            res2dy(:) = -vf2dy(in1,:)
            res2dz(:) = -vf2dz(in1,:)

!         res3 = -vf3(in1)
            res3dx(:) = -vf3dx(in1,:)
            res3dy(:) = -vf3dy(in1,:)
            res3dz(:) = -vf3dz(in1,:)

!         res4 = -vf4(in1)
            res4dx(:) = -vf4dx(in1,:)
            res4dy(:) = -vf4dy(in1,:)
            res4dz(:) = -vf4dz(in1,:)

!         res5 = -vf5(in1)
            res5dx(:) = -vf5dx(in1,:)
            res5dy(:) = -vf5dy(in1,:)
            res5dz(:) = -vf5dz(in1,:)

!         if (bnode <= nnodes0) then
!           res(n_momx,bnode) = res(n_momx,bnode) - vf2(in1)
!           res(n_momy,bnode) = res(n_momy,bnode) - vf3(in1)
!           res(n_momz,bnode) = res(n_momz,bnode) - vf4(in1)
!           res(n_etot,bnode) = res(n_etot,bnode) - vf5(in1)
!         end if

! finally add up all the pieces

          fcn_loop : do k = 1, nfunctions

            lambda(:) = coltag(:,bnode)*rlam(:,bnode,k)

! place the derivatives of the residual at bnode

            if ( bnode <= nnodes0 ) then
              do j = 1, elem(ielem)%node_per_cell
                node = c2n_cell(j)
                drdxl(1,node,k,1) = drdxl(1,node,k,1) + res2dx(j)*lambda(2)
                drdxl(1,node,k,1) = drdxl(1,node,k,1) + res3dx(j)*lambda(3)
                drdxl(1,node,k,1) = drdxl(1,node,k,1) + res4dx(j)*lambda(4)
                drdxl(1,node,k,1) = drdxl(1,node,k,1) + res5dx(j)*lambda(5)

                drdxl(2,node,k,1) = drdxl(2,node,k,1) + res2dy(j)*lambda(2)
                drdxl(2,node,k,1) = drdxl(2,node,k,1) + res3dy(j)*lambda(3)
                drdxl(2,node,k,1) = drdxl(2,node,k,1) + res4dy(j)*lambda(4)
                drdxl(2,node,k,1) = drdxl(2,node,k,1) + res5dy(j)*lambda(5)

                drdxl(3,node,k,1) = drdxl(3,node,k,1) + res2dz(j)*lambda(2)
                drdxl(3,node,k,1) = drdxl(3,node,k,1) + res3dz(j)*lambda(3)
                drdxl(3,node,k,1) = drdxl(3,node,k,1) + res4dz(j)*lambda(4)
                drdxl(3,node,k,1) = drdxl(3,node,k,1) + res5dz(j)*lambda(5)

                if ( bnode == rn .and. node == np ) then
                  pointsum(2,1) = pointsum(2,1) + res2dx(j)*coltag(2,bnode)
                  pointsum(3,1) = pointsum(3,1) + res3dx(j)*coltag(3,bnode)
                  pointsum(4,1) = pointsum(4,1) + res4dx(j)*coltag(4,bnode)
                  pointsum(5,1) = pointsum(5,1) + res5dx(j)*coltag(5,bnode)

                  pointsum(2,2) = pointsum(2,2) + res2dy(j)*coltag(2,bnode)
                  pointsum(3,2) = pointsum(3,2) + res3dy(j)*coltag(3,bnode)
                  pointsum(4,2) = pointsum(4,2) + res4dy(j)*coltag(4,bnode)
                  pointsum(5,2) = pointsum(5,2) + res5dy(j)*coltag(5,bnode)

                  pointsum(2,3) = pointsum(2,3) + res2dz(j)*coltag(2,bnode)
                  pointsum(3,3) = pointsum(3,3) + res3dz(j)*coltag(3,bnode)
                  pointsum(4,3) = pointsum(4,3) + res4dz(j)*coltag(4,bnode)
                  pointsum(5,3) = pointsum(5,3) + res5dz(j)*coltag(5,bnode)
                endif

              end do
            endif

          end do fcn_loop

        end do in1_loop

       end do loop_faces

      end do

    end if close_off_viscous

  end subroutine bc_drdgeomv_mix


!================================ DRDGEOMV_MIX_I =============================80
!
! This routine computes the full viscous fluxes for general (mixed) elements
! using a cell-based formulation to compute gradients. It is the generalization
! of the original tetrahedral formulation to more general elements.
!
! For non-tetrahedral cells, the cell-based gradients can be augmented with
! edge gradients to enhance h-ellipticity.
!
! Note: qnode assumed to contain primitive variables
!
! Optimized version of visrhs_mix_cell but does not contain all of the bells
! and whistles of that routine
!
!=============================================================================80
  subroutine drdgeomv_mix_i(nnodes0, nnodes01, ncell, c2n, x, y, z, qnode,     &
                            amut, local_f2n, local_e2n, chk_norm,              &
                            face_per_cell, node_per_cell, edge_per_cell, n_tot,&
                            ndim, adim, nfunctions, ntp, rlam, drdxl, coltag,  &
                            type_cell)

    use info_depr,       only : re, ivgrd
    use element_defs,    only : max_node_per_cell, max_face_per_cell
    use generic_gas_map, only : n_momx, n_momy, n_momz
    use kinddefs,        only : dp
    use debug_defs,      only : gradient_construction_rhs
    use lmpi,            only : lmpi_die

    integer, intent(in) :: n_tot
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: ndim, adim, nfunctions, ntp

    integer, dimension(node_per_cell,ncell),         intent(in) :: c2n
    integer, dimension(face_per_cell,4),             intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),             intent(in) :: local_e2n
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp), dimension(nnodes01),       intent(in) :: x,y,z
    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode
    real(dp), dimension(nnodes01),       intent(in) :: amut
    real(dp), dimension(adim,nnodes01),  intent(in) :: coltag
    real(dp), dimension(3,nnodes01,nfunctions,ntp), intent(inout) :: drdxl
    real(dp), dimension(adim,nnodes01,nfunctions),  intent(in)    :: rlam

    character(len=*), intent(in) :: type_cell

    integer :: n, j, iface, nn1, nn2, nn3, nn4
    integer :: ie, i, eqn
    integer :: n1_loc, n2_loc, node, n3_loc, n4_loc, n5_loc, n6_loc
    integer :: n1, n2, big_angle
    integer :: eqn0, eqn1, k

    real(dp) :: dot, xavg, yavg, zavg, termx, termy, termz
    real(dp) :: rmu, qavg, xavg1, yavg1, zavg1, rei
    real(dp) :: ux, uy, uz, vx, vy, vz, wx, wy, wz
    real(dp) :: uxavg, uyavg, uzavg, vxavg, vyavg, vzavg
    real(dp) :: wxavg, wyavg, wzavg
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz, qavg1, qavg2, xavg2, yavg2, zavg2
    real(dp) :: xc, yc, zc, cell_vol, fact, term1, term2
    real(dp) :: ex, ey, ez, disi, nx, ny, nz, nx1, ny1, nz1, nx2, ny2, nz2
    real(dp) :: termx1, termy1, termz1, ex_new, ey_new, ez_new
    real(dp) :: u1, u2, v1, v2, w1, w2, termx2, termy2, termz2
    real(dp) :: egradu, egradv, egradw, cell_vol_inv
    real(dp) :: gradu_xi, gradv_xi, gradw_xi
    real(dp) :: txx, txy, txz, tyy, tyz, tzz, rax, ray, raz
    real(dp) :: stuff
    real(dp) :: mu_node
    real(dp) :: areai, xnf, ynf, znf, e_dot_n
    real(dp) :: augment_weight1, augment_weight2, augment_weight3
    real(dp) :: augment_weight1_new, augment_weight2_new, augment_weight3_new

    real(dp), dimension(max_face_per_cell)      :: nxf, nyf, nzf
    real(dp), dimension(max_node_per_cell)      :: x_node, y_node, z_node
    real(dp), dimension(ndim,max_node_per_cell) :: q_node
    real(dp), dimension(ndim)                   :: gradx_cell, grady_cell
    real(dp), dimension(ndim)                   :: gradz_cell
    real(dp), dimension(ndim)                   :: gradx_cell_new,grady_cell_new
    real(dp), dimension(ndim)                   :: gradz_cell_new

    real(dp), dimension(max_node_per_cell) :: xcdx, ycdy, zcdz
    real(dp), dimension(max_node_per_cell) :: nxdx, nxdy, nxdz
    real(dp), dimension(max_node_per_cell) :: nydx, nydy, nydz
    real(dp), dimension(max_node_per_cell) :: nzdx, nzdy, nzdz
    real(dp), dimension(max_node_per_cell) :: nx1dx, nx1dy, nx1dz
    real(dp), dimension(max_node_per_cell) :: ny1dx, ny1dy, ny1dz
    real(dp), dimension(max_node_per_cell) :: nz1dx, nz1dy, nz1dz
    real(dp), dimension(max_node_per_cell) :: nx2dx, nx2dy, nx2dz
    real(dp), dimension(max_node_per_cell) :: ny2dx, ny2dy, ny2dz
    real(dp), dimension(max_node_per_cell) :: nz2dx, nz2dy, nz2dz
    real(dp), dimension(max_node_per_cell) :: xavgdx, yavgdy, zavgdz
    real(dp), dimension(max_node_per_cell) :: xavg1dx, yavg1dy, zavg1dz
    real(dp), dimension(max_node_per_cell) :: xavg2dx, yavg2dy, zavg2dz
    real(dp), dimension(max_node_per_cell) :: cell_voldx, cell_voldy, cell_voldz
    real(dp), dimension(max_node_per_cell) :: termxdx, termxdy, termxdz
    real(dp), dimension(max_node_per_cell) :: termydx, termydy, termydz
    real(dp), dimension(max_node_per_cell) :: termzdx, termzdy, termzdz
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdz
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdz
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdz
    real(dp), dimension(max_node_per_cell) :: term1dx, term1dy, term1dz
    real(dp), dimension(max_node_per_cell) :: term2dx, term2dy, term2dz
    real(dp), dimension(max_node_per_cell) :: termx1dx, termx1dy, termx1dz
    real(dp), dimension(max_node_per_cell) :: termy1dx, termy1dy, termy1dz
    real(dp), dimension(max_node_per_cell) :: termz1dx, termz1dy, termz1dz
    real(dp), dimension(max_node_per_cell) :: termx2dx, termx2dy, termx2dz
    real(dp), dimension(max_node_per_cell) :: termy2dx, termy2dy, termy2dz
    real(dp), dimension(max_node_per_cell) :: termz2dx, termz2dy, termz2dz
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdx
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdy
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdz
    real(dp), dimension(max_node_per_cell) :: uxavgdx, uxavgdy, uxavgdz
    real(dp), dimension(max_node_per_cell) :: uyavgdx, uyavgdy, uyavgdz
    real(dp), dimension(max_node_per_cell) :: uzavgdx, uzavgdy, uzavgdz
    real(dp), dimension(max_node_per_cell) :: vxavgdx, vxavgdy, vxavgdz
    real(dp), dimension(max_node_per_cell) :: vyavgdx, vyavgdy, vyavgdz
    real(dp), dimension(max_node_per_cell) :: vzavgdx, vzavgdy, vzavgdz
    real(dp), dimension(max_node_per_cell) :: wxavgdx, wxavgdy, wxavgdz
    real(dp), dimension(max_node_per_cell) :: wyavgdx, wyavgdy, wyavgdz
    real(dp), dimension(max_node_per_cell) :: wzavgdx, wzavgdy, wzavgdz
    real(dp), dimension(max_node_per_cell) :: xmdx, ymdy, zmdz
    real(dp), dimension(max_node_per_cell) :: xldx, yldy, zldz
    real(dp), dimension(max_node_per_cell) :: xrdx, yrdy, zrdz
    real(dp), dimension(max_node_per_cell) :: areaxdx, areaxdy, areaxdz
    real(dp), dimension(max_node_per_cell) :: areaydx, areaydy, areaydz
    real(dp), dimension(max_node_per_cell) :: areazdx, areazdy, areazdz
    real(dp), dimension(max_node_per_cell) :: exdx
    real(dp), dimension(max_node_per_cell) :: eydy
    real(dp), dimension(max_node_per_cell) :: ezdz
    real(dp), dimension(max_node_per_cell) :: ex_newdx, ex_newdy, ex_newdz
    real(dp), dimension(max_node_per_cell) :: ey_newdx, ey_newdy, ey_newdz
    real(dp), dimension(max_node_per_cell) :: ez_newdx, ez_newdy, ez_newdz
    real(dp), dimension(max_node_per_cell) :: areaidx, areaidy, areaidz
    real(dp), dimension(max_node_per_cell) :: xnfdx, xnfdy, xnfdz
    real(dp), dimension(max_node_per_cell) :: ynfdx, ynfdy, ynfdz
    real(dp), dimension(max_node_per_cell) :: znfdx, znfdy, znfdz
    real(dp), dimension(max_node_per_cell) :: e_dot_ndx, e_dot_ndy, e_dot_ndz
    real(dp), dimension(max_node_per_cell) :: augment_weight1dx
    real(dp), dimension(max_node_per_cell) :: augment_weight1dy
    real(dp), dimension(max_node_per_cell) :: augment_weight1dz
    real(dp), dimension(max_node_per_cell) :: augment_weight2dx
    real(dp), dimension(max_node_per_cell) :: augment_weight2dy
    real(dp), dimension(max_node_per_cell) :: augment_weight2dz
    real(dp), dimension(max_node_per_cell) :: augment_weight3dx
    real(dp), dimension(max_node_per_cell) :: augment_weight3dy
    real(dp), dimension(max_node_per_cell) :: augment_weight3dz
    real(dp), dimension(max_node_per_cell) :: augment_weight1_newdx
    real(dp), dimension(max_node_per_cell) :: augment_weight1_newdy
    real(dp), dimension(max_node_per_cell) :: augment_weight1_newdz
    real(dp), dimension(max_node_per_cell) :: augment_weight2_newdx
    real(dp), dimension(max_node_per_cell) :: augment_weight2_newdy
    real(dp), dimension(max_node_per_cell) :: augment_weight2_newdz
    real(dp), dimension(max_node_per_cell) :: augment_weight3_newdx
    real(dp), dimension(max_node_per_cell) :: augment_weight3_newdy
    real(dp), dimension(max_node_per_cell) :: augment_weight3_newdz
    real(dp), dimension(max_node_per_cell) :: disidx, disidy, disidz
    real(dp), dimension(max_node_per_cell) :: egradudx, egradudy, egradudz
    real(dp), dimension(max_node_per_cell) :: egradvdx, egradvdy, egradvdz
    real(dp), dimension(max_node_per_cell) :: egradwdx, egradwdy, egradwdz
    real(dp), dimension(max_node_per_cell) :: gradu_xidx, gradu_xidy, gradu_xidz
    real(dp), dimension(max_node_per_cell) :: gradv_xidx, gradv_xidy, gradv_xidz
    real(dp), dimension(max_node_per_cell) :: gradw_xidx, gradw_xidy, gradw_xidz
    real(dp), dimension(max_node_per_cell) :: uxdx, uxdy, uxdz
    real(dp), dimension(max_node_per_cell) :: vxdx, vxdy, vxdz
    real(dp), dimension(max_node_per_cell) :: wxdx, wxdy, wxdz
    real(dp), dimension(max_node_per_cell) :: uydx, uydy, uydz
    real(dp), dimension(max_node_per_cell) :: vydx, vydy, vydz
    real(dp), dimension(max_node_per_cell) :: wydx, wydy, wydz
    real(dp), dimension(max_node_per_cell) :: uzdx, uzdy, uzdz
    real(dp), dimension(max_node_per_cell) :: vzdx, vzdy, vzdz
    real(dp), dimension(max_node_per_cell) :: wzdx, wzdy, wzdz
    real(dp), dimension(max_node_per_cell) :: txxdx, txxdy, txxdz
    real(dp), dimension(max_node_per_cell) :: txydx, txydy, txydz
    real(dp), dimension(max_node_per_cell) :: txzdx, txzdy, txzdz
    real(dp), dimension(max_node_per_cell) :: tyydx, tyydy, tyydz
    real(dp), dimension(max_node_per_cell) :: tyzdx, tyzdy, tyzdz
    real(dp), dimension(max_node_per_cell) :: tzzdx, tzzdy, tzzdz
    real(dp), dimension(max_node_per_cell) :: raxdx, raxdy, raxdz
    real(dp), dimension(max_node_per_cell) :: raydx, raydy, raydz
    real(dp), dimension(max_node_per_cell) :: razdx, razdy, razdz
    real(dp), dimension(max_node_per_cell) :: vf2dx, vf2dy, vf2dz
    real(dp), dimension(max_node_per_cell) :: vf3dx, vf3dy, vf3dz
    real(dp), dimension(max_node_per_cell) :: vf4dx, vf4dy, vf4dz
    real(dp), dimension(max_node_per_cell) :: res12dx, res12dy, res12dz
    real(dp), dimension(max_node_per_cell) :: res13dx, res13dy, res13dz
    real(dp), dimension(max_node_per_cell) :: res14dx, res14dy, res14dz
    real(dp), dimension(max_node_per_cell) :: res22dx, res22dy, res22dz
    real(dp), dimension(max_node_per_cell) :: res23dx, res23dy, res23dz
    real(dp), dimension(max_node_per_cell) :: res24dx, res24dy, res24dz
    real(dp), dimension(adim) :: lambda1, lambda2

    real(dp), parameter :: my_0     = 0.0_dp
    real(dp), parameter :: my_half  = 0.50_dp
    real(dp), parameter :: my_mxd  = 0.99939_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_18th = my_1/18.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp
    real(dp), parameter :: my_6th  = 1.0_dp/6.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp

  continue

    augment_weight1 = 0.0_dp
    augment_weight2 = 0.0_dp
    augment_weight3 = 0.0_dp

    fact  = 1.0_dp / real(node_per_cell, dp)
    rei   = 1.0_dp/re
    eqn0  = 2
    eqn1  = 4

! Loop over the cells

    cell_loop: do n = 1, ncell

! Initialization

      rmu = 0.0_dp

      xc  = 0.0_dp
      yc  = 0.0_dp
      zc  = 0.0_dp

! Compute cell averages, cell center, and set up some local solution arrays

      node_loop : do i = 1, node_per_cell

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        xc  =  xc + x_node(i)
        yc  =  yc + y_node(i)
        zc  =  zc + z_node(i)

        q_node(1:ndim,i) = qnode(1:ndim,node)
        mu_node = 1.0_dp + amut(node)
        rmu = rmu + mu_node

      end do node_loop

!     get cell averages by dividing by the number of nodes that contributed

      rmu    = rmu*fact
      xc     =  xc*fact
      yc     =  yc*fact
      zc     =  zc*fact

      xcdx(:) = fact
      ycdy(:) = fact
      zcdz(:) = fact

! Get the gradients in the primal cell via Green-Gauss

      cell_vol = 0.0_dp
        cell_voldx = 0.0_dp
        cell_voldy = 0.0_dp
        cell_voldz = 0.0_dp

      gradx_cell(:) = my_0
        gradx_celldx(:,:) = my_0
        gradx_celldy(:,:) = my_0
        gradx_celldz(:,:) = my_0
      grady_cell(:) = my_0
        grady_celldx(:,:) = my_0
        grady_celldy(:,:) = my_0
        grady_celldz(:,:) = my_0
      gradz_cell(:) = my_0
        gradz_celldx(:,:) = my_0
        gradz_celldy(:,:) = my_0
        gradz_celldz(:,:) = my_0

      threed_faces : do iface = 1, face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        nxdx = 0.0_dp
        nxdy = 0.0_dp
        nxdz = 0.0_dp
        nx1dx = 0.0_dp
        nx1dy = 0.0_dp
        nx1dz = 0.0_dp
        nx2dx = 0.0_dp
        nx2dy = 0.0_dp
        nx2dz = 0.0_dp

        nydx = 0.0_dp
        nydy = 0.0_dp
        nydz = 0.0_dp
        ny1dx = 0.0_dp
        ny1dy = 0.0_dp
        ny1dz = 0.0_dp
        ny2dx = 0.0_dp
        ny2dy = 0.0_dp
        ny2dz = 0.0_dp

        nzdx = 0.0_dp
        nzdy = 0.0_dp
        nzdz = 0.0_dp
        nz1dx = 0.0_dp
        nz1dy = 0.0_dp
        nz1dz = 0.0_dp
        nz2dx = 0.0_dp
        nz2dy = 0.0_dp
        nz2dz = 0.0_dp

        if (nn4 == nn1) then

!         triangular faces of the cell

!         face normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))         &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

            nxdy(nn1)=-(z_node(nn3) - z_node(nn1)) + (z_node(nn2) - z_node(nn1))
            nxdy(nn2)=  z_node(nn3) - z_node(nn1)
            nxdy(nn3)=-(z_node(nn2) - z_node(nn1))

            nxdz(nn1)=-(y_node(nn2) - y_node(nn1)) + (y_node(nn3) - y_node(nn1))
            nxdz(nn2)=-(y_node(nn3) - y_node(nn1))
            nxdz(nn3)= (y_node(nn2) - y_node(nn1))

          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))         &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

            nydx(nn1)=-(z_node(nn2) - z_node(nn1)) + (z_node(nn3) - z_node(nn1))
            nydx(nn2)=-(z_node(nn3) - z_node(nn1))
            nydx(nn3)= (z_node(nn2) - z_node(nn1))

            nydz(nn1)=-(x_node(nn3) - x_node(nn1)) + (x_node(nn2) - x_node(nn1))
            nydz(nn2)= (x_node(nn3) - x_node(nn1))
            nydz(nn3)=-(x_node(nn2) - x_node(nn1))

          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))         &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

            nzdx(nn1)=-(y_node(nn3) - y_node(nn1)) + (y_node(nn2) - y_node(nn1))
            nzdx(nn2)= (y_node(nn3) - y_node(nn1))
            nzdx(nn3)=-(y_node(nn2) - y_node(nn1))

            nzdy(nn1)=-(x_node(nn2) - x_node(nn1)) + (x_node(nn3) - x_node(nn1))
            nzdy(nn2)=-(x_node(nn3) - x_node(nn1))
            nzdy(nn3)= (x_node(nn2) - x_node(nn1))

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavgdx = 0.0_dp
          yavgdy = 0.0_dp
          zavgdz = 0.0_dp

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)

            xavgdx(nn1) = 1.0_dp
            xavgdx(nn2) = 1.0_dp
            xavgdx(nn3) = 1.0_dp

          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
            yavgdy(nn1) = 1.0_dp
            yavgdy(nn2) = 1.0_dp
            yavgdy(nn3) = 1.0_dp

          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)
            zavgdz(nn1) = 1.0_dp
            zavgdz(nn2) = 1.0_dp
            zavgdz(nn3) = 1.0_dp

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th
            cell_voldx(:) = cell_voldx(:) + (xavg*nxdx(:) + yavg*nydx(:)       &
                          + zavg*nzdx(:) + xavgdx(:)*nx)*my_18th
            cell_voldy(:) = cell_voldy(:) + (xavg*nxdy(:) + yavg*nydy(:)       &
                          + zavg*nzdy(:) + yavgdy(:)*ny)*my_18th
            cell_voldz(:) = cell_voldz(:) + (xavg*nxdz(:) + yavg*nydz(:)       &
                          + zavg*nzdz(:) + zavgdz(:)*nz)*my_18th

          termx = nx*my_6th
            termxdx(:) = nxdx(:)*my_6th
            termxdy(:) = nxdy(:)*my_6th
            termxdz(:) = nxdz(:)*my_6th
          termy = ny*my_6th
            termydx(:) = nydx(:)*my_6th
            termydy(:) = nydy(:)*my_6th
            termydz(:) = nydz(:)*my_6th
          termz = nz*my_6th
            termzdx(:) = nzdx(:)*my_6th
            termzdy(:) = nzdy(:)*my_6th
            termzdz(:) = nzdz(:)*my_6th

!         gradient contributions

          do eqn = eqn0, eqn1
            qavg = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
            gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
              gradx_celldx(eqn,:) = gradx_celldx(eqn,:) + termxdx(:)*qavg
              gradx_celldy(eqn,:) = gradx_celldy(eqn,:) + termxdy(:)*qavg
              gradx_celldz(eqn,:) = gradx_celldz(eqn,:) + termxdz(:)*qavg
            grady_cell(eqn) = grady_cell(eqn) + termy*qavg
              grady_celldx(eqn,:) = grady_celldx(eqn,:) + termydx(:)*qavg
              grady_celldy(eqn,:) = grady_celldy(eqn,:) + termydy(:)*qavg
              grady_celldz(eqn,:) = grady_celldz(eqn,:) + termydz(:)*qavg
            gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg
              gradz_celldx(eqn,:) = gradz_celldx(eqn,:) + termzdx(:)*qavg
              gradz_celldy(eqn,:) = gradz_celldy(eqn,:) + termzdy(:)*qavg
              gradz_celldz(eqn,:) = gradz_celldz(eqn,:) + termzdz(:)*qavg
          end do

        else

!         quadrilateral faces of the cell

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg1dx = 0.0_dp
          yavg1dy = 0.0_dp
          zavg1dz = 0.0_dp

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
            xavg1dx(nn1) = 1.0_dp
            xavg1dx(nn2) = 1.0_dp
            xavg1dx(nn3) = 1.0_dp
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
            yavg1dy(nn1) = 1.0_dp
            yavg1dy(nn2) = 1.0_dp
            yavg1dy(nn3) = 1.0_dp
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)
            zavg1dz(nn1) = 1.0_dp
            zavg1dz(nn2) = 1.0_dp
            zavg1dz(nn3) = 1.0_dp

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))        &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

            nx1dy(nn1)=-(z_node(nn3)- z_node(nn1)) + (z_node(nn2) - z_node(nn1))
            nx1dy(nn2)=  z_node(nn3)- z_node(nn1)
            nx1dy(nn3)=-(z_node(nn2)- z_node(nn1))

            nx1dz(nn1)=-(y_node(nn2)- y_node(nn1)) + (y_node(nn3) - y_node(nn1))
            nx1dz(nn2)=-(y_node(nn3)- y_node(nn1))
            nx1dz(nn3)= (y_node(nn2)- y_node(nn1))

          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))        &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

            ny1dx(nn1)=-(z_node(nn2)- z_node(nn1)) + (z_node(nn3) - z_node(nn1))
            ny1dx(nn2)=-(z_node(nn3)- z_node(nn1))
            ny1dx(nn3)= (z_node(nn2)- z_node(nn1))

            ny1dz(nn1)=-(x_node(nn3)- x_node(nn1)) + (x_node(nn2) - x_node(nn1))
            ny1dz(nn2)= (x_node(nn3)- x_node(nn1))
            ny1dz(nn3)=-(x_node(nn2)- x_node(nn1))

          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))        &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

            nz1dx(nn1)=-(y_node(nn3)- y_node(nn1)) + (y_node(nn2) - y_node(nn1))
            nz1dx(nn2)= (y_node(nn3)- y_node(nn1))
            nz1dx(nn3)=-(y_node(nn2)- y_node(nn1))

            nz1dy(nn1)=-(x_node(nn2)- x_node(nn1)) + (x_node(nn3) - x_node(nn1))
            nz1dy(nn2)=-(x_node(nn3)- x_node(nn1))
            nz1dy(nn3)= (x_node(nn2)- x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1
            term1dx(:) = xavg1*nx1dx(:) + yavg1*ny1dx(:) + zavg1*nz1dx(:) +    &
                         xavg1dx(:)*nx1
            term1dy(:) = xavg1*nx1dy(:) + yavg1*ny1dy(:) + zavg1*nz1dy(:) +    &
                         yavg1dy(:)*ny1
            term1dz(:) = xavg1*nx1dz(:) + yavg1*ny1dz(:) + zavg1*nz1dz(:) +    &
                         zavg1dz(:)*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg2dx = 0.0_dp
          yavg2dy = 0.0_dp
          zavg2dz = 0.0_dp

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
            xavg2dx(nn1) = 1.0_dp
            xavg2dx(nn3) = 1.0_dp
            xavg2dx(nn4) = 1.0_dp
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
            yavg2dy(nn1) = 1.0_dp
            yavg2dy(nn3) = 1.0_dp
            yavg2dy(nn4) = 1.0_dp
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)
            zavg2dz(nn1) = 1.0_dp
            zavg2dz(nn3) = 1.0_dp
            zavg2dz(nn4) = 1.0_dp

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))        &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))

            nx2dy(nn1)=-(z_node(nn4)- z_node(nn1)) + (z_node(nn3) - z_node(nn1))
            nx2dy(nn3)=  z_node(nn4)- z_node(nn1)
            nx2dy(nn4)=-(z_node(nn3)- z_node(nn1))

            nx2dz(nn1)=-(y_node(nn3)- y_node(nn1)) + (y_node(nn4) - y_node(nn1))
            nx2dz(nn3)=-(y_node(nn4)- y_node(nn1))
            nx2dz(nn4)= (y_node(nn3)- y_node(nn1))

          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))        &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))

            ny2dx(nn1)=-(z_node(nn3)- z_node(nn1)) + (z_node(nn4) - z_node(nn1))
            ny2dx(nn3)=-(z_node(nn4)- z_node(nn1))
            ny2dx(nn4)= (z_node(nn3)- z_node(nn1))

            ny2dz(nn1)=-(x_node(nn4)- x_node(nn1)) + (x_node(nn3) - x_node(nn1))
            ny2dz(nn3)= (x_node(nn4)- x_node(nn1))
            ny2dz(nn4)=-(x_node(nn3)- x_node(nn1))

          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))        &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

            nz2dx(nn1)=-(y_node(nn4)- y_node(nn1)) + (y_node(nn3) - y_node(nn1))
            nz2dx(nn3)= (y_node(nn4)- y_node(nn1))
            nz2dx(nn4)=-(y_node(nn3)- y_node(nn1))

            nz2dy(nn1)=-(x_node(nn3)- x_node(nn1)) + (x_node(nn4) - x_node(nn1))
            nz2dy(nn3)=-(x_node(nn4)- x_node(nn1))
            nz2dy(nn4)= (x_node(nn3)- x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2
            term2dx(:) = xavg2*nx2dx(:) + yavg2*ny2dx(:) + zavg2*nz2dx(:) +    &
                         xavg2dx(:)*nx2
            term2dy(:) = xavg2*nx2dy(:) + yavg2*ny2dy(:) + zavg2*nz2dy(:) +    &
                         yavg2dy(:)*ny2
            term2dz(:) = xavg2*nx2dz(:) + yavg2*ny2dz(:) + zavg2*nz2dz(:) +    &
                         zavg2dz(:)*nz2

!         cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th
            cell_voldx(:) = cell_voldx(:) + (term1dx(:) + term2dx(:))*my_18th
            cell_voldy(:) = cell_voldy(:) + (term1dy(:) + term2dy(:))*my_18th
            cell_voldz(:) = cell_voldz(:) + (term1dz(:) + term2dz(:))*my_18th

!         gradient contributions

          termx1 = nx1*my_6th
            termx1dx(:) = nx1dx(:)*my_6th
            termx1dy(:) = nx1dy(:)*my_6th
            termx1dz(:) = nx1dz(:)*my_6th
          termy1 = ny1*my_6th
            termy1dx(:) = ny1dx(:)*my_6th
            termy1dy(:) = ny1dy(:)*my_6th
            termy1dz(:) = ny1dz(:)*my_6th
          termz1 = nz1*my_6th
            termz1dx(:) = nz1dx(:)*my_6th
            termz1dy(:) = nz1dy(:)*my_6th
            termz1dz(:) = nz1dz(:)*my_6th

          termx2 = nx2*my_6th
            termx2dx(:) = nx2dx(:)*my_6th
            termx2dy(:) = nx2dy(:)*my_6th
            termx2dz(:) = nx2dz(:)*my_6th
          termy2 = ny2*my_6th
            termy2dx(:) = ny2dx(:)*my_6th
            termy2dy(:) = ny2dy(:)*my_6th
            termy2dz(:) = ny2dz(:)*my_6th
          termz2 = nz2*my_6th
            termz2dx(:) = nz2dx(:)*my_6th
            termz2dy(:) = nz2dy(:)*my_6th
            termz2dz(:) = nz2dz(:)*my_6th

          do eqn = eqn0, eqn1
            qavg1 = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
            qavg2 = q_node(eqn,nn1) + q_node(eqn,nn3) + q_node(eqn,nn4)
            gradx_cell(eqn) = gradx_cell(eqn) + termx1*qavg1 + termx2*qavg2
              gradx_celldx(eqn,:) = gradx_celldx(eqn,:) + termx1dx(:)*qavg1    &
                                  + termx2dx(:)*qavg2
              gradx_celldy(eqn,:) = gradx_celldy(eqn,:) + termx1dy(:)*qavg1    &
                                  + termx2dy(:)*qavg2
              gradx_celldz(eqn,:) = gradx_celldz(eqn,:) + termx1dz(:)*qavg1    &
                                  + termx2dz(:)*qavg2
            grady_cell(eqn) = grady_cell(eqn) + termy1*qavg1 + termy2*qavg2
              grady_celldx(eqn,:) = grady_celldx(eqn,:) + termy1dx(:)*qavg1    &
                                  + termy2dx(:)*qavg2
              grady_celldy(eqn,:) = grady_celldy(eqn,:) + termy1dy(:)*qavg1    &
                                  + termy2dy(:)*qavg2
              grady_celldz(eqn,:) = grady_celldz(eqn,:) + termy1dz(:)*qavg1    &
                                  + termy2dz(:)*qavg2
            gradz_cell(eqn) = gradz_cell(eqn) + termz1*qavg1 + termz2*qavg2
              gradz_celldx(eqn,:) = gradz_celldx(eqn,:) + termz1dx(:)*qavg1    &
                                  + termz2dx(:)*qavg2
              gradz_celldy(eqn,:) = gradz_celldy(eqn,:) + termz1dy(:)*qavg1    &
                                  + termz2dy(:)*qavg2
              gradz_celldz(eqn,:) = gradz_celldz(eqn,:) + termz1dz(:)*qavg1    &
                                  + termz2dz(:)*qavg2
          end do

          nx = nx1 + nx2
            nxdx(:) = nx1dx(:) + nx2dx(:)
            nxdy(:) = nx1dy(:) + nx2dy(:)
            nxdz(:) = nx1dz(:) + nx2dz(:)
          ny = ny1 + ny2
            nydx(:) = ny1dx(:) + ny2dx(:)
            nydy(:) = ny1dy(:) + ny2dy(:)
            nydz(:) = ny1dz(:) + ny2dz(:)
          nz = nz1 + nz2
            nzdx(:) = nz1dx(:) + nz2dx(:)
            nzdy(:) = nz1dy(:) + nz2dy(:)
            nzdz(:) = nz1dz(:) + nz2dz(:)

        end if

        nxf(iface) = nx
        nyf(iface) = ny
        nzf(iface) = nz

      end do threed_faces

!   need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

      cell_vol_inv = my_1/cell_vol
        cell_vol_invdx(:) = -my_1/cell_vol/cell_vol*cell_voldx(:)
        cell_vol_invdy(:) = -my_1/cell_vol/cell_vol*cell_voldy(:)
        cell_vol_invdz(:) = -my_1/cell_vol/cell_vol*cell_voldz(:)

      gradx_cell_new(eqn0:eqn1) = gradx_cell(eqn0:eqn1) * cell_vol_inv
        do k = eqn0, eqn1
          gradx_cell_newdx(k,:) = gradx_cell(k)*cell_vol_invdx(:)              &
                                + cell_vol_inv*gradx_celldx(k,:)
          gradx_cell_newdy(k,:) = gradx_cell(k)*cell_vol_invdy(:)              &
                                + cell_vol_inv*gradx_celldy(k,:)
          gradx_cell_newdz(k,:) = gradx_cell(k)*cell_vol_invdz(:)              &
                                + cell_vol_inv*gradx_celldz(k,:)
        end do
      grady_cell_new(eqn0:eqn1) = grady_cell(eqn0:eqn1) * cell_vol_inv
        do k = eqn0, eqn1
          grady_cell_newdx(k,:) = grady_cell(k)*cell_vol_invdx(:)              &
                                + cell_vol_inv*grady_celldx(k,:)
          grady_cell_newdy(k,:) = grady_cell(k)*cell_vol_invdy(:)              &
                                + cell_vol_inv*grady_celldy(k,:)
          grady_cell_newdz(k,:) = grady_cell(k)*cell_vol_invdz(:)              &
                                + cell_vol_inv*grady_celldz(k,:)
        end do
      gradz_cell_new(eqn0:eqn1) = gradz_cell(eqn0:eqn1) * cell_vol_inv
        do k = eqn0, eqn1
          gradz_cell_newdx(k,:) = gradz_cell(k)*cell_vol_invdx(:)              &
                                + cell_vol_inv*gradz_celldx(k,:)
          gradz_cell_newdy(k,:) = gradz_cell(k)*cell_vol_invdy(:)              &
                                + cell_vol_inv*gradz_celldy(k,:)
          gradz_cell_newdz(k,:) = gradz_cell(k)*cell_vol_invdz(:)              &
                                + cell_vol_inv*gradz_celldz(k,:)
        end do

      uxavg = gradx_cell_new(n_momx)
        uxavgdx(:) = gradx_cell_newdx(n_momx,:)
        uxavgdy(:) = gradx_cell_newdy(n_momx,:)
        uxavgdz(:) = gradx_cell_newdz(n_momx,:)
      vxavg = gradx_cell_new(n_momy)
        vxavgdx(:) = gradx_cell_newdx(n_momy,:)
        vxavgdy(:) = gradx_cell_newdy(n_momy,:)
        vxavgdz(:) = gradx_cell_newdz(n_momy,:)
      wxavg = gradx_cell_new(n_momz)
        wxavgdx(:) = gradx_cell_newdx(n_momz,:)
        wxavgdy(:) = gradx_cell_newdy(n_momz,:)
        wxavgdz(:) = gradx_cell_newdz(n_momz,:)

      uyavg = grady_cell_new(n_momx)
        uyavgdx(:) = grady_cell_newdx(n_momx,:)
        uyavgdy(:) = grady_cell_newdy(n_momx,:)
        uyavgdz(:) = grady_cell_newdz(n_momx,:)
      vyavg = grady_cell_new(n_momy)
        vyavgdx(:) = grady_cell_newdx(n_momy,:)
        vyavgdy(:) = grady_cell_newdy(n_momy,:)
        vyavgdz(:) = grady_cell_newdz(n_momy,:)
      wyavg = grady_cell_new(n_momz)
        wyavgdx(:) = grady_cell_newdx(n_momz,:)
        wyavgdy(:) = grady_cell_newdy(n_momz,:)
        wyavgdz(:) = grady_cell_newdz(n_momz,:)

      uzavg = gradz_cell_new(n_momx)
        uzavgdx(:) = gradz_cell_newdx(n_momx,:)
        uzavgdy(:) = gradz_cell_newdy(n_momx,:)
        uzavgdz(:) = gradz_cell_newdz(n_momx,:)
      vzavg = gradz_cell_new(n_momy)
        vzavgdx(:) = gradz_cell_newdx(n_momy,:)
        vzavgdy(:) = gradz_cell_newdy(n_momy,:)
        vzavgdz(:) = gradz_cell_newdz(n_momy,:)
      wzavg = gradz_cell_new(n_momz)
        wzavgdx(:) = gradz_cell_newdx(n_momz,:)
        wzavgdy(:) = gradz_cell_newdy(n_momz,:)
        wzavgdz(:) = gradz_cell_newdz(n_momz,:)

! Check cell-face angles (dot product) - if greater than a specified value,
! we skip the contribution from this cell (currently for 3D cases only)

!     note: only need to check upper part of matrix chk_norm since
!     dot products commute (A*B = B*A) and the diagonal indicates
!     the dot product of a face with itself; also note that nx, ny, nz
!     are not unit normals so we must scale the dot product accordingly

      if ( ivgrd == 1 ) then
        big_angle = 0
        do i=1,face_per_cell
          do j=i+1,face_per_cell
            if (chk_norm(i,j) > 0) then
              dot = nxf(i)*nxf(j) + nyf(i)*nyf(j) + nzf(i)*nzf(j)
!             scale to unit normals
              dot = dot / sqrt(nxf(i)*nxf(i) + nyf(i)*nyf(i) + nzf(i)*nzf(i))
              dot = dot / sqrt(nxf(j)*nxf(j) + nyf(j)*nyf(j) + nzf(j)*nzf(j))
              if (dot >= my_mxd) big_angle = big_angle + 1
            end if
          end do
        end do
        if ( big_angle > 0 )  cycle cell_loop
      end if

! Next loop over the edges in the cell and get each ones
! contribution to the residual

      edge_loop : do ie = 1, edge_per_cell

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)
        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

! Get this edges' contribution to the dual normal and area

!       edge midpoint

        xmdx(:) = 0.0_dp
        ymdy(:) = 0.0_dp
        zmdz(:) = 0.0_dp

        xm = (x_node(n1_loc) + x_node(n2_loc))*my_half
          xmdx(n1_loc) = my_half
          xmdx(n2_loc) = my_half

        ym = (y_node(n1_loc) + y_node(n2_loc))*my_half
          ymdy(n1_loc) = my_half
          ymdy(n2_loc) = my_half

        zm = (z_node(n1_loc) + z_node(n2_loc))*my_half
          zmdz(n1_loc) = my_half
          zmdz(n2_loc) = my_half

!       compute left face centroid

        xldx(:) = 0.0_dp
        yldy(:) = 0.0_dp
        zldz(:) = 0.0_dp

        if (n4_loc /= 0) then
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc)               &
              + x_node(n4_loc))*my_4th
            xldx(n1_loc) = my_4th
            xldx(n2_loc) = my_4th
            xldx(n3_loc) = my_4th
            xldx(n4_loc) = my_4th

          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc)               &
              + y_node(n4_loc))*my_4th
            yldy(n1_loc) = my_4th
            yldy(n2_loc) = my_4th
            yldy(n3_loc) = my_4th
            yldy(n4_loc) = my_4th

          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc)               &
              + z_node(n4_loc))*my_4th
            zldz(n1_loc) = my_4th
            zldz(n2_loc) = my_4th
            zldz(n3_loc) = my_4th
            zldz(n4_loc) = my_4th
        else
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc))*my_3rd
            xldx(n1_loc) = my_3rd
            xldx(n2_loc) = my_3rd
            xldx(n3_loc) = my_3rd

          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc))*my_3rd
            yldy(n1_loc) = my_3rd
            yldy(n2_loc) = my_3rd
            yldy(n3_loc) = my_3rd

          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc))*my_3rd
            zldz(n1_loc) = my_3rd
            zldz(n2_loc) = my_3rd
            zldz(n3_loc) = my_3rd
        end if

!       compute right face centroid

        xrdx(:) = 0.0_dp
        yrdy(:) = 0.0_dp
        zrdz(:) = 0.0_dp

        if (n6_loc /= 0) then
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc)               &
              + x_node(n6_loc))*my_4th
            xrdx(n1_loc) = my_4th
            xrdx(n2_loc) = my_4th
            xrdx(n5_loc) = my_4th
            xrdx(n6_loc) = my_4th

          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc)               &
              + y_node(n6_loc))*my_4th
            yrdy(n1_loc) = my_4th
            yrdy(n2_loc) = my_4th
            yrdy(n5_loc) = my_4th
            yrdy(n6_loc) = my_4th

          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc)               &
              + z_node(n6_loc))*my_4th
            zrdz(n1_loc) = my_4th
            zrdz(n2_loc) = my_4th
            zrdz(n5_loc) = my_4th
            zrdz(n6_loc) = my_4th
        else
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc))*my_3rd
            xrdx(n1_loc) = my_3rd
            xrdx(n2_loc) = my_3rd
            xrdx(n5_loc) = my_3rd

          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc))*my_3rd
            yrdy(n1_loc) = my_3rd
            yrdy(n2_loc) = my_3rd
            yrdy(n5_loc) = my_3rd

          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc))*my_3rd
            zrdz(n1_loc) = my_3rd
            zrdz(n2_loc) = my_3rd
            zrdz(n5_loc) = my_3rd
        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
          areaxdx(:) = 0.0_dp
          areaxdy(:) = ((ycdy(:)-ymdy(:))*(zl-zr) - (zc-zm)*(yldy(:)-yrdy(:))) &
                                                                        *my_half
          areaxdz(:) = ((yc-ym)*(zldz(:)-zrdz(:)) - (zcdz(:)-zmdz(:))*(yl-yr)) &
                                                                        *my_half

        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
          areaydx(:) = ((zc-zm)*(xldx(:)-xrdx(:)) - (xcdx(:)-xmdx(:))*(zl-zr)) &
                                                                        *my_half
          areaydy(:) = 0.0_dp
          areaydz(:) = ((zcdz(:)-zmdz(:))*(xl-xr) - (xc-xm)*(zldz(:)-zrdz(:))) &
                                                                        *my_half

        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half
          areazdx(:) = ((xcdx(:)-xmdx(:))*(yl-yr) - (yc-ym)*(xldx(:)-xrdx(:))) &
                                                                        *my_half
          areazdy(:) = ((xc-xm)*(yldy(:)-yrdy(:)) - (ycdy(:)-ymdy(:))*(xl-xr)) &
                                                                        *my_half
          areazdz(:) = 0.0_dp

! Get gradients at the dual face; either take gradients for
! this piece of the dual face to be the same as the cell-average gradient
! computed above  (which is what the legacy FUN3D solver does for tets),
! or combine with the edge-gradient to increase h-ellipticity on
! non-simplicial meshes.

        areai = 1._dp/sqrt( areax**2 + areay**2 + areaz**2 )
          areaidx(:) = -0.5_dp/( areax**2 + areay**2 + areaz**2 )**1.5_dp      &
                       *(2.0_dp*areax*areaxdx(:) + 2.0_dp*areay*areaydx(:)     &
                       + 2.0_dp*areaz*areazdx(:))
          areaidy(:) = -0.5_dp/( areax**2 + areay**2 + areaz**2 )**1.5_dp      &
                       *(2.0_dp*areax*areaxdy(:) + 2.0_dp*areay*areaydy(:)     &
                       + 2.0_dp*areaz*areazdy(:))
          areaidz(:) = -0.5_dp/( areax**2 + areay**2 + areaz**2 )**1.5_dp      &
                       *(2.0_dp*areax*areaxdz(:) + 2.0_dp*areay*areaydz(:)     &
                       + 2.0_dp*areaz*areazdz(:))

        xnf = areax*areai
          xnfdx(:) = areax*areaidx(:) + areai*areaxdx(:)
          xnfdy(:) = areax*areaidy(:) + areai*areaxdy(:)
          xnfdz(:) = areax*areaidz(:) + areai*areaxdz(:)

        ynf = areay*areai
          ynfdx(:) = areay*areaidx(:) + areai*areaydx(:)
          ynfdy(:) = areay*areaidy(:) + areai*areaydy(:)
          ynfdz(:) = areay*areaidz(:) + areai*areaydz(:)

        znf = areaz*areai
          znfdx(:) = areaz*areaidx(:) + areai*areazdx(:)
          znfdy(:) = areaz*areaidy(:) + areai*areazdy(:)
          znfdz(:) = areaz*areaidz(:) + areai*areazdz(:)

!       ex, ey, ez is unit vector along edge direction

        exdx(:) = 0.0_dp
        eydy(:) = 0.0_dp
        ezdz(:) = 0.0_dp

        ex   = x_node(n2_loc) - x_node(n1_loc)
          exdx(n1_loc) = -1.0_dp
          exdx(n2_loc) =  1.0_dp

        ey   = y_node(n2_loc) - y_node(n1_loc)
          eydy(n1_loc) = -1.0_dp
          eydy(n2_loc) =  1.0_dp

        ez   = z_node(n2_loc) - z_node(n1_loc)
          ezdz(n1_loc) = -1.0_dp
          ezdz(n2_loc) =  1.0_dp

        disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

        stuff = ex**2 + ey**2 + ez**2

          disidx(:) = -0.5_dp*stuff**(-1.5_dp)*(2.0_dp*ex*exdx(:))
          disidy(:) = -0.5_dp*stuff**(-1.5_dp)*(2.0_dp*ey*eydy(:))
          disidz(:) = -0.5_dp*stuff**(-1.5_dp)*(2.0_dp*ez*ezdz(:))

        ex_new = ex*disi
          ex_newdx(:) = ex*disidx(:) + disi*exdx(:)
          ex_newdy(:) = ex*disidy(:)
          ex_newdz(:) = ex*disidz(:)

        ey_new = ey*disi
          ey_newdx(:) = ey*disidx(:)
          ey_newdy(:) = ey*disidy(:) + disi*eydy(:)
          ey_newdz(:) = ey*disidz(:)

        ez_new = ez*disi
          ez_newdx(:) = ez*disidx(:)
          ez_newdy(:) = ez*disidy(:)
          ez_newdz(:) = ez*disidz(:) + disi*ezdz(:)

        select case( gradient_construction_rhs )
        case (0)

          augment_weight1 = ex_new
            augment_weight1dx(:) = ex_newdx(:)
            augment_weight1dy(:) = ex_newdy(:)
            augment_weight1dz(:) = ex_newdz(:)
          augment_weight2 = ey_new
            augment_weight2dx(:) = ey_newdx(:)
            augment_weight2dy(:) = ey_newdy(:)
            augment_weight2dz(:) = ey_newdz(:)
          augment_weight3 = ez_new
            augment_weight3dx(:) = ez_newdx(:)
            augment_weight3dy(:) = ez_newdy(:)
            augment_weight3dz(:) = ez_newdz(:)

        case (1)

          e_dot_n = ex_new*xnf + ey_new*ynf + ez_new*znf
            e_dot_ndx(:) = ex_new*xnfdx(:) + ey_new*ynfdx(:) + ez_new*znfdx(:) &
                         + ex_newdx(:)*xnf + ey_newdx(:)*ynf + ez_newdx(:)*znf
            e_dot_ndy(:) = ex_new*xnfdy(:) + ey_new*ynfdy(:) + ez_new*znfdy(:) &
                         + ex_newdy(:)*xnf + ey_newdy(:)*ynf + ez_newdy(:)*znf
            e_dot_ndz(:) = ex_new*xnfdz(:) + ey_new*ynfdz(:) + ez_new*znfdz(:) &
                         + ex_newdz(:)*xnf + ey_newdz(:)*ynf + ez_newdz(:)*znf

          augment_weight1 = xnf
            augment_weight1dx(:) = xnfdx(:)
            augment_weight1dy(:) = xnfdy(:)
            augment_weight1dz(:) = xnfdz(:)

          augment_weight2 = ynf
            augment_weight2dx(:) = ynfdx(:)
            augment_weight2dy(:) = ynfdy(:)
            augment_weight2dz(:) = ynfdz(:)

          augment_weight3 = znf
            augment_weight3dx(:) = znfdx(:)
            augment_weight3dy(:) = znfdy(:)
            augment_weight3dz(:) = znfdz(:)

          if ( e_dot_n < 1.0e-10_dp ) then
            augment_weight1 = 0.0_dp
              augment_weight1dx(:) = 0.0_dp
              augment_weight1dy(:) = 0.0_dp
              augment_weight1dz(:) = 0.0_dp
            augment_weight2 = 0.0_dp
              augment_weight2dx(:) = 0.0_dp
              augment_weight2dy(:) = 0.0_dp
              augment_weight2dz(:) = 0.0_dp
            augment_weight3 = 0.0_dp
              augment_weight3dx(:) = 0.0_dp
              augment_weight3dy(:) = 0.0_dp
              augment_weight3dz(:) = 0.0_dp
          else
            augment_weight1_new = augment_weight1/e_dot_n
              augment_weight1_newdx(:) = (e_dot_n*augment_weight1dx(:)         &
                                 - augment_weight1*e_dot_ndx(:))/e_dot_n/e_dot_n
              augment_weight1_newdy(:) = (e_dot_n*augment_weight1dy(:)         &
                                 - augment_weight1*e_dot_ndy(:))/e_dot_n/e_dot_n
              augment_weight1_newdz(:) = (e_dot_n*augment_weight1dz(:)         &
                                 - augment_weight1*e_dot_ndz(:))/e_dot_n/e_dot_n
            augment_weight2_new = augment_weight2/e_dot_n
              augment_weight2_newdx(:) = (e_dot_n*augment_weight2dx(:)         &
                                 - augment_weight2*e_dot_ndx(:))/e_dot_n/e_dot_n
              augment_weight2_newdy(:) = (e_dot_n*augment_weight2dy(:)         &
                                 - augment_weight2*e_dot_ndy(:))/e_dot_n/e_dot_n
              augment_weight2_newdz(:) = (e_dot_n*augment_weight2dz(:)         &
                                 - augment_weight2*e_dot_ndz(:))/e_dot_n/e_dot_n
            augment_weight3_new = augment_weight3/e_dot_n
              augment_weight3_newdx(:) = (e_dot_n*augment_weight3dx(:)         &
                                 - augment_weight3*e_dot_ndx(:))/e_dot_n/e_dot_n
              augment_weight3_newdy(:) = (e_dot_n*augment_weight3dy(:)         &
                                 - augment_weight3*e_dot_ndy(:))/e_dot_n/e_dot_n
              augment_weight3_newdz(:) = (e_dot_n*augment_weight3dz(:)         &
                                 - augment_weight3*e_dot_ndz(:))/e_dot_n/e_dot_n
            augment_weight1 = augment_weight1_new
              augment_weight1dx(:) = augment_weight1_newdx(:)
              augment_weight1dy(:) = augment_weight1_newdy(:)
              augment_weight1dz(:) = augment_weight1_newdz(:)
            augment_weight2 = augment_weight2_new
              augment_weight2dx(:) = augment_weight2_newdx(:)
              augment_weight2dy(:) = augment_weight2_newdy(:)
              augment_weight2dz(:) = augment_weight2_newdz(:)
            augment_weight3 = augment_weight3_new
              augment_weight3dx(:) = augment_weight3_newdx(:)
              augment_weight3dy(:) = augment_weight3_newdy(:)
              augment_weight3dz(:) = augment_weight3_newdz(:)
          endif

        case default

          write(*,*) 'unknown gradient_construction_rhs in drdgeomv_mix'
          call lmpi_die
          stop

        end select

!       primitive variables at nodes 1 and 2

        u1 = q_node(n_momx,n1_loc)
        v1 = q_node(n_momy,n1_loc)
        w1 = q_node(n_momz,n1_loc)

        u2 = q_node(n_momx,n2_loc)
        v2 = q_node(n_momy,n2_loc)
        w2 = q_node(n_momz,n2_loc)

!       directional gradients along edge

        egradu = ( u2 - u1 )*disi
          egradudx(:) = (u2-u1)*disidx(:)
          egradudy(:) = (u2-u1)*disidy(:)
          egradudz(:) = (u2-u1)*disidz(:)

        egradv = ( v2 - v1 )*disi
          egradvdx(:) = (v2-v1)*disidx(:)
          egradvdy(:) = (v2-v1)*disidy(:)
          egradvdz(:) = (v2-v1)*disidz(:)

        egradw = ( w2 - w1 )*disi
          egradwdx(:) = (w2-w1)*disidx(:)
          egradwdy(:) = (w2-w1)*disidy(:)
          egradwdz(:) = (w2-w1)*disidz(:)

!       average Green-Gauss gradient in edge direction

        gradu_xi = uxavg*ex_new + uyavg*ey_new + uzavg*ez_new
          gradu_xidx(:)=uxavg*ex_newdx(:)+uyavg*ey_newdx(:)+uzavg*ez_newdx(:) +&
                        ex_new*uxavgdx(:)+ey_new*uyavgdx(:)+ez_new*uzavgdx(:)
          gradu_xidy(:)=uxavg*ex_newdy(:)+uyavg*ey_newdy(:)+uzavg*ez_newdy(:) +&
                        ex_new*uxavgdy(:)+ey_new*uyavgdy(:)+ez_new*uzavgdy(:)
          gradu_xidz(:)=uxavg*ex_newdz(:)+uyavg*ey_newdz(:)+uzavg*ez_newdz(:) +&
                        ex_new*uxavgdz(:)+ey_new*uyavgdz(:)+ez_new*uzavgdz(:)

        gradv_xi = vxavg*ex_new + vyavg*ey_new + vzavg*ez_new
          gradv_xidx(:)=vxavg*ex_newdx(:)+vyavg*ey_newdx(:)+vzavg*ez_newdx(:) +&
                        ex_new*vxavgdx(:)+ey_new*vyavgdx(:)+ez_new*vzavgdx(:)
          gradv_xidy(:)=vxavg*ex_newdy(:)+vyavg*ey_newdy(:)+vzavg*ez_newdy(:) +&
                        ex_new*vxavgdy(:)+ey_new*vyavgdy(:)+ez_new*vzavgdy(:)
          gradv_xidz(:)=vxavg*ex_newdz(:)+vyavg*ey_newdz(:)+vzavg*ez_newdz(:) +&
                        ex_new*vxavgdz(:)+ey_new*vyavgdz(:)+ez_new*vzavgdz(:)

        gradw_xi = wxavg*ex_new + wyavg*ey_new + wzavg*ez_new
          gradw_xidx(:)=wxavg*ex_newdx(:)+wyavg*ey_newdx(:)+wzavg*ez_newdx(:) +&
                        ex_new*wxavgdx(:)+ey_new*wyavgdx(:)+ez_new*wzavgdx(:)
          gradw_xidy(:)=wxavg*ex_newdy(:)+wyavg*ey_newdy(:)+wzavg*ez_newdy(:) +&
                        ex_new*wxavgdy(:)+ey_new*wyavgdy(:)+ez_new*wzavgdy(:)
          gradw_xidz(:)=wxavg*ex_newdz(:)+wyavg*ey_newdz(:)+wzavg*ez_newdz(:) +&
                        ex_new*wxavgdz(:)+ey_new*wyavgdz(:)+ez_new*wzavgdz(:)

!       combine gradient contributions from edge and primal cell if non-tet

        tet_or_not : if ( type_cell == 'tet' ) then

          ux = uxavg
            uxdx(:) = uxavgdx(:)
            uxdy(:) = uxavgdy(:)
            uxdz(:) = uxavgdz(:)

          uy = uyavg
            uydx(:) = uyavgdx(:)
            uydy(:) = uyavgdy(:)
            uydz(:) = uyavgdz(:)

          uz = uzavg
            uzdx(:) = uzavgdx(:)
            uzdy(:) = uzavgdy(:)
            uzdz(:) = uzavgdz(:)

          vx = vxavg
            vxdx(:) = vxavgdx(:)
            vxdy(:) = vxavgdy(:)
            vxdz(:) = vxavgdz(:)

          vy = vyavg
            vydx(:) = vyavgdx(:)
            vydy(:) = vyavgdy(:)
            vydz(:) = vyavgdz(:)

          vz = vzavg
            vzdx(:) = vzavgdx(:)
            vzdy(:) = vzavgdy(:)
            vzdz(:) = vzavgdz(:)

          wx = wxavg
            wxdx(:) = wxavgdx(:)
            wxdy(:) = wxavgdy(:)
            wxdz(:) = wxavgdz(:)

          wy = wyavg
            wydx(:) = wyavgdx(:)
            wydy(:) = wyavgdy(:)
            wydz(:) = wyavgdz(:)

          wz = wzavg
            wzdx(:) = wzavgdx(:)
            wzdy(:) = wzavgdy(:)
            wzdz(:) = wzavgdz(:)

        else tet_or_not

          ux = uxavg + ( egradu - gradu_xi )*augment_weight1
            uxdx(:) = uxavgdx(:) + (egradu-gradu_xi)*augment_weight1dx(:)      &
                    + augment_weight1*(egradudx(:)-gradu_xidx(:))
            uxdy(:) = uxavgdy(:) + (egradu-gradu_xi)*augment_weight1dy(:)      &
                    + augment_weight1*(egradudy(:)-gradu_xidy(:))
            uxdz(:) = uxavgdz(:) + (egradu-gradu_xi)*augment_weight1dz(:)      &
                    + augment_weight1*(egradudz(:)-gradu_xidz(:))

          uy = uyavg + ( egradu - gradu_xi )*augment_weight2
            uydx(:) = uyavgdx(:) + (egradu-gradu_xi)*augment_weight2dx(:)      &
                    + augment_weight2*(egradudx(:)-gradu_xidx(:))
            uydy(:) = uyavgdy(:) + (egradu-gradu_xi)*augment_weight2dy(:)      &
                    + augment_weight2*(egradudy(:)-gradu_xidy(:))
            uydz(:) = uyavgdz(:) + (egradu-gradu_xi)*augment_weight2dz(:)      &
                    + augment_weight2*(egradudz(:)-gradu_xidz(:))

          uz = uzavg + ( egradu - gradu_xi )*augment_weight3
            uzdx(:) = uzavgdx(:) + (egradu-gradu_xi)*augment_weight3dx(:)      &
                    + augment_weight3*(egradudx(:)-gradu_xidx(:))
            uzdy(:) = uzavgdy(:) + (egradu-gradu_xi)*augment_weight3dy(:)      &
                    + augment_weight3*(egradudy(:)-gradu_xidy(:))
            uzdz(:) = uzavgdz(:) + (egradu-gradu_xi)*augment_weight3dz(:)      &
                    + augment_weight3*(egradudz(:)-gradu_xidz(:))

          vx = vxavg + ( egradv - gradv_xi )*augment_weight1
            vxdx(:) = vxavgdx(:) + (egradv-gradv_xi)*augment_weight1dx(:)      &
                    + augment_weight1*(egradvdx(:)-gradv_xidx(:))
            vxdy(:) = vxavgdy(:) + (egradv-gradv_xi)*augment_weight1dy(:)      &
                    + augment_weight1*(egradvdy(:)-gradv_xidy(:))
            vxdz(:) = vxavgdz(:) + (egradv-gradv_xi)*augment_weight1dz(:)      &
                    + augment_weight1*(egradvdz(:)-gradv_xidz(:))

          vy = vyavg + ( egradv - gradv_xi )*augment_weight2
            vydx(:) = vyavgdx(:) + (egradv-gradv_xi)*augment_weight2dx(:)      &
                    + augment_weight2*(egradvdx(:)-gradv_xidx(:))
            vydy(:) = vyavgdy(:) + (egradv-gradv_xi)*augment_weight2dy(:)      &
                    + augment_weight2*(egradvdy(:)-gradv_xidy(:))
            vydz(:) = vyavgdz(:) + (egradv-gradv_xi)*augment_weight2dz(:)      &
                    + augment_weight2*(egradvdz(:)-gradv_xidz(:))

          vz = vzavg + ( egradv - gradv_xi )*augment_weight3
            vzdx(:) = vzavgdx(:) + (egradv-gradv_xi)*augment_weight3dx(:)      &
                    + augment_weight3*(egradvdx(:)-gradv_xidx(:))
            vzdy(:) = vzavgdy(:) + (egradv-gradv_xi)*augment_weight3dy(:)      &
                    + augment_weight3*(egradvdy(:)-gradv_xidy(:))
            vzdz(:) = vzavgdz(:) + (egradv-gradv_xi)*augment_weight3dz(:)      &
                    + augment_weight3*(egradvdz(:)-gradv_xidz(:))

          wx = wxavg + ( egradw - gradw_xi )*augment_weight1
            wxdx(:) = wxavgdx(:) + (egradw-gradw_xi)*augment_weight1dx(:)      &
                    + augment_weight1*(egradwdx(:)-gradw_xidx(:))
            wxdy(:) = wxavgdy(:) + (egradw-gradw_xi)*augment_weight1dy(:)      &
                    + augment_weight1*(egradwdy(:)-gradw_xidy(:))
            wxdz(:) = wxavgdz(:) + (egradw-gradw_xi)*augment_weight1dz(:)      &
                    + augment_weight1*(egradwdz(:)-gradw_xidz(:))

          wy = wyavg + ( egradw - gradw_xi )*augment_weight2
            wydx(:) = wyavgdx(:) + (egradw-gradw_xi)*augment_weight2dx(:)      &
                    + augment_weight2*(egradwdx(:)-gradw_xidx(:))
            wydy(:) = wyavgdy(:) + (egradw-gradw_xi)*augment_weight2dy(:)      &
                    + augment_weight2*(egradwdy(:)-gradw_xidy(:))
            wydz(:) = wyavgdz(:) + (egradw-gradw_xi)*augment_weight2dz(:)      &
                    + augment_weight2*(egradwdz(:)-gradw_xidz(:))

          wz = wzavg + ( egradw - gradw_xi )*augment_weight3
            wzdx(:) = wzavgdx(:) + (egradw-gradw_xi)*augment_weight3dx(:)      &
                    + augment_weight3*(egradwdx(:)-gradw_xidx(:))
            wzdy(:) = wzavgdy(:) + (egradw-gradw_xi)*augment_weight3dy(:)      &
                    + augment_weight3*(egradwdy(:)-gradw_xidy(:))
            wzdz(:) = wzavgdz(:) + (egradw-gradw_xi)*augment_weight3dz(:)      &
                    + augment_weight3*(egradwdz(:)-gradw_xidz(:))

        endif tet_or_not

! Viscous contributions at dual face [ full Navier-Stokes terms ]

!       components of symmetric stress tensor

        txx = rmu*2.0_dp*ux
          txxdx(:) = rmu*2.0_dp*uxdx(:)
          txxdy(:) = rmu*2.0_dp*uxdy(:)
          txxdz(:) = rmu*2.0_dp*uxdz(:)

        txy = rmu*(uy + vx)
          txydx(:) = rmu*(uydx(:) + vxdx(:))
          txydy(:) = rmu*(uydy(:) + vxdy(:))
          txydz(:) = rmu*(uydz(:) + vxdz(:))

        txz = rmu*(uz + wx)
          txzdx(:) = rmu*(uzdx(:) + wxdx(:))
          txzdy(:) = rmu*(uzdy(:) + wxdy(:))
          txzdz(:) = rmu*(uzdz(:) + wxdz(:))

        tyy = rmu*2.0_dp*vy
          tyydx(:) = rmu*2.0_dp*vydx(:)
          tyydy(:) = rmu*2.0_dp*vydy(:)
          tyydz(:) = rmu*2.0_dp*vydz(:)

        tyz = rmu*(vz + wy)
          tyzdx(:) = rmu*(vzdx(:) + wydx(:))
          tyzdy(:) = rmu*(vzdy(:) + wydy(:))
          tyzdz(:) = rmu*(vzdz(:) + wydz(:))

        tzz = rmu*2.0_dp*wz
          tzzdx(:) = rmu*2.0_dp*wzdx(:)
          tzzdy(:) = rmu*2.0_dp*wzdy(:)
          tzzdz(:) = rmu*2.0_dp*wzdz(:)

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

!       [nondimensionalization factor rei ] * [ viscosity ]
!       [ unit normal and area ] at dual face

        rax = rei*areax
          raxdx(:) = rei*areaxdx(:)
          raxdy(:) = rei*areaxdy(:)
          raxdz(:) = rei*areaxdz(:)

        ray = rei*areay
          raydx(:) = rei*areaydx(:)
          raydy(:) = rei*areaydy(:)
          raydz(:) = rei*areaydz(:)

        raz = rei*areaz
          razdx(:) = rei*areazdx(:)
          razdy(:) = rei*areazdy(:)
          razdz(:) = rei*areazdz(:)

!       vf2 = rax*txx + ray*txy + raz*txz
          vf2dx(:) = rax*txxdx(:) + ray*txydx(:) + raz*txzdx(:) +              &
                     txx*raxdx(:) + txy*raydx(:) + txz*razdx(:)
          vf2dy(:) = rax*txxdy(:) + ray*txydy(:) + raz*txzdy(:) +              &
                     txx*raxdy(:) + txy*raydy(:) + txz*razdy(:)
          vf2dz(:) = rax*txxdz(:) + ray*txydz(:) + raz*txzdz(:) +              &
                     txx*raxdz(:) + txy*raydz(:) + txz*razdz(:)

!       vf3 = rax*txy + ray*tyy + raz*tyz
          vf3dx(:) = rax*txydx(:) + ray*tyydx(:) + raz*tyzdx(:) +              &
                     txy*raxdx(:) + tyy*raydx(:) + tyz*razdx(:)
          vf3dy(:) = rax*txydy(:) + ray*tyydy(:) + raz*tyzdy(:) +              &
                     txy*raxdy(:) + tyy*raydy(:) + tyz*razdy(:)
          vf3dz(:) = rax*txydz(:) + ray*tyydz(:) + raz*tyzdz(:) +              &
                     txy*raxdz(:) + tyy*raydz(:) + tyz*razdz(:)

!       vf4 = rax*txz + ray*tyz + raz*tzz
          vf4dx(:) = rax*txzdx(:) + ray*tyzdx(:) + raz*tzzdx(:) +              &
                     txz*raxdx(:) + tyz*raydx(:) + tzz*razdx(:)
          vf4dy(:) = rax*txzdy(:) + ray*tyzdy(:) + raz*tzzdy(:) +              &
                     txz*raxdy(:) + tyz*raydy(:) + tzz*razdy(:)
          vf4dz(:) = rax*txzdz(:) + ray*tyzdz(:) + raz*tzzdz(:) +              &
                     txz*raxdz(:) + tyz*raydz(:) + tzz*razdz(:)

! Finally, add the contribution of this piece of the dual face to the residual

!       if ( n1 <= nnodes0 ) then
!         res(n_momx,n1) = res(n_momx,n1) - vf2
!         res(n_momy,n1) = res(n_momy,n1) - vf3
!         res(n_momz,n1) = res(n_momz,n1) - vf4
!       end if

!       if ( n2 <= nnodes0 ) then
!         res(n_momx,n2) = res(n_momx,n2) + vf2
!         res(n_momy,n2) = res(n_momy,n2) + vf3
!         res(n_momz,n2) = res(n_momz,n2) + vf4
!       end if

! derivatives of residual at n1

        res12dx(:) = -vf2dx(:)
        res12dy(:) = -vf2dy(:)
        res12dz(:) = -vf2dz(:)

        res13dx(:) = -vf3dx(:)
        res13dy(:) = -vf3dy(:)
        res13dz(:) = -vf3dz(:)

        res14dx(:) = -vf4dx(:)
        res14dy(:) = -vf4dy(:)
        res14dz(:) = -vf4dz(:)

! derivatives of residual at n2

        res22dx(:) = vf2dx(:)
        res22dy(:) = vf2dy(:)
        res22dz(:) = vf2dz(:)

        res23dx(:) = vf3dx(:)
        res23dy(:) = vf3dy(:)
        res23dz(:) = vf3dz(:)

        res24dx(:) = vf4dx(:)
        res24dy(:) = vf4dy(:)
        res24dz(:) = vf4dz(:)

! finally add up all the pieces

        fcn_loop : do k = 1, nfunctions

          lambda1(:) = coltag(:,n1)*rlam(:,n1,k)
          lambda2(:) = coltag(:,n2)*rlam(:,n2,k)

! place the derivatives of the residual at n1

          if ( n1 <= nnodes0 ) then
            do j = 1, node_per_cell
              node = c2n(j,n)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res12dx(j)*lambda1(2)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res13dx(j)*lambda1(3)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res14dx(j)*lambda1(4)

              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res12dy(j)*lambda1(2)
              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res13dy(j)*lambda1(3)
              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res14dy(j)*lambda1(4)

              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res12dz(j)*lambda1(2)
              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res13dz(j)*lambda1(3)
              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res14dz(j)*lambda1(4)
            end do
          endif

! place the derivatives of the residual at n2

          if ( n2 <= nnodes0 ) then
            do j = 1, node_per_cell
              node = c2n(j,n)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res22dx(j)*lambda2(2)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res23dx(j)*lambda2(3)
              drdxl(1,node,k,1) = drdxl(1,node,k,1) + res24dx(j)*lambda2(4)

              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res22dy(j)*lambda2(2)
              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res23dy(j)*lambda2(3)
              drdxl(2,node,k,1) = drdxl(2,node,k,1) + res24dy(j)*lambda2(4)

              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res22dz(j)*lambda2(2)
              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res23dz(j)*lambda2(3)
              drdxl(3,node,k,1) = drdxl(3,node,k,1) + res24dz(j)*lambda2(4)
            end do
          endif

        end do fcn_loop

      end do edge_loop

    end do cell_loop

  end subroutine drdgeomv_mix_i


!============================= BC_DRDGEOMV_MIX_I =============================80
!
! Closes off viscous flux on boundaries for the general (mixed) element case
! It is the generalization of the original tetrahedral formulation to more
! general elements.
!
! Note: qnode assumed to contain primitive variables
!
!=============================================================================80
  subroutine bc_drdgeomv_mix_i(nnodes0, nnodes01, nbnode, ibc, ibnode, nbfacet,&
                               f2ntb, nbfaceq, f2nqb, nelem, elem, x, y, z,    &
                               qnode, amut, n_tot, drdxl, coltag, rlam,        &
                               nfunctions, ntp, adim)

    use info_depr,       only : twod, re
    use generic_gas_map, only : n_momx, n_momy, n_momz, ndim_g
    use bc_names,        only : bc_has_visc_flux_closure
    use element_types,   only : elem_type
    use element_defs,    only : max_node_per_cell
    use grid_metrics,    only : dual_area_quad
    use twod_util,       only : yplane_2d, y_coplanar_tol
    use kinddefs,        only : dp
    use nml_noninertial_reference_frame, only : noninertial

    integer, intent(in) :: n_tot
    integer, intent(in) :: nbfacet
    integer, intent(in) :: nbfaceq
    integer, intent(in) :: nbnode
    integer, intent(in) :: ibc
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nelem, nfunctions, ntp, adim

    integer, dimension(nbnode),    intent(in) :: ibnode
    integer, dimension(nbfacet,5), intent(in) :: f2ntb
    integer, dimension(nbfaceq,6), intent(in) :: f2nqb

    real(dp), dimension(nnodes01),       intent(in) :: amut
    real(dp), dimension(nnodes01),       intent(in) :: x, y, z
    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode
    real(dp), dimension(adim,nnodes01),  intent(in) :: coltag
    real(dp), dimension(adim,nnodes01,nfunctions),  intent(in)    :: rlam
    real(dp), dimension(3,nnodes01,nfunctions,ntp), intent(inout) :: drdxl

    type(elem_type), dimension(nelem), intent(in) :: elem

    integer, dimension(max_node_per_cell) :: c2n_cell
    integer, dimension(max_node_per_cell) :: node_map

    integer :: i, icell, ielem, nf, node, iface, nn1, nn2, nn3, nn4
    integer :: in1, i_local, bnode, face_2d, eqn, eqn0, eqn1, k, j
    integer :: bnode1, bnode2, bnode3, bnode4
    integer :: nodes_local
    integer :: face_type, nl, nbface_type

    real(dp) :: qavg, term1, term2, termx1, termy1, termz1
    real(dp) :: cell_vol, fact, termx2, termy2, termz2
    real(dp) :: rmu, qavg1, qavg2, cell_vol_inv
    real(dp) :: txx, txy, txz
    real(dp) :: tyy, tyz
    real(dp) :: tzz

    real(dp), dimension(4) :: xn, yn, zn

    real(dp) :: ux, uy, uz
    real(dp) :: vx, vy, vz
    real(dp) :: wx, wy, wz
    real(dp) :: x1, x2, x3
    real(dp) :: y1, y2, y3
    real(dp) :: z1, z2, z3
    real(dp) :: areax, areay, areaz
    real(dp) :: rei, mu_node

    real(dp), dimension(4) :: xnorm_q, ynorm_q, znorm_q

    real(dp) :: nx, ny, nz
    real(dp), dimension(max_node_per_cell) :: nxdx, nydx, nzdx
    real(dp), dimension(max_node_per_cell) :: nxdy, nydy, nzdy
    real(dp), dimension(max_node_per_cell) :: nxdz, nydz, nzdz

    real(dp) :: nx1, ny1, nz1
    real(dp), dimension(max_node_per_cell) :: nx1dx, ny1dx, nz1dx
    real(dp), dimension(max_node_per_cell) :: nx1dy, ny1dy, nz1dy
    real(dp), dimension(max_node_per_cell) :: nx1dz, ny1dz, nz1dz

    real(dp) :: nx2, ny2, nz2
    real(dp), dimension(max_node_per_cell) :: nx2dx, ny2dx, nz2dx
    real(dp), dimension(max_node_per_cell) :: nx2dy, ny2dy, nz2dy
    real(dp), dimension(max_node_per_cell) :: nx2dz, ny2dz, nz2dz

    real(dp) :: termx, termy, termz
    real(dp) :: xavg, yavg, zavg
    real(dp), dimension(max_node_per_cell) :: xavgdx, yavgdy, zavgdz
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdx
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdy
    real(dp), dimension(max_node_per_cell) :: cell_vol_invdz
    real(dp), dimension(max_node_per_cell) :: cell_voldx, cell_voldy, cell_voldz
    real(dp), dimension(max_node_per_cell) :: termxdx, termxdy, termxdz
    real(dp), dimension(max_node_per_cell) :: termydx, termydy, termydz
    real(dp), dimension(max_node_per_cell) :: termzdx, termzdy, termzdz
    real(dp) :: xavg1, yavg1, zavg1
    real(dp), dimension(max_node_per_cell) :: xavg1dx, yavg1dy, zavg1dz
    real(dp) :: xavg2, yavg2, zavg2
    real(dp), dimension(max_node_per_cell) :: xavg2dx, yavg2dy, zavg2dz
    real(dp), dimension(max_node_per_cell) :: term1dx, term1dy, term1dz
    real(dp), dimension(max_node_per_cell) :: term2dx, term2dy, term2dz
    real(dp), dimension(max_node_per_cell) :: termx1dx, termx1dy, termx1dz
    real(dp), dimension(max_node_per_cell) :: termx2dx, termx2dy, termx2dz
    real(dp), dimension(max_node_per_cell) :: termy1dx, termy1dy, termy1dz
    real(dp), dimension(max_node_per_cell) :: termy2dx, termy2dy, termy2dz
    real(dp), dimension(max_node_per_cell) :: termz1dx, termz1dy, termz1dz
    real(dp), dimension(max_node_per_cell) :: termz2dx, termz2dy, termz2dz
    real(dp), dimension(max_node_per_cell) :: uxdx, uxdy, uxdz
    real(dp), dimension(max_node_per_cell) :: vxdx, vxdy, vxdz
    real(dp), dimension(max_node_per_cell) :: wxdx, wxdy, wxdz
    real(dp), dimension(max_node_per_cell) :: uydx, uydy, uydz
    real(dp), dimension(max_node_per_cell) :: vydx, vydy, vydz
    real(dp), dimension(max_node_per_cell) :: wydx, wydy, wydz
    real(dp), dimension(max_node_per_cell) :: uzdx, uzdy, uzdz
    real(dp), dimension(max_node_per_cell) :: vzdx, vzdy, vzdz
    real(dp), dimension(max_node_per_cell) :: wzdx, wzdy, wzdz
    real(dp), dimension(max_node_per_cell) :: txxdx, txxdy, txxdz
    real(dp), dimension(max_node_per_cell) :: txydx, txydy, txydz
    real(dp), dimension(max_node_per_cell) :: txzdx, txzdy, txzdz
    real(dp), dimension(max_node_per_cell) :: tyydx, tyydy, tyydz
    real(dp), dimension(max_node_per_cell) :: tyzdx, tyzdy, tyzdz
    real(dp), dimension(max_node_per_cell) :: tzzdx, tzzdy, tzzdz
    real(dp), dimension(max_node_per_cell) :: x1dx, y1dy, z1dz
    real(dp), dimension(max_node_per_cell) :: x2dx, y2dy, z2dz
    real(dp), dimension(max_node_per_cell) :: x3dx, y3dy, z3dz
    real(dp), dimension(max_node_per_cell) :: areaxdx, areaxdy, areaxdz
    real(dp), dimension(max_node_per_cell) :: areaydx, areaydy, areaydz
    real(dp), dimension(max_node_per_cell) :: areazdx, areazdy, areazdz
    real(dp), dimension(4,max_node_per_cell) :: xndx, yndx, zndx
    real(dp), dimension(4,max_node_per_cell) :: xndy, yndy, zndy
    real(dp), dimension(4,max_node_per_cell) :: xndz, yndz, zndz
    real(dp), dimension(4,max_node_per_cell) :: xnorm_qdx, ynorm_qdx, znorm_qdx
    real(dp), dimension(4,max_node_per_cell) :: xnorm_qdy, ynorm_qdy, znorm_qdy
    real(dp), dimension(4,max_node_per_cell) :: xnorm_qdz, ynorm_qdz, znorm_qdz
    real(dp), dimension(4,max_node_per_cell) :: vf2dx, vf3dx, vf4dx
    real(dp), dimension(4,max_node_per_cell) :: vf2dy, vf3dy, vf4dy
    real(dp), dimension(4,max_node_per_cell) :: vf2dz, vf3dz, vf4dz
    real(dp), dimension(max_node_per_cell) :: res2dx, res2dy, res2dz
    real(dp), dimension(max_node_per_cell) :: res3dx, res3dy, res3dz
    real(dp), dimension(max_node_per_cell) :: res4dx, res4dy, res4dz
    real(dp), dimension(4,4) :: dual_areaxdx
    real(dp), dimension(4,4) :: dual_areaxdy
    real(dp), dimension(4,4) :: dual_areaxdz
    real(dp), dimension(4,4) :: dual_areaydx
    real(dp), dimension(4,4) :: dual_areaydy
    real(dp), dimension(4,4) :: dual_areaydz
    real(dp), dimension(4,4) :: dual_areazdx
    real(dp), dimension(4,4) :: dual_areazdy
    real(dp), dimension(4,4) :: dual_areazdz

    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node

    real(dp), dimension(ndim_g,max_node_per_cell)   :: prim_node

    real(dp), dimension(ndim_g) :: gradx_cell
    real(dp), dimension(ndim_g) :: grady_cell
    real(dp), dimension(ndim_g) :: gradz_cell
    real(dp), dimension(ndim_g) :: gradx_cell_new
    real(dp), dimension(ndim_g) :: grady_cell_new
    real(dp), dimension(ndim_g) :: gradz_cell_new

    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_celldx
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_celldy
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_celldz
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_celldx
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_celldy
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_celldz
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_celldx
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_celldy
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_celldz
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_cell_newdx
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_cell_newdy
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradx_cell_newdz
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_cell_newdx
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_cell_newdy
    real(dp), dimension(ndim_g,max_node_per_cell) :: grady_cell_newdz
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_cell_newdx
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_cell_newdy
    real(dp), dimension(ndim_g,max_node_per_cell) :: gradz_cell_newdz
    real(dp), dimension(adim) :: lambda

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_2   = 2.0_dp
    real(dp), parameter :: my_6th = 1.0_dp/6.0_dp
    real(dp), parameter :: my_18th = my_1/18.0_dp

  continue

    rei   = my_1 / re

    eqn0 = 2
    eqn1 = 4

! For bctype viscous_solid and viscous_solid_trs:
! note that the viscous no-slip strong boundary residuals will be set to zero
! after the all viscous fluxes have been closed off on the boundaries

    close_off_viscous: if ( bc_has_visc_flux_closure(ibc) ) then

      do face_type = 1,2
! First loop over all the tria boundary faces with face_type=1
! Next  loop over all the quad boundary faces with face_type=2
      if(face_type==1)then
        nbface_type = nbfacet
      else
        nbface_type = nbfaceq
      end if

      if(nbface_type==0)cycle

      loop_faces: do nf = 1, nbface_type

!       global node numbers of the cell/face nodes on the boundary

        if(face_type==1)then
          bnode1 = ibnode(f2ntb(nf,1))
          bnode2 = ibnode(f2ntb(nf,2))
          bnode3 = ibnode(f2ntb(nf,3))
          icell = f2ntb(nf,4)         ! global cell number
          ielem = f2ntb(nf,5)         ! cell type indicator
        else
          bnode1 = ibnode(f2nqb(nf,1))
          bnode2 = ibnode(f2nqb(nf,2))
          bnode3 = ibnode(f2nqb(nf,3))
          bnode4 = ibnode(f2nqb(nf,4))
          icell = f2nqb(nf,5)         ! global cell number
          ielem = f2nqb(nf,6)         ! cell type indicator
        end if

!       copy c2n array from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

        end if

! Compute cell averages, cell center, and set up some local solution arrays

        rmu = my_0

        node_loop1 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          prim_node(n_momx:n_momz,i) = qnode(n_momx:n_momz,node)
          mu_node = my_1 + amut(node)
          rmu = rmu + mu_node

        end do node_loop1

!       now compute cell averages by dividing by the number of nodes
!       that contributed

        fact = 1._dp / real(nodes_local, dp)

        rmu = rmu*fact

! Get the gradients in the primal cell via Green-Gauss

        cell_vol = 0.0_dp
          cell_voldx = 0.0_dp
          cell_voldy = 0.0_dp
          cell_voldz = 0.0_dp

        gradx_cell(:) = my_0
          gradx_celldx(:,:) = my_0
          gradx_celldy(:,:) = my_0
          gradx_celldz(:,:) = my_0
        grady_cell(:) = my_0
          grady_celldx(:,:) = my_0
          grady_celldy(:,:) = my_0
          grady_celldz(:,:) = my_0
        gradz_cell(:) = my_0
          gradz_celldx(:,:) = my_0
          gradz_celldy(:,:) = my_0
          gradz_celldz(:,:) = my_0

        threed_faces : do iface = 1, elem(ielem)%face_per_cell

          nn1 = elem(ielem)%local_f2n(iface,1)
          nn2 = elem(ielem)%local_f2n(iface,2)
          nn3 = elem(ielem)%local_f2n(iface,3)
          nn4 = elem(ielem)%local_f2n(iface,4)

          nxdx = 0.0_dp
          nxdy = 0.0_dp
          nxdz = 0.0_dp
          nx1dx = 0.0_dp
          nx1dy = 0.0_dp
          nx1dz = 0.0_dp
          nx2dx = 0.0_dp
          nx2dy = 0.0_dp
          nx2dz = 0.0_dp

          nydx = 0.0_dp
          nydy = 0.0_dp
          nydz = 0.0_dp
          ny1dx = 0.0_dp
          ny1dy = 0.0_dp
          ny1dz = 0.0_dp
          ny2dx = 0.0_dp
          ny2dy = 0.0_dp
          ny2dz = 0.0_dp

          nzdx = 0.0_dp
          nzdy = 0.0_dp
          nzdz = 0.0_dp
          nz1dx = 0.0_dp
          nz1dy = 0.0_dp
          nz1dz = 0.0_dp
          nz2dx = 0.0_dp
          nz2dy = 0.0_dp
          nz2dz = 0.0_dp

          if (nn4 == nn1) then

!         triangular faces of the cell

!         face normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

            nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))       &
               - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

              nxdy(nn1) = -(z_node(nn3) - z_node(nn1))                         &
                         + (z_node(nn2) - z_node(nn1))
              nxdy(nn2) =   z_node(nn3) - z_node(nn1)
              nxdy(nn3) = -(z_node(nn2) - z_node(nn1))

              nxdz(nn1) = -(y_node(nn2) - y_node(nn1))                         &
                         + (y_node(nn3) - y_node(nn1))
              nxdz(nn2) = -(y_node(nn3) - y_node(nn1))
              nxdz(nn3) =  (y_node(nn2) - y_node(nn1))

            ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))       &
               - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

              nydx(nn1) = -(z_node(nn2) - z_node(nn1))                         &
                         + (z_node(nn3) - z_node(nn1))
              nydx(nn2) = -(z_node(nn3) - z_node(nn1))
              nydx(nn3) =  (z_node(nn2) - z_node(nn1))

              nydz(nn1) = -(x_node(nn3) - x_node(nn1))                         &
                         + (x_node(nn2) - x_node(nn1))
              nydz(nn2) =  (x_node(nn3) - x_node(nn1))
              nydz(nn3) = -(x_node(nn2) - x_node(nn1))

            nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))       &
               - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

              nzdx(nn1) = -(y_node(nn3) - y_node(nn1))                         &
                         + (y_node(nn2) - y_node(nn1))
              nzdx(nn2) =  (y_node(nn3) - y_node(nn1))
              nzdx(nn3) = -(y_node(nn2) - y_node(nn1))

              nzdy(nn1) = -(x_node(nn2) - x_node(nn1))                         &
                         + (x_node(nn3) - x_node(nn1))
              nzdy(nn2) = -(x_node(nn3) - x_node(nn1))
              nzdy(nn3) =  (x_node(nn2) - x_node(nn1))

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

            xavgdx = 0.0_dp
            yavgdy = 0.0_dp
            zavgdz = 0.0_dp

            xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
              xavgdx(nn1) = 1.0_dp
              xavgdx(nn2) = 1.0_dp
              xavgdx(nn3) = 1.0_dp

            yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
              yavgdy(nn1) = 1.0_dp
              yavgdy(nn2) = 1.0_dp
              yavgdy(nn3) = 1.0_dp

            zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)
              zavgdz(nn1) = 1.0_dp
              zavgdz(nn2) = 1.0_dp
              zavgdz(nn3) = 1.0_dp

!         cell volume contributions

            cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th
              cell_voldx(:) = cell_voldx(:) + (xavg*nxdx(:) + yavg*nydx(:)     &
                            + zavg*nzdx(:) + xavgdx(:)*nx)*my_18th
              cell_voldy(:) = cell_voldy(:) + (xavg*nxdy(:) + yavg*nydy(:)     &
                            + zavg*nzdy(:) + yavgdy(:)*ny)*my_18th
              cell_voldz(:) = cell_voldz(:) + (xavg*nxdz(:) + yavg*nydz(:)     &
                            + zavg*nzdz(:) + zavgdz(:)*nz)*my_18th

            termx = nx*my_6th
              termxdx(:) = nxdx(:)*my_6th
              termxdy(:) = nxdy(:)*my_6th
              termxdz(:) = nxdz(:)*my_6th
            termy = ny*my_6th
              termydx(:) = nydx(:)*my_6th
              termydy(:) = nydy(:)*my_6th
              termydz(:) = nydz(:)*my_6th
            termz = nz*my_6th
              termzdx(:) = nzdx(:)*my_6th
              termzdy(:) = nzdy(:)*my_6th
              termzdz(:) = nzdz(:)*my_6th

!         gradient contributions

            do eqn = eqn0, eqn1
              qavg= prim_node(eqn,nn1) + prim_node(eqn,nn2) + prim_node(eqn,nn3)
              gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
                gradx_celldx(eqn,:) = gradx_celldx(eqn,:) + termxdx(:)*qavg
                gradx_celldy(eqn,:) = gradx_celldy(eqn,:) + termxdy(:)*qavg
                gradx_celldz(eqn,:) = gradx_celldz(eqn,:) + termxdz(:)*qavg
              grady_cell(eqn) = grady_cell(eqn) + termy*qavg
                grady_celldx(eqn,:) = grady_celldx(eqn,:) + termydx(:)*qavg
                grady_celldy(eqn,:) = grady_celldy(eqn,:) + termydy(:)*qavg
                grady_celldz(eqn,:) = grady_celldz(eqn,:) + termydz(:)*qavg
              gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg
                gradz_celldx(eqn,:) = gradz_celldx(eqn,:) + termzdx(:)*qavg
                gradz_celldy(eqn,:) = gradz_celldy(eqn,:) + termzdy(:)*qavg
                gradz_celldz(eqn,:) = gradz_celldz(eqn,:) + termzdz(:)*qavg
            end do

          else

!         quadrilateral faces of the cell

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

            xavg1dx = 0.0_dp
            yavg1dy = 0.0_dp
            zavg1dz = 0.0_dp

            xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
              xavg1dx(nn1) = 1.0_dp
              xavg1dx(nn2) = 1.0_dp
              xavg1dx(nn3) = 1.0_dp
            yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
              yavg1dy(nn1) = 1.0_dp
              yavg1dy(nn2) = 1.0_dp
              yavg1dy(nn3) = 1.0_dp
            zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)
              zavg1dz(nn1) = 1.0_dp
              zavg1dz(nn2) = 1.0_dp
              zavg1dz(nn3) = 1.0_dp

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

            nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))      &
                - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

              nx1dy(nn1) = -(z_node(nn3) - z_node(nn1))                        &
                          + (z_node(nn2) - z_node(nn1))
              nx1dy(nn2) =   z_node(nn3) - z_node(nn1)
              nx1dy(nn3) = -(z_node(nn2) - z_node(nn1))

              nx1dz(nn1) = -(y_node(nn2) - y_node(nn1))                        &
                          + (y_node(nn3) - y_node(nn1))
              nx1dz(nn2) = -(y_node(nn3) - y_node(nn1))
              nx1dz(nn3) =  (y_node(nn2) - y_node(nn1))

            ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))      &
                - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

              ny1dx(nn1) = -(z_node(nn2) - z_node(nn1))                        &
                          + (z_node(nn3) - z_node(nn1))
              ny1dx(nn2) = -(z_node(nn3) - z_node(nn1))
              ny1dx(nn3) =  (z_node(nn2) - z_node(nn1))

              ny1dz(nn1) = -(x_node(nn3) - x_node(nn1))                        &
                          + (x_node(nn2) - x_node(nn1))
              ny1dz(nn2) =  (x_node(nn3) - x_node(nn1))
              ny1dz(nn3) = -(x_node(nn2) - x_node(nn1))

            nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))      &
                - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

              nz1dx(nn1) = -(y_node(nn3) - y_node(nn1))                        &
                          + (y_node(nn2) - y_node(nn1))
              nz1dx(nn2) =  (y_node(nn3) - y_node(nn1))
              nz1dx(nn3) = -(y_node(nn2) - y_node(nn1))

              nz1dy(nn1) = -(x_node(nn2) - x_node(nn1))                        &
                          + (x_node(nn3) - x_node(nn1))
              nz1dy(nn2) = -(x_node(nn3) - x_node(nn1))
              nz1dy(nn3) =  (x_node(nn2) - x_node(nn1))

            term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1
              term1dx(:) = xavg1*nx1dx(:) + yavg1*ny1dx(:) + zavg1*nz1dx(:) +  &
                           xavg1dx(:)*nx1
              term1dy(:) = xavg1*nx1dy(:) + yavg1*ny1dy(:) + zavg1*nz1dy(:) +  &
                           yavg1dy(:)*ny1
              term1dz(:) = xavg1*nx1dz(:) + yavg1*ny1dz(:) + zavg1*nz1dz(:) +  &
                           zavg1dz(:)*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

            xavg2dx = 0.0_dp
            yavg2dy = 0.0_dp
            zavg2dz = 0.0_dp

            xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
              xavg2dx(nn1) = 1.0_dp
              xavg2dx(nn3) = 1.0_dp
              xavg2dx(nn4) = 1.0_dp
            yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
              yavg2dy(nn1) = 1.0_dp
              yavg2dy(nn3) = 1.0_dp
              yavg2dy(nn4) = 1.0_dp
            zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)
              zavg2dz(nn1) = 1.0_dp
              zavg2dz(nn3) = 1.0_dp
              zavg2dz(nn4) = 1.0_dp

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

            nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))      &
                - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))

              nx2dy(nn1) = -(z_node(nn4) - z_node(nn1))                        &
                          + (z_node(nn3) - z_node(nn1))
              nx2dy(nn3) =   z_node(nn4) - z_node(nn1)
              nx2dy(nn4) = -(z_node(nn3) - z_node(nn1))

              nx2dz(nn1) = -(y_node(nn3) - y_node(nn1))                        &
                          + (y_node(nn4) - y_node(nn1))
              nx2dz(nn3) = -(y_node(nn4) - y_node(nn1))
              nx2dz(nn4) =  (y_node(nn3) - y_node(nn1))

            ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))      &
                - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))

              ny2dx(nn1) = -(z_node(nn3) - z_node(nn1))                        &
                          + (z_node(nn4) - z_node(nn1))
              ny2dx(nn3) = -(z_node(nn4) - z_node(nn1))
              ny2dx(nn4) =  (z_node(nn3) - z_node(nn1))

              ny2dz(nn1) = -(x_node(nn4) - x_node(nn1))                        &
                          + (x_node(nn3) - x_node(nn1))
              ny2dz(nn3) =  (x_node(nn4) - x_node(nn1))
              ny2dz(nn4) = -(x_node(nn3) - x_node(nn1))

            nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))      &
                - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

              nz2dx(nn1) = -(y_node(nn4) - y_node(nn1))                        &
                          + (y_node(nn3) - y_node(nn1))
              nz2dx(nn3) =  (y_node(nn4) - y_node(nn1))
              nz2dx(nn4) = -(y_node(nn3) - y_node(nn1))

              nz2dy(nn1) = -(x_node(nn3) - x_node(nn1))                        &
                          + (x_node(nn4) - x_node(nn1))
              nz2dy(nn3) = -(x_node(nn4) - x_node(nn1))
              nz2dy(nn4) =  (x_node(nn3) - x_node(nn1))

            term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2
              term2dx(:) = xavg2*nx2dx(:) + yavg2*ny2dx(:) + zavg2*nz2dx(:) +  &
                           xavg2dx(:)*nx2
              term2dy(:) = xavg2*nx2dy(:) + yavg2*ny2dy(:) + zavg2*nz2dy(:) +  &
                           yavg2dy(:)*ny2
              term2dz(:) = xavg2*nx2dz(:) + yavg2*ny2dz(:) + zavg2*nz2dz(:) +  &
                           zavg2dz(:)*nz2

!         cell volume contributions

            cell_vol = cell_vol + (term1 + term2)*my_18th
              cell_voldx(:) = cell_voldx(:) + (term1dx(:) + term2dx(:))*my_18th
              cell_voldy(:) = cell_voldy(:) + (term1dy(:) + term2dy(:))*my_18th
              cell_voldz(:) = cell_voldz(:) + (term1dz(:) + term2dz(:))*my_18th

!         gradient contributions

            termx1 = nx1*my_6th
              termx1dx(:) = nx1dx(:)*my_6th
              termx1dy(:) = nx1dy(:)*my_6th
              termx1dz(:) = nx1dz(:)*my_6th
            termy1 = ny1*my_6th
              termy1dx(:) = ny1dx(:)*my_6th
              termy1dy(:) = ny1dy(:)*my_6th
              termy1dz(:) = ny1dz(:)*my_6th
            termz1 = nz1*my_6th
              termz1dx(:) = nz1dx(:)*my_6th
              termz1dy(:) = nz1dy(:)*my_6th
              termz1dz(:) = nz1dz(:)*my_6th

            termx2 = nx2*my_6th
              termx2dx(:) = nx2dx(:)*my_6th
              termx2dy(:) = nx2dy(:)*my_6th
              termx2dz(:) = nx2dz(:)*my_6th
            termy2 = ny2*my_6th
              termy2dx(:) = ny2dx(:)*my_6th
              termy2dy(:) = ny2dy(:)*my_6th
              termy2dz(:) = ny2dz(:)*my_6th
            termz2 = nz2*my_6th
              termz2dx(:) = nz2dx(:)*my_6th
              termz2dy(:) = nz2dy(:)*my_6th
              termz2dz(:) = nz2dz(:)*my_6th

            do eqn = eqn0, eqn1
              qavg1=prim_node(eqn,nn1) + prim_node(eqn,nn2) + prim_node(eqn,nn3)
              qavg2=prim_node(eqn,nn1) + prim_node(eqn,nn3) + prim_node(eqn,nn4)
              gradx_cell(eqn) = gradx_cell(eqn) + termx1*qavg1 + termx2*qavg2
                gradx_celldx(eqn,:) = gradx_celldx(eqn,:) + termx1dx(:)*qavg1  &
                                    + termx2dx(:)*qavg2
                gradx_celldy(eqn,:) = gradx_celldy(eqn,:) + termx1dy(:)*qavg1  &
                                    + termx2dy(:)*qavg2
                gradx_celldz(eqn,:) = gradx_celldz(eqn,:) + termx1dz(:)*qavg1  &
                                    + termx2dz(:)*qavg2
              grady_cell(eqn) = grady_cell(eqn) + termy1*qavg1 + termy2*qavg2
                grady_celldx(eqn,:) = grady_celldx(eqn,:) + termy1dx(:)*qavg1  &
                                    + termy2dx(:)*qavg2
                grady_celldy(eqn,:) = grady_celldy(eqn,:) + termy1dy(:)*qavg1  &
                                    + termy2dy(:)*qavg2
                grady_celldz(eqn,:) = grady_celldz(eqn,:) + termy1dz(:)*qavg1  &
                                    + termy2dz(:)*qavg2
              gradz_cell(eqn) = gradz_cell(eqn) + termz1*qavg1 + termz2*qavg2
                gradz_celldx(eqn,:) = gradz_celldx(eqn,:) + termz1dx(:)*qavg1  &
                                    + termz2dx(:)*qavg2
                gradz_celldy(eqn,:) = gradz_celldy(eqn,:) + termz1dy(:)*qavg1  &
                                    + termz2dy(:)*qavg2
                gradz_celldz(eqn,:) = gradz_celldz(eqn,:) + termz1dz(:)*qavg1  &
                                    + termz2dz(:)*qavg2
            end do

            nx = nx1 + nx2
              nxdx(:) = nx1dx(:) + nx2dx(:)
              nxdy(:) = nx1dy(:) + nx2dy(:)
              nxdz(:) = nx1dz(:) + nx2dz(:)
            ny = ny1 + ny2
              nydx(:) = ny1dx(:) + ny2dx(:)
              nydy(:) = ny1dy(:) + ny2dy(:)
              nydz(:) = ny1dz(:) + ny2dz(:)
            nz = nz1 + nz2
              nzdx(:) = nz1dx(:) + nz2dx(:)
              nzdy(:) = nz1dy(:) + nz2dy(:)
              nzdz(:) = nz1dz(:) + nz2dz(:)

          end if

        end do threed_faces

!   need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

        cell_vol_inv = my_1/cell_vol
          cell_vol_invdx(:) = -my_1/cell_vol/cell_vol*cell_voldx(:)
          cell_vol_invdy(:) = -my_1/cell_vol/cell_vol*cell_voldy(:)
          cell_vol_invdz(:) = -my_1/cell_vol/cell_vol*cell_voldz(:)

        gradx_cell_new(eqn0:eqn1) = gradx_cell(eqn0:eqn1) * cell_vol_inv
          do k = eqn0, eqn1
            gradx_cell_newdx(k,:) = gradx_cell(k)*cell_vol_invdx(:)            &
                                  + cell_vol_inv*gradx_celldx(k,:)
            gradx_cell_newdy(k,:) = gradx_cell(k)*cell_vol_invdy(:)            &
                                  + cell_vol_inv*gradx_celldy(k,:)
            gradx_cell_newdz(k,:) = gradx_cell(k)*cell_vol_invdz(:)            &
                                  + cell_vol_inv*gradx_celldz(k,:)
          end do
        grady_cell_new(eqn0:eqn1) = grady_cell(eqn0:eqn1) * cell_vol_inv
          do k = eqn0, eqn1
            grady_cell_newdx(k,:) = grady_cell(k)*cell_vol_invdx(:)            &
                                  + cell_vol_inv*grady_celldx(k,:)
            grady_cell_newdy(k,:) = grady_cell(k)*cell_vol_invdy(:)            &
                                  + cell_vol_inv*grady_celldy(k,:)
            grady_cell_newdz(k,:) = grady_cell(k)*cell_vol_invdz(:)            &
                                  + cell_vol_inv*grady_celldz(k,:)
          end do
        gradz_cell_new(eqn0:eqn1) = gradz_cell(eqn0:eqn1) * cell_vol_inv
          do k = eqn0, eqn1
            gradz_cell_newdx(k,:) = gradz_cell(k)*cell_vol_invdx(:)            &
                                  + cell_vol_inv*gradz_celldx(k,:)
            gradz_cell_newdy(k,:) = gradz_cell(k)*cell_vol_invdy(:)            &
                                  + cell_vol_inv*gradz_celldy(k,:)
            gradz_cell_newdz(k,:) = gradz_cell(k)*cell_vol_invdz(:)            &
                                  + cell_vol_inv*gradz_celldz(k,:)
          end do

        ux = gradx_cell_new(n_momx)
          uxdx(:) = gradx_cell_newdx(n_momx,:)
          uxdy(:) = gradx_cell_newdy(n_momx,:)
          uxdz(:) = gradx_cell_newdz(n_momx,:)
        vx = gradx_cell_new(n_momy)
          vxdx(:) = gradx_cell_newdx(n_momy,:)
          vxdy(:) = gradx_cell_newdy(n_momy,:)
          vxdz(:) = gradx_cell_newdz(n_momy,:)
        wx = gradx_cell_new(n_momz)
          wxdx(:) = gradx_cell_newdx(n_momz,:)
          wxdy(:) = gradx_cell_newdy(n_momz,:)
          wxdz(:) = gradx_cell_newdz(n_momz,:)

        uy = grady_cell_new(n_momx)
          uydx(:) = grady_cell_newdx(n_momx,:)
          uydy(:) = grady_cell_newdy(n_momx,:)
          uydz(:) = grady_cell_newdz(n_momx,:)
        vy = grady_cell_new(n_momy)
          vydx(:) = grady_cell_newdx(n_momy,:)
          vydy(:) = grady_cell_newdy(n_momy,:)
          vydz(:) = grady_cell_newdz(n_momy,:)
        wy = grady_cell_new(n_momz)
          wydx(:) = grady_cell_newdx(n_momz,:)
          wydy(:) = grady_cell_newdy(n_momz,:)
          wydz(:) = grady_cell_newdz(n_momz,:)

        uz = gradz_cell_new(n_momx)
          uzdx(:) = gradz_cell_newdx(n_momx,:)
          uzdy(:) = gradz_cell_newdy(n_momx,:)
          uzdz(:) = gradz_cell_newdz(n_momx,:)
        vz = gradz_cell_new(n_momy)
          vzdx(:) = gradz_cell_newdx(n_momy,:)
          vzdy(:) = gradz_cell_newdy(n_momy,:)
          vzdz(:) = gradz_cell_newdz(n_momy,:)
        wz = gradz_cell_new(n_momz)
          wzdx(:) = gradz_cell_newdx(n_momz,:)
          wzdy(:) = gradz_cell_newdy(n_momz,:)
          wzdz(:) = gradz_cell_newdz(n_momz,:)

! Now we get this boundary faces' contribution to the dual normal at each
! node; note that for a triangle, this is just 1/3 the face normal, and is
! the same at each node

        if(face_type ==1)then

          x1dx(:) = 0.0_dp
          x2dx(:) = 0.0_dp
          x3dx(:) = 0.0_dp

          y1dy(:) = 0.0_dp
          y2dy(:) = 0.0_dp
          y3dy(:) = 0.0_dp

          z1dz(:) = 0.0_dp
          z2dz(:) = 0.0_dp
          z3dz(:) = 0.0_dp

          x1 = x(bnode1)
          y1 = y(bnode1)
          z1 = z(bnode1)

          x2 = x(bnode2)
          y2 = y(bnode2)
          z2 = z(bnode2)

          x3 = x(bnode3)
          y3 = y(bnode3)
          z3 = z(bnode3)

          do k = 1, elem(ielem)%node_per_cell

            if ( bnode1 == c2n_cell(k) ) then
              x1dx(k) = 1.0_dp
              y1dy(k) = 1.0_dp
              z1dz(k) = 1.0_dp
            endif

            if ( bnode2 == c2n_cell(k) ) then
              x2dx(k) = 1.0_dp
              y2dy(k) = 1.0_dp
              z2dz(k) = 1.0_dp
            endif

            if ( bnode3 == c2n_cell(k) ) then
              x3dx(k) = 1.0_dp
              y3dy(k) = 1.0_dp
              z3dz(k) = 1.0_dp
            endif

          end do

!       - sign on normals because Green-Gauss needs outward pointing normals

          areax = -my_6th*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
            areaxdx(:) =  0.0_dp
            areaxdy(:) = -my_6th*( (y2dy(:)-y1dy(:))*(z3-z1)                   &
                       - (z2-z1)*(y3dy(:)-y1dy(:)) )
            areaxdz(:) = -my_6th*( (y2-y1)*(z3dz(:)-z1dz(:))                   &
                       - (z2dz(:)-z1dz(:))*(y3-y1) )

          areay = -my_6th*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
            areaydx(:) = -my_6th*( (z2-z1)*(x3dx(:)-x1dx(:))                   &
                       - (x2dx(:)-x1dx(:))*(z3-z1) )
            areaydy(:) = 0.0_dp
            areaydz(:) = -my_6th*( (z2dz(:)-z1dz(:))*(x3-x1)                   &
                       - (x2-x1)*(z3dz(:)-z1dz(:)) )

          areaz = -my_6th*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )
            areazdx(:) = -my_6th*( (x2dx(:)-x1dx(:))*(y3-y1)                   &
                       - (y2-y1)*(x3dx(:)-x1dx(:)) )
            areazdy(:) = -my_6th*( (x2-x1)*(y3dy(:)-y1dy(:))                   &
                       - (y2dy(:)-y1dy(:))*(x3-x1) )
            areazdz(:) = 0.0_dp

          nl = 3

          xn(1:3) = areax
            do k = 1, 3
              xndx(k,:) = areaxdx(:)
              xndy(k,:) = areaxdy(:)
              xndz(k,:) = areaxdz(:)
            end do

          yn(1:3) = areay
            do k = 1, 3
              yndx(k,:) = areaydx(:)
              yndy(k,:) = areaydy(:)
              yndz(k,:) = areaydz(:)
            end do

          zn(1:3) = areaz
            do k = 1, 3
              zndx(k,:) = areazdx(:)
              zndy(k,:) = areazdy(:)
              zndz(k,:) = areazdz(:)
            end do

        else

          call dual_area_quad(nnodes01,x,y,z,bnode1,bnode2,bnode3,bnode4,      &
                              noninertial,xnorm_q,ynorm_q,znorm_q,             &
                              dual_areaxdx,dual_areaxdy,dual_areaxdz,          &
                              dual_areaydx,dual_areaydy,dual_areaydz,          &
                              dual_areazdx,dual_areazdy,dual_areazdz)

          xnorm_qdx(:,:) = 0.0_dp
          xnorm_qdy(:,:) = 0.0_dp
          xnorm_qdz(:,:) = 0.0_dp

          ynorm_qdx(:,:) = 0.0_dp
          ynorm_qdy(:,:) = 0.0_dp
          ynorm_qdz(:,:) = 0.0_dp

          znorm_qdx(:,:) = 0.0_dp
          znorm_qdy(:,:) = 0.0_dp
          znorm_qdz(:,:) = 0.0_dp

          do k = 1, elem(ielem)%node_per_cell

            if ( bnode1 == c2n_cell(k) ) then
              xnorm_qdx(:,k) = dual_areaxdx(:,1)
              xnorm_qdy(:,k) = dual_areaxdy(:,1)
              xnorm_qdz(:,k) = dual_areaxdz(:,1)

              ynorm_qdx(:,k) = dual_areaydx(:,1)
              ynorm_qdy(:,k) = dual_areaydy(:,1)
              ynorm_qdz(:,k) = dual_areaydz(:,1)

              znorm_qdx(:,k) = dual_areazdx(:,1)
              znorm_qdy(:,k) = dual_areazdy(:,1)
              znorm_qdz(:,k) = dual_areazdz(:,1)
            endif

            if ( bnode2 == c2n_cell(k) ) then
              xnorm_qdx(:,k) = dual_areaxdx(:,2)
              xnorm_qdy(:,k) = dual_areaxdy(:,2)
              xnorm_qdz(:,k) = dual_areaxdz(:,2)

              ynorm_qdx(:,k) = dual_areaydx(:,2)
              ynorm_qdy(:,k) = dual_areaydy(:,2)
              ynorm_qdz(:,k) = dual_areaydz(:,2)

              znorm_qdx(:,k) = dual_areazdx(:,2)
              znorm_qdy(:,k) = dual_areazdy(:,2)
              znorm_qdz(:,k) = dual_areazdz(:,2)
            endif

            if ( bnode3 == c2n_cell(k) ) then
              xnorm_qdx(:,k) = dual_areaxdx(:,3)
              xnorm_qdy(:,k) = dual_areaxdy(:,3)
              xnorm_qdz(:,k) = dual_areaxdz(:,3)

              ynorm_qdx(:,k) = dual_areaydx(:,3)
              ynorm_qdy(:,k) = dual_areaydy(:,3)
              ynorm_qdz(:,k) = dual_areaydz(:,3)

              znorm_qdx(:,k) = dual_areazdx(:,3)
              znorm_qdy(:,k) = dual_areazdy(:,3)
              znorm_qdz(:,k) = dual_areazdz(:,3)
            endif

            if ( bnode4 == c2n_cell(k) ) then
              xnorm_qdx(:,k) = dual_areaxdx(:,4)
              xnorm_qdy(:,k) = dual_areaxdy(:,4)
              xnorm_qdz(:,k) = dual_areaxdz(:,4)

              ynorm_qdx(:,k) = dual_areaydx(:,4)
              ynorm_qdy(:,k) = dual_areaydy(:,4)
              ynorm_qdz(:,k) = dual_areaydz(:,4)

              znorm_qdx(:,k) = dual_areazdx(:,4)
              znorm_qdy(:,k) = dual_areazdy(:,4)
              znorm_qdz(:,k) = dual_areazdz(:,4)
            endif

          end do

          nl = 4

          xn(1:4) = xnorm_q(1:4)
            xndx(1:4,:) = xnorm_qdx(1:4,:)
            xndy(1:4,:) = xnorm_qdy(1:4,:)
            xndz(1:4,:) = xnorm_qdz(1:4,:)

          yn(1:4) = ynorm_q(1:4)
            yndx(1:4,:) = ynorm_qdx(1:4,:)
            yndy(1:4,:) = ynorm_qdy(1:4,:)
            yndz(1:4,:) = ynorm_qdz(1:4,:)

          zn(1:4) = znorm_q(1:4)
            zndx(1:4,:) = znorm_qdx(1:4,:)
            zndy(1:4,:) = znorm_qdy(1:4,:)
            zndz(1:4,:) = znorm_qdz(1:4,:)

        end if

! Viscous contributions at dual face [ full Navier-Stokes terms ]

!       components of symmetric stress tensor

        txx = rmu*my_2*ux
          txxdx(:) = rmu*my_2*uxdx(:)
          txxdy(:) = rmu*my_2*uxdy(:)
          txxdz(:) = rmu*my_2*uxdz(:)

        txy = rmu*(uy + vx)
          txydx(:) = rmu*(uydx(:) + vxdx(:))
          txydy(:) = rmu*(uydy(:) + vxdy(:))
          txydz(:) = rmu*(uydz(:) + vxdz(:))

        txz = rmu*(uz + wx)
          txzdx(:) = rmu*(uzdx(:) + wxdx(:))
          txzdy(:) = rmu*(uzdy(:) + wxdy(:))
          txzdz(:) = rmu*(uzdz(:) + wxdz(:))

        tyy = rmu*my_2*vy
          tyydx(:) = rmu*my_2*vydx(:)
          tyydy(:) = rmu*my_2*vydy(:)
          tyydz(:) = rmu*my_2*vydz(:)

        tyz = rmu*(vz + wy)
          tyzdx(:) = rmu*(vzdx(:) + wydx(:))
          tyzdy(:) = rmu*(vzdy(:) + wydy(:))
          tyzdz(:) = rmu*(vzdz(:) + wydz(:))

        tzz = rmu*my_2*wz
          tzzdx(:) = rmu*my_2*wzdx(:)
          tzzdy(:) = rmu*my_2*wzdy(:)
          tzzdz(:) = rmu*my_2*wzdz(:)

!       [nondimensionalization factor rei ] * [ viscosity ]
!       [ unit normal and area ] at dual face

!       vf2(1:nl) = rei*(xn(1:nl)*txx + yn(1:nl)*txy + zn(1:nl)*txz)
          do k = 1, nl
            vf2dx(k,:) = rei*(xn(k)*txxdx(:) + yn(k)*txydx(:) + zn(k)*txzdx(:) &
                              + txx*xndx(k,:)  + txy*yndx(k,:)  + txz*zndx(k,:))
            vf2dy(k,:) = rei*(xn(k)*txxdy(:) + yn(k)*txydy(:) + zn(k)*txzdy(:) &
                              + txx*xndy(k,:)  + txy*yndy(k,:)  + txz*zndy(k,:))
            vf2dz(k,:) = rei*(xn(k)*txxdz(:) + yn(k)*txydz(:) + zn(k)*txzdz(:) &
                              + txx*xndz(k,:)  + txy*yndz(k,:)  + txz*zndz(k,:))
          end do

!       vf3(1:nl) = rei*(xn(1:nl)*txy + yn(1:nl)*tyy + zn(1:nl)*tyz)
          do k = 1, nl
            vf3dx(k,:) = rei*(xn(k)*txydx(:) + yn(k)*tyydx(:) + zn(k)*tyzdx(:) &
                              + txy*xndx(k,:)  + tyy*yndx(k,:)  + tyz*zndx(k,:))
            vf3dy(k,:) = rei*(xn(k)*txydy(:) + yn(k)*tyydy(:) + zn(k)*tyzdy(:) &
                              + txy*xndy(k,:)  + tyy*yndy(k,:)  + tyz*zndy(k,:))
            vf3dz(k,:) = rei*(xn(k)*txydz(:) + yn(k)*tyydz(:) + zn(k)*tyzdz(:) &
                              + txy*xndz(k,:)  + tyy*yndz(k,:)  + tyz*zndz(k,:))
          end do

!       vf4(1:nl) = rei*(xn(1:nl)*txz + yn(1:nl)*tyz + zn(1:nl)*tzz)
          do k = 1, nl
            vf4dx(k,:) = rei*(xn(k)*txzdx(:) + yn(k)*tyzdx(:) + zn(k)*tzzdx(:) &
                              + txz*xndx(k,:)  + tyz*yndx(k,:)  + tzz*zndx(k,:))
            vf4dy(k,:) = rei*(xn(k)*txzdy(:) + yn(k)*tyzdy(:) + zn(k)*tzzdy(:) &
                              + txz*xndy(k,:)  + tyz*yndy(k,:)  + tzz*zndy(k,:))
            vf4dz(k,:) = rei*(xn(k)*txzdz(:) + yn(k)*tyzdz(:) + zn(k)*tzzdz(:) &
                              + txz*xndz(k,:)  + tyz*yndz(k,:)  + tzz*zndz(k,:))
          end do

        in1_loop : do in1 = 1, nl

          if (in1 == 1) then
            bnode = bnode1
          else if (in1 == 2) then
            bnode = bnode2
          else if (in1 == 3) then
            bnode = bnode3
          else if (in1 == 4) then
            bnode = bnode4
          end if

          if (twod) then
             if (abs(y(bnode)-yplane_2d) > y_coplanar_tol) cycle
          end if

!         res2 = -vf2(in1)
            res2dx(:) = -vf2dx(in1,:)
            res2dy(:) = -vf2dy(in1,:)
            res2dz(:) = -vf2dz(in1,:)

!         res3 = -vf3(in1)
            res3dx(:) = -vf3dx(in1,:)
            res3dy(:) = -vf3dy(in1,:)
            res3dz(:) = -vf3dz(in1,:)

!         res4 = -vf4(in1)
            res4dx(:) = -vf4dx(in1,:)
            res4dy(:) = -vf4dy(in1,:)
            res4dz(:) = -vf4dz(in1,:)

!         if (bnode <= nnodes0) then
!           res(n_momx,bnode) = res(n_momx,bnode) - vf2(in1)
!           res(n_momy,bnode) = res(n_momy,bnode) - vf3(in1)
!           res(n_momz,bnode) = res(n_momz,bnode) - vf4(in1)
!         end if

! finally add up all the pieces

          fcn_loop : do k = 1, nfunctions

            lambda(:) = coltag(:,bnode)*rlam(:,bnode,k)

! place the derivatives of the residual at bnode

            if ( bnode <= nnodes0 ) then
              do j = 1, elem(ielem)%node_per_cell
                node = c2n_cell(j)
                drdxl(1,node,k,1) = drdxl(1,node,k,1) + res2dx(j)*lambda(2)
                drdxl(1,node,k,1) = drdxl(1,node,k,1) + res3dx(j)*lambda(3)
                drdxl(1,node,k,1) = drdxl(1,node,k,1) + res4dx(j)*lambda(4)

                drdxl(2,node,k,1) = drdxl(2,node,k,1) + res2dy(j)*lambda(2)
                drdxl(2,node,k,1) = drdxl(2,node,k,1) + res3dy(j)*lambda(3)
                drdxl(2,node,k,1) = drdxl(2,node,k,1) + res4dy(j)*lambda(4)

                drdxl(3,node,k,1) = drdxl(3,node,k,1) + res2dz(j)*lambda(2)
                drdxl(3,node,k,1) = drdxl(3,node,k,1) + res3dz(j)*lambda(3)
                drdxl(3,node,k,1) = drdxl(3,node,k,1) + res4dz(j)*lambda(4)
              end do
            endif

          end do fcn_loop

        end do in1_loop

       end do loop_faces

      end do

    end if close_off_viscous

  end subroutine bc_drdgeomv_mix_i


!================================= BC_VISCOUS_WALL_RATES =====================80
!
! For noninertial cases, pick up the linearization of the residual on viscous
! walls wrt noninertial rates
!         [X          ]
!         [u-uwall    ]         uwall=bdxdt(i)
! Rwall = [v-vwall    ]   where vwall=bdydt(i)
!         [w-wwall    ]         wwall=bdzdt(i)
!         [E/rho-ewall]         ewall=twall/ggm1 + my_half*(uwall**2 + vwall**2
!                                                                    + wwall**2)
!
!=============================================================================80
  subroutine bc_viscous_wall_rates(nnodes0,nbound,nfunctions,bc,rlam,adim,     &
                                   nnodes01,drdxl,qnode,ndim,x,y,z,symmetry,   &
                                   eqn_set)

    use fun3d_constants, only : my_0
    use kinddefs,        only : dp
    use bc_names,        only : bc_strong_viscous_adjoint
    use bc_types,        only : bcgrid_type
    use noninertials,    only : xrotcen_ni,yrotcen_ni,zrotcen_ni
    use lmpi,            only : lmpi_die
    use solution_types,  only : compressible, incompressible

    integer, intent(in) :: nnodes0, nbound, nfunctions, adim, nnodes01
    integer, intent(in) :: ndim, eqn_set

    integer, dimension(nnodes01), intent(in) :: symmetry

    real(dp), dimension(adim,nnodes01,nfunctions), intent(in)    :: rlam
    real(dp), dimension(ndim,nnodes01),            intent(in)    :: qnode
    real(dp), dimension(nnodes01),                 intent(in)    :: x, y, z
    real(dp), dimension(3,nfunctions),             intent(inout) :: drdxl

    type(bcgrid_type), dimension(nbound), intent(in) :: bc

    integer :: ib, inode, i, j

    real(dp) :: rho, uwall, vwall, wwall, rlam2, rlam3, rlam4, rlam5
    real(dp) :: rx, ry, rz
    real(dp) :: uwallx,uwally,uwallz
    real(dp) :: vwallx,vwally,vwallz
    real(dp) :: wwallx,wwally,wwallz
    real(dp) :: res2x,res2y,res2z
    real(dp) :: res3x,res3y,res3z
    real(dp) :: res4x,res4y,res4z
    real(dp) :: res5x,res5y,res5z

    logical, dimension(nnodes0) :: moving_wall_piece_added

  continue

    moving_wall_piece_added = .false.

    boundary_loop : do ib = 1, nbound
      strong_bc : if ( bc_strong_viscous_adjoint(bc(ib)%ibc) ) then
        node_loop : do i = 1, bc(ib)%nbnode
          inode = bc(ib)%ibnode(i)
          local_node : if ( inode <= nnodes0 ) then
            add_contrib : if ( .not. moving_wall_piece_added(inode) ) then

              rho = 1.0_dp
              if ( eqn_set == compressible ) rho = qnode(1,inode)

              rx = x(inode) - xrotcen_ni
              ry = y(inode) - yrotcen_ni
              rz = z(inode) - zrotcen_ni

              uwall = bc(ib)%bdxdt(i)
              vwall = bc(ib)%bdydt(i)
              wwall = bc(ib)%bdzdt(i)

              uwallx =  my_0
              uwally =  rz
              uwallz = -ry

              vwallx = -rz
              vwally = my_0
              vwallz =  rx

              wwallx =  ry
              wwally = -rx
              wwallz =  my_0

              res2x = -rho*uwallx
              res2y = -rho*uwally
              res2z = -rho*uwallz

              res3x = -rho*vwallx
              res3y = -rho*vwally
              res3z = -rho*vwallz

              res4x = -rho*wwallx
              res4y = -rho*wwally
              res4z = -rho*wwallz

              res5x = -rho*(uwall*uwallx + vwall*vwallx + wwall*wwallx)
              res5y = -rho*(uwall*uwally + vwall*vwally + wwall*wwally)
              res5z = -rho*(uwall*uwallz + vwall*vwallz + wwall*wwallz)

! If current point also belongs to a symmetry plane, then those residual
! statements will override the viscous BC expressed above

              select case(symmetry(inode))
              case(000) ! do nothing
              case(100)
                res2x = my_0
                res2y = my_0
                res2z = my_0
              case(010)
                res3x = my_0
                res3y = my_0
                res3z = my_0
              case(001)
                res4x = my_0
                res4y = my_0
                res4z = my_0
              case default
                write(*,*) 'Invalid value of symmetry: ', symmetry(inode)
                call lmpi_die
                stop
              end select

              fcn_loop : do j = 1, nfunctions
                rlam2 = rlam(2,inode,j)
                rlam3 = rlam(3,inode,j)
                rlam4 = rlam(4,inode,j)
                if ( eqn_set == compressible ) then
                  rlam5 = rlam(5,inode,j)
                  drdxl(1,j)=drdxl(1,j) + res2x*rlam2 + res3x*rlam3            &
                                        + res4x*rlam4 + res5x*rlam5
                  drdxl(2,j)=drdxl(2,j) + res2y*rlam2 + res3y*rlam3            &
                                        + res4y*rlam4 + res5y*rlam5
                  drdxl(3,j)=drdxl(3,j) + res2z*rlam2 + res3z*rlam3            &
                                        + res4z*rlam4 + res5z*rlam5
                else if ( eqn_set == incompressible ) then
                  drdxl(1,j)=drdxl(1,j) + res2x*rlam2 + res3x*rlam3            &
                                        + res4x*rlam4
                  drdxl(2,j)=drdxl(2,j) + res2y*rlam2 + res3y*rlam3            &
                                        + res4y*rlam4
                  drdxl(3,j)=drdxl(3,j) + res2z*rlam2 + res3z*rlam3            &
                                        + res4z*rlam4
                endif
              end do fcn_loop

              moving_wall_piece_added(inode) = .true.

            endif add_contrib
          endif local_node
        end do node_loop
      endif strong_bc
    end do boundary_loop

  end subroutine bc_viscous_wall_rates

  include 'viscosity_law.f90'

end module dshape_laminar

\section{Introduction}

\FunThreeD began as a research code in the late 1980s.\cite{FUN2D}
The code was created to develop
new algorithms for unstructured-grid fluid dynamic simulations of 
incompressible and compressible transonic flows.
The project has since grown into a suite of codes that cover not only
flow analysis, but adjoint-based error estimation, mesh adaptation, and
design optimization of fluid dynamic problems extending into the
hypersonic regime.\cite{faast:03tm}

\FunThreeD is currently used as a production flow analysis and design
tool to support NASA programs.
Continued research efforts have also benefited by the
improvements to stability, ease of use, portability, and performance 
that this shift to simultaneous support of 
development and production environments has required.
These benefits also include the rapid evaluation of new techniques 
on realistic simulations
and a rapid maturation of experimental techniques to production-level
capabilities.

\subsection{Primary Capabilities and Features}

The primary capabilities of \FunThreeD are:
\begin{itemize}
  \item Parallel domain decomposition 
        with Message Passing Interface (MPI) 
        communication for distributed computing
  \item Two-dimensional (2D) and Three-dimensional (3D) 
        node-based, finite-volume discretization
  \item Thermodynamic models: perfect gas (compressible and incompressible)
        and thermochemical equilibrium, and non-equilibrium%
        \footnote{The multi-species, thermochemical non-equilibrium capability
         requires the high-energy physics library,
         which is only made available upon specific request and 
         under certain conditions, see \sectionref{s:obtaining} for details.}
  \item Time-accurate options from first- to fourth-order 
        with temporal error controllers
  \item Upwind flux functions: 
        flux difference splitting, 
        flux vector splitting, 
        artificially upstream flux vector splitting, 
        Harten-Lax-van Leer contact, 
        low dissipation flux splitting scheme, 
        and others
  \item Turbulence models: Spalart-Allmaras, Menter k-omega SST, Wilcox k-omega,
        detached eddy simulation, and others, 
        including specified or predicted transition
  \item Implicit time stepping where the linear system is
        solved using either point-implicit, line-implicit, or Newton-Krylov
        (multigrid is also under active development) 
  \item Boundary conditions for internal flows and 
        propulsion simulation including inlets, nozzles, and system performance
  \item Grid motion: time-varying translation, rotation, and deformation
        including overset meshes and 
        six degrees of freedom trajectory computations
  \item Adjoint- and feature-based grid adaptation
  \item Gradient based sensitivity analysis and design optimization
        via hand-coded discrete adjoint for reverse mode differentiation
        and automated complex variables for forward mode differentiation
\end{itemize}

Before exploring more advanced applications
(e.g., grid adaptation, moving grids, overset grids, design optimization),
the user should become familiar 
with \FunThreeD's basic flow solving capabilities
and have appropriate computational capability available
as indicated in the next section.

\subsection{Requirements}

The \FunThreeD development team's typical computing platform is Linux clusters;
so this is the most thoroughly tested environment for the software.
A number of users also run on other UNIX-like environments including \MacOSX;
these platforms are supported as well.
Users have also run on other architectures
such as \Windows-based PC's;
however, the team cannot provide explicit support for these environments.

The user will need GNU Make and a Fortran compiler that supports
at least the Fortran 95 standard.
During configuration, the Fortran compiler is
tested, and any newer Fortran features or extensions are detected are used
to the greatest extent possible.
A large number of compilers are tested by an automated build
framework, including \Intel, \PGI, \NAG, \Lahey, \Cray, 
\Absoft, \IBM, GFortran, and G95.

While the code can be compiled to run on only a single processor,
as demonstrated in the Quick Start section,
most applications will require compiling against an MPI implementation
and the ParMETIS domain decomposition library to allow parallel execution.

The flow solver uses approximately 2.4 kilobytes of memory per grid point
for a perfect gas RANS simulation with a loosely-coupled turbulence model.
For example, a grid with one million mesh points
would require approximately 2.4 gigabytes of memory.
Memory usage will increase slightly with the increase in the number of 
processors because of the increasing boundary data exchanged.
Different solution algorithms and co-visualization options will
also require additional memory.
Typically, one CPU core per 50,000 grid points is suggested,
where a 3D mesh of 20 million grid points would require 400 cores.

\subsection{Grid Generation}\label{s:grid-gen}

\FunThreeD has no grid generation capability.
For internal development at NASA, the most common sources of 3D 
grids are \href{http://tetruss.larc.nasa.gov/vgrid/index.html}{\VGRID}
(ViGYAN, Inc.~and NASA Langley), 
\href{http://www.simcenter.msstate.edu/}%
     {SolidMesh/AFLR3} (Mississippi State), 
\href{http://www.pointwise.com}{Pointwise} (Pointwise, Inc.), and
\href{http://geolab.larc.nasa.gov/GridEx/}{GridEx} (NASA Langley).

For 2D grids, the development team normally uses the AFLR2
software written by Prof.~Marcum et al.~at Mississippi State University
Center for Advanced Vehicular Systems (CAVS) 
\href{http://www.simcenter.msstate.edu/}%
     {SimCenter}.
Scripts are available to facilitate the use of this grid generator,
but the generator itself must be obtained from Prof.~Marcum.
BAMG\cite{bamg} is also used for 2D grid generation and adaptation.

\subsection{Obtaining \FunThreeD}\label{s:obtaining}

\FunThreeD is export restricted and can only be given to a ``U.S. Person,''
which is a citizen of the United States, a lawful permanent
resident alien of the U.S., or someone in the U.S. as a protected political
asylee or under amnesty.
The word ``person'' includes U.S. organizations and entities,
such as companies or universities, see
\href{http://www.access.gpo.gov/nara/cfr/waisidx_99/22cfr120_99.html}%
     {22 CFR \textsection120.15}
for the full legal definition.
Release of the high-energy, real-gas capability is further restricted
because of International Traffic in Arms Regulations (ITAR).

To request the \FunThreeD software suite,
which will include the \refine grid adaptation and mesh untangling library
and the \knife cut-cell library,
please use the website request form available
at \\ \url{http://fun3d.larc.nasa.gov/chapter-1.html#request_fun3d}\\
or send an email to 
\href{mailto:<EMAIL>?subject=FUN3D%20Code%20Request}%
     {\funsupport}
containing the following information:
\begin{itemize}
  \item ``U.S. person'' to put on agreement form,
        i.e., an institution or individual
  \item Point of contact (if ``U.S. Person'' is not an individual)
  \item Point of contact email address
  \item Phone number, extension
  \item FAX number (if available)
  \item Address (PO boxes not allowed)
  \item Proposed application%
  \footnote{The high-energy physics library that allows multiple species and 
  non-equilibrium chemistry are only included upon specific request---be sure
  to note that you desire access to this beta functionality as part of your
  application. Please include the phrase, ``requesting high-energy gas libraries''. } (optional)
  \item How did you discover \FunThreeD? (optional)
\end{itemize}
We will forward your email to initiate a review by Langley software release
authority (SRA) that verifies you qualify as a ``U.S. Person.''
Depending on the SRA's backlog, 
you will be sent a software usage agreement form in a week or two.
Once a completed usage agreement form is received and 
the SRA notifies the \FunThreeD support team, 
the \FunThreeD support team will
make arrangements for transfer of the \FunThreeD software suite.



LIB=port
FFLAGS=-O2
OBJ=\
d1mach.o\
i1mach.o\
r1mach.o\
s1mach.o\
s2mach.o\
s3mach.o\
fdump.o\
iceil.o\
iflr.o\
sdump.o\
stkdmp.o\
u9dmp.o\
a9rntc.o\
a9rntd.o\
a9rnti.o\
a9rntl.o\
a9rntr.o\
frmatd.o\
frmati.o\
frmatr.o\
i10wid.o\
a0xtrp.o\
a7sst.o\
c7vfn.o\
call.o\
d0xtrp.o\
d7dgb.o\
d7dog.o\
d7dup.o\
d7egr.o\
d7mlp.o\
d7tpr.o\
d7upd.o\
da7sst.o\
dalloc.o\
dc7vfn.o\
dd7dgb.o\
dd7dog.o\
dd7dup.o\
dd7mlp.o\
dd7tpr.o\
dd7upd.o\
df7dhb.o\
df7hes.o\
dg7itb.o\
dg7lit.o\
dg7qsb.o\
dg7qts.o\
dh2rfa.o\
dh2rfg.o\
ditsum.o\
divset.o\
dl7itv.o\
dl7ivm.o\
dl7msb.o\
dl7mst.o\
dl7nvr.o\
dl7sqr.o\
dl7srt.o\
dl7svn.o\
dl7svx.o\
dl7tsq.o\
dl7tvm.o\
dl7upd.o\
dl7vml.o\
dmnf.o\
dmnfb.o\
dmng.o\
dmngb.o\
dmnh.o\
dmnhb.o\
dn2cvp.o\
dn2f.o\
dn2fb.o\
dn2g.o\
dn2gb.o\
dn2lrd.o\
dn2p.o\
dn2pb.o\
dn2rdp.o\
dnsf.o\
dnsfb.o\
dnsg.o\
dnsgb.o\
do7prd.o\
dparck.o\
dq7apl.o\
dq7rad.o\
dq7rfh.o\
dq7rsh.o\
dr7mdc.o\
dr7tvm.o\
drldst.o\
drmnf.o\
drmnfb.o\
drmng.o\
drmngb.o\
drmnh.o\
drmnhb.o\
drn2g.o\
drn2gb.o\
drnsg.o\
drnsgb.o\
ds3grd.o\
ds7bqn.o\
ds7cpr.o\
ds7dmp.o\
ds7grd.o\
ds7ipr.o\
ds7lup.o\
ds7lvm.o\
dsm.o\
dv2axy.o\
dv2nrm.o\
dv7cpy.o\
dv7dfl.o\
dv7ipr.o\
dv7prm.o\
dv7scl.o\
dv7scp.o\
dv7shf.o\
dv7swp.o\
dv7vmp.o\
dw7zbf.o\
dxtrap.o\
e9rint.o\
enter.o\
entsrc.o\
eprint.o\
erroff.o\
f7dhb.o\
f7hes.o\
g7itb.o\
g7lit.o\
g7qsb.o\
g7qts.o\
h2rfa.o\
h2rfg.o\
i0tk00.o\
i0tk01.o\
i7copy.o\
i7do.o\
i7mdcn.o\
i7pnvr.o\
i7shft.o\
i8save.o\
i8tsel.o\
ialloc.o\
istkgt.o\
istkin.o\
istkmd.o\
istkqu.o\
istkrl.o\
istkst.o\
itsum.o\
ivset.o\
l7itv.o\
l7ivm.o\
l7msb.o\
l7mst.o\
l7nvr.o\
l7sqr.o\
l7srt.o\
l7svn.o\
l7svx.o\
l7tsq.o\
l7tvm.o\
l7upd.o\
l7vml.o\
leave.o\
m7seq.o\
m7slo.o\
mnf.o\
mnfb.o\
mng.o\
mngb.o\
mnh.o\
mnhb.o\
movebc.o\
movebd.o\
movebi.o\
movebl.o\
movebr.o\
movefc.o\
movefd.o\
movefi.o\
movefl.o\
movefr.o\
mtstak.o\
n2cvp.o\
n2f.o\
n2fb.o\
n2g.o\
n2gb.o\
n2lrd.o\
n2p.o\
n2pb.o\
n2rdp.o\
n7msrt.o\
nerror.o\
nirall.o\
nsf.o\
nsfb.o\
nsg.o\
nsgb.o\
o7prd.o\
parck.o\
q7apl.o\
q7rad.o\
q7rfh.o\
q7rsh.o\
r7mdc.o\
r7tvm.o\
retsrc.o\
rldst.o\
rmnf.o\
rmnfb.o\
rmng.o\
rmngb.o\
rmnh.o\
rmnhb.o\
rn2g.o\
rn2gb.o\
rnsg.o\
rnsgb.o\
s3grd.o\
s7bqn.o\
s7cpr.o\
s7dmp.o\
s7etr.o\
s7grd.o\
s7ipr.o\
s7lup.o\
s7lvm.o\
s7rtdt.o\
s88fmt.o\
setc.o\
setd.o\
seterr.o\
seti.o\
setl.o\
setr.o\
srecap.o\
stinit.o\
stopx.o\
v2axy.o\
v2nrm.o\
v7cpy.o\
v7dfl.o\
v7ipr.o\
v7prm.o\
v7scl.o\
v7scp.o\
v7shf.o\
v7swp.o\
v7vmp.o\
w7zbf.o\
xtrap.o

.f.o:
	ifort $(FFLAGS) -c $<

lib$(LIB).a:	$(OBJ)
	ar -r lib$(LIB).a $?

install:	lib$(LIB).a
	mv lib$(LIB).a /usr/local/lib
	rm *.o

test: test.o
	ifort test.o -l$(LIB)
	time a.out

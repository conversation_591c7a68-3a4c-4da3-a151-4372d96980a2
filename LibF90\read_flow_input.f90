module read_flow_input


  implicit none

  private

  public :: read_restart_data_v01


contains

!=============================== READ_RESTART_DATA_V01 =======================80
!
! Reads input parameters from version 1 and version 0
!
!=============================================================================80

  subroutine read_restart_data_v01(title, xmach, alpha, yaw, re, tref, prandtl,&
                                  eqn_set, ivisc, iflim, nitfo, ivgrd,         &
                                  sref, cref, bref, xmc, ymc, zmc, cfl1, cfl2, &
                                  iramp, cflturb1, cflturb2, ncyc, iterwrt,    &
                                  rmstol, irest, jupdate, nsweep, ncyct,       &
                                  use_local_dt, itime, dt, subiters,           &
                                  version, gunit, unformat)

    use kinddefs, only : dp, system_r8
    use info_depr, only : complex_mode, beta

    logical, optional, intent(in)  :: unformat
    integer,           intent(in)  :: version, gunit
    character(80),     intent(out) :: title
    logical,           intent(out) :: use_local_dt
    integer,           intent(out) :: eqn_set, ivisc, iflim, nitfo
    integer,           intent(out) :: ivgrd, iramp, ncyc, iterwrt, irest
    integer,           intent(out) :: jupdate, nsweep, ncyct, itime
    integer,           intent(out) :: subiters
    real(dp),          intent(out) :: xmach, alpha, yaw, re, tref, prandtl
    real(dp),          intent(out) :: dt, sref, cref, bref, xmc, ymc, zmc
    real(dp),          intent(out) :: cfl1, cfl2, cflturb1, cflturb2, rmstol

    integer :: i_use_local_dt, i_deprecated

! beginNeverComplex
    real(system_r8) :: real_xmach, real_alpha, real_yaw, real_re, real_tref
    real(system_r8) :: real_prandtl
    real(system_r8) :: real_sref, real_cref, real_bref
    real(system_r8) :: real_xmc, real_ymc, real_zmc
    real(system_r8) :: real_cfl1, real_cfl2, real_cflturb1, real_cflturb2
    real(system_r8) :: real_rmstol, real_dt, real_dtau
! endNeverComplex

    integer,dimension(5) :: i_deprecated_mg

    continue

    if (present(unformat)) then
      read(gunit) title
      read(gunit)
      read(gunit)real_xmach, real_alpha, real_yaw,                             &
                 real_re, real_tref, real_prandtl
    else
      read(gunit,'(a)') title
      read(gunit,*)
      read(gunit,*)real_xmach, real_alpha, real_yaw,                           &
                   real_re, real_tref, real_prandtl
    end if

    if ( complex_mode ) then
      xmach   = cmplx(real_xmach,   0.0_dp, dp)
      alpha   = cmplx(real_alpha,   0.0_dp, dp)
      yaw     = cmplx(real_yaw,     0.0_dp, dp)
      re      = cmplx(real_re,      0.0_dp, dp)
      tref    = cmplx(real_tref,    0.0_dp, dp)
      prandtl = cmplx(real_prandtl, 0.0_dp, dp)
      beta    = cmplx(real_xmach,   0.0_dp, dp)
    else
      xmach   = real_xmach
      alpha   = real_alpha
      yaw     = real_yaw
      re      = real_re
      tref    = real_tref
      prandtl = real_prandtl
      beta    = real_xmach
    endif

    if (present(unformat)) then
      read(gunit)
      read(gunit)eqn_set, ivisc, iflim, nitfo, i_deprecated, ivgrd
      read(gunit)
      read(gunit)real_sref,real_cref,real_bref,real_xmc,real_ymc,real_zmc
    else
      read(gunit,*)
      read(gunit,*)eqn_set, ivisc, iflim, nitfo, i_deprecated, ivgrd
      read(gunit,*)
      read(gunit,*)real_sref,real_cref,real_bref,real_xmc,real_ymc,real_zmc
    end if

    if ( 0 == 1 ) write(*,*) i_deprecated

    if ( complex_mode ) then
      sref = cmplx(real_sref, 0.0_dp, dp)
      cref = cmplx(real_cref, 0.0_dp, dp)
      bref = cmplx(real_bref, 0.0_dp, dp)
      xmc  = cmplx(real_xmc,  0.0_dp, dp)
      ymc  = cmplx(real_ymc,  0.0_dp, dp)
      zmc  = cmplx(real_zmc,  0.0_dp, dp)
    else
      sref = real_sref
      cref = real_cref
      bref = real_bref
      xmc  = real_xmc
      ymc  = real_ymc
      zmc  = real_zmc
    endif

    if (present(unformat)) then
      read(gunit)
      read(gunit)real_cfl1, real_cfl2, iramp, real_cflturb1, real_cflturb2
    else
      read(gunit,*)
      read(gunit,*)real_cfl1, real_cfl2, iramp, real_cflturb1, real_cflturb2
    end if

    if ( complex_mode ) then
      cfl1     = cmplx(real_cfl1,     0.0_dp, dp)
      cfl2     = cmplx(real_cfl2,     0.0_dp, dp)
      cflturb1 = cmplx(real_cflturb1, 0.0_dp, dp)
      cflturb2 = cmplx(real_cflturb2, 0.0_dp, dp)
    else
      cfl1     = real_cfl1
      cfl2     = real_cfl2
      cflturb1 = real_cflturb1
      cflturb2 = real_cflturb2
    endif

    if (present(unformat)) then
      read(gunit)
      read(gunit)ncyc, iterwrt, real_rmstol, irest
    else
      read(gunit,*)
      read(gunit,*)ncyc, iterwrt, real_rmstol, irest
    end if

    if ( complex_mode ) then
      rmstol = cmplx(real_rmstol, 0.0_dp, dp)
    else
      rmstol = real_rmstol
    end if

    use_local_dt = .false.

    if (present(unformat)) then
      read(gunit)
    else
      read(gunit,*)
    end if

    if ( version == 0 ) then
      if (present(unformat)) then
        read(gunit)jupdate, nsweep, ncyct
      else
        read(gunit,*)jupdate, nsweep, ncyct
      end if
    else
      if (present(unformat)) then
        read(gunit)jupdate, nsweep, ncyct, i_use_local_dt
      else
        read(gunit,*)jupdate, nsweep, ncyct, i_use_local_dt
      end if
       if( i_use_local_dt == 1 ) use_local_dt = .true.
    end if

    if (present(unformat)) then
      read(gunit)
    else
      read(gunit,*)
    end if

    if ( version == 0 ) then
      if (present(unformat)) then
        read(gunit) itime, real_dt, real_dtau, subiters
      else
        read(gunit,*) itime, real_dt, real_dtau, subiters
      end if
!     convert old input to new expected internal values
      if( itime == 1 ) then
        itime = 0
        if( real_dt < 0.0_dp ) use_local_dt = .true.
        real_dt = abs(real_dt)
      else
        if( real_dtau < 0.0_dp ) use_local_dt = .true.
      endif
    else
      if (present(unformat)) then
        read(gunit) itime, real_dt, subiters
      else
        read(gunit,*) itime, real_dt, subiters
      end if
      real_dt = abs(real_dt)
    end if

!   now that itime is known, ensure that use_local_dt = .true. for
!   steady state cases

    if (itime == 0) use_local_dt = .true.

    if ( complex_mode ) then
      dt   = cmplx(real_dt,   0.0_dp, dp)
    else
      dt   = real_dt
    endif

    if (present(unformat)) then
      read(gunit)
      read(gunit) i_deprecated_mg(1:5)
      read(gunit)
      read(gunit) i_deprecated_mg(1:3)
    else
      read(gunit,*)
      read(gunit,*)i_deprecated_mg(1:5)
      read(gunit,*)
      read(gunit,*)i_deprecated_mg(1:3)
    end if

    if( 0 == 1 ) write(*,*) i_deprecated_mg

  end subroutine read_restart_data_v01

end module read_flow_input

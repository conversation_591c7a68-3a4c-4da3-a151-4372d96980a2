module pparty_mixed_element

  use kinddefs, only : dp, system_i1
  use lmpi,     only : lmpi_id, lmpi_nproc, lmpi_bcast, lmpi_min, lmpi_max,    &
                       lmpi_die, lmpi_conditional_stop, lmpi_reduce, lmpi_master
  implicit none

  private

  public :: edge_pointer_driver, edge_pointer_driver_cc, test_edges,           &
            sort_edges_2d, compute_cc_specific

  integer                        :: nlocs
  integer, dimension(:), pointer :: locs, locvc, locvc_type

contains

!============================= TEST_EDGES ====================================80
!
! test to make sure nedgeloc edges are consistent with level-01 nodes
!
!=============================================================================80

  subroutine test_edges(grid)

    use grid_types,         only : grid_type

    type(grid_type),      intent(in) :: grid

    logical :: b1, b2
    integer :: i, bsize, tag_size, word, word1, word2, node1, node2
    real(dp):: bsize_inv

    integer(system_i1), dimension(:), allocatable :: tag

  continue

! Set level01

    bsize     = bit_size(tag)
    bsize_inv = 1./bit_size(tag)

    tag_size = ceiling(grid%nnodesg*bsize_inv)
    allocate(tag(tag_size)); tag = 0

    ! tag level01
    do i = 1,grid%nnodes01
       word      = ceiling(grid%l2g(i)*bsize_inv)
       tag(word) = ibset(tag(word), mod(grid%l2g(i),bsize) )
    end do

! Test nedgeloc (should be level01)

    do i= 1,grid%nedgeloc
       node1 = grid%eptr(1,i)
       word1 = tag(ceiling(node1*bsize_inv))
       b1    = btest(word1, mod(node1,bsize))

       node2 = grid%eptr(2,i)
       word2 = tag(ceiling(node2*bsize_inv))
       b2    = btest(word2, mod(node2,bsize))

       if (.not.b1.or..not.b2) write(*,*)'ERROR ',lmpi_id,i,node1,node2
    end do

  end subroutine test_edges

!============================= EDGE_POINTER_DRIVER ===========================80
!
! driver routine to construct edge pointers
!
!=============================================================================80

  subroutine edge_pointer_driver(grid)

    use local_grid,   only : pp_nhead, pp_ntail, pp_nsize
    use grid_types,   only : grid_type
    use kinddefs,     only : system_i8
    use sort,         only : binary_search, heap_sort, small_sort
    use pparty_metis, only : adj_count

    type(grid_type),      intent(inout) :: grid

    integer :: n0, n1, n01, isum, icell, node1,node2,node3
    integer :: ielem, i1, i2, i, j, k, m, ipe, is,ie,last, ioff
    integer :: nedges, nedgeloc01, local_nedgeg, ierr, ierrg
    integer :: max_node01, my_adj_total, my_n0, istart, iend, n
    integer(system_i8) :: i8, running_offset !,j8

    integer, dimension(:), allocatable :: fwa, temp, t1, gcnt
    integer, dimension(:), allocatable :: adj_list, adj_count_local
    integer, dimension(:), allocatable :: my_l2g, my_cnt, my_adj

    integer(system_i8), dimension(:), allocatable :: gfwa

    logical :: b1, b2
    integer :: bsize, tag_size, word
    real(dp):: bsize_inv

    integer(system_i1), dimension(:), allocatable :: tag, skip
    integer,            dimension(:), allocatable :: tag012, lc2e

  continue

    n0 = grid%nnodes0
    n1 = grid%nnodes01 - grid%nnodes0
    n01 = grid%nnodes01

    ierr = 0
    if ( n0 <= 0 .or. n01 <= 0 ) then
      write(*,*) 'failure...lmpi_id,n0,n1,n01=',lmpi_id,n0,n1,n01
      ierr = 1
    endif
    call lmpi_reduce(ierr,ierrg)
    call lmpi_bcast(ierrg)
    if ( ierrg > 0 ) then
      if ( n0 <= 0 .or. n01 <= 0 ) then
      else
      write(*,*) 'success...lmpi_id,n0,n1,n01=',lmpi_id,n0,n1,n01
      endif
      if ( lmpi_master ) write(*,*) ' Some partitions contain no points!'
      if ( lmpi_master ) write(*,*) ' If multigrid, reduce ngrid_request.'
    endif
    call lmpi_conditional_stop(ierr,'edge_pointer_driver')

    allocate(t1(n01))
    t1 = grid%l2g
    call heap_sort(n01,t1)
    max_node01 = t1(n01)

    allocate(tag012(n01)); tag012 = 0

    last = 1
    do i = 1,n1
       node1 = grid%l2g(n0+i)
       do j = last,n01
          if (t1(j) == node1) then
             tag012(j) = 1
             last = j
             exit
          end if
       end do
    end do

    ! First collect the adj_count each local node from ParMetis.
    ! NOTE: retained adj_count from ParMetis. (passed forward)

    allocate(fwa(n01+1)); fwa = 0

    running_offset = 0
    isum = 1
    last = 1
    allocate(temp(pp_nsize(0))); temp = 0
    out1: do ipe = 0, lmpi_nproc-1
       is = pp_nhead(ipe)
       ie = pp_ntail(ipe)
       if (size(temp) /= pp_nsize(ipe)) then
          deallocate(temp)
          allocate(temp(pp_nsize(ipe))); temp = 0
       end if
       if (lmpi_id == ipe) temp = adj_count
       call lmpi_bcast(temp,ipe)

       if (last > n01) cycle
       k = pp_nhead(ipe)-1
       do i = 1,pp_nsize(ipe) ! is,ie
          if (i+k == t1(last)) then
             fwa(last) = isum
             isum = isum + temp(i)
             last = last + 1
             if (last > n01) exit
          end if
       end do
    end do out1
    fwa(n01+1) = isum
    deallocate(temp)

!------------------------------
! Build adjacency list for n01 using local c2n

    allocate(adj_list(isum)); adj_list = 0
    allocate(lc2e(isum+1));   lc2e = 0

    ! Build adjancy_list for nodes level01

    allocate(adj_count_local(n01)); adj_count_local = 0

    do ielem = 1,grid%nelem

       do icell = 1,grid%elem(ielem)%ncell

          loop_ie: do ie = 1,grid%elem(ielem)%edge_per_cell

            node1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
            node2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)

             if (node2 < node1) then ! swap
                node3 = node1; node1 = node2; node2 = node3
             end if

             j = binary_search(n01,t1,node1)
             if (adj_count_local(j) > 0) then
                do k = fwa(j),fwa(j)+(adj_count_local(j)-1)
                   if (adj_list(k) == node2) cycle loop_ie
                end do
             end if
             adj_list(fwa(j)+adj_count_local(j)) = node2
             adj_count_local(j) = adj_count_local(j) + 1

           end do loop_ie
       end do
    end do

    isum = 0
    do i = 1,n01
       isum = isum + adj_count_local(i)
       call small_sort(adj_count_local(i),                                     &
                  adj_list(fwa(i):fwa(i)+adj_count_local(i)-1))
    end do

!------------------------------
! Fill out level 1 adjacency list

    is = pp_nhead(lmpi_id)
    ie = pp_ntail(lmpi_id)
    allocate(gcnt(is:ie)); gcnt = 0

    do ipe = 0,lmpi_nproc-1

       if (ipe == lmpi_id) then
          my_n0        = n0
          my_adj_total = 0
          do i = 1,n01
             if (tag012(i) == 0) my_adj_total = my_adj_total+adj_count_local(i)
          end do
         !write(*,*) 'my_adj_total ',lmpi_id,my_adj_total
       end if
       call lmpi_bcast(my_n0,ipe)
       allocate(my_cnt(my_n0)); my_cnt = 0
       allocate(my_l2g(my_n0)); my_l2g = 0

       call lmpi_bcast(my_adj_total,ipe)
       allocate(my_adj(my_adj_total)); my_adj = 0

       if (ipe == lmpi_id) then
          j = 1
          k = 1
          do i = 1,n01
             if (tag012(i) == 0) then
                 my_l2g(j) = t1(i)
                 my_cnt(j) = adj_count_local(i)
                 my_adj(k:k+(my_cnt(j)-1))=adj_list(fwa(i):fwa(i)+my_cnt(j)-1)
                 k = k + my_cnt(j)
                 j = j + 1
             end if
          end do
       end if
       call lmpi_bcast(my_l2g,ipe)
       call lmpi_bcast(my_cnt,ipe)
       call lmpi_bcast(my_adj,ipe)

       do i = 1,my_n0
          if ((my_l2g(i)>=is).and.(my_l2g(i)<=ie)) gcnt(my_l2g(i)) = my_cnt(i)
       end do

       if (ipe /= lmpi_id) then
          ioff = 0
          do i = 1,my_n0
             j = binary_search(n01,t1,my_l2g(i))
             if (j > 0) then
               out4: do m = 1,my_cnt(i)
                   node2 = my_adj(ioff + m)
                   if (adj_count_local(j) > 0) then
                      istart = fwa(j)
                      iend   = fwa(j)+(adj_count_local(j)-1)
                      do k = istart,iend
                         if (adj_list(k) == node2) cycle out4
                      end do
                      adj_count_local(j) = adj_count_local(j)+1
                      do k = istart,iend
                         if (iabs(adj_list(k)) > node2) then
                         ! insertion sort
                           do n = iend,k,-1
                              adj_list(n+1) = adj_list(n)
                           end do
                           adj_list(k) = -node2
                           cycle out4
                         end if
                      end do
                      adj_list(iend+1) = -node2
                   else
                      adj_count_local(j) = 1
                      adj_list(fwa(j)) = -node2
                   end if
                end do out4
             end if
             ioff = ioff + my_cnt(i)
          end do
       end if

       deallocate(my_adj,my_cnt,my_l2g)

    end do ! ipe

!------------------------------
! Compute global fwa using global count

    allocate(gfwa(n01)); gfwa = 0
    running_offset = 1
    last = 1
    do ipe = 0, lmpi_nproc-1
       is = pp_nhead(ipe)
       ie = pp_ntail(ipe)
       if (ipe > 0) deallocate(temp)
       allocate(temp(is:ie))
       if (lmpi_id == ipe) temp = gcnt
       call lmpi_bcast(temp,ipe)

       do i = is,ie
          if (last <= n01) then
             if (i == t1(last)) then
                gfwa(last) = running_offset
                last = last + 1
             end if
          end if
          running_offset = running_offset + temp(i)
       end do
    end do
!write(20000+lmpi_id,*)'gfwa ',running_offset,n01,gfwa(n01)
    deallocate(temp)
    deallocate(gcnt)

!------------------------------

! Compute eptr

! Beyond this simple rule, the edges belonging to the partition
! are sorted so that those edges that have both nodes
! on level 0 of the partition are put in the top of the list,
! those nodes that have only one node on level 0 are put
! next in the list.

    allocate(skip(size(adj_list))); skip = 0
    do i = 1,size(adj_list)
       if (adj_list(i) < 0) then
          skip(i) = 1
          adj_list(i) = -adj_list(i)
       end if
    end do

    local_nedgeg = isum ! grid%nedgeg = isum
    allocate(grid%eptr(2,local_nedgeg)); grid%eptr = 0
    allocate(grid%el2g(local_nedgeg));   grid%el2g = 0

    bsize     = bit_size(tag)
    bsize_inv = 1._dp/bit_size(tag)

    tag_size = ceiling(max_node01*bsize_inv)
    allocate(tag(tag_size))
    tag = 0

    ! tag level0
    do i = 1,n0
       word      = ceiling(grid%l2g(i)*bsize_inv)
       tag(word) = ibset(tag(word), mod(grid%l2g(i),bsize) )
    end do

    ! collect level0 edges (both nodes level0)

    nedges = 0
    do i = 1,n01
       if ((tag012(i)==0).and.(adj_count_local(i) > 0)) then
          node1 = t1(i)
          do j = 0,adj_count_local(i)-1
             if (skip(fwa(i)+j) == 1) cycle
             node2 = adj_list(fwa(i)+j)
             word = tag(ceiling(node2*bsize_inv))
             b2 = btest(word, mod(node2,bsize))
             if (b2) then
                nedges = nedges + 1
                grid%eptr(1,nedges) = node1
                grid%eptr(2,nedges) = node2
                grid%el2g(  nedges) = gfwa(i)+j
                lc2e(fwa(i)+j) = nedges
                adj_list(fwa(i)+j) = -adj_list(fwa(i)+j) ! tag as used
             end if
          end do
       end if
    end do
!   write(*,*)'TOP NE = ',lmpi_id,nedges

    ! collect level0/1 edges (only one level0)
    do i = 1,n01
       if (adj_count_local(i) > 0) then
          node1 = t1(i)
          word = tag(ceiling(node1*bsize_inv))
          b1 = btest(word, mod(node1,bsize))
          do j = 0,adj_count_local(i)-1
             if (skip(fwa(i)+j) == 1) cycle
             node2 = adj_list(fwa(i)+j)
             if (node2 > 0) then
                word = tag(ceiling(node2*bsize_inv))
                b2 = btest(word, mod(node2,bsize))
                if ((b1.and..not.b2).or.(.not.b1.and.b2)) then
                   nedges = nedges + 1
                   grid%eptr(1,nedges) = node1
                   grid%eptr(2,nedges) = node2
                   grid%el2g(  nedges) = gfwa(i)+j
                   lc2e(fwa(i)+j) = nedges
                   adj_list(fwa(i)+j) = -adj_list(fwa(i)+j) ! tag as used
                end if
             end if
          end do
       end if
    end do
    nedgeloc01 = nedges
!   write(*,*)'MID NE = ',lmpi_id,nedges,nedgeloc01

    ! collect level1 edges (both nodes level1)
    do i = 1,n01
       if ((tag012(i) > 0).and.(adj_count_local(i) > 0)) then
          node1 = t1(i)
          do j = 0,adj_count_local(i)-1
             if (skip(fwa(i)+j) == 1) cycle
             node2 = adj_list(fwa(i)+j)
             if (node2 > 0) then
                nedges = nedges + 1
                grid%eptr(1,nedges) = node1
                grid%eptr(2,nedges) = node2
                grid%el2g(  nedges) = gfwa(i)+j
                lc2e(fwa(i)+j) = nedges
             end if
          end do
       end if
    end do

    deallocate(tag)
    adj_list = iabs(adj_list)
    deallocate(skip)

!   if (db1 == 1) then
!      write(*,*)'BOTA NE,NEL,NEL01 = ',lmpi_id,nedges,nedgeloc,nedgeloc01
!   end if
    grid%nedgeloc = nedgeloc01
    grid%nedge    = nedges

!------------------------------------------------------------------------------

    ! allocate and compute c2e

    do ielem = 1,grid%nelem
       i1 = grid%elem(ielem)%edge_per_cell
       i2 = grid%elem(ielem)%ncell
       allocate(grid%elem(ielem)%c2e(i1,i2))
       grid%elem(ielem)%c2e = 0

       do icell = 1,grid%elem(ielem)%ncell
          do ie = 1,grid%elem(ielem)%edge_per_cell

             node1=grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
             node2=grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)

             if (node2 < node1) then ! swap
                node3 = node1; node1 = node2; node2 = node3
             end if

             j = binary_search(n01,t1,node1)
             do k = fwa(j),fwa(j)+(adj_count_local(j)-1)
                if (adj_list(k) == node2) then
                   grid%elem(ielem)%c2e(ie,icell) = lc2e(k)
                   exit
                end if
             end do

           end do
       end do
    end do
    deallocate(lc2e)

!---------------------------------------------------------------------

    deallocate(adj_list)
    deallocate(fwa)
    deallocate(adj_count_local)
    deallocate(tag012)
    deallocate(t1)

    i8 = 0
    do i = 1,size(grid%el2g)
       if (grid%el2g(i) > i8) i8 = grid%el2g(i)
    end do
    call lmpi_max(i8,grid%nedgeg)
    call lmpi_bcast(grid%nedgeg)

   !if (grid%cc) then
   !   write(*,*)"EXIT edge_pointer_driver ",lmpi_id,grid%nedgeloc,grid%nedge
   !   do i = 1,grid%nedge
   !      write(92000+lmpi_id,*) grid%el2g(i),grid%eptr(1:2,i)
   !   end do
   !end if

  end subroutine edge_pointer_driver


!============================= EDGE_POINTER_DRIVER_CC ========================80
!
! driver routine to construct edge pointers (CC)
!
!=============================================================================80

  subroutine edge_pointer_driver_cc(grid) ! nedgeg

    use local_grid,   only : pp_nhead, pp_ntail
    use kinddefs,     only : system_i8
    use grid_types,   only : grid_type
    use pparty_metis, only : cc_adj_count, cc_adj
    use sort,         only : binary_search, heap_sort, small_sort

    type(grid_type),      intent(inout) :: grid

    integer :: n01, icell, node1,node2,node3
    integer :: ielem, i1, i2, i, j, k, m, ipe, is,ie, ioff, edge
    integer :: nedges, isize, ilast, ival, goff
    integer(system_i8) :: i8, j8

    logical :: b1, b2

    integer, dimension(:), allocatable :: fwa, temp, temp1, ia, ja
    integer, dimension(:), allocatable :: t1, ja_el2g, lc2e

    integer(system_i1), dimension(:), allocatable :: L01, tag

  continue

if ((grid%cc).and.(lmpi_nproc==1).and.(grid%nelem > 1)) then

    write(*,*)"HARDWIRED CC NPROC=1 for testing ************"
    allocate(grid%eptr(2,grid%nedge)); grid%eptr = 0
    allocate(grid%el2g(grid%nedge));   grid%el2g = 0
    do ielem = 1,grid%nelem
       i1 = grid%elem(ielem)%edge_per_cell
       i2 = grid%elem(ielem)%ncell
       allocate(grid%elem(ielem)%c2e(i1,i2)); grid%elem(ielem)%c2e = 0
    end do

    edge = 0
    do ielem  = 1,grid%nelem
       do icell = 1,grid%elem(ielem)%ncell
          loop_ie: do ie = 1,grid%elem(ielem)%edge_per_cell
             i = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
             j = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)
             node1 = min(i,j)
             node2 = max(i,j)
             do i = 1,edge
                if ((grid%eptr(1,i)==node1).and.(grid%eptr(2,i)==node2)) then
                   grid%elem(ielem)%c2e(ie,icell) = i
                   cycle loop_ie
                end if
             end do
             edge = edge + 1
             grid%el2g(edge) = edge
             grid%eptr(1,edge) = node1
             grid%eptr(2,edge) = node2
             grid%elem(ielem)%c2e(ie,icell) = edge
         end do loop_ie
      end do
    end do
    grid%nedge    = edge
    grid%nedgeloc = edge
    grid%nedgeg   = edge
    do i = 1,grid%nedge
      write(92000+lmpi_id,'(1x,i0,1x,i0,1x,i0,1x)')grid%eptr(1:2,i),grid%el2g(i)
    end do

    allocate(tag(grid%nnodesg)); tag = 0
    do i = 1,grid%nnodes01
       tag(grid%l2g(i)) = 1
    end do

! Test nedgeloc (should be level01)

    do i= 1,grid%nedgeloc
       node1 = grid%eptr(1,i)
       node2 = grid%eptr(2,i)
       if ((tag(node1)==0).or.(tag(node2)==0))                                 &
          write(*,*)'ERROR ',lmpi_id,i,node1,node2
    end do
    deallocate(tag)

    return
end if

    n01 = grid%nnodes01

    allocate(t1(n01))
    t1 = grid%l2g
    call heap_sort(n01,t1)

    allocate(L01(n01)); L01 = 1
    do ielem = 1,grid%nelem
       do icell = 1,grid%elem(ielem)%ncell0
          do ie = 1,grid%elem(ielem)%edge_per_cell
            node1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
            node2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)
            i1 = binary_search(n01,t1,node1)
            L01(i1) = 0
          end do
       end do
    end do

    allocate(ia(n01)); ia = 0

    do ielem = 1,grid%nelem
       do icell = 1,grid%elem(ielem)%ncell
          do ie = 1,grid%elem(ielem)%edge_per_cell
            node1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
            node2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)
            if (node2 > node1) then
               i1 = binary_search(n01,t1,node1)
               ia(i1) = ia(i1) + 1
            else
               i2 = binary_search(n01,t1,node2)
               ia(i2) = ia(i2) + 1
            end if
          end do
       end do
    end do

    allocate(fwa(n01+1)); fwa = 0
    fwa(1) = 1
    do i = 2,n01+1
       fwa(i) = fwa(i-1) + ia(i-1)
    end do
    !do i = 1,n01
    !   write(900+lmpi_id,*) ia(i),fwa(i)
    !end do

    allocate(ja(fwa(n01+1))); ja = 0

    ia = 0
    do ielem = 1,grid%nelem
       do icell = 1,grid%elem(ielem)%ncell
          do ie = 1,grid%elem(ielem)%edge_per_cell
            node1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
            node2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)
            if (node2 > node1) then
               i1 = binary_search(n01,t1,node1)
               ja(fwa(i1)+ ia(i1)) = node2
               ia(i1) = ia(i1) + 1
            else
               i2 = binary_search(n01,t1,node2)
               ja(fwa(i2)+ ia(i2)) = node1
               ia(i2) = ia(i2) + 1
            end if
          end do
       end do
    end do

    do i = 1,n01
       is = fwa(i)
       ie = (is + ia(i))-1
       isize = (ie-is)+2 ! extra trailer
       allocate(temp1(isize))
       temp1(1:isize-1) = ja(is:ie)
       temp1(isize) = grid%nnodesg+1
       call small_sort(isize,temp1)
       ia(i) = 0
       do j = 1,isize-1
          if (temp1(j) /= temp1(j+1)) then
             ja(is + ia(i)) = temp1(j)
             ia(i) = ia(i) + 1
          end if
       end do
       ie = (is + ia(i))-1
       deallocate(temp1)
    end do

    !   do i = 1,n01
    !      is = fwa(i)
    !      ie = (is + ia(i))-1
    !      write(1910+lmpi_id,'(1x,i0," : ",i0,1x," :: ",100(i0,1x))')  &
    !        t1(i),ia(i),ja(is:ie)
    !   end do
    !call lmpi_conditional_stop(40,'See 1910 -- local ia,ja')

    grid%nedge = sum(ia)
    grid%nedgeloc = grid%nedge
    i8 = sum(cc_adj_count)
    call lmpi_reduce(i8,j8); grid%nedgeg = j8
    call lmpi_bcast(grid%nedgeg)
    grid%nedgeg = grid%nedgeg

! Gather the el2g from cc_adj_count and cc_adj for each local edge
!
! Essentially, pass a (whole) list of sorted (whole) ragged arrays, and
! compare to a (partial) list of (partial) ragged arrays.

    allocate(ja_el2g(size(ja))); ja_el2g = 0

    ilast = 1 ! last local value examined
    ! loff  = 0 ! running fwa global eptr slice
    goff  = 0 ! running fwa global eptr
    do ipe = 0, lmpi_nproc-1
       is = pp_nhead(ipe)
       ie = pp_ntail(ipe)
       allocate(temp(is:ie))
       if (lmpi_id == ipe) then
          temp = cc_adj_count
          j = size(cc_adj)
       end if
       call lmpi_bcast(temp,ipe)
       call lmpi_bcast(j,ipe)
       allocate(temp1(j)); temp1 = 0
       if (lmpi_id == ipe) temp1 = cc_adj
       call lmpi_bcast(temp1,ipe)

       ! ASSERT SUM(15K) == SUM(5K)
       !do i = is,ie
       !   loff = 0
       !   if (i > is) loff = sum(temp(is:i-1))
       !   do j = 1,temp(i)
       !      write(15000+lmpi_id,'(1x,i0,1x,i0,1x,i0,1x)') &
       !        i,temp1(loff+j),goff+loff+j
       !   end do
       !end do
       if (ilast <= n01) then
          do i = 1,n01
             ival = t1(i)
             if ((ival >= is).and.(ival <= ie)) then
                if ((temp(ival) > 0).and.(ia(i) > 0)) then ! two short lists
                   ioff = 0
                   if (ival > is) ioff = sum(temp(is:ival-1))
                   do j = fwa(i),fwa(i)+ia(i)-1
                      do k = 1,temp(ival)
                         if (temp1(ioff+k) == ja(j)) ja_el2g(j) = goff+ioff+k
                      end do
                   end do
                end if
             end if
          end do
       end if
       goff = goff + sum(temp)
       deallocate(temp,temp1)
    end do

!---------------------
! Compute eptr

! Beyond this simple rule, the edges belonging to the partition
! are sorted so that those edges that have both nodes
! on level 0 of the partition are put in the top of the list,
! those nodes that have only one node on level 0 are put
! next in the list.

    allocate(grid%eptr(2,grid%nedge)); grid%eptr = 0
    allocate(grid%el2g(grid%nedge));   grid%el2g = 0
    allocate(lc2e(fwa(n01+1)));        lc2e      = 0

    ! collect level0 edges (both nodes level0)

    nedges = 0
    do i = 1,n01
       if ((L01(i)==0).and.(ia(i) > 0)) then
          node1 = t1(i)
          k = fwa(i)-1
          do j = 1,ia(i)
             node2 = ja(k+j)
             m = binary_search(n01,t1,node2)
             if (L01(m)==0) then
                nedges = nedges + 1
                grid%eptr(1,nedges) = node1
                grid%eptr(2,nedges) = node2
                grid%el2g(  nedges) = ja_el2g(k+j)
                lc2e(k+j) = nedges
                ja(k+j) = -ja(k+j) ! tag as used
             end if
          end do
       end if
    end do
!   write(*,*)'TOP NE = ',lmpi_id,nedges,grid%nedge

    ! collect level0/1 edges (only one level0)
    do i = 1,n01
       if (ia(i) > 0) then
          node1 = t1(i)
          k = fwa(i)-1
          b1 = (L01(i) == 0)
          do j = 1,ia(i)
             node2 = ja(k+j)
             if (node2 > 0) then
                m = binary_search(n01,t1,node2)
                b2 = (L01(m) == 0)
                if ((b1.and..not.b2).or.(.not.b1.and.b2)) then
                   nedges = nedges + 1
                   grid%eptr(1,nedges) = node1
                   grid%eptr(2,nedges) = node2
                   grid%el2g(  nedges) = ja_el2g(k+j)
                   lc2e(k+j) = nedges
                   ja(k+j) = -ja(k+j) ! tag as used
                end if
             end if
          end do
       end if
    end do
!   write(*,*)'MID NE = ',lmpi_id,nedges,grid%nedge

    ! collect level1 edges (both nodes level1)
    do i = 1,n01
       node1 = t1(i)
       k = fwa(i)-1
       if (L01(i) == 1) then
          do j = 1,ia(i)
             node2 = ja(k+j)
             if (node2 > 0) then
                nedges = nedges + 1
                grid%eptr(1,nedges) = node1
                grid%eptr(2,nedges) = node2
                grid%el2g(  nedges) = ja_el2g(k+j)
                lc2e(k+j) = nedges
             end if
          end do
       end if
    end do
    deallocate(L01)
    deallocate(ja_el2g)
!   write(*,*)'BOTA NE,NEL,NEL01 = ',lmpi_id,nedges,grid%nedge

!------------------------------------------------------------------------------
! allocate and compute c2e

    ja = iabs(ja)
    do ielem = 1,grid%nelem
       i1 = grid%elem(ielem)%edge_per_cell
       i2 = grid%elem(ielem)%ncell
       allocate(grid%elem(ielem)%c2e(i1,i2)); grid%elem(ielem)%c2e = 0

       do icell = 1,grid%elem(ielem)%ncell
          do ie = 1,grid%elem(ielem)%edge_per_cell

             node1=grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
             node2=grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)

             if (node2 < node1) then ! swap
                node3 = node1; node1 = node2; node2 = node3
             end if

             j = binary_search(n01,t1,node1)
             m = -1
             do k = fwa(j),fwa(j)+(ia(j)-1)
                if (ja(k) == node2) then
                   m = lc2e(k)
                   grid%elem(ielem)%c2e(ie,icell) = lc2e(k)
                   exit
                end if
             end do
             if (m == -1) then
                write(*,*)'Internal error LC2E: ',lmpi_id,ielem,icell,ie,' FAIL'
                call lmpi_conditional_stop(1,'Internal error LC2E')
             end if
           end do
       end do
    end do
    call lmpi_conditional_stop(0)
    deallocate(lc2e)

!---------------------------------------------------------------------
!ASSERT SUM(92K) = SUM(15k)
!   do i = 1,grid%nedge
!     write(92000+lmpi_id,'(1x,i0,1x,i0,1x,i0,1x)')grid%eptr(1:2,i),grid%el2g(i)
!   end do
!   call lmpi_conditional_stop(20,"END edge_pointer_driver_cc; see 92K")
!
!   do ielem = 1,grid%nelem
!      epc = grid%elem(ielem)%edge_per_cell
!      do icell = 1,grid%elem(ielem)%ncell
!         do ie = 1,grid%elem(ielem)%edge_per_cell
!            node1=grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
!            node2=grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)
!            if (node2 < node1) then ! swap
!               node3 = node1; node1 = node2; node2 = node3
!            end if
!            i = grid%elem(ielem)%c2e(ie,icell)
!            node3 = grid%eptr(1,grid%elem(ielem)%c2e(ie,icell))
!            node4 = grid%eptr(2,grid%elem(ielem)%c2e(ie,icell))
!            if (node4 < node3) then ! swap
!               node5 = node3; node3 = node4; node4 = node5
!            end if
!            b1 = ((node1==node3).and.(node2==node4))
!            write(93000+lmpi_id,'(1x,6(i0,1x),1x,1L)') &
!              icell,ie,node1,node2,node3,node4,b1
!          end do
!      end do
!   end do

!---------------------------------------------------------------------
! deallocate arrays

    deallocate(t1,fwa,ia,ja)

  end subroutine edge_pointer_driver_cc

!============================= COMPUTE_CC_SPECIFIC ===========================80
!
! Compute the cc specific variables not computed by the NC path.
! grid: (nface) fptr, area_face, xyzn_face, xyz_face, (fl2g?)
! grid: (ncell01) cell_vol, xyzc, (cl2g?), (rlsq_ia?)
!
!=============================================================================80

  subroutine compute_cc_specific(grid)

    use grid_types,   only : grid_type
    use sort,         only : binary_search, heap_sort
   !use puns3ds,      only : face_metrics, check_face_metrics_par
    use info_depr,    only : cc_centroids
    use skewness,     only : check_face_skewness
    use bc_cache_cc,  only : bc_ghost

    type(grid_type), intent(inout) :: grid

    integer :: ielem, icell, iface, fpc
    integer :: i,k,L, n(4), if1,if2,if3,if4, ict,ib
    integer :: icell1, icell2, ncell0, icell1_ind

! DANA CC NEW (in merge)
!   integer                        :: nlocs, nlocvc
!   integer, dimension(:), pointer :: locs,  locvc, locvc_type

    integer, dimension(:), allocatable :: temp_cl2gs, temp_cl2gs_ind
    integer, dimension(:), allocatable :: temp0,  temp0s,  temp0s_ind

    logical :: verbose = .false.

  continue

  !write(*,*)"ENTER compute_cc_specific ",lmpi_id,grid%nbfaceg,grid%nbface0

! Map cells to current grid%cl2g

    allocate(temp_cl2gs_ind(grid%ncell01)); temp_cl2gs_ind = 0
    call heap_sort(grid%ncell01,grid%cl2g,temp_cl2gs_ind)
    allocate(temp_cl2gs(grid%ncell01)); temp_cl2gs = 0
    temp_cl2gs = grid%cl2g(temp_cl2gs_ind)

! allocate and populate cell_map

   do ielem = 1,grid%nelem
      allocate(grid%elem(ielem)%cell_map(grid%elem(ielem)%ncell))
      do icell = 1,grid%elem(ielem)%ncell
         icell1 = grid%elem(ielem)%cl2g(icell)
         k = binary_search(grid%ncell01,temp_cl2gs,icell1)
         if (k == 0) then
            write(*,*)"BAD BS (9) ",ielem,icell,icell1
            call lmpi_conditional_stop(1)
         end if
         grid%elem(ielem)%cell_map(icell) = temp_cl2gs_ind(k)
      end do
   end do
   call lmpi_conditional_stop(0)

! KEEP
!  if (db) then
!  do ielem = 1,grid%nelem
!     npc = grid%elem(ielem)%node_per_cell
!     do icell = 1,grid%elem(ielem)%ncell
!        write(66000+lmpi_id,'(1x,i0," : ",8(i0,1x))')                         &
!          grid%elem(ielem)%cl2g(icell),                                       &
!          grid%l2g(grid%elem(ielem)%c2n(1:npc,icell))
!        do j = 1,npc
!           write(67000+lmpi_id,'(1x,i0," : ",8(1x,i0,1x))')                   &
!             grid%l2g(grid%elem(ielem)%c2n(j,icell)),                         &
!             grid%l2g(grid%elem(ielem)%c2n(1:npc,icell))
!        end do
!     end do
!  end do
!  call lmpi_conditional_stop(1,'See 66k--cl2g,l2g')
!  end if ! db

! Gather level0 cells

   ncell0 = sum(grid%elem(:)%ncell0)
   allocate(temp0(ncell0)); temp0 = 0
   i = 0
   do ielem = 1,grid%nelem
      do icell = 1,grid%elem(ielem)%ncell0
         i = i + 1
         temp0(i) = grid%elem(ielem)%cl2g(icell)
      end do
   end do
   allocate(temp0s_ind(ncell0)); temp0s_ind = 0
   call heap_sort(ncell0,temp0,temp0s_ind)
   allocate(temp0s(ncell0)); temp0s = temp0(temp0s_ind)
   deallocate(temp0)

! Count and allocate fptr (using c2c)

   allocate(grid%fptr(6,grid%nface)); grid%fptr = 0
   allocate(grid%fl2g(grid%nface));   grid%fl2g = 0

! KEEP Debug
!
!  if (db) then
!  do ielem = 1,grid%nelem
!     fpc = grid%elem(ielem)%face_per_cell
!     do icell = 1,grid%elem(ielem)%ncell0
!        do iface = 1,fpc
!          icell2 = grid%elem(ielem)%c2c(iface,icell)
!          if (icell2 /= 0) then
!          n(1)=grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,1),icell)
!          n(2)=grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,2),icell)
!          n(3)=grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,3),icell)
!          if (grid%elem(ielem)%local_f2n(iface,1) ==                          &
!               grid%elem(ielem)%local_f2n(iface,4)) then
!               n(4) = n(1)
!           else
!           n(4)=grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,4),icell)
!           end if
!           if (grid%elem(ielem)%cl2g(icell) < icell2) then
!             write(70000+lmpi_id,'(1x,6(i0,1x))')                             &
!              grid%elem(ielem)%cl2g(icell),icell2, grid%l2g(n(1:4))
!           end if
!          end if
!        end do
!     end do
!  end do
!  end if ! db

! count unique faces

   grid%nface = 0
   do ielem = 1,grid%nelem
      fpc = grid%elem(ielem)%face_per_cell
      do icell = 1,grid%elem(ielem)%ncell0
         icell1 = grid%elem(ielem)%cl2g(icell)
         do iface = 1,fpc
            icell2 = grid%elem(ielem)%c2c(iface,icell)
            if (icell2 /= 0) then
               if (icell1 < icell2) then
                  grid%nface = grid%nface + 1
               else
                  k = binary_search(grid%ncell0,temp0s,icell2)
                  if (k == 0) grid%nface = grid%nface + 1
               end if
            end if
         end do
      end do
   end do

! Populate grid%fptr using c2c

   allocate(grid%fptr(6,grid%nface)); grid%fptr = 0
   ict = 0
   do ielem = 1,grid%nelem
      fpc = grid%elem(ielem)%face_per_cell
      do icell = 1,grid%elem(ielem)%ncell0
         icell1 = grid%elem(ielem)%cl2g(icell)
         k = binary_search(grid%ncell01,temp_cl2gs,icell1)
         icell1_ind = temp_cl2gs_ind(k)
         do iface = 1,fpc
            icell2 = grid%elem(ielem)%c2c(iface,icell)
            if (icell2 /= 0) then
               L = 1
               if (icell1 < icell2) then
                  L = 0
               else
                  L = binary_search(grid%ncell0,temp0s,icell2)
                  ! if L==0, then use it (looking for 0)
               end if
               if (L == 0) then
                  ict = ict + 1
                  grid%fptr(1,ict) = icell1_ind
                  k = binary_search(grid%ncell01,temp_cl2gs,icell2)
                  grid%fptr(2,ict) = temp_cl2gs_ind(k)
                  if1 = grid%elem(ielem)%local_f2n(iface,1)
                  if2 = grid%elem(ielem)%local_f2n(iface,2)
                  if3 = grid%elem(ielem)%local_f2n(iface,3)
                  n(1)=grid%elem(ielem)%c2n(if1,icell)
                  n(2)=grid%elem(ielem)%c2n(if2,icell)
                  n(3)=grid%elem(ielem)%c2n(if3,icell)
                  if4 = grid%elem(ielem)%local_f2n(iface,4)
                  if (if1 == if4) then
                     n(4) = n(1)
                  else
                     n(4)=grid%elem(ielem)%c2n(if4,icell)
                  end if
                  grid%fptr(3:6,ict) = n(1:4)
               end if
            end if
         end do
      end do
   end do

! Compute ncell01 related variables
!  cell_vol, xc,yc,zc,(cl2g),(rlsq_ia)

   allocate(grid%cell_vol(grid%ncell01)); grid%cell_vol = 0.0_dp
   allocate(grid%xc(grid%ncell01));       grid%xc = 0.0_dp
   allocate(grid%yc(grid%ncell01));       grid%yc = 0.0_dp
   allocate(grid%zc(grid%ncell01));       grid%zc = 0.0_dp

! Compute nface related variables
!  fptr,area_face,xn_face,yn_face,zn_face,x_face,y_face,z_face, (fl2g, flsq_ia)

   allocate(grid%area_face(grid%nface)); grid%area_face = 0.0_dp
   allocate(grid%xn_face(grid%nface));   grid%xn_face   = 0.0_dp
   allocate(grid%yn_face(grid%nface));   grid%yn_face   = 0.0_dp
   allocate(grid%zn_face(grid%nface));   grid%zn_face   = 0.0_dp
   allocate(grid%x_face(grid%nface));    grid%x_face    = 0.0_dp
   allocate(grid%y_face(grid%nface));    grid%y_face    = 0.0_dp
   allocate(grid%z_face(grid%nface));    grid%z_face    = 0.0_dp

   if ( cc_centroids ) then
     call lmpi_conditional_stop(1,'cface_metrics not tested.')
     ! Probably only minor changes, like ncell vs ncellg (etc.)

!    !...compute metrics with centroid as cell center.
!    call cface_metrics(grid%nnodesg, grid%ncellg, grid%x, grid%y, grid%z,     &
!           grid%nelem, grid%elem, grid%cell_vol, grid%xc, grid%yc, grid%zc,   &
!           grid%nfaceg, grid%xn_face, grid%yn_face, grid%zn_face,             &
!           grid%area_face,                                                    &
!           grid%fptr, grid%x_face, grid%y_face, grid%z_face, verbose)
   else
     call face_metrics(grid%nnodes01, grid%ncell01,                            &
            grid%x, grid%y, grid%z,                                            &
            grid%nelem,                                                        &
            grid%elem, grid%cell_vol, grid%xc,                                 &
            grid%yc, grid%zc, grid%nface,              & ! grid%nface not nfaceg
            grid%xn_face, grid%yn_face,                                        &
            grid%zn_face, grid%area_face,                                      &
            grid%fptr, grid%x_face,                                            &
            grid%y_face, grid%z_face, verbose)
   endif

! KEEP
!  if (db) then
!  do icell = 1,grid%ncell01
!     write(80000+lmpi_id,'(1x,i0,1x,4(E20.9,1x))') grid%cl2g(icell),          &
!       grid%cell_vol(icell),grid%xc(icell),grid%yc(icell),grid%zc(icell)
!  end do
!  do i = 1,grid%nface
!     icell1 = grid%cl2g(grid%fptr(1,i))
!     icell2 = grid%cl2g(grid%fptr(2,i))
!     write(81000+lmpi_id,'(1x,E20.9,1x,2(i0,1x),2(E20.9,1x))') &
!       grid%area_face(i),icell1,icell2,grid%xn_face(i),grid%x_face(i)
!  end do
!  end if ! db

! TBD. The next two checks -- test for overlapping faces, and
!      the count may be misleading.

   call check_face_metrics_par(grid%project, grid%nface, grid%area_face,       &
          grid%fptr, grid%x_face, grid%y_face, grid%z_face )

   if (lmpi_master) then
      write(*,*)
      write(*,*) 'Checking face skewness...'
   end if

   call check_face_skewness(grid%nface, grid%ncell0, grid%ncell01,             &
          grid%fptr, grid%xn_face, grid%yn_face, grid%zn_face,                 &
          grid%xc, grid%yc, grid%zc )

! fl2g, flsq_ia, flsq_ja

   call compute_fl2g_par(grid)

   ! DANA CC TBD
     call pp_cc_setup_loc(grid)

   if (lmpi_master) then
      write(*,*); write(*,*) 'Forming face LSQ stencil...'
   end if

   call form_face_lsq_stencil_par(grid)

      allocate(grid%cell_skewness( grid%ncell01)); grid%cell_skewness = 0
      allocate(grid%skip_q(        grid%ncell01)); grid%skip_q = 0
      allocate(grid%boundary_flag( grid%ncell01)); grid%boundary_flag = 0
      allocate(grid%cgamma(        grid%ncell01)); grid%cgamma = 0
      allocate(grid%slenxn(        grid%ncell01)); grid%slenxn = 0
      allocate(grid%slenyn(        grid%ncell01)); grid%slenyn = 0
      allocate(grid%slenzn(        grid%ncell01)); grid%slenzn = 0
      allocate(grid%nlsq( 1,1,1)); grid%nlsq = 0

      allocate(grid%flsq_lu( 4,4, grid%nface)); grid%flsq_lu = 0
      allocate(grid%flsq_flag_rhs(  grid%nface)); grid%flsq_flag_rhs = 0
      allocate(grid%flsq_flag_lhs(  grid%nface)); grid%flsq_flag_lhs = 0

      allocate(grid%r11(       1)); grid%r11 = 0
      allocate(grid%r12(       1)); grid%r12 = 0
      allocate(grid%r13(       1)); grid%r13 = 0
      allocate(grid%r22(       1)); grid%r22 = 0
      allocate(grid%r23(       1)); grid%r23 = 0
      allocate(grid%r33(       1)); grid%r33 = 0
      allocate(grid%rlsq(  3,3,grid%ncell0)); grid%rlsq = 0
      allocate(grid%rlsq_ia( grid%ncell01+1)); grid%rlsq_ia = 0
      allocate(grid%rlsq_ja( grid%ncell_augmentors)); grid%rlsq_ja = 0

      allocate(grid%facespeed(grid%nedgeloc)); grid%facespeed = 0.0_dp

      grid%xq    => grid%xc
      grid%yq    => grid%yc
      grid%zq    => grid%zc
      grid%volq  => grid%cell_vol
      grid%cc    = .true.
      grid%dof0  = grid%ncell0

  ! bc related data

       do ib = 1,grid%nbound
          k = grid%bc(ib)%nbfacet
          if (k > 0) then
             allocate(grid%bc(ib)%qcell_ptr_t(k))
             grid%bc(ib)%qcell_ptr_t = 0
             do i = 1,k
                grid%bc(ib)%qcell_ptr_t(i) = grid%bc(ib)%f2ntb(i,4)
             end do
          end if
          k = grid%bc(ib)%nbfaceq
          if (k > 0) then
             allocate(grid%bc(ib)%qcell_ptr_q(k))
             grid%bc(ib)%qcell_ptr_q = 0
             do i = 1,k
                grid%bc(ib)%qcell_ptr_q(i) = grid%bc(ib)%f2nqb(i,5)
             end do
          end if
       end do

      ! bc_ghost

       do ib = 1,grid%nbound
          k = bc_ghost(ib)%nbfacet
          if (k > 0) then
             allocate(bc_ghost(ib)%qcell_ptr_t(k))
             bc_ghost(ib)%qcell_ptr_t = 0
             do i = 1,k
                bc_ghost(ib)%qcell_ptr_t(i) = bc_ghost(ib)%f2ntb(i,4)
             end do
          end if
          k = bc_ghost(ib)%nbfaceq
          if (k > 0) then
             allocate(bc_ghost(ib)%qcell_ptr_q(k))
             bc_ghost(ib)%qcell_ptr_q = 0
             do i = 1,k
                bc_ghost(ib)%qcell_ptr_q(i) = bc_ghost(ib)%f2nqb(i,5)
             end do
          end if
       end do

  end subroutine compute_cc_specific

!=============================================================================80
!
! PAR Determine face-based LSQ stencil for cell-centered.
! ONLY FULL AUGMENTATION: see LibF90/flsq_stencil.f90 for other methods
!
!=============================================================================80
  subroutine form_face_lsq_stencil_par(grid)

    use grid_types,   only : grid_type
   !use global_grids, only : nloc, locs, locvc, locvc_type
    use cc_defs,      only : lsq_face_stencil, allowable_skew_edge_based
    use flsq_stencil, only : finalize_sort

    type(grid_type), intent(inout) :: grid

    integer  :: nface_augmentors_par
    logical  :: verbose = .false.
    real(dp) :: average

  continue

    allocate(grid%flsq_ia(grid%nfaceg+1)); grid%flsq_ia = 0

    if ( lsq_face_stencil == -2 ) then
       write(*,*)                                                              &
         'Set up crs for face-based lsq (type b stencil AIAA 2009-597 ++ )'
       stop "NOT IMPLEMENTED"
    elseif ( lsq_face_stencil == -1 ) then
      if (lmpi_master) then
         write(*,*) 'Set up crs for face-based lsq (full augmentation)'
         write(*,*) '...allowable_skew_edge_based=',allowable_skew_edge_based
      end if
      call set_flsq_full_crs_gg_par(grid)
    elseif ( lsq_face_stencil == 2 ) then
      write(*,*)'Set up crs for face-based lsq (type b stencil AIAA 2009-597 +)'
      stop "NOT IMPLEMENTED"
    elseif ( lsq_face_stencil == 1 .or. lsq_face_stencil == 3 ) then
      write(*,*)                                                               &
      'Set up crs for face-based lsq...lsq_face_stencil=',lsq_face_stencil
      stop "NOT IMPLEMENTED"
    else
      write(*,*)' Inadmissible lsq_face_stencil=',lsq_face_stencil
      stop
    endif

    if (lsq_face_stencil /= -1 ) then ! DANA
       call finalize_sort( grid%nfaceg, grid%flsq_ia, grid%flsq_ja, verbose )
    end if

   ! grid%nface_augmentors = grid%flsq_ia(grid%nfaceg+1) - 1
    grid%nface_augmentors = grid%flsq_ia(grid%nface+1)

    call lmpi_reduce(grid%nface_augmentors,nface_augmentors_par)

    if (lmpi_master) then
       write(*,*)
       write(*,*) ' FLSQ:       lsq_face_stencil=',lsq_face_stencil
       write(*,*) ' FLSQ:                  cells=',grid%ncellg
       write(*,*) ' FLSQ:                  faces=',grid%nfaceg
       write(*,*) ' FLSQ:        face_augmentors=',nface_augmentors_par
       average = real( nface_augmentors_par , dp )/real( grid%nfaceg , dp )
       write(*,*) ' FLSQ: augmentors(avgperface)=',average
       average = real( grid%nfaceg , dp )/real( grid%ncellg , dp )
       write(*,*) ' FLSQ:      faces(avgpercell)=',average
       average = real( nface_augmentors_par , dp )/real( grid%ncellg , dp )
       write(*,*) ' FLSQ: augmentors(avgpercell)=',average
       write(*,*)
    end if

  end subroutine form_face_lsq_stencil_par

!========================= SET_FLSQ_FULL_CRS_GG_PAR ==========================80
!
! PAR Set compressed row storage of full augmentation.
! Invoked by Party.   _gg=> global_grid.
!
! OUT: grid%flsq_ia, grid%flsq_ja
!=============================================================================80
  subroutine set_flsq_full_crs_gg_par(grid)

    use lmpi,            only : lmpi_conditional_stop
    use grid_types,      only : grid_type
    use sort,            only : small_sort, sort_uniq

    type(grid_type), intent(inout) :: grid

    integer :: i,j,k, is,ie, ict, cell1, cell2, node, iface
    integer :: faces_min, faces_max, isize_temp1, loff, icell1, icell2
    integer, dimension(:), allocatable :: ct, fwa, ja, temp1, jct, jja
    integer :: ierr

  continue

    ierr = 0

! Count

    allocate(ct(grid%nnodes01)); ct = 0
    do iface = 1, grid%nface
       node = grid%fptr(3,iface); ct(node) = ct(node) + 1
       node = grid%fptr(4,iface); ct(node) = ct(node) + 1
       node = grid%fptr(5,iface); ct(node) = ct(node) + 1
       if ( grid%fptr(3,iface) /= grid%fptr(6,iface) ) then
          node = grid%fptr(6,iface); ct(node) = ct(node) + 1
       end if
    end do
    ct = ct*2

! Populate

    allocate(fwa(grid%nnodes01+1)); fwa = 0
    fwa(1) = 1
    do i = 2,grid%nnodes01+1
       fwa(i) = fwa(i-1)+ct(i-1)
    end do

    ict = fwa(grid%nnodes01+1)

    allocate(ja(ict)); ja = 0

    ct = 0
    do iface = 1, grid%nface
       cell1 = grid%cl2g(grid%fptr(1,iface))
       cell2 = grid%cl2g(grid%fptr(2,iface))
       do j = 3,5
          node = grid%fptr(j,iface)
          i = fwa(node)+ct(node)
          ja(i)   = cell1
          ja(i+1) = cell2
          ct(node) = ct(node) + 2
       end do
       if ( grid%fptr(3,iface) /= grid%fptr(6,iface) ) then
          node = grid%fptr(6,iface)
          i = fwa(node)+ct(node)
          ja(i)   = cell1
          ja(i+1) = cell2
          ct(node) = ct(node) + 2
       end if
    end do

! Remove redundant cells per node

    i = size(ja)
    do node = 1,grid%nnodes01
       if (ct(node) > 0) then
          is = fwa(node)
          ie = fwa(node) + ct(node)-1
          if ( ie > size( ja, 1 ) ) then
            ierr = 1
            exit
          endif
          call sort_uniq(i,ja,ct(node),is,ie)
       end if
    end do
    call lmpi_conditional_stop(ierr,'RUNTIME-FAILURE-TRAP')

    allocate(jct(grid%nface)); jct = 0
    do iface = 1,grid%nface
       jct(iface) = jct(iface) + ct(grid%fptr(3,iface))
       jct(iface) = jct(iface) + ct(grid%fptr(4,iface))
       jct(iface) = jct(iface) + ct(grid%fptr(5,iface))
       if ( grid%fptr(3,iface) /= grid%fptr(6,iface) )            &
          jct(iface) = jct(iface) + ct(grid%fptr(6,iface))
    end do

    allocate(jja(sum(jct))); jja = 0
    isize_temp1 = maxval(jct)
    allocate(temp1(isize_temp1)); temp1 = 0
    deallocate(jct)

    allocate(grid%flsq_ia(grid%nface+1)); grid%flsq_ia    = 0
    grid%flsq_ia(1) = 1

    do iface = 1,grid%nface
       loff = 1
       do j = 3,5
          node = grid%fptr(j,iface)
          ict = ct(node)
          is  = fwa(node)
          ie  = fwa(node)+(ict-1)
          temp1(loff:loff+ict-1) = ja(is:ie)
          loff = loff + ict
       end do
       if ( grid%fptr(3,iface) /= grid%fptr(6,iface) ) then
          node = grid%fptr(6,iface)
          ict = ct(node)
          is  = fwa(node)
          ie  = is+(ict-1)
          temp1(loff:loff+ict-1) = ja(is:ie)
          loff = loff + ict
       end if
       ! Remove the two guide cells.  First, find first unique value.
        icell1 = grid%cl2g(grid%fptr(1,iface))
        icell2 = grid%cl2g(grid%fptr(2,iface))
        k = 0
        do i = 1,loff-1 ! find a unique (non-icell1 or icell2 value in list)
           k = temp1(i)
           if ((k /= icell1).and.(k /= icell2)) exit
        end do
        ! second, replace all icell1 and icell2 with unique value
        do i = 1,loff-1
           if ((temp1(i)==icell1).or.(temp1(i)==icell2)) temp1(i) = k
        end do

       call sort_uniq(isize_temp1,temp1,ict,1,loff-1)
       grid%flsq_ia(iface+1) = grid%flsq_ia(iface)+ict
       is = grid%flsq_ia(iface)
       ie = grid%flsq_ia(iface+1)-1
       jja(is:ie) = temp1(1:ict)
       if (((ie-is)+1) /= ict) write(*,*)'Mismatch ',lmpi_id,(ie-is)+1,ict,is,ie
       call small_sort(ict,temp1)

    end do
    deallocate(temp1,ct,fwa)

! Store

    ict = grid%flsq_ia(grid%nface+1)
    allocate(grid%flsq_ja(ict))
    grid%flsq_ja = jja

    deallocate(jja)

!   Set grid%flsq_ja based on face_list_g entries.

    faces_min = grid%flsq_ia(2)-grid%flsq_ia(1)
    faces_max = faces_min
    do iface = 2,grid%nface
       i = grid%flsq_ia(iface+1)-grid%flsq_ia(iface)
       if (i > faces_max) faces_max = i
       if (i < faces_min) faces_min = i
    end do
    i = faces_min; call lmpi_min(i,faces_min)
    j = faces_max; call lmpi_max(j,faces_max)
    if (lmpi_master) then
       write(*,*)
       write(*,*) ' FLSQ:                   faces_min=',faces_min
       write(*,*) ' FLSQ:                   faces_max=',faces_max
    end if

  end subroutine set_flsq_full_crs_gg_par

!============================== PP_CC_SETUP_LOC ==============================80
!
! compute nloc, locs, locvc, locvc_type for Level lev (default 0)
! Results in locs using the standard practice of : i = locs(i),locs(i+1)-1
!
!=============================================================================80

  subroutine pp_cc_setup_loc(grid)

    use grid_types, only : grid_type

    type(grid_type),       intent(inout) :: grid

    integer :: i,j,is, ielem, icell, inode, npc

  continue

!-------------------------
! KEEP
!  if (db) then
!  do ielem = 1,grid%nelem
!     npc = grid%elem(ielem)%node_per_cell
!     do icell = 1,grid%elem(ielem)%ncell
!        write(166000+lmpi_id,'(1x,i0," : ",8(i0,1x))')                        &
!          grid%elem(ielem)%cl2g(icell),                                       &
!          grid%l2g(grid%elem(ielem)%c2n(1:npc,icell))
!        do j = 1,npc
!           write(167000+lmpi_id,'(1x,i0," : ",8(1x,i0,1x))')                  &
!             grid%l2g(grid%elem(ielem)%c2n(j,icell)),                         &
!             grid%l2g(grid%elem(ielem)%c2n(1:npc,icell))
!        end do
!     end do
!  end do
!  call lmpi_conditional_stop(1,'See 166k--cl2g,l2g')
!  end if ! db
!-------------------------

!   find the elements that surround each node

    nlocs = sum(grid%elem(:)%ncell*grid%elem(:)%node_per_cell)

    allocate(locvc(nlocs));          locvc      = 0
    allocate(locvc_type(nlocs));     locvc_type = 0
    allocate(locs(grid%nnodes0+1));  locs       = 0

! countsurel (note: store ahead)

    locs = 0
    do ielem = 1,grid%nelem
       npc = grid%elem(ielem)%node_per_cell
       do icell = 1,grid%elem(ielem)%ncell0
          do inode = 1,npc
             j = grid%elem(ielem)%c2n(inode,icell)+1 ! WARNING forward by 1
             locs(j) = locs(j) + 1
          end do
       end do
    end do
    do i=2,grid%nnodes0+1
       locs(i) = locs(i) + locs(i-1)
    end do

! surel

    do ielem = 1,grid%nelem
       npc = grid%elem(ielem)%node_per_cell
       do icell = 1,grid%elem(ielem)%ncell0
          do i = 1,npc
             inode = grid%elem(ielem)%c2n(i,icell)
             is = locs(inode) + 1
             locs(inode) = is
             locvc(is) = icell
             locvc_type(is) = ielem
          end do
       end do
    end do
    do i=grid%nnodes0+1,2,-1
       locs(i) = locs(i-1)
    end do
    locs(1) = 0

  end subroutine pp_cc_setup_loc

!============================== COMPUTE_FL2G_PAR =============================80
!
! compute grid%fl2g in parallel
! Note: This can be speed-up considerably. Namely, the innermost binary
!       search (which really compares two sorted lists) can be leveraged.
!       Also small_sort can be replaced with order4.
!
!=============================================================================80

  subroutine compute_fl2g_par(grid)

    use grid_types, only : grid_type
    use sort,       only : small_sort, heap_sort, binary_search

    type(grid_type), intent(inout) :: grid

    integer :: i,k, iface, ipe, n4(4), ict, ict2, n_left, gid
    integer :: nn1,nn2,nn3

    integer,  dimension(:), allocatable :: thash_ind
    real(dp), dimension(:), allocatable :: thash, temp1, temp

    integer(system_i1), dimension(:), allocatable  :: tag

  continue

    allocate(grid%fl2g(grid%nface)); grid%fl2g = 0
    allocate(temp1(grid%nface)); temp1 = 0._dp
    do iface = 1,grid%nface
       n4 = grid%l2g(grid%fptr(3:6,iface))
       if (n4(1) == n4(4)) then
         nn1 = minval(n4)
         nn3 = maxval(n4)
         nn2 = sum(n4(1:3))-(nn1+nn3)
         temp1(iface) = (nn1+nn2+nn3)*1._dp +                                  &
                 sqrt(nn1*1._dp)*sqrt(nn2*1._dp)*sqrt(nn3*1._dp)
       else
         call small_sort(4,n4)
         temp1(iface) = sum(n4)*1._dp +                                        &
         sqrt(n4(1)*1._dp)*sqrt(n4(2)*1._dp)*sqrt(n4(3)*1._dp)+sqrt(n4(4)*1._dp)
       end if
    end do
    allocate(thash_ind(grid%nface)); thash_ind = 0
    call heap_sort(grid%nface,temp1,thash_ind)
    allocate(thash(grid%nface)); thash = temp1(thash_ind)

    n_left = grid%nface
    allocate(tag(grid%nface)); tag = 0

    gid = 0
    do ipe = 0,lmpi_nproc-1
       if (lmpi_id == ipe) ict = n_left
       call lmpi_bcast(ict,ipe)
       if (ict > 0) then
          allocate(temp(ict));  temp  = 0._dp
          if (lmpi_id == ipe) then
             ict2 = 0
             do i = 1,grid%nface
                if (tag(i) == 0) then
                   ict2 = ict2 + 1
                   temp(ict2)  = thash(i)
                   grid%fl2g(thash_ind(i)) = gid+ict2
                end if
             end do
             tag = 1
             n_left = 0
          end if
          call lmpi_bcast(temp,ipe)
          if (lmpi_id > ipe) then
             if (n_left > 0) then
                do i = 1,grid%nface
                   if (tag(i) == 0) then
                      k = binary_search(ict,temp,thash(i))
                      if (k > 0) then
                         n_left = n_left - 1
                         tag(i) = 1
                         grid%fl2g(thash_ind(i)) = gid+k
                      end if
                   end if
                end do
             end if
          end if
          deallocate(temp)
          gid = gid + ict
       end if
    end do
    deallocate(tag,thash_ind,thash,temp1)
    if (n_left > 0)                                                            &
       write(*,*)"Internal error. N_left > 0 ",lmpi_id,n_left,grid%nface
    call lmpi_conditional_stop(n_left,'compute_fl2g_par error')

    i = maxval(grid%fl2g)
    call lmpi_max(i,k)
    call lmpi_bcast(k)
    if (k /= grid%nfaceg) then
       if (lmpi_master)                                                        &
       write(*,*)"Internal error in compute_fl2g_par. # faces ",k,grid%nfaceg
       call lmpi_conditional_stop(1,'Internal error in compute_fl2g_par.')
    end if

   !do i = 1,grid%nface
   !   write(2000+lmpi_id,'(1x,i0,1x,3(E20.9,1x))')                            &
   !     grid%fl2g(i),grid%x_face(i),grid%y_face(i),grid%z_face(i)
   !end do

  end subroutine compute_fl2g_par

!============================== SORT_EDGES_2D ================================80
!
! Reorders the partitioned edgelist so that for 2D computations, the first
! group of level 01 edges lies on one of the 2D planes, the second group
! of level 01 edges lies on the opposite plane, and the last group of
! level 01 edges is comprised of those edges that span the two planes. A
! similar reordering is performed for the level 2 edges in the list.
!
!=============================================================================80

  subroutine sort_edges_2d(grid)

    use grid_types,        only : grid_type
    use string_utils,      only : sprintf, max_str_len
    use system_extensions, only : se_open
    use twod_util,         only : yplane_2d, y_coplanar_tol


    type(grid_type), intent(inout) :: grid

    integer :: nnew, n, n1, n2, old, ielem, ie, old_edge, new_edge, ierr

    integer, dimension(:),   allocatable :: new2old, old2new, temp1
    integer, dimension(:,:), allocatable :: temp2

    character(max_str_len) :: filename, error_file

  continue

    if ( lmpi_master ) write(*,*) '    ... ordering edges for 2D.'

! Mapping of edges

    allocate(new2old(grid%nedge)); new2old = 0

    nnew = 0

! Level-1 edges first
! edges on first y-plane

    do n = 1, grid%nedgeloc
      n1 = grid%eptr(1,n)
      n2 = grid%eptr(2,n)
      if (abs(grid%y(n1) - grid%y(n2)) <= y_coplanar_tol) then
        if (abs(grid%y(n1) - yplane_2d) <= y_coplanar_tol) then
          nnew          = nnew + 1
          new2old(nnew) = n
        end if
      end if
    end do

    grid%nedgeloc_2d = nnew

! edges on second y-plane

    do n = 1, grid%nedgeloc
      n1 = grid%eptr(1,n)
      n2 = grid%eptr(2,n)
      if (abs(grid%y(n1) - grid%y(n2)) <= y_coplanar_tol) then
        if (abs(grid%y(n1) - yplane_2d) > y_coplanar_tol) then
          nnew          = nnew + 1
          new2old(nnew) = n
        end if
      end if
    end do

! Ensure the number of edges in first y-plane match the number
! of edges in the second y-plane.

   n1 = 0
   if (nnew /= grid%nedgeloc_2d*2) n1 = 1
   call lmpi_reduce(n1,ierr)
   call lmpi_bcast(ierr)
   if (ierr /= 0) then
      if (lmpi_master) then
         write(*,*)"ERROR in sort_edges_2d. # of y-planes do not match. ",     &
           lmpi_id
         write(*,*)"See file(s) project_twod_error.xxx for details."
      end if

      if (nnew /= grid%nedgeloc_2d*2) then
         filename = trim(grid%project) // '_twod_error.'
         error_file = sprintf(trim(filename)//'%i0',lmpi_id)
         call se_open(20,file=error_file,form='formatted',status='unknown')

         write(20,*)'Edges on 1st y-plane: ',grid%nedgeloc_2d
         write(20,*)'Edges on 2nd y-plane: ',nnew-grid%nedgeloc_2d
         write(20,*)'Total y-plane edges : ',nnew
         write(20,*)'y_coplanar_tol      : ',y_coplanar_tol
         write(20,*)'yplane_2d           : ',yplane_2d
         write(20,*)
         write(20,*)'Writing out xyz for n1 for all edges in y-planes.'
         write(20,*)' n1 (x, z, y), l2g(n1), l2g(n2)'
         do n = 1, grid%nedgeloc
            n1 = grid%eptr(1,n)
            n2 = grid%eptr(2,n)
            if (abs(grid%y(n1) - grid%y(n2)) <= y_coplanar_tol) then
               write(20,'(" ",3(F16.12,1x),2(i10,1x),3(F16.12,1x)," ")')      &
                 grid%x(n1),grid%z(n1),grid%y(n1),grid%l2g(n1),grid%l2g(n2),  &
                 grid%x(n2),grid%z(n2),grid%y(n2)
           end if
         end do
         close(20)
      end if

      call lmpi_conditional_stop(1,'Number of Y-plane edges do not match.')
   end if


! edges that span the planes

    do n = 1, grid%nedgeloc
      n1 = grid%eptr(1,n)
      n2 = grid%eptr(2,n)
      if (abs(grid%y(n1) - grid%y(n2)) > y_coplanar_tol) then
        nnew          = nnew + 1
        new2old(nnew) = n
      end if
    end do

! Now level-2 edges
! edges on first y-plane

    do n = grid%nedgeloc+1, grid%nedge
      n1 = grid%eptr(1,n)
      n2 = grid%eptr(2,n)
      if (abs(grid%y(n1) - grid%y(n2)) <= y_coplanar_tol) then
        if (abs(grid%y(n1) - yplane_2d) <= y_coplanar_tol) then
          nnew          = nnew + 1
          new2old(nnew) = n
        end if
      end if
    end do

! edges on second y-plane

    do n = grid%nedgeloc+1, grid%nedge
      n1 = grid%eptr(1,n)
      n2 = grid%eptr(2,n)
      if (abs(grid%y(n1) - grid%y(n2)) <= y_coplanar_tol) then
        if (abs(grid%y(n1) - yplane_2d) > y_coplanar_tol) then
          nnew          = nnew + 1
          new2old(nnew) = n
        end if
      end if
    end do

! edges that span the planes

    do n = grid%nedgeloc+1, grid%nedge
      n1 = grid%eptr(1,n)
      n2 = grid%eptr(2,n)
      if (abs(grid%y(n1) - grid%y(n2)) > y_coplanar_tol) then
        nnew          = nnew + 1
        new2old(nnew) = n
      end if
    end do

! error-checking

    if ( nnew /= grid%nedge ) then
      write(*,*) 'Error in reordering 2D edges...'
      call lmpi_die
      stop
    endif

    do n = 1, grid%nedge
      if ( new2old(n) == 0 .or. new2old(n) > grid%nedge ) then
        write(*,*) 'Error in new2old in reordering 2D edges...'
        call lmpi_die
        stop
      endif
    end do

! shuffle the grid data

    allocate(temp2(2,grid%nedge))
    temp2(:,:) = grid%eptr(:,:)
    do n = 1, grid%nedge
      grid%eptr(:,n) = temp2(:,new2old(n))
    end do
    deallocate(temp2)

    allocate(temp1(grid%nedge))
    temp1(:) = grid%el2g(:)
    do n = 1, grid%nedge
      grid%el2g(n) = temp1(new2old(n))
    end do
    deallocate(temp1)

    allocate(old2new(grid%nedge)); old2new = 0

    do n = 1, grid%nedge
      old = new2old(n)
      old2new(old) = n
    end do

    do ielem = 1, grid%nelem
      allocate(temp2(grid%elem(ielem)%edge_per_cell,grid%elem(ielem)%ncell))
      temp2(:,:) = grid%elem(ielem)%c2e(:,:)
      do n = 1, grid%elem(ielem)%ncell
        do ie = 1, grid%elem(ielem)%edge_per_cell
          old_edge = temp2(ie,n)
          new_edge = old2new(old_edge)
          grid%elem(ielem)%c2e(ie,n) = new_edge
        end do
      end do
      deallocate(temp2)
    end do

    deallocate(new2old,old2new)

  end subroutine sort_edges_2d

!================================= FACE_METRICS ==============================80
!
!  Get metrics for face-based system: cell volumes, face areas and normals
!
!=============================================================================80

  subroutine face_metrics(nnodes, ncell, x, y, z, nelem, elem, cell_vol, xc,   &
                          yc, zc, nface, xn, yn, zn, area, fptr, x_face,       &
                          y_face, z_face, verbose)

    use element_types, only : elem_type

    integer,                           intent(in)  :: nelem,nnodes,ncell,nface
    integer,  dimension(6,nface),      intent(in)  :: fptr

    real(dp), dimension(nnodes),       intent(in)  :: x, y, z
    real(dp), dimension(ncell),        intent(out) :: cell_vol, xc, yc, zc
    real(dp), dimension(nface),        intent(out) :: xn, yn, zn, area
    real(dp), dimension(nface),        intent(out) :: x_face, y_face, z_face

    type(elem_type), dimension(nelem), intent(in) :: elem

    logical,                           intent(in) :: verbose

    integer :: iface,n,n1,n2,n3,n4,ielem,global_cell,i,inode,cell1,cell2

    integer, dimension(6) :: face_type

    real(dp)    :: term0,term1,term2,xavg,yavg,zavg,areax,areay,areaz
    real(dp)    :: x1,x2,x3,x4,y1,y2,y3,y4,z1,z2,z3,z4,vx,vy,vz,norm,dot
    real(dp)    :: factor

    real(dp), parameter :: face_tol = 1.0e-14_dp

  continue

! Get the cell volumes first and store
! Unfortunately the normals are a part of this
! computation but they cannot be stored here because
! we won't know the face number.  Do them next in a
! face loop

    do ielem = 1, nelem

! determine which faces are tria and mark them; all other
! faces assumed to be quad

! default to tria faces

      face_type = 0

      do iface = 1, elem(ielem)%face_per_cell
        if (elem(ielem)%local_f2n(iface,4)/=elem(ielem)%local_f2n(iface,1)) then
          face_type(iface) = 1
        end if
      end do

! loop over cells

      cell_loop : do n = 1, elem(ielem)%ncell

        global_cell = elem(ielem)%cell_map(n)

        term0 = 0._dp; term1 = 0._dp; term2 = 0._dp

! get cell average value.

        xc(global_cell)    = 0.0_dp
        yc(global_cell)    = 0.0_dp
        zc(global_cell)    = 0.0_dp

        node_loop : do i = 1, elem(ielem)%node_per_cell
          inode = elem(ielem)%c2n(i,n)
          xc(global_cell)    =    xc(global_cell) +    x(inode)
          yc(global_cell)    =    yc(global_cell) +    y(inode)
          zc(global_cell)    =    zc(global_cell) +    z(inode)
        end do node_loop

        factor = 1.0_dp/real(elem(ielem)%node_per_cell,dp)
        xc(global_cell)    =    xc(global_cell)*factor
        yc(global_cell)    =    yc(global_cell)*factor
        zc(global_cell)    =    zc(global_cell)*factor

! loop over tria faces

        tria_faces_loop : do iface = 1, elem(ielem)%face_per_cell

          if (face_type(iface) == 0) then

            n1 = elem(ielem)%c2n(elem(ielem)%local_f2n(iface,1),n)
            n2 = elem(ielem)%c2n(elem(ielem)%local_f2n(iface,2),n)
            n3 = elem(ielem)%c2n(elem(ielem)%local_f2n(iface,3),n)

            x1 = x(n1); x2 = x(n2); x3 = x(n3)
            y1 = y(n1); y2 = y(n2); y3 = y(n3)
            z1 = z(n1); z2 = z(n2); z3 = z(n3)

            xavg = (x1 + x2 + x3)/3._dp
            yavg = (y1 + y2 + y3)/3._dp
            zavg = (z1 + z2 + z3)/3._dp

! triangle 1-2-3

            areax = 0.5_dp*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
            areay = 0.5_dp*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
            areaz = 0.5_dp*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )

            term0 = term0 + xavg*areax + yavg*areay + zavg*areaz

          end if

        end do tria_faces_loop

        term0 = term0/3._dp

! loop over quad faces

        quad_faces_loop : do iface = 1, elem(ielem)%face_per_cell

          if (face_type(iface) /= 0) then

            n1 = elem(ielem)%c2n(elem(ielem)%local_f2n(iface,1),n)
            n2 = elem(ielem)%c2n(elem(ielem)%local_f2n(iface,2),n)
            n3 = elem(ielem)%c2n(elem(ielem)%local_f2n(iface,3),n)
            n4 = elem(ielem)%c2n(elem(ielem)%local_f2n(iface,4),n)

            x1 = x(n1); x2 = x(n2); x3 = x(n3); x4 = x(n4)
            y1 = y(n1); y2 = y(n2); y3 = y(n3); y4 = y(n4)
            z1 = z(n1); z2 = z(n2); z3 = z(n3); z4 = z(n4)

! quad faces are split into 2 triangles - this splitting can
! be done in 2 ways (one for each diagonal). Start by
! picking one diagonal, then calculate volumes. Later,
! pick the other diagonal and repeat. Finally can average
! the two results to eliminate bias.

! break face up into triangles 1-2-3 and 1-3-4

! triangle 1-2-3

            xavg = (x1 + x2 + x3)/3._dp
            yavg = (y1 + y2 + y3)/3._dp
            zavg = (z1 + z2 + z3)/3._dp

            areax = 0.5_dp*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
            areay = 0.5_dp*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
            areaz = 0.5_dp*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )

            term1 = term1 + xavg*areax + yavg*areay + zavg*areaz

! triangle 1-3-4

            xavg = (x1 + x3 + x4)/3._dp
            yavg = (y1 + y3 + y4)/3._dp
            zavg = (z1 + z3 + z4)/3._dp

            areax = 0.5_dp*( (y3-y1)*(z4-z1) - (z3-z1)*(y4-y1) )
            areay = 0.5_dp*( (z3-z1)*(x4-x1) - (x3-x1)*(z4-z1) )
            areaz = 0.5_dp*( (x3-x1)*(y4-y1) - (y3-y1)*(x4-x1) )

            term1 = term1 + xavg*areax + yavg*areay + zavg*areaz

! now repeat calculation, but this time use alternate face
! diagonal when splitting faces into triangles

! break face up into triangles 1-2-4 and 2-3-4

! triangle 1-2-4

            xavg = (x1 + x2 + x4)/3._dp
            yavg = (y1 + y2 + y4)/3._dp
            zavg = (z1 + z2 + z4)/3._dp

            areax = 0.5_dp*( (y2-y1)*(z4-z1) - (z2-z1)*(y4-y1) )
            areay = 0.5_dp*( (z2-z1)*(x4-x1) - (x2-x1)*(z4-z1) )
            areaz = 0.5_dp*( (x2-x1)*(y4-y1) - (y2-y1)*(x4-x1) )

            term2 = term2 + xavg*areax + yavg*areay + zavg*areaz

! triangle 2-3-4

            xavg = (x2 + x3 + x4)/3._dp
            yavg = (y2 + y3 + y4)/3._dp
            zavg = (z2 + z3 + z4)/3._dp

            areax = 0.5_dp*( (y3-y2)*(z4-z2) - (z3-z2)*(y4-y2) )
            areay = 0.5_dp*( (z3-z2)*(x4-x2) - (x3-x2)*(z4-z2) )
            areaz = 0.5_dp*( (x3-x2)*(y4-y2) - (y3-y2)*(x4-x2) )

            term2 = term2 + xavg*areax + yavg*areay + zavg*areaz

          end if

        end do quad_faces_loop

        term1 = term1/3._dp
        term2 = term2/3._dp

! add the volume contribution from the tria faces and average the
! resultant volume contributions from the two different quad splittings

        cell_vol(global_cell) = term0 + (term1 + term2)/2._dp

      end do cell_loop

    end do

! Now go back with a face loop and get the face normals

    do iface = 1, nface

! default to tria face

      face_type(1) = 0

      cell1 = fptr(1,iface)
      cell2 = fptr(2,iface)
      n1    = fptr(3,iface)
      n2    = fptr(4,iface)
      n3    = fptr(5,iface)
      n4    = fptr(6,iface)

      if (n1 /= n4) face_type(1) = 1

      xn(iface) = 0._dp; yn(iface) = 0._dp; zn(iface) = 0._dp

      if (face_type(1) == 0) then

        x1 = x(n1); x2 = x(n2); x3 = x(n3)
        y1 = y(n1); y2 = y(n2); y3 = y(n3)
        z1 = z(n1); z2 = z(n2); z3 = z(n3)

        x_face(iface) = (x1 + x2 + x3) / 3.0_dp
        y_face(iface) = (y1 + y2 + y3) / 3.0_dp
        z_face(iface) = (z1 + z2 + z3) / 3.0_dp

! triangle 1-2-3

        areax = 0.5_dp*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
        areay = 0.5_dp*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
        areaz = 0.5_dp*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )

        xn(iface) = areax
        yn(iface) = areay
        zn(iface) = areaz

! unit normal

        area(iface) = sqrt( xn(iface)*xn(iface) + yn(iface)*yn(iface)         &
                    +       zn(iface)*zn(iface) )

        ! Allow degenerate faces (bookkeep later).
        if ( area( iface ) > face_tol ) then
          xn(iface) = xn(iface)/area(iface)
          yn(iface) = yn(iface)/area(iface)
          zn(iface) = zn(iface)/area(iface)
        endif

      end if

      if (face_type(1) /= 0) then

        x1 = x(n1); x2 = x(n2); x3 = x(n3); x4 = x(n4)
        y1 = y(n1); y2 = y(n2); y3 = y(n3); y4 = y(n4)
        z1 = z(n1); z2 = z(n2); z3 = z(n3); z4 = z(n4)

        x_face(iface) = (x1 + x2 + x3 + x4) / 4.0_dp
        y_face(iface) = (y1 + y2 + y3 + y4) / 4.0_dp
        z_face(iface) = (z1 + z2 + z3 + z4) / 4.0_dp

! quad faces are split into 2 triangles - this splitting can
! be done in 2 ways (one for each diagonal). Start by
! picking one diagonal, then calculate volumes. Later,
! pick the other diagonal and repeat. Finally can average
! the two results to eliminate bias.

! break face up into triangles 1-2-3 and 1-3-4

! triangle 1-2-3

        areax = 0.5_dp*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
        areay = 0.5_dp*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
        areaz = 0.5_dp*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )

        xn(iface) = xn(iface) + areax
        yn(iface) = yn(iface) + areay
        zn(iface) = zn(iface) + areaz

! triangle 1-3-4

        areax = 0.5_dp*( (y3-y1)*(z4-z1) - (z3-z1)*(y4-y1) )
        areay = 0.5_dp*( (z3-z1)*(x4-x1) - (x3-x1)*(z4-z1) )
        areaz = 0.5_dp*( (x3-x1)*(y4-y1) - (y3-y1)*(x4-x1) )

        xn(iface) = xn(iface) + areax
        yn(iface) = yn(iface) + areay
        zn(iface) = zn(iface) + areaz

! now repeat calculation, but this time use alternate face
! diagonal when splitting faces into triangles

! break face up into triangles 1-2-4 and 2-3-4

! triangle 1-2-4

        areax = 0.5_dp*( (y2-y1)*(z4-z1) - (z2-z1)*(y4-y1) )
        areay = 0.5_dp*( (z2-z1)*(x4-x1) - (x2-x1)*(z4-z1) )
        areaz = 0.5_dp*( (x2-x1)*(y4-y1) - (y2-y1)*(x4-x1) )

        xn(iface) = xn(iface) + areax
        yn(iface) = yn(iface) + areay
        zn(iface) = zn(iface) + areaz

! triangle 2-3-4

        areax = 0.5_dp*( (y3-y2)*(z4-z2) - (z3-z2)*(y4-y2) )
        areay = 0.5_dp*( (z3-z2)*(x4-x2) - (x3-x2)*(z4-z2) )
        areaz = 0.5_dp*( (x3-x2)*(y4-y2) - (y3-y2)*(x4-x2) )

        xn(iface) = xn(iface) + areax
        yn(iface) = yn(iface) + areay
        zn(iface) = zn(iface) + areaz

! average face normals from the two different quad splittings

        xn(iface) = xn(iface)/2._dp
        yn(iface) = yn(iface)/2._dp
        zn(iface) = zn(iface)/2._dp

! unit normal

        area(iface) = sqrt( xn(iface)*xn(iface) + yn(iface)*yn(iface) +        &
                            zn(iface)*zn(iface) )

        ! Allow degenerate faces (bookkeep later).
        if ( area( iface ) > face_tol ) then
          xn(iface) = xn(iface)/area(iface)
          yn(iface) = yn(iface)/area(iface)
          zn(iface) = zn(iface)/area(iface)
        endif

      end if

! Find a unit vector from cell1 to cell2

      vx = xc(cell2) - xc(cell1)
      vy = yc(cell2) - yc(cell1)
      vz = zc(cell2) - zc(cell1)

      norm = sqrt(vx*vx + vy*vy + vz*vz)

      vx = vx / norm
      vy = vy / norm
      vz = vz / norm

! Take the dot product of the face normal with the unit vector between
! cell centers.  If the result is positive, we have our normal in the
! right direction.  If not, its backwards and we will flip the sign on
! the components

      dot = xn(iface)*vx + yn(iface)*vy + zn(iface)*vz

      if ( dot < 0.0_dp ) then
        xn(iface) = -xn(iface)
        yn(iface) = -yn(iface)
        zn(iface) = -zn(iface)
      endif

    end do

    if ( verbose ) then
      write(*,*)' ........Total interior faces=',nface
    endif

  end subroutine face_metrics

!================================= CHECK_FACE_METRICS_PAR ====================80
!
! Isoloate degenerate metrics for face-based system. (parallel)
! TBD. Partial implementation--Degenerate path needs to be exercised.
! TBD. Also, the face number reported in the (collective) error file,
!      should probably be a global face number.
!
!=============================================================================80

  subroutine check_face_metrics_par( project, nface, area, fptr,               &
                                 x_face, y_face, z_face )

    use system_extensions, only : se_open
    use lmpi,              only : lmpi_bcast, lmpi_gather, lmpi_master,        &
                                  lmpi_reduce, lmpi_send, lmpi_recv, lmpi_nproc

    character(len=80),            intent(in) :: project
    integer,                      intent(in) :: nface
    integer,  dimension(6,nface), intent(in) :: fptr

    real(dp), dimension(nface),   intent(in) :: area
    real(dp), dimension(nface),   intent(in) :: x_face, y_face, z_face

    integer :: i, ipe, ict, iface, cifaces, ierr, ioff, running_ct

    integer,  dimension(:), allocatable :: iall
    real(dp), dimension(:), allocatable :: xyz_face

    real(dp), parameter :: face_tol = 1.0e-14_dp

  continue

    ict = 0
    do iface = 1,nface
        if ( area( iface ) <= face_tol ) ict = ict + 1
    end do

    call lmpi_reduce(ict,cifaces)
    call lmpi_bcast(cifaces)
    if (cifaces == 0) then
       if (lmpi_master)                                                        &
          write(*,*)' ...No degenerate interior faces...area_tol=',face_tol
       return
    end if

    if (lmpi_master) then
       write(*,*)"NOTE check_face_metrics_par has not been tested for ",       &
                 "degenerate faces. Double check results."
    end if

! Degenerate faces exist.

    if (lmpi_master) then
      write(*,*)' ...Degenerate interior faces=',cifaces,'...area_tol=',face_tol
      call se_open(56,file=trim(project)//'_collapsed_interior_faces',         &
           form = 'formatted')
      rewind(56)
      write(56,"(1x,a,4x,a,19x,a,19x,a,19x,a)") 'et','face','x','y','z'
    end if

    allocate(iall(0:lmpi_nproc-1)); iall = 0
    call lmpi_gather(ict,iall)

    if (ict > 0) then
       allocate(xyz_face(4*ict)); xyz_face = 0.0_dp
       ioff = 1
       do iface = 1,nface
          if (area(iface) <= face_tol) then
             xyz_face(ioff) = 1._dp
             if (fptr(3,iface) == fptr(6,iface)) xyz_face(ioff) = 2._dp
             xyz_face(ioff+1) = x_face(iface)
             xyz_face(ioff+2) = y_face(iface)
             xyz_face(ioff+3) = z_face(iface)
             ioff = ioff + 4
          end if
        end do
    end if

    if (lmpi_master) then
       running_ct = 0
       if (ict > 0) then
          ioff = 1
          do i = 1,ict
             running_ct = running_ct + 1
             write(56,"(1x,i2,i8,3e20.10)")                                    &
               nint(xyz_face(ioff)),running_ct,xyz_face(ioff+1:ioff+3)
             ioff = ioff + 4
          end do
          deallocate(xyz_face)
       end if
       do ipe = 1,lmpi_nproc-1
          if (iall(ipe) > 0) then
             allocate(xyz_face(iall(ipe)*4)); xyz_face = 0._dp
             call lmpi_recv(xyz_face,iall(ipe)*4,ipe,100,ierr)
             ioff = 1
             do i = 1,iall(ipe)
                running_ct = running_ct + 1
                write(56,"(1x,i2,i8,3e20.10)")                                 &
                  nint(xyz_face(ioff)),running_ct,xyz_face(ioff+1:ioff+3)
                ioff = ioff + 4
             end do
             deallocate(xyz_face)
          end if
       end do
       close(56)
    else
       if (ict > 0) then
          call lmpi_send(xyz_face,ict*4,0,100,ierr)
          deallocate(xyz_face)
       end if
    end if

  end subroutine check_face_metrics_par

end module pparty_mixed_element

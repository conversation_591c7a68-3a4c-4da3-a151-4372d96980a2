! Defines the general attributes that make up a cell or element, and sets up
! some standard attributes for tetrahedra, hexahedra, prisms and pyramids

module element_defs

  implicit none

  private

  !beginNoComplexInterface
  public :: nullify_elem
  public :: allocate_elem
  public :: deallocate_elem
  public :: initialize_elem
  public :: element_metric
  !endNoComplexInterface
  public :: type_tet, max_node_per_cell, max_face_per_cell, max_edge_per_cell
  public :: type_prz, type_hex, type_pyr
  public :: node_per_tet, node_per_hex, node_per_prz, node_per_pyr
  public :: edge_per_tet, edge_per_hex, edge_per_prz, edge_per_pyr
  public :: local_e2n_tet, local_e2n_hex, local_e2n_prz, local_e2n_pyr
  public :: local_f2n_tet, local_f2n_hex, local_f2n_prz, local_f2n_pyr
  public :: local_f2e_tet, local_f2e_hex, local_f2e_prz, local_f2e_pyr
  public :: type_cell_2_integer
  public :: local_tet2tet, local_hex2tet, local_prz2tet, local_pyr2tet
  public :: chk_norm_tet, chk_norm_hex, chk_norm_prz, chk_norm_pyr

  public :: fun3d_to_ugrid_pyramid, fun3d_to_ugrid_prism, fun3d_to_ugrid_hex
  public :: ugrid_to_fun3d_pyramid, ugrid_to_fun3d_prism, ugrid_to_fun3d_hex

  public :: fun3d_to_vtk_prz
  public :: fun3d_to_cgns_prism, fun3d_to_cgns_hex

! Some useful max values over all element types for sizing local arrays

  integer                             :: max_node_per_cell
  integer                             :: max_face_per_cell
  integer                             :: max_edge_per_cell

!-------------------------------------------------------------------------------
!
! define some simple attributes of currently supported element (cell) types
!
!-------------------------------------------------------------------------------

!------------------
! tetrahedral cells
!------------------

  character(len=3), parameter            :: type_tet     = 'tet'
  integer,          parameter            :: node_per_tet = 4
  integer,          parameter            :: edge_per_tet = 6
  integer,          parameter            :: face_per_tet = 4

! local node and edge definitions; nodes on faces ordered to give outward normal

!                               e6                    edge #    connects nodes
!                       n4----------------n3          ------    --------------
!                      / \              . /             1             1-2
!                     /   \          .   /              2             1-3
!                    /     \e5    .     /               3             1-4
!                 e3/       \  .       /                4             2-3
!                  /        .\        / e4              5             2-4
!                 /    e2.    \      /                  6             3-4
!                /    .        \    /
!               /  .            \  /                  face #    contains nodes
!              /.                \/                   ------    --------------
!             n1-----------------n2                     1            1-3-2
!                      e1                               2            2-3-4
!                                                       3            1-4-3
!                                                       4            1-2-4


!------------------
! hexahedral cells
!------------------

  character(len=3), parameter            :: type_hex     = 'hex'
  integer,          parameter            :: node_per_hex = 8
  integer,          parameter            :: edge_per_hex = 12
  integer,          parameter            :: face_per_hex = 6

! local node and edge definitions; nodes on faces ordered to give outward normal

!                                e11                  edge #    connects nodes
!                        n6----------------n8         ------    --------------
!                       /.                /|            1             1-2
!                      / .               / |            2             1-3
!                     /  .              /  |            3             1-5
!                    /   .             /   |            4             2-4
!                   /    .e9          /    |            5             2-6
!                  /e5   .           /e8   |e12         6             3-4
!                 /      .          /      |            7             3-7
!                /       .  e4     /       |            8             4-8
!               n2----------------n4       |            9             5-6
!               |        n5.......|........n7          10             5-7
!               |       .    e10  |       /            11             6-8
!               |      .          |      /             12             7-8
!               |     .           |     /
!             e1|    .e3        e6|    /e7            face #    contains nodes
!               |   .             |   /               ------    --------------
!               |  .              |  /                  1           1-3-4-2
!               | .               | /                   2           5-6-8-7
!               |.                |/                    3           5-7-3-1
!               n1----------------n3                    4           6-2-4-8
!                        e2                             5           5-1-2-6
!                                                       6           7-8-4-3


!------------------
! prismatic cells
!------------------

  character(len=3), parameter            :: type_prz     = 'prz'
  integer,          parameter            :: node_per_prz = 6
  integer,          parameter            :: edge_per_prz = 9
  integer,          parameter            :: face_per_prz = 5

! local node and edge definitions; nodes on faces ordered to give outward normal

!                                          n3         edge #    connects nodes
!                                        . /|         ------    --------------
!                                     .   / |           1             1-2
!                                  .     /  |           2             1-4
!                               .       /   |           3             1-6
!                          e7.         /    |           4             2-3
!                         .           /e4   |e6         5             2-5
!                      .             /      |           6             3-4
!                   .               /       |           7             3-5
!                .                 /        |           8             4-6
!               n5----------------n2       n4           9             5-6
!               |        e5       |      . /
!               |                 |   .   /           face #    contains nodes
!               |                 |.     /            ------    --------------
!               |               . |     /e2             1           2-3-5
!             e9|          e8.    |e1  /                2           1-6-4
!               |         .       |   /                 3          1-4-3-2
!               |      .          |  /                  4          1-2-5-6
!               |   .             | /                   5          4-6-5-3
!               |.                |/
!               n6--------------- n1
!                        e3


!------------------
! pyramidal cells
!------------------

  character(len=3), parameter            :: type_pyr     = 'pyr'
  integer,          parameter            :: node_per_pyr = 5
  integer,          parameter            :: edge_per_pyr = 8
  integer,          parameter            :: face_per_pyr = 5

! local node and edge definitions; nodes on faces ordered to give outward normal

!                        e5                           edge #    connects nodes
!               n1----------------n2                  ------    --------------
!               |    .            | \                   1             1-5
!               |       .         |  \                  2             2-5
!               |          .      |   \                 3             3-5
!               |         e1  .   |    \ e2             4             4-5
!               |                .|     \               5             1-2
!               |                 | .    \              6             2-3
!               |                 |    .  \             7             3-4
!               |                 |       .n5           8             1-4
!             e8|               e6|      . /
!               |                 |   .   /           face #    contains nodes
!               |                 |.     /            ------    --------------
!               |               . |     /               1            1-2-5
!               |            .    |    /e3              2            3-4-5
!               |         .       |   /                 3            1-5-4
!               |      .  e4      |  /                  4            2-3-5
!               |   .             | /                   5           1-4-3-2
!               |.                |/
!               n4----------------n3
!                        e7


!-------------------------------------------------------------------------------
!
! define some local (to particular subroutines) arrays  for
! currently supported element (cell) types; these are needed
! for calculating grid and dual-grid metrics, volumes, etc
!
!-------------------------------------------------------------------------------

!--------------------------------------------------------
! local_e2n used for metrics and viscous fluxes/jacobians
!--------------------------------------------------------

! local edge to node pointer structure for edge i:
!
! local_e2n(i,1) = n1 (node 1 of edge, common to left and right faces)
! local_e2n(i,2) = n2 (node 2 of edge, common to left and right faces)
! local_e2n(i,3) = n3 (n1-n2-n3-n4 define the left  quad/tria face)
! local_e2n(i,4) = n4 (n1-n2-n3-n4 define the left  quad/tria face)
! local_e2n(i,5) = n5 (n1-n2-n5-n6 define the right quad/tria face)
! local_e2n(i,6) = n6 (n1-n2-n5-n6 define the right quad/tria face)
!
! where: n1,n2,...n6 are local cell node numbers
!        "left"  is to the left  along a line directed from n1 to n2
!        "right" is to the right along a line directed from n1 to n2
!
! notes:
!       1) 2nd dimension of the local_e2n array is always 6 - to accomodate
!          both tria and quad faces in the same data structure, n4 and/or
!          n6 may be set to zero; if n4 = 0, then the left face is a tria;
!          if n6 = 0, then the right face is a tria)
!       2) the arrays are set up below using the reshape function; the structure
!          is illustrated for the tetrahedral case (note the zeroes for node
!          numbers 4 and 6; no quad faces in a tet!):
!
!                                   edge # ------->
!
! local_e2n_tet  =        reshape((/ 1, 1, 1, 2, 2, 3,
!                  n  |              2, 3, 4, 3, 4, 4,
!                  o  |              4, 2, 3, 4, 1, 2,
!                  d  |              0, 0, 0, 0, 0, 0,
!                  e  |              3, 4, 2, 1, 3, 1,
!                  #  V              0, 0, 0, 0, 0, 0 /), (/ 6, 6/))
!
! *** see pictograms above ***

  integer, parameter, dimension (12, 6) :: local_e2n_hex =                     &
  reshape((/ 1, 1, 1, 2, 2, 3, 3, 4, 5, 5, 6, 7,                               &
             2, 3, 5, 4, 6, 4, 7, 8, 6, 7, 8, 8,                               &
             6, 4, 7, 8, 5, 2, 8, 6, 8, 3, 7, 4,                               &
             5, 2, 3, 6, 1, 1, 4, 2, 7, 1, 5, 3,                               &
             3, 5, 2, 1, 4, 7, 1, 3, 1, 6, 2, 5,                               &
             4, 7, 6, 3, 8, 8, 5, 7, 2, 8, 4, 6 /), (/12, 6/))

  integer, parameter, dimension ( 6, 6) :: local_e2n_tet =                     &
  reshape((/ 1, 1, 1, 2, 2, 3,                                                 &
             2, 3, 4, 3, 4, 4,                                                 &
             4, 2, 3, 4, 1, 2,                                                 &
             0, 0, 0, 0, 0, 0,                                                 &
             3, 4, 2, 1, 3, 1,                                                 &
             0, 0, 0, 0, 0, 0 /), (/ 6, 6/))

  integer, parameter, dimension ( 9, 6) :: local_e2n_prz =                     &
  reshape((/ 1, 1, 1, 2, 2, 3, 3, 4, 5,                                        &
             2, 4, 6, 3, 5, 4, 5, 6, 6,                                        &
             5, 3, 4, 5, 6, 6, 2, 5, 1,                                        &
             6, 2, 0, 0, 1, 5, 0, 3, 2,                                        &
             4, 6, 2, 1, 3, 2, 4, 1, 3,                                        &
             3, 0, 5, 4, 0, 1, 6, 0, 4 /), (/ 9, 6/))

  integer, parameter, dimension ( 8, 6) :: local_e2n_pyr =                     &
  reshape((/ 1, 2, 3, 4, 1, 2, 3, 1,                                           &
             5, 5, 5, 5, 2, 3, 4, 4,                                           &
             4, 1, 2, 3, 5, 5, 5, 3,                                           &
             0, 0, 0, 0, 0, 0, 0, 2,                                           &
             2, 3, 4, 1, 4, 1, 2, 5,                                           &
             0, 0, 0, 0, 3, 4, 1, 0 /), (/ 8, 6/))


!--------------------------------------------------------
! local_f2n used for metrics and viscous fluxes/jacobians
!--------------------------------------------------------

! the arrays are set up below using the reshape function. the following
! illustrates the array for a tetrahedral cell; note that entries for
! 4 nodes are always present - if there are only 3 nodes on a face
! (as for a tet), the 1st node is repeated as the 4th entry
!
!                                   face # ------>
!
! local_f2n_tet          reshape((/ 1, 2, 1, 1,
!                  n |              3, 3, 4, 2,
!                  o |              2, 4, 3, 4,
!                  d |              1, 2, 1, 1 /), (/ 4, 4/))
!                  e |
!                  # V
!
! *** see pictograms above ***

  integer, parameter, dimension (6,4) :: local_f2n_hex =                       &
  reshape((/ 1, 5, 5, 6, 5, 7,                                                 &
             3, 6, 7, 2, 1, 8,                                                 &
             4, 8, 3, 4, 2, 4,                                                 &
             2, 7, 1, 8, 6, 3 /), (/ 6, 4/))

  integer, parameter, dimension (4,4) :: local_f2n_tet =                       &
  reshape((/  1, 2, 1, 1,                                                      &
              3, 3, 4, 2,                                                      &
              2, 4, 3, 4,                                                      &
              1, 2, 1, 1 /), (/ 4, 4/))

  integer, parameter, dimension (5,4) :: local_f2n_prz =                       &
  reshape((/ 2, 1, 1, 1, 4,                                                    &
             3, 6, 4, 2, 6,                                                    &
             5, 4, 3, 5, 5,                                                    &
             2, 1, 2, 6, 3 /), (/ 5, 4/))

  integer, parameter, dimension (5,4) :: local_f2n_pyr =                       &
  reshape((/ 1, 3, 1, 2, 1,                                                    &
             2, 4, 5, 3, 4,                                                    &
             5, 5, 4, 5, 3,                                                    &
             1, 3, 1, 2, 2 /), (/ 5, 4/))


!--------------------------------------------------------
! chk_norm  used for metrics and viscous fluxes/jacobians
!--------------------------------------------------------

! the chk_norm array is a convenient way of indicating which faces in an element
! should have their inner products (i.e. the angle between them) computed:
!
! non-zero elements i,j in chk_norm indicate  we want to take the inner product
! of face i normal with face j normal, for the purpose of computing the angle
! between face i and face j. the arrays are set up below using the reshape
! function. the following illustrates the full array for a tetrahedral cell
! (though only the elements above the diagonal are used) :
!
!                                   face # ------>
!
! chk_norm =             reshape((/ 0, 1, 1, 1,
!                  f |              1, 0, 1, 1,
!                  a |              1, 1, 0, 1,
!                  c |              1, 1, 1, 0 /), (/ 4, 4/))
!                  e |
!                  # v
!
!
! in the example above, angles between faces 1-2, 1-3, 1-4,
! 2-3, 2-4, and 3-4 will be computed

  integer, parameter, dimension (6,6) :: chk_norm_hex =                        &
  reshape((/ 0, 0, 1, 1, 1, 1,                                                 &
             0, 0, 1, 1, 1, 1,                                                 &
             1, 1, 0, 0, 1, 1,                                                 &
             1, 1, 0, 0, 1, 1,                                                 &
             1, 1, 1, 1, 0, 0,                                                 &
             1, 1, 1, 1, 0, 0 /), (/ 6, 6/))

  integer, parameter, dimension (4,4) :: chk_norm_tet =                        &
  reshape((/ 0, 1, 1, 1,                                                       &
             1, 0, 1, 1,                                                       &
             1, 1, 0, 1,                                                       &
             1, 1, 1, 0 /), (/ 4, 4/))

  integer, parameter, dimension (5,5) :: chk_norm_prz =                        &
  reshape((/ 0, 0, 1, 1, 1,                                                    &
             0, 0, 1, 1, 1,                                                    &
             1, 1, 0, 1, 1,                                                    &
             1, 1, 1, 0, 1,                                                    &
             1, 1, 1, 1, 0 /), (/ 5, 5/))

  integer, parameter, dimension (5,5) :: chk_norm_pyr =                        &
  reshape((/ 0, 0, 1, 1, 1,                                                    &
             0, 0, 1, 1, 1,                                                    &
             1, 1, 0, 0, 1,                                                    &
             1, 1, 0, 0, 1,                                                    &
             1, 1, 1, 1, 0 /), (/ 5, 5/))


!---------------------------------------------
! local_e2n_2 used in subroutine edge_pointers
!---------------------------------------------

  integer, parameter, dimension (12, 2) :: local_e2n_2_hex =                   &
  reshape((/ 1, 1, 1, 2, 2, 3, 3, 4, 5, 5, 6, 7,                               &
             2, 3, 5, 4, 6, 4, 7, 8, 6, 7, 8, 8 /), (/12, 2/))

  integer, parameter, dimension ( 6, 2) :: local_e2n_2_tet =                   &
  reshape((/ 1, 1, 1, 2, 2, 3,                                                 &
             2, 3, 4, 3, 4, 4 /), (/ 6, 2/))

  integer, parameter, dimension ( 9, 2) :: local_e2n_2_prz =                   &
  reshape((/ 1, 1, 1, 2, 2, 3, 3, 4, 5,                                        &
             2, 4, 6, 3, 5, 4, 5, 6, 6 /), (/ 9, 2/))

  integer, parameter, dimension ( 8, 2) :: local_e2n_2_pyr =                   &
  reshape((/ 1, 2, 3, 4, 1, 2, 3, 1,                                           &
             5, 5, 5, 5, 2, 3, 4, 4 /), (/ 8, 2/))

!--------------------------------------------
! local_f2e used for viscous fluxes/jacobians
!--------------------------------------------

! the arrays are set up below using the reshape function. the following
! illustrates the array for a tetrahedral cell; note that entries for
! 4 edges are always present - if there are only 3 edges on a face
! (as for any face in a tet), the 1st edge is repeated as the 4th entry
!
! the ordering is abitrary but the the following system is used: looking
! at the face from outside the cell, the lowest edge number is placed first.
! then, subsequent edges are added by moving CCW around the face.
!
!                                   face # ------>
!
! local_f2e_tet          reshape((/ 1,  4,  2,  1,
!                  e |              2,  6,  3,  5,
!                  d |              4,  5,  6,  3,
!                  g |              1,  4,  2,  1,/), (/ 4, 4/))
!                  e |
!                  # V
!
! *** see pictograms above ***

   integer, parameter, dimension (6,4) :: local_f2e_hex =                      &
   reshape((/ 1,  9,  2,  4,  1,  6,                                           &
              2, 11,  3,  8,  5,  7,                                           &
              6, 12, 10, 11,  9, 12,                                           &
              4, 10,  7,  5,  3,  8 /), (/ 6, 4/))

   integer, parameter, dimension (4,4) :: local_f2e_tet =                      &
   reshape((/ 1,  4,  2,  1,                                                   &
              2,  6,  3,  5,                                                   &
              4,  5,  6,  3,                                                   &
              1,  4,  2,  1 /), (/ 4, 4/))

   integer, parameter, dimension (5,4) :: local_f2e_prz =                      &
   reshape((/ 4,  2,  1,  1,  6,                                               &
              7,  3,  2,  5,  8,                                               &
              5,  8,  6,  9,  9,                                               &
              4,  2,  4,  3,  7 /), (/ 5, 4/))

   integer, parameter, dimension (5,4) :: local_f2e_pyr =                      &
   reshape((/ 1,  3,  1,  2,  5,                                               &
              5,  7,  4,  6,  8,                                               &
              2,  4,  8,  3,  7,                                               &
              1,  3,  1,  2,  6 /), (/ 5, 4/))

!--------------------------------------------
! local_###2tet
!--------------------------------------------

! the arrays are set up below using the reshape function. the following
! illustrates the array for cutting a prizm in to 3 tetrahedral cells
!
!                                   tet # ------>
!
! local_prz2tet          reshape((/ 6,  6,  6,
!                  n |              1,  1,  2,
!                  o |              4,  3,  3,
!                  d |              3,  2,  4/), (/ 3, 4/))
!                  e |
!                  # V
!
! *** see pictograms above ***

   integer, parameter, dimension(1,4) :: local_tet2tet =                       &
   reshape((/ 1,                                                               &
              2,                                                               &
              3,                                                               &
              4 /), (/ 1, 4/))

   integer, parameter, dimension(5,4) :: local_hex2tet =                       &
   reshape((/ 1,  1,  1,  1,  7,                                               &
              3,  7,  7,  4,  6,                                               &
              7,  6,  5,  6,  4,                                               &
              4,  4,  6,  2,  8 /), (/ 5, 4/))

   integer, parameter, dimension(3,4) :: local_prz2tet =                       &
   reshape((/ 6,  6,  6,                                                       &
              1,  1,  2,                                                       &
              4,  3,  3,                                                       &
              3,  2,  5 /), (/ 3, 4/))

   integer, parameter, dimension(2,4) :: local_pyr2tet =                       &
   reshape((/ 1,  1,                                                           &
              2,  3,                                                           &
              3,  4,                                                           &
              5,  5 /), (/ 2, 4/))

 contains

!============================= NULLIFY_ELEM ==================================80
!
! Nullifies all the pointers in elem to make sure that their state is
! disassociated and not undefined so it is safe to use the associated intrinsic
!
!=============================================================================80

  subroutine nullify_elem( elem )

    use element_types, only : elem_type

    type(elem_type),  intent(inout) :: elem

    continue

    nullify(elem%c2n)
    nullify(elem%c2e)
    nullify(elem%cl2g)
    nullify(elem%c2c)
    nullify(elem%cell_map)
    nullify(elem%local_e2n)
    nullify(elem%local_f2n)
    nullify(elem%chk_norm)
    nullify(elem%local_e2n_2)
    nullify(elem%local_f2e)
    nullify(elem%e2n_2d)
    nullify(elem%big_angle)

  end subroutine nullify_elem

!=============================== DEALLOCATE_ELEM =============================80
!
! Deallocates memory for the grid derived type
!
!=============================================================================80

  subroutine deallocate_elem(elem)

    use element_types, only : elem_type

    type(elem_type), intent(inout) :: elem

    continue

    if (associated(elem%c2n))         deallocate(elem%c2n)
    if (associated(elem%c2e))         deallocate(elem%c2e)
    if (associated(elem%cl2g))        deallocate(elem%cl2g)

    if (associated(elem%c2c))         deallocate(elem%c2c)
    if (associated(elem%cell_map))    deallocate(elem%cell_map)

    if (associated(elem%local_e2n))   deallocate(elem%local_e2n)
    if (associated(elem%local_f2n))   deallocate(elem%local_f2n)
    if (associated(elem%chk_norm))    deallocate(elem%chk_norm)
    if (associated(elem%local_e2n_2)) deallocate(elem%local_e2n_2)
    if (associated(elem%local_f2e))   deallocate(elem%local_f2e)
    if (associated(elem%e2n_2d))      deallocate(elem%e2n_2d)
    if (associated(elem%big_angle))   deallocate(elem%big_angle)

  end subroutine deallocate_elem

!============================= INITIALIZE_ELEM ===============================80
!
! Allocates and initializes the element derived type helper arrays
! User must set elem%type_cell before calling routine or pass optional argument
!
!=============================================================================80

  subroutine initialize_elem( elem, optional_type_cell )

    use allocations,      only : my_alloc_ptr
    use element_types,    only : elem_type

    type(elem_type),            intent(inout) :: elem
    character(len=3), optional, intent(in)    :: optional_type_cell

    continue

    if (present(optional_type_cell)) elem%type_cell = optional_type_cell

    select case (elem%type_cell)
    case(type_tet)
      elem%node_per_cell = node_per_tet
      elem%edge_per_cell = edge_per_tet
      elem%face_per_cell = face_per_tet
      elem%face_2d       = 0              ! 2D not applicable
    case(type_hex)
      elem%node_per_cell = node_per_hex
      elem%edge_per_cell = edge_per_hex
      elem%face_per_cell = face_per_hex
      elem%face_2d       = 1              ! default
    case(type_prz)
      elem%node_per_cell = node_per_prz
      elem%edge_per_cell = edge_per_prz
      elem%face_per_cell = face_per_prz
      elem%face_2d       = 1              ! default
    case(type_pyr)
      elem%node_per_cell = node_per_pyr
      elem%edge_per_cell = edge_per_pyr
      elem%face_per_cell = face_per_pyr
      elem%face_2d       = 0              ! 2D not applicable
    case('agg')
      elem%node_per_cell = 1
      elem%edge_per_cell = 1
      elem%face_per_cell = 1
      elem%face_2d       = 0              ! 2D not applicable
    case default
      write(*,'(3a)') ' ERROR: Element type ',elem%type_cell,' not supported ',&
        'in module element_defs subroutine initialize_elem_data.'
    end select

    call my_alloc_ptr(elem%local_e2n,   elem%edge_per_cell, 6)
    call my_alloc_ptr(elem%local_e2n_2, elem%edge_per_cell, 2)
    call my_alloc_ptr(elem%local_f2n,   elem%face_per_cell, 4)
    call my_alloc_ptr(elem%chk_norm,    elem%face_per_cell, elem%face_per_cell)
    call my_alloc_ptr(elem%local_f2e,   elem%face_per_cell, 4)
    call my_alloc_ptr(elem%e2n_2d,                       4, 2)

    select case (elem%type_cell)
    case(type_tet)
      elem%local_e2n     = local_e2n_tet
      elem%local_e2n_2   = local_e2n_2_tet
      elem%local_f2n     = local_f2n_tet
      elem%chk_norm      = chk_norm_tet
      elem%local_f2e     = local_f2e_tet
    case(type_hex)
      elem%local_e2n     = local_e2n_hex
      elem%local_e2n_2   = local_e2n_2_hex
      elem%local_f2n     = local_f2n_hex
      elem%chk_norm      = chk_norm_hex
      elem%local_f2e     = local_f2e_hex
    case(type_prz)
      elem%local_e2n     = local_e2n_prz
      elem%local_e2n_2   = local_e2n_2_prz
      elem%local_f2n     = local_f2n_prz
      elem%chk_norm      = chk_norm_prz
      elem%local_f2e     = local_f2e_prz
    case(type_pyr)
      elem%local_e2n     = local_e2n_pyr
      elem%local_e2n_2   = local_e2n_2_pyr
      elem%local_f2n     = local_f2n_pyr
      elem%chk_norm      = chk_norm_pyr
      elem%local_f2e     = local_f2e_pyr
    case('agg')
      elem%local_e2n     = local_e2n_tet
      elem%local_e2n_2   = local_e2n_2_tet
      elem%local_f2n     = local_f2n_tet
      elem%chk_norm      = chk_norm_tet
      elem%local_f2e     = local_f2e_tet
    case default
      write(*,'(3a)') ' ERROR: Element type ',elem%type_cell,' not supported ',&
        'in module element_defs subroutine initialize_elem_data.'
    end select

  end subroutine initialize_elem

!=============================== ALLOCATE_ELEM ===============================80
!
! Allocates memory for the element derived type
! (2nd layer of the grid derived type)
!
!=============================================================================80

  subroutine allocate_elem(elem, cc_primal)

    use allocations,   only : my_alloc_ptr
    use element_types, only : elem_type

    type(elem_type),           intent(inout) :: elem
    logical,                   intent(in)    :: cc_primal

    integer :: ncell

    continue

    ncell = max(elem%ncell,1)

    call my_alloc_ptr(elem%cl2g, ncell)
    call my_alloc_ptr(elem%c2n,  elem%node_per_cell, ncell)
    call my_alloc_ptr(elem%c2e,  elem%edge_per_cell, ncell)
    if ( cc_primal ) then
      call my_alloc_ptr(elem%cell_map, ncell)
      call my_alloc_ptr(elem%big_angle,    1)
    else
      call my_alloc_ptr(elem%cell_map,      1)
      call my_alloc_ptr(elem%big_angle, ncell)
    endif

  end subroutine allocate_elem


!=============================== ELEMENT_METRIC ==============================80
!
!  Computes the volume and face normals of a grid cells via the method used in
!  NSU3D. This information is only used for grid quality checking; it is not
!  stored.
!
!  Cell types currently supported: tetrahedrons, hexahedrons, prisms, and
!  pyramids.
!
!-------------------------------------------------------------------------------
!
!   volumes are calculated by application of Green's theorem as follows:
!
!         _ _ _                     _ _
!        / / /                     / /
!       / / /     ->              / / ->  ->
!      / / /  Div(r ) dV  =      / /  n * r  dS    (* denotes dot product)
!    _/_/_/                    _/_/
!
!      V                        S
!
!        ->            ->  ->
!   Div(r ) = 3,   n * r dS = (x n  + y n  + z n  )dS = x dS  + y dS  + z dS
!                                 x      y      z           x       y       z
!
!   where S , S , S   are components of the directed area (outward pointing)
!          x   y   z
!
!   thus,
!
!                        _ _
!                  1.   / /
!            V  =  --  / /  x dS  + y dS  + z dS
!                  3. / /       x       y       z
!                   _/_/
!
!                    S
!
!   assuming piecewise constant (avg) values of x,y,z on surface segments:
!
!                  1.
!            V  =  -- sum ( x    S  + y    S  + z    S  )
!                  3.  i     avg  x    avg  y    avg  z  i
!
!   where each piece i is a triangle. Directed areas S , S , S  for triangles
!   are computed in subroutine triarea                x   y   z
!
!=============================================================================80
  subroutine element_metric(icell, ielem, grid, volume, xn, yn, zn)

    use kinddefs,        only : dp
    use grid_types,      only : grid_type
    use fun3d_constants, only : my_3rd, my_half

    integer,                                             intent(in)  :: icell
    integer,                                             intent(in)  :: ielem
    type(grid_type),                                     intent(in)  :: grid
    real(dp),                                            intent(out) :: volume
    real(dp), dimension(grid%elem(ielem)%face_per_cell), optional,             &
                                                         intent(out) :: xn,yn,zn

    integer  :: if,n1,n2,n3,n4

    real(dp) :: term0,term1,term2
    real(dp) :: xavg,yavg,zavg
    real(dp) :: areax,areay,areaz
    real(dp) :: rainv
    logical  :: need_normal

  continue

    term0 = 0._dp
    term1 = 0._dp
    term2 = 0._dp

    need_normal = .false.
    if (present(xn) .and. present(yn) .and. present(zn)) need_normal = .true.

    if (need_normal) then
      xn = 0._dp
      yn = 0._dp
      zn = 0._dp
    endif

! loop over tria faces

    tria_faces_loop : do if = 1, grid%elem(ielem)%face_per_cell

      if (grid%elem(ielem)%local_f2n(if,4) ==                                  &
          grid%elem(ielem)%local_f2n(if,1)) then

        n1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(if,1),icell)
        n2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(if,2),icell)
        n3 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(if,3),icell)

        xavg = (grid%x(n1) + grid%x(n2) + grid%x(n3))*my_3rd
        yavg = (grid%y(n1) + grid%y(n2) + grid%y(n3))*my_3rd
        zavg = (grid%z(n1) + grid%z(n2) + grid%z(n3))*my_3rd

        call triarea( grid%x(n1), grid%y(n1), grid%z(n1),                      &
                      grid%x(n2), grid%y(n2), grid%z(n2),                      &
                      grid%x(n3), grid%y(n3), grid%z(n3),                      &
                      areax, areay, areaz )

        term0 = term0 + xavg*areax + yavg*areay + zavg*areaz

        if (need_normal) then
          xn(if) = areax
          yn(if) = areay
          zn(if) = areaz

!         unit normal

          rainv = 1._dp/sqrt(xn(if)*xn(if) + yn(if)*yn(if) + zn(if)*zn(if))

          xn(if) = xn(if)*rainv
          yn(if) = yn(if)*rainv
          zn(if) = zn(if)*rainv
        endif

      end if

    end do tria_faces_loop

    term0 = term0*my_3rd

!   loop over quad faces

    quad_faces_loop : do if = 1, grid%elem(ielem)%face_per_cell

      if (grid%elem(ielem)%local_f2n(if,4) /=                                  &
          grid%elem(ielem)%local_f2n(if,1)) then

        n1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(if,1),icell)
        n2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(if,2),icell)
        n3 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(if,3),icell)
        n4 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(if,4),icell)

!       quad faces are split into 2 triangles - this splitting can
!       be done in 2 ways (one for each diagonal). Start by
!       picking one diagonal, then calculate volumes. Later,
!       pick the other diagonal and repeat. Finally can average
!       the two results to eliminate bias.

!       break face up into triangles 1-2-3 and 1-3-4

!       triangle 1-2-3

        xavg = (grid%x(n1) + grid%x(n2) + grid%x(n3))*my_3rd
        yavg = (grid%y(n1) + grid%y(n2) + grid%y(n3))*my_3rd
        zavg = (grid%z(n1) + grid%z(n2) + grid%z(n3))*my_3rd

        call triarea( grid%x(n1), grid%y(n1), grid%z(n1),                      &
                      grid%x(n2), grid%y(n2), grid%z(n2),                      &
                      grid%x(n3), grid%y(n3), grid%z(n3),                      &
                      areax, areay, areaz )

        term1 = term1 + xavg*areax + yavg*areay + zavg*areaz

        if (need_normal) then
          xn(if) = xn(if) + areax
          yn(if) = yn(if) + areay
          zn(if) = zn(if) + areaz
        endif

!       triangle 1-3-4

        xavg = (grid%x(n1) + grid%x(n3) + grid%x(n4))*my_3rd
        yavg = (grid%y(n1) + grid%y(n3) + grid%y(n4))*my_3rd
        zavg = (grid%z(n1) + grid%z(n3) + grid%z(n4))*my_3rd

        call triarea( grid%x(n1), grid%y(n1), grid%z(n1),                      &
                      grid%x(n3), grid%y(n3), grid%z(n3),                      &
                      grid%x(n4), grid%y(n4), grid%z(n4),                      &
                      areax, areay, areaz )

        term1 = term1 + xavg*areax + yavg*areay + zavg*areaz

        if (need_normal) then
          xn(if) = xn(if) + areax
          yn(if) = yn(if) + areay
          zn(if) = zn(if) + areaz
        endif

!       now repeat calculation, but this time use alternate face
!       diagonal when splitting faces into triangles

!       break face up into triangles 1-2-4 and 2-3-4

!       triangle 1-2-3

        xavg = (grid%x(n1) + grid%x(n2) + grid%x(n4))*my_3rd
        yavg = (grid%y(n1) + grid%y(n2) + grid%y(n4))*my_3rd
        zavg = (grid%z(n1) + grid%z(n2) + grid%z(n4))*my_3rd

        call triarea( grid%x(n1), grid%y(n1), grid%z(n1),                      &
                      grid%x(n2), grid%y(n2), grid%z(n2),                      &
                      grid%x(n4), grid%y(n4), grid%z(n4),                      &
                      areax, areay, areaz )

        term2 = term2 + xavg*areax + yavg*areay + zavg*areaz

        if (need_normal) then
          xn(if) = xn(if) + areax
          yn(if) = yn(if) + areay
          zn(if) = zn(if) + areaz
        endif

!       triangle 2-3-4

        xavg = (grid%x(n2) + grid%x(n3) + grid%x(n4))*my_3rd
        yavg = (grid%y(n2) + grid%y(n3) + grid%y(n4))*my_3rd
        zavg = (grid%z(n2) + grid%z(n3) + grid%z(n4))*my_3rd

        call triarea( grid%x(n2), grid%y(n2), grid%z(n2),                      &
                      grid%x(n3), grid%y(n3), grid%z(n3),                      &
                      grid%x(n4), grid%y(n4), grid%z(n4),                      &
                        areax, areay, areaz)

        term2 = term2 + xavg*areax + yavg*areay + zavg*areaz

        if (need_normal) then
          xn(if) = xn(if) + areax
          yn(if) = yn(if) + areay
          zn(if) = zn(if) + areaz

!         average face normals from the two different quad splittings

          xn(if) = xn(if)*my_half
          yn(if) = yn(if)*my_half
          zn(if) = zn(if)*my_half

!         unit normal

          rainv = 1._dp/sqrt(xn(if)*xn(if) + yn(if)*yn(if) + zn(if)*zn(if))

          xn(if) = xn(if)*rainv
          yn(if) = yn(if)*rainv
          zn(if) = zn(if)*rainv
        endif

      end if

    end do quad_faces_loop

    term1 = term1*my_3rd
    term2 = term2*my_3rd

!   add the volume contribution from the tria faces and average the
!   resultant volume contributions from the two different quad splittings

    volume = term0 + (term1 + term2)*my_half

  end subroutine element_metric


!================================ TRIAREA ====================================80
!
!  Computes the components of the directed area of a triangle with nodes 1,2,3
!  via a vector cross product
!
!=============================================================================80

  subroutine triarea( x1, y1, z1, x2, y2, z2, x3, y3, z3, areax, areay, areaz )

    use kinddefs,        only : dp

    real(dp),     intent(in) :: x1,y1,z1,x2,y2,z2,x3,y3,z3
    real(dp),    intent(out) :: areax,areay,areaz

!-------------------------------------------------------------------------------
!
!   Area of triangle is 1/2 area of parallelogram with sides
!
!   ->   ->       ->   ->
!   r2 - r1  and  r3 - r1
!
!            3
!
!           /\               ->          ->   ->     ->   ->
!          /  \             Area = 0.5*((r2 - r1) x (r3 - r1))
!         /    \
!        /      \            ->         ->        ->        ->
!     1 /________\ 2        Area = areax i + areay j + areaz k
!
!
!-------------------------------------------------------------------------------

    continue

    areax = 0.5_dp*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )

    areay = 0.5_dp*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )

    areaz = 0.5_dp*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )

  end subroutine triarea


!============================= CELL_TYPE_2_INTEGER ===========================80
!
! Convert type_cell to integer
!
!=============================================================================80

  function type_cell_2_integer( type_cell )

    character(len=3), intent(in)  :: type_cell
    integer                       :: type_cell_2_integer

    continue

    type_cell_2_integer = huge(1)
    select case (type_cell)
    case(type_tet)
      type_cell_2_integer = 1
    case(type_hex)
      type_cell_2_integer = 2
    case(type_prz)
      type_cell_2_integer = 3
    case(type_pyr)
      type_cell_2_integer = 4
    case('agg')
      type_cell_2_integer = 5
    end select

  end function type_cell_2_integer

  pure subroutine fun3d_to_ugrid_pyramid(fun3d,ugrid)
    integer, dimension(5), intent(in)  :: fun3d
    integer, dimension(5), intent(out) :: ugrid
    continue
    ugrid(1) = fun3d(4)
    ugrid(2) = fun3d(3)
    ugrid(3) = fun3d(5)
    ugrid(4) = fun3d(1)
    ugrid(5) = fun3d(2)
  end subroutine fun3d_to_ugrid_pyramid
  pure subroutine ugrid_to_fun3d_pyramid(ugrid,fun3d)
    integer, dimension(5), intent(in)   :: ugrid
    integer, dimension(5), intent(out)  :: fun3d
    continue
    fun3d(4) = ugrid(1)
    fun3d(3) = ugrid(2)
    fun3d(5) = ugrid(3)
    fun3d(1) = ugrid(4)
    fun3d(2) = ugrid(5)
  end subroutine ugrid_to_fun3d_pyramid

  pure subroutine fun3d_to_ugrid_prism(fun3d,ugrid)
    integer, dimension(6), intent(in)  :: fun3d
    integer, dimension(6), intent(out) :: ugrid
    continue
    ugrid(1) = fun3d(1)
    ugrid(2) = fun3d(4)
    ugrid(3) = fun3d(6)
    ugrid(4) = fun3d(2)
    ugrid(5) = fun3d(3)
    ugrid(6) = fun3d(5)
  end subroutine fun3d_to_ugrid_prism
  pure subroutine ugrid_to_fun3d_prism(ugrid,fun3d)
    integer, dimension(6), intent(in)  :: ugrid
    integer, dimension(6), intent(out) :: fun3d
    continue
    fun3d(1) = ugrid(1)
    fun3d(4) = ugrid(2)
    fun3d(6) = ugrid(3)
    fun3d(2) = ugrid(4)
    fun3d(3) = ugrid(5)
    fun3d(5) = ugrid(6)
  end subroutine ugrid_to_fun3d_prism

  pure subroutine fun3d_to_ugrid_hex(fun3d,ugrid)
    integer, dimension(8), intent(in)  :: fun3d
    integer, dimension(8), intent(out) :: ugrid
    continue
    ugrid(1) = fun3d(1)
    ugrid(2) = fun3d(5)
    ugrid(3) = fun3d(6)
    ugrid(4) = fun3d(2)
    ugrid(5) = fun3d(3)
    ugrid(6) = fun3d(7)
    ugrid(7) = fun3d(8)
    ugrid(8) = fun3d(4)
  end subroutine fun3d_to_ugrid_hex
  pure subroutine ugrid_to_fun3d_hex(ugrid,fun3d)
    integer, dimension(8), intent(in)  :: ugrid
    integer, dimension(8), intent(out) :: fun3d
    continue
    fun3d(1) = ugrid(1)
    fun3d(5) = ugrid(2)
    fun3d(6) = ugrid(3)
    fun3d(2) = ugrid(4)
    fun3d(3) = ugrid(5)
    fun3d(7) = ugrid(6)
    fun3d(8) = ugrid(7)
    fun3d(4) = ugrid(8)
  end subroutine ugrid_to_fun3d_hex

  pure subroutine fun3d_to_vtk_prz(fun3d,vtk)
    integer, dimension(6), intent(in)  :: fun3d
    integer, dimension(6), intent(out) :: vtk
    continue
    vtk(1) = fun3d(6)
    vtk(2) = fun3d(4)
    vtk(3) = fun3d(1)
    vtk(4) = fun3d(5)
    vtk(5) = fun3d(3)
    vtk(6) = fun3d(2)
  end subroutine fun3d_to_vtk_prz

  pure subroutine fun3d_to_cgns_prism(fun3d,cgns)
    integer, dimension(6), intent(in)  :: fun3d
    integer, dimension(6), intent(out) :: cgns
    continue
    cgns(1) = fun3d(1)
    cgns(4) = fun3d(2)
    cgns(5) = fun3d(3)
    cgns(2) = fun3d(4)
    cgns(6) = fun3d(5)
    cgns(3) = fun3d(6)
  end subroutine fun3d_to_cgns_prism

  pure subroutine fun3d_to_cgns_hex(fun3d,cgns)
    integer, dimension(8), intent(in)  :: fun3d
    integer, dimension(8), intent(out) :: cgns
    continue
    cgns(4) = fun3d(1)
    cgns(8) = fun3d(2)
    cgns(1) = fun3d(3)
    cgns(5) = fun3d(4)
    cgns(3) = fun3d(5)
    cgns(7) = fun3d(6)
    cgns(2) = fun3d(7)
    cgns(6) = fun3d(8)
  end subroutine fun3d_to_cgns_hex

end module element_defs

!=============================== ELEMENT_CENTER ==============================80
!
! Cell center (must loop over node_per_cell even in 2D)
!
!=============================================================================80

  pure function element_center( node_per_cell, r )

    integer,                  intent(in)   :: node_per_cell
    real(dp), dimension(:,:), intent(in)   :: r
    real(dp), dimension(3)                 :: element_center

    integer :: i

  continue

    element_center(1:3) = 0._dp

    do i = 1, node_per_cell

      element_center(1:3) = element_center(1:3) + r(i,1:3)

    end do

    element_center(1:3) = element_center(1:3) / real(node_per_cell, dp)

  end function element_center

!================================= KW_BLEND  =================================80
!
! This routine computes the turbulent blending function terms for K_W
!
!=============================================================================80

  pure function kw_blend( xmre, rnu, distance, turb,                           &
                            tgradx, tgrady, tgradz )

    use kinddefs,          only : dp
    use turb_kw_const,     only : betastar, sig_w2

    real(dp),               intent(in) :: xmre, rnu, distance
    real(dp), dimension(2), intent(in) :: tgradx, tgrady, tgradz
    real(dp), dimension(2), intent(in) :: turb

    real(dp) :: kw_blend

    real(dp) :: term
    real(dp) :: crossterm
    real(dp) :: arg, arga, argt, arg1, arg2, arg3, dist, omega
    real(dp) :: rwx, rwy, rwz, rkx, rky, rkz, xmr, xmr2
    real(dp) :: turb1, turb2

    real(dp), parameter     :: my_tiny = tiny(1.0_dp)

  continue

!   Calculate the cross-derivative term as well as the blending function

    xmr = xmre
    xmr2 = xmr*xmr

      rkx = tgradx(1)
      rky = tgrady(1)
      rkz = tgradz(1)
      rwx = tgradx(2)
      rwy = tgrady(2)
      rwz = tgradz(2)

      turb1 = turb(1)
      turb2 = turb(2)

      omega = turb2
      dist = distance
      if(abs(dist) <= my_tiny) dist = 1.0e-12_dp

      term = rkx*rwx + rky*rwy + rkz*rwz
      crossterm = 2.0_dp*xmr/sig_w2*term/omega

!     Blending function

      arg1 = xmr*sqrt(turb1)/(betastar*omega*dist)
      arg2 = 500.0_dp*xmr2*rnu/(dist*dist*omega)
      argt = max(crossterm/xmr,1.0e-20_dp)
      arg3 = 4.0_dp/sig_w2*turb1/(argt*dist*dist)
      arga = max(arg1,arg2)
      arg  = min(arga,arg3)

      kw_blend = tanh(arg*arg*arg*arg)

  end function kw_blend

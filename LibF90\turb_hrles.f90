module turb_hrles

  use lmpi,     only : lmpi_die, lmpi_conditional_stop
  use kinddefs, only : dp

  implicit none

  private

  public :: residual_hrles
  public :: jacobian_hrles
  public :: bc_hrles_set_walls
  public :: flux_turb

  real(dp), parameter :: zero    = 0.0_dp
  real(dp), parameter :: half    = 0.5_dp

  real(dp), dimension(:), allocatable :: blend
  real(dp), dimension(:), allocatable :: crossd

  logical :: blend_setup = .false.

contains

!============================= RESIDUAL_HRLES ===============================80
!
! Driver routine for residual evaluation for HRLES model
!
!=============================================================================80

  subroutine residual_hrles(grid, soln)

    use info_depr,      only : tightly_couple, ngrid
    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use lmpi,           only : lmpi_master

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln

  continue

    if ( ngrid > 1 ) then
      write(*,*) ' Because templated turbulence routines ditched, need to ',&
                 ' cut and paste code to use multigrid.'
      call lmpi_die() ; stop
    endif

    coupling : if ( tightly_couple ) then

      if ( lmpi_master ) then
        write(*,*) 'Cannot perform tightly-coupled HRLES simulation.'
        call lmpi_die
      endif

    else coupling

      call hrles_resid(soln%eqn_set, soln%viscous_method,                      &
                       grid%nnodes0,              grid%nnodes01,               &
                       grid%nedgeloc,             grid%eptr,                   &
                       soln%turb,                 soln%q_dof,                  &
                       soln%turbres,              grid%slen,                   &
                       soln%gradx,                soln%grady,                  &
                       soln%gradz,                grid%vol,                    &
                       grid%xn,                   grid%yn,                     &
                       grid%zn,                   grid%ra,                     &
                       grid%x,                    grid%y,                      &
                       grid%z,                    grid%nedgeloc_2d,            &
                       grid%nnodes0_2d,           grid%node_pairs_2d,          &
                       grid%iflagslen,            grid%facespeed,              &
                       soln%n_turb,               soln%n_tot,                  &
                       soln%n_grd,                soln%amut,                   &
                       grid%nelem,                grid%elem )

    end if coupling

  end subroutine residual_hrles


!============================ BC_HRLES_SET_WALLS ============================80
!
!  Sets quantities on viscous walls for HRLES
!
!=============================================================================80

  subroutine bc_hrles_set_walls(eqn_set,                                       &
                                 nnodes0, nnodes01, nbnode, ibnode, slen_wall, &
                                 turb, qnode, n_turb, n_tot, ibc, mu_t_wf      &
                               , k_wf, omega_wf )

    use turb_2eqn_routines,      only : wall_turbulence_kw_sst
    use solution_types, only : compressible, incompressible

    integer, intent(in) :: nbnode, n_turb, n_tot, eqn_set
    integer, intent(in) :: nnodes0, nnodes01, ibc

    integer,     dimension(nbnode),         intent(in)    :: ibnode
    real(dp),    dimension(nbnode),         intent(in)    :: slen_wall
    real(dp),    dimension(nbnode),         intent(in)    :: mu_t_wf
    real(dp),    dimension(nbnode),         intent(in)    :: k_wf
    real(dp),    dimension(nbnode),         intent(in)    :: omega_wf

    real(dp), dimension(n_turb,nnodes01),intent(inout) :: turb
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode

    integer :: i,inode
    real(dp) :: tke_off

    real(dp), dimension(n_tot)  :: qlocal
    real(dp), dimension(n_turb) :: turb_wall

    continue

    if ( eqn_set /= compressible .and. eqn_set /= incompressible ) then
      call lmpi_conditional_stop(1,'bc_hrles_set_walls: only in/cmpr pg')
    end if

    tke_off = 0.0_dp

    do i = 1,nbnode
      inode = ibnode(i)
      if (inode <= nnodes0) then

        qlocal = qnode(1:n_tot,inode)

        call wall_turbulence_kw_sst( eqn_set, ibc, slen_wall(i), qlocal        &
             , tke_off, turb_wall, k_wf(i), omega_wf(i), mu_t_wf(i) )
        turb(:,inode) = turb_wall(:)

      end if
    end do

  end subroutine bc_hrles_set_walls


!============================= JACOBIAN_HRLES ===============================80
!
! Driver routine for jacobian evaluation for HRLES model
!
!=============================================================================80

  subroutine jacobian_hrles(grid, soln, crow)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow

    type(grid_type), intent(in)    :: grid
    type(crow_flow), intent(in)    :: crow
    type(soln_type), intent(inout) :: soln

  continue

    call hrles_jacob(soln%eqn_set, soln%viscous_method, grid%nnodes0,          &
                     grid%nnodes01, grid%nedgeloc, soln%max_nnz,               &
                     grid%eptr, soln%turb, soln%q_dof, soln%gradx,             &
                     soln%grady, soln%gradz, grid%vol, grid%xn, grid%yn,       &
                     grid%zn, grid%ra, soln%a_turb_diag, soln%a_turb_off,      &
                     crow%fhelp, grid%nedgeloc_2d, grid%nnodes0_2d,            &
                     grid%node_pairs_2d, grid%x, grid%y, grid%z, crow%nnz01,   &
                     crow%ia, crow%ja, grid%facespeed, soln%n_turb, soln%n_tot,&
                     soln%n_grd, crow%nzg2m, crow%g2m, soln%amut, grid%nelem,  &
                     grid%elem )

  end subroutine jacobian_hrles


!========================== FLUX_TURB ========================================80
!
! Flux function for HRLES bc residual calculation
!
!=============================================================================80
  pure function flux_turb ( ubar, area, ql, qr, n_turb )

    integer,                     intent(in) :: n_turb
    real(dp),                    intent(in) :: ubar
    real(dp),                    intent(in) :: area
    real(dp), dimension(n_turb), intent(in) :: ql
    real(dp), dimension(n_turb), intent(in) :: qr

    real(dp), dimension(n_turb)        :: flux_turb
    real(dp), dimension(n_turb)        :: fluxl
    real(dp), dimension(n_turb)        :: fluxr
    real(dp)                           :: uplus
    real(dp)                           :: uminus

! inside to outside

    uplus  = half * ( ubar + abs(ubar) )
    uminus = half * ( ubar - abs(ubar) )

    fluxl(1)     = uplus  * ql(1)
    fluxr(1)     = uminus * qr(1)
    flux_turb(1) = ( fluxl(1) + fluxr(1) ) * area

    if ( n_turb == 2 ) then
      fluxl(n_turb)     = uplus  * ql(n_turb)
      fluxr(n_turb)     = uminus * qr(n_turb)
      flux_turb(n_turb) = ( fluxl(n_turb) + fluxr(n_turb) ) * area
    endif

! node 2 to node 1
!   ubarm  = -ubar
!   uplus  = half * ( ubarm + abs(ubarm) )
!   uminus = half * ( ubarm - abs(ubarm) )

!   fluxl(1) = uminus * ql(1)
!   fluxl(2) = uminus * ql(2)

!   fluxr(1) = uplus  * qr(1)
!   fluxr(2) = uplus  * qr(2)

!   flux_turb(1) = flux_turb(1) + ( fluxl(1) + fluxr(1) ) * area
!   flux_turb(2) = flux_turb(1) + ( fluxl(2) + fluxr(2) ) * area

  end function flux_turb

!================================= HRLES_RESID ===============================80
!
! Calculates the residual for HRLES model on mixed-element grids.i
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine hrles_resid(eqn_set, viscous_method, nnodes0, nnodes01,           &
                         nedgeloc, eptr, turb, qnode, res, slen, gradx, grady, &
                         gradz, vol, xn, yn, zn, ra, x, y, z, nedgeloc_2d,     &
                         nnodes0_2d, node_pairs_2d, iflagslen, facespeed,      &
                         n_turb, n_tot, n_grd, amut, nelem, elem )

    use info_depr,           only : tref, xmach, re, twod
    use grid_motion_helpers, only : need_grid_velocity
    use nml_two_d_trans,     only : turb_transition
    use debug_defs,          only : test_freestream
    use solution_types,      only : compressible, incompressible
    use turb_kw_const,       only : beta1, beta2, betastar, sig_w1, sig_w2,    &
                                    kappa, strain_production, sstrc_crc, sstrc,&
                                    lns_hybrid, pre_4_17_2009_hrles,           &
                                    turb_compress_model
    use turb_hrles_const,    only : c_ep, c_nu
    use fluid,               only : gamma, sutherland_constant
    use turb_util,           only : kloc, wloc
    use element_types,       only : elem_type
    use lmpi_app,            only : lmpi_xfer

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: nelem
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_grd
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: nnodes0_2d

    integer,  dimension(nnodes01),           intent(in) :: iflagslen
    integer,  dimension(2,nedgeloc),         intent(in) :: eptr
    integer,  dimension(2,nnodes0_2d),       intent(in) :: node_pairs_2d

    real(dp), dimension(nnodes01),        intent(in)    :: slen
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z, vol
    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),        intent(in)    :: facespeed
    real(dp), dimension(nedgeloc),        intent(in)    :: ra
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(nnodes01),        intent(in)    :: amut
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    type(elem_type), dimension(nelem), intent(in)       :: elem

    integer :: n, nedge_flux_eval, node_src_eval, ielem
    integer :: i, ii
    integer :: node1, node2

    real(dp) :: rho, rmut, vort, tij
    real(dp) :: Pk, Pk_rans, Pk_sgs, Dk, Dk_rans, Dk_sgs, sourcek
    real(dp) :: Pw, Dw, sourcew
    real(dp) :: rkx, rky, rkz, rwx, rwy, rwz, dist
    real(dp) :: turb1_nodei, turb2_nodei, omega, term, crossterm
    real(dp) :: arg1, arg2, argt, arg3, arga, arg
    real(dp) :: xmr, xmr2
    real(dp) :: SijSij, SiiSii, ke_term
    real(dp) :: f1
    real(dp) :: zeman_lag
    real(dp) :: heaviside
    real(dp) :: f_turb_mach
    real(dp) :: f_beta
    real(dp) :: f_betastar, betastar_inv
    real(dp) :: turb_mach
    real(dp) :: tke
    real(dp) :: alpa_innr, alpa_outr, alpa_blend
    real(dp) :: beta_blend
    real(dp) :: xnormf, ynormf, znormf, areaf, cstar, face_speed
    real(dp) :: p, rnu, temp, rhoinv
    real(dp) :: u, v, w, ubar, uminus, uplus
    real(dp) :: uy, uz, vx, vz, wx, wy, ux, vy, wz
    real(dp) :: my_xmach, xmrinv, sqrt_betastar_inv
    real(dp) :: sig_w1_inv, sig_w2_inv
    real(dp) :: xis, sij, ri, f4, f_lns, f, mut_sgs, turb_mach_0
    real(dp) :: xsi_star
    real(dp) :: delta

    real(dp), parameter :: my_tiny     = tiny(1.0_dp)  !epsilon???
    real(dp), parameter :: my_2        = 2.0_dp
    real(dp), parameter :: third       = 1.0_dp/3.0_dp
    real(dp), parameter :: twothird    = 2.0_dp/3.0_dp
    real(dp), parameter :: epsilon0    = 1.0e-20_dp
    real(dp), parameter :: my_0        = 0.0_dp
    real(dp), parameter :: my_half     = 0.5_dp
    real(dp), parameter :: my_1        = 1.0_dp

  continue

!   kloc = n_grd-2
!   wloc = n_grd-1

    if ( .not. blend_setup ) then
      allocate(blend(nnodes01))
      allocate(crossd(nnodes0))
      blend_setup = .true.
    endif

    cstar = sutherland_constant / tref
    my_xmach   = 0.0_dp
    p          = 0.0_dp
    f_beta     = 0.0_dp
    f_betastar = 0.0_dp
    rho        = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'hrles_resid_mix: only for in/comprss pg')
    end select

    xmr     = my_xmach / re
    xmrinv  = my_1 / xmr
    xmr2    = xmr*xmr
    betastar_inv = my_1 / betastar
    sqrt_betastar_inv = my_1 / sqrt(betastar)
    sig_w1_inv = my_1/sig_w1
    sig_w2_inv = my_1/sig_w2

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    nedge_flux_eval = nedgeloc
    node_src_eval   = nnodes0
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
      node_src_eval   = nnodes0_2d
    endif

!   Calculate the cross-derivative term as well as the blending function

    nnodes0_loop1: do i = 1, nnodes0

      rkx = gradx(kloc,i)
      rky = grady(kloc,i)
      rkz = gradz(kloc,i)
      rwx = gradx(wloc,i)
      rwy = grady(wloc,i)
      rwz = gradz(wloc,i)

      turb1_nodei = turb(1,i)
      turb2_nodei = turb(2,i)

      omega = turb2_nodei
      dist = slen(i)
      if(abs(dist) <= my_tiny) dist = 1.0e-12_dp

      term = rkx*rwx + rky*rwy + rkz*rwz
      crossterm = 2.0_dp*xmr*sig_w2_inv*term/omega

!     Blending function

      if ( eqn_set == compressible ) then
        rhoinv  = 1.0_dp / qnode(1,i)
        p    = qnode(5,i)
        temp = gamma * p * rhoinv
        rnu  = viscosity_law( cstar, temp ) * rhoinv
      else
        rnu  = my_1
      end if

      arg1 = xmr*sqrt(turb1_nodei)/(betastar*omega*dist)
      arg2 = 500.0_dp*xmr2*rnu/(dist*dist*omega)
      argt = max(crossterm*xmrinv,1.0e-20_dp)
      arg3 = 4.0_dp*sig_w2_inv*turb1_nodei/(argt*dist*dist)
      arga = max(arg1,arg2)
      arg  = min(arga,arg3)

      blend(i) = tanh(arg*arg*arg*arg)
      crossd(i) = crossterm

    end do nnodes0_loop1

    call lmpi_xfer(blend)

!   Now that we have the blending function we can evaluate the weights for
!   the second-order terms

! Now lets compute the convective terms.

    conv : do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

!     Unit normal to dual face and area

      xnormf = xn(n)
      ynormf = yn(n)
      znormf = zn(n)
      areaf  = ra(n)

!     Dual face speed

      face_speed = 0._dp

      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

!     First node
!     Convective part

      u = qnode(2,node1)
      v = qnode(3,node1)
      w = qnode(4,node1)
      ubar   = xnormf*u + ynormf*v + znormf*w - face_speed
      uplus  = my_half * (ubar+abs(ubar))
      uminus = my_half * (ubar-abs(ubar))

      if(node1 <= nnodes0) then
        res(1,node1) = res(1,node1) + (uplus*turb(1,node1)                     &
                                    + uminus*turb(1,node2))*areaf
        res(2,node1) = res(2,node1) + (uplus*turb(2,node1)                     &
                                    + uminus*turb(2,node2))*areaf
      endif

!     Now do the other node (note that ubar pts in other direction)

      u = qnode(2,node2)
      v = qnode(3,node2)
      w = qnode(4,node2)

      ubar   = -(xnormf*u + ynormf*v + znormf*w - face_speed)
      uplus  = my_half * (ubar+abs(ubar))
      uminus = my_half * (ubar-abs(ubar))

      if (node2 <= nnodes0) then
        res(1,node2) = res(1,node2) + (uplus*turb(1,node2)                     &
                                    + uminus*turb(1,node1))*areaf
        res(2,node2) = res(2,node2) + (uplus*turb(2,node2)                     &
                                    + uminus*turb(2,node1))*areaf
      endif

    end do conv

!   Next compute the source (production/destruction) terms
!   Note that these terms are node-based and thus are grid transparent, and
!   hence only need be computed while processing the first element type

    alpa_innr  = beta1*betastar_inv - kappa*kappa*sig_w1_inv*sqrt_betastar_inv
    alpa_outr  = beta2*betastar_inv - kappa*kappa*sig_w2_inv*sqrt_betastar_inv

    source1 : do ii = 1,node_src_eval

      if (twod) then
        i = node_pairs_2d(1,ii)
      else
        i = ii
      end if

      tke   = turb(1,i)
      omega = turb(2,i)

! kappa == turb_kappa
! beta1 == beta_innr
! beta2 == beta_outr
! betab == beta_blend

      f1         = blend(i)

      beta_blend = f1*beta1     + (1.0_dp-f1)*beta2
      alpa_blend = f1*alpa_innr + (1.0_dp-f1)*alpa_outr

      if ( eqn_set == compressible ) then
        rho     = qnode(1,i)
        rhoinv  = my_1 / qnode(1,i)
        p    = qnode(5,i)
        temp = gamma * p * rhoinv
        rnu  = viscosity_law( cstar, temp ) !FIXME: rnu or rmu, is it even used?
      else
        temp = my_1
        rnu  = my_1
        rho  = my_1
        rhoinv  = my_1
      end if

      u  = qnode(2,i)
      v  = qnode(3,i)
      w  = qnode(4,i)

      ux = gradx(2,i)
      uy = grady(2,i)
      uz = gradz(2,i)
      vx = gradx(3,i)
      vy = grady(3,i)
      vz = gradz(3,i)
      wx = gradx(4,i)
      wy = grady(4,i)
      wz = gradz(4,i)

      rmut     = amut(i)     + 1.0e-6_dp
      xis = ux**2 + vy**2 + wz**2 + my_half*((uy + vx)**2 + (uz + wx)**2 +     &
            (vz + wy)**2)
      vort = sqrt((wy-vz)**2+(uz-wx)**2+(vx-uy)**2)
      if (strain_production) then
        tij = 2.0_dp*rmut*xis
      else
        tij = rmut*vort*vort
      end if
      if(abs(tij) <= my_tiny) tij = 1.0e-6_dp

!       Curvature Correction
      f4 = 1.0_dp
      if (sstrc) then
        sij = sqrt(2.0_dp*xis) + 1.0e-20_dp
        ri = (vort/sij)*(vort/sij - 1.0_dp)
        f4 = 1.0_dp/(1.0_dp + sstrc_crc*ri)
      end if

!     Terms needed for the k_sgs equation

      SijSij    =  my_2*ux**2  +       uy**2  +       uz**2                    &
                 +      vx**2  +  my_2*vy**2  +       vz**2                    &
                 +      wx**2  +       wy**2  +  my_2*wz**2                    &
                 + my_2*uy*vx  +  my_2*uz*wx  +  my_2*vz*wy

      SiiSii    = (ux + vy + wz)**2
      ke_term   = tke*(ux + vy + wz)
      turb_mach = 0.0_dp

      select case ( turb_compress_model )
      case ( 'off')
        zeman_lag   = 0.0_dp
        heaviside   = 0.0_dp
        f_turb_mach = 0.0_dp
        f_betastar  = betastar
        f_beta      = beta_blend
      case ( 'wilcox')

        turb_mach_0 = 0.25_dp
        xsi_star    = 2.00_dp
! Wilcox compressibility correction
! (Wilcox "Turbulence Modeling for CFD, Ed 2, p. 244)
        turb_mach   = sqrt(2.0_dp *rho * tke /(gamma*p))
        zeman_lag   = turb_mach - turb_mach_0
        heaviside   = max(zeman_lag+epsilon0,0.0_dp)/(abs(zeman_lag)+epsilon0)
        f_turb_mach = (turb_mach**2 - turb_mach_0**2) * heaviside
        f_beta      = beta_blend - betastar*xsi_star*f_turb_mach
        f_betastar  = betastar * ( 1.0_dp + xsi_star*f_turb_mach )

      end select

      delta   = vol(i)**third
      mut_sgs = rho*c_nu*sqrt(tke)*delta/xmr
      f_lns   = zero
      if ( lns_hybrid ) f_lns   = int(min((mut_sgs/rmut),my_1))
      f       = max( blend(i), f_lns )

!     Production

      Pk = abs( xmr * rhoinv * tij )  ! default production term
      if ( .not. pre_4_17_2009_hrles ) then
        Pk_rans = abs(  xmr * rhoinv * tij)
        Pk_sgs  = abs( (xmr * rhoinv * rmut)*(SijSij - twothird*SiiSii)        &
                  - twothird*ke_term )
        Pk      = f * Pk_rans + (my_1 - f) * Pk_sgs
      endif

      Pw = xmr * alpa_blend * tij / rmut

!     Destruction

      Dk_rans = f_betastar * tke * omega * xmrinv
      Dk_sgs  = c_ep * tke**1.5_dp / delta
      Dk      = f * Dk_rans + (my_1 - f) * Dk_sgs

      Dw      = f4*f_beta*omega*omega*xmrinv - (my_1 - f1)*crossd(i)

!     Limit on k-production term

      Pk = min(Pk,20.0_dp*Dk)

!     test for laminar node
      if (turb_transition) then
        if (iflagslen(i) < 0) then
          Pk=my_0
          Pw=my_0
        end if
      end if

      sourcek = vol(i)*(Pk - Dk)
      sourcew = vol(i)*(Pw - Dw)

      if (test_freestream) then
        sourcek = 0._dp
        sourcew = 0._dp
      end if

      res(1,i) = res(1,i) - sourcek
      res(2,i) = res(2,i) - sourcew

    end do source1

! Diffusion terms

      do ielem = 1, nelem
        call hrles_resid_diff(eqn_set, viscous_method, nnodes0, nnodes01,      &
                              nedgeloc, eptr, turb, qnode, res, gradx, grady,  &
                              gradz, xn, yn, zn, ra, ielem, elem(ielem)%ncell, &
                              elem(ielem)%c2n, elem(ielem)%c2e, x, y, z,       &
                              elem(ielem)%local_f2n, elem(ielem)%local_e2n,    &
                              elem(ielem)%local_f2e, elem(ielem)%e2n_2d,       &
                              nedgeloc_2d, elem(ielem)%face_per_cell,          &
                              elem(ielem)%node_per_cell,                       &
                              elem(ielem)%edge_per_cell, elem(ielem)%type_cell,&
                              n_turb, n_tot, n_grd, elem(ielem)%face_2d, amut)
      end do

  end subroutine hrles_resid


!================================= HRLES_RESID_DIFF ==========================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for HRLES model on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine hrles_resid_diff(eqn_set, viscous_method, nnodes0, nnodes01,      &
                              nedgeloc, eptr, turb, qnode, res, gradx, grady,  &
                              gradz, xn, yn, zn, ra, ielem, ncell,             &
                              c2n, c2e, x, y, z, local_f2n, local_e2n,         &
                              local_f2e, e2n_2d, nedgeloc_2d, face_per_cell,   &
                              node_per_cell, edge_per_cell, type_cell, n_turb, &
                              n_tot, n_grd, face_2d, amut)

    use info_depr,         only : tref, xmach, re, twod, skeleton,             &
                                  grad_x_y_z_contents, use_edge_gradients
    use debug_defs,        only : gradient_construction_rhs
    use solution_types,    only : compressible, incompressible
    use turb_kw_const,     only : sig_w1, sig_w2, sig_k1, sig_k2
    use turb_hrles_const,  only : prt, amut_rans
    use fluid,             only : gamma, sutherland_constant
    use lmpi,              only : lmpi_die, lmpi_master
    use utilities,         only : tangents

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_grd
    integer, intent(in) :: ncell
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: face_2d

    integer,  dimension(2,nedgeloc),          intent(in) :: eptr
    integer,  dimension(node_per_cell,ncell), intent(in) :: c2n
    integer,  dimension(edge_per_cell,ncell), intent(in) :: c2e
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer,  dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer,  dimension(4,2),                 intent(in) :: e2n_2d

    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),        intent(in)    :: ra
    real(dp), dimension(nnodes01),        intent(in)    :: amut
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    character(len=3), intent(in) :: type_cell

    integer :: n, nedge_flux_eval
    integer :: node1, node2
    integer :: nn

    real(dp) :: coeff1, coeff2
    real(dp) :: xmr, phi1, phi2
    real(dp) :: mu_node1, mu_node2, mut_node1, mut_node2
    real(dp) :: mut_sst_node1, mut_sst_node2
    real(dp) :: cstar
    real(dp) :: phi
    real(dp) :: rnu1, rnu2
    real(dp) :: rho1, p1, temp1, rho2, p2, temp2
    real(dp) :: lx, ly, lz, mx, my, mz, deti
    real(dp) :: my_xmach
    real(dp) :: txavg, tyavg, tzavg, gradt_xi
    real(dp) :: ex, ey, ez, disi
    real(dp) :: tx, ty, tz, egradt
    real(dp) :: lgradt, mgradt, rho1inv, rho2inv

    real(dp), dimension(3,3)    :: b
    real(dp), dimension(n_turb) :: ngradt
    real(dp), dimension(n_turb) :: coe1, coe2

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp

  continue

    tx = 0._dp ; ty = 0._dp ; tz = 0._dp

!   When using edge-based diffusion terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'hrles_resid_mix: only for in/comprss pg')
    end select

    xmr  = my_xmach / re

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    endif

    diffusion_terms : if ( viscous_method > 0 ) then

!     Check method for computing gradx,... (diffusion + source terms)

      if(skeleton > 0) then
        write(*,*) ' Edge-based residuals of decoupled turbulent diffusion.'
        write(*,*) ' Using weighted least squares for average gradient.'
        write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
      endif
      if(trim(grad_x_y_z_contents) /= 'viscous weighted-least-squares') then
        if(lmpi_master) then
          write(*,*) ' Failure in edge-based turb residuals...stopping.'
          write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
        endif
        call lmpi_die
      endif

      mixed_edge_loop: do n = 1, nedge_flux_eval

!       loop over all the faces and calculate the turbulent diffusion terms
!       (in the 2D case, this loop contains edges on only one y=constant plane)
!       turb(nn,node) contains the turbulence values
!       gradx(n_grd) contains gradx of turb, ...
!
!       Break into two contributions - directional and the average gradient -
!       and enforce M-property on only the directional piece

        node1 = eptr(1,n)
        node2 = eptr(2,n)

!       rnu = laminar viscosity / density

        if ( eqn_set == compressible ) then
          rho1   = qnode(1,node1)
          rho1inv = my_1/rho1
          p1     = qnode(5,node1)
          temp1  = gamma*p1*rho1inv
          rnu1   = viscosity_law( cstar, temp1 ) * rho1inv

          rho2   = qnode(1,node2)
          rho2inv = my_1/rho2
          p2     = qnode(5,node2)
          temp2  = gamma*p2*rho2inv
          rnu2   = viscosity_law( cstar, temp2 ) * rho2inv
        else
          rho1   = my_1
          rho2   = my_1
          rnu1   = my_1
          rnu2   = my_1
        end if

        coe1(1) = sig_k1
        coe2(1) = sig_k2
        coe1(2) = sig_w1
        coe2(2) = sig_w2

        turbvariable_loop1 : do nn = 1, n_turb

          coeff1 = blend(node1)*coe1(nn) + (my_1 - blend(node1))*coe2(nn)
          coeff2 = blend(node2)*coe1(nn) + (my_1 - blend(node2))*coe2(nn)

          if (nn == 1) then  ! k equation is blended
            coeff1 = blend(node1)*coeff1 + (my_1 - blend(node1))*prt
            coeff2 = blend(node2)*coeff2 + (my_1 - blend(node2))*prt
          end if

          mu_node1  = rnu1*rho1
          mu_node2  = rnu2*rho2
          mut_node1 = amut(node1)
          mut_node2 = amut(node2)
          mut_sst_node1 = amut_rans(node1)
          mut_sst_node2 = amut_rans(node2)

          if (nn == 2) then  ! w eq. is unblended, so use turb. visc. from SST
            phi1 = mu_node1 + mut_sst_node1/coeff1
            phi2 = mu_node2 + mut_sst_node2/coeff2
          else
            phi1 = mu_node1 + mut_node1/coeff1
            phi2 = mu_node2 + mut_node2/coeff2
          end if

          phi = my_half*(phi1 + phi2)

!         average node-based gradients : gradx, grady, gradz

          txavg = my_half*( gradx(n_grd-n_turb+nn,node1) +                     &
                            gradx(n_grd-n_turb+nn,node2) )
          tyavg = my_half*( grady(n_grd-n_turb+nn,node1) +                     &
                            grady(n_grd-n_turb+nn,node2) )
          tzavg = my_half*( gradz(n_grd-n_turb+nn,node1) +                     &
                            gradz(n_grd-n_turb+nn,node2) )

!         ex, ey, ez is unit vector along edge direction

          ex   = x(node2) - x(node1)
          ey   = y(node2) - y(node1)
          ez   = z(node2) - z(node1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          if( viscous_method == 2 ) then

!         directional gradients along edge

          egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

          tx = egradt*ex
          ty = egradt*ey
          tz = egradt*ez

          elseif( gradient_construction_rhs == 0 ) then

!         directional gradients along edge

          egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

!         average gradient in edge direction

          gradt_xi = txavg*ex + tyavg*ey + tzavg*ez

!         resolve gradient contributions from edge and nodes
!         u, v, w, and speed-of-sound-squared (i.e., perfect gas temperature)

          tx = txavg + ( egradt - gradt_xi )*ex
          ty = tyavg + ( egradt - gradt_xi )*ey
          tz = tzavg + ( egradt - gradt_xi )*ez

          elseif( gradient_construction_rhs == 1 ) then

          !...find tangent vectors in the dual face
          call tangents(xn(n), yn(n), zn(n), lx, ly, lz, mx, my, mz)

          !...find inverse elements of transformation matrix
          deti =  my_1/ ( ex*( ly*mz - lz*my ) &
                        + ey*( lz*mx - lx*mz ) &
                        + ez*( lx*my - ly*mx ) )

          b(1,1) =  deti*( ly*mz - lz*my )
          b(1,2) = -deti*( ey*mz - ez*my )
          b(1,3) =  deti*( ey*lz - ez*ly )

          b(2,1) = -deti*( lx*mz - lz*mx )
          b(2,2) =  deti*( ex*mz - ez*mx )
          b(2,3) = -deti*( ex*lz - ez*lx )

          b(3,1) =  deti*( lx*my - ly*mx )
          b(3,2) = -deti*( ex*my - ey*mx )
          b(3,3) =  deti*( ex*ly - ey*lx )

!         directional gradients

          egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

          lgradt = txavg*lx + tyavg*ly + tzavg*lz

          mgradt = txavg*mx + tyavg*my + tzavg*mz

!         resolve gradient contributions from edge and dual face

          tx = b(1,1)*egradt + b(1,2)*lgradt + b(1,3)*mgradt
          ty = b(2,1)*egradt + b(2,2)*lgradt + b(2,3)*mgradt
          tz = b(3,1)*egradt + b(3,2)*lgradt + b(3,3)*mgradt

          endif

!         turbulent diffusion contribution at dual face [ two terms ]

!         [area]*[nondimensionalization factor : Mach / Re / sigma ]*
!         [normal gradient] at dual face

          ngradt(nn) = xmr*ra(n)*( tx*xn(n) + ty*yn(n) + tz*zn(n) )

          if ( node1 <= nnodes0 ) then
            res(nn,node1) = res(nn,node1) - ( phi*ngradt(nn) )
          end if

          if ( node2 <= nnodes0 ) then
            res(nn,node2) = res(nn,node2) + ( phi*ngradt(nn) )
          end if

        end do turbvariable_loop1
      end do mixed_edge_loop

    else diffusion_terms

      if ( twod .or. (.not.use_edge_gradients) ) then
        call cell_based_diff_res(eqn_set, viscous_method, nnodes0, nnodes01,   &
                                 nedgeloc, turb, qnode, res, ielem,            &
                                 ncell, c2n, c2e, x, y, z, local_f2n,          &
                                 local_e2n, local_f2e, e2n_2d, nedgeloc_2d,    &
                                 face_per_cell, node_per_cell, edge_per_cell,  &
                                 type_cell, n_turb, n_tot, face_2d, amut)
      else
        call cell_based_diff_res_opt(eqn_set, nnodes0, nnodes01, turb, qnode,  &
                                     res, ncell, c2n, x, y, z,                 &
                                     local_e2n, local_f2n, face_per_cell,      &
                                     node_per_cell, edge_per_cell, type_cell,  &
                                     n_turb, n_tot, amut)
      endif

    end if diffusion_terms

  end subroutine hrles_resid_diff


!================================= CELL_BASED_DIFF_RES =======================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for HRLES model on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine cell_based_diff_res(eqn_set, viscous_method, nnodes0, nnodes01,   &
                                 nedgeloc, turb, qnode, res, ielem,            &
                                 ncell, c2n, c2e, x, y, z, local_f2n,          &
                                 local_e2n, local_f2e, e2n_2d, nedgeloc_2d,    &
                                 face_per_cell, node_per_cell, edge_per_cell,  &
                                 type_cell, n_turb, n_tot, face_2d, amut)

    use info_depr,        only : tref, xmach, re, twod, skeleton,              &
                                 grad_x_y_z_contents, use_edge_gradients
    use debug_defs,       only : gradient_construction_rhs
    use solution_types,   only : compressible, incompressible
    use turb_kw_const,    only : sig_w1, sig_w2, sig_k1, sig_k2
    use turb_hrles_const, only : prt, amut_rans
    use fluid,            only : gamma, sutherland_constant
    use element_defs,     only : max_node_per_cell, max_face_per_cell,         &
                                 max_edge_per_cell
    use lmpi,             only : lmpi_die, lmpi_master
    use utilities,        only : tangents, tinverse, cell_gradients

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: ncell
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: face_2d

    integer,  dimension(node_per_cell,ncell), intent(in) :: c2n
    integer,  dimension(edge_per_cell,ncell), intent(in) :: c2e
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer,  dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer,  dimension(4,2),                 intent(in) :: e2n_2d

    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nnodes01),        intent(in)    :: amut
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    character(len=3), intent(in) :: type_cell

    integer :: n, nedge_flux_eval
    integer :: ie, i, ie_local, i_local
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, edge, node
    integer :: n1, n2, n3, n4, n5, n6, nn

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: phi_k, phi_w, coeff_k, coeff_w
    real(dp) :: xmr, mu_node, mut_node
    real(dp) :: mut_sst_node
    real(dp) :: cstar
    real(dp) :: rho, rhoinv
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: xc, yc, zc, cell_vol, fact1, fact2
    real(dp) :: areai, xnf, ynf, znf, lx, ly, lz, mx, my, mz
    real(dp) :: my_xmach
    real(dp) :: gradt_xi
    real(dp) :: ex, ey, ez, disi
    real(dp) :: egradt
    real(dp) :: lgradt, mgradt

    real(dp), dimension(3,3)                      :: b, t
    real(dp), dimension(max_face_per_cell)        :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)        :: nu_node, t_node
    real(dp), dimension(max_node_per_cell)        :: x_node, y_node, z_node
    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(n_turb)                   :: gradx_cell, grady_cell
    real(dp), dimension(n_turb)                   :: gradz_cell
    real(dp), dimension(n_turb)                   :: trbrex, trbrey, trbrez
    real(dp), dimension(n_turb)                   :: trbrexavg, trbreyavg
    real(dp), dimension(n_turb)                   :: trbrezavg, ngradt

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp

    logical :: edge_gradients

  continue

!   When using edge-based diffusion terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp
    nodes_local = 0

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'hrles_resid_mix: only for in/compress pg')
    end select

    xmr  = my_xmach / re

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    edge_map = 0
    node_map = 0

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    endif

    if ( twod .and. (viscous_method == 0) ) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    elseif( viscous_method == 0 ) then

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

!   Check method for computing gradx,... (diffusion + source terms)

    if(skeleton > 0) then
      write(*,*) ' Cell-based residuals of decoupled turbulent diffusion.'
      write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
    endif
    if(skeleton > 0) then
      write(*,*) ' Using Green-Gauss (turbgrad) for source term gradients.'
    endif
    if(trim(grad_x_y_z_contents) /= 'turbgrad') then
      if(lmpi_master) then
        write(*,*) ' Failure in cell-based HRLES residuals...stopping.'
        write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
        write(*,*) ' ........should be via ','turbgrad'
      endif
      call lmpi_die
    endif

    fact1 = 1._dp / real(nodes_local, dp)
    fact2 = 1._dp / real(node_per_cell, dp)

    diffusion_term_cell : do n = 1, ncell

      phi_k    = 0._dp
      phi_w    = 0._dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

!     compute cell averages and set up some local solution arrays

      node_loop : do i_local = 1, nodes_local

        i = node_map(i_local)

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        do nn=1,n_turb
          trbre_node(nn,i) = turb(nn,node)
        end do

        if ( eqn_set == compressible ) then
          rho        = qnode(1,node)
          rhoinv     = my_1/rho
          t_node(i)  = gamma*qnode(5,node)*rhoinv
          nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv
        else
          rho        = my_1
          nu_node(i) = my_1
        end if

        coeff_k = blend(node)*sig_k1 + (my_1 - blend(node))*sig_k2
        coeff_w = blend(node)*sig_w1 + (my_1 - blend(node))*sig_w2

        ! Further blending for k equation
        coeff_k = blend(node)*coeff_k + (my_1 - blend(node))*prt

        mu_node      = rho*nu_node(i)
        mut_node     = amut(node)
        mut_sst_node = amut_rans(node)

        phi_k = phi_k + (mu_node + mut_node/coeff_k)
        phi_w = phi_w + (mu_node + mut_sst_node/coeff_w) ! w eq. is unblended

      end do node_loop

!     get cell averages by dividing by the number of nodes that contributed

      phi_k    = phi_k*fact1
      phi_w    = phi_w*fact1

!     compute the cell center (must loop over node_per_cell even in 2D)

      do i = 1, node_per_cell

        node = c2n(i,n)

        xc  =  xc + x(node)
        yc  =  yc + y(node)
        zc  =  zc + z(node)

      end do

      xc  =  xc*fact2
      yc  =  yc*fact2
      zc  =  zc*fact2

!     get the gradients in the primal cell via Green-Gauss

      call cell_gradients(edges_local, max_node_per_cell,face_per_cell,      &
                          x_node, y_node, z_node, n_turb, trbre_node,        &
                          local_f2n, e2n_2d, gradx_cell, grady_cell,         &
                          gradz_cell, cell_vol, nx, ny, nz)

      do nn = 1, n_turb
        trbrexavg(nn) = gradx_cell(nn)
        trbreyavg(nn) = grady_cell(nn)
        trbrezavg(nn) = gradz_cell(nn)
      end do

!     next loop over the edges in the cell and get each ones
!     contribution to the residual

      edge_loop : do ie_local = 1,edges_local

!       local edge number

        ie = edge_map(ie_local)

!       global edge number

        edge = c2e(ie,n)

!       check edge to make sure it is not off-processor - if it is
!       we don't need its contribution

        if (edge > nedge_flux_eval) cycle edge_loop

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)

!       global node numbers of edge endpoints

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

!       edge midpoint

        xm = (x(n1) + x(n2))*my_half
        ym = (y(n1) + y(n2))*my_half
        zm = (z(n1) + z(n2))*my_half

!       compute left face centroid

        n3 = c2n(local_e2n(ie,3),n)

        if (local_e2n(ie,4) /= 0) then

!         quad cell face

          n4 = c2n(local_e2n(ie,4),n)

          xl = (x(n1) + x(n2) + x(n3) + x(n4))*my_4th
          yl = (y(n1) + y(n2) + y(n3) + y(n4))*my_4th
          zl = (z(n1) + z(n2) + z(n3) + z(n4))*my_4th

        else

!         tria cell face

          xl = (x(n1) + x(n2) + x(n3))*my_3rd
          yl = (y(n1) + y(n2) + y(n3))*my_3rd
          zl = (z(n1) + z(n2) + z(n3))*my_3rd

        end if

!       compute right face centroid

        n5 = c2n(local_e2n(ie,5),n)

        if (local_e2n(ie,6) /= 0) then

!         quad cell face

          n6 = c2n(local_e2n(ie,6),n)

          xr = (x(n1) + x(n2) + x(n5) + x(n6))*my_4th
          yr = (y(n1) + y(n2) + y(n5) + y(n6))*my_4th
          zr = (z(n1) + z(n2) + z(n5) + z(n6))*my_4th

        else

!         tria cell face

          xr = (x(n1) + x(n2) + x(n5))*my_3rd
          yr = (y(n1) + y(n2) + y(n5))*my_3rd
          zr = (z(n1) + z(n2) + z(n5))*my_3rd

        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

!       get gradients at the dual face; either take gradients for this
!       piece of the dual face to be the same as the cell-average gradient
!       computed above  (which is what the legacy FUN3D solver does for tets),
!       or combine with the edge-gradient to increase h-ellipticity on
!       non-simplicial meshes.

!       for tets in 3D or prisms in 2D, edge gradients add no new info
!       so there is no need to do the extra work

        edge_gradients = use_edge_gradients

        if (type_cell == 'tet') edge_gradients = .false.
        if (twod .and. type_cell == 'prz') edge_gradients = .false.

        include_edge_gradients : if (edge_gradients) then

        do nn = 1, n_turb
!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          if ( gradient_construction_rhs == 0 ) then

!           directional gradients along edge

            egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

!           average Green-Gauss gradient in edge direction

            gradt_xi = trbrexavg(nn)*ex + trbreyavg(nn)*ey + trbrezavg(nn)*ez

!           combine gradient contributions from edge and primal cell

            trbrex(nn) = trbrexavg(nn) + ( egradt - gradt_xi )*ex
            trbrey(nn) = trbreyavg(nn) + ( egradt - gradt_xi )*ey
            trbrez(nn) = trbrezavg(nn) + ( egradt - gradt_xi )*ez

          elseif(  gradient_construction_rhs == 1 ) then

!           find tangent vectors in the dual face

            areai = my_1/sqrt( areax**2 + areay**2 + areaz**2 )
            xnf = areax*areai
            ynf = areay*areai
            znf = areaz*areai
            call tangents(xnf, ynf, znf, lx, ly, lz, mx, my, mz)

!           form transformation matrix

            !...edge direction
            b(1,1) = ex
            b(1,2) = ey
            b(1,3) = ez

            !...l direction
            b(2,1) = lx
            b(2,2) = ly
            b(2,3) = lz

            !...m direction
            b(3,1) = mx
            b(3,2) = my
            b(3,3) = mz

            call tinverse( b , t )

!           directional gradients

            egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

            lgradt = trbrexavg(nn)*b(2,1) + trbreyavg(nn)*b(2,2)             &
                   + trbrezavg(nn)*b(2,3)

            mgradt = trbrexavg(nn)*b(3,1) + trbreyavg(nn)*b(3,2)             &
                   + trbrezavg(nn)*b(3,3)

!           resolve gradient contributions from edge and dual face

            trbrex(nn) = t(1,1)*egradt + t(1,2)*lgradt + t(1,3)*mgradt
            trbrey(nn) = t(2,1)*egradt + t(2,2)*lgradt + t(2,3)*mgradt
            trbrez(nn) = t(3,1)*egradt + t(3,2)*lgradt + t(3,3)*mgradt

          end if
        end do

        else include_edge_gradients

!         just use Green-Gauss cell-average gradients (this
!         is what the baseline code does for tets)

          do nn = 1, n_turb
            trbrex(nn) = trbrexavg(nn)
            trbrey(nn) = trbreyavg(nn)
            trbrez(nn) = trbrezavg(nn)
          end do

        end if include_edge_gradients

!       turbulent diffusion contribution at dual face

!       [nondimensionalization factor : Mach / Re ]*
!       [normal gradient * area] at dual face

        ngradt(1) = xmr*(trbrex(1)*areax + trbrey(1)*areay + trbrez(1)*areaz)
        ngradt(2) = xmr*(trbrex(2)*areax + trbrey(2)*areay + trbrez(2)*areaz)

        if ( n1 <= nnodes0 ) then
          res(1,n1) = res(1,n1) - ( phi_k*ngradt(1)/qnode(1,n1) )
          res(2,n1) = res(2,n1) - ( phi_w*ngradt(2)/qnode(1,n1) )
        end if

        if ( n2 <= nnodes0 ) then
          res(1,n2) = res(1,n2) + ( phi_k*ngradt(1)/qnode(1,n2) )
          res(2,n2) = res(2,n2) + ( phi_w*ngradt(2)/qnode(1,n2) )
        end if

      end do edge_loop

    end do diffusion_term_cell

  end subroutine cell_based_diff_res


!================================= CELL_BASED_DIFF_RES_OPT ===================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for HRLES model on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Cell-based mixed element diffusion for HRLES model optimized for speed but
! does not have all of the bells and whistles that the cell_based_diff_res
! routine has
!
!=============================================================================80
  subroutine cell_based_diff_res_opt(eqn_set, nnodes0, nnodes01, turb, qnode,  &
                                     res, ncell, c2n, x, y, z,                 &
                                     local_e2n, local_f2n, face_per_cell,      &
                                     node_per_cell, edge_per_cell, type_cell,  &
                                     n_turb, n_tot, amut)

    use info_depr,         only : tref, xmach, re, skeleton, grad_x_y_z_contents
    use solution_types,    only : compressible, incompressible
    use turb_kw_const,     only : sig_w1, sig_w2, sig_k1, sig_k2
    use turb_hrles_const,  only : prt, amut_rans
    use fluid,             only : gamma, sutherland_constant
    use element_defs,      only : max_node_per_cell
    use lmpi,              only : lmpi_die, lmpi_master

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01

    integer,  dimension(node_per_cell,ncell), intent(in) :: c2n
    integer,  dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2n

    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nnodes01),        intent(in)    :: amut
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    character(len=3), intent(in) :: type_cell

    integer :: n, iface, nn1, nn2, nn3, nn4
    integer :: ie, i
    integer :: n1_loc, n2_loc, n3_loc, n4_loc, n5_loc, n6_loc, node
    integer :: n1, n2, nn

    real(dp) :: phi_k, phi_w, coeff_k, coeff_w
    real(dp) :: xmr, mu_node, mut_node, xavg, yavg, zavg
    real(dp) :: mut_sst_node, termx, termy, termz, xavg1, yavg1, zavg1
    real(dp) :: cstar, nx1, ny1, nz1, xavg2, yavg2, zavg2
    real(dp) :: rho, term1, nx2, ny2, nz2, term2, termx1, termy1, termz1
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz, termx2, termy2, termz2
    real(dp) :: xc, yc, zc, cell_vol, fact, qavg1, qavg2
    real(dp) :: my_xmach, cell_vol_inv
    real(dp) :: gradt_xi
    real(dp) :: ex, ey, ez, disi
    real(dp) :: egradt, nu
    real(dp) :: nx, ny, nz, qavg

    real(dp), dimension(max_node_per_cell)        :: t_node
    real(dp), dimension(max_node_per_cell)        :: rhoinv_node
    real(dp), dimension(max_node_per_cell)        :: x_node, y_node, z_node
    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(n_turb)                   :: trbrex, trbrey, trbrez
    real(dp), dimension(n_turb)                   :: trbrexavg, trbreyavg
    real(dp), dimension(n_turb)                   :: trbrezavg, ngradt

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp
    real(dp), parameter :: my_6th  = 1.0_dp/6.0_dp
    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'hrles_resid_mix: only for in/comprss pg')
    end select

    xmr  = my_xmach / re

!   Check method for computing gradx,... (diffusion + source terms)

    if(skeleton > 0) then
      write(*,*) ' Cell-based residuals of decoupled turbulent diffusion.'
      write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
    endif
    if(skeleton > 0) then
      write(*,*) ' Using Green-Gauss (turbgrad) for source term gradients.'
    endif
    if(trim(grad_x_y_z_contents) /= 'turbgrad') then
      if(lmpi_master) then
        write(*,*) ' Failure in cell-based HRLES residuals...stopping.'
        write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
        write(*,*) ' ........should be via ','turbgrad'
      endif
      call lmpi_die
    endif

    fact = 1._dp / real(node_per_cell, dp)

    diffusion_term_cell : do n = 1, ncell

      phi_k    = 0._dp
      phi_w    = 0._dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

!     compute cell averages and set up some local solution arrays

      node_loop : do i = 1, node_per_cell

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        xc  =  xc + x_node(i)
        yc  =  yc + y_node(i)
        zc  =  zc + z_node(i)

        do nn=1,n_turb
          trbre_node(nn,i) = turb(nn,node)
        end do

        if ( eqn_set == compressible ) then
          rho           = qnode(1,node)
          rhoinv_node(i)= my_1/rho
          t_node(i)     = gamma*qnode(5,node)*rhoinv_node(i)
          nu = viscosity_law( cstar, t_node(i) ) * rhoinv_node(i)
        else
          rho = my_1
          rhoinv_node(i) = my_1
          nu  = my_1
        end if

        coeff_k  = blend(node)*sig_k1 + (my_1 - blend(node))*sig_k2
        coeff_w  = blend(node)*sig_w1 + (my_1 - blend(node))*sig_w2

        ! Further blending for k equation
        coeff_k  = blend(node)*coeff_k + (my_1 - blend(node))*prt

        mu_node      = rho*nu
        mut_node     = amut(node)
        mut_sst_node = amut_rans(node)

        phi_k = phi_k + (mu_node + mut_node/coeff_k)
        phi_w = phi_w + (mu_node + mut_sst_node/coeff_w) ! w eq. is unblended

      end do node_loop

!     get cell averages by dividing by the number of nodes that contributed

      phi_k = phi_k*fact
      phi_w = phi_w*fact

      xc  =  xc*fact
      yc  =  yc*fact
      zc  =  zc*fact

!     get the gradients in the primal cell via Green-Gauss

      trbrexavg = 0.0_dp
      trbreyavg = 0.0_dp
      trbrezavg = 0.0_dp

      cell_vol = my_0

      threed_faces : do iface = 1, face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!       triangular faces of the cell

!       face normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))         &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))         &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))         &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

!       gradient contributions

          do nn = 1, n_turb
            qavg = trbre_node(nn,nn1) + trbre_node(nn,nn2) + trbre_node(nn,nn3)

            trbrexavg(nn) = trbrexavg(nn) + termx*qavg
            trbreyavg(nn) = trbreyavg(nn) + termy*qavg
            trbrezavg(nn) = trbrezavg(nn) + termz*qavg
          end do

        else

!       quadrilateral faces of the cell

!       break face up into triangles 1-2-3 and 1-3-4 and add together

!       triangle 1: 1-2-3

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       triangle 1 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))        &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))        &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))        &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!       triangle 2: 1-3-4

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)

!       triangle 2 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))        &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))
          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))        &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))
          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))        &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!       cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th

!       gradient contributions

          termx1 = nx1*my_6th
          termy1 = ny1*my_6th
          termz1 = nz1*my_6th

          termx2 = nx2*my_6th
          termy2 = ny2*my_6th
          termz2 = nz2*my_6th

          do nn = 1, n_turb
            qavg1 = trbre_node(nn,nn1) + trbre_node(nn,nn2) + trbre_node(nn,nn3)
            qavg2 = trbre_node(nn,nn1) + trbre_node(nn,nn3) + trbre_node(nn,nn4)

            trbrexavg(nn) = trbrexavg(nn) + termx1*qavg1 + termx2*qavg2
            trbreyavg(nn) = trbreyavg(nn) + termy1*qavg1 + termy2*qavg2
            trbrezavg(nn) = trbrezavg(nn) + termz1*qavg1 + termz2*qavg2
          end do

        end if

      end do threed_faces

!   need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

      cell_vol_inv = my_1/cell_vol

      trbrexavg(:) = trbrexavg(:) * cell_vol_inv
      trbreyavg(:) = trbreyavg(:) * cell_vol_inv
      trbrezavg(:) = trbrezavg(:) * cell_vol_inv

!     next loop over the edges in the cell and get each ones
!     contribution to the residual

      edge_loop : do ie = 1, edge_per_cell

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)
        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

!       global node numbers of edge endpoints

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

!       edge midpoint

        xm = (x_node(n1_loc) + x_node(n2_loc))*my_half
        ym = (y_node(n1_loc) + y_node(n2_loc))*my_half
        zm = (z_node(n1_loc) + z_node(n2_loc))*my_half

!       compute left face centroid

        if (n4_loc /= 0) then
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc)               &
              + x_node(n4_loc))*my_4th
          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc)               &
              + y_node(n4_loc))*my_4th
          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc)               &
              + z_node(n4_loc))*my_4th
        else
          xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc))*my_3rd
          yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc))*my_3rd
          zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc))*my_3rd
        end if

!       compute right face centroid

        if (n6_loc /= 0) then
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc)               &
              + x_node(n6_loc))*my_4th
          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc)               &
              + y_node(n6_loc))*my_4th
          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc)               &
              + z_node(n6_loc))*my_4th
        else
          xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc))*my_3rd
          yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc))*my_3rd
          zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc))*my_3rd
        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

!       get gradients at the dual face; either take gradients for this
!       piece of the dual face to be the same as the cell-average gradient
!       computed above  (which is what the legacy FUN3D solver does for tets),
!       or combine with the edge-gradient to increase h-ellipticity on
!       non-simplicial meshes.

!       for tets in 3D or prisms in 2D, edge gradients add no new info
!       so there is no need to do the extra work

        if (type_cell /= 'tet') then

!         ex, ey, ez is unit vector along edge direction

          ex   = x_node(n2_loc) - x_node(n1_loc)
          ey   = y_node(n2_loc) - y_node(n1_loc)
          ez   = z_node(n2_loc) - z_node(n1_loc)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )
          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          do nn = 1, n_turb
!           directional gradients along edge

            egradt = ( trbre_node(nn,n2_loc) - trbre_node(nn,n1_loc) )*disi

!           average Green-Gauss gradient in edge direction

            gradt_xi = trbrexavg(nn)*ex + trbreyavg(nn)*ey + trbrezavg(nn)*ez

!           combine gradient contributions from edge and primal cell

            trbrex(nn) = trbrexavg(nn) + ( egradt - gradt_xi )*ex
            trbrey(nn) = trbreyavg(nn) + ( egradt - gradt_xi )*ey
            trbrez(nn) = trbrezavg(nn) + ( egradt - gradt_xi )*ez
          end do

        else

          trbrex(:) = trbrexavg(:)
          trbrey(:) = trbreyavg(:)
          trbrez(:) = trbrezavg(:)

        end if

!       turbulent diffusion contribution at dual face

!       [nondimensionalization factor : Mach / Re ]*
!       [normal gradient * area] at dual face

        ngradt(1) = xmr*(trbrex(1)*areax + trbrey(1)*areay + trbrez(1)*areaz)
        ngradt(2) = xmr*(trbrex(2)*areax + trbrey(2)*areay + trbrez(2)*areaz)

        if ( n1 <= nnodes0 ) then
          res(1,n1) = res(1,n1) - ( phi_k*ngradt(1)*rhoinv_node(n1_loc) )
          res(2,n1) = res(2,n1) - ( phi_w*ngradt(2)*rhoinv_node(n1_loc) )
        end if

        if ( n2 <= nnodes0 ) then
          res(1,n2) = res(1,n2) + ( phi_k*ngradt(1)*rhoinv_node(n2_loc) )
          res(2,n2) = res(2,n2) + ( phi_w*ngradt(2)*rhoinv_node(n2_loc) )
        end if

      end do edge_loop

    end do diffusion_term_cell

  end subroutine cell_based_diff_res_opt


!============================= HRLES_JACOB ===================================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for HRLES model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine hrles_jacob(eqn_set, viscous_method, nnodes0, nnodes01,           &
                         nedgeloc, max_nnz, eptr, turb, qnode, gradx,          &
                         grady, gradz, vol, xn, yn, zn, ra, a_diag, a_off,     &
                         fhelp, nedgeloc_2d, nnodes0_2d, node_pairs_2d, x, y,  &
                         z, nnz01, ia, ja, facespeed, n_turb, n_tot, n_grd,    &
                         nzg2m, g2m, amut, nelem, elem )

    use kinddefs,            only : odp
    use info_depr,           only : xmach, re, twod
    use grid_motion_helpers, only : need_grid_velocity
    use debug_defs,          only : test_freestream
    use solution_types,      only : compressible, incompressible
    use turb_kw_const,       only : beta1, beta2, betastar, sstrc, sstrc_crc,  &
                                    strain_production, pre_4_17_2009_hrles
    use turb_hrles_const,    only : c_ep
    use lmpi,                only : lmpi_master
    use element_types,       only : elem_type

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: n_tot, n_grd
    integer, intent(in) :: n_turb, nelem
    integer, intent(in) :: max_nnz
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: nnodes0_2d
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: nnz01

    integer, dimension(2,nedgeloc),   intent(in) :: eptr
    integer, dimension(2,nedgeloc),   intent(in) :: fhelp
    integer, dimension(2,nnodes0_2d), intent(in) :: node_pairs_2d
    integer, dimension(nnodes01+1),   intent(in) :: ia
    integer, dimension(nnz01),        intent(in) :: ja
    integer, dimension(:),            intent(in) :: nzg2m, g2m

    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradz
    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z, vol
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),              intent(in)    :: facespeed
    real(dp),  dimension(nnodes01),              intent(in)    :: amut
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    type(elem_type), dimension(nelem), intent(in) :: elem

    integer :: n, nedge_jac_eval, node_src_eval, row
    integer :: i, ii, ioff
    integer :: node1, node2
    integer :: ielem

    real(dp) :: w1, w2, betab, rmut, vort, tij, xis, sij, ri, f4
    real(dp) :: omega
    real(dp) :: xmr
    real(dp) :: Pkdk, Pkdk_rans, Pkdk_sgs
    real(dp) :: Dkdk, Dkdk_rans, Dkdk_sgs, Dkdw, Dwdw
    real(dp) :: xnormf, ynormf, znormf, areaf
    real(dp) :: face_speed
    real(dp) :: u, ubar, uminus, uplus, uy, uz, v
    real(dp) :: vx, vz, w, wx, wy, ux, vy, wz
    real(dp) :: diag1_k, diag1_w, off1_k, off1_w, diag2_k, diag2_w
    real(dp) :: off2_k, off2_w, xmrinv

    real(dp) :: my_xmach

    real(dp), parameter :: my_tiny   = tiny(1.0_dp)
    real(dp), parameter :: my_third  = 1.0_dp / 3.0_dp
    real(dp), parameter :: my_half   = 0.5_dp
    real(dp), parameter :: two_third = 2.0_dp / 3.0_dp
    real(dp), parameter :: my_1      = 1.0_dp
    real(dp), parameter :: my_1p5    = 1.5_dp

  continue

    if ( .not. blend_setup ) then
      if ( lmpi_master ) then
        write(*,*) 'Suspicious that blend not yet setup in jacobian routine...!'
      endif
      allocate(blend(nnodes01))
      allocate(crossd(nnodes0))
      blend_setup = .true.
    endif

    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
       my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'hrles_jacob_mix: only for in/comprss pg')
    end select

    xmr    = my_xmach / re
    xmrinv = my_1/xmr

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    nedge_jac_eval = nedgeloc
    node_src_eval  = nnodes0
    if (twod) then
      nedge_jac_eval = nedgeloc_2d
      node_src_eval   = nnodes0_2d
    endif

!   First compute the convective terms

    do n = 1, nedge_jac_eval
      node1 = eptr(1,n)
      node2 = eptr(2,n)
!     Unit normal to dual face and area

      xnormf = xn(n)
      ynormf = yn(n)
      znormf = zn(n)
      areaf  = ra(n)

!     Dual face speed

      face_speed = 0._dp

      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

!     First node
      u = qnode(2,node1)
      v = qnode(3,node1)
      w = qnode(4,node1)
      ubar   = xnormf*u + ynormf*v + znormf*w - face_speed
      uplus  = my_half * (ubar+abs(ubar))
      uminus = my_half * (ubar-abs(ubar))

      diag1_k = uplus *areaf
      diag1_w = uplus *areaf
      off1_k  = uminus*areaf
      off1_w  = uminus*areaf

!     if (node1 <= nnodes0)  res(node1) = res(node1) + (uplus*turb(1,node1)  &
!                                       +  uminus*turb(1,node2))*areaf

      if (node1 <= nnodes0) then
        row = g2m(node1)
        a_diag(1,1,row) = a_diag(1,1,row) + diag1_k
        a_diag(2,2,row) = a_diag(2,2,row) + diag1_w
        ioff            = fhelp(1,n)
        a_off(1,1,ioff) = a_off(1,1,ioff) + real(off1_k,odp)
        a_off(2,2,ioff) = a_off(2,2,ioff) + real(off1_w,odp)
      end if

!     Now do the other node (note that ubar pts in other direction)

      u = qnode(2,node2)
      v = qnode(3,node2)
      w = qnode(4,node2)

      ubar   = -(xnormf*u + ynormf*v + znormf*w - face_speed)
      uplus  = my_half * (ubar+abs(ubar))
      uminus = my_half * (ubar-abs(ubar))

      diag2_k = uplus *areaf
      diag2_w = uplus *areaf
      off2_k  = uminus*areaf
      off2_w  = uminus*areaf

!     if (node2 <= nnodes0)  res(node2) = res(node2) + (uplus*turb(1,node2)  &
!                                       + uminus*turb(1,node1))*areaf

      if (node2 <= nnodes0) then
        row = g2m(node2)
        a_diag(1,1,row) = a_diag(1,1,row) + diag2_k
        a_diag(2,2,row) = a_diag(2,2,row) + diag2_w
        ioff        = fhelp(2,n)
        a_off(1,1,ioff) = a_off(1,1,ioff) + real(off2_k,odp)
        a_off(2,2,ioff) = a_off(2,2,ioff) + real(off2_w,odp)
      end if

    end do

!   Next compute the source (production/destruction) terms
!   This assumes that the blending and cross terms have been previously computed
!   for the residual computation!

    source1 : do ii = 1,node_src_eval

      if (twod) then
        i = node_pairs_2d(1,ii)
      else
        i = ii
      end if

      w1 = blend(i)
      w2 = 1.0_dp - blend(i)
      betab = w1*beta1 + w2*beta2

      omega = turb(2,i)

      u  = qnode(2,i)
      v  = qnode(3,i)
      w  = qnode(4,i)

      ux = gradx(2,i)
      uy = grady(2,i)
      uz = gradz(2,i)
      vx = gradx(3,i)
      vy = grady(3,i)
      vz = gradz(3,i)
      wx = gradx(4,i)
      wy = grady(4,i)
      wz = gradz(4,i)

      rmut = amut(i) + 1.0e-6_dp

      xis = ux**2 + vy**2 + wz**2 + my_half*((uy + vx)**2 + (uz + wx)**2 +   &
            (vz + wy)**2)
      vort = sqrt((wy-vz)**2+(uz-wx)**2+(vx-uy)**2)
      if (strain_production) then
        tij = 2.0_dp*rmut*xis
      else
        tij = rmut*vort*vort
      end if
      if(abs(tij) <= my_tiny) tij = 1.0e-6_dp

!       Curvature Correction

        f4 = 1.0_dp
        if (sstrc) then
          sij = sqrt(2.0_dp*xis) + 1.0e-20_dp
          ri = (vort/sij)*(vort/sij - 1.0_dp)
          f4 = 1.0_dp/(1.0_dp + sstrc_crc*ri)
        end if

!     Add linearizations from just the destruction term since they will
!     add a positive contribution to the diagonal

      if ( .not. pre_4_17_2009_hrles ) then            ! default
        Pkdk_rans = 0._dp
        Pkdk_sgs  = two_third * (ux + vy + wz)
        Pkdk      = w1*Pkdk_rans + w2*Pkdk_sgs
      else                                             ! original terms
        Pkdk = 0.0_dp
      endif

      Dkdk_rans = betastar*turb(2,i)*xmrinv
      Dkdk_sgs  = my_1p5*c_ep*sqrt(turb(1,i)) / (vol(i)**my_third)
      Dkdk      = w1*Dkdk_rans + w2*Dkdk_sgs
      Dkdw = w1*betastar*turb(1,i)*xmrinv
      Dwdw = f4*2.0_dp*betab*turb(2,i)*xmrinv

      omega = turb(2,i)
      Dwdw = Dwdw + abs(crossd(i))/omega

      if (test_freestream) then
        Pkdk = 0._dp
        Dkdk = 0._dp
        Dkdw = 0._dp
        Dwdw = 0._dp
      end if

      row = g2m(i)
      a_diag(1,1,row) = a_diag(1,1,row) - vol(i)*(Pkdk - Dkdk)
      a_diag(1,2,row) = a_diag(1,2,row) + vol(i)*Dkdw
      a_diag(2,2,row) = a_diag(2,2,row) + vol(i)*Dwdw

    end do source1

!   Finally pick up diffusion terms

      do ielem = 1, nelem
        call hrles_jacob_diff(eqn_set, viscous_method, nnodes0, nnodes01,      &
                              nedgeloc, max_nnz, eptr, qnode, xn, yn, zn, ra,  &
                              a_diag, a_off, fhelp, nedgeloc_2d,               &
                              ielem, elem(ielem)%ncell, elem(ielem)%c2n,       &
                              elem(ielem)%c2e, x, y, z, elem(ielem)%type_cell, &
                              elem(ielem)%local_f2n, elem(ielem)%local_e2n,    &
                              elem(ielem)%local_f2e, elem(ielem)%e2n_2d,       &
                              elem(ielem)%face_per_cell,                       &
                              elem(ielem)%node_per_cell,                       &
                              elem(ielem)%edge_per_cell, nnz01, ia, ja, n_turb,&
                              n_tot, elem(ielem)%face_2d, nzg2m, g2m, amut)
      end do

  end subroutine hrles_jacob


!============================= HRLES_JACOB_DIFF ==============================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for HRLES model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine hrles_jacob_diff(eqn_set, viscous_method, nnodes0, nnodes01,      &
                              nedgeloc, max_nnz, eptr, qnode, xn, yn, zn, ra,  &
                              a_diag, a_off, fhelp, nedgeloc_2d,               &
                              ielem, ncell, c2n, c2e, x, y, z, type_cell,      &
                              local_f2n, local_e2n,local_f2e, e2n_2d,          &
                              face_per_cell, node_per_cell, edge_per_cell,     &
                              nnz01, ia, ja, n_turb, n_tot, face_2d, nzg2m,    &
                              g2m, amut)

    use kinddefs,         only : odp
    use info_depr,        only : tref, xmach, re, twod, skeleton,              &
                                 grad_x_y_z_contents, use_edge_gradients
    use debug_defs,       only : gradient_construction_lhs
    use solution_types,   only : compressible, incompressible
    use turb_kw_const,    only : sig_w1, sig_w2, sig_k1, sig_k2
    use turb_hrles_const, only : prt, amut_rans
    use fluid,            only : gamma, sutherland_constant
    use lmpi,             only : lmpi_die
    use utilities,        only : tangents

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem, n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: face_2d
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: nnz01

    integer, dimension(2,nedgeloc),          intent(in) :: eptr
    integer, dimension(2,nedgeloc),          intent(in) :: fhelp
    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(edge_per_cell,ncell), intent(in) :: c2e
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(nnz01),               intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m

    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: amut
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    character(len=3), intent(in) :: type_cell

    integer :: n, nedge_jac_eval, row
    integer :: ioff
    integer :: node, node1, node2
    integer :: nn
    integer :: total_nodes, edge_node, nodec, local_node
    integer :: k, column

    integer, parameter :: max_total_nodes = 100

    integer, dimension(max_total_nodes)   :: list_total_nodes

    real(dp) :: coeff1, coeff2
    real(dp) :: xmr, phi1, phi2
    real(dp) :: mu_node1, mu_node2, mut_node1, mut_node2
    real(dp) :: cstar
    real(dp) :: rho1inv, rho2inv
    real(dp) :: phi
    real(dp) :: p1
    real(dp) :: lx, ly, lz, mx, my, mz
    real(dp) :: deti
    real(dp) :: ex, ey, ez, disi
    real(dp) :: ngradt, factor, face_term
    real(dp) :: rnu1, rnu2
    real(dp) :: rho1, temp1, rho2, p2, temp2, my_xmach
    real(dp) :: mut_sst_node1, mut_sst_node2

    real(dp), dimension(3,3)               :: b
    real(dp), dimension(max_total_nodes)   :: w_ngradt
    real(dp), dimension(n_turb)            :: coe1, coe2

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp

  continue

    ngradt = 0._dp

!   When using the edge-based terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'hrles_jacob_mix: only for in/comprss pg')
    end select

    b   = my_0
    xmr = my_xmach / re

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    nedge_jac_eval = nedgeloc
    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    endif

!   Diffusion terms:

    diffusion_terms : if ( viscous_method > 0 ) then

      if(skeleton > 0) then
        write(*,*) ' Compute Jacobians of decoupled turbulent diffusion terms.'
        write(*,*) ' Using weighted least squares for average gradient.'
        write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
      endif
      if(trim(grad_x_y_z_contents) /= 'viscous weighted-least-squares') then
        call lmpi_die
      endif

      mixed_edge_loop: do n = 1, nedge_jac_eval

!       loop over all the faces and calculate the turbulent diffusion terms
!       (in the 2D case, this loop contains edges on only one y=constant plane)
!       turb(1,node) contains the turbulence value
!
!       Break into two contributions - directional and the average gradient

!       Nearest neighbor nodes

        node1 = eptr(1,n)
        node2 = eptr(2,n)

!       Initialize number of contributing nodes and global node list
!       for average gradient at face contributions

        total_nodes = 2
        list_total_nodes(1) = node1
        list_total_nodes(2) = node2
        w_ngradt(1)         = my_0
        w_ngradt(2)         = my_0

!       rnu = laminar viscosity / density

        if ( eqn_set == compressible ) then
          rho1   = qnode(1,node1)
          rho1inv = my_1/rho1
          p1     = qnode(5,node1)
          temp1  = gamma*p1*rho1inv
          rnu1   = viscosity_law( cstar, temp1 ) * rho1inv

          rho2   = qnode(1,node2)
          rho2inv = my_1/rho2
          p2     = qnode(5,node2)
          temp2  = gamma*p2*rho2inv
          rnu2   = viscosity_law( cstar, temp2 ) * rho2inv
        else
          rho1   = my_1
          rho2   = my_1
          rnu1   = my_1
          rnu2   = my_1
        end if

        coe1(1) = sig_k1
        coe2(1) = sig_k2
        coe1(2) = sig_w1
        coe2(2) = sig_w2

        turbvariable_loop1 : do nn = 1, n_turb

          coeff1 = blend(node1)*coe1(nn) + (my_1 - blend(node1))*coe2(nn)
          coeff2 = blend(node2)*coe1(nn) + (my_1 - blend(node2))*coe2(nn)

          if (nn == 1) then  ! k equation is blended
            coeff1 = blend(node1)*coeff1 + (my_1 - blend(node1))*prt
            coeff2 = blend(node2)*coeff2 + (my_1 - blend(node2))*prt
          end if

          mu_node1  = rnu1*rho1
          mu_node2  = rnu2*rho2
          mut_node1 = amut(node1)
          mut_node2 = amut(node2)
          mut_sst_node1 = amut_rans(node1)
          mut_sst_node2 = amut_rans(node2)

          if (nn == 2) then  ! w eq. is unblended, so use turb. visc. from SST
            phi1   = mu_node1 + mut_sst_node1/coeff1
            phi2   = mu_node2 + mut_sst_node2/coeff2
          else
            phi1   = mu_node1 + mut_node1/coeff1
            phi2   = mu_node2 + mut_node2/coeff2
          end if

          phi = my_half*(phi1 + phi2)

!         ex, ey, ez is unit vector along edge direction

          ex   = x(node2) - x(node1)
          ey   = y(node2) - y(node1)
          ez   = z(node2) - z(node1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          if( viscous_method == 2 ) then

          elseif( gradient_construction_lhs == 0 ) then

          elseif( gradient_construction_lhs == 1 ) then

          !...find tangent vectors in the dual face
          call tangents(xn(n), yn(n), zn(n), lx, ly, lz, mx, my, mz)

          !...find inverse elements of transformation matrix
          deti =  my_1/ ( ex*( ly*mz - lz*my )                                 &
                        + ey*( lz*mx - lx*mz )                                 &
                        + ez*( lx*my - ly*mx ) )

          b(1,1) =  deti*( ly*mz - lz*my )
          b(1,2) = -deti*( ey*mz - ez*my )
          b(1,3) =  deti*( ey*lz - ez*ly )

          b(2,1) = -deti*( lx*mz - lz*mx )
          b(2,2) =  deti*( ex*mz - ez*mx )
          b(2,3) = -deti*( ex*lz - ez*lx )

          b(3,1) =  deti*( lx*my - ly*mx )
          b(3,2) = -deti*( ex*my - ey*mx )
          b(3,3) =  deti*( ex*ly - ey*lx )

          endif

!         First gradient contribution from directional contribution

!         turbulent diffusion contribution at dual face [ two terms ]

!         [area]*[nondimensionalization factor: Mach / Re / sigma] at dual face

          face_term = xmr*ra(n)

!         [area]*[nondimensionalization factor : Mach / Re / sigma ]*
!         [edge:face vector dot product]/[edge distance] at dual face

          if( viscous_method == 2 ) then

            ngradt = face_term*( ex*xn(n) + ey*yn(n) + ez*zn(n) )*disi

          elseif( gradient_construction_lhs == 0 ) then

            ngradt = face_term*( ex*xn(n) + ey*yn(n) + ez*zn(n) )*disi

          elseif( gradient_construction_lhs == 0 ) then

            ngradt = face_term*( b(1,1)*xn(n) + b(2,1)*yn(n) + b(3,1)*zn(n) )* &
                     disi

          endif

          if ( node1 <= nnodes0 ) then

            ioff = fhelp(1,n)
            row  = g2m(node1)
            a_diag(nn,nn,row) = a_diag(nn,nn,row) + phi*ngradt
            a_off(nn,nn,ioff) = a_off(nn,nn,ioff) - phi*ngradt

          end if

          if ( node2 <= nnodes0 ) then

            ioff = fhelp(2,n)
            row  = g2m(node2)
            a_diag(nn,nn,row) = a_diag(nn,nn,row) + phi*ngradt
            a_off(nn,nn,ioff) = a_off(nn,nn,ioff) - phi*ngradt

          end if

!         Assemble final Jacobian matrices into sparse matrix form

          factor = -face_term

          edge_node_loop2 : do edge_node = 1,2

!           global node number

            node = list_total_nodes(edge_node)

            factor = -factor

!         Diagonal contributions

          if ( node <= nnodes0 ) then
            row = g2m(node)
            a_diag(nn,nn,row) = a_diag(nn,nn,row) - factor*phi*                &
                                 w_ngradt( edge_node )
          end if

!         Off-diagonal contributions

          local_node_loop : do local_node = 1, total_nodes

            nodec = list_total_nodes(local_node) ! global node number

            if (nodec == node) cycle

            if ( node <= nnodes0 ) then

!             Location of nonzero contribution via compressed row storage

              ioff = 0

              do k = ia(node), ia(node+1) - 1
                column = ja(k)
                if (column == nodec) ioff = nzg2m(k)
              end do

              if (ioff == 0) then
                write(6,*)'error: no place to put contribution from node ',    &
                           nodec,' to the off diagonal of node ',node
                stop ! FIXME: should be lmpi_die or se_exit(1)?
              end if

              a_off(nn,nn,ioff) = a_off(nn,nn,ioff) - factor*phi*              &
                                  w_ngradt( local_node )

            end if

            end do local_node_loop

          end do edge_node_loop2

        end do turbvariable_loop1

      end do mixed_edge_loop

    else

      if ( twod .or. (.not.use_edge_gradients) ) then
        call hrles_jacob_cell_diff(eqn_set, viscous_method, nnodes0, nnodes01, &
                                   nedgeloc, max_nnz, qnode, a_diag, a_off,    &
                                   nedgeloc_2d, ielem, ncell, c2n, c2e, x, y,  &
                                   z, type_cell, local_f2n, local_e2n,         &
                                   local_f2e, e2n_2d, face_per_cell,           &
                                   node_per_cell, edge_per_cell, nnz01, ia, ja,&
                                   n_turb, n_tot, face_2d, nzg2m, g2m, amut)
      else
        call hrles_jacob_cell_diff_opt(eqn_set, nnodes0, nnodes01, max_nnz,    &
                                       qnode, a_diag, a_off, ncell, c2n, x, y, &
                                       z, type_cell, local_f2n, local_e2n,     &
                                       face_per_cell, node_per_cell,           &
                                       edge_per_cell, nnz01, ia, ja, n_turb,   &
                                       n_tot, nzg2m, g2m, amut)
      endif

    end if diffusion_terms

  end subroutine hrles_jacob_diff


!============================= HRLES_JACOB_CELL_DIFF =========================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for HRLES model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine hrles_jacob_cell_diff(eqn_set, viscous_method, nnodes0, nnodes01, &
                                   nedgeloc, max_nnz, qnode, a_diag, a_off,    &
                                   nedgeloc_2d, ielem, ncell, c2n, c2e, x, y,  &
                                   z, type_cell, local_f2n, local_e2n,         &
                                   local_f2e, e2n_2d, face_per_cell,           &
                                   node_per_cell, edge_per_cell, nnz01, ia, ja,&
                                   n_turb, n_tot, face_2d, nzg2m, g2m, amut)

    use kinddefs,         only : odp
    use info_depr,        only : tref, xmach, re, twod, use_edge_gradients
    use debug_defs,       only : gradient_construction_lhs
    use solution_types,   only : compressible, incompressible
    use turb_kw_const,    only : sig_w1, sig_w2, sig_k1, sig_k2
    use turb_hrles_const, only : prt, amut_rans
    use fluid,            only : gamma, sutherland_constant
    use element_defs,     only : max_node_per_cell, max_face_per_cell,         &
                                 max_edge_per_cell
    use utilities,        only : tangents, tinverse, cell_jacobians

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem, n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: face_2d
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: nnz01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(edge_per_cell,ncell), intent(in) :: c2e
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(nnz01),               intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m

    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: amut
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    character(len=3), intent(in) :: type_cell

    integer :: n, nedge_jac_eval, row
    integer :: ie, i, ii, ie_local, i_local, ioff
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, edge, node
    integer :: n1, n2, n3, n4, n5, n6, nn
    integer :: nodec
    integer :: k, column

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: mut_sst_node
    real(dp) :: phi_k, phi_w, coeff_k, coeff_w
    real(dp) :: xmr, mu_node, mut_node
    real(dp) :: cstar
    real(dp) :: rho, rhoinv
    real(dp) :: dgradt_xi
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: areai, xnf, ynf, znf, lx, ly, lz, mx, my, mz
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: ex, ey, ez, disi
    real(dp) :: factor
    real(dp) :: dlgradt, dmgradt
    real(dp) :: my_xmach

    real(dp), dimension(3,3)               :: b, t
    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell) :: dgradx_celldq, dgrady_celldq
    real(dp), dimension(max_node_per_cell) :: dgradz_celldq
    real(dp), dimension(max_node_per_cell) :: dtrbrex, dtrbrey, dtrbrez
    real(dp), dimension(max_node_per_cell) :: dtrbrexavg, dtrbreyavg
    real(dp), dimension(max_node_per_cell) :: dtrbrezavg, dngradt
    real(dp), dimension(max_face_per_cell) :: nx, ny, nz
    real(dp), dimension(max_node_per_cell) :: nu_node, t_node

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp

    logical :: edge_gradients

  continue

!   When using the edge-based terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'hrles_jacob_mix: only for in/compress pg')
    end select

    xmr  = my_xmach / re

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    edge_map     = 0
    node_map     = 0

    nedge_jac_eval = nedgeloc
    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    endif

    nodes_local = 0
    if ( twod .and. (viscous_method == 0) ) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    elseif( viscous_method == 0 ) then

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

!   Diffusion terms:

      diffusion_term_cell : do n = 1, ncell

!       initialization

        cell_vol = 0.0_dp

        dtrbrex(:) = 0._dp
        dtrbrey(:) = 0._dp
        dtrbrez(:) = 0._dp

        phi_k    = 0._dp
        phi_w    = 0._dp

        xc = 0._dp
        yc = 0._dp
        zc = 0._dp

        x_node(:)       = 0._dp
        y_node(:)       = 0._dp
        z_node(:)       = 0._dp
        t_node(:)       = 0._dp
        nu_node(:)      = 0._dp
        dtrbrexavg(:) = 0._dp
        dtrbreyavg(:) = 0._dp
        dtrbrezavg(:) = 0._dp

!       compute cell averages and set up some local solution arrays

        node_loop : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

!         global node number

          node = c2n(i,n)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          if ( eqn_set == compressible ) then
            rho        = qnode(1,node)
            rhoinv     = my_1/rho
            t_node(i)  = gamma*qnode(5,node)*rhoinv
            nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv
          else
            rho        = my_1
            nu_node(i) = my_1
          end if

          coeff_k = blend(node)*sig_k1 + (my_1 - blend(node))*sig_k2
          coeff_w = blend(node)*sig_w1 + (my_1 - blend(node))*sig_w2

          ! Further blending for k equation
          coeff_k = blend(node)*coeff_k + (my_1 - blend(node))*prt

          mu_node      = rho*nu_node(i)
          mut_node     = amut(node)
          mut_sst_node = amut_rans(node)

          phi_k = phi_k + (mu_node + mut_node/coeff_k)
          phi_w = phi_w + (mu_node + mut_sst_node/coeff_w)

        end do node_loop

!       get cell averages by dividing by the number of nodes that contributed

        fact = 1._dp / real(nodes_local, dp)

        phi_k    = phi_k*fact
        phi_w    = phi_w*fact

!       compute the cell center (must loop over node_per_cell even in 2D)

        do i = 1, node_per_cell

!         global node number

          node = c2n(i,n)

          xc  =  xc + x(node)
          yc  =  yc + y(node)
          zc  =  zc + z(node)

        end do

        fact = 1._dp / real(node_per_cell, dp)

        xc  =  xc*fact
        yc  =  yc*fact
        zc  =  zc*fact

!       get the jacobians of the gradients in the primal cell via Green-Gauss

        call cell_jacobians(edges_local, max_node_per_cell, face_per_cell,     &
                            x_node, y_node, z_node, local_f2n, e2n_2d,         &
                            dgradx_celldq, dgrady_celldq, dgradz_celldq,       &
                            cell_vol, nx, ny, nz)

!       store off these average gradients

        if (twod) then

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            dtrbrexavg(i) = dgradx_celldq(i)
            dtrbrezavg(i) = dgradz_celldq(i)

          end do

        else

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            dtrbrexavg(i) = dgradx_celldq(i)
            dtrbreyavg(i) = dgrady_celldq(i)
            dtrbrezavg(i) = dgradz_celldq(i)

          end do

        end if

!       next loop over the edges in the cell and get each one's
!       contribution to the jacobian

        edge_loop : do ie_local = 1,edges_local

!         local edge number

          ie = edge_map(ie_local)

!         global edge number

          edge = c2e(ie,n)

!         check edge to make sure it is not off-processor - if it is
!         we don't need its contribution

          if (edge > nedge_jac_eval) cycle edge_loop

!         local node numbers of edge endpoints

          n1_loc = local_e2n(ie,1)
          n2_loc = local_e2n(ie,2)

!         global node numbers of edge endpoints

          n1 = c2n(n1_loc,n)
          n2 = c2n(n2_loc,n)

!         get this edges' contributiuon to the dual normal and area

!         edge midpoint

          xm = (x(n1) + x(n2))*my_half
          ym = (y(n1) + y(n2))*my_half
          zm = (z(n1) + z(n2))*my_half

!         compute left face centroid

          n3 = c2n(local_e2n(ie,3),n)

          if (local_e2n(ie,4) /= 0) then

!           quad cell face

            n4 = c2n(local_e2n(ie,4),n)

            xl = (x(n1) + x(n2) + x(n3) + x(n4))*my_4th
            yl = (y(n1) + y(n2) + y(n3) + y(n4))*my_4th
            zl = (z(n1) + z(n2) + z(n3) + z(n4))*my_4th

          else

!           tria cell face

            xl = (x(n1) + x(n2) + x(n3))*my_3rd
            yl = (y(n1) + y(n2) + y(n3))*my_3rd
            zl = (z(n1) + z(n2) + z(n3))*my_3rd

          end if

!         compute right face centroid

          n5 = c2n(local_e2n(ie,5),n)

          if (local_e2n(ie,6) /= 0) then

!           quad cell face

            n6 = c2n(local_e2n(ie,6),n)

            xr = (x(n1) + x(n2) + x(n5) + x(n6))*my_4th
            yr = (y(n1) + y(n2) + y(n5) + y(n6))*my_4th
            zr = (z(n1) + z(n2) + z(n5) + z(n6))*my_4th

          else

!           tria cell face

            xr = (x(n1) + x(n2) + x(n5))*my_3rd
            yr = (y(n1) + y(n2) + y(n5))*my_3rd
            zr = (z(n1) + z(n2) + z(n5))*my_3rd

          end if

!         get the contributions to dual normals from the two triangles
!         that form part of the dual-cell surface

!         area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

          areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
          areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
          areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

!         get (jacobians of) gradients at the dual face; either take gradients
!         for this piece of the dual face to be the same as the cell-average
!         gradient computed above  (which is what the legacy FUN3D solver does
!         for tets), or combine with the edge-gradient to increase h-ellipticity
!         non-simplicial meshes.

!         for tets in 3D or prisms in 2D, edge gradients add no new info
!         so there is no need to do the extra work

          edge_gradients = use_edge_gradients

          if (type_cell == 'tet') edge_gradients = .false.
          if (twod .and. type_cell == 'prz') edge_gradients = .false.

          include_edge_gradients : if (edge_gradients) then

          do nn = 1, n_turb
!           ex, ey, ez is unit vector along edge direction

            ex   = x(n2) - x(n1)
            ey   = y(n2) - y(n1)
            ez   = z(n2) - z(n1)
            disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

            ex   = ex*disi
            ey   = ey*disi
            ez   = ez*disi

            if ( gradient_construction_lhs == 0 ) then

!             commented lines below included from flux routine for reference

!             directional gradients along edge

!             egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

!             average Green-Gauss gradient in edge direction

!             gradt_xi = trbrexavg*ex + trbreyavg*ey + trbrezavg*ez

!             combine gradient contributions from edge and primal cell

!             trbrex = trbrexavg + ( egradt - gradt_xi )*ex
!             trbrey = trbreyavg + ( egradt - gradt_xi )*ey
!             trbrez = trbrezavg + ( egradt - gradt_xi )*ez

!             write the above gradients as
!             trbrex = trbrexavg - (trbrexavg*ex+trbreyavg*ey+trbrezavg*ez)*ex
!                      <-----------------------avg_term---------------------->
!                    + (turb2-turb1)*disi*ex
!                      <-----edge_term----->
!             etc.

!             first get the avg_term pieces; all active cell nodes contribute

              do i_local = 1, nodes_local

!               local node number

                i = node_map(i_local)

                dgradt_xi = dtrbrexavg(i)*ex+dtrbreyavg(i)*ey+dtrbrezavg(i)*ez

                dtrbrex(i) = dtrbrexavg(i) - dgradt_xi*ex
                dtrbrey(i) = dtrbreyavg(i) - dgradt_xi*ey
                dtrbrez(i) = dtrbrezavg(i) - dgradt_xi*ez

              end do

!             next get the edge_term pieces; only the two edge nodes contribute

              dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*ex
              dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*ex
              dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*ey
              dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*ey
              dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*ez
              dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*ez

            elseif( gradient_construction_lhs == 1 ) then

!             find tangent vectors in the dual face

              areai = my_1/sqrt( areax**2 + areay**2 + areaz**2 )
              xnf = areax*areai
              ynf = areay*areai
              znf = areaz*areai
              call tangents(xnf, ynf, znf, lx, ly, lz, mx, my, mz)

!             form transformation matrix

              !...edge direction
              b(1,1) = ex
              b(1,2) = ey
              b(1,3) = ez

              !...l direction
              b(2,1) = lx
              b(2,2) = ly
              b(2,3) = lz

              !...m direction
              b(3,1) = mx
              b(3,2) = my
              b(3,3) = mz

              call tinverse( b , t )

!             commented lines below included from flux routine for reference

!             directional gradients

!             egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

!             lgradt = trbrexavg*b(2,1) + trbreyavg*b(2,2) + trbrezavg*b(2,3)

!             mgradt = trbrexavg*b(3,1) + trbreyavg*b(3,2) + trbrezavg*b(3,3)

!             resolve gradient contributions from edge and dual face

!             trbrex = t(1,1)*egradt + t(1,2)*lgradt + t(1,3)*mgradt
!             trbrey = t(2,1)*egradt + t(2,2)*lgradt + t(2,3)*mgradt
!             trbrez = t(3,1)*egradt + t(3,2)*lgradt + t(3,3)*mgradt

!             first get the avg_term pieces; all active cell nodes contribute

              do i_local = 1, nodes_local

!               local node number

                i = node_map(i_local)

                dlgradt = dtrbrexavg(i)*b(2,1) + dtrbreyavg(i)*b(2,2)          &
                        + dtrbrezavg(i)*b(2,3)

                dmgradt = dtrbrexavg(i)*b(3,1) + dtrbreyavg(i)*b(3,2)          &
                        + dtrbrezavg(i)*b(3,3)

                dtrbrex(i) = t(1,2)*dlgradt + t(1,3)*dmgradt
                dtrbrey(i) = t(2,2)*dlgradt + t(2,3)*dmgradt
                dtrbrez(i) = t(3,2)*dlgradt + t(3,3)*dmgradt

              end do

!             next get the edge_term pieces; only the two edge nodes contribute

              dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*t(1,1)
              dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*t(1,1)
              dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*t(2,1)
              dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*t(2,1)
              dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*t(3,1)
              dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*t(3,1)

            end if
          end do

          else include_edge_gradients

!           only have the unaltered, average green-gauss contributions;
!           all active nodes in the cell contribute

            do nn = 1, n_turb
              do i_local = 1, nodes_local

!               local node number

                i = node_map(i_local)

                dtrbrex(i) = dtrbrexavg(i)
                dtrbrey(i) = dtrbreyavg(i)
                dtrbrez(i) = dtrbrezavg(i)

              end do
            end do

          end if include_edge_gradients

!         form some intermediate Jacobians at all nodes

          dngradt(:) = xmr*( dtrbrex(:)*areax + dtrbrey(:)*areay               &
                     +       dtrbrez(:)*areaz )

!         assemble final Jacobian matrices into sparse matrix form

          factor = -my_1

          edge_node_loop : do ii = 1,2

!           global node number

!           diagonal contributions

!           local (i) and global (node) numbers

            if (ii == 1) then
              i = n1_loc
              node = c2n(n1_loc,n)
            else
              i = n2_loc
              node = c2n(n2_loc,n)
            end if

            factor = -my_1*factor

            if ( node <= nnodes0 ) then
              row = g2m(node)
              a_diag(1,1,row) = a_diag(1,1,row) - factor*phi_k*dngradt(i)      &
                                 /qnode(1,node)
              a_diag(2,2,row) = a_diag(2,2,row) - factor*phi_w*dngradt(i)      &
                                 /qnode(1,node)
            end if

!           off-diagonal contributions

            node_loop_2 : do i_local = 1, nodes_local

!             local node number

              i = node_map(i_local)

!             global node number

              nodec = c2n(i,n)

              if (nodec == node) cycle node_loop_2

              if ( node <= nnodes0 ) then

!               determine location of nonzero contribution in comp row storage

                ioff = 0

                do k = ia(node), ia(node+1) - 1
                  column = ja(k)
                  if (column == nodec) ioff = nzg2m(k)
                end do

                if (ioff == 0) then
                  write(6,*)'error: no place to put contribution from node ',  &
                             nodec,' to the off diagonal of node ',node
                  stop ! FIXME: should be lmpi_die or se_exit(1)?
                end if

                a_off(1,1,ioff) = a_off(1,1,ioff) - factor*phi_k*dngradt(i)    &
                                  /qnode(1,node)
                a_off(2,2,ioff) = a_off(2,2,ioff) - factor*phi_w*dngradt(i)    &
                                  /qnode(1,node)

              end if

            end do node_loop_2

          end do edge_node_loop

        end do edge_loop

      end do diffusion_term_cell

  end subroutine hrles_jacob_cell_diff


!============================= HRLES_JACOB_CELL_DIFF_OPT =====================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for HRLES model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine hrles_jacob_cell_diff_opt(eqn_set, nnodes0, nnodes01, max_nnz,    &
                                       qnode, a_diag, a_off, ncell, c2n, x, y, &
                                       z, type_cell, local_f2n, local_e2n,     &
                                       face_per_cell, node_per_cell,           &
                                       edge_per_cell, nnz01, ia, ja, n_turb,   &
                                       n_tot, nzg2m, g2m, amut)

    use kinddefs,         only : odp
    use info_depr,        only : tref, xmach, re
    use solution_types,   only : compressible, incompressible
    use turb_kw_const,    only : sig_w1, sig_w2, sig_k1, sig_k2
    use turb_hrles_const, only : prt, amut_rans
    use fluid,            only : gamma, sutherland_constant
    use element_defs,     only : max_node_per_cell

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: nnz01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(nnz01),               intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m

    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: amut
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    character(len=3), intent(in) :: type_cell

    integer :: n, row
    integer :: ie, i, ioff
    integer :: n1_loc, n2_loc, node
    integer :: nodec, n3_loc, n4_loc, n5_loc, n6_loc, j
    integer :: k, column, iface, nn1, nn2, nn3, nn4

    real(dp) :: phi_k, phi_w, coeff_k, coeff_w
    real(dp) :: xmr, mu_node, mut_node, xavg, yavg, zavg
    real(dp) :: mut_sst_node
    real(dp) :: cstar
    real(dp) :: rho
    real(dp) :: dgradt_xi, cell_vol_inv
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: xc, yc, zc, cell_vol, fact, nx2, ny2, nz2, term2
    real(dp) :: ex, ey, ez, disi, xavg1, yavg1, zavg1, nx1, ny1, nz1
    real(dp) :: my_xmach, nx, ny, nz, xavg2, yavg2, zavg2, term1

    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell) :: rhoinv_node
    real(dp), dimension(max_node_per_cell) :: dtrbrex, dtrbrey, dtrbrez
    real(dp), dimension(max_node_per_cell) :: dtrbrexavg, dtrbreyavg
    real(dp), dimension(max_node_per_cell) :: dtrbrezavg, dngradt
    real(dp), dimension(max_node_per_cell) :: nu_node, t_node
    real(dp), dimension(2,node_per_cell,node_per_cell) :: a

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp
    real(dp), parameter :: my_6th  = 1.0_dp/6.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp
    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'hrles_jacob_mix: only for in/comprss pg')
    end select

    xmr  = my_xmach / re
    fact = 1._dp / real(node_per_cell, dp)

!   Diffusion terms:

      diffusion_term_cell : do n = 1, ncell

        a        = 0._dp
        phi_k    = 0._dp
        phi_w    = 0._dp

        xc = 0._dp
        yc = 0._dp
        zc = 0._dp

!       compute cell averages and set up some local solution arrays

        node_loop : do i = 1, node_per_cell

          node = c2n(i,n)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          xc  =  xc + x_node(i)
          yc  =  yc + y_node(i)
          zc  =  zc + z_node(i)

          if ( eqn_set == compressible ) then
            rho           = qnode(1,node)
            rhoinv_node(i)= my_1/rho
            t_node(i)     = gamma*qnode(5,node)*rhoinv_node(i)
            nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv_node(i)
          else
            rho        = my_1
            rhoinv_node(i) = my_1
            nu_node(i) = my_1
          end if

          coeff_k  = blend(node)*sig_k1 + (my_1 - blend(node))*sig_k2
          coeff_w  = blend(node)*sig_w1 + (my_1 - blend(node))*sig_w2

          ! Further blending for k equation
          coeff_k  = blend(node)*coeff_k + (my_1 - blend(node))*prt

          mu_node      = rho*nu_node(i)
          mut_node     = amut(node)
          mut_sst_node = amut_rans(node)

          phi_k = phi_k + (mu_node + mut_node/coeff_k)
          phi_w = phi_w + (mu_node + mut_sst_node/coeff_w)

        end do node_loop

        phi_k    = phi_k*fact
        phi_w    = phi_w*fact

        xc  =  xc*fact
        yc  =  yc*fact
        zc  =  zc*fact

!       get the jacobians of the gradients in the primal cell via Green-Gauss

        dtrbrexavg(:) = 0.0_dp
        dtrbreyavg(:) = 0.0_dp
        dtrbrezavg(:) = 0.0_dp
        cell_vol      = 0.0_dp

      threed_faces : do iface = 1,face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!         triangular faces of the cell

!         face normals (factor of 1/2 deferred till cell_vol
!         and jacobian terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))         &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))         &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))         &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)

!         jacobian contributions

          dtrbrexavg(nn1) = dtrbrexavg(nn1) + nx
          dtrbreyavg(nn1) = dtrbreyavg(nn1) + ny
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + nz

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + nx
          dtrbreyavg(nn2) = dtrbreyavg(nn2) + ny
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + nz

          dtrbrexavg(nn3) = dtrbrexavg(nn3) + nx
          dtrbreyavg(nn3) = dtrbreyavg(nn3) + ny
          dtrbrezavg(nn3) = dtrbrezavg(nn3) + nz

        else

!         quadrilateral faces of the cell

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))        &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))        &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))        &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))        &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))
          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))        &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))
          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))        &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!         cell volume contributions

          cell_vol = cell_vol + (term1 + term2)

!         jacobian contributions

          dtrbrexavg(nn1) = dtrbrexavg(nn1) + nx1 + nx2
          dtrbreyavg(nn1) = dtrbreyavg(nn1) + ny1 + ny2
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + nz1 + nz2

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + nx1
          dtrbreyavg(nn2) = dtrbreyavg(nn2) + ny1
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + nz1

          dtrbrexavg(nn3) = dtrbrexavg(nn3) + nx1 + nx2
          dtrbreyavg(nn3) = dtrbreyavg(nn3) + ny1 + ny2
          dtrbrezavg(nn3) = dtrbrezavg(nn3) + nz1 + nz2

          dtrbrexavg(nn4) = dtrbrexavg(nn4) + nx2
          dtrbreyavg(nn4) = dtrbreyavg(nn4) + ny2
          dtrbrezavg(nn4) = dtrbrezavg(nn4) + nz2

        end if

      end do threed_faces

      cell_vol = cell_vol * my_18th
      cell_vol_inv = my_6th/cell_vol

      dtrbrexavg(:) = dtrbrexavg(:) * cell_vol_inv
      dtrbreyavg(:) = dtrbreyavg(:) * cell_vol_inv
      dtrbrezavg(:) = dtrbrezavg(:) * cell_vol_inv

!       next loop over the edges in the cell and get each one's
!       contribution to the jacobian

        edge_loop : do ie = 1, edge_per_cell

          n1_loc = local_e2n(ie,1)
          n2_loc = local_e2n(ie,2)
          n3_loc = local_e2n(ie,3)
          n4_loc = local_e2n(ie,4)
          n5_loc = local_e2n(ie,5)
          n6_loc = local_e2n(ie,6)

!         get this edges' contributiuon to the dual normal and area

!         edge midpoint

          xm = (x_node(n1_loc) + x_node(n2_loc))*my_half
          ym = (y_node(n1_loc) + y_node(n2_loc))*my_half
          zm = (z_node(n1_loc) + z_node(n2_loc))*my_half

!       compute left face centroid

          if (n4_loc /= 0) then
            xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc)             &
                + x_node(n4_loc))*my_4th
            yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc)             &
                + y_node(n4_loc))*my_4th
            zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc)             &
                + z_node(n4_loc))*my_4th
          else
            xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc))*my_3rd
            yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc))*my_3rd
            zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc))*my_3rd
          end if

!       compute right face centroid

          if (n6_loc /= 0) then
            xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc)             &
                + x_node(n6_loc))*my_4th
            yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc)             &
                + y_node(n6_loc))*my_4th
            zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc)             &
                + z_node(n6_loc))*my_4th
          else
            xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc))*my_3rd
            yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc))*my_3rd
            zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc))*my_3rd
          end if

!         get the contributions to dual normals from the two triangles
!         that form part of the dual-cell surface

!         area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

          areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
          areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
          areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

!         get (jacobians of) gradients at the dual face; either take gradients
!         for this piece of the dual face to be the same as the cell-average
!         gradient computed above  (which is what the legacy FUN3D solver does
!         for tets), or combine with the edge-gradient to increase h-ellipticity
!         non-simplicial meshes.

!         for tets in 3D or prisms in 2D, edge gradients add no new info
!         so there is no need to do the extra work

          if (type_cell /= 'tet') then

            ex   = x_node(n2_loc) - x_node(n1_loc)
            ey   = y_node(n2_loc) - y_node(n1_loc)
            ez   = z_node(n2_loc) - z_node(n1_loc)
            disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )
            ex   = ex*disi
            ey   = ey*disi
            ez   = ez*disi

!           commented lines below included from flux routine for reference

!           directional gradients along edge

!           egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

!           average Green-Gauss gradient in edge direction

!           gradt_xi = trbrexavg*ex + trbreyavg*ey + trbrezavg*ez

!           combine gradient contributions from edge and primal cell

!           trbrex = trbrexavg + ( egradt - gradt_xi )*ex
!           trbrey = trbreyavg + ( egradt - gradt_xi )*ey
!           trbrez = trbrezavg + ( egradt - gradt_xi )*ez

!           write the above gradients as
!           trbrex = trbrexavg - (trbrexavg*ex+trbreyavg*ey+trbrezavg*ez)*ex
!                    <-----------------------avg_term---------------------->
!                  + (turb2-turb1)*disi*ex
!                    <-----edge_term----->
!           etc.

!           first get the avg_term pieces; all active cell nodes contribute

            do i = 1, node_per_cell
              dgradt_xi = dtrbrexavg(i)*ex+dtrbreyavg(i)*ey+dtrbrezavg(i)*ez
              dtrbrex(i) = dtrbrexavg(i) - dgradt_xi*ex
              dtrbrey(i) = dtrbreyavg(i) - dgradt_xi*ey
              dtrbrez(i) = dtrbrezavg(i) - dgradt_xi*ez
            end do

!           next get the edge_term pieces; only the two edge nodes contribute

            dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*ex
            dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*ex
            dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*ey
            dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*ey
            dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*ez
            dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*ez

          else

            dtrbrex(:) = dtrbrexavg(:)
            dtrbrey(:) = dtrbreyavg(:)
            dtrbrez(:) = dtrbrezavg(:)

          end if

!         form some intermediate Jacobians at all nodes

          dngradt(:) = xmr*( dtrbrex(:)*areax + dtrbrey(:)*areay               &
                     +       dtrbrez(:)*areaz )

!         assemble final Jacobian matrices into sparse matrix form
!         map the local entries into compact space and unpack later

          node_loop_3 : do j = 1, node_per_cell
            a(1,n1_loc,j) = a(1,n1_loc,j) - phi_k*dngradt(j)*rhoinv_node(n1_loc)
            a(2,n1_loc,j) = a(2,n1_loc,j) - phi_w*dngradt(j)*rhoinv_node(n1_loc)
          end do node_loop_3

          node_loop_4 : do j = 1, node_per_cell
            a(1,n2_loc,j) = a(1,n2_loc,j) + phi_k*dngradt(j)*rhoinv_node(n2_loc)
            a(2,n2_loc,j) = a(2,n2_loc,j) + phi_w*dngradt(j)*rhoinv_node(n2_loc)
          end do node_loop_4

        end do edge_loop

        do i = 1, node_per_cell
          node = c2n(i,n)
          if ( node <= nnodes0 ) then
            do j = 1, node_per_cell
              if ( i == j ) then
                row = g2m(node)
                a_diag(1,1,row) = a_diag(1,1,row) + a(1,i,i)
                a_diag(2,2,row) = a_diag(2,2,row) + a(2,i,i)
              else
                nodec = c2n(j,n)
                ioff = 0
                search : do k = ia(node), ia(node+1) - 1
                  column = ja(k)
                  if (column == nodec) then
                    ioff = nzg2m(k)
                    exit search
                  endif
                end do search
                a_off(1,1,ioff) = a_off(1,1,ioff) + real(a(1,i,j),odp)
                a_off(2,2,ioff) = a_off(2,2,ioff) + real(a(2,i,j),odp)
              endif
            end do
          end if
        end do

      end do diffusion_term_cell

  end subroutine hrles_jacob_cell_diff_opt

  include 'viscosity_law.f90'

end module turb_hrles

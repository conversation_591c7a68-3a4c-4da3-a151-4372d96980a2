module matrix_monitor

  use kinddefs,        only : dp, jp, odp, dqp, r4, r8
  use lmpi,            only : lmpi_master, lmpi_reduce, lmpi_max,            &
                              lmpi_conditional_stop, lmpi_bcast,             &
                              lmpi_max_and_maxid
  use flux_util,       only : spectral_rate

  implicit none

  private

  public :: ax_matrix_order, dq_stats
  public :: array_rms, array_inf, array_norms, lm_stats
  public :: array_inf_location


  interface array_inf_location
    module procedure array_inf_location_r4
    module procedure array_inf_location_r8
  end interface

  interface array_inf
    module procedure array_inf_r4
    module procedure array_inf_r8
  end interface

contains

!============================= AX_MATRIX_ORDER ===============================80
!
! A times x where A and x are stored in matrix ordering.
!
!=============================================================================80
  subroutine ax_matrix_order( nb, dof0, a_diag, a_off, x,      &
                              crow, res_matvec )

    use comprow_types,        only : crow_flow

    use periodics, only : periodic, periodic_nature, periodic_data

    integer, intent(in) :: nb, dof0

    type(crow_flow), intent(in) :: crow

    real( dp), dimension(nb, nb, dof0),             intent(in) :: a_diag
    real(odp), dimension(nb, nb, size(crow%ja,1) ), intent(in) :: a_off
    real(dqp), dimension(:,:),                      intent(in)  :: x

    real( jp), dimension(nb, dof0), intent(out) :: res_matvec

    integer :: dofm, jm, j, colm, group, primary_entry

  continue

    if ( .not.periodic ) then

      do dofm = 1, dof0

        res_matvec(:,dofm) = a_diag(:,1,dofm)*x(1,dofm)
        do j = 2, nb
          res_matvec(:,dofm) = res_matvec(:,dofm)       &
                             + a_diag(:,j,dofm)*x(j,dofm)
        end do

        !...Jacobian entries accessed sequentially.
        do jm = crow%iam(dofm), crow%iam(dofm+1) - 1
          colm = crow%jam(jm)  !column number in matrix format.
          do j = 1, nb
            res_matvec(:,dofm) = res_matvec(:,dofm)    &
                               + a_off(:,j,jm)*x(j,colm)
          end do
        end do

      end do

    else

      do dofm = 1, dof0

        group = periodic_nature(dofm)

        if ( group >= 0 ) then
          res_matvec(:,dofm) = a_diag(:,1,dofm)*x(1,dofm)
          do j = 2, nb
            res_matvec(:,dofm) = res_matvec(:,dofm)       &
                               + a_diag(:,j,dofm)*x(j,dofm)
          end do
        else
          res_matvec(:,dofm) = 0._dqp
        endif

      end do

      do dofm = 1, dof0

        group = periodic_nature(dofm)

        primary_entry = dofm
        if ( group < 0 ) then
          primary_entry = periodic_data( abs(group) )%mlist(1)
        endif

        !...Jacobian entries accessed sequentially.
        do jm = crow%iam(dofm), crow%iam(dofm+1) - 1
          colm = crow%jam(jm)  !column number in matrix format.
          do j = 1, nb
            res_matvec(:,primary_entry) = res_matvec(:,primary_entry)    &
                                        + a_off(:,j,jm)*x(j,colm)
          end do
        end do

      end do

    endif

  end subroutine ax_matrix_order



!=========================== DQ_STATS ========================================80
!
! Write some statistics about dq.
!
!=============================================================================80

  subroutine dq_stats( nb, dof0, dq )

    integer,                   intent(in) :: nb, dof0
    real(dqp), dimension(:,:), intent(in) :: dq

    integer :: dof, ndof, ndofg

    real(dqp), dimension(nb) :: r1, r2

!beginNeverComplex
    real(dp),  dimension(nb) :: rg, rgm
!endNeverComplex

    character(len=80) :: t

    character(len=100) :: fs

  continue

    t = 'CHECK'

    r1(:) = 0._dp
    r2(:) = 0._dp
    ndof  = 0
    do dof=1,dof0
      r1(:) = max( r1(:), abs( dq(:,dof) ) )
      r2(:) = r2(:)          + dq(:,dof)**2
      ndof  = ndof + 1
    enddo

    rg(1:nb) = real(r1(1:nb),dp)
    call lmpi_max(    rg(1:nb), rgm(1:nb) )
    if ( lmpi_master ) r1(1:nb) = rgm(1:nb)

    rg(1:nb) = real(r2(1:nb),dp)
    call lmpi_reduce( rg(1:nb), rgm(1:nb) )
    if ( lmpi_master ) r2(1:nb) = rgm(1:nb)

    ndofg = ndof
    call lmpi_reduce( ndofg, ndof )

    if ( .not. lmpi_master ) return

    r2(:) = sqrt( r2(:) / real( ndof, dp ) )

    write(*,*)
    write(*,"(1x,2a,i2,1x,i0)") trim(t),'....nb:dof=',nb,ndof

    fs = "(1x,2a,1x,7e13.5))"
    if ( nb > 7 ) then
      fs = "(1x,2a,1x,7e13.5/(16x,7e13.5))"
    endif

    write(*,*)
    write(*,trim(fs)) trim(t),' dq    max=',real(r1,dp)
    write(*,trim(fs)) trim(t),' dq    rms=',real(r2,dp)
    write(*,*)

  end subroutine dq_stats

!=========================== LM_STATS ========================================80
!
! Checking convergence of of lm.
!
!=============================================================================80

  subroutine lm_stats( level, nb, dofg, qset, work, rms_1, rms_2 )

    integer,                  intent(in) :: level, nb, dofg, qset
    real(dp),                 intent(in) :: work
    real(dp), dimension(:,:), intent(in) :: rms_1, rms_2

    integer :: f=88, ierr

    real(dp), dimension(nb) :: cr

    real(dp), parameter :: unity = 1._dp

    character(len=80) :: t

    character(len=100) :: fs


  continue

    ierr = 0
    if ( lmpi_master ) then
      open( unit=f, file='monitor_linear.summary',           &
            form='formatted', position='append', iostat=ierr )
    endif

    call lmpi_conditional_stop(ierr,'file-open-error:lm_stats')

    if ( .not.lmpi_master ) return

    t = 'RELAX'

    cr = -9.0_dp

    write(*,*)
    write(*,"(1x,2a,i2,i20,2i2,1x,f10.2)")    &
    trim(t),'..level:dofg:qset:nb:work=',level,dofg,qset,nb,work

    write(f,*)
    write(f,"(1x,2a,i2,i20,2i2,1x,f10.2)")    &
    trim(t),'..level:dofg:qset:nb:work=',level,dofg,qset,nb,work

    fs = "(1x,2a,7e13.5))"
    if ( nb > 7 ) then
      fs = "(1x,2a,7e13.5/(14x,7e13.5))"
    endif

    write(*,trim(fs)) trim(t),':rms_1 =',real(rms_1(1:nb,level),dp)
    write(*,trim(fs)) trim(t),':rms_2 =',real(rms_2(1:nb,level),dp)

    write(f,trim(fs)) trim(t),':rms_1 =',real(rms_1(1:nb,level),dp)
    write(f,trim(fs)) trim(t),':rms_2 =',real(rms_2(1:nb,level),dp)

    call spectral_rate( rms_2(1:nb,level), rms_1(1:nb,level), &
                        unity, cr(1:nb) )

    write(*,trim(fs)) trim(t),':cr:rms=',real(cr(1:nb),dp)
    write(*,*)

    write(f,trim(fs)) trim(t),':cr:rms=',real(cr(1:nb),dp)

    close(f)

  end subroutine lm_stats

!============================= ARRAY_RMS =====================================80
!
! Rms of array.
!
!=============================================================================80
  subroutine array_rms( nb, dof0, dofg, array, rms )

    integer,                  intent(in)  :: nb, dof0, dofg
    real(jp), dimension(:,:), intent(in)  :: array
    real(dp), dimension(:),   intent(out) :: rms

!beginNeverComplex
    real(dp),  dimension(nb) :: rg, rgm
!endNeverComplex

    integer :: dof

  continue

    rms(1:nb) = 0._dp
    do dof = 1, dof0
      rms(1:nb) = rms(1:nb) + array(1:nb,dof)**2
    enddo

    rg(1:nb) = real(rms(1:nb),dp)
    call lmpi_reduce( rg(1:nb), rgm(1:nb) )
    if ( lmpi_master ) rms(1:nb) = rgm(1:nb)

    if ( lmpi_master ) rms(1:nb) = sqrt( rms(1:nb) / real( dofg, dp ) )

    call lmpi_bcast(rms)

  end subroutine array_rms

!============================= ARRAY_INF =====================================80
!
! Infinity norm of array.
!
!=============================================================================80
  subroutine array_inf_r8( nb, dof0, array, inf )

    use kinddefs, only : r8

    integer,                   intent(in)  :: nb, dof0
    real(r8), dimension(:,:), intent(in)  :: array
    real(dp), dimension(:),   intent(out) :: inf

    real(dp), dimension(nb) :: tt

!beginNeverComplex
    real(dp),  dimension(nb) :: rg, rgm
!endNeverComplex

    integer :: dof

  continue

    inf(1:nb) = 0._dp
    do dof = 1, dof0

       tt(1:nb) = array(1:nb,dof)
      inf(1:nb) = max( inf(1:nb), abs( tt(1:nb) ) )

    enddo

    rg(1:nb) = real(inf(1:nb),dp)
    call lmpi_max( rg(1:nb), rgm(1:nb) )
    if ( lmpi_master ) inf(1:nb) = rgm(1:nb)

    call lmpi_bcast(inf)

  end subroutine array_inf_r8

!============================= ARRAY_INF =====================================80
!
! Infinity norm of array.
!
!=============================================================================80
  subroutine array_inf_r4( nb, dof0, array, inf )

    use kinddefs, only : r4

    integer,                   intent(in)  :: nb, dof0
    real(r4), dimension(:,:), intent(in)  :: array
    real(dp),  dimension(:),   intent(out) :: inf

    real(dp), dimension(nb) :: tt

!beginNeverComplex
    real(dp),  dimension(nb) :: rg, rgm
!endNeverComplex

    integer :: dof

  continue

    inf(1:nb) = 0._dp
    do dof = 1, dof0

       tt(1:nb) = array(1:nb,dof)
      inf(1:nb) = max( inf(1:nb), abs( tt(1:nb) ) )

    enddo

    rg(1:nb) = real(inf(1:nb),dp)
    call lmpi_max( rg(1:nb), rgm(1:nb) )
    if ( lmpi_master ) inf(1:nb) = rgm(1:nb)

    call lmpi_bcast(inf)

  end subroutine array_inf_r4


!============================= ARRAY_NORMS ===================================80
!
! Norms of array (rms and inf).
!
!=============================================================================80
  subroutine array_norms( nb, dof0, dofg, array, rms, inf )

    integer,                      intent(in)  :: nb, dof0, dofg
    real(jp), dimension(nb,dof0), intent(in)  :: array
    real(dp), dimension(nb),      intent(out) :: rms, inf

    real(dp), dimension(nb) :: tt

!beginNeverComplex
    real(dp),  dimension(nb) :: rg, rgm
!endNeverComplex

    integer :: dof

  continue

    rms(1:nb) = 0._dp
    inf(1:nb) = 0._dp
    do dof = 1, dof0

       tt(1:nb) = array(1:nb,dof)
      inf(1:nb) = max( inf(1:nb), abs( tt(1:nb) ) )
      rms(1:nb) = rms(1:nb) + tt(1:nb)**2

    enddo

    rg(1:nb) = real(rms(1:nb),dp)
    call lmpi_reduce( rg(1:nb), rgm(1:nb) )
    if ( lmpi_master ) rms(1:nb) = rgm(1:nb)

    rg(1:nb) = real(inf(1:nb),dp)
    call lmpi_max( rg(1:nb), rgm(1:nb) )
    if ( lmpi_master ) inf(1:nb) = rgm(1:nb)

    if ( lmpi_master ) rms(1:nb) = sqrt( rms(1:nb) / real( dofg, dp ) )

    call lmpi_bcast(rms)
    call lmpi_bcast(inf)

  end subroutine array_norms

!============================= ARRAY_INF_LOCATION_R4 =========================80
!
! Location of infinity norm of array.
!
!=============================================================================80
  subroutine array_inf_location_r4( site, fl, nb, dof0, array, m2g, &
                                    x, y, z, slen )

    integer,                  intent(in) :: fl, nb, dof0
    real(r4), dimension(:,:), intent(in) :: array
    real(dp), dimension(:),   intent(in) :: x, y, z, slen
    integer,  dimension(:),   intent(in) :: m2g
    character(len=*),         intent(in) :: site

    integer :: dofm, eq, maxid, eq_loc, dof_loc, dofm_loc

!BeginNeverComplex
    real(dp) :: inf
!EndNeverComplex

    real(dp), dimension(4) :: rloc

  continue

    inf = -huge(1._dp)
    do dofm = 1, dof0

       do eq=1,nb
         if ( real( abs( array(eq,dofm) ), dp )  < real( inf, dp ) ) cycle
         eq_loc  = eq
         dofm_loc = dofm
         inf     = abs( array(eq,dofm) )
       enddo

    enddo

    dof_loc = m2g(dofm_loc)

    rloc(1) =    x(dof_loc)
    rloc(2) =    y(dof_loc)
    rloc(3) =    z(dof_loc)
    rloc(4) = slen(dof_loc)

    call lmpi_max_and_maxid( real(inf,dp), maxid )
    call lmpi_bcast(     inf, maxid)
    call lmpi_bcast(  eq_loc, maxid)
    call lmpi_bcast(dofm_loc, maxid)
    call lmpi_bcast( dof_loc, maxid)
    call lmpi_bcast(    rloc, maxid)

    if ( lmpi_master ) then
      write(*,*)
      write(*,"(1x,a,2(a,i0),a,e20.10,4(a,i5))") site,' fl=',fl,&
      ' nb=',nb,'  Max value=',inf,' eq=',eq_loc,               &
      ' dof:g=',dof_loc,' dof:m=',dofm_loc,' pid=',maxid
      write(*,"(1x,a,3f20.12,a,f20.12)") &
      ' .......x/y/z=',rloc(1:3),' slen=',rloc(4)
    endif

  end subroutine array_inf_location_r4

!============================= ARRAY_INF_LOCATION_R8 =========================80
!
! Location of infinity norm of array.
!
!=============================================================================80
  subroutine array_inf_location_r8( site, fl, nb, dof0, array, m2g, &
                                    x, y, z, slen )

    integer,                   intent(in) :: fl, nb, dof0
    real(r8),  dimension(:,:), intent(in) :: array
    real(dp),  dimension(:),   intent(in) :: x, y, z, slen
    integer,   dimension(:),   intent(in) :: m2g
    character(len=*),          intent(in) :: site

    integer :: dofm, eq, maxid, eq_loc, dof_loc, dofm_loc

!BeginNeverComplex
    real(dp) :: inf
!EndNeverComplex

    real(dp), dimension(4) :: rloc

  continue

    inf = -huge(1._dp)
    do dofm = 1, dof0

       do eq=1,nb
         if ( real( abs( array(eq,dofm) ), dp )  < real( inf, dp ) ) cycle
         eq_loc  = eq
         dofm_loc = dofm
         inf     = abs( array(eq,dofm) )
       enddo

    enddo

    dof_loc = m2g(dofm_loc)

    rloc(1) =    x(dof_loc)
    rloc(2) =    y(dof_loc)
    rloc(3) =    z(dof_loc)
    rloc(4) = slen(dof_loc)

    call lmpi_max_and_maxid( real(inf,dp), maxid )
    call lmpi_bcast(     inf, maxid)
    call lmpi_bcast(  eq_loc, maxid)
    call lmpi_bcast(dofm_loc, maxid)
    call lmpi_bcast( dof_loc, maxid)
    call lmpi_bcast(    rloc, maxid)

    if ( lmpi_master ) then
      write(*,*)
      write(*,"(1x,a,2(a,i0),a,e20.10,4(a,i5))") site,' fl=',fl,&
      ' nb=',nb,' Max value=',inf,' eq=',eq_loc,                &
      ' dof:g=',dof_loc,' dof:m=',dofm_loc,' pid=',maxid
      write(*,"(1x,a,3f20.12,a,f20.12)") &
      ' .......x/y/z=',rloc(1:3),' slen=',rloc(4)
    endif

  end subroutine array_inf_location_r8

end module matrix_monitor

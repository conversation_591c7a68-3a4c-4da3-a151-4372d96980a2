module cfl_defs

  use kinddefs,           only : dp
  use fun3d_maximums,     only : ngrid_max

  implicit none

  private

  public :: hanim, hanim_projection, divergence_checking_preconditioner
  public :: cfl_m_frechet,  cfl_t_frechet,  cfl_m_max_level
  public :: cfl_m_jacobian, cfl_t_jacobian, cfl_t_max_level
  public :: cfl_m_w_control, cfl_t_w_control
  public :: nonlinear_control_via_rt

  public :: cfl_ramp_on_restart, cfl_ramp_on_fmg

  public :: amut_max

  public :: w_q_positive_m, w_q_positive_t
  public :: w_rt_m, w_rt_t
  public :: w_m, w_t, w_m_s, w_t_s
  public :: n_outer_searches

  public :: reduce_cfl_m, reduce_cfl_t               !reducing cfl
  public :: freeze_cfl_m, freeze_cfl_t               !freezing cfl
  public :: updated_jacobian_m, updated_jacobian_t   !tracking Jacobian updates
  public :: refresh_jacobian_m, refresh_jacobian_t   !refreshing Jacobians
  public :: faulty_discretization_m, faulty_discretization_t

  public :: relaxation_logfile, relaxation_logunit

  public :: cfl_m_max, cfl_m_nominal
  public :: cfl_t_max, cfl_t_nominal
  public :: cfl_increase, cfl_decrease
  public :: ramping_ntt_interval
  public :: cfl_m_max_decrease, cfl_t_max_decrease
  public :: onecfl
  public :: linear_system_target_m, preconditioner_target_m
  public :: linear_system_target_t, preconditioner_target_t
  public :: preconditioner_target_m_max
  public :: preconditioner_target_t_max
  public :: subiterations_min_m, subiterations_max_m
  public :: subiterations_min_t, subiterations_max_t
  public :: f_allow_change_m, f_allow_minimum_m

  public :: cfl1, cfl2, cflturb1, cflturb2, cfl_floor
  public :: iramp, previous_fas_cycles

  public :: cnld_positive_diagonal_warnings
  public :: turbulence_diagonal_fixups
  public :: turbulence_diagonal_cfl_equiv
  public :: fraction_negative_s_diag
  public :: nonlinear_control_method
  public :: turb_var_neg_max
  public :: face_realizability
  public :: unrealizable, edge_type, realization, realization_hits
  public :: efix_cfl, jacobian_aggression
  public :: preconditioner_cr_to_reduce_cfl, &
            preconditioner_cr_to_disallow,   &
                    linear_cr_to_reduce_cfl, &
                 nonlinear_cr_to_disallow,   &
                 nonlinear_cr_to_reduce_cfl
  public :: cycles_at_maxcfl_allowed

  logical :: hanim, divergence_checking_preconditioner
  integer :: hanim_projection
  real(dp) :: efix_cfl
  integer :: jacobian_aggression, cycles_at_maxcfl_allowed
  integer :: nonlinear_control_method
  integer :: iramp = 0

  real(dp) :: cfl1      = 1.0_dp  ! Starting CFL number
  real(dp) :: cfl2      = 1.0_dp  ! Ending CFL number
  real(dp) :: cflturb1  = 1.0_dp  ! Starting turbulence model CFL number
  real(dp) :: cflturb2  = 1.0_dp  ! Ending turbulence model CFL number
  real(dp) :: cfl_floor = 1.0_dp  ! Floor for adaptive CFL

  real(dp), dimension(ngrid_max) :: cfl_m_frechet  = 1._dp
  real(dp), dimension(ngrid_max) :: cfl_m_jacobian =-1._dp
  real(dp), dimension(ngrid_max) :: cfl_t_frechet  = 1._dp
  real(dp), dimension(ngrid_max) :: cfl_t_jacobian =-1._dp

  real(dp), dimension(ngrid_max) :: cfl_m_max_level = 1._dp
  real(dp), dimension(ngrid_max) :: cfl_t_max_level = 1._dp

  logical :: cfl_m_w_control = .true.
  logical :: cfl_t_w_control = .true.
  logical :: nonlinear_control_via_rt
  logical :: onecfl        = .false.

  real(dp) :: f_allow_change_m = 0.20_dp, f_allow_minimum_m = 0.01_dp

  real(dp) :: amut_max = 0._dp

  real(dp) :: w_q_positive_m = 1._dp, w_q_positive_t = 1._dp
  real(dp) :: w_rt_m         = 1._dp, w_rt_t         = 1._dp

  real(dp), dimension(ngrid_max) :: w_m   = 1._dp
  real(dp), dimension(ngrid_max) :: w_t   = 1._dp
  real(dp), dimension(ngrid_max) :: w_m_s = 1._dp
  real(dp), dimension(ngrid_max) :: w_t_s = 1._dp

  integer :: n_outer_searches =  3

  logical, dimension(ngrid_max) :: reduce_cfl_m = .false.
  logical, dimension(ngrid_max) :: reduce_cfl_t = .false.
  logical, dimension(ngrid_max) :: freeze_cfl_m = .false.
  logical, dimension(ngrid_max) :: freeze_cfl_t = .false.
  logical, dimension(ngrid_max) :: refresh_jacobian_m = .false.
  logical, dimension(ngrid_max) :: refresh_jacobian_t = .false.
  integer, dimension(ngrid_max) :: updated_jacobian_m
  integer, dimension(ngrid_max) :: updated_jacobian_t
  integer, dimension(ngrid_max) :: faulty_discretization_m = 0
  integer, dimension(ngrid_max) :: faulty_discretization_t = 0

  character (len=21) :: relaxation_logfile = 'relaxation_log_linear'
  integer :: relaxation_logunit

  real(dp) :: preconditioner_target_m
  real(dp) :: preconditioner_target_t
  real(dp) :: linear_system_target_m
  real(dp) :: linear_system_target_t
  real(dp) :: preconditioner_target_m_max = 0.01_dp
  real(dp) :: preconditioner_target_t_max = 0.01_dp

  integer  :: subiterations_min_m, subiterations_max_m
  integer  :: subiterations_min_t, subiterations_max_t

  real(dp) :: cfl_m_max, cfl_t_max

  real(dp) :: cfl_m_nominal = 200._dp, cfl_t_nominal = 300._dp

  real(dp) :: cfl_increase
  real(dp) :: cfl_decrease
  real(dp) :: cfl_m_max_decrease
  real(dp) :: cfl_t_max_decrease

  integer  :: ramping_ntt_interval  = 10

  logical :: cfl_ramp_on_restart = .true.
  logical :: cfl_ramp_on_fmg     = .true.

  integer  :: previous_fas_cycles = 0

  integer,  dimension(ngrid_max) :: turbulence_diagonal_fixups = 0
  real(dp), dimension(ngrid_max) :: turbulence_diagonal_cfl_equiv = 0._dp
  real(dp), dimension(ngrid_max) :: fraction_negative_s_diag = 0._dp

  ! Purely diagnostic warnings when for turbulence, the LHS diagonal is zero
  ! after forming convection and nonlinear diffusion linearizations.
  integer,  dimension(ngrid_max) :: cnld_positive_diagonal_warnings = 0

  real(dp) :: turb_var_neg_max = 0._dp

  logical :: face_realizability = .false.
  type edge_type
    integer,  dimension(:), allocatable :: edge_counter
  end type
  type(edge_type), dimension(:),   allocatable :: unrealizable
  logical :: realization
  integer :: realization_hits = 20

  real(dp) :: preconditioner_cr_to_reduce_cfl, &
              preconditioner_cr_to_disallow,   &
                      linear_cr_to_reduce_cfl, &
                   nonlinear_cr_to_disallow,   &
                   nonlinear_cr_to_reduce_cfl

end module cfl_defs

# Makefile.in generated by automake 1.11.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006, 2007, 2008, 2009  Free Software Foundation,
# Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
target_triplet = @target@
DIST_COMMON = $(srcdir)/../Common.am $(srcdir)/Makefile.am \
	$(srcdir)/Makefile.in $(top_srcdir)/make.rules
@BUILD_CUDA_SUPPORT_TRUE@am__append_1 = \
@BUILD_CUDA_SUPPORT_TRUE@	cuda_point_solve_mpi.cu

@BUILD_CGNS_SUPPORT_TRUE@am__append_2 = $(FC_MODINC)@CGNSinclude@
@BUILD_PUNDIT_SUPPORT_TRUE@am__append_3 = $(FC_MODINC)@punditinclude@
@BUILD_VISIT_SUPPORT_TRUE@am__append_4 = $(FC_MODINC)@VisItinclude@
subdir = LibF90/Complex
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps =  \
	$(top_srcdir)/aclocal/ax_f90_module_extension.m4 \
	$(top_srcdir)/aclocal/ax_f90_module_flag.m4 \
	$(top_srcdir)/aclocal/capri.m4 $(top_srcdir)/aclocal/cgns.m4 \
	$(top_srcdir)/aclocal/cuda.m4 \
	$(top_srcdir)/aclocal/dynamic_loading.m4 \
	$(top_srcdir)/aclocal/f90_tuner.m4 \
	$(top_srcdir)/aclocal/f90_unix.m4 \
	$(top_srcdir)/aclocal/fccht.m4 \
	$(top_srcdir)/aclocal/fortran_2003_environment.m4 \
	$(top_srcdir)/aclocal/fortran_asynchronous_io.m4 \
	$(top_srcdir)/aclocal/fortran_c_interoperability.m4 \
	$(top_srcdir)/aclocal/fortran_etime.m4 \
	$(top_srcdir)/aclocal/fortran_open_big_endian.m4 \
	$(top_srcdir)/aclocal/fortran_open_stream.m4 \
	$(top_srcdir)/aclocal/fortran_posix_interface.m4 \
	$(top_srcdir)/aclocal/fun3d.m4 $(top_srcdir)/aclocal/gsi.m4 \
	$(top_srcdir)/aclocal/meshsim.m4 $(top_srcdir)/aclocal/mpi.m4 \
	$(top_srcdir)/aclocal/parmetis.m4 \
	$(top_srcdir)/aclocal/resource_limit.m4 \
	$(top_srcdir)/aclocal/zoltan.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
LIBRARIES = $(noinst_LIBRARIES)
AR = ar
ARFLAGS = cru
libsink_a_AR = $(AR) $(ARFLAGS)
libsink_a_LIBADD =
@BUILD_CUDA_SUPPORT_TRUE@am__objects_1 =  \
@BUILD_CUDA_SUPPORT_TRUE@	cuda_point_solve_mpi.$(OBJEXT)
am__objects_2 = adaptation_parameter.$(OBJEXT) adjust_alphas.$(OBJEXT) \
	aeroelastic.$(OBJEXT) amut.$(OBJEXT) amut_cc.$(OBJEXT) \
	analysis.$(OBJEXT) array_check.$(OBJEXT) averagings.$(OBJEXT) \
	bc_cache_cc.$(OBJEXT) bc_cc.$(OBJEXT) bc_cc_i.$(OBJEXT) \
	bc_element_based.$(OBJEXT) bc_element_based_i.$(OBJEXT) \
	bc_inviscid.$(OBJEXT) bc_state.$(OBJEXT) bc_state_i.$(OBJEXT) \
	bc_strong.$(OBJEXT) bc_strong_nc.$(OBJEXT) bc_util.$(OBJEXT) \
	bc_wallfun.$(OBJEXT) bcc_util.$(OBJEXT) \
	big_angle_set.$(OBJEXT) block_solver.$(OBJEXT) \
	boundary_slicing.$(OBJEXT) boundary_writes_cc.$(OBJEXT) \
	bspline.$(OBJEXT) cell_reynolds.$(OBJEXT) cgamma_cc.$(OBJEXT) \
	cgamma_data.$(OBJEXT) cgamma_nc.$(OBJEXT) \
	cgamma_util.$(OBJEXT) cgns_interface.$(OBJEXT) \
	check_discretization.$(OBJEXT) check_face_lsq.$(OBJEXT) \
	check_lisbon.$(OBJEXT) check_mapped_lsq.$(OBJEXT) \
	check_sa.$(OBJEXT) check_solves.$(OBJEXT) \
	check_symmetry.$(OBJEXT) choleski_lapack.$(OBJEXT) \
	coloring_cc.$(OBJEXT) command_line_parser.$(OBJEXT) \
	component_performance.$(OBJEXT) comprow.$(OBJEXT) \
	comprow_util.$(OBJEXT) convection_flux.$(OBJEXT) \
	crinkle_cut.$(OBJEXT) csd_coupling.$(OBJEXT) \
	custom_transforms.$(OBJEXT) cut_cell.$(OBJEXT) \
	cut_gradient.$(OBJEXT) cut_limiters.$(OBJEXT) \
	cut_sensitivities.$(OBJEXT) cut_utils.$(OBJEXT) \
	cut_visualizations.$(OBJEXT) dcif.$(OBJEXT) \
	ddf_binary.$(OBJEXT) debug_jacobian.$(OBJEXT) \
	debug_verify.$(OBJEXT) deconstruction.$(OBJEXT) \
	design_io_helpers.$(OBJEXT) designs.$(OBJEXT) \
	dirtlib.$(OBJEXT) distance_function.$(OBJEXT) \
	dual_numbering.$(OBJEXT) dymore_interface.$(OBJEXT) \
	element_based_bc_util.$(OBJEXT) engine_interface.$(OBJEXT) \
	engine_sims.$(OBJEXT) eqn_groups.$(OBJEXT) \
	equiv_area.$(OBJEXT) exact.$(OBJEXT) exact_airfoil.$(OBJEXT) \
	exact_cylinder.$(OBJEXT) exact_cylinder_viscous.$(OBJEXT) \
	exact_ext_i.$(OBJEXT) exact_lisbon_backstep.$(OBJEXT) \
	exact_lisbon_ms1.$(OBJEXT) exact_lisbon_ms2.$(OBJEXT) \
	exact_lisbon_ms4.$(OBJEXT) exact_simple.$(OBJEXT) \
	exact_smith_hutton.$(OBJEXT) exact_source.$(OBJEXT) \
	exact_sphere.$(OBJEXT) exact_supersonic_vortex.$(OBJEXT) \
	exact_util.$(OBJEXT) fill_jacobians_cc.$(OBJEXT) \
	finite_element_interp.$(OBJEXT) flow_initialization.$(OBJEXT) \
	flsq_lu.$(OBJEXT) flsq_stencil.$(OBJEXT) flux_bc.$(OBJEXT) \
	flux_constants.$(OBJEXT) flux_fds.$(OBJEXT) \
	flux_fds_aj.$(OBJEXT) flux_fds_cc_i.$(OBJEXT) \
	flux_forces.$(OBJEXT) flux_functions.$(OBJEXT) \
	flux_fvs.$(OBJEXT) flux_jacob_kw_bc_cc.$(OBJEXT) \
	flux_jacob_kw_cc.$(OBJEXT) flux_jacob_sa_bc_cc.$(OBJEXT) \
	flux_jacob_sa_cc.$(OBJEXT) flux_kw_bc_cc.$(OBJEXT) \
	flux_kw_cc.$(OBJEXT) flux_ldfs.$(OBJEXT) \
	flux_lowmach_prec.$(OBJEXT) flux_nc.$(OBJEXT) \
	flux_nc_i.$(OBJEXT) flux_nc_opt0.$(OBJEXT) \
	flux_nc_opt1.$(OBJEXT) flux_nc_opt2.$(OBJEXT) \
	flux_perfgas.$(OBJEXT) flux_sa_bc_cc.$(OBJEXT) \
	flux_sa_cc.$(OBJEXT) flux_symmetry.$(OBJEXT) \
	flux_util.$(OBJEXT) force_helper.$(OBJEXT) forces.$(OBJEXT) \
	forces_cc.$(OBJEXT) forces_hinge.$(OBJEXT) \
	fsi_coupling.$(OBJEXT) fsi_coupling_tight.$(OBJEXT) \
	gcr_util.$(OBJEXT) geometry_utils.$(OBJEXT) \
	global_file_interface.$(OBJEXT) global_image.$(OBJEXT) \
	gradient_driver.$(OBJEXT) grid_converter.$(OBJEXT) \
	grid_metrics.$(OBJEXT) grid_motion_helpers.$(OBJEXT) \
	grid_symmetry_cc.$(OBJEXT) grids.$(OBJEXT) ilu.$(OBJEXT) \
	implicit_lines.$(OBJEXT) input_sanity.$(OBJEXT) \
	internal_flow_fcns.$(OBJEXT) interpolate_utilities.$(OBJEXT) \
	io.$(OBJEXT) jacobian.$(OBJEXT) jacobian_bc_cc.$(OBJEXT) \
	jacobian_bc_cc_i.$(OBJEXT) jacobian_mean_util.$(OBJEXT) \
	jacobian_turb_util.$(OBJEXT) jacobian_util.$(OBJEXT) \
	jacobian_viscous_i.$(OBJEXT) jacobians_cc.$(OBJEXT) \
	jacobians_cc_i.$(OBJEXT) kdtree.$(OBJEXT) \
	knife_interface.$(OBJEXT) knife_utils.$(OBJEXT) \
	limiter_functions.$(OBJEXT) line_adaptation.$(OBJEXT) \
	line_algebraic_turb.$(OBJEXT) line_jacobi_solver.$(OBJEXT) \
	line_lu_ddq.$(OBJEXT) line_nsolver.$(OBJEXT) \
	line_solver_ddq.$(OBJEXT) linear_decompositions.$(OBJEXT) \
	linear_projections.$(OBJEXT) linear_spectral.$(OBJEXT) \
	linearsolve.$(OBJEXT) linearsolve_nodivcheck.$(OBJEXT) \
	load_balance.$(OBJEXT) local_grid.$(OBJEXT) lsq.$(OBJEXT) \
	lsq_augment_cloud.$(OBJEXT) lsq_cc.$(OBJEXT) \
	lsq_cc_test.$(OBJEXT) lsq_nc.$(OBJEXT) lsq_util.$(OBJEXT) \
	massoud.$(OBJEXT) matrix_monitor.$(OBJEXT) \
	meshsim_grid_adapter.$(OBJEXT) meshsim_interface.$(OBJEXT) \
	monitor.$(OBJEXT) move_mixed.$(OBJEXT) moves.$(OBJEXT) \
	multiblocks.$(OBJEXT) nearest_neighbor.$(OBJEXT) \
	nml_exact.$(OBJEXT) node_avg_cc.$(OBJEXT) \
	noninertials.$(OBJEXT) overset.$(OBJEXT) \
	parallel_edge_swap.$(OBJEXT) parallel_embed.$(OBJEXT) \
	parmetis_interface.$(OBJEXT) party_lmpi.$(OBJEXT) \
	periodics.$(OBJEXT) point_lu_ddq.$(OBJEXT) \
	point_solver.$(OBJEXT) point_solver_ddq.$(OBJEXT) \
	porous_model.$(OBJEXT) porous2.$(OBJEXT) pparty.$(OBJEXT) \
	pparty_computes.$(OBJEXT) pparty_io.$(OBJEXT) \
	pparty_metis.$(OBJEXT) pparty_metis_lines_revised.$(OBJEXT) \
	pparty_mixed_element.$(OBJEXT) pparty_preprocessor.$(OBJEXT) \
	pparty_puns3d.$(OBJEXT) preconditioners.$(OBJEXT) \
	pressure_gradient_src.$(OBJEXT) profiles.$(OBJEXT) \
	projection_distance.$(OBJEXT) puns3d_checker.$(OBJEXT) \
	puns3d_io_c2n.$(OBJEXT) pydata.$(OBJEXT) \
	quadrature_rules.$(OBJEXT) ray_tracing_utilities.$(OBJEXT) \
	read_flow_input.$(OBJEXT) reconstruction.$(OBJEXT) \
	reconstruction_cc.$(OBJEXT) reference_q.$(OBJEXT) \
	refine_grid_adapter.$(OBJEXT) refine_interface.$(OBJEXT) \
	rotor_motion.$(OBJEXT) rotors.$(OBJEXT) rtiming.$(OBJEXT) \
	sampling_funclib.$(OBJEXT) sampling_gather.$(OBJEXT) \
	sampling_headers.$(OBJEXT) sampling_main.$(OBJEXT) \
	sampling_output.$(OBJEXT) sampling_templates.$(OBJEXT) \
	scalar_diffusion.$(OBJEXT) scratch_q.$(OBJEXT) \
	search.$(OBJEXT) sensitivity.$(OBJEXT) sfe_interface.$(OBJEXT) \
	sixdof.$(OBJEXT) skewness.$(OBJEXT) solution_adj.$(OBJEXT) \
	solution_getg.$(OBJEXT) solution_globals.$(OBJEXT) \
	solution_io_helpers.$(OBJEXT) solution_writes.$(OBJEXT) \
	solve_box.$(OBJEXT) solver_data.$(OBJEXT) source.$(OBJEXT) \
	source_gen.$(OBJEXT) split_comm.$(OBJEXT) \
	split_element.$(OBJEXT) ssdc_interface.$(OBJEXT) \
	structured_lines.$(OBJEXT) suggar.$(OBJEXT) \
	suggar_interface.$(OBJEXT) tecplot_io_helpers.$(OBJEXT) \
	test_reconstruction.$(OBJEXT) thermo.$(OBJEXT) \
	timeacc.$(OBJEXT) timeacc_coeffs.$(OBJEXT) timestep.$(OBJEXT) \
	timestep_cc.$(OBJEXT) timestep_gen.$(OBJEXT) \
	trig_utils.$(OBJEXT) turb_2eqn.$(OBJEXT) \
	turb_2eqn_routines.$(OBJEXT) turb_3eqn.$(OBJEXT) \
	turb_4eqn.$(OBJEXT) turb_7eqn.$(OBJEXT) turb_bc_nc.$(OBJEXT) \
	turb_convection.$(OBJEXT) turb_diffusion.$(OBJEXT) \
	turb_diffusion5.$(OBJEXT) turb_diffusion6.$(OBJEXT) \
	turb_hrles.$(OBJEXT) turb_ke.$(OBJEXT) turb_main.$(OBJEXT) \
	turb_ras_2011.$(OBJEXT) turb_sa_2012.$(OBJEXT) \
	turb_spalart.$(OBJEXT) turb_util.$(OBJEXT) \
	unravel_interface.$(OBJEXT) visit.$(OBJEXT) \
	vortex_generators.$(OBJEXT) wall_model.$(OBJEXT) \
	wall_model_main.$(OBJEXT) $(am__objects_1) \
	complex_oldschool.$(OBJEXT)
nodist_libsink_a_OBJECTS = $(am__objects_2)
libsink_a_OBJECTS = $(nodist_libsink_a_OBJECTS)
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
PPFCCOMPILE = $(FC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_FCFLAGS) $(FCFLAGS)
FCLD = $(FC)
FCLINK = $(FCLD) $(AM_FCFLAGS) $(FCFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o \
	$@
FCCOMPILE = $(FC) $(AM_FCFLAGS) $(FCFLAGS)
SOURCES = $(nodist_libsink_a_SOURCES)
DIST_SOURCES =
ETAGS = etags
CTAGS = ctags
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
ACLOCAL_AMFLAGS = @ACLOCAL_AMFLAGS@
AMTAR = @AMTAR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BIBTEX = @BIBTEX@
CAPRIheader = @CAPRIheader@
CAPRIlibrary = @CAPRIlibrary@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CGNSinclude = @CGNSinclude@
CGNSlibrary = @CGNSlibrary@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CUDACC = @CUDACC@
CUDAFLAGS = @CUDAFLAGS@
CUDA_LIB_PATH = @CUDA_LIB_PATH@
CXX = @CXX@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
F90_EXT_LIB = @F90_EXT_LIB@
FC = @FC@
FCFLAGS = @FCFLAGS@
FCLIBS = @FCLIBS@
FC_MODEXT = @FC_MODEXT@
FC_MODINC = @FC_MODINC@
GREP = @GREP@
HAVE_F2PY = @HAVE_F2PY@
HAVE_RUBY = @HAVE_RUBY@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
KNIFE_SUBDIR = @KNIFE_SUBDIR@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
MOD_DEP_COMPILER = @MOD_DEP_COMPILER@
MPIF90 = @MPIF90@
MPIINC = @MPIINC@
MPIRUN = @MPIRUN@
MPI_EXT = @MPI_EXT@
MPI_Prefix = @MPI_Prefix@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PDFLATEX = @PDFLATEX@
PERL5 = @PERL5@
PHYSICS_TYPE = @PHYSICS_TYPE@
PYTHON = @PYTHON@
PYTHON_EXEC_PREFIX = @PYTHON_EXEC_PREFIX@
PYTHON_PLATFORM = @PYTHON_PLATFORM@
PYTHON_PREFIX = @PYTHON_PREFIX@
PYTHON_SUBDIR = @PYTHON_SUBDIR@
PYTHON_VERSION = @PYTHON_VERSION@
RANLIB = @RANLIB@
REFINE_SUBDIR = @REFINE_SUBDIR@
SBOOMlibrary = @SBOOMlibrary@
SDKheader = @SDKheader@
SDKlibrary = @SDKlibrary@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SIXDOFLIBS = @SIXDOFLIBS@
STRIP = @STRIP@
TECIOLIBS = @TECIOLIBS@
VERSION = @VERSION@
VisItinclude = @VisItinclude@
VisItlibrary = @VisItlibrary@
XMKMF = @XMKMF@
Xheader = @Xheader@
Xlibrary = @Xlibrary@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_FC = @ac_ct_FC@
ac_empty = @ac_empty@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
dirtlibrary = @dirtlibrary@
docdir = @docdir@
dotlibrary = @dotlibrary@
dvidir = @dvidir@
dymorelibrary = @dymorelibrary@
exec_prefix = @exec_prefix@
fcompiler = @fcompiler@
have_bibtex = @have_bibtex@
have_latex = @have_latex@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
irslibrary = @irslibrary@
knife_deps = @knife_deps@
knife_ldadd = @knife_ldadd@
ksoptlibrary = @ksoptlibrary@
libcore_path = @libcore_path@
libdir = @libdir@
libexecdir = @libexecdir@
libturb_path = @libturb_path@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
meshsim_ldadd = @meshsim_ldadd@
mkdir_p = @mkdir_p@
mpi_ldadd = @mpi_ldadd@
npsollibrary = @npsollibrary@
oldincludedir = @oldincludedir@
parmetis_include = @parmetis_include@
parmetis_ldadd = @parmetis_ldadd@
pdfdir = @pdfdir@
pkgpyexecdir = @pkgpyexecdir@
pkgpythondir = @pkgpythondir@
portlibrary = @portlibrary@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
punditinclude = @punditinclude@
punditlibrary = @punditlibrary@
pyexecdir = @pyexecdir@
pythondir = @pythondir@
refine_deps = @refine_deps@
refine_ldadd = @refine_ldadd@
sbindir = @sbindir@
sdxlibrary = @sdxlibrary@
sfelibrary = @sfelibrary@
sharedstatedir = @sharedstatedir@
snoptlibrary = @snoptlibrary@
sparskitlibrary = @sparskitlibrary@
srcdir = @srcdir@
ssdclibrary = @ssdclibrary@
subdirs = @subdirs@
suggarlibrary = @suggarlibrary@
sysconfdir = @sysconfdir@
target = @target@
target_alias = @target_alias@
target_cpu = @target_cpu@
target_os = @target_os@
target_vendor = @target_vendor@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
zoltan_include = @zoltan_include@
zoltan_ldadd = @zoltan_ldadd@
libsink_SRCS = adaptation_parameter.f90 adjust_alphas.f90 \
	aeroelastic.F90 amut.f90 amut_cc.f90 analysis.f90 \
	array_check.f90 averagings.f90 bc_cache_cc.f90 bc_cc.f90 \
	bc_cc_i.f90 bc_element_based.f90 bc_element_based_i.f90 \
	bc_inviscid.f90 bc_state.f90 bc_state_i.f90 bc_strong.f90 \
	bc_strong_nc.f90 bc_util.f90 bc_wallfun.f90 bcc_util.f90 \
	big_angle_set.f90 block_solver.f90 boundary_slicing.f90 \
	boundary_writes_cc.f90 bspline.f90 cell_reynolds.f90 \
	cgamma_cc.f90 cgamma_data.f90 cgamma_nc.f90 cgamma_util.f90 \
	cgns_interface.F90 check_discretization.f90 check_face_lsq.f90 \
	check_lisbon.f90 check_mapped_lsq.f90 check_sa.f90 \
	check_solves.f90 check_symmetry.f90 choleski_lapack.f90 \
	coloring_cc.f90 command_line_parser.F90 \
	component_performance.f90 comprow.f90 comprow_util.f90 \
	convection_flux.f90 crinkle_cut.f90 csd_coupling.F90 \
	custom_transforms.f90 cut_cell.f90 cut_gradient.f90 \
	cut_limiters.f90 cut_sensitivities.f90 cut_utils.f90 \
	cut_visualizations.f90 dcif.f90 ddf_binary.F90 \
	debug_jacobian.f90 debug_verify.f90 deconstruction.f90 \
	design_io_helpers.f90 designs.f90 dirtlib.F90 \
	distance_function.f90 dual_numbering.f90 dymore_interface.f90 \
	element_based_bc_util.f90 engine_interface.F90 engine_sims.f90 \
	eqn_groups.f90 equiv_area.f90 exact.f90 exact_airfoil.f90 \
	exact_cylinder.f90 exact_cylinder_viscous.f90 exact_ext_i.f90 \
	exact_lisbon_backstep.f90 exact_lisbon_ms1.f90 \
	exact_lisbon_ms2.f90 exact_lisbon_ms4.f90 exact_simple.f90 \
	exact_smith_hutton.f90 exact_source.f90 exact_sphere.f90 \
	exact_supersonic_vortex.f90 exact_util.f90 \
	fill_jacobians_cc.f90 finite_element_interp.f90 \
	flow_initialization.f90 flsq_lu.f90 flsq_stencil.f90 \
	flux_bc.f90 flux_constants.f90 flux_fds.f90 flux_fds_aj.f90 \
	flux_fds_cc_i.f90 flux_forces.f90 flux_functions.f90 \
	flux_fvs.f90 flux_jacob_kw_bc_cc.f90 flux_jacob_kw_cc.f90 \
	flux_jacob_sa_bc_cc.f90 flux_jacob_sa_cc.f90 flux_kw_bc_cc.f90 \
	flux_kw_cc.f90 flux_ldfs.f90 flux_lowmach_prec.f90 flux_nc.f90 \
	flux_nc_i.f90 flux_nc_opt0.f90 flux_nc_opt1.f90 \
	flux_nc_opt2.f90 flux_perfgas.f90 flux_sa_bc_cc.f90 \
	flux_sa_cc.f90 flux_symmetry.f90 flux_util.f90 \
	force_helper.f90 forces.f90 forces_cc.f90 forces_hinge.f90 \
	fsi_coupling.f90 fsi_coupling_tight.F90 gcr_util.f90 \
	geometry_utils.f90 global_file_interface.f90 global_image.f90 \
	gradient_driver.f90 grid_converter.f90 grid_metrics.f90 \
	grid_motion_helpers.f90 grid_symmetry_cc.f90 grids.f90 ilu.f90 \
	implicit_lines.f90 input_sanity.F90 internal_flow_fcns.f90 \
	interpolate_utilities.f90 io.f90 jacobian.f90 \
	jacobian_bc_cc.f90 jacobian_bc_cc_i.f90 jacobian_mean_util.f90 \
	jacobian_turb_util.f90 jacobian_util.f90 \
	jacobian_viscous_i.f90 jacobians_cc.f90 jacobians_cc_i.f90 \
	kdtree.f90 knife_interface.F90 knife_utils.f90 \
	limiter_functions.f90 line_adaptation.f90 \
	line_algebraic_turb.f90 line_jacobi_solver.f90 line_lu_ddq.f90 \
	line_nsolver.f90 line_solver_ddq.f90 linear_decompositions.f90 \
	linear_projections.f90 linear_spectral.f90 linearsolve.F90 \
	linearsolve_nodivcheck.F90 load_balance.f90 local_grid.f90 \
	lsq.f90 lsq_augment_cloud.f90 lsq_cc.f90 lsq_cc_test.f90 \
	lsq_nc.f90 lsq_util.f90 massoud.f90 matrix_monitor.f90 \
	meshsim_grid_adapter.F90 meshsim_interface.f90 monitor.f90 \
	move_mixed.f90 moves.f90 multiblocks.f90 nearest_neighbor.f90 \
	nml_exact.f90 node_avg_cc.f90 noninertials.f90 overset.f90 \
	parallel_edge_swap.f90 parallel_embed.f90 \
	parmetis_interface.F90 party_lmpi.f90 periodics.f90 \
	point_lu_ddq.f90 point_solver.F90 point_solver_ddq.F90 \
	porous_model.f90 porous2.f90 pparty.f90 pparty_computes.f90 \
	pparty_io.f90 pparty_metis.F90 pparty_metis_lines_revised.F90 \
	pparty_mixed_element.f90 pparty_preprocessor.f90 \
	pparty_puns3d.f90 preconditioners.f90 \
	pressure_gradient_src.f90 profiles.f90 projection_distance.f90 \
	puns3d_checker.f90 puns3d_io_c2n.f90 pydata.f90 \
	quadrature_rules.f90 ray_tracing_utilities.f90 \
	read_flow_input.f90 reconstruction.f90 reconstruction_cc.f90 \
	reference_q.f90 refine_grid_adapter.F90 refine_interface.f90 \
	rotor_motion.F90 rotors.f90 rtiming.f90 sampling_funclib.f90 \
	sampling_gather.f90 sampling_headers.f90 sampling_main.f90 \
	sampling_output.F90 sampling_templates.f90 \
	scalar_diffusion.f90 scratch_q.f90 search.f90 sensitivity.f90 \
	sfe_interface.F90 sixdof.F90 skewness.f90 solution_adj.f90 \
	solution_getg.f90 solution_globals.f90 solution_io_helpers.f90 \
	solution_writes.F90 solve_box.f90 solver_data.f90 source.f90 \
	source_gen.f90 split_comm.f90 split_element.f90 \
	ssdc_interface.F90 structured_lines.f90 suggar.F90 \
	suggar_interface.f90 tecplot_io_helpers.F90 \
	test_reconstruction.f90 thermo.f90 timeacc.f90 \
	timeacc_coeffs.f90 timestep.f90 timestep_cc.f90 \
	timestep_gen.f90 trig_utils.f90 turb_2eqn.f90 \
	turb_2eqn_routines.f90 turb_3eqn.f90 turb_4eqn.f90 \
	turb_7eqn.f90 turb_bc_nc.f90 turb_convection.f90 \
	turb_diffusion.f90 turb_diffusion5.f90 turb_diffusion6.f90 \
	turb_hrles.f90 turb_ke.f90 turb_main.f90 turb_ras_2011.f90 \
	turb_sa_2012.f90 turb_spalart.f90 turb_util.f90 \
	unravel_interface.F90 visit.F90 vortex_generators.f90 \
	wall_model.f90 wall_model_main.f90 $(am__append_1) \
	complex_oldschool.f90
@BUILD_MPI_FALSE@AM_FCFLAGS = $(FC_MODINC)@top_builddir@ \
@BUILD_MPI_FALSE@	$(FC_MODINC)$(LIBINIT_DIR) \
@BUILD_MPI_FALSE@	$(FC_MODINC)$(LIBDDFB_DIR) \
@BUILD_MPI_FALSE@	$(FC_MODINC)$(LIBSMEMRD_DIR) \
@BUILD_MPI_FALSE@	$(FC_MODINC)$(LIBTURB_DIR) \
@BUILD_MPI_FALSE@	$(FC_MODINC)$(srcdir)/$(LIBTURB_DIR) \
@BUILD_MPI_FALSE@	$(FC_MODINC)$(PHYSICS_DIR) \
@BUILD_MPI_FALSE@	$(FC_MODINC)$(LIBDEFS_DIR) \
@BUILD_MPI_FALSE@	$(FC_MODINC)$(LIBCORE_DIR) \
@BUILD_MPI_FALSE@	$(FC_MODINC)$(srcdir)/$(FUNCLIB_DIR) \
@BUILD_MPI_FALSE@	$(FC_MODINC)$(FUNCLIB_DIR) $(am__append_2) \
@BUILD_MPI_FALSE@	$(am__append_3) $(am__append_4)
@BUILD_MPI_TRUE@AM_FCFLAGS = $(FC_MODINC)@MPIINC@ \
@BUILD_MPI_TRUE@	$(FC_MODINC)@top_builddir@ \
@BUILD_MPI_TRUE@	$(FC_MODINC)$(LIBINIT_DIR) \
@BUILD_MPI_TRUE@	$(FC_MODINC)$(LIBDDFB_DIR) \
@BUILD_MPI_TRUE@	$(FC_MODINC)$(LIBSMEMRD_DIR) \
@BUILD_MPI_TRUE@	$(FC_MODINC)$(LIBTURB_DIR) \
@BUILD_MPI_TRUE@	$(FC_MODINC)$(srcdir)/$(LIBTURB_DIR) \
@BUILD_MPI_TRUE@	$(FC_MODINC)$(PHYSICS_DIR) \
@BUILD_MPI_TRUE@	$(FC_MODINC)$(LIBDEFS_DIR) \
@BUILD_MPI_TRUE@	$(FC_MODINC)$(LIBCORE_DIR) \
@BUILD_MPI_TRUE@	$(FC_MODINC)$(srcdir)/$(FUNCLIB_DIR) \
@BUILD_MPI_TRUE@	$(FC_MODINC)$(FUNCLIB_DIR) $(am__append_2) \
@BUILD_MPI_TRUE@	$(am__append_3) $(am__append_4)

# remove *.mod *.fh when mod_suffix is repaired for OS X
CLEANFILES = *.$(FC_MODEXT) mpif.h *.time *.mod *.fh *.d \
	$(BUILT_SOURCES)
LibF90_SRCS = $(libsink_SRCS)
LibF90_f90s = $(LibF90_SRCS:.F90=.f90)
LibF90_deps = $(LibF90_f90s:.f90=.d)
DISTCLEANFILES = $(LibF90_deps)
SUFFIXES = .d
BUILT_SOURCES = $(LibF90_deps) $(nodist_libsink_a_SOURCES)
lib_MODULES = $(LibF90_f90s:.f90=.$(FC_MODEXT))
LIBCORE_DIR = @libcore_path@/Complex
LIBDEFS_DIR = @top_builddir@/libdefs/Complex
LIBTURB_DIR = @libturb_path@/Complex
LIBSMEMRD_DIR = @top_builddir@/libsmemrd/Complex
LIBDDFB_DIR = @top_srcdir@/libddfb
FUNCLIB_DIR = @top_builddir@/FuncLib90/Complex
PHYSICS_DIR = @top_builddir@/@PHYSICS_TYPE@/Complex
LIBINIT_DIR = @top_builddir@/libinit/Complex
noinst_LIBRARIES = libsink.a
nodist_libsink_a_SOURCES = $(libsink_SRCS)
all: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) all-am

.SUFFIXES:
.SUFFIXES: .d .F90 .cu .f90 .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am $(srcdir)/../Common.am $(top_srcdir)/make.rules $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu LibF90/Complex/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu LibF90/Complex/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstLIBRARIES:
	-test -z "$(noinst_LIBRARIES)" || rm -f $(noinst_LIBRARIES)
libsink.a: $(libsink_a_OBJECTS) $(libsink_a_DEPENDENCIES) 
	-rm -f libsink.a
	$(libsink_a_AR) libsink.a $(libsink_a_OBJECTS) $(libsink_a_LIBADD)
	$(RANLIB) libsink.a

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

.F90.o:
	$(PPFCCOMPILE) -c -o $@ $<

.F90.obj:
	$(PPFCCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.f90.o:
	$(FCCOMPILE) -c -o $@ $<

.f90.obj:
	$(FCCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

ID: $(HEADERS) $(SOURCES) $(LISP) $(TAGS_FILES)
	list='$(SOURCES) $(HEADERS) $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	mkid -fID $$unique
tags: TAGS

TAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	set x; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: CTAGS
CTAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-am
all-am: Makefile $(LIBRARIES) all-local
installdirs:
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-test -z "$(DISTCLEANFILES)" || rm -f $(DISTCLEANFILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
clean: clean-am

clean-am: clean-generic clean-noinstLIBRARIES mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: all check install install-am install-strip

.PHONY: CTAGS GTAGS all all-am all-local check check-am clean \
	clean-generic clean-noinstLIBRARIES ctags distclean \
	distclean-compile distclean-generic distclean-tags distdir dvi \
	dvi-am html html-am info info-am install install-am \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-man install-pdf \
	install-pdf-am install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic pdf pdf-am ps ps-am tags uninstall \
	uninstall-am


-include $(LibF90_deps)
%.o %.$(FC_MODEXT): %.F90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(PPFCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

%.obj %.$(FC_MODEXT): %.F90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(PPFCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

%.o %.$(FC_MODEXT): %.f90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(FCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

%.obj %.$(FC_MODEXT): %.f90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(FCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

all-local:
	@$(top_srcdir)/remove_stalemods.sh $(FC_MODEXT) $(sort $(lib_MODULES))

clean-stalemods:
	@if `/bin/ls *.o > /dev/null 2>&1`; \
	then \
	  for i in *.o; \
	   do \
	     root=`echo "$$i" | sed 's/\.o$$//'`; \
	     if test ! -e "$(srcdir)/$$root.f90" -a ! -e "$(srcdir)/$$root.F90" -a ! -e "$(srcdir)/$$root.c"; \
	     then \
	       echo "Removing $(srcdir)/$$root objects..."; \
	       /bin/rm -f "$$root.o" "$$root.$(FC_MODEXT)" "$$root.fh" "$$root.mod" "$$root.d"; \
	     fi; \
	   done; \
	fi

clean-stalemods-complex:
	@if `/bin/ls *.o > /dev/null 2>&1`; \
	then \
	  for i in *.o; \
	   do \
	     root=`echo "$$i" | sed 's/\.o$$//'`; \
	     if test ! -e "$(srcdir)/../$$root.f90" -a ! -e "$(srcdir)/../$$root.F90" -a ! -e "$(srcdir)/../$$root.c"; \
	     then \
	       echo "Removing $(srcdir)/../$$root objects..."; \
	       /bin/rm -f "$$root.o" "$$root.$(FC_MODEXT)" "$$root.fh" "$$root.mod" "$$root.d" "$$root.f90" "$$root.F90"; \
	     fi; \
	   done; \
	fi

ordered_targets:
	@$(MAKE) clean
	@$(MAKE) -n | grep "^\$(FC)" | sed 's/.*-o /       /' | \
	                            sed 's/\.o .*/\.f90 \\/'

@BUILD_CUDA_SUPPORT_TRUE@.cu.o:
@BUILD_CUDA_SUPPORT_TRUE@	@CUDACC@ -DCUDA $(CUDAFLAGS) -c -o $@ $<

%.f90: $(top_srcdir)/LibF90/%.f90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.F90: $(top_srcdir)/LibF90/%.F90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

#Build Fortran dependencies
%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-L $(FUNCLIB_DIR) \
	-L $(LIBTURB_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-L $(FUNCLIB_DIR) \
	-L $(LIBTURB_DIR) > $@

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

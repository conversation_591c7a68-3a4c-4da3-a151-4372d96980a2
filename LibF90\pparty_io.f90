module pparty_io

  use kinddefs, only : dp
  use lmpi,     only : lmpi_id, lmpi_nproc

  implicit none

  private
  public :: writeme_headvol_sm, writeme_boundary_sm, writeme_cc_sm,            &
            dump_grid, dump_cc
  public :: writeme_vol_level2
! cc
    integer :: ncell_augmentorsg, nface_augmentorsg ! TBD

contains

!========================= WRITEME_HEADER_AND_VOLUME_SM ======================80
!
! Write grid part file header and volume
!
!=============================================================================80

  subroutine writeme_headvol_sm(unit,grid,outformat,nparts)

    use grid_types,           only : grid_type
    use info_depr,            only : cc_primal

    integer,         intent(in) :: unit,nparts
    type(grid_type), intent(in) :: grid
    logical,         intent(in) :: outformat

    character(len=80) :: iformatdesc, rformatdesc, eformatdesc
    real(dp)          :: version
    integer           :: tempcell2, ielem, nnodes1, nedgeg_i4, ncellg_i4

    continue

    iformatdesc = '(i10)'
    rformatdesc = '(f5.2)'
    eformatdesc = '(a10,6i10)'

!   default to version 3 format for now; version 4 only for other than tets

    version = 3.0_dp

    do ielem = 1,grid%nelem
      if (grid%elem(ielem)%type_cell /= 'tet') version = 4.0_dp
    end do

!   Modify version number to check for cc_primal.

    if (grid%cc) version = version + 0.1_dp

    if (outformat) then
      write(unit,*)'beg writeme_header_and_volume'
      write (unit,rformatdesc) version
    else
      write (unit) version
    end if

    if (cc_primal) then
      ncellg_i4 = grid%elem(1)%ncellg
      ncell_augmentorsg = 0 ! computed in solver
      nface_augmentorsg = -1000 ! TBD
      if ( outformat ) then
        write(unit,*)  grid%nface,   grid%ncell0, grid%elem(1)%ncell,          &
                       grid%nbface0, ncellg_i4, grid%nfaceg,                   &
                       grid%nbfaceg,                                           &
                      !grid%ncell_augmentors, grid%nface_augmentors,           &
                       0,                     -1000,                           &
                       ncell_augmentorsg,     nface_augmentorsg, .false.

      else
        write(unit)  grid%nface,   grid%ncell0, grid%elem(1)%ncell,            &
                     grid%nbface0, ncellg_i4, grid%nfaceg,                     &
                     grid%nbfaceg,                                             &
                     0,                     -1000,                             &
                     ncell_augmentorsg,     nface_augmentorsg, .false.
      endif
    end if

!   write basic dimensioning data

    nnodes1 = grid%nnodes01 - grid%nnodes0
    nedgeg_i4 = grid%nedgeg

!   if (int(version) > 3) then
    if (nint(real(version,dp)) > 3) then

      if (outformat) then
        write (unit,iformatdesc)                                               &
             grid%partid,   nparts,                                            &
             grid%nnodesg,  nedgeg_i4,                                         &
             grid%nbound,   grid%nelem,                                        &
             grid%idistfcn,                                                    &
             grid%nnodes0,  nnodes1,         grid%nnodes01,                    &
             grid%nedgeloc, grid%nedge,      grid%nedgeloc_2d
      else
        write (unit)                                                           &
             grid%partid,   nparts,                                            &
             grid%nnodesg,  nedgeg_i4,                                         &
             grid%nbound,   grid%nelem,                                        &
             grid%idistfcn,                                                    &
             grid%nnodes0,  nnodes1,         grid%nnodes01,                    &
             grid%nedgeloc, grid%nedge,      grid%nedgeloc_2d
      end if

    else

      tempcell2 = 0
      ncellg_i4 = grid%elem(1)%ncellg
      if (outformat) then
        write (unit,iformatdesc)                                               &
             grid%partid,   nparts,                                            &
             grid%nnodesg,  ncellg_i4,  nedgeg_i4,                             &
             grid%nbound,                                                      &
             grid%idistfcn,                                                    &
             grid%nnodes0,  nnodes1, grid%nnodes01,                            &
             grid%elem(1)%ncell,   tempcell2,                                  &
             grid%nedgeloc, grid%nedge,                                        &
             1,             1
      else
        write (unit)                                                           &
             grid%partid,   nparts,                                            &
             grid%nnodesg,  ncellg_i4,  nedgeg_i4,                             &
             grid%nbound,                                                      &
             grid%idistfcn,                                                    &
             grid%nnodes0,  nnodes1, grid%nnodes01,                            &
             grid%elem(1)%ncell,    tempcell2,                                 &
             grid%nedgeloc, grid%nedge,                                        &
             1,             1
      end if

    end if

!   write element data (versions higher than 3.0 only)

    if (int(version) > 3)  then

      ncellg_i4 = grid%elem(ielem)%ncellg
      if (outformat) then
        do ielem = 1,grid%nelem
          tempcell2 = 0
          write(unit, eformatdesc)                                             &
                      grid%elem(ielem)%type_cell,                              &
                      grid%elem(ielem)%ncell,                                  &
                      tempcell2,                                               &
                      ncellg_i4,                                               &
                      grid%elem(ielem)%node_per_cell,                          &
                      grid%elem(ielem)%edge_per_cell,                          &
                      grid%elem(ielem)%face_per_cell
        end do
      else
        do ielem = 1,grid%nelem
          tempcell2 = 0
          write(unit)                                                          &
                      grid%elem(ielem)%type_cell,                              &
                      grid%elem(ielem)%ncell,                                  &
                      tempcell2,                                               &
                      ncellg_i4,                                               &
                      grid%elem(ielem)%node_per_cell,                          &
                      grid%elem(ielem)%edge_per_cell,                          &
                      grid%elem(ielem)%face_per_cell
        end do
      end if

    end if

!   write mesh data

    call writeme_vol_level2( unit,                                             &
         grid%nnodes01, grid%ncell01,                                          &
         grid%nedge,    grid%nedgeloc,                                         &
         grid%eptr,                                                            &
         grid%nelem,    grid%elem,                                             &
         grid%x,        grid%y,        grid%z,                                 &
         grid%idistfcn, grid%l2g,      grid%el2g,     outformat,      version)

      if (outformat) write(unit,*)'end writeme_header_and_volume'

  end subroutine writeme_headvol_sm

!========================== WRITEME_VOL_LEVEL2 ===============================80
!
! Write volume information from grid
!
!=============================================================================80

  subroutine writeme_vol_level2(unit,                                          &
                                nnodes, ncell01,                               &
                                nedge, nedgeloc,                               &
                                eptr,                                          &
                                nelem, elem,                                   &
                                x, y, z, idistfcn,                             &
                                l2g, el2g, outformat, version)

    use element_types, only : elem_type
    use info_depr,     only : cc_primal
    use kinddefs,      only : dp, system_i8, system_i4

    use party_lmpi,    only : ppdb_part

    logical,                              intent(in) :: outformat
    integer,                              intent(in) :: unit
    integer,                              intent(in) :: nnodes, ncell01
    integer,                              intent(in) :: nelem
    integer,                              intent(in) :: nedge, nedgeloc
    integer,         dimension(nnodes),   intent(in) :: l2g
    integer(system_i8), dimension(nedge), intent(in) :: el2g
    integer,         dimension(2,nedge),  intent(in) :: eptr
    real(dp),        dimension(nnodes),   intent(in) :: x, y, z
    real(dp),                             intent(in) :: version
    integer,                              intent(in) :: idistfcn
    type(elem_type), dimension(nelem),    intent(in) :: elem

    character(len=80) :: iformatdesc, rformatdesc

    integer  :: i, k, ielem, loop_index
    real(dp) :: my_0

  continue

    my_0 = 0.0_dp

    iformatdesc = '(i10)'
    rformatdesc = '(e20.13)'

!  Write out local-to-global array

    if (outformat) then
      if(ppdb_part) write(unit,*)'beg writeme_vol_level2; l2g'
      write (unit,iformatdesc) (l2g(i),i=1,nnodes)
      if(ppdb_part) write(unit,*)'end writeme_vol_level2; l2g'
    else
      write (unit) (l2g(i),i=1,nnodes)
    end if

!  Write cl2g array

    if (ppdb_part.and.outformat) write(unit,*)'beg writeme_vol_level2; cl2g'
    do ielem = 1,nelem
      call write_cl2g(elem(ielem)%ncell, elem(ielem)%cl2g, unit, outformat)
    end do
    if (ppdb_part.and.outformat) write(unit,*)'end writeme_vol_level2; cl2g'

!  Write out edge local-to-global array

    if (outformat) then
      if (ppdb_part) write(unit,*)'beg writeme_vol_level2; el2g'
      write (unit,iformatdesc) (int(el2g(i),system_i4),i=1,nedge) ! I8 to I4
      if (ppdb_part) write(unit,*)'end writeme_vol_level2; el2g'
    else
      write (unit) (int(el2g(i),system_i4),i=1,nedge)
    end if

!  Write Cell to Node indices

    if (ppdb_part.and.outformat) write(unit,*)'beg writeme_vol_level2; c2n'
    do ielem=1,nelem
      call write_con( elem(ielem)%ncell,                                       &
                      elem(ielem)%node_per_cell,                               &
                      elem(ielem)%c2n, unit, outformat )
    end do
    if (ppdb_part.and.outformat) write(unit,*)'end writeme_vol_level2; c2n'

!  Write Cell to Edge indices

    if (ppdb_part.and.outformat) write(unit,*)'beg writeme_vol_level2; c2e'
    do ielem=1,nelem
      call write_con( elem(ielem)%ncell,                                       &
                      elem(ielem)%edge_per_cell,                               &
                      elem(ielem)%c2e, unit, outformat )
    end do
    if (ppdb_part.and.outformat) write(unit,*)'end writeme_vol_level2; c2e'

!  Write coordinates of grid points

    if (outformat) then
      if (ppdb_part) write(unit,*)'beg writeme_vol_level2; xyz'
      write (unit,rformatdesc) (x(i),i=1,nnodes)
      write (unit,rformatdesc) (y(i),i=1,nnodes)
      write (unit,rformatdesc) (z(i),i=1,nnodes)
      if (ppdb_part) write(unit,*)'end writeme_vol_level2; xyz'
    else
      write (unit) (x(i),i=1,nnodes)
      write (unit) (y(i),i=1,nnodes)
      write (unit) (z(i),i=1,nnodes)
    end if

!  Write median dual volume surrounding each node
!---rtb scheduled for deletion

    if (outformat) then
      if (ppdb_part) write(unit,*)'beg writeme_vol_level2; vol'
      write (unit,rformatdesc) (my_0,i=1,nnodes)
      if (ppdb_part) write(unit,*)'end writeme_vol_level2; vol'
    else
      write (unit) (my_0,i=1,nnodes)
    end if

!  Write node indices for each edge

    if (outformat) then
      if (ppdb_part) write(unit,*)'beg writeme_vol_level2; eptr'
      write (unit,iformatdesc) (eptr(1,k),k=1,nedge),(eptr(2,k),k=1,nedge)
      if (ppdb_part) write(unit,*)'end writeme_vol_level2; eptr'
    else
      write (unit) (eptr(1,k),k=1,nedge),(eptr(2,k),k=1,nedge)
    end if

!  Write out unit normals of dual mesh face (from node 1 to node 2)
!  and the area of the dual mesh face
!---rtb scheduled for deletion

    if (outformat) then
      if (ppdb_part) write(unit,*)'beg writeme_vol_level2; xn'
      write (unit,*)' start xn '
      write (unit,rformatdesc) (my_0,k=1,nedge)
      write (unit,*)' start yn '
      write (unit,rformatdesc) (my_0,k=1,nedge)
      write (unit,*)' start zn '
      write (unit,rformatdesc) (my_0,k=1,nedge)
      write (unit,*)' start ra '
      write (unit,rformatdesc) (my_0,k=1,nedge)
      if (ppdb_part) write(unit,*)'end writeme_vol_level2; xn'
    else
      write (unit) (my_0,k=1,nedge)
      write (unit) (my_0,k=1,nedge)
      write (unit) (my_0,k=1,nedge)
      write (unit) (my_0,k=1,nedge)
    end if

!  Write (dummy) cell and edge colors

    if (ppdb_part.and.outformat) write(unit,*)'beg writeme_vol_level2; ncell'

    if (nint(real(version,dp)) <= 3) then

      if (outformat) then
        write (unit,iformatdesc) elem(1)%ncell
        write (unit,iformatdesc) nedgeloc
      else
        write (unit) elem(1)%ncell
        write (unit) nedgeloc
      end if

    end if

    if (ppdb_part.and.outformat) write(unit,*)'end writeme_vol_level2; ncell'

!  Write out the distance function
!---rtb scheduled for deletion

    if (ppdb_part.and.outformat) write(unit,*)'beg writeme_vol_level2; slen'

    if (idistfcn == 1) then

      loop_index = nnodes
      if ( cc_primal ) loop_index = ncell01

      if (outformat) then

        if (ppdb_part) write(unit,*)'beg writeme_vol_level2; slenslen'
        write(unit,rformatdesc) (my_0,i=1,loop_index)
        if (ppdb_part) write(unit,*)'end writeme_vol_level2; slenslen'

        if (ppdb_part) write(unit,*)'beg writeme_vol_level2; sleniflag'
        write(unit,iformatdesc) (0,i=1,loop_index)
        if (ppdb_part) write(unit,*)'end writeme_vol_level2; sleniflag'

        if (ppdb_part) write(unit,*)'beg writeme_vol_level2; slenielem'
        write(unit,iformatdesc) (0,i=1,loop_index)
        if (ppdb_part) write(unit,*)'end writeme_vol_level2; slenielem'

      else
        write(unit) (my_0,i=1,loop_index),                                     &
                    (0,i=1,loop_index),                                        &
                    (0,i=1,loop_index)
      end if
    end if
    if (ppdb_part.and.outformat) write(unit,*)'end writeme_vol_level2; slen'

   if (ppdb_part.and.outformat) write(unit,*)'end writeme_vol_level2'

  end subroutine writeme_vol_level2


!================================ WRITE_CL2G =================================80
!
! Writes cl2g array of the elem derived type
!
!=============================================================================80

  subroutine write_cl2g(ncell, cl2g, unit, outformat)

    use party_lmpi,    only : ppdb_part

    logical,                   intent(in) :: outformat
    integer,                   intent(in) :: ncell, unit
    integer, dimension(ncell), intent(in) :: cl2g

    character(len=80) :: iformatdesc

    integer :: i

  continue

    iformatdesc = '(i10)'

    if (outformat) then
      if (ppdb_part) write(unit,*)'beg write_cl2g'
      write (unit,iformatdesc) (cl2g(i),i=1,ncell)
      if (ppdb_part) write(unit,*)'end write_cl2g'
    else
      write (unit) (cl2g(i),i=1,ncell)
    end if

  end subroutine write_cl2g


!================================= WRITE_CON =================================80
!
! Writes cell connectivity information of the elem derived type
!
!=============================================================================80

  subroutine write_con(ncell, number_per_cell, connectivity, unit, outformat)

    use party_lmpi,    only : ppdb_part

    logical,                                   intent(in) :: outformat
    integer,                                   intent(in) :: ncell
    integer,                                   intent(in) :: number_per_cell
    integer,                                   intent(in) :: unit
    integer, dimension(number_per_cell,ncell), intent(in) :: connectivity

    character(len=80) :: iformatdesc

    integer :: i,j

  continue

    iformatdesc = '(i10)'

    if (outformat) then
      if (ppdb_part) write(unit,*)'beg write_con'
      do j=1,number_per_cell
        write(unit,iformatdesc) (connectivity(j,i),i=1,ncell)
      end do
      if (ppdb_part) write(unit,*)'end write_con'
    else
      do j=1,number_per_cell
        write(unit) (connectivity(j,i),i=1,ncell)
      end do
    end if

  end subroutine write_con


!============================= WRITEME_BOUNDARY_SM ===========================80
!
! Write grid part file boundary info
!
!=============================================================================80

  subroutine writeme_boundary_sm(unit,grid,outformat,version)

    use grid_types,        only : grid_type
    use bc_util,           only : writebc

    logical,                    intent(in)  :: outformat
    integer,                    intent(in)  :: unit
! beginNeverComplex
    real(dp),                   intent(in)  :: version
! endNeverComplex
    type(grid_type),            intent(in)  :: grid

    integer :: i

  continue

    if (outformat) write(unit,*)'beg writeme_boundary'

    do i = 1, grid%nbound
      if (outformat)                                                           &
       write(unit,*)'beg writeme_boundary; ibound,nbnode =',i,grid%bc(i)%nbnode

      call writebc(unit,                                                       &
           grid%bc(i)%ibc,                                                     &
           grid%bc(i)%nbnodeg,  grid%bc(i)%nbfacetg,     grid%bc(i)%nbfaceqg,  &
           grid%bc(i)%nbnode,   grid%bc(i)%nbfacet,      grid%bc(i)%nbfaceq,   &
           grid%bc(i)%f2ntb,    grid%bc(i)%f2nqb,                              &
           grid%bc(i)%face_bit, grid%bc(i)%face_bitq,   grid%bc(i)%ibnode,     &
           outformat,           version, i) ! pparty (added i)

      if (outformat) write(unit,*)'end writeme_boundary; ibound =',i

    end do

  end subroutine writeme_boundary_sm

!================================= dump_grid  ================================80
! dump grid
!=============================================================================80
  subroutine dump_grid(grid,nface_augmentorsg)

    use grid_types,   only : grid_type
    use string_utils, only : sprintf, max_str_len
    use info_depr,    only : twod

    type(grid_type),   intent(in) :: grid
    integer, optional, intent(in) :: nface_augmentorsg

    character(max_str_len) :: filename, gridname

    integer :: ielem, i,ib, npc,npf,epc, ie1,ie2,gie1,gie2, extradim
    real(dp) :: version

    integer, parameter :: iunit = 150
    integer, parameter :: dummy_unused_physics_index = 0
    integer, parameter :: tempcell2 = 0

    continue

    filename = trim(grid%project)//'_dump.%i0'
    gridname = sprintf( filename, lmpi_id+1 )
    open(iunit,file=gridname,form='formatted',status='unknown')

    write(*,*)"ENTER writeme_headvol ",lmpi_id,grid%cc,twod
    version = 4.0_dp
    if (grid%cc) version = 4.1_dp
    write(iunit,*) "version ",version

    if (grid%cc) then
       if (present(nface_augmentorsg)) then
          i = nface_augmentorsg
       else
          i = 0
       end if
       write(iunit,*)
       write(iunit,*)'nc0,    nc01,   ncg ',grid%ncell0,grid%ncell01,grid%ncellg
       write(iunit,*)'nf,     nfg         ',grid%nface, grid%nfaceg
       write(iunit,*)'nbf0,   nbfg        ',grid%nbface0,grid%nbfaceg
       write(iunit,*)'nf_aug, nf_augg     ',grid%nface_augmentors,i
    end if

    write(iunit,*)
    write(iunit,*)"id   nproc            ",lmpi_id,lmpi_nproc
    write(iunit,*)"nn0  nn1,   nn01, nng ",                                    &
      grid%nnodes0, grid%nnodes01 - grid%nnodes0, grid%nnodes01,grid%nnodesg
    write(iunit,*)"neg  ne     neloc  neloc2d ",                               &
      grid%nedgeg, grid%nedge, grid%nedgeloc, grid%nedgeloc_2d
    write(iunit,*)"nb   nelem, idis      ",grid%nbound, grid%nelem,grid%idistfcn

    write(iunit,*)
    do ielem = 1,grid%nelem
       write(iunit, '(a10,6i10)')  grid%elem(ielem)%type_cell,                 &
         grid%elem(ielem)%ncell, tempcell2, grid%elem(ielem)%ncellg
    end do

    write(iunit,*)
!   Write l2g
    write(iunit,*)'beg writeme_vol_level2; l2g ',grid%nnodes01
    do i = 1,grid%nnodes01
       write(iunit,'(1x,i0)') grid%l2g(i)
    end do
    write(iunit,*)'end writeme_vol_level2; l2g'

!   Write cl2g array
    write(iunit,*)'beg writeme_vol_level2; cl2g ',size(grid%cl2g)
    do i = 1,size(grid%cl2g)
       write(iunit,'(1x,i0)') grid%cl2g(i)
    end do
    write(iunit,*)'end writeme_vol_level2; cl2g'

!   Write cl2g array
    write(iunit,*)'beg writeme_vol_level2; cl2g with elem'
    do ielem = 1,grid%nelem
       write(iunit,*)'beg write_cl2g ',ielem,grid%elem(ielem)%ncell
       do i = 1,grid%elem(ielem)%ncell
          write(iunit,'(1x,i0)') grid%elem(ielem)%cl2g(i)
       end do
       write(iunit,*)'end write_cl2g'
    end do
    write(iunit,*)'end writeme_vol_level2; cl2g'

!   Write out edge local-to-global array
    write(iunit,*)'beg writeme_vol_level2; el2g ',grid%nedge
    do i = 1,grid%nedge
       write(iunit,'(1x,i0)') grid%el2g(i)
    end do
    write(iunit,*)'end writeme_vol_level2; el2g'

!   Write Cell to Node indices
    write(iunit,*)'beg writeme_vol_level2; c2n'
    do ielem=1,grid%nelem
      npc = grid%elem(ielem)%node_per_cell
      write(iunit,*)'beg write_con ',ielem,npc,grid%elem(ielem)%ncell
      do i = 1,grid%elem(ielem)%ncell
        write(iunit,'(1x,8(i0,1x))') grid%elem(ielem)%c2n(1:npc,i)
      end do
      write(iunit,*)'end write_con'
    end do
    write(iunit,*)'end writeme_vol_level2; c2n'

!   Write Cell to Edge indices
    write(iunit,*)'beg writeme_vol_level2; c2e'
    do ielem = 1,grid%nelem
      epc = grid%elem(ielem)%edge_per_cell
      write(iunit,*)'beg write_con ',ielem,epc,grid%elem(ielem)%ncell
      do i = 1,grid%elem(ielem)%ncell
         write(iunit,'(1x,10(i0,1x))') grid%elem(ielem)%c2e(1:epc,i)
      end do
    end do
    write(iunit,*)'end writeme_vol_level2; c2e'

!   Write coordinates of grid points
    write(iunit,*)'beg writeme_vol_level2; xyz ',grid%nnodes01
    do i = 1,grid%nnodes01
       write(iunit,'(1x,3(e20.13,1x))') grid%x(i),grid%y(i),grid%z(i)
    end do
    write(iunit,*)'end writeme_vol_level2; xyz'

!   Write node indices for each edge
    write(iunit,*)'beg writeme_vol_level2; eptr ',grid%nedge
    write(51000+lmpi_id,*)'BEG writeme_VOL_level2; eptr ',grid%nedge
    do i = 1,grid%nedge
       write(iunit,'(1x,2(i0,1x))') grid%eptr(1:2,i)
       ie1 = grid%eptr(1,i); gie1 = grid%l2g(ie1)
       ie2 = grid%eptr(2,i); gie2 = grid%l2g(ie2)
       write(51000+lmpi_id,*) gie1,gie2
       write(52000+lmpi_id,*) min(gie1,gie2), max(gie1,gie2)
       write(53000+lmpi_id,*) grid%el2g(i),gie1,gie2
    end do
    write(iunit,*)'end writeme_vol_level2; eptr'

! ------------------------------------
    extradim = 2
    write(iunit,*)'beg writeme_boundary'
    do ib = 1, grid%nbound
      write(iunit,*)'beg writeme_boundary; ibound,nbnode =',grid%bc(ib)%nbnode
      write(iunit,*)"beg bound_info; ibound = ",ib
      write(iunit,*)"     ibc       dummy     nbnodeg   nbfacetg  nbfaceqg  "//&
                    "nbnode    nbfacet   nbfaceq"
      write(iunit,'(10(i10,1x))')                                              &
         grid%bc(ib)%ibc,dummy_unused_physics_index,                           &
         grid%bc(ib)%nbnodeg,grid%bc(ib)%nbfacetg,grid%bc(ib)%nbfaceqg,        &
         grid%bc(ib)%nbnode, grid%bc(ib)%nbfacet,grid%bc(ib)%nbfaceq
      write(iunit,"(' end bound_info; ibound = ',i0)") ib

       npf = 3+extradim
       write(iunit,*) "beg Nbfacet; ibound = ",ib,npf,grid%bc(ib)%nbfacet
       do i = 1,grid%bc(ib)%nbfacet
          write(iunit,'(1x,8(i0,1x))') grid%bc(ib)%f2ntb(i,1:npf)
       end do
       write(iunit,"(' end nbfacet; ibound = ',i0)") ib

       npf = 4+extradim
       write(iunit,*) "beg nbfaceq; ibound = ",ib,npf,grid%bc(ib)%nbfaceq
       do i = 1,grid%bc(ib)%nbfaceq
          write(iunit,'(1x,8(i0,1x))') grid%bc(ib)%f2nqb(i,1:npf)
       end do
       write(iunit,"(' end nbfaceq; ibound = ',i0)") ib

       write(iunit,*) "beg face_bit; ibound = ",ib,grid%bc(ib)%nbfacet
       do i = 1,grid%bc(ib)%nbfacet
          write(iunit,'(1x,i0)')grid%bc(ib)%face_bit(i)
       end do
       write(iunit,"(' end face_bitt; ibound = ',i0)") ib
       write(iunit,"(' beg face_bitq; ibound = ',i0)") ib
       do i = 1,grid%bc(ib)%nbfaceq
          write(iunit,'(1x,i0)')grid%bc(ib)%face_bitq(i)
       end do
       write(iunit,"(' end face_bitq; ibound = ',i0)") ib

       write(iunit,"(' beg ibnode; ibound = ',i0)") ib
       do i = 1,grid%bc(ib)%nbnode
          write(iunit,'(1x,i0)')grid%bc(ib)%ibnode(i)
       end do
       write(iunit,"(' end ibnode; ibound = ',i0)") ib
   end do

   if (.false.) call dump_grid_bc(grid)

  end subroutine dump_grid


!================================= dump_grid_bc ==============================80
! dump grid
!=============================================================================80
  subroutine dump_grid_bc(grid)

    use grid_types,   only : grid_type
    use string_utils, only : sprintf, max_str_len
    use info_depr,    only : twod

    type(grid_type),   intent(in) :: grid

    character(max_str_len) :: filename, gridname

    integer :: i,ib, npf, icell, ielem
    real(dp) :: version

    integer, parameter :: iunit = 150
    integer, parameter :: dummy_unused_physics_index = 0

    continue

    filename = trim(grid%project)//'_dump_bc.%i0'
    gridname = sprintf( filename, lmpi_id+1 )
    open(iunit,file=gridname,form='formatted',status='unknown')

    write(*,*)"ENTER writeme_headvol ",lmpi_id,grid%cc,twod
    version = 4.0_dp
    if (grid%cc) version = 4.1_dp
    write(iunit,*) "version ",version

! ------------------------------------
    write(iunit,*)'beg writeme_boundary'
    do ib = 1, grid%nbound
      write(iunit,*)'beg writeme_boundary; ibound,nbnode =',grid%bc(ib)%nbnode
      write(iunit,*)"beg bound_info; ibound = ",ib
      write(iunit,*)"     ibc       dummy     nbnodeg   nbfacetg  nbfaceqg  "//&
                    "nbnode    nbfacet   nbfaceq"
      write(iunit,'(10(i10,1x))')                                              &
         grid%bc(ib)%ibc,dummy_unused_physics_index,                           &
         grid%bc(ib)%nbnodeg,grid%bc(ib)%nbfacetg,grid%bc(ib)%nbfaceqg,        &
         grid%bc(ib)%nbnode, grid%bc(ib)%nbfacet,grid%bc(ib)%nbfaceq
      write(iunit,"(' end bound_info; ibound = ',i0)") ib

       npf = 3
       write(iunit,*) "beg NBfacet; ibound = ",ib,npf,grid%bc(ib)%nbfacet
       do i = 1,grid%bc(ib)%nbfacet
          write(iunit,'(1x,8(i0,1x))') grid%l2g(grid%bc(ib)%f2ntb(i,1:npf)),   &
                                       grid%bc(ib)%f2ntb(i,npf+1:npf+2)
       end do
       write(iunit,"(' end nbfacet; ibound = ',i0)") ib

       npf = 4
       write(iunit,*) "beg nbfaceq; ibound = ",ib,npf,grid%bc(ib)%nbfaceq
       do i = 1,grid%bc(ib)%nbfaceq
          write(iunit,'(1x,8(i0,1x))') grid%l2g(grid%bc(ib)%f2nqb(i,1:npf)),   &
                                       grid%bc(ib)%f2nqb(i,npf+1:npf+2)
       end do
       write(iunit,"(' end nbfaceq; ibound = ',i0)") ib

       write(iunit,*) "beg ibnode; ibound = ",ib,grid%bc(ib)%nbnode
       do i = 1,grid%bc(ib)%nbnode
          write(iunit,'(1x,i0)') grid%l2g(grid%bc(ib)%ibnode(i))
       end do
       write(iunit,"(' end ibnode; ibound = ',i0)") ib


       npf = 3
       write(iunit,*) "beg nbfacet(DEREF); ibound = ",ib,npf,grid%bc(ib)%nbfacet
       do i = 1,grid%bc(ib)%nbfacet
          icell = grid%bc(ib)%f2ntb(i,npf+1)
          ielem = grid%bc(ib)%f2ntb(i,npf+2)
          write(iunit,'(1x,8(i0,1x))')                                         &
            grid%l2g(grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(i,1:npf))),          &
            icell,ielem
          write(iunit,'(1x,16(i0,1x))')                                        &
            grid%l2g(grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(i,1:npf))),          &
            grid%cl2g(icell),ielem,grid%elem(ielem)%c2n(:,icell)
          icell = grid%elem(ielem)%cl2g(icell)
          write(iunit,'(1x,16(i0,1x))')                                        &
            grid%l2g(grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(i,1:npf))),          &
            icell,ielem,grid%elem(ielem)%c2n(:,icell)
       end do
       write(iunit,"(' end nbfacet; ibound = ',i0)") ib

       npf = 4
       write(iunit,*) "beg nbfaceq(DEREF); ibound = ",ib,npf,grid%bc(ib)%nbfaceq
       do i = 1,grid%bc(ib)%nbfaceq
          icell = grid%bc(ib)%f2nqb(i,npf+1)
          ielem = grid%bc(ib)%f2nqb(i,npf+2)
          write(iunit,'(1x,8(i0,1x))')                                         &
            grid%l2g(grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(i,1:npf))),          &
            icell,ielem
          write(iunit,'(1x,16(i0,1x))')                                        &
            grid%l2g(grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(i,1:npf))),          &
            grid%cl2g(icell),ielem,grid%elem(ielem)%c2n(:,icell)
       end do
       write(iunit,"(' end nbfaceq; ibound = ',i0)") ib

   end do

  end subroutine dump_grid_bc

!================================= dump_cc  ==================================80
! dump cc
!=============================================================================80
  subroutine dump_cc(soln_eqn_set,grid,bc_ghost)

    use grid_types,   only : grid_type
    use bc_types,     only : bcgrid_type
    use string_utils, only : sprintf, max_str_len
    use info_depr,    only : twod

    integer,           intent(in) :: soln_eqn_set
    type(grid_type),   intent(in) :: grid
    type(bcgrid_type), dimension(:), pointer :: bc_ghost

    character(max_str_len) :: filename, gridname

    integer :: ielem,i,ib
    real(dp) :: version

    integer, parameter :: iunit = 150
    integer, parameter :: tempcell2 = 0

    continue

    filename = trim(grid%project)//'_dump_cc.%i0'
    gridname = sprintf( filename, lmpi_id+1 )
    open(iunit,file=gridname,form='formatted',status='unknown')

    write(*,*)"ENTER writeme_headvol ",lmpi_id,grid%cc,twod
    version = 4.0_dp
    if (grid%cc) version = 4.1_dp
    write(iunit,*) "version ",version,soln_eqn_set

    if (grid%cc) then
       write(iunit,*)
       write(iunit,*)'nc0,    nc01,   ncg ',grid%ncell0,grid%ncell01,grid%ncellg
       write(iunit,*)'nf,     nfg         ',grid%nface, grid%nfaceg
       write(iunit,*)'nbf0,   nbfg        ',grid%nbface0,grid%nbfaceg
       write(iunit,*)'nf_aug,             ',grid%nface_augmentors
    end if

    write(iunit,*)
    write(iunit,*)"id   nproc            ",lmpi_id,lmpi_nproc
    write(iunit,*)"nn0  nn1,   nn01, nng ",                                    &
      grid%nnodes0, grid%nnodes01 - grid%nnodes0, grid%nnodes01,grid%nnodesg
    write(iunit,*)"neg  ne     neloc  neloc2d ",                               &
      grid%nedgeg, grid%nedge, grid%nedgeloc, grid%nedgeloc_2d
    write(iunit,*)"nb   nelem, idis      ",grid%nbound, grid%nelem,grid%idistfcn

    write(iunit,*)
    do ielem = 1,grid%nelem
       write(iunit, '(a10,6i10)')  grid%elem(ielem)%type_cell,                 &
         grid%elem(ielem)%ncell, tempcell2, grid%elem(ielem)%ncellg
    end do

    !  read(unit) (grid%fptr(1,i),i=1,grid%nface),                             &
    !             (grid%fptr(2,i),i=1,grid%nface),                             &
    !             (grid%fptr(3,i),i=1,grid%nface),                             &
    !             (grid%fptr(4,i),i=1,grid%nface),                             &
    !             (grid%fptr(5,i),i=1,grid%nface),                             &
    !             (grid%fptr(6,i),i=1,grid%nface)
    !  read(unit) (grid%cell_vol(i),i=1,grid%ncell01)
    !  read(unit) (grid%area_face(i),i=1,grid%nface)
    !  read(unit) (grid%xn_face(i),i=1,grid%nface)
    !  read(unit) (grid%yn_face(i),i=1,grid%nface)
    !  read(unit) (grid%zn_face(i),i=1,grid%nface)
    !  read(unit) (grid%x_face(i),i=1,grid%nface)
    !  read(unit) (grid%y_face(i),i=1,grid%nface)
    !  read(unit) (grid%z_face(i),i=1,grid%nface)
    !  read(unit) (grid%xc(i),i=1,grid%ncell01)
    !  read(unit) (grid%yc(i),i=1,grid%ncell01)
    !  read(unit) (grid%zc(i),i=1,grid%ncell01)
    !  read(unit) (grid%cl2g(i),i=1,grid%ncell01)
    !  read(unit) (grid%fl2g(i),i=1,grid%nface)
    !  read(unit) (grid%rlsq_ia(i),i=1,grid%ncell01+1)
    !  read(unit) (grid%rlsq_ja(i),i=1,grid%ncell_augmentors)
    !  read(unit) (grid%flsq_ia(i),i=1,grid%nface+1)
    !  read(unit) (grid%flsq_ja(i),i=1,grid%nface_augmentors)

! nface:   fptr, xn_face,yn_face,zn_face,x_face,y_face,z_face, fl2g
! ncell01: cell_vol,xc,yc,zc,cl2g,
! lsq:     flsq_ia, flsq_ja

    write(iunit,*)"beg face related : fl2g,xyz_face,xyn_n,area ",grid%nface
    do i = 1,grid%nface
     ! write(iunit,'(1x,i0,1x,3(E20.9,1x)," : ",3(E20.9,1x)," : ",E20.9)')     &
       write(iunit,'(1x,i0,1x,3(E20.9,1x)," : ",3(E20.9,1x)," : ",E20.9)')     &
         grid%fl2g(i),grid%x_face(i),grid%y_face(i),grid%z_face(i),            &
                      grid%xn_face(i),grid%yn_face(i),grid%zn_face(i),         &
                      grid%area_face(i)
       write(iunit,'(1x,3(E20.9,1x)," : ",i0)')                                &
         grid%x_face(i),grid%y_face(i),grid%z_face(i),grid%fl2g(i)
    end do

    write(iunit,*)"beg ncell01 related : cl2g,xyzc,cell_vol ",grid%ncell01
    do i = 1,grid%ncell01
       write(iunit,'(1x,i0,1x,3(E20.9,1x)," : ",3(E20.9,1x)," : ",E20.9)')     &
         grid%cl2g(i),grid%xc(i),grid%yc(i),grid%zc(i),grid%cell_vol(i)
    end do

    write(iunit,*)"beg grid%flsq_ia ",size(grid%flsq_ia)
    do i = 1,size(grid%flsq_ia)
       write(iunit,*) grid%flsq_ia(i)
    end do
    write(iunit,*)"beg grid%flsq_ia ",size(grid%flsq_ia)

! cell_map
! read(unit) (grid%elem(ielem)%cell_map(i),i=1,grid%elem(ielem)%ncell)

    write(iunit,*)"beg cell_map (1) : nelem,ncell ",grid%nface
    do ielem = 1, grid%nelem
       write(iunit,*)"beg cell_map (1) ",ielem,grid%elem(ielem)%ncell
       do i = 1,grid%elem(ielem)%ncell
          write(iunit,*) ielem,i,grid%elem(ielem)%cell_map(i)
       end do
       write(iunit,*)"end cell_map (1) : nelem,ncell ",grid%nface
     end do
     write(iunit,*)"end cell_map (2) : nelem,ncell ",grid%nface
     do ielem = 1, grid%nelem
       write(iunit,*)"beg cell_map (2) ",ielem,grid%elem(ielem)%ncell
       do i = 1,grid%elem(ielem)%ncell
          write(iunit,*) ielem,i,grid%cl2g(grid%elem(ielem)%cell_map(i))
       end do
       write(iunit,*)"end cell_map (2) : nelem,ncell ",grid%nface
     end do
     write(iunit,*)"end cell_map (3) : nelem,ncell ",grid%nface
     do ielem = 1, grid%nelem
       write(iunit,*)"beg cell_map (3) ",ielem,grid%elem(ielem)%ncell
       do i = 1,grid%elem(ielem)%ncell
          write(iunit,*) grid%cl2g(grid%elem(ielem)%cell_map(i))
       end do
       write(iunit,*)"end cell_map (3) : nelem,ncell ",grid%nface
     end do
     write(iunit,*)"end cell_map (4) : nelem,ncell ",grid%nface
     do ielem = 1, grid%nelem
       write(iunit,*)"beg cell_map (4) ",ielem,grid%elem(ielem)%ncell
       do i = 1,grid%elem(ielem)%ncell
          write(iunit,'(1x,i0,1x,8(i0,1x))')                                  &
             grid%cl2g(grid%elem(ielem)%cell_map(i)),                         &
             grid%l2g(grid%elem(ielem)%c2n(1:grid%elem(ielem)%node_per_cell,i))
       end do
       write(iunit,*)"end cell_map (4) : nelem,ncell ",grid%nface
     end do
     write(iunit,*)"end cell_map (E): nelem,ncell ",grid%nface

! bc, bc_ghost
!     do ib = 1, grid%nbound
!       read(unit) (grid%bc(ib)%qcell_ptr_t(i), i=1,grid%bc(ib)%nbfacet)
!       read(unit) (grid%bc(ib)%qcell_ptr_q(i), i=1,grid%bc(ib)%nbfaceq)
!       read(unit) bc_ghost(ib)%nbfacet,bc_ghost(ib)%nbfaceq,bc_ghost(ib)%nbnode
!       read(unit) (bc_ghost(ib)%ibnode(i),i=1,bc_ghost(ib)%nbnode)
!       read(unit) ((bc_ghost(ib)%f2ntb(i,j),i=1,bc_ghost(ib)%nbfacet),j=1,5)
!       read(unit) ((bc_ghost(ib)%f2nqb(i,j),i=1,bc_ghost(ib)%nbfaceq),j=1,6)
!       read(unit) (bc_ghost(ib)%qcell_ptr_t(i),i=1,bc_ghost(ib)%nbfacet)
!       read(unit) (bc_ghost(ib)%qcell_ptr_q(i),i=1,bc_ghost(ib)%nbfaceq)
!     end do

    write(iunit,*)"beg bc_ghost"
    do ib = 1, grid%nbound
       write(iunit,*)"beg qcell_ptr_t ",ib,grid%bc(ib)%nbfacet
       do i = 1,grid%bc(ib)%nbfacet
          write(iunit,*) grid%bc(ib)%qcell_ptr_t(i)
       end do
       write(iunit,*)"beg qcell_ptr_q ",ib,grid%bc(ib)%nbfaceq
       do i = 1,grid%bc(ib)%nbfaceq
          write(iunit,*) grid%bc(ib)%qcell_ptr_q(i)
       end do
       write(iunit,*)"beg bc_ghost nbfacet, nbaceq ,nbnode ",                  &
         bc_ghost(ib)%nbfacet,bc_ghost(ib)%nbfaceq,bc_ghost(ib)%nbnode
       write(iunit,*)"beg bc_ghost ibnode ",ib,bc_ghost(ib)%nbnode
       do i = 1,bc_ghost(ib)%nbnode
          write(iunit,*) bc_ghost(ib)%ibnode(i)
       end do
       write(iunit,*)"beg bc_ghost f2ntb ",ib,bc_ghost(ib)%nbfacet,5
       do i = 1,bc_ghost(ib)%nbfacet
          write(iunit,*) bc_ghost(ib)%f2ntb(i,1:5)
       end do
       write(iunit,*)"beg bc_ghost f2nqb ",ib,bc_ghost(ib)%nbfaceq,5
       do i = 1,bc_ghost(ib)%nbfaceq
          write(iunit,*) bc_ghost(ib)%f2nqb(i,1:6)
       end do
!------------- l2g
       write(iunit,*)"beg bc_ghost ibnode ",ib,bc_ghost(ib)%nbnode
       do i = 1,bc_ghost(ib)%nbnode
          write(iunit,*) bc_ghost(ib)%ibnode(i)
       end do
       write(iunit,*)"beg bc_ghost f2ntb ",ib,bc_ghost(ib)%nbfacet,5
       do i = 1,bc_ghost(ib)%nbfacet
        write(iunit,*)                                             &
         grid%l2g(bc_ghost(ib)%ibnode(bc_ghost(ib)%f2ntb(i,1:3))), &
         bc_ghost(ib)%f2ntb(i,4:5)
       end do
       write(iunit,*)"end bc_ghost f2ntb ",ib,bc_ghost(ib)%nbfacet,5
       write(iunit,*)"beg bc_ghost f2nqb ",ib,bc_ghost(ib)%nbfaceq,5
       do i = 1,bc_ghost(ib)%nbfaceq
         write(iunit,*)                                            &
         grid%l2g(bc_ghost(ib)%ibnode(bc_ghost(ib)%f2nqb(i,1:4))), &
         bc_ghost(ib)%f2nqb(i,5:6)
       end do
       write(iunit,*)"end bc_ghost f2nqb ",ib,bc_ghost(ib)%nbfaceq,5
!------------------------
       write(iunit,*)"beg bc_ghost qcell_ptr_t",ib,bc_ghost(ib)%nbfacet
       do i = 1,bc_ghost(ib)%nbfacet
          write(iunit,*) bc_ghost(ib)%qcell_ptr_t(i)
       end do
       write(iunit,*)"beg bc_ghost qcell_ptr_q",ib,bc_ghost(ib)%nbfaceq
       do i = 1,bc_ghost(ib)%nbfaceq
          write(iunit,*) bc_ghost(ib)%qcell_ptr_q(i)
       end do
    end do
    write(iunit,*)"end bc_ghost"

  end subroutine dump_cc

!============================= WRITEME_CC_SM =================================80
!
! Write grid part file boundary info
!
!=============================================================================80

  subroutine writeme_cc_sm(unit,outformat,grid)

    use grid_types,  only : grid_type
    use bc_cache_cc, only : bc_ghost

    integer,         intent(in) :: unit
    logical,         intent(in) :: outformat
    type(grid_type), intent(in) :: grid

    integer :: i,j, ib, ielem

  continue

    if (outformat) then
      write(unit,*)'beg writeme_cc_sm fptr ',grid%nface
      write(unit,*)(grid%fptr(1,i),i=1,grid%nface),                            &
                  (grid%fptr(2,i),i=1,grid%nface),                             &
                  (grid%fptr(3,i),i=1,grid%nface),                             &
                  (grid%fptr(4,i),i=1,grid%nface),                             &
                  (grid%fptr(5,i),i=1,grid%nface),                             &
                  (grid%fptr(6,i),i=1,grid%nface)
      write(unit,*)'beg writeme_cc_sm cell_vol ',grid%ncell01
      write(unit,*) (grid%cell_vol(i),i=1,grid%ncell01)
      write(unit,*)'beg writeme_cc_sm area_face ',grid%nface
      write(unit,*) (grid%area_face(i),i=1,grid%nface)
      write(unit,*)'beg writeme_cc_sm xn_face ',grid%nface
      write(unit,*) (grid%xn_face(i),i=1,grid%nface)
      write(unit,*)'beg writeme_cc_sm yn_face ',grid%nface
      write(unit,*) (grid%yn_face(i),i=1,grid%nface)
      write(unit,*)'beg writeme_cc_sm zn_face ',grid%nface
      write(unit,*) (grid%zn_face(i),i=1,grid%nface)
      write(unit,*)'beg writeme_cc_sm x_face ',grid%nface
      write(unit,*) (grid%x_face(i),i=1,grid%nface)
      write(unit,*)'beg writeme_cc_sm y_face ',grid%nface
      write(unit,*) (grid%y_face(i),i=1,grid%nface)
      write(unit,*)'beg writeme_cc_sm z_face ',grid%nface
      write(unit,*) (grid%z_face(i),i=1,grid%nface)
      write(unit,*)'beg writeme_cc_sm xc ',grid%ncell01
      write(unit,*) (grid%xc(i),i=1,grid%ncell01)
      write(unit,*)'beg writeme_cc_sm yc ',grid%ncell01
      write(unit,*) (grid%yc(i),i=1,grid%ncell01)
      write(unit,*)'beg writeme_cc_sm zc ',grid%ncell01
      write(unit,*) (grid%zc(i),i=1,grid%ncell01)
      write(unit,*)'beg writeme_cc_sm cl2g ',grid%ncell01
      write(unit,*) (grid%cl2g(i),i=1,grid%ncell01)
      write(unit,*)'beg writeme_cc_sm fl2g ',grid%nface
      write(unit,*) (grid%fl2g(i),i=1,grid%nface)
      write(unit,*)'beg writeme_cc_sm rlsq_ia ',grid%ncell01+1
      write(unit,*) (grid%rlsq_ia(i),i=1,grid%ncell01+1)
      write(unit,*)'beg writeme_cc_sm rlsq_ja ',0 ! grid%ncell_augmentors
      j = 0
      write(unit,*) (grid%rlsq_ja(i),i=1,j) ! grid%ncell_augmentors)
      write(unit,*)'beg writeme_cc_sm flsq_ia ',grid%nface+1
      write(unit,*) (grid%flsq_ia(i),i=1,grid%nface+1)
      write(unit,*)'beg writeme_cc_sm flsq_ja ',grid%nface_augmentors
      write(unit,*) (grid%flsq_ja(i),i=1,grid%nface_augmentors)
      do ielem = 1, grid%nelem
      write(unit,*)'beg writeme_cc_sm cell_map ',ielem,grid%elem(ielem)%ncell
        write(unit,*) (grid%elem(ielem)%cell_map(i),i=1,grid%elem(ielem)%ncell)
      end do
      do ib = 1, grid%nbound
        write(unit,*)'beg writeme_cc_sm qcell_ptr_t ',ib,grid%bc(ib)%nbfacet
        write(unit,*) (grid%bc(ib)%qcell_ptr_t(i), i=1,grid%bc(ib)%nbfacet)
        write(unit,*)'beg writeme_cc_sm qcell_ptr_q ',ib,grid%bc(ib)%nbfaceq
        write(unit,*) (grid%bc(ib)%qcell_ptr_q(i), i=1,grid%bc(ib)%nbfaceq)
        write(unit,*)'beg WM_cc_sm gc_g%nbt,ntq,ntn ',ib,bc_ghost(ib)%nbfacet, &
                      bc_ghost(ib)%nbfaceq, bc_ghost(ib)%nbnode
        write(unit,*) bc_ghost(ib)%nbfacet, bc_ghost(ib)%nbfaceq,              &
                      bc_ghost(ib)%nbnode
        write(unit,*)'beg writeme_cc_sm bc_g%ibnode ',ib,bc_ghost(ib)%nbnode
        write(unit,*) (bc_ghost(ib)%ibnode(i),i=1,bc_ghost(ib)%nbnode)
        write(unit,*)'beg writeme_cc_sm bc_g%f2ntb ',ib,bc_ghost(ib)%nbfacet
        write(unit,*) ((bc_ghost(ib)%f2ntb(i,j),i=1,bc_ghost(ib)%nbfacet),j=1,5)
        write(unit,*)'beg writeme_cc_sm bc_g%f2nqb ',ib,bc_ghost(ib)%nbfaceq
        write(unit,*) ((bc_ghost(ib)%f2nqb(i,j),i=1,bc_ghost(ib)%nbfaceq),j=1,6)
        write(unit,*)'beg WM_cc_sm bc_g%qcell_ptr_t ',ib,bc_ghost(ib)%nbfacet
        write(unit,*) (bc_ghost(ib)%qcell_ptr_t(i),i=1,bc_ghost(ib)%nbfacet)
        write(unit,*)'beg WM_cc_sm bc_g%qcell_ptr_q ',ib,bc_ghost(ib)%nbfaceq
        write(unit,*) (bc_ghost(ib)%qcell_ptr_q(i),i=1,bc_ghost(ib)%nbfaceq)
      end do
      write(unit,*)'end writeme_cc_sm '
    else
      write(unit) (grid%fptr(1,i),i=1,grid%nface),                             &
                  (grid%fptr(2,i),i=1,grid%nface),                             &
                  (grid%fptr(3,i),i=1,grid%nface),                             &
                  (grid%fptr(4,i),i=1,grid%nface),                             &
                  (grid%fptr(5,i),i=1,grid%nface),                             &
                  (grid%fptr(6,i),i=1,grid%nface)
      write(unit) (grid%cell_vol(i),i=1,grid%ncell01)
      write(unit) (grid%area_face(i),i=1,grid%nface)
      write(unit) (grid%xn_face(i),i=1,grid%nface)
      write(unit) (grid%yn_face(i),i=1,grid%nface)
      write(unit) (grid%zn_face(i),i=1,grid%nface)
      write(unit) (grid%x_face(i),i=1,grid%nface)
      write(unit) (grid%y_face(i),i=1,grid%nface)
      write(unit) (grid%z_face(i),i=1,grid%nface)
      write(unit) (grid%xc(i),i=1,grid%ncell01)
      write(unit) (grid%yc(i),i=1,grid%ncell01)
      write(unit) (grid%zc(i),i=1,grid%ncell01)
      write(unit) (grid%cl2g(i),i=1,grid%ncell01)
      write(unit) (grid%fl2g(i),i=1,grid%nface)
      write(unit) (grid%rlsq_ia(i),i=1,grid%ncell01+1)
      j = 0
      write(unit) (grid%rlsq_ja(i),i=1,j) ! grid%ncell_augmentors) ! TBD
      write(unit) (grid%flsq_ia(i),i=1,grid%nface+1)
      write(unit) (grid%flsq_ja(i),i=1,grid%nface_augmentors)
      do ielem = 1, grid%nelem
        write(unit) (grid%elem(ielem)%cell_map(i),i=1,grid%elem(ielem)%ncell)
      end do
      do ib = 1, grid%nbound
        write(unit) (grid%bc(ib)%qcell_ptr_t(i), i=1,grid%bc(ib)%nbfacet)
        write(unit) (grid%bc(ib)%qcell_ptr_q(i), i=1,grid%bc(ib)%nbfaceq)
        write(unit) bc_ghost(ib)%nbfacet,bc_ghost(ib)%nbfaceq,                 &
                    bc_ghost(ib)%nbnode
        write(unit) (bc_ghost(ib)%ibnode(i),i=1,bc_ghost(ib)%nbnode)
        write(unit) ((bc_ghost(ib)%f2ntb(i,j),i=1,bc_ghost(ib)%nbfacet),j=1,5)
        write(unit) ((bc_ghost(ib)%f2nqb(i,j),i=1,bc_ghost(ib)%nbfaceq),j=1,6)
        write(unit) (bc_ghost(ib)%qcell_ptr_t(i),i=1,bc_ghost(ib)%nbfacet)
        write(unit) (bc_ghost(ib)%qcell_ptr_q(i),i=1,bc_ghost(ib)%nbfaceq)
      end do

    end if

  end subroutine writeme_cc_sm

end module pparty_io

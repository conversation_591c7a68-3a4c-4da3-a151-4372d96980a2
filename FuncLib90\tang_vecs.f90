!=================================== TANGENTS ================================80
!
! Compute the unit surface tangent vectors given the surface unit normal vector
!
!=============================================================================80

  pure function tang_vecs(nx, ny, nz)

    use kinddefs, only : dp

    real(dp),                 intent(in) :: nx, ny, nz  ! unit normal vector
    real(dp), dimension(3,2)             :: tang_vecs   ! tangency vectors

    real(dp) :: denom ! denominator to recover unit vector

  continue

!   Because nx**2+ny**2+nz**2=1, at least one component
!   must be larger than sqrt(1/3).

    if (abs(nx)>0.577_dp) then
      tang_vecs(1,1) =  ny-nz
      tang_vecs(2,1) = -nx
      tang_vecs(3,1) =  nx
    else if (abs(ny)>0.577_dp) then
      tang_vecs(1,1) =  ny
      tang_vecs(2,1) =  nz-nx
      tang_vecs(3,1) = -ny
    else
      tang_vecs(1,1) = -nz
      tang_vecs(2,1) =  nz
      tang_vecs(3,1) =  nx-ny
    end if

    denom = sqrt(tang_vecs(1,1)**2+tang_vecs(2,1)**2+tang_vecs(3,1)**2)

    tang_vecs(1,1) = tang_vecs(1,1)/denom
    tang_vecs(2,1) = tang_vecs(2,1)/denom
    tang_vecs(3,1) = tang_vecs(3,1)/denom

    tang_vecs(1,2) = ny*tang_vecs(3,1) - nz*tang_vecs(2,1)
    tang_vecs(2,2) = nz*tang_vecs(1,1) - nx*tang_vecs(3,1)
    tang_vecs(3,2) = nx*tang_vecs(2,1) - ny*tang_vecs(1,1)

  end function tang_vecs


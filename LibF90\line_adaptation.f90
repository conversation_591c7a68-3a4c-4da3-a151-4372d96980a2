module line_adaptation

  use kinddefs,         only : dp
  use line_types,       only : line_type
  use grid_types,       only : grid_type
  use comprow_types,    only : crow_flow
  use fun3d_constants,  only : zero, half, one

  implicit none

  private
  public :: line_adapt, set_adapt_lines, shkfit_line_pspv


  type(line_type), dimension(:), allocatable :: adapt_lines

contains

!=============================== SET_ADAPT_LINES ============================80
!
!  Set structured lines data.
!
!=============================================================================80
  subroutine set_adapt_lines( grid, crow )

    use info_depr,         only : ngrid
    use implicit_lines,    only : set_dof_lines, recast_lines,                 &
                                  read_lines_fmt_write_lines
    use lmpi,              only : lmpi_conditional_stop

    type(grid_type),      intent(in   ) :: grid
    type(crow_flow),      intent(in   ) :: crow

    integer :: n_lines, fl, ierr

    integer, dimension(:),   pointer :: endline
    integer, dimension(:,:), pointer :: line

    logical :: first_time_through = .true.
    character(80) :: flow_dir

  continue

    if ( first_time_through ) then
      allocate(adapt_lines(ngrid))
      first_time_through = .false.
      flow_dir = ''
      call read_lines_fmt_write_lines(1,flow_dir,grid%project,ierr)
      call lmpi_conditional_stop(ierr,"fmt_to_unfmt lines:line_adaptation")
    endif

    call set_dof_lines( 1, '', grid%project,                       &
                        line, endline, n_lines,                    &
                        grid%dof0, grid%l2g,                       &
                        crow%ia, crow%ja, crow%ia_ns,              &
                        grid%x,  grid%y,  grid%z, .true.  )

    fl = grid%igrid

    call recast_lines( n_lines, line, endline, adapt_lines(fl), grid%y )

    deallocate(line)
    deallocate(endline)

  end subroutine set_adapt_lines

!============================= LINE_ADAPT ====================================80
!
!  Drives line adaptation
!
!=============================================================================80

  subroutine line_adapt( soln, grid )

    use grid_types,              only : grid_type
    use solution_types,          only : soln_type
    use grid_metrics,            only : compute_dual_metrics
    use distance_function,       only : compute_distance_function
    use bc_names,                only : need_distance_function
    use lmpi,                    only : lmpi_master, lmpi_die
    use lmpi_app,                only : lmpi_xfer
    use info_depr,               only : write_mesh, write_mesh_project
    use refine_adaptation_input, only : adapt_library, sfadapt_grdspd
    use nml_global,              only : moving_grid

    type(grid_type), intent(inout)   :: grid
    type(soln_type), intent(inout)   :: soln

    integer :: line_count, line_length, pencil_length, fl

    logical :: update_face_speed

  continue



    fl            = grid%igrid

    line_count    = adapt_lines(fl)%n_lines
    pencil_length = adapt_lines(fl)%total_dofs
    line_length   = adapt_lines(fl)%first_entry(2)                             &
                  - adapt_lines(fl)%first_entry(1)

    if ( adapt_library == 'line' ) then

!     Align the mesh with the shock


      call align_shock(line_count, line_length, pencil_length,                 &
                       adapt_lines(fl)%first_entry,                            &
                       adapt_lines(fl)%line_to_dof_index,                      &
                       soln%n_tot, grid%x, grid%y, grid%z, soln%q_dof,         &
                       soln%eqn_set, grid%nnodes0)

    else if ( adapt_library == 'sfline' ) then

!     Fit the mesh to the shock

      call line_fit_shock(line_count, line_length, pencil_length,              &
                          adapt_lines(fl)%first_entry,                         &
                          adapt_lines(fl)%line_to_dof_index,                   &
                          soln%ndim, soln%n_tot, grid%nbound, grid,            &
                          grid%x,     grid%y,     grid%z,                      &
                          soln%cdt, soln%q_dof, soln%eqn_set,                  &
                          grid%nnodes0,  grid%nnodes01)

    else

      if ( lmpi_master )                                                       &
      write(*,*) ' Incorrect adaptation option detected inside line_adapt'
      if ( lmpi_master )                                                       &
      write(*,*) ' The adaptation option was = ', adapt_library
      call lmpi_die

    end if

    call lmpi_xfer(grid%x)
    call lmpi_xfer(grid%y)
    call lmpi_xfer(grid%z)
    call lmpi_xfer(soln%q_dof)

    if ( sfadapt_grdspd ) then
      update_face_speed = .true.

      call compute_dual_metrics(grid, moving_grid, update_face_speed)

    else

      call compute_dual_metrics(grid, moving_grid)

    end if

    compute_dist_fcn : if (need_distance_function(grid%bc)) then
      call compute_distance_function(grid,.true.)
      call lmpi_xfer(grid%slen)
      call lmpi_xfer(grid%iflagslen)
    else
      if (lmpi_master) write(*,*)"skip distance function..."
      grid%idistfcn = 0
    end if compute_dist_fcn

    write_mesh = .true. ! if we get here we want to save new mesh for restart
    write_mesh_project = trim(grid%project)//'_new'  ! default to old approach

  end subroutine line_adapt

!============================= ALIGN_SHOCK ===================================80
!
!  Align grid with bow shock and concentrate points near wall.
!
!=============================================================================80

  subroutine align_shock( n_lines, line_length, pencil_length, first_index,    &
                          node_map, n_tot, x, y, z, qnode, eqn_set, nnodes0 )

    use info_depr,               only : ivisc, xmach, re, tref
    use refine_adaptation_input, only : ladapt_re_cell, ladapt_beta_grd,       &
                                        ladapt_ep0_grd, ladapt_fstr,           &
                                        ladapt_jumpflag, ladapt_max_distance,  &
                                        ladapt_fctrjmp, ladapt_g_limiter
    use generic_gas_map,         only : n_amu_k, n_temperature_j, n_density,   &
                                        n_sonic_k, n_pressure_k
    use lmpi,                    only : lmpi_conditional_stop, lmpi_nproc,     &
                                        lmpi_allgather
    use ivals,                   only : q0
    use solution_types,          only : generic_gas
    use fluid,                   only : sutherland_constant, gamma

    integer, intent(in) :: n_lines, line_length, pencil_length, n_tot, eqn_set
    integer, intent(in) :: nnodes0
    integer, dimension(n_lines+1),        intent(in) :: first_index
    integer, dimension(pencil_length),    intent(in) :: node_map

    real(dp), dimension(:    ),  intent(inout) :: x
    real(dp), dimension(:    ),  intent(inout) :: y
    real(dp), dimension(:    ),  intent(inout) :: z
    real(dp), dimension(:,:   ), intent(inout) :: qnode

    real(dp), dimension(:), allocatable        :: my_min

    real(dp) :: bz, brat
    real(dp) :: dist
    real(dp) :: ep
    real(dp) :: fsh1
    real(dp) :: fctr1, fctra
    real(dp) :: fctrmx
    real(dp) :: pi, dpi
    real(dp) :: amul
    real(dp) :: rhowall
    real(dp) :: hmin_old, dh1, hmin, hmin1, hmin2, hmin3
    real(dp) :: ss1, snezn
    real(dp) :: ds, cwall
    real(dp) :: my_h, dh0
    real(dp) :: newlen
    real(dp) :: mu1, mu2, xmr, cstar, cw1, cw2

    integer  :: not_ok
    integer  :: order
    integer  :: nstr, ans
    integer  :: line, line_length_l
    integer  :: n, m, n0
    integer  :: node, node0, node1

    real(dp), dimension(:),       allocatable :: snez, snew, sold
    real(dp), dimension(:,:),     allocatable :: dep, dnew

    continue

    hmin1 = zero
    hmin2 = zero
    hmin3 = zero
    pi = acos( -one )

    not_ok = 0

!   Check that all lines have same number of nodes; else algorithm fails

    do line = 1,n_lines
      line_length_l = first_index(line+1) - first_index(line)
      if(line_length_l /= line_length)then
        call lmpi_conditional_stop(1,                                          &
              'The number of nodes in a line is not a constant.')
      end if
    end do

    allocate(dep(n_tot,line_length),dnew(n_tot,line_length-1))
    allocate(snez(line_length),snew(line_length),sold(line_length))

!   Determine where minimum mesh requirement exists

    do line = 1,n_lines

      sold(1) = zero
      n0      = first_index(line)
      node0   = node_map(n0)
      if(node0 > nnodes0)cycle
      do m = 2,line_length
        node0 = node_map(n0 + m - 2)
        node1 = node_map(n0 + m - 1)
        ds = sqrt((x(node1)-x(node0))**2 + (y(node1)-y(node0))**2              &
                + (z(node1)-z(node0))**2)
        sold(m) = sold(m-1) + ds
      end do

      hmin = zero
      hmin_old = one ! to fool g95 compiler warning may be used uninitialized
      node0   = node_map(n0)
      node1   = node_map(n0+1)
      if(ivisc==0)then
        hmin_old = half / (line_length-1)
      else
        dist = sold(line_length) ! distance from wall to grid outer boundary
        rhowall = half*max(one, qnode(n_density,node0) + qnode(n_density,node1))
        if ( eqn_set == generic_gas ) then
          amul = half*(qnode(n_amu_k(1),node0) + qnode(n_amu_k(1),node1))
          ! use effective viscosity here, even if turbulent
          cwall   = half*(qnode(n_sonic_k(1),node0) + qnode(n_sonic_k(1),node1))
        else
          xmr = xmach/re
          cstar = sutherland_constant/tref
          mu1 = viscosity_law(cstar,gamma*qnode(5,node0)/qnode(1,node0))*xmr
          mu2 = viscosity_law(cstar,gamma*qnode(5,node1)/qnode(1,node1))*xmr
          amul = half*(mu1 + mu2)
          cw1 = sqrt(gamma*qnode(5,node0)/qnode(1,node0))
          cw2 = sqrt(gamma*qnode(5,node1)/qnode(1,node1))
          cwall = half*(cw1 + cw2)
        end if

!       define spacing for cell Re = ladapt_re_cell at wall

        hmin1 = ladapt_re_cell*amul/(rhowall * cwall * dist )
        hmin2 = one / line_length
        hmin3 = ladapt_g_limiter * ladapt_re_cell/dist
        if (hmin3 <= zero) hmin3 = hmin2
        hmin = min(hmin1, hmin2, hmin3)
        if (line == 1) hmin_old = hmin
        hmin_old = min(hmin,hmin_old)
      end if
      my_h = hmin_old

    end do

    allocate(my_min(lmpi_nproc)); my_min = zero
    call lmpi_allgather(my_h,my_min)
    dh0 = minval(my_min,mask=my_min > zero)
    deallocate(my_min)

!   Compute bow shock alignment factors

    do line = 1,n_lines

      sold(1) = zero
      n0      = first_index(line)
      node0   = node_map(n0)
      if(node0 > nnodes0)cycle
      dep(1:n_tot,1) = qnode(1:n_tot,node0)
      do m = 2,line_length
        node0 = node_map(n0 + m - 2)
        node1 = node_map(n0 + m - 1)
        if(node1 > nnodes0)then
          write(6,*)"line, m, node1 = ",line, m, node1
          call lmpi_conditional_stop(1,                                        &
              'All nodes of this line are not on the same partition.')
        end if
        ds = sqrt((x(node1)-x(node0))**2 + (y(node1)-y(node0))**2              &
                + (z(node1)-z(node0))**2)
        sold(m) = sold(m-1) + ds
        dep(1:n_tot,m) = qnode(1:n_tot,node1)
      end do

      if ( ladapt_jumpflag == 0 ) then

        snew(1)       = zero
        snew(line_length) = one

      else if ( ladapt_jumpflag == 1 ) then

        call shock_loc(n_pressure_k(1), line_length, dep, fsh1, sold, snew)

      else if ( ladapt_jumpflag == 2 ) then

        call shock_loc(n_density,       line_length, dep, fsh1, sold, snew)

      else if ( ladapt_jumpflag == 3 ) then

        call shock_loc(n_temperature_j(1), line_length, dep, fsh1, sold, snew)

      else if ( ladapt_jumpflag == 4 ) then

        snew(1)       = zero
        snew(line_length) = ladapt_fctrjmp

      end if

      nstr = int( ladapt_fstr*(line_length-1) ) ! domain of stretched grid in bl

      dpi = pi / nstr

      if ( ladapt_beta_grd > one ) then
        brat = ( ladapt_beta_grd + one ) / ( ladapt_beta_grd - one )
        ans = real(line_length - 1)
        do n=1,line_length - 1
          bz = brat**( ( ans - n ) / ans )
          snez(n) = one  -  ladapt_beta_grd * ( bz - one ) / ( bz + one )
        end do

      else

        dh1 = dh0
        fctra = ( ladapt_fstr / dh1 ) ** ( one / nstr )  -  one
        ss1 = dh1
        snez(1) = ss1
        fctr1 = max( one, one + fctra * sin( dpi )  )
        dh1 = fctr1 * dh1
        ss1 = ss1  +  dh1
        snez(2) = ss1
        fctrmx = fctr1

        do n=3,line_length-1

          fctr1 = max(  one, one  +  fctra * sin( ( n - 1 ) * dpi )  )
          dh1   = min(  fctr1 * ( snez(n-1) - snez(n-2) ) ,                    &
                     ( one - snez(n-1) ) / ( line_length - n )  )
          ss1   = ss1 + dh1
          snez(n) = ss1
          if ( fctr1 > fctrmx ) then
            fctrmx = fctr1
          end if
        end do

!       CHECK if should be < 0.5 for not_ok

        if ( ss1 > 1.5_dp ) not_ok = not_ok + 1
        ss1 = one / ss1

        do n=1,line_length - 1
          snez(n) = snez(n) * ss1 ! normalize
        end do

        if ( ladapt_ep0_grd > zero ) then ! cluster grid at shock
          do n=1,line_length - 1
            snezn = snez(n)
            ep = snezn**2 * ( one - snezn ) * ladapt_ep0_grd
            snez(n) = ( one - ep ) * snezn  +  fsh1 * ep
          end do
        end if

      end if

!     Interpolate dependent variables on new grid

      newlen = sold(line_length)*snew(line_length)
      newlen = min( newlen, ladapt_max_distance )

      do n=1,line_length-1
        snew(n) = snez(n) * newlen
      end do

      dep(1:n_tot,line_length) = q0(1:n_tot)

      order = 1
      call q4iuniv (line_length, sold, dep, line_length-1, snew, dnew, order )

      do n=1,line_length-1
        node = node_map(n0 + n )
        qnode(1:n_tot,node) = dnew(1:n_tot,n)
      end do

!   interpolate coordinates (x,y,z) from old grid to new grid

      do n = 1, line_length
        node = node_map(n0 + n - 1)
        dep(1,n) = x(node)
        dep(2,n) = y(node)
        dep(3,n) = z(node)
      end do
      order = 2
      call q4iuniv (line_length, sold, dep(1:3,:), line_length-1, snew,        &
                    dnew(1:3,:), order)
      do n=1,line_length - 1
        node = node_map(n0 + n)
        x(node) = dnew(1,n)
        y(node) = dnew(2,n)
        z(node) = dnew(3,n)
      end do
    end do

    if ( not_ok > 0 ) write (6,3000) not_ok
    3000 format ( '        WARNING:  Recell_w criterion not satisfied in ',    &
                                 '"algnshk" for a total',                      &
            /, 'of ', i4, ' stations (see files ',                             &
               '"algnshk.out" & "grid.out").', / )

    deallocate(dep,dnew)
    deallocate(snez,snew,sold)

  end subroutine align_shock

!================================= SHOCK_LOC =================================80
!
!  Detect shock location along grid line
!
!=============================================================================80

  subroutine shock_loc ( index, line_length, q, fsh1, sold, snew)

    use refine_adaptation_input,   only : ladapt_fsh, ladapt_fctrjmp
    use ivals,                     only : q0

    integer,                  intent(in)    :: index
    integer,                  intent(in)    :: line_length
    real(dp), dimension(:,:), intent(in)    :: q
    real(dp),                 intent(out)   :: fsh1
    real(dp), dimension(:),   intent(in)    :: sold
    real(dp), dimension(:),   intent(inout) :: snew

    real(dp) :: atest
    real(dp) :: proprty, proprty1
    real(dp) :: dtest, dtestref
    integer  :: n

    continue

    fsh1 = ladapt_fsh

    snew(1)           = zero
    snew(line_length) = one
    proprty           = q0(index)
    dtestref          = ladapt_fctrjmp * proprty
    n_loop: do n=line_length-1,2,-1
      proprty1 =  proprty
      proprty  = q(index,n)
      dtest    = dtestref - proprty
      if ( dtest < -1.e-6_dp ) then ! shock located

!       load stretching/compression factor for this line of nodes

        atest = dtest*( sold(n+1) - sold(n) )/(proprty1 - proprty)  +  sold(n)
        snew(line_length) = atest/( fsh1*sold(line_length) )
        exit n_loop
      end if
    end do n_loop

  end subroutine shock_loc

!================================== Q4IUNIV ==================================80
!
! Legacy routine for interpolation along a curved grid line
!
!=============================================================================80

  subroutine q4iuniv(n, x, g, m, y, f, order )

    integer, intent(in) :: n ! number of points in master
    integer, intent(in) :: m ! number of points in adapted
    integer, intent(in) :: order

    real(dp), dimension(:),   intent(in)  :: x ! master independent variable
    real(dp), dimension(:,:), intent(in)  :: g ! master   dependent variable
    real(dp), dimension(:),   intent(in)  :: y ! adapted independent variable
    real(dp), dimension(:,:), intent(out) :: f ! adapted   dependent variable

    logical :: match_found

    integer :: i, j, k

    real(dp) :: dxi, c0, c1

    continue

    c0 = one ! to fool g95 compiler warning may be used uninitialized
    c1 = zero ! to fool g95 compiler warning may be used uninitialized
    k = 1 ! initialize index to search through master grid

    loop_adapted: do j=1,m

      if ( y(j) <= x(1) ) then

!     extrapolate low side

        dxi = one / ( x(2) - x(1) )
        k = 1
        if ( order == 1 ) then ! first-order: move to closest point
          c0 = one
          c1 = zero
        else
          c0 = ( x(2) - y(j) ) * dxi
          c1 = ( y(j) - x(1) ) * dxi
        end if

      else if ( y(j) >= x(n) ) then

!     extrapolate high side

        dxi = one / ( x(n) - x(n-1) )
        k = n - 1
        if ( order == 1 ) then ! first-order: move to closest point
          c1 = one
          c0 = zero
        else
          c0 = ( x(n) - y(j  ) ) * dxi
          c1 = ( y(j) - x(n-1) ) * dxi
        end if

      else

!     between high and low values at extrema of master grid

        match_found = .false.
        loop_master: do i=k,n-1

          if ( y(j) <= x(i+1) ) then

            dxi = one / ( x(i+1) - x(i) )
            k = i
            c0 = ( x(i+1) - y(j) ) * dxi

            if ( order == 1 ) then ! go to closest point

              if ( c0 > half ) then
                c0 = one
                c1 = zero
              else
                c0 = zero
                c1 = one
              end if

            else ! interpolate

              c1 = ( y(j) - x(i) ) * dxi

            end if

            match_found = .true.
            exit loop_master

          end if

        end do loop_master

        if( .not. match_found)then
          write (6,*)"Interpolation failed in q4iuniv for j = ",j
          write (6,*)"y(j) =",y(j)
          write (6,*)"x = ",x
          stop
        end if

      end if

      f(:,j) = c0 * g(:,k)  +  c1 * g(:,k+1)

    end do loop_adapted

  end subroutine q4iuniv

!============================= LINE_FIT_SHOCK ================================80
!
! Adapt the line lengths so that the freestream boundary fits the shock
!
!=============================================================================80

  subroutine line_fit_shock( n_lines, line_length, pencil_length, first_index, &
                             node_map, ndim, n_tot, nbound, grid, x, y, z,     &
                             cdt, qnode, eqn_set, nnodes0, nnodes01 )

    use grid_types,              only : grid_type
    use bc_names,                only : farfield_shkfit
    use info_depr,               only : ivisc, xmach, re, tref
    use refine_adaptation_input, only : ladapt_re_cell, ladapt_ep0_grd,        &
                                        ladapt_fstr, ladapt_g_limiter,         &
                                        ladapt_fsh, sfadapt_shkdtct,           &
                                        sfadapt_ceqinc, sfadapt_fsfrac0,       &
                                        sfadapt_fsfraci,                       &
                                        sfadapt_grdspd, sfadapt_fsbuffr
    use generic_gas_map,         only : n_momx, n_momz,                        &
                                        n_amu_k, n_density,                    &
                                        n_sonic_k, n_pressure_k
    use lmpi,                    only : lmpi_conditional_stop, lmpi_nproc,     &
                                        lmpi_allgather, lmpi_min, lmpi_reduce, &
                                        lmpi_master, lmpi_bcast
    use ivals,                   only : rho0, u0, v0, w0, p0, q0, density0
    use solution_types,          only : generic_gas
    use fluid,                   only : sutherland_constant, gamma

    integer,                                 intent(in) :: n_lines
    integer,                                 intent(in) :: line_length
    integer,                                 intent(in) :: pencil_length
    integer,                                 intent(in) :: ndim
    integer,                                 intent(in) :: n_tot
    integer,                                 intent(in) :: eqn_set
    integer,                                 intent(in) :: nbound
    integer,                                 intent(in) :: nnodes0
    integer,                                 intent(in) :: nnodes01
    integer, dimension(n_lines+1),           intent(in) :: first_index
    integer, dimension(pencil_length),       intent(in) :: node_map

    type(grid_type),                      intent(inout) :: grid

    real(dp), dimension(nnodes0),            intent(in) :: cdt
    real(dp), dimension(nnodes01),        intent(inout) :: x
    real(dp), dimension(nnodes01),        intent(inout) :: y
    real(dp), dimension(nnodes01),        intent(inout) :: z
    real(dp), dimension(n_tot, nnodes01), intent(inout) :: qnode

    logical :: shock_clustering
    logical :: cluster_at_shock

    integer  :: not_ok
    integer  :: order
    integer  :: nstr
    integer  :: line, line_length_l, loc_on_line
    integer  :: n, n0, nodell, nbufrnd
    integer  :: node, node0, node1, node2
    integer  :: ib, ibc, nbnode, i, inode

    real(dp) :: dist
    real(dp) :: ep
    real(dp) :: fsh1
    real(dp) :: fctr1, fctra
    real(dp) :: fctrmx
    real(dp) :: pi, dpi
    real(dp) :: amul
    real(dp) :: rhowall
    real(dp) :: hmin_old, dh1, hmin, hmin1, hmin2, hmin3
    real(dp) :: ss1, snezn
    real(dp) :: ds, cwall
    real(dp) :: my_h, dh0
    real(dp) :: newlen
    real(dp) :: mu1, mu2, xmr, cstar, cw1, cw2
    real(dp) :: dxsn, dysn, dzsn, area
    real(dp) :: dx1, dy1, dz1, ds1
    real(dp) :: dxlt, dylt, dzlt, dslt, shkdslt
    real(dp) :: shkspd, shkcdt_min, shksoff_min
    real(dp) :: localreal
    real(dp) :: shkspdl2, shkdsl2, gnlines
    real(dp) :: sum_shkfitnd, sum_shkftfss
    real(dp) :: pbndvlcty, pshktrans, pshkinter
    real(dp) :: fsfraci, ceqinc, shkdtct

    real(dp), dimension(ndim) :: ql, qr

    real(dp), dimension(:),   allocatable :: my_min
    real(dp), dimension(:),   allocatable :: snez, snew, sold
    real(dp), dimension(:),   allocatable :: shkcdt
    real(dp), dimension(:,:), allocatable :: dep, dnew
    real(dp), dimension(:,:), allocatable :: shkdsn

    real(dp), dimension(:),   allocatable, save :: shkspd_old
    real(dp), dimension(:),   allocatable, save :: shkfitnd
    real(dp), dimension(:),   allocatable, save :: shkftfss
    real(dp), dimension(:),   allocatable, save :: shkdslt_old

    real(dp), parameter :: rlxshkds = 0.10_dp

  continue

    cluster_at_shock = .false.
    if (ladapt_ep0_grd > zero) then
      shock_clustering = .true.
    else
      shock_clustering = .false.
    end if

    fsfraci = max(zero, min(one, sfadapt_fsfraci))
    ceqinc  = max(zero, min(one, sfadapt_ceqinc))
    shkdtct = max(zero, min(one, sfadapt_shkdtct))
    nbufrnd = min(nint((1.0_dp-ladapt_fsh)*line_length), sfadapt_fsbuffr)
    nbufrnd = max(0, nbufrnd)

    hmin1 = zero
    hmin2 = zero
    hmin3 = zero
    pi = acos( -one )

!   loc_on_line = line_length
    loc_on_line = line_length - nbufrnd

    not_ok = 0

!   Check that all lines have same number of nodes; else algorithm fails

    do line = 1,n_lines
      line_length_l = first_index(line+1) - first_index(line)
      if (line_length_l /= line_length) then
        call lmpi_conditional_stop(1,                                          &
              'The number of nodes in a line is not a constant.')
      end if
    end do

!   Determine the shock fitting boundary shock node time step and
!   compute the shock fitting boundary node shock speeds

    shkcdt_min = huge(0.0_dp)

    allocate( shkdsn(3,n_lines) )
    allocate( shkcdt(n_lines) )

    if (.not.allocated(shkspd_old)) then
      allocate( shkspd_old(n_lines) )
      shkspd_old = zero
    end if

    if (.not.allocated(shkfitnd)) then
      allocate( shkfitnd(n_lines) )
      shkfitnd = zero
    end if

    if (.not.allocated(shkftfss)) then
      allocate( shkftfss(n_lines) )
      shkftfss = sfadapt_fsfrac0
    end if

    if (.not.allocated(shkdslt_old)) then
      allocate( shkdslt_old(n_lines) )
      shkdslt_old = zero
    end if

    shkspdl2 = zero

    if (lmpi_master) then
      write(*,*) ' '
      write(*,*) ' Shock fitting the free-stream boundary'
!     write(*,*) ' 1) Computing shock speed '
    end if

    loop_ovr_lines: do line = 1,n_lines

      n0    = first_index(line)
      node0 = node_map(n0)

      if (node0 > nnodes0) cycle

      nodell = node_map(n0 + line_length - 1)
      node1  = node_map(n0 + loc_on_line - 1)
      if (node1 == nodell) then
        node0  = node1
      else
        node0  = node1 + 1
      end if
      node2  = node_map(n0 + loc_on_line - 2)

      loop_ovr_bc_patches: do ib = 1, nbound

        ibc = grid%bc(ib)%ibc

        if (ibc == farfield_shkfit) then

!         The current bc node type is shockfitting

          nbnode = grid%bc(ib)%nbnode
!
          loop_ovr_nodes_on_bc_patches: do i = 1, nbnode

!           Determine the local node number of the boundary node

            inode = grid%bc(ib)%ibnode(i)

            if (inode > nnodes0) cycle

            if (inode == nodell) then

!             Determine the min timestep on the shock fitting boundaries
!             that reside on this processor

              shkcdt(line) = cdt(inode)
              shkcdt_min = min(shkcdt_min, shkcdt(line))
!             write(*,*) "inode, cdt=", inode,cdt(inode)

!             Gather the node primitive variables from the node state vector
!             solve the shock fitting compatibility equations

              if (eqn_set == generic_gas) then

!               Generic gas

!               Convert freestream property vector to primitive

!               Set the node pre-shock (left) state vector

                if (node1 == nodell) then
                  ql(1)   = density0
                  ql(2:4) = q0(n_momx:n_momz)/ql(1)
                  ql(5)   = q0(n_pressure_k(1))
                else
                  ql(1)   = qnode(n_density,node0)
                  ql(2:4) = qnode(n_momx:n_momz,node0)/ql(1)
                  ql(5)   = qnode(n_pressure_k(1),node0)
                end if

!               Set the node post-shock (right) state vector

                qr(1)   = qnode(n_density,node1)
                qr(2:4) = qnode(n_momx:n_momz,node1)/qr(1)
                qr(5)   = qnode(n_pressure_k(1),node1)

!               qr(1)    = qnode(n_density,node2)
!               qr(2:4)  = qnode(n_momx:n_momz,node2)/qr(1)
!               qr(5)    = qnode(n_pressure_k(1),node2)
!               dqm(1)   = qnode(n_density,node3)-qnode(n_density,node4)
!               dqp(1)   = qnode(n_density,node2)-qnode(n_density,node3)
!               dqm(2:4)=qnode(n_momx:n_momz,node3)-qnode(n_momx:n_momz,node4)
!               dqp(2:4)=qnode(n_momx:n_momz,node2)-qnode(n_momx:n_momz,node3)
!               dqm(5)=qnode(n_pressure_k(1),node3)-qnode(n_pressure_k(1),node4)
!               dqp(5)=qnode(n_pressure_k(1),node2)-qnode(n_pressure_k(1),node3)

!               qr(1:5)  = qr(1:5) - vnleerpe(dqm(1:5),dqp(1:5),1.0e-9_dp)

              else

!               Calorically perfect gas

!               Set the node pre-shock (left) state vector

                ql(1) = rho0
                ql(2) = u0
                ql(3) = v0
                ql(4) = w0
                ql(5) = p0

!               Set the node post-shock (right) state vector

                qr(1:5) = qnode(1:5,node1)

              end if

              if (abs(ql(1)-qr(1))/max(ql(1),qr(1)) >= shkdtct) then
                shkfitnd(line) = one
                if (shkftfss(line) > fsfraci) then
                  shkftfss(line) = fsfraci
                end if
              else
                shkfitnd(line) = zero
                if (shkftfss(line) == fsfraci) then
                  shkftfss = fsfraci
                end if
              end if

              if (shkftfss(line) <= fsfraci) then
                shkftfss(line) = max(0.01_dp,shkftfss(line)*0.995_dp)
                if (shock_clustering .and. .not.cluster_at_shock) then
                  cluster_at_shock = .true.
                end if
              end if

!             Compute the boundary node unit normal vector components

              dxsn  = grid%bc(ib)%bxn(i)
              dysn  = grid%bc(ib)%byn(i)
              dzsn  = grid%bc(ib)%bzn(i)
              area  = sqrt(dxsn*dxsn + dysn*dysn + dzsn*dzsn)
              dxsn  = dxsn / area
              dysn  = dysn / area
              dzsn  = dzsn / area

!             Solve the shock fitting compatibility equations and
!             compute the node shock speed

              shkspd = shkftfss(line)*shk_fit_shkspd(ceqinc, shkfitnd(line),   &
                                                     dxsn, dysn, dzsn, ql, qr)

              shkspd = rlxshkds*shkspd + (one-rlxshkds)*shkspd_old(line)
!             shkspd_old = shkspd
              shkspd_old(line) = shkspd

              shkspdl2 = shkspdl2 + shkspd**2

!             Multiply the shock speed times the shock unit normal vector

              shkdsn(1,line) = dxsn * shkspd
              shkdsn(2,line) = dysn * shkspd
              shkdsn(3,line) = dzsn * shkspd

!             if (eqn_set == generic_gas) then
!               write(*,*) 'i, v_ref,shkspd(i) =', i,v_ref,shkspd*v_ref
!             else
!               write(*,*) 'i, c0,shkspd(i) =', i,c0,shkspd(i)*c0
!             endif
!             write(*,*) ' line =', line
!             write(*,*) ' ceqinc =', ceqinc
!             write(*,*) ' shkftfss =', shkftfss(line)
!             write(*,*) ' shkdtct  =', shkdtct

              exit loop_ovr_nodes_on_bc_patches

            end if

          end do loop_ovr_nodes_on_bc_patches

        end if

      end do loop_ovr_bc_patches

    end do loop_ovr_lines

!   Compute convergence information

    localreal = sum(shkfitnd)
    call lmpi_reduce(localreal, sum_shkfitnd)
    call lmpi_bcast(sum_shkfitnd)

    localreal = sum(shkftfss)
    call lmpi_reduce(localreal, sum_shkftfss)
    call lmpi_bcast(sum_shkftfss)

    localreal = shkspdl2
    call lmpi_reduce(localreal, shkspdl2)
    call lmpi_bcast(shkspdl2)

    localreal = real(n_lines, dp)
    call lmpi_reduce(localreal, gnlines)
    call lmpi_bcast(gnlines)

!   Compute the percentage of lines that will be fit to the shock
!   and compute the L2 of the shock speed

    if (lmpi_master) then
      pbndvlcty = sum_shkftfss / gnlines * 100.0_dp
      pshkinter = sum_shkfitnd / gnlines
      pshktrans = one - pshkinter
      pshktrans = pshktrans * 100.0_dp
      pshkinter = pshkinter * 100.0_dp
      shkspdl2  = sqrt(shkspdl2) / gnlines
      write(*,*)' Average boundary velocity scale coeff.=',pbndvlcty,' %'
      write(*,*)' Fraction of boundary moving  to shock =',pshktrans,' %'
      write(*,*)' Fraction of boundary fitting to shock =',pshkinter,' %'
      write(*,*)' L2 of (boundary/freestream) velocity  =',shkspdl2
      !write(*,*) ' Node speed treatment type:'
      !do line = 1,n_lines
      !  write(*,*) 'lmpi_id, line, shkfitnd=',lmpi_id,line,nint(shkfitnd(line))
      !end do
    end if

!   Find the minimum global shock fitting boundary shock time step

!   localreal = shkcdt_min
!   call lmpi_min(localreal, shkcdt_min)

    allocate( dep(n_tot,line_length) )
    allocate( dnew(n_tot,line_length-1) )
    allocate( snez(line_length) )
    allocate( sold(line_length) )
    allocate( snew(line_length) )

!   Determine where the minimum mesh requirement exists

    do line = 1,n_lines

      sold(1) = zero
      n0      = first_index(line)
      node0   = node_map(n0)

!     Is the node a level 0 node on the current processor?

      if (node0 > nnodes0) cycle

      do n = 2,line_length
        node0 = node_map(n0 + n - 2)
        node1 = node_map(n0 + n - 1)
        ds = sqrt((x(node1)-x(node0))**2 + (y(node1)-y(node0))**2              &
                + (z(node1)-z(node0))**2)
        sold(n) = sold(n-1) + ds
        dep(1:n_tot,n) = qnode(1:n_tot,node1)
      end do

      hmin     = zero
      hmin_old = one ! to fool g95 compiler warning may be used uninitialized
      node0    = node_map(n0)
      node1    = node_map(n0 + 1)

      if (ivisc == 0) then

        hmin_old = half / (line_length-1)

      else

        dist = sold(line_length) ! distance from wall to grid outer boundary

        if (eqn_set == generic_gas) then

          rhowall = half*max(one, qnode(n_density,node0)+qnode(n_density,node1))
          amul = half*(qnode(n_amu_k(1),node0) + qnode(n_amu_k(1),node1))
          ! use effective viscosity here, even if turbulent
          cwall   = half*(qnode(n_sonic_k(1),node0) + qnode(n_sonic_k(1),node1))

        else

          rhowall = half*max(one, qnode(1,node0) + qnode(1,node1))
          xmr = xmach/re
          cstar = sutherland_constant/tref
          mu1 = viscosity_law(cstar,gamma*qnode(5,node0)/qnode(1,node0))*xmr
          mu2 = viscosity_law(cstar,gamma*qnode(5,node1)/qnode(1,node1))*xmr
          amul = half*(mu1 + mu2)
          cw1 = sqrt(gamma*qnode(5,node0)/qnode(1,node0))
          cw2 = sqrt(gamma*qnode(5,node1)/qnode(1,node1))
          cwall = half*(cw1 + cw2)

        end if

!       define spacing for cell Re = ladapt_re_cell at wall

        hmin1 = ladapt_re_cell*amul/(rhowall * cwall * dist )
        hmin2 = one / line_length
        hmin3 = ladapt_g_limiter * ladapt_re_cell/dist

        if (hmin3 <= zero) hmin3 = hmin2

        hmin = min(hmin1, hmin2, hmin3)

        if (line == 1) hmin_old = hmin

        hmin_old = min(hmin,hmin_old)

      end if

      my_h = hmin_old

    end do

    allocate( my_min(lmpi_nproc) )
    my_min = zero
    call lmpi_allgather(my_h, my_min)
    dh0 = minval( my_min, mask=my_min > zero)
    deallocate( my_min )

!   if (lmpi_master) then
!     write(*,*) ' 2) Computing shock movement and adapting the grid'
!   end if

    shksoff_min = 999999999.0_dp
    shkdsl2     = zero

!   Compute new line lengths to fit the boundary to the shock

    loop_ovr_lines2: do line = 1,n_lines

      n0    = first_index(line)
      node0 = node_map(n0)
      nodell = node_map(n0 + line_length - 1)

!     Is the node a level 0 node on the current processor?

      if (node0 > nnodes0)cycle

!     Compute the Cartesian coordinates for the end of each line

      node0 = node_map(n0 + loc_on_line - 3)
      node1 = node_map(n0 + loc_on_line - 2)
      node2 = node_map(n0 + loc_on_line - 1)

!     grid spacing at the shock fitting node

      dx1 = x(node2) - x(node1)
      dy1 = y(node2) - y(node1)
      dz1 = z(node2) - z(node1)
      ds1 = sqrt (dx1*dx1 + dy1*dy1 + dz1*dz1)

!     1st order line tangency vector

!     dxlt = x(node2) - x(node1)
!     dylt = y(node2) - y(node1)
!     dzlt = z(node2) - z(node1)

!     2nd order line tangency vector

      dxlt = (3.0_dp*x(node2) - 4.0_dp*x(node1) + x(node0)) / 2.0_dp
      dylt = (3.0_dp*y(node2) - 4.0_dp*y(node1) + y(node0)) / 2.0_dp
      dzlt = (3.0_dp*z(node2) - 4.0_dp*z(node1) + z(node0)) / 2.0_dp

      dslt = sqrt(dxlt*dxlt + dylt*dylt + dzlt*dzlt)

!     Line tangent unit vector

      dxlt = dxlt / dslt
      dylt = dylt / dslt
      dzlt = dzlt / dslt

!     Project the shock normal displacement vector onto the line tangency vector

      shkdslt = shkdsn(1,line)*dxlt + shkdsn(2,line)*dylt  +shkdsn(3,line)*dzlt
!     shkdslt = shkdslt * shkcdt_min
      shkdslt = shkdslt * shkcdt(line)

!     write(*,*) ' line =', line
!     write(*,*) ' shkdslt =', shkdslt

!     Compute independent and gather dependent variable arrays

      sold(1) = zero
      n0      = first_index(line)
      node0   = node_map(n0)

      if (node0 > nnodes0) cycle

      dep(1:n_tot,1) = qnode(1:n_tot,node0)

      do n = 2,line_length

        node0 = node_map(n0 + n - 2)
        node1 = node_map(n0 + n - 1)

        if(node1 > nnodes0)then
          write(6,*)"line, n, node1 = ",line, n, node1
          call lmpi_conditional_stop(1,                                        &
              'All nodes of this line are not on the same partition.')
        end if

        ds = sqrt((x(node1)-x(node0))**2 + (y(node1)-y(node0))**2              &
                + (z(node1)-z(node0))**2)
        sold(n) = sold(n-1) + ds
        dep(1:n_tot,n) = qnode(1:n_tot,node1)

      end do

!     Compute the l2 of the relative shock movement

!     shkdsl2 = shkdsl2 + (shkdslt/sold(line_length))**2
      shkdsl2 = shkdsl2 + (shkdslt/ds1)**2

!     Compute new idependent parametric variable array
!     which includes the effect of the shock movement

      snew(1)           = zero
      snew(line_length) = sold(line_length) + shkdslt

      if (line == 1) then
!       write(*,*) ' line =', line
!       write(*,*) ' node0, x, z =', x(node0), z(node0)
!       write(*,*) ' node1, x, z =', x(node1), z(node1)
!       write(*,*) ' sold, snew =', sold(line_length), snew(line_length)
!       write(*,*) ' old, x, z =', x(node2), z(node2)
      end if

      snew(line_length) = snew(line_length)/sold(line_length)

!     do n = 2,line_length
!       snew(n) = snew(line_length)/sold(line_length)
!       write(*,*) ' n,snew/sold =', n,snew(m)
!     end do

!     write(*,*) ' snew(p)= snew/sold =', snew(line_length)

!     Domain of stretched grid in the boundary layer

      nstr = int( ladapt_fstr*(line_length-1) )

      dpi = pi / nstr

      dh1 = dh0
      fctra = ( ladapt_fstr / dh1 ) ** ( one / nstr )  -  one
      ss1 = dh1
      snez(1) = ss1
      fctr1 = max( one, one + fctra * sin( dpi )  )
      dh1 = fctr1 * dh1
      ss1 = ss1  +  dh1
      snez(2) = ss1
      fctrmx = fctr1

      do n=3,line_length-1
        fctr1 = max(  one, one  +  fctra * sin( ( n - 1 ) * dpi )  )
        dh1   = min(  fctr1 * ( snez(n-1) - snez(n-2) ) ,                    &
                   ( one - snez(n-1) ) / ( line_length - n )  )
        ss1   = ss1 + dh1
        snez(n) = ss1
        if (fctr1 > fctrmx) then
          fctrmx = fctr1
        end if
      end do

!     CHECK if should be < 0.5 for not_ok

      if (ss1 > 1.5_dp) not_ok = not_ok + 1

      ss1 = one / ss1

      do n = 1,line_length - 1
        snez(n) = snez(n) * ss1 ! normalize
      end do

      if (cluster_at_shock) then ! cluster grid at shock

        fsh1 = one

        do n = 1,line_length
          snezn = snez(n)
          ep = snezn**2 * ( one - snezn ) * ladapt_ep0_grd
          snez(n) = ( one - ep ) * snezn  +  fsh1 * ep
        end do

      end if

!     Interpolate dependent variables on new grid

      newlen = sold(line_length)*snew(line_length)

      do n = 1,line_length-1
        snew(n) = snez(n) * newlen
      end do

      shksoff_min       = min(shksoff_min, snew(loc_on_line))

!     Interpolate dependent variables on new grid

      if (.not.sfadapt_grdspd) then

!       dep(1:n_tot,line_length) = (one-shkfitnd(line))*q0(1:n_tot) +          &
!                                   shkfitnd(line)*qnode(1:n_tot,nodell)
!       dep(1:n_tot,line_length) = q0(1:n_tot)
        dep(1:n_tot,line_length) = qnode(1:n_tot,nodell)

        order = 1
        call q4iuniv (line_length, sold, dep, line_length-1, snew, dnew, order )

        do n = 1,line_length-1
          node = node_map(n0 + n)
          qnode(1:n_tot,node) = dnew(1:n_tot,n)
        end do

      end if

!     dep(1:n_tot,line_length) = q0(1:n_tot)

!   Interpolate coordinates (x,y,z) from old grid to new grid

      do n = 1,line_length
        node = node_map(n0 + n - 1)
        dep(1,n) = x(node)
        dep(2,n) = y(node)
        dep(3,n) = z(node)
      end do

!     if (line == 1) then
!       node2 = node_map(n0 + line_length - 1)
!       write(*,*) ' before node2, x, z =', node2, x(node2), z(node2)
!       write(*,*) ' snew =', snew(line_length)
!     end if

      order = 2
      call q4iuniv (line_length, sold, dep(1:3,:), line_length-1, snew,        &
                    dnew(1:3,:), order)

!     Compute the grid motion terms

      if (sfadapt_grdspd) then

        do n = 1,line_length - 1
          node = node_map(n0 + n)
          grid%dxdt(node) = (dnew(1,n) - x(node)) / cdt(node)
          grid%dydt(node) = (dnew(2,n) - y(node)) / cdt(node)
          grid%dzdt(node) = (dnew(3,n) - z(node)) / cdt(node)
!         grid%dxdt(node) = (dnew(1,n) - x(node)) / shkcdt_min
!         grid%dydt(node) = (dnew(2,n) - y(node)) / shkcdt_min
!         grid%dzdt(node) = (dnew(3,n) - z(node)) / shkcdt_min
!         grid%dxdt(node) = zero
!         grid%dydt(node) = zero
!         grid%dzdt(node) = zero
        end do

!       Extract the boundary grid motion terms

        do ib = 1, nbound

          ibc = grid%bc(ib)%ibc

          if (ibc == farfield_shkfit) then

!           The current bc node type is shockfitting

            nbnode = grid%bc(ib)%nbnode
!
            do i = 1, nbnode

!             Determine the local node number of the boundary node

              inode = grid%bc(ib)%ibnode(i)

              if (inode > nnodes0) cycle

              if (inode == nodell) then

                grid%bc(ib)%bdxdt(i) = grid%dxdt(inode)
                grid%bc(ib)%bdydt(i) = grid%dydt(inode)
                grid%bc(ib)%bdzdt(i) = grid%dzdt(inode)

              end if

            end do

          end if

        end do

      end if

!     Compute the new grid point locations

      do n = 1,line_length - 1
        node = node_map(n0 + n)
        x(node) = dnew(1,n)
        y(node) = dnew(2,n)
        z(node) = dnew(3,n)
      end do

!     if (line == 1) then
!       node2 = node_map(n0 + line_length - 1)
!       write(*,*) ' new, x, z =', x(node2), z(node2)
!       write(100,*) line, x(node2), z(node2)
!       write(*,*) ' snew =', snew(line_length)
!     end if

    end do loop_ovr_lines2

    localreal = shkdsl2
    call lmpi_reduce(localreal, shkdsl2)

    localreal = shksoff_min
    call lmpi_min(localreal, shksoff_min)

    if (lmpi_master) then
      shkdsl2 = sqrt(shkdsl2) / gnlines
      write(*,*)' L2 of (boundary displace/line length) =', shkdsl2
      write(*,*)' Minimum shock stand off distance      =', shksoff_min
      write(*,*)' '
    end if

    if (not_ok > 0) write (6,3000) not_ok
    3000 format ( '        WARNING:  Recell_w criterion not satisfied in ',    &
                                 '"algnshk" for a total',                      &
            /, 'of ', i4, ' stations (see files ',                             &
               '"algnshk.out" & "grid.out").', / )

    deallocate( shkdsn )
    deallocate( shkcdt )
    deallocate( dep, dnew )
    deallocate( snez, snew, sold )

  end subroutine line_fit_shock

!============================= SHKFIT_LINE_PSPV ==============================80
!
!  Computes line based shock fitting interior (post shock) property vector
!
!=============================================================================80

  subroutine shkfit_line_pspv ( nnodes0, nnodes01, n_tot, ndim, igrid, ibnode, &
                                qnode, q_pstshk )

    integer,                             intent(in) :: nnodes0
    integer,                             intent(in) :: nnodes01
    integer,                             intent(in) :: n_tot
    integer,                             intent(in) :: ndim
    integer,                             intent(in) :: igrid
    integer,                             intent(in) :: ibnode

    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode

    real(dp), dimension(n_tot),         intent(out) :: q_pstshk

    integer :: pencil_length, line_count, line_length

  continue

    line_count    = adapt_lines(igrid)%n_lines
    pencil_length = adapt_lines(igrid)%total_dofs
    line_length   = adapt_lines(igrid)%first_entry(2)                          &
                  - adapt_lines(igrid)%first_entry(1)

    call shkfit_line_ps_state( line_count, line_length, pencil_length,         &
                               adapt_lines(igrid)%first_entry,                 &
                               adapt_lines(igrid)%line_to_dof_index,           &
                               nnodes0, nnodes01, n_tot, ndim, ibnode,         &
                               qnode, q_pstshk )

  end subroutine shkfit_line_pspv

!============================= SHKFIT_LINE_PS_STATE ==========================80
!
!  Computes line based shock fitting post shock state vector
!
!=============================================================================80

  subroutine shkfit_line_ps_state( line_count, line_length, pencil_length,     &
                                   first_index, node_map, nnodes0, nnodes01,   &
                                   n_tot, ndim, ibnode, qnode, q_pstshk )

    use generic_gas_map, only : n_species, n_momx, n_momz, n_etot, n_density

    integer,                             intent(in) :: line_count
    integer,                             intent(in) :: line_length
    integer,                             intent(in) :: pencil_length
    integer,                             intent(in) :: nnodes0
    integer,                             intent(in) :: nnodes01
    integer,                             intent(in) :: n_tot
    integer,                             intent(in) :: ndim
    integer,                             intent(in) :: ibnode

    integer, dimension(line_count+1),    intent(in) :: first_index
    integer, dimension(pencil_length),   intent(in) :: node_map

    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode


    real(dp), dimension(n_tot),         intent(out) :: q_pstshk

    real(dp), dimension(ndim)       :: dqprimm, dqprimp, dqpriml
    real(dp), dimension(ndim+1,0:3) :: qprim

    integer :: line, n0, node0, nprim
    integer :: nodesm0, nodesm1, nodesm2, nodesm3

    real(dp) :: sum_massfrac

    real(dp), parameter :: gradient_limit = 1.0e-9

  continue

    nprim = n_species+5

! Loop over lines looking for which line end point has the smae
! node number as the current boundary condition node

    line_loop: do line = 1,line_count

      n0    = first_index(line)
      node0 = node_map(n0)

      if (node0 > nnodes0) cycle

! Node number of the node at the end of the line, i.e. at the shock boundary

      nodesm0 = node_map(n0 + line_length - 1)

! Is the current boundary node also the end node of the current line?

      if (ibnode == nodesm0) then

! Get node numbers that make up the reconstruction stencil

        nodesm1 = node_map(n0 + line_length - 2)
        nodesm2 = node_map(n0 + line_length - 3)
        nodesm3 = node_map(n0 + line_length - 4)

! 1st order fully upwind

        q_pstshk(1:n_tot)= qnode(1:n_tot,nodesm1)
        exit line_loop

! Compute primitive variables: density, mass fractions, u,v,w, and  pressure

! density

        qprim(n_species+1,1) = qnode(n_density,nodesm1)
        qprim(n_species+1,2) = qnode(n_density,nodesm2)
        qprim(n_species+1,3) = qnode(n_density,nodesm3)

! mass fractions

        qprim(1:n_species,1) = qnode(1:n_species,nodesm1) / qprim(n_species+1,1)
        qprim(1:n_species,2) = qnode(1:n_species,nodesm2) / qprim(n_species+1,2)
        qprim(1:n_species,3) = qnode(1:n_species,nodesm3) / qprim(n_species+1,3)
!
! u,v, and w
!
        qprim(n_species+2:n_species+4,1) = qnode(n_momx:n_momz,nodesm1)
        qprim(n_species+2:n_species+4,2) = qnode(n_momx:n_momz,nodesm2)
        qprim(n_species+2:n_species+4,3) = qnode(n_momx:n_momz,nodesm3)

! pressure
!
        qprim(n_species+5,1) = qnode(n_etot,nodesm1)
        qprim(n_species+5,2) = qnode(n_etot,nodesm2)
        qprim(n_species+5,3) = qnode(n_etot,nodesm3)
!
! Perform reconstruction at interior of shock using downwind data

! 2nd order fully upwind using a gradient limiter

! Form the 1-D successive gradients

        dqprimm(1:nprim) = qprim(1:nprim,2)-qprim(1:nprim,3)
        dqprimp(1:nprim) = qprim(1:nprim,1)-qprim(1:nprim,2)

! Limit the gradient

        dqpriml(1:ndim) = vnleerpe(dqprimm(1:ndim),dqprimp(1:ndim),&
                                   gradient_limit)

! Perform higher order reconstruction of the primitive variables

        qprim(1:nprim,0) = qprim(1:nprim,1) + dqpriml(1:nprim)

! Compute realizable species mass fractions and sum of species mass fractions

        qprim(1:n_species,0) = max(zero, min(one, qprim(1:n_species,0)))
        sum_massfrac = sum(qprim(1:n_species,0))
        q_pstshk(1:n_species) = qprim(1:n_species,0)/sum_massfrac

! Store density in the right place

        q_pstshk(n_density) = qprim(n_species+1,0)

! Compute species densities

        q_pstshk(1:n_species) = q_pstshk(1:n_species)*q_pstshk(n_density)

! Store u,v, and w in the right place

        q_pstshk(n_momx:n_momz) = qprim(n_species+2:n_species+4,0)

! Store pressure in the right place

        q_pstshk(n_etot) = qprim(n_species+5,0)

! N.B.: must add logic for additional equations and calorically perfect

        exit line_loop
      end if
    end do line_loop

  end subroutine shkfit_line_ps_state

  pure elemental &
  function vnleerpe(x,y,smal)
    ! Classic vanLeer flux/gradient limiter function
    real(dp), intent(in) :: x,y
    real(dp), intent(in) :: smal
    real(dp)             :: vnleerpe
    vnleerpe = (y*abs(x) + x*abs(y)) / (abs(x) + abs(y) + smal)
  end function vnleerpe

  include 'shk_fit_shkspd.f90'
  include 'viscosity_law.f90'

end module line_adaptation

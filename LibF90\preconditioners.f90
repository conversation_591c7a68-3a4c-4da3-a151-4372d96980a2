module preconditioners

  use kinddefs,       only : jp
  use solution_types, only : soln_type
  use comprow_types,  only : crow_flow
  use relax_types,    only : relax_type
  use info_depr,      only : ntt, skeleton
  use debug_defs,     only : bp_solution

  implicit none

  private

  public :: preconditioner

contains

!============================= PRECONDITIONER ================================80
!
! Calls various preconditioners
!
!=============================================================================80
  subroutine preconditioner(soln, crow, relaxation, grp_num)

    use kinddefs,        only : dp, dqp
    use ilu,             only : ilu0_nosubiterations
    use linearsolve,     only : eqn_group_relax_q
    use linearsolve_nodivcheck, only : nodivcheck_relax_q
    use lmpi,            only : lmpi_die
    use solution_types,  only : elasticity

    integer, intent(in) :: grp_num

    type(crow_flow),  intent(in)    :: crow
    type(relax_type), intent(in)    :: relaxation
    type(soln_type),  intent(inout) :: soln

    integer :: ntt_hold, skeleton_hold, i
    integer :: cur_lev = 1

    real(dp) :: field
    real(dqp) :: value

  continue

    if(soln%gmres_eqn_groups_entry == 5) then

! Check for non-trivial dual numbering
! This preconditioner does not handle this

      do i = 1, soln%dof0
        if ( crow%g2m(i) /= i ) then
          write(*,*)'precond/ilu0_nosubiterations cannot handle dual numbering.'
          call lmpi_die
        endif
      end do

      call ilu0_nosubiterations(soln%njac,                                     &
                      soln%eqn_groups(grp_num)%n_eqns,                         &
                      soln%eqn_groups(grp_num)%crow%nnz0,                      &
                      soln%eqn_groups(grp_num)%crow%ia,                        &
                      soln%eqn_groups(grp_num)%crow%ja,                        &
                      soln%eqn_groups(grp_num)%iau,                            &
!                     soln%eqn_groups(grp_num)%a_precond,                      &
                      soln%eqn_groups(grp_num)%a_diag_prec,                    &
                      soln%eqn_groups(grp_num)%a_off_prec,                     &
                      soln%eqn_groups(grp_num)%precond_rhs,                    &
                      soln%eqn_groups(grp_num)%precond_soln)

    else

!     Relax linear system with any of preconditioners from relaxation schedule

      soln%dq = 0.0_jp

      ntt_hold      = ntt
      skeleton_hold = skeleton
      ntt           = 0
      skeleton      = 0

      if ( .not.bp_solution .or. soln%eqn_set == elasticity) then
      value = -1._dqp
      call nodivcheck_relax_q( soln, crow, relaxation, cur_lev, field, value )
      else
      value = -1._dqp
      call eqn_group_relax_q( soln, crow, relaxation, cur_lev, field, value )
      endif

      ntt           = ntt_hold
      skeleton      = skeleton_hold

    end if

  end subroutine preconditioner

end module preconditioners

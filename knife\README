
knife

Boolean Subtraction Library for Polyhedra

The knife library calculates the boolean subtraction of arbitrary
watertight triangular polyhedra. The result of this subtraction is
also watertight triangular polyhedra. The triangular faces of the
resultant polyhedra are created with a Delaunay triangle mesher.
These polyhedra are suitable for performing cut cell partial
differential equation solutions (i.e., computational fluid flow
simulations). Tetrahedra as well as the median dual of a tetrahedral
mesh are standard inputs. The knife library is implemented with an
object-oriented flavor in the C language.

The knife library can be called by FUN3D <http://fun3d.larc.nasa.gov/>
to perform dual tetrahedra cut cell flow and adjoint solutions.

See the INSTALL file for build instructions.

<PERSON>
<EMAIL>
Mike.<PERSON>@NASA.Gov
(*************

Obtaining the lastest version with
% git clone ssh://cmb20.larc.nasa.gov/~mikepark/git/knife.git/


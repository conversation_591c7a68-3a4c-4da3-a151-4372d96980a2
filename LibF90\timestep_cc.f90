module timestep_cc

  use kinddefs, only : dp, i2

  implicit none

  private

  public :: cdt_cc, cdt_cc_i, cdt_cc_convection

  real(dp) :: cdt_min, cdt_max

  logical :: first_time_through = .true.

  logical :: cfl_viscous_terms

  integer(i2) :: skew_angle_to_reduce_cfl

contains

!================================ CDT_CC =====================================80
!
! Calculate a time step at each cell
! Note that this routine assumes conservative variables
!
! Cell-centered version
!
!=============================================================================80

  subroutine cdt_cc( fl, nface, ncell0, ncell01, qcell, amut,                  &
                     cdt, cell_vol, fptr,                                      &
                     area_face, xn_face, yn_face, zn_face,                     &
                     n_tot, bcc, cell_skewness, origin )

    use lmpi,                 only : lmpi_max, lmpi_master
    use info_depr,            only : ivisc
    use nml_nonlinear_solves, only : use_local_dt
    use fluid,                only : gamma, gm1
    use bc_types,             only : bcc_type
    use bc_names,             only : bc_null
    use flux_constants,       only : vterm_cdt, xmr, cstar
    use timeacc,              only : ramp_cfl
    use cc_defs,              only : cfl_viscous_terms_in_def,                 &
                                     cfl_skew_deg_cutoff, cc_full_visc_jac

    integer,                            intent(in)    :: fl, nface
    integer,                            intent(in)    :: ncell0, ncell01
    integer,                            intent(in)    :: n_tot
    integer,  dimension(6,nface),       intent(in)    :: fptr
    real(dp), dimension(ncell01),       intent(in)    :: cell_vol
    real(dp), dimension(nface),         intent(in)    :: area_face
    real(dp), dimension(nface),         intent(in)    :: xn_face
    real(dp), dimension(nface),         intent(in)    :: yn_face
    real(dp), dimension(nface),         intent(in)    :: zn_face
    real(dp), dimension(n_tot,ncell01), intent(in)    :: qcell
    real(dp), dimension(ncell01),       intent(in)    :: amut
    real(dp), dimension(ncell0),        intent(out)   :: cdt
    type(bcc_type),                     intent(in)    :: bcc
    integer(i2), dimension(ncell01),    intent(inout) :: cell_skewness
    integer,                            intent(in)    :: origin

    integer :: i, n, cell, cell1, cell2

    integer(i2) :: iskew

    real(dp) :: area, c, c1, c2, e1, e2, p1, p2, rho1, rho2, u, u1, u2, v, v1
    real(dp) :: v2, w, w1, w2, xnorm, ynorm, znorm
    real(dp) :: rho_avgi, vol_avgi, mut_avg
    real(dp) :: term_inv, term_vis, term, cfl, cflv, cflv_min

  continue

    cfl_viscous_terms = .false.
    if ( (ivisc >= 2) .and.                                  &
          cfl_viscous_terms_in_def ) cfl_viscous_terms = .true.
    if ( origin == 3 )  cfl_viscous_terms = .false.
    skew_angle_to_reduce_cfl = cfl_skew_deg_cutoff

    cfl = ramp_cfl(fl)
    cflv_min = cfl
    if ( cfl_viscous_terms ) cflv_min = huge(1.0_dp)

! First loop over cells and zero out cdt

    local_dt: if ( use_local_dt ) then

      do i = 1, ncell0
        cdt(i) = 0._dp
      end do

! Start by getting interior contributions

      face_loop: do n = 1, nface

        cell1 = fptr(1,n)
        cell2 = fptr(2,n)

!       get normal to face and area

        xnorm = xn_face(n)
        ynorm = yn_face(n)
        znorm = zn_face(n)
        area  = area_face(n)

        xnorm = xnorm*area
        ynorm = ynorm*area
        znorm = znorm*area

        rho1 = qcell(1,cell1)
        u1   = qcell(2,cell1)/rho1
        v1   = qcell(3,cell1)/rho1
        w1   = qcell(4,cell1)/rho1
        e1   = qcell(5,cell1)
        p1   = gm1*(e1 - 0.5_dp*rho1*(u1*u1 + v1*v1 + w1*w1))
        c1   = sqrt(gamma*p1/rho1)

        rho2 = qcell(1,cell2)
        u2   = qcell(2,cell2)/rho2
        v2   = qcell(3,cell2)/rho2
        w2   = qcell(4,cell2)/rho2
        e2   = qcell(5,cell2)
        p2   = gm1*(e2 - 0.5_dp*rho2*(u2*u2 + v2*v2 + w2*w2))
        c2   = sqrt(gamma*p2/rho2)

!       get average values on face

        u    = 0.5_dp*(u1 + u2)
        v    = 0.5_dp*(v1 + v2)
        w    = 0.5_dp*(w1 + w2)
        c    = 0.5_dp*(c1 + c2)

        term_inv = abs(u*xnorm + v*ynorm + w*znorm) + c*area

        term_vis = 0._dp
        if ( cfl_viscous_terms ) then
          !...from CFL3D User's Manual (B-28)
          rho_avgi = 2.0_dp/( rho1 + rho2 )
          vol_avgi = 2.0_dp/( cell_vol(cell1) + cell_vol(cell2) )
          mut_avg  = viscosity_law( cstar, c )                 &
                   + 0.5_dp*( amut(cell1) + amut(cell2) )
          term_vis = 2._dp*area*area*vol_avgi*    &
                     mut_avg*vterm_cdt*xmr*rho_avgi
          iskew = max( cell_skewness(cell1) , cell_skewness(cell2) )
          cflv  = cfl_visc( iskew , cfl )
          !...scale viscous contribution by ratio of inviscid/viscous
          term_vis = term_vis*cfl/cflv
          cflv_min = min( cflv, cflv_min )
        endif

        term = term_inv + term_vis

        if (cell1 <= ncell0) cdt(cell1) = cdt(cell1) + term
        if (cell2 <= ncell0) cdt(cell2) = cdt(cell2) + term

      end do face_loop

! Finish the boundary closure

      faces : do n = 1, bcc%n_faces0

        if ( bcc%ibc(n) == bc_null ) cycle faces

        cell = bcc%cell(n)

        term_inv = bcc%cdt_bc_inv(n)
        term_vis = bcc%cdt_bc_vis(n)
        if ( cfl_viscous_terms ) then
          cflv = cfl_visc( cell_skewness(cell) , cfl )
          !...scale viscous contribution by ratio of inviscid/viscous
          term_vis = term_vis*cfl/cflv
          cflv_min = min( cflv, cflv_min )
        endif

        term = term_inv + term_vis

        cdt( cell ) = cdt( cell ) + term

      enddo faces

! Now cdt has sum of inviscid and (possibly) viscous terms

      do i = 1, ncell0
        cdt(i) = cell_vol(i)/cdt(i)
      end do

    else

! If not doing local time stepping just set cdt=1

      do i = 1, ncell0
        cdt(i) = 1._dp
      end do

    end if local_dt

    if ( first_time_through .and. use_local_dt ) then
      cdt_max = -1._dp
      cdt_min = huge(1._dp)
      do i = 1, ncell0
        cdt_max = max( cdt_max, cdt(i) )
        cdt_min = min( cdt_min, cdt(i) )
      end do
      term = cdt_max
      call lmpi_max(term,cdt_max)
      term = -cdt_min
      call lmpi_max(term,cdt_min) ; cdt_min = -cdt_min
      term = -cflv_min
      call lmpi_max(term,cflv_min) ; cflv_min = -cflv_min
      if ( lmpi_master ) then
        write(*,"(1x,a,e12.5)") ' Time step               CFL=',cfl
        write(*,"(1x,a,L12)")    ' Time step cfl_viscous_terms=',&
                                             cfl_viscous_terms
        write(*,"(1x,a,L12)")    ' Time step  cc_full_visc_jac=',&
                                              cc_full_visc_jac
        write(*,"(1x,a,2e12.5)") ' Time step  CFL(viscous)_min=',cflv_min
        write(*,"(1x,a,2e12.5)") ' Time step            dt_min=',cdt_min*cfl
        write(*,"(1x,a,2e12.5)") ' Time step            dt_max=',cdt_max*cfl
        write(*,"(1x,a,2e12.5)") ' Time step           max/min=',cdt_max/cdt_min
      endif
    elseif ( first_time_through ) then
      if ( lmpi_master ) then
        write(*,"(1x,a,2e12.5)") ' Time step...global constant time step'
      endif
    endif
    first_time_through = .false.

  end subroutine cdt_cc

!================================ CDT_CC_I ===================================80
!
! Calculate a time step at each cell
! Note that this routine assumes conservative variables
!
! Cell-centered version
! Incompressible version
!
!=============================================================================80

  subroutine cdt_cc_i( fl, nface, ncell0, ncell01, qcell, amut,                &
                       cdt, cell_vol, fptr,                                    &
                       area_face, xn_face, yn_face, zn_face, n_tot, bcc,       &
                       cell_skewness, origin )

    use lmpi,                 only : lmpi_master, lmpi_max
    use info_depr,            only : beta, ivisc
    use nml_nonlinear_solves, only : use_local_dt
    use bc_types,             only : bcc_type
    use bc_names,             only : bc_null
    use flux_constants,       only : xmr
    use timeacc,              only : ramp_cfl
    use cc_defs,              only : cfl_viscous_terms_in_def,                 &
                                     cfl_skew_deg_cutoff

    integer,                            intent(in)    :: fl, nface
    integer,                            intent(in)    :: ncell0, ncell01
    integer,                            intent(in)    :: n_tot
    integer, dimension(6,nface),        intent(in)    :: fptr
    real(dp), dimension(ncell01),       intent(in)    :: cell_vol
    real(dp), dimension(nface),         intent(in)    :: area_face
    real(dp), dimension(nface),         intent(in)    :: xn_face
    real(dp), dimension(nface),         intent(in)    :: yn_face
    real(dp), dimension(nface),         intent(in)    :: zn_face
    real(dp), dimension(n_tot,ncell01), intent(in)    :: qcell
    real(dp), dimension(ncell01),       intent(in)    :: amut
    real(dp), dimension(ncell0),        intent(out)   :: cdt
    type(bcc_type),                     intent(in)    :: bcc
    integer(i2), dimension(ncell01),    intent(inout) :: cell_skewness
    integer,                            intent(in)    :: origin

    integer :: i, n, cell, cell1, cell2

    integer(i2) :: iskew

    real(dp) :: area, c, u, u1, u2, v, v1, v2, w, w1, w2
    real(dp) :: xnorm, ynorm, znorm, face_speed, fspd_half, ubar
    real(dp) :: vol_avgi, mut_avg
    real(dp) :: term, term_inv, term_vis, cfl, cflv, cflv_min

  continue

    cfl_viscous_terms = .false.
    if ( (ivisc >= 2) .and.                                  &
         cfl_viscous_terms_in_def ) cfl_viscous_terms = .true.
    if ( origin == 3 )  cfl_viscous_terms = .false.
    skew_angle_to_reduce_cfl = cfl_skew_deg_cutoff

    cfl = ramp_cfl(fl)
    cflv_min = cfl
    if ( cfl_viscous_terms ) cflv_min = huge(1.0_dp)

! First loop over cells and zero out cdt

    local_dt: if ( use_local_dt ) then

      do i = 1, ncell0
        cdt(i) = 0._dp
      end do

! Start by getting interior contributions

      face_loop: do n = 1, nface

        cell1 = fptr(1,n)
        cell2 = fptr(2,n)

!       get normal to face

        xnorm = xn_face(n)
        ynorm = yn_face(n)
        znorm = zn_face(n)
        area  = area_face(n)

        xnorm = xnorm*area
        ynorm = ynorm*area
        znorm = znorm*area

        u1   = qcell(2,cell1)
        v1   = qcell(3,cell1)
        w1   = qcell(4,cell1)

        u2   = qcell(2,cell2)
        v2   = qcell(3,cell2)
        w2   = qcell(4,cell2)

        face_speed = 0._dp

        fspd_half = 0.5_dp*face_speed

!       get average values on face

        u    = 0.5_dp*(u1 + u2)
        v    = 0.5_dp*(v1 + v2)
        w    = 0.5_dp*(w1 + w2)
        ubar = xn_face(n)*u + yn_face(n)*v + zn_face(n)*w
        c    = sqrt((ubar-fspd_half)*(ubar-fspd_half) + beta)

        term_inv = abs(u*xnorm + v*ynorm + w*znorm - fspd_half*area) + c*area

        term_vis = 0._dp
        if ( cfl_viscous_terms ) then
          !...from CFL3D User's Manual (B-28)
          vol_avgi = 2.0_dp/( cell_vol(cell1) + cell_vol(cell2) )
          mut_avg  = 1.0_dp + 0.5_dp*( amut(cell1) + amut(cell2) )
          term_vis = 2._dp*area*area*vol_avgi*mut_avg*xmr
          iskew = max( cell_skewness(cell1) , cell_skewness(cell2) )
          cflv  = cfl_visc( iskew , cfl )
          !...scale viscous contribution by ratio of inviscid/viscous
          term_vis = term_vis*cfl/cflv
          cflv_min = min( cflv, cflv_min )
        endif

        term = term_inv + term_vis

        if (cell1 <= ncell0) cdt(cell1) = cdt(cell1) + term
        if (cell2 <= ncell0) cdt(cell2) = cdt(cell2) + term

      end do face_loop

! Finish the boundary closure

      faces : do n = 1, bcc%n_faces0

        if ( bcc%ibc(n) == bc_null ) cycle faces

        cell = bcc%cell(n)

        term_inv = bcc%cdt_bc_inv(n)
        term_vis = bcc%cdt_bc_vis(n)
        if ( cfl_viscous_terms ) then
          cflv = cfl_visc( cell_skewness(cell) , cfl )
          !...scale viscous contribution by ratio of inviscid/viscous
          term_vis = term_vis*cfl/cflv
          cflv_min = min( cflv, cflv_min )
        endif

        term = term_inv + term_vis

        cdt( cell ) = cdt( cell ) + term

      enddo faces

! Now cdt has sum of inviscid and (possibly) viscous terms

      do i = 1, ncell0
        cdt(i) = cell_vol(i)/cdt(i)
      end do

    else

! If not doing local time stepping just set cdt=1

      do i = 1, ncell0
        cdt(i) = 1._dp
      end do

    end if local_dt

    if ( first_time_through .and. use_local_dt ) then
      cdt_max = -1._dp
      cdt_min = huge(1._dp)
      do i = 1, ncell0
        cdt_max = max( cdt_max, cdt(i) )
        cdt_min = min( cdt_min, cdt(i) )
      end do
      term = cdt_max
      call lmpi_max(term,cdt_max)
      term = -cdt_min
      call lmpi_max(term,cdt_min) ; cdt_min = -cdt_min
      term = cflv_min
      call lmpi_max(term,cflv_min) ; cflv_min = -cflv_min
      if ( lmpi_master ) then
        write(*,"(1x,a,2e12.5)") ' Time step               CFL=',cfl
        write(*,"(1x,a,L12)")    ' Time step cfl_viscous_terms=',&
                                             cfl_viscous_terms
        write(*,"(1x,a,2e12.5)") ' Time step  CFL(viscous)_min=',cflv_min
        write(*,"(1x,a,2e12.5)") ' Time step             dt_min=',cdt_min*cfl
        write(*,"(1x,a,2e12.5)") ' Time step             dt_max=',cdt_max*cfl
        write(*,"(1x,a,2e12.5)") ' Time step           max/min=',cdt_max/cdt_min
      endif
    elseif ( first_time_through ) then
      if ( lmpi_master ) then
        write(*,"(1x,a,2e12.5)") ' Time step...global constant time step'
      endif
    endif
    first_time_through = .false.

  end subroutine cdt_cc_i

!================================ CDT_CC_CONVECTION ==========================80
!
! Calculate a time step for CFL=1 at each cell
! Note that this routine assumes conservative variables
!
! Cell-centered version
! Set of scalar convection equations version
!
!=============================================================================80

  subroutine cdt_cc_convection( nface, ncell0, ncell01, cdt, cell_vol, fptr,   &
                                area_face, xn_face, yn_face, zn_face, bcc )

    use nml_nonlinear_solves, only : use_local_dt
    use bc_types,             only : bcc_type
    use bc_names,             only : bc_null
    use convection_defs,      only : cu, cv, cw

    integer,                      intent(in)  :: nface, ncell0, ncell01
    integer,  dimension(6,nface), intent(in)  :: fptr
    real(dp), dimension(ncell01), intent(in)  :: cell_vol
    real(dp), dimension(nface),   intent(in)  :: area_face
    real(dp), dimension(nface),   intent(in)  :: xn_face
    real(dp), dimension(nface),   intent(in)  :: yn_face
    real(dp), dimension(nface),   intent(in)  :: zn_face
    real(dp), dimension(ncell0),  intent(out) :: cdt
    type(bcc_type),               intent(in)  :: bcc

    integer :: i, n, cell, cell1, cell2

    real(dp) :: area, term, xnorm, ynorm, znorm

  continue

! First loop over cells and zero out cdt

    local_dt: if ( use_local_dt ) then

      do i = 1, ncell0
        cdt(i) = 0._dp
      end do

! Start by getting interior contributions

      face_loop: do n = 1, nface

        cell1 = fptr(1,n)
        cell2 = fptr(2,n)

!       get normal to face and area

        xnorm = xn_face(n)
        ynorm = yn_face(n)
        znorm = zn_face(n)
        area  = area_face(n)

        xnorm = xnorm*area
        ynorm = ynorm*area
        znorm = znorm*area

        term = abs( cu*xnorm + cv*ynorm + cw*znorm )

        if (cell1 <= ncell0) cdt(cell1) = cdt(cell1) + term
        if (cell2 <= ncell0) cdt(cell2) = cdt(cell2) + term

      end do face_loop

! Finish the boundary closure

      faces : do n = 1, bcc%n_faces0

        if ( bcc%ibc(n) == bc_null ) cycle faces

        cell        = bcc%cell(n)
        cdt( cell ) = cdt( cell ) + bcc%cdt_bc_inv(n)

      enddo faces

      do i = 1, ncell0
        cdt(i) = cell_vol(i)/cdt(i)
      end do

    else

! If not doing local time stepping just set cdt=1

      do i = 1, ncell0
        cdt(i) = 1._dp
      end do

    end if local_dt

  end subroutine cdt_cc_convection

!=============================== CFL_VISC ====================================80
!
! Determine a cfl for the viscous terms.
!
!=============================================================================80

  pure function cfl_visc( iskew, cfl )

    use cc_defs, only : cfl_reduced_value_high_skew, cc_full_visc_jac

    integer(i2), intent(in) :: iskew
    real(dp),    intent(in) :: cfl

    real(dp) :: cfl_visc

  continue

    !...default the viscous cfl to a large value.
    cfl_visc = max( 1.0e+08_dp , cfl )
    if ( iskew > skew_angle_to_reduce_cfl .and. &
         .not.cc_full_visc_jac) then
      cfl_visc = min( cfl_visc , cfl_reduced_value_high_skew )
    endif

  end function cfl_visc

  include 'viscosity_law.f90'

end module timestep_cc

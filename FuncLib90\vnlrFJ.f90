!=================================== vnlrFJ ==================================80
!
! van Leer flux jacobians
!
! Note: this function uses conservative variables
!
! The left  jacobian is returned in dflr(:,:,1)
! The right jacobian is returned in dflr(:,:,2)
!
!=============================================================================80

  subroutine vnlrFJ(xnorm, ynorm, znorm, area, face_speed, ql, qr, dflr)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_4th, my_half, my_1, my_2, my_4

    use fluid,           only : gamma, gm1, ggm1

    real(dp),               intent(in) :: xnorm, ynorm, znorm, area
    real(dp),               intent(in) :: face_speed
    real(dp), dimension(5), intent(in) :: ql, qr

    real(dp), dimension(5,5,2), intent(out) :: dflr

    real(dp) :: rho, u, v, w
    real(dp) :: q2, enrgy, c, c1, c2
    real(dp) :: ubar, fmach, ubp2a, ubm2a
    real(dp) :: dcdr, dcdru, dcdrv, dcdrw, dcde
    real(dp) :: dfdq1, dfdq2, dfdq3, dfdq4, dfdq5
    real(dp) :: fluxm1, fluxm2, fluxm3, fluxm4, fluxm5
    real(dp) :: fluxp1, fluxp2, fluxp3, fluxp4, fluxp5
    real(dp) :: fof, phi, rho_inv, rhoc_inv, gamma_inv

  continue

    gamma_inv = my_1/gamma

!   take care of the jacobian due to the left state (dfl)

    rho   = ql(1)
    rho_inv = my_1/rho
    u     = ql(2)*rho_inv
    v     = ql(3)*rho_inv
    w     = ql(4)*rho_inv
    q2    = u*u + v*v + w*w
    enrgy = ql(5)
    c     = sqrt(gamma*gm1*(enrgy-my_half*rho*q2)*rho_inv)
    ubar  = xnorm*u + ynorm*v + znorm*w  - face_speed
    fmach = ubar / c
    ubp2a = -ubar + my_2*c

!   if subsonic calculate dfl and add contribution to A

    first_machvalue_check: if (abs(fmach) < my_1) then

!     get first order fluxes

      fluxp1 = area * my_4th * rho * c * (fmach+1)**2
      fluxp2 = fluxp1 * (xnorm*ubp2a*gamma_inv+u)
      fluxp3 = fluxp1 * (ynorm*ubp2a*gamma_inv+v)
      fluxp4 = fluxp1 * (znorm*ubp2a*gamma_inv+w)
      fluxp5 = fluxp1*((-gm1*ubar*ubar                                         &
                   + my_2*gm1*ubar*c + my_2*c*c)/(gamma*gamma - my_1)          &
                   + my_half*q2 + face_speed*(-ubar + my_2*c)*gamma_inv)

!     derivatives of speed of sound

      rhoc_inv = my_1/(rho*c)
      dcdr  =  my_half*ggm1 * (q2-enrgy*rho_inv) * rhoc_inv
      dcdru = -my_half*ggm1*u*rhoc_inv
      dcdrv = -my_half*ggm1*v*rhoc_inv
      dcdrw = -my_half*ggm1*w*rhoc_inv
      dcde  =  my_half*ggm1  *rhoc_inv

!     if subsonic calculate dfl and add contribution to Jacobian

      dfdq1 = my_4th*area*(my_1 - fmach**2)*(c + rho*dcdr)                     &
            - my_half*area*face_speed*(fmach + my_1)
      dfdq2 = my_4th*area*(my_2*xnorm*(fmach + my_1)                           &
            + rho*(my_1 - fmach**2)*dcdru)
      dfdq3 = my_4th*area*(my_2*ynorm*(fmach + my_1)                           &
            + rho*(my_1 - fmach**2)*dcdrv)
      dfdq4 = my_4th*area*(my_2*znorm*(fmach + my_1)                           &
            + rho*(my_1 - fmach**2)*dcdrw)
      dfdq5 = my_4th*area*rho*dcde*(my_1 - fmach**2)

      dflr(1,1,1) = dfdq1
      dflr(1,2,1) = dfdq2
      dflr(1,3,1) = dfdq3
      dflr(1,4,1) = dfdq4
      dflr(1,5,1) = dfdq5

      fof = fluxp2 / fluxp1
      dflr(2,1,1) = fluxp1*(my_2*xnorm*gamma_inv*dcdr                          &
                    + xnorm*(ubar+face_speed)*gamma_inv*rho_inv - u*rho_inv)   &
                    + fof*dfdq1
      dflr(2,2,1) = fluxp1*(-xnorm*xnorm*gamma_inv*rho_inv                     &
                    + my_2*xnorm*dcdru*gamma_inv + rho_inv) + fof*dfdq2
      dflr(2,3,1) = fluxp1*(-xnorm*ynorm*gamma_inv*rho_inv                     &
                    + my_2*xnorm*dcdrv*gamma_inv) + fof*dfdq3
      dflr(2,4,1) = fluxp1*(-xnorm*znorm*gamma_inv*rho_inv                     &
                    + my_2*xnorm*dcdrw*gamma_inv) + fof*dfdq4
      dflr(2,5,1) = fluxp1*(2*xnorm*dcde*gamma_inv)                            &
                    + fof*dfdq5

      fof = fluxp3/fluxp1
      dflr(3,1,1) = fluxp1*(my_2*ynorm*gamma_inv*dcdr                          &
                    + ynorm*(ubar+face_speed)*gamma_inv*rho_inv - v*rho_inv)   &
                    + fof*dfdq1
      dflr(3,2,1) = fluxp1*(-ynorm*xnorm*gamma_inv*rho_inv                     &
                    + my_2*ynorm*dcdru*gamma_inv) + fof*dfdq2
      dflr(3,3,1) = fluxp1*(-ynorm*ynorm*gamma_inv*rho_inv                     &
                    + my_2*ynorm*dcdrv*gamma_inv + rho_inv) + fof*dfdq3
      dflr(3,4,1) = fluxp1*(-ynorm*znorm*gamma_inv*rho_inv                     &
                    + my_2*ynorm*dcdrw*gamma_inv) + fof*dfdq4
      dflr(3,5,1) = fluxp1*(my_2*ynorm*dcde*gamma_inv)                         &
                    + fof*dfdq5

      fof = fluxp4/fluxp1
      dflr(4,1,1) = fluxp1*(my_2*znorm*gamma_inv*dcdr                          &
                    + znorm*(ubar+face_speed)*gamma_inv*rho_inv - w*rho_inv)   &
                    + fof*dfdq1
      dflr(4,2,1) = fluxp1*(-znorm*xnorm*gamma_inv*rho_inv                     &
                    + my_2*znorm*dcdru*gamma_inv) + fof*dfdq2
      dflr(4,3,1) = fluxp1*(-znorm*ynorm*gamma_inv*rho_inv                     &
                    + my_2*znorm*dcdrv*gamma_inv) + fof*dfdq3
      dflr(4,4,1) = fluxp1*(-znorm*znorm*gamma_inv*rho_inv                     &
                    + my_2*znorm*dcdrw*gamma_inv + rho_inv) + fof*dfdq4
      dflr(4,5,1) = fluxp1*(my_2*znorm*dcde*gamma_inv)                         &
                    + fof*dfdq5

      c1  = (my_2*gm1*ubar+my_4*c) / (gamma*gamma-my_1)                        &
          + my_2*gamma_inv*face_speed
      c2  = my_2 * gm1 / (gamma*gamma-my_1) * (-ubar+c) * rho_inv
      fof = fluxp5 / fluxp1

      dflr(5,1,1) = fluxp1*(dcdr*c1 - c2*(ubar+face_speed) - q2*rho_inv        &
                    + face_speed*(ubar+face_speed)*gamma_inv*rho_inv)          &
                    + fof*dfdq1
      dflr(5,2,1) = fluxp1*(dcdru*c1 + c2*xnorm + u*rho_inv                    &
                    - face_speed*xnorm*gamma_inv*rho_inv)                      &
                    + fof*dfdq2
      dflr(5,3,1) = fluxp1*(dcdrv*c1 + c2*ynorm + v*rho_inv                    &
                    - face_speed*ynorm*gamma_inv*rho_inv)                      &
                    + fof*dfdq3
      dflr(5,4,1) = fluxp1*(dcdrw*c1 + c2*znorm + w*rho_inv                    &
                    - face_speed*znorm*gamma_inv*rho_inv)                      &
                    + fof*dfdq4
      dflr(5,5,1) = fluxp1*dcde*c1 + fof*dfdq5

    else if (fmach >= my_1) then

      phi = my_half * gm1 * q2
      dflr(1,1,1) = -area*face_speed
      dflr(1,2,1) = area*xnorm
      dflr(1,3,1) = area*ynorm
      dflr(1,4,1) = area*znorm
      dflr(1,5,1) = my_0

      dflr(2,1,1) = area*(xnorm*phi-u*(ubar+face_speed))
      dflr(2,2,1) = area*(xnorm*(my_2-gamma)*u+ubar)
      dflr(2,3,1) = area*(ynorm*u-xnorm*gm1*v)
      dflr(2,4,1) = area*(znorm*u-xnorm*gm1*w)
      dflr(2,5,1) = area*xnorm*gm1

      dflr(3,1,1) = area*(ynorm*phi-v*(ubar+face_speed))
      dflr(3,2,1) = area*(xnorm*v-ynorm*gm1*u)
      dflr(3,3,1) = area*(ynorm*(my_2-gamma)*v+ubar)
      dflr(3,4,1) = area*(znorm*v-ynorm*gm1*w)
      dflr(3,5,1) = area*ynorm*gm1

      dflr(4,1,1) = area*(znorm*phi-w*(ubar+face_speed))
      dflr(4,2,1) = area*(xnorm*w-znorm*gm1*u)
      dflr(4,3,1) = area*(ynorm*w-znorm*gm1*v)
      dflr(4,4,1) = area*(znorm*(my_2-gamma)*w+ubar)
      dflr(4,5,1) = area*znorm*gm1

      dflr(5,1,1) = area*(my_2*phi-gamma*enrgy*rho_inv)*(ubar+face_speed)
      dflr(5,2,1) = area*(xnorm*(gamma*enrgy*rho_inv-phi)-                     &
                            gm1*u*(ubar+face_speed))
      dflr(5,3,1) = area*(ynorm*(gamma*enrgy*rho_inv-phi)-                     &
                            gm1*v*(ubar+face_speed))
      dflr(5,4,1) = area*(znorm*(gamma*enrgy*rho_inv-phi)-                     &
                            gm1*w*(ubar+face_speed))
      dflr(5,5,1) = area*(gamma*ubar + gm1*face_speed)

    else

      dflr(1,1,1) = my_0
      dflr(1,2,1) = my_0
      dflr(1,3,1) = my_0
      dflr(1,4,1) = my_0
      dflr(1,5,1) = my_0

      dflr(2,1,1) = my_0
      dflr(2,2,1) = my_0
      dflr(2,3,1) = my_0
      dflr(2,4,1) = my_0
      dflr(2,5,1) = my_0

      dflr(3,1,1) = my_0
      dflr(3,2,1) = my_0
      dflr(3,3,1) = my_0
      dflr(3,4,1) = my_0
      dflr(3,5,1) = my_0

      dflr(4,1,1) = my_0
      dflr(4,2,1) = my_0
      dflr(4,3,1) = my_0
      dflr(4,4,1) = my_0
      dflr(4,5,1) = my_0

      dflr(5,1,1) = my_0
      dflr(5,2,1) = my_0
      dflr(5,3,1) = my_0
      dflr(5,4,1) = my_0
      dflr(5,5,1) = my_0

    end if first_machvalue_check

!   take care of the jacobian due to the right state (dfr)

    rho   = qr(1)
    rho_inv = my_1/rho
    u     = qr(2)*rho_inv
    v     = qr(3)*rho_inv
    w     = qr(4)*rho_inv
    enrgy = qr(5)
    q2    = u*u + v*v + w*w
    c     = sqrt(gamma*gm1*(enrgy - my_half*rho*q2)*rho_inv)
    ubar  = xnorm*u + ynorm*v + znorm*w - face_speed
    fmach = ubar/c
    ubm2a = -ubar - my_2*c

!   if subsonic calculate dfr and add contribution to A

    second_machvalue_check: if (abs(fmach) < my_1) then

!     get first order fluxes

      fluxm1 = -area*my_4th*rho*c*(fmach-1)**2
      fluxm2 = fluxm1 * (xnorm*ubm2a*gamma_inv+u)
      fluxm3 = fluxm1 * (ynorm*ubm2a*gamma_inv+v)
      fluxm4 = fluxm1 * (znorm*ubm2a*gamma_inv+w)
      fluxm5 = fluxm1*((-gm1*ubar*ubar                                         &
                   - my_2*gm1*ubar*c + my_2*c*c)/(gamma*gamma - my_1)          &
                   + my_half*q2 + face_speed*(-ubar - my_2*c)*gamma_inv)

!     derivatives of speed of sound

      rhoc_inv = my_1/(rho*c)
      dcdr  =  my_half*ggm1*(q2 - enrgy*rho_inv)*rhoc_inv
      dcdru = -my_half*ggm1*u*rhoc_inv
      dcdrv = -my_half*ggm1*v*rhoc_inv
      dcdrw = -my_half*ggm1*w*rhoc_inv
      dcde  =  my_half*ggm1  *rhoc_inv

!     if subsonic calculate dfr and subtract contribution to A

      dfdq1 = -my_4th*area*(my_1 - fmach**2)*(c + rho*dcdr)                    &
            +  my_half*area*face_speed*(fmach - my_1)
      dfdq2 = -my_4th*area*(my_2*xnorm*(fmach - my_1)                          &
            + rho*(my_1 - fmach**2)*dcdru)
      dfdq3 = -my_4th*area*(my_2*ynorm*(fmach - my_1)                          &
            + rho*(my_1 - fmach**2)*dcdrv)
      dfdq4 = -my_4th*area*(my_2*znorm*(fmach - my_1)                          &
            + rho*(my_1 - fmach**2)*dcdrw)
      dfdq5 = -my_4th*area*rho*dcde*(my_1 - fmach**2)

      dflr(1,1,2) = dfdq1
      dflr(1,2,2) = dfdq2
      dflr(1,3,2) = dfdq3
      dflr(1,4,2) = dfdq4
      dflr(1,5,2) = dfdq5

      fof = fluxm2/fluxm1
      dflr(2,1,2) = fluxm1*(-my_2*xnorm*gamma_inv*dcdr                         &
                    + xnorm*(ubar+face_speed)*gamma_inv*rho_inv - u*rho_inv)   &
                    + fof*dfdq1
      dflr(2,2,2) = fluxm1*(-xnorm*xnorm*gamma_inv*rho_inv                     &
                    - my_2*xnorm*dcdru*gamma_inv + rho_inv) + fof*dfdq2
      dflr(2,3,2) = fluxm1*(-xnorm*ynorm*gamma_inv*rho_inv                     &
                    - my_2*xnorm*dcdrv*gamma_inv) + fof*dfdq3
      dflr(2,4,2) = fluxm1*(-xnorm*znorm*gamma_inv*rho_inv                     &
                    - my_2*xnorm*dcdrw*gamma_inv) + fof*dfdq4
      dflr(2,5,2) = fluxm1*(-2*xnorm*dcde*gamma_inv)                           &
                    + fof*dfdq5

      fof = fluxm3/fluxm1
      dflr(3,1,2) = fluxm1*(-my_2*ynorm*gamma_inv*dcdr                         &
                    + ynorm*(ubar+face_speed)*gamma_inv*rho_inv - v*rho_inv)   &
                    + fof*dfdq1
      dflr(3,2,2) = fluxm1*(-ynorm*xnorm*gamma_inv*rho_inv                     &
                    - my_2*ynorm*dcdru*gamma_inv) + fof*dfdq2
      dflr(3,3,2) = fluxm1*(-ynorm*ynorm*gamma_inv*rho_inv                     &
                    - my_2*ynorm*dcdrv*gamma_inv + rho_inv) + fof*dfdq3
      dflr(3,4,2) = fluxm1*(-ynorm*znorm*gamma_inv*rho_inv                     &
                    - my_2*ynorm*dcdrw*gamma_inv) + fof*dfdq4
      dflr(3,5,2) = fluxm1*(-my_2*ynorm*dcde*gamma_inv)                        &
                    + fof*dfdq5

      fof = fluxm4/fluxm1
      dflr(4,1,2) = fluxm1*(-my_2*znorm*gamma_inv*dcdr                         &
                    + znorm*(ubar+face_speed)*gamma_inv*rho_inv - w*rho_inv)   &
                    + fof*dfdq1
      dflr(4,2,2) = fluxm1*(-znorm*xnorm*gamma_inv*rho_inv                     &
                    - my_2*znorm*dcdru*gamma_inv) + fof*dfdq2
      dflr(4,3,2) = fluxm1*(-znorm*ynorm*gamma_inv*rho_inv                     &
                    - my_2*znorm*dcdrv*gamma_inv) + fof*dfdq3
      dflr(4,4,2) = fluxm1*(-znorm*znorm*gamma_inv*rho_inv                     &
                    - my_2*znorm*dcdrw*gamma_inv + rho_inv) + fof*dfdq4
      dflr(4,5,2) = fluxm1*(-my_2*znorm*dcde*gamma_inv)                        &
                    + fof*dfdq5

      c1  = (-my_2*gm1*ubar + my_4*c)/(gamma*gamma - my_1)                     &
          - my_2*gamma_inv*face_speed
      c2  = my_2*gm1/(gamma*gamma - my_1)*(-ubar - c)*rho_inv
      fof = fluxm5/fluxm1

      dflr(5,1,2) = fluxm1*(dcdr*c1 - c2*(ubar+face_speed) - q2*rho_inv        &
                    + face_speed*(ubar+face_speed)*gamma_inv*rho_inv)          &
                    + fof*dfdq1
      dflr(5,2,2) = fluxm1*(dcdru*c1 + c2*xnorm + u*rho_inv                    &
                    - face_speed*xnorm*gamma_inv*rho_inv)                      &
                    + fof*dfdq2
      dflr(5,3,2) = fluxm1*(dcdrv*c1 + c2*ynorm + v*rho_inv                    &
                    - face_speed*ynorm*gamma_inv*rho_inv)                      &
                    + fof*dfdq3
      dflr(5,4,2) = fluxm1*(dcdrw*c1 + c2*znorm + w*rho_inv                    &
                    - face_speed*znorm*gamma_inv*rho_inv)                      &
                    + fof*dfdq4
      dflr(5,5,2) = fluxm1*dcde*c1 + fof*dfdq5

    else if (fmach <= -my_1) then

      phi = my_half * gm1 * q2
      dflr(1,1,2) = -area*face_speed
      dflr(1,2,2) = area*xnorm
      dflr(1,3,2) = area*ynorm
      dflr(1,4,2) = area*znorm
      dflr(1,5,2) = my_0

      dflr(2,1,2) = area*(xnorm*phi-u*(ubar+face_speed))
      dflr(2,2,2) = area*(xnorm*(my_2-gamma)*u+ubar)
      dflr(2,3,2) = area*(ynorm*u-xnorm*gm1*v)
      dflr(2,4,2) = area*(znorm*u-xnorm*gm1*w)
      dflr(2,5,2) = area*xnorm*gm1

      dflr(3,1,2) = area*(ynorm*phi-v*(ubar+face_speed))
      dflr(3,2,2) = area*(xnorm*v-ynorm*gm1*u)
      dflr(3,3,2) = area*(ynorm*(my_2-gamma)*v+ubar)
      dflr(3,4,2) = area*(znorm*v-ynorm*gm1*w)
      dflr(3,5,2) = area*ynorm*gm1

      dflr(4,1,2) = area*(znorm*phi-w*(ubar+face_speed))
      dflr(4,2,2) = area*(xnorm*w-znorm*gm1*u)
      dflr(4,3,2) = area*(ynorm*w-znorm*gm1*v)
      dflr(4,4,2) = area*(znorm*(my_2-gamma)*w+ubar)
      dflr(4,5,2) = area*znorm*gm1

      dflr(5,1,2) = area*(my_2*phi-gamma*enrgy*rho_inv)*(ubar+face_speed)
      dflr(5,2,2) = area*(xnorm*(gamma*enrgy*rho_inv-phi)-                     &
                            gm1*u*(ubar+face_speed))
      dflr(5,3,2) = area*(ynorm*(gamma*enrgy*rho_inv-phi)-                     &
                            gm1*v*(ubar+face_speed))
      dflr(5,4,2) = area*(znorm*(gamma*enrgy*rho_inv-phi)-                     &
                            gm1*w*(ubar+face_speed))
      dflr(5,5,2) = area*(gamma*ubar + gm1*face_speed)

    else

      dflr(1,1,2) = my_0
      dflr(1,2,2) = my_0
      dflr(1,3,2) = my_0
      dflr(1,4,2) = my_0
      dflr(1,5,2) = my_0

      dflr(2,1,2) = my_0
      dflr(2,2,2) = my_0
      dflr(2,3,2) = my_0
      dflr(2,4,2) = my_0
      dflr(2,5,2) = my_0

      dflr(3,1,2) = my_0
      dflr(3,2,2) = my_0
      dflr(3,3,2) = my_0
      dflr(3,4,2) = my_0
      dflr(3,5,2) = my_0

      dflr(4,1,2) = my_0
      dflr(4,2,2) = my_0
      dflr(4,3,2) = my_0
      dflr(4,4,2) = my_0
      dflr(4,5,2) = my_0

      dflr(5,1,2) = my_0
      dflr(5,2,2) = my_0
      dflr(5,3,2) = my_0
      dflr(5,4,2) = my_0
      dflr(5,5,2) = my_0

    end if second_machvalue_check

  end subroutine vnlrFJ

AC_DEFUN([AX_FORTRAN_ETIME],
[AC_CACHE_CHECK([fortran etime available],
 ax_cv_fortran_etime,
 [AC_LANG_PUSH(Fortran)
  AC_LINK_IFELSE(
  [
       program main
       real :: result, tarray(2)
       real :: etime
       external etime
       result = etime(tarray)
       print *, result
       end
  ],
  [ax_cv_fortran_etime=yes],
  [ax_cv_fortran_etime=no]
   )
  AC_LANG_POP(Fortran)
 ])
if test "$ax_cv_fortran_etime" != 'no'
then
 AC_DEFINE([HAVE_FORTRAN_ETIME],[1],[fortran provides etime])
fi
])


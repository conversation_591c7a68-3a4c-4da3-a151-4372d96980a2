!================================= FLUX_LDFS_DDT =============================80
!
! Overloaded version of the  routine that
! calculates the fluxes on the face and compute the contribution to the
! flux balance using Edwards low dissipation flux splitting scheme
!
! Blending of the Low Dissipation Flux Split Schemes (LDFSS) no. 0 and 2 of
! to remove the carbuncle that LDFSS(2) can develop for stong shock aligned with
! the grid by switching it with the LDFSS(0) scheme while controlling the higher
! dissipation of LDFSS(0) away from the shocks by causing the scheme to revert
! to the LDFSS(2) scheme. LDFSS(0) and LDFSS(2) schemes  were developed by
<PERSON> <PERSON>,J.R., "A Low-Diffusion Flux-Splitting Scheme for Navier Stokes
! Calculations," AIAA-96-1704-CP.
!
! Note that this function uses primitive variables
!
!=============================================================================80

  pure function flux_ldfss_ddt(rx1, ry1, rz1, rx2, ry2, rz2,                   &
                               xnorm, ynorm, znorm, area, vol1, vol2,          &
                               gradx1, grady1, gradz1, phi1,                   &
                               gradx2, grady2, gradz2, phi2,                   &
                               face_speed, mu, ql, qr, second,                 &
                               utilize_switch )

    use kinddefs,        only : dp
    use ddt,             only : ddt5, assignment(=), operator(+), operator(-), &
                                operator(*), operator(/), operator(**),        &
                                operator(>), ddt_min, ddt_max, ddt_abs,        &
                                ddt_sqrt, ddt_tanh, ddt_sign, ddt_sin
    use fun3d_constants, only : my_0, my_4th, my_half, my_1, my_2, pi

    use info_depr,       only : adptv_entropy_fix, ivisc
    use fluid,           only : gm1
    use inviscid_flux,   only : flux_construction

    implicit none

    real(dp),                 intent(in) :: rx1, ry1, rz1, rx2, ry2, rz2
    real(dp),                 intent(in) :: xnorm, ynorm, znorm, area
    real(dp),                 intent(in) :: vol1, vol2
    type(ddt5),               intent(in) :: gradx1, grady1, gradz1,            &
                                            gradx2, grady2, gradz2
    real(dp),                 intent(in) :: phi1, phi2
    real(dp),                 intent(in) :: face_speed, mu
    type(ddt5), dimension(5), intent(in) :: ql, qr
    type(ddt5), dimension(6)             :: flux_ldfss_ddt

    logical,                  intent(in) :: second, utilize_switch

    type(ddt5) :: rhol, ul, vl, wl, q2l, pressl, energyl, Hl, ubarl, cl
    type(ddt5) :: rhor, ur, vr, wr, q2r, pressr, energyr, Hr, ubarr, cr
    type(ddt5) :: rho, wat, u, v, w, q2, ubar, h, c,c2
    type(ddt5) :: chalf, rmbl, rmbr, btl, btr
    type(ddt5) :: alfl, alfr, xmml, xmmr, xmhalf, xmc
    type(ddt5) :: delp, psum, xmcl, xmcr
    type(ddt5) :: mfil, mfir, ppl, ppr, pnet
    type(ddt5) :: switch, switchv, diss_path
    type(ddt5) :: xmfunct, btfunct, fact
    type(ddt5) :: gradpl, gradpr, pgrad, dpx2, dpy2, dpz2, plapc, pcoef
    type(ddt5) :: utngl, utngr, utot, dutng, unorm

    integer,  parameter :: behavior = 01
    integer,  parameter :: powerv = 4
    real(dp), parameter :: poweri = my_2
    real(dp), parameter :: laplcc = my_2
    real(dp), parameter :: plim_cf2 = 1.0_dp

    real(dp), parameter :: one_e_negative_6 = 0.000001_dp
    real(dp), parameter :: point_005 = 0.005_dp

  continue

!   Get left and right state primitive variables

    rhol   = ql(1)
    ul     = ql(2)
    vl     = ql(3)
    wl     = ql(4)
    pressl = ql(5)

    rhor   = qr(1)
    ur     = qr(2)
    vr     = qr(3)
    wr     = qr(4)
    pressr = qr(5)

!   Compute the remaining needed left and right state variables:

    q2l     = ul*ul + vl*vl + wl*wl
    energyl = pressl/gm1 + my_half*rhol*q2l
    Hl      = (energyl + pressl)/rhol
    ubarl   = xnorm*ul + ynorm*vl + znorm*wl
    cl      = ddt_sqrt(gm1*(Hl-my_half*q2l))

    q2r     = ur*ur + vr*vr + wr*wr
    energyr = pressr/gm1 + my_half*rhor*q2r
    Hr      = (energyr + pressr)/rhor
    ubarr   = xnorm*ur + ynorm*vr + znorm*wr
    cr      = ddt_sqrt(gm1*(Hr-my_half*q2r))

!   Interface contravariant Mach numbers including face_speed

    chalf = my_half * (cl+cr) ! eq. 48

    rmbl  = (ubarl - face_speed) / chalf ! eq. 49
    rmbr  = (ubarr - face_speed) / chalf ! eq. 49

!   Split contravariant Mach number

    alfl = my_half * (my_1 + ddt_sign(my_1, rmbl)) ! eq. 15
    alfr = my_half * (my_1 - ddt_sign(my_1, rmbr)) ! eq. 15
!
    btl = -max(my_0, my_1-int(abs(rmbl%f))) ! eq. 16
    btr = -max(my_0, my_1-int(abs(rmbr%f))) ! eq. 16
!
    xmml =  my_4th * (rmbl+my_1)**2 ! eq. 14
    xmmr = -my_4th * (rmbr-my_1)**2 ! eq. 14

    xmhalf = ddt_sqrt(my_half*(rmbl**2+rmbr**2))

    xmc  = my_4th * btl * btr * (xmhalf-my_1)**2

    delp = pressl - pressr
    psum = pressl + pressr

!   Modified form of eq. 26

    xmcl = btl*btr*xmc * (my_1 - (delp/psum + my_2*ddt_abs(delp)/pressl))
    xmcr = btl*btr*xmc * (my_1 + (delp/psum - my_2*ddt_abs(delp)/pressr))

    diss_path = my_1

    if ( .not. second ) then

      switch  = my_0
      switchv = my_0
      if ( flux_construction == 'ldfss' ) then
        switch = my_1
      endif

    else if ( flux_construction == 'dldfss' ) then

      diss_path = my_0

!     Blend LDFSS(0) with LDFSS(2) using the local pressure gradient and
!     the ratio deltaU(tangent)/U(normal) such that the flux normal to
!     a stagnation streamline line gets forced to be LDFSS(0) and the flux
!     normal to a slip line gets forced to be LDFSS(2) due to the behavior:
!     parameter       [stag. streamline] [slip line]
!     ----------------------------------------------
!     deltaU(tangent)        = 0            /= 0
!     U(normal)              = 0             = 0
!     gradp                  = 0             = 0

!     Construct the normalized magnitude of the undivided pressure gradient

      gradpl = ddt_sqrt( gradx1*gradx1 + grady1*grady1 + gradz1*gradz1 )       &
             * sqrt( rx1*rx1 + ry1*ry1 + rz1*rz1 )
      gradpr = ddt_sqrt( gradx2*gradx2 + grady2*grady2 + gradz2*gradz2 )       &
             * sqrt( rx2*rx2 + ry2*ry2 + rz2*rz2 )

      pgrad  = ddt_max(gradpl, gradpr) / ddt_min(pressl, pressr)

!     Construct a crude estimate of the magnitude of the
!     normalized undivided pressure Laplacian

      dpx2 = gradx2*rx2 - gradx1*rx1
      dpy2 = grady2*ry2 - grady1*ry1
      dpz2 = gradz2*rz2 - gradz1*rz1

      plapc = ddt_sqrt(dpx2*dpx2+dpy2*dpy2+dpz2*dpz2) / ddt_min(pressl, pressr)

      pcoef = ddt_max(pgrad, plim_cf2*plapc)

!     Compute deltaU(tangent) and U(normal) to compute the blend coeff.

      utngl  = ddt_sqrt(ddt_max(my_0, q2l-ubarl*ubarl))
      utngr  = ddt_sqrt(ddt_max(my_0, q2r-ubarr*ubarr))
      utot   = my_half*(ddt_sqrt(q2l)+ddt_sqrt(q2r))
      dutng  = ddt_abs(utngr-utngl)/(utot+one_e_negative_6)
      unorm  = xmhalf*chalf ! This is more robust then myhalf*(ubarl+ubar) !
      unorm  = ddt_max(unorm/(utot+one_e_negative_6), point_005 )

!     Compute the coeff. used to blend LDFSS(0) and LDFSS(2)

      switch = ddt_min(my_1-ddt_tanh(pcoef),                                   &
                       ddt_max(my_0, ddt_min(my_1, dutng**2/unorm)))
      switch = switch*min(phi1, phi2)

      zero_switch : if ( .not. utilize_switch ) then

!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
!     Set the switch to zero since this seems
!     to be destabilizing until we figure out why
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

        switch  = my_0

      end if zero_switch

!     Compute the cell face reynolds number to make the switch coeff. one
!     on low Reynolds number cells faces

      if (ivisc >= 2) then
        switchv = vswch_coef_orig_ddt(rhol, rhor,                              &
                                  q2l, q2r, chalf, vol1, vol2, area,           &
                                  powerv, mu)
        switch = ddt_max(switch, switchv)
      else
        switchv = my_0
      end if

    else if (.not. adptv_entropy_fix) then

      switch  = my_1
      switchv = my_1

    else if (adptv_entropy_fix) then

      diss_path = my_0

!     Compute the flux type switch: N.B. switch=0==LDFSS(0), 1==LDFSS(2)

!     Compute the Roe averages of the primitive variables

      rho = ddt_sqrt(rhol*rhor)
      wat = rho/(rho + rhor)
      u   = ul*wat + ur*(my_1 - wat)
      v   = vl*wat + vr*(my_1 - wat)
      w   = wl*wat + wr*(my_1 - wat)
      q2  = u*u + v*v + w*w
      h   = Hl*wat + Hr*(my_1 - wat)
      c2  = gm1*(h - my_half*q2)
      c   = ddt_sqrt(c2)
      ubar = xnorm*u + ynorm*v + znorm*w

      switch = iswch_coef_ddt(rx1,ry1,rz1,rx2,ry2,rz2,gradx1,grady1,gradz1,    &
                          gradx2,grady2,gradz2,pressl,pressr,phi1,phi2,        &
                          ubarl,ubarr,q2l,q2r,q2,c2,laplcc,poweri,behavior)

!     Compute the cell face reynolds number to make the switch coeff. one
!     on low Reynolds number cells faces

      if (ivisc >= 2) then
        ubar = xnorm*u + ynorm*v + znorm*w
        switchv = vswch_coef_ddt(wat,rho,                                      &
                             q2l,ubarl,q2r,ubarr,ubar,c,vol1,vol2,area,        &
                             powerv,mu)
        if (switchv > switch) switch = my_1
      else
        switchv = my_0
      end if

    end if

!   Sonic rarefaction treatment (unpublished to the best of my knowledge)

    xmfunct = ddt_sin(my_half*pi*ddt_min(xmhalf, my_1))
    btfunct = my_4th * (rmbl - rmbr - ddt_abs(rmbl-rmbr))
    fact    = ddt_max(-my_half, btfunct) * xmfunct

!   Switch between the LDFSS(0) [switch=0] with the LDFSS(2) scheme [switch=1]

    xmcl = switch*xmcl + (my_1 - diss_path*switchv)*fact
    xmcr = switch*xmcr + (my_1 - diss_path*switchv)*fact

!   Mass flux splitting (combination of eq. 24 with 47 and 25 with 47)

    mfil = (alfl*(my_1+btl)*rmbl - btl*xmml - xmcl) * (rhol * chalf)
    mfir = (alfr*(my_1+btr)*rmbr - btr*xmmr + xmcr) * (rhor * chalf)

!   Pressure splitting  and interface pressure for the momentum eq.s

    ppl = my_4th * (rmbl+my_1)**2 * (my_2-rmbl) ! eq. 20
    ppr = my_4th * (rmbr-my_1)**2 * (my_2+rmbr) ! eq. 20
    pnet = (alfl*(my_1+btl)-btl*ppl)*pressl +                                  &
           (alfr*(my_1+btr)-btr*ppr)*pressr ! eq. 18 and 19

!   Compute the contribution to the flux balance

    flux_ldfss_ddt(1) = area*(mfil    + mfir)
    flux_ldfss_ddt(2) = area*(mfil*ul + mfir*ur + pnet*xnorm)
    flux_ldfss_ddt(3) = area*(mfil*vl + mfir*vr + pnet*ynorm)
    flux_ldfss_ddt(4) = area*(mfil*wl + mfir*wr + pnet*znorm)
    flux_ldfss_ddt(5) = area*(mfil*Hl + mfir*Hr + face_speed*pnet)

    flux_ldfss_ddt(6) = switch

  end function flux_ldfss_ddt

!================================= KW_SOURCE =================================80
!
! This routine computes the Jacobians turbulent source terms for K_W
!
!=============================================================================80

  pure function dkw_source( xmre, rnu, amut_i, distance, turb,                 &
                            vgradx, vgrady, vgradz, tgradx, tgrady, tgradz )

    use kinddefs,          only : dp
    use turb_parameters,   only : t_dest
    use turb_kw_const,     only : beta1, beta2, betastar, sig_w2,              &
                                  strain_production, sstrc, sstrc_crc

    real(dp),               intent(in) :: xmre, rnu, distance, amut_i
    real(dp), dimension(3), intent(in) :: vgradx, vgrady, vgradz
    real(dp), dimension(2), intent(in) :: tgradx, tgrady, tgradz
    real(dp), dimension(2), intent(in) :: turb

    real(dp), dimension(2,2) :: dkw_source

    real(dp) :: ux, uy, uz, vx, vy, vz, wx, wy, wz, xis, sij, ri, f4

    real(dp) :: blend, term
!    real(dp) :: gg, pk, pw, dk, dw, tij, vort, rmut
    real(dp) :: tij, vort, rmut
    real(dp) :: dkdk, dkdw, dwdk, dwdw
    real(dp) :: betab, w1, w2, crossterm
    real(dp) :: arg, arga, argt, arg1, arg2, arg3, dist, omega
    real(dp) :: rwx, rwy, rwz, rkx, rky, rkz, xmr, xmr2
    real(dp) :: turb1, turb2

    real(dp), parameter     :: my_tiny = tiny(1.0_dp)

  continue

!   Calculate the cross-derivative term as well as the blending function

    xmr = xmre
    xmr2 = xmr*xmr

      rkx = tgradx(1)
      rky = tgrady(1)
      rkz = tgradz(1)
      rwx = tgradx(2)
      rwy = tgrady(2)
      rwz = tgradz(2)

      turb1 = turb(1)
      turb2 = turb(2)

      omega = turb2
      dist = distance
      if(abs(dist) <= my_tiny) dist = 1.0e-12_dp

      term = rkx*rwx + rky*rwy + rkz*rwz
      crossterm = 2.0_dp*xmr/sig_w2*term/omega

!     Blending function

      arg1 = xmr*sqrt(turb1)/(betastar*omega*dist)
      arg2 = 500.0_dp*xmr2*rnu/(dist*dist*omega)
      argt = max(crossterm/xmr,1.0e-20_dp)
      arg3 = 4.0_dp/sig_w2*turb1/(argt*dist*dist)
      arga = max(arg1,arg2)
      arg  = min(arga,arg3)

      blend = tanh(arg*arg*arg*arg)

!   Now lets compute the source term

      w1 = blend
      w2 = 1.0_dp - blend
!     g1 = beta1/betastar - kappa*kappa/sig_w1/sqrt(betastar)
!     g2 = beta2/betastar - kappa*kappa/sig_w2/sqrt(betastar)
!     gg = w1*g1 + w2*g2
      betab = w1*beta1 + w2*beta2

      ux   = vgradx(1)
      uy   = vgrady(1)
      uz   = vgradz(1)
      vx   = vgradx(2)
      vy   = vgrady(2)
      vz   = vgradz(2)
      wx   = vgradx(3)
      wy   = vgrady(3)
      wz   = vgradz(3)

      rmut = amut_i + 1.0e-6_dp
      xis = ux**2 + vy**2 + wz**2 + 0.5_dp*((uy + vx)**2 + (uz + wx)**2 +      &
            (vz + wy)**2)
      vort = sqrt((wy-vz)**2+(uz-wx)**2+(vx-uy)**2)
      if (strain_production) then
        tij = 2.0_dp*rmut*xis
      else
        tij = rmut*vort*vort
      end if
      if(abs(tij) <= my_tiny) tij = 1.0e-6_dp

!     Curvature Correction
      f4 = 1.0_dp
      if (sstrc) then
        sij = sqrt(2.0_dp*xis) + 1.0e-20_dp
        ri = (vort/sij)*(vort/sij - 1.0_dp)
        f4 = 1.0_dp/(1.0_dp + sstrc_crc*ri)
      end if

!     Production

!      Pk = abs(xmr/rho*tij)
!      Pw = xmr*gg/rmut*tij

!     Destruction

!      Dk = betastar*turb1*turb2/xmr
!      Dw = f4*betab*turb2*turb2/xmr - (1.0_dp - blend)*crossterm

      Dkdk = betastar*turb2/xmr
      Dkdw = betastar*turb1/xmr
      Dwdk = 0.0_dp
      Dwdw = f4*2.0_dp*betab*turb2/xmr

!     Limit on k-production term

!        Pk = min(Pk,20.0_dp*Dk)

!     Ambient terms - Spalart and Rumsey, AIAA J. Vol.45 No.10
!       if (keepambient) then
!         Dk = Dk - betastar*k_inf*w_inf/xmr
!         Dw = Dw - betab*w_inf*w_inf/xmr
!       end if

!    kw_source(1) = t_prod*Pk - t_dest*Dk
!    kw_source(2) = t_prod*Pw - t_dest*Dw

    dkw_source(1,1) =  - t_dest*Dkdk
    dkw_source(1,2) =  - t_dest*Dkdw
    dkw_source(2,1) =  - t_dest*Dwdk
    dkw_source(2,2) =  - t_dest*Dwdw

  end function dkw_source

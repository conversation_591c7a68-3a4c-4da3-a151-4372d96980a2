module defect_correction

  use kinddefs,            only : dp
  use info_depr,           only : skeleton
  use lmpi,                only : lmpi_id, lmpi_conditional_stop, lmpi_reduce, &
                                  lmpi_master, lmpi_max_and_maxid, lmpi_bcast
  use grid_types,          only : grid_type, mass_type
  use solution_types,      only : soln_type
  use comprow_types,       only : crow_flow
  use debug_defs,          only : debug_q, debug_q_proc, debug_q_loc,          &
                                  stability_rinds, stability_rind_adj
  use inviscid_flux,       only : first_order_iterations

  implicit none

  private

  public :: defect_correction_local

  integer, parameter :: neim = 400*5
  integer, parameter :: mm   = 400
  integer, parameter :: neqm = 5
  integer, parameter :: work_size = neim*10

  real(dp), dimension(neim,neim) :: a, b, c

contains


!======================= DEFECT_CORRECTION_LOCAL =============================80
!
! Check defect correction locally.
!
!=============================================================================80

  subroutine defect_correction_local(grid, soln, crow, mass)

    use stability_defs,           only : d_lhs, a_lhs, d_rhs, a_rhs,           &
                                         r00, skip_jac, spectral_local, adj_rhs
    use comprow_util,             only : max_adj_faces, max_adj_faces_1,       &
                                         max_adj_lsq, find_entry_list
    use composite_jacobian,       only : lhs_rhs_crs_setup
    use comprow,                  only : set_comprow_flow_rhs
    use system_extensions,        only : se_open

    type(grid_type),               intent(inout) :: grid
    type(soln_type),               intent(inout) :: soln
    type(crow_flow),               intent(inout) :: crow
    type(mass_type), dimension(:), intent(in)    :: mass

    integer :: neig, ierr, set, n_mf, dc_file = 60
    integer :: max_adj, max_adj_1, max_adj_rhs
    integer :: neq, entry, i, ii, iii, k, row, dof_sets, n_list
    integer :: dof, dof_off, proc_with_max, dof_1
    integer :: rind_entry, n_list_max
    real(dp) :: rho, rho_max

    integer, dimension(:), allocatable :: dof_list

    real(dp), dimension(:,:,:,:),allocatable :: lhs, rhs
    real(dp), dimension(:,:),    allocatable :: zero_block, one_block

    logical :: found, check_entire_row_found
    logical, dimension(neim,neim) :: fm

    character(len=80) :: string
    character(len=80) :: jacobian_type

    logical :: inspecting

  continue

    call set_comprow_flow_rhs(grid, crow)

    call se_open(dc_file,file='local_defect_correction',  &
         form='formatted')

    rewind(dc_file)

    rho_max = -huge(1._dp)

    ierr         = 0
    inspecting   = .false.
    if ( debug_q ) then
      if ( lmpi_id == debug_q_proc ) then
       inspecting = .true.
      endif
    endif

    write (6,*) "inspecting",inspecting
    write (6,*) " .......debug_q=",debug_q
    write (6,*) " ...debug_q_loc=",debug_q_loc

    call test_linear_algebra()

    n_mf    = soln%n_q - soln%n_turb

    set          = 1
    jacobian_type='meanflow'
    neq          = n_mf

    allocate(r00(neq,soln%dof0))
    allocate(spectral_local(soln%dof0))

    allocate( skip_jac( size(grid%skip_q,1) ) )

    skip_jac(:) = .false.
    do dof = 1,size(grid%skip_q,1)
      if ( grid%skip_q(dof) > 0 ) skip_jac(dof) = .true.
    enddo

    skeleton = 0 !turn off skeleton

    call lhs_rhs_crs_setup(grid, soln, crow, mass, set, r00, skip_jac )

    call max_adj_faces(  soln%dof0, crow, max_adj)
    call max_adj_faces_1(soln%dof0, crow, max_adj_1)
    call max_adj_lsq(    soln%dof0, grid, crow, max_adj_rhs)

    allocate( dof_list(soln%dof0) )

    n_list_max = 0
    find_n_list_max : do dof = 1, soln%dof0

      if ( skip_jac(dof) ) cycle

      call expand_list( soln, crow, dof, n_list, dof_list )

      n_list_max = max( n_list_max, n_list )

    enddo find_n_list_max

    deallocate( dof_list )
    allocate( dof_list(n_list_max) )

    if ( n_list_max > mm ) then
      write (6,*) " stopping in defect_correction_local"
      write (6,*) " ........ n_list_max=",n_list_max
      write (6,*) " ...max dof possible=",mm
      ierr = 1
    end if
    call lmpi_conditional_stop(ierr)

    if ( neq > neqm ) then
      write (6,*) " stopping in defect_correction_local"
      write (6,*) " ...block size to be solved=",neq
      write (6,*) " ...maximum number possible=",neqm
      ierr = 1
    end if
    call lmpi_conditional_stop(ierr)

    allocate( lhs(neqm, neqm, mm, mm ) )
    allocate( rhs(neqm, neqm, mm, mm ) )

    allocate( zero_block(neqm, neqm ) )
    allocate(  one_block(neqm, neqm ) )

    zero_block(:,:) = 0._dp

    one_block(:,:)  = 0._dp
    do i=1,neqm
      one_block(i,i)  = 1._dp
    enddo

    dof_sets = 0
    found = .true.
    assembly : do dof = 1, soln%dof0

      spectral_local(dof) = 0._dp
      if ( skip_jac(dof) ) cycle

      fm(:,:) = .false.

      dof_sets       = dof_sets + 1

      row          = 0
      lhs(:,:,:,:) = 0._dp
      rhs(:,:,:,:) = 0._dp

      call expand_list( soln, crow, dof, n_list, dof_list )

      if ( inspecting .and. debug_q_loc == dof_list(1) ) then
        write (6,*) " ...stability_rinds (adj expansions)=",stability_rinds
        write (6,*) " .................stability_rind_adj=",stability_rind_adj
        write (6,*) " .....................resulting dofs=",n_list
      endif

      do rind_entry = 1,n_list

        dof_1 = dof_list(rind_entry)

        row  = row + 1
        found = find_entry_list(dof_1, n_list, dof_list, 1, entry)
        if ( .not.found ) exit assembly
        i     = entry

        !...diagonal Jacobian.
        fm(i,i)              = .true.
        lhs(1:neq,1:neq,i,i) = d_lhs(1:neq,1:neq,dof_1)
        rhs(1:neq,1:neq,i,i) = d_rhs(1:neq,1:neq,dof_1)

        do k = adj_rhs%ia(dof_1), adj_rhs%ia(dof_1+1) - 1

          dof_off = adj_rhs%ja(k)
          found   = find_entry_list(dof_off, n_list, dof_list, 1, entry)
          if ( .not.found ) cycle
          ii      = entry

          !...off diagonal Jacobian.
          fm(i,ii)              = .true.
          lhs(1:neq,1:neq,i,ii) = a_lhs(1:neq,1:neq,k)
          rhs(1:neq,1:neq,i,ii) = a_rhs(1:neq,1:neq,k)

        end do
      end do

      if ( inspecting .and. debug_q_loc == dof_list(1) ) then
        string = trim(jacobian_type)
        call info_matrixf(string, n_list, dof_list,               &
                          fm, skip_jac, grid%xq, grid%yq, grid%zq )
        string = ' LHS:' // trim(jacobian_type)
        call info_matrix(string, neqm, mm, neq, n_list, dof_list, &
                         lhs, skip_jac, grid%xq, grid%yq, grid%zq )
        string = ' RHS:' // trim(jacobian_type)
        call info_matrix(string, neqm, mm, neq, n_list, dof_list, &
                         rhs, skip_jac, grid%xq, grid%yq, grid%zq )
      endif

      found = .true.
      check_entire_row_found = .false.
      if ( ( stability_rinds == 0 ) .or.                            &
           ( stability_rinds == 1 .and. stability_rind_adj == 1 ) ) &
      check_entire_row_found = .true.
      if ( check_entire_row_found ) then
        do ii=1,n_list
          do iii=1,n_list
            found = fm(ii,iii)
            if (found ) cycle
            ierr = 999 ; found = .true. ; exit assembly
          enddo
        enddo
      endif

      call unroll_block_matrix(neqm, mm, neq, n_list, lhs, a, ierr)
      call unroll_block_matrix(neqm, mm, neq, n_list, rhs, b, ierr)

      neig = neq*n_list
      call spectral_dc(neig, a, b, c, rho, ierr)

      rho_max = max( rho, rho_max )
      write (6,"(1x,a,i6,i4,3f20.5,f12.5)")                    &
      "dof,n_list,x,y,z,eigenvalue=",                          &
      dof, n_list, grid%xq(dof), grid%yq(dof), grid%zq(dof), rho
      spectral_local(dof) = rho

    enddo assembly

    if ( .not.found ) ierr = ierr + 1
    if ( ierr > 0 ) then
      write (6,"(1x,a,i10)")                            &
      'DOF not found in dof_list...stopping...ierr=',ierr
      write (6,"(1x,a,i4,2(a,i10))")                &
      '...lmpi_id=',lmpi_id,'...dof=',dof,' row=',row
      string = trim(jacobian_type)
      call info_matrixf(string, n_list, dof_list,               &
                        fm, skip_jac, grid%xq, grid%yq, grid%zq )
      string = ' LHS:' // trim(jacobian_type)
      call info_matrix(string, neqm, mm, neq, n_list, dof_list, &
                       lhs, skip_jac, grid%xq, grid%yq, grid%zq )
      string = ' RHS:' // trim(jacobian_type)
      call info_matrix(string, neqm, mm, neq, n_list, dof_list, &
                       rhs, skip_jac, grid%xq, grid%yq, grid%zq )
      write (6,"(1x,a,i4,2(a,i10))")                            &
      'Problem in adjacency search...defect_correction_local'
    endif

    call lmpi_conditional_stop(ierr)

    write (6,"(1x,a,f12.5,a,i10)") '..max eigenvalue=',rho_max, &
                                        ' n_list_max=',n_list_max

    i = dof_sets  ; call lmpi_reduce(i,dof_sets)
    call lmpi_max_and_maxid( real(rho_max,dp), proc_with_max )
    call lmpi_bcast( rho_max, proc_with_max )
    if ( lmpi_master ) then
      do ii=6,dc_file,dc_file-6
        write(ii,"(1x,2a)")                                        &
        'Local stability check...jacobian_type=',trim(jacobian_type)
        write(ii,"(1x,a,i10)")                     &
        '..........stability_rinds=',stability_rinds
        write(ii,"(1x,a,i10)")                        &
        '.......stability_rind_adj=',stability_rind_adj
        write(ii,"(1x,a,i10,a)")                &
        '...............n_list_max=',n_list_max,&
        ' : max dof in local eigenvalue problems'
        write(ii,"(1x,a,i10)")                            &
        "...first_order_iterations=",first_order_iterations
        write(ii,"(1x,a,e20.6)")  &
        "..max eigenvalue=",rho_max
        write(ii,"(1x,a,i10)") &
        ".............neq=",neq
        write(ii,"(1x,a,i10)")     &
        "........dof_sets=",dof_sets
        write(ii,"(1x,a,i10)")      &
        ".......dof_total=",soln%dofg
        write(ii,"(1x,a,e20.6)")                               &
        "..fraction_total=",real(dof_sets,dp)/real(soln%dofg,dp)
      enddo
    endif

    ! deallocate the work arrays

    deallocate(dof_list) ; deallocate(lhs)     ; deallocate(rhs)

    deallocate(d_lhs)   ; deallocate(d_rhs) ; deallocate(skip_jac)
    deallocate(a_lhs)   ; deallocate(a_rhs) ; deallocate(r00)

    close(dc_file)

  end subroutine defect_correction_local

!======================= SPECTRAL ============================================80
!
! Find spectral radii.
!
!=============================================================================80
  subroutine spectral(nd, mm, neq, neqm, matrix, rho, ierr)

    use eigen_eispack,            only : cg

    integer,                              intent(in)  :: mm, nd, neq, neqm
    integer,                              intent(out) :: ierr
    real(dp),                             intent(out) :: rho
    real(dp), dimension(neqm,neqm,mm,mm), intent(in)  :: matrix

    integer :: i, ichk, ii, io, iz, j, jj, jo, m, matz, n, neig

    real(dp) :: gtemp

    real(dp), dimension(neim) :: f1,f2,f3,wi,wr

    real(dp), dimension(neim,neim) :: ai,ar,zi,zr

  continue

    ierr = 0

    neig = nd * neq
    if (neig > neim) then
      write (6,*) " stopping....insufficient dimensions in spectral"
      write (6,*) " number of eigenvalues needed =", neig
      write (6,*) "      maximum number possible =", neim
      ierr = 1
      return
    end if
    do m = 1,nd
      do n = 1,nd
        do ii = 1,neq
          do jj = 1,neq
            io = neq*(m-1) + ii
            jo = neq*(n-1) + jj
            ar(io,jo) = matrix(ii,jj,m,n)
            ai(io,jo) = 0._dp
          end do
        end do
      end do
    end do

    matz = 0
    ichk = 0
    if (ichk == 1) then
      do iz = 1,neig
        write (6,*) " real parts of amatrix=", iz, (ar(iz,j), j = 1,neig)
        write (6,*) " imag parts of amatrix=", iz, (ai(iz,j), j = 1,neig)
      end do
    end if
    call cg(neim,neig,ar,ai,wr,wi,matz,zr,zi,f1,f2,f3,ierr)
    if ( ierr /= 0 ) return
    rho = -10._dp
    do i = 1,neig
      gtemp = sqrt(wr(i)**2+wi(i)**2)
      if (ichk == 1) then
        write (6,*) "eigenvalue=", i, gtemp
      end if
      if (gtemp > rho) then
        rho = gtemp
      end if
    end do

  end subroutine spectral

!======================= SPECTRAL_DC =========================================80
!
! Find spectral radii of defect correction.
!
!=============================================================================80
  subroutine spectral_dc(neig, driver, target, matrix_dc, rho, ierr)

    use eigen_eispack,            only : cg
    use invert_lapack,            only : invert

    integer,                        intent(in)  :: neig
    integer,                        intent(out) :: ierr
    real(dp),                       intent(out) :: rho
    real(dp), dimension(neim,neim), intent(in)  :: target, driver
    real(dp), dimension(neim,neim), intent(out) :: matrix_dc

    integer :: i, ichk, ii, iz, j, jj, matz, kk

    real(dp) :: gtemp

    real(dp), dimension(neim) :: f1, f2, f3, wi, wr

    real(dp), dimension(neim,neim) :: ai, ar, zi, zr, c

  continue

    ierr = 0

    if (neig > neim) then
      write (6,*) " stopping....insufficient dimensions in spectral"
      write (6,*) " number of eigenvalues needed =", neig
      write (6,*) "      maximum number possible =", neim
      ierr = 1
      return
    end if

    c(1:neig,1:neig) = driver(1:neig,1:neig)
    call invert(neim, neig, c, work_size )

    do ii = 1,neig
      do jj = 1,neig
        ar(ii,jj) = 0._dp
        do kk = 1,neig
          ar(ii,jj) = ar(ii,jj) - c(ii,kk)*target(kk,jj)
        end do
      end do
    end do

    do ii = 1,neig
      ar(ii,ii) = 1._dp + ar(ii,ii)
    end do

    do ii = 1,neig
      do jj = 1,neig
        matrix_dc(ii,jj) = ar(ii,jj)
      end do
    end do

    ai(1:neig,1:neig) = 0._dp

    matz = 0
    ichk = 0
    if (ichk == 1) then
      do iz = 1,neig
        write (6,*) " real parts of amatrix=", iz, (ar(iz,j), j = 1,neig)
        write (6,*) " imag parts of amatrix=", iz, (ai(iz,j), j = 1,neig)
      end do
    end if
    call cg(neim,neig,ar,ai,wr,wi,matz,zr,zi,f1,f2,f3,ierr)
    if ( ierr /= 0 ) return
    rho = -10._dp
    do i = 1,neig
      gtemp = sqrt(wr(i)**2+wi(i)**2)
      if (ichk == 1) then
        write (6,*) "eigenvalue=", i, gtemp
      end if
      if (gtemp > rho) then
        rho = gtemp
      end if
    end do

  end subroutine spectral_dc

!===================================== INFO_MATRIX ===========================80
!
! Info about matrix terms.
!
!=============================================================================80

  subroutine info_matrix( string, neqm, mm, neq, n_list, dof_list,             &
                          matrix, skip_jac, x, y, z )

    integer,                              intent(in)  :: neqm, mm, neq, n_list
    integer,  dimension(:),               intent(in)  :: dof_list
    real(dp), dimension(neqm,neqm,mm,mm), intent(in)  :: matrix
    character(len=80),                    intent(in)  :: string
    logical,  dimension(:),               intent(in)  :: skip_jac
    real(dp), dimension(:),               intent(in)  :: x, y, z

    integer  :: i, j, k, kk

  continue

    do k=1,n_list
      write(*,*)
      write(*,'(2x,3a,i3,i10,L2)')                           &
      "=== ",trim(string)," Jacobians For DOF (k,dof,skip)=",&
      k,dof_list(k),skip_jac(dof_list(k))
      write(*,*)
      write(*,'(8x,a,3f20.10)')                           &
      "x/y/z=",x(dof_list(k)),y(dof_list(k)),z(dof_list(k))
      write(*,*)
      do kk = 1, n_list
        write(*,*)
        if ( k == kk ) then
          write(*,'(2x,2a,i3,i10,L2)')                                    &
          trim(string),"  Block Diagonal For DOF (k,dof,skip)=",          &
          k,dof_list(k),skip_jac(dof_list(k))
        else
          write(*,'(2x,2a,i3,i10,L2,a,i3,i10,L2,a)')                      &
          trim(string)," Block Jacobians For DOF ",                       &
          k,dof_list(k),skip_jac(dof_list(k)),                            &
          "   Due to DOF ",kk,dof_list(kk),skip_jac(dof_list(kk))
        endif
        write(*,*)
        do i=1,neq
          write(*,'(10(3x,e13.5))')(real(matrix(i,j,k,kk)),j=1,neq)
        end do
      end do
    end do

  end subroutine info_matrix

!===================================== INFO_MATRIXF ==========================80
!
! Info about matrix terms.
!
!=============================================================================80

  subroutine info_matrixf( string, n_list, dof_list,                           &
                          matrix, skip_jac, x, y, z )

    integer,                              intent(in)  :: n_list
    integer,  dimension(:),               intent(in)  :: dof_list
    logical,  dimension(neim,neim),       intent(in)  :: matrix
    character(len=80),                    intent(in)  :: string
    logical,  dimension(:),               intent(in)  :: skip_jac
    real(dp), dimension(:),               intent(in)  :: x, y, z

    integer  :: k, kk

  continue

    do k=1,n_list
      write(*,*)
      write(*,'(2x,3a,i3,i10,L2)')                            &
      "=== ",trim(string)," Check Fill For DOF (k,dof,skip)=",&
      k,dof_list(k),skip_jac(dof_list(k))
      write(*,*)
      write(*,'(8x,a,3f20.10)')                           &
      "x/y/z=",x(dof_list(k)),y(dof_list(k)),z(dof_list(k))
      write(*,*)
      do kk = 1, n_list
        write(*,*)
        if ( k == kk ) then
          write(*,'(2x,2a,i3,i10,L2)')                                    &
          trim(string),"  Block Diagonal For DOF (k,dof,skip)=",          &
          k,dof_list(k),skip_jac(dof_list(k))
        else
          write(*,'(2x,2a,i3,i10,L2,a,i3,i10,L2,a)')                      &
          trim(string)," Block Jacobians For DOF ",                       &
          k,dof_list(k),skip_jac(dof_list(k)),                            &
          "   Due to DOF ",kk,dof_list(kk),skip_jac(dof_list(kk))
        endif
        write(*,*)
        write(*,'(10(3x,L2))') matrix(k,kk)
      end do
    end do

  end subroutine info_matrixf

!======================= UNROLL_BLOCK_MATRIX =================================80
!
! Unroll block matrix.
!
!=============================================================================80
  subroutine unroll_block_matrix(neqm, mm, neq, nd, matrix, a, ierr)

    integer,                              intent(in)  :: neqm, mm, neq, nd
    real(dp), dimension(neqm,neqm,mm,mm), intent(in)  :: matrix
    integer,                              intent(out) :: ierr
    real(dp), dimension(neim,neim),       intent(out) :: a

    integer :: ii, io, jj, jo, m, n, neig

  continue

    ierr = 0

    neig = nd * neq
    if (neig > neim) then
      write (6,*) " stopping....insufficient dimensions in spectral"
      write (6,*) " number of eigenvalues needed =", neig
      write (6,*) "      maximum number possible =", neim
      ierr = 1
      return
    end if

    do m = 1,nd
      do n = 1,nd
        do ii = 1,neq
          do jj = 1,neq
            io = neq*(m-1) + ii
            jo = neq*(n-1) + jj
            a(io,jo) = matrix(ii,jj,m,n)
          end do
        end do
      end do
    end do

  end subroutine unroll_block_matrix

!======================= TEST_LINEAR_ALGEBRA =================================80
!
! Test linear algebra routines.
!
!=============================================================================80

  subroutine test_linear_algebra()

    use eigen_eispack,            only : cg
    use invert_lapack,            only : invert

    integer :: neig, matz, ierr
    integer :: nd, neq, i
    real(dp), dimension(neim,neim) :: ar,ai,zr,zi
    real(dp), dimension(neim)      :: wr,wi,f1,f2,f3
    real(dp) :: gtemp, rho

    real(dp), dimension(neqm,neqm,mm,mm) :: matrix

    real(dp), dimension(neim,neim) :: test_inv

  continue

    ierr         = 0

    neig = 2

    test_inv(:,:) = 0._dp
    test_inv(1,1) = 1._dp
    test_inv(2,2) = 2._dp
    do i = 1,neig
      write (6,*) " test-matrix=", i, test_inv(i,1:neig)
    end do

    call invert(neim, neig, test_inv, work_size )

    do i = 1,neig
      write (6,*) "inverse=", i, test_inv(i,1:neig)
    end do

    matz = 0

    ai(:,:) = 0._dp
    ar(:,:) = 0._dp
    ar(1,1) = 1._dp
    ar(2,2) = 0._dp
    do i = 1,neig
      write (6,*) " test-matrix=", i, ar(i,1:neig)
    end do
    call cg(neim,neig,ar,ai,wr,wi,matz,zr,zi,f1,f2,f3,ierr)

    do i = 1,neig
      gtemp = sqrt(wr(i)**2+wi(i)**2)
      write (6,*) "eigenvalue=", i, gtemp
    end do

    if ( ierr > 0 )                                    &
    write (6,*) "abnormal exit in cg...lmpi_id=",lmpi_id
    call lmpi_conditional_stop(ierr)

    neq = 2
    nd  = 1
    matrix(:,:,:,:) = 0._dp
    matrix(1,1,1,1) = 1._dp
    matrix(2,2,1,1) = 0._dp
    do i = 1,neig
      write (6,*) " test-matrix=", i, matrix(i,1:neig,1,1)
    end do
    call spectral(nd, mm, neq, neqm, matrix, rho, ierr)

    write (6,*) "max eigenvalue=",rho

    if ( ierr > 0 )                                    &
    write (6,*) "abnormal exit in cg...lmpi_id=",lmpi_id
    call lmpi_conditional_stop(ierr)

    do i = 1,neig
      write (6,*) " test-matrix=", i, matrix(i,1:neig,1,1)
    end do

    call unroll_block_matrix(neqm, mm, neq, nd, matrix, a, ierr)

    do i = 1,neig
      write (6,*) " unrolled-matrix=", i, a(i,1:neig)
    end do

    if ( ierr > 0 ) write (6,*) &
    "abnormal exit in unroll_block_matrix...lmpi_id=",lmpi_id
    call lmpi_conditional_stop(ierr)

  end subroutine test_linear_algebra

!======================= EXPAND_LIST =========================================80
!
! Expand list of local dof using adjacencies of either:
! (1) crow%ia simply-connected (2) crow%ia (3) adj_rhs%.
!
!=============================================================================80

  subroutine expand_list( soln, crow, dof, n_list, dof_list )

    use stability_defs,           only : adj_rhs, skip_jac
    use comprow_util,             only : find_entry_list

    type(soln_type), intent(in) :: soln
    type(crow_flow), intent(in) :: crow

    integer,               intent(in)  :: dof
    integer,               intent(out) :: n_list
    integer, dimension(:), intent(out) :: dof_list

    integer :: k, k_sta, k_end, entry, dof_1, dof_2
    integer :: rind, rind_entry, n_list_sta, n_list_end

    logical :: found

  continue

    !...n_list -> adjacencies + diagonal entry.

    n_list           = 1
    dof_list(n_list) = dof
    n_list_sta       = n_list ; n_list_end = n_list

    do rind=1,stability_rinds
      do rind_entry = n_list_sta,n_list_end
        dof_1 = dof_list(rind_entry)
        if ( stability_rind_adj == 1 ) then
          k_sta = crow%ia(dof_1)
          k_end = crow%ia_ns(dof_1) - 1
        elseif ( stability_rind_adj == 2 ) then
          k_sta = crow%ia(dof_1)
          k_end = crow%ia(dof_1+1) - 1
        else
          k_sta = adj_rhs%ia(dof_1)
          k_end = adj_rhs%ia(dof_1+1) - 1
        endif
        do k = k_sta,k_end
          if ( stability_rind_adj == 1 .or. stability_rind_adj == 2 ) then
            dof_2 = crow%ja(k)
          else
            dof_2 = adj_rhs%ja(k)
          endif
          found   = find_entry_list(dof_2, n_list, dof_list, 1, entry)
          if ( found ) cycle
          if ( skip_jac(dof_2) ) cycle
          if ( dof_2 > soln%dof0 ) cycle
          n_list           = n_list + 1
          dof_list(n_list) = dof_2
        end do
      enddo
      n_list_sta = n_list_end + 1
      n_list_end = n_list
    enddo

 end subroutine expand_list

end module defect_correction

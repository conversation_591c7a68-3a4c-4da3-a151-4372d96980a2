#
# Assigned Shell Variables:
#   $with_meshsim      Build with MeshSim adaptation support
#
# Assigned Output Variables:
#   @meshsimlibrary@   Spec for path to MeshSim libraries
#
# Assigned AM_CONDITIONALS:
#   BUILD_MESHSIM_SUPPORT
#
AC_DEFUN([AX_MESHSIM],[

AC_ARG_WITH(meshsim,
        [[  --with-meshsim[=ARG]    use MeshSim adaptation package [ARG=no]]],
        [with_meshsim=$withval],     [with_meshsim="no"])

if test "$with_meshsim" != 'no'
then
  AC_CHECK_FILES([
                   $with_meshsim/libfun3dmeshsim.a
                   $with_meshsim/libSimAdvMeshing.a
                   $with_meshsim/libSimMeshing.a
                   $with_meshsim/libSimField.a
                   $with_meshsim/libSimParasolid250.a
                   $with_meshsim/libSimMeshTools.a
                   $with_meshsim/libSimPartitionedMesh.a
                   $with_meshsim/libSimPartitionWrapper.a
                   $with_meshsim/libSimModel.a
                   $with_meshsim/libpskernel.so
                 ],
                [have_meshsim='yes'],[have_meshsim='no'])

  if test "$have_meshsim" != 'no'
  then
    AC_DEFINE([HAVE_MESHSIM],[1],[MeshSim is available])
    # No idea how to specify runtime path system independently w/o libtool - SST
    meshsim_ldadd="-Xlinker -rpath -Xlinker $with_meshsim -L$with_meshsim -lfun3dmeshsim -lSimAdvMeshing -lSimMeshing -lSimField -lSimParaso"
    AM_CONDITIONAL(BUILD_MESHSIM_SUPPORT,true)
  else
    AC_MSG_ERROR([MeshSim requested but not found])
  fi
  AC_SUBST([meshsim_ldadd])
else
  AM_CONDITIONAL(BUILD_MESHSIM_SUPPORT,false)
fi

])


module stability_defs

  use kinddefs,             only : dp

  implicit none

  private
  public :: d_lhs, a_lhs
  public :: d_rhs, a_rhs
  public :: skip_jac, r00, q00, pjac00, H00
  public :: spectral_local
  public :: adj_rhs, crow_flow_rhs

  real(dp), dimension(:,:,:),allocatable :: d_lhs, d_rhs
  real(dp), dimension(:,:,:),allocatable :: a_lhs, a_rhs
  real(dp), dimension(:,:,:),allocatable :: pjac00
  real(dp), dimension(:,:,:),allocatable :: H00

  real(dp), dimension(:,:),  allocatable :: r00, q00

  logical, dimension(:), allocatable :: skip_jac

  real(dp), dimension(:),allocatable :: spectral_local

  type crow_flow_rhs
    integer                        :: nnz  ! Number of nonzeros
    integer, dimension(:), pointer :: ia
    integer, dimension(:), pointer :: ja
  end type crow_flow_rhs

  type(crow_flow_rhs) :: adj_rhs

end module stability_defs

module turb_ke

  use kinddefs, only : dp
  use lmpi,     only : lmpi_conditional_stop
  use amut,     only : mut_ke0
  use info_depr,only : skeleton

  implicit none

  private

  public :: residual_ke
  public :: jacobian_ke
  public :: bc_ke_set_walls
  public :: flux_turb
  public :: compute_ctg
  public :: e_compress
  public :: ke_grad_sqrt_k
  public :: ke_source

  real(dp), save  :: friction_velocity, rhow

contains

!============================= RESIDUAL_KW ===================================80
!
! Driver routine for residual evaluation for ke model
!
!=============================================================================80

  subroutine residual_ke(grid, soln )

    use info_depr,       only : tightly_couple
    use grid_types,      only : grid_type
    use solution_types,  only : soln_type

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln

  continue

    coupling : if ( tightly_couple ) then

!     not yet implemented

    else coupling

      if ( skeleton > 20 ) write(*,*) 'Calling ke_resid in residual_ke'

      call  ke_resid(soln%eqn_set, soln%viscous_method, grid%nnodes0,          &
                     grid%nnodes01, grid%nedgeloc, grid%eptr,                  &
                     soln%turb, soln%q_dof, soln%turbres, grid%slen,           &
                     soln%gradx, soln%grady, soln%gradz, grid%vol, grid%xn,    &
                     grid%yn, grid%zn, grid%ra, grid%x, grid%y, grid%z,        &
                     grid%nedgeloc_2d, grid%nnodes0_2d, grid%node_pairs_2d,    &
                     grid%iflagslen, grid%facespeed, soln%n_turb, soln%n_tot,  &
                     soln%n_grd,                                               &
                     grid%nelem, grid%elem)

    end if coupling

  end subroutine residual_ke

!============================= JACOBIAN_KW ===================================80
!
! Driver routine for jacobian evaluation for ke model
!
!=============================================================================80

  subroutine jacobian_ke(grid, soln, crow)

    use info_depr,       only : tightly_couple
    use grid_types,      only : grid_type
    use solution_types,  only : soln_type
    use comprow_types,   only : crow_flow

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(crow_flow),   intent(in)    :: crow

  continue

    coupling : if ( tightly_couple ) then

!     not yet implemented

    else coupling

      if ( skeleton > 20 ) write(*,*) 'Calling ke_jacob in jacobian_ke'

      call ke_jacob(soln%eqn_set, soln%viscous_method, grid%nnodes0,           &
                     grid%nnodes01, grid%nedgeloc, soln%max_nnz,               &
                     grid%eptr, soln%turb, grid%slen, soln%q_dof,              &
                     soln%gradx, soln%grady, soln%gradz,                       &
                     grid%vol, grid%xn, grid%yn,                               &
                     grid%zn, grid%ra, soln%a_turb_diag, soln%a_turb_off,      &
                     crow%fhelp, grid%nedgeloc_2d, grid%nnodes0_2d,            &
                     grid%node_pairs_2d, grid%x, grid%y, grid%z, crow%nnz01,   &
                     crow%ia, crow%ja, grid%facespeed, soln%n_turb, soln%n_tot,&
                     soln%n_grd,                                               &
                     crow%nzg2m, crow%g2m                                      &
!                  , soln%amut                                                 &
                   , grid%nelem, grid%elem )

    end if coupling

  end subroutine jacobian_ke

!================================== KW_RESID =================================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for Wilcox-ke model on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine ke_resid(eqn_set, viscous_method, nnodes0, nnodes01,              &
                       nedgeloc, eptr, turb, qnode, res, slen, gradx, grady,   &
                       gradz, vol, xn, yn, zn, ra, x, y, z, nedgeloc_2d,       &
                       nnodes0_2d, node_pairs_2d, iflagslen, facespeed, n_turb,&
                       n_tot, n_grd, nelem, elem )

    use info_depr,      only : twod
    use element_types,  only : elem_type

    integer,                                     intent(in) :: eqn_set
    integer,                                     intent(in) :: viscous_method
    integer,                                     intent(in) :: nelem
    integer,                                     intent(in) :: n_turb
    integer,                                     intent(in) :: n_tot
    integer,                                     intent(in) :: n_grd
    integer,                                     intent(in) :: nedgeloc
    integer,                                     intent(in) :: nnodes0
    integer,                                     intent(in) :: nnodes01
    integer,                                     intent(in) :: nedgeloc_2d
    integer,                                     intent(in) :: nnodes0_2d


    integer,         dimension(2,nedgeloc),      intent(in)    :: eptr
    real(dp),        dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp),        dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp),        dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp),        dimension(nnodes01),        intent(in)    :: slen
    real(dp),        dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp),        dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp),        dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp),        dimension(nnodes01),        intent(in)    :: vol
    real(dp),        dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp),        dimension(nedgeloc),        intent(in)    :: ra
    real(dp),        dimension(nnodes01),        intent(in)    :: x, y, z
    integer,         dimension(2,nnodes0_2d),    intent(in)    :: node_pairs_2d
    integer,         dimension(nnodes01),          intent(in)    :: iflagslen
    real(dp),        dimension(nedgeloc),        intent(in)    :: facespeed
!   real(dp),        dimension(nnodes01),        intent(in)    :: amut
    type(elem_type), dimension(nelem),           intent(in)    :: elem

    integer :: n, nedge_flux_eval, node_src_eval, ielem
    integer :: i, ii

    real(dp), dimension(2) :: source_ke

  continue

    nedge_flux_eval = nedgeloc
    node_src_eval   = nnodes0
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
      node_src_eval   = nnodes0_2d
    endif

!   kloc = n_grd-1
!   wloc = n_grd

!=============================================================================80
! Compute the convective component.
!=============================================================================80
    edge_flux_eval0: do n = 1, nedge_flux_eval
      call ke_convection( n, nnodes0, nnodes01,                                &
                     nedgeloc, eptr, turb, qnode, res, xn, yn, zn, ra,         &
                     facespeed, n_turb, n_tot )
    end do edge_flux_eval0
!=============================================================================80
!=============================================================================80
!   Next compute the source (production/destruction) terms
!   Note that these terms are node-based and thus are grid transparent, and
!   hence only need be computed while processing the first element type
      source1 : do ii = 1, node_src_eval

        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if

!   source_ke = ke_source(i, eqn_set, nnodes01, nnodes, turb, qnode, slen  &
!                       , gradx, grady, gradz, vol, iflagslen              &
!                       , n_turb, n_tot, n_grd )

        source_ke= ke_source  (             turb(1,i), turb(2,i)               &
                              , qnode(1,i), qnode(2,i), qnode(5,i), slen(i)    &
                              , gradx(2,i), gradx(3,i), gradx(4,i)             &
                              , grady(2,i), grady(3,i), grady(4,i)             &
                              , gradz(2,i), gradz(3,i), gradz(4,i)             &
                              , vol(i), iflagslen(i), x(i) )

        res(1,i) = res(1,i) - source_ke(1)
        res(2,i) = res(2,i) - source_ke(2)

      end do source1
!=============================================================================80
!=============================================================================80
! --resid_mix_diff for mixed elementx
!=============================================================================80
!=============================================================================80
    if ( viscous_method == 0 ) then
        elem_flux_eval:  do ielem = 1, nelem
        call resid_mix_diff(eqn_set, viscous_method, nnodes0, nnodes01,        &
                            nedgeloc, eptr, turb, qnode, res, gradx, grady,    &
                            gradz, xn, yn, zn, ra, ielem, elem(ielem)%ncell,   &
                            elem(ielem)%c2n, elem(ielem)%c2e, x, y, z,         &
                            elem(ielem)%local_f2n, elem(ielem)%local_e2n,      &
                            elem(ielem)%local_f2e, elem(ielem)%e2n_2d,         &
                            nedgeloc_2d, elem(ielem)%face_per_cell,            &
                            elem(ielem)%node_per_cell,                         &
                            elem(ielem)%edge_per_cell, elem(ielem)%type_cell,  &
                            n_turb, n_tot, n_grd, elem(ielem)%face_2d,         &
                            slen )
        end do elem_flux_eval
    else
      call lmpi_conditional_stop(1,'edge-based issues:residual_ke')
    endif

  end subroutine ke_resid

!================================== KW_CONVECTION ============================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for Wilcox-ke model on mixed-element grids.
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
   subroutine ke_convection( n, nnodes0, nnodes01,                             &
                       nedgeloc, eptr, turb, qnode, res, xn, yn, zn, ra,       &
                       facespeed, n_turb, n_tot )

    use grid_motion_helpers, only : need_grid_velocity

    integer,                                     intent(in) :: n
    integer,                                     intent(in) :: n_turb
    integer,                                     intent(in) :: n_tot
    integer,                                     intent(in) :: nedgeloc
    integer,                                     intent(in) :: nnodes0
    integer,                                     intent(in) :: nnodes01


    integer,         dimension(2,nedgeloc),      intent(in)    :: eptr
    real(dp),        dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp),        dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp),        dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp),        dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp),        dimension(nedgeloc),        intent(in)    :: ra
    real(dp),        dimension(nedgeloc),        intent(in)    :: facespeed

    integer :: node1, node2

    real(dp) :: xnormf, ynormf, znormf, areaf, face_speed
    real(dp) :: u, v, w, ubar, uminus, uplus

    real(dp), parameter :: half     = 0.5_dp

  continue

        node1 = eptr(1,n)
        node2 = eptr(2,n)

!       Unit normal to dual face and area

        xnormf = xn(n)
        ynormf = yn(n)
        znormf = zn(n)
        areaf  = ra(n)

!       Dual face speed

        face_speed = 0._dp

        if (need_grid_velocity) then
          face_speed = facespeed(n)
        end if

!       First node

        u = qnode(2,node1)
        v = qnode(3,node1)
        w = qnode(4,node1)
        ubar   = xnormf*u + ynormf*v + znormf*w - face_speed
        uplus  = half * (ubar+abs(ubar))
        uminus = half * (ubar-abs(ubar))

!       Dissipative part
!              phi  = mu + mut/sig
!             (term = phi*(wxx + wyy + wzz))

        if(node1 <= nnodes0) then
            res(1,node1) = res(1,node1) + (uplus*turb(1,node1)                 &
                                        + uminus*turb(1,node2))*areaf
            res(2,node1) = res(2,node1) + (uplus*turb(2,node1)                 &
                                        + uminus*turb(2,node2))*areaf
        endif

!       Now do the other node (note that ubar pts in other direction)

        u = qnode(2,node2)
        v = qnode(3,node2)
        w = qnode(4,node2)
        ubar   = -(xnormf*u + ynormf*v + znormf*w - face_speed)
        uplus  = half * (ubar+abs(ubar))
        uminus = half * (ubar-abs(ubar))

        if (node2 <= nnodes0) then
            res(1,node2) = res(1,node2) + (uplus*turb(1,node2)                 &
                                        + uminus*turb(1,node1))*areaf
            res(2,node2) = res(2,node2) + (uplus*turb(2,node2)                 &
                                        + uminus*turb(2,node1))*areaf
        endif

  end subroutine ke_convection

!=============================================================================80
!=============================================================================80

!================================= KW_SOURCE =================================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for Wilcox-ke model on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
!      function ke_source(i, eqn_set, nnodes01, nnodes, turb, qnode            &
!                        , slen, gradx, grady, gradz, vol, iflagslen           &
!                        , n_turb, n_tot, n_grd )                              &
!   result ( source_ke )

       function ke_source  (             tke, varepsilon, rho, u, p, dist,     &
                     dudx, dvdx, dwdx, dudy, dvdy, dwdy, dudz, dvdz, dwdz,     &
                                     vol, iflagslen, x                      )  &
    result ( source_ke )

    use info_depr,      only : xmach, re, tref
    use nml_two_d_trans, only : turb_transition
    use debug_defs,     only : test_freestream
    use turb_ke_const,  only : ke_k_inf, ke_mut_inf, ce1_ke, ce2_ke,           &
                               keepambient, strain_production, slen_kdes,      &
                               cmu_temp, ke_version, cmu_ke, limit_f2,         &
                               write_profile
    use turb_kw_const,  only : turb_compress_model
    use fluid,          only : gamma, sutherland_constant
    use turb_util,      only : set_gradv, get_tau
    use turb_functions, only : get_prodk

    real(dp),        dimension(2)                           :: source_ke
!   integer,                                     intent(in) :: i
!   integer,    intent(in) :: eqn_set
    real(dp),   intent(in) :: tke
    real(dp),   intent(in) :: varepsilon
    real(dp),   intent(in) :: rho
    real(dp),   intent(in) :: u
    real(dp),   intent(in) :: p
    real(dp),   intent(in) :: dist
    real(dp),   intent(in) :: dudx, dvdx, dwdx
    real(dp),   intent(in) :: dudy, dvdy, dwdy
    real(dp),   intent(in) :: dudz, dvdz, dwdz
    real(dp),                intent(in) :: vol
    integer,                 intent(in) :: iflagslen
    real(dp),                intent(in) :: x

    integer  :: sunit
    real(dp) :: press
    real(dp) :: temp
    real(dp) :: rmut, vort, f4
!   real(dp) :: sijmag, ri, xis
    real(dp) :: Pk, Pd, Dk, Dd
    real(dp) :: xmr
    real(dp) :: turb_mach
    real(dp) :: rhoinv
    real(dp) :: xmrinv
    real(dp) :: ke_e_inf
    real(dp) :: f2_threshold
    real(dp) :: xsid, xsiw

    real(dp), parameter :: zero     = 0.0_dp
!   real(dp), parameter :: half     = 0.5_dp
    real(dp), parameter :: one      = 1.0_dp
    real(dp), parameter :: prodk_limiter = 100.0_dp
    real(dp), parameter :: prode_limiter = 0.0_dp
!   real(dp), parameter :: small         = 1.0e-10_dp
    real(dp), parameter :: rsmall        = 1.0e-12_dp

    real(dp)            :: cstar
    real(dp)            :: mu_lam
    real(dp)            :: prodk, prodk_full
    real(dp)            :: prode
    real(dp)            :: dissp_c
    real(dp)            :: etilde
    real(dp)            :: re_k, re_t
    real(dp)            :: f1
    real(dp)            :: f2
    real(dp)            :: ctg
    real(dp)            :: uplus, yplus, kplus, eplus
    real(dp)            :: knorm, enorm
    real(dp)            :: del2usq, del2k
    real(dp), dimension(3,3) :: gradv
    real(dp), dimension(3,3) :: sij
    real(dp), dimension(3,3) :: tau

    real(dp) :: x_lower,  x_upper
  continue

    x_lower = 1.64_dp
    x_upper = 1.66_dp

    DEL2USQ = zero
    DEL2K = zero
    cstar = sutherland_constant / tref

    ke_e_inf = cmu_ke * ke_k_inf * ke_k_inf / ke_mut_inf
    xmr     = xmach / re
    xmrinv  = one / xmr

    press      = abs( p )
    Pk         = zero
    Dk         = zero
    Pd         = zero
    Dd         = zero

!=============================================================================80
!=============================================================================80
!   Next compute the source (production/destruction) terms
!   Note that these terms are node-based and thus are grid transparent, and
!   hence only need be computed while processing the first element type

!     tke   = turb(1,i)
!     dissp = turb(2,i)

      rhoinv  = one/rho
      temp    = gamma * press * rhoinv
      mu_lam  = viscosity_law(cstar, temp)

!     xis  = gradx(2,i)**2 + grady(3,i)**2 + gradz(4,i)**2 + half              &
!        *( (grady(2,i) + gradx(3,i))**2                                       &
!        +  (gradz(2,i) + gradx(4,i))**2                                       &
!        +  (gradz(3,i) + grady(4,i))**2 )
!     vort  = sqrt( (grady(4,i)-gradz(3,i))**2                                 &
!                 + (gradz(2,i)-gradx(4,i))**2                                 &
!                 + (gradx(3,i)-grady(2,i))**2 )
      vort  = sqrt( (dwdy - dvdz)**2                                 &
                  + (dudz - dwdx)**2                                 &
                  + (dvdx - dudy)**2 )
!------------------------------------------------------------------------------
!        Curvature correction
!------------------------------------------------------------------------------
      f4 = one
!     if (sstrc) then
!       sijmag = sqrt(2.0_dp*xis) + 1.0e-20_dp
!       ri     = (vort/sijmag)*(vort/sijmag - 1.0_dp)
!       f4     = 1.0_dp/(1.0_dp + sstrc_crc*ri)
!     end if
!-----------------------------------------------------------------------------80
!        Temperature correction
!-----------------------------------------------------------------------------80
        ctg = one
        if ( cmu_temp ) then
!         ctg   = compute_ctg ( n_grd, n_turb, n_tot, qnode(1:n_tot,i)         &
!                         , turb(1:n_turb,i)                                   &
!                         , gradx(1:n_grd,i)                                   &
!                         , grady(1:n_grd,i)                                   &
!                         , gradz(1:n_grd,i) )
        endif
!-----------------------------------------------------------------------------80
      gradv = set_gradv( dudx, dudy, dudz , &
                         dvdx, dvdy, dvdz , &
                         dwdx, dwdy, dwdz )
!------------------------------------------------------------------------------
      rmut      = mut_ke0 ( rho, press, tke, varepsilon, dist )
      sij       = get_sij( gradv )
      prodk     = zero

!      prodk       =   TAU(I,1) * DQDX(I,2)
!1                   + TAU(I,2) * DQDY(I,3)
!2                   + TAU(I,3) * DQDZ(I,4)
!3                   + TAU(I,4) * (DQDY(I,2) + DQDX(I,3))
!4                   + TAU(I,5) * (DQDZ(I,2) + DQDX(I,4))
!5                   + TAU(I,6) * (DQDZ(I,3) + DQDY(I,4))
!       IF (POSPRD) PROD = ABS (PROD)
      select case ( strain_production )
      case ( .true. )
        tau       = get_tau ( sij, rmut, rho, tke )
        prodk     = rho * get_prodk ( tau, gradv )
      case ( .false. )
        prodk       = vort * vort * rmut
      end select
      prodk_full   = prodk
      f2_threshold =                                                           &
        (prodk/varepsilon)*((ce1_ke-1.0_dp)/ce2_ke) + (1.0_dp/ce2_ke)
!-----------------------------------------------------------------------------80
!        Compressibility correction
!-----------------------------------------------------------------------------80
      turb_mach = sqrt ( 2.0_dp * tke / temp )
      dissp_c = e_compress( rho, varepsilon, prodk, turb_mach                  &
                 , turb_compress_model )

    f1   = one
    f2   = one
    xsiw = zero
    xsid = zero
    select case ( ke_version )

    case ( 'hr' )
      f2   = one
      Dk = ctg * ( varepsilon + dissp_c ) * xmrinv
      if ( tke > zero ) then
        Pk = abs( prodk * xmr / rho )
        Pd = ce1_ke * f1 * prodk * (cmu_ke * tke / rmut ) * xmr
!       Pd = ce1_ke * f1 * prodk * varepsilon * xmr / ( rho * tke )
        Dd = ce2_ke * f2 * f4 * varepsilon * ( varepsilon / tke ) * xmrinv
      else
        Pk = zero
        Pd = zero
        Dd = zero
      endif
      xsiw = zero
      xsid = zero

    case ( 'abid-ke')

      f1 = one
      re_k = ( rho * sqrt(tke) * dist  / mu_lam ) * xmrinv
      re_k = min( re_k, huge(0.0_dp) )
      re_k = max( re_k, tiny(0.0_dp) )
      f2   = 1.0_dp - exp(-re_k/12.0_dp)
! Rumsey, Reif & Gatski, "Arbitrary Steady-state Solutions with the K-Epsilon
! Model"
      if ( limit_f2 ) then
        f2 = min(one, max(f2, f2_threshold) )
      endif

!     Pk = getProductionK( xmr, rho, prodk )
!     Dk = getDestructionK( dissp, dissp_c, xmr )
!     Dk = ctg * Dk
!     Pd = getProductionE( xmr, ce1_ke, cmu_ke, prodk, rmut, tke  )
!     Dd = getDestructionE( ce2_ke, f2,  dissp, tke, xmr )

!     Pd = ce1 * cmu * prodk * xmr * tke / rmut
      Dk = ctg * ( varepsilon + dissp_c ) * xmrinv
      if ( tke > zero ) then
        Pk = abs( prodk * xmr / rho )
        Pd = ce1_ke * f1 * prodk * (cmu_ke * tke / rmut ) * xmr
!       Pd = ce1_ke * f1 * prodk * varepsilon * xmr / ( rho * tke )
        Dd = ce2_ke * f2 * f4 * varepsilon * ( varepsilon / tke ) * xmrinv
      else
        Pk = zero
        Pd = zero
        Dd = zero
      endif
      xsiw = zero
      xsid = zero
!     write(6,'(a,10(1x,es12.5))')                          &
!     'source    ',  tke, varepsilon, rmut, prodk,vort,pk, dk, pd, dd


    case ( 'jl')

!     epslnt  = varepsilon - mu_lam / rho * DEL2K(I) * xmach / re
      re_t    = rho * tke * tke / (varepsilon * mu_lam+rsmall)*re/xmach
      f1      = one
      f2      = one
      if ( re_t > zero ) then
        f2      = 1.0_dp - 0.30_dp * exp ( -re_t * re_t )
      endif
      if ( limit_f2 ) then
        f2 = min(one, max(f2, f2_threshold) )
      endif

      etilde  = varepsilon ! epslnt
      etilde  = max (etilde, zero)
! Limit production term for robustness:
      prodk      = min (prodk_full, (prodk_limiter * rho * varepsilon) )
      prode      = prode_limiter * prodk + (1.0_dp-prode_limiter)* prodk_full
! eterm - epsilon eqn.
      xsiw       = 2.0_dp * mu_lam * rmut * DEL2USQ / rho * ( xmach / re )**2
! dterm
      xsid       = - mu_lam / rho * DEL2K * xmach / re

      Pk = abs( prodk / rho ) * xmr
      Dk = rho * (varepsilon + dissp_c) * xmrinv
      if ( tke > zero ) then
        Pd = ce1_ke * f1 * prode * ( varepsilon / tke ) * xmr
        Dd = ce2_ke * f2 * f4 * rho * etilde * ( varepsilon / tke ) * xmrinv
      else
        Pd = zero
        Dd = zero
      endif

    case ( 'ls')

!     epslnt  = varepsilon - mu_lam / rho * DEL2K * xmach / re
!            varepsilon  = AMAX1 ( varepsilon , 1.0E-8 )
      re_t    = rho * tke * tke / (varepsilon * mu_lam)*re/xmach
      f1      = one
      f2      = 1.E0_dp - 0.3E0_dp * exp ( -re_t * re_t )
      etilde  = varepsilon ! EPSLNT
      etilde  = max (etilde, zero)
      prodk   = min (prodk, (prodk_limiter * rho * varepsilon) )
      prode      = prode_limiter * prodk + (1.0_dp-prode_limiter)* prodk_full

! ETERM, eps. eqn.
      xsiw    = 2.0E0 * mu_lam * rmut * DEL2USQ / rho * ( xmach / re )**2
      xsid    = - mu_lam / rho * DEL2K * xmach / re

      case default

        f1   = zero
        f2   = zero

    end select

if (write_profile) then
if ( (x > x_lower) .and. (x < x_upper) ) then
  sunit = 205
  if ( dist == 0.0 ) then
   open(sunit,file='profile.dat',form='formatted', status='unknown')

    rhow = rho
    friction_velocity = sqrt(mu_lam*dudz/rhow*xmach/re)
!   write(6,'(a,20(1x,es12.5))') 'rhow             =',rhow
!   write(6,'(a,20(1x,es12.5))') 'rmu              =',mu_lam
!   write(6,'(a,20(1x,es12.5))') 'dudz             =',dudz
!   write(6,'(a,20(1x,es12.5))') 'xmach/re         =',xmach/re
!   write(6,'(a,20(1x,es12.5))') 'friction_velocity=',friction_velocity
    write(sunit,'(a)') 'title="near-wall velocity profiles"'
    write(sunit,'(6a)') 'variables="dist","y+","u+","k+","<greek>e</greek>+"'
    write(sunit,'(6a)') 'log10(yplus)","<greek>m</greek>t","f<sub>2</sub>"'
    write(sunit,'(6a)') '"Pk","Dk","Pe","De","Prod/<greek>e</greek>"'
    write(sunit,'(a)') 'zone'

  else
     uplus = u/friction_velocity
     yplus = dist*rhow/mu_lam*friction_velocity*re/xmach
     knorm = (friction_velocity*friction_velocity)
     enorm = (friction_velocity**4) * (mu_lam*re/xmach)
     kplus = tke /knorm
     eplus = varepsilon /enorm

 write(sunit,'(20(1x,es12.5))')                         &
 dist, yplus, uplus, kplus, eplus, log(yplus), rmut, f2 &
  , pk/enorm, dk/enorm                                  &
  , pd*knorm/(enorm*enorm), dd*knorm/(enorm*enorm)      &
  , prodk/varepsilon
endif
  if ( dist == 1.0 ) close(sunit)
endif
endif
!       Limit on k-production term

        Pk = min(Pk,20.0_dp*Dk)

!       test for laminar node
      if (turb_transition) then
        if (iflagslen < 0) then
          Pk=zero
          Pd=zero
        end if
      end if
!     set laminar flow outside distance = slen_kdes
!     this mimicks the suggestion by Khorami ala CFL3D
!     default for slen_kdes is set to huge - should not affect standard cases
      if( dist    > slen_kdes) then
        Pk=zero
        Pd=zero
      end if
!       Ambient terms - Spalart and Rumsey, AIAA J. Vol.45 No.10
      if (keepambient) then
!FIXME
        Dk = Dk - ke_e_inf*xmrinv
        Dd = Dd - ke_k_inf*ke_k_inf*xmrinv/(ke_mut_inf*ke_mut_inf)
      end if

      source_ke(1) = vol*(Pk - Dk + xsid)
      source_ke(2) = vol*(Pd - Dd + xsiw)

!if ( i > 69 .and. i < 139 ) then
!write(6,'(a,i8,20(1x,es12.5))') &
! 'ke_source', i, dist, rmut, tke, varepsilon  &
!   ,  prodk, prodk_full  &
!   , pk, dk, pd, dd
!endif

! if ( tke == 0.0 ) then
! write(*,'(i10,12(1x,es12.5))') &
!    i, slen(i), tke, dissp, rmut, vort, prodk, pk, dk
!    i, dist,  tke, dissp, rmut, vort, prodk, pk, dk, pd, dd
! endif

      if (test_freestream) then
        source_ke(1) = 0._dp
        source_ke(2) = 0._dp
      end if

  end function ke_source

!===================================== RESID_MIX_DIFF ========================80
!
! Diffusion residual for mixed element formulation.
!
! Calculates the residual for Wilcox-ke model on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine resid_mix_diff(eqn_set, viscous_method, nnodes0, nnodes01,        &
                            nedgeloc, eptr, turb, qnode, res, gradx, grady,    &
                            gradz, xn, yn, zn, ra, ielem, ncell, c2n, c2e,     &
                            x, y, z, local_f2n, local_e2n, local_f2e,          &
                            e2n_2d, nedgeloc_2d, face_per_cell,                &
                            node_per_cell, edge_per_cell, type_cell,           &
                            n_turb, n_tot, n_grd, face_2d, slen )

    use info_depr,      only : tref, xmach, re, twod, skeleton,                &
                               grad_x_y_z_contents, use_edge_gradients
    use debug_defs,     only : gradient_construction_rhs
    use solution_types, only : compressible, incompressible
    use turb_ke_const,  only : sig_k_ke, sig_e_ke, ke_version
    use fluid,          only : gamma, sutherland_constant
    use lmpi,           only : lmpi_master
    use utilities,      only : tangents

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_grd
    integer, intent(in) :: ncell
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: face_2d

    integer,  dimension(2,nedgeloc),          intent(in) :: eptr
    integer,  dimension(node_per_cell,ncell), intent(in) :: c2n
    integer,  dimension(edge_per_cell,ncell), intent(in) :: c2e
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer,  dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer,  dimension(4,2),                 intent(in) :: e2n_2d

    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),        intent(in)    :: ra
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp), dimension(nnodes01),          intent(in)    :: slen

    character(len=3), intent(in) :: type_cell

    integer :: n, nedge_flux_eval
    integer :: node1, node2
    integer :: nn

    real(dp) :: coeff1, coeff2
    real(dp) :: xmr, phi1, phi2
    real(dp) :: mu_node1, mu_node2, mut_node1, mut_node2
    real(dp) :: cstar
    real(dp) :: phi
    real(dp) :: rnu1, rnu2
    real(dp) :: rho1, p1, temp1, rho2, p2, temp2
    real(dp) :: lx, ly, lz, mx, my, mz, deti
    real(dp) :: my_xmach
    real(dp) :: txavg, tyavg, tzavg, gradt_xi
    real(dp) :: ex, ey, ez, disi
    real(dp) :: tx, ty, tz, egradt
    real(dp) :: lgradt, mgradt, rho1inv, rho2inv
    real(dp) :: dist1, dist2

    real(dp), parameter :: half     = 0.5_dp
    real(dp), parameter :: my_1     = 1.0_dp

    real(dp), dimension(n_turb) :: coe1
    real(dp), dimension(3,3)    :: b
    real(dp), dimension(n_turb) :: ngradt

  continue

    tx = 0._dp ; ty = 0._dp ; tz = 0._dp

!   When using edge-based diffusion terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'ke_resid_mix: only for in/compress pg')
    end select

    xmr  = my_xmach / re

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    endif

!   Diffusion terms:

    diffusion_terms : if ( viscous_method > 0 ) then

!     Check method for computing gradx,... (diffusion + source terms)

      if(skeleton > 0) then
        write(*,*) ' Edge-based residuals of decoupled turbulent diffusion.'
        write(*,*) ' Using weighted least squares for average gradient.'
        write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
      endif
      if(trim(grad_x_y_z_contents) /= 'viscous weighted-least-squares') then
        if(lmpi_master) then
          write(*,*) ' Failure in edge-based turb residuals...stopping.'
          write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
        endif
        call lmpi_conditional_stop(1)
      endif

      select case ( ke_version )
      case ( 'abid-ke' )
        coe1(1) = 1.0/sig_k_ke
        coe1(2) = 1.0/sig_e_ke
      case ( 'jl' )
        coe1(1) = 1.0/sig_k_ke
        coe1(2) = 1.0/sig_e_ke
      case ( 'ls' )
        coe1(1) = 1.0/sig_k_ke
        coe1(2) = 1.0/sig_e_ke
      case ( 'hr' )
        coe1(1) = 1.0/sig_k_ke
        coe1(2) = 1.0/sig_e_ke
      end select

      mixed_edge_loop: do n = 1, nedge_flux_eval

!       loop over all the faces and calculate the turbulent diffusion terms
!       (in the 2D case, this loop contains edges on only one y=constant plane)
!       turb(nn,node) contains the turbulence values
!       gradx(n_grd) contains gradx of turb, ...
!
!       Break into two contributions - directional and the average gradient -
!       and enforce M-property on only the directional piece

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        if ( eqn_set == compressible ) then
          rho1   = qnode(1,node1)
          rho1inv = my_1/rho1
          p1     = qnode(5,node1)
          temp1  = gamma*p1*rho1inv
          rnu1   = viscosity_law( cstar, temp1 ) * rho1inv

          rho2   = qnode(1,node2)
          rho2inv = my_1/rho2
          p2     = qnode(5,node2)
          temp2  = gamma*p2*rho2inv
          rnu2   = viscosity_law( cstar, temp2 ) * rho2inv
        else
          rho1   = my_1
          rho2   = my_1
          rnu1   = my_1
          rnu2   = my_1
        end if


        turbvariable_loop1 : do nn = 1, n_turb
          coeff1    = coe1(nn)
          coeff2    = coe1(nn)
          mu_node1  = rnu1*rho1
          mu_node2  = rnu2*rho2
          dist1     = slen(node1)
          dist2     = slen(node2)
          mut_node1 = mut_ke0 ( rho1, p1, turb(1,node1), turb(2,node1), dist1)
          mut_node2 = mut_ke0 ( rho2, p2, turb(1,node2), turb(2,node2), dist2)

          phi1   = mu_node1 + coeff1*mut_node1
          phi2   = mu_node2 + coeff2*mut_node2

          phi = half *(phi1 + phi2)

!         average node-based gradients : gradx, grady, gradz

          txavg = half *( gradx(n_grd-n_turb+nn,node1) +                       &
                          gradx(n_grd-n_turb+nn,node2) )
          tyavg = half *( grady(n_grd-n_turb+nn,node1) +                       &
                          grady(n_grd-n_turb+nn,node2) )
          tzavg = half *( gradz(n_grd-n_turb+nn,node1) +                       &
                          gradz(n_grd-n_turb+nn,node2) )

!         ex, ey, ez is unit vector along edge direction

          ex   = x(node2) - x(node1)
          ey   = y(node2) - y(node1)
          ez   = z(node2) - z(node1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          if( viscous_method == 2 ) then

!         directional gradients along edge

          egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

          tx = egradt*ex
          ty = egradt*ey
          tz = egradt*ez

          elseif( gradient_construction_rhs == 0 ) then

!         directional gradients along edge

          egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

!         average gradient in edge direction

          gradt_xi = txavg*ex + tyavg*ey + tzavg*ez

!         resolve gradient contributions from edge and nodes
!         u, v, w, and speed-of-sound-squared (i.e., perfect gas temperature)

          tx = txavg + ( egradt - gradt_xi )*ex
          ty = tyavg + ( egradt - gradt_xi )*ey
          tz = tzavg + ( egradt - gradt_xi )*ez

          elseif( gradient_construction_rhs == 1 ) then

          !...find tangent vectors in the dual face
          call tangents(xn(n), yn(n), zn(n), lx, ly, lz, mx, my, mz)

          !...find inverse elements of transformation matrix
          deti =  my_1/ ( ex*( ly*mz - lz*my ) &
                        + ey*( lz*mx - lx*mz ) &
                        + ez*( lx*my - ly*mx ) )

          b(1,1) =  deti*( ly*mz - lz*my )
          b(1,2) = -deti*( ey*mz - ez*my )
          b(1,3) =  deti*( ey*lz - ez*ly )

          b(2,1) = -deti*( lx*mz - lz*mx )
          b(2,2) =  deti*( ex*mz - ez*mx )
          b(2,3) = -deti*( ex*lz - ez*lx )

          b(3,1) =  deti*( lx*my - ly*mx )
          b(3,2) = -deti*( ex*my - ey*mx )
          b(3,3) =  deti*( ex*ly - ey*lx )

!         directional gradients

          egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

          lgradt = txavg*lx + tyavg*ly + tzavg*lz

          mgradt = txavg*mx + tyavg*my + tzavg*mz

!         resolve gradient contributions from edge and dual face

          tx = b(1,1)*egradt + b(1,2)*lgradt + b(1,3)*mgradt
          ty = b(2,1)*egradt + b(2,2)*lgradt + b(2,3)*mgradt
          tz = b(3,1)*egradt + b(3,2)*lgradt + b(3,3)*mgradt

          endif

!         turbulent diffusion contribution at dual face [ two terms ]

!         [area]*[nondimensionalization factor : Mach / Re / sigma ]*
!         [normal gradient] at dual face

          ngradt(nn) = xmr*ra(n)*( tx*xn(n) + ty*yn(n) + tz*zn(n) )

          if ( node1 <= nnodes0 ) then
            res(nn,node1) = res(nn,node1) - ( phi*ngradt(nn) )
          end if

          if ( node2 <= nnodes0 ) then
            res(nn,node2) = res(nn,node2) + ( phi*ngradt(nn) )
          end if

        end do turbvariable_loop1
      end do mixed_edge_loop

    else diffusion_terms

      if ( twod .or. (.not.use_edge_gradients) ) then
        call resid_mix_diff_cell(eqn_set, viscous_method, nnodes0, nnodes01,   &
                                 nedgeloc, turb, qnode, res, ielem, ncell, c2n,&
                                 c2e, x, y, z, local_f2n, local_e2n, local_f2e,&
                                 e2n_2d, nedgeloc_2d, face_per_cell,           &
                                 node_per_cell, edge_per_cell, type_cell,      &
                                 n_turb, n_tot, face_2d, slen)
      else
        call resid_mix_diff_cell_opt(eqn_set, viscous_method, nnodes0,         &
                                     nnodes01, turb, qnode, res, ielem, ncell, &
                                     c2n, x, y, z, local_f2n, local_e2n,       &
                                     face_per_cell, node_per_cell,             &
                                     edge_per_cell, type_cell, n_turb, n_tot,  &
                                     slen )
      endif

    end if diffusion_terms

  end subroutine resid_mix_diff


!===================================== RESID_MIX_DIFF_CELL ===================80
!
! Diffusion residual for mixed element formulation.
!
! Calculates the residual for Wilcox-ke model on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine resid_mix_diff_cell(eqn_set, viscous_method, nnodes0, nnodes01,   &
                                 nedgeloc, turb, qnode, res, ielem, ncell, c2n,&
                                 c2e, x, y, z, local_f2n, local_e2n, local_f2e,&
                                 e2n_2d, nedgeloc_2d, face_per_cell,           &
                                 node_per_cell, edge_per_cell, type_cell,      &
                                 n_turb, n_tot, face_2d, slen )

    use info_depr,      only : tref, xmach, re, twod, skeleton,                &
                               grad_x_y_z_contents, use_edge_gradients
    use debug_defs,     only : gradient_construction_rhs
    use solution_types, only : compressible, incompressible
    use turb_ke_const,  only : sig_k_ke, sig_e_ke, ke_version
    use fluid,          only : gamma, sutherland_constant
    use element_defs,   only : max_node_per_cell, max_face_per_cell,           &
                               max_edge_per_cell
    use lmpi,           only : lmpi_master
    use utilities,      only : tangents, tinverse, cell_gradients

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: ncell
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: face_2d

    integer,  dimension(node_per_cell,ncell), intent(in) :: c2n
    integer,  dimension(edge_per_cell,ncell), intent(in) :: c2e
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer,  dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer,  dimension(4,2),                 intent(in) :: e2n_2d

    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp), dimension(nnodes01),        intent(in)    :: slen

    character(len=3), intent(in) :: type_cell

    integer :: n, nedge_flux_eval
    integer :: ie, i, ie_local, i_local
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, edge, node
    integer :: n1, n2, n3, n4, n5, n6, nn

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: phi_k, phi_w, coeff_k, coeff_d
    real(dp) :: xmr, mu_node, mut_node
    real(dp) :: cstar
    real(dp) :: rho, rhoinv, p
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: areai, xnf, ynf, znf, lx, ly, lz, mx, my, mz
    real(dp) :: my_xmach
    real(dp) :: gradt_xi
    real(dp) :: ex, ey, ez, disi
    real(dp) :: egradt
    real(dp) :: lgradt, mgradt

    real(dp), parameter :: half     = 0.5_dp
    real(dp), parameter :: my_1     = 1.0_dp
    real(dp), parameter :: my_3rd   = 1.0_dp/3.0_dp
    real(dp), parameter :: my_4th   = 1.0_dp/4.0_dp

    real(dp), dimension(3,3)                      :: b, t
    real(dp), dimension(max_face_per_cell)        :: nx, ny, nz
    real(dp)                                      :: nu_node, t_node
    real(dp), dimension(max_node_per_cell)        :: x_node, y_node, z_node
    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(n_turb)                   :: gradx_cell, grady_cell
    real(dp), dimension(n_turb)                   :: gradz_cell
    real(dp), dimension(n_turb)                   :: trbrex, trbrey, trbrez
    real(dp), dimension(n_turb)                   :: trbrexavg, trbreyavg
    real(dp), dimension(n_turb)                   :: trbrezavg, ngradt

    logical :: edge_gradients

  continue

!   When using edge-based diffusion terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp
    nodes_local = 0

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'ke_resid_mix: only for in/compress pg')
    end select

    xmr  = my_xmach / re

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    edge_map = 0
    node_map = 0

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    endif

    if ( twod .and. (viscous_method == 0) ) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    elseif( viscous_method == 0 ) then

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

!     Check method for computing gradx,... (diffusion + source terms)

      if(skeleton > 0) then
        write(*,*) ' Cell-based residuals of decoupled turbulent diffusion.'
        write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
      endif
      if(skeleton > 0) then
        write(*,*) ' Using Green-Gauss (turbgrad) for source term gradients.'
      endif
      if(trim(grad_x_y_z_contents) /= 'turbgrad') then
        if(lmpi_master) then
          write(*,*) ' Failure in cell-based ke residuals...stopping.'
          write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
          write(*,*) ' ........should be via ','turbgrad'
        endif
        call lmpi_conditional_stop(1)
      endif

      diffusion_term_cell : do n = 1, ncell

        phi_k    = 0._dp
        phi_w    = 0._dp

        xc = 0._dp
        yc = 0._dp
        zc = 0._dp

!       compute cell averages and set up some local solution arrays

        coeff_k  = 0.0_dp
        coeff_d  = 0.0_dp
        select case ( ke_version )
        case ( 'abid-ke' )
          coeff_k  = (1.0/sig_k_ke)
          coeff_d  = (1.0/sig_e_ke)
        case ( 'jl' )
          coeff_k  = (1.0/sig_k_ke)
          coeff_d  = (1.0/sig_e_ke)
        case ( 'ls' )
          coeff_k  = (1.0/sig_k_ke)
          coeff_d  = (1.0/sig_e_ke)
        case ( 'hr' )
          coeff_k  = (1.0/sig_k_ke)
          coeff_d  = (1.0/sig_e_ke)
        end select

        node_loop : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

!         global node number

          node = c2n(i,n)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          do nn=1,n_turb
            trbre_node(nn,i) = turb(nn,node)
          end do

          if ( eqn_set == compressible ) then
            rho     = qnode(1,node)
            p       = qnode(5,node)
            rhoinv  = my_1/rho
            t_node  = gamma*p*rhoinv
            nu_node = viscosity_law( cstar, t_node ) * rhoinv
          else
            rho     = my_1
            p       = my_1
            nu_node = my_1
          end if
          mu_node  = rho*nu_node
!         mut_node = amut(node)
          mut_node = mut_ke0 ( rho, p, turb(1,node), turb(2,node), slen(node))

          phi_k = phi_k + (mu_node + coeff_k*mut_node)
          phi_w = phi_w + (mu_node + coeff_d*mut_node)

        end do node_loop

!       get cell averages by dividing by the number of nodes that contributed

        fact = 1._dp / real(nodes_local, dp)

        phi_k    = phi_k*fact
        phi_w    = phi_w*fact

!       compute the cell center (must loop over node_per_cell even in 2D)

        do i = 1, node_per_cell

!         global node number

          node = c2n(i,n)

          xc  =  xc + x(node)
          yc  =  yc + y(node)
          zc  =  zc + z(node)

        end do

        fact = 1._dp / real(node_per_cell, dp)

        xc  =  xc*fact
        yc  =  yc*fact
        zc  =  zc*fact

!       get the gradients in the primal cell via Green-Gauss

        call cell_gradients(edges_local, max_node_per_cell,face_per_cell,      &
                            x_node, y_node, z_node, n_turb, trbre_node,        &
                            local_f2n, e2n_2d, gradx_cell, grady_cell,         &
                            gradz_cell, cell_vol, nx, ny, nz)

        do nn = 1, n_turb
          trbrexavg(nn) = gradx_cell(nn)
          trbreyavg(nn) = grady_cell(nn)
          trbrezavg(nn) = gradz_cell(nn)
        end do

!       next loop over the edges in the cell and get each ones
!       contribution to the residual

        edge_loop : do ie_local = 1,edges_local

!         local edge number

          ie = edge_map(ie_local)

!         global edge number

          edge = c2e(ie,n)

!         check edge to make sure it is not off-processor - if it is
!         we don't need its contribution

          if (edge > nedge_flux_eval) cycle edge_loop

!         local node numbers of edge endpoints

          n1_loc = local_e2n(ie,1)
          n2_loc = local_e2n(ie,2)

!         global node numbers of edge endpoints

          n1 = c2n(n1_loc,n)
          n2 = c2n(n2_loc,n)

!         edge midpoint

          xm = (x(n1) + x(n2))*half
          ym = (y(n1) + y(n2))*half
          zm = (z(n1) + z(n2))*half

!         compute left face centroid

          n3 = c2n(local_e2n(ie,3),n)

          if (local_e2n(ie,4) /= 0) then

!           quad cell face

            n4 = c2n(local_e2n(ie,4),n)

            xl = (x(n1) + x(n2) + x(n3) + x(n4))*my_4th
            yl = (y(n1) + y(n2) + y(n3) + y(n4))*my_4th
            zl = (z(n1) + z(n2) + z(n3) + z(n4))*my_4th

          else

!           tria cell face

            xl = (x(n1) + x(n2) + x(n3))*my_3rd
            yl = (y(n1) + y(n2) + y(n3))*my_3rd
            zl = (z(n1) + z(n2) + z(n3))*my_3rd

          end if

!         compute right face centroid

          n5 = c2n(local_e2n(ie,5),n)

          if (local_e2n(ie,6) /= 0) then

!           quad cell face

            n6 = c2n(local_e2n(ie,6),n)

            xr = (x(n1) + x(n2) + x(n5) + x(n6))*my_4th
            yr = (y(n1) + y(n2) + y(n5) + y(n6))*my_4th
            zr = (z(n1) + z(n2) + z(n5) + z(n6))*my_4th

          else

!           tria cell face

            xr = (x(n1) + x(n2) + x(n5))*my_3rd
            yr = (y(n1) + y(n2) + y(n5))*my_3rd
            zr = (z(n1) + z(n2) + z(n5))*my_3rd

          end if

!         get the contributions to dual normals from the two triangles
!         that form part of the dual-cell surface

!         area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

          areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*half
          areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*half
          areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*half

!         get gradients at the dual face; either take gradients for this
!         piece of the dual face to be the same as the cell-average gradient
!         computed above  (which is what the legacy FUN3D solver does for tets),
!         or combine with the edge-gradient to increase h-ellipticity on
!         non-simplicial meshes.

!         for tets in 3D or prisms in 2D, edge gradients add no new info
!         so there is no need to do the extra work

          edge_gradients = use_edge_gradients

          if (type_cell == 'tet') edge_gradients = .false.
          if (twod .and. type_cell == 'prz') edge_gradients = .false.

          include_edge_gradients : if (edge_gradients) then

          do nn = 1, n_turb
!           ex, ey, ez is unit vector along edge direction

            ex   = x(n2) - x(n1)
            ey   = y(n2) - y(n1)
            ez   = z(n2) - z(n1)
            disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

            ex   = ex*disi
            ey   = ey*disi
            ez   = ez*disi

            if (  gradient_construction_rhs == 0 ) then

!             directional gradients along edge

              egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

!             average Green-Gauss gradient in edge direction

              gradt_xi = trbrexavg(nn)*ex + trbreyavg(nn)*ey + trbrezavg(nn)*ez

!             combine gradient contributions from edge and primal cell

              trbrex(nn) = trbrexavg(nn) + ( egradt - gradt_xi )*ex
              trbrey(nn) = trbreyavg(nn) + ( egradt - gradt_xi )*ey
              trbrez(nn) = trbrezavg(nn) + ( egradt - gradt_xi )*ez

            elseif( gradient_construction_rhs == 1 ) then

!             find tangent vectors in the dual face

              areai = my_1/sqrt( areax**2 + areay**2 + areaz**2 )
              xnf = areax*areai
              ynf = areay*areai
              znf = areaz*areai
              call tangents(xnf, ynf, znf, lx, ly, lz, mx, my, mz)

!             form transformation matrix

              !...edge direction
              b(1,1) = ex
              b(1,2) = ey
              b(1,3) = ez

              !...l direction
              b(2,1) = lx
              b(2,2) = ly
              b(2,3) = lz

              !...m direction
              b(3,1) = mx
              b(3,2) = my
              b(3,3) = mz

              call tinverse( b , t )

!             directional gradients

              egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

              lgradt = trbrexavg(nn)*b(2,1) + trbreyavg(nn)*b(2,2)             &
                     + trbrezavg(nn)*b(2,3)

              mgradt = trbrexavg(nn)*b(3,1) + trbreyavg(nn)*b(3,2)             &
                     + trbrezavg(nn)*b(3,3)

!             resolve gradient contributions from edge and dual face

              trbrex(nn) = t(1,1)*egradt + t(1,2)*lgradt + t(1,3)*mgradt
              trbrey(nn) = t(2,1)*egradt + t(2,2)*lgradt + t(2,3)*mgradt
              trbrez(nn) = t(3,1)*egradt + t(3,2)*lgradt + t(3,3)*mgradt

            end if
          end do

          else include_edge_gradients

!           just use Green-Gauss cell-average gradients (this
!           is what the baseline code does for tets)

            do nn = 1, n_turb
              trbrex(nn) = trbrexavg(nn)
              trbrey(nn) = trbreyavg(nn)
              trbrez(nn) = trbrezavg(nn)
            end do

          end if include_edge_gradients

!         turbulent diffusion contribution at dual face

!         [nondimensionalization factor : Mach / Re ]*
!         [normal gradient * area] at dual face

          ngradt(1) = xmr*(trbrex(1)*areax + trbrey(1)*areay + trbrez(1)*areaz)
          ngradt(2) = xmr*(trbrex(2)*areax + trbrey(2)*areay + trbrez(2)*areaz)

          if ( n1 <= nnodes0 ) then
            res(1,n1) = res(1,n1) - ( phi_k*ngradt(1)/qnode(1,n1) )
            res(2,n1) = res(2,n1) - ( phi_w*ngradt(2)/qnode(1,n1) )
          end if

          if ( n2 <= nnodes0 ) then
            res(1,n2) = res(1,n2) + ( phi_k*ngradt(1)/qnode(1,n2) )
            res(2,n2) = res(2,n2) + ( phi_w*ngradt(2)/qnode(1,n2) )
          end if

        end do edge_loop

      end do diffusion_term_cell

  end subroutine resid_mix_diff_cell


!================================= RESID_MIX_DIFF_CELL_OPT ===============80
!
! Diffusion residual for mixed element formulation.
!
! Calculates the residual for Wilcox-ke model on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine resid_mix_diff_cell_opt(eqn_set, viscous_method, nnodes0,         &
                                     nnodes01, turb, qnode, res, ielem, ncell, &
                                     c2n, x, y, z, local_f2n, local_e2n,       &
                                     face_per_cell, node_per_cell,             &
                                     edge_per_cell, type_cell, n_turb, n_tot,  &
                                     slen )

    use info_depr,      only : tref, xmach, re, skeleton,                      &
                               grad_x_y_z_contents, use_edge_gradients
    use solution_types, only : compressible, incompressible
    use turb_ke_const,  only : sig_k_ke, sig_e_ke, ke_version
    use fluid,          only : gamma, sutherland_constant
    use element_defs,   only : max_node_per_cell
    use lmpi,           only : lmpi_master

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01

    integer,  dimension(node_per_cell,ncell), intent(in) :: c2n
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer,  dimension(edge_per_cell,6),     intent(in) :: local_e2n

    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp), dimension(nnodes01),        intent(in)    :: slen

    character(len=3), intent(in) :: type_cell

    integer :: n, iface
    integer :: ie, i
    integer :: n1_loc, n2_loc, node, n3_loc, n4_loc, n5_loc, n6_loc
    integer :: n1, n2, nn, nn1, nn2, nn3, nn4

    real(dp) :: phi_k, phi_w, coeff_k, coeff_d, xavg, yavg, zavg
    real(dp) :: xmr, mu_node, mut_node, xavg1, yavg1, zavg1, xavg2, yavg2, zavg2
    real(dp) :: cstar, nx1, ny1, nz1, nx2, ny2, nz2, nx, ny, nz
    real(dp) :: rho, termx, termy, termz, term1, term2
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm, termx1, termy1, termz1
    real(dp) :: areax, areay, areaz, termx2, termy2, termz2
    real(dp) :: xc, yc, zc, cell_vol, fact, qavg, qavg1, qavg2
    real(dp) :: my_xmach, cell_vol_inv
    real(dp) :: gradt_xi
    real(dp) :: ex, ey, ez, disi
    real(dp) :: egradt
    real(dp) :: p

    real(dp), parameter :: zero     = 0.0_dp
    real(dp), parameter :: half     = 0.5_dp
    real(dp), parameter :: my_1     = 1.0_dp
    real(dp), parameter :: my_3rd   = 1.0_dp/3.0_dp
    real(dp), parameter :: my_4th   = 1.0_dp/4.0_dp
    real(dp), parameter :: my_6th   = 1.0_dp/6.0_dp
    real(dp), parameter :: my_18th  = 1.0_dp/18.0_dp

    real(dp), dimension(max_node_per_cell)        :: rhoinv_node
    real(dp), dimension(max_node_per_cell)        :: nu_node, t_node
    real(dp), dimension(max_node_per_cell)        :: x_node, y_node, z_node
    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(n_turb)                   :: trbrex, trbrey, trbrez
    real(dp), dimension(n_turb)                   :: trbrexavg, trbreyavg
    real(dp), dimension(n_turb)                   :: trbrezavg, ngradt

    logical :: edge_gradients

  continue

!   When using edge-based diffusion terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'ket_resid_mix: only in/compress pg')
    end select

    xmr  = my_xmach / re

!     Check method for computing gradx,... (diffusion + source terms)

      if(skeleton > 0) then
        write(*,*) ' Cell-based residuals of decoupled turbulent diffusion.'
        write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
      endif
      if(skeleton > 0) then
        write(*,*) ' Using Green-Gauss (turbgrad) for source term gradients.'
      endif
      if(trim(grad_x_y_z_contents) /= 'turbgrad') then
        if(lmpi_master) then
          write(*,*) ' Failure in cell-based ke residuals...stopping.'
          write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
          write(*,*) ' ........should be via ','turbgrad'
        endif
        call lmpi_conditional_stop(1)
      endif

      fact = 1._dp / real(node_per_cell, dp)

      diffusion_term_cell : do n = 1, ncell

        phi_k    = 0._dp
        phi_w    = 0._dp

        xc = 0._dp
        yc = 0._dp
        zc = 0._dp

!       compute cell averages and set up some local solution arrays

        coeff_k  = 0.0_dp
        coeff_d  = 0.0_dp
        select case ( ke_version )
        case ( 'abid-ke' )
          coeff_k  = (1.0/sig_k_ke)
          coeff_d  = (1.0/sig_e_ke)
        case ( 'jl' )
          coeff_k  = (1.0/sig_k_ke)
          coeff_d  = (1.0/sig_e_ke)
        case ( 'ls' )
          coeff_k  = (1.0/sig_k_ke)
          coeff_d  = (1.0/sig_e_ke)
        case ( '2006' )
          coeff_k  = (1.0/sig_k_ke)
          coeff_d  = (1.0/sig_e_ke)
        end select

        node_loop : do i = 1, node_per_cell

          node = c2n(i,n)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          xc  =  xc + x_node(i)
          yc  =  yc + y_node(i)
          zc  =  zc + z_node(i)

          do nn=1,n_turb
            trbre_node(nn,i) = turb(nn,node)
          end do

          if ( eqn_set == compressible ) then
            rho            = qnode(1,node)
            p              = qnode(5,node)
            rhoinv_node(i) = my_1/rho
            t_node(i)      = gamma*qnode(5,node)*rhoinv_node(i)
            nu_node(i)     = viscosity_law( cstar, t_node(i) ) * rhoinv_node(i)
          else
            rho            = my_1
            p              = my_1
            rhoinv_node(i) = my_1
            nu_node(i)     = my_1
          end if


          mu_node  = rho*nu_node(i)
!         mut_node = amut(node)
          mut_node = mut_ke0 ( rho, p, turb(1,node), turb(2,node), slen(node))

          phi_k = phi_k + (mu_node + coeff_k*mut_node)
          phi_w = phi_w + (mu_node + coeff_d*mut_node)

        end do node_loop

        phi_k = phi_k*fact
        phi_w = phi_w*fact

        xc  =  xc*fact
        yc  =  yc*fact
        zc  =  zc*fact

!     get the gradients in the primal cell via Green-Gauss

      trbrexavg = 0.0_dp
      trbreyavg = 0.0_dp
      trbrezavg = 0.0_dp

      cell_vol = zero

      threed_faces : do iface = 1, face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!       triangular faces of the cell

!       face normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))         &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))         &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))         &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

!       gradient contributions

          do nn = 1, n_turb
            qavg = trbre_node(nn,nn1) + trbre_node(nn,nn2) + trbre_node(nn,nn3)

            trbrexavg(nn) = trbrexavg(nn) + termx*qavg
            trbreyavg(nn) = trbreyavg(nn) + termy*qavg
            trbrezavg(nn) = trbrezavg(nn) + termz*qavg
          end do

        else

!       quadrilateral faces of the cell

!       break face up into triangles 1-2-3 and 1-3-4 and add together

!       triangle 1: 1-2-3

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       triangle 1 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))        &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))        &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))        &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!       triangle 2: 1-3-4

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)

!       triangle 2 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))        &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))
          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))        &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))
          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))        &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!       cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th

!       gradient contributions

          termx1 = nx1*my_6th
          termy1 = ny1*my_6th
          termz1 = nz1*my_6th

          termx2 = nx2*my_6th
          termy2 = ny2*my_6th
          termz2 = nz2*my_6th

          do nn = 1, n_turb
            qavg1 = trbre_node(nn,nn1) + trbre_node(nn,nn2) + trbre_node(nn,nn3)
            qavg2 = trbre_node(nn,nn1) + trbre_node(nn,nn3) + trbre_node(nn,nn4)

            trbrexavg(nn) = trbrexavg(nn) + termx1*qavg1 + termx2*qavg2
            trbreyavg(nn) = trbreyavg(nn) + termy1*qavg1 + termy2*qavg2
            trbrezavg(nn) = trbrezavg(nn) + termz1*qavg1 + termz2*qavg2
          end do

        end if

      end do threed_faces

!   need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

      cell_vol_inv = my_1/cell_vol

      trbrexavg(:) = trbrexavg(:) * cell_vol_inv
      trbreyavg(:) = trbreyavg(:) * cell_vol_inv
      trbrezavg(:) = trbrezavg(:) * cell_vol_inv

!       next loop over the edges in the cell and get each ones
!       contribution to the residual

        edge_loop : do ie = 1, edge_per_cell

          n1_loc = local_e2n(ie,1)
          n2_loc = local_e2n(ie,2)
          n3_loc = local_e2n(ie,3)
          n4_loc = local_e2n(ie,4)
          n5_loc = local_e2n(ie,5)
          n6_loc = local_e2n(ie,6)

!         global node numbers of edge endpoints

          n1 = c2n(n1_loc,n)
          n2 = c2n(n2_loc,n)

!         edge midpoint

          xm = (x_node(n1_loc) + x_node(n2_loc))*half
          ym = (y_node(n1_loc) + y_node(n2_loc))*half
          zm = (z_node(n1_loc) + z_node(n2_loc))*half

!         compute left face centroid

          if (n4_loc /= 0) then
            xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc)             &
                + x_node(n4_loc))*my_4th
            yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc)             &
                + y_node(n4_loc))*my_4th
            zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc)             &
                + z_node(n4_loc))*my_4th
          else
            xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc))*my_3rd
            yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc))*my_3rd
            zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc))*my_3rd
          end if

!       compute right face centroid

          if (n6_loc /= 0) then
            xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc)             &
                + x_node(n6_loc))*my_4th
            yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc)             &
                + y_node(n6_loc))*my_4th
            zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc)             &
                + z_node(n6_loc))*my_4th
          else
            xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc))*my_3rd
            yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc))*my_3rd
            zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc))*my_3rd
          end if

!         get the contributions to dual normals from the two triangles
!         that form part of the dual-cell surface

!         area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

          areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*half
          areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*half
          areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*half

!         get gradients at the dual face; either take gradients for this
!         piece of the dual face to be the same as the cell-average gradient
!         computed above  (which is what the legacy FUN3D solver does for tets),
!         or combine with the edge-gradient to increase h-ellipticity on
!         non-simplicial meshes.

!         for tets in 3D or prisms in 2D, edge gradients add no new info
!         so there is no need to do the extra work

          edge_gradients = use_edge_gradients

          if (type_cell == 'tet') edge_gradients = .false.

          include_edge_gradients : if (edge_gradients) then

!           ex, ey, ez is unit vector along edge direction

            ex   = x_node(n2_loc) - x_node(n1_loc)
            ey   = y_node(n2_loc) - y_node(n1_loc)
            ez   = z_node(n2_loc) - z_node(n1_loc)
            disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )
            ex   = ex*disi
            ey   = ey*disi
            ez   = ez*disi

            do nn = 1, n_turb

!             directional gradients along edge

              egradt = ( trbre_node(nn,n2_loc) - trbre_node(nn,n1_loc) )*disi

!             average Green-Gauss gradient in edge direction

              gradt_xi = trbrexavg(nn)*ex + trbreyavg(nn)*ey + trbrezavg(nn)*ez

!             combine gradient contributions from edge and primal cell

              trbrex(nn) = trbrexavg(nn) + ( egradt - gradt_xi )*ex
              trbrey(nn) = trbreyavg(nn) + ( egradt - gradt_xi )*ey
              trbrez(nn) = trbrezavg(nn) + ( egradt - gradt_xi )*ez

            end do

          else include_edge_gradients

            trbrex(:) = trbrexavg(:)
            trbrey(:) = trbreyavg(:)
            trbrez(:) = trbrezavg(:)

          end if include_edge_gradients

!         turbulent diffusion contribution at dual face

!         [nondimensionalization factor : Mach / Re ]*
!         [normal gradient * area] at dual face

          ngradt(1) = xmr*(trbrex(1)*areax + trbrey(1)*areay + trbrez(1)*areaz)
          ngradt(2) = xmr*(trbrex(2)*areax + trbrey(2)*areay + trbrez(2)*areaz)

          if ( n1 <= nnodes0 ) then
            res(1,n1) = res(1,n1) - ( phi_k*ngradt(1)*rhoinv_node(n1_loc) )
            res(2,n1) = res(2,n1) - ( phi_w*ngradt(2)*rhoinv_node(n1_loc) )
          end if

          if ( n2 <= nnodes0 ) then
            res(1,n2) = res(1,n2) + ( phi_k*ngradt(1)*rhoinv_node(n2_loc) )
            res(2,n2) = res(2,n2) + ( phi_w*ngradt(2)*rhoinv_node(n2_loc) )
          end if

        end do edge_loop

      end do diffusion_term_cell

  end subroutine resid_mix_diff_cell_opt

!================================= KW_JACOB ==================================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for Wilcox-ke model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine ke_jacob(eqn_set, viscous_method, nnodes0, nnodes01,              &
                       nedgeloc, max_nnz, eptr, turb, slen, qnode,             &
                       gradx, grady, gradz,                                    &
                       vol, xn, yn, zn, ra, a_diag, a_off,                     &
                       fhelp, nedgeloc_2d, nnodes0_2d, node_pairs_2d,          &
                       x, y, z, nnz01, ia, ja, facespeed, n_turb, n_tot,       &
                       n_grd,                                                  &
                      nzg2m, g2m,       nelem, elem )

    use kinddefs,       only : odp
    use element_types,  only : elem_type
    use solution_types, only : compressible, incompressible

    integer, intent(in) :: eqn_set, viscous_method, nelem
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_grd
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: nnodes0_2d
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: nnz01

    integer, dimension(2,nedgeloc),          intent(in) :: eptr
    integer, dimension(2,nedgeloc),          intent(in) :: fhelp
    integer, dimension(2,nnodes0_2d),        intent(in) :: node_pairs_2d
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(nnz01),               intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m

    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradz
    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z, vol
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),              intent(in)    :: facespeed
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(nnodes01),              intent(in)    :: slen
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    type(elem_type), dimension(nelem), intent(in) :: elem

    integer :: ielem

  continue

!   When using the edge-based terms, we only need to visit this routine
!   one time

!   my_xmach = 0.0_dp

    if ( eqn_set /= compressible .and. eqn_set /= incompressible ) then
      call lmpi_conditional_stop(1,'ke_jacob_mix: only for in/compress pg')
    end if

!   xmr    = my_xmach / re

!   First compute the convective terms

        call ke_jacob_convect(nnodes0, nnodes01,                               &
                              nedgeloc, max_nnz, eptr, qnode,                  &
                              xn, yn, zn, ra, a_diag, a_off,                   &
                              fhelp, nedgeloc_2d, facespeed, n_turb, n_tot,    &
                              g2m )

        call ke_jacob_source(eqn_set, n_tot, nnodes0, nnodes01,                &
                             qnode, turb, slen                                 &
                           , gradx, grady, gradz                               &
                           , vol, a_diag,                                      &
                             nnodes0_2d, node_pairs_2d, n_turb                 &
                           , n_grd                                             &
                           , g2m )

!   Finally pick up diffusion terms on non-tetrahedral element types

    if ( viscous_method == 0 ) then
      do ielem = 1, nelem
        call jacob_mix_diff(eqn_set, viscous_method, nnodes0, nnodes01,        &
                            nedgeloc, max_nnz, eptr, qnode, xn, yn, zn, ra,    &
                            a_diag, a_off, fhelp, nedgeloc_2d,                 &
                            ielem, elem(ielem)%ncell, elem(ielem)%c2n,         &
                            elem(ielem)%c2e, x, y, z, elem(ielem)%type_cell,   &
                            elem(ielem)%local_f2n, elem(ielem)%local_e2n,      &
                            elem(ielem)%local_f2e, elem(ielem)%e2n_2d,         &
                            elem(ielem)%face_per_cell,                         &
                            elem(ielem)%node_per_cell,                         &
                            elem(ielem)%edge_per_cell, nnz01, ia, ja, n_turb,  &
                            n_tot, elem(ielem)%face_2d, nzg2m, g2m,            &
                            turb, slen )
      end do
    else
      call lmpi_conditional_stop(1,'edge-based issues:ke_jacob')
    endif

  end subroutine ke_jacob


!================================= KW_JACOB_CONVECT ==========================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for Wilcox-ke model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine ke_jacob_convect(nnodes0, nnodes01,                               &
                              nedgeloc, max_nnz, eptr, qnode,                  &
                              xn, yn, zn, ra, a_diag, a_off,                   &
                              fhelp, nedgeloc_2d, facespeed, n_turb, n_tot,    &
                              g2m )

    use kinddefs,            only : odp
    use info_depr,           only : twod
    use grid_motion_helpers, only : need_grid_velocity
    use turb_parameters,     only : t_conv

    integer, intent(in) :: n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: nnodes0, nnodes01

    integer, dimension(2,nedgeloc),          intent(in) :: eptr
    integer, dimension(2,nedgeloc),          intent(in) :: fhelp
    integer, dimension(:),                   intent(in) ::  g2m

    real(dp),  dimension(nedgeloc),              intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),              intent(in)    :: facespeed
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    integer :: n, nedge_jac_eval, row
    integer :: ioff
    integer :: node1, node2

!   real(dp) :: Dkdk, Dkdd, Dddd
    real(dp) :: xnormf, ynormf, znormf, areaf
    real(dp) :: face_speed
    real(dp) :: u, ubar, uminus, uplus, v, w
    real(dp) :: diag1_k, diag1_w, off1_k, off1_w, diag2_k, diag2_w
    real(dp) :: off2_k, off2_w
!   real(dp) :: xmrinv

!   real(dp), dimension(3,3) :: sij

    real(dp), parameter :: half    = 0.5_dp

  continue

!   When using the edge-based terms, we only need to visit this routine
!   one time

    nedge_jac_eval = nedgeloc
    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    endif

!   First compute the convective terms

    do n = 1, nedge_jac_eval
      node1 = eptr(1,n)
      node2 = eptr(2,n)
!     Unit normal to dual face and area

      xnormf = xn(n)
      ynormf = yn(n)
      znormf = zn(n)
      areaf  = ra(n)

!     Dual face speed

      face_speed = 0._dp

        if (need_grid_velocity) then
          face_speed = facespeed(n)
        end if

!       First node

        u = qnode(2,node1)
        v = qnode(3,node1)
        w = qnode(4,node1)
        ubar   = xnormf*u + ynormf*v + znormf*w - face_speed
        uplus  = t_conv*half * (ubar+abs(ubar))
        uminus = t_conv*half * (ubar-abs(ubar))

        diag1_k = uplus *areaf
        diag1_w = uplus *areaf
        off1_k  = uminus*areaf
        off1_w  = uminus*areaf

        if (node1 <= nnodes0) then
          row = g2m(node1)
          a_diag(1,1,row) = a_diag(1,1,row) + diag1_k
          a_diag(2,2,row) = a_diag(2,2,row) + diag1_w
          ioff            = fhelp(1,n)
          a_off(1,1,ioff) = a_off(1,1,ioff) + off1_k
          a_off(2,2,ioff) = a_off(2,2,ioff) + off1_w
        end if

!       Now do the other node (note that ubar pts in other direction)

        u = qnode(2,node2)
        v = qnode(3,node2)
        w = qnode(4,node2)
        ubar   = -(xnormf*u + ynormf*v + znormf*w - face_speed)
        uplus  = t_conv*half * (ubar+abs(ubar))
        uminus = t_conv*half * (ubar-abs(ubar))

        diag2_k = uplus *areaf
        diag2_w = uplus *areaf
        off2_k  = uminus*areaf
        off2_w  = uminus*areaf

        if (node2 <= nnodes0) then
          row = g2m(node2)
          a_diag(1,1,row) = a_diag(1,1,row) + diag2_k
          a_diag(2,2,row) = a_diag(2,2,row) + diag2_w
          ioff        = fhelp(2,n)
          a_off(1,1,ioff) = a_off(1,1,ioff) + off2_k
          a_off(2,2,ioff) = a_off(2,2,ioff) + off2_w
        end if

      end do

  end subroutine ke_jacob_convect

!================================= KW_JACOB_SOURCE ===========================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for Wilcox-ke model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine ke_jacob_source(eqn_set, n_tot, nnodes0, nnodes01                 &
                           , qnode, turb, slen                                 &
                           , gradx, grady, gradz                               &
                           , vol, a_diag                                       &
                           , nnodes0_2d, node_pairs_2d, n_turb                 &
                           , n_grd                                             &
                           , g2m )

    use info_depr,      only : xmach, re, twod, tref
    use debug_defs,     only : test_freestream
    use solution_types, only : compressible, incompressible
    use turb_ke_const,  only : ke_version, ce1_ke, ce2_ke, cmu_ke, limit_f2,   &
                               strain_production
    use turb_kw_const,  only : turb_compress_model
    use fluid,          only : gamma, sutherland_constant
    use turb_util,      only : set_gradv, get_tau
    use turb_functions, only : get_prodk

    integer,                              intent(in) :: eqn_set
    integer,                              intent(in) :: n_tot
    integer,                              intent(in) :: nnodes0
    integer,                              intent(in) :: nnodes01

    integer,                              intent(in) :: n_grd
    integer,                              intent(in) :: n_turb
    integer,                              intent(in) :: nnodes0_2d

    integer, dimension(2,nnodes0_2d),        intent(in) :: node_pairs_2d
    integer, dimension(:),                   intent(in) :: g2m

    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradz
    real(dp),  dimension(nnodes01),              intent(in)    :: vol
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(nnodes01),              intent(in)    :: slen
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag


    integer :: node_src_eval, row
    integer :: i, ii

    real(dp) :: vort
    real(dp) :: tke
    real(dp) :: varepsilon
    real(dp) :: xmr
    real(dp) :: re_t, re_k, f2
    real(dp) :: Dkdk, Dkdd, Dddk, Dddd
    real(dp) :: cstar
    real(dp) :: xmrinv
    real(dp) :: rho, press
    real(dp) :: temp
    real(dp) :: rmu
    real(dp) :: rmut
    real(dp) :: prodk
!   real(dp) :: turb_mach

    real(dp) :: my_xmach
    real(dp) :: dist
!   real(dp) :: ke_e_inf
    real(dp) :: timescaleinv
    real(dp) :: f2_threshold
    real(dp), dimension(3,3) :: gradv
!   real(dp), dimension(3,3) :: sijhat
    real(dp), dimension(3,3) :: sij
    real(dp), dimension(3,3) :: tau
!   real(dp), dimension(3,3) :: wij

    real(dp), parameter :: zero    = 0.0_dp
!   real(dp), parameter :: half    = 0.5_dp
    real(dp), parameter :: one     = 1.0_dp
    real(dp), parameter :: my_tiny = tiny(0.0)
!   real(dp), parameter :: small   = 1.0e-10_dp
    real(dp), parameter :: rsmall  = 1.0e-12_dp

  continue

    cstar = sutherland_constant / tref

!   When using the edge-based terms, we only need to visit this routine
!   one time

    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = one
    case default
      call lmpi_conditional_stop(1, 'ke_jacob_mix: only for in/compress pg')
    end select

!   ke_e_inf = cmu_ke * ke_k_inf * ke_k_inf / ke_mut_inf
    xmr    = my_xmach / re
    xmrinv = re / my_xmach
    Dkdk = zero
    Dkdd = zero
    Dddk = zero
    Dddd = zero

!   nedge_jac_eval = nedgeloc
    node_src_eval  = nnodes0
    if (twod) then
!     nedge_jac_eval = nedgeloc_2d
      node_src_eval   = nnodes0_2d
    endif

!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!                loop over nodes
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!   Next compute the source (production/destruction) terms
!   This assumes that the blending and cross terms have been previously computed
!   for the residual computation!

      source1 : do ii = 1,node_src_eval

        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if

        tke        = turb(1,i)
        varepsilon = turb(2,i)
        dist       = slen(i)

        rho = one
        rmu = one

        if ( eqn_set == compressible ) then
          rho   = qnode(1,i)
          press = qnode(5,i)
          temp  = gamma * press / rho
          rmu  = viscosity_law( cstar, temp )
!         rnu  = rmu  / rho
        end if
!-----------------------------------------------------------------------------80
        gradv = set_gradv( gradx(2,i), grady(2,i), gradz(2,i)                  &
                         , gradx(3,i), grady(3,i), gradz(3,i)                  &
                         , gradx(4,i), grady(4,i), gradz(4,i) )
        vort  = sqrt( (grady(4,i)-gradz(3,i))**2                               &
                    + (gradz(2,i)-gradx(4,i))**2                               &
                    + (gradx(3,i)-grady(2,i))**2 )
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!       Curvature Correction
!-----------------------------------------------------------------------------80
!       f4 = 1.0_dp
!       if (sstrc) then
!         xis  = gradx(2,i)**2 + grady(3,i)**2 + gradz(4,i)**2 + half          &
!            *( (grady(2,i) + gradx(3,i))**2                                   &
!            +  (gradz(2,i) + gradx(4,i))**2                                   &
!            +  (gradz(3,i) + grady(4,i))**2 )
!         vort  = sqrt( (grady(4,i)-gradz(3,i))**2                             &
!                     + (gradz(2,i)-gradx(4,i))**2                             &
!                     + (gradx(3,i)-grady(2,i))**2 )
!         sijmag = sqrt(2.0_dp*xis) + 1.0e-20_dp
!         ri = (vort/sijmag)*(vort/sijmag - 1.0_dp)
!         f4 = 1.0_dp/(1.0_dp + sstrc_crc*ri)
!       end if
!-----------------------------------------------------------------------------80
       select case ( turb_compress_model )
       case ( 'off' )
!        turb_mach = 0.0_dp
 !       dissp_c = 0.0_dp
       case ( 'on', 'sarkar', 'wilcox' )
!        turb_mach = sqrt ( 2.0_dp * tke / temp )
 !       dissp_c = e_compress( rho, dissp, prodk, turb_mach
 !                , turb_compress_model )
       case default
!        turb_mach = 0.0_dp
       end select

      rmut      = mut_ke0 ( rho, press, tke, varepsilon, dist ) ! + 1.0e-12_dp
      sij       = get_sij( gradv )
      prodk     = zero

      select case ( strain_production )
      case ( .true. )
        tau       = get_tau ( sij, rmut, rho, tke )
        prodk     = rho * get_prodk ( tau, gradv )
      case ( .false. )
        prodk       = vort * vort * rmut
      end select
      f2_threshold =                                                          &
        (prodk/varepsilon)*((ce1_ke-1.0_dp)/ce2_ke) + (1.0_dp/ce2_ke)

      select case ( ke_version )
      case ( 'hr' )
        varepsilon = turb(2,i)
        rmut      = mut_ke0 ( rho, press, tke, varepsilon, dist ) ! + 1.0e-12_dp
        if(abs(tke) <= my_tiny) tke = 1.0e-12 ! ke_k_inf
         dkdk  = (2.0_dp*tke*cmu_ke/rmut)/xmr
         dkdd  = 0.0_dp / xmr
         dddk  = ce2_ke * varepsilon*varepsilon/ (tke*tke) ! 0.0_dp
         dddd  = ce2_ke*2.0_dp*(varepsilon/tke)/xmr
      case ( 'abid-ke' )
        re_k = ( rho * sqrt(tke) * dist  / rmu ) * xmrinv
        re_k = min( re_k, huge(0.0_dp) )
        re_k = max( re_k, tiny(0.0_dp) )
        f2   = 1.0_dp - exp(-re_k/12.0_dp)
!       f2   = 1.0_dp
        if ( limit_f2 ) then
          f2 = min(one, max(f2, f2_threshold) )
        endif

        rmut      = mut_ke0 ( rho, press, tke, varepsilon, dist ) ! + 1.0e-12_dp
!       if( tke <= ke_k_inf ) tke = ke_k_inf
!       if( varepsilon <= ke_e_inf ) varepsilon = ke_e_inf
 !           Abid's Model
 !
 !     Use only destruction terms
!        dkdk  = (2.0_dp*tke*cmu_ke/rmut)*xmrinv
         if ( tke <= my_tiny ) then
           timescaleinv = 0.0
           dkdk         = 0.0
           dkdd         = 0.0
           dddk         = 0.0
           dddd         = 0.0
         else
           timescaleinv = varepsilon / tke
           dkdk = 2.0_dp * timescaleinv * xmrinv
           dkdd = 0.0_dp
           dddk =         -ce2_ke * f2 * timescaleinv * timescaleinv * xmrinv
           dddd = 2.0_dp * ce2_ke * f2 * timescaleinv * xmrinv
         endif
!        dddk =          ce2_ke * timescaleinv * timescaleinv * xmrinv
!        dddd = 2.0_dp * ce2_ke * timescaleinv * xmrinv
!        dddd  = ce2_ke*2.0_dp*turb(2,i)/bottom/xmr                            &
!             *( 1.0_dp -1.0_dp                                                &
!             *exp(-(bottom**half*dist/(12.0_dp*rnu*xmr))))

      case ( 'jl')

        re_t    = rho * tke * tke / (varepsilon * rmu+rsmall)*re/xmach
        f2      = one
        if ( re_t > my_tiny ) f2 = 1.0_dp - 0.30_dp * exp ( -re_t * re_t )
        if ( limit_f2 )         f2 = min(one, max(f2, f2_threshold) )
! Limit production term for robustness:
! eterm - epsilon eqn.
!     xsiw       = 2.0_dp * mu_lam * rmut * DEL2USQ / rho * ( xmach / re )**2
! dterm
!     xsid       = - mu_lam / rho * DEL2K * xmach / re
         if ( tke <= my_tiny ) then
           timescaleinv = 0.0
           dkdk         = 0.0
           dkdd         = 0.0
           dddk         = 0.0
           dddd         = 0.0
         else
           timescaleinv = varepsilon / tke
           dkdk = 2.0_dp * timescaleinv * xmrinv
!          dkdk = (2.0_dp*tke*cmu_ke/rmut)*xmrinv
           dkdd = 0.0_dp
           dddk =    abs (-ce2_ke * f2 * timescaleinv * timescaleinv * xmrinv)
           dddd = 2.0_dp * ce2_ke * f2 * timescaleinv * xmrinv
        endif

    case ( 'ls')

      re_t    = rho * tke * tke / (varepsilon * rmu+rsmall)*re/xmach
      f2      = one
      if ( re_t > zero ) then
        f2      = 1.0_dp - 0.30_dp * exp ( -re_t * re_t )
      endif
         if ( tke <= my_tiny ) then
           timescaleinv = 0.0
           dkdk         = 0.0
           dkdd         = 0.0
           dddk         = 0.0
           dddd         = 0.0
         else
           timescaleinv = varepsilon / tke
           dkdk = 2.0_dp * timescaleinv * xmrinv
           dkdd = 0.0_dp
           dddk =         -ce2_ke * f2 * timescaleinv * timescaleinv * xmrinv
           dddd = 2.0_dp * ce2_ke * f2 * timescaleinv * xmrinv
        endif

    end select

        if (test_freestream) then
          Dkdk = 0._dp
          Dkdd = 0._dp
          Dddk = 0._dp
          Dddd = 0._dp
        end if

        row = g2m(i)
        a_diag(1,1,row) = a_diag(1,1,row) + vol(i)*Dkdk
        a_diag(1,2,row) = a_diag(1,2,row) + vol(i)*Dkdd
        a_diag(2,1,row) = a_diag(2,1,row) + vol(i)*Dddk
        a_diag(2,2,row) = a_diag(2,2,row) + vol(i)*Dddd
! if ( i > 69 .and. i < 139 ) then
!  write(6,'(a,i8,10(1x,es12.5))') 'ke_jacob_source', &
!      i,tke, varepsilon, 1.0/timescaleinv,dkdk, dkdd, dddk, dddd
!endif

      end do source1

  end subroutine ke_jacob_source

!================================ JACOB_MIX_DIFF =============================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for Wilcox-ke model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine jacob_mix_diff(eqn_set, viscous_method, nnodes0, nnodes01,        &
                            nedgeloc, max_nnz, eptr, qnode, xn, yn, zn, ra,    &
                            a_diag, a_off, fhelp, nedgeloc_2d,                 &
                            ielem, ncell, c2n, c2e, x, y, z, type_cell,        &
                            local_f2n, local_e2n,local_f2e, e2n_2d,            &
                            face_per_cell, node_per_cell, edge_per_cell, nnz01,&
                            ia, ja, n_turb, n_tot, face_2d, nzg2m, g2m,        &
                            turb, slen )

    use kinddefs,       only : odp
    use info_depr,      only : tref, twod, xmach, re, skeleton,                &
                               grad_x_y_z_contents, use_edge_gradients
    use debug_defs,     only : gradient_construction_lhs
    use solution_types, only : compressible, incompressible
    use turb_ke_const,  only : sig_k_ke, sig_e_ke
    use fluid,          only : gamma, sutherland_constant
    use utilities,      only : tangents

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem, n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: face_2d
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: nnz01

    integer, dimension(2,nedgeloc),          intent(in) :: eptr
    integer, dimension(2,nedgeloc),          intent(in) :: fhelp
    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(edge_per_cell,ncell), intent(in) :: c2e
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(nnz01),               intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m

    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(nnodes01),              intent(in)    :: slen

    character(len=3), intent(in) :: type_cell

    integer :: n, nedge_jac_eval, row
    integer :: ioff
    integer :: node, node1, node2
    integer :: nn
    integer :: total_nodes, edge_node, nodec, local_node
    integer :: k, column

    integer, parameter :: max_total_nodes = 100

    integer, dimension(max_total_nodes)   :: list_total_nodes

    real(dp) :: coeff1, coeff2
    real(dp) :: xmr, phi1, phi2
    real(dp) :: mu_node1, mu_node2, mut_node1, mut_node2
    real(dp) :: cstar
    real(dp) :: rho1inv, rho2inv
    real(dp) :: phi
    real(dp) :: p1
    real(dp) :: lx, ly, lz, mx, my, mz
    real(dp) :: deti
    real(dp) :: ex, ey, ez, disi
    real(dp) :: ngradt, factor, face_term
    real(dp) :: rnu1, rnu2
    real(dp) :: rho1, temp1, rho2, p2, temp2, my_xmach
    real(dp) :: dist1, dist2

    real(dp), dimension(3,3)               :: b
    real(dp), dimension(max_total_nodes)   :: w_ngradt
    real(dp), dimension(n_turb)            :: coe1

    real(dp), parameter :: zero    = 0.0_dp
    real(dp), parameter :: half    = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp

  continue

    ngradt = 0._dp

!   When using the edge-based terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'ke_jacob_mix: only for in/compressible pg')
    end select

    b   = zero
    xmr = my_xmach / re

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    nedge_jac_eval = nedgeloc
    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    endif

!   Diffusion terms:

    diffusion_terms : if ( viscous_method > 0 ) then

      if(skeleton > 0) then
        write(*,*) ' Compute Jacobians of decoupled turbulent diffusion terms.'
        write(*,*) ' Using weighted least squares for average gradient.'
        write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
      endif
      if(trim(grad_x_y_z_contents) /= 'viscous weighted-least-squares') then
        call lmpi_conditional_stop(1)
      endif

      mixed_edge_loop: do n = 1, nedge_jac_eval

!       loop over all the faces and calculate the turbulent diffusion terms
!       (in the 2D case, this loop contains edges on only one y=constant plane)
!       turb(1,node) contains the turbulence value
!
!       Break into two contributions - directional and the average gradient

!       Nearest neighbor nodes

        node1 = eptr(1,n)
        node2 = eptr(2,n)

!       Initialize number of contributing nodes and global node list
!       for average gradient at face contributions

        total_nodes = 2
        list_total_nodes(1) = node1
        list_total_nodes(2) = node2
        w_ngradt(1)         = zero
        w_ngradt(2)         = zero

        if ( eqn_set == compressible ) then
          rho1   = qnode(1,node1)
          rho1inv = my_1/rho1
          p1     = qnode(5,node1)
          temp1  = gamma*p1*rho1inv
          rnu1   = viscosity_law( cstar, temp1 ) * rho1inv

          rho2   = qnode(1,node2)
          rho2inv = my_1/rho2
          p2     = qnode(5,node2)
          temp2  = gamma*p2*rho2inv
          rnu2   = viscosity_law( cstar, temp2 ) * rho2inv
        else
          rho1   = my_1
          rho2   = my_1
          rnu1   = my_1
          rnu2   = my_1
        end if

        coe1(1) = 1.0/sig_k_ke
        coe1(2) = 1.0/sig_e_ke

        turbvariable_loop1 : do nn = 1, n_turb

          coeff1 = coe1(nn)
          coeff2 = coe1(nn)
          mu_node1  = rnu1*rho1
          mu_node2  = rnu2*rho2
!         mut_node1 = amut(node1)
!         mut_node2 = amut(node2)
          dist1     = slen(node1)
          dist2     = slen(node2)
          mut_node1 = mut_ke0 ( rho1, p1, turb(1,node1), turb(2,node1), dist1)
          mut_node2 = mut_ke0 ( rho2, p2, turb(1,node2), turb(2,node2), dist2)

          phi1   = mu_node1 + coeff1*mut_node1
          phi2   = mu_node2 + coeff2*mut_node2

          phi = half*(phi1 + phi2)

!         ex, ey, ez is unit vector along edge direction

          ex   = x(node2) - x(node1)
          ey   = y(node2) - y(node1)
          ez   = z(node2) - z(node1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          if( viscous_method == 2 ) then

          elseif( gradient_construction_lhs == 0 ) then

          elseif( gradient_construction_lhs == 1 ) then

          !...find tangent vectors in the dual face
            call tangents(xn(n), yn(n), zn(n), lx, ly, lz, mx, my, mz)

            !...find inverse elements of transformation matrix
            deti =  my_1/ ( ex*( ly*mz - lz*my )                               &
                          + ey*( lz*mx - lx*mz )                               &
                          + ez*( lx*my - ly*mx ) )

            b(1,1) =  deti*( ly*mz - lz*my )
            b(1,2) = -deti*( ey*mz - ez*my )
            b(1,3) =  deti*( ey*lz - ez*ly )

            b(2,1) = -deti*( lx*mz - lz*mx )
            b(2,2) =  deti*( ex*mz - ez*mx )
            b(2,3) = -deti*( ex*lz - ez*lx )

            b(3,1) =  deti*( lx*my - ly*mx )
            b(3,2) = -deti*( ex*my - ey*mx )
            b(3,3) =  deti*( ex*ly - ey*lx )

          endif

!         First gradient contribution from directional contribution

!         turbulent diffusion contribution at dual face [ two terms ]

!         [area]*[nondimensionalization factor: Mach / Re / sigma] at dual face

          face_term = xmr*ra(n)

!         [area]*[nondimensionalization factor : Mach / Re / sigma ]*
!         [edge:face vector dot product]/[edge distance] at dual face

          if( viscous_method == 2 ) then

            ngradt = face_term*( ex*xn(n) + ey*yn(n) + ez*zn(n) )*disi

          elseif( gradient_construction_lhs == 0 ) then

            ngradt = face_term*( ex*xn(n) + ey*yn(n) + ez*zn(n) )*disi

          elseif( gradient_construction_lhs == 1 ) then

            ngradt = face_term*( b(1,1)*xn(n) + b(2,1)*yn(n) + b(3,1)*zn(n) )* &
                     disi

          endif

          if ( node1 <= nnodes0 ) then

            ioff = fhelp(1,n)
            row  = g2m(node1)
            a_diag(nn,nn,row) = a_diag(nn,nn,row) + phi*ngradt
            a_off(nn,nn,ioff) = a_off(nn,nn,ioff) - phi*ngradt

          end if

          if ( node2 <= nnodes0 ) then

            ioff = fhelp(2,n)
            row  = g2m(node2)
            a_diag(nn,nn,row) = a_diag(nn,nn,row) + phi*ngradt
            a_off(nn,nn,ioff) = a_off(nn,nn,ioff) - phi*ngradt

          end if

!         Assemble final Jacobian matrices into sparse matrix form

          factor = -face_term

          edge_node_loop2 : do edge_node = 1,2

!           global node number

            node = list_total_nodes(edge_node)

            factor = -factor

!         Diagonal contributions

          if ( node <= nnodes0 ) then
            row = g2m(node)
            a_diag(nn,nn,row) = a_diag(nn,nn,row) - factor*phi*              &
                                 w_ngradt( edge_node )
          end if

!         Off-diagonal contributions

          local_node_loop : do local_node = 1, total_nodes

            nodec = list_total_nodes(local_node) ! global node number

            if (nodec == node) cycle

            if ( node <= nnodes0 ) then

!             Location of nonzero contribution via compressed row storage

              ioff = 0

              do k = ia(node), ia(node+1) - 1
                column = ja(k)
                if (column == nodec) ioff = nzg2m(k)
              end do

              if (ioff == 0) then
                write(6,*)'error: no place to put contribution from node ',    &
                           nodec,' to the off diagonal of node ',node
                call lmpi_conditional_stop(1)
              end if

              a_off(nn,nn,ioff) = a_off(nn,nn,ioff) - factor*phi*              &
                                  w_ngradt( local_node )

            end if

            end do local_node_loop

          end do edge_node_loop2

        end do turbvariable_loop1

      end do mixed_edge_loop

    else

      if ( twod .or. (.not.use_edge_gradients) ) then
        call jacob_mix_diff_cell(eqn_set, viscous_method, nnodes0, nnodes01,   &
                                 nedgeloc, max_nnz, qnode, a_diag, a_off,      &
                                 nedgeloc_2d, ielem, ncell, c2n, c2e, x, y, z, &
                                 type_cell, local_f2n, local_e2n,local_f2e,    &
                                 e2n_2d, face_per_cell, node_per_cell,         &
                                 edge_per_cell, nnz01, ia, ja, n_turb, n_tot,  &
                                 face_2d, nzg2m, g2m, turb, slen )
      else
        call jacob_mix_diff_cell_opt(eqn_set, nnodes0, nnodes01, max_nnz,      &
                                     qnode, a_diag, a_off, ncell, c2n, x, y, z,&
                                     type_cell, local_f2n, local_e2n,          &
                                     face_per_cell, node_per_cell,             &
                                     edge_per_cell, nnz01, ia, ja, n_turb,     &
                                     n_tot, nzg2m, g2m, turb, slen )
      endif

    end if diffusion_terms

  end subroutine jacob_mix_diff


!================================ JACOB_MIX_DIFF_CELL ========================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for Wilcox-ke model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine jacob_mix_diff_cell(eqn_set, viscous_method, nnodes0, nnodes01,   &
                                 nedgeloc, max_nnz, qnode, a_diag, a_off,      &
                                 nedgeloc_2d, ielem, ncell, c2n, c2e, x, y, z, &
                                 type_cell, local_f2n, local_e2n,local_f2e,    &
                                 e2n_2d, face_per_cell, node_per_cell,         &
                                 edge_per_cell, nnz01, ia, ja, n_turb, n_tot,  &
                                 face_2d, nzg2m, g2m, turb, slen)

    use kinddefs,       only : odp
    use info_depr,      only : tref, xmach, re, twod, use_edge_gradients
    use debug_defs,     only : gradient_construction_lhs
    use solution_types, only : compressible, incompressible
    use turb_ke_const,  only : sig_k_ke, sig_e_ke
    use fluid,          only : gamma, sutherland_constant
    use element_defs,   only : max_node_per_cell, max_face_per_cell,           &
                               max_edge_per_cell
    use utilities,      only : tangents, tinverse, cell_jacobians

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem, n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: face_2d
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: nnz01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(edge_per_cell,ncell), intent(in) :: c2e
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(nnz01),               intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m

    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(nnodes01),              intent(in)    :: slen
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    character(len=3), intent(in) :: type_cell

    integer :: n, nedge_jac_eval, row
    integer :: ie, i, ii, ie_local, i_local, ioff
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, edge, node
    integer :: n1, n2, n3, n4, n5, n6, nn
    integer :: nodec
    integer :: k, column

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: phi_k, phi_w, coeff_k, coeff_d
    real(dp) :: xmr, mu_node, mut_node
    real(dp) :: cstar
    real(dp) :: rho, rhoinv
    real(dp) :: dgradt_xi
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: areai, xnf, ynf, znf, lx, ly, lz, mx, my, mz
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: ex, ey, ez, disi
    real(dp) :: factor
    real(dp) :: dlgradt, dmgradt
    real(dp) :: my_xmach
    real(dp) :: p

    real(dp), dimension(3,3)               :: b, t
    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell) :: dgradx_celldq, dgrady_celldq
    real(dp), dimension(max_node_per_cell) :: dgradz_celldq
    real(dp), dimension(max_node_per_cell) :: dtrbrex, dtrbrey, dtrbrez
    real(dp), dimension(max_node_per_cell) :: dtrbrexavg, dtrbreyavg
    real(dp), dimension(max_node_per_cell) :: dtrbrezavg, dngradt
    real(dp), dimension(max_face_per_cell) :: nx, ny, nz
    real(dp), dimension(max_node_per_cell) :: nu_node, t_node

    real(dp), parameter :: half    = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp

    logical :: edge_gradients

  continue

!   When using the edge-based terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'ke_jacob_mix: only for in/compress pg')
    end select

    xmr  = my_xmach / re

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    edge_map     = 0
    node_map     = 0

    nedge_jac_eval = nedgeloc
    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    endif

    nodes_local = 0
    if ( twod .and. (viscous_method == 0) ) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    elseif( viscous_method == 0 ) then

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

      diffusion_term_cell : do n = 1, ncell

!       initialization

        cell_vol = 0.0_dp

        dtrbrex(:) = 0._dp
        dtrbrey(:) = 0._dp
        dtrbrez(:) = 0._dp

        phi_k    = 0._dp
        phi_w    = 0._dp

        xc = 0._dp
        yc = 0._dp
        zc = 0._dp

        x_node(:)       = 0._dp
        y_node(:)       = 0._dp
        z_node(:)       = 0._dp
        t_node(:)       = 0._dp
        nu_node(:)      = 0._dp
        dtrbrexavg(:) = 0._dp
        dtrbreyavg(:) = 0._dp
        dtrbrezavg(:) = 0._dp

!       compute cell averages and set up some local solution arrays
        coeff_k  = (1.0/sig_k_ke)
        coeff_d  = (1.0/sig_e_ke)

        node_loop : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

!         global node number

          node = c2n(i,n)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          if ( eqn_set == compressible ) then
            rho        = qnode(1,node)
            p          = qnode(5,node)
            rhoinv     = my_1/rho
            t_node(i)  = gamma*qnode(5,node)*rhoinv
            nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv
          else
            rho        = my_1
            p          = my_1
            nu_node(i) = my_1
          end if

          mu_node  = rho*nu_node(i)
!         mut_node = amut(node)
          mut_node = mut_ke0 ( rho, p, turb(1,node), turb(2,node), slen(node))

          phi_k = phi_k + (mu_node + coeff_k*mut_node)
          phi_w = phi_w + (mu_node + coeff_d*mut_node)

        end do node_loop

!       get cell averages by dividing by the number of nodes that contributed

        fact = 1._dp / real(nodes_local, dp)

        phi_k    = phi_k*fact
        phi_w    = phi_w*fact

!       compute the cell center (must loop over node_per_cell even in 2D)

        do i = 1, node_per_cell

!         global node number

          node = c2n(i,n)

          xc  =  xc + x(node)
          yc  =  yc + y(node)
          zc  =  zc + z(node)

        end do

        fact = 1._dp / real(node_per_cell, dp)

        xc  =  xc*fact
        yc  =  yc*fact
        zc  =  zc*fact

!       get the jacobians of the gradients in the primal cell via Green-Gauss

        call cell_jacobians(edges_local, max_node_per_cell, face_per_cell,     &
                            x_node, y_node, z_node, local_f2n, e2n_2d,         &
                            dgradx_celldq, dgrady_celldq, dgradz_celldq,       &
                            cell_vol, nx, ny, nz)

!       store off these average gradients

        if (twod) then

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            dtrbrexavg(i) = dgradx_celldq(i)
            dtrbrezavg(i) = dgradz_celldq(i)

          end do

        else

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            dtrbrexavg(i) = dgradx_celldq(i)
            dtrbreyavg(i) = dgrady_celldq(i)
            dtrbrezavg(i) = dgradz_celldq(i)

          end do

        end if

!       next loop over the edges in the cell and get each one's
!       contribution to the jacobian

        edge_loop : do ie_local = 1,edges_local

!         local edge number

          ie = edge_map(ie_local)

!         global edge number

          edge = c2e(ie,n)

!         check edge to make sure it is not off-processor - if it is
!         we don't need its contribution

          if (edge > nedge_jac_eval) cycle edge_loop

!         local node numbers of edge endpoints

          n1_loc = local_e2n(ie,1)
          n2_loc = local_e2n(ie,2)

!         global node numbers of edge endpoints

          n1 = c2n(n1_loc,n)
          n2 = c2n(n2_loc,n)

!         get this edges' contributiuon to the dual normal and area

!         edge midpoint

          xm = (x(n1) + x(n2))*half
          ym = (y(n1) + y(n2))*half
          zm = (z(n1) + z(n2))*half

!         compute left face centroid

          n3 = c2n(local_e2n(ie,3),n)

          if (local_e2n(ie,4) /= 0) then

!           quad cell face

            n4 = c2n(local_e2n(ie,4),n)

            xl = (x(n1) + x(n2) + x(n3) + x(n4))*my_4th
            yl = (y(n1) + y(n2) + y(n3) + y(n4))*my_4th
            zl = (z(n1) + z(n2) + z(n3) + z(n4))*my_4th

          else

!           tria cell face

            xl = (x(n1) + x(n2) + x(n3))*my_3rd
            yl = (y(n1) + y(n2) + y(n3))*my_3rd
            zl = (z(n1) + z(n2) + z(n3))*my_3rd

          end if

!         compute right face centroid

          n5 = c2n(local_e2n(ie,5),n)

          if (local_e2n(ie,6) /= 0) then

!           quad cell face

            n6 = c2n(local_e2n(ie,6),n)

            xr = (x(n1) + x(n2) + x(n5) + x(n6))*my_4th
            yr = (y(n1) + y(n2) + y(n5) + y(n6))*my_4th
            zr = (z(n1) + z(n2) + z(n5) + z(n6))*my_4th

          else

!           tria cell face

            xr = (x(n1) + x(n2) + x(n5))*my_3rd
            yr = (y(n1) + y(n2) + y(n5))*my_3rd
            zr = (z(n1) + z(n2) + z(n5))*my_3rd

          end if

!         get the contributions to dual normals from the two triangles
!         that form part of the dual-cell surface

!         area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

          areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*half
          areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*half
          areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*half

!         get (jacobians of) gradients at the dual face; either take gradients
!         for this piece of the dual face to be the same as the cell-average
!         gradient computed above  (which is what the legacy FUN3D solver does
!         for tets), or combine with the edge-gradient to increase h-ellipticity
!         non-simplicial meshes.

!         for tets in 3D or prisms in 2D, edge gradients add no new info
!         so there is no need to do the extra work

          edge_gradients = use_edge_gradients

          if (type_cell == 'tet') edge_gradients = .false.
          if (twod .and. type_cell == 'prz') edge_gradients = .false.

          include_edge_gradients : if (edge_gradients) then

          do nn = 1, n_turb
!           ex, ey, ez is unit vector along edge direction

            ex   = x(n2) - x(n1)
            ey   = y(n2) - y(n1)
            ez   = z(n2) - z(n1)
            disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

            ex   = ex*disi
            ey   = ey*disi
            ez   = ez*disi

            if ( gradient_construction_lhs == 0 ) then

!             first get the avg_term pieces; all active cell nodes contribute

              do i_local = 1, nodes_local

!               local node number

                i = node_map(i_local)

                dgradt_xi = dtrbrexavg(i)*ex+dtrbreyavg(i)*ey+dtrbrezavg(i)*ez

                dtrbrex(i) = dtrbrexavg(i) - dgradt_xi*ex
                dtrbrey(i) = dtrbreyavg(i) - dgradt_xi*ey
                dtrbrez(i) = dtrbrezavg(i) - dgradt_xi*ez

              end do

!             next get the edge_term pieces; only the two edge nodes contribute

              dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*ex
              dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*ex
              dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*ey
              dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*ey
              dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*ez
              dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*ez

            elseif( gradient_construction_lhs == 1 ) then

!             find tangent vectors in the dual face

              areai = my_1/sqrt( areax**2 + areay**2 + areaz**2 )
              xnf = areax*areai
              ynf = areay*areai
              znf = areaz*areai
              call tangents(xnf, ynf, znf, lx, ly, lz, mx, my, mz)

!             form transformation matrix

              !...edge direction
              b(1,1) = ex
              b(1,2) = ey
              b(1,3) = ez

              !...l direction
              b(2,1) = lx
              b(2,2) = ly
              b(2,3) = lz

              !...m direction
              b(3,1) = mx
              b(3,2) = my
              b(3,3) = mz

              call tinverse( b , t )

!             first get the avg_term pieces; all active cell nodes contribute

              do i_local = 1, nodes_local

!               local node number

                i = node_map(i_local)

                dlgradt = dtrbrexavg(i)*b(2,1) + dtrbreyavg(i)*b(2,2)          &
                        + dtrbrezavg(i)*b(2,3)

                dmgradt = dtrbrexavg(i)*b(3,1) + dtrbreyavg(i)*b(3,2)          &
                        + dtrbrezavg(i)*b(3,3)

                dtrbrex(i) = t(1,2)*dlgradt + t(1,3)*dmgradt
                dtrbrey(i) = t(2,2)*dlgradt + t(2,3)*dmgradt
                dtrbrez(i) = t(3,2)*dlgradt + t(3,3)*dmgradt

              end do

!             next get the edge_term pieces; only the two edge nodes contribute

              dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*t(1,1)
              dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*t(1,1)
              dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*t(2,1)
              dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*t(2,1)
              dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*t(3,1)
              dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*t(3,1)

            end if
          end do

          else include_edge_gradients

!           only have the unaltered, average green-gauss contributions;
!           all active nodes in the cell contribute

            do nn = 1, n_turb
              do i_local = 1, nodes_local

!               local node number

                i = node_map(i_local)

                dtrbrex(i) = dtrbrexavg(i)
                dtrbrey(i) = dtrbreyavg(i)
                dtrbrez(i) = dtrbrezavg(i)

              end do
            end do

          end if include_edge_gradients

!         form some intermediate Jacobians at all nodes

          dngradt(:) = xmr*( dtrbrex(:)*areax + dtrbrey(:)*areay               &
                     +       dtrbrez(:)*areaz )

!         assemble final Jacobian matrices into sparse matrix form

          factor = -my_1

          edge_node_loop : do ii = 1,2

!           diagonal contributions

!           local (i) and global (node) numbers

            if (ii == 1) then
              i = n1_loc
              node = c2n(n1_loc,n)
            else
              i = n2_loc
              node = c2n(n2_loc,n)
            end if

            factor = -my_1*factor

            if ( node <= nnodes0 ) then
              row = g2m(node)
              a_diag(1,1,row) = a_diag(1,1,row) - factor*phi_k*dngradt(i)    &
                                 /qnode(1,node)
              a_diag(2,2,row) = a_diag(2,2,row) - factor*phi_w*dngradt(i)    &
                                 /qnode(1,node)
            end if

!           off-diagonal contributions

            node_loop_2 : do i_local = 1, nodes_local

!             local node number

              i = node_map(i_local)

!             global node number

              nodec = c2n(i,n)

              if (nodec == node) cycle node_loop_2

              if ( node <= nnodes0 ) then

!               avoid unused entries in a_off when running solver from adjoint
                if (node > nnodes0 .and. nodec > nnodes0) cycle node_loop_2

!               determine location of nonzero contribution in comp row storage

                ioff = 0

                do k = ia(node), ia(node+1) - 1
                  column = ja(k)
                  if (column == nodec) ioff = nzg2m(k)
                end do

                if (ioff == 0) then
                  write(6,*)'error: no place to put contribution from node ',  &
                             nodec,' to the off diagonal of node ',node
                  call lmpi_conditional_stop(1)
                end if

                a_off(1,1,ioff) = a_off(1,1,ioff) - factor*phi_k*dngradt(i)    &
                                  /qnode(1,node)
                a_off(2,2,ioff) = a_off(2,2,ioff) - factor*phi_w*dngradt(i)    &
                                  /qnode(1,node)

              end if

            end do node_loop_2

          end do edge_node_loop

        end do edge_loop

      end do diffusion_term_cell

  end subroutine jacob_mix_diff_cell


!================================ JACOB_MIX_DIFF_CELL_OPT ====================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for Wilcox-ke model (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine jacob_mix_diff_cell_opt(eqn_set, nnodes0, nnodes01, max_nnz,      &
                                     qnode, a_diag, a_off, ncell, c2n, x, y, z,&
                                     type_cell, local_f2n, local_e2n,          &
                                     face_per_cell, node_per_cell,             &
                                     edge_per_cell, nnz01, ia, ja, n_turb,     &
                                     n_tot, nzg2m, g2m, turb, slen )

    use kinddefs,       only : odp
    use info_depr,      only : tref, xmach, re
    use solution_types, only : compressible, incompressible
    use turb_ke_const,  only : sig_k_ke, sig_e_ke
    use fluid,          only : gamma, sutherland_constant
    use element_defs,   only : max_node_per_cell

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: nnz01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(nnz01),               intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m

    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(nnodes01),              intent(in)    :: slen
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off

    character(len=3), intent(in) :: type_cell

    integer :: n, row, n3_loc, n4_loc, n5_loc, n6_loc, j
    integer :: ie, i, ioff, iface, nn1, nn2, nn3, nn4
    integer :: n1_loc, n2_loc, node
    integer :: nodec
    integer :: k, column

    real(dp) :: phi_k, phi_w, coeff_k, coeff_d
    real(dp) :: xmr, mu_node, mut_node, xavg, yavg, zavg, cell_vol
    real(dp) :: cstar, nx, ny, nz, xavg1, yavg1, zavg1
    real(dp) :: rho, xavg2, yavg2, zavg2, nx1, ny1, nz1, term1, term2
    real(dp) :: dgradt_xi, nx2, ny2, nz2
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: xc, yc, zc, fact
    real(dp) :: ex, ey, ez, disi
    real(dp) :: my_xmach, cell_vol_inv
    real(dp) :: p

    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell) :: rhoinv_node
    real(dp), dimension(max_node_per_cell) :: dtrbrex, dtrbrey, dtrbrez
    real(dp), dimension(max_node_per_cell) :: dtrbrexavg, dtrbreyavg
    real(dp), dimension(max_node_per_cell) :: dtrbrezavg, dngradt
    real(dp), dimension(max_node_per_cell) :: nu_node, t_node
    real(dp), dimension(2,node_per_cell,node_per_cell) :: a

    real(dp), parameter :: half    = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_4th  = 1.0_dp/4.0_dp
    real(dp), parameter :: my_6th  = 1.0_dp/6.0_dp
    real(dp), parameter :: my_3rd  = 1.0_dp/3.0_dp
    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'ke_jacob_mix: only for in/comprss pg')
    end select

    xmr  = my_xmach / re
    fact = 1._dp / real(node_per_cell, dp)

!   Diffusion terms:

      diffusion_term_cell : do n = 1, ncell

        a        = 0._dp
        phi_k    = 0._dp
        phi_w    = 0._dp

        xc = 0._dp
        yc = 0._dp
        zc = 0._dp

!       compute cell averages and set up some local solution arrays

        coeff_k  = (1.0/sig_k_ke)
        coeff_d  = (1.0/sig_e_ke)

        node_loop : do i = 1, node_per_cell

          node = c2n(i,n)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          xc  =  xc + x_node(i)
          yc  =  yc + y_node(i)
          zc  =  zc + z_node(i)

          if ( eqn_set == compressible ) then
            rho            = qnode(1,node)
            p              = qnode(5,node)
            rhoinv_node(i) = my_1/rho
            t_node(i)      = gamma*qnode(5,node)*rhoinv_node(i)
            nu_node(i)     = viscosity_law( cstar, t_node(i) ) * rhoinv_node(i)
          else
            rho            = my_1
            p              = my_1
            rhoinv_node(i) = my_1
            nu_node(i)     = my_1
          end if

          mu_node  = rho*nu_node(i)
!         mut_node = amut(node)
          mut_node = mut_ke0 ( rho, p, turb(1,node), turb(2,node), slen(node))

          phi_k = phi_k + (mu_node + mut_node/coeff_k)
          phi_w = phi_w + (mu_node + mut_node/coeff_d)

        end do node_loop

        phi_k    = phi_k*fact
        phi_w    = phi_w*fact

        xc  =  xc*fact
        yc  =  yc*fact
        zc  =  zc*fact

!       get the jacobians of the gradients in the primal cell via Green-Gauss

        dtrbrexavg(:) = 0.0_dp
        dtrbreyavg(:) = 0.0_dp
        dtrbrezavg(:) = 0.0_dp
        cell_vol      = 0.0_dp

      threed_faces : do iface = 1,face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!         triangular faces of the cell

!         face normals (factor of 1/2 deferred till cell_vol
!         and jacobian terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))         &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))         &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))         &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)

!         jacobian contributions

          dtrbrexavg(nn1) = dtrbrexavg(nn1) + nx
          dtrbreyavg(nn1) = dtrbreyavg(nn1) + ny
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + nz

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + nx
          dtrbreyavg(nn2) = dtrbreyavg(nn2) + ny
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + nz

          dtrbrexavg(nn3) = dtrbrexavg(nn3) + nx
          dtrbreyavg(nn3) = dtrbreyavg(nn3) + ny
          dtrbrezavg(nn3) = dtrbrezavg(nn3) + nz

        else

!         quadrilateral faces of the cell

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))        &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))        &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))        &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))        &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))
          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))        &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))
          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))        &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!         cell volume contributions

          cell_vol = cell_vol + (term1 + term2)

!         jacobian contributions

          dtrbrexavg(nn1) = dtrbrexavg(nn1) + nx1 + nx2
          dtrbreyavg(nn1) = dtrbreyavg(nn1) + ny1 + ny2
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + nz1 + nz2

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + nx1
          dtrbreyavg(nn2) = dtrbreyavg(nn2) + ny1
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + nz1

          dtrbrexavg(nn3) = dtrbrexavg(nn3) + nx1 + nx2
          dtrbreyavg(nn3) = dtrbreyavg(nn3) + ny1 + ny2
          dtrbrezavg(nn3) = dtrbrezavg(nn3) + nz1 + nz2

          dtrbrexavg(nn4) = dtrbrexavg(nn4) + nx2
          dtrbreyavg(nn4) = dtrbreyavg(nn4) + ny2
          dtrbrezavg(nn4) = dtrbrezavg(nn4) + nz2

        end if

      end do threed_faces

      cell_vol = cell_vol * my_18th
      cell_vol_inv = my_6th/cell_vol

      dtrbrexavg(:) = dtrbrexavg(:) * cell_vol_inv
      dtrbreyavg(:) = dtrbreyavg(:) * cell_vol_inv
      dtrbrezavg(:) = dtrbrezavg(:) * cell_vol_inv

!       next loop over the edges in the cell and get each one's
!       contribution to the jacobian

        edge_loop : do ie = 1, edge_per_cell

          n1_loc = local_e2n(ie,1)
          n2_loc = local_e2n(ie,2)
          n3_loc = local_e2n(ie,3)
          n4_loc = local_e2n(ie,4)
          n5_loc = local_e2n(ie,5)
          n6_loc = local_e2n(ie,6)

!         get this edges' contributiuon to the dual normal and area

!         edge midpoint

          xm = (x_node(n1_loc) + x_node(n2_loc))*half
          ym = (y_node(n1_loc) + y_node(n2_loc))*half
          zm = (z_node(n1_loc) + z_node(n2_loc))*half

!       compute left face centroid

          if (n4_loc /= 0) then
            xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc)             &
                + x_node(n4_loc))*my_4th
            yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc)             &
                + y_node(n4_loc))*my_4th
            zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc)             &
                + z_node(n4_loc))*my_4th
          else
            xl = (x_node(n1_loc) + x_node(n2_loc) + x_node(n3_loc))*my_3rd
            yl = (y_node(n1_loc) + y_node(n2_loc) + y_node(n3_loc))*my_3rd
            zl = (z_node(n1_loc) + z_node(n2_loc) + z_node(n3_loc))*my_3rd
          end if

!       compute right face centroid

          if (n6_loc /= 0) then
            xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc)             &
                + x_node(n6_loc))*my_4th
            yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc)             &
                + y_node(n6_loc))*my_4th
            zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc)             &
                + z_node(n6_loc))*my_4th
          else
            xr = (x_node(n1_loc) + x_node(n2_loc) + x_node(n5_loc))*my_3rd
            yr = (y_node(n1_loc) + y_node(n2_loc) + y_node(n5_loc))*my_3rd
            zr = (z_node(n1_loc) + z_node(n2_loc) + z_node(n5_loc))*my_3rd
          end if

!         get the contributions to dual normals from the two triangles
!         that form part of the dual-cell surface

!         area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

          areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*half
          areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*half
          areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*half

!         get (jacobians of) gradients at the dual face; either take gradients
!         for this piece of the dual face to be the same as the cell-average
!         gradient computed above  (which is what the legacy FUN3D solver does
!         for tets), or combine with the edge-gradient to increase h-ellipticity
!         non-simplicial meshes.

!         for tets in 3D or prisms in 2D, edge gradients add no new info
!         so there is no need to do the extra work

          if (type_cell /= 'tet') then

            ex   = x_node(n2_loc) - x_node(n1_loc)
            ey   = y_node(n2_loc) - y_node(n1_loc)
            ez   = z_node(n2_loc) - z_node(n1_loc)
            disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )
            ex   = ex*disi
            ey   = ey*disi
            ez   = ez*disi

            do i = 1, node_per_cell
              dgradt_xi = dtrbrexavg(i)*ex+dtrbreyavg(i)*ey+dtrbrezavg(i)*ez
              dtrbrex(i) = dtrbrexavg(i) - dgradt_xi*ex
              dtrbrey(i) = dtrbreyavg(i) - dgradt_xi*ey
              dtrbrez(i) = dtrbrezavg(i) - dgradt_xi*ez
            end do

!           next get the edge_term pieces; only the two edge nodes contribute

            dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*ex
            dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*ex
            dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*ey
            dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*ey
            dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*ez
            dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*ez

          else

            dtrbrex(:) = dtrbrexavg(:)
            dtrbrey(:) = dtrbreyavg(:)
            dtrbrez(:) = dtrbrezavg(:)

          end if

!         form some intermediate Jacobians at all nodes

          dngradt(:) = xmr*( dtrbrex(:)*areax + dtrbrey(:)*areay               &
                     +       dtrbrez(:)*areaz )

!         assemble final Jacobian matrices into sparse matrix form
!         map the local entries into compact space and unpack later

          a(1,n1_loc,n1_loc) = a(1,n1_loc,n1_loc)                              &
                             - phi_k*dngradt(n1_loc)*rhoinv_node(n1_loc)
          a(2,n1_loc,n1_loc) = a(2,n1_loc,n1_loc)                              &
                             - phi_w*dngradt(n1_loc)*rhoinv_node(n1_loc)

          node_loop_3 : do j = 1, node_per_cell
            if ( j == n1_loc ) cycle node_loop_3
            a(1,n1_loc,j) = a(1,n1_loc,j) - phi_k*dngradt(j)*rhoinv_node(n1_loc)
            a(2,n1_loc,j) = a(2,n1_loc,j) - phi_w*dngradt(j)*rhoinv_node(n1_loc)
          end do node_loop_3

          a(1,n2_loc,n2_loc) = a(1,n2_loc,n2_loc)                              &
                             + phi_k*dngradt(n2_loc)*rhoinv_node(n2_loc)
          a(2,n2_loc,n2_loc) = a(2,n2_loc,n2_loc)                              &
                             + phi_w*dngradt(n2_loc)*rhoinv_node(n2_loc)

          node_loop_4 : do j = 1, node_per_cell
            if ( j == n2_loc ) cycle node_loop_4
            a(1,n2_loc,j) = a(1,n2_loc,j) + phi_k*dngradt(j)*rhoinv_node(n2_loc)
            a(2,n2_loc,j) = a(2,n2_loc,j) + phi_w*dngradt(j)*rhoinv_node(n2_loc)
          end do node_loop_4

        end do edge_loop

        do i = 1, node_per_cell
          node = c2n(i,n)
          if ( node <= nnodes0 ) then
            do j = 1, node_per_cell
              if ( i == j ) then
                row = g2m(node)
                a_diag(1,1,row) = a_diag(1,1,row) + a(1,i,i)
                a_diag(2,2,row) = a_diag(2,2,row) + a(2,i,i)
              else
                nodec = c2n(j,n)
                ioff = 0
                search : do k = ia(node), ia(node+1) - 1
                  column = ja(k)
                  if (column == nodec) then
                    ioff = nzg2m(k)
                    exit search
                  endif
                end do search
                a_off(1,1,ioff) = a_off(1,1,ioff) + a(1,i,j)
                a_off(2,2,ioff) = a_off(2,2,ioff) + a(2,i,j)
              endif
            end do
          end if
        end do

      end do diffusion_term_cell

  end subroutine jacob_mix_diff_cell_opt

!========================== FLUX_TURB ========================================80
!
! Flux function for ke bc residual calculation
!
!=============================================================================80
  pure function flux_turb ( ubar, area, ql, qr, n_turb )

    integer,                     intent(in) :: n_turb
    real(dp),                    intent(in) :: ubar
    real(dp),                    intent(in) :: area
    real(dp), dimension(n_turb), intent(in) :: ql
    real(dp), dimension(n_turb), intent(in) :: qr

    real(dp), dimension(n_turb)        :: flux_turb
    real(dp), dimension(n_turb)        :: fluxl
    real(dp), dimension(n_turb)        :: fluxr
    real(dp)                           :: uplus
    real(dp)                           :: uminus

    real(dp), parameter :: half = 0.5_dp

! inside to outside

    uplus  = half * ( ubar + abs(ubar) )
    uminus = half * ( ubar - abs(ubar) )

    fluxl(1)     = uplus  * ql(1)
    fluxr(1)     = uminus * qr(1)
    flux_turb(1) = ( fluxl(1) + fluxr(1) ) * area

    if ( n_turb == 2 ) then
      fluxl(n_turb)     = uplus  * ql(n_turb)
      fluxr(n_turb)     = uminus * qr(n_turb)
      flux_turb(n_turb) = ( fluxl(n_turb) + fluxr(n_turb) ) * area
    endif

! node 2 to node 1
!   ubarm  = -ubar
!   uplus  = half * ( ubarm + abs(ubarm) )
!   uminus = half * ( ubarm - abs(ubarm) )

!   fluxl(1) = uminus * ql(1)
!   fluxl(2) = uminus * ql(2)

!   fluxr(1) = uplus  * qr(1)
!   fluxr(2) = uplus  * qr(2)

!   flux_turb(1) = flux_turb(1) + ( fluxl(1) + fluxr(1) ) * area
!   flux_turb(2) = flux_turb(1) + ( fluxl(2) + fluxr(2) ) * area

  end function flux_turb

!================================ COMPUTE_CTG ================================80
!
! Compute temperature correction term to turbulent eddy viscosity
!
!=============================================================================80

  pure function compute_ctg ( n_grd, n_turb, n_tot, q, turb                    &
                            , gradx, grady, gradz )                            &
          result ( coeff )

    use fluid,         only : gm1, gamma
    use turb_ke_const, only : cmu_ke
    use info_depr,     only : xmach, Re

    real(dp)                                  :: coeff
    integer,                       intent(in) :: n_grd
    integer,                       intent(in) :: n_turb
    integer,                       intent(in) :: n_tot
    real(dp),  dimension(n_tot),   intent(in) :: q
    real(dp),  dimension(n_turb),  intent(in) :: turb
    real(dp),  dimension(n_grd),   intent(in) :: gradx
    real(dp),  dimension(n_grd),   intent(in) :: grady
    real(dp),  dimension(n_grd),   intent(in) :: gradz

    real(dp), parameter :: epsilon0   = 1.0e-20_dp
    real(dp), parameter :: zero       = 0.0_dp
    real(dp), parameter :: half       = 0.5_dp
    real(dp), parameter :: one        = 1.0_dp
    real(dp), parameter :: two        = 2.0_dp
    real(dp), parameter :: five       = 5.0_dp

    real(dp) :: ctg
    real(dp) :: r_gas, r_gasinv
    real(dp) :: rho, rhoinv, u, v, w, umag2, ainv, e, press, temp, a
    real(dp) :: tinv, pinv, mach
    real(dp) :: dtdx, dtdy, dtdz
    real(dp) :: dmdx, dmdy, dmdz
    real(dp) :: fact, tt
    real(dp) :: dttdx, dttdy, dttdz
    real(dp) :: gradtt_mag
    real(dp) :: tke, dissp
    real(dp) :: f_turb_mach, turb_mach_0, turb_mach, zeman_lag
    real(dp) :: heaviside

  continue

    r_gas      = one / gamma
    r_gasinv   = gamma
!FIXME - account for primitive variable function call
    rho    = q(1)
    rhoinv = one /rho
    u      = q(2) * rhoinv
    v      = q(3) * rhoinv
    w      = q(4) * rhoinv
    umag2  = u*u + v*v + w*w
    e      = q(5)
    press  = gm1 * (e-half*rho*umag2)
    pinv   = one / press
    temp   = press * rhoinv * r_gasinv
    tinv   = one /  temp
    a      = sqrt( gamma * r_gas * temp )
    mach   = sqrt(umag2) / a
    ainv   = one / a
!write(*,'(a,10(1x,es12.4))') 'q',rho,u,v,w,e,press,temp,mach

    dtdx = temp * ( rhoinv*gradx(1) + pinv*gradx(5) )
    dtdy = temp * ( rhoinv*grady(1) + pinv*grady(5) )
    dtdz = temp * ( rhoinv*gradz(1) + pinv*gradz(5) )
!write(*,'(a,10(1x,es12.4))') 't',dtdx,dtdy,dtdz

    dmdx = mach*tinv*dtdx + ainv*( gradx(2) +gradx(3) +gradx(4) )
    dmdy = mach*tinv*dtdy + ainv*( grady(2) +grady(3) +grady(4) )
    dmdz = mach*tinv*dtdz + ainv*( gradz(2) +gradz(3) +gradz(4) )
!write(*,'(a,10(1x,es12.4))') 'm',dmdx,dmdy,dmdz

    fact = one + half * gm1 * mach * mach
    tt   = temp * fact

    dttdx      = gm1 * temp * mach * dmdx + fact * dtdx
    dttdy      = gm1 * temp * mach * dmdy + fact * dtdy
    dttdz      = gm1 * temp * mach * dmdz + fact * dtdz
!write(*,'(a,10(1x,es12.4))') 'tt',dttdx,dttdy,dttdz

    gradtt_mag = sqrt(dttdx*dttdx +dttdy*dttdy +dttdz*dttdz)

    tke   = turb(1)
    dissp = turb(2)
    ctg   = ( gradtt_mag / tt ) * ( sqrt(tke) / ( cmu_ke * dissp ) ) &
          * ( xmach / re )

    turb_mach_0 = 0.1_dp
    turb_mach   = sqrt(two * rho * tke /(gamma*press))
    zeman_lag   = turb_mach - turb_mach_0
    heaviside   = max(zeman_lag+epsilon0,zero) / (abs(zeman_lag)+epsilon0)
    f_turb_mach = (turb_mach**2 - turb_mach_0**2) * heaviside

    coeff = one + ctg**3/(0.041_dp + f_turb_mach)
!if ( coeff > 5.0 ) then
!write(*,'(a,12(1x,f12.4))') 'ctg',ctg, coeff, gradtt_mag &
!  , rho, u, v, w, e, press, temp, mach
!endif

! limit the upper bounds of the coefficient
    coeff = min(coeff, five)

  end function compute_ctg

!============================ BC_KEPS_SET_WALLS ==============================80
!
!  Sets quantities on viscous walls for ke
!
!=============================================================================80

  subroutine bc_ke_set_walls( eqn_set,                                        &
                                nnodes0, nnodes01, nbnode, ibnode,            &
                                turb, qnode, n_turb, n_tot,                   &
                                nedgeloc, nedgeloc_2d, nnodes0_2d,            &
                                node_pairs_2d, eptr,                          &
                                x, y, z, xn, yn, zn, ra, vol,                 &
                                nbound, bc, bxn, byn, bzn, elem, nelem )

    use fluid,          only : gamma, sutherland_constant
    use info_depr,      only : tref, xmach, Re, skeleton
    use solution_types, only : compressible, incompressible
    use turb_ke_const,  only : ke_k_inf, ke_mut_inf, cmu_ke
    use bc_types,       only : bcgrid_type
    use element_types,  only : elem_type

    integer,                                      intent(in)    :: nbnode
    integer,                                      intent(in)    :: n_turb
    integer,                                      intent(in)    :: n_tot
    integer,                                      intent(in)    :: eqn_set
    integer,                                      intent(in)    :: nelem
    integer,                                      intent(in)    :: nnodes0
    integer,                                      intent(in)    :: nnodes01
    integer,                                      intent(in)    :: nedgeloc_2d
    integer,                                      intent(in)    :: nnodes0_2d
    integer,          dimension(nbnode),          intent(in)    :: ibnode
    real(dp),         dimension(n_turb,nnodes01), intent(inout) :: turb
    real(dp),         dimension(n_tot,nnodes01),  intent(in)    :: qnode
    integer,                                      intent(in)    :: nedgeloc
    integer,          dimension(2,nedgeloc),      intent(in)    :: eptr
    integer,          dimension(2,nnodes0_2d),    intent(in)    :: node_pairs_2d
    real(dp),         dimension(nnodes01)                       :: gradx_sqrtk
    real(dp),         dimension(nnodes01)                       :: grady_sqrtk
    real(dp),         dimension(nnodes01)                       :: gradz_sqrtk
    real(dp),         dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp),         dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp),         dimension(nedgeloc),        intent(in)    :: ra
    real(dp),         dimension(nnodes01),        intent(in)    :: vol
    real(dp),         dimension(nbnode),          intent(in)    :: bxn,byn,bzn
    integer,                                      intent(in)    :: nbound
    type(bcgrid_type),dimension(nbound),          intent(in)    :: bc
    type(elem_type),  dimension(nelem),           intent(in)    :: elem
!
    integer :: i,inode

    real(dp)    :: rho, p
    real(dp)    :: temp, rnu, cstar
    real(dp)    :: my_xmach, xmr

    real(dp)    :: xnorm, ynorm, znorm, area
    real(dp)    :: gradn_sqrtk
    real(dp)    :: varepsilon_bc, varepsilon_inf

    real(dp), parameter       :: my_1   =    1._dp

    continue

    varepsilon_inf = cmu_ke * ke_k_inf * ke_k_inf / ke_mut_inf
    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = my_1
    case default
      call lmpi_conditional_stop(1,'bc_ke_set_walls: only for in/comprss pg')
    end select

    xmr = my_xmach/Re

    call ke_grad_sqrt_k(nnodes0,nnodes01,nedgeloc,nedgeloc_2d,nnodes0_2d,      &
                              node_pairs_2d,eptr,turb,x,y,z,                   &
                              xn,yn,zn,ra,vol,nbound,bc, n_turb, elem, nelem,  &
                              gradx_sqrtk,grady_sqrtk,gradz_sqrtk)
    do i = 1,nbnode
      inode = ibnode(i)
      if (inode <= nnodes0) then

          xnorm      = bxn(i)
          ynorm      = byn(i)
          znorm      = bzn(i)
          area       = sqrt(xnorm * xnorm + ynorm * ynorm + znorm * znorm)
          xnorm      = xnorm / area
          ynorm      = ynorm / area
          znorm      = znorm / area

        if ( eqn_set == compressible ) then
          rho  = qnode(1,inode)
          p    = qnode(5,inode)
          temp = gamma * p / rho
          rnu  = viscosity_law( cstar, temp ) / rho
        else
          rnu  = my_1
        end if
        turb(1,inode) = 0.0_dp
!    Chen's model
!       turb(2,inode) = 0.0_dp
!    Abid's model
        gradn_sqrtk = gradx_sqrtk(inode) * xnorm +                             &
                      grady_sqrtk(inode) * ynorm +                             &
                      gradz_sqrtk(inode) * znorm
        varepsilon_bc =  2.0_dp*rnu*gradn_sqrtk*gradn_sqrtk*xmr*xmr
        if ( varepsilon_bc > varepsilon_inf ) then
          turb(2,inode) = varepsilon_bc
        else
          turb(2,inode) = varepsilon_inf
        endif
        if ( skeleton > 15 )                                                   &
        write(*,'(a,5(1x,f20.10))') 'kewallkewall', turb(2,inode), rnu         &
       , gradx_sqrtk(inode) , grady_sqrtk(inode) , gradz_sqrtk(inode)
      end if
    end do

  end subroutine bc_ke_set_walls

!============================== KEPS_GRAD_SQRT_K =============================80
!
! Calculates the gradients of k,e for turbulence model using Green-Gauss
! Note : this routine can definitely be renamed and generalized for turbulence
!
!=============================================================================80

  subroutine ke_grad_sqrt_k(nnodes0,nnodes01,nedgeloc,nedgeloc_2d,nnodes0_2d,  &
                              node_pairs_2d,eptr,turb,x,y,z,                   &
                              xn,yn,zn,ra,vol,nbound,bc, n_turb, elem, nelem,  &
                              gradx_sqrtk,grady_sqrtk,gradz_sqrtk)

    use bc_types,      only : bcgrid_type
    use info_depr,     only : twod
    use bc_names,      only : bc_ignore_2d
    use element_types, only : elem_type

    integer, intent(in) :: n_turb, nedgeloc_2d, nnodes0_2d, nelem
    integer,                                 intent(in)    :: nnodes0, nnodes01
    integer,                                 intent(in)    :: nedgeloc
    integer,      dimension(2,nedgeloc),     intent(in)    :: eptr
    integer,      dimension(2,nnodes0_2d),   intent(in)    :: node_pairs_2d

    real(dp),     dimension(n_turb,nnodes01),intent(in)    :: turb

    real(dp),     dimension(nnodes01),       intent(out)   :: gradx_sqrtk
    real(dp),     dimension(nnodes01),       intent(out)   :: grady_sqrtk
    real(dp),     dimension(nnodes01),       intent(out)   :: gradz_sqrtk

    real(dp),     dimension(nnodes01),       intent(in)    :: x, y, z
    real(dp),     dimension(nedgeloc),       intent(in)    :: xn, yn, zn, ra
    real(dp),     dimension(nnodes01),       intent(in)    :: vol

    integer,                                 intent(in)    :: nbound
    type(bcgrid_type),dimension(nbound),     intent(in)    :: bc
    type(elem_type),  dimension(nelem),      intent(in)    :: elem

    integer     :: i,ib,n,node1,node2

    real(dp) :: area,q3,xnorm,ynorm,znorm

    real(dp), parameter :: my_0   =  0._dp
    real(dp), parameter :: my_half = 0.5_dp

    continue

! zero out the gradients

    do n = 1,nnodes01

      gradx_sqrtk(n) = my_0
      grady_sqrtk(n) = my_0
      gradz_sqrtk(n) = my_0

    end do

    twod_mode : if (twod) then

      edge_loop_2d : do n = 1, nedgeloc_2d

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        area  = ra(n)
        xnorm = area * xn(n)
        znorm = area * zn(n)

        q3 = my_half * (turb(1,node2)**my_half+turb(1,node1)**my_half)

        if (node1 <= nnodes0) then
          gradx_sqrtk(node1) = gradx_sqrtk(node1) + xnorm*q3
          gradz_sqrtk(node1) = gradz_sqrtk(node1) + znorm*q3
        end if

        if (node2 <= nnodes0) then
          gradx_sqrtk(node2) = gradx_sqrtk(node2) - xnorm*q3
          gradz_sqrtk(node2) = gradz_sqrtk(node2) - znorm*q3
        end if

      end do edge_loop_2d

    else twod_mode

      edge_loop : do n = 1, nedgeloc
        node1 = eptr(1,n)
        node2 = eptr(2,n)

        area = ra(n)
        xnorm = area * xn(n)
        ynorm = area * yn(n)
        znorm = area * zn(n)

        q3 = my_half * (turb(1,node2)**my_half+turb(1,node1)**my_half)

        if (node1 <= nnodes0) then

          gradx_sqrtk(node1) = gradx_sqrtk(node1) + xnorm*q3
          grady_sqrtk(node1) = grady_sqrtk(node1) + ynorm*q3
          gradz_sqrtk(node1) = gradz_sqrtk(node1) + znorm*q3

        end if

        if (node2 <= nnodes0) then

          gradx_sqrtk(node2) = gradx_sqrtk(node2) - xnorm*q3
          grady_sqrtk(node2) = grady_sqrtk(node2) - ynorm*q3
          gradz_sqrtk(node2) = gradz_sqrtk(node2) - znorm*q3

        end if

      end do edge_loop

    endif twod_mode

    do ib = 1,nbound

      if (twod .and. bc_ignore_2d(bc(ib)%ibc)) cycle

      call bc_ke_grad_sqrt_k(nnodes0,nnodes01,turb,x,y,z,                      &
                      bc(ib)%nbnode,bc(ib)%ibnode,bc(ib)%nbfacet,              &
                      bc(ib)%nbfaceq,                                          &
                      bc(ib)%f2ntb, n_turb, bc(ib)%f2nqb, elem, nelem,         &
                      gradx_sqrtk,grady_sqrtk,gradz_sqrtk)
    end do

! correct gradients for symmetry conditions

    do ib = 1,nbound
      call sqrtk_grad_symmetry(bc(ib)%ibc, bc(ib)%nbnode, bc(ib)%ibnode,       &
                                nnodes0, nnodes01, gradx_sqrtk,                &
                                grady_sqrtk, gradz_sqrtk)
    end do

    if (twod) then

      do i = 1,nnodes0_2d

        node1 = node_pairs_2d(1,i)
        node2 = node_pairs_2d(2,i)

        gradx_sqrtk(node1) = gradx_sqrtk(node1) / vol(node1)
        gradz_sqrtk(node1) = gradz_sqrtk(node1) / vol(node1)

        gradx_sqrtk(node2) = gradx_sqrtk(node1)
        gradz_sqrtk(node2) = gradz_sqrtk(node1)

      end do

    else

      do i = 1,nnodes0

        gradx_sqrtk(i) = gradx_sqrtk(i) / vol(i)
        grady_sqrtk(i) = grady_sqrtk(i) / vol(i)
        gradz_sqrtk(i) = gradz_sqrtk(i) / vol(i)

      end do

    end if

  end subroutine ke_grad_sqrt_k


!=============================== BC_KEPS_GRAD_SQRT_K =========================80
!
! This routine closes off the boundaries for the Green-Gauss gradients
!
!=============================================================================80

  subroutine bc_ke_grad_sqrt_k(nnodes0,nnodes01,turb,x,y,z,nbnode,             &
                                 ibnode,nbfacet,nbfaceq,                       &
                                 f2ntb,n_turb,f2nqb,elem,nelem,                &
                                 gradx_sqrtk,grady_sqrtk,gradz_sqrtk)

    use info_depr,     only : twod
    use element_types, only : elem_type
    use twod_util,     only : yplane_2d, y_coplanar_tol
    use grid_metrics,  only : dual_area_quad
    use nml_noninertial_reference_frame, only : noninertial

    integer,                                intent(in)    :: nnodes0,nnodes01
    integer,                                intent(in)    :: nbfacet,nbnode
    integer,                                intent(in)    :: nbfaceq
    integer,                                intent(in)    :: n_turb,nelem

    integer,     dimension(nbnode),         intent(in)    :: ibnode
    integer,     dimension(nbfacet,5),      intent(in)    :: f2ntb
    integer,     dimension(nbfaceq,6),      intent(in)    :: f2nqb

    real(dp),    dimension(nnodes01),       intent(in)    :: x,y,z
    real(dp),    dimension(n_turb,nnodes01),intent(in)    :: turb

    real(dp),    dimension(nnodes01),       intent(inout) :: gradx_sqrtk
    real(dp),    dimension(nnodes01),       intent(inout) :: grady_sqrtk
    real(dp),    dimension(nnodes01),       intent(inout) :: gradz_sqrtk

    integer                                               :: n, ielem
    integer                                               :: node1,node2,node3
    integer                                               :: node4,neighbor

    real(dp)                                           :: ax,ay,az
    real(dp)                                           :: bx,by,bz
    real(dp)                                           :: x1,x2,x3,xnorm
    real(dp)                                           :: y1,y2,y3,ynorm
    real(dp)                                           :: z1,z2,z3,znorm
    real(dp)                                           :: c68, c18, c56, c16

    real(dp)                                           :: q3
    real(dp)                                           :: sqrtk1
    real(dp)                                           :: sqrtk2
    real(dp)                                           :: sqrtk3
    real(dp)                                           :: sqrtk4

    real(dp), dimension(4) :: xnorm_q, ynorm_q, znorm_q

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_3   = 3.0_dp
    real(dp), parameter :: my_5   = 5.0_dp
    real(dp), parameter :: my_6   = 6.0_dp
    real(dp), parameter :: my_8   = 8.0_dp
    real(dp), parameter :: my_half = 0.5_dp

    type(elem_type), dimension(nelem), intent(in) :: elem

    continue

    twod_mode : if (twod) then

      c68 = 1.0_dp
      c18 = 0.0_dp

!     close off gradient evaluation on triangular faces
!    (should never get here in 2D...)

      loop_tris_1 : do n = 1, nbfacet

        write(*,*) 'bc_ke_grad_sqrt_k: should not have hit triangular'
        write(*,*) 'boundary faces!'
        call lmpi_conditional_stop(1)

      end do loop_tris_1

!     close off gradient evaluation on quadralateral faces

      loop_quads_1 : do n = 1, nbfaceq

        ielem = f2nqb(n,6)

        if (elem(ielem)%type_cell == 'prz' .and. twod) then
          c56 = my_5/my_6
        else
          c56 = my_1
        endif
        c16 = my_1 - c56

        node1 = ibnode(f2nqb(n,1))
        node2 = ibnode(f2nqb(n,2))
        node3 = ibnode(f2nqb(n,3))
        node4 = ibnode(f2nqb(n,4))

!       get dual norm contributions at each node of the quad face

        call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,noninertial,&
                            xnorm_q,ynorm_q,znorm_q)

        if (node1 <= nnodes0) then

          xnorm = xnorm_q(1)
          znorm = znorm_q(1)

!         find neighbor of node 1

          neighbor = node2
          if(abs(y(node3)-yplane_2d) < y_coplanar_tol) neighbor = node3
          if(abs(y(node4)-yplane_2d) < y_coplanar_tol) neighbor = node4

          q3 = c56*turb(1,node1)**my_half + c16*turb(1,neighbor)**my_half

          gradx_sqrtk(node1) = gradx_sqrtk(node1) + xnorm*q3
          gradz_sqrtk(node1) = gradz_sqrtk(node1) + znorm*q3

        end if

        if (node2 <= nnodes0) then

          xnorm = xnorm_q(2)
          znorm = znorm_q(2)

!         find neighbor of node 2

          neighbor = node3
          if(abs(y(node1)-yplane_2d) < y_coplanar_tol) neighbor = node1
          if(abs(y(node4)-yplane_2d) < y_coplanar_tol) neighbor = node4

          q3 = c56*turb(1,node2)**my_half + c16*turb(1,neighbor)**my_half

          gradx_sqrtk(node2) = gradx_sqrtk(node2) + xnorm*q3
          gradz_sqrtk(node2) = gradz_sqrtk(node2) + znorm*q3

        end if

        if (node3 <= nnodes0) then

          xnorm = xnorm_q(3)
          znorm = znorm_q(3)

!         find neighbor of node 3

          neighbor = node4
          if(abs(y(node1)-yplane_2d) < y_coplanar_tol) neighbor = node1
          if(abs(y(node2)-yplane_2d) < y_coplanar_tol) neighbor = node2

          q3 = c56*turb(1,node3)**my_half + c16*turb(1,neighbor)**my_half

          gradx_sqrtk(node3) = gradx_sqrtk(node3) + xnorm*q3
          gradz_sqrtk(node3) = gradz_sqrtk(node3) + znorm*q3

        end if

        if (node4 <= nnodes0) then

          xnorm = xnorm_q(4)
          znorm = znorm_q(4)

!         find neighbor of node 4

          neighbor = node1
          if(abs(y(node2)-yplane_2d) < y_coplanar_tol) neighbor = node2
          if(abs(y(node3)-yplane_2d) < y_coplanar_tol) neighbor = node3

          q3 = c56*turb(1,node4)**my_half + c16*turb(1,neighbor)**my_half

          gradx_sqrtk(node4) = gradx_sqrtk(node4) + xnorm*q3
          gradz_sqrtk(node4) = gradz_sqrtk(node4) + znorm*q3

        end if

      end do loop_quads_1

    else twod_mode

!     close off gradient evaluation on triangular faces

      loop_tris: do n = 1, nbfacet

        ielem = f2ntb(n,5)  ! index to type of element attached to this face

        c68 = my_1
        c18 = my_0

        if (elem(ielem)%type_cell == 'tet') then
!         for linear function preservation during flux closure - tets only
          c68 = my_6/my_8
          c18 = my_1/my_8
        end if

        node1 = ibnode(f2ntb(n,1))
        node2 = ibnode(f2ntb(n,2))
        node3 = ibnode(f2ntb(n,3))

        x1 = x(node1)
        y1 = y(node1)
        z1 = z(node1)
        sqrtk1 = turb(1,node1)**my_half

        x2 = x(node2)
        y2 = y(node2)
        z2 = z(node2)
        sqrtk2 = turb(1,node2)**my_half

        x3 = x(node3)
        y3 = y(node3)
        z3 = z(node3)
        sqrtk3 = turb(1,node3)**my_half

        ax = x2 - x1
        ay = y2 - y1
        az = z2 - z1

        bx = x3 - x1
        by = y3 - y1
        bz = z3 - z1

!           norm points away from grid interior
!           norm magnitude is 1/3 of surface triangle area

        xnorm = -my_half*(ay*bz-by*az)/my_3
        ynorm =  my_half*(ax*bz-bx*az)/my_3
        znorm = -my_half*(ax*by-bx*ay)/my_3

        q3 = c68*sqrtk1 + c18*(sqrtk2+sqrtk3)

        if (node1 <= nnodes0) then

          gradx_sqrtk(node1) = gradx_sqrtk(node1) + xnorm*q3
          grady_sqrtk(node1) = grady_sqrtk(node1) + ynorm*q3
          gradz_sqrtk(node1) = gradz_sqrtk(node1) + znorm*q3

        end if

        q3 = c68*sqrtk2 + c18*(sqrtk1+sqrtk3)

        if (node2 <= nnodes0) then

          gradx_sqrtk(node2) = gradx_sqrtk(node2) + xnorm*q3
          grady_sqrtk(node2) = grady_sqrtk(node2) + ynorm*q3
          gradz_sqrtk(node2) = gradz_sqrtk(node2) + znorm*q3

        end if

        q3 = c68*sqrtk3 + c18*(sqrtk1+sqrtk2)

        if (node3 <= nnodes0) then

          gradx_sqrtk(node3) = gradx_sqrtk(node3) + xnorm*q3
          grady_sqrtk(node3) = grady_sqrtk(node3) + ynorm*q3
          gradz_sqrtk(node3) = gradz_sqrtk(node3) + znorm*q3

        end if
      end do loop_tris

!     close off gradient evaluation on quadralateral faces

      c68 = my_1
      c18 = my_0

      loop_quads_2 : do n = 1, nbfaceq

        node1 = ibnode(f2nqb(n,1))
        node2 = ibnode(f2nqb(n,2))
        node3 = ibnode(f2nqb(n,3))
        node4 = ibnode(f2nqb(n,4))

        sqrtk1 = turb(1,node1)**my_half
        sqrtk2 = turb(1,node2)**my_half
        sqrtk3 = turb(1,node3)**my_half
        sqrtk4 = turb(1,node4)**my_half

!       get dual norm contributions at each node of the quad face

        call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,noninertial,&
                            xnorm_q,ynorm_q,znorm_q)

        q3 = sqrtk1

        xnorm = xnorm_q(1)
        ynorm = ynorm_q(1)
        znorm = znorm_q(1)

        if (node1 <= nnodes0) then
          gradx_sqrtk(node1) = gradx_sqrtk(node1) + xnorm*q3
          grady_sqrtk(node1) = grady_sqrtk(node1) + ynorm*q3
          gradz_sqrtk(node1) = gradz_sqrtk(node1) + znorm*q3
        end if

        q3 = sqrtk2

        xnorm = xnorm_q(2)
        ynorm = ynorm_q(2)
        znorm = znorm_q(2)

        if (node2 <= nnodes0) then
          gradx_sqrtk(node2) = gradx_sqrtk(node2) + xnorm*q3
          grady_sqrtk(node2) = grady_sqrtk(node2) + ynorm*q3
          gradz_sqrtk(node2) = gradz_sqrtk(node2) + znorm*q3
        end if

        q3 = sqrtk3

        xnorm = xnorm_q(3)
        ynorm = ynorm_q(3)
        znorm = znorm_q(3)

        if (node3 <= nnodes0) then
          gradx_sqrtk(node3) = gradx_sqrtk(node3) + xnorm*q3
          grady_sqrtk(node3) = grady_sqrtk(node3) + ynorm*q3
          gradz_sqrtk(node3) = gradz_sqrtk(node3) + znorm*q3
        end if

        q3 = sqrtk4

        xnorm = xnorm_q(4)
        ynorm = ynorm_q(4)
        znorm = znorm_q(4)

        if (node4 <= nnodes0) then
          gradx_sqrtk(node4) = gradx_sqrtk(node4) + xnorm*q3
          grady_sqrtk(node4) = grady_sqrtk(node4) + ynorm*q3
          gradz_sqrtk(node4) = gradz_sqrtk(node4) + znorm*q3
        end if

      end do loop_quads_2

    end if twod_mode

  end subroutine bc_ke_grad_sqrt_k

!=============================== SQRTK_GRAD_SYMMETRY =========================80
!
! Set known gradients at symmetry planes.
! For example, at symmetry_y:
!   y-gradients of sqrt(k) is zero, e.g. sqrtk_y = 0.
!
!=============================================================================80
  subroutine sqrtk_grad_symmetry(ibc, nbnode, ibnode, nnodes0, nnodes01,       &
                                  gradx_sqrtk, grady_sqrtk, gradz_sqrtk)

    use bc_names,  only : symmetry_x       , symmetry_1_strong,                &
                          symmetry_y       , symmetry_2_strong,                &
                          symmetry_z       , symmetry_3_strong

    integer, intent(in) :: ibc, nbnode, nnodes0, nnodes01

    integer, dimension(nbnode), intent(in) :: ibnode

    real(dp), dimension(nnodes01),   intent(inout) :: gradx_sqrtk,             &
                                                      grady_sqrtk,             &
                                                      gradz_sqrtk

    integer :: n, node

    real(dp), parameter    :: my_0 = 0.0_dp

  continue

    select case (ibc)

      case (symmetry_x)

        do n = 1,nbnode
          node = ibnode(n)
          if(node <= nnodes0) then
            gradx_sqrtk(node) = my_0
          end if
        end do

      case (symmetry_y)

        do n = 1,nbnode
          node = ibnode(n)
          if(node <= nnodes0) then
            grady_sqrtk(node) = my_0
          end if
        end do

      case (symmetry_z)

        do n = 1,nbnode
          node = ibnode(n)
          if(node <= nnodes0) then
            gradz_sqrtk(node) = my_0
          end if
        end do

      case (symmetry_1_strong, symmetry_2_strong, symmetry_3_strong)
        write(*,*) 'symmetry_1_strong, symmetry_2_strong, symmetry_3_strong '
        write(*,*) 'not taken into account in sqrtk_grad_symmetry.'
        call lmpi_conditional_stop(1)

    end select

  end subroutine sqrtk_grad_symmetry

!========================== E_COMPRESS =======================================80
!
! Function for compressibility correction in k equation
!
!=============================================================================80
  pure function e_compress( rho, dissp, prodk, turb_mach                  &
                            , turb_compress_model )

    real(dp)                      :: e_compress
    real(dp),          intent(in) :: rho
    real(dp),          intent(in) :: dissp
    real(dp),          intent(in) :: prodk
    real(dp),          intent(in) :: turb_mach
    character(len=40), intent(in) :: turb_compress_model

! local

    real(dp)            :: lambda
    real(dp)            :: alpha1
    real(dp)            :: alpha2
    real(dp)            :: tilde_turb_mach

  continue

      e_compress = 0.0_dp

      select case ( turb_compress_model )

      case ( 'off' )

! No compressibility correction
        e_compress = 0.0_dp

!     case ( 1 )
!       turb_mach_0 = 0.25d+0
! Wilcox compressibility correction
! (Wilcox "Turbulence Modeling for CFD, Ed 2, p. 244)
!       if (f2 < 0.99_dp) then
!         zeman_lag   = turb_mach - turb_mach_0
!         zeman_lag2  = turb_mach*turb_mach - turb_mach_0*turb_mach_0
!         heaviside   = max(zeman_lag+epsilon0,my_0) /                         &
!                       (abs(zeman_lag)+epsilon0)
!         f_turb_mach = 1.0_dp + (zeman_lag2)*heaviside/2.0_dp
!         f_turb_mach = min(1.0_dp, max(my_0, f_turb_mach))
!       else
!         f_turb_mach = 1.0_dp
!       end if

      case ( 'sarkar' )

! limiter
!         e_compress      = min( e_compress, 20.0_dp * pk )

          lambda          = 0.2d+0
          alpha1          = 0.0d+0 !  2.5d+0
          alpha2          = 2.0d+0
          tilde_turb_mach = max(turb_mach - lambda, 0.0_dp )
          e_compress      = tilde_turb_mach * tilde_turb_mach                  &
                            * ( alpha1 * prodk + alpha2 * rho * dissp )
      case ( 'sarkar2' )

          lambda          = 0.0d+0
          alpha1          = 1.0d+0
          alpha2          = 0.4d+0
!         alpha3          = 0.2d+0
          tilde_turb_mach = max(turb_mach - lambda, 0.0_dp )
          e_compress      = tilde_turb_mach * tilde_turb_mach                  &
                            * ( alpha2 * prodk                                 &
                              + ( alpha1 - alpha2 ) * rho * dissp )
      case ( 'wilcox' )

!         turb_mach_0 = 0.25d+0
!         zeman_lag2  = turb_mach*turb_mach - turb_mach_0*turb_mach_0
!         f_turb_mach = 1.5d+0 * max(zeman_lag2, 0.0d+0)

      case default

        e_compress = 0.0_dp

      end select

  end function e_compress

  include 'get_sij.f90'
  include 'viscosity_law.f90'

end module turb_ke

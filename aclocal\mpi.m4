# -*- Autoconf -*-
# autoconf macros for detecting MPI installation
#
# NOTE: This macro overrides the $(FC) and $(CC) output variable 
#         if MPI is detected!
#
# Assigned Shell Variables:
#   $with_mpi        Path to MPI installation (may be 'no')
#   $mpifc           MPI Fortran compiler
#
# Assigned Output Variables:
#   @MPIINC@         MPI include directory (full path)
#   @MPIF90@         MPI Fortran compiler wrapper (full path)
#   @MPIRUN@         MPI Execution script (full path)
#   @MPI_Prefix@     MPI Installation path
#
# Assigned AM_CONDITIONALS:
#   BUILD_MPI

AC_DEFUN([MPI_LIB_PATH],[

AC_ARG_WITH(mpi,
        [[  --with-mpi[=ARG]        Path to MPI library installation [ARG=no]]],
        [with_mpi="$withval"],      [with_mpi="no"] )
AC_ARG_WITH(mpif90,
        [[  --with-mpif90[=ARG]     MPI Fortran compiler wrapper [mpif90]]],
        [with_mpif90="$withval"],   [with_mpif90="mpif90"] )
AC_ARG_WITH(mpicc,
        [[  --with-mpicc[=ARG]      MPI C compiler wrapper [mpicc]]],
        [with_mpicc="$withval"],    [with_mpicc="mpicc"] )
AC_ARG_WITH(mpiexec,
        [[  --with-mpiexec[=ARG]    MPI execution script [mpiexec]]],
        [with_mpiexec="$withval"],  [with_mpiexec="mpiexec"] )
AC_ARG_WITH(mpibin,
        [[  --with-mpibin[=ARG]     MPI bin directory [bin]]],
        [with_mpibin="$withval"],   [with_mpibin="bin"] )
AC_ARG_WITH(mpiinc,
        [[  --with-mpiinc[=ARG]     MPI include directory for mpif.h [include]]],
        [with_mpiinc="$withval"],   [with_mpiinc="include"] )

AC_DEFINE_UNQUOTED([MPI_PATH],["$with_mpi"],[MPI installation])

if test "$with_mpi" != 'no'
then
  AC_MSG_NOTICE([checking MPI installation])

  if test "$with_mpiinc" != 'no'
  then
    if test `echo $with_mpiinc | grep /`
    then
      MPIINC="$with_mpiinc"
    else
      MPIINC="$with_mpi/$with_mpiinc"
    fi

#    AC_CHECK_FILE([$MPIINC/mpif.h],
#                  [],AC_MSG_ERROR([mpif.h not found]))
  else
    MPIINC="."
  fi
  AC_SUBST([MPIINC])

  if test "$with_mpibin" != 'no'
  then
    if test `echo $with_mpibin | grep /`
    then
      MPIBIN="$with_mpibin"
    else
      MPIBIN="$with_mpi/$with_mpibin"
    fi
  fi

  if test "$with_mpiexec" != 'no'
  then
    if test `echo $with_mpiexec | grep /`
    then
      MPIRUN="$with_mpiexec"
    else
      MPIRUN="$MPIBIN/$with_mpiexec"
    fi

    if eval "test ! -e $MPIRUN"
    then
      AC_MSG_ERROR([$MPIRUN not found])
    fi
    AC_SUBST([MPIRUN])
  fi

  if test "$with_mpif90" != 'no'
  then
    AC_CHECKING([mpi Fortran compiler wrapper])

    if test `echo $with_mpif90 | grep /`
    then
      MPIF90="$with_mpif90"
    else
      MPIF90="$MPIBIN/$with_mpif90"
    fi

    AC_MSG_RESULT([  $MPIF90])
    if eval "test -e $MPIF90"
    then
      FC=$MPIF90
    else
      AC_MSG_ERROR([MPI Fortran compiler wrapper does not exist])
    fi
  fi

  if test "$with_mpicc" != 'no'
  then
    AC_CHECKING([mpi C compiler wrapper])

    if test `echo $with_mpicc | grep /`
    then
      MPICC="$with_mpicc"
    else
      MPICC="$MPIBIN/$with_mpicc"
    fi

    AC_MSG_RESULT([  $MPICC])
    if eval "test -e $MPICC"
    then
      CC=$MPICC
    else
      AC_MSG_ERROR([MPI C compiler wrapper does not exist])
    fi
  else
    # when wrapper is missing (i.e. sgi mpt) link with -lmpi
    mpi_ldadd=-lmpi
    AC_SUBST([mpi_ldadd])
  fi
 
  AM_CONDITIONAL(BUILD_MPI,true)
  AC_DEFINE([HAVE_MPI],[1],[MPI is available])
  AC_MSG_RESULT([  Using MPI installation: $with_mpi])

# if test "x$CXX" != "x"
# then
#   AC_LANG_PUSH(C++)
#   save_CXX="$CXX"
#   CXX="$with_mpi/$MPIBIN/mpicxx"
#   AC_COMPILE_IFELSE([AC_LANG_SOURCE([[]])], [cxxmpi=yes], [cxxmpi=no])
#   CXX="$save_CXX"
#   AC_LANG_POP(C++)
#
#   if test "x$cxxmpi" = xyes
#   then
#     MPI_CXX="$with_mpi/$MPIBIN/mpicxx"
#   else
#     AC_MSG_ERROR([mpicxx does not work])
#   fi
#   AC_SUBST([MPI_CXX])
# fi
#
#
# if test "x$F77" != "x"
# then
#   AC_LANG_PUSH(Fortran 77)
#   save_F77="$F77"
#   F77="$with_mpi/$MPIBIN/mpif77"
#   AC_COMPILE_IFELSE([AC_LANG_SOURCE([[      program check
#       stop
#       end]])],
#             [f77mpi=yes], [f77mpi=no])
#   F77="$save_F77"
#   AC_LANG_POP(Fortran 77)
#
#   if test "x$f77mpi" = xyes
#   then
#     MPI_F77="$with_mpi/$MPIBIN/mpif77"
#   else
#     AC_MSG_ERROR([mpif77 does not work])
#   fi
#   AC_SUBST([MPI_F77])
# fi
#

else
  AM_CONDITIONAL(BUILD_MPI,false)
fi

AC_DEFINE_UNQUOTED([MPI_RUN],["$MPIRUN"],[MPI job execution])

MPI_Prefix=$with_mpi
AC_SUBST([MPI_Prefix])
AC_SUBST([MPIF90])

])

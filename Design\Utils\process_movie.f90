program process_movie

! Small program to post-process a movie.tec to combine zones within
! each design step so that we can look at animations of an entire wing,
! nacelle, etc regardless of how many boundary groups these were composed
! of in the computation.
! This is necessary because Tecplot can only animate one zone at a time.
! This just concatenates raw zones into new combined zones - duplicate
! nodes at seams are NOT thrown out.

  implicit none

  integer :: i, j, k, ik
  integer :: nzones, ndatasets, nvariables, noutput_zones
  integer :: combined_nodes, combined_faces, offset, face
  integer :: node, zone, ivar, inode, iface, dataset
  integer :: node_contribution, face_contribution

  integer, parameter :: system_r8=selected_real_kind(15, 307)
  integer, parameter :: dp=system_r8

  integer, dimension(:,:), allocatable :: global_f2n

  real(dp), dimension(:,:), allocatable    :: global_variables

  character(len=80)  :: line
  character(len=120) :: variable_names

  type combine_type
    integer :: ncontributions
    integer, dimension(:), pointer :: zone_list
    character(len=80) :: name
  end type combine_type

  type zone_variables_type
    integer :: nnodes
    integer :: nfaces
    integer, dimension(:,:), pointer :: f2n
    real(dp), dimension(:,:), pointer    :: variables
  end type zone_variables_type

  type(combine_type),        dimension(:),   allocatable :: combine_info
  type(zone_variables_type), dimension(:,:), allocatable :: zone_variables

continue

! Get the number of zones, datasets, output combinations, etc

  open(67,file='process_movie.dat',status='old')
  rewind(67)

  read(67,*)  ! Header line
  read(67,*) ndatasets, nzones, nvariables, noutput_zones

  allocate(combine_info(noutput_zones))

  do i = 1, noutput_zones
    read(67,*) combine_info(i)%ncontributions, combine_info(i)%name
    allocate(combine_info(i)%zone_list(combine_info(i)%ncontributions))
    read(67,*) (combine_info(i)%zone_list(j),                                  &
                                            j=1, combine_info(i)%ncontributions)
  end do

  close(67)

  allocate(zone_variables(ndatasets,nzones))

! Now read the actual movie file

  open(67,file='movie.tec',status='old')
  rewind(67)

! Loop over the number of datasets and the number of zones
! within each one to get the variables in

  read_dataset : do i = 1, ndatasets
    write(*,*) 'Reading dataset ', i
    read(67,*)    ! zone title
    read(67,'(a120)') variable_names
    read_zone : do j = 1, nzones
      read(67,'(a80)') line

      call geti(line,'i',zone_variables(i,j)%nnodes)
      call geti(line,'j',zone_variables(i,j)%nfaces)

      allocate(zone_variables(i,j)%variables(nvariables,                       &
                                                    zone_variables(i,j)%nnodes))
      allocate(zone_variables(i,j)%f2n(zone_variables(i,j)%nfaces,4))

      read(67,*) ((zone_variables(i,j)%variables(ivar,inode),                  &
                                       inode=1,zone_variables(i,j)%nnodes),    &
                                       ivar=1,nvariables)
      do iface = 1, zone_variables(i,j)%nfaces
        read(67,*) zone_variables(i,j)%f2n(iface,1),    &
                   zone_variables(i,j)%f2n(iface,2),    &
                   zone_variables(i,j)%f2n(iface,3),    &
                   zone_variables(i,j)%f2n(iface,4)
      end do

    end do read_zone
  end do read_dataset

  close(67)

! Now that we have all of the raw data read in, combine it all into the
! desired zones and write it out

  open(67,file='processed_movie.tec')
  rewind(67)

  datasets : do dataset = 1, ndatasets
    write(*,*) 'Building merged dataset ', dataset
    outzones : do i = 1, noutput_zones
      combined_nodes = 0
      combined_faces = 0
      do j = 1, combine_info(i)%ncontributions
        zone = combine_info(i)%zone_list(j)
        node_contribution = zone_variables(1,zone)%nnodes
        face_contribution = zone_variables(1,zone)%nfaces
        combined_nodes = combined_nodes + node_contribution
        combined_faces = combined_faces + face_contribution
      end do

      allocate(global_variables(nvariables,combined_nodes))
      allocate(global_f2n(combined_faces,4))

      combined_nodes = 0
      combined_faces = 0
      offset = 0

      do j = 1, combine_info(i)%ncontributions
        zone = combine_info(i)%zone_list(j)
        do node = 1, zone_variables(dataset,zone)%nnodes
          combined_nodes = combined_nodes + 1
          do ivar = 1, nvariables
            global_variables(ivar,combined_nodes) =                            &
                               zone_variables(dataset,zone)%variables(ivar,node)
          end do
        end do
        do face = 1, zone_variables(dataset,zone)%nfaces
          combined_faces = combined_faces + 1
          global_f2n(combined_faces,1)=zone_variables(dataset,zone)%f2n(face,1)&
                                      + offset
          global_f2n(combined_faces,2)=zone_variables(dataset,zone)%f2n(face,2)&
                                      + offset
          global_f2n(combined_faces,3)=zone_variables(dataset,zone)%f2n(face,3)&
                                      + offset
          global_f2n(combined_faces,4)=zone_variables(dataset,zone)%f2n(face,4)&
                                      + offset
        end do
        offset = offset + zone_variables(dataset,zone)%nnodes
      end do

! Now just write this combined output zone to file

      write(67,*) 'title="postprocessed movie data"'
      write(67,*) trim(variable_names)
      write(67,*) 'zone t=',trim(combine_info(i)%name),', i = ',combined_nodes,&
                                         ', j = ', combined_faces, ', f=feblock'
      write(67,'(10e23.15)') ((global_variables(ivar,inode),                   &
                                                       inode=1,combined_nodes),&
                                                       ivar=1,nvariables)
      do ik = 1, combined_faces
        write(67,'(4i10)') global_f2n(ik,1),global_f2n(ik,2),                  &
                           global_f2n(ik,3),global_f2n(ik,4)
      end do

      deallocate(global_variables,global_f2n)

    end do outzones
  end do datasets

  close(67)

end program process_movie


!=================================== GETI ====================================80
!
!  Grab an integer out of a text string
!
!=============================================================================80
  subroutine geti(line,key,idata)

    integer, intent(out) :: idata

    character(len=80), intent(in) :: line
    character(len=1),  intent(in) :: key

    integer :: istart, iend, i, ifound, j, iloc, power, number

  continue

! Now lets see if we can find the data we want
! First, find the I= and the , and get the data in between

    istart = 1
    iend   = 1
    line_loop : do i = 1, 79
      if ( (line(i:i) == key) .and. (line(i+1:i+1) == '=') ) then

! Now look for the first comma

        ifound = 0
        do j = i+3, 80
           if( ifound /= 1 .and. ((line(j:j) == '0' ) .or.                     &
                                  (line(j:j) == '1')  .or.                     &
                                  (line(j:j) == '2')  .or.                     &
                                  (line(j:j) == '3')  .or.                     &
                                  (line(j:j) == '4')  .or.                     &
                                  (line(j:j) == '5')  .or.                     &
                                  (line(j:j) == '6')  .or.                     &
                                  (line(j:j) == '7')  .or.                     &
                                  (line(j:j) == '8')  .or.                     &
                                  (line(j:j) == '9')) ) then
             istart = j
             ifound = 1
           end if

           if ( line(j:j) == ',' ) then
             iend = j-1
             exit line_loop
           end if

        end do
      end if
    end do line_loop

! Now convert this into an integer
! We know we have iend - istart + 1

    iloc = iend
    idata = 0
    do i = istart, iend
      power = 10**(i - istart)
      if ( line(iloc:iloc) == '0' ) number = 0
      if ( line(iloc:iloc) == '1' ) number = 1
      if ( line(iloc:iloc) == '2' ) number = 2
      if ( line(iloc:iloc) == '3' ) number = 3
      if ( line(iloc:iloc) == '4' ) number = 4
      if ( line(iloc:iloc) == '5' ) number = 5
      if ( line(iloc:iloc) == '6' ) number = 6
      if ( line(iloc:iloc) == '7' ) number = 7
      if ( line(iloc:iloc) == '8' ) number = 8
      if ( line(iloc:iloc) == '9' ) number = 9
      idata = idata + number*power
      iloc = iloc - 1
    end do

  end subroutine geti

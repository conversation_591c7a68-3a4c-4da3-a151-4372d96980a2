include ../Common.am

LIBCORE_DIR=@libcore_path@/Complex
LIBDEFS_DIR=@top_builddir@/libdefs/Complex
LIBTURB_DIR=@libturb_path@/Complex
LIBSMEMRD_DIR=@top_builddir@/libsmemrd/Complex
LIBDDFB_DIR=@top_srcdir@/libddfb
FUNCLIB_DIR=@top_builddir@/FuncLib90/Complex
PHYSICS_DIR=@top_builddir@/@PHYSICS_TYPE@/Complex
LIBINIT_DIR=@top_builddir@/libinit/Complex

libsink_SRCS += complex_oldschool.f90

noinst_LIBRARIES = libsink.a

nodist_libsink_a_SOURCES = $(libsink_SRCS)

BUILT_SOURCES += \
	$(nodist_libsink_a_SOURCES)

%.f90: $(top_srcdir)/LibF90/%.f90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.F90: $(top_srcdir)/LibF90/%.F90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

#Build Fortran dependencies
%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-L $(FUNCLIB_DIR) \
	-L $(LIBTURB_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-L $(FUNCLIB_DIR) \
	-L $(LIBTURB_DIR) > $@


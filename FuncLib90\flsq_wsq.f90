!================================= FLSQ_WSQ ==================================80
!
! Weights squared using face-based-lsq and inverse-distance weighting.
!
!=============================================================================80

  pure function flsq_wsq(flsq_n, cell1, cell2,                                 &
                         ncell01, xc, yc, zc, flsq_ja )

    use lsq_constants,  only : tf

    integer, intent(in) :: cell1, cell2, ncell01, flsq_n

    real(dp), dimension(ncell01), intent(in) :: xc, yc, zc

    integer,  dimension(flsq_n), intent(in) :: flsq_ja

    integer :: i, cella

    real(dp) :: dx, dy, dz
    real(dp) :: dsq1, dsq2, dsq1_min, dsq2_min, dsq_min

    real(dp), dimension(flsq_n+2) :: dsq
    real(dp), dimension(flsq_n+2) :: flsq_wsq

  continue

    dsq1_min = huge( 1._dp )
    dsq2_min = huge( 1._dp )
    do i=1,flsq_n

      cella = flsq_ja(i)

      dx = ( xc(cella) - xc(cell1) )
      dy = ( yc(cella) - yc(cell1) )*tf
      dz = ( zc(cella) - zc(cell1) )

      dsq1 = dx**2 + dy**2 + dz**2

      dx = ( xc(cella) - xc(cell2) )
      dy = ( yc(cella) - yc(cell2) )*tf
      dz = ( zc(cella) - zc(cell2) )

      dsq2 = dx**2 + dy**2 + dz**2

      dsq(i) = min ( dsq1 , dsq2 )

      dsq1_min = min( dsq1_min , dsq1 )
      dsq2_min = min( dsq2_min , dsq2 )

    enddo

    dsq_min = min ( dsq1_min , dsq2_min )
    do i=1,flsq_n
      flsq_wsq(i) = dsq_min/dsq(i)
    enddo

    flsq_wsq(flsq_n+1) = 1.0_dp
    flsq_wsq(flsq_n+2) = 1.0_dp

  end function flsq_wsq

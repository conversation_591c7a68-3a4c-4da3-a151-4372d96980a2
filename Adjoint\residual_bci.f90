module residual_bci

  implicit none

  private

  public :: atlam_bci
  public :: enforce_lam_strong_bci, enforce_turb_strong_bci

contains

!================================ ATLAM_BCI ==================================80
!
!  This does A-transpose times lambda at time-level n
!  for part of the residual (dI/dQ is the other part)
!
!  Puts the result in res
!
!  Incompressible
!
!=============================================================================80

  subroutine atlam_bci(nnodes0,nnodes01,x,y,z,qnode,nbound,bc,nelem,elem,ndim, &
                       adim,dxdt,dydt,dzdt,coltag,rlam,res,nfunctions,ia,ja,   &
                       iau,aa)

    use ivals,         only : u0, v0, w0, p0
    use info_depr,     only : beta
    use bc_types,      only : bcgrid_type
    use bc_names,      only : farfield_riem, farfield_extr, farfield_pbck,     &
                              bc_has_pressure_closure,                         &
                              viscous_solid, tangency
    use element_types, only : elem_type
    use kinddefs,      only : dp
    use grid_metrics,  only : dual_area_tria, dual_area_quad
    use design_types,  only : max_functions
    use lmpi,          only : lmpi_master, lmpi_die
    use nml_noninertial_reference_frame, only : noninertial
    use grid_motion_helpers, only             : need_grid_velocity

    integer, intent(in) :: nnodes0,nnodes01,ndim,adim,nelem,nbound
    integer, intent(in), optional :: nfunctions

    integer, dimension(:), intent(in), optional :: ia, ja, iau

    real(dp),  dimension(nnodes01),      intent(in)    :: x,y,z
    real(dp),  dimension(ndim,nnodes01), intent(in)    :: qnode
    real(dp),  dimension(nnodes01),      intent(in)    :: dxdt
    real(dp),  dimension(nnodes01),      intent(in)    :: dydt
    real(dp),  dimension(nnodes01),      intent(in)    :: dzdt
    real(dp),  dimension(adim,nnodes01), intent(in)   , optional :: coltag
    real(dp),  dimension(:,:,:),         intent(in)   , optional :: rlam
    real(dp),  dimension(:,:,:),         intent(inout), optional :: res
    real(dp),  dimension(:,:,:),         intent(inout), optional :: aa

    type(bcgrid_type), dimension(nbound), intent(in) :: bc
    type(elem_type),   dimension(nelem), intent(in)  :: elem

    integer :: ib,n,node1,node2,node3,node4,i,j,ielem,node,inode
    integer :: idiag1, idiag2, idiag3,jstart,jend, idiag
    integer :: neighbor,ioff13,ioff21,ioff23,ioff31,ioff32
    integer :: triangle,triangle_node,ioff12,quad,quad_node

    real(dp) :: c18,c68
    real(dp) :: p1q1,p1q2
    real(dp) :: p2q1,p2q2
    real(dp) :: p3q1,p3q2
    real(dp) :: ax,ay,az,bx,by,bz
    real(dp) :: paq11,paq12,paq13,paq14
    real(dp) :: paq21,paq22,paq23,paq24
    real(dp) :: paq31,paq32,paq33,paq34
    real(dp) :: pbq11,pbq12,pbq13,pbq14
    real(dp) :: pbq21,pbq22,pbq23,pbq24
    real(dp) :: pbq31,pbq32,pbq33,pbq34
    real(dp) :: pcq11,pcq12,pcq13,pcq14
    real(dp) :: pcq21,pcq22,pcq23,pcq24
    real(dp) :: pcq31,pcq32,pcq33,pcq34
    real(dp) :: area
    real(dp) :: p1q3,p1q4,p2q3,p2q4,p3q3,p3q4
    real(dp) :: ubar
    real(dp) :: x1,y1,z1,x2,y2,z2,x3,y3,z3
    real(dp) :: xnorm,ynorm,znorm
    real(dp) :: face_speed
    real(dp) :: pq1
    real(dp) :: u, uq2
    real(dp) :: v, vq3
    real(dp) :: w, wq4
    real(dp) :: ubarq2, ubarq3, ubarq4
    real(dp) :: res1q2, res1q3, res1q4
    real(dp) :: res2q1, res2q2, res2q3, res2q4
    real(dp) :: res3q1, res3q2, res3q3, res3q4
    real(dp) :: res4q1, res4q2, res4q3, res4q4
    real(dp) :: rlamb2,rlamb3,rlamb4

    real(dp), dimension(max_functions) :: rlam1,rlam2,rlam3,rlam4
    real(dp), dimension(max_functions) :: rlam12,rlam13,rlam14
    real(dp), dimension(max_functions) :: rlam22,rlam23,rlam24
    real(dp), dimension(max_functions) :: rlam32,rlam33,rlam34
    real(dp), dimension(3)             :: xnorm_t, ynorm_t, znorm_t
    real(dp), dimension(3)             :: face_speed_t
    real(dp), dimension(4)             :: face_speed_q, area_q
    real(dp), dimension(4)             :: ql, qr
    real(dp), dimension(4,4)           :: df_dql, df_dqr
    real(dp), dimension(4)             :: xnorm_q, ynorm_q, znorm_q

    real(dp), parameter :: zero   = 0.0_dp
    real(dp), parameter :: half   = 0.5_dp
    real(dp), parameter :: my_3   = 3.0_dp

    logical :: fill_res, fill_a

  continue

    fill_res = .false.
    fill_a   = .false.

    if ( present(res) ) then
      if ( .not.present(coltag) .or. .not.present(rlam) .or.                   &
           .not.present(nfunctions) ) then
        if ( lmpi_master ) then
          write(*,*) 'res requested in atlam_bci, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(aa) ) then
      if ( .not.present(iau) .or. .not.present(ia) .or. .not.present(ja) ) then
        if ( lmpi_master ) then
          write(*,*) 'a requested in atlam_bci, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_a = .true.
    endif

! For solid boundaries add contribution from pressure

    close_pressure : do ib = 1, nbound
      conditional_01: if ( bc_has_pressure_closure(bc(ib)%ibc) .or. &
                           bc(ib)%ibc == viscous_solid .or.         &
                           bc(ib)%ibc == tangency ) then

        loop_tris_01: do n = 1, bc(ib)%nbfacet

          ielem = bc(ib)%f2ntb(n,5)  ! index to type of element
                                     ! attached to this face

          c68 = 1._dp
          c18 = 0._dp

          if (elem(ielem)%type_cell == 'tet') then
!           for linear function preservation during flux closure - tets only
            c68 = 6._dp/8._dp
            c18 = 1._dp/8._dp
          end if

          node1 = bc(ib)%ibnode(bc(ib)%f2ntb(n,1))
          node2 = bc(ib)%ibnode(bc(ib)%f2ntb(n,2))
          node3 = bc(ib)%ibnode(bc(ib)%f2ntb(n,3))

          x1 = x(node1)
          y1 = y(node1)
          z1 = z(node1)
          p1q1 = 1.0_dp
          p1q2 = 0.0_dp
          p1q3 = 0.0_dp
          p1q4 = 0.0_dp

          x2 = x(node2)
          y2 = y(node2)
          z2 = z(node2)
          p2q1 = 1.0_dp
          p2q2 = 0.0_dp
          p2q3 = 0.0_dp
          p2q4 = 0.0_dp

          x3 = x(node3)
          y3 = y(node3)
          z3 = z(node3)
          p3q1 = 1.0_dp
          p3q2 = 0.0_dp
          p3q3 = 0.0_dp
          p3q4 = 0.0_dp

          ax = x2 - x1
          ay = y2 - y1
          az = z2 - z1

          bx = x3 - x1
          by = y3 - y1
          bz = z3 - z1

! norm point away from grid interior.
! norm magnitude is 1/3 area of surface triangle.

          xnorm = -0.5_dp*(ay*bz-az*by)/3.0_dp
          ynorm =  0.5_dp*(ax*bz-az*bx)/3.0_dp
          znorm = -0.5_dp*(ax*by-ay*bx)/3.0_dp

          paq11 = c68*p1q1
          paq12 = c68*p1q2
          paq13 = c68*p1q3
          paq14 = c68*p1q4

          paq21 = c18*p2q1
          paq22 = c18*p2q2
          paq23 = c18*p2q3
          paq24 = c18*p2q4

          paq31 = c18*p3q1
          paq32 = c18*p3q2
          paq33 = c18*p3q3
          paq34 = c18*p3q4

          pbq11 = c18*p1q1
          pbq12 = c18*p1q2
          pbq13 = c18*p1q3
          pbq14 = c18*p1q4

          pbq21 = c68*p2q1
          pbq22 = c68*p2q2
          pbq23 = c68*p2q3
          pbq24 = c68*p2q4

          pbq31 = c18*p3q1
          pbq32 = c18*p3q2
          pbq33 = c18*p3q3
          pbq34 = c18*p3q4

          pcq11 = c18*p1q1
          pcq12 = c18*p1q2
          pcq13 = c18*p1q3
          pcq14 = c18*p1q4

          pcq21 = c18*p2q1
          pcq22 = c18*p2q2
          pcq23 = c18*p2q3
          pcq24 = c18*p2q4

          pcq31 = c68*p3q1
          pcq32 = c68*p3q2
          pcq33 = c68*p3q3
          pcq34 = c68*p3q4

        res_contribs1 : if ( fill_res ) then
          do i = 1, nfunctions
            rlam12(i) = coltag(2,node1)*rlam(2,node1,i)
            rlam13(i) = coltag(3,node1)*rlam(3,node1,i)
            rlam14(i) = coltag(4,node1)*rlam(4,node1,i)

            rlam22(i) = coltag(2,node2)*rlam(2,node2,i)
            rlam23(i) = coltag(3,node2)*rlam(3,node2,i)
            rlam24(i) = coltag(4,node2)*rlam(4,node2,i)

            rlam32(i) = coltag(2,node3)*rlam(2,node3,i)
            rlam33(i) = coltag(3,node3)*rlam(3,node3,i)
            rlam34(i) = coltag(4,node3)*rlam(4,node3,i)
          end do

          if (node1 <= nnodes0) then
            do i = 1, nfunctions
              res(1,node1,i) = res(1,node1,i)                                  &
                + xnorm*paq11*rlam12(i)                                        &
                + ynorm*paq11*rlam13(i)                                        &
                + znorm*paq11*rlam14(i)                                        &
                + xnorm*pbq11*rlam22(i)                                        &
                + ynorm*pbq11*rlam23(i)                                        &
                + znorm*pbq11*rlam24(i)                                        &
                + xnorm*pcq11*rlam32(i)                                        &
                + ynorm*pcq11*rlam33(i)                                        &
                + znorm*pcq11*rlam34(i)

              res(2,node1,i) = res(2,node1,i)                                  &
                + xnorm*paq12*rlam12(i)                                        &
                + ynorm*paq12*rlam13(i)                                        &
                + znorm*paq12*rlam14(i)                                        &
                + xnorm*pbq12*rlam22(i)                                        &
                + ynorm*pbq12*rlam23(i)                                        &
                + znorm*pbq12*rlam24(i)                                        &
                + xnorm*pcq12*rlam32(i)                                        &
                + ynorm*pcq12*rlam33(i)                                        &
                + znorm*pcq12*rlam34(i)

              res(3,node1,i) = res(3,node1,i)                                  &
                + xnorm*paq13*rlam12(i)                                        &
                + ynorm*paq13*rlam13(i)                                        &
                + znorm*paq13*rlam14(i)                                        &
                + xnorm*pbq13*rlam22(i)                                        &
                + ynorm*pbq13*rlam23(i)                                        &
                + znorm*pbq13*rlam24(i)                                        &
                + xnorm*pcq13*rlam32(i)                                        &
                + ynorm*pcq13*rlam33(i)                                        &
                + znorm*pcq13*rlam34(i)

              res(4,node1,i) = res(4,node1,i)                                  &
                + xnorm*paq14*rlam12(i)                                        &
                + ynorm*paq14*rlam13(i)                                        &
                + znorm*paq14*rlam14(i)                                        &
                + xnorm*pbq14*rlam22(i)                                        &
                + ynorm*pbq14*rlam23(i)                                        &
                + znorm*pbq14*rlam24(i)                                        &
                + xnorm*pcq14*rlam32(i)                                        &
                + ynorm*pcq14*rlam33(i)                                        &
                + znorm*pcq14*rlam34(i)
            end do
          end if

          if (node2 <= nnodes0) then
            do i = 1, nfunctions
              res(1,node2,i) = res(1,node2,i)                                  &
                + xnorm*paq21*rlam12(i)                                        &
                + ynorm*paq21*rlam13(i)                                        &
                + znorm*paq21*rlam14(i)                                        &
                + xnorm*pbq21*rlam22(i)                                        &
                + ynorm*pbq21*rlam23(i)                                        &
                + znorm*pbq21*rlam24(i)                                        &
                + xnorm*pcq21*rlam32(i)                                        &
                + ynorm*pcq21*rlam33(i)                                        &
                + znorm*pcq21*rlam34(i)

              res(2,node2,i) = res(2,node2,i)                                  &
                + xnorm*paq22*rlam12(i)                                        &
                + ynorm*paq22*rlam13(i)                                        &
                + znorm*paq22*rlam14(i)                                        &
                + xnorm*pbq22*rlam22(i)                                        &
                + ynorm*pbq22*rlam23(i)                                        &
                + znorm*pbq22*rlam24(i)                                        &
                + xnorm*pcq22*rlam32(i)                                        &
                + ynorm*pcq22*rlam33(i)                                        &
                + znorm*pcq22*rlam34(i)

              res(3,node2,i) = res(3,node2,i)                                  &
                + xnorm*paq23*rlam12(i)                                        &
                + ynorm*paq23*rlam13(i)                                        &
                + znorm*paq23*rlam14(i)                                        &
                + xnorm*pbq23*rlam22(i)                                        &
                + ynorm*pbq23*rlam23(i)                                        &
                + znorm*pbq23*rlam24(i)                                        &
                + xnorm*pcq23*rlam32(i)                                        &
                + ynorm*pcq23*rlam33(i)                                        &
                + znorm*pcq23*rlam34(i)

              res(4,node2,i) = res(4,node2,i)                                  &
                + xnorm*paq24*rlam12(i)                                        &
                + ynorm*paq24*rlam13(i)                                        &
                + znorm*paq24*rlam14(i)                                        &
                + xnorm*pbq24*rlam22(i)                                        &
                + ynorm*pbq24*rlam23(i)                                        &
                + znorm*pbq24*rlam24(i)                                        &
                + xnorm*pcq24*rlam32(i)                                        &
                + ynorm*pcq24*rlam33(i)                                        &
                + znorm*pcq24*rlam34(i)
            end do
          end if

          if (node3 <= nnodes0) then
            do i = 1, nfunctions
              res(1,node3,i) = res(1,node3,i)                                  &
                + xnorm*paq31*rlam12(i)                                        &
                + ynorm*paq31*rlam13(i)                                        &
                + znorm*paq31*rlam14(i)                                        &
                + xnorm*pbq31*rlam22(i)                                        &
                + ynorm*pbq31*rlam23(i)                                        &
                + znorm*pbq31*rlam24(i)                                        &
                + xnorm*pcq31*rlam32(i)                                        &
                + ynorm*pcq31*rlam33(i)                                        &
                + znorm*pcq31*rlam34(i)

              res(2,node3,i) = res(2,node3,i)                                  &
                + xnorm*paq32*rlam12(i)                                        &
                + ynorm*paq32*rlam13(i)                                        &
                + znorm*paq32*rlam14(i)                                        &
                + xnorm*pbq32*rlam22(i)                                        &
                + ynorm*pbq32*rlam23(i)                                        &
                + znorm*pbq32*rlam24(i)                                        &
                + xnorm*pcq32*rlam32(i)                                        &
                + ynorm*pcq32*rlam33(i)                                        &
                + znorm*pcq32*rlam34(i)

              res(3,node3,i) = res(3,node3,i)                                  &
                + xnorm*paq33*rlam12(i)                                        &
                + ynorm*paq33*rlam13(i)                                        &
                + znorm*paq33*rlam14(i)                                        &
                + xnorm*pbq33*rlam22(i)                                        &
                + ynorm*pbq33*rlam23(i)                                        &
                + znorm*pbq33*rlam24(i)                                        &
                + xnorm*pcq33*rlam32(i)                                        &
                + ynorm*pcq33*rlam33(i)                                        &
                + znorm*pcq33*rlam34(i)

              res(4,node3,i) = res(4,node3,i)                                  &
                + xnorm*paq34*rlam12(i)                                        &
                + ynorm*paq34*rlam13(i)                                        &
                + znorm*paq34*rlam14(i)                                        &
                + xnorm*pbq34*rlam22(i)                                        &
                + ynorm*pbq34*rlam23(i)                                        &
                + znorm*pbq34*rlam24(i)                                        &
                + xnorm*pcq34*rlam32(i)                                        &
                + ynorm*pcq34*rlam33(i)                                        &
                + znorm*pcq34*rlam34(i)
            end do
          end if
        endif res_contribs1

        a_contribs1 : if ( fill_a ) then
          idiag1 = iau(node1)
          idiag2 = iau(node2)
          idiag3 = iau(node3)

          ioff12 = 0
          jstart = ia(node2)
          jend   = ia(node2+1)-1
          search1 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node1 ) then
              ioff12 = j
              exit search1
            endif
          end do search1

          if ( ioff12 == 0 .and. node2 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 12'
            call lmpi_die
          endif

          ioff13 = 0
          jstart = ia(node3)
          jend   = ia(node3+1)-1
          search2 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node1 ) then
              ioff13 = j
              exit search2
            endif
          end do search2

          if ( ioff13 == 0 .and. node3 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 13'
            call lmpi_die
          endif

          ioff21 = 0
          jstart = ia(node1)
          jend   = ia(node1+1)-1
          search3 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node2 ) then
              ioff21 = j
              exit search3
            endif
          end do search3

          if ( ioff21 == 0 .and. node1 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 21'
            call lmpi_die
          endif

          ioff23 = 0
          jstart = ia(node3)
          jend   = ia(node3+1)-1
          search4 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node2 ) then
              ioff23 = j
              exit search4
            endif
          end do search4

          if ( ioff23 == 0 .and. node3 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 23'
            call lmpi_die
          endif

          ioff31 = 0
          jstart = ia(node1)
          jend   = ia(node1+1)-1
          search5 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node3 ) then
              ioff31 = j
              exit search5
            endif
          end do search5

          if ( ioff31 == 0 .and. node1 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 31'
            call lmpi_die
          endif

          ioff32 = 0
          jstart = ia(node2)
          jend   = ia(node2+1)-1
          search6 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node3 ) then
              ioff32 = j
              exit search6
            endif
          end do search6

          if ( ioff32 == 0 .and. node2 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 32'
            call lmpi_die
          endif

! Now assemble the contributions

!  dR1/dQ1

          if ( node1 <= nnodes0 ) then
            aa(2,1,idiag1) = aa(2,1,idiag1) + xnorm*paq11
            aa(2,2,idiag1) = aa(2,2,idiag1) + xnorm*paq12
            aa(2,3,idiag1) = aa(2,3,idiag1) + xnorm*paq13
            aa(2,4,idiag1) = aa(2,4,idiag1) + xnorm*paq14

            aa(3,1,idiag1) = aa(3,1,idiag1) + ynorm*paq11
            aa(3,2,idiag1) = aa(3,2,idiag1) + ynorm*paq12
            aa(3,3,idiag1) = aa(3,3,idiag1) + ynorm*paq13
            aa(3,4,idiag1) = aa(3,4,idiag1) + ynorm*paq14

            aa(4,1,idiag1) = aa(4,1,idiag1) + znorm*paq11
            aa(4,2,idiag1) = aa(4,2,idiag1) + znorm*paq12
            aa(4,3,idiag1) = aa(4,3,idiag1) + znorm*paq13
            aa(4,4,idiag1) = aa(4,4,idiag1) + znorm*paq14
          endif

!  dR1/dQ2

          if ( node2 <= nnodes0 ) then
            aa(2,1,ioff12) = aa(2,1,ioff12) + xnorm*paq21
            aa(2,2,ioff12) = aa(2,2,ioff12) + xnorm*paq22
            aa(2,3,ioff12) = aa(2,3,ioff12) + xnorm*paq23
            aa(2,4,ioff12) = aa(2,4,ioff12) + xnorm*paq24

            aa(3,1,ioff12) = aa(3,1,ioff12) + ynorm*paq21
            aa(3,2,ioff12) = aa(3,2,ioff12) + ynorm*paq22
            aa(3,3,ioff12) = aa(3,3,ioff12) + ynorm*paq23
            aa(3,4,ioff12) = aa(3,4,ioff12) + ynorm*paq24

            aa(4,1,ioff12) = aa(4,1,ioff12) + znorm*paq21
            aa(4,2,ioff12) = aa(4,2,ioff12) + znorm*paq22
            aa(4,3,ioff12) = aa(4,3,ioff12) + znorm*paq23
            aa(4,4,ioff12) = aa(4,4,ioff12) + znorm*paq24
          endif

!  dR1/dQ3

          if ( node3 <= nnodes0 ) then
            aa(2,1,ioff13) = aa(2,1,ioff13) + xnorm*paq31
            aa(2,2,ioff13) = aa(2,2,ioff13) + xnorm*paq32
            aa(2,3,ioff13) = aa(2,3,ioff13) + xnorm*paq33
            aa(2,4,ioff13) = aa(2,4,ioff13) + xnorm*paq34

            aa(3,1,ioff13) = aa(3,1,ioff13) + ynorm*paq31
            aa(3,2,ioff13) = aa(3,2,ioff13) + ynorm*paq32
            aa(3,3,ioff13) = aa(3,3,ioff13) + ynorm*paq33
            aa(3,4,ioff13) = aa(3,4,ioff13) + ynorm*paq34

            aa(4,1,ioff13) = aa(4,1,ioff13) + znorm*paq31
            aa(4,2,ioff13) = aa(4,2,ioff13) + znorm*paq32
            aa(4,3,ioff13) = aa(4,3,ioff13) + znorm*paq33
            aa(4,4,ioff13) = aa(4,4,ioff13) + znorm*paq34
          endif

!  dR2/dQ1

          if ( node1 <= nnodes0 ) then
            aa(2,1,ioff21) = aa(2,1,ioff21) + xnorm*pbq11
            aa(2,2,ioff21) = aa(2,2,ioff21) + xnorm*pbq12
            aa(2,3,ioff21) = aa(2,3,ioff21) + xnorm*pbq13
            aa(2,4,ioff21) = aa(2,4,ioff21) + xnorm*pbq14

            aa(3,1,ioff21) = aa(3,1,ioff21) + ynorm*pbq11
            aa(3,2,ioff21) = aa(3,2,ioff21) + ynorm*pbq12
            aa(3,3,ioff21) = aa(3,3,ioff21) + ynorm*pbq13
            aa(3,4,ioff21) = aa(3,4,ioff21) + ynorm*pbq14

            aa(4,1,ioff21) = aa(4,1,ioff21) + znorm*pbq11
            aa(4,2,ioff21) = aa(4,2,ioff21) + znorm*pbq12
            aa(4,3,ioff21) = aa(4,3,ioff21) + znorm*pbq13
            aa(4,4,ioff21) = aa(4,4,ioff21) + znorm*pbq14
          endif

!  dR2/dQ2

          if ( node2 <= nnodes0 ) then
            aa(2,1,idiag2) = aa(2,1,idiag2) + xnorm*pbq21
            aa(2,2,idiag2) = aa(2,2,idiag2) + xnorm*pbq22
            aa(2,3,idiag2) = aa(2,3,idiag2) + xnorm*pbq23
            aa(2,4,idiag2) = aa(2,4,idiag2) + xnorm*pbq24

            aa(3,1,idiag2) = aa(3,1,idiag2) + ynorm*pbq21
            aa(3,2,idiag2) = aa(3,2,idiag2) + ynorm*pbq22
            aa(3,3,idiag2) = aa(3,3,idiag2) + ynorm*pbq23
            aa(3,4,idiag2) = aa(3,4,idiag2) + ynorm*pbq24

            aa(4,1,idiag2) = aa(4,1,idiag2) + znorm*pbq21
            aa(4,2,idiag2) = aa(4,2,idiag2) + znorm*pbq22
            aa(4,3,idiag2) = aa(4,3,idiag2) + znorm*pbq23
            aa(4,4,idiag2) = aa(4,4,idiag2) + znorm*pbq24
          endif


!  dR2/dQ3

          if ( node3 <= nnodes0 ) then
            aa(2,1,ioff23) = aa(2,1,ioff23) + xnorm*pbq31
            aa(2,2,ioff23) = aa(2,2,ioff23) + xnorm*pbq32
            aa(2,3,ioff23) = aa(2,3,ioff23) + xnorm*pbq33
            aa(2,4,ioff23) = aa(2,4,ioff23) + xnorm*pbq34

            aa(3,1,ioff23) = aa(3,1,ioff23) + ynorm*pbq31
            aa(3,2,ioff23) = aa(3,2,ioff23) + ynorm*pbq32
            aa(3,3,ioff23) = aa(3,3,ioff23) + ynorm*pbq33
            aa(3,4,ioff23) = aa(3,4,ioff23) + ynorm*pbq34

            aa(4,1,ioff23) = aa(4,1,ioff23) + znorm*pbq31
            aa(4,2,ioff23) = aa(4,2,ioff23) + znorm*pbq32
            aa(4,3,ioff23) = aa(4,3,ioff23) + znorm*pbq33
            aa(4,4,ioff23) = aa(4,4,ioff23) + znorm*pbq34
          endif

!  dR3/dQ1

          if ( node1 <= nnodes0 ) then
            aa(2,1,ioff31) = aa(2,1,ioff31) + xnorm*pcq11
            aa(2,2,ioff31) = aa(2,2,ioff31) + xnorm*pcq12
            aa(2,3,ioff31) = aa(2,3,ioff31) + xnorm*pcq13
            aa(2,4,ioff31) = aa(2,4,ioff31) + xnorm*pcq14

            aa(3,1,ioff31) = aa(3,1,ioff31) + ynorm*pcq11
            aa(3,2,ioff31) = aa(3,2,ioff31) + ynorm*pcq12
            aa(3,3,ioff31) = aa(3,3,ioff31) + ynorm*pcq13
            aa(3,4,ioff31) = aa(3,4,ioff31) + ynorm*pcq14

            aa(4,1,ioff31) = aa(4,1,ioff31) + znorm*pcq11
            aa(4,2,ioff31) = aa(4,2,ioff31) + znorm*pcq12
            aa(4,3,ioff31) = aa(4,3,ioff31) + znorm*pcq13
            aa(4,4,ioff31) = aa(4,4,ioff31) + znorm*pcq14
          endif

!  dR3/dQ2

          if ( node2 <= nnodes0 ) then
            aa(2,1,ioff32) = aa(2,1,ioff32) + xnorm*pcq21
            aa(2,2,ioff32) = aa(2,2,ioff32) + xnorm*pcq22
            aa(2,3,ioff32) = aa(2,3,ioff32) + xnorm*pcq23
            aa(2,4,ioff32) = aa(2,4,ioff32) + xnorm*pcq24

            aa(3,1,ioff32) = aa(3,1,ioff32) + ynorm*pcq21
            aa(3,2,ioff32) = aa(3,2,ioff32) + ynorm*pcq22
            aa(3,3,ioff32) = aa(3,3,ioff32) + ynorm*pcq23
            aa(3,4,ioff32) = aa(3,4,ioff32) + ynorm*pcq24

            aa(4,1,ioff32) = aa(4,1,ioff32) + znorm*pcq21
            aa(4,2,ioff32) = aa(4,2,ioff32) + znorm*pcq22
            aa(4,3,ioff32) = aa(4,3,ioff32) + znorm*pcq23
            aa(4,4,ioff32) = aa(4,4,ioff32) + znorm*pcq24
          endif

!  dR3/dQ3

          if ( node3 <= nnodes0 ) then
            aa(2,1,idiag3) = aa(2,1,idiag3) + xnorm*pcq31
            aa(2,2,idiag3) = aa(2,2,idiag3) + xnorm*pcq32
            aa(2,3,idiag3) = aa(2,3,idiag3) + xnorm*pcq33
            aa(2,4,idiag3) = aa(2,4,idiag3) + xnorm*pcq34

            aa(3,1,idiag3) = aa(3,1,idiag3) + ynorm*pcq31
            aa(3,2,idiag3) = aa(3,2,idiag3) + ynorm*pcq32
            aa(3,3,idiag3) = aa(3,3,idiag3) + ynorm*pcq33
            aa(3,4,idiag3) = aa(3,4,idiag3) + ynorm*pcq34

            aa(4,1,idiag3) = aa(4,1,idiag3) + znorm*pcq31
            aa(4,2,idiag3) = aa(4,2,idiag3) + znorm*pcq32
            aa(4,3,idiag3) = aa(4,3,idiag3) + znorm*pcq33
            aa(4,4,idiag3) = aa(4,4,idiag3) + znorm*pcq34
          endif
        endif a_contribs1

        end do loop_tris_01

        loop_quads_01: do n = 1, bc(ib)%nbfaceq

          node1 = bc(ib)%ibnode(bc(ib)%f2nqb(n,1))
          node2 = bc(ib)%ibnode(bc(ib)%f2nqb(n,2))
          node3 = bc(ib)%ibnode(bc(ib)%f2nqb(n,3))
          node4 = bc(ib)%ibnode(bc(ib)%f2nqb(n,4))

          call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,         &
                              noninertial,xnorm_q,ynorm_q,znorm_q)

          res_contribs1b : if ( fill_res ) then
            if (node1 <= nnodes0) then
              xnorm = xnorm_q(1)
              ynorm = ynorm_q(1)
              znorm = znorm_q(1)
              do i = 1, nfunctions
                rlamb2 = coltag(2,node1)*rlam(2,node1,i)
                rlamb3 = coltag(3,node1)*rlam(3,node1,i)
                rlamb4 = coltag(4,node1)*rlam(4,node1,i)
                res(1,node1,i) = res(1,node1,i) + xnorm*rlamb2 + ynorm*rlamb3  &
                                                + znorm*rlamb4
              end do
            end if

            if (node2 <= nnodes0) then
              xnorm = xnorm_q(2)
              ynorm = ynorm_q(2)
              znorm = znorm_q(2)
              do i = 1, nfunctions
                rlamb2 = coltag(2,node2)*rlam(2,node2,i)
                rlamb3 = coltag(3,node2)*rlam(3,node2,i)
                rlamb4 = coltag(4,node2)*rlam(4,node2,i)
                res(1,node2,i) = res(1,node2,i) + xnorm*rlamb2 + ynorm*rlamb3  &
                                                + znorm*rlamb4
              end do
            end if

            if (node3 <= nnodes0) then
              xnorm = xnorm_q(3)
              ynorm = ynorm_q(3)
              znorm = znorm_q(3)
              do i = 1, nfunctions
                rlamb2 = coltag(2,node3)*rlam(2,node3,i)
                rlamb3 = coltag(3,node3)*rlam(3,node3,i)
                rlamb4 = coltag(4,node3)*rlam(4,node3,i)
                res(1,node3,i) = res(1,node3,i) + xnorm*rlamb2 + ynorm*rlamb3  &
                                                + znorm*rlamb4
              end do
            end if

            if (node4 <= nnodes0) then
              xnorm = xnorm_q(4)
              ynorm = ynorm_q(4)
              znorm = znorm_q(4)
              do i = 1, nfunctions
                rlamb2 = coltag(2,node4)*rlam(2,node4,i)
                rlamb3 = coltag(3,node4)*rlam(3,node4,i)
                rlamb4 = coltag(4,node4)*rlam(4,node4,i)
                res(1,node4,i) = res(1,node4,i) + xnorm*rlamb2 + ynorm*rlamb3  &
                                                + znorm*rlamb4
              end do
            end if
          endif res_contribs1b

          a_contribs1b : if ( fill_a ) then
            if ( node1 <= nnodes0 ) then
              idiag = iau(node1)
              xnorm = xnorm_q(1)
              ynorm = ynorm_q(1)
              znorm = znorm_q(1)
              aa(2,1,idiag) = aa(2,1,idiag) + xnorm
              aa(3,1,idiag) = aa(3,1,idiag) + ynorm
              aa(4,1,idiag) = aa(4,1,idiag) + znorm
            endif

            if ( node2 <= nnodes0 ) then
              idiag = iau(node2)
              xnorm = xnorm_q(2)
              ynorm = ynorm_q(2)
              znorm = znorm_q(2)
              aa(2,1,idiag) = aa(2,1,idiag) + xnorm
              aa(3,1,idiag) = aa(3,1,idiag) + ynorm
              aa(4,1,idiag) = aa(4,1,idiag) + znorm
            endif

            if ( node3 <= nnodes0 ) then
              idiag = iau(node3)
              xnorm = xnorm_q(3)
              ynorm = ynorm_q(3)
              znorm = znorm_q(3)
              aa(2,1,idiag) = aa(2,1,idiag) + xnorm
              aa(3,1,idiag) = aa(3,1,idiag) + ynorm
              aa(4,1,idiag) = aa(4,1,idiag) + znorm
            endif

            if ( node4 <= nnodes0 ) then
              idiag = iau(node4)
              xnorm = xnorm_q(4)
              ynorm = ynorm_q(4)
              znorm = znorm_q(4)
              aa(2,1,idiag) = aa(2,1,idiag) + xnorm
              aa(3,1,idiag) = aa(3,1,idiag) + ynorm
              aa(4,1,idiag) = aa(4,1,idiag) + znorm
            endif
          endif a_contribs1b

        end do loop_quads_01

      end if conditional_01
    end do close_pressure

! Farfield

    do ib = 1, nbound
      if (bc(ib)%ibc == farfield_riem) then

        loop_bnodes2: do n = 1, bc(ib)%nbnode

          node = bc(ib)%ibnode(n)

          if (node <= nnodes0) then

            xnorm = bc(ib)%bxn(n)
            ynorm = bc(ib)%byn(n)
            znorm = bc(ib)%bzn(n)

            area = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)

            xnorm = xnorm / area
            ynorm = ynorm / area
            znorm = znorm / area

            face_speed = 0.0_dp

            if (need_grid_velocity) then
              face_speed = bc(ib)%bfacespeed(n)
            end if

! fill in the guts with a riemann solver

            ql(1) = qnode(1,node)
            ql(2) = qnode(2,node)
            ql(3) = qnode(3,node)
            ql(4) = qnode(4,node)

            qr(1) = p0
            qr(2) = u0
            qr(3) = v0
            qr(4) = w0

            call dfroe_i(xnorm,ynorm,znorm,area,face_speed,beta,ql,qr,df_dql,  &
                         df_dqr)

          res_contribs2 : if ( fill_res ) then
            do j = 1, nfunctions
              rlam1(j) = coltag(1,node)*rlam(1,node,j)
              rlam2(j) = coltag(2,node)*rlam(2,node,j)
              rlam3(j) = coltag(3,node)*rlam(3,node,j)
              rlam4(j) = coltag(4,node)*rlam(4,node,j)

              res(1,node,j) = res(1,node,j) + df_dql(1,1)*rlam1(j)
              res(2,node,j) = res(2,node,j) + df_dql(1,2)*rlam1(j)
              res(3,node,j) = res(3,node,j) + df_dql(1,3)*rlam1(j)
              res(4,node,j) = res(4,node,j) + df_dql(1,4)*rlam1(j)

              res(1,node,j) = res(1,node,j) + df_dql(2,1)*rlam2(j)
              res(2,node,j) = res(2,node,j) + df_dql(2,2)*rlam2(j)
              res(3,node,j) = res(3,node,j) + df_dql(2,3)*rlam2(j)
              res(4,node,j) = res(4,node,j) + df_dql(2,4)*rlam2(j)

              res(1,node,j) = res(1,node,j) + df_dql(3,1)*rlam3(j)
              res(2,node,j) = res(2,node,j) + df_dql(3,2)*rlam3(j)
              res(3,node,j) = res(3,node,j) + df_dql(3,3)*rlam3(j)
              res(4,node,j) = res(4,node,j) + df_dql(3,4)*rlam3(j)

              res(1,node,j) = res(1,node,j) + df_dql(4,1)*rlam4(j)
              res(2,node,j) = res(2,node,j) + df_dql(4,2)*rlam4(j)
              res(3,node,j) = res(3,node,j) + df_dql(4,3)*rlam4(j)
              res(4,node,j) = res(4,node,j) + df_dql(4,4)*rlam4(j)
            end do
          endif res_contribs2

          a_contribs2 : if ( fill_a ) then
            idiag = iau(node)
            aa(1:4,1:4,idiag) = aa(1:4,1:4,idiag) + df_dql(1:4,1:4)
          endif a_contribs2

          end if

        end do loop_bnodes2

      endif

    end do

! Get any pieces from farfield_extr or farfield_pbck boundaries

    conditional_ext : do ib = 1, nbound

      if(bc(ib)%ibc == farfield_extr .or. bc(ib)%ibc == farfield_pbck) then

        ext_tri_loop : do triangle = 1, bc(ib)%nbfacet

          node1 = bc(ib)%ibnode( bc(ib)%f2ntb(triangle, 1) )
          node2 = bc(ib)%ibnode( bc(ib)%f2ntb(triangle, 2) )
          node3 = bc(ib)%ibnode( bc(ib)%f2ntb(triangle, 3) )

!         norm points away from grid interior
!         norm magnitude is 1/3 area of surface triangle
!         and is the same at all 3 nodes

          x1 = x(node1)
          y1 = y(node1)
          z1 = z(node1)

          x2 = x(node2)
          y2 = y(node2)
          z2 = z(node2)

          x3 = x(node3)
          y3 = y(node3)
          z3 = z(node3)

          ax = x2 - x1
          ay = y2 - y1
          az = z2 - z1

          bx = x3 - x1
          by = y3 - y1
          bz = z3 - z1

! the 1/3 term is added here for each node's contribution
          xnorm = -half*(ay*bz - az*by)/my_3
          ynorm =  half*(ax*bz - az*bx)/my_3
          znorm = -half*(ax*by - ay*bx)/my_3

          area  = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)
          xnorm = xnorm / area
          ynorm = ynorm / area
          znorm = znorm / area

!       For moving grid cases get face speed contributions

          face_speed_t(:) = 0.0_dp

          if ( need_grid_velocity ) then
            call dual_area_tria(nnodes01,x,y,z,node1,node2,node3,noninertial,  &
                                xnorm_t,ynorm_t,znorm_t,dxdt,dydt,dzdt,        &
                                face_speed_t)
            face_speed_t(:) = face_speed_t(:)/area
          end if

          ext_tri_node_loop : do triangle_node = 1, 3

            inode  = bc(ib)%ibnode( bc(ib)%f2ntb(triangle, triangle_node))

            local_node : if(inode <= nnodes0) then

              face_speed = face_speed_t(triangle_node)

              u     = qnode(2,inode)
                uq2 = 1.0_dp

              v     = qnode(3,inode)
                vq3 = 1.0_dp

              w     = qnode(4,inode)
                wq4 = 1.0_dp

!             p   = qnode(1,inode)
                pq1 = 1.0_dp

              if (bc(ib)%ibc == farfield_pbck) then
!             p   = pback
                pq1 = 0.0_dp
              end if

              ubar = xnorm*u + ynorm*v + znorm*w
                ubarq2 = xnorm*uq2
                ubarq3 = ynorm*vq3
                ubarq4 = znorm*wq4

!             res1 = area*beta*ubar
              res1q2 = area*beta*ubarq2
              res1q3 = area*beta*ubarq3
              res1q4 = area*beta*ubarq4

!             res2 = area*(u*(ubar-face_speed) + xnorm*p)
              res2q1 = area*( xnorm*pq1 )
              res2q2 = area*( (u*ubarq2+(ubar-face_speed)*uq2))
              res2q3 = area*( u*ubarq3 )
              res2q4 = area*( u*ubarq4 )

!             res3 = area*(v*(ubar-face_speed) + ynorm*p)
              res3q1 = area*( ynorm*pq1 )
              res3q2 = area*( v*ubarq2 )
              res3q3 = area*( (v*ubarq3+(ubar-face_speed)*vq3))
              res3q4 = area*( v*ubarq4 )

!             res4 = area*(w*(ubar-face_speed) + znorm*p)
              res4q1 = area*( znorm*pq1 )
              res4q2 = area*( w*ubarq2 )
              res4q3 = area*( w*ubarq3 )
              res4q4 = area*( (w*ubarq4+(ubar-face_speed)*wq4) )

             res_contribs3 : if ( fill_res ) then
              do j = 1, nfunctions
                rlam1(j) = coltag(1,inode)*rlam(1,inode,j)
                rlam2(j) = coltag(2,inode)*rlam(2,inode,j)
                rlam3(j) = coltag(3,inode)*rlam(3,inode,j)
                rlam4(j) = coltag(4,inode)*rlam(4,inode,j)
              end do

              fcn_loop : do j = 1, nfunctions
                res(1,inode,j) = res(1,inode,j) + res2q1*rlam2(j)              &
                                                + res3q1*rlam3(j)              &
                                                + res4q1*rlam4(j)

                res(2,inode,j) = res(2,inode,j) + res1q2*rlam1(j)              &
                                                + res2q2*rlam2(j)              &
                                                + res3q2*rlam3(j)              &
                                                + res4q2*rlam4(j)

                res(3,inode,j) = res(3,inode,j) + res1q3*rlam1(j)              &
                                                + res2q3*rlam2(j)              &
                                                + res3q3*rlam3(j)              &
                                                + res4q3*rlam4(j)

                res(4,inode,j) = res(4,inode,j) + res1q4*rlam1(j)              &
                                                + res2q4*rlam2(j)              &
                                                + res3q4*rlam3(j)              &
                                                + res4q4*rlam4(j)

              end do fcn_loop
             endif res_contribs3

             a_contribs3 : if ( fill_a ) then
              idiag = iau(inode)

              aa(1,2,idiag) = aa(1,2,idiag) + res1q2
              aa(1,3,idiag) = aa(1,3,idiag) + res1q3
              aa(1,4,idiag) = aa(1,4,idiag) + res1q4

              aa(2,1,idiag) = aa(2,1,idiag) + res2q1
              aa(2,2,idiag) = aa(2,2,idiag) + res2q2
              aa(2,3,idiag) = aa(2,3,idiag) + res2q3
              aa(2,4,idiag) = aa(2,4,idiag) + res2q4

              aa(3,1,idiag) = aa(3,1,idiag) + res3q1
              aa(3,2,idiag) = aa(3,2,idiag) + res3q2
              aa(3,3,idiag) = aa(3,3,idiag) + res3q3
              aa(3,4,idiag) = aa(3,4,idiag) + res3q4

              aa(4,1,idiag) = aa(4,1,idiag) + res4q1
              aa(4,2,idiag) = aa(4,2,idiag) + res4q2
              aa(4,3,idiag) = aa(4,3,idiag) + res4q3
              aa(4,4,idiag) = aa(4,4,idiag) + res4q4
             endif a_contribs3

            endif local_node

          end do ext_tri_node_loop

        end do ext_tri_loop

        ext_quad_loop : do quad = 1, bc(ib)%nbfaceq

          node1 = bc(ib)%ibnode( bc(ib)%f2nqb(quad, 1) )
          node2 = bc(ib)%ibnode( bc(ib)%f2nqb(quad, 2) )
          node3 = bc(ib)%ibnode( bc(ib)%f2nqb(quad, 3) )
          node4 = bc(ib)%ibnode( bc(ib)%f2nqb(quad, 4) )

!         get dual norm/face speed  contributions at each node of the quad face

          face_speed_q(:) = zero

          if (need_grid_velocity) then

            call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,        &
                                noninertial,xnorm_q,ynorm_q,znorm_q,dxdt=dxdt, &
                                dydt=dydt,dzdt=dzdt,facespeed=face_speed_q)

            area_q(:)  = sqrt(xnorm_q(:)*xnorm_q(:) + ynorm_q(:)*ynorm_q(:) +  &
                              znorm_q(:)*znorm_q(:))
            xnorm_q(:) = xnorm_q(:)/area_q(:)
            ynorm_q(:) = ynorm_q(:)/area_q(:)
            znorm_q(:) = znorm_q(:)/area_q(:)

            face_speed_q(:) = face_speed_q(:)/area_q(:)

          else

            call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,        &
                                noninertial,xnorm_q,ynorm_q,znorm_q)

            area_q(:)  = sqrt(xnorm_q(:)*xnorm_q(:) + ynorm_q(:)*ynorm_q(:) +  &
                              znorm_q(:)*znorm_q(:))
            xnorm_q(:) = xnorm_q(:)/area_q(:)
            ynorm_q(:) = ynorm_q(:)/area_q(:)
            znorm_q(:) = znorm_q(:)/area_q(:)

          end if

          ext_quad_node_loop : do quad_node = 1, 4

            inode  = bc(ib)%ibnode( bc(ib)%f2nqb(quad, quad_node))

            local_node2 : if(inode <= nnodes0) then

              xnorm = xnorm_q(quad_node)
              ynorm = ynorm_q(quad_node)
              znorm = znorm_q(quad_node)
              area  = area_q(quad_node)
              face_speed = face_speed_q(quad_node)

              u     = qnode(2,inode)
                uq2 = 1.0_dp

              v     = qnode(3,inode)
                vq3 = 1.0_dp

              w     = qnode(4,inode)
                wq4 = 1.0_dp

!             p   = qnode(1,inode)
                pq1 = 1.0_dp

              if (bc(ib)%ibc == farfield_pbck) then
!             p   = pback
                pq1 = 0.0_dp
              end if

              ubar = xnorm*u + ynorm*v + znorm*w
                ubarq2 = xnorm*uq2
                ubarq3 = ynorm*vq3
                ubarq4 = znorm*wq4

!             res1 = area*beta*ubar
              res1q2 = area*beta*ubarq2
              res1q3 = area*beta*ubarq3
              res1q4 = area*beta*ubarq4

!             res2 = area*(u*(ubar-face_speed) + xnorm*p)
              res2q1 = area*( xnorm*pq1 )
              res2q2 = area*( (u*ubarq2+(ubar-face_speed)*uq2))
              res2q3 = area*( u*ubarq3 )
              res2q4 = area*( u*ubarq4 )

!             res3 = area*(v*(ubar-face_speed) + ynorm*p)
              res3q1 = area*( ynorm*pq1 )
              res3q2 = area*( v*ubarq2 )
              res3q3 = area*( (v*ubarq3+(ubar-face_speed)*vq3))
              res3q4 = area*( v*ubarq4 )

!             res4 = area*(w*(ubar-face_speed) + znorm*p)
              res4q1 = area*( znorm*pq1 )
              res4q2 = area*( w*ubarq2 )
              res4q3 = area*( w*ubarq3 )
              res4q4 = area*( (w*ubarq4+(ubar-face_speed)*wq4) )

             res_contribs4 : if ( fill_res ) then
              do j = 1, nfunctions
                rlam1(j) = coltag(1,inode)*rlam(1,inode,j)
                rlam2(j) = coltag(2,inode)*rlam(2,inode,j)
                rlam3(j) = coltag(3,inode)*rlam(3,inode,j)
                rlam4(j) = coltag(4,inode)*rlam(4,inode,j)
              end do

              fcn_loop2 : do j = 1, nfunctions
                res(1,inode,j) = res(1,inode,j) + res2q1*rlam2(j)              &
                                                + res3q1*rlam3(j)              &
                                                + res4q1*rlam4(j)

                res(2,inode,j) = res(2,inode,j) + res1q2*rlam1(j)              &
                                                + res2q2*rlam2(j)              &
                                                + res3q2*rlam3(j)              &
                                                + res4q2*rlam4(j)

                res(3,inode,j) = res(3,inode,j) + res1q3*rlam1(j)              &
                                                + res2q3*rlam2(j)              &
                                                + res3q3*rlam3(j)              &
                                                + res4q3*rlam4(j)

                res(4,inode,j) = res(4,inode,j) + res1q4*rlam1(j)              &
                                                + res2q4*rlam2(j)              &
                                                + res3q4*rlam3(j)              &
                                                + res4q4*rlam4(j)

              end do fcn_loop2
             endif res_contribs4

             a_contribs4 : if ( fill_a ) then
              idiag = iau(inode)

              aa(1,2,idiag) = aa(1,2,idiag) + res1q2
              aa(1,3,idiag) = aa(1,3,idiag) + res1q3
              aa(1,4,idiag) = aa(1,4,idiag) + res1q4

              aa(2,1,idiag) = aa(2,1,idiag) + res2q1
              aa(2,2,idiag) = aa(2,2,idiag) + res2q2
              aa(2,3,idiag) = aa(2,3,idiag) + res2q3
              aa(2,4,idiag) = aa(2,4,idiag) + res2q4

              aa(3,1,idiag) = aa(3,1,idiag) + res3q1
              aa(3,2,idiag) = aa(3,2,idiag) + res3q2
              aa(3,3,idiag) = aa(3,3,idiag) + res3q3
              aa(3,4,idiag) = aa(3,4,idiag) + res3q4

              aa(4,1,idiag) = aa(4,1,idiag) + res4q1
              aa(4,2,idiag) = aa(4,2,idiag) + res4q2
              aa(4,3,idiag) = aa(4,3,idiag) + res4q3
              aa(4,4,idiag) = aa(4,4,idiag) + res4q4
             endif a_contribs4

            endif local_node2

          end do ext_quad_node_loop

        end do ext_quad_loop

      end if

    end do conditional_ext

  end subroutine atlam_bci


!================================ ENFORCE_LAM_STRONG_BCI =====================80
!
! Set diag for strong laminar bc - incompressible.
!
! Note: The block jacobian matrices are not yet transposed.
!
!=============================================================================80

  subroutine enforce_lam_strong_bci(nnodes,nnodes0,nnz,adim,A,iau2,nbound,bc)

    use kinddefs, only : dp
    use bc_types, only : bcgrid_type
    use bc_names, only : bc_strong_viscous_adjoint

    integer, intent(in) :: nnodes, nnodes0, nnz, adim, nbound

    real(dp), dimension(adim,adim,nnz), intent(inout) :: A

    integer, dimension(nnodes), intent(in) :: iau2

    type(bcgrid_type), dimension(nbound), intent(in) :: bc

    integer :: i, icolumn
    integer :: ib, node

    real(dp), parameter    :: my_0     = 0.0_dp
    real(dp), parameter    :: my_1     = 1.0_dp

  continue

! Enforce viscous boundaries as appropriate
! For incompressible, moving_grid/noninertial diagonal
! blocks look just the same as standard no-slip wall
! for static grid case

    do ib = 1,nbound

      strong_boundary : if( bc_strong_viscous_adjoint( bc(ib)%ibc ) ) then

        do i = 1,bc(ib)%nbnode
          node = bc(ib)%ibnode(i)
          if(node <= nnodes0) then
            icolumn = iau2(node)

            A(2,1,icolumn) = my_0
            A(2,2,icolumn) = my_1
            A(2,3,icolumn) = my_0
            A(2,4,icolumn) = my_0

            A(3,1,icolumn) = my_0
            A(3,2,icolumn) = my_0
            A(3,3,icolumn) = my_1
            A(3,4,icolumn) = my_0

            A(4,1,icolumn) = my_0
            A(4,2,icolumn) = my_0
            A(4,3,icolumn) = my_0
            A(4,4,icolumn) = my_1
          endif
        end do

      endif strong_boundary

    end do

  end subroutine enforce_lam_strong_bci


!================================ ENFORCE_TURB_STRONG_BCI ====================80
!
! Set diag for strong turbulent bc - incompressible.
!
! Note: The block jacobian matrices are not yet transposed.
!
!=============================================================================80
  subroutine enforce_turb_strong_bci(nnodes,nnodes0,nnz,adim,A,iau2,nbound,bc)


    use kinddefs, only : dp
    use bc_types, only : bcgrid_type
    use bc_names, only : bc_strong_viscous_adjoint

    integer, intent(in) :: nnodes, nnodes0, nnz, adim, nbound

    real(dp), dimension(adim,adim,nnz), intent(inout) :: A

    integer, dimension(nnodes), intent(in) :: iau2

    type(bcgrid_type), dimension(nbound), intent(in) :: bc

    integer :: i, icolumn
    integer :: ib, node

    real(dp), parameter    :: my_0     = 0.0_dp
    real(dp), parameter    :: my_1     = 1.0_dp

  continue

    set_strong_bc_diag : do ib = 1,nbound

      strong_boundary : if(bc_strong_viscous_adjoint(bc(ib)%ibc)) then

        do i = 1,bc(ib)%nbnode
          node = bc(ib)%ibnode(i)
          if(node <= nnodes0) then

            icolumn = iau2(node)

! Fix the A's on the viscous walls

            A(2,1,icolumn) = my_0
            A(2,2,icolumn) = my_1
            A(2,3,icolumn) = my_0
            A(2,4,icolumn) = my_0
            A(2,5,icolumn) = my_0

            A(3,1,icolumn) = my_0
            A(3,2,icolumn) = my_0
            A(3,3,icolumn) = my_1
            A(3,4,icolumn) = my_0
            A(3,5,icolumn) = my_0

            A(4,1,icolumn) = my_0
            A(4,2,icolumn) = my_0
            A(4,3,icolumn) = my_0
            A(4,4,icolumn) = my_1
            A(4,5,icolumn) = my_0

            A(5,1,icolumn) = my_0
            A(5,2,icolumn) = my_0
            A(5,3,icolumn) = my_0
            A(5,4,icolumn) = my_0
            A(5,5,icolumn) = my_1

          endif
        end do

      endif strong_boundary

    end do set_strong_bc_diag

  end subroutine enforce_turb_strong_bci

  include 'dfroe_i.f90'

end module residual_bci

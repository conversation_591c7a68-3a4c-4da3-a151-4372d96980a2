!=================================== Q_CRITERION =============================80
!
!  Computes the "Q-criterion" (second invariant of the velocity-gradient tensor)
!  at a point; often used for vortex visualization
!
!=============================================================================80

  pure function q_criterion( eqn_set, n_grd, gradx, grady, gradz )

    use kinddefs, only       : dp
    use solution_types, only : compressible

    integer,                    intent(in) :: eqn_set, n_grd
    real(dp), dimension(n_grd), intent(in) :: gradx, grady, gradz

    real(dp)                               :: q_criterion

  continue

    if ( eqn_set == compressible ) then

      q_criterion =                                                            &
                  gradx(2)*grady(3) + gradx(2)*gradz(4) + grady(3)*gradz(4)    &
         -0.5_dp*(gradx(3)*grady(2) + gradx(4)*gradz(2) + grady(2)*gradx(3) +  &
                  grady(4)*gradz(3) + gradz(2)*gradx(4) + gradz(3)*grady(4))

    else

      q_criterion =                                                            &
         -0.5_dp*(gradx(2)*gradx(2) + grady(3)*grady(3) + gradz(4)*gradz(4)) - &
                 (grady(2)*gradx(3) + gradz(2)*gradx(4) + gradz(3)*grady(4))


    end if

  end function q_criterion

!=============================== DQT_FLSQ ====================================80
!
! Linearization coefficients for bc face values in terms of
! T variables (var1, u, v, w, T, turb1, turb2, ...).
! ...assume   QT_face = diag( dqt )* QT_interior + diag( 1-dqt )*dQT_exterior
! ...so that dQT_face = diag( dqt )*dQT_interior
!
!=============================================================================80
  pure function dqt_flsq( ibc, n_q )

    use kinddefs,   only : system_i1
    use exact_defs, only : ic_exact
    use bc_names,   only : tangency, farfield_riem,                            &
                           viscous_solid, farfield_roe,                        &
                           symmetry_x, symmetry_y, symmetry_z,                 &
                           extrapolate, back_pressure,                         &
                           dirichlet, dirichlet_viscous, bc_null

    integer, intent(in) :: ibc, n_q

    integer(system_i1), dimension(n_q) :: dqt_flsq

  continue

!   Default weights to Neumann condition : q(face) = q(interior).

    dqt_flsq(1:n_q) = 1

    select case(ibc)

    case ( viscous_solid )

      dqt_flsq(2:n_q) = 0

    case ( dirichlet, dirichlet_viscous )

      dqt_flsq(1:n_q) = 0

    case ( symmetry_x )

      dqt_flsq(    2) = 0

    case ( symmetry_y, bc_null )

      dqt_flsq(    3) = 0

    case ( symmetry_z )

      dqt_flsq(    4) = 0

    case ( farfield_riem, farfield_roe,  &
           extrapolate, back_pressure )

      if ( ic_exact ) dqt_flsq(1:n_q) = 0

    case ( tangency )

      !default

    case default

      dqt_flsq(1) = -1

    end select

  end function dqt_flsq

module namelist_util

  implicit none

  private
  public :: nml_error

contains

!=============================== NML_ERROR ==================================80
!
! Stop for incomplete reads of namelist.
!
!=============================================================================80

  subroutine nml_error( iostat1, iostat2, path, name )

    use lmpi, only : lmpi_master, lmpi_conditional_stop

    integer,      intent(in) :: iostat1, iostat2
    character(*), intent(in) :: path
    character(*), intent(in) :: name

    integer :: ierr

    continue

    ierr = 0

    if ( lmpi_master ) then

      if ( iostat1 /= 0 ) then

        write(*,*) ' Problem opening namelist file: '//trim(path)
        write(*,*) ' No read of namelist: &'//trim(name),             &
                   ' iostat1=',iostat1

        ierr = iostat1

      elseif ( iostat2 == 0 ) then

!       successfully read namelist

      elseif ( iostat2 < 0 ) then

!       namelist not found

      else

        write(*,*) ' Probable incomplete read of namelist: &'//trim(name),     &
                   ' iostat2=',iostat2
        ierr = iostat2

      endif

    endif

    call lmpi_conditional_stop(ierr,'namelist_util:nml_error')

  end subroutine nml_error

end module namelist_util

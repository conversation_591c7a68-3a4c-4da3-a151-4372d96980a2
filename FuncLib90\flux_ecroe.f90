!================================= FLUX_ECROE ================================80
!
! This routine computes the fluxes using Roe's Entropy Consistent
! flux difference splitting and computes the contribution to the flux balance
!
! Note that this subroutine uses primitive variables
!
!=============================================================================80

  pure function flux_ecroe(xnorm, ynorm, znorm, area, ql, qr)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_half, my_1

    use fluid,           only : gm1, gamma, gm1g, gp1

    real(dp),               intent(in) :: xnorm, ynorm, znorm, area
    real(dp), dimension(5), intent(in) :: ql, qr
    real(dp), dimension(5)             :: flux_ecroe

    real(dp) :: rhol, rhor, ul, ur, vl, vr, wl, wr, pressl, pressr
    real(dp) :: q2l, q2r, cl, cr

    real(dp) :: sl, sr, unorml, unormr
    real(dp) :: logmean_z1, logmean_z5, mean_z1
    real(dp) :: b1, b2, b3
    real(dp) :: ca_nx, ca_ny, ca_nz
    real(dp) :: ra, ua, va, wa, pa
    real(dp) :: ca2, ca, qa2, ra_qna, pa2, Ha, qna_ca, qla, qma, qna

    real(dp), dimension (3,2) :: lm
    real(dp), dimension (5,5) :: R_EIGN
    real(dp), dimension (5)   :: zl, zr, lambda
    real(dp), dimension (5)   :: Fc, dV, RdV, DELF

    real(dp), parameter :: alpha = 0.2_dp
!   real(dp), parameter :: beta  = 0.1_dp

  continue

!   Get left and right state primitive variables

    rhol   = ql(1)
    ul     = ql(2)
    vl     = ql(3)
    wl     = ql(4)
    pressl = ql(5)

    rhor   = qr(1)
    ur     = qr(2)
    vr     = qr(3)
    wr     = qr(4)
    pressr = qr(5)

!   Compute the remaining needed left and right state variables:

    q2l    = ul*ul + vl*vl + wl*wl
    cl     = sqrt(gamma*pressl/rhol)

    q2r    = ur*ur + vr*vr + wr*wr
    cr     = sqrt(gamma*pressr/rhor)

!   Entropy at Left and Right cells

    sl = (log(pressl)-gamma*log(rhol))
    sr = (log(pressr)-gamma*log(rhor))

!   Compute the tangency vectors

    lm = tang_vecs(xnorm,ynorm,znorm)

!   Parameter Vectors at LEFT AND RIGHT STATES

    zl(1) = sqrt(rhol/pressl)
    zl(2) = zl(1) * ul
    zl(3) = zl(1) * vl
    zl(4) = zl(1) * wl
    zl(5) = zl(1) * pressl

    zr(1) = sqrt(rhor/pressr)
    zr(2) = zr(1) * ur
    zr(3) = zr(1) * vr
    zr(4) = zr(1) * wr
    zr(5) = zr(1) * pressr

!   NEW AVERAGES based on the parameter vectors

    LOGMEAN_z1 = log_mean(zl(1),zr(1))
    LOGMEAN_z5 = log_Mean(zl(5),zr(5))
       MEAN_z1 = my_half *(zl(1) + zr(1))
            ra = MEAN_z1 * LOGMEAN_z5
            ua = my_half *(zl(2) + zr(2)) / MEAN_z1
            va = my_half *(zl(3) + zr(3)) / MEAN_z1
            wa = my_half *(zl(4) + zr(4)) / MEAN_z1
            pa = my_half *(zl(5) + zr(5)) / MEAN_z1
           ca2 = gamma * pa / ra
            ca = sqrt(ca2)
           qa2 = ua*ua + va*va + wa*wa
           qna = ua*xnorm + va*ynorm + wa*znorm
!
!*************************************************************************
!* (2) Entropy-Conserving Flux, Fc
!*************************************************************************
!
    ra_qna = ra*qna
    pa2    = my_half/gamma * ( gp1*(LOGMEAN_z5/LOGMEAN_z1) + gm1*pa )

    Fc(1)  = ra_qna
    Fc(2)  = ra_qna * ua  +  pa * xnorm
    Fc(3)  = ra_qna * va  +  pa * ynorm
    Fc(4)  = ra_qna * wa  +  pa * znorm
    Fc(5)  = qna*( my_half*ra*qa2 + pa2/gm1g ) !=ra*qna*Ha : Ha with p2, not pa
!
!*************************************************************************
!* (3) Entropy generation term (Dissipation term).
!*************************************************************************
!   Scaling Factors for Eigenvectors>

    Ha = my_half*qa2 + pa2/(ra*gm1g)

    b1 = sqrt( ra * gm1g )
    b2 = sqrt( ra / gamma * my_half  )
    b3 = sqrt( pa )

!   RIGHT EIGENVECTORS

    ca_nx  = ca*xnorm
    ca_ny  = ca*ynorm
    ca_nz  = ca*znorm
    qna_ca = qna*ca
    qla    = ua*lm(1,1) + va*lm(2,1) + wa*lm(3,1)
    qma    = ua*lm(1,2) + va*lm(2,2) + wa*lm(3,2)

    R_EIGN(1,1) = my_1
    R_EIGN(2,1) = ua -  ca_nx
    R_EIGN(3,1) = va -  ca_ny
    R_EIGN(4,1) = wa -  ca_nz
    R_EIGN(5,1) = Ha - qna_ca

    R_EIGN(1,2) = my_1
    R_EIGN(2,2) = ua
    R_EIGN(3,2) = va
    R_EIGN(4,2) = wa
    R_EIGN(5,2) = my_half * qa2

    R_EIGN(1,3) = my_1
    R_EIGN(2,3) = ua +  ca_nx
    R_EIGN(3,3) = va +  ca_ny
    R_EIGN(4,3) = wa +  ca_nz
    R_EIGN(5,3) = Ha + qna_ca

    R_EIGN(1,4) = my_0
    R_EIGN(2,4) = lm(1,1)
    R_EIGN(3,4) = lm(2,1)
    R_EIGN(4,4) = lm(3,1)
    R_EIGN(5,4) = qla

    R_EIGN(1,5) = my_0
    R_EIGN(2,5) = lm(1,2)
    R_EIGN(3,5) = lm(2,2)
    R_EIGN(4,5) = lm(3,2)
    R_EIGN(5,5) = qma

!   The right scaling to the RIGHT EIGENVECTORS

    R_EIGN(:,1) = b2 * R_EIGN(:,1)
    R_EIGN(:,2) = b1 * R_EIGN(:,2)
    R_EIGN(:,3) = b2 * R_EIGN(:,3)
    R_EIGN(:,4) = b3 * R_EIGN(:,4)
    R_EIGN(:,5) = b3 * R_EIGN(:,5)

!   Difference in the Entropy Variables

    dV(1) = -(sr-sl)/gm1 - my_half*(rhor*q2r/pressr - rhol*q2l/pressl)
    dV(2) =  (rhor*ur/pressr - rhol*ul/pressl)
    dV(3) =  (rhor*vr/pressr - rhol*vl/pressl)
    dV(4) =  (rhor*wr/pressr - rhol*wl/pressl)
    dV(5) = -(rhor/pressr - rhol/pressl)

!   Wave Strength (RdV = R^t * dV)

    RdV(:) = R_EIGN(1,:)*dV(1)                                                 &
           + R_EIGN(2,:)*dV(2)                                                 &
           + R_EIGN(3,:)*dV(3)                                                 &
           + R_EIGN(4,:)*dV(4)                                                 &
           + R_EIGN(5,:)*dV(5)

!   Wave Speeds

    lambda(1) = abs(qna-ca)
    lambda(2) = abs(qna)
    lambda(3) = abs(qna+ca)

!   Additional Dissipation in the form of a modified wave speed.
!   Only for the acoustic waves. Alpha=0.2 is an empirical value.

    unormr   = ur*xnorm + vr*ynorm + wr*znorm
    unorml   = ul*xnorm + vl*ynorm + wl*znorm

    lambda(1) = lambda(1) + alpha * abs( (unormr - cr) - (unorml - cl) )
    lambda(3) = lambda(3) + alpha * abs( (unormr + cr) - (unorml + cl) )

!   beta     = 0.01_dp
!   lambda(2) = lambda(2) + beta * abs( (unormr + cr) - (unorml + cl) )

    lambda(4) = lambda(2)
    lambda(5) = lambda(2)

!   Dissipation Term: DF = R|lambda|dWS = R|lambda|L[u] or R|lambda|R^t[v]
!   where |lambda| = diag[lambda(1),lambda(2),lambda(3),lambda(4)]

    DELF(:) = lambda(1)*R_EIGN(:,1)*RdV(1)                                     &
            + lambda(2)*R_EIGN(:,2)*RdV(2)                                     &
            + lambda(3)*R_EIGN(:,3)*RdV(3)                                     &
            + lambda(4)*R_EIGN(:,4)*RdV(4)                                     &
            + lambda(5)*R_EIGN(:,5)*RdV(5)

!   Compute the contribution to the flux balance

    flux_ecroe = area * (Fc - my_half*DELF)

  end function flux_ecroe

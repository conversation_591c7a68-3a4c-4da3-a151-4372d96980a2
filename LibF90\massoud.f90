module massoud

  use kinddefs, only : dp, system_r8

  implicit none

  private

  public :: write_mdo_surface_data
  public :: load_massoud, set_up_massoud
  public :: pack_boundary_position
  public :: max_recl, dv_index, body_index
  public :: write_massoud_file

  integer, parameter :: max_recl = 30000 ! Max length of record for
                                         ! MASSOUD/bandaid file

  integer :: dv_index   = -9999   ! Which design variable to differentiate
                                  ! in gridmove and getgrad
  integer :: body_index = -9999   ! On which body dv_index refers to

  logical :: write_massoud_file = .false.

contains


!============================== WRITE_MDO_SURFACE_DATA ======================80
!
! Dumps out a tecplot or binary ddf file with x,y,z and l2g, and optionally,
! airloads data (Cp, Cfx, Cfy, Cfz, temperature, and heat flux (or dT/dn) for
! MASSOUD/DDF or other middleware to to use in MDO applications
!
!=============================================================================80

  subroutine write_mdo_surface_data(grid, soln, time_step, write_aero_data)

    use info_depr,            only : xmach, simulation_time
    use nml_nonlinear_solves, only : itime
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type, compressible, generic_gas,     &
                                     incompressible
    use lmpi,                 only : lmpi_master, lmpi_die
    use system_extensions,    only : se_open
    use fluid,                only : gamma
    use ivals,                only : q0
    use generic_gas_map,      only : n_temperature_j, n_pressure_k
    use io,                   only : prior_iters
    use grid_motion_helpers,  only : set_boundaries_for_mdo, mdo_body_type,    &
                                     get_inverse_transform, transform_coord,   &
                                     transform_vector, static_mesh_deform
    use nml_mdo_surface_data, only : massoud_file_format,                      &
                                     aero_loads_file_format,                   &
                                     massoud_output_freq,                      &
                                     massoud_use_initial_coords,               &
                                     aero_loads_output_freq,                   &
                                     aero_loads_use_initial_coords,            &
                                     output_transform, output_scale_factor,    &
                                     aero_loads_dynamic_pressure,              &
                                     include_time_info
    use file_utils,           only : available_unit
    use ddf_binary,           only : write_ddf_header, write_ddf_zone_header,  &
                                     write_ddf_point, write_ddf_element
    use aeroelastic,          only : airloads_handshake_file, structural_model

    integer,           intent(in)    :: time_step

    type(grid_type),   intent(inout) :: grid
    type(soln_type),   intent(inout) :: soln

    logical, optional, intent(in)    :: write_aero_data

    logical, save                              :: initialize = .true.
    logical, save                              :: seams_removed = .false.
    logical, save,   dimension(:), allocatable :: output_bndry
    logical, save                              :: append_timestep_mass
    logical, save                              :: append_timestep_elas
    logical                                    :: append_timestep

    logical, save                              :: use_strands_mass
    logical, save                              :: use_strands_elas
    logical                                    :: use_strands
    logical                                    :: use_initial_coords
    logical                                    :: airloads_written
    logical                                    :: found, found1, found2
    logical                                    :: found3, found4

    character(len=80)                          :: step, bnd, bod, zone_title
    character(len=14)                          :: time_str
    character(len=80)                          :: filename, fileroot
    character(len=5)                           :: extension
    character(len=256)                         :: format0, format1, format2
    character(len=80)                          :: title
    character(len=10)                          :: file_format

    integer                                    :: ib, j, iu, strandid
    integer                                    :: nface, nfacenodes
    integer                                    :: body, nloads
    integer                                    :: ind1, ind2, ind3, ind4, inode
    integer                                    :: n, n1, n2, n3, n4, k, l
    integer, save                              :: n_bodies

    integer, dimension(:),         allocatable :: unique_node
    integer, dimension(4)                      :: elem
    integer                                    :: id, pid, setid

    real(dp)                                   :: pinf, cp, temp, time_val
    real(dp),      dimension(6)                :: loads
    real(dp),      dimension(4,4)              :: inv_output_transform
    real(dp),      dimension(:),       pointer :: x => null()
    real(dp),      dimension(:),       pointer :: y => null()
    real(dp),      dimension(:),       pointer :: z => null()

    type(mdo_body_type), pointer, dimension(:), save :: output_body

  continue

!   initialization: set which boundaries are output to the file; any boundary
!   used for force calculations (i.e. solid walls) is by default assumed to
!   be output

    if (initialize) then

      allocate(output_bndry(grid%nbound))

      call set_boundaries_for_mdo(grid%nbound,   output_bndry,               &
                                  n_bodies,      output_body,                &
                                  grid%bc,       grid%nnodes01,              &
                                  grid%y,        ignore_twod_option=.true.)

      do ib = 1,grid%nbound
        if (output_bndry(ib)) then
          soln%global_bndry_data(ib)%used = .true.
        end if
      end do

      append_timestep_mass = .true.
      use_strands_mass     = .true.
      if (massoud_output_freq < 0) then
        append_timestep_mass = .false.
        use_strands_mass     = .false.
      end if

      append_timestep_elas = .true.
      use_strands_elas     = .true.
      if (aero_loads_output_freq < 0) then
        append_timestep_elas = .false.
        use_strands_elas     = .false.
      end if

      initialize = .false.

      return

    end if

    airloads_written = .false.
    if (present(write_aero_data)) airloads_written = write_aero_data

    if ( soln%eqn_set == compressible ) then
      pinf = 1.0_dp/gamma
    else
      pinf = 1.0_dp
    end if

    if (itime == 0) then
      time_val = real(time_step+prior_iters, dp)
    else
      time_val = simulation_time
    end if

    if (airloads_written) then
      file_format        = trim(aero_loads_file_format)
      append_timestep    = append_timestep_elas
      use_strands        = use_strands_elas
      use_initial_coords = aero_loads_use_initial_coords
    else
      file_format        = trim(massoud_file_format)
      append_timestep    = append_timestep_mass
      use_strands        = use_strands_mass
      use_initial_coords = massoud_use_initial_coords
    end if

!   to avoid excessive file name manipulations insde the code, when performing
!   aeroelastic simulations using an external structural model, just output
!   the generic file name without the time step in the name

    if (trim(structural_model) == 'external'         .or.                      &
        trim(structural_model) == 'external_static'  .or.                      &
        static_mesh_deform) then
        append_timestep = .false.
    end if

    extension = ".dat"
    if (trim(file_format) == 'stream') extension = ".ddfb"

    master_writes_data : if (lmpi_master) then

!     transform x,y,z and cfx,cfy,cfz into the desired output
!     coordinate system (all other output is scalar and is thus
!     independent of coordinate system); also scale x,y,z coordinates as
!     specified

!     note: scaling and transform applied in reverse order from similar
!     input operations

      do ib=1,grid%nbound

        if (.not. output_bndry(ib)) cycle

        if (use_initial_coords) then
          call transform_coord(soln%global_bndry_data(ib)%xat0global_bndry,    &
                               soln%global_bndry_data(ib)%yat0global_bndry,    &
                               soln%global_bndry_data(ib)%zat0global_bndry,    &
                               output_transform)
          soln%global_bndry_data(ib)%xat0global_bndry =                        &
                soln%global_bndry_data(ib)%xat0global_bndry*output_scale_factor
          soln%global_bndry_data(ib)%yat0global_bndry =                        &
                soln%global_bndry_data(ib)%yat0global_bndry*output_scale_factor
          soln%global_bndry_data(ib)%zat0global_bndry =                        &
                soln%global_bndry_data(ib)%zat0global_bndry*output_scale_factor
        else
          call transform_coord(soln%global_bndry_data(ib)%xglobal_bndry,       &
                               soln%global_bndry_data(ib)%yglobal_bndry,       &
                               soln%global_bndry_data(ib)%zglobal_bndry,       &
                               output_transform)
          soln%global_bndry_data(ib)%xglobal_bndry =                           &
                soln%global_bndry_data(ib)%xglobal_bndry*output_scale_factor
          soln%global_bndry_data(ib)%yglobal_bndry =                           &
                soln%global_bndry_data(ib)%yglobal_bndry*output_scale_factor
          soln%global_bndry_data(ib)%zglobal_bndry =                           &
                soln%global_bndry_data(ib)%zglobal_bndry*output_scale_factor
        end if

        if (airloads_written) then
          call transform_vector(soln%global_bndry_data(ib)%cfxglobal_bndry,    &
                                soln%global_bndry_data(ib)%cfyglobal_bndry,    &
                                soln%global_bndry_data(ib)%cfzglobal_bndry,    &
                                output_transform)
        end if

      end do

      if (n_bodies > 0 .and. .not. seams_removed) then

!       bodies that are composed of multiple boundaries will have usually
!       have duplicate nodes along shared seams - eliminate the duplicates

        body_loop_1 : do body = 1,n_bodies

          output_body(body)%nfacenodes = 0
          output_body(body)%nface      = 0

!         first get a conservative count (no duplicates eliminated yet)

          nfacenodes = 0
          nface      = 0
          do n=1,output_body(body)%nbndry
            ib = output_body(body)%bndry_list(n)
            nface       = nface      + soln%global_bndry_data(ib)%nface
            nfacenodes  = nfacenodes + soln%global_bndry_data(ib)%nfacenodes
          end do

          output_body(body)%nfacenodes = nfacenodes
          output_body(body)%nface      = nface

!         now allocate some memory to store info about the unique nodes on the
!         current body, checking that we haven't already included a node on an
!         earlier boundary (seams).

          if (.not. allocated(unique_node)) allocate(unique_node(nfacenodes))

          unique_node(:)   = 0

          allocate(output_body(body)%bndry_id_node(nfacenodes))
          allocate(output_body(body)%bndry_node(nfacenodes))

          output_body(body)%bndry_id_node(:) = 0
          output_body(body)%bndry_node(:)    = 0

          allocate(output_body(body)%f2n(4,nface))
          allocate(output_body(body)%bndry_id_face(nface))

          output_body(body)%f2n(:,:)         = 0
          output_body(body)%bndry_id_face(:) = 0

          nfacenodes = 0

!         keep only the (global id of) the *unique* nodes in unique_node;
!         when we identify a unique node, also store the boundary on which
!         that node sits, as well as the local node number on the boundary,
!         so that we only have to do this search one time.

          gather_body_nodes : do n=1,output_body(body)%nbndry

            ib = output_body(body)%bndry_list(n)

            do j = 1, soln%global_bndry_data(ib)%nfacenodes
              inode = nint(soln%global_bndry_data(ib)%l2gglobal_bndry(j))
              found = .false.
              search1 : do k = 1, nfacenodes
                if ( inode == unique_node(k) ) then
                  found = .true.
                  exit search1
                endif
              end do search1
              if ( .not. found ) then
                nfacenodes                                  = nfacenodes + 1
                output_body(body)%nfacenodes                = nfacenodes
                output_body(body)%bndry_id_node(nfacenodes) = ib
                output_body(body)%bndry_node(nfacenodes)    = j
                unique_node(nfacenodes)                     = inode
              end if
            end do

          end do gather_body_nodes

!         create a face-to-node mapping based on these unique nodes

          nface = 0

          gather_f2n : do n=1,output_body(body)%nbndry

            ib = output_body(body)%bndry_list(n)

            face_loop : do j=1,soln%global_bndry_data(ib)%nface

              tria_face_1 : if (soln%global_bndry_data(ib)%f2n(4,j) == 0) then

                nface = nface + 1

                ind1 = soln%global_bndry_data(ib)%f2n(1,j) ! indices into local
                ind2 = soln%global_bndry_data(ib)%f2n(2,j) ! boundary numbering
                ind3 = soln%global_bndry_data(ib)%f2n(3,j) ! set

                ! global node numbers
                n1 = nint(soln%global_bndry_data(ib)%l2gglobal_bndry(ind1))
                n2 = nint(soln%global_bndry_data(ib)%l2gglobal_bndry(ind2))
                n3 = nint(soln%global_bndry_data(ib)%l2gglobal_bndry(ind3))

                found1 = .false.
                found2 = .false.
                found3 = .false.

                search3 : do k = 1, output_body(body)%nfacenodes

                  if ( .not.found1 .and.                                       &
                        n1 == unique_node(k) ) then
                    ind1 = k
                    found1 = .true.
                    cycle search3
                  endif

                  if ( .not.found2 .and.                                       &
                        n2 == unique_node(k) ) then
                    ind2 = k
                    found2 = .true.
                    cycle search3
                  endif

                  if ( .not.found3 .and.                                       &
                       n3 == unique_node(k) ) then
                    ind3 = k
                    found3 = .true.
                    cycle search3
                  endif

                  if ( found1 .and. found2 .and. found3 ) exit search3

                end do search3

                if ( (.not.found1) .or. (.not.found2) .or. (.not.found3) ) then
                  write(*,*) 'Trouble with duplicate node elimination in'
                  write(*,*) 'write_mdo_surface_data...'
                  call lmpi_die
                  stop
                else
                  output_body(body)%bndry_id_face(nface) = ib
                  output_body(body)%f2n(1,nface) = ind1
                  output_body(body)%f2n(2,nface) = ind2
                  output_body(body)%f2n(3,nface) = ind3
                  output_body(body)%f2n(4,nface) = 0    ! flags face as tria
                end if

              end if tria_face_1

              quad_face_1 : if (soln%global_bndry_data(ib)%f2n(4,j) /= 0) then

                nface = nface + 1

                ind1 = soln%global_bndry_data(ib)%f2n(1,j) ! indices into local
                ind2 = soln%global_bndry_data(ib)%f2n(2,j) ! boundary numbering
                ind3 = soln%global_bndry_data(ib)%f2n(3,j) ! set
                ind4 = soln%global_bndry_data(ib)%f2n(4,j)

                ! global node numbers
                n1 = nint(soln%global_bndry_data(ib)%l2gglobal_bndry(ind1))
                n2 = nint(soln%global_bndry_data(ib)%l2gglobal_bndry(ind2))
                n3 = nint(soln%global_bndry_data(ib)%l2gglobal_bndry(ind3))
                n4 = nint(soln%global_bndry_data(ib)%l2gglobal_bndry(ind4))

                found1 = .false.
                found2 = .false.
                found3 = .false.
                found4 = .false.

                search4 : do k = 1, output_body(body)%nfacenodes

                  if ( .not.found1 .and.                                       &
                       n1 == unique_node(k) ) then
                    ind1 = k
                    found1 = .true.
                    cycle search4
                  endif

                  if ( .not.found2 .and.                                       &
                       n2 == unique_node(k) ) then
                    ind2 = k
                    found2 = .true.
                    cycle search4
                  endif

                  if ( .not.found3 .and.                                       &
                       n3 == unique_node(k) ) then
                    ind3 = k
                    found3 = .true.
                    cycle search4
                  endif

                  if ( .not.found4 .and.                                       &
                       n4 == unique_node(k) ) then
                    ind4 = k
                    found4 = .true.
                    cycle search4
                  endif

                  if (found1 .and. found2 .and. found3 .and. found4)exit search4

                end do search4

                if ( (.not.found1) .or. (.not.found2) .or. (.not.found3) .or.  &
                     (.not.found4) ) then
                  write(*,*) 'Trouble with duplicate node elimination in'
                  write(*,*) 'write_mdo_surface_data...'
                  call lmpi_die
                  stop
                else
                  output_body(body)%f2n(1,nface) = ind1
                  output_body(body)%f2n(2,nface) = ind2
                  output_body(body)%f2n(3,nface) = ind3
                  output_body(body)%f2n(4,nface) = ind4
                endif

              end if quad_face_1

            end do face_loop

          end do gather_f2n

          deallocate(unique_node)

        end do body_loop_1

        seams_removed = .true.

      end if

      type_of_data_1 : if (airloads_written) then

        write(*,*)
        if ( trim(file_format) == 'stream' ) then
          write(*,'(a,i5)') " Writing aero loads to binary file" //            &
                            " for time step ", time_step
        else
          write(*,'(a,i5)') " Writing aero loads to tecplot file" //           &
                            " for time step ", time_step
        end if

      else   type_of_data_1

        write(*,*)
        if ( trim(file_format) == 'stream' ) then
          write(*,'(a,i5)') " Writing massoud data to binary file" //          &
                            " for time step ", time_step
        else
          write(*,'(a,i5)') " Writing massoud data to tecplot file" //         &
                            " for time step ", time_step
        end if

      end if type_of_data_1

      if (use_initial_coords) then
         write(*,'(a)') " (x,y,z are initial surface coordinates)"
      else
         write(*,'(a)') " (x,y,z are current surface coordinates)"
      end if

      iu = 83

      if (append_timestep) then
        write(step,"(i0)")    time_step+prior_iters
        step = '_timestep' // trim(adjustl(step))
      else
        step = ''
      end if

      format0 = "('zone t=',a,', i=',i0,', j=',i0,', f=fepoint')"
      format1 = "('zone t=',a,', i=',i0,', j=',i0,                             &
                &', f=fepoint,  solutiontime=',e14.7,', strandid=',i0)"
      format2 = '(2x, 3(e23.15e3,1x), i10, 1x, 10(e23.15e3,1x))'

      if (simulation_time < 0._dp   ) then
        write(time_str,"(i0)") time_step+prior_iters
      else
        write(time_str,"(e14.7)") simulation_time
      end if

      nloads = 0
      if (airloads_written) nloads = 6

      output_individual_bodies : if (n_bodies > 0) then

        body_loop_2 : do body = 1,n_bodies

          strandid = body + 2000
          if (.not. use_strands) strandid = 0

          write(bod,"(i0)") body

          type_of_data_2 : if (airloads_written) then

            if ( soln%eqn_set /= generic_gas ) then
              title = 'aero loads for ddfdrive'
            else if ( soln%eqn_set == generic_gas ) then
              title = 'aero loads (generic gas) for ddfdrive'
            endif

            fileroot = trim(grid%project)//'_ddfdrive_body'//trim(adjustl(bod))
            filename = trim(fileroot)//trim(adjustl(step))//extension

            if ( trim(file_format) == 'stream' ) then
              call write_ddf_header(filename, title, 1, time_val)
            else
              iu = available_unit()
              call se_open(iu,file=filename)

              write(iu,'(3a)') 'title="',trim(title),'"'
              if ( soln%eqn_set /= generic_gas ) then
                write(iu,'(3a)') 'variables="x","y","z","id","cp","cfx",' //   &
                                 '"cfy","cfz","temp","dtdn"'
              else if ( soln%eqn_set == generic_gas ) then
                write(iu,'(3a)') 'variables="x","y","z","id","cp","cfx",' //   &
                                 '"cfy","cfz","temp","heat_flux"'
              end if
            end if

          else   type_of_data_2

            fileroot = trim(grid%project)//'_massoud_body'//trim(adjustl(bod))
            filename = trim(fileroot)//trim(adjustl(step))//extension

            if ( trim(file_format) == 'stream' ) then
              title = 'surface points and l2g id for massoud'
              call write_ddf_header(filename, title, 1, time_val)
            else
              iu = available_unit()
              call se_open(iu,file=filename)

              write(iu,'(a)') 'title="surface points and l2g id for massoud"'
              write(iu,'(3a)') 'variables="x","y","z","id"'
            end if

          end if  type_of_data_2

          if ( trim(file_format) == 'stream' ) then
            title = 'time ' // trim(adjustl(time_str))
            call write_ddf_zone_header(title, output_body(body)%nfacenodes,    &
                                       nloads, output_body(body)%nface)
          else
            zone_title = '"mdo body ' // trim(adjustl(bod)) // '"'
            if (include_time_info) then
              write(iu,format1) trim(adjustl(zone_title)),                     &
                                output_body(body)%nfacenodes,                  &
                                output_body(body)%nface, time_val, strandid
            else
              write(iu,format0) trim(adjustl(zone_title)),                     &
                                output_body(body)%nfacenodes,                  &
                                output_body(body)%nface
            end if
          end if

          data_loop : do n=1,output_body(body)%nfacenodes

            ib = output_body(body)%bndry_id_node(n)
            j  = output_body(body)%bndry_node(n)

!           for airloads output, use either the initial (undeflected)
!           coordinates output or the current values

            x => null()
            y => null()
            z => null()

            if (use_initial_coords) then
              x => soln%global_bndry_data(ib)%xat0global_bndry
              y => soln%global_bndry_data(ib)%yat0global_bndry
              z => soln%global_bndry_data(ib)%zat0global_bndry
            else
              x => soln%global_bndry_data(ib)%xglobal_bndry
              y => soln%global_bndry_data(ib)%yglobal_bndry
              z => soln%global_bndry_data(ib)%zglobal_bndry
            end if

            type_of_data_3 : if (airloads_written) then

              physics_type_1 : select case(soln%eqn_set)

              case (compressible)
                cp = 2._dp*(                                                   &
                     soln%global_bndry_data(ib)%qglobal_bndry(5,j)/pinf        &
                     - 1._dp ) / (gamma*xmach*xmach)
                temp = gamma*                                                  &
                       soln%global_bndry_data(ib)%qglobal_bndry(5,j) /         &
                       soln%global_bndry_data(ib)%qglobal_bndry(1,j)

              case (incompressible)
                cp = 2._dp*(                                                   &
                     soln%global_bndry_data(ib)%qglobal_bndry(1,j)/pinf        &
                     - 1._dp)
                temp = 1.0_dp

              case (generic_gas)
              cp = 2._dp*(                                                     &
                   soln%global_bndry_data(ib)%qglobal_bndry(n_pressure_k(1),j) &
                   - q0(n_pressure_k(1)) )
              temp =                                                           &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_temperature_j(1),j)

              case default

                cp   = 0._dp
                temp = 0._dp

              end select physics_type_1

              loads(1) = cp
              loads(2) = soln%global_bndry_data(ib)%cfxglobal_bndry(j)
              loads(3) = soln%global_bndry_data(ib)%cfyglobal_bndry(j)
              loads(4) = soln%global_bndry_data(ib)%cfzglobal_bndry(j)
              loads(5) = temp
              loads(6) = soln%global_bndry_data(ib)%cqglobal_bndry(j)

!             convert force coefficients to forces via the dynamic pressure

              loads(1) = loads(1)*aero_loads_dynamic_pressure
              loads(2) = loads(2)*aero_loads_dynamic_pressure
              loads(3) = loads(3)*aero_loads_dynamic_pressure
              loads(4) = loads(4)*aero_loads_dynamic_pressure

              if ( trim(file_format) == 'stream' ) then

                call write_ddf_point(x(j), y(j), z(j),                         &
                        nint(soln%global_bndry_data(ib)%l2gglobal_bndry(j)),   &
                        loads)

              else

                write(iu,format2) x(j), y(j), z(j),                            &
                      nint(soln%global_bndry_data(ib)%l2gglobal_bndry(j)),     &
                      (loads(l), l=1,nloads)

              end if

            else   type_of_data_3

              if ( trim(file_format) == 'stream' ) then
                call write_ddf_point(x(j), y(j), z(j),                         &
                      nint(soln%global_bndry_data(ib)%l2gglobal_bndry(j)))
              else
                write(iu,format2) x(j), y(j), z(j),                            &
                      nint(soln%global_bndry_data(ib)%l2gglobal_bndry(j))
              end if

            end if type_of_data_3

          end do data_loop

          connectivity_loop : do j=1,output_body(body)%nface

            tria_face_2 : if (output_body(body)%f2n(4,j) == 0) then

              ind1 = output_body(body)%f2n(1,j)
              ind2 = output_body(body)%f2n(2,j)
              ind3 = output_body(body)%f2n(3,j)

              if ( trim(file_format) == 'stream' ) then
                id  = 0
                pid = 0
                setid = output_body(body)%bndry_id_face(j)
                elem(1) = ind1
                elem(2) = ind2
                elem(3) = ind3
                elem(4) = ind3  ! massoud convention: repeat last index of tria
                call write_ddf_element(3, id, pid, setid, elem)
              else
                write(iu,'(4i10)') ind1, ind2, ind3, ind3
              endif

            end if tria_face_2

            quad_face_2 : if (output_body(body)%f2n(4,j) /= 0) then

              ind1 = output_body(body)%f2n(1,j)
              ind2 = output_body(body)%f2n(2,j)
              ind3 = output_body(body)%f2n(3,j)
              ind4 = output_body(body)%f2n(4,j)

              if ( trim(file_format) == 'stream' ) then
                id  = 0
                pid = 0
                setid = output_body(body)%bndry_id_face(j)
                elem(1) = ind1
                elem(2) = ind2
                elem(3) = ind3
                elem(4) = ind4
                call write_ddf_element(4, id, pid, setid, elem)
              else
                write(iu,'(4i10)') ind1, ind2, ind3, ind4
              endif

            end if quad_face_2

          end do connectivity_loop

          if ( trim(file_format) /= 'stream' ) close(iu)

        end do body_loop_2

      else output_individual_bodies  ! output individual boundaries, not bodies

        bndry_loop : do ib=1,grid%nbound

          if (.not.output_bndry(ib)) cycle

!         for airloads output, use either the initial (undeflected)
!         coordinates output or the current values

          x => null()
          y => null()
          z => null()

          if (use_initial_coords) then
            x => soln%global_bndry_data(ib)%xat0global_bndry
            y => soln%global_bndry_data(ib)%yat0global_bndry
            z => soln%global_bndry_data(ib)%zat0global_bndry
          else
            x => soln%global_bndry_data(ib)%xglobal_bndry
            y => soln%global_bndry_data(ib)%yglobal_bndry
            z => soln%global_bndry_data(ib)%zglobal_bndry
          end if

          strandid = ib + 2000
          if (.not. use_strands) strandid = 0

          write(bnd,"(i0)") ib

          nface      = soln%global_bndry_data(ib)%nface
          nfacenodes = soln%global_bndry_data(ib)%nfacenodes

          type_of_data_4 : if (airloads_written) then

            if ( soln%eqn_set /= generic_gas ) then
              title = 'aero loads for ddfdrive'
            else if ( soln%eqn_set == generic_gas ) then
              title = 'aero loads (generic gas) for ddfdrive'
            end if

            fileroot = trim(grid%project)//'_ddfdrive_bndry'//trim(adjustl(bnd))
            filename = trim(fileroot)//trim(adjustl(step))//extension

            if ( trim(aero_loads_file_format) == 'stream' ) then
              call write_ddf_header(filename, title, 1, time_val)
            else
              iu = available_unit()
              call se_open(iu,file=filename)

              write(iu,'(3a)') 'title="',trim(title),'"'
              if ( soln%eqn_set /= generic_gas ) then
                write(iu,'(3a)') 'variables="x","y","z","id","cp","cfx",' //   &
                                 '"cfy","cfz","temp","dtdn"'
              else if ( soln%eqn_set == generic_gas ) then
                write(iu,'(3a)') 'variables="x","y","z","id","cp","cfx",' //   &
                                 '"cfy","cfz","temp","heat_flux"'
              end if
            end if

            title = 'time '      // trim(adjustl(time_str)) //                 &
                    ' boundary ' // trim(adjustl(bnd))

            if ( trim(aero_loads_file_format) == 'stream' ) then
              call write_ddf_zone_header(title, nfacenodes, 6, nface)
            else
              zone_title = '"mdo boundary ' // trim(adjustl(bnd)) // '"'
              if (include_time_info) then
                write(iu,format1) trim(adjustl(zone_title)), nfacenodes,       &
                                  nface, time_val, strandid
              else
                write(iu,format0) trim(adjustl(zone_title)), nfacenodes, nface
              end if
            end if

            nodeloop_1 : do j=1,nfacenodes

              physics_type_2 : select case(soln%eqn_set)

                case (compressible)
                  cp = 2._dp*(                                                 &
                             soln%global_bndry_data(ib)%qglobal_bndry(5,j)/pinf&
                             - 1._dp ) / (gamma*xmach*xmach)
                  temp = gamma*                                                &
                               soln%global_bndry_data(ib)%qglobal_bndry(5,j) / &
                               soln%global_bndry_data(ib)%qglobal_bndry(1,j)

                case (incompressible)
                  cp = 2._dp*(                                                 &
                             soln%global_bndry_data(ib)%qglobal_bndry(1,j)/pinf&
                             - 1._dp)
                  temp = 1.0_dp

                case (generic_gas)
                  cp = 2._dp*(                                                 &
                    soln%global_bndry_data(ib)%qglobal_bndry(n_pressure_k(1),j)&
                    - q0(n_pressure_k(1)) )
                  temp =                                                       &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_temperature_j(1),j)

                case default

                  cp   = 0._dp
                  temp = 0._dp

              end select physics_type_2

              loads(1) = cp
              loads(2) = soln%global_bndry_data(ib)%cfxglobal_bndry(j)
              loads(3) = soln%global_bndry_data(ib)%cfyglobal_bndry(j)
              loads(4) = soln%global_bndry_data(ib)%cfzglobal_bndry(j)
              loads(5) = temp
              loads(6) = soln%global_bndry_data(ib)%cqglobal_bndry(j)

!             convert force coefficients to forces via the dynamic pressure

              loads(1) = loads(1)*aero_loads_dynamic_pressure
              loads(2) = loads(2)*aero_loads_dynamic_pressure
              loads(3) = loads(3)*aero_loads_dynamic_pressure
              loads(4) = loads(4)*aero_loads_dynamic_pressure

              if ( trim(file_format) == 'stream' ) then

                call write_ddf_point(x(j), y(j), z(j),                         &
                        nint(soln%global_bndry_data(ib)%l2gglobal_bndry(j)),   &
                        loads)

              else

                write(iu,format2) x(j), y(j), z(j),                            &
                      nint(soln%global_bndry_data(ib)%l2gglobal_bndry(j)),     &
                      (loads(l), l=1,nloads)

              end if

            end do nodeloop_1

          else   type_of_data_4

            fileroot = trim(grid%project)//'_massoud_bndry'//trim(adjustl(bnd))
            filename = trim(fileroot)//trim(adjustl(step))//extension

            if ( trim(file_format) == 'stream' ) then
              title = 'surface points and l2g id for massoud'
              call write_ddf_header(filename, title, 1, time_val)

              title = 'time ' // trim(adjustl(time_str)) //                    &
                      ' boundary ' // trim(adjustl(bnd))
              call write_ddf_zone_header(title, nfacenodes, 0, nface)
            else
              iu = available_unit()
              call se_open(iu,file=filename)

              write(iu,'(a)') 'title="surface points and l2g id for massoud"'
              write(iu,'(3a)') 'variables="x","y","z","id"'

              zone_title = '"mdo boundary ' // trim(adjustl(bnd)) // '"'
              if (include_time_info) then
                write(iu,format1) trim(adjustl(zone_title)),                   &
                                  nfacenodes, nface, time_val, strandid
              else
                write(iu,format0) trim(adjustl(zone_title)), nfacenodes, nface
              end if
            end if

            nodeloop_2 : do j=1,nfacenodes

              if ( trim(file_format) == 'stream' ) then

                call write_ddf_point(x(j), y(j), z(j),                         &
                           nint(soln%global_bndry_data(ib)%l2gglobal_bndry(j)))

              else

                write(iu,format2) x(j), y(j), z(j),                            &
                           nint(soln%global_bndry_data(ib)%l2gglobal_bndry(j))
              endif

            end do nodeloop_2

          end if type_of_data_4

          tria_face_loop : do j=1,nface

            if (soln%global_bndry_data(ib)%f2n(4,j) == 0) then

              ind1 = soln%global_bndry_data(ib)%f2n(1,j)
              ind2 = soln%global_bndry_data(ib)%f2n(2,j)
              ind3 = soln%global_bndry_data(ib)%f2n(3,j)

              if ( trim(file_format) == 'stream' ) then
                id  = 0
                pid = 0
                setid = output_body(body)%bndry_id_face(j)
                elem(1) = ind1
                elem(2) = ind2
                elem(3) = ind3
                elem(4) = ind3  ! massoud convention: repeat last index of tria
                call write_ddf_element(3, id, pid, setid, elem)
              else
                write(iu,'(4i10)') ind1, ind2, ind3, ind3
              endif

            end if

          end do tria_face_loop

          quad_face_loop : do j=1,nface

            if (soln%global_bndry_data(ib)%f2n(4,j) /= 0) then

              ind1 = soln%global_bndry_data(ib)%f2n(1,j)
              ind2 = soln%global_bndry_data(ib)%f2n(2,j)
              ind3 = soln%global_bndry_data(ib)%f2n(3,j)
              ind4 = soln%global_bndry_data(ib)%f2n(4,j)

              if ( trim(file_format) == 'stream' ) then
                id  = 0
                pid = 0
                setid = output_body(body)%bndry_id_face(j)
                elem(1) = ind1
                elem(2) = ind2
                elem(3) = ind3
                elem(4) = ind4
                call write_ddf_element(4, id, pid, setid, elem)
              else
                write(iu,'(4i10)') ind1, ind2, ind3, ind4
              endif

            end if

          end do quad_face_loop

          if ( trim(file_format) /= 'stream' ) close(iu)

        end do bndry_loop

      end if output_individual_bodies

!     create a "handshake" file to signal that new airloads are available

      if (airloads_written) then
        filename = trim(airloads_handshake_file)
        iu = available_unit()
        call se_open(iu,file=filename)
        write(iu,*) simulation_time
        close(iu)
      end if

!     undo any coordinate scaling and revert x,y,z and cfx,cfy,cfz into the
!     original coordinate system (note: this order of operation is done in
!     reverse order from the original transform and scaling above)

      call get_inverse_transform(output_transform, inv_output_transform)

      do ib=1,grid%nbound

        if (.not. output_bndry(ib)) cycle

        if (use_initial_coords) then
          soln%global_bndry_data(ib)%xat0global_bndry =                        &
                soln%global_bndry_data(ib)%xat0global_bndry/output_scale_factor
          soln%global_bndry_data(ib)%yat0global_bndry =                        &
                soln%global_bndry_data(ib)%yat0global_bndry/output_scale_factor
          soln%global_bndry_data(ib)%zat0global_bndry =                        &
                soln%global_bndry_data(ib)%zat0global_bndry/output_scale_factor
          call transform_coord(soln%global_bndry_data(ib)%xat0global_bndry,    &
                               soln%global_bndry_data(ib)%yat0global_bndry,    &
                               soln%global_bndry_data(ib)%zat0global_bndry,    &
                               inv_output_transform)
        else
          soln%global_bndry_data(ib)%xglobal_bndry =                           &
                soln%global_bndry_data(ib)%xglobal_bndry/output_scale_factor
          soln%global_bndry_data(ib)%yglobal_bndry =                           &
                soln%global_bndry_data(ib)%yglobal_bndry/output_scale_factor
          soln%global_bndry_data(ib)%zglobal_bndry =                           &
                soln%global_bndry_data(ib)%zglobal_bndry/output_scale_factor
          call transform_coord(soln%global_bndry_data(ib)%xglobal_bndry,       &
                               soln%global_bndry_data(ib)%yglobal_bndry,       &
                               soln%global_bndry_data(ib)%zglobal_bndry,       &
                               inv_output_transform)
        end if

        if (airloads_written) then
          call transform_vector(soln%global_bndry_data(ib)%cfxglobal_bndry,    &
                                soln%global_bndry_data(ib)%cfyglobal_bndry,    &
                                soln%global_bndry_data(ib)%cfzglobal_bndry,    &
                                inv_output_transform)
        end if

      end do

    end if master_writes_data

  end subroutine write_mdo_surface_data


!============================ LOAD_MASSOUD ===================================80
!
! Reads parameterization data
!
!=============================================================================80

  subroutine load_massoud(mass,design,g,ngrid,ialloc_arg)

    use design_types,         only : design_type
    use allocations,          only : my_alloc_ptr
    use lmpi,                 only : lmpi_master, lmpi_bcast, lmpi_die,        &
                                     lmpi_conditional_stop
    use info_depr,            only : complex_mode, complex_epsilon
    use nml_global,           only : moving_grid
    use string_utils,         only : geti, int_to_s
    use grid_types,           only : mass_type
    use file_utils,           only : available_unit
    use nml_code_run_control, only : sd_file_format
    use ddf_binary,           only : read_ddf_header, read_ddf_zone_header,    &
                                     read_ddf_point, read_ddf_complete

    integer, intent(in) :: g, ngrid

    integer, optional, intent(in) :: ialloc_arg

    type(design_type), intent(in) :: design

    type(mass_type), dimension(design%nbodies,ngrid), intent(inout) :: mass

    integer :: ialloc, iostat, n

    integer :: iunit, i, j, ibody, highest_index_found

    integer :: itotal, test_ndv, nzone, nelem

    real(system_r8),dimension(:),  pointer:: real_xmt, real_ymt, real_zmt
    real(system_r8),dimension(:),  pointer:: real_xmtD, real_ymtD, real_zmtD
    real(system_r8),dimension(:),  pointer:: real_xTempD,real_yTempD,real_zTempD
    real(system_r8),dimension(:),  pointer:: baseline_x, baseline_y, baseline_z
    real(system_r8),dimension(:),  pointer:: realx, realy, realz
    real(system_r8),dimension(:,:),pointer:: dx, dy, dz

    real(dp) :: soln_time

    character(len=80) :: filename, line

    logical, dimension(:), pointer :: fake_body

  continue

    if (present(ialloc_arg) ) then
      ialloc = ialloc_arg
    else
      ialloc = 1
    endif
    iostat = 0

! If this is a moving grid case, some bodies may be "fake" to provide parent
! motion of some kind.  In this case, we need to identify fake bodies and avoid
! trying to find a model.tec file for them, since it won't be there.  Simply set
! itotal=0 for such bodies.

    call my_alloc_ptr(fake_body, design%nbodies)
    fake_body(:) = .false.

    if ( moving_grid ) then

      do i = 1, design%nbodies

        highest_index_found = 0

        search : do j = 1, design%nbodies

          if ( j == i ) cycle search

          if ( design%body_data(j)%body_name == '' .or.                        &
               design%body_data(i)%body_name == '' ) then
            write(*,*) 'Error: body_name not established.'
            call lmpi_die
            stop
          endif

          if ( trim(design%body_data(j)%body_name) ==                          &
               trim(design%body_data(i)%body_name) ) then
            fake_body(i) = .true.
            fake_body(j) = .true.
            highest_index_found = max(i,j)
          endif

        end do search

        if ( highest_index_found > 0 ) then
          fake_body(highest_index_found) = .false.
        endif

      end do

    endif

    body_loop : do ibody = 1, design%nbodies

      if ( fake_body(ibody) ) then
        mass(ibody,g)%itotal = 0
        if (ialloc == 1 ) call set_up_massoud(mass(ibody,g))
        cycle body_loop
      endif

      master_open_file: if (lmpi_master) then

        select case(design%body_data(ibody)%parameterization)
        case(0)   ! fake body; already cycled above
        case(1,3,4,5)
          if ( trim(sd_file_format) == 'stream' ) then
            filename= '../Rubberize/model.ddf.' // trim(int_to_s(ibody)) //    &
                      '.sd1'
          else
            filename= '../Rubberize/model.tec.' // trim(int_to_s(ibody)) //    &
                      '.sd1'
          endif
        case(2)
          filename= '../Rubberize/bandaid.data.' // trim(int_to_s(ibody))
        end select

        write(*,*)"reading:",trim(filename)

        iostat = 0
        iunit = available_unit()

        if ( trim(sd_file_format) /= 'stream' ) then
          open(iunit,file=filename,status='old',recl=max_recl,iostat=iostat)
        endif

        if (iostat /= 0) write (*,*) ' Error: file not found...stopping'

      endif master_open_file

      call lmpi_conditional_stop(iostat)

      if ( lmpi_master ) then

        rewind(iunit)

        select case(design%body_data(ibody)%parameterization)
        case(0)   ! fake body; already cycled above
        case(1,3,4,5)
          if ( trim(sd_file_format) == 'stream' ) then
            call read_ddf_header(filename, nzone, soln_time)
            if (nzone  /= 1) write (*,*) ' Error: multiple zones...stopping'

            call read_ddf_zone_header(itotal, test_ndv, nelem)

            mass(ibody,g)%itotal = itotal
            if ( test_ndv /= 3*design%body_data(ibody)%ndv ) then
              write(*,*) 'Wrong number of design variables in ',trim(filename)
              call lmpi_die
            endif
          else
            read(iunit,*)
            read(iunit,*)
            read(iunit,'(a80)') line
            mass(ibody,g)%itotal = geti(line,' I')
          endif
        case(2)
          read(iunit,*) test_ndv, mass(ibody,g)%itotal
          if ( test_ndv /= design%body_data(ibody)%ndv ) then
            write(*,*) 'Wrong number of design variables in ',trim(filename)
            call lmpi_die
          endif
        end select

        write(*,*) trim(filename)," itotal = ", mass(ibody,g)%itotal

      endif

      call lmpi_bcast(mass(ibody,g)%itotal)

      if (ialloc == 1 ) call set_up_massoud(mass(ibody,g))

      read_stuff : if(lmpi_master) then

        select case(design%body_data(ibody)%parameterization)
        case(0)   ! fake body; already cycled above
        case(1,3,4,5)

          call my_alloc_ptr(real_xmt,     mass(ibody,g)%itotal)
          call my_alloc_ptr(real_ymt,     mass(ibody,g)%itotal)
          call my_alloc_ptr(real_zmt,     mass(ibody,g)%itotal)

          if ( complex_mode ) then           ! Read sensitivities

            call my_alloc_ptr(real_xTempD,     design%body_data(ibody)%ndv)
            call my_alloc_ptr(real_yTempD,     design%body_data(ibody)%ndv)
            call my_alloc_ptr(real_zTempD,     design%body_data(ibody)%ndv)
            call my_alloc_ptr(real_xmtD,       mass(ibody,g)%itotal)
            call my_alloc_ptr(real_ymtD,       mass(ibody,g)%itotal)
            call my_alloc_ptr(real_zmtD,       mass(ibody,g)%itotal)

            do j = 1, mass(ibody,g)%itotal

              if ( trim(sd_file_format) == 'stream' ) then
                call read_ddf_point(real_xmt(j), real_ymt(j), real_zmt(j),     &
                                    mass(ibody,g)%inodemt(j),                  &
                                    real_xTempD, real_yTempD, real_zTempD)
              else
                read(iunit,*) real_xmt(j), real_ymt(j), real_zmt(j),           &
                              mass(ibody,g)%inodemt(j),                        &
                             (real_xTempD(i),real_yTempD(i),real_zTempD(i),    &
                              i = 1, design%body_data(ibody)%ndv)
              endif

              if ( body_index == ibody ) then
                real_xmtD(j) = real_xTempD(dv_index)
                real_ymtD(j) = real_yTempD(dv_index)
                real_zmtD(j) = real_zTempD(dv_index)
              endif
            end do

            mass(ibody,g)%xmt = cmplx( real(real_xmt,dp),                      &
                                       real(real_xmtD*complex_epsilon,dp), dp )
            mass(ibody,g)%ymt = cmplx( real(real_ymt,dp),                      &
                                       real(real_ymtD*complex_epsilon,dp), dp )
            mass(ibody,g)%zmt = cmplx( real(real_zmt,dp),                      &
                                       real(real_zmtD*complex_epsilon,dp), dp )

            deallocate(real_xTempD,real_yTempD,real_zTempD)
            deallocate(real_xmtD,real_ymtD,real_zmtD)

          else                                                ! Read coords

            do j = 1, mass(ibody,g)%itotal
              if ( trim(sd_file_format) == 'stream' ) then
                call read_ddf_point(real_xmt(j), real_ymt(j), real_zmt(j),     &
                                    mass(ibody,g)%inodemt(j))
              else
                read(iunit,*) real_xmt(j), real_ymt(j), real_zmt(j),           &
                              mass(ibody,g)%inodemt(j)
              endif
            end do

            mass(ibody,g)%xmt = real_xmt
            mass(ibody,g)%ymt = real_ymt
            mass(ibody,g)%zmt = real_zmt

          endif

          deallocate(real_xmt)
          deallocate(real_ymt)
          deallocate(real_zmt)

        case(2)

          call my_alloc_ptr(baseline_x,mass(ibody,g)%itotal)
          call my_alloc_ptr(baseline_y,mass(ibody,g)%itotal)
          call my_alloc_ptr(baseline_z,mass(ibody,g)%itotal)
          call my_alloc_ptr(dx,mass(ibody,g)%itotal,design%body_data(ibody)%ndv)
          call my_alloc_ptr(dy,mass(ibody,g)%itotal,design%body_data(ibody)%ndv)
          call my_alloc_ptr(dz,mass(ibody,g)%itotal,design%body_data(ibody)%ndv)

          get_data : do j = 1, mass(ibody,g)%itotal
            read(iunit,*) baseline_x(j), baseline_y(j), baseline_z(j),         &
                          mass(ibody,g)%inodemt(j),                            &
                         (dx(j,n),dy(j,n),dz(j,n),                             &
                          n = 1, design%body_data(ibody)%ndv)
          end do get_data

          call my_alloc_ptr(realx, mass(ibody,g)%itotal)
          call my_alloc_ptr(realy, mass(ibody,g)%itotal)
          call my_alloc_ptr(realz, mass(ibody,g)%itotal)

          add_design : do i = 1, mass(ibody,g)%itotal
            realx(i) = baseline_x(i)
            realy(i) = baseline_y(i)
            realz(i) = baseline_z(i)
            loop_design : do n = 1, design%body_data(ibody)%ndv
             realx(i)=realx(i)+dx(i,n)*real(design%body_data(ibody)%value(n),dp)
             realy(i)=realy(i)+dy(i,n)*real(design%body_data(ibody)%value(n),dp)
             realz(i)=realz(i)+dz(i,n)*real(design%body_data(ibody)%value(n),dp)
            end do loop_design
          end do add_design

          if ( complex_mode ) then

            if ( body_index == ibody ) then
              mass(ibody,g)%xmt = &
                cmplx(real(realx,dp),real(dx(:,dv_index)*complex_epsilon,dp),dp)
              mass(ibody,g)%ymt = &
                cmplx(real(realy,dp),real(dy(:,dv_index)*complex_epsilon,dp),dp)
              mass(ibody,g)%zmt = &
                cmplx(real(realz,dp),real(dz(:,dv_index)*complex_epsilon,dp),dp)
            else
              mass(ibody,g)%xmt = cmplx( real(realx,dp), 0.0_dp, dp )
              mass(ibody,g)%ymt = cmplx( real(realy,dp), 0.0_dp, dp )
              mass(ibody,g)%zmt = cmplx( real(realz,dp), 0.0_dp, dp )
            endif

          else

            mass(ibody,g)%xmt = realx
            mass(ibody,g)%ymt = realy
            mass(ibody,g)%zmt = realz

          endif

          deallocate(baseline_x,baseline_y,baseline_z,dx,dy,dz)
          deallocate(realx,realy,realz)

        end select

        if ( trim(sd_file_format) == 'stream' ) then
          call read_ddf_complete
        else
          close(iunit)
        endif

      endif read_stuff

      call lmpi_bcast(mass(ibody,g)%xmt)
      call lmpi_bcast(mass(ibody,g)%ymt)
      call lmpi_bcast(mass(ibody,g)%zmt)
      call lmpi_bcast(mass(ibody,g)%inodemt)

    end do body_loop

    deallocate(fake_body)

  end subroutine load_massoud

!======================== PACK_BOUNDARY_POSITION =============================80
!
! converts inodemt from global to local numbers
! packs inodemt, xmt, ymt, zmt, xmtD, ymtD, zmtD; removing off-proc nodes
! reduces itotal
!
! WARNING: this routine is order n**2 it could be inproved
!
!=============================================================================80

  subroutine pack_boundary_position(nnodes01,l2g,itotal,inodemt,xmt,ymt,zmt)

    integer, intent(in)    :: nnodes01
    integer, intent(inout) :: itotal

    integer, dimension(nnodes01), intent(in)    :: l2g
    integer, dimension(itotal),   intent(inout) :: inodemt

    real(dp), dimension(itotal), intent(inout) :: xmt, ymt, zmt

    integer :: inode, localnode, globalnode, newtotal

  continue

    newtotal = 0

    mt_loop : do inode = 1, itotal

      globalnode = inodemt( inode )

      local_loop : do localnode = 1, nnodes01

        found_node : if ( globalnode == l2g( localnode ) ) then

          newtotal = newtotal + 1

          inodemt( newtotal ) = localnode

          xmt( newtotal ) = xmt( inode )
          ymt( newtotal ) = ymt( inode )
          zmt( newtotal ) = zmt( inode )

          exit local_loop

        endif found_node

      enddo local_loop

    enddo mt_loop

    itotal = newtotal

  end subroutine pack_boundary_position


!================================ SET_UP_MASSOUD =============================80
!
! Allocates arrays for the massoud (mass) derived type
!
!=============================================================================80

  subroutine set_up_massoud(mass)

    use allocations, only : my_alloc_ptr
    use info_depr,   only : skeleton
    use grid_types,  only : mass_type

    type(mass_type), intent(inout) :: mass

    integer :: array_size

  continue

    if(skeleton > 0) write(*,*) ' Set up mass with type mass_type'

    array_size = max(mass%itotal, 1)

    call my_alloc_ptr(mass%inodemt, array_size)
    call my_alloc_ptr(mass%xmt,     array_size)
    call my_alloc_ptr(mass%ymt,     array_size)
    call my_alloc_ptr(mass%zmt,     array_size)

  end subroutine set_up_massoud

end module massoud

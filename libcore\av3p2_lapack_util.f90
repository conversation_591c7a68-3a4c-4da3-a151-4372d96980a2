module av3p2_lapack_util

  use kinddefs,            only : dp

  implicit none

  private

  public :: ilaenv, xerbla
  public :: lsame
  public :: disnan, dlaisnan, iparmq, ieeeck
  public :: dgemm ,dgemv, dtrsm, dsyrk


contains

!> \brief \b ILAENV

!  =========== DOCUMENTATION ===========

! Online html documentation available at
!            http://www.netlib.org/lapack/explore-html/

! Definition:
! ===========

!      INTEGER FUNCTION ILAENV( ISPEC, NAME, OPTS, N1, N2, N3, N4 )
!
!      .. Scalar Arguments ..
!      CHARACTER*( *)    NAME, OPTS
!      INTEGER            ISPEC, N1, N2, N3, N4
!      ..
!

!> \par Purpose:
! =============
!>
!> \verbatim
!>
!> ILAENV is called from the LAPACK routines to choose problem-dependent
!> parameters for the local environment.  See ISPEC for a description of
!> the parameters.
!>
!> ILAENV returns an INTEGER
!> if ILAENV >= 0: ILAENV returns the value of the parameter specified by ISPEC
!> if ILAENV < 0:  if ILAENV = -k, the k-th argument had an illegal value.
!>
!> This version provides a set of parameters which should give good,
!> but not optimal, performance on many of the currently available
!> computers.  Users are encouraged to modify this subroutine to set
!> the tuning parameters for their particular machine using the option
!> and problem size information in the arguments.
!>
!> This routine will not function correctly if it is converted to all
!> lower case.  Converting it to all upper case is allowed.
!> \endverbatim

! Arguments:
! ==========

!> \param[in] ISPEC
!> \verbatim
!>          ISPEC is INTEGER
!>          Specifies the parameter to be returned as the value of
!>          ILAENV.
!>          = 1: the optimal blocksize; if this value is 1, an unblocked
!>               algorithm will give the best performance.
!>          = 2: the minimum block size for which the block routine
!>               should be used; if the usable block size is less than
!>               this value, an unblocked routine should be used.
!>          = 3: the crossover point (in a block routine, for N less
!>               than this value, an unblocked routine should be used)
!>          = 4: the number of shifts, used in the nonsymmetric
!>               eigenvalue routines (DEPRECATED)
!>          = 5: the minimum column dimension for blocking to be used;
!>               rectangular blocks must have dimension at least k by m,
!>               where k is given by ILAENV(2,...) and m by ILAENV(5,...)
!>          = 6: the crossover point for the SVD (when reducing an m by n
!>               matrix to bidiagonal form, if max(m,n)/min(m,n) exceeds
!>               this value, a QR factorization is used first to reduce
!>               the matrix to a triangular form.)
!>          = 7: the number of processors
!>          = 8: the crossover point for the multishift QR method
!>               for nonsymmetric eigenvalue problems (DEPRECATED)
!>          = 9: maximum size of the subproblems at the bottom of the
!>               computation tree in the divide-and-conquer algorithm
!>               (used by xGELSD and xGESDD)
!>          =10: ieee NaN arithmetic can be trusted not to trap
!>          =11: infinity arithmetic can be trusted not to trap
!>          12 <= ISPEC <= 16:
!>               xHSEQR or one of its subroutines,
!>               see IPARMQ for detailed explanation
!> \endverbatim
!>
!> \param[in] NAME
!> \verbatim
!>          NAME is CHARACTER*(*)
!>          The name of the calling subroutine, in either upper case or
!>          lower case.
!> \endverbatim
!>
!> \param[in] OPTS
!> \verbatim
!>          OPTS is CHARACTER*(*)
!>          The character options to the subroutine NAME, concatenated
!>          into a single character string.  For example, UPLO = 'U',
!>          TRANS = 'T', and DIAG = 'N' for a triangular routine would
!>          be specified as OPTS = 'UTN'.
!> \endverbatim
!>
!> \param[in] N1
!> \verbatim
!>          N1 is INTEGER
!> \endverbatim
!>
!> \param[in] N2
!> \verbatim
!>          N2 is INTEGER
!> \endverbatim
!>
!> \param[in] N3
!> \verbatim
!>          N3 is INTEGER
!> \endverbatim
!>
!> \param[in] N4
!> \verbatim
!>          N4 is INTEGER
!>          Problem dimensions for the subroutine NAME; these may not all
!>          be required.
!> \endverbatim

! Authors: <AUTHORS>

!> \author Univ. of Tennessee
!> \author Univ. of California Berkeley
!> \author Univ. of Colorado Denver
!> \author NAG Ltd.

!> \date November 2011

!> \ingroup auxOTHERauxiliary

!> \par Further Details:
! =====================
!>
!> \verbatim
!>
!>  The following conventions have been used when calling ILAENV from the
!>  LAPACK routines:
!>  1)  OPTS is a concatenation of all of the character options to
!>      subroutine NAME, in the same order that they appear in the
!>      argument list for NAME, even if they are not used in determining
!>      the value of the parameter specified by ISPEC.
!>  2)  The problem dimensions N1, N2, N3, N4 are specified in the order
!>      that they appear in the argument list for NAME.  N1 is used
!>      first, N2 second, and so on, and unused problem dimensions are
!>      passed a value of -1.
!>  3)  The parameter value returned by ILAENV is checked for validity in
!>      the calling subroutine.  For example, ILAENV is used to retrieve
!>      the optimal blocksize for STRTRI as follows:
!>
!>      NB = ILAENV( 1, 'STRTRI', UPLO // DIAG, N, -1, -1, -1 )
!>      IF( NB <= 1 ) NB = MAX( 1, N )
!> \endverbatim
!>
! =====================================================================
      INTEGER FUNCTION ILAENV( ISPEC, NAME, N1, N2, N3, N4 ) !OPTS

! -- LAPACK auxiliary routine (version 3.4.0) --
! -- LAPACK is a software package provided by Univ. of Tennessee,    --
! -- Univ. of California Berkeley, Univ. of Colorado Denver and NAG Ltd..--
!    November 2011

  character(LEN=*), intent(in) :: NAME!, OPTS
  integer,          intent(in) :: ISPEC,N1,N2,N3,N4

!    .. Scalar Arguments ..
     !CHARACTER*( * )    NAME, OPTS
     !INTEGER            ISPEC, N1, N2, N3, N4
!    ..

! =====================================================================

!    .. Local Scalars ..
      INTEGER            I, IC, IZ, NB, NBMIN, NX
      LOGICAL            CNAME, SNAME
      character(len=1) :: C1
      character(len=2) :: C2, C4
      character(len=3) :: C3
      character(len=6) :: SUBNAM
     !CHARACTER          C1*1, C2*2, C4*2, C3*3, SUBNAM*6
!    ..
!    .. Intrinsic Functions ..
    !INTRINSIC          CHAR, ICHAR, INT, MIN, REAL
!    ..
!    .. External Functions ..
    !INTEGER            IEEECK, IPARMQ
    !EXTERNAL           IEEECK, IPARMQ

  real(dp) :: zero, one
!    ..
!    .. Executable Statements ..

  zero = 0._dp
  one  = 1._dp

  select case (ISPEC)

  case(1,2,3)
    !
    !     Convert NAME to upper case if the first character is lower case.
    !
    !          1   2   3   4   5    6    7    8
    ! GO TO ( 10, 10, 10, 80, 90, 100, 110, 120, &
    !           9   10   11   12   13   14   15   16
    !         130, 140, 150, 160, 160, 160, 160, 160 )ISPEC

!   10 CONTINUE

!    Convert NAME to upper case if the first character is lower case.

      ILAENV = 1
      SUBNAM = NAME
      IC = ICHAR( SUBNAM( 1: 1 ) )
      IZ = ICHAR( 'Z' )
      IF( IZ == 90 .OR. IZ == 122 ) THEN

!       ASCII character set

         IF( IC >= 97 .AND. IC <= 122 ) THEN
            SUBNAM( 1: 1 ) = CHAR( IC-32 )
            DO  I = 2, 6
               IC = ICHAR( SUBNAM( I: I ) )
               IF( IC >= 97 .AND. IC <= 122 ) &
                 SUBNAM( I: I ) = CHAR( IC-32 )
            ENDDO
         END IF

      ELSE IF( IZ == 233 .OR. IZ == 169 ) THEN

!       EBCDIC character set

         IF( ( IC >= 129 .AND. IC <= 137 ) .OR. &
             ( IC >= 145 .AND. IC <= 153 ) .OR. &
             ( IC >= 162 .AND. IC <= 169 ) ) THEN
            SUBNAM( 1: 1 ) = CHAR( IC+64 )
            DO I = 2, 6
               IC = ICHAR( SUBNAM( I: I ) )
               IF( ( IC >= 129 .AND. IC <= 137 ) .OR. &
                   ( IC >= 145 .AND. IC <= 153 ) .OR. &
                   ( IC >= 162 .AND. IC <= 169 ) )SUBNAM( I: I ) = CHAR( IC+64 )
            ENDDO
         END IF

      ELSE IF( IZ == 218 .OR. IZ == 250 ) THEN

!       Prime machines:  ASCII+128

         IF( IC >= 225 .AND. IC <= 250 ) THEN
            SUBNAM( 1: 1 ) = CHAR( IC-32 )
            DO  I = 2, 6
               IC = ICHAR( SUBNAM( I: I ) )
               IF( IC >= 225 .AND. IC <= 250 ) &
                 SUBNAM( I: I ) = CHAR( IC-32 )
            ENDDO
         END IF
      END IF

      C1 = SUBNAM( 1: 1 )
      SNAME = C1 == 'S' .OR. C1 == 'D'
      CNAME = C1 == 'C' .OR. C1 == 'Z'
      IF( .NOT.( CNAME .OR. SNAME ) )  RETURN
      C2 = SUBNAM( 2: 3 )
      C3 = SUBNAM( 4: 6 )
      C4 = C3( 2: 3 )

      select case( ISPEC )
      !         1   2   3
      !GO TO ( 50, 60, 70 )ISPEC
      case( 1 )

!   50 CONTINUE

!    ISPEC = 1:  block size

!    In these examples, separate code is provided for setting NB for
!    real and complex.  We assume that NB will take the same value in
!    single or double precision.

      NB = 1

      IF( C2 == 'GE' ) THEN
         IF( C3 == 'TRF' ) THEN
            IF( SNAME ) THEN
               NB = 64
            ELSE
               NB = 64
            END IF
         ELSE IF( C3 == 'QRF' .OR. C3 == 'RQF' .OR. C3 == 'LQF' .OR. &
                 C3 == 'QLF' ) THEN
            IF( SNAME ) THEN
               NB = 32
            ELSE
               NB = 32
            END IF
         ELSE IF( C3 == 'HRD' ) THEN
            IF( SNAME ) THEN
               NB = 32
            ELSE
               NB = 32
            END IF
         ELSE IF( C3 == 'BRD' ) THEN
            IF( SNAME ) THEN
               NB = 32
            ELSE
               NB = 32
            END IF
         ELSE IF( C3 == 'TRI' ) THEN
            IF( SNAME ) THEN
               NB = 64
            ELSE
               NB = 64
            END IF
         END IF
      ELSE IF( C2 == 'PO' ) THEN
         IF( C3 == 'TRF' ) THEN
            IF( SNAME ) THEN
               NB = 64
            ELSE
               NB = 64
            END IF
         END IF
      ELSE IF( C2 == 'SY' ) THEN
         IF( C3 == 'TRF' ) THEN
            IF( SNAME ) THEN
               NB = 64
            ELSE
               NB = 64
            END IF
         ELSE IF( SNAME .AND. C3 == 'TRD' ) THEN
            NB = 32
         ELSE IF( SNAME .AND. C3 == 'GST' ) THEN
            NB = 64
         END IF
      ELSE IF( CNAME .AND. C2 == 'HE' ) THEN
         IF( C3 == 'TRF' ) THEN
            NB = 64
         ELSE IF( C3 == 'TRD' ) THEN
            NB = 32
         ELSE IF( C3 == 'GST' ) THEN
            NB = 64
         END IF
      ELSE IF( SNAME .AND. C2 == 'OR' ) THEN
         IF( C3( 1: 1 ) == 'G' ) THEN
            IF( C4 == 'QR' .OR. C4 == 'RQ' .OR. C4 == 'LQ' .OR. C4 ==  &
                'QL' .OR. C4 == 'HR' .OR. C4 == 'TR' .OR. C4 == 'BR' ) &
                 THEN
               NB = 32
            END IF
         ELSE IF( C3( 1: 1 ) == 'M' ) THEN
            IF( C4 == 'QR' .OR. C4 == 'RQ' .OR. C4 == 'LQ' .OR. C4 ==  &
                'QL' .OR. C4 == 'HR' .OR. C4 == 'TR' .OR. C4 == 'BR' ) &
                 THEN
               NB = 32
            END IF
         END IF
      ELSE IF( CNAME .AND. C2 == 'UN' ) THEN
         IF( C3( 1: 1 ) == 'G' ) THEN
            IF( C4 == 'QR' .OR. C4 == 'RQ' .OR. C4 == 'LQ' .OR. C4 ==  &
                'QL' .OR. C4 == 'HR' .OR. C4 == 'TR' .OR. C4 == 'BR' ) &
                 THEN
               NB = 32
            END IF
         ELSE IF( C3( 1: 1 ) == 'M' ) THEN
            IF( C4 == 'QR' .OR. C4 == 'RQ' .OR. C4 == 'LQ' .OR. C4 ==  &
                'QL' .OR. C4 == 'HR' .OR. C4 == 'TR' .OR. C4 == 'BR' ) &
                 THEN
               NB = 32
            END IF
         END IF
      ELSE IF( C2 == 'GB' ) THEN
         IF( C3 == 'TRF' ) THEN
            IF( SNAME ) THEN
               IF( N4 <= 64 ) THEN
                  NB = 1
               ELSE
                  NB = 32
               END IF
            ELSE
               IF( N4 <= 64 ) THEN
                  NB = 1
               ELSE
                  NB = 32
               END IF
            END IF
         END IF
      ELSE IF( C2 == 'PB' ) THEN
         IF( C3 == 'TRF' ) THEN
            IF( SNAME ) THEN
               IF( N2 <= 64 ) THEN
                  NB = 1
               ELSE
                  NB = 32
               END IF
            ELSE
               IF( N2 <= 64 ) THEN
                  NB = 1
               ELSE
                  NB = 32
               END IF
            END IF
         END IF
      ELSE IF( C2 == 'TR' ) THEN
         IF( C3 == 'TRI' ) THEN
            IF( SNAME ) THEN
               NB = 64
            ELSE
               NB = 64
            END IF
         END IF
      ELSE IF( C2 == 'LA' ) THEN
         IF( C3 == 'UUM' ) THEN
            IF( SNAME ) THEN
               NB = 64
            ELSE
               NB = 64
            END IF
         END IF
      ELSE IF( SNAME .AND. C2 == 'ST' ) THEN
         IF( C3 == 'EBZ' ) THEN
            NB = 1
         END IF
      END IF
      ILAENV = NB
      RETURN

      case( 2 )
!   60 CONTINUE

!    ISPEC = 2:  minimum block size

      NBMIN = 2
      IF( C2 == 'GE' ) THEN
         IF( C3 == 'QRF' .OR. C3 == 'RQF' .OR. C3 == 'LQF' .OR. C3 ==  &
             'QLF' ) THEN
            IF( SNAME ) THEN
               NBMIN = 2
            ELSE
               NBMIN = 2
            END IF
         ELSE IF( C3 == 'HRD' ) THEN
            IF( SNAME ) THEN
               NBMIN = 2
            ELSE
               NBMIN = 2
            END IF
         ELSE IF( C3 == 'BRD' ) THEN
            IF( SNAME ) THEN
               NBMIN = 2
            ELSE
               NBMIN = 2
            END IF
         ELSE IF( C3 == 'TRI' ) THEN
            IF( SNAME ) THEN
               NBMIN = 2
            ELSE
               NBMIN = 2
            END IF
         END IF
      ELSE IF( C2 == 'SY' ) THEN
         IF( C3 == 'TRF' ) THEN
            IF( SNAME ) THEN
               NBMIN = 8
            ELSE
               NBMIN = 8
            END IF
         ELSE IF( SNAME .AND. C3 == 'TRD' ) THEN
            NBMIN = 2
         END IF
      ELSE IF( CNAME .AND. C2 == 'HE' ) THEN
         IF( C3 == 'TRD' ) THEN
            NBMIN = 2
         END IF
      ELSE IF( SNAME .AND. C2 == 'OR' ) THEN
         IF( C3( 1: 1 ) == 'G' ) THEN
            IF( C4 == 'QR' .OR. C4 == 'RQ' .OR. C4 == 'LQ' .OR. C4 ==  &
                'QL' .OR. C4 == 'HR' .OR. C4 == 'TR' .OR. C4 == 'BR' ) &
                THEN
               NBMIN = 2
            END IF
         ELSE IF( C3( 1: 1 ) == 'M' ) THEN
            IF( C4 == 'QR' .OR. C4 == 'RQ' .OR. C4 == 'LQ' .OR. C4 ==  &
                'QL' .OR. C4 == 'HR' .OR. C4 == 'TR' .OR. C4 == 'BR' ) &
                THEN
               NBMIN = 2
            END IF
         END IF
      ELSE IF( CNAME .AND. C2 == 'UN' ) THEN
         IF( C3( 1: 1 ) == 'G' ) THEN
            IF( C4 == 'QR' .OR. C4 == 'RQ' .OR. C4 == 'LQ' .OR. C4 ==  &
                'QL' .OR. C4 == 'HR' .OR. C4 == 'TR' .OR. C4 == 'BR' ) &
                THEN
               NBMIN = 2
            END IF
         ELSE IF( C3( 1: 1 ) == 'M' ) THEN
            IF( C4 == 'QR' .OR. C4 == 'RQ' .OR. C4 == 'LQ' .OR. C4 ==  &
                'QL' .OR. C4 == 'HR' .OR. C4 == 'TR' .OR. C4 == 'BR' ) &
                THEN
               NBMIN = 2
            END IF
         END IF
      END IF
      ILAENV = NBMIN
      RETURN

      case( 3 )
!   70 CONTINUE

!    ISPEC = 3:  crossover point

      NX = 0
      IF( C2 == 'GE' ) THEN
         IF( C3 == 'QRF' .OR. C3 == 'RQF' .OR. C3 == 'LQF' .OR. C3 ==  &
             'QLF' ) THEN
            IF( SNAME ) THEN
               NX = 128
            ELSE
               NX = 128
            END IF
         ELSE IF( C3 == 'HRD' ) THEN
            IF( SNAME ) THEN
               NX = 128
            ELSE
               NX = 128
            END IF
         ELSE IF( C3 == 'BRD' ) THEN
            IF( SNAME ) THEN
               NX = 128
            ELSE
               NX = 128
            END IF
         END IF
      ELSE IF( C2 == 'SY' ) THEN
         IF( SNAME .AND. C3 == 'TRD' ) THEN
            NX = 32
         END IF
      ELSE IF( CNAME .AND. C2 == 'HE' ) THEN
         IF( C3 == 'TRD' ) THEN
            NX = 32
         END IF
      ELSE IF( SNAME .AND. C2 == 'OR' ) THEN
         IF( C3( 1: 1 ) == 'G' ) THEN
            IF( C4 == 'QR' .OR. C4 == 'RQ' .OR. C4 == 'LQ' .OR. C4 ==  &
                'QL' .OR. C4 == 'HR' .OR. C4 == 'TR' .OR. C4 == 'BR' ) &
                THEN
               NX = 128
            END IF
         END IF
      ELSE IF( CNAME .AND. C2 == 'UN' ) THEN
         IF( C3( 1: 1 ) == 'G' ) THEN
            IF( C4 == 'QR' .OR. C4 == 'RQ' .OR. C4 == 'LQ' .OR. C4 ==  &
                'QL' .OR. C4 == 'HR' .OR. C4 == 'TR' .OR. C4 == 'BR' ) &
                THEN
               NX = 128
            END IF
         END IF
      END IF
      ILAENV = NX
      RETURN

      end select

   case(4)
!   80 CONTINUE

!    ISPEC = 4:  number of shifts (used by xHSEQR)

      ILAENV = 6
      RETURN

   case(5)

!   90 CONTINUE

!    ISPEC = 5:  minimum column dimension (not used)

      ILAENV = 2
      RETURN

  case(6)
!  100 CONTINUE

!    ISPEC = 6:  crossover point for SVD (used by xGELSS and xGESVD)

      ILAENV = INT( REAL( MIN( N1, N2 ) )*1.6E0 )
      RETURN

  case(7)
!  110 CONTINUE

!    ISPEC = 7:  number of processors (not used)

      ILAENV = 1
      RETURN

  case(8)
!  120 CONTINUE

!    ISPEC = 8:  crossover point for multishift (used by xHSEQR)

      ILAENV = 50
      RETURN

  case(9)
!  130 CONTINUE

!    ISPEC = 9:  maximum size of the subproblems at the bottom of the
!                computation tree in the divide-and-conquer algorithm
!                (used by xGELSD and xGESDD)

      ILAENV = 25
      RETURN

  case(10)
!  140 CONTINUE

!    ISPEC = 10: ieee NaN arithmetic can be trusted not to trap

!    ILAENV = 0
      ILAENV = 1
      IF( ILAENV == 1 ) THEN
         ILAENV = IEEECK( 1, zero, one )
      END IF
      RETURN

  case(11)
!  150 CONTINUE

!    ISPEC = 11: infinity arithmetic can be trusted not to trap

!    ILAENV = 0
      ILAENV = 1
      IF( ILAENV == 1 ) THEN
         ILAENV = IEEECK( 0, zero, one )
      END IF
      RETURN

  case(12:16)
!  160 CONTINUE

!    12 <= ISPEC <= 16: xHSEQR or one of its subroutines.

      ILAENV = IPARMQ( ISPEC, N2, N3 )
     !ILAENV = IPARMQ( ISPEC, NAME, OPTS, N1, N2, N3, N4 )
      RETURN

  case default

!    Invalid value for ISPEC

      ILAENV = -1
      RETURN

  end select

!    End of ILAENV

      END FUNCTION ILAENV

!> \brief \b XERBLA

!  =========== DOCUMENTATION ===========

! Online html documentation available at
!            http://www.netlib.org/lapack/explore-html/

!  Definition:
!  ===========
!
!       SUBROUTINE XERBLA( SRNAME, INFO )

!       .. Scalar Arguments ..
!       CHARACTER*(*)      SRNAME
!       INTEGER            INFO
!       ..

!> \par Purpose:
!  =============
!>
!> \verbatim
!>
!> XERBLA  is an error handler for the LAPACK routines.
!> It is called by an LAPACK routine if an input parameter has an
!> invalid value.  A message is printed and execution stops.
!>
!> Installers may consider modifying the STOP statement in order to
!> call system-specific exception-handling facilities.
!> \endverbatim

!  Arguments:
!  ==========

!> \param[in] SRNAME
!> \verbatim
!>          SRNAME is CHARACTER*(*)
!>          The name of the routine which called XERBLA.
!> \endverbatim
!>
!> \param[in] INFO
!> \verbatim
!>          INFO is INTEGER
!>          The position of the invalid parameter in the parameter list
!>          of the calling routine.
!> \endverbatim

!  Authors: <AUTHORS>

!> \author Univ. of Tennessee
!> \author Univ. of California Berkeley
!> \author Univ. of Colorado Denver
!> \author NAG Ltd.

!> \date November 2011

!> \ingroup auxOTHERauxiliary

!  =====================================================================
      SUBROUTINE XERBLA( SRNAME, INFO )
!
!  -- LAPACK auxiliary routine (version 3.4.0) --
!  -- LAPACK is a software package provided by Univ. of Tennessee,    --
!  -- Univ. of California Berkeley, Univ. of Colorado Denver and NAG Ltd..--
!     November 2011


  character(LEN=6), intent(in) :: SRNAME
  integer, intent(in) :: INFO
!     .. Scalar Arguments ..
      !CHARACTER*(*)      SRNAME
      !INTEGER            INFO
!     ..

! =====================================================================

!     .. Intrinsic Functions ..
     !INTRINSIC          LEN_TRIM
!     ..
!     .. Executable Statements ..
!
      WRITE( *, FMT = 9999 )SRNAME( 1:LEN_TRIM( SRNAME ) ), INFO

      STOP

 9999 FORMAT( ' ** On entry to ', A, ' parameter number ', I2, ' had ',&
           'an illegal value' )


!
!     End of XERBLA
!
end subroutine XERBLA


!> \brief \b LSAME

!  =========== DOCUMENTATION ===========

! Online html documentation available at
!            http://www.netlib.org/lapack/explore-html/

!  Definition:
!  ===========

!      LOGICAL FUNCTION LSAME( CA, CB )

!     .. Scalar Arguments ..
!      CHARACTER          CA, CB
!     ..
!

!> \par Purpose:
!  =============
!>
!> \verbatim
!>
!> LSAME returns .TRUE. if CA is the same letter as CB regardless of
!> case.
!> \endverbatim

!  Arguments:
!  ==========

!> \param[in] CA
!> \verbatim
!> \endverbatim
!>
!> \param[in] CB
!> \verbatim
!>          CA and CB specify the single characters to be compared.
!> \endverbatim

!  Authors: <AUTHORS>

!> \author Univ. of Tennessee
!> \author Univ. of California Berkeley
!> \author Univ. of Colorado Denver
!> \author NAG Ltd.

!> \date November 2011

!> \ingroup auxOTHERauxiliary

!  =====================================================================
      LOGICAL FUNCTION LSAME( CA, CB )

!  -- LAPACK auxiliary routine (version 3.4.0) --
!  -- LAPACK is a software package provided by Univ. of Tennessee,    --
!  -- Univ. of California Berkeley, Univ. of Colorado Denver and NAG Ltd..--
!     November 2011

  character, intent(in) :: CA,CB

!     .. Scalar Arguments ..
     !CHARACTER          CA, CB
!     ..

! =====================================================================

!     .. Intrinsic Functions ..
     !INTRINSIC          ICHAR
!     ..
!     .. Local Scalars ..
      INTEGER            INTA, INTB, ZCODE
!     ..
!     .. Executable Statements ..

!     Test if the characters are equal

      LSAME = CA == CB
      IF( LSAME ) RETURN

!     Now test for equivalence if both characters are alphabetic.

      ZCODE = ICHAR( 'Z' )

!     Use 'Z' rather than 'A' so that ASCII can be detected on Prime
!     machines, on which ICHAR returns a value with bit 8 set.
!     ICHAR('A') on Prime machines returns 193 which is the same as
!     ICHAR('A') on an EBCDIC machine.

      INTA = ICHAR( CA )
      INTB = ICHAR( CB )

      IF( ZCODE == 90 .OR. ZCODE == 122 ) THEN

!        ASCII is assumed - ZCODE is the ASCII code of either lower or
!        upper case 'Z'.

         IF( INTA >= 97 .AND. INTA <= 122 ) INTA = INTA - 32
         IF( INTB >= 97 .AND. INTB <= 122 ) INTB = INTB - 32

      ELSE IF( ZCODE == 233 .OR. ZCODE == 169 ) THEN

!        EBCDIC is assumed - ZCODE is the EBCDIC code of either lower or
!        upper case 'Z'.

         IF( INTA >= 129 .AND. INTA <= 137 .OR. &
             INTA >= 145 .AND. INTA <= 153 .OR. &
             INTA >= 162 .AND. INTA <= 169 ) INTA = INTA + 64
         IF( INTB >= 129 .AND. INTB <= 137 .OR. &
             INTB >= 145 .AND. INTB <= 153 .OR. &
             INTB >= 162 .AND. INTB <= 169 ) INTB = INTB + 64

      ELSE IF( ZCODE == 218 .OR. ZCODE == 250 ) THEN

!        ASCII is assumed, on Prime machines - ZCODE is the ASCII code
!        plus 128 of either lower or upper case 'Z'.

         IF( INTA >= 225 .AND. INTA <= 250 ) INTA = INTA - 32
         IF( INTB >= 225 .AND. INTB <= 250 ) INTB = INTB - 32
      END IF
      LSAME = INTA == INTB

end function LSAME

      LOGICAL FUNCTION DISNAN( DIN )

  real(dp), intent(in) :: DIN

!  -- LAPACK auxiliary routine (version 3.2.2) --
!  -- LAPACK is a software package provided by Univ. of Tennessee,    --
!  -- Univ. of California Berkeley, Univ. of Colorado Denver and NAG Ltd..--
!     June 2010

!     .. Scalar Arguments ..
      !DOUBLE PRECISION   DIN
!     ..

!  Purpose
!  =======

!  DISNAN returns .TRUE. if its argument is NaN, and .FALSE.
!  otherwise.  To be replaced by the Fortran 2003 intrinsic in the
!  future.

!  Arguments
!  =========

!  DIN     (input) DOUBLE PRECISION
!          Input to test for NaN.

!  =====================================================================

!  .. External Functions ..
      !LOGICAL DLAISNAN
      !EXTERNAL DLAISNAN
!  ..
!  .. Executable Statements ..
      DISNAN = DLAISNAN(DIN,DIN)

      END FUNCTION DISNAN


      LOGICAL FUNCTION DLAISNAN( DIN1, DIN2 )

  real(dp), intent(in) :: DIN1, DIN2

!  -- LAPACK auxiliary routine (version 3.2.2) --
!  -- LAPACK is a software package provided by Univ. of Tennessee,    --
!  -- Univ. of California Berkeley, Univ. of Colorado Denver and NAG Ltd..--
!     June 2010

!     .. Scalar Arguments ..
     !DOUBLE PRECISION   DIN1, DIN2
!     ..

!  Purpose
!  =======

!  This routine is not for general use.  It exists solely to avoid
!  over-optimization in DISNAN.

!  DLAISNAN checks for NaNs by comparing its two arguments for
!  inequality.  NaN is the only floating-point value where NaN != NaN
!  returns .TRUE.  To check for NaNs, pass the same variable as both
!  arguments.

!  A compiler must assume that the two arguments are
!  not the same variable, and the test will not be optimized away.
!  Interprocedural or whole-program optimization may delete this
!  test.  The ISNAN functions will be replaced by the correct
!  Fortran 03 intrinsic once the intrinsic is widely available.

!  Arguments
!  =========

!  DIN1    (input) DOUBLE PRECISION

!  DIN2    (input) DOUBLE PRECISION
!          Two numbers to compare for inequality.

!  =====================================================================

!  .. Executable Statements ..
      DLAISNAN = (DIN1 /= DIN2)

      END FUNCTION DLAISNAN

!> \brief \b IPARMQ

!  =========== DOCUMENTATION ===========

! Online html documentation available at
!            http://www.netlib.org/lapack/explore-html/

!  Definition:
!  ===========

!       INTEGER FUNCTION IPARMQ( ISPEC, NAME, OPTS, N, ILO, IHI, LWORK )

!       .. Scalar Arguments ..
!       INTEGER            IHI, ILO, ISPEC, LWORK, N
!       CHARACTER          NAME*( * ), OPTS*( * )
!

!> \par Purpose:
!  =============
!>
!> \verbatim
!>
!>      This program sets problem and machine dependent parameters
!>      useful for xHSEQR and its subroutines. It is called whenever
!>      ILAENV is called with 12 <= ISPEC <= 16
!> \endverbatim

!  Arguments:
!  ==========

!> \param[in] ISPEC
!> \verbatim
!>          ISPEC is integer scalar
!>              ISPEC specifies which tunable parameter IPARMQ should
!>              return.
!>
!>              ISPEC=12: (INMIN)  Matrices of order nmin or less
!>                        are sent directly to xLAHQR, the implicit
!>                        double shift QR algorithm.  NMIN must be
!>                        at least 11.
!>
!>              ISPEC=13: (INWIN)  Size of the deflation window.
!>                        This is best set greater than or equal to
!>                        the number of simultaneous shifts NS.
!>                        Larger matrices benefit from larger deflation
!>                        windows.
!>
!>              ISPEC=14: (INIBL) Determines when to stop nibbling and
!>                        invest in an (expensive) multi-shift QR sweep.
!>                        If the aggressive early deflation subroutine
!>                        finds LD converged eigenvalues from an order
!>                        NW deflation window and LD > (NW*NIBBLE)/100,
!>                        then the next QR sweep is skipped and early
!>                        deflation is applied immediately to the
!>                        remaining active diagonal block.  Setting
!>                        IPARMQ(ISPEC=14) = 0 causes TTQRE to skip a
!>                        multi-shift QR sweep whenever early deflation
!>                        finds a converged eigenvalue.  Setting
!>                        IPARMQ(ISPEC=14) greater than or equal to 100
!>                        prevents TTQRE from skipping a multi-shift
!>                        QR sweep.
!>
!>              ISPEC=15: (NSHFTS) The number of simultaneous shifts in
!>                        a multi-shift QR iteration.
!>
!>              ISPEC=16: (IACC22) IPARMQ is set to 0, 1 or 2 with the
!>                        following meanings.
!>                        0:  During the multi-shift QR sweep,
!>                            xLAQR5 does not accumulate reflections and
!>                            does not use matrix-matrix multiply to
!>                            update the far-from-diagonal matrix
!>                            entries.
!>                        1:  During the multi-shift QR sweep,
!>                            xLAQR5 and/or xLAQRaccumulates reflections and
!>                            uses matrix-matrix multiply to update the
!>                            far-from-diagonal matrix entries.
!>                        2:  During the multi-shift QR sweep.
!>                            xLAQR5 accumulates reflections and takes
!>                            advantage of 2-by-2 block structure during
!>                            matrix-matrix multiplies.
!>                        (If xTRMM is slower than xGEMM, then
!>                        IPARMQ(ISPEC=16)=1 may be more efficient than
!>                        IPARMQ(ISPEC=16)=2 despite the greater level of
!>                        arithmetic work implied by the latter choice.)
!> \endverbatim
!>
!> \param[in] NAME
!> \verbatim
!>          NAME is character string
!>               Name of the calling subroutine
!> \endverbatim
!>
!> \param[in] OPTS
!> \verbatim
!>          OPTS is character string
!>               This is a concatenation of the string arguments to
!>               TTQRE.
!> \endverbatim
!>
!> \param[in] N
!> \verbatim
!>          N is integer scalar
!>               N is the order of the Hessenberg matrix H.
!> \endverbatim
!>
!> \param[in] ILO
!> \verbatim
!>          ILO is INTEGER
!> \endverbatim
!>
!> \param[in] IHI
!> \verbatim
!>          IHI is INTEGER
!>               It is assumed that H is already upper triangular
!>               in rows and columns 1:ILO-1 and IHI+1:N.
!> \endverbatim
!>
!> \param[in] LWORK
!> \verbatim
!>          LWORK is integer scalar
!>               The amount of workspace available.
!> \endverbatim

!  Authors: <AUTHORS>

!> \author Univ. of Tennessee
!> \author Univ. of California Berkeley
!> \author Univ. of Colorado Denver
!> \author NAG Ltd.

!> \date November 2011

!> \ingroup auxOTHERauxiliary

!> \par Further Details:
!  =====================
!>
!> \verbatim
!>
!>       Little is known about how best to choose these parameters.
!>       It is possible to use different values of the parameters
!>       for each of CHSEQR, DHSEQR, SHSEQR and ZHSEQR.
!>
!>       It is probably best to choose different parameters for
!>       different matrices and different parameters at different
!>       times during the iteration, but this has not been
!>       implemented --- yet.
!>
!>
!>       The best choices of most of the parameters depend
!>       in an ill-understood way on the relative execution
!>       rate of xLAQR3 and xLAQR5 and on the nature of each
!>       particular eigenvalue problem.  Experiment may be the
!>       only practical way to determine which choices are most
!>       effective.
!>
!>       Following is a list of default values supplied by IPARMQ.
!>       These defaults may be adjusted in order to attain better
!>       performance in any particular computational environment.
!>
!>       IPARMQ(ISPEC=12) The xLAHQR vs xLAQR0 crossover point.
!>                        Default: 75. (Must be at least 11.)
!>
!>       IPARMQ(ISPEC=13) Recommended deflation window size.
!>                        This depends on ILO, IHI and NS, the
!>                        number of simultaneous shifts returned
!>                        by IPARMQ(ISPEC=15).  The default for
!>                        (IHI-ILO+1) <= 500 is NS.  The default
!>                        for (IHI-ILO+1) > 500 is 3*NS/2.
!>
!>       IPARMQ(ISPEC=14) Nibble crossover point.  Default: 14.
!>
!>       IPARMQ(ISPEC=15) Number of simultaneous shifts, NS.
!>                        a multi-shift QR iteration.
!>
!>                        If IHI-ILO+1 is ...
!>
!>                        greater than      ...but less    ... the
!>                        or equal to ...      than        default is
!>
!>                                0               30       NS =   2+
!>                               30               60       NS =   4+
!>                               60              150       NS =  10
!>                              150              590       NS =  **
!>                              590             3000       NS =  64
!>                             3000             6000       NS = 128
!>                             6000             infinity   NS = 256
!>
!>                    (+)  By default matrices of this order are
!>                         passed to the implicit double shift routine
!>                         xLAHQR.  See IPARMQ(ISPEC=12) above.   These
!>                         values of NS are used only in case of a rare
!>                         xLAHQR failure.
!>
!>                    (**) The asterisks (**) indicate an ad-hoc
!>                         function increasing from 10 to 64.
!>
!>       IPARMQ(ISPEC=16) Select structured matrix multiply.
!>                        (See ISPEC=16 above for details.)
!>                        Default: 3.
!> \endverbatim
!>
!  =====================================================================
      INTEGER FUNCTION IPARMQ( ISPEC, ILO, IHI )!NAME, OPTS, N, LWORK

  integer,          intent(in) :: ISPEC, ILO, IHI
 !integer,          intent(in) :: N, LWORK
 !character(len=*), intent(in) :: NAME, OPTS

!  -- LAPACK auxiliary routine (version 3.4.0) --
!  -- LAPACK is a software package provided by Univ. of Tennessee,    --
!  -- Univ. of California Berkeley, Univ. of Colorado Denver and NAG Ltd..--
!     November 2011

!     .. Scalar Arguments ..
      !INTEGER            IHI, ILO, ISPEC, LWORK, N
      !CHARACTER          NAME*( * ), OPTS*( * )

!  ================================================================
!     .. Parameters ..
      INTEGER            INMIN, INWIN, INIBL, ISHFTS, IACC22
      PARAMETER          ( INMIN = 12, INWIN = 13, INIBL = 14, &
                         ISHFTS = 15, IACC22 = 16 )
      INTEGER            NMIN, K22MIN, KACMIN, NIBBLE, KNWSWP
      PARAMETER          ( NMIN = 75, K22MIN = 14, KACMIN = 14, &
                         NIBBLE = 14, KNWSWP = 500 )
      REAL               TWO
      PARAMETER          ( TWO = 2.0 )
!     ..
!     .. Local Scalars ..
      INTEGER            NH, NS
!     ..
!     .. Intrinsic Functions ..
     !INTRINSIC          LOG, MAX, MOD, NINT, REAL
!     ..
!     .. Executable Statements ..

      NH = 0
      NS = 0

      IF( ( ISPEC == ISHFTS ) .OR. ( ISPEC == INWIN ) .OR. &
          ( ISPEC == IACC22 ) ) THEN

!        ==== Set the number simultaneous shifts ====

         NH = IHI - ILO + 1
         NS = 2
         IF( NH >= 30 ) &
            NS = 4
         IF( NH >= 60 ) &
            NS = 10
         IF( NH >= 150 ) &
            NS = MAX( 10, NH / NINT( LOG( REAL( NH ) ) / LOG( TWO ) ) )
         IF( NH >= 590 ) &
            NS = 64
         IF( NH >= 3000 ) &
            NS = 128
         IF( NH >= 6000 ) &
            NS = 256
         NS = MAX( 2, NS-MOD( NS, 2 ) )
      END IF

      IF( ISPEC == INMIN ) THEN


!        ===== Matrices of order smaller than NMIN get sent
!        .     to xLAHQR, the classic double shift algorithm.
!        .     This must be at least 11. ====

         IPARMQ = NMIN

      ELSE IF( ISPEC == INIBL ) THEN

!        ==== INIBL: skip a multi-shift qr iteration and
!        .    whenever aggressive early deflation finds
!        .    at least (NIBBLE*(window size)/100) deflations. ====

         IPARMQ = NIBBLE

      ELSE IF( ISPEC == ISHFTS ) THEN

!        ==== NSHFTS: The number of simultaneous shifts =====

         IPARMQ = NS

      ELSE IF( ISPEC == INWIN ) THEN

!        ==== NW: deflation window size.  ====

         IF( NH <= KNWSWP ) THEN
            IPARMQ = NS
         ELSE
            IPARMQ = 3*NS / 2
         END IF

      ELSE IF( ISPEC == IACC22 ) THEN

!        ==== IACC22: Whether to accumulate reflections
!        .     before updating the far-from-diagonal elements
!        .     and whether to use 2-by-2 block structure while
!        .     doing it.  A small amount of work could be saved
!        .     by making this choice dependent also upon the
!        .     NH=IHI-ILO+1.

         IPARMQ = 0
         IF( NS >= KACMIN ) IPARMQ = 1
         IF( NS >= K22MIN ) IPARMQ = 2

      ELSE
!        ===== invalid value of ispec =====
         IPARMQ = -1

      END IF

      END FUNCTION IPARMQ

!> \brief \b IEEECK

!  =========== DOCUMENTATION ===========

! Online html documentation available at
!            http://www.netlib.org/lapack/explore-html/

!  Definition:
!  ===========

!       INTEGER          FUNCTION IEEECK( ISPEC, ZERO, ONE )

!       .. Scalar Arguments ..
!       INTEGER            ISPEC
!       REAL               ONE, ZERO
!       ..
!

!> \par Purpose:
!  =============
!>
!> \verbatim
!>
!> IEEECK is called from the ILAENV to verify that Infinity and
!> possibly NaN arithmetic is safe (i.e. will not trap).
!> \endverbatim

!  Arguments:
!  ==========

!> \param[in] ISPEC
!> \verbatim
!>          ISPEC is INTEGER
!>          Specifies whether to test just for inifinity arithmetic
!>          or whether to test for infinity and NaN arithmetic.
!>          = 0: Verify infinity arithmetic only.
!>          = 1: Verify infinity and NaN arithmetic.
!> \endverbatim
!>
!> \param[in] ZERO
!> \verbatim
!>          ZERO is REAL
!>          Must contain the value 0.0
!>          This is passed to prevent the compiler from optimizing
!>          away this code.
!> \endverbatim
!>
!> \param[in] ONE
!> \verbatim
!>          ONE is REAL
!>          Must contain the value 1.0
!>          This is passed to prevent the compiler from optimizing
!>          away this code.
!>
!>  RETURN VALUE:  INTEGER
!>          = 0:  Arithmetic failed to produce the correct answers
!>          = 1:  Arithmetic produced the correct answers
!> \endverbatim

!  Authors: <AUTHORS>

!> \author Univ. of Tennessee
!> \author Univ. of California Berkeley
!> \author Univ. of Colorado Denver
!> \author NAG Ltd.

!> \date November 2011

!> \ingroup auxOTHERauxiliary

!  =====================================================================
      INTEGER          FUNCTION IEEECK( ISPEC, ZERO, ONE )

  integer,  intent(in) :: ISPEC
  real(dp), intent(in) :: ZERO, ONE

!  -- LAPACK auxiliary routine (version 3.4.0) --
!  -- LAPACK is a software package provided by Univ. of Tennessee,    --
!  -- Univ. of California Berkeley, Univ. of Colorado Denver and NAG Ltd..--
!     November 2011

!     .. Scalar Arguments ..
     !INTEGER            ISPEC
     !REAL               ONE, ZERO
!     ..

!  =====================================================================

!     .. Local Scalars ..
      REAL               NAN1, NAN2, NAN3, NAN4, NAN5, NAN6, NEGINF, &
                         NEGZRO, NEWZRO, POSINF
!     ..
!     .. Executable Statements ..
      IEEECK = 1

      POSINF = ONE / ZERO
      IF( real(POSINF,dp) <= real(ONE,dp) ) THEN
         IEEECK = 0
         RETURN
      END IF

      NEGINF = -ONE / ZERO
      IF( real(NEGINF,dp) >= real(ZERO,dp) ) THEN
         IEEECK = 0
         RETURN
      END IF

      NEGZRO = ONE / ( NEGINF+ONE )
      IF( real(NEGZRO,dp) /= real(ZERO,dp) ) THEN
         IEEECK = 0
         RETURN
      END IF

      NEGINF = ONE / NEGZRO
      IF( real(NEGINF,dp) >= real(ZERO,dp) ) THEN
         IEEECK = 0
         RETURN
      END IF

      NEWZRO = NEGZRO + ZERO
      IF( NEWZRO /= ZERO ) THEN
         IEEECK = 0
         RETURN
      END IF

      POSINF = ONE / NEWZRO
      IF( real(POSINF,dp) <= real(ONE,dp) ) THEN
         IEEECK = 0
         RETURN
      END IF

      NEGINF = NEGINF*POSINF
      IF( real(NEGINF,dp) >= real(ZERO,dp) ) THEN
         IEEECK = 0
         RETURN
      END IF

      POSINF = POSINF*POSINF
      IF( real(POSINF,dp) <= real(ONE,dp) ) THEN
         IEEECK = 0
         RETURN
      END IF




!     Return if we were only asked to check infinity arithmetic

      IF( ISPEC == 0 )  RETURN

      NAN1 = POSINF + NEGINF

      NAN2 = POSINF / NEGINF

      NAN3 = POSINF / POSINF

      NAN4 = POSINF*ZERO

      NAN5 = NEGINF*NEGZRO

      NAN6 = NAN5*ZERO

      IF( NAN1 == NAN1 ) THEN
         IEEECK = 0
         RETURN
      END IF

      IF( NAN2 == NAN2 ) THEN
         IEEECK = 0
         RETURN
      END IF

      IF( NAN3 == NAN3 ) THEN
         IEEECK = 0
         RETURN
      END IF

      IF( NAN4 == NAN4 ) THEN
         IEEECK = 0
         RETURN
      END IF

      IF( NAN5 == NAN5 ) THEN
         IEEECK = 0
         RETURN
      END IF

      IF( NAN6 == NAN6 ) THEN
         IEEECK = 0
         RETURN
      END IF

      END FUNCTION IEEECK

      SUBROUTINE DGEMM(TRANSA,TRANSB,M,N,K,ALPHA,A,LDA,B,LDB,BETA,C,LDC)

!     .. Scalar Arguments ..
      real(dp),  intent(in) :: ALPHA,BETA
      integer,   intent(in) :: K,LDA,LDB,LDC,M,N
      character, intent(in) :: TRANSA,TRANSB
!     ..
!     .. Array Arguments ..
      real(dp), intent(in)    :: A(LDA,*),B(LDB,*)
      real(dp), intent(inout) :: C(LDC,*)

!  Purpose
!  =======

!  DGEMM  performs one of the matrix-matrix operations

!     C := alpha*op( A )*op( B ) + beta*C,

!  where  op( X ) is one of

!     op( X ) = X   or   op( X ) = X',

!  alpha and beta are scalars, and A, B and C are matrices, with op( A )
!  an m by k matrix,  op( B )  a  k by n matrix and  C an m by n matrix.

!  Arguments
!  ==========

!  TRANSA - CHARACTER*1.
!           On entry, TRANSA specifies the form of op( A ) to be used in
!           the matrix multiplication as follows:

!              TRANSA = 'N' or 'n',  op( A ) = A.

!              TRANSA = 'T' or 't',  op( A ) = A'.

!              TRANSA = 'C' or 'c',  op( A ) = A'.

!           Unchanged on exit.

!  TRANSB - CHARACTER*1.
!           On entry, TRANSB specifies the form of op( B ) to be used in
!           the matrix multiplication as follows:

!              TRANSB = 'N' or 'n',  op( B ) = B.

!              TRANSB = 'T' or 't',  op( B ) = B'.

!              TRANSB = 'C' or 'c',  op( B ) = B'.

!           Unchanged on exit.

!  M      - INTEGER.
!           On entry,  M  specifies  the number  of rows  of the  matrix
!           op( A )  and of the  matrix  C.  M  must  be at least  zero.
!           Unchanged on exit.

!  N      - INTEGER.
!           On entry,  N  specifies the number  of columns of the matrix
!           op( B ) and the number of columns of the matrix C. N must be
!           at least zero.
!           Unchanged on exit.

!  K      - INTEGER.
!           On entry,  K  specifies  the number of columns of the matrix
!           op( A ) and the number of rows of the matrix op( B ). K must
!           be at least  zero.
!           Unchanged on exit.

!  ALPHA  - DOUBLE PRECISION.
!           On entry, ALPHA specifies the scalar alpha.
!           Unchanged on exit.

!  A      - DOUBLE PRECISION array of DIMENSION ( LDA, ka ), where ka is
!           k  when  TRANSA = 'N' or 'n',  and is  m  otherwise.
!           Before entry with  TRANSA = 'N' or 'n',  the leading  m by k
!           part of the array  A  must contain the matrix  A,  otherwise
!           the leading  k by m  part of the array  A  must contain  the
!           matrix A.
!           Unchanged on exit.

!  LDA    - INTEGER.
!           On entry, LDA specifies the first dimension of A as declared
!           in the calling (sub) program. When  TRANSA = 'N' or 'n' then
!           LDA must be at least  max( 1, m ), otherwise  LDA must be at
!           least  max( 1, k ).
!           Unchanged on exit.

!  B      - DOUBLE PRECISION array of DIMENSION ( LDB, kb ), where kb is
!           n  when  TRANSB = 'N' or 'n',  and is  k  otherwise.
!           Before entry with  TRANSB = 'N' or 'n',  the leading  k by n
!           part of the array  B  must contain the matrix  B,  otherwise
!           the leading  n by k  part of the array  B  must contain  the
!           matrix B.
!           Unchanged on exit.

!  LDB    - INTEGER.
!           On entry, LDB specifies the first dimension of B as declared
!           in the calling (sub) program. When  TRANSB = 'N' or 'n' then
!           LDB must be at least  max( 1, k ), otherwise  LDB must be at
!           least  max( 1, n ).
!           Unchanged on exit.

!  BETA   - DOUBLE PRECISION.
!           On entry,  BETA  specifies the scalar  beta.  When  BETA  is
!           supplied as zero then C need not be set on input.
!           Unchanged on exit.

!  C      - DOUBLE PRECISION array of DIMENSION ( LDC, n ).
!           Before entry, the leading  m by n  part of the array  C must
!           contain the matrix  C,  except when  beta  is zero, in which
!           case C need not be set on entry.
!           On exit, the array  C  is overwritten by the  m by n  matrix
!           ( alpha*op( A )*op( B ) + beta*C ).

!  LDC    - INTEGER.
!           On entry, LDC specifies the first dimension of C as declared
!           in  the  calling  (sub)  program.   LDC  must  be  at  least
!           max( 1, m ).
!           Unchanged on exit.


!  Level 3 Blas routine.

!  -- Written on 8-February-1989.
!     Jack Dongarra, Argonne National Laboratory.
!     Iain Duff, AERE Harwell.
!     Jeremy Du Croz, Numerical Algorithms Group Ltd.
!     Sven Hammarling, Numerical Algorithms Group Ltd.


!     .. External Functions ..
      !LOGICAL LSAME
      !EXTERNAL LSAME
!     ..
!     .. External Subroutines ..
      !EXTERNAL XERBLA
!     ..
!     .. Intrinsic Functions ..
     !INTRINSIC MAX
!     ..
!     .. Local Scalars ..
      real(dp) :: TEMP
      integer  :: I,INFO,J,L,NROWA,NROWB !NCOLA
      LOGICAL NOTA,NOTB
!     ..
!     .. Parameters ..
      real(dp) :: ONE,ZERO
      PARAMETER (ONE=1.0_dp,ZERO=0.0_dp)
!     ..

!     Set  NOTA  and  NOTB  as  true if  A  and  B  respectively are not
!     transposed and set  NROWA, NCOLA and  NROWB  as the number of rows
!     and  columns of  A  and the  number of  rows  of  B  respectively.

      NOTA = LSAME(TRANSA,'N')
      NOTB = LSAME(TRANSB,'N')
      IF (NOTA) THEN
          NROWA = M
         !NCOLA = K
      ELSE
          NROWA = K
         !NCOLA = M
      END IF
      IF (NOTB) THEN
          NROWB = K
      ELSE
          NROWB = N
      END IF

!     Test the input parameters.

      INFO = 0
      IF ((.NOT.NOTA) .AND. (.NOT.LSAME(TRANSA,'C')) .AND. &
          (.NOT.LSAME(TRANSA,'T'))) THEN
          INFO = 1
      ELSE IF ((.NOT.NOTB) .AND. (.NOT.LSAME(TRANSB,'C')) .AND. &
               (.NOT.LSAME(TRANSB,'T'))) THEN
          INFO = 2
      ELSE IF (M < 0) THEN
          INFO = 3
      ELSE IF (N < 0) THEN
          INFO = 4
      ELSE IF (K < 0) THEN
          INFO = 5
      ELSE IF (LDA < MAX(1,NROWA)) THEN
          INFO = 8
      ELSE IF (LDB < MAX(1,NROWB)) THEN
          INFO = 10
      ELSE IF (LDC < MAX(1,M)) THEN
          INFO = 13
      END IF
      IF (INFO /= 0) THEN
          CALL XERBLA('DGEMM ',INFO)
          RETURN
      END IF

!     Quick return if possible.

      IF ((M == 0) .OR. (N == 0) .OR. &
          (((ALPHA == ZERO).OR. (K == 0)).AND. (BETA == ONE))) RETURN

!     And if  alpha = zero.

      IF (ALPHA == ZERO) THEN
          IF (BETA == ZERO) THEN
              DO  J = 1,N
                  DO  I = 1,M
                      C(I,J) = ZERO
                  ENDDO
              ENDDO
          ELSE
              DO  J = 1,N
                  DO  I = 1,M
                      C(I,J) = BETA*C(I,J)
                  ENDDO
              ENDDO
          END IF
          RETURN
      END IF

!     Start the operations.

      IF (NOTB) THEN
          IF (NOTA) THEN

!           Form  C := alpha*A*B + beta*C.

              DO  J = 1,N
                  IF (BETA == ZERO) THEN
                      DO  I = 1,M
                          C(I,J) = ZERO
                      ENDDO
                  ELSE IF (BETA /= ONE) THEN
                      DO  I = 1,M
                          C(I,J) = BETA*C(I,J)
                      ENDDO
                  END IF
                  DO  L = 1,K
                      IF (B(L,J) /= ZERO) THEN
                          TEMP = ALPHA*B(L,J)
                          DO  I = 1,M
                              C(I,J) = C(I,J) + TEMP*A(I,L)
                          ENDDO
                      END IF
                  ENDDO
              ENDDO
          ELSE

!           Form  C := alpha*A'*B + beta*C

              DO  J = 1,N
                  DO  I = 1,M
                      TEMP = ZERO
                      DO  L = 1,K
                          TEMP = TEMP + A(L,I)*B(L,J)
                      ENDDO
                      IF (BETA == ZERO) THEN
                          C(I,J) = ALPHA*TEMP
                      ELSE
                          C(I,J) = ALPHA*TEMP + BETA*C(I,J)
                      END IF
                  ENDDO
              ENDDO
          END IF
      ELSE
          IF (NOTA) THEN

!           Form  C := alpha*A*B' + beta*C

              DO  J = 1,N
                  IF (BETA == ZERO) THEN
                      DO  I = 1,M
                          C(I,J) = ZERO
                      ENDDO
                  ELSE IF (BETA /= ONE) THEN
                      DO  I = 1,M
                          C(I,J) = BETA*C(I,J)
                      ENDDO
                  END IF
                  DO  L = 1,K
                      IF (B(J,L) /= ZERO) THEN
                          TEMP = ALPHA*B(J,L)
                          DO  I = 1,M
                              C(I,J) = C(I,J) + TEMP*A(I,L)
                          ENDDO
                      END IF
                  ENDDO
              ENDDO
          ELSE

!           Form  C := alpha*A'*B' + beta*C

              DO J = 1,N
                  DO I = 1,M
                      TEMP = ZERO
                      DO  L = 1,K
                          TEMP = TEMP + A(L,I)*B(J,L)
                      ENDDO
                      IF (BETA == ZERO) THEN
                          C(I,J) = ALPHA*TEMP
                      ELSE
                          C(I,J) = ALPHA*TEMP + BETA*C(I,J)
                      END IF
                 ENDDO
              ENDDO
          END IF
      END IF

      END SUBROUTINE DGEMM
      SUBROUTINE DGEMV(TRANS,M,N,ALPHA,A,LDA,X,INCX,BETA,Y,INCY)

!     .. Scalar Arguments ..
      real(dp),  intent(in) :: ALPHA,BETA
      integer,   intent(in) :: INCX,INCY,LDA,M,N
      character, intent(in) :: TRANS

!     .. Array Arguments ..
      real(dp), intent(in)    :: A(LDA,*),X(*)
      real(dp), intent(inout) :: Y(*)

!  Purpose
!  =======

!  DGEMV  performs one of the matrix-vector operations

!     y := alpha*A*x + beta*y,   or   y := alpha*A'*x + beta*y,

!  where alpha and beta are scalars, x and y are vectors and A is an
!  m by n matrix.

!  Arguments
!  ==========

!  TRANS  - CHARACTER*1.
!           On entry, TRANS specifies the operation to be performed as
!           follows:

!              TRANS = 'N' or 'n'   y := alpha*A*x + beta*y.

!              TRANS = 'T' or 't'   y := alpha*A'*x + beta*y.

!              TRANS = 'C' or 'c'   y := alpha*A'*x + beta*y.

!           Unchanged on exit.

!  M      - INTEGER.
!           On entry, M specifies the number of rows of the matrix A.
!           M must be at least zero.
!           Unchanged on exit.

!  N      - INTEGER.
!           On entry, N specifies the number of columns of the matrix A.
!           N must be at least zero.
!           Unchanged on exit.

!  ALPHA  - DOUBLE PRECISION.
!           On entry, ALPHA specifies the scalar alpha.
!           Unchanged on exit.

!  A      - DOUBLE PRECISION array of DIMENSION ( LDA, n ).
!           Before entry, the leading m by n part of the array A must
!           contain the matrix of coefficients.
!           Unchanged on exit.

!  LDA    - INTEGER.
!           On entry, LDA specifies the first dimension of A as declared
!           in the calling (sub) program. LDA must be at least
!           max( 1, m ).
!           Unchanged on exit.

!  X      - DOUBLE PRECISION array of DIMENSION at least
!           ( 1 + ( n - 1 )*abs( INCX ) ) when TRANS = 'N' or 'n'
!           and at least
!           ( 1 + ( m - 1 )*abs( INCX ) ) otherwise.
!           Before entry, the incremented array X must contain the
!           vector x.
!           Unchanged on exit.

!  INCX   - INTEGER.
!           On entry, INCX specifies the increment for the elements of
!           X. INCX must not be zero.
!           Unchanged on exit.

!  BETA   - DOUBLE PRECISION.
!           On entry, BETA specifies the scalar beta. When BETA is
!           supplied as zero then Y need not be set on input.
!           Unchanged on exit.

!  Y      - DOUBLE PRECISION array of DIMENSION at least
!           ( 1 + ( m - 1 )*abs( INCY ) ) when TRANS = 'N' or 'n'
!           and at least
!           ( 1 + ( n - 1 )*abs( INCY ) ) otherwise.
!           Before entry with BETA non-zero, the incremented array Y
!           must contain the vector y. On exit, Y is overwritten by the
!           updated vector y.

!  INCY   - INTEGER.
!           On entry, INCY specifies the increment for the elements of
!           Y. INCY must not be zero.
!           Unchanged on exit.


!  Level 2 Blas routine.

!  -- Written on 22-October-1986.
!     Jack Dongarra, Argonne National Lab.
!     Jeremy Du Croz, Nag Central Office.
!     Sven Hammarling, Nag Central Office.
!     Richard Hanson, Sandia National Labs.


!     .. Parameters ..
      real(dp) :: ONE,ZERO
      PARAMETER (ONE=1.0_dp,ZERO=0.0_dp)
!     ..
!     .. Local Scalars ..
      real(dp) :: TEMP
      INTEGER I,INFO,IX,IY,J,JX,JY,KX,KY,LENX,LENY
!     ..
!     .. External Functions ..
      !LOGICAL LSAME
      !EXTERNAL LSAME
!     ..
!     .. External Subroutines ..
      !EXTERNAL XERBLA
!     ..
!     .. Intrinsic Functions ..
     !INTRINSIC MAX
!     ..

!     Test the input parameters.

      INFO = 0
      IF (.NOT.LSAME(TRANS,'N') .AND. .NOT.LSAME(TRANS,'T') .AND. &
          .NOT.LSAME(TRANS,'C')) THEN
          INFO = 1
      ELSE IF (M < 0) THEN
          INFO = 2
      ELSE IF (N < 0) THEN
          INFO = 3
      ELSE IF (LDA < MAX(1,M)) THEN
          INFO = 6
      ELSE IF (INCX == 0) THEN
          INFO = 8
      ELSE IF (INCY == 0) THEN
          INFO = 11
      END IF
      IF (INFO /= 0) THEN
          CALL XERBLA('DGEMV ',INFO)
          RETURN
      END IF

!     Quick return if possible.

      IF ((M == 0) .OR. (N == 0) .OR. &
          ((ALPHA == ZERO).AND. (BETA == ONE))) RETURN

!     Set  LENX  and  LENY, the lengths of the vectors x and y, and set
!     up the start points in  X  and  Y.

      IF (LSAME(TRANS,'N')) THEN
          LENX = N
          LENY = M
      ELSE
          LENX = M
          LENY = N
      END IF
      IF (INCX > 0) THEN
          KX = 1
      ELSE
          KX = 1 - (LENX-1)*INCX
      END IF
      IF (INCY > 0) THEN
          KY = 1
      ELSE
          KY = 1 - (LENY-1)*INCY
      END IF

!     Start the operations. In this version the elements of A are
!     accessed sequentially with one pass through A.

!     First form  y := beta*y.

      IF (BETA /= ONE) THEN
          IF (INCY == 1) THEN
              IF (BETA == ZERO) THEN
                  DO  I = 1,LENY
                      Y(I) = ZERO
                  ENDDO
              ELSE
                  DO  I = 1,LENY
                      Y(I) = BETA*Y(I)
                  ENDDO
              END IF
          ELSE
              IY = KY
              IF (BETA == ZERO) THEN
                  DO  I = 1,LENY
                      Y(IY) = ZERO
                      IY = IY + INCY
                  ENDDO
              ELSE
                  DO  I = 1,LENY
                      Y(IY) = BETA*Y(IY)
                      IY = IY + INCY
                  ENDDO
              END IF
          END IF
      END IF
      IF (ALPHA == ZERO) RETURN
      IF (LSAME(TRANS,'N')) THEN

!        Form  y := alpha*A*x + y.

          JX = KX
          IF (INCY == 1) THEN
              DO  J = 1,N
                  IF (X(JX) /= ZERO) THEN
                      TEMP = ALPHA*X(JX)
                      DO  I = 1,M
                          Y(I) = Y(I) + TEMP*A(I,J)
                      ENDDO
                  END IF
                  JX = JX + INCX
              ENDDO
          ELSE
              DO  J = 1,N
                  IF (X(JX) /= ZERO) THEN
                      TEMP = ALPHA*X(JX)
                      IY = KY
                      DO  I = 1,M
                          Y(IY) = Y(IY) + TEMP*A(I,J)
                          IY = IY + INCY
                      ENDDO
                  END IF
                  JX = JX + INCX
              ENDDO
          END IF
      ELSE

!        Form  y := alpha*A'*x + y.

          JY = KY
          IF (INCX == 1) THEN
              DO  J = 1,N
                  TEMP = ZERO
                  DO  I = 1,M
                      TEMP = TEMP + A(I,J)*X(I)
                  ENDDO
                  Y(JY) = Y(JY) + ALPHA*TEMP
                  JY = JY + INCY
              ENDDO
          ELSE
              DO  J = 1,N
                  TEMP = ZERO
                  IX = KX
                  DO  I = 1,M
                      TEMP = TEMP + A(I,J)*X(IX)
                      IX = IX + INCX
                  ENDDO
                  Y(JY) = Y(JY) + ALPHA*TEMP
                  JY = JY + INCY
              ENDDO
          END IF
      END IF

      END SUBROUTINE DGEMV

      SUBROUTINE DTRSM(SIDE,UPLO,TRANSA,DIAG,M,N,ALPHA,A,LDA,B,LDB)

!     .. Scalar Arguments ..
      real(dp),  intent(in) :: ALPHA
      integer,   intent(in) :: LDA,LDB,M,N
      character, intent(in) :: DIAG,SIDE,TRANSA,UPLO
!     ..
!     .. Array Arguments ..
      real(dp), intent(in)    :: A(LDA,*)
      real(dp), intent(inout) :: B(LDB,*)

!  Purpose
!  =======

!  DTRSM  solves one of the matrix equations

!     op( A )*X = alpha*B,   or   X*op( A ) = alpha*B,

!  where alpha is a scalar, X and B are m by n matrices, A is a unit, or
!  non-unit,  upper or lower triangular matrix  and  op( A )  is one  of

!     op( A ) = A   or   op( A ) = A'.

!  The matrix X is overwritten on B.

!  Arguments
!  ==========

!  SIDE   - CHARACTER*1.
!           On entry, SIDE specifies whether op( A ) appears on the left
!           or right of X as follows:

!              SIDE = 'L' or 'l'   op( A )*X = alpha*B.

!              SIDE = 'R' or 'r'   X*op( A ) = alpha*B.

!           Unchanged on exit.

!  UPLO   - CHARACTER*1.
!           On entry, UPLO specifies whether the matrix A is an upper or
!           lower triangular matrix as follows:

!              UPLO = 'U' or 'u'   A is an upper triangular matrix.

!              UPLO = 'L' or 'l'   A is a lower triangular matrix.

!           Unchanged on exit.

!  TRANSA - CHARACTER*1.
!           On entry, TRANSA specifies the form of op( A ) to be used in
!           the matrix multiplication as follows:

!              TRANSA = 'N' or 'n'   op( A ) = A.

!              TRANSA = 'T' or 't'   op( A ) = A'.

!              TRANSA = 'C' or 'c'   op( A ) = A'.

!           Unchanged on exit.

!  DIAG   - CHARACTER*1.
!           On entry, DIAG specifies whether or not A is unit triangular
!           as follows:

!              DIAG = 'U' or 'u'   A is assumed to be unit triangular.

!              DIAG = 'N' or 'n'   A is not assumed to be unit
!                                  triangular.

!           Unchanged on exit.

!  M      - INTEGER.
!           On entry, M specifies the number of rows of B. M must be at
!           least zero.
!           Unchanged on exit.

!  N      - INTEGER.
!           On entry, N specifies the number of columns of B.  N must be
!           at least zero.
!           Unchanged on exit.

!  ALPHA  - DOUBLE PRECISION.
!           On entry,  ALPHA specifies the scalar  alpha. When  alpha is
!           zero then  A is not referenced and  B need not be set before
!           entry.
!           Unchanged on exit.

!  A      - DOUBLE PRECISION array of DIMENSION ( LDA, k ), where k is m
!           when  SIDE = 'L' or 'l'  and is  n  when  SIDE = 'R' or 'r'.
!           Before entry  with  UPLO = 'U' or 'u',  the  leading  k by k
!           upper triangular part of the array  A must contain the upper
!           triangular matrix  and the strictly lower triangular part of
!           A is not referenced.
!           Before entry  with  UPLO = 'L' or 'l',  the  leading  k by k
!           lower triangular part of the array  A must contain the lower
!           triangular matrix  and the strictly upper triangular part of
!           A is not referenced.
!           Note that when  DIAG = 'U' or 'u',  the diagonal elements of
!           A  are not referenced either,  but are assumed to be  unity.
!           Unchanged on exit.

!  LDA    - INTEGER.
!           On entry, LDA specifies the first dimension of A as declared
!           in the calling (sub) program.  When  SIDE = 'L' or 'l'  then
!           LDA  must be at least  max( 1, m ),  when  SIDE = 'R' or 'r'
!           then LDA must be at least max( 1, n ).
!           Unchanged on exit.

!  B      - DOUBLE PRECISION array of DIMENSION ( LDB, n ).
!           Before entry,  the leading  m by n part of the array  B must
!           contain  the  right-hand  side  matrix  B,  and  on exit  is
!           overwritten by the solution matrix  X.

!  LDB    - INTEGER.
!           On entry, LDB specifies the first dimension of B as declared
!           in  the  calling  (sub)  program.   LDB  must  be  at  least
!           max( 1, m ).
!           Unchanged on exit.


!  Level 3 Blas routine.


!  -- Written on 8-February-1989.
!     Jack Dongarra, Argonne National Laboratory.
!     Iain Duff, AERE Harwell.
!     Jeremy Du Croz, Numerical Algorithms Group Ltd.
!     Sven Hammarling, Numerical Algorithms Group Ltd.


!     .. External Functions ..
     !LOGICAL LSAME
     !EXTERNAL LSAME
!     ..
!     .. External Subroutines ..
     !EXTERNAL XERBLA
!     ..
!     .. Intrinsic Functions ..
     !INTRINSIC MAX
!     ..
!     .. Local Scalars ..
      real(dp) :: TEMP
      INTEGER I,INFO,J,K,NROWA
      LOGICAL LSIDE,NOUNIT,UPPER
!     ..
!     .. Parameters ..
      real(dp) :: ONE,ZERO
      PARAMETER (ONE=1.0_dp,ZERO=0.0_dp)
!     ..

!     Test the input parameters.

      LSIDE = LSAME(SIDE,'L')
      IF (LSIDE) THEN
          NROWA = M
      ELSE
          NROWA = N
      END IF
      NOUNIT = LSAME(DIAG,'N')
      UPPER = LSAME(UPLO,'U')

      INFO = 0
      IF ((.NOT.LSIDE) .AND. (.NOT.LSAME(SIDE,'R'))) THEN
          INFO = 1
      ELSE IF ((.NOT.UPPER) .AND. (.NOT.LSAME(UPLO,'L'))) THEN
          INFO = 2
      ELSE IF ((.NOT.LSAME(TRANSA,'N')) .AND. &
               (.NOT.LSAME(TRANSA,'T')) .AND. &
               (.NOT.LSAME(TRANSA,'C'))) THEN
          INFO = 3
      ELSE IF ((.NOT.LSAME(DIAG,'U')) .AND. (.NOT.LSAME(DIAG,'N'))) THEN
          INFO = 4
      ELSE IF (M < 0) THEN
          INFO = 5
      ELSE IF (N < 0) THEN
          INFO = 6
      ELSE IF (LDA < MAX(1,NROWA)) THEN
          INFO = 9
      ELSE IF (LDB < MAX(1,M)) THEN
          INFO = 11
      END IF
      IF (INFO /= 0) THEN
          CALL XERBLA('DTRSM ',INFO)
          RETURN
      END IF

!     Quick return if possible.

      IF (M == 0 .OR. N == 0) RETURN

!     And when  alpha = zero.

      IF (ALPHA == ZERO) THEN
          DO  J = 1,N
              DO  I = 1,M
                  B(I,J) = ZERO
              ENDDO
          ENDDO
          RETURN
      END IF

!     Start the operations.

      IF (LSIDE) THEN
          IF (LSAME(TRANSA,'N')) THEN

!           Form  B := alpha*inv( A )*B.

              IF (UPPER) THEN
                  DO  J = 1,N
                      IF (ALPHA /= ONE) THEN
                          DO  I = 1,M
                              B(I,J) = ALPHA*B(I,J)
                          ENDDO
                      END IF
                      DO  K = M,1,-1
                          IF (B(K,J) /= ZERO) THEN
                              IF (NOUNIT) B(K,J) = B(K,J)/A(K,K)
                              DO  I = 1,K - 1
                                  B(I,J) = B(I,J) - B(K,J)*A(I,K)
                              ENDDO
                          END IF
                      ENDDO
                  ENDDO
              ELSE
                  DO  J = 1,N
                      IF (ALPHA /= ONE) THEN
                          DO  I = 1,M
                              B(I,J) = ALPHA*B(I,J)
                          ENDDO
                      END IF
                      DO  K = 1,M
                          IF (B(K,J) /= ZERO) THEN
                              IF (NOUNIT) B(K,J) = B(K,J)/A(K,K)
                              DO  I = K + 1,M
                                  B(I,J) = B(I,J) - B(K,J)*A(I,K)
                              ENDDO
                          END IF
                      ENDDO
                 ENDDO
              END IF
          ELSE

!           Form  B := alpha*inv( A' )*B.

              IF (UPPER) THEN
                  DO  J = 1,N
                      DO  I = 1,M
                          TEMP = ALPHA*B(I,J)
                          DO  K = 1,I - 1
                              TEMP = TEMP - A(K,I)*B(K,J)
                          ENDDO
                          IF (NOUNIT) TEMP = TEMP/A(I,I)
                          B(I,J) = TEMP
                      ENDDO
                  ENDDO
              ELSE
                  DO  J = 1,N
                      DO  I = M,1,-1
                          TEMP = ALPHA*B(I,J)
                          DO  K = I + 1,M
                              TEMP = TEMP - A(K,I)*B(K,J)
                          ENDDO
                          IF (NOUNIT) TEMP = TEMP/A(I,I)
                          B(I,J) = TEMP
                      ENDDO
                  ENDDO
              END IF
          END IF
      ELSE
          IF (LSAME(TRANSA,'N')) THEN

!           Form  B := alpha*B*inv( A ).

              IF (UPPER) THEN
                  DO  J = 1,N
                      IF (ALPHA /= ONE) THEN
                          DO  I = 1,M
                              B(I,J) = ALPHA*B(I,J)
                          ENDDO
                      END IF
                      DO  K = 1,J - 1
                          IF (A(K,J) /= ZERO) THEN
                              DO  I = 1,M
                                  B(I,J) = B(I,J) - A(K,J)*B(I,K)
                              ENDDO
                          END IF
                      ENDDO
                      IF (NOUNIT) THEN
                          TEMP = ONE/A(J,J)
                          DO  I = 1,M
                              B(I,J) = TEMP*B(I,J)
                          ENDDO
                      END IF
                  ENDDO
              ELSE
                  DO  J = N,1,-1
                      IF (ALPHA /= ONE) THEN
                          DO  I = 1,M
                              B(I,J) = ALPHA*B(I,J)
                          ENDDO
                      END IF
                      DO  K = J + 1,N
                          IF (A(K,J) /= ZERO) THEN
                              DO  I = 1,M
                                  B(I,J) = B(I,J) - A(K,J)*B(I,K)
                              ENDDO
                          END IF
                      ENDDO
                      IF (NOUNIT) THEN
                          TEMP = ONE/A(J,J)
                          DO  I = 1,M
                              B(I,J) = TEMP*B(I,J)
                          ENDDO
                      END IF
                  ENDDO
              END IF
          ELSE

!           Form  B := alpha*B*inv( A' ).

              IF (UPPER) THEN
                  DO  K = N,1,-1
                      IF (NOUNIT) THEN
                          TEMP = ONE/A(K,K)
                          DO  I = 1,M
                              B(I,K) = TEMP*B(I,K)
                          ENDDO
                      END IF
                      DO  J = 1,K - 1
                          IF (A(J,K) /= ZERO) THEN
                              TEMP = A(J,K)
                              DO  I = 1,M
                                  B(I,J) = B(I,J) - TEMP*B(I,K)
                              ENDDO
                          END IF
                      ENDDO
                      IF (ALPHA /= ONE) THEN
                          DO  I = 1,M
                              B(I,K) = ALPHA*B(I,K)
                          ENDDO
                      END IF
                  ENDDO
              ELSE
                  DO  K = 1,N
                      IF (NOUNIT) THEN
                          TEMP = ONE/A(K,K)
                          DO  I = 1,M
                              B(I,K) = TEMP*B(I,K)
                          ENDDO
                      END IF
                      DO  J = K + 1,N
                          IF (A(J,K) /= ZERO) THEN
                              TEMP = A(J,K)
                              DO  I = 1,M
                                  B(I,J) = B(I,J) - TEMP*B(I,K)
                              ENDDO
                          END IF
                      ENDDO
                      IF (ALPHA /= ONE) THEN
                          DO  I = 1,M
                              B(I,K) = ALPHA*B(I,K)
                          ENDDO
                      END IF
                  ENDDO
              END IF
          END IF
      END IF

      END SUBROUTINE DTRSM

      SUBROUTINE DSYRK(UPLO,TRANS,N,K,ALPHA,A,LDA,BETA,C,LDC)

!     .. Scalar Arguments ..
      real(dp),  intent(in) :: ALPHA,BETA
      integer,   intent(in) :: K,LDA,LDC,N
      character, intent(in) :: TRANS,UPLO
!     ..
!     .. Array Arguments ..
      real(dp), intent(in)    :: A(LDA,*)
      real(dp), intent(inout) :: C(LDC,*)

!  Purpose
!  =======

!  DSYRK  performs one of the symmetric rank k operations

!     C := alpha*A*A' + beta*C,

!  or

!     C := alpha*A'*A + beta*C,

!  where  alpha and beta  are scalars, C is an  n by n  symmetric matrix
!  and  A  is an  n by k  matrix in the first case and a  k by n  matrix
!  in the second case.

!  Arguments
!  ==========

!  UPLO   - CHARACTER*1.
!           On  entry,   UPLO  specifies  whether  the  upper  or  lower
!           triangular  part  of the  array  C  is to be  referenced  as
!           follows:

!              UPLO = 'U' or 'u'   Only the  upper triangular part of  C
!                                  is to be referenced.

!              UPLO = 'L' or 'l'   Only the  lower triangular part of  C
!                                  is to be referenced.

!           Unchanged on exit.

!  TRANS  - CHARACTER*1.
!           On entry,  TRANS  specifies the operation to be performed as
!           follows:

!              TRANS = 'N' or 'n'   C := alpha*A*A' + beta*C.

!              TRANS = 'T' or 't'   C := alpha*A'*A + beta*C.

!              TRANS = 'C' or 'c'   C := alpha*A'*A + beta*C.

!           Unchanged on exit.

!  N      - INTEGER.
!           On entry,  N specifies the order of the matrix C.  N must be
!           at least zero.
!           Unchanged on exit.

!  K      - INTEGER.
!           On entry with  TRANS = 'N' or 'n',  K  specifies  the number
!           of  columns   of  the   matrix   A,   and  on   entry   with
!           TRANS = 'T' or 't' or 'C' or 'c',  K  specifies  the  number
!           of rows of the matrix  A.  K must be at least zero.
!           Unchanged on exit.

!  ALPHA  - DOUBLE PRECISION.
!           On entry, ALPHA specifies the scalar alpha.
!           Unchanged on exit.

!  A      - DOUBLE PRECISION array of DIMENSION ( LDA, ka ), where ka is
!           k  when  TRANS = 'N' or 'n',  and is  n  otherwise.
!           Before entry with  TRANS = 'N' or 'n',  the  leading  n by k
!           part of the array  A  must contain the matrix  A,  otherwise
!           the leading  k by n  part of the array  A  must contain  the
!           matrix A.
!           Unchanged on exit.

!  LDA    - INTEGER.
!           On entry, LDA specifies the first dimension of A as declared
!           in  the  calling  (sub)  program.   When  TRANS = 'N' or 'n'
!           then  LDA must be at least  max( 1, n ), otherwise  LDA must
!           be at least  max( 1, k ).
!           Unchanged on exit.

!  BETA   - DOUBLE PRECISION.
!           On entry, BETA specifies the scalar beta.
!           Unchanged on exit.

!  C      - DOUBLE PRECISION array of DIMENSION ( LDC, n ).
!           Before entry  with  UPLO = 'U' or 'u',  the leading  n by n
!           upper triangular part of the array C must contain the upper
!           triangular part  of the  symmetric matrix  and the strictly
!           lower triangular part of C is not referenced.  On exit, the
!           upper triangular part of the array  C is overwritten by the
!           upper triangular part of the updated matrix.
!           Before entry  with  UPLO = 'L' or 'l',  the leading  n by n
!           lower triangular part of the array C must contain the lower
!           triangular part  of the  symmetric matrix  and the strictly
!           upper triangular part of C is not referenced.  On exit, the
!           lower triangular part of the array  C is overwritten by the
!           lower triangular part of the updated matrix.

!  LDC    - INTEGER.
!           On entry, LDC specifies the first dimension of C as declared
!           in  the  calling  (sub)  program.   LDC  must  be  at  least
!           max( 1, n ).
!           Unchanged on exit.


!  Level 3 Blas routine.

!  -- Written on 8-February-1989.
!     Jack Dongarra, Argonne National Laboratory.
!     Iain Duff, AERE Harwell.
!     Jeremy Du Croz, Numerical Algorithms Group Ltd.
!     Sven Hammarling, Numerical Algorithms Group Ltd.


!     .. External Functions ..
     !LOGICAL LSAME
     !EXTERNAL LSAME
!     ..
!     .. External Subroutines ..
     !EXTERNAL XERBLA
!     ..
!     .. Intrinsic Functions ..
     !INTRINSIC MAX
!     ..
!     .. Local Scalars ..
      real(dp) :: TEMP
      INTEGER I,INFO,J,L,NROWA
      LOGICAL UPPER
!     ..
!     .. Parameters ..
      real(dp) :: ONE,ZERO
      PARAMETER (ONE=1.0_dp,ZERO=0.0_dp)
!     ..

!     Test the input parameters.

      IF (LSAME(TRANS,'N')) THEN
          NROWA = N
      ELSE
          NROWA = K
      END IF
      UPPER = LSAME(UPLO,'U')

      INFO = 0
      IF ((.NOT.UPPER) .AND. (.NOT.LSAME(UPLO,'L'))) THEN
          INFO = 1
      ELSE IF ((.NOT.LSAME(TRANS,'N')) .AND. &
               (.NOT.LSAME(TRANS,'T')) .AND. &
               (.NOT.LSAME(TRANS,'C'))) THEN
          INFO = 2
      ELSE IF (N < 0) THEN
          INFO = 3
      ELSE IF (K < 0) THEN
          INFO = 4
      ELSE IF (LDA < MAX(1,NROWA)) THEN
          INFO = 7
      ELSE IF (LDC < MAX(1,N)) THEN
          INFO = 10
      END IF
      IF (INFO /= 0) THEN
          CALL XERBLA('DSYRK ',INFO)
          RETURN
      END IF

!     Quick return if possible.

      IF ((N == 0) .OR. (((ALPHA == ZERO).OR. &
          (K == 0)).AND. (BETA == ONE))) RETURN

!     And when  alpha = zero.

      IF (ALPHA == ZERO) THEN
          IF (UPPER) THEN
              IF (BETA == ZERO) THEN
                  DO  J = 1,N
                      DO  I = 1,J
                          C(I,J) = ZERO
                       ENDDO
                  ENDDO
              ELSE
                  DO  J = 1,N
                      DO  I = 1,J
                          C(I,J) = BETA*C(I,J)
                      ENDDO
                  ENDDO
              END IF
          ELSE
              IF (BETA == ZERO) THEN
                  DO  J = 1,N
                      DO  I = J,N
                          C(I,J) = ZERO
                      ENDDO
                  ENDDO
              ELSE
                  DO  J = 1,N
                      DO  I = J,N
                          C(I,J) = BETA*C(I,J)
                      ENDDO
                  ENDDO
              END IF
          END IF
          RETURN
      END IF

!     Start the operations.

      IF (LSAME(TRANS,'N')) THEN

!        Form  C := alpha*A*A' + beta*C.

          IF (UPPER) THEN
              DO  J = 1,N
                  IF (BETA == ZERO) THEN
                      DO  I = 1,J
                          C(I,J) = ZERO
                      ENDDO
                  ELSE IF (BETA /= ONE) THEN
                      DO  I = 1,J
                          C(I,J) = BETA*C(I,J)
                      ENDDO
                  END IF
                  DO  L = 1,K
                      IF (A(J,L) /= ZERO) THEN
                          TEMP = ALPHA*A(J,L)
                          DO  I = 1,J
                              C(I,J) = C(I,J) + TEMP*A(I,L)
                          ENDDO
                      END IF
                  ENDDO
              ENDDO
          ELSE
              DO  J = 1,N
                  IF (BETA == ZERO) THEN
                      DO  I = J,N
                          C(I,J) = ZERO
                      ENDDO
                  ELSE IF (BETA /= ONE) THEN
                      DO  I = J,N
                          C(I,J) = BETA*C(I,J)
                      ENDDO
                  END IF
                  DO  L = 1,K
                      IF (A(J,L) /= ZERO) THEN
                          TEMP = ALPHA*A(J,L)
                          DO  I = J,N
                              C(I,J) = C(I,J) + TEMP*A(I,L)
                          ENDDO
                      END IF
                  ENDDO
              ENDDO
          END IF
      ELSE

!        Form  C := alpha*A'*A + beta*C.

          IF (UPPER) THEN
              DO  J = 1,N
                  DO  I = 1,J
                      TEMP = ZERO
                      DO  L = 1,K
                          TEMP = TEMP + A(L,I)*A(L,J)
                      ENDDO
                      IF (BETA == ZERO) THEN
                          C(I,J) = ALPHA*TEMP
                      ELSE
                          C(I,J) = ALPHA*TEMP + BETA*C(I,J)
                      END IF
                  ENDDO
              ENDDO
          ELSE
              DO  J = 1,N
                  DO  I = J,N
                      TEMP = ZERO
                      DO  L = 1,K
                          TEMP = TEMP + A(L,I)*A(L,J)
                      ENDDO
                      IF (BETA == ZERO) THEN
                          C(I,J) = ALPHA*TEMP
                      ELSE
                          C(I,J) = ALPHA*TEMP + BETA*C(I,J)
                      END IF
                  ENDDO
              ENDDO
          END IF
      END IF

      END SUBROUTINE DSYRK



end module av3p2_lapack_util

!================================= MAPPING_COORDS ===========================80
!
! Approximate-mapping coordinates (xie,eta,zie).
!
!=============================================================================80

  pure function mapping_coords( xc, yc, zc, xr, yr, zr, tr, sc, sr )

    real(dp),                 intent(in)           :: xc, yc, zc, xr, yr, zr
    real(dp), dimension(3,3), intent(in)           :: tr
    real(dp),                 intent(in), optional :: sc, sr

    real(dp) :: xie, eta, zie
    real(dp), dimension(3) :: dr

    real(dp), dimension(3) :: mapping_coords

  continue

    dr(1) =  xc - xr !dx = xc - xr
    dr(2) =  yc - yr !dy = yc - yr
    dr(3) =  zc - zr !dz = zc - zr

    if ( present(sc) .and. present(sr) ) then
      xie = sc - sr
    else
      xie = sum( tr(1,:)*dr(:) )
    endif

    eta = sum( tr(2,:)*dr(:) )
    zie = sum( tr(3,:)*dr(:) )

    mapping_coords(1) = xie  ! xie = tx*dx + ty*dy + tz*dz
    mapping_coords(2) = eta  ! eta = lx*dx + ly*dy + lz*dz
    mapping_coords(3) = zie  ! zie = mx*dx + my*dy + mz*dz

  end function mapping_coords

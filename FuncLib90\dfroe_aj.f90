!=========================== DFROE_AJ ========================================80
!
! Roe (approximate) flux jacobians for the left and right states
!
! Note: ql, qr are conservative variables
!
! The left  jacobian is returned in dfroe_aj(:,:,1)
! The right jacobian is returned in dfroe_aj(:,:,2)
!
!=============================================================================80

  pure function dfroe_aj(xnorm, ynorm, znorm, area, face_speed, ql, qr)


    use inviscid_flux,     only : lhs_a_eigenvalue_coef, lhs_u_eigenvalue_coef
    use fluid,     only : gm1

    real(dp), intent(in) :: xnorm, ynorm, znorm, area, face_speed

    real(dp), dimension(5), intent(in) :: ql, qr

    real(dp), dimension(5,5,2) :: dfroe_aj

    real(dp) :: flux1, half_area

    real(dp) :: flux1r,flux1u,flux1v,flux1w
    real(dp) :: flux2r,flux2u,flux2v,flux2w,flux2p
    real(dp) :: flux3r,flux3u,flux3v,flux3w,flux3p
    real(dp) :: flux4r,flux4u,flux4v,flux4w,flux4p
    real(dp) :: flux5r,flux5u,flux5v,flux5w,flux5p

    real(dp) :: t1r,t1u,t1v,t1w,t1p
    real(dp) :: t2r,t2u,t2v,t2w,t2p
    real(dp) :: t3r,t3u,t3v,t3w,t3p
    real(dp) :: t4r,t4u,t4v,t4w,t4p
    real(dp) :: t5r,t5u,t5v,t5w,t5p

    real(dp) :: r54

    real(dp) :: r44

    real(dp) :: r34

    real(dp) :: r24

!   real(dp) :: r53
    real(dp) :: r53ul,r53vl,r53wl

!   real(dp) :: r43
    real(dp) :: r43ul,r43vl,r43wl

!   real(dp) :: r33
    real(dp) :: r33ul,r33vl,r33wl

!   real(dp) :: r23
    real(dp) :: r23ul,r23vl,r23wl

    real(dp) :: r52

    real(dp) :: r42

    real(dp) :: r32

    real(dp) :: r22

    real(dp) :: r51

    real(dp) :: r41

    real(dp) :: r31

    real(dp) :: r21

!   real(dp) :: dv1
    real(dp) :: dv1ul,dv1vl,dv1wl,dv1pl

!   real(dp) :: dv2
    real(dp) :: dv2ul,dv2vl,dv2wl,dv2pl

!   real(dp) :: dv3 = rho (not actually a jump)

!   real(dp) :: dv4
    real(dp) :: dv4rl,dv4pl

    real(dp) :: c2, c2i, rhoc, ri

!   real(dp) :: dubar, du, dv, dw, dpress, drho
    real(dp) :: eig1, abseig1
    real(dp) :: eig2, abseig2
    real(dp) :: eig3, abseig3, abseig4

    real(dp) :: ubar, c, q2, h, u, v, w, wat, rho

    real(dp) :: ubarr

    real(dp) :: hr
    real(dp) :: hrrr,hrur,hrvr,hrwr,hrpr

    real(dp) :: enrgyr
!   real(dp) :: enrgyrpr

    real(dp) :: pr
    real(dp) :: prrr,prur,prvr,prwr,prpr

    real(dp) :: q2r
    real(dp) :: q2rrr,q2rur,q2rvr,q2rwr

    real(dp) :: ur
    real(dp) :: urrr,urur

    real(dp) :: vr
    real(dp) :: vrrr,vrvr

    real(dp) :: wr
    real(dp) :: wrrr,wrwr

    real(dp) :: rhor, rri, rri2

    real(dp) :: ubarl

    real(dp) :: hl
    real(dp) :: hlrl,hlul,hlvl,hlwl,hlpl

    real(dp) :: enrgyl
!   real(dp) :: enrgylpl

    real(dp) :: pl
    real(dp) :: plrl,plul,plvl,plwl,plpl

    real(dp) :: q2l
    real(dp) :: q2lrl,q2lul,q2lvl,q2lwl

    real(dp) :: ul
    real(dp) :: ulrl,ulul

    real(dp) :: vl
    real(dp) :: vlrl,vlvl

    real(dp) :: wl
    real(dp) :: wlrl,wlwl

    real(dp) :: rhol, rli, rli2

    real(dp) :: eigeps1, eigeps2, eigeps3, maxeig

  continue

! Primitive variables on "left" side of face

      rhol = ql(1)
      rli  = 1.0_dp / rhol
      rli2 = rli*rli

      ul = ql(2)*rli
        ulrl = -ul*rli
        ulul = rli
      vl = ql(3)*rli
        vlrl = -vl*rli
        vlvl = rli
      wl = ql(4)*rli
        wlrl = -wl*rli
        wlwl = rli

      q2l = ul*ul + vl*vl + wl*wl
        q2lrl = 2.0_dp*ul*ulrl + 2.0_dp*vl*vlrl + 2.0_dp*wl*wlrl
        q2lul = 2.0_dp*ul*ulul
        q2lvl = 2.0_dp*vl*vlvl
        q2lwl = 2.0_dp*wl*wlwl

      enrgyl = ql(5)
!       enrgylpl = 1.0_dp

      pl = gm1*(enrgyl - 0.5_dp*rhol*q2l)
        plrl = -0.5_dp*gm1*(rhol*q2lrl + q2l)
        plul = -0.5_dp*gm1*rhol*q2lul
        plvl = -0.5_dp*gm1*rhol*q2lvl
        plwl = -0.5_dp*gm1*rhol*q2lwl
        plpl = gm1

      Hl = (enrgyl + pl)*rli
        Hlrl = (         plrl)*rli - (enrgyl+pl)*rli2
        Hlul = (         plul)*rli
        Hlvl = (         plvl)*rli
        Hlwl = (         plwl)*rli
        Hlpl = (1.0_dp + plpl)*rli

      ubarl = xnorm*ul + ynorm*vl + znorm*wl - face_speed

! Primitive variables on "right" side of face

      rhor = qr(1)
      rri  = 1.0_dp/rhor
      rri2 = rri*rri

      ur = qr(2)*rri
        urrr = -ur*rri
        urur = rri

      vr = qr(3)*rri
        vrrr = -vr/rhor
        vrvr = rri

      wr = qr(4)*rri
        wrrr = -wr/rhor
        wrwr = rri

      q2r = ur*ur + vr*vr + wr*wr
        q2rrr = 2.0_dp*ur*urrr + 2.0_dp*vr*vrrr + 2.0_dp*wr*wrrr
        q2rur = 2.0_dp*ur*urur
        q2rvr = 2.0_dp*vr*vrvr
        q2rwr = 2.0_dp*wr*wrwr

      enrgyr = qr(5)
!       enrgyrpr = 1.0_dp

      pr = gm1*(enrgyr - 0.5_dp*rhor*q2r)
        prrr = -0.5_dp*gm1*(rhor*q2rrr + q2r)
        prur = -0.5_dp*gm1*rhor*q2rur
        prvr = -0.5_dp*gm1*rhor*q2rvr
        prwr = -0.5_dp*gm1*rhor*q2rwr
        prpr = gm1

      Hr = (enrgyr + pr)/rhor
        Hrrr = (         prrr)*rri - (enrgyr+pr)*rri2
        Hrur = (         prur)*rri
        Hrvr = (         prvr)*rri
        Hrwr = (         prwr)*rri
        Hrpr = (1.0_dp + prpr)*rri

      ubarr  = xnorm*ur + ynorm*vr + znorm*wr - face_speed

! Compute Roe averages

      rho = sqrt(rhol*rhor)

      wat = rho/(rho + rhor)

      u = ul*wat + ur*(1.0_dp - wat)

      v = vl*wat + vr*(1.0_dp - wat)

      w = wl*wat + wr*(1.0_dp - wat)

      H = Hl*wat + Hr*(1.0_dp - wat)

      q2 = u*u + v*v + w*w

      c = sqrt(gm1*(H - 0.5_dp*q2))

      ubar = xnorm*u + ynorm*v + znorm*w - face_speed

! Now compute eigenvalues, eigenvectors, and strengths

      eig1 = ubar + c
      eig2 = ubar - c
      eig3 = ubar

! acoustic eigenvalue limiters

      maxeig = abs(ubar) + c

      eigeps1 = lhs_a_eigenvalue_coef*maxeig
      eigeps2 = lhs_a_eigenvalue_coef*maxeig

! convective eigenvalue limiter

      eigeps3 = lhs_u_eigenvalue_coef*maxeig

      abseig1 = abs( eig1 )
      abseig2 = abs( eig2 )
      abseig3 = abs( eig3 )

      if(abseig1 < eigeps1) abseig1 = 0.5_dp*(eig1**2/eigeps1 + eigeps1)
      if(abseig2 < eigeps2) abseig2 = 0.5_dp*(eig2**2/eigeps2 + eigeps2)
      if(abseig3 < eigeps3) abseig3 = 0.5_dp*(eig3**2/eigeps3 + eigeps3)

!     drho = rhor - rhol

!     dpress = pr - pl

!     du = ur - ul

!     dv = vr - vl

!     dw = wr - wl

!     dubar = ubarr - ubarl

      c2   = c*c
      rhoc = rho*c
      c2i  = 1.0_dp/c2

! jumps have units of density

!     dv1 = 0.5_dp*(dpress + rho*c*dubar)/c2

        dv1ul = + rhoc*xnorm
        dv1vl = + rhoc*ynorm
        dv1wl = + rhoc*znorm
        dv1pl = 1.0_dp

!     dv2 = 0.5_dp*(dpress - rho*c*dubar)/c2

        dv2ul = - rhoc*xnorm
        dv2vl = - rhoc*ynorm
        dv2wl = - rhoc*znorm
        dv2pl = 1.0_dp

!     dv3 = rho

!     dv4 = (c*c*drho - dpress)/c2

        dv4rl = c2
        dv4pl = - 1.0_dp

      r21 = u + c*xnorm

      r31 = v + c*ynorm

      r41 = w + c*znorm

      r51 = H + c*(ubar+face_speed)

      r22 = u - c*xnorm

      r32 = v - c*ynorm

      r42 = w - c*znorm

      r52 = H - c*(ubar+face_speed)

!     Jumps in velocity
!     r23 = du - dubar*xnorm
        r23ul = 1.0_dp - xnorm*xnorm
        r23vl =        - xnorm*ynorm
        r23wl =        - xnorm*znorm

!     r33 = dv - dubar*ynorm
        r33ul =        - ynorm*xnorm
        r33vl = 1.0_dp - ynorm*ynorm
        r33wl =        - ynorm*znorm

!     r43 = dw - dubar*znorm
        r43ul =        - znorm*xnorm
        r43vl =        - znorm*ynorm
        r43wl = 1.0_dp - znorm*znorm

!     r53 = u*du + v*dv + w*dw - (ubar+face_speed)*dubar
        r53ul = u - (ubar+face_speed)*xnorm
        r53vl = v - (ubar+face_speed)*ynorm
        r53wl = w - (ubar+face_speed)*znorm

      r24 = u

      r34 = v

      r44 = w

      r54 = 0.5_dp*q2

!     Scale abseig to reduce operations
!     dv1 = 0.5_dp*(dpress + rhoc*dubar)/c2
!     dv2 = 0.5_dp*(dpress - rhoc*dubar)/c2
!     dv3 = rho
!     dv4 = (c*c*drho - dpress)/c2

      abseig1 = 0.5_dp*abseig1*c2i
      abseig2 = 0.5_dp*abseig2*c2i
      abseig4 =        abseig3*c2i
      abseig3 =    rho*abseig3

!           t1 = abseig1*dv1     + abseig2*dv2                                 &
!                                + abseig3*dv4

        t1r = abseig4*dv4rl
        t1u = abseig1*dv1ul + abseig2*dv2ul
        t1v = abseig1*dv1vl + abseig2*dv2vl
        t1w = abseig1*dv1wl + abseig2*dv2wl
        t1p = abseig1*dv1pl + abseig2*dv2pl + abseig4*dv4pl

!           t2 = abseig1*r21*dv1 + abseig2*r22*dv2  &
!              + abseig3*r23*dv3 + abseig3*r24*dv4

        t2r = abseig4*r24*dv4rl

        t2u = abseig1*r21*dv1ul                &
            + abseig2*r22*dv2ul                &
            + abseig3*r23ul

        t2v = abseig1*r21*dv1vl                &
            + abseig2*r22*dv2vl                &
            + abseig3*r23vl

        t2w = abseig1*r21*dv1wl                &
            + abseig2*r22*dv2wl                &
            + abseig3*r23wl

        t2p = abseig1*r21*dv1pl                &
            + abseig2*r22*dv2pl                &
            + abseig4*r24*dv4pl

!           t3 = abseig1*r31*dv1 + abseig2*r32*dv2                             &
!              + abseig3*r33*dv3 + abseig3*r34*dv4

        t3r = abseig4*r34*dv4rl

        t3u = abseig1*r31*dv1ul                &
            + abseig2*r32*dv2ul                &
            + abseig3*r33ul

        t3v = abseig1*r31*dv1vl                &
            + abseig2*r32*dv2vl                &
            + abseig3*r33vl

        t3w = abseig1*r31*dv1wl                &
            + abseig2*r32*dv2wl                &
            + abseig3*r33wl

        t3p = abseig1*r31*dv1pl                &
            + abseig2*r32*dv2pl                &
            + abseig4*r34*dv4pl

!           t4 = abseig1*r41*dv1 + abseig2*r42*dv2                             &
!              + abseig3*r43*dv3 + abseig3*r44*dv4

        t4r = abseig4*r44*dv4rl

        t4u = abseig1*r41*dv1ul                &
            + abseig2*r42*dv2ul                &
            + abseig3*r43ul

        t4v = abseig1*r41*dv1vl                &
            + abseig2*r42*dv2vl                &
            + abseig3*r43vl

        t4w = abseig1*r41*dv1wl                &
            + abseig2*r42*dv2wl                &
            + abseig3*r43wl

        t4p = abseig1*r41*dv1pl                &
            + abseig2*r42*dv2pl                &
            + abseig4*r44*dv4pl

!           t5 = abseig1*r51*dv1 + abseig2*r52*dv2                             &
!              + abseig3*r53*dv3 + abseig3*r54*dv4

        t5r = abseig4*r54*dv4rl

        t5u = abseig1*r51*dv1ul                &
            + abseig2*r52*dv2ul                &
            + abseig3*r53ul

        t5v = abseig1*r51*dv1vl                &
            + abseig2*r52*dv2vl                &
            + abseig3*r53vl

        t5w = abseig1*r51*dv1wl                &
            + abseig2*r52*dv2wl                &
            + abseig3*r53wl

        t5p = abseig1*r51*dv1pl                &
            + abseig2*r52*dv2pl                &
            + abseig4*r54*dv4pl

! Compute flux using variables from left side of face

!           fluxp1 = area*rhol*ubarl
!           fluxp2 = area*(rhol*ul*ubarl + xnorm*pl)
!           fluxp3 = area*(rhol*vl*ubarl + ynorm*pl)
!           fluxp4 = area*(rhol*wl*ubarl + znorm*pl)
!           fluxp5 = area*(enrgyl + pl)*ubarl + face_speed*pl


        flux1 = rhol*ubarl

        flux1r = -face_speed
        flux1u = xnorm
        flux1v = ynorm
        flux1w = znorm

        flux2r = ul*flux1r + ulrl*flux1 + xnorm*plrl
        flux2u = ul*flux1u + ulul*flux1 + xnorm*plul
        flux2v = ul*flux1v              + xnorm*plvl
        flux2w = ul*flux1w              + xnorm*plwl
        flux2p =                        + xnorm*plpl

        flux3r = vl*flux1r + vlrl*flux1 + ynorm*plrl
        flux3u = vl*flux1u              + ynorm*plul
        flux3v = vl*flux1v + vlvl*flux1 + ynorm*plvl
        flux3w = vl*flux1w              + ynorm*plwl
        flux3p =                        + ynorm*plpl

        flux4r = wl*flux1r + wlrl*flux1 + znorm*plrl
        flux4u = wl*flux1u              + znorm*plul
        flux4v = wl*flux1v              + znorm*plvl
        flux4w = wl*flux1w + wlwl*flux1 + znorm*plwl
        flux4p =                        + znorm*plpl

        flux5r = Hl*flux1r + Hlrl*flux1 + face_speed*plrl
        flux5u = Hl*flux1u + Hlul*flux1 + face_speed*plul
        flux5v = Hl*flux1v + Hlvl*flux1 + face_speed*plvl
        flux5w = Hl*flux1w + Hlwl*flux1 + face_speed*plwl
        flux5p =           + Hlpl*flux1 + face_speed*plpl

      half_area = 0.5_dp*area

      ri = rli

      dfroe_aj(1,1,1) =  half_area*(flux1r +     t1r                       &
                             +  t1u*ulrl + t1v*vlrl + t1w*wlrl + t1p*plrl)
      dfroe_aj(1,2,1) =  half_area*(flux1u +  ri*t1u             + t1p*plul)
      dfroe_aj(1,3,1) =  half_area*(flux1v +  ri*t1v             + t1p*plvl)
      dfroe_aj(1,4,1) =  half_area*(flux1w +  ri*t1w             + t1p*plwl)
      dfroe_aj(1,5,1) =  half_area*(       + gm1*t1p)

      dfroe_aj(2,1,1) =  half_area*(flux2r +     t2r                       &
                              + t2u*ulrl + t2v*vlrl + t2w*wlrl + t2p*plrl)
      dfroe_aj(2,2,1) =  half_area*(flux2u +  ri*t2u             + t2p*plul)
      dfroe_aj(2,3,1) =  half_area*(flux2v +  ri*t2v             + t2p*plvl)
      dfroe_aj(2,4,1) =  half_area*(flux2w +  ri*t2w             + t2p*plwl)
      dfroe_aj(2,5,1) =  half_area*(flux2p + gm1*t2p)

      dfroe_aj(3,1,1) =  half_area*(flux3r +     t3r                       &
                              + t3u*ulrl + t3v*vlrl + t3w*wlrl + t3p*plrl)
      dfroe_aj(3,2,1) =  half_area*(flux3u +  ri*t3u             + t3p*plul)
      dfroe_aj(3,3,1) =  half_area*(flux3v +  ri*t3v             + t3p*plvl)
      dfroe_aj(3,4,1) =  half_area*(flux3w +  ri*t3w             + t3p*plwl)
      dfroe_aj(3,5,1) =  half_area*(flux3p + gm1*t3p)

      dfroe_aj(4,1,1) =  half_area*(flux4r +     t4r                       &
                              + t4u*ulrl + t4v*vlrl + t4w*wlrl + t4p*plrl)
      dfroe_aj(4,2,1) =  half_area*(flux4u +  ri*t4u             + t4p*plul)
      dfroe_aj(4,3,1) =  half_area*(flux4v +  ri*t4v             + t4p*plvl)
      dfroe_aj(4,4,1) =  half_area*(flux4w +  ri*t4w             + t4p*plwl)
      dfroe_aj(4,5,1) =  half_area*(flux4p + gm1*t4p)

      dfroe_aj(5,1,1) =  half_area*(flux5r +     t5r                       &
                              + t5u*ulrl + t5v*vlrl + t5w*wlrl + t5p*plrl)
      dfroe_aj(5,2,1) =  half_area*(flux5u +  ri*t5u             + t5p*plul)
      dfroe_aj(5,3,1) =  half_area*(flux5v +  ri*t5v             + t5p*plvl)
      dfroe_aj(5,4,1) =  half_area*(flux5w +  ri*t5w             + t5p*plwl)
      dfroe_aj(5,5,1) =  half_area*(flux5p + gm1*t5p)

! Now the right side

!           fluxm1 = area*rhor*ubarr
!           fluxm2 = area*(rhor*ur*ubarr + xnorm*pr)
!           fluxm3 = area*(rhor*vr*ubarr + ynorm*pr)
!           fluxm4 = area*(rhor*wr*ubarr + znorm*pr)
!           fluxm5 = area*(enrgyr + pr)*ubarr + face_speed)

        flux1 = rhor*ubarr

        flux1r = -face_speed
        flux1u = xnorm
        flux1v = ynorm
        flux1w = znorm

        flux2r = ur*flux1r + urrr*flux1 + xnorm*prrr
        flux2u = ur*flux1u + urur*flux1 + xnorm*prur
        flux2v = ur*flux1v              + xnorm*prvr
        flux2w = ur*flux1w              + xnorm*prwr
        flux2p =                        + xnorm*prpr

        flux3r = vr*flux1r + vrrr*flux1 + ynorm*prrr
        flux3u = vr*flux1u              + ynorm*prur
        flux3v = vr*flux1v + vrvr*flux1 + ynorm*prvr
        flux3w = vr*flux1w              + ynorm*prwr
        flux3p =                        + ynorm*prpr

        flux4r = wr*flux1r + wrrr*flux1 + znorm*prrr
        flux4u = wr*flux1u              + znorm*prur
        flux4v = wr*flux1v              + znorm*prvr
        flux4w = wr*flux1w + wrwr*flux1 + znorm*prwr
        flux4p =                        + znorm*prpr

        flux5r = Hr*flux1r + Hrrr*flux1 + face_speed*prrr
        flux5u = Hr*flux1u + Hrur*flux1 + face_speed*prur
        flux5v = Hr*flux1v + Hrvr*flux1 + face_speed*prvr
        flux5w = Hr*flux1w + Hrwr*flux1 + face_speed*prwr
        flux5p =           + Hrpr*flux1 + face_speed*prpr

!         flux1 = 0.5_dp*(fluxp1 + fluxm1 - t1)
!         flux2 = 0.5_dp*(fluxp2 + fluxm2 - t2)
!         flux3 = 0.5_dp*(fluxp3 + fluxm3 - t3)
!         flux4 = 0.5_dp*(fluxp4 + fluxm4 - t4)
!         flux5 = 0.5_dp*(fluxp5 + fluxm5 - t5)

      ri = rri

      dfroe_aj(1,1,2) =  half_area*(flux1r -     t1r                       &
                              - t1u*urrr - t1v*vrrr - t1w*wrrr - t1p*prrr)
      dfroe_aj(1,2,2) =  half_area*(flux1u -  ri*t1u             - t1p*prur)
      dfroe_aj(1,3,2) =  half_area*(flux1v -  ri*t1v             - t1p*prvr)
      dfroe_aj(1,4,2) =  half_area*(flux1w -  ri*t1w             - t1p*prwr)
      dfroe_aj(1,5,2) =  half_area*(       - gm1*t1p)

      dfroe_aj(2,1,2) =  half_area*(flux2r -    t2r                        &
                              - t2u*urrr - t2v*vrrr - t2w*wrrr - t2p*prrr)
      dfroe_aj(2,2,2) =  half_area*(flux2u -  ri*t2u             - t2p*prur)
      dfroe_aj(2,3,2) =  half_area*(flux2v -  ri*t2v             - t2p*prvr)
      dfroe_aj(2,4,2) =  half_area*(flux2w -  ri*t2w             - t2p*prwr)
      dfroe_aj(2,5,2) =  half_area*(flux2p - gm1*t2p)

      dfroe_aj(3,1,2) =  half_area*(flux3r -    t3r                        &
                              - t3u*urrr - t3v*vrrr - t3w*wrrr - t3p*prrr)
      dfroe_aj(3,2,2) =  half_area*(flux3u -  ri*t3u             - t3p*prur)
      dfroe_aj(3,3,2) =  half_area*(flux3v -  ri*t3v             - t3p*prvr)
      dfroe_aj(3,4,2) =  half_area*(flux3w -  ri*t3w             - t3p*prwr)
      dfroe_aj(3,5,2) =  half_area*(flux3p - gm1*t3p)

      dfroe_aj(4,1,2) =  half_area*(flux4r -     t4r                       &
                              - t4u*urrr - t4v*vrrr - t4w*wrrr - t4p*prrr)
      dfroe_aj(4,2,2) =  half_area*(flux4u -  ri*t4u             - t4p*prur)
      dfroe_aj(4,3,2) =  half_area*(flux4v -  ri*t4v             - t4p*prvr)
      dfroe_aj(4,4,2) =  half_area*(flux4w -  ri*t4w             - t4p*prwr)
      dfroe_aj(4,5,2) =  half_area*(flux4p - gm1*t4p)

      dfroe_aj(5,1,2) =  half_area*(flux5r -     t5r                       &
                              - t5u*urrr - t5v*vrrr - t5w*wrrr - t5p*prrr)
      dfroe_aj(5,2,2) =  half_area*(flux5u -  ri*t5u             - t5p*prur)
      dfroe_aj(5,3,2) =  half_area*(flux5v -  ri*t5v             - t5p*prvr)
      dfroe_aj(5,4,2) =  half_area*(flux5w -  ri*t5w             - t5p*prwr)
      dfroe_aj(5,5,2) =  half_area*(flux5p - gm1*t5p)

  end function dfroe_aj

noinst_LIBRARIES = libdesign.a

LIBCORE_DIR=@libcore_path@
LIBTURB_DIR=@libturb_path@
LIBSMEMRD_DIR=@top_builddir@/libsmemrd
FUNCLIB_DIR=@top_builddir@/FuncLib90
LIBF90_DIR=@top_builddir@/LibF90
LIBDEFS_DIR=@top_builddir@/libdefs
LIBINIT_DIR=@top_builddir@/libinit
PHYSICS_DIR=@top_builddir@/@PHYSICS_TYPE@
PHYSICS_DEPS_DIR=@top_builddir@/PHYSICS_DEPS

AM_FCFLAGS = \
	$(FC_MODINC)$(LIBCORE_DIR) \
	$(FC_MODINC)$(LIBDEFS_DIR) \
	$(FC_MODINC)$(LIBTURB_DIR) \
	$(FC_MODINC)$(srcdir)/../libturb \
	$(FC_MODINC)$(LIBSMEMRD_DIR) \
	$(FC_MODINC)$(LIBF90_DIR) \
	$(FC_MODINC)$(LIBINIT_DIR) \
	$(FC_MODINC)$(PHYSICS_DIR) \
	$(FC_MODINC)$(srcdir)/$(FUNCLIB_DIR) \
	$(FC_MODINC)$(PHYSICS_DEPS_DIR) \
	$(FC_MODINC)@top_builddir@

libdesign_a_LIBADD =
libdesign_a_LINK = $(F90LINK)
design_SOURCES = \
	dalpha.f90 \
	djet.f90 \
	dmach.f90 \
	dshape.f90 \
	dshape_boundary.f90 \
	dshape_boundary_ni.f90 \
	dshape_cost.f90 \
	dshape_inviscid.f90 \
	dshape_laminar.f90 \
	dshape_residual.f90 \
	dshape_turbulent.f90 \
	dshape_types.f90 \
	timedep_sensitivities.f90

# remove *.mod *.interface when mod_suffix is repaired for OS X
CLEANFILES = *.$(FC_MODEXT)  mpif.h *.time *.mod *.interface *.d *.fh

design_f90s=$(design_SOURCES:.F90=.f90)
design_deps=$(design_f90s:.f90=.d)

lib_MODULES=$(design_f90s:.f90=.$(FC_MODEXT))

DISTCLEANFILES = $(design_deps)

SUFFIXES = .d

%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-I $(PHYSICS_DEPS_DIR) \
	-L $(top_srcdir)/FuncLib90 \
	-L $(LIBTURB_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-I $(PHYSICS_DEPS_DIR) \
	-L $(top_srcdir)/FuncLib90 \
	-L $(LIBTURB_DIR) > $@

BUILT_SOURCES = $(design_deps)

-include $(design_deps)
include $(top_srcdir)/make.rules

CORE_LIBS = libcore.a
DEFS_LIBS = libdefs.a
TURB_LIBS = libturb.a
INIT_LIBS = libinit.a
FUN3D_F90_LIBS = libsink.a

libdesign_a_SOURCES = $(design_SOURCES)

EXTRA_DIST =

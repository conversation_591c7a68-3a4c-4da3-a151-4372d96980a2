module radp

  implicit none

  private

  public :: embed_rad
  public :: single_rad
  public :: inria_rad
  public :: inria_rad_time_adapt
  public :: adjoint_adaptation
  public :: rad_error_est_to_indicator

contains

!================================ RAD_MAIN ===================================80
!
! Residual Adjoint Dot-product for embedded grids
!
!=============================================================================80

  subroutine embed_rad(origgrid, origsoln, origsadj, design, raw_grid_data,    &
    nml_path)

    use refine_adaptation_input, only : adapt_verbose

    use kinddefs,             only : dp
    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use system_extensions,    only : se_flush, se_open
    use lmpi,                 only : lmpi_master, lmpi_die,                    &
                                     lmpi_conditional_stop
    use lmpi_app,             only : lmpi_xfer, lmpi_sumnode
    use comprow_types,        only : crow_type, crow_flow,                     &
      deallocate_crow_type, deallocate_crow_flow
    use comprow,              only : set_up_comprow, set_up_comprow_flow

    use info_depr,            only : ivisc, ncyc

    use nml_noninertial_reference_frame, only : noninertial

    use solution_types,       only : soln_type, compressible, incompressible,  &
                                     generic_gas
    use solution,             only : nullify_soln, set_global_scalars,         &
                                     deallocate_soln
    use bc_types,             only : allocate_bcsoln
    use solution_adj,         only : sadj_type, nullify_sadj, deallocate_sadj

    use rad_defs,             only : rads_type, rads_restrict_adapt,           &
                                     deallocate_rads

    use grid_types,           only : grid_type, raw_grid_data_type
    use grids,                only : nullify_grid, deallocate_grid
    use grid_helper,          only : create_test_g2l, create_part,             &
                                     grid_reset_lmpi_xfer

    use design_types,         only : design_type
    use designs,              only : gettag

    use bc_names,             only : bc_allowed_for_adjoint

    use inviscid_flux,        only : iflim, freeze_limiter_iteration

    use flow_initialization,  only : init, initi, initialize_backplanes
    use reconstruction,       only : sumgs
    use twod_util,            only : setup_2d
    use adjoint_switches,     only : always_recompute
    use parallel_embed,       only : get_embed_grid
    use noninertials,         only : setup_nonin
    use force_helper,         only : remove_bcforce_from_total
    use force_types,          only : nullify_force
    use adaptation_parameter, only : get_tensor_adapt

    use file_utils,           only : available_unit
    use gradient_driver,      only : update_grads_and_lim
    use cut_types,            only : cut_cell_activated
    use nml_nonlinear_solves, only : itime

    type(grid_type),                            intent(inout) :: origgrid
    type(soln_type),                            intent(inout) :: origsoln
    type(sadj_type),                            intent(inout) :: origsadj
    type(design_type),                          intent(inout) :: design
    type(raw_grid_data_type),                   intent(inout) :: raw_grid_data

    type(grid_type)   :: grid        ! Grid info
    type(soln_type)   :: soln        ! Solution info
    type(sadj_type)   :: sadj        ! Adj Solution
    type(crow_type)   :: crow            ! compressed row
    type(crow_flow)   :: crowf
    type(rads_type)   :: rads, origrads  ! adapt-err data

    character(len=*), intent(in) :: nml_path

    integer  :: ib, ibc

    real(dp), dimension(6,origgrid%nnodes01) :: anisotropic_metric

    integer :: golden_file = -1

    continue

    if (cut_cell_activated) &
      call lmpi_conditional_stop(1,'embed_rad not available for cut_cell')

! evaluate the gradients and limiter for inviscid reconstruction

    call update_grads_and_lim(origgrid,origsoln)

    call nullify_grid(grid)
    call nullify_soln(soln)
    call nullify_sadj(sadj)

    if ( lmpi_master )  write(*,*) 'always_recompute forced .true. in RAD main'
    always_recompute = .true.

! set soln dimensioning parameters (ndim, adim, etc.) for both solution types

    call set_global_scalars(soln)

! hessian_before_reconstruction as in single grid path
    call get_tensor_adapt(anisotropic_metric, origgrid,                        &
                          origsoln%eqn_set, origsoln%n_tot, origsoln%q_dof )

    grid%project = origgrid%project

    call get_embed_grid( origgrid, grid, origsoln, soln,                       &
                         origsadj, sadj, rads, design, adapt_verbose )
    if ( noninertial ) call setup_nonin(grid)
    if ( lmpi_master ) write(*,*) 'Allocating r11-r33'
    if ( lmpi_master ) call se_flush()
    call my_alloc_ptr(grid%r11,      grid%nnodes01)
    call my_alloc_ptr(grid%r12,      grid%nnodes01)
    call my_alloc_ptr(grid%r13,      grid%nnodes01)
    call my_alloc_ptr(grid%r22,      grid%nnodes01)
    call my_alloc_ptr(grid%r23,      grid%nnodes01)
    call my_alloc_ptr(grid%r33,      grid%nnodes01)

    if ( lmpi_master .and. freeze_limiter_iteration >= 0 )                     &
      write(*,*) 'freeze_limiter_iteration forced -1 in RAD main after rrest', &
      ' because it is not implemented yet'
    freeze_limiter_iteration = -1

    if ( lmpi_master .and. 0 /= iflim )                        &
      write(*,*) 'limiter force to zero (0) in RAD main',      &
      ' because it can seg fault on large grids'
    iflim = 0

    check_boundary_types : do ib = 1,grid%nbound
      ibc = grid%bc(ib)%ibc
      if ( .not. bc_allowed_for_adjoint( soln%eqn_set, ibc ) ) then
        write(*,*)"ERROR: Adjoint can not handle bc type ", ibc
        call lmpi_die
      endif
    end do check_boundary_types

    ncyc = 1

    if ( lmpi_master ) write(*,*) 'Allocating qnode' ; call se_flush()
    call my_realloc_ptr( soln%q_dof, soln%ndim,  grid%nnodes01)
    if ( lmpi_master ) write(*,*) 'Allocating rlam' ; call se_flush()
    call my_alloc_ptr( sadj%coltag, soln%adim, grid%nnodes01)
    call my_realloc_ptr( sadj%rlam, soln%adim, grid%nnodes01, design%nfunctions)
    if ( lmpi_master ) write(*,*) 'Allocating grads' ; call se_flush()
    call my_alloc_ptr( soln%gradx, soln%ndim, grid%nnodes01 )
    call my_alloc_ptr( soln%grady, soln%ndim, grid%nnodes01 )
    call my_alloc_ptr( soln%gradz, soln%ndim, grid%nnodes01 )
    if ( lmpi_master ) write(*,*)                                              &
      'Allocating turbulent variables' ; call se_flush()
    call my_realloc_ptr( soln%turb, max(soln%n_turb,1), grid%nnodes01 )
    call my_alloc_ptr( soln%turbatn, 1, 1 )
    call my_alloc_ptr( soln%turbatn1, 1, 1 )
    call my_alloc_ptr( soln%amut, grid%nnodes01 )
    call my_alloc_ptr( soln%dft1, grid%nedge )
    call my_alloc_ptr( soln%dft2, grid%nedge )
    if ( lmpi_master ) write(*,*)                                              &
      'Allocating residual variables' ; call se_flush()
    call my_alloc_ptr( soln%phi, soln%n_grd, grid%nnodes01 )
    call my_alloc_ptr( sadj%res, soln%adim, grid%nnodes01, design%nfunctions )
    call my_alloc_ptr( soln%cdt, grid%nnodes0 )
    call my_alloc_ptr( soln%dq,       1, 1)

    call my_alloc_ptr( soln%turbres, 1, 1 )

    allocate(soln%bcsoln(grid%nbound))
    do ib = 1,grid%nbound
      call allocate_bcsoln(grid%bc(ib)%nbnode,soln%bcsoln(ib))
    end do
    call my_alloc_ptr(soln%enthalpy_ij,  1,          1,        1)
    call my_alloc_ptr(soln%pressure_jac, 1,                    1,    1)

    call my_alloc_ptr(soln%forcing, 1, 1)

    allocate( soln%bcforce(grid%nbound) )
    call remove_bcforce_from_total(grid%nbound,grid%bc,soln%bcforce,'../Flow/')
    do ib = 1,grid%nbound
      call nullify_force( soln%bcforce(ib) )
      call my_alloc_ptr( soln%bcforce(ib)%cp_t,  max(1,grid%bc(ib)%nbfacet) )
      call my_alloc_ptr( soln%bcforce(ib)%cq_t,  max(1,grid%bc(ib)%nbfacet) )
      call my_alloc_ptr( soln%bcforce(ib)%cfx_t, max(1,grid%bc(ib)%nbfacet) )
      call my_alloc_ptr( soln%bcforce(ib)%cfy_t, max(1,grid%bc(ib)%nbfacet) )
      call my_alloc_ptr( soln%bcforce(ib)%cfz_t, max(1,grid%bc(ib)%nbfacet) )
      call my_alloc_ptr( soln%bcforce(ib)%cp_q,  max(1,grid%bc(ib)%nbfaceq) )
      call my_alloc_ptr( soln%bcforce(ib)%cq_q,  max(1,grid%bc(ib)%nbfaceq) )
      call my_alloc_ptr( soln%bcforce(ib)%cfx_q, max(1,grid%bc(ib)%nbfaceq) )
      call my_alloc_ptr( soln%bcforce(ib)%cfy_q, max(1,grid%bc(ib)%nbfaceq) )
      call my_alloc_ptr( soln%bcforce(ib)%cfz_q, max(1,grid%bc(ib)%nbfaceq) )
    end do
    allocate( soln%totforce(1) )
    call nullify_force( soln%totforce(1) )
    allocate( soln%eqn_groups(1) )

    call my_alloc_ptr( soln%rmshist,  soln%ndim+soln%n_turb, ncyc, 1 )
    call my_alloc_ptr( soln%rmaxhist, soln%ndim+soln%n_turb, ncyc, 1 )
    call my_alloc_ptr( soln%xlochist, soln%ndim+soln%n_turb, ncyc, 1 )
    call my_alloc_ptr( soln%ylochist, soln%ndim+soln%n_turb, ncyc, 1 )
    call my_alloc_ptr( soln%zlochist, soln%ndim+soln%n_turb, ncyc, 1 )
    call my_alloc_ptr( soln%walltime, ncyc )

    call my_alloc_ptr( soln%res, soln%njac, grid%nnodes01 )
    call my_alloc_ptr( soln%flux_efixc, grid%nedgeloc )
    if ( ivisc >= 6 ) then
      deallocate( soln%turbres )
      if ( lmpi_master ) write(*,*) 'Allocating turbres'
      if ( lmpi_master ) call se_flush()
      call my_alloc_ptr( soln%turbres, soln%n_turb, grid%nnodes01 )
    end if

    call set_up_comprow( soln%viscous_method, grid, crow )

    call my_alloc_ptr( sadj%AA,  1, 1, 1 )

    call sumgs( grid%nnodes0, grid%nnodes01, grid%nedgeloc,                    &
                grid%eptr,    grid%symmetry,                                   &
                grid%x,       grid%y,        grid%z,                           &
                grid%r11,     grid%r12,      grid%r13,                         &
                grid%r22,     grid%r23,      grid%r33)

    call lmpi_xfer(grid%r11)
    call lmpi_xfer(grid%r12)
    call lmpi_xfer(grid%r13)
    call lmpi_xfer(grid%r22)
    call lmpi_xfer(grid%r23)
    call lmpi_xfer(grid%r33)

    call gettag( grid, soln%adim, sadj%coltag)

    select case( soln%eqn_set )
    case (compressible, generic_gas)
      call init( grid%cc, soln%eqn_set, grid%nnodes01,grid%nnodes01,soln%q_dof,&
                 soln%n_turb, soln%turb, soln%amut,                            &
                 grid%slen, grid%x, grid%y, grid%z, grid%vol, soln%n_tot,      &
                 soln%njac, soln%ndim, soln%n_q, soln%n_grd, grid%nnodes0,     &
                 nml_path, soln%pressure_jac, soln%enthalpy_ij,                &
                 leave_soln_alone = .true. )
    case (incompressible)
      call initi( grid%cc, soln%eqn_set, grid%nnodes01, grid%nnodes01,         &
                  soln%q_dof, soln%n_turb, soln%turb,                          &
                  soln%amut, grid%slen, grid%x, grid%y, grid%z, grid%vol,      &
                  soln%n_tot, soln%njac, soln%ndim, soln%n_q,                  &
                  leave_soln_alone = .true.)
    case default
      call lmpi_conditional_stop(1,'rad_main: only for in/compress perfect gas')
    end select

    if ( itime /= 0 ) then
      call initialize_backplanes(soln%n_tot,soln%n_turb,size(soln%q_dof,2),    &
                                 soln%q_dof,soln%qatn,soln%qatn1,soln%qatn2,   &
                                 soln%qatn3,soln%qatn4,soln%turb,soln%turbatn, &
                                 soln%turbatn1,soln%turbatn2,soln%turbatn3,    &
                                 soln%turbatn4,grid%x,grid%z,soln%eqn_set)
    endif

! set up compressed row storage for flow...needed to set 2D

    call set_up_comprow_flow( soln%viscous_method, grid, crowf )

! set up 2D data structures (dummy structures if 3D)

    call setup_2d( grid )

    if ( lmpi_master ) then
      golden_file = available_unit()
      call se_open( golden_file, file=trim(origgrid%project)//'_rad.golden' )
      write(*,'(a)') " -- Embedded grid error correction"
      write(golden_file,'(a)') "# Embedded grid error correction"
      call se_flush()
    end if

    call rad_error_est_to_indicator( grid, soln, sadj, rads,                   &
                                     crow, crowf, design, golden_file )

    call deallocate_crow_flow( crowf )
    call deallocate_crow_type( crow )
    call deallocate_grid( grid )
    call deallocate_soln( soln )
    call deallocate_sadj( sadj )
    call rads_restrict_adapt( rads, origgrid, origrads )
    call deallocate_rads( rads )
    call create_test_g2l( origgrid )

    if ( associated(origgrid%part) ) deallocate(origgrid%part)
    allocate(origgrid%part(origgrid%nnodes01))
    call create_part(origgrid%nnodes0, origgrid%nnodes01, origgrid%l2g, &
      origgrid%part)
    call grid_reset_lmpi_xfer( origgrid )
    call lmpi_sumnode( origrads%adapt_param, 1 )

    call adjoint_adaptation(anisotropic_metric, origgrid, origsoln, origsadj,  &
                            origrads, design, raw_grid_data, golden_file)

    if (lmpi_master) then
      close(golden_file)
      write(*,*) 'rad done.'
      call se_flush()
    end if

  end subroutine embed_rad

!================================ single_rad =================================80
!
! Residual Adjoint Dot-product for single grid error estimates and adaptation.
! Has checks for cut cell cases.
!
!=============================================================================80

  subroutine single_rad( grid, soln, sadj, crow, crowf, design, raw_grid_data )

    use kinddefs,             only : dp
    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use system_extensions,    only : se_flush, se_open
    use lmpi,                 only : lmpi_master
    use comprow_types,        only : crow_type, crow_flow

    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type
    use rad_defs,             only : rads_type

    use grid_types,           only : grid_type, raw_grid_data_type

    use adaptation_parameter, only : get_tensor_adapt
    use parallel_embed,       only : clean_lambda_parallel
    use cut_types,            only : cut_cell_activated

    use file_utils,           only : available_unit
    use gradient_driver,      only : update_grads_and_lim

    type(grid_type),          intent(inout) :: grid
    type(soln_type),          intent(inout) :: soln
    type(sadj_type),          intent(inout) :: sadj
    type(crow_type),          intent(in)    :: crow
    type(crow_flow),          intent(in)    :: crowf
    type(design_type),        intent(inout) :: design
    type(raw_grid_data_type), intent(inout) :: raw_grid_data

    type(rads_type)    :: rads

    integer            :: func
    integer            :: golden_file = -1

    real(dp),   dimension(6, grid%nnodes01) :: anisotropic_metric

  continue

! evaluate the gradients and limiter for inviscid reconstruction

    call update_grads_and_lim(grid,soln)

    !! This must be done before the reconstructions since
    !! the original solution is needed
    call get_tensor_adapt( anisotropic_metric, grid,                           &
                           soln%eqn_set, soln%n_tot, soln%q_dof )

    !! perform quadratic and constant reconstructions
    !! store quadratic in place of originals
    !! store constant in  rads%*_linear
    if ( lmpi_master )  write(*,*) 'rad performing reconstructions'
    call my_alloc_ptr(rads%prim_linear, soln%n_tot,   grid%nnodes0)
    call single_grid_reconstruction(                                           &
      soln%n_tot, grid%nnodes0, grid%nnodes01,                                 &
      soln%q_dof, rads%prim_linear,                                            &
      grid%x,grid%y,grid%z,                                                    &
      grid%r11,grid%r12,grid%r13,grid%r22,grid%r23,grid%r33,                   &
      grid%nedgeloc, grid%eptr)

    reconstruct_turb : if (soln%n_turb > 0 ) then
      call my_alloc_ptr(rads%turb_linear, soln%n_turb, grid%nnodes0)
      call single_grid_reconstruction(                                         &
        soln%n_turb, grid%nnodes0, grid%nnodes01,                              &
        soln%turb, rads%turb_linear,                                           &
        grid%x,grid%y,grid%z,                                                  &
        grid%r11,grid%r12,grid%r13,grid%r22,grid%r23,grid%r33,                 &
        grid%nedgeloc, grid%eptr)
    else
      call my_alloc_ptr(rads%turb_linear, 1, 1)
    end if reconstruct_turb

    if ( .not. cut_cell_activated ) then
      do func = 1, design%nfunctions
        call clean_lambda_parallel(soln%viscous_method, grid,                  &
          sadj%rlam(:,:,func), soln%adim, soln%turb, soln%n_turb)
      end do
    end if

    call my_alloc_ptr(rads%dual_linear, soln%adim,    grid%nnodes0)
    call single_grid_reconstruction(                                           &
      soln%adim, grid%nnodes0, grid%nnodes01,                                  &
      sadj%rlam, rads%dual_linear,                                             &
      grid%x,grid%y,grid%z,                                                    &
      grid%r11,grid%r12,grid%r13,grid%r22,grid%r23,grid%r33,                   &
      grid%nedgeloc, grid%eptr)

    if (size(soln%res,1) /= soln%n_tot .or. size(soln%res,2) /= grid%nnodes01)&
      call my_realloc_ptr(soln%res,soln%n_tot,grid%nnodes01)

    if ( lmpi_master ) then
      golden_file = available_unit()
      call se_open( golden_file, file=trim(grid%project)//'_rad.golden' )
      write(*,'(a)') " -- Single grid error correction"
      write(golden_file,'(a)') "# Single grid error correction"
      call se_flush()
    end if

    call rad_error_est_to_indicator( grid, soln, sadj, rads,                   &
                                     crow, crowf, design, golden_file )

    call adjoint_adaptation(anisotropic_metric, grid, soln, sadj, rads, design,&
                             raw_grid_data, golden_file)

    if (lmpi_master) then
      close(golden_file)
      write(*,*) 'rad done.'
      call se_flush()
    end if

  end subroutine single_rad

!========================== rad_error_est_to_indicator =======================80
!
! Computes the adjoint error estimation and creates adaptation scaling factor
!
!=============================================================================80
  subroutine rad_error_est_to_indicator( grid, soln, sadj, rads,               &
                                         crow, crowf, design, golden_file )

    use grid_types,        only : grid_type
    use solution_types,    only : soln_type
    use solution_adj,      only : sadj_type
    use rad_defs,          only : rads_type
    use design_types,      only : design_type
    use comprow_types,     only : crow_type, crow_flow

    use info_depr,         only : ntt
    use lmpi,              only : lmpi_master, lmpi_die
    use allocations,       only : my_alloc_ptr
    use system_extensions, only : se_flush

    use refine_adaptation_input, only : adapt_debug

    type(grid_type),   intent(inout) :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(rads_type),   intent(inout) :: rads
    type(crow_type),   intent(in)    :: crow
    type(crow_flow),   intent(in)    :: crowf
    type(design_type), intent(inout) :: design
    integer,           intent(in)    :: golden_file

    continue

    ntt  = 1

    rads%noutput = design%nfunctions
    if ( design%nfunctions > 1) then
      if ( lmpi_master ) &
        write(*,*)"FIXME: single grid cannot handle multiple design functions"
      call lmpi_die
    end if

    if ( lmpi_master ) write(*,*) 'Allocating adaptation parameter'
    if ( lmpi_master ) call se_flush()
    call my_alloc_ptr( rads%adapt_param, rads%noutput, grid%nnodes0 )

    if ( lmpi_master )  write(*,*)'rad recover linear'
    call rad_switch_solution( grid, soln, sadj, rads )
    call recover_boundary_adjoint( grid, soln, sadj, crow, crowf,              &
                                   design, golden_file )

    if ( lmpi_master )  write(*,*)'rad evaluate primal residual'
    call residual_flow( grid, soln, crowf, design )
    call rad_error_est( grid, soln, sadj, design, golden_file )

    if ( lmpi_master )  write(*,*)'rad recover high order'
    call rad_switch_solution( grid, soln, sadj, rads )
    call recover_boundary_adjoint( grid, soln, sadj, crow, crowf,              &
                                   design, golden_file )

    if ( lmpi_master )  write(*,*)'rad evaluate primal residual'
    call residual_flow( grid, soln, crowf, design )
    call rad_error_est( grid, soln, sadj, design, golden_file )

    if ( lmpi_master )  write(*,*)'rad evaluate adjoint residual'
    call reset_grad_get_adj_resid( grid, soln, sadj, crow,                     &
                                   crowf, design, golden_file )

    call rad_adapt_indicator(grid, rads, soln, sadj, golden_file)

    if ( adapt_debug ) call rad_adapt_debug(grid, rads, soln, sadj)

  end subroutine rad_error_est_to_indicator

!============================ adjoint_adaptation =============================80
!
! Combines the adjoint adaptive indicator with the Hessian and adapts the grid
!
!=============================================================================80
  subroutine adjoint_adaptation(anisotropic_metric, grid, soln, sadj, rads,   &
                                design, raw_grid_data, golden_file)

    use kinddefs,          only : dp
    use grid_types,        only : grid_type, raw_grid_data_type
    use solution_types,    only : soln_type
    use solution_adj,      only : sadj_type
    use rad_defs,          only : rads_type
    use design_types,      only : design_type

    use rad_defs,          only : rads_grid_adapt_param
    use info_depr,         only : adapt
    use cut_types,         only : cut_cell_activated
    use lmpi,              only : lmpi_master
    use lmpi_app,          only : lmpi_xfer

    use grids,             only : nullify_grid
    use adaptation_parameter, only : create_adapt_metric, metric_visualize,    &
                                     metric_finalization
    use refine_adaptation_input, only : adapt_visualize_metric,                &
                                        adapt_node_dist_file, adapt_library
    use global_image,      only : global_image_read_raw_ascii

    type(grid_type),   intent(inout) :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(rads_type),   intent(inout) :: rads
    type(design_type), intent(inout) :: design
    type(raw_grid_data_type), intent(inout) :: raw_grid_data
    integer,           intent(in)    :: golden_file
    real(dp), dimension(6, grid%nnodes01), intent(inout) :: anisotropic_metric

    type(grid_type) :: box_grid
    real(dp), dimension(grid%nnodes01)       :: scalar_metric
    real(dp), dimension(:,:), pointer        :: box_anisotropic_metric
    real(dp), dimension(:,:), allocatable    :: node_dist

    integer :: func

    continue

    adapt_using_node_dist : if ( adapt_library == 'refine/two' .and.           &
                                 len_trim(adapt_node_dist_file) > 0 ) then
      allocate(node_dist(1,grid%nnodes0))
      call global_image_read_raw_ascii(adapt_node_dist_file, grid, node_dist)
      call rads_grid_adapt_param( grid, node_dist,                    &
        scalar_metric, golden_file)
      call lmpi_xfer( scalar_metric )
      deallocate(node_dist)
    else
      adapt_func : do func = 1, design%nfunctions
        call rads_grid_adapt_param( grid, rads%adapt_param(func,:),            &
          scalar_metric, golden_file)
        call lmpi_xfer( scalar_metric )
      end do adapt_func
    end if adapt_using_node_dist

    call create_adapt_metric( grid, anisotropic_metric, scalar_metric )
    call metric_finalization( grid, anisotropic_metric )

    adapting : if ( adapt ) then
      if (lmpi_master) write(*,*) 'rad adaptation time!'

      cut_cell_check : if (cut_cell_activated) then
        call nullify_grid(box_grid)
        box_grid%project = grid%project
        call create_cut_cell_metric( grid%nnodes0, raw_grid_data, soln,        &
          anisotropic_metric, box_grid, box_anisotropic_metric)
        cut_dump_metric : if ( 'none' /= trim(adapt_visualize_metric) ) then
          call metric_visualize(box_grid,box_anisotropic_metric,&
            adapt_visualize_metric)
        end if cut_dump_metric
       call rad_adapt( box_grid, box_anisotropic_metric )
        deallocate(box_anisotropic_metric)
      else
        body_dump_metric : if ( 'none' /= trim(adapt_visualize_metric) ) then
          call metric_visualize(grid,anisotropic_metric,&
            adapt_visualize_metric)
        end if body_dump_metric
        call rad_adapt( grid, anisotropic_metric, scalar_metric,&
          soln, sadj, design )
      end if cut_cell_check

    end if adapting

  end subroutine adjoint_adaptation

!======================== RAD_SWITCH_SOLUTION ================================80
!
! Switch where the original solutions and interpolated solutions are stored
!
!=============================================================================80

  subroutine rad_switch_solution( grid, soln, sadj, rads )

    use kinddefs,       only : dp
    use info_depr,      only : ivisc
    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use solution_adj,   only : sadj_type
    use lmpi_app,       only : lmpi_xfer
    use rad_defs,       only : rads_type
    use amut,           only : compute_amut

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(sadj_type), intent(inout) :: sadj
    type(rads_type), intent(inout) :: rads

    integer :: i, j, func

    real(dp) :: temp

    continue

    do j = 1, grid%nnodes0
      do i = 1, soln%ndim
        temp = soln%q_dof(i,j)
        soln%q_dof(i,j) = rads%prim_linear(i,j)
        rads%prim_linear(i,j) = temp
      end do
    end do

    call lmpi_xfer( soln%q_dof )

    do j = 1, grid%nnodes0
      do i = 1, soln%n_turb
        temp = soln%turb(i,j)
        soln%turb(i,j) = rads%turb_linear(i,j)
        rads%turb_linear(i,j) = temp
      end do
    end do

    if ( soln%n_turb > 0 ) call lmpi_xfer( soln%turb )

    call validate_soln( grid%nnodes0, soln )

    mod_amut : if ( ivisc >= 6 ) then
      call compute_amut(soln%eqn_set, grid%nnodes0, grid%nnodes01,             &
        soln%q_dof, soln%turb, soln%amut, grid%iflagslen, grid%slen,           &
        grid%vol, soln%gradx,                                                  &
        soln%grady, soln%gradz,soln%n_turb, soln%n_tot, soln%n_grd,            &
        grid%nbound, grid%bc, soln%bcsoln )
      call lmpi_xfer( soln%amut )
    end if mod_amut

    do func = 1, rads%noutput
      do j = 1, grid%nnodes0
        do i = 1, soln%adim
          temp = sadj%rlam(i,j,func)
          sadj%rlam(i,j,func) = rads%dual_linear(i,j)
          rads%dual_linear(i,j) = temp
        end do
      end do
      call lmpi_xfer( sadj%rlam(:,:,func) )
    end do
  end subroutine rad_switch_solution

!============================== residual_flow ================================80
!
! Calculate flow and adjoint residuals for radp and cut_rad
!
!=============================================================================80

  subroutine residual_flow( grid, soln, crowf, design )

    use refine_adaptation_input, only : adapt_verbose

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type, compressible, incompressible
    use comprow_types,        only : crow_flow
    use design_types,         only : design_type

    use info_depr,            only : ivisc, ntt
    use force_driver,         only : compute_forces
    use lmpi,                 only : lmpi_master, lmpi_conditional_stop
    use lmpi_app,             only : lmpi_collect_res

    use flux_util,            only : l2norm
    use flux,                 only : residual_compressible,                    &
                                     residual_incompressible
    use flux_turb,            only : turbulent_residual
    use gradient_driver,      only : grad_variable
    use thermo,               only : etop, ptoe

    use system_extensions,    only : se_flush
    use designs,              only : evaluate_function_values

    type(grid_type),                 intent(inout) :: grid
    type(soln_type),                 intent(inout) :: soln
    type(crow_flow),                 intent(in)    :: crowf
    type(design_type),               intent(inout) :: design

    integer :: eqn, func, dummy_int

    integer, dimension(1) :: dummy_int_array

    real(dp) :: time_value

    character(len=80) :: variable, gradient

    real(dp), parameter :: zero = 0.0_dp

    continue

    dummy_int = 0
    dummy_int_array(:) = 0

    if (adapt_verbose.and.lmpi_master) then
      write(*,*)"start error prediction..." ; call se_flush()
    end if

! compute_forces will over-write flow residual for iskinfriccalc > 0

    if ( compressible == soln%eqn_set ) then
      call etop( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
    end if
    variable = 'inviscid'
    gradient = 'least-squares'
    call grad_variable(grid, soln, variable, gradient)
    if ( compressible  == soln%eqn_set ) then
      call ptoe( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
    endif

    if ( adapt_verbose .and. lmpi_master ) then
      write (*,*) "grads...",sum(soln%gradx),sum(soln%grady),sum(soln%gradz)
      call se_flush()
    end if

    if (adapt_verbose.and.lmpi_master) then
      write(*,*) "rad compute_forces..." ; call se_flush()
    end if

    call compute_forces(grid,soln,ntt)

    if (adapt_verbose.and.lmpi_master) then
      write(*,*) "rad compute functionals..." ; call se_flush()
    end if

    call evaluate_function_values(grid, soln, design)

    fcn_loop : do func = 1, design%nfunctions
      if (lmpi_master) write(*,'(" rad           function ",i4," = ",e25.16)') &
        func,design%function_data(func)%value
    end do fcn_loop

    if (adapt_verbose.and.lmpi_master) then
      write(*,*) "rad get flow res ",soln%eqn_set ; call se_flush()
    end if

    soln%res(:,:)     = zero
    soln%turbres(:,:) = zero

    select case( soln%eqn_set )
    case ( compressible )
      call residual_compressible( grid, soln, crowf, adapt_verbose )
    case ( incompressible )
      call residual_incompressible( grid, soln, crowf )
    case default
      call lmpi_conditional_stop(1,'residual_flow: only in/compress pg')
    end select

    if (adapt_verbose.and.lmpi_master) then
      write(*,*) "rad l2norm flow residual..." ; call se_flush()
    end if

    call l2norm(grid%cc,                                                       &
      soln%res,                 grid%nnodes0,                                  &
      grid%x,                   grid%y,         grid%z,                        &
      grid%xc,                  grid%yc,        grid%zc,                       &
      grid%vol, grid%cell_vol,  soln%rmshist,                                  &
      soln%rmaxhist,            soln%xlochist,                                 &
      soln%ylochist,            soln%zlochist,                                 &
      grid%nnodes0_2d,          grid%node_pairs_2d,                            &
      soln%njac, 1, soln%njac, 1, 0, dummy_int, dummy_int_array,               &
      dummy_int_array)

    time_value = zero
    eqn_num : do eqn = 1, soln%ndim
      call lmpi_collect_res(                                                   &
        soln%rmshist(eqn,ntt,1),  time_value,  soln%rmaxhist(eqn,ntt,1),       &
        soln%xlochist(eqn,ntt,1),                                              &
        soln%ylochist(eqn,ntt,1),                                              &
        soln%zlochist(eqn,ntt,1))
      soln%rmshist(eqn,ntt,1) =                                                &
        sqrt(soln%rmshist(eqn,ntt,1)/real(grid%nnodesG, dp))
    end do eqn_num

! this call will set the grads to turbgrad
    compute_turbulent_residual : if ( ivisc >= 6 ) then
      if (adapt_verbose.and.lmpi_master) then
        write(*,*)"rad get turb residual...", sum(soln%res) ; call se_flush()
      end if
      call turbulent_residual(grid,soln,crowf)
    end if compute_turbulent_residual

! finished flow residual

    if (adapt_verbose.and.lmpi_master) then
      write(*,*)"rad complete flow res...", sum(soln%res) ; call se_flush()
    end if

  end subroutine residual_flow

!========================= recover_boundary_adjoint ==========================80
!
! Corrects boundary adjoint (Venditti thesis section 4.4.1)
!
!=============================================================================80

  subroutine recover_boundary_adjoint( grid, soln, sadj, crow,                 &
                                       crowf, design, golden_file )

    use refine_adaptation_input, only : adapt_verbose

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use comprow_types,        only : crow_type, crow_flow
    use design_types,         only : design_type

    use info_depr,            only : ntt
    use force_driver,         only : compute_forces
    use designs,              only : evaluate_function_values

    use lmpi,                 only : lmpi_master, lmpi_min, lmpi_bcast
    use lmpi_app,             only : lmpi_xfer

    use system_extensions,    only : se_flush

    type(grid_type),                 intent(inout) :: grid
    type(soln_type),                 intent(inout) :: soln
    type(sadj_type),                 intent(inout) :: sadj
    type(crow_type),                 intent(in)    :: crow
    type(crow_flow),                 intent(in)    :: crowf
    type(design_type),               intent(inout) :: design
    integer,                         intent(in)    :: golden_file

    integer :: j, eqn, func

    real(dp) :: min_coltag, local_real

    continue

 ! make sure that reconstruction is needed, otherwise return

    min_coltag = 1.0_dp
    do j = 1, grid%nnodes0
      do eqn = 1, soln%adim
        min_coltag = min( min_coltag, sadj%coltag(eqn,j) )
      end do
    end do
    local_real = min_coltag
    call lmpi_min( local_real, min_coltag )
    call lmpi_bcast( min_coltag )

    if ( min_coltag > 0.5_dp ) return

    if (adapt_verbose.and.lmpi_master) then
      write(*,*) "rad compute_forces..." ; call se_flush()
    end if

    call compute_forces(grid,soln,ntt)

    if (adapt_verbose.and.lmpi_master) then
      write(*,*) "rad compute functionals..." ; call se_flush()
    end if

    call evaluate_function_values(grid, soln, design)

    if ( adapt_verbose .and. lmpi_master) then
      write(*,*) "clean up boundary adjoint..."
      write(*,*) "coltag...", sum(sadj%coltag)
      write(*,*) "orig rlam...", sum(sadj%rlam)
      call se_flush()
    end if

    do func = 1, design%nfunctions
      do j = 1, grid%nnodes0
        sadj%rlam(:,j,func) = sadj%rlam(:,j,func) * sadj%coltag(:,j)
      end do
      call lmpi_xfer( sadj%rlam(:,:,func) )
      if ( adapt_verbose .and. lmpi_master ) then
        write(*,*) "clean rlam...", sum(sadj%rlam) ; call se_flush()
      end if
    end do

    call reset_grad_get_adj_resid( grid, soln, sadj, crow,                     &
                                   crowf, design, golden_file )

    do func = 1, design%nfunctions
      do j = 1, grid%nnodes0
        do eqn = 1, soln%adim
          if ( sadj%coltag(eqn,j) < 0.5_dp ) then
            sadj%rlam(eqn,j,func) = sadj%res(eqn,j,func)
          end if
        end do
      end do
    call lmpi_xfer( sadj%rlam(:,:,func) )
    end do

    if ( adapt_verbose .and. lmpi_master ) then
      write(*,*) "sadj%rlam...", sum(sadj%rlam) ; call se_flush()
    end if
  end subroutine recover_boundary_adjoint

!=============================== rad_error_est ===============================80
!
! Corrects strong bc enforcement for adjoint
!
!=============================================================================80

  subroutine rad_error_est( grid, soln, sadj, design, golden_file)

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type

    use lmpi,                 only : lmpi_master, lmpi_reduce, lmpi_bcast
    use system_extensions,    only : se_flush
    use info_depr,            only : ntt

    type(grid_type),   intent(inout) :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(inout) :: design
    integer,           intent(in)    :: golden_file

    real(dp), dimension(design%nfunctions) :: corrected_functional

    integer  :: i, j, func
    real(dp) :: localfunctionerror, totalfunctionerror
    real(dp) :: equterm, nodeterm

    real(dp), parameter :: zero = 0.0_dp

    continue

    state_info: if ( lmpi_master ) then
      write (golden_file,'(a, e23.15)')                                      &
        "# Lift = ", soln%totforce(ntt)%cl
      write (golden_file,'(a, e23.15)')                                      &
        "# Drag = ", soln%totforce(ntt)%cd
      write (golden_file,'(a, e23.15)')                                      &
        "# Flow rms= ", soln%rmshist(1,ntt,1)
      write (golden_file,'(4(a, e13.6))')                                    &
        "# rmax= ", soln%rmaxhist(1,ntt,1),                                  &
        " xloc= ", soln%xlochist(1,ntt,1),                                   &
        " yloc= ", soln%ylochist(1,ntt,1),                                   &
        " zloc= ", soln%zlochist(1,ntt,1)
      call se_flush(golden_file)
    end if state_info

    corr_fcn_loop : do func = 1, design%nfunctions
      localfunctionerror = zero
      do j = 1, grid%nnodes0
        nodeterm = zero
        do i = 1, soln%njac
          equterm = soln%res(i,j) * sadj%rlam(i,j,func)
          nodeterm = nodeterm + equterm
        end do
        do i = 1, soln%n_turb
          equterm = soln%turbres(i,j) * sadj%rlam(i+soln%ndim,j,func)
          nodeterm = nodeterm + equterm
        end do
        localfunctionerror = localfunctionerror + nodeterm
      end do

      call lmpi_reduce( localfunctionerror, totalfunctionerror )
      corrected_functional(func) = design%function_data(func)%value +          &
                                     totalfunctionerror

      call lmpi_bcast(corrected_functional(func))

      func_info: if ( lmpi_master ) then
        write (*,'(a, e23.15)') "function           = ", &
          design%function_data(func)%value
        write (*,'(a, e23.15)') "function_error     = ", &
          totalfunctionerror
        write (*,'(a, e23.15)') "function_corrected = ", &
          corrected_functional(func)
        call se_flush()
        write (golden_file,'(a, e23.15)') "function           = ", &
          design%function_data(func)%value
        write (golden_file,'(a, e23.15)') "function_error     = ", &
          totalfunctionerror
        write (golden_file,'(a, e23.15)') "function_corrected = ", &
          corrected_functional(func)
        call se_flush(golden_file)
      end if func_info

    end do corr_fcn_loop

  end subroutine rad_error_est

!=============================== inria_rad ===============================80
! KH - No edits, added block here
! Handles passing of grid metric to visualizing and saving
!
!=============================================================================80

  subroutine inria_rad( grid,soln,sadj,design )

    use adaptation_parameter, only : inria_opt_goal_metric

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type
    use kinddefs,             only : dp
    use refine_adaptation_input, only : adapt_visualize_metric, &
         adapt_export_metric
    use global_image,         only : global_image_export_to
    use adaptation_parameter, only : metric_visualize

    type(grid_type),          intent(inout) :: grid
    type(soln_type),          intent(inout) :: soln
    type(sadj_type),          intent(inout) :: sadj
    type(design_type),        intent(inout) :: design

    real(dp), dimension(6,grid%nnodes01) :: metric

    continue

    call inria_opt_goal_metric( grid%nnodes01, grid,             &
      soln, sadj, metric )

    visualize_metric : if ( 'none' /= trim(adapt_visualize_metric) ) then
      call metric_visualize(grid,metric,adapt_visualize_metric)
    end if visualize_metric
    export_metric : if ( adapt_export_metric ) then
      call global_image_export_to( "raw_ascii",trim(grid%project) // ".metric",&
        grid, metric )
    end if export_metric

    call rad_adapt( grid,metric,soln=soln,sadj=sadj,design=design )

  end subroutine inria_rad

!=============================== inria_rad_time_adapt ========================80
! KH
! Handles the passing of metric data and adaptation
! Copied and modified form radp.f90
!
!=============================================================================80
  subroutine inria_rad_time_adapt(grid,soln,sadj,design)

    use adaptation_parameter, only : inria_opt_goal_metric_scale
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type
    use kinddefs,             only : dp
    use refine_adaptation_input, only : adapt_visualize_metric, &
                                        adapt_export_metric
    use global_image,         only : global_image_export_to
    use adaptation_parameter, only : metric_visualize

    type(grid_type),          intent(inout) :: grid
    type(soln_type),          intent(inout) :: soln
    type(sadj_type),          intent(inout) :: sadj
    type(design_type),        intent(inout) :: design

    real(dp), dimension(6,grid%nnodes01) :: metric

    continue

    call inria_opt_goal_metric_scale( grid%nnodes01, grid,             &
      metric)

    visualize_metric : if ( 'none' /= trim(adapt_visualize_metric) ) then
      call metric_visualize(grid,metric,adapt_visualize_metric)
    end if visualize_metric

    export_metric : if ( adapt_export_metric ) then
      call global_image_export_to("raw_ascii",trim(grid%project)//".metric",&
        grid, metric )
    end if export_metric

    call rad_adapt( grid,metric,soln=soln,sadj=sadj,design=design )

  end subroutine inria_rad_time_adapt

!=============================== rad_adapt ===================================80
!
! Wraps grid adaptation and adapted grid output for adjoint path
!
!=============================================================================80

  subroutine rad_adapt( grid,metric,h_h0,soln,sadj,design )

    use kinddefs,                 only : dp
    use grid_types,               only : grid_type
    use solution_types,           only : soln_type
    use solution_adj,             only : sadj_type, wresta
    use design_types,             only : design_type

    use solution,                 only : wrest
    use io,                       only : use_prior
    use info_depr,                only : ntt
    use lmpi,                     only : lmpi_master
    use system_extensions,        only : se_flush

    use refine_adaptation_driver, only : refine_adapt
    use parallel_embed,           only : verify_l2g
    use global_image,             only : global_image_ugrid,                   &
                                         global_image_component_ugrid
    use bc_util,                  only : write_aflr3_mapbc

    use nml_overset_data,         only : overset_flag
    use grid_helper,              only : grid_maximum_imesh
    use string_utils,             only : int_to_s
    use file_utils,               only : big_endian_io

    type(grid_type),             intent(inout) :: grid
    real(dp),    dimension(:,:), intent(in)    :: metric
    real(dp),    dimension(:), optional, intent(in) :: h_h0
    type(soln_type),   optional, intent(inout) :: soln
    type(sadj_type),   optional, intent(inout) :: sadj
    type(design_type), optional, intent(inout) :: design

    integer :: maximum_imesh, component

    character(256) :: filename

    continue

    if (lmpi_master) write(*,*) 'rad adaptation time!'

    if ( present(h_h0) ) then
      call refine_adapt( grid, metric, h_h0=h_h0, &
        soln=soln, sadj=sadj, design=design )
    else
      call refine_adapt( grid, metric, &
        soln=soln, sadj=sadj, design=design )
    endif

    call verify_l2g(grid)

    ntt = 0
    call write_aflr3_mapbc( grid, '../Flow/'//trim(grid%project)//'.mapbc' )
    if ( big_endian_io() ) then
      filename =  '../Flow/'//trim(grid%project)//'.b8.ugrid'
    else
      filename =  '../Flow/'//trim(grid%project)//'.lb8.ugrid'
    end if
    call global_image_ugrid( grid, filename, cl2g_available_arg=.false. )

    if (overset_flag) then
      call grid_maximum_imesh(grid, maximum_imesh)
      do component = 0, maximum_imesh
        filename = "../Flow/Component_Grid_"//trim(int_to_s(component))
        if (lmpi_master) write(*,*) "writing ", trim(filename)
        call global_image_component_ugrid( grid, filename, component )
      end do
    end if

    if ( lmpi_master ) then
      write (*,*) 'write soln files...' ; call se_flush()
    end if

    restart_write : if ( present(soln) ) then
      use_prior = 1
      call wrest( grid, soln, '../Flow/' )

      if (lmpi_master) then
        write (*,*) 'write sadj files...' ; call se_flush()
      end if

      call wresta( grid, soln, sadj, design )
    end if restart_write
  end subroutine rad_adapt

!============================= rad_adapt_indicator ===========================80
!
! Calculates the adjoint adaptive indicator
!
!=============================================================================80
subroutine rad_adapt_indicator( grid, rads, soln, sadj, golden_file )

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use rad_defs,             only : rads_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use lmpi,                 only : lmpi_master, lmpi_reduce
    use lmpi_app,             only : lmpi_collect_res

    use system_extensions,    only : se_flush

    type(grid_type),   intent(in)    :: grid
    type(rads_type),   intent(inout) :: rads
    type(soln_type),   intent(in)    :: soln
    type(sadj_type),   intent(in)    :: sadj
    integer,           intent(in)    :: golden_file

    integer  :: func, i, j
    real(dp) :: nodesum, adapttime
    real(dp) :: adaptrms, adapttotal, adaptmax
    real(dp) :: adaptxloc, adaptyloc, adaptzloc

    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: half = 0.5_dp
    real(dp), parameter :: one  = 1.0_dp

    continue

    adaptrms   = zero
    adapttotal = zero
    adaptmax   = -one

    adapt_func : do func = 1, rads%noutput
      do j = 1, grid%nnodes0
        nodesum = zero
        do i = 1, soln%ndim
          nodesum = nodesum + half *                                           &
            (abs((sadj%rlam(i,j,func ) - rads%dual_linear(i,j)) *              &
            soln%res(i,j))+                                                    &
            abs((soln%q_dof(i,j) - rads%prim_linear(i,j)) *                    &
            sadj%res(i,j,func)))
        end do
        do i = 1, soln%n_turb
          nodesum = nodesum + half *                                           &
            (abs((sadj%rlam(i+soln%ndim,j,func) -                              &
            rads%dual_linear(i+soln%ndim,j)) *                                 &
            soln%turbres(i,j)) +                                               &
            abs((soln%turb(i,j) - rads%turb_linear(i,j)) *                     &
            sadj%res(i+soln%ndim,j,func)) )
        end do
        rads%adapt_param(func,j) = nodesum
        adapttotal = adapttotal + nodesum
        adaptrms   = adaptrms   + nodesum*nodesum
        if ( nodesum > adaptmax ) then
          adaptmax  = nodesum
          adaptxloc = grid%x(j)
          adaptyloc = grid%y(j)
          adaptzloc = grid%z(j)
        endif
      end do
    end do adapt_func

    adapttime = zero
    nodesum   = adapttotal
    call lmpi_reduce( nodesum, adapttotal )
    call lmpi_collect_res( adaptrms, adapttime, adaptmax,                      &
                           adaptxloc, adaptyloc, adaptzloc )
    adaptrms = sqrt( adaptrms / real(grid%nnodesG, dp) )

    adapt_info: if ( lmpi_master ) then
      write (golden_file,'(a, e23.15)') "# Adapt Param rms= ", adaptrms
      write (golden_file,'(4(a, e13.6))')                                    &
        "# amax= ", adaptmax,                                                &
        " xloc= ", adaptxloc,                                                &
        " yloc= ", adaptyloc,                                                &
        " zloc= ", adaptzloc
      write(golden_file,'(a,e23.15)') ' remaining_error = ', adapttotal
      call se_flush(golden_file)
      write(*,'(a,e23.15)') ' remaining_error = ', adapttotal
      call se_flush()
    end if adapt_info

  end subroutine rad_adapt_indicator

!============================= rad_adapt_debug ===============================80
!
! Examines the adjoint adaptive indicator
!
!=============================================================================80
  subroutine rad_adapt_debug( grid, rads, soln, sadj )

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use rad_defs,             only : rads_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use global_image,         only : global_image_export_to

    type(grid_type),   intent(in)    :: grid
    type(rads_type),   intent(inout) :: rads
    type(soln_type),   intent(in)    :: soln
    type(sadj_type),   intent(in)    :: sadj

    integer :: n_output_variables
    integer :: output_data_size
    character(80), dimension(:), allocatable  :: output_variables
    real(dp), dimension(:,:), allocatable :: output_data
    character(256) :: filename
    integer  :: i,j
    integer  :: func = 1
    continue

    n_output_variables = 0
    n_output_variables = n_output_variables + 3 ! xyz
    n_output_variables = n_output_variables + 1 ! adapt
    n_output_variables = n_output_variables + soln%ndim ! sol
    n_output_variables = n_output_variables + soln%ndim ! sol res
    n_output_variables = n_output_variables + soln%adim ! adj
    n_output_variables = n_output_variables + soln%adim ! adj res

    output_data_size = grid%nnodes0

    allocate(output_variables(n_output_variables))

    output_variables(1) = 'x'
    output_variables(2) = 'y'
    output_variables(3) = 'z'

    output_variables(4) = 'adpt'

    output_variables(5) = 'rho'
    output_variables(6) = 'rhou'
    output_variables(7) = 'rhov'
    output_variables(8) = 'rhow'
    output_variables(9) = 'e'

    output_variables(10) = 'cont'
    output_variables(11) = 'xmom'
    output_variables(12) = 'ymom'
    output_variables(13) = 'zmom'
    output_variables(14) = 'engy'

    output_variables(15) = 'l1'
    output_variables(16) = 'l2'
    output_variables(17) = 'l3'
    output_variables(18) = 'l4'
    output_variables(19) = 'l5'

    output_variables(20) = 'r1'
    output_variables(21) = 'r2'
    output_variables(22) = 'r3'
    output_variables(23) = 'r4'
    output_variables(24) = 'r5'

    allocate(output_data(n_output_variables,output_data_size))

    do i = 1, grid%nnodes0
      output_data(1,i) = grid%x(i)
      output_data(2,i) = grid%y(i)
      output_data(3,i) = grid%z(i)
      output_data(4,i) = rads%adapt_param(1,i)

      do j = 1, soln%ndim
        output_data(4+j,i) = soln%q_dof(j,i)
      end do
      do j = 1, soln%ndim
        output_data(9+j,i) = soln%res(j,i)
      end do
      do j = 1, soln%adim
        output_data(14+j,i) = sadj%rlam(j,i,func)
      end do
      do j = 1, soln%adim
        output_data(19+j,i) = sadj%res(j,i,func)
      end do
    end do

    filename = trim(grid%project)//'_debug_soln'

    call global_image_export_to('tec',filename,grid,&
      n_output_variables, output_variables, output_data_size, output_data)

    do i = 1, grid%nnodes0
      output_data(1,i) = grid%x(i)
      output_data(2,i) = grid%y(i)
      output_data(3,i) = grid%z(i)
      output_data(4,i) = rads%adapt_param(1,i)

      do j = 1, soln%ndim
        output_data(4+j,i) = abs(soln%q_dof(j,i)- rads%prim_linear(j,i))
      end do
      do j = 1, soln%ndim
        output_data(9+j,i) = abs(soln%res(j,i))
      end do
      do j = 1, soln%adim
        output_data(14+j,i) = abs(sadj%rlam(j,i,func) - rads%dual_linear(j,i))
      end do
      do j = 1, soln%adim
        output_data(19+j,i) = abs(sadj%res(j,i,func))
      end do
    end do

    filename = trim(grid%project)//'_debug_interp'

    call global_image_export_to('tec',filename,grid,&
      n_output_variables, output_variables, output_data_size, output_data)

    deallocate( output_data )
    deallocate( output_variables )

  end subroutine rad_adapt_debug

!=========================== reset_grad_get_adj_resid ========================80
!
! Resets the inviscid gradients and gets the adjoint residual
!
!=============================================================================80
  subroutine reset_grad_get_adj_resid( grid, soln, sadj, crow,                 &
                                       crowf, design, golden_file )

    use refine_adaptation_input, only : adapt_verbose
    use kinddefs,                only : dp
    use grid_types,              only : grid_type
    use solution_types,          only : soln_type, compressible
    use solution_adj,            only : sadj_type
    use comprow_types,           only : crow_type, crow_flow
    use design_types,            only : design_type
    use info_depr,               only : ivisc, ntt
    use lmpi,                    only : lmpi_master, lmpi_die
    use lmpi_app,                only : lmpi_xferedge, lmpi_collect_res
    use system_extensions,       only : se_flush
    use gradient_driver,         only : grad_variable
    use residual,                only : residual_adj, dfdq_filled, l2norm
    use flux_turb,               only : turbulent_edget
    use thermo,                  only : etop, ptoe
    use inviscid_flux,           only : iflim
    use cut_types,               only : cut_cell_activated
    use cut_limiters,            only : cut_venlim
    use reconstruction,          only : timlim, venlim, no_lim, gsblim
    use info_depr,               only : mixed

    type(grid_type),                 intent(inout) :: grid
    type(soln_type),                 intent(inout) :: soln
    type(sadj_type),                 intent(inout) :: sadj
    type(crow_type),                 intent(in)    :: crow
    type(crow_flow),                 intent(in)    :: crowf
    type(design_type),               intent(inout) :: design
    integer,                         intent(in)    :: golden_file

    character(len=80) :: variable, gradient

    real(dp) :: rmscur2, rmscur3, rmscur4, rmscur5, rmscur6

    logical, parameter :: update_all_points = .false.

  continue

    if ( compressible == soln%eqn_set) then
      call etop( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
    end if

    variable = 'inviscid'
    gradient = 'least-squares'
    call grad_variable(grid, soln, variable, gradient)

    if ( adapt_verbose .and. lmpi_master ) then
      write (*,*) "grads...",sum(soln%gradx),sum(soln%grady),sum(soln%gradz)
      call se_flush()
    end if

! Set up the inviscid flux limiters, if used

      choice_flux_limiter_eqn_set0 : select case(iflim)

        case (0) choice_flux_limiter_eqn_set0  ! no limit

!       initalize limiters to 1.0, this is the same as no limiter

          call no_lim(grid%nnodes01, soln%phi, soln%n_grd)

        case (1) choice_flux_limiter_eqn_set0 ! flux limiter

          if (cut_cell_activated) then
            call cut_venlim(                                                   &
                        grid%nnodes0, grid%nnodes01, grid%nedgeloc, soln%q_dof,&
                        soln%phi,     soln%gradx,    soln%grady,    soln%gradz,&
                        grid%x,       grid%y,        grid%z,        grid%eptr, &
                        grid%vol,     grid%nedgeloc_2d,                        &
                        soln%ndim,    soln%n_tot,                              &
                        acts_like_barth = .true. )

          else if (.not. grid%cc) then
            call timlim(grid%nnodes0, grid%nnodes01, grid%nedgeloc, soln%q_dof,&
                        soln%phi,     soln%gradx,    soln%grady,    soln%gradz,&
                        grid%x,       grid%y,        grid%z,        grid%vol,  &
                        grid%eptr,    grid%nedgeloc_2d,                        &
                        soln%n_grd,   soln%n_tot,    soln%ndim,                &
                        grid%nbound,  grid%bc,       grid%cgamma,              &
                        grid%slenxn,  grid%slenyn,   grid%slenzn,   grid%slen, &
                        grid%jag )
          else
            write(*,*)'limiter selection is not a valid choice ',              &
                      'for cell centered: iflim = ',iflim
            write(*,*)'stopping...'
            call lmpi_die
          end if

        case (2) choice_flux_limiter_eqn_set0 ! Venkat's flux Limiter

          if (cut_cell_activated) then
            call cut_venlim(                                                   &
                        grid%nnodes0, grid%nnodes01, grid%nedgeloc, soln%q_dof,&
                        soln%phi,     soln%gradx,    soln%grady,    soln%gradz,&
                        grid%x,       grid%y,        grid%z,        grid%eptr, &
                        grid%vol,     grid%nedgeloc_2d,                        &
                        soln%n_grd,   soln%n_tot)
          else if (.not. grid%cc) then
            call venlim(grid%nnodes0, grid%nnodes01, grid%nedgeloc, soln%q_dof,&
                        soln%phi,     soln%gradx,    soln%grady,    soln%gradz,&
                        grid%x,       grid%y,        grid%z,        grid%vol,  &
                        grid%eptr,    grid%nedgeloc_2d,                        &
                        soln%n_grd,   soln%n_tot,    soln%ndim,                &
                        grid%nbound,  grid%bc,       grid%cgamma,              &
                        grid%slenxn,  grid%slenyn,   grid%slenzn,   grid%slen, &
                        grid%jag )
          else
            write(*,*)'limiter selection is not a valid choice ',              &
                      'for cell centered: iflim = ',iflim
            write(*,*)'stopping...'
            call lmpi_die
          end if

        case (3:7) choice_flux_limiter_eqn_set0 !phi not used; set phi=1

          call no_lim(grid%nnodes01, soln%phi, soln%n_grd)

        case (13, 14, 15, 16, 17) choice_flux_limiter_eqn_set0 !stencil based

          call gsblim(grid%nnodes0, grid%nnodes01, grid%nedgeloc, soln%q_dof,  &
                      soln%phi,     soln%gradx,    soln%grady,    soln%gradz,  &
                      grid%x,       grid%y,        grid%z,        grid%vol,    &
                      grid%xn,      grid%yn,       grid%zn,       grid%ra,     &
                      grid%eptr,    grid%nedgeloc_2d,                          &
                      soln%n_grd,   soln%n_tot,    soln%eqn_set,  soln%ndim,   &
                      grid%nbound,  grid%bc,       grid%cgamma,                &
                      grid%slenxn,  grid%slenyn,   grid%slenzn,   grid%slen,   &
                      grid%jag )

        case default

          write(*,*) 'limiter selection is not a valid choice, iflim = ',iflim
          write(*,*) 'stopping...'
          call lmpi_die

      end select choice_flux_limiter_eqn_set0

      if (adapt_verbose.and.lmpi_master)                                       &
        write(*,*) "sum(phi)...",sum(soln%phi)
      if (adapt_verbose.and.lmpi_master) call se_flush()

    ! only the adjoint residual needs these on edges > nedgeloc
    reset_dft : if ( ivisc >= 6 .and. .not. mixed ) then
      call turbulent_edget(grid, soln )
      call lmpi_xferedge( soln%dft1 )
      call lmpi_xferedge( soln%dft2 )
      if ( adapt_verbose .and. lmpi_master ) then
        write (*,*) "xferedge...", sum(soln%dft1), sum(soln%dft2)
        call se_flush()
      end if
    endif reset_dft

    if ( compressible == soln%eqn_set ) then
      call ptoe( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
    end if

    dfdq_filled = .false.
    call residual_adj(grid,soln,sadj,crow,crowf,design,soln%totforce(ntt),     &
                      update_all_points, adapt_verbose)

! Flip sign on residual

    sadj%res = -sadj%res

    if ( adapt_verbose .and. lmpi_master ) then
      write(*,*) "sadj%res...", sum(sadj%res) ; call se_flush()
    end if
    call l2norm( sadj%res,     grid%nnodes0, grid%nnodes01,                    &
                 grid%x,       grid%y,       grid%z,                           &
                 soln%rmshist(1,ntt,1),                                        &
                 rmscur2, rmscur3, rmscur4, rmscur5, rmscur6,                  &
                 soln%rmaxhist(1,ntt,1),                                       &
                 soln%xlochist(1,ntt,1), soln%ylochist(1,ntt,1),               &
                 soln%zlochist(1,ntt,1), soln%adim )

    call lmpi_collect_res(                                                     &
      soln%rmshist(1,ntt,1),  soln%walltime(ntt),  soln%rmaxhist(1,ntt,1),     &
      soln%xlochist(1,ntt,1), soln%ylochist(1,ntt,1), soln%zlochist(1,ntt,1) )

    soln%rmshist(1,ntt,1) = sqrt(soln%rmshist(1,ntt,1)/real(grid%nnodesG,dp))

    adjoint_info: if ( lmpi_master ) then
      write (golden_file,'(a, e23.15)') "#Adjoint rms= ", soln%rmshist(1,ntt,1)
      write (golden_file,'(4(a, e13.6))')                                      &
        "# rmax= ", soln%rmaxhist(1,ntt,1),                                    &
        " xloc= ", soln%xlochist(1,ntt,1),                                     &
        " yloc= ", soln%ylochist(1,ntt,1),                                     &
        " zloc= ", soln%zlochist(1,ntt,1)
    end if adjoint_info

  end subroutine reset_grad_get_adj_resid

!=========================== validate_soln ===================================80
!
!  Validates the solution by applying floors, etc.
!
!=============================================================================80

  subroutine validate_soln( nnodes0, soln )

    use kinddefs,       only : dp
    use solution_types, only : soln_type, compressible
    use ivals,          only : p0, rho0
    use fluid,          only : gm1
    use lmpi_app,       only : lmpi_xfer

    use adjoint_switches, only : use_bp_model

    integer,         intent(in)    :: nnodes0
    type(soln_type), intent(inout) :: soln

    real(dp), parameter :: half    = 0.5_dp
    real(dp), parameter :: percent = 0.01_dp
    real(dp), parameter :: zero    = 0.0_dp

    real(dp) :: rho,u,v,w,e,p,q2

    integer :: node, dep_var

    continue

    if ( soln%eqn_set == compressible ) then

      do node = 1, nnodes0

        rho = soln%q_dof(1,node)
        if ( rho <= percent*rho0 ) then
          rho = percent*rho0
          soln%q_dof(1,node) = rho
        endif

        rho = soln%q_dof(1,node)
        u   = soln%q_dof(2,node)/rho
        v   = soln%q_dof(3,node)/rho
        w   = soln%q_dof(4,node)/rho
        q2  = u*u + v*v + w*w
        e   = soln%q_dof(5,node)
        p   = gm1*(e - half*rho*q2)

        if ( p <= percent*p0 ) then
          p = percent*p0
          soln%q_dof(5,node) = p/gm1 + half*rho*q2
        endif
      end do
    endif

    call lmpi_xfer(soln%q_dof)

    floor_turb_unless_bp : if ( .not. use_bp_model ) then
      do dep_var = 1, soln%n_turb
        do node = 1, nnodes0
          soln%turb(dep_var,node) = max( soln%turb(dep_var,node), zero )
        end do
      end do
    end if floor_turb_unless_bp

    if ( soln%n_turb > 0 ) call lmpi_xfer( soln%turb )

  end subroutine validate_soln

!=========================== create_cut_cell_metric ==========================80
!
!=============================================================================80

  subroutine create_cut_cell_metric(nnodes0, raw_grid_data, soln,              &
    anisotropic_metric, box_grid, box_anisotropic_metric)

    use kinddefs,             only : dp
    use allocations,          only : my_alloc_ptr
    use grid_types,           only : grid_type, raw_grid_data_type
    use solution_types,       only : soln_type
    use cut_types,            only : cut

    use io,                   only : readme
    use lmpi,                 only : lmpi_master
    use lmpi_app,             only : lmpi_xfer

    use pparty_preprocessor,  only : pparty_preprocess, pparty_setup_stuff
    use solution,             only : set_up_neq
    use grid_helper,          only : grid_reset_lmpi_xfer, &
                                     create_test_g2l, create_part
    use adaptation_parameter, only : average_current_metric

    integer,                  intent(in)    :: nnodes0
    type(raw_grid_data_type), intent(inout) :: raw_grid_data
    type(soln_type),          intent(inout) :: soln
    real(dp), dimension(:,:), intent(inout) :: anisotropic_metric
    type(grid_type),          intent(inout) :: box_grid
    real(dp), dimension(:,:), pointer       :: box_anisotropic_metric

    integer :: node
    real(dp), parameter :: half = 0.5_dp

    continue

    if (lmpi_master) write(*,*) 'rad reading box grid'
    from_raw_grid : if (.not.raw_grid_data%read_part_files) then
      box_grid%igrid = 1 ! to prevent multigrid
      call pparty_preprocess('../Flow/',raw_grid_data, box_grid)
      call pparty_setup_stuff(box_grid)
    else
      call readme(soln%eqn_set,box_grid,"../Flow/")
    end if from_raw_grid
    call set_up_neq ( box_grid, soln )

    ! reset the lmpi_xfer arrays for the recently read grid (box_grid)
    if ( lmpi_master )  write(*,*) 'rad reset box grid mpi xfer stencil'
    call create_test_g2l(box_grid)
    if ( associated(box_grid%part) ) deallocate(box_grid%part)
    allocate(box_grid%part(box_grid%nnodes01))
    call create_part(box_grid%nnodes0, box_grid%nnodes01, box_grid%l2g, &
      box_grid%part)
    call grid_reset_lmpi_xfer(box_grid)

    if (lmpi_master) write(*,*)"rad average box grid metric"
    call my_alloc_ptr(box_anisotropic_metric,6,box_grid%nnodes01)
    call average_current_metric(box_grid%nnodes01, box_grid%nnodes0, &
      box_grid%x, box_grid%y, box_grid%z, box_anisotropic_metric,    &
      box_grid%elem(1)%ncell, size(box_grid%elem(1)%c2n,2),          &
      box_grid%elem(1)%c2n )
    call lmpi_xfer(box_anisotropic_metric)

! to enhance the coarsening of the background grid outside of the domain
! to speed up adaptation and cutting.

    box_anisotropic_metric = half*box_anisotropic_metric !note eig(m)=1/h^2

    if (lmpi_master) write(*,*)"rad set box metric with cut grid"
    do node = 1, nnodes0
      box_anisotropic_metric(:,cut%parent(node))=anisotropic_metric(:,node)
    end do
    call lmpi_xfer( box_anisotropic_metric )

  end subroutine create_cut_cell_metric

!=========================== single_grid_reconstruction ======================80
!
!=============================================================================80

  subroutine single_grid_reconstruction(                                       &
    ndim, nnodes0, nnodes,                                                     &
    q,           linear,                                                       &
    x,           y,      z,                                                    &
    r11,         r12,    r13,                                                  &
    r22,         r23,    r33,                                                  &
    nedgeloc, eptr )

    use kinddefs,              only : dp
    use cut_types,             only : cut_cell_activated
    use cut_gradient,          only : cut_lstgs
    use reconstruction,        only : lstgs
    use inviscid_flux,         only : first_order_iterations
    use info_depr,             only : ntt
    use nml_nonlinear_solves,  only : itime
    use timeacc,               only : pseudo_sub
    use lmpi_app,              only : lmpi_xfer

    integer,                           intent(in)    :: ndim
    integer,                           intent(in)    :: nnodes0, nnodes
    real(dp), dimension(ndim,nnodes),  intent(inout) :: q
    real(dp), dimension(ndim,nnodes0), intent(out)   :: linear
    real(dp), dimension(nnodes),       intent(in)    :: x,y,z
    real(dp), dimension(nnodes0),      intent(in)    :: r11, r12, r13
    real(dp), dimension(nnodes0),      intent(in)    :: r22, r23, r33
    integer,                           intent(in)    :: nedgeloc
    integer,  dimension(2,nedgeloc),   intent(in)    :: eptr

    integer  :: dummy_viscous_method
    integer  :: dummy_n_turb
    integer  :: eq, edge, node
    integer  :: n1, n2

    real(dp) :: dx, dy, dz, div

    integer,  dimension(nnodes)      :: dummy_symmetry
    integer,  dimension(nnodes0)     :: degree

    real(dp), dimension(1,nnodes)    :: dummy_turb
    real(dp), dimension(ndim,nnodes) :: gradx
    real(dp), dimension(ndim,nnodes) :: grady
    real(dp), dimension(ndim,nnodes) :: gradz
    real(dp), dimension(ndim,nnodes) :: quad

    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: my02 = 0.2_dp
    real(dp), parameter :: my04 = 0.4_dp
    real(dp), parameter :: half = 0.5_dp
    real(dp), parameter :: my06 = 0.6_dp

    continue

!   this is to make sure that the gradients are computed
    if (itime == 0) then
      ntt = 1
    else
      pseudo_sub = 1
    end if
    first_order_iterations = 0

    if ( cut_cell_activated ) then
      call cut_lstgs(                &
        nnodes0, nnodes, x, y, z,    &
        r11, r12, r13, r22, r23, r33,&
        ndim, q,                     &
        ndim, gradx, grady, gradz,   &
        nedgeloc,  eptr, 0, ndim)
    else
      dummy_n_turb =0
      dummy_turb = zero
      dummy_symmetry = 0
      dummy_viscous_method = 0
      call lstgs(dummy_viscous_method,        &
        nnodes0,nnodes,                       &
        nedgeloc,eptr,                        &
        dummy_symmetry,                       &
        q, gradx,grady,gradz,                 &
        x,y,z,                                &
        r11,r12,r13,r22,r23,r33,              &
        ndim,ndim,                            &
        dummy_turb,dummy_n_turb,0,ndim)
    end if

    call lmpi_xfer(gradx)
    call lmpi_xfer(grady)
    call lmpi_xfer(gradz)

    degree = 0
    linear = zero
    quad   = zero

    edge_loop : do edge = 1, nedgeloc
      n1 = eptr(1,edge)
      n2 = eptr(2,edge)

      dx = x(n2) - x(n1)
      dy = y(n2) - y(n1)
      dz = z(n2) - z(n1)

      if ( n1 <= nnodes0 ) then
        degree(n1) = degree(n1) + 1
        do eq = 1, ndim
          linear(eq,n1) = linear(eq,n1) + half * q(eq,n1) + half * q(eq,n2)

          quad(eq,n1)   = quad(eq,n1)   + my06 * q(eq,n1) + my04 * q(eq,n2)    &
            - my02 * (dx*gradx(eq,n1) + dy*grady(eq,n1) + dz*gradz(eq,n1))     &
            - my02 * (dx*gradx(eq,n2) + dy*grady(eq,n2) + dz*gradz(eq,n2))
        end do
      end if

      if ( n2 <= nnodes0 ) then
        degree(n2) = degree(n2) + 1
        do eq = 1, ndim
          linear(eq,n2) = linear(eq,n2) + half * q(eq,n2) + half * q(eq,n1)

          quad(eq,n2)   = quad(eq,n2)   + my06 * q(eq,n2) + my04 * q(eq,n1)    &
            + my02 * (dx*gradx(eq,n2) + dy*grady(eq,n2) + dz*gradz(eq,n2))     &
            + my02 * (dx*gradx(eq,n1) + dy*grady(eq,n1) + dz*gradz(eq,n1))
        end do
      end if
    end do edge_loop

    do node = 1, nnodes0
      div = real(degree(node),dp)
      do eq = 1, ndim
        linear(eq,node) = linear(eq,node) / div
        q(eq,node)      = quad(eq,node)   / div
      end do
    end do

    call lmpi_xfer(q)

  end subroutine single_grid_reconstruction

end module radp

!======================= DQC_VIA_DNU =========================================80
!
! d(Q-conservative) via d(viscosity/rho) = d(nu)
!
!=============================================================================80
  pure function dqc_via_dnu( dnu, cstar, rho, u, v, w, p )

    use fluid, only : gamma, ggm1

    real(dp), intent(in) :: dnu, cstar, rho, u, v, w, p

    real(dp), dimension(5) :: dqc_via_dnu

    real(dp) :: T51, T52, T53, T54, T55, T, qsq, rhoinv
    real(dp) :: dqc_via_drho, dqc_via_dT

  continue

    rhoinv = 1._dp/rho

    T = gamma * p * rhoinv

    !...variation wrt rho.
    dqc_via_drho = - dnu*viscosity_law( cstar, T )*rhoinv**2

    !...variation wrt temperature.
    dqc_via_dT = dnu*dviscosity_law( cstar, T )*rhoinv

    !...transformation from T to Q variables.
    qsq = u**2 + v**2 + w**2
    T51 = -rhoinv*( T - 0.5_dp*ggm1*( qsq ) )
    T55 =  ggm1*rhoinv
    T52 = -T55*u
    T53 = -T55*v
    T54 = -T55*w

    dqc_via_dnu(1) = dqc_via_drho &
                   + dqc_via_dT*T51
    dqc_via_dnu(2) = dqc_via_dT*T52
    dqc_via_dnu(3) = dqc_via_dT*T53
    dqc_via_dnu(4) = dqc_via_dT*T54
    dqc_via_dnu(5) = dqc_via_dT*T55

  end function dqc_via_dnu

!================================== QFCC =====================================80
!
!  Reconstruction of the primitive state variables at an interface for the
!  Cell Centered Scheme
!
!=============================================================================80

  pure function qfcc(rx, ry, rz, gradx, grady, gradz, q1, q2,                  &
                     phi, eps, epscoef, kappa_umuscl,                          &
                     second, useage, iflim, omega, ndim)

    use kinddefs,        only : dp
    use generic_gas_map, only : n_species

    integer,                   intent(in) :: iflim, useage, ndim

    real(dp),                  intent(in) :: rx, ry, rz
    real(dp),                  intent(in) :: eps, epscoef, omega
    real(dp),                  intent(in) :: kappa_umuscl
    real(dp), dimension(ndim), intent(in) :: gradx, grady, gradz
    real(dp), dimension(ndim), intent(in) :: q1, q2
    real(dp), dimension(ndim), intent(in) :: phi

    logical,                   intent(in) :: second

    real(dp), dimension(ndim+1)           :: qfcc

    real(dp),    parameter :: my_0    = 0.00_dp
    real(dp),    parameter :: my_1    = 1.00_dp
    real(dp)               :: rhoi_min

  continue

!   Initialize reconstruction realizability flag to default state

    qfcc(ndim+1) = my_0

!   Higher order reconstructed state primitive variables:

    if (second) then
      qfcc(1:ndim) = q1 + dqccm(rx,ry,rz,gradx,grady,gradz,q1,q2,phi,          &
                             eps,epscoef,kappa_umuscl,                         &
                             useage,iflim,omega,ndim)
    else
      qfcc(1:ndim) = q1
    end if

!   Detect unrealizable higher order reconstruction of density and/or pressure
!   then force a first order reconstruction of the state variables and then
!   tag the reconstruction as unrealizable

    rhoi_min = minval(qfcc(1:n_species))
    if ((rhoi_min <= my_0) .or. (qfcc(5) <= my_0)) then
      qfcc(1:ndim) = q1(1:ndim)
      qfcc(ndim+1)   = my_1
    end if

  end function qfcc

module kinddefs

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  implicit none

  private

! single precision (IEEE 754)
  integer, parameter, public :: system_r4=selected_real_kind(6, 37)
  integer, parameter, public :: r4=system_r4  ! shorthand for system_r4

! double precision (IEEE 754)
  integer, parameter, public :: system_r8=selected_real_kind(15, 307)
  integer, parameter, public :: r8=system_r8  ! shorthand for system_r8

! one byte integer (greater than 10e2)
  integer, parameter, public :: system_i1=selected_int_kind(2)
  integer, parameter, public :: i1=system_i1

! two byte integer (greater than 10e3)
  integer, parameter, public :: system_i2=selected_int_kind(3)
  integer, parameter, public :: i2=system_i2

! four byte integer (greater than 10e5)
  integer, parameter, public :: system_i4=selected_int_kind(5)
  integer, parameter, public :: i4=system_i4
  integer, parameter, public :: max_i4 = huge(1_system_i4)

! eight byte integer (greater than 10e10)
  integer, parameter, public :: system_i8=selected_int_kind(10)
  integer, parameter, public :: i8=system_i8

  integer, parameter, public :: dp =system_r8 ! default precision

! Types controlling precision of linear solves.
  integer, parameter, public :: jp =system_r8 ! Diagonal LU Jacobian precision
#ifdef FULL_PRECISION
  integer, parameter, public :: dqp=system_r8 ! Delta-q precision
  integer, parameter, public :: odp=system_r8 ! Off-diagonal Jacobian precision
#else
  integer, parameter, public :: dqp=system_r4 ! Delta-q precision
  integer, parameter, public :: odp=system_r4 ! Off-diagonal Jacobian precision
#endif
  integer, parameter, public :: lup=system_r8 ! Off-diagonal ILU precision
  integer, parameter, public :: krp=system_r8 ! Krylov precision

  integer, parameter, public :: default_integer_kind = kind(1)
  integer, parameter, public :: default_real_kind = kind(1.0)

end module kinddefs

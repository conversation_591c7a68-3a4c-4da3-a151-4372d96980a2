!================================= ROE_AVG ===================================80
!
! This routine computes thermodynamic independent Roe averages
! Energy must be computed from primitives prior to call
!
! Note that this function uses primitive variables ql and qr
!
!=============================================================================80

  pure function roe_avg(ql, qr, enrgyl, enrgyr, ndim, n_species, n_momx,       &
                        n_etot, n_turb_g, n_turb_ke, betat)

    use kinddefs,        only : dp

    integer,                   intent(in) :: ndim, n_species, n_momx, n_etot
    integer,                   intent(in) :: n_turb_g, n_turb_ke
    real(dp), dimension(ndim), intent(in) :: ql, qr
    real(dp),                  intent(in) :: enrgyl, enrgyr
    real(dp),                  intent(in) :: betat

    real(dp), dimension(ndim)             :: roe_avg

    real(dp) :: rho, rhol, rhor
    real(dp) :: Hl, Hr
    real(dp) :: watl, watr
    real(dp) :: ratl, ratr

    real(dp), parameter :: my_1    =  1.0_dp

  continue

!   Get left and right state primitive variables

    rhol   = sum(ql(1:n_species))
    rhor   = sum(qr(1:n_species))

!   Compute the remaining needed left and right state variables:

    Hl     = (enrgyl + ql(n_etot))/rhol
    Hr     = (enrgyr + qr(n_etot))/rhor

    if( n_turb_g > 1 .and. n_turb_ke > n_etot)then
      Hl = Hl + betat*ql(n_turb_ke)
      Hr = Hr + betat*qr(n_turb_ke)
    end if

!   Compute Roe averages

    rho  = sqrt(rhol*rhor)
    watl = rho/(rho + rhor)
    watr = my_1 - watl
    ratl = rho/rhol
    ratr = rho/rhor
    roe_avg(1:n_species) = ql(1:n_species)*watl*ratl + qr(1:n_species)*watr*ratr
    roe_avg(n_momx:ndim) = ql(n_momx:ndim)*watl      + qr(n_momx:ndim)*watr
    roe_avg(n_etot) =                   Hl*watl      +              Hr*watr

  end function roe_avg

!=============================================================================80
!
!  Find the norm of a vector.  Note that the sqrt is not taken here
!
!=============================================================================80

  function snrm2( n, sx )

    use kinddefs, only : dp

    real(dp) :: snrm2

    integer,                intent(in) :: n
    real(dp), dimension(n), intent(in) :: sx

    integer :: i

    continue

    snrm2 = 0.0_dp

    do i = 1, n
      snrm2 = snrm2 + sx(i)*sx(i)
    end do

  end function snrm2

!=============================================================================80
!
!  Take the dot product of two vectors
!
!     forms the dot product of two vectors.
!     uses unrolled loops for increments equal to one.
!     Jack Dongarra, linpack, 3/11/78.
!
!=============================================================================80

  function sdot( n, sx, incx, sy, incy )

    use kinddefs, only : dp

    real(dp) :: sdot

    integer,                            intent(in) :: n, incx, incy
    real(dp), dimension(n*max(1,incx)), intent(in) :: sx
    real(dp), dimension(n*max(1,incy)), intent(in) :: sy

    integer :: i, ix, iy, m, mp1

    real(dp) :: stemp

  continue

    stemp = 0.0_dp
    sdot = 0.0_dp
    if(n <= 0)return
    if(incx == 1.and.incy == 1)go to 20

!   code for unequal increments or equal increments
!   not equal to 1

    ix = 1
    iy = 1
    if(incx < 0)ix = (-n+1)*incx + 1
    if(incy < 0)iy = (-n+1)*incy + 1
    do i = 1,n
      stemp = stemp + sx(ix)*sy(iy)
      ix = ix + incx
      iy = iy + incy
    end do
    sdot = stemp

    return

!   code for both increments equal to 1

!   clean-up loop

20  m = mod(n,5)
    if( m  ==  0 ) go to 40
    do i = 1,m
      stemp = stemp + sx(i)*sy(i)
    end do
    if( n  <  5 ) go to 60
40  mp1 = m + 1
    do i = mp1,n,5
      stemp = stemp + sx(i)*sy(i) + sx(i + 1)*sy(i + 1)                        &
                    + sx(i + 2)*sy(i + 2) + sx(i + 3)*sy(i + 3)                &
                    + sx(i + 4)*sy(i + 4)
    end do
60  sdot = stemp

  end function sdot

!================================== DAXPY ====================================80
!
! Compute a constant times a vector plus a vector.
!
!***library   slatec (blas)
!***category  d1a7
!***type      double precision (saxpy-s, daxpy-d, caxpy-c)
!***keywords  blas, linear algebra, triad, vector
!***author  lawson, c. l., (jpl)
!           hanson, r. j., (snla)
!           kincaid, d. r., (u. of texas)
!           krogh, f. t., (jpl)
!***description
!
!                b l a s  subprogram
!    description of parameters
!
!     --input--
!        n  number of elements in input vector(s)
!       da  double precision scalar multiplier
!       dx  double precision vector with n elements
!     incx  storage spacing between elements of dx
!       dy  double precision vector with n elements
!     incy  storage spacing between elements of dy

!     --output--
!       dy  double precision result (unchanged if n  <=  0)

!     overwrite double precision dy with double precision da*dx + dy.
!     for i = 0 to n-1, replace  dy(ly+i*incy) with da*dx(lx+i*incx) +
!       dy(ly+i*incy),
!     where lx = 1 if incx  >=  0, else lx = 1+(1-n)*incx, and ly is
!     defined in a similar way using incy.
!
!***references  c. l. lawson, r. j. hanson, d. r. kincaid and f. t.
!                 krogh, basic linear algebra subprograms for fortran
!                 usage, algorithm no. 539, transactions on mathematical
!                 software 5, 3 (september 1979), pp. 308-323.
!***routines called  (none)
!
!=============================================================================80

  subroutine daxpy( n, da, dx, incx, dy, incy )

    use kinddefs, only : dp

    integer,                intent(in)    :: n, incx, incy
    real(dp),               intent(in)    :: da
    real(dp), dimension(n), intent(in)    :: dx
    real(dp), dimension(n), intent(inout) :: dy

    integer :: ix, iy, i, m, mp1, ns

    continue

    if (n <= 0 .or. da == 0.0_dp) return

! unequal or non-positive increments

    if ( ( incx /= incy ) .or. ( incx <= 0 ) ) then

      ix = 1
      iy = 1
      if (incx  <  0) ix = (-n+1)*incx + 1
      if (incy  <  0) iy = (-n+1)*incy + 1
      do i = 1,n
        dy(iy) = dy(iy) + da*dx(ix)
        ix = ix + incx
        iy = iy + incy
      end do

! both unit increments

    elseif ( incx == 1 ) then

!     clean-up loop so remaining vector length is a multiple of 4.
      m = mod(n,4)
      if (m  ==  0) go to 40
      do i = 1,m
        dy(i) = dy(i) + da*dx(i)
      end do
      if (n  <  4) return

40    mp1 = m + 1
      do i = mp1,n,4
        dy(i) = dy(i) + da*dx(i)
        dy(i+1) = dy(i+1) + da*dx(i+1)
        dy(i+2) = dy(i+2) + da*dx(i+2)
        dy(i+3) = dy(i+3) + da*dx(i+3)
      end do

! equal, positive, non-unit increments

    else

      ns = n*incx
      do i = 1,ns,incx
        dy(i) = da*dx(i) + dy(i)
      end do

    endif

    return

  end subroutine daxpy

!================================== DCOPY ====================================80
!
! Copy a vector.
!
!***library   slatec (blas)
!***category  d1a5
!***type      double precision (scopy-s, dcopy-d, ccopy-c, icopy-i)
!***keywords  blas, copy, linear algebra, vector
!***author  lawson, c. l., (jpl)
!           hanson, r. j., (snla)
!           kincaid, d. r., (u. of texas)
!           krogh, f. t., (jpl)
!***description
!
!                b l a s  subprogram
!    description of parameters
!
!     --input--
!        n  number of elements in input vector(s)
!       dx  double precision vector with n elements
!     incx  storage spacing between elements of dx
!       dy  double precision vector with n elements
!     incy  storage spacing between elements of dy
!
!     --output--
!       dy  copy of vector dx (unchanged if n  <=  0)
!
!     copy double precision dx to double precision dy.
!     for i = 0 to n-1, copy dx(lx+i*incx) to dy(ly+i*incy),
!     where lx = 1 if incx  >=  0, else lx = 1+(1-n)*incx, and ly is
!     defined in a similar way using incy.
!
!***references  c. l. lawson, r. j. hanson, d. r. kincaid and f. t.
!                 krogh, basic linear algebra subprograms for fortran
!                 usage, algorithm no. 539, transactions on mathematical
!                 software 5, 3 (september 1979), pp. 308-323.
!***routines called  (none)
!
!=============================================================================80

  subroutine dcopy( n, dx, incx, dy, incy )

    use kinddefs, only : dp

    integer,                intent(in)  :: n, incx, incy
    real(dp), dimension(n), intent(in)  :: dx
    real(dp), dimension(n), intent(out) :: dy

    integer :: ix, iy, i, m, mp1, ns

    continue

    if (n  <=  0) return

! unequal or nonpositive increments

    if ( ( incx /= incy ) .or. ( incx <= 0 ) ) then

      ix = 1
      iy = 1
      if (incx  <  0) ix = (-n+1)*incx + 1
      if (incy  <  0) iy = (-n+1)*incy + 1
      do i = 1,n
        dy(iy) = dx(ix)
        ix = ix + incx
        iy = iy + incy
      end do

! both unit increments

    elseif ( incx == 1 ) then

!     clean-up loop so remaining vector length is a multiple of 7.

      m = mod(n,7)
      if (m  ==  0) go to 40
      do i = 1,m
        dy(i) = dx(i)
      end do
      if (n  <  7) return

40    mp1 = m + 1
      do i = mp1,n,7
        dy(i) = dx(i)
        dy(i+1) = dx(i+1)
        dy(i+2) = dx(i+2)
        dy(i+3) = dx(i+3)
        dy(i+4) = dx(i+4)
        dy(i+5) = dx(i+5)
        dy(i+6) = dx(i+6)
      end do

! equal, positive, non-unit increments

    else

      ns = n*incx
      do i = 1,ns,incx
        dy(i) = dx(i)
      end do

    endif

    return

  end subroutine dcopy


!=================================== DDOT ====================================80
!
!  Distributed version of blas DDOT for SPARSKIT
!
!=============================================================================80
  function distdot(n,x,ix,y,iy)

    use kinddefs, only : dp
    use lmpi,     only : lmpi_reduce, lmpi_bcast, lmpi_die

    integer, intent(in) :: n, ix, iy

    real(dp) :: distdot

    real(dp), dimension(1+(n-1)*ix), intent(in) :: x
    real(dp), dimension(1+(n-1)*iy), intent(in) :: y

    real(dp) :: temp, tempg

    interface
      function ddot(n,dx,incx,dy,incy)
        use kinddefs, only : dp
        integer, intent(in) :: n, incx, incy
        real(dp), dimension(n*incx), intent(in) :: dx
        real(dp), dimension(n*incy), intent(in) :: dy
        real(dp) :: ddot
      end function ddot
    end interface

  continue

    if ( ix /= 1 .or. iy /= 1 ) then
      write(*,*) 'function distdot not implemented for non-unit increments.'
      call lmpi_die
      stop
    endif

    temp = ddot(n,x,ix,y,iy)

    call lmpi_reduce(temp,tempg)
    call lmpi_bcast(tempg)

    distdot = tempg

  end function distdot

!=================================== DDOT ====================================80
!
! Compute the inner product of two vectors.
!
!***library   slatec (blas)
!***category  d1a4
!***type      double precision (sdot-s, ddot-d, cdotu-c)
!***keywords  blas, inner product, linear algebra, vector
!***author  lawson, c. l., (jpl)
!           hanson, r. j., (snla)
!           kincaid, d. r., (u. of texas)
!           krogh, f. t., (jpl)
!***description
!
!                b l a s  subprogram
!    description of parameters
!
!     --input--
!        n  number of elements in input vector(s)
!       dx  double precision vector with n elements
!     incx  storage spacing between elements of dx
!       dy  double precision vector with n elements
!     incy  storage spacing between elements of dy
!
!     --output--
!     ddot  double precision dot product (zero if n  <=  0)
!
!     returns the dot product of double precision dx and dy.
!     ddot = sum for i = 0 to n-1 of  dx(lx+i*incx) * dy(ly+i*incy),
!     where lx = 1 if incx  >=  0, else lx = 1+(1-n)*incx, and ly is
!     defined in a similar way using incy.
!
!***references  c. l. lawson, r. j. hanson, d. r. kincaid and f. t.
!                 krogh, basic linear algebra subprograms for fortran
!                 usage, algorithm no. 539, transactions on mathematical
!                 software 5, 3 (september 1979), pp. 308-323.
!***routines called  (none)
!
!=============================================================================80

  function ddot( n, dx, incx, dy, incy )

    use kinddefs, only : dp

    real(dp) :: ddot

    integer,                     intent(in) :: n, incx, incy
    real(dp), dimension(n*incx), intent(in) :: dx
    real(dp), dimension(n*incy), intent(in) :: dy

    integer :: ix, iy, i, m, mp1, ns

    continue

    ddot = 0.0_dp
    if (n  <=  0) return

!   unequal or non-positive increments

    if ( ( incx /= incy) .or. ( incx <= 0 ) ) then

      ix = 1
      iy = 1
      if (incx  <  0) ix = (-n+1)*incx + 1
      if (incy  <  0) iy = (-n+1)*incy + 1
      do i = 1,n
        ddot = ddot + dx(ix)*dy(iy)
        ix = ix + incx
        iy = iy + incy
      end do

!   both unit increments

    elseif ( incx == 1 ) then

!     clean-up loop so remaining vector length is a multiple of 5.

      m = mod(n,5)
      if (m  ==  0) go to 40
      do i = 1,m
        ddot = ddot + dx(i)*dy(i)
      end do
      if (n  <  5) return

40    mp1 = m + 1
      do i = mp1,n,5
        ddot = ddot + dx(i)*dy(i) + dx(i+1)*dy(i+1) + dx(i+2)*dy(i+2) +        &
                      dx(i+3)*dy(i+3) + dx(i+4)*dy(i+4)
      end do

!   equal, positive, non-unit increments

    else
      ns = n*incx
      do i = 1,ns,incx
        ddot = ddot + dx(i)*dy(i)
      end do

    end if

    return

  end function ddot

!================================== DNRM2 ====================================80
!
! Compute the euclidean length (l2 norm) of a vector.
!
!=============================================================================80

  function dnrm2( n, dx, incx )

    use kinddefs, only : dp

    real(dp) :: dnrm2

    integer,                intent(in) :: n, incx
    real(dp), dimension(n), intent(in) :: dx

    real(dp) :: acc

    real(dp), parameter :: zero = 0.0_dp

    integer :: j

  continue

! Major hack by Eric:
! f95 compilers hate the assign statements used in the
! original version of this routine, so I'm going to just
! compute the norm the simplest possible way.  If incx
! ever comes in as something other than 1, then shut down,
! because I have not taken that case into account here.

    if ( incx /= 1 ) then
      write(*,*) 'dnrm2 cannot handle incx /= 1...stopping.'
      stop ! FIXME: should be lmpi_die or se_exit(1)?
    end if

    acc = zero

    do j = 1, n
      acc = acc + dx(j)**2
    end do
    dnrm2 = sqrt(acc)

  end function dnrm2

!================================== DSCAL ====================================80
!
! Multiply a vector by a constant.
!
!***library   slatec (blas)
!***category  d1a6
!***type      double precision (sscal-s, dscal-d, cscal-c)
!***keywords  blas, linear algebra, scale, vector
!***author  lawson, c. l., (jpl)
!           hanson, r. j., (snla)
!           kincaid, d. r., (u. of texas)
!           krogh, f. t., (jpl)
!***description
!
!                b l a s  subprogram
!    description of parameters
!
!     --input--
!        n  number of elements in input vector(s)
!       da  double precision scale factor
!       dx  double precision vector with n elements
!     incx  storage spacing between elements of dx
!
!     --output--
!       dx  double precision result (unchanged if n <= 0)

!     replace double precision dx by double precision da*dx.
!     for i = 0 to n-1, replace dx(ix+i*incx) with  da * dx(ix+i*incx),
!     where ix = 1 if incx  >=  0, else ix = 1+(1-n)*incx.
!
!***references  c. l. lawson, r. j. hanson, d. r. kincaid and f. t.
!                 krogh, basic linear algebra subprograms for fortran
!                 usage, algorithm no. 539, transactions on mathematical
!                 software 5, 3 (september 1979), pp. 308-323.
!***routines called  (none)
!
!=============================================================================80

  subroutine dscal( n, da, dx, incx )

    use kinddefs, only : dp

    integer,                     intent(in)    :: n, incx
    real(dp),                    intent(in)    :: da
    real(dp), dimension(n*incx), intent(inout) :: dx

    integer :: i, ix, m, mp1

    continue

    if (n  <=  0) return

!   non-unity increment

    if (incx  /=  1) then

      ix = 1
      if (incx  <  0) ix = (-n+1)*incx + 1
      do i = 1,n
        dx(ix) = da*dx(ix)
        ix = ix + incx
      end do

!   unity increment

    else

!     clean-up loop so remaining vector length is a multiple of 5.

      m = mod(n,5)
      if (m  ==  0) goto 40
      do i = 1,m
        dx(i) = da*dx(i)
      end do
      if (n  <  5) return

40    mp1 = m + 1
      do i = mp1,n,5
        dx(i) = da*dx(i)
        dx(i+1) = da*dx(i+1)
        dx(i+2) = da*dx(i+2)
        dx(i+3) = da*dx(i+3)
        dx(i+4) = da*dx(i+4)
      end do

    end if

    return

  end subroutine dscal

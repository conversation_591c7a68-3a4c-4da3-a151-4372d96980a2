program putcost

  implicit none

  integer                            :: nbound,i,idum
  integer, dimension(:), allocatable :: bctype

  real,    dimension(:), allocatable :: cl,cd,cmx,cmy,cmz,cx,cy,cz
  real,    dimension(:), allocatable :: clp,cdp,cmxp,cmyp,cmzp,cxp,cyp,czp
  real,    dimension(:), allocatable :: clv,cdv,cmxv,cmyv,cmzv,cxv,cyv,czv

  real                     :: clpt,cdpt,cmxpt,cmypt,cmzpt,cxpt,cypt,czpt
  real                     :: clvt,cdvt,cmxvt,cmyvt,cmzvt,cxvt,cyvt,czvt
  real                     :: clt,cdt,cmxt,cmyt,cmzt,cxt,cyt,czt
  character(len=100)       :: project,filename

  continue

  open(78,file='ginput.faces', status='old')
  read(78,*)    ! Title
  read(78,*)    ! IHANE
  read(78,*)    ! <PERSON><PERSON><PERSON>
  read(78,*)    ! XMACH
  read(78,*)    ! XMACH
  read(78,*)    ! SREF
  read(78,*)    ! SREF
  read(78,*)    ! NCYC
  read(78,*)    ! NCYC
  read(78,*)    ! IREST
  read(78,*)    ! IREST
  read(78,*)    ! FILES
  read(78,*) project
  close(78)

  filename = project(1:len_trim(project)) // '.forces.clean'

  open(79,file=filename, status='old')

  read(79,*) nbound

  allocate(bctype(nbound))
  allocate(cl(nbound))
  allocate(cd(nbound))
  allocate(cmx(nbound))
  allocate(cmy(nbound))
  allocate(cmz(nbound))
  allocate(cx(nbound))
  allocate(cy(nbound))
  allocate(cz(nbound))

  allocate(clp(nbound))
  allocate(cdp(nbound))
  allocate(cmxp(nbound))
  allocate(cmyp(nbound))
  allocate(cmzp(nbound))
  allocate(cxp(nbound))
  allocate(cyp(nbound))
  allocate(czp(nbound))

  allocate(clv(nbound))
  allocate(cdv(nbound))
  allocate(cmxv(nbound))
  allocate(cmyv(nbound))
  allocate(cmzv(nbound))
  allocate(cxv(nbound))
  allocate(cyv(nbound))
  allocate(czv(nbound))


  do i = 1, nbound
    read(79,*) idum
    read(79,*) bctype(i)
    read(79,*)                              ! Pressure forces
    read(79,*) clp(i), cdp(i)
    read(79,*) cmxp(i), cmyp(i), cmzp(i)
    read(79,*) cxp(i), cyp(i), czp(i)
    read(79,*)                              ! Viscous forces
    read(79,*) clv(i), cdv(i)
    read(79,*) cmxv(i), cmyv(i), cmzv(i)
    read(79,*) cxv(i), cyv(i), czv(i)
    read(79,*)                              ! Total forces
    read(79,*) cl(i), cd(i)
    read(79,*) cmx(i), cmy(i), cmz(i)
    read(79,*) cx(i), cy(i), cz(i)
  end do

  read(79,*)                                ! Totals for all boundaries
  read(79,*)                                ! Pressure forces
  read(79,*) clpt, cdpt
  read(79,*) cmxpt, cmypt, cmzpt
  read(79,*) cxpt, cypt, czpt
  read(79,*)                                ! Viscous forces
  read(79,*) clvt, cdvt
  read(79,*) cmxvt, cmyvt, cmzvt
  read(79,*) cxvt, cyvt, czvt
  read(79,*)                                ! Total forces
  read(79,*) clt, cdt
  read(79,*) cmxt, cmyt, cmzt
  read(79,*) cxt, cyt, czt

  close(79)

  call readwrite(clt,cdt,cmyt)

end program putcost


subroutine readwrite(cl,cd,cm)

  implicit none

  integer nelem,ncon,nobj,nside,imach,ialpha,ishape,ndv
  integer idum,i

  real cost,domega1,domega2,domega3,domega4
  real clstar,cdstar,cmstar
  real cl,cd,cm
  real rdum1,rdum2,rdum3,rdum4

  character(100)       :: line

  continue

  open(87,file='rubber.data')
  open(88,file='rubber.data.temp')

  read(87,*)
  read(87,*) nelem,ncon,nobj,nside,imach,ialpha,ishape
  read(87,*)
  read(87,*) ndv
  read(87,*)
  do i = 1, ndv
    read(87,*)
  end do
  read(87,*)
  read(87,*) idum, cost
  read(87,*)
  do i = 1, ndv
    read(87,*)
  end do
  read(87,*)
  read(87,*)
  read(87,*) domega1,domega2,domega3,domega4
  read(87,*)
  read(87,*) clstar,cdstar,cmstar

  rewind(87)

  cost = domega3*0.5*(cl-clstar)**2 + domega1*0.5*(cd-cdstar)**2

  read(87,*)
  write(88,'(a)') &
    '    NBODIES    NCONST      NOBJ     NSIDE     IMACH     IALPHA   ISHAPE'

  read(87,*) nelem,ncon,nobj,nside,imach,ialpha,ishape
  write(88,'(1x,10(i10))') nelem,ncon,nobj,nside,imach,ialpha,ishape

  read(87,*)
  write(88,'(a)') '        NDV'

  read(87,*) ndv
  write(88,'(1x,10(i10))') ndv

  read(87,*)
  write(88,'(a)') '  Design Variables   Lower    Upper    Scale'

  do i = 1, ndv
    read(87,*) idum, rdum1, rdum2, rdum3, rdum4
    write(88,'(i7,1x,4(e19.10))') idum, rdum1, rdum2, rdum3, rdum4
  end do

  read(87,*)
  write(88,'(a)') '  Objective Functions'

  read(87,*) idum,rdum1
  write(88,'(i7,1x,4(e19.10))') idum,cost

  read(87,*)
  write(88,'(a)') '  Gradients of Design Variables for element 1'

  do i = 1, ndv
    read(87,*) idum, rdum1
    write(88,'(i7,1x,4(e19.10))') idum, rdum1
  end do

  read(87,*)
  write(88,'(a)') '   Functions: Omega1    Omega2     Omega3     Omega4'

  read(87,*)
  write(88,'(a)') '             (cd-cdd)^2    (cp-cpd)^2  (cl-cld)^2    cd/cl'

  read(87,*)
  write(88,'(15x,4(f12.5))') domega1,domega2,domega3,domega4

  read(87,*)
  write(88,'(a)') ' Target values: clstar       cdstar       cmstar'

  read(87,*)
  write(88,'(15x,4(f12.5))') clstar,cdstar,cmstar

  read(87,*)
  write(88,'(a)') ' Aerodynamic coefficients: cltot      cdtot     cmtot'

  read(87,*)
  write(88,'(15x,4(f12.5))') cl,cd,cm

  close(87)
  close(88)

end subroutine readwrite

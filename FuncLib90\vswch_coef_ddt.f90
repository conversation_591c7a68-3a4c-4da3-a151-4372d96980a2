!============================== VSWCH_COEF ===================================80
!
!  Computes the viscous switching coefficient used turn off the inviscid
!  switching coefficient on cell faces that have small cell reynolds numbers
!
!=============================================================================80

  pure function vswch_coef_ddt(roewat, rho,                                    &
                           q2l, ubarl, q2r, ubarr, ubar, c,                    &
                           vol1, vol2, area, power, MU_face)

    use kinddefs,        only : dp
    use ddt,             only : ddt5, assignment(=),                           &
      ddt_min, ddt_max, ddt_abs, ddt_sqrt,                                     &
      operator(+), operator(-), operator(*), operator(/), operator(**),        &
      operator(<=)
    use fun3d_constants, only : my_0, my_1, my_half

    integer,  intent(in) :: power

    type(ddt5), intent(in) :: roewat
    type(ddt5), intent(in) :: rho
    type(ddt5), intent(in) :: q2l, ubarl, q2r, ubarr, ubar, c
    real(dp),   intent(in) :: vol1, vol2, area
    real(dp),   intent(in) :: MU_face

    type(ddt5)             :: vswch_coef_ddt

    type(ddt5) :: utngl, utngr, utang
    type(ddt5) :: RO_face, U_face, L_face, RE_face

    real(dp), parameter :: RE_min  =    50.0_dp
    real(dp), parameter :: RE_max  =   500.0_dp

  continue

    RO_face = rho
    utngl   = ddt_sqrt(ddt_max(my_0, q2l-ubarl*ubarl))
    utngr   = ddt_sqrt(ddt_max(my_0, q2r-ubarr*ubarr))
    utang   = utngl*roewat + utngr*(my_1 - roewat)
    U_face  = ddt_max(ddt_abs(ubar), utang) + c
    L_face  = my_half*(vol1 + vol2)/area

!   Compute the cell face reynolds number to make the coeff. behave as desired
!   vcoef = f(RE-face) when RE_min <= RE_face >= RE_max

    RE_face = RO_face*U_face*L_face/MU_face

!   Compute the viscous scaling coeff. such that the dissipation of the inviscid
!   scheme is minimized when the cell face Reynolds number is lower than RE_min
!   vswch_coef_ddt = f(RE-face) when RE_min <= RE_face >= RE_max

    vswch_coef_ddt = &
      ddt_max(my_0,ddt_min(my_1,(RE_face-RE_min)/(RE_max-RE_min)))**power
    vswch_coef_ddt = my_1 - vswch_coef_ddt

  end function vswch_coef_ddt

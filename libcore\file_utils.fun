!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite file_utils

integer :: au_start = 100

test available_unit_behaves
  assert_equal( au_start, available_unit() )
  open( au_start, status='scratch' )
  assert_equal( au_start+1, available_unit() )
  close( au_start )
  assert_equal( au_start, available_unit() )
end test

test available_units_nominal_cases
  assert_equal( au_start, available_units(1)  )
  assert_equal( au_start, available_units(10) )
end test

test available_units_checks_full_range
  open( au_start+99, status='scratch')
  assert_equal( au_start+100, available_units(100) )
  close( au_start+99 )
end test

test available_unit_range_error_code
  integer :: max_int = huge(1)
  assert_equal(  -1, available_units(max_int) )
end test

test cp_behaves
  call touch('sample_file')
  call cp('sample_file','sample_file_new')
  assert_true( file_exists('sample_file') )
  assert_true( file_exists('sample_file_new') )
end test

test file_does_not_exist_behaves
  assert_true( file_does_not_exist('sample_file') )
  call touch('sample_file')
  assert_false( file_does_not_exist('sample_file') )
end test

test file_exists_behaves
  call touch('sample_file')
  assert_true( file_exists('sample_file') )
  assert_false( file_exists('non-existent_file') )
end test

test rm_behaves

  assert_true( file_does_not_exist('non-existent_file') )
  call rm( 'non-existent_file' )
  assert_true( file_does_not_exist('non-existent_file') )

  call touch('sample_file')
  assert_true( file_exists('sample_file') )
  call rm( 'sample_file' )
  assert_true( file_does_not_exist('sample_file') )

end test

test touch_behaves
  assert_true( file_does_not_exist('sample_file') )
  call touch('sample_file')
  assert_true( file_exists('sample_file') )
end test

teardown
  call rm('sample_file')
  call rm('sample_file_new')
end teardown

end test_suite

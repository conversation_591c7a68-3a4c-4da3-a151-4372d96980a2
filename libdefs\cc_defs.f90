module cc_defs

  use kinddefs,          only : dp

  implicit none

  private

  public :: cfl_viscous_terms_in_def
  public :: cfl_skew_deg_cutoff
  public :: cfl_reduced_value_high_skew
  public :: ccrecon, cc_setup_augment
  public :: lsq_face, wlsq_face, lsq_face_stencil, lsq_face_bc, clsq_bc
  public :: lsq_face_bounded, lsq_face_edge_based, revert_null
  public :: no_bounded_lsqfs
  public :: cc_full_visc_jac, cc_visc_node_avg
  public :: clip_viscous_weights
  public :: allowable_skew_edge_based
  public :: augments_max_per_node

  logical :: cfl_viscous_terms_in_def = .true.

  integer :: cfl_skew_deg_cutoff = 40

  real(dp) :: cfl_reduced_value_high_skew = 1.0_dp

  !...allowable skew angle (degrees) for edge terms on LHS.
  integer :: allowable_skew_edge_based = -90

! SPATIAL DISCRETIZATION - CELL_CENTERED
  integer :: ccrecon         = -1      ! Cell-centered reconstruction type
  logical :: cc_setup_augment       = .true. ! Set up augment in Party
! logical :: cc              = .false. ! Cell-centered flag

! logical :: cc_clip_weights = .true.  ! Clip weights if cc node avegaging
  logical :: clip_viscous_weights  = .false. ! Clip weights for qT variables.
! integer :: augment_kludge1 = 0       ! Kludge for smart boundary augment
! integer :: augment_kludge2 = 0       ! Kludge for smart boundary augment

  logical :: lsq_face             = .true. ! Face-based lsq for viscous RHS
  logical :: lsq_face_bounded     = .false.! Bounded face-based lsq
  logical :: revert_null          = .false.! Revert to null if out-of-bounds
  logical :: lsq_face_edge_based  = .false.! Edge based face-based lsq
  logical :: cc_visc_node_avg = .false.   ! Node averaging for viscous RHS
  logical :: cc_full_visc_jac = .true.    ! Use full viscous LHS linearization
  integer :: lsq_face_stencil = -1        ! Scheme for choosing lsq_face stencil
  logical :: lsq_face_bc      = .true.    ! Incorporate bc into lsq_face

  logical :: clsq_bc      = .false.    ! Incorporate bc into clsq

  !Weighted lsq for face-based and mapped lsq path, if .true.
  logical :: wlsq_face = .false.

  integer :: no_bounded_lsqfs = 0  ! number of unbounded least squares face

  !maximum number of cell-centered lsq augments per node at interior nodes:
  !...=1000 => large stencil equivalent in complexity to node-averaging.
  !...=1,   => smaller stencil with equivalent benefits.
  integer :: augments_max_per_node = 1

end module cc_defs


pure function in_primitive_variables(state)

  use kinddefs,        only : dp
  use fluid,           only : gm1

  real(dp), dimension(5), intent(in) :: state

  real(dp), dimension(5)             :: in_primitive_variables

  continue

  in_primitive_variables(1)   = state(1)
  in_primitive_variables(2:4) = state(2:4) / in_primitive_variables(1)
  in_primitive_variables(5)   =                                                &
     gm1 * ( state(5) - 0.5_dp * in_primitive_variables(1) *                   &
                 ( in_primitive_variables(2)**2                                &
                 + in_primitive_variables(3)**2                                &
                 + in_primitive_variables(4)**2 ) )

  end function in_primitive_variables

module av2p0_lapack_util

  use kinddefs,            only : dp

  implicit none

  private

  public :: xerbla, claswp, icamax
  public :: cgemm, cgemv, cgeru, ctrtri
  public :: ilaenv, lsame
  public :: cscal, cswap, ctrsm


contains


subroutine CLASWP(N,A,LDA,K1,K2,IPIV,INCX)
  !
  !  -- LAPACK auxiliary routine (version 2.0) --
  !     Univ. of Tennessee, Univ. of California Berkeley, NAG Ltd.,
  !     Courant Institute, Argonne National Lab, and Rice University
  !     October 31, 1992

  integer, intent(in) :: INCX,K1,K2,LDA,N
  integer, dimension(*), intent(in) :: IPIV
  complex(dp), dimension(LDA,*), intent(inout) :: A
  !

  integer :: I,IP,IX
  !

  !
  !     Interchange row I with row IPIV(I) for each of rows K1 through K2.
  !
  if (INCX == 0) return
  if (INCX > 0) then
    IX = K1
  else
    IX = 1 + (1-K2)*INCX
  end if
  !
  if (INCX == 1) then
    do I = K1,K2
      IP = IPIV(I)
      if (IP /= I) then
        call CSWAP(N,A(I,1),LDA,A(IP,1),LDA)
      end if
    end do
  elseif (INCX > 1) then
    do I = K1,K2
      IP = IPIV(IX)
      if (IP /= I) then
        call CSWAP(N,A(I,1),LDA,A(IP,1),LDA)
      end if
      IX = IX + INCX
    end do
  elseif (INCX < 0) then
    do I = K2,K1,-1
      IP = IPIV(IX)
      if (IP /= I) then
        call CSWAP(N,A(I,1),LDA,A(IP,1),LDA)
      end if
      IX = IX + INCX
    end do
  end if
!
!     End of CLASWP
!
end subroutine CLASWP
function ILAENV(ISPEC,NAME,N1,N2,N4)
  !
  !  -- LAPACK auxiliary routine (version 2.0) --
  !     Univ. of Tennessee, Univ. of California Berkeley, NAG Ltd.,
  !     Courant Institute, Argonne National Lab, and Rice University
  !     September 30, 1994
  !

  character(LEN=*), intent(in) :: NAME
  integer,          intent(in) :: ISPEC,N1,N2,N4

  integer :: ILAENV

  character :: C1
  character(LEN=2) :: C2,C4
  character(LEN=3) :: C3
  character(LEN=6) :: SUBNAM
  logical :: CNAME,SNAME
  integer :: I,IC,IZ,NB,NBMIN,NX

  select case (ISPEC)
  case(1,2,3)
    !
    !     Convert NAME to upper case if the first character is lower case.
    !
    !
    ILAENV = 1
    SUBNAM = NAME
    IC = ICHAR(SUBNAM(1:1))
    IZ = ICHAR("Z")
    if (IZ==90 .or. IZ==122) then
      !
      !        ASCII character set
      !
      if (IC>=97 .and. IC<=122) then
        SUBNAM(1:1) = CHAR(IC-32)
        do I = 2,6
          IC = ICHAR(SUBNAM(I:I))
          if (IC>=97 .and. IC<=122) then
            SUBNAM(I:I) = CHAR(IC-32)
          end if
        end do
      end if
    !
    elseif (IZ==233 .or. IZ==169) then
      !
      !        EBCDIC character set
      !
      if ((IC>=129.and.IC<=137) .or. (IC>=145.and.IC<=153) .or. &
          (IC>=162.and.IC<=169)) then
        SUBNAM(1:1) = CHAR(IC+64)
        do I = 2,6
          IC = ICHAR(SUBNAM(I:I))
          if ((IC>=129.and.IC<=137) .or. (IC>=145.and.IC<=153) .or. &
              (IC>=162.and.IC<=169)) then
            SUBNAM(I:I) = CHAR(IC+64)
          end if
        end do
      end if
    !
    elseif (IZ==218 .or. IZ==250) then
      !
      !        Prime machines:  ASCII+128
      !
      if (IC>=225 .and. IC<=250) then
        SUBNAM(1:1) = CHAR(IC-32)
        do I = 2,6
          IC = ICHAR(SUBNAM(I:I))
          if (IC>=225 .and. IC<=250) then
            SUBNAM(I:I) = CHAR(IC-32)
          end if
        end do
      end if
    end if
    !
    C1 = SUBNAM(1:1)
    SNAME = C1=="S" .or. C1=="D"
    CNAME = C1=="C" .or. C1=="Z"
    if (.not. (CNAME.or.SNAME)) return
    C2 = SUBNAM(2:3)
    C3 = SUBNAM(4:6)
    C4 = C3(2:3)
    !
    select case (ISPEC)
    case(2)
      !
      !     ISPEC = 2:  minimum block size
      !
      !
      NBMIN = 2
      if (C2 == "GE") then
        if (C3=="QRF" .or. C3=="RQF" .or. C3=="LQF" .or. C3=="QLF") then
          if (SNAME) then
            NBMIN = 2
          else
            NBMIN = 2
          end if
        elseif (C3 == "HRD") then
          if (SNAME) then
            NBMIN = 2
          else
            NBMIN = 2
          end if
        elseif (C3 == "BRD") then
          if (SNAME) then
            NBMIN = 2
          else
            NBMIN = 2
          end if
        elseif (C3 == "TRI") then
          if (SNAME) then
            NBMIN = 2
          else
            NBMIN = 2
          end if
        end if
      elseif (C2 == "SY") then
        if (C3 == "TRF") then
          if (SNAME) then
            NBMIN = 8
          else
            NBMIN = 8
          end if
        elseif (SNAME .and. C3=="TRD") then
          NBMIN = 2
        end if
      elseif (CNAME .and. C2=="HE") then
        if (C3 == "TRD") then
          NBMIN = 2
        end if
      elseif (SNAME .and. C2=="OR") then
        if (C3(1:1) == "G") then
          if (C4=="QR" .or. C4=="RQ" .or. C4=="LQ" &
                       .or. C4=="QL" .or. C4=="HR" &
                       .or. C4=="TR" .or. C4=="BR") then
            NBMIN = 2
          end if
        elseif (C3(1:1) == "M") then
          if (C4=="QR" .or. C4=="RQ" .or. C4=="LQ" &
                       .or. C4=="QL" .or. C4=="HR" &
                       .or. C4=="TR" .or. C4=="BR") then
            NBMIN = 2
          end if
        end if
      elseif (CNAME .and. C2=="UN") then
        if (C3(1:1) == "G") then
          if (C4=="QR" .or. C4=="RQ" .or. C4=="LQ" &
                       .or. C4=="QL" .or. C4=="HR" &
                       .or. C4=="TR" .or. C4=="BR") then
            NBMIN = 2
          end if
        elseif (C3(1:1) == "M") then
          if (C4=="QR" .or. C4=="RQ" .or. C4=="LQ" &
                       .or. C4=="QL" .or. C4=="HR" &
                       .or. C4=="TR" .or. C4=="BR") then
            NBMIN = 2
          end if
        end if
      end if
      ILAENV = NBMIN
    case(3)
      !
      !     ISPEC = 3:  crossover point
      !
      !
      NX = 0
      if (C2 == "GE") then
        if (C3=="QRF" .or. C3=="RQF" .or. C3=="LQF" .or. C3=="QLF") then
          if (SNAME) then
            NX = 128
          else
            NX = 128
          end if
        elseif (C3 == "HRD") then
          if (SNAME) then
            NX = 128
          else
            NX = 128
          end if
        elseif (C3 == "BRD") then
          if (SNAME) then
            NX = 128
          else
            NX = 128
          end if
        end if
      elseif (C2 == "SY") then
        if (SNAME .and. C3=="TRD") then
          NX = 1
        end if
      elseif (CNAME .and. C2=="HE") then
        if (C3 == "TRD") then
          NX = 1
        end if
      elseif (SNAME .and. C2=="OR") then
        if (C3(1:1) == "G") then
          if (C4=="QR" .or. C4=="RQ" .or. C4=="LQ" &
                       .or. C4=="QL" .or. C4=="HR" &
                       .or. C4=="TR" .or. C4=="BR") then
            NX = 128
          end if
        end if
      elseif (CNAME .and. C2=="UN") then
        if (C3(1:1) == "G") then
          if (C4=="QR" .or. C4=="RQ" .or. C4=="LQ" &
                       .or. C4=="QL" .or. C4=="HR" &
                       .or. C4=="TR" .or. C4=="BR") then
            NX = 128
          end if
        end if
      end if
      ILAENV = NX
    case default
      !
      !     ISPEC = 1:  block size
      !
      !     In these examples, separate code is provided for setting NB for
      !     real and complex.  We assume that NB will take the same value in
      !     single or double precision.
      !
      !
      NB = 1
      !
      if (C2 == "GE") then
        if (C3 == "TRF") then
          if (SNAME) then
            NB = 64
          else
            NB = 64
          end if
        elseif (C3=="QRF" .or. C3=="RQF" .or. C3=="LQF" .or. C3=="QLF") then
          if (SNAME) then
            NB = 32
          else
            NB = 32
          end if
        elseif (C3 == "HRD") then
          if (SNAME) then
            NB = 32
          else
            NB = 32
          end if
        elseif (C3 == "BRD") then
          if (SNAME) then
            NB = 32
          else
            NB = 32
          end if
        elseif (C3 == "TRI") then
          if (SNAME) then
            NB = 64
          else
            NB = 64
          end if
        end if
      elseif (C2 == "PO") then
        if (C3 == "TRF") then
          if (SNAME) then
            NB = 64
          else
            NB = 64
          end if
        end if
      elseif (C2 == "SY") then
        if (C3 == "TRF") then
          if (SNAME) then
            NB = 64
          else
            NB = 64
          end if
        elseif (SNAME .and. C3=="TRD") then
          NB = 1
        elseif (SNAME .and. C3=="GST") then
          NB = 64
        end if
      elseif (CNAME .and. C2=="HE") then
        if (C3 == "TRF") then
          NB = 64
        elseif (C3 == "TRD") then
          NB = 1
        elseif (C3 == "GST") then
          NB = 64
        end if
      elseif (SNAME .and. C2=="OR") then
        if (C3(1:1) == "G") then
          if (C4=="QR" .or. C4=="RQ" .or. C4=="LQ" &
                       .or. C4=="QL" .or. C4=="HR" &
                       .or. C4=="TR" .or. C4=="BR") then
            NB = 32
          end if
        elseif (C3(1:1) == "M") then
          if (C4=="QR" .or. C4=="RQ" .or. C4=="LQ" &
                       .or. C4=="QL" .or. C4=="HR" &
                       .or. C4=="TR" .or. C4=="BR") then
            NB = 32
          end if
        end if
      elseif (CNAME .and. C2=="UN") then
        if (C3(1:1) == "G") then
          if (C4=="QR" .or. C4=="RQ" .or. C4=="LQ" &
                       .or. C4=="QL" .or. C4=="HR" &
                       .or. C4=="TR" .or. C4=="BR") then
            NB = 32
          end if
        elseif (C3(1:1) == "M") then
          if (C4=="QR" .or. C4=="RQ" .or. C4=="LQ" &
                       .or. C4=="QL" .or. C4=="HR" &
                       .or. C4=="TR" .or. C4=="BR") then
            NB = 32
          end if
        end if
      elseif (C2 == "GB") then
        if (C3 == "TRF") then
          if (SNAME) then
            if (N4 <= 64) then
              NB = 1
            else
              NB = 32
            end if
          elseif (N4 <= 64) then
            NB = 1
          else
            NB = 32
          end if
        end if
      elseif (C2 == "PB") then
        if (C3 == "TRF") then
          if (SNAME) then
            if (N2 <= 64) then
              NB = 1
            else
              NB = 32
            end if
          elseif (N2 <= 64) then
            NB = 1
          else
            NB = 32
          end if
        end if
      elseif (C2 == "TR") then
        if (C3 == "TRI") then
          if (SNAME) then
            NB = 64
          else
            NB = 64
          end if
        end if
      elseif (C2 == "LA") then
        if (C3 == "UUM") then
          if (SNAME) then
            NB = 64
          else
            NB = 64
          end if
        end if
      elseif (SNAME .and. C2=="ST" .and. C3=="EBZ") then
        NB = 1
      end if
      ILAENV = NB
    end select
  case(4)
    !
    !     ISPEC = 4:  number of shifts (used by xHSEQR)
    !
    !
    ILAENV = 6
  case(5)
    !
    !     ISPEC = 5:  minimum column dimension (not used)
    !
    !
    ILAENV = 2
  case(6)
    !
    !     ISPEC = 6:  crossover point for SVD (used by xGELSS and xGESVD)
    !
    !
    ILAENV = INT(REAL( MIN(N1,N2) , dp )*1.6_dp)
  case(7)
    !
    !     ISPEC = 7:  number of processors (not used)
    !
    !
    ILAENV = 1
  case(8)
    !
    !     ISPEC = 8:  crossover point for multishift (used by xHSEQR)
    !
    !
    ILAENV = 50
  case default
    !
    !     Invalid value for ISPEC
    !
    ILAENV = -1
  end select
!
!     End of ILAENV
!
end function ILAENV
subroutine XERBLA(SRNAME,INFO)
  !
  !  -- LAPACK auxiliary routine (version 2.0) --
  !     Univ. of Tennessee, Univ. of California Berkeley, NAG Ltd.,
  !     Courant Institute, Argonne National Lab, and Rice University
  !     September 30, 1994
  !
  !     .. Scalar Arguments ..


  character(LEN=6), intent(in) :: SRNAME
  integer, intent(in) :: INFO
  !     ..
  !
  !  Purpose
  !  =======
  !
  !  XERBLA  is an error handler for the LAPACK routines.
  !  It is called by an LAPACK routine if an input parameter has an
  !  invalid value.  A message is printed and execution stops.
  !
  !  Installers may consider modifying the STOP statement in order to
  !  call system-specific exception-handling facilities.
  !
  !  Arguments
  !  =========
  !
  !  SRNAME  (input) CHARACTER*6
  !          The name of the routine which called XERBLA.
  !
  !  INFO    (input) INTEGER
  !          The position of the invalid parameter in the parameter list
  !          of the calling routine.
  !
  ! =====================================================================
  !
  !     .. Executable Statements ..
  !
  !
  write (*,FMT = 10000) SRNAME, INFO
  stop
  !
  ! ... Format Declarations ...
  !
  !
  10000 format (                                               &
        " ** On entry to ",a6," parameter number ",i2," had ", &
        "an illegal value")
!
!     End of XERBLA
!
end subroutine XERBLA
!

subroutine CTRTI2(UPLO,DIAG,N,A,LDA,INFO)
  !
  !  -- LAPACK routine (version 2.0) --
  !     Univ. of Tennessee, Univ. of California Berkeley, NAG Ltd.,
  !     Courant Institute, Argonne National Lab, and Rice University
  !     September 30, 1994

  complex(dp) :: ONE

  character, intent(in) :: DIAG,UPLO
  integer, intent(in) :: LDA,N
  integer, intent(out) :: INFO
  complex(dp), dimension(LDA,*), intent(inout) :: A

  logical :: NOUNIT,UPPER
  integer :: J
  complex(dp) :: AJJ
  !

  !
  !     Test the input parameters.
  !
  ONE = cmplx(1.0_dp,0.0_dp,dp)
  INFO = 0
  UPPER = LSAME(UPLO,"U")
  NOUNIT = LSAME(DIAG,"N")
  if (.not.UPPER .and. .not.LSAME(UPLO,"L")) then
    INFO = -1
  elseif (.not.NOUNIT .and. .not.LSAME(DIAG,"U")) then
    INFO = -2
  elseif (N < 0) then
    INFO = -3
  elseif (LDA < MAX(1,N)) then
    INFO = -5
  end if
  if (INFO /= 0) then
    call XERBLA("CTRTI2",-INFO)
    stop
  end if
  !
  !
  if (UPPER) then
    !
    !        Compute inverse of upper triangular matrix.
    !
    do J = 1,N
      if (NOUNIT) then
        A(J,J) = ONE / A(J,J)
        AJJ = -A(J,J)
      else
        AJJ = -ONE
      end if
      !
      !           Compute elements 1:j-1 of j-th column.
      !
      call CTRMV("Upper","No transpose",DIAG,J-1,A,LDA,A(1,J),1)
      call CSCAL(J-1,AJJ,A(1,J),1)
    end do
  else
    !
    !        Compute inverse of lower triangular matrix.
    !
    do J = N,1,-1
      if (NOUNIT) then
        A(J,J) = ONE / A(J,J)
        AJJ = -A(J,J)
      else
        AJJ = -ONE
      end if
      if (J < N) then
        !
        !              Compute elements j+1:n of j-th column.
        !
        call CTRMV("Lower","No transpose",DIAG,N-J,A(J+1,J+1),LDA,A(J+1,J),1)
        call CSCAL(N-J,AJJ,A(J+1,J),1)
      end if
    end do
  end if
!
!     End of CTRTI2
!
end subroutine CTRTI2
subroutine CTRTRI(UPLO,DIAG,N,A,LDA,INFO)
  !
  !  -- LAPACK routine (version 2.0) --
  !     Univ. of Tennessee, Univ. of California Berkeley, NAG Ltd.,
  !     Courant Institute, Argonne National Lab, and Rice University
  !     September 30, 1994

  complex(dp) :: ONE
  complex(dp) :: ZERO

  character, intent(in) :: DIAG,UPLO
  integer, intent(in) :: LDA,N
  integer, intent(out) :: INFO
  complex(dp), dimension(LDA,*), intent(inout) :: A

  logical :: NOUNIT,UPPER
  integer :: J,JB,NB,NN
  !

  !
  !     Test the input parameters.
  !
  ONE  = cmplx(1.0_dp,0.0_dp,dp)
  ZERO = cmplx(0.0_dp,0.0_dp,dp)
  INFO = 0
  UPPER = LSAME(UPLO,"U")
  NOUNIT = LSAME(DIAG,"N")
  if (.not.UPPER .and. .not.LSAME(UPLO,"L")) then
    INFO = -1
  elseif (.not.NOUNIT .and. .not.LSAME(DIAG,"U")) then
    INFO = -2
  elseif (N < 0) then
    INFO = -3
  elseif (LDA < MAX(1,N)) then
    INFO = -5
  end if
  if (INFO /= 0) then
    call XERBLA("CTRTRI",-INFO)
    stop
  end if
  !
  !     Quick return if possible
  !
  if (N == 0) return
  !
  !     Check for singularity if non-unit.
  !
  if (NOUNIT) then
    do INFO = 1,N
      if (A(INFO,INFO) == ZERO) return
    end do
    INFO = 0
  end if
  !
  !     Determine the block size for this environment.
  !
  NB = ILAENV(1,"CTRTRI",N,-1,-1)
  !
  if (NB<=1 .or. NB>=N) then
    !
    !        Use unblocked code
    !
    call CTRTI2(UPLO,DIAG,N,A,LDA,INFO)
    return
  end if
  !
  !        Use blocked code
  !
  if (UPPER) then
    !
    !           Compute inverse of upper triangular matrix
    !
    do J = 1,N,NB
      JB = MIN(NB,N-J+1)
      !
      !              Compute rows 1:j-1 of current block column
      !
      call CTRMM("Left","Upper","No transpose",DIAG,J-1,JB,ONE,A,LDA,A(1,J), &
                LDA)
      call CTRSM("Right","Upper","No transpose",DIAG,J-1,JB,-ONE,A(J,J),LDA, &
                A(1,J),LDA)
      !
      !              Compute inverse of current diagonal block
      !
      call CTRTI2("Upper",DIAG,JB,A(J,J),LDA,INFO)
    end do
  else
    !
    !           Compute inverse of lower triangular matrix
    !
    NN = ((N-1)/NB)*NB + 1
    do J = NN,1,-NB
      JB = MIN(NB,N-J+1)
      if (J+JB <= N) then
        !
        !                 Compute rows j+jb:n of current block column
        !
        call CTRMM("Left","Lower","No transpose",DIAG,N-J-JB+1,JB,ONE, &
                  A(J+JB,J+JB),LDA,A(J+JB,J),LDA)
        call CTRSM("Right","Lower","No transpose",DIAG,N-J-JB+1,JB,-ONE, &
                  A(J,J),LDA,A(J+JB,J),LDA)
      end if
      !
      !              Compute inverse of current diagonal block
      !
      call CTRTI2("Lower",DIAG,JB,A(J,J),LDA,INFO)
    end do
  end if
!
!     End of CTRTRI
!
end subroutine CTRTRI
function LSAME(CA,CB)
  !
  !  -- LAPACK auxiliary routine (version 2.0) --
  !     Univ. of Tennessee, Univ. of California Berkeley, NAG Ltd.,
  !     Courant Institute, Argonne National Lab, and Rice University
  !     September 30, 1994

  character, intent(in) :: CA,CB

  logical :: LSAME

  integer :: INTA,INTB,ZCODE

  !
  !     Test if the characters are equal
  !
  LSAME = CA == CB
  if (LSAME) return
  !
  !     Now test for equivalence if both characters are alphabetic.
  !
  ZCODE = ICHAR("Z")
  !
  !     Use 'Z' rather than 'A' so that ASCII can be detected on Prime
  !     machines, on which ICHAR returns a value with bit 8 set.
  !     ICHAR('A') on Prime machines returns 193 which is the same as
  !     ICHAR('A') on an EBCDIC machine.
  !
  INTA = ICHAR(CA)
  INTB = ICHAR(CB)
  !
  if (ZCODE==90 .or. ZCODE==122) then
    !
    !        ASCII is assumed - ZCODE is the ASCII code of either lower or
    !        upper case 'Z'.
    !
    if (INTA>=97 .and. INTA<=122) then
      INTA = INTA - 32
    end if
    if (INTB>=97 .and. INTB<=122) then
      INTB = INTB - 32
    end if
  !
  elseif (ZCODE==233 .or. ZCODE==169) then
    !
    !        EBCDIC is assumed - ZCODE is the EBCDIC code of either lower or
    !        upper case 'Z'.
    !
    if (INTA>=129.and.INTA<=137 .or. INTA>=145.and.INTA<=153 .or. &
        INTA>=162.and.INTA<=169) then
      INTA = INTA + 64
    end if
    if (INTB>=129.and.INTB<=137 .or. INTB>=145.and.INTB<=153 .or. &
        INTB>=162.and.INTB<=169) then
      INTB = INTB + 64
    end if
  !
  elseif (ZCODE==218 .or. ZCODE==250) then
    !
    !        ASCII is assumed, on Prime machines - ZCODE is the ASCII code
    !        plus 128 of either lower or upper case 'Z'.
    !
    if (INTA>=225 .and. INTA<=250) then
      INTA = INTA - 32
    end if
    if (INTB>=225 .and. INTB<=250) then
      INTB = INTB - 32
    end if
  end if
  LSAME = INTA == INTB
!
!     RETURN
!
!     End of LSAME
!
end function LSAME
subroutine CGEMM(TRANSA,TRANSB,M,N,K,ALPHA,A,LDA,B,LDB,BETA,C,LDC)

  complex(dp) :: ONE
  complex(dp) :: ZERO

  character, intent(in) :: TRANSA,TRANSB
  integer, intent(in) :: K,LDA,LDB,LDC,M,N
  complex(dp), intent(in) :: ALPHA,BETA
  complex(dp), dimension(LDA,*), intent(in) :: A
  complex(dp), dimension(LDB,*), intent(in) :: B
  complex(dp), dimension(LDC,*), intent(inout) :: C

  logical :: CONJA,CONJB,NOTA,NOTB
  integer :: I,INFO,J,L,NROWA,NROWB!,NCOLA
  complex(dp) :: TEMP
  !

  !
  !     Set  NOTA  and  NOTB  as  true if  A  and  B  respectively are not
  !     conjugated or transposed, set  CONJA and CONJB  as true if  A  and
  !     B  respectively are to be  transposed but  not conjugated  and set
  !     NROWA, NCOLA and  NROWB  as the number of rows and  columns  of  A
  !     and the number of rows of  B  respectively.
  !
  ONE  = cmplx(1.0_dp,0.0_dp,dp)
  ZERO = cmplx(0.0_dp,0.0_dp,dp)
  NOTA = LSAME(TRANSA,"N")
  NOTB = LSAME(TRANSB,"N")
  CONJA = LSAME(TRANSA,"C")
  CONJB = LSAME(TRANSB,"C")
  if (NOTA) then
    NROWA = M
    !NCOLA = K
  else
    NROWA = K
    !NCOLA = M
  end if
  if (NOTB) then
    NROWB = K
  else
    NROWB = N
  end if
  !
  !     Test the input parameters.
  !
  INFO = 0
  if ((.not.NOTA) .and. (.not.CONJA) .and. (.not.LSAME(TRANSA,"T"))) then
    INFO = 1
  elseif ((.not.NOTB) .and. (.not.CONJB) .and. (.not.LSAME(TRANSB,"T"))) then
    INFO = 2
  elseif (M < 0) then
    INFO = 3
  elseif (N < 0) then
    INFO = 4
  elseif (K < 0) then
    INFO = 5
  elseif (LDA < MAX(1,NROWA)) then
    INFO = 8
  elseif (LDB < MAX(1,NROWB)) then
    INFO = 10
  elseif (LDC < MAX(1,M)) then
    INFO = 13
  end if
  if (INFO /= 0) then
    call XERBLA("CGEMM ",INFO)
    stop
  end if
  !
  !     Quick return if possible.
  !
  if ((M==0) .or. (N==0) .or. (((ALPHA==ZERO).or.(K==0)).and.(BETA==ONE))) &
    return
  !
  !     And when  alpha == zero.
  !
  if (ALPHA == ZERO) then
    if (BETA == ZERO) then
      do J = 1,N
        do I = 1,M
          C(I,J) = ZERO
        end do
      end do
    else
      do J = 1,N
        do I = 1,M
          C(I,J) = BETA * C(I,J)
        end do
      end do
    end if
  !
  !     Start the operations.
  !
  !
  elseif (NOTB) then
    if (NOTA) then
      !
      !           Form  C := alpha*A*B + beta*C.
      !
      do J = 1,N
        if (BETA == ZERO) then
          do I = 1,M
            C(I,J) = ZERO
          end do
        elseif (BETA /= ONE) then
          do I = 1,M
            C(I,J) = BETA * C(I,J)
          end do
        end if
        do L = 1,K
          if (B(L,J) /= ZERO) then
            TEMP = ALPHA * B(L,J)
            do I = 1,M
              C(I,J) = C(I,J) + TEMP*A(I,L)
            end do
          end if
        end do
      end do
    elseif (CONJA) then
      !
      !           Form  C := alpha*conjg( A' )*B + beta*C.
      !
      do J = 1,N
        do I = 1,M
          TEMP = ZERO
          do L = 1,K
            TEMP = TEMP + CONJG(A(L,I))*B(L,J)
          end do
          if (BETA == ZERO) then
            C(I,J) = ALPHA * TEMP
          else
            C(I,J) = ALPHA*TEMP + BETA*C(I,J)
          end if
        end do
      end do
    else
      !
      !           Form  C := alpha*A'*B + beta*C
      !
      do J = 1,N
        do I = 1,M
          TEMP = ZERO
          do L = 1,K
            TEMP = TEMP + A(L,I)*B(L,J)
          end do
          if (BETA == ZERO) then
            C(I,J) = ALPHA * TEMP
          else
            C(I,J) = ALPHA*TEMP + BETA*C(I,J)
          end if
        end do
      end do
    end if
  elseif (NOTA) then
    if (CONJB) then
      !
      !           Form  C := alpha*A*conjg( B' ) + beta*C.
      !
      do J = 1,N
        if (BETA == ZERO) then
          do I = 1,M
            C(I,J) = ZERO
          end do
        elseif (BETA /= ONE) then
          do I = 1,M
            C(I,J) = BETA * C(I,J)
          end do
        end if
        do L = 1,K
          if (B(J,L) /= ZERO) then
            TEMP = ALPHA * CONJG(B(J,L))
            do I = 1,M
              C(I,J) = C(I,J) + TEMP*A(I,L)
            end do
          end if
        end do
      end do
    else
      !
      !           Form  C := alpha*A*B'          + beta*C
      !
      do J = 1,N
        if (BETA == ZERO) then
          do I = 1,M
            C(I,J) = ZERO
          end do
        elseif (BETA /= ONE) then
          do I = 1,M
            C(I,J) = BETA * C(I,J)
          end do
        end if
        do L = 1,K
          if (B(J,L) /= ZERO) then
            TEMP = ALPHA * B(J,L)
            do I = 1,M
              C(I,J) = C(I,J) + TEMP*A(I,L)
            end do
          end if
        end do
      end do
    end if
  elseif (CONJA) then
    if (CONJB) then
      !
      !           Form  C := alpha*conjg( A' )*conjg( B' ) + beta*C.
      !
      do J = 1,N
        do I = 1,M
          TEMP = ZERO
          do L = 1,K
            TEMP = TEMP + CONJG(A(L,I))*CONJG(B(J,L))
          end do
          if (BETA == ZERO) then
            C(I,J) = ALPHA * TEMP
          else
            C(I,J) = ALPHA*TEMP + BETA*C(I,J)
          end if
        end do
      end do
    else
      !
      !           Form  C := alpha*conjg( A' )*B' + beta*C
      !
      do J = 1,N
        do I = 1,M
          TEMP = ZERO
          do L = 1,K
            TEMP = TEMP + CONJG(A(L,I))*B(J,L)
          end do
          if (BETA == ZERO) then
            C(I,J) = ALPHA * TEMP
          else
            C(I,J) = ALPHA*TEMP + BETA*C(I,J)
          end if
        end do
      end do
    end if
  elseif (CONJB) then
    !
    !           Form  C := alpha*A'*conjg( B' ) + beta*C
    !
    do J = 1,N
      do I = 1,M
        TEMP = ZERO
        do L = 1,K
          TEMP = TEMP + A(L,I)*CONJG(B(J,L))
        end do
        if (BETA == ZERO) then
          C(I,J) = ALPHA * TEMP
        else
          C(I,J) = ALPHA*TEMP + BETA*C(I,J)
        end if
      end do
    end do
  else
    !
    !           Form  C := alpha*A'*B' + beta*C
    !
    do J = 1,N
      do I = 1,M
        TEMP = ZERO
        do L = 1,K
          TEMP = TEMP + A(L,I)*B(J,L)
        end do
        if (BETA == ZERO) then
          C(I,J) = ALPHA * TEMP
        else
          C(I,J) = ALPHA*TEMP + BETA*C(I,J)
        end if
      end do
    end do
  end if
!
!     End of CGEMM .
!
end subroutine CGEMM
subroutine CTRMM(SIDE,UPLO,TRANSA,DIAG,M,N,ALPHA,A,LDA,B,LDB)

  complex(dp) :: ONE
  complex(dp) :: ZERO

  character, intent(in) :: DIAG,SIDE,TRANSA,UPLO
  integer, intent(in) :: LDA,LDB,M,N
  complex(dp), intent(in) :: ALPHA
  complex(dp), dimension(LDA,*), intent(in) :: A
  complex(dp), dimension(LDB,*), intent(inout) :: B

  logical :: LSIDE,NOCONJ,NOUNIT,UPPER
  integer :: I,INFO,J,K,NROWA
  complex(dp) :: TEMP
  !

  !
  !     Test the input parameters.
  !
  ONE  = cmplx(1.0_dp,0.0_dp,dp)
  ZERO = cmplx(0.0_dp,0.0_dp,dp)
  LSIDE = LSAME(SIDE,"L")
  if (LSIDE) then
    NROWA = M
  else
    NROWA = N
  end if
  NOCONJ = LSAME(TRANSA,"T")
  NOUNIT = LSAME(DIAG,"N")
  UPPER = LSAME(UPLO,"U")
  !
  INFO = 0
  if ((.not.LSIDE) .and. (.not.LSAME(SIDE,"R"))) then
    INFO = 1
  elseif ((.not.UPPER) .and. (.not.LSAME(UPLO,"L"))) then
    INFO = 2
  elseif ((.not.LSAME(TRANSA,"N")) .and. (.not.LSAME(TRANSA,"T")) .and. &
          (.not.LSAME(TRANSA,"C"))) then
    INFO = 3
  elseif ((.not.LSAME(DIAG,"U")) .and. (.not.LSAME(DIAG,"N"))) then
    INFO = 4
  elseif (M < 0) then
    INFO = 5
  elseif (N < 0) then
    INFO = 6
  elseif (LDA < MAX(1,NROWA)) then
    INFO = 9
  elseif (LDB < MAX(1,M)) then
    INFO = 11
  end if
  if (INFO /= 0) then
    call XERBLA("CTRMM ",INFO)
    stop
  end if
  !
  !     Quick return if possible.
  !
  if (N == 0) return
  !
  !     And when  alpha == zero.
  !
  if (ALPHA == ZERO) then
    do J = 1,N
      do I = 1,M
        B(I,J) = ZERO
      end do
    end do
  !
  !     Start the operations.
  !
  !
  elseif (LSIDE) then
    if (LSAME(TRANSA,"N")) then
      !
      !           Form  B := alpha*A*B.
      !
      if (UPPER) then
        do J = 1,N
          do K = 1,M
            if (B(K,J) /= ZERO) then
              TEMP = ALPHA * B(K,J)
              do I = 1,K-1
                B(I,J) = B(I,J) + TEMP*A(I,K)
              end do
              if (NOUNIT) then
                TEMP = TEMP * A(K,K)
              end if
              B(K,J) = TEMP
            end if
          end do
        end do
      else
        do J = 1,N
          do K = M,1,-1
            if (B(K,J) /= ZERO) then
              TEMP = ALPHA * B(K,J)
              B(K,J) = TEMP
              if (NOUNIT) then
                B(K,J) = B(K,J) * A(K,K)
              end if
              do I = K+1,M
                B(I,J) = B(I,J) + TEMP*A(I,K)
              end do
            end if
          end do
        end do
      end if
    !
    !           Form  B := alpha*A'*B   or   B := alpha*conjg( A' )*B.
    !
    elseif (UPPER) then
      do J = 1,N
        do I = M,1,-1
          TEMP = B(I,J)
          if (NOCONJ) then
            if (NOUNIT) then
              TEMP = TEMP * A(I,I)
            end if
            do K = 1,I-1
              TEMP = TEMP + A(K,I)*B(K,J)
            end do
          else
            if (NOUNIT) then
              TEMP = TEMP * CONJG(A(I,I))
            end if
            do K = 1,I-1
              TEMP = TEMP + CONJG(A(K,I))*B(K,J)
            end do
          end if
          B(I,J) = ALPHA * TEMP
        end do
      end do
    else
      do J = 1,N
        do I = 1,M
          TEMP = B(I,J)
          if (NOCONJ) then
            if (NOUNIT) then
              TEMP = TEMP * A(I,I)
            end if
            do K = I+1,M
              TEMP = TEMP + A(K,I)*B(K,J)
            end do
          else
            if (NOUNIT) then
              TEMP = TEMP * CONJG(A(I,I))
            end if
            do K = I+1,M
              TEMP = TEMP + CONJG(A(K,I))*B(K,J)
            end do
          end if
          B(I,J) = ALPHA * TEMP
        end do
      end do
    end if
  elseif (LSAME(TRANSA,"N")) then
    !
    !           Form  B := alpha*B*A.
    !
    if (UPPER) then
      do J = N,1,-1
        TEMP = ALPHA
        if (NOUNIT) then
          TEMP = TEMP * A(J,J)
        end if
        do I = 1,M
          B(I,J) = TEMP * B(I,J)
        end do
        do K = 1,J-1
          if (A(K,J) /= ZERO) then
            TEMP = ALPHA * A(K,J)
            do I = 1,M
              B(I,J) = B(I,J) + TEMP*B(I,K)
            end do
          end if
        end do
      end do
    else
      do J = 1,N
        TEMP = ALPHA
        if (NOUNIT) then
          TEMP = TEMP * A(J,J)
        end if
        do I = 1,M
          B(I,J) = TEMP * B(I,J)
        end do
        do K = J+1,N
          if (A(K,J) /= ZERO) then
            TEMP = ALPHA * A(K,J)
            do I = 1,M
              B(I,J) = B(I,J) + TEMP*B(I,K)
            end do
          end if
        end do
      end do
    end if
  !
  !           Form  B := alpha*B*A'   or   B := alpha*B*conjg( A' ).
  !
  elseif (UPPER) then
    do K = 1,N
      do J = 1,K-1
        if (A(J,K) /= ZERO) then
          if (NOCONJ) then
            TEMP = ALPHA * A(J,K)
          else
            TEMP = ALPHA * CONJG(A(J,K))
          end if
          do I = 1,M
            B(I,J) = B(I,J) + TEMP*B(I,K)
          end do
        end if
      end do
      TEMP = ALPHA
      if (NOUNIT) then
        if (NOCONJ) then
          TEMP = TEMP * A(K,K)
        else
          TEMP = TEMP * CONJG(A(K,K))
        end if
      end if
      if (TEMP /= ONE) then
        do I = 1,M
          B(I,K) = TEMP * B(I,K)
        end do
      end if
    end do
  else
    do K = N,1,-1
      do J = K+1,N
        if (A(J,K) /= ZERO) then
          if (NOCONJ) then
            TEMP = ALPHA * A(J,K)
          else
            TEMP = ALPHA * CONJG(A(J,K))
          end if
          do I = 1,M
            B(I,J) = B(I,J) + TEMP*B(I,K)
          end do
        end if
      end do
      TEMP = ALPHA
      if (NOUNIT) then
        if (NOCONJ) then
          TEMP = TEMP * A(K,K)
        else
          TEMP = TEMP * CONJG(A(K,K))
        end if
      end if
      if (TEMP /= ONE) then
        do I = 1,M
          B(I,K) = TEMP * B(I,K)
        end do
      end if
    end do
  end if
!
!     End of CTRMM .
!
end subroutine CTRMM
subroutine CTRSM(SIDE,UPLO,TRANSA,DIAG,M,N,ALPHA,A,LDA,B,LDB)

  complex(dp) :: ONE
  complex(dp) :: ZERO

  character, intent(in) :: DIAG,SIDE,TRANSA,UPLO
  integer, intent(in) :: LDA,LDB,M,N
  complex(dp), intent(in) :: ALPHA
  complex(dp), dimension(LDA,*), intent(in) :: A
  complex(dp), dimension(LDB,*), intent(inout) :: B

  logical :: LSIDE,NOCONJ,NOUNIT,UPPER
  integer :: I,INFO,J,K,NROWA
  complex(dp) :: TEMP
  !

  !
  !     Test the input parameters.
  !
  ONE  = cmplx(1.0_dp,0.0_dp,dp)
  ZERO = cmplx(0.0_dp,0.0_dp,dp)
  LSIDE = LSAME(SIDE,"L")
  if (LSIDE) then
    NROWA = M
  else
    NROWA = N
  end if
  NOCONJ = LSAME(TRANSA,"T")
  NOUNIT = LSAME(DIAG,"N")
  UPPER = LSAME(UPLO,"U")
  !
  INFO = 0
  if ((.not.LSIDE) .and. (.not.LSAME(SIDE,"R"))) then
    INFO = 1
  elseif ((.not.UPPER) .and. (.not.LSAME(UPLO,"L"))) then
    INFO = 2
  elseif ((.not.LSAME(TRANSA,"N")) .and. (.not.LSAME(TRANSA,"T")) .and. &
          (.not.LSAME(TRANSA,"C"))) then
    INFO = 3
  elseif ((.not.LSAME(DIAG,"U")) .and. (.not.LSAME(DIAG,"N"))) then
    INFO = 4
  elseif (M < 0) then
    INFO = 5
  elseif (N < 0) then
    INFO = 6
  elseif (LDA < MAX(1,NROWA)) then
    INFO = 9
  elseif (LDB < MAX(1,M)) then
    INFO = 11
  end if
  if (INFO /= 0) then
    call XERBLA("CTRSM ",INFO)
    stop
  end if
  !
  !     Quick return if possible.
  !
  if (N == 0) return
  !
  !     And when  alpha == zero.
  !
  if (ALPHA == ZERO) then
    do J = 1,N
      do I = 1,M
        B(I,J) = ZERO
      end do
    end do
  !
  !     Start the operations.
  !
  !
  elseif (LSIDE) then
    if (LSAME(TRANSA,"N")) then
      !
      !           Form  B := alpha*inv( A )*B.
      !
      if (UPPER) then
        do J = 1,N
          if (ALPHA /= ONE) then
            do I = 1,M
              B(I,J) = ALPHA * B(I,J)
            end do
          end if
          do K = M,1,-1
            if (B(K,J) /= ZERO) then
              if (NOUNIT) then
                B(K,J) = B(K,J) / A(K,K)
              end if
              do I = 1,K-1
                B(I,J) = B(I,J) - B(K,J)*A(I,K)
              end do
            end if
          end do
        end do
      else
        do J = 1,N
          if (ALPHA /= ONE) then
            do I = 1,M
              B(I,J) = ALPHA * B(I,J)
            end do
          end if
          do K = 1,M
            if (B(K,J) /= ZERO) then
              if (NOUNIT) then
                B(K,J) = B(K,J) / A(K,K)
              end if
              do I = K+1,M
                B(I,J) = B(I,J) - B(K,J)*A(I,K)
              end do
            end if
          end do
        end do
      end if
    !
    !           Form  B := alpha*inv( A' )*B
    !           or    B := alpha*inv( conjg( A' ) )*B.
    !
    elseif (UPPER) then
      do J = 1,N
        do I = 1,M
          TEMP = ALPHA * B(I,J)
          if (NOCONJ) then
            do K = 1,I-1
              TEMP = TEMP - A(K,I)*B(K,J)
            end do
            if (NOUNIT) then
              TEMP = TEMP / A(I,I)
            end if
          else
            do K = 1,I-1
              TEMP = TEMP - CONJG(A(K,I))*B(K,J)
            end do
            if (NOUNIT) then
              TEMP = TEMP / CONJG(A(I,I))
            end if
          end if
          B(I,J) = TEMP
        end do
      end do
    else
      do J = 1,N
        do I = M,1,-1
          TEMP = ALPHA * B(I,J)
          if (NOCONJ) then
            do K = I+1,M
              TEMP = TEMP - A(K,I)*B(K,J)
            end do
            if (NOUNIT) then
              TEMP = TEMP / A(I,I)
            end if
          else
            do K = I+1,M
              TEMP = TEMP - CONJG(A(K,I))*B(K,J)
            end do
            if (NOUNIT) then
              TEMP = TEMP / CONJG(A(I,I))
            end if
          end if
          B(I,J) = TEMP
        end do
      end do
    end if
  elseif (LSAME(TRANSA,"N")) then
    !
    !           Form  B := alpha*B*inv( A ).
    !
    if (UPPER) then
      do J = 1,N
        if (ALPHA /= ONE) then
          do I = 1,M
            B(I,J) = ALPHA * B(I,J)
          end do
        end if
        do K = 1,J-1
          if (A(K,J) /= ZERO) then
            do I = 1,M
              B(I,J) = B(I,J) - A(K,J)*B(I,K)
            end do
          end if
        end do
        if (NOUNIT) then
          TEMP = ONE / A(J,J)
          do I = 1,M
            B(I,J) = TEMP * B(I,J)
          end do
        end if
      end do
    else
      do J = N,1,-1
        if (ALPHA /= ONE) then
          do I = 1,M
            B(I,J) = ALPHA * B(I,J)
          end do
        end if
        do K = J+1,N
          if (A(K,J) /= ZERO) then
            do I = 1,M
              B(I,J) = B(I,J) - A(K,J)*B(I,K)
            end do
          end if
        end do
        if (NOUNIT) then
          TEMP = ONE / A(J,J)
          do I = 1,M
            B(I,J) = TEMP * B(I,J)
          end do
        end if
      end do
    end if
  !
  !           Form  B := alpha*B*inv( A' )
  !           or    B := alpha*B*inv( conjg( A' ) ).
  !
  elseif (UPPER) then
    do K = N,1,-1
      if (NOUNIT) then
        if (NOCONJ) then
          TEMP = ONE / A(K,K)
        else
          TEMP = ONE / CONJG(A(K,K))
        end if
        do I = 1,M
          B(I,K) = TEMP * B(I,K)
        end do
      end if
      do J = 1,K-1
        if (A(J,K) /= ZERO) then
          if (NOCONJ) then
            TEMP = A(J,K)
          else
            TEMP = CONJG(A(J,K))
          end if
          do I = 1,M
            B(I,J) = B(I,J) - TEMP*B(I,K)
          end do
        end if
      end do
      if (ALPHA /= ONE) then
        do I = 1,M
          B(I,K) = ALPHA * B(I,K)
        end do
      end if
    end do
  else
    do K = 1,N
      if (NOUNIT) then
        if (NOCONJ) then
          TEMP = ONE / A(K,K)
        else
          TEMP = ONE / CONJG(A(K,K))
        end if
        do I = 1,M
          B(I,K) = TEMP * B(I,K)
        end do
      end if
      do J = K+1,N
        if (A(J,K) /= ZERO) then
          if (NOCONJ) then
            TEMP = A(J,K)
          else
            TEMP = CONJG(A(J,K))
          end if
          do I = 1,M
            B(I,J) = B(I,J) - TEMP*B(I,K)
          end do
        end if
      end do
      if (ALPHA /= ONE) then
        do I = 1,M
          B(I,K) = ALPHA * B(I,K)
        end do
      end if
    end do
  end if
!
!     End of CTRSM .
!
end subroutine CTRSM
!
subroutine CGEMV(TRANS,M,N,ALPHA,A,LDA,X,INCX,BETA,Y,INCY)

  complex(dp) :: ONE
  complex(dp) :: ZERO

  character, intent(in) :: TRANS
  integer, intent(in) :: INCX,INCY,LDA,M,N
  complex(dp), intent(in) :: ALPHA,BETA
  complex(dp), dimension(*), intent(in) :: X
  complex(dp), dimension(*), intent(inout) :: Y
  complex(dp), dimension(LDA,*), intent(in) :: A

  logical :: NOCONJ
  integer :: I,INFO,IX,IY,J,JX,JY,KX,KY,LENX,LENY
  complex(dp) :: TEMP
  !

  !
  !     Test the input parameters.
  !
  ONE  = cmplx(1.0_dp,0.0_dp,dp)
  ZERO = cmplx(0.0_dp,0.0_dp,dp)
  INFO = 0
  if (.not.LSAME(TRANS,"N") .and. .not.LSAME(TRANS,"T") .and. &
      .not.LSAME(TRANS,"C")) then
    INFO = 1
  elseif (M < 0) then
    INFO = 2
  elseif (N < 0) then
    INFO = 3
  elseif (LDA < MAX(1,M)) then
    INFO = 6
  elseif (INCX == 0) then
    INFO = 8
  elseif (INCY == 0) then
    INFO = 11
  end if
  if (INFO /= 0) then
    call XERBLA("CGEMV ",INFO)
    stop
  end if
  !
  !     Quick return if possible.
  !
  if ((M==0) .or. (N==0) .or. ((ALPHA==ZERO).and.(BETA==ONE))) return
  !
  NOCONJ = LSAME(TRANS,"T")
  !
  !     Set  LENX  and  LENY, the lengths of the vectors x and y, and set
  !     up the start points in  X  and  Y.
  !
  if (LSAME(TRANS,"N")) then
    LENX = N
    LENY = M
  else
    LENX = M
    LENY = N
  end if
  if (INCX > 0) then
    KX = 1
  else
    KX = 1 - (LENX-1)*INCX
  end if
  if (INCY > 0) then
    KY = 1
  else
    KY = 1 - (LENY-1)*INCY
  end if
  !
  !     Start the operations. In this version the elements of A are
  !     accessed sequentially with one pass through A.
  !
  !     First form  y := beta*y.
  !
  if (BETA /= ONE) then
    if (INCY == 1) then
      if (BETA == ZERO) then
        do I = 1,LENY
          Y(I) = ZERO
        end do
      else
        do I = 1,LENY
          Y(I) = BETA * Y(I)
        end do
      end if
    else
      IY = KY
      if (BETA == ZERO) then
        do I = 1,LENY
          Y(IY) = ZERO
          IY = IY + INCY
        end do
      else
        do I = 1,LENY
          Y(IY) = BETA * Y(IY)
          IY = IY + INCY
        end do
      end if
    end if
  end if
  if (ALPHA == ZERO) return
  !
  if (LSAME(TRANS,"N")) then
    !
    !        Form  y := alpha*A*x + y.
    !
    JX = KX
    if (INCY == 1) then
      do J = 1,N
        if (X(JX) /= ZERO) then
          TEMP = ALPHA * X(JX)
          do I = 1,M
            Y(I) = Y(I) + TEMP*A(I,J)
          end do
        end if
        JX = JX + INCX
      end do
    else
      do J = 1,N
        if (X(JX) /= ZERO) then
          TEMP = ALPHA * X(JX)
          IY = KY
          do I = 1,M
            Y(IY) = Y(IY) + TEMP*A(I,J)
            IY = IY + INCY
          end do
        end if
        JX = JX + INCX
      end do
    end if
  else
    !
    !        Form  y := alpha*A'*x + y  or  y := alpha*conjg( A' )*x + y.
    !
    JY = KY
    if (INCX == 1) then
      do J = 1,N
        TEMP = ZERO
        if (NOCONJ) then
          do I = 1,M
            TEMP = TEMP + A(I,J)*X(I)
          end do
        else
          do I = 1,M
            TEMP = TEMP + CONJG(A(I,J))*X(I)
          end do
        end if
        Y(JY) = Y(JY) + ALPHA*TEMP
        JY = JY + INCY
      end do
    else
      do J = 1,N
        TEMP = ZERO
        IX = KX
        if (NOCONJ) then
          do I = 1,M
            TEMP = TEMP + A(I,J)*X(IX)
            IX = IX + INCX
          end do
        else
          do I = 1,M
            TEMP = TEMP + CONJG(A(I,J))*X(IX)
            IX = IX + INCX
          end do
        end if
        Y(JY) = Y(JY) + ALPHA*TEMP
        JY = JY + INCY
      end do
    end if
  end if
!
!     End of CGEMV .
!
end subroutine CGEMV
!
subroutine CGERU(M,N,ALPHA,X,INCX,Y,INCY,A,LDA)

  complex(dp) :: ZERO

  integer, intent(in) :: INCX,INCY,LDA,M,N
  complex(dp), intent(in) :: ALPHA
  complex(dp), dimension(*), intent(in) :: X,Y
  complex(dp), dimension(LDA,*), intent(inout) :: A

  integer :: I,INFO,IX,J,JY,KX
  complex(dp) :: TEMP
  !

  !
  !     Test the input parameters.
  !
  ZERO = cmplx(0.0_dp,0.0_dp,dp)
  INFO = 0
  if (M < 0) then
    INFO = 1
  elseif (N < 0) then
    INFO = 2
  elseif (INCX == 0) then
    INFO = 5
  elseif (INCY == 0) then
    INFO = 7
  elseif (LDA < MAX(1,M)) then
    INFO = 9
  end if
  if (INFO /= 0) then
    call XERBLA("CGERU ",INFO)
    stop
  end if
  !
  !     Quick return if possible.
  !
  if ((M==0) .or. (N==0) .or. (ALPHA==ZERO)) return
  !
  !     Start the operations. In this version the elements of A are
  !     accessed sequentially with one pass through A.
  !
  if (INCY > 0) then
    JY = 1
  else
    JY = 1 - (N-1)*INCY
  end if
  !
  if (INCX == 1) then
    do J = 1,N
      if (Y(JY) /= ZERO) then
        TEMP = ALPHA * Y(JY)
        do I = 1,M
          A(I,J) = A(I,J) + X(I)*TEMP
        end do
      end if
      JY = JY + INCY
    end do
  else
    if (INCX > 0) then
      KX = 1
    else
      KX = 1 - (M-1)*INCX
    end if
    do J = 1,N
      if (Y(JY) /= ZERO) then
        TEMP = ALPHA * Y(JY)
        IX = KX
        do I = 1,M
          A(I,J) = A(I,J) + X(IX)*TEMP
          IX = IX + INCX
        end do
      end if
      JY = JY + INCY
    end do
  end if
!
!     End of CGERU .
!
end subroutine CGERU
!
subroutine CTRMV(UPLO,TRANS,DIAG,N,A,LDA,X,INCX)

  complex(dp) :: ZERO

  character, intent(in) :: DIAG,TRANS,UPLO
  integer, intent(in) :: INCX,LDA,N
  complex(dp), dimension(*), intent(inout) :: X
  complex(dp), dimension(LDA,*), intent(in) :: A

  logical :: NOCONJ,NOUNIT
  integer :: I,INFO,IX,J,JX,KX
  complex(dp) :: TEMP

  !
  !     Test the input parameters.
  !
  ZERO = cmplx(0.0_dp,0.0_dp,dp)
  INFO = 0
  if (.not.LSAME(UPLO,"U") .and. .not.LSAME(UPLO,"L")) then
    INFO = 1
  elseif (.not.LSAME(TRANS,"N") .and. .not.LSAME(TRANS,"T") .and. &
          .not.LSAME(TRANS,"C")) then
    INFO = 2
  elseif (.not.LSAME(DIAG,"U") .and. .not.LSAME(DIAG,"N")) then
    INFO = 3
  elseif (N < 0) then
    INFO = 4
  elseif (LDA < MAX(1,N)) then
    INFO = 6
  elseif (INCX == 0) then
    INFO = 8
  end if
  if (INFO /= 0) then
    call XERBLA("CTRMV ",INFO)
    stop
  end if
  !
  !     Quick return if possible.
  !
  if (N == 0) return
  !
  NOCONJ = LSAME(TRANS,"T")
  NOUNIT = LSAME(DIAG,"N")
  !
  !     Set up the start point in X if the increment is not unity. This
  !     will be  ( N - 1 )*INCX  too small for descending loops.
  !
  kx = huge(1)  !CCI indicates possible use before definition.
  if (INCX <= 0) then
    KX = 1 - (N-1)*INCX
  elseif (INCX /= 1) then
    KX = 1
  end if
  !
  !     Start the operations. In this version the elements of A are
  !     accessed sequentially with one pass through A.
  !
  !
  if (LSAME(TRANS,"N")) then
    !
    !        Form  x := A*x.
    !
    if (LSAME(UPLO,"U")) then
      if (INCX == 1) then
        do J = 1,N
          if (X(J) /= ZERO) then
            TEMP = X(J)
            do I = 1,J-1
              X(I) = X(I) + TEMP*A(I,J)
            end do
            if (NOUNIT) then
              X(J) = X(J) * A(J,J)
            end if
          end if
        end do
      else
        JX = KX
        do J = 1,N
          if (X(JX) /= ZERO) then
            TEMP = X(JX)
            IX = KX
            do I = 1,J-1
              X(IX) = X(IX) + TEMP*A(I,J)
              IX = IX + INCX
            end do
            if (NOUNIT) then
              X(JX) = X(JX) * A(J,J)
            end if
          end if
          JX = JX + INCX
        end do
      end if
    elseif (INCX == 1) then
      do J = N,1,-1
        if (X(J) /= ZERO) then
          TEMP = X(J)
          do I = N,J+1,-1
            X(I) = X(I) + TEMP*A(I,J)
          end do
          if (NOUNIT) then
            X(J) = X(J) * A(J,J)
          end if
        end if
      end do
    else
      KX = KX + (N-1)*INCX
      JX = KX
      do J = N,1,-1
        if (X(JX) /= ZERO) then
          TEMP = X(JX)
          IX = KX
          do I = N,J+1,-1
            X(IX) = X(IX) + TEMP*A(I,J)
            IX = IX - INCX
          end do
          if (NOUNIT) then
            X(JX) = X(JX) * A(J,J)
          end if
        end if
        JX = JX - INCX
      end do
    end if
  !
  !        Form  x := A'*x  or  x := conjg( A' )*x.
  !
  elseif (LSAME(UPLO,"U")) then
    if (INCX == 1) then
      do J = N,1,-1
        TEMP = X(J)
        if (NOCONJ) then
          if (NOUNIT) then
            TEMP = TEMP * A(J,J)
          end if
          do I = J-1,1,-1
            TEMP = TEMP + A(I,J)*X(I)
          end do
        else
          if (NOUNIT) then
            TEMP = TEMP * CONJG(A(J,J))
          end if
          do I = J-1,1,-1
            TEMP = TEMP + CONJG(A(I,J))*X(I)
          end do
        end if
        X(J) = TEMP
      end do
    else
      JX = KX + (N-1)*INCX
      do J = N,1,-1
        TEMP = X(JX)
        IX = JX
        if (NOCONJ) then
          if (NOUNIT) then
            TEMP = TEMP * A(J,J)
          end if
          do I = J-1,1,-1
            IX = IX - INCX
            TEMP = TEMP + A(I,J)*X(IX)
          end do
        else
          if (NOUNIT) then
            TEMP = TEMP * CONJG(A(J,J))
          end if
          do I = J-1,1,-1
            IX = IX - INCX
            TEMP = TEMP + CONJG(A(I,J))*X(IX)
          end do
        end if
        X(JX) = TEMP
        JX = JX - INCX
      end do
    end if
  elseif (INCX == 1) then
    do J = 1,N
      TEMP = X(J)
      if (NOCONJ) then
        if (NOUNIT) then
          TEMP = TEMP * A(J,J)
        end if
        do I = J+1,N
          TEMP = TEMP + A(I,J)*X(I)
        end do
      else
        if (NOUNIT) then
          TEMP = TEMP * CONJG(A(J,J))
        end if
        do I = J+1,N
          TEMP = TEMP + CONJG(A(I,J))*X(I)
        end do
      end if
      X(J) = TEMP
    end do
  else
    JX = KX
    do J = 1,N
      TEMP = X(JX)
      IX = JX
      if (NOCONJ) then
        if (NOUNIT) then
          TEMP = TEMP * A(J,J)
        end if
        do I = J+1,N
          IX = IX + INCX
          TEMP = TEMP + A(I,J)*X(IX)
        end do
      else
        if (NOUNIT) then
          TEMP = TEMP * CONJG(A(J,J))
        end if
        do I = J+1,N
          IX = IX + INCX
          TEMP = TEMP + CONJG(A(I,J))*X(IX)
        end do
      end if
      X(JX) = TEMP
      JX = JX + INCX
    end do
  end if
!
!     End of CTRMV .
!
end subroutine CTRMV
!
!
subroutine cscal(n,ca,cx,incx)
  !
  !     scales a vector by a constant.
  !     jack dongarra, linpack,  3/11/78.
  !     modified 3/93 to return if incx <= 0.
  !     modified 12/3/93, array(1) declarations changed to array(*)

  integer, intent(in) :: n
  complex(dp), intent(in) :: ca
  complex(dp), dimension(*), intent(inout) :: cx
  integer, intent(in) :: incx

  integer :: i,nincx
  !
  ! ... Executable Statements ...
  !
  !
  if (n<=0 .or. incx<=0) return
  if (incx == 1) then
    !
    !        code for increment equal to 1
    !
    do i = 1,n
      cx(i) = ca * cx(i)
    end do
  else
    !
    !        code for increment not equal to 1
    !
    nincx = n * incx
    do i = 1,nincx,incx
      cx(i) = ca * cx(i)
    end do
  end if
end subroutine cscal
!
subroutine cswap(n,cx,incx,cy,incy)
  !
  !     interchanges two vectors.
  !     jack dongarra, linpack, 3/11/78.
  !     modified 12/3/93, array(1) declarations changed to array(*)
  !


  integer, intent(in) :: n
  complex(dp), dimension(*), intent(inout) :: cx
  integer, intent(in) :: incx
  complex(dp), dimension(*), intent(inout) :: cy
  integer, intent(in) :: incy

  integer :: i,ix,iy
  complex(dp) :: ctemp
  !
  ! ... Executable Statements ...
  !
  !
  if (n <= 0) return
  if (incx==1 .and. incy==1) then
    !
    !       code for both increments equal to 1
    do i = 1,n
      ctemp = cx(i)
      cx(i) = cy(i)
      cy(i) = ctemp
    end do
  else
    !
    !       code for unequal increments or equal increments not equal
    !         to 1
    !
    ix = 1
    iy = 1
    if (incx < 0) then
      ix = (-n+1)*incx + 1
    end if
    if (incy < 0) then
      iy = (-n+1)*incy + 1
    end if
    do i = 1,n
      ctemp = cx(ix)
      cx(ix) = cy(iy)
      cy(iy) = ctemp
      ix = ix + incx
      iy = iy + incy
    end do
  end if
end subroutine cswap
function icamax(n,cx,incx)
  !
  !     finds the index of element having max. absolute value.
  !     jack dongarra, linpack, 3/11/78.
  !     modified 3/93 to return if incx <= 0.
  !     modified 12/3/93, array(1) declarations changed to array(*)
  !

  integer, intent(in) :: n
  complex(dp), dimension(*), intent(in) :: cx
  integer, intent(in) :: incx
  !

  integer :: icamax

  integer :: i,ix
  real(dp) :: smax
  !complex(dp) :: zdum

  !real(dp) :: cabs1
  !cabs1(zdum) = abs( real(zdum,dp) ) !+ abs( aimag(zdum) )

  !zdum = cmplx( 0._dp, 0._dp, dp )

  icamax = 0
  if (n<1 .or. incx<=0) return
  icamax = 1
  if (n == 1) return
  if (incx == 1) then
    !
    !        code for increment equal to 1
    !
    smax = cabs1(cx(1))
    do i = 2,n
      if (cabs1(cx(i)) > smax) then
        icamax = i
        smax = cabs1(cx(i))
      end if
    end do
  else
    !
    !        code for increment not equal to 1
    !
    ix = 1
    smax = cabs1(cx(1))
    ix = ix + incx
    do i = 2,n
      if (cabs1(cx(ix)) > smax) then
        icamax = i
        smax = cabs1(cx(ix))
      end if
      ix = ix + incx
    end do
  end if
end function icamax

function cabs1(z)

  complex(dp), intent(in) :: z

  real(dp) :: cabs1

  cabs1 = abs( real(z,dp) ) !+ abs( aimag(z) )

end function cabs1

end module av2p0_lapack_util

/******************************************************************************
 *
 *      Developed By:  <PERSON>
 *                     NASA Langley Research Center
 *                     Phone:(757)864-5318
 *                     Email:<EMAIL>
 *
 *      Modifications: <PERSON>
 *
 *
 *      Developed For: NASA Langley Research Center
 *
 *      Copyright:     This material is declared a work of the U.S. Government
 *                     and is not subject to copyright protection in the
 *                     United States.
 *
 ******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <ddfreader.h>

#define MAX_VERTS 4

int main(int argc, char* argv[])
{
  long   i,j,n;
  char   title[80];
  char   ztitle[80];
  char   version[20];
  long   nzone;
  long   type;
  long   npts;
  long   nfunc;
  long   nelem;
  long   pid;
  long   setid;
  long   npe;
  long   id;
  long   nf1;
  long   nv;
  double stime;
  double x,y,z;
  long   elem[MAX_VERTS];
  double *func=NULL;
  FILE   *tec_fp=NULL;

  if( argc != 3 ) {
    fprintf(stderr,"Usage: ddf2tecplot ddf_file tecplot_file\n");
    exit(1);
  }

  if( readBinaryDDFHeader(argv[1], title, version, &nzone, &stime) != 0L ) {
    fprintf(stderr,"Could not read DDF header\n");
    exit(1);
  }

  if( (tec_fp=fopen(argv[2],"w")) == NULL ) {
    fprintf(stderr,"Could not open \'%s\' for writing\n",argv[2]);
    exit(1);
  }

  if( fprintf(tec_fp,"Title=\"%s\"\n",title) < 0 ) {
    fprintf(stderr,"Could not write title\n");
    goto Error;
  }

  if( fprintf(tec_fp,"Variables=\"X\",\"Y\",\"Z\",\"Id\",\"SetId\"") < 0 ) {
    fprintf(stderr,"Could not write Variables specifier\n");
    goto Error;
  }

  /* Read 1st zone header to determine the number of functions/zone */
  if( readBinaryDDFZoneHeader(ztitle, &type, &npts, &nfunc, &nelem) != 0L ) {
    fprintf(stderr,"Could not read Zone 1 header\n");
    goto Error;
  }

  for( j=1; j<=nfunc; j++ ) {
    if( fprintf(tec_fp,",\"F%ld\"",j) < 0 ) {
      fprintf(stderr,"Could not write Function Variables specifier\n");
      goto Error;
    }
  }
  if( fprintf(tec_fp,"\n") < 0 ) {
    fprintf(stderr,"Could not complete Variables specifier\n");
    goto Error;
  }
  nf1 = nfunc;

  if( (func=(double*)malloc(nfunc*sizeof(double))) == NULL ) {
    fprintf(stderr,"Could not allocate %ld function values\n",nfunc);
    goto Error;
  }

  /* Zones */
  for( n=0; n<nzone; n++ ) {

    if( fprintf(tec_fp,"Zone T=\"%s\", I=%ld, J=%ld, F=FEPOINT\n",ztitle,npts,nelem) < 0 ) {
      fprintf(stderr,"Could not write Zone title\n");
      goto Error;
    }

    if( nfunc != nf1 ) {
      fprintf(stderr,"WARNING: %ld functions in Zone %ld (%ld in Zone 1).  Writing %ld\n",nfunc,n+1,nf1,nf1);
    }

    if( nfunc > nf1 ) {
      if( (func=(double*)realloc(func,nfunc*sizeof(double))) == NULL ) {
        fprintf(stderr,"Could not reallocate %ld function values\n",nfunc);
        goto Error;
      }
    }
    nv = (nfunc < nf1) ? nfunc : nf1;

    for( i=0; i<npts; i++ ) {
      if( readBinaryDDFPoint(&x, &y, &z, &pid, &setid, func) ) {
        fprintf(stderr,"Could not read point %ld\n",i+1);
        goto Error;
      }

      if( fprintf(tec_fp,"%21.15g,%21.15g,%21.15g,%ld,%ld",x,y,z,pid,setid) < 0 ) {
        fprintf(stderr,"Could not write point %ld\n",i+1);
        goto Error;
      }

      for( j=0; j<nv; j++ ) {
        if( fprintf(tec_fp," %21.15g",func[j]) < 0 ) {
          fprintf(stderr,"Could not write point %ld, function %ld\n",i+1,j+1);
          goto Error;
        }
      }

      /* Possibly pad with 0.0 */
      for( j=nv; j<nf1; j++ ) {
        if( fprintf(tec_fp,",0.0") < 0 ) {
          fprintf(stderr,"Could not pad point %ld, function %ld\n",i+1,j+1);
          goto Error;
        }
      }
      if( fprintf(tec_fp,"\n") < 0 ) {
        fprintf(stderr,"Could not complete Point %ld\n",i+1);
        goto Error;
      }
    }

    for( i=0; i<nelem; i++ ) {
      if( readBinaryDDFElement(&npe, &id, &pid, &setid, elem) ) {
        fprintf(stderr,"Could not read element %ld\n",i+1);
        goto Error;
      }

      if( npe > MAX_VERTS ) {
        fprintf(stderr,"WARNING: Element %ld, %ld Verticies exceeds the max of %d.  Writing %d\n",i+1,npe,MAX_VERTS,MAX_VERTS);
      }

      for( j=0; j<MAX_VERTS; j++ ) {
        if( fprintf(tec_fp," %ld",elem[j%npe]) < 0 ) {
          fprintf(stderr,"Could not write point %ld\n",i+1);
          goto Error;
        }
      }
      if( fprintf(tec_fp,"\n") < 0 ) {
        fprintf(stderr,"Could not complete Element %ld\n",i+1);
        goto Error;
      }
    }

    if( n+1 < nzone ) {
      /* Read next zone header */
      if( readBinaryDDFZoneHeader(ztitle,&type,&npts,&nfunc,&nelem) != 0L ) {
        fprintf(stderr,"Could not read Zone %ld header\n",n+2);
        goto Error;
      }
    }
  }

  exit(0);

Error:
  if(func) free(func);
  if(tec_fp) fclose(tec_fp);
  readBinaryDDFComplete();
  exit(1);
}

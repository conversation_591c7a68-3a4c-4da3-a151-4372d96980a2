AC_DEFUN([AX_FORTRAN_OPEN_STREAM],
[AC_CACHE_CHECK([fortran open for access='stream'],
 ax_cv_fortran_open_stream,
 [AC_LANG_PUSH(Fortran)
  AC_COMPILE_IFELSE(
  [
       program main
       open(10,file='stream.dat',access='stream')
       end
  ],
  [ax_cv_fortran_open_stream=yes],
  [ax_cv_fortran_open_stream=no] 
   )
  AC_LANG_POP(Fortran)
 ])
if test "$ax_cv_fortran_open_stream" != 'no'
then
 AC_DEFINE([HAVE_OPEN_STREAM],[1],[fortran open accepts access stream])
fi
])


module port

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs, only : dp

  implicit none

  private

  public :: port_driver

  real(dp), dimension(:,:), pointer :: last_flow_dvs

  real(dp), dimension(:), pointer :: c, d  ! Scaling coefficients

contains

!================================= PORT_DRIVER ===============================80
!
!  Performs a multipoint optimization using PORT
!
!=============================================================================80
  subroutine port_driver(max_low_functions,max_design_cycles,tau_subproblem,   &
                         io,what_to_do,restart_optimization,ammo_directory,    &
                         lss_flag)

    use allocations,       only : my_alloc_ptr
    use kinddefs,          only : dp
    use lmpi,              only : lmpi_die
    use analysis,          only : perform_analysis
    use sensitivity,       only : perform_sensitivity_analysis
    use design_types,      only : opt_data_type
    use designs,           only : load_optimization_data, been_there,          &
                                  unload_optimization_data,                    &
                                  free_optimization_data
    use system_extensions, only : se_flush, se_chdir
    use utilities,         only : check_for_stop
    use file_utils,        only : rm
    use nml_design,        only : n_design_pts, model_variables

    integer, intent(in) :: max_low_functions, max_design_cycles
    integer, intent(in) :: io, what_to_do

    real(dp), intent(in) :: tau_subproblem

    logical, intent(in) :: lss_flag
    logical, intent(in) :: restart_optimization

    character(len=*), intent(in) :: ammo_directory

    integer :: i                 ! Loop indices
    integer :: iunit = 68        ! PORT output file number
    integer :: port_int_size     ! Integer workspace size for PORT
    integer :: port_real_size    ! Real workspace size for PORT
    integer :: port_ndv          ! # of DV's PORT sees
    integer :: istop             ! stop?

    integer, dimension(:), pointer :: port_int_workspace

    real(dp) :: port_objectives

    real(dp), dimension(:),   pointer :: port_real_workspace
    real(dp), dimension(:),   pointer :: port_scale
    real(dp), dimension(:),   pointer :: port_gradients
    real(dp), dimension(:),   pointer :: port_design_variables
    real(dp), dimension(:,:), pointer :: port_dvbounds

    type(opt_data_type), dimension(n_design_pts) :: opt_data

  continue

! Load the optimization data for each model

    do i = 1, n_design_pts
      call se_chdir(model_variables(i)%model_directory)
      opt_data(i)%allocated = .false.
      call load_optimization_data(opt_data(i),io,'PORT: Location 1')
      opt_data(i)%scale = 1.0_dp
    end do

! Load the design variable bounds into the PORT-style array
! using the values from the first model.  We assume the
! parameterization, bounds, active DV's, etc are the same
! across all models

    call my_alloc_ptr(port_dvbounds, 2, opt_data(1)%ndv)
    port_dvbounds(1,:) = opt_data(1)%lower_bounds(:)
    port_dvbounds(2,:) = opt_data(1)%upper_bounds(:)

! Allocate PORT work arrays

    port_int_size  = 59 + opt_data(1)%ndv
    port_real_size = 71 + opt_data(1)%ndv * (opt_data(1)%ndv + 19) / 2

    call my_alloc_ptr(port_int_workspace,  port_int_size)
    call my_alloc_ptr(port_real_workspace, port_real_size)

! Open the PORT output file

    open(iunit,file='port.output')

! Initialize the PORT workspace arrays

#ifdef HAVE_PORT3
    call divset(2, port_int_workspace, port_int_size, port_real_size,          &
                port_real_workspace)
#else
    write(*,*) 'You do not have PORT installed.'
    call lmpi_die
#endif

! Set the maximum number of function evaluations PORT is allowed

    port_int_workspace(17) = max_low_functions

! Set the maximum number of iterations PORT is allowed

    port_int_workspace(18) = max_design_cycles

! Tell PORT about its output file

    port_int_workspace(21) = iunit

! Set the relative function convergence criterion for PORT

    port_real_workspace(32) = tau_subproblem

! Allocate and set some initial PORT inputs based on the values in the
! first model

    call my_alloc_ptr(port_scale,            opt_data(1)%ndv)
    call my_alloc_ptr(port_gradients,        opt_data(1)%ndv)
    call my_alloc_ptr(port_design_variables, opt_data(1)%ndv)

    port_scale            = opt_data(1)%scale
    port_objectives       = 0.0_dp
    port_gradients        = 0.0_dp
    do i = 1, opt_data(1)%nobjectives
      port_objectives = port_objectives + opt_data(1)%weights(i)*              &
       (opt_data(1)%objectives(i)-opt_data(1)%targets(i))**opt_data(1)%powers(i)

      port_gradients(:) = port_gradients(:) + opt_data(1)%weights(i)*          &
                                              opt_data(1)%powers(i)*           &
                       (opt_data(1)%objectives(i)-opt_data(1)%targets(i))**    &
                       (opt_data(1)%powers(i)-1.0_dp)*opt_data(1)%gradients(i,:)
    end do
    port_ndv              = opt_data(1)%ndv
    port_design_variables = opt_data(1)%design_variables

! Initialize the arrays to store the previous set of DV's for each model

    call my_alloc_ptr(last_flow_dvs, n_design_pts, port_ndv)

! Allocate routine sets things to zero, so we ought
! to set it to something crazy, since this could
! very well be our starting design point

    last_flow_dvs = huge(1.0_dp)

! Call PORT

100 continue

    call se_chdir(ammo_directory)
    call check_for_stop(istop)
    if ( istop > 0 ) then
      call rm('stop.dat')
      write(*,*) 'User requested premature stop...'
      stop
    endif

    call scale_problem(port_ndv,port_dvbounds,port_design_variables,           &
                       port_gradients)
#ifdef HAVE_PORT3
    call drmngb(port_dvbounds,           port_scale,                           &
                port_objectives,         port_gradients,                       &
                port_int_workspace,      port_int_size,                        &
                port_real_size,          port_ndv,                             &
                port_real_workspace,     port_design_variables)
#else
    write(*,*) 'You do not have PORT installed.'
    call lmpi_die
#endif

    call unscale_problem(port_ndv,port_dvbounds,port_design_variables,         &
                         port_gradients)

! Flush the PORT output

    call se_flush(iunit)

! Write latest optimization data - this assumes that the only
! outbound variable from the PORT call is the design variable
! vector

    do i = 1, n_design_pts
      opt_data(i)%design_variables = port_design_variables
      call se_chdir(model_variables(i)%model_directory)
      call unload_optimization_data(opt_data(i),io,'After returning from PORT')
    end do

! Based on the info returned by PORT, either get function, gradient, or exit

    select case ( port_int_workspace(1) )
    case (1)

      do i = 1, n_design_pts
        if ( .not. been_there(port_ndv,port_design_variables,                  &
            last_flow_dvs(i,:),.false.) ) then
          call se_chdir(model_variables(i)%model_directory)
          call perform_analysis(model_variables(i)%restart_flow,               &
                                i,n_design_pts,what_to_do,restart_optimization,&
                                model_variables(i)%desc_directory)
          call load_optimization_data(opt_data(i),io,'PORT: Location 2')
          last_flow_dvs(i,:) = port_design_variables
        endif
      end do

! Combine single point functions into a composite function

      call combine_mp_functions(port_objectives,opt_data)

    case (2)

      do i = 1, n_design_pts

        call se_chdir(model_variables(i)%model_directory)

        if ( .not. been_there(port_ndv,port_design_variables,                  &
            last_flow_dvs(i,:),.true.) ) then
          write(*,*) 'WARNING: Sensitivity analysis requested at design point'
          write(*,*) 'different from previous function analysis!'
          call perform_analysis(model_variables(i)%restart_flow,               &
                                i,n_design_pts,what_to_do,restart_optimization,&
                                model_variables(i)%desc_directory)
          call load_optimization_data(opt_data(i),io,'PORT: Location 3')
          last_flow_dvs(i,:) = port_design_variables
        endif

        call perform_sensitivity_analysis(model_variables(i)%restart_dual,     &
                                          i,n_design_pts,what_to_do,lss_flag)
        call load_optimization_data(opt_data(i),io,'PORT: Location 4')
      end do

! Combine single point gradients into a composite gradient

      call combine_mp_gradients(port_ndv,port_gradients,opt_data)

    case default

      call interpret_port_output(port_int_workspace(1),io)
      goto 200

    end select

! Return to PORT

    goto 100

200 continue

! Close the PORT output file and deallocate its memory

    close(iunit)

    deallocate(port_int_workspace,port_real_workspace,port_dvbounds)
    deallocate(port_scale,port_gradients,port_design_variables,last_flow_dvs)

! Free the memory used by the optimization data derived type

    do i = 1, n_design_pts
      call free_optimization_data(opt_data(i))
    end do

  end subroutine port_driver


!============================== INTERPRET_PORT_OUTPUT ========================80
!
!  Interprets the flag returned from PORT after an optimization
!
!=============================================================================80

  subroutine interpret_port_output(port_flag,io)

    use system_extensions, only : se_flush

    integer, intent(in) :: port_flag, io

  continue

    write(*,*) 'PORT has returned the following flag: ',port_flag
    write(io,*) 'PORT has returned the following flag: ',port_flag

    select case ( port_flag )
    case (3:6)
      write(*,*) 'Good news.'
      write(io,*) 'Good news.'
    case (7:14)
      write(*,*) 'Semi-good news...you can still restart.'
      write(io,*) 'Semi-good news...you can still restart.'
    case (15:85)
      write(*,*) 'Lousy news...you cannot even restart.'
      write(io,*) 'Lousy news...you cannot even restart.'
    case default
      write(*,*) 'Unknown news.'
      write(io,*) 'Unknown news.'
    end select

    select case ( port_flag )
    case (3)
      write(*,*) 'X-convergence: The current iterate appears to be a scaled'
      write(*,*) 'distance of at most V(XCTOL) = V(33) from a locally optimal'
      write(*,*) 'point.'
    case (4)
      write(*,*) 'Relative function convergence: The current objective function'
      write(*,*) 'value f(x) appears to differ from a locally optimal value by'
      write(*,*) 'at most |f(x)|.V(RFCTOL) = |f(x)|.V(32).'
    case (5)
      write(*,*) 'Both X- and relative function convergence:'
      write(*,*) 'X-convergence: The current iterate appears to be a scaled'
      write(*,*) 'distance of at most V(XCTOL) = V(33) from a locally optimal'
      write(*,*) 'point.'
      write(*,*) 'Relative function convergence: The current objective function'
      write(*,*) 'value f(x) appears to differ from a locally optimal value by'
      write(*,*) 'at most |f(x)|.V(RFCTOL) = |f(x)|.V(32).'
    case (6)
      write(*,*) 'Absolute function convergence: |f(x)|<V(AFCTOL) = V(31). This'
      write(*,*) 'test is only of interest in problems where f(x) = 0 means a'
      write(*,*) 'perfect fit, such as nonlinear least-squares problems.'
    case (7)
      write(*,*) 'Singular convergence: x may have too many free components.'
    case (8)
      write(*,*) 'False convergence: the gradient gradf(x) may be computed'
      write(*,*) 'incorrectly, the other stopping tolerances may be too tight,'
      write(*,*) 'or either f or gradf may be discontinuous near the current'
      write(*,*) 'iterate x.'
    case (9)
      write(*,*) 'Function evaluation limit; no convergence after'
      write(*,*) 'IV(MXFCAL) = IV(17) evaluations of f(x).'
    case (10)
      write(*,*) 'Iteration limit: No convergence after IV(MXITER) = IV(18)'
      write(*,*) 'iterations.'
    case (11)
      write(*,*) 'STOPX returned .TRUE.: You supplied a system-dependent STOPX'
      write(*,*) 'routine and hit the BREAK key.'
    case (14)
      write(*,*) 'Storage has been allocated (after a call with IV(1)=13).'
    case (15)
      write(*,*) 'LIV too small.'
    case (16)
      write(*,*) 'LV too small.'
    case (17)
      write(*,*) 'Restart attempted with problem dimensions changed.'
    case (18)
      write(*,*) 'd has a negative component and IV(DTYPE) <= 0.'
    case (19:43)
      write(*,*) 'V(IV(1)) is out of range.'
    case (63)
      write(*,*) 'f(x) cannot be computed at the initial x.'
    case (64)
      write(*,*) 'Bad parameters on an internal call (should not occur).'
    case (65)
      write(*,*) 'The gradient could not be computed at x.'
    case (66)
      write(*,*) 'Bad input array - if this return is relevant, the associated'
      write(*,*) 'PORT reference sheet will say so and explain what is good'
      write(*,*) 'and bad.'
    case (67)
      write(*,*) 'Bad first parameter to IVSET.'
    case (68:69)
      write(*,*) 'Bugs encountered (should not occur).'
    case (70)
      write(*,*) 'Could not get initial S matrix by finite differences.'
    case (80)
      write(*,*) 'IV(1) was out of range (exceeded 14).'
    case (81)
      write(*,*) 'Bad problem dimensions (e.g. a nonpositive number of'
      write(*,*) 'variables or, for regression routines, number of'
      write(*,*) 'observations).'
    case (82:83)
      write(*,*) 'Inconsistent bounds.'
    case (84)
      write(*,*) 'Some row of the constraint matrix, C, is all zeros.'
    case (85)
      write(*,*) 'Inconsistent constraints.'
    case default
      write(*,*) 'Nothing seems to be known about this PORT flag...'
    end select

    call se_flush(6)

  end subroutine interpret_port_output


!============================== COMBINE_MP_FUNCTIONS =========================80
!
!  Combines objective functions from single design points into a
!  weighted multipoint objective function
!
!=============================================================================80
  subroutine combine_mp_functions(port_objectives,opt_data)

    use kinddefs,     only : dp
    use design_types, only : opt_data_type, ks_function, ks_rho
    use nml_design,   only : model_variables, n_design_pts

    real(dp), intent(out) :: port_objectives

    type(opt_data_type), dimension(n_design_pts), intent(in) :: opt_data

    integer :: i, j

    real(dp) :: obj_max, ks_sum

  continue

    port_objectives = 0.0_dp

    if ( ks_function ) then

! First find the maximum objective function at the current design point

      obj_max = -huge(1.0_dp)
      do i = 1, n_design_pts
        obj_max = max(obj_max,opt_data(i)%objectives(1))
      end do

! Now form the contributions to the composite KS function

      ks_sum = 0.0_dp
      do i = 1, n_design_pts
        ks_sum = ks_sum + exp(ks_rho*(opt_data(i)%objectives(1) - obj_max))
      end do

! Turn this into the final KS function

      port_objectives = obj_max + 1.0_dp/ks_rho*log(ks_sum)

      write(*,*) 'KS Function: obj_max, ks_sum, port_objectives = ',           &
                 obj_max, ks_sum, port_objectives

    else

      do i = 1, n_design_pts
        do j = 1, opt_data(i)%nobjectives
          port_objectives = port_objectives +                                  &
                            model_variables(i)%weight *                        &
                            opt_data(i)%weights(j)*                            &
                            (opt_data(i)%objectives(j)-opt_data(i)%targets(j)) &
                           **opt_data(i)%powers(j)
        end do
      end do

    endif

  end subroutine combine_mp_functions


!============================== COMBINE_MP_GRADIENTS =========================80
!
!  Combines gradients from single design points into a weighted multipoint
!  gradient
!
!=============================================================================80
  subroutine combine_mp_gradients(ndv,port_gradients,opt_data)

    use kinddefs,     only : dp
    use design_types, only : opt_data_type, ks_function, ks_rho
    use nml_design,   only : model_variables, n_design_pts

    integer, intent(in) :: ndv

    real(dp), dimension(ndv), intent(out) :: port_gradients

    type(opt_data_type), dimension(n_design_pts), intent(in) :: opt_data

    integer :: i, j

    real(dp) :: obj_max, num_sum, den_sum

  continue

    port_gradients(:) = 0.0_dp

    if ( ks_function ) then

! First find the maximum objective function at the current design point

      obj_max = -huge(1.0_dp)
      do i = 1, n_design_pts
        obj_max = max(obj_max,opt_data(i)%objectives(1))
      end do

! Form the sums for the numerator and denominator

      do i = 1, opt_data(1)%ndv
        num_sum = 0.0_dp
        den_sum = 0.0_dp
        do j = 1, n_design_pts
          num_sum = num_sum + opt_data(j)%gradients(1,i) *                     &
                               exp(ks_rho*(opt_data(j)%objectives(1) - obj_max))
          den_sum = den_sum + exp(ks_rho*(opt_data(j)%objectives(1) - obj_max))
        end do
        port_gradients(i) = num_sum / den_sum
      end do

      write(*,*) 'KS Function: obj_max, port_gradients = ',                    &
                  obj_max, port_gradients

    else

      do i = 1, n_design_pts
        do j = 1, opt_data(i)%nobjectives
          port_gradients(:) = port_gradients(:) +                              &
                              model_variables(i)%weight*                       &
                              opt_data(i)%weights(j)*opt_data(i)%powers(j)*    &
              (opt_data(i)%objectives(j)-opt_data(i)%targets(j))               &
                           **(opt_data(i)%powers(j)-1.0_dp)*                   &
                              opt_data(i)%gradients(j,:)
        end do
      end do

    endif

  end subroutine combine_mp_gradients


!============================== SCALE_PROBLEM ================================80
!
!  Scales problem for PORT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine scale_problem(n,b,x,g)

    use allocations, only : my_alloc_ptr

    integer, intent(in) :: n

    real(dp), dimension(2,n), intent(inout) :: b
    real(dp), dimension(n),   intent(inout) :: x, g

    logical, save :: first_time = .true.

  continue

    if ( first_time ) then

! determine the scaling coefficients

      call my_alloc_ptr(c,n)
      call my_alloc_ptr(d,n)

      c(:) = 0.5_dp*(b(1,:)+b(2,:))
      d(:) = 0.5_dp*(b(2,:)-b(1,:))

      first_time = .false.
    endif

! scale the DV's

    x(:) = (x(:)-c(:)) / d(:)

! scale the bounds

    b(1,:) = (b(1,:)-c(:)) / d(:)
    b(2,:) = (b(2,:)-c(:)) / d(:)

! scale the gradient

    g(:) = d(:)*g(:)

  end subroutine scale_problem


!============================== UNSCALE_PROBLEM ==============================80
!
!  Unscales problem for PORT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine unscale_problem(n,b,x,g)

    integer, intent(in) :: n

    real(dp), dimension(2,n), intent(inout) :: b
    real(dp), dimension(n),   intent(inout) :: x, g

  continue

! unscale the DV's

    x(:) = d(:)*x(:) + c(:)

! unscale the bounds

    b(1,:) = d(:)*b(1,:) + c(:)
    b(2,:) = d(:)*b(2,:) + c(:)

! unscale the gradient

    g(:) = g(:)/d(:)

  end subroutine unscale_problem

end module port

module suggar_info

  implicit none

  private

  public :: is_fun3d, is_suggar, is_dci_io
  public :: suggar_nproc, world_nproc, fun3d_nproc
  public :: fun3d_comm, suggar_comm, dci_io_comm, world_comm
  public :: fun3d_id_l2g, suggar_id_l2g, dci_io_id_l2g
  public :: n_dc_groups, my_dc_group, active_dc_group

  logical :: is_fun3d  = .true.
  logical :: is_suggar = .false.
  logical :: is_dci_io = .false.

  integer            :: fun3d_nproc
  integer            :: suggar_nproc = 1
  integer            :: active_dc_group
  integer            :: fun3d_comm, suggar_comm, world_comm
  integer            :: dci_io_comm
  integer            :: world_nproc
  integer            :: my_dc_group = 0
  integer, parameter :: n_dc_groups = 1

  integer, dimension(:), allocatable :: fun3d_id_l2g
  integer, dimension(:), allocatable :: suggar_id_l2g
  integer, dimension(:), allocatable :: dci_io_id_l2g

end module suggar_info

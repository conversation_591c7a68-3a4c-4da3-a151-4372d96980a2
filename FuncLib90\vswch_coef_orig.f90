

!============================== VSWCH_COEF_ORIG ==============================80
!
!  Computes the viscous switching coefficient used turn off the inviscid
!  switching coefficient on cell faces that have small cell reynolds numbers
!
!=============================================================================80

  pure function vswch_coef_orig(rhol, rhor, q2l, q2r, chalf, vol1, vol2, area, &
                                power, Mu_face)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_1, my_half

    integer,  intent(in) :: power

    real(dp), intent(in) :: rhol, rhor
    real(dp), intent(in) :: q2l, q2r, chalf
    real(dp), intent(in) :: vol1, vol2, area
    real(dp), intent(in) :: Mu_face

    real(dp)             :: vswch_coef_orig

    real(dp) :: RO_face, U_face, L_face, RE_face

    real(dp), parameter :: RE_min  =    50.0_dp
    real(dp), parameter :: RE_max  =   500.0_dp

  continue

    RO_face = my_half*(rhol + rhor)
    U_face  = my_half*(my_half*(sqrt(q2l)+sqrt(q2r))+chalf)
    L_face  = my_half*(vol1 + vol2)/area
    RE_face = RO_face*U_face*L_face/MU_face
    vswch_coef_orig  = my_1-max(my_0, min(my_1, (RE_face    -RE_min)/          &
                                           (RE_max-RE_min)))**power

  end function vswch_coef_orig

! The final main program for the flow solver simply invokes entry points
! in the proper order to execute the code.

program nodet

  use flow,      only : initialize_project, initialize_data, iterate, post,    &
                        fas_cycles, start_of_timestep_loop, end_of_timestep_loop
  use utilities, only : my_clock

  implicit none

  integer :: it

  logical :: bcontinue

continue

  call initialize_project()
  call initialize_data(bcontinue)

  if (bcontinue) then
    it = 0
    call my_clock(start_of_timestep_loop)
    do
      it = it + 1
      if (it > fas_cycles) exit
      call iterate(bcontinue)
      if (.not.bcontinue) exit
    end do
    call my_clock(end_of_timestep_loop)
  endif

  call post

end program nodet

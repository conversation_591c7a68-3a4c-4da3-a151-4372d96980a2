!================================== DET_3x3 ==================================80
!
! Function to find the determinant of a 3x3 matrix whose coefficients are
! a, b and c
!
! | a1  b1  c1|      | b2 c2 |      | a2 c2 |      | a2 b2 |
! | a2  b2  c2| = a1*| b3 c3 | - b1*| a3 c3 | + c1*| a3 b3 |
! | a3  b3  c3|
!
!
!=============================================================================80

  pure function det_3x3(a1, a2, a3, b1, b2, b3, c1, c2, c3)

    use kinddefs, only : dp

    real(dp), intent(in) :: a1, a2, a3, b1, b2, b3, c1, c2, c3
    real(dp)             :: det_3x3

  continue

    det_3x3 = a1*(b2*c3-c2*b3) + b1*(c2*a3-a2*c3) + c1*(a2*b3-b2*a3)

  end function det_3x3

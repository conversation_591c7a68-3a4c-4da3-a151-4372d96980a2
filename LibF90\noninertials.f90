module noninertials

  use kinddefs, only : dp
  use nml_noninertial_reference_frame, only : xrotcen_ni  => rotation_center_x,&
                                              yrotcen_ni  => rotation_center_y,&
                                              zrotcen_ni  => rotation_center_z,&
                                              xrotrate_ni => rotation_rate_x,  &
                                              yrotrate_ni => rotation_rate_y,  &
                                              zrotrate_ni => rotation_rate_z

  implicit none

  private

  public :: xrotcen_ni,yrotcen_ni,zrotcen_ni
  public :: xrotrate_ni,yrotrate_ni,zrotrate_ni
  public :: noninrhs,noninlhs,setup_nonin, setup_nonin_observer

contains


!================================== SETUP_NONIN ==============================80
!
! Computes geometrically conservative rotation velocity components
!
!============================================================================80

  subroutine setup_nonin(grid)

    use bc_types,     only : bcgrid_type
    use grid_types,   only : grid_type
    use grid_metrics, only : compute_dual_metrics
    use lmpi,         only : lmpi_master, lmpi_die
    use bc_names,     only : symmetry_x, symmetry_y, symmetry_z
    use nml_global,   only : moving_grid

    type(grid_type), intent(inout) :: grid

    type(bcgrid_type), dimension(:),  pointer :: bc

    integer :: i, ib, inode, n

    real(dp) :: rx, ry, rz

    continue

    bc => grid%bc

! Make sure we are not performing noninertial rotations that conflict
! with symmetry conditions present

    do ib = 1, grid%nbound
      if ( bc(ib)%ibc == symmetry_x ) then
        if ( yrotrate_ni > 100.0_dp*epsilon(1.0_dp) .or.                       &
             zrotrate_ni > 100.0_dp*epsilon(1.0_dp) ) then
          if (lmpi_master) then
            write(*,*) 'Incompatible noninertial rates and symmetry conditions.'
          endif
          call lmpi_die
        endif
      endif
      if ( bc(ib)%ibc == symmetry_y ) then
        if ( xrotrate_ni > 100.0_dp*epsilon(1.0_dp) .or.                       &
             zrotrate_ni > 100.0_dp*epsilon(1.0_dp) ) then
          if (lmpi_master) then
            write(*,*) 'Incompatible noninertial rates and symmetry conditions.'
          endif
          call lmpi_die
        endif
      endif
      if ( bc(ib)%ibc == symmetry_z ) then
        if ( xrotrate_ni > 100.0_dp*epsilon(1.0_dp) .or.                       &
             yrotrate_ni > 100.0_dp*epsilon(1.0_dp) ) then
          if (lmpi_master) then
            write(*,*) 'Incompatible noninertial rates and symmetry conditions.'
          endif
          call lmpi_die
        endif
      endif
    end do

!   Fill the dxdt, dydt and dzdt arrays with the rotation velocity components

    do n = 1, grid%nnodes01

      rx = grid%x(n) - xrotcen_ni
      ry = grid%y(n) - yrotcen_ni
      rz = grid%z(n) - zrotcen_ni

      grid%dxdt(n) = yrotrate_ni*rz - zrotrate_ni*ry
      grid%dydt(n) = zrotrate_ni*rx - xrotrate_ni*rz
      grid%dzdt(n) = xrotrate_ni*ry - yrotrate_ni*rx

    end do

!   Copy boundary velocity components into boundary arrays

    do ib = 1,grid%nbound

      do i=1,bc(ib)%nbnode
        inode = bc(ib)%ibnode(i)
        bc(ib)%bdxdt(i) = grid%dxdt(inode)
        bc(ib)%bdydt(i) = grid%dydt(inode)
        bc(ib)%bdzdt(i) = grid%dzdt(inode)
      end do

    end do

!   Compute rotation speeds for dual grid

    call compute_dual_metrics(grid, moving_grid, update_face_speeds=.true.)

  end subroutine setup_nonin


!============================= SETUP_NONIN_OBSERVER =========================80
!
!   Sets up an observer moving with the noninertial frame in case output is
!   requested relative to the noninertial frame
!
!============================================================================80

  subroutine setup_nonin_observer()

    use moving_body_types, only : observer

  continue

    observer%parent_name             = ''
    observer%rotate                  = 1
    observer%rotrate                 = sqrt(xrotrate_ni**2 + yrotrate_ni**2 +  &
                                            zrotrate_ni**2)
    observer%rfreq_rotate            = 0._dp
    observer%rotation_vector%xorigin = xrotcen_ni
    observer%rotation_vector%yorigin = yrotcen_ni
    observer%rotation_vector%zorigin = zrotcen_ni
    observer%rotation_vector%tx      = xrotrate_ni/observer%rotrate
    observer%rotation_vector%ty      = yrotrate_ni/observer%rotrate
    observer%rotation_vector%tz      = zrotrate_ni/observer%rotrate
    observer%translate               = 0
    observer%transrate               = 0._dp
    observer%rfreq_trans             = 0._dp
    observer%translation_vector%sx   = 0._dp
    observer%translation_vector%sy   = 0._dp
    observer%translation_vector%sz   = 1._dp

    observer%transform_matrix(:,:) = 0._dp
    observer%transform_matrix(1,1) = 1._dp
    observer%transform_matrix(2,2) = 1._dp
    observer%transform_matrix(3,3) = 1._dp
    observer%transform_matrix(4,4) = 1._dp
    observer%transform_matrix(1,4) = xrotcen_ni
    observer%transform_matrix(2,4) = yrotcen_ni
    observer%transform_matrix(3,4) = zrotcen_ni

    observer%inv_transform(:,:) = 0._dp
    observer%inv_transform(1,1) = 1._dp
    observer%inv_transform(2,2) = 1._dp
    observer%inv_transform(3,3) = 1._dp
    observer%inv_transform(4,4) = 1._dp
    observer%inv_transform(1,4) = -xrotcen_ni
    observer%inv_transform(2,4) = -yrotcen_ni
    observer%inv_transform(3,4) = -zrotcen_ni

    observer%xcg = xrotcen_ni
    observer%ycg = yrotcen_ni
    observer%zcg = zrotcen_ni

    observer%body_lin_vel(1) = 0._dp
    observer%body_lin_vel(2) = 0._dp
    observer%body_lin_vel(3) = 0._dp

    observer%body_ang_vel(1) = xrotrate_ni
    observer%body_ang_vel(2) = yrotrate_ni
    observer%body_ang_vel(3) = zrotrate_ni

  end subroutine setup_nonin_observer


!================================= NONINRHS =================================80
!
! Subroutine noninrhs computes the source term contribution to the right hand
! side due to non-inertial rotation
!
! Note: this routine assumes primitive varables as input
!
!============================================================================80

  subroutine noninrhs(eqn_set, vol,nnodes0,nnodes01,qnode,res, n_tot, njac)

    use solution_types, only : compressible, incompressible

    integer, intent(in) :: eqn_set
    integer, intent(in) :: nnodes0, nnodes01, n_tot, njac
    real(dp),  dimension(n_tot,nnodes01),     intent(in)   :: qnode
    real(dp),  dimension(njac,nnodes01),      intent(inout):: res
    real(dp),  dimension(nnodes01),           intent(in)   :: vol

    integer     :: n
    real(dp)    :: u,v,w,rho

    continue

    select case (eqn_set)

      case (compressible)

        do n = 1, nnodes0

          rho = qnode(1,n)
          u   = qnode(2,n)
          v   = qnode(3,n)
          w   = qnode(4,n)

!         (1/2) coriolis acceleration - omega x u

          res(2,n) = res(2,n) + rho*vol(n)*(yrotrate_ni*w - zrotrate_ni*v)
          res(3,n) = res(3,n) + rho*vol(n)*(zrotrate_ni*u - xrotrate_ni*w)
          res(4,n) = res(4,n) + rho*vol(n)*(xrotrate_ni*v - yrotrate_ni*u)

        end do

      case (incompressible)

        do n = 1, nnodes0

          u   = qnode(2,n)
          v   = qnode(3,n)
          w   = qnode(4,n)

!         (1/2) coriolis acceleration - omega x u

          res(2,n) = res(2,n) + vol(n)*(yrotrate_ni*w - zrotrate_ni*v)
          res(3,n) = res(3,n) + vol(n)*(zrotrate_ni*u - xrotrate_ni*w)
          res(4,n) = res(4,n) + vol(n)*(xrotrate_ni*v - yrotrate_ni*u)

        end do

      case default

      end select

  end subroutine noninrhs


!================================== NONINLHS =================================80
!
!  Subroutine noninlhs computes the source jacobian contribution to the left
!  hand side of the equation due to the non-inertial rotation terms.
!
! Note: this routine assumes conservative varables as input
!
!=============================================================================80

  subroutine noninlhs(eqn_set, vol, nnodes0, nnodes01, a_diag, njac, g2m)

    use solution_types, only : compressible, incompressible

    integer,                                    intent(in)    :: eqn_set
    integer,                                    intent(in)    :: nnodes0
    integer,                                    intent(in)    :: nnodes01
    integer,                                    intent(in)    :: njac
    integer, dimension(:),                      intent(in)    :: g2m
    real(dp), dimension(njac,njac,nnodes0),     intent(inout) :: a_diag
    real(dp), dimension(nnodes01),              intent(in)    :: vol

    integer :: n, row

    continue

    select case (eqn_set)

      case (incompressible)

        do n = 1, nnodes0

          row = g2m(n)
!         res(2,n) = res(2,n) + vol(n)*(yrotrate_ni*w - zrotrate_ni*v)
!         res(3,n) = res(3,n) + vol(n)*(zrotrate_ni*u - xrotrate_ni*w)
!         res(4,n) = res(4,n) + vol(n)*(xrotrate_ni*v - yrotrate_ni*u)

!         x-momentum

          a_diag(2,3,row) = a_diag(2,3,row) - vol(n)*zrotrate_ni
          a_diag(2,4,row) = a_diag(2,4,row) + vol(n)*yrotrate_ni

!         y-momentum

          a_diag(3,2,row) = a_diag(3,2,row) + vol(n)*zrotrate_ni
          a_diag(3,4,row) = a_diag(3,4,row) - vol(n)*xrotrate_ni

!         z-momentum

          a_diag(4,2,row) = a_diag(4,2,row) - vol(n)*yrotrate_ni
          a_diag(4,3,row) = a_diag(4,3,row) + vol(n)*xrotrate_ni

        end do

      case (compressible)

        do n = 1, nnodes0

          row = g2m(n)
!         res(2,n) = res(2,n) + rho*vol(n)*(yrotrate_ni*w - zrotrate_ni*v)
!         res(3,n) = res(3,n) + rho*vol(n)*(zrotrate_ni*u - xrotrate_ni*w)
!         res(4,n) = res(4,n) + rho*vol(n)*(xrotrate_ni*v - yrotrate_ni*u)

!         x-momentum

          a_diag(2,3,row) = a_diag(2,3,row) - vol(n)*zrotrate_ni
          a_diag(2,4,row) = a_diag(2,4,row) + vol(n)*yrotrate_ni

!         y-momentum

          a_diag(3,2,row) = a_diag(3,2,row) + vol(n)*zrotrate_ni
          a_diag(3,4,row) = a_diag(3,4,row) - vol(n)*xrotrate_ni

!         z-momentum

          a_diag(4,2,row) = a_diag(4,2,row) - vol(n)*yrotrate_ni
          a_diag(4,3,row) = a_diag(4,3,row) + vol(n)*xrotrate_ni

        end do

      case default

    end select

  end subroutine noninlhs


end module noninertials

!============================ DFC_TANGENCY_I =================================80
!
! Correct Roe incompressible flux jacobians (-) for inviscid
!
!=============================================================================80

  pure function dfc_tangency_i(xnorm, ynorm, znorm, dfm )

    real(dp), intent(in) :: xnorm, ynorm, znorm

    real(dp), dimension(4,4), intent(in) :: dfm

    real(dp), dimension(4,4) :: dfc_tangency_i

    integer :: i, j, k

    real(dp), dimension(4,4) :: correction

  continue

    dfc_tangency_i = dfm

    correction(2,2) = -2.0_dp*xnorm*xnorm
    correction(2,3) = -2.0_dp*xnorm*ynorm
    correction(2,4) = -2.0_dp*xnorm*znorm

    correction(3,2) = -2.0_dp*ynorm*xnorm
    correction(3,3) = -2.0_dp*ynorm*ynorm
    correction(3,4) = -2.0_dp*ynorm*znorm

    correction(4,2) = -2.0_dp*znorm*xnorm
    correction(4,3) = -2.0_dp*znorm*ynorm
    correction(4,4) = -2.0_dp*znorm*znorm

    do i=1,4
      do j=2,4
        do k=2,4
          dfc_tangency_i(i,j) = dfc_tangency_i(i,j) + dfm(i,k)*correction(k,j)
        enddo
      enddo
    enddo

  end function dfc_tangency_i

module point_solver

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs,        only : dp, jp, odp, dqp
  use linear_spectral, only : monitor_rms_sr
  use lmpi_app,        only : lmpi_start_xfer, lmpi_complete_xfer,             &
                              lmpi_start_cudampi_xfer,                         &
                              lmpi_complete_cudampi_xfer
  use interp_defs,     only : sendrecv_type
#ifdef HAVE_CUDA
  use lmpi_app,        only : lmpi_xfer
#endif

  implicit none

  private

  public :: point_solve

contains

!================================= POINT_SOLVE ===============================80
!
! Routes code to appropriate low-level point-solve routine.
!
!=============================================================================80

  subroutine point_solve( colored_sweeps, color_indices, max_colored_sweeps,   &
                          ddq, solve_backwards, omega, nb, dq_dim, nr, nm,     &
                          neqmax, neq0, nia, nja, iam, jam, n_eqns,            &
                          sweeps_to_do, res, dq, a_diag_lu, a_off, sr,         &
                          color_periodic_end,color_boundary_end,nnz0,          &
                          color_xfer_indices, a_off_dc)

    use info_depr,      only : partial_pivoting
    use linear_systems, only : monitor_eqn_group_relax
    use periodics,      only : periodic
    use lmpi,           only : lmpi_die

    integer, intent(in) :: neq0, nia, nja, n_eqns, nb, nnz0
    integer, intent(in) :: dq_dim, nr, nm, neqmax, sweeps_to_do
    integer, intent(in) :: colored_sweeps, max_colored_sweeps

    integer, dimension(2,colored_sweeps),     intent(in) :: color_indices
    integer, dimension(2,max_colored_sweeps), intent(in) :: color_xfer_indices
    integer, dimension(colored_sweeps),       intent(in) :: color_periodic_end
    integer, dimension(colored_sweeps),       intent(in) :: color_boundary_end

    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0),        intent(in) :: a_off
    real(odp), dimension(nm,nnz0), optional, intent(in) :: a_off_dc

    type(sendrecv_type), dimension(:), pointer :: sr

    logical, intent(inout) :: ddq
    real(dp), intent(in) :: omega
    integer, intent(in) :: solve_backwards

    integer :: nb_check

  continue

    if ( abs(omega-1._dp) > 0.001_dp ) ddq = .true.

    if ( partial_pivoting ) then
      write(*,*) 'partial pivoting no longer supported in legacy point_solve.'
      call lmpi_die
      stop
    endif

    if ( ddq ) then
      write(*,*) 'ddq no longer supported in legacy point_solve.'
      call lmpi_die
      stop
    endif

    if ( present(a_off_dc) ) then

      !Route monitor_eqn_group_relax through general-block solve
      nb_check = nb
      if(monitor_eqn_group_relax) nb_check = -nb

      call point_solve_dc(colored_sweeps,color_indices,max_colored_sweeps,   &
                         neq0,nia,nja,iam,jam,n_eqns,solve_backwards,nb,     &
                         dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu,  &
                         a_off_dc,sr,color_boundary_end,nnz0)

    else if ( periodic ) then

      !Route monitor_eqn_group_relax through general-block solve
      nb_check = nb
      if(monitor_eqn_group_relax) nb_check = -nb

      select case (nb_check)
      case(7)
        call ppoint_solve_7(colored_sweeps,color_indices,max_colored_sweeps,   &
                            neq0,nia,nja,iam,jam,n_eqns,solve_backwards,       &
                            dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu, &
                            a_off,sr,color_periodic_end,color_boundary_end,nnz0)
      case(6)
        call ppoint_solve_6(colored_sweeps,color_indices,max_colored_sweeps,   &
                            neq0,nia,nja,iam,jam,n_eqns,solve_backwards,       &
                            dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu, &
                            a_off,sr,color_periodic_end,color_boundary_end,nnz0)
      case(5)
        call ppoint_solve_5(colored_sweeps,color_indices,max_colored_sweeps,   &
                            neq0,nia,nja,iam,jam,n_eqns,solve_backwards,       &
                            dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu, &
                            a_off,sr,color_periodic_end,color_boundary_end,nnz0)
      case(4)
        call ppoint_solve_4(colored_sweeps,color_indices,max_colored_sweeps,   &
                            neq0,nia,nja,iam,jam,n_eqns,solve_backwards,       &
                            dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu, &
                            a_off,sr,color_periodic_end,color_boundary_end,nnz0)
      case(3)
        call ppoint_solve_3(colored_sweeps,color_indices,max_colored_sweeps,   &
                            neq0,nia,nja,iam,jam,n_eqns,solve_backwards,       &
                            dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu, &
                            a_off,sr,color_periodic_end,color_boundary_end,nnz0)
      case(2)
        call ppoint_solve_2(colored_sweeps,color_indices,max_colored_sweeps,   &
                            neq0,nia,nja,iam,jam,n_eqns,solve_backwards,       &
                            dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu, &
                            a_off,sr,color_periodic_end,color_boundary_end,nnz0)
      case(1)
        call ppoint_solve_1(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0,nia,nja,iam,jam,n_eqns,solve_backwards,       &
                            dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu, &
                            a_off,sr,color_periodic_end,color_boundary_end,nnz0)
      case(8:)
        write(*,*) 'ppoint_solve_n not implemented.'
        call lmpi_die
        stop
      case default
        write(*,*) 'ppoint_solve_ml not implemented.'
        call lmpi_die
        stop
      end select

    else

      !Route monitor_eqn_group_relax through general-block solve
      nb_check = nb
      if(monitor_eqn_group_relax) nb_check = -nb

      select case (nb_check)
      case(7)
        call point_solve_7(colored_sweeps,color_indices,max_colored_sweeps,    &
                           neq0,nia,nja,iam,jam,n_eqns,solve_backwards,        &
                           dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu,  &
                           a_off,sr,color_boundary_end,nnz0)
      case(6)
        call point_solve_6(colored_sweeps,color_indices,max_colored_sweeps,    &
                           neq0,nia,nja,iam,jam,n_eqns,solve_backwards,        &
                           dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu,  &
                           a_off,sr,color_boundary_end,nnz0)
      case(5)
#ifndef HAVE_CUDA
        call point_solve_5(colored_sweeps,color_indices,max_colored_sweeps,    &
                           neq0,nia,nja,iam,jam,n_eqns,solve_backwards,        &
                           dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu,  &
                           a_off,sr,color_boundary_end,nnz0,color_xfer_indices)
#else
        call point_solve_5_cuda(colored_sweeps,color_indices,                  &
                                max_colored_sweeps,neq0,nia,nja,iam,jam,n_eqns,&
                                ((solve_backwards>1).or.(solve_backwards<-1)), &
                                nb,dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,    &
                                a_diag_lu,a_off,sr)
#endif
      case(4)
        call point_solve_4(colored_sweeps,color_indices,max_colored_sweeps,    &
                           neq0,nia,nja,iam,jam,n_eqns,solve_backwards,        &
                           dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu,  &
                           a_off,sr,color_boundary_end,nnz0)
      case(3)
        call point_solve_3(colored_sweeps,color_indices,max_colored_sweeps,    &
                           neq0,nia,nja,iam,jam,n_eqns,solve_backwards,        &
                           dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu,  &
                           a_off,sr,color_boundary_end,nnz0)
      case(2)
        call point_solve_2(colored_sweeps,color_indices,max_colored_sweeps,    &
                           neq0,nia,nja,iam,jam,n_eqns,solve_backwards,        &
                           dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu,  &
                           a_off,sr,color_boundary_end,nnz0)
      case(1)
        call point_solve_1(colored_sweeps,color_indices,max_colored_sweeps,    &
                           neq0,nia,nja,iam,jam,n_eqns,solve_backwards,        &
                           dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu,  &
                           a_off,sr,color_boundary_end,nnz0)
      case(8:)
        call point_solve_n(colored_sweeps,color_indices,max_colored_sweeps,    &
                           neq0,nia,nja,iam,jam,n_eqns,solve_backwards,nb,     &
                           dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu,  &
                           a_off,sr,color_boundary_end,nnz0)
      case default
        call point_solve_ml(colored_sweeps,color_indices,max_colored_sweeps,   &
                           neq0,nia,nja,iam,jam,n_eqns,solve_backwards,nb,     &
                           dq_dim,nr,nm,sweeps_to_do,neqmax,res,dq,a_diag_lu,  &
                           a_off,sr,color_boundary_end,nnz0)
      end select
    endif

  end subroutine point_solve

!================================ POINT_SOLVE_DC =============================80
!
! Performs G-S iteration on nxn system of specified equation set - GG decoupled
!
!=============================================================================80

  subroutine point_solve_dc(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam, n_eqns, solve_backwards, &
                            nb, dq_dim, nr, nm, n_sweeps, neqmax, res, dq,     &
                            a_diag_lu, a_off, sr, color_boundary_end, nnz0)

    use generic_gas_map, only : rf_inv

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax, nb

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end

    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nnz0),   intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,j1,k,n,sweep,icol,istart,iend,start,end,ipass
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride

    real(jp) :: change_sign

    real(jp), dimension(nb,1) :: b

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      color_sweep : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(2,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            do k = 1, nb
              b(k,1) = change_sign * res(k,n)
            end do

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)
              do k = 1, nb
                  b(k,1) = b(k,1) - a_off(k,j)*dq(k,icol)
              end do
            end do

! Forward

            do j = 2,nb
              j1 = j-1
              b(j:nb,1) = b(j:nb,1) - a_diag_lu(j:nb,j1,n)*b(j1,1)
            end do

! Backward

            do j = nb,1,-1
              if (j<nb) then
                j1 = j+1
                do k = j1,nb
                  b(j,1) = b(j,1) - a_diag_lu(j,k,n)*b(k,1)
                end do
              end if
              b(j,1) = b(j,1) * a_diag_lu(j,j,n)
            end do

! Copy the solution into the dq array

            b(1:nb,1)  = b(1:nb,1)/rf_inv

            dq(1:nb,n) = b(1:nb,1)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)
            call lmpi_start_xfer(dq,sr_opt=sr(color))
          case(2)
            call lmpi_complete_xfer(dq,sr_opt=sr(color))
          end select

        end do pass_loop

      end do color_sweep

    end do sweeping

  end subroutine point_solve_dc

!================================ POINT_SOLVE_N ==============================80
!
! Performs G-S iteration on nxn system of specified equation set
!
!=============================================================================80

  subroutine point_solve_n(colored_sweeps, color_indices, max_colored_sweeps,  &
                           neq0, nia, nja, iam, jam, n_eqns, solve_backwards,  &
                           nb, dq_dim, nr, nm, n_sweeps, neqmax, res, dq,      &
                           a_diag_lu, a_off, sr, color_boundary_end, nnz0)

    use generic_gas_map, only : rf_inv

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax, nb

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end

    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,j1,k,kk,n,sweep,icol,istart,iend,start,end,ipass
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride

    real(jp) :: change_sign

    real(jp), dimension(nb,1) :: b

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      color_sweep : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(2,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            do k = 1, nb
              b(k,1) = change_sign * res(k,n)
            end do

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)
              do k = 1, nb
                do kk = 1, nb
                  b(k,1) = b(k,1) - a_off(k,kk,j)*dq(kk,icol)
                end do
              end do
            end do

! Forward

            do j = 2,nb
              j1 = j-1
              b(j:nb,1) = b(j:nb,1) - a_diag_lu(j:nb,j1,n)*b(j1,1)
            end do

! Backward

            do j = nb,1,-1
              if (j<nb) then
                j1 = j+1
                do k = j1,nb
                  b(j,1) = b(j,1) - a_diag_lu(j,k,n)*b(k,1)
                end do
              end if
              b(j,1) = b(j,1) * a_diag_lu(j,j,n)
            end do

! Copy the solution into the dq array

            b(1:nb,1)  = b(1:nb,1)/rf_inv

            dq(1:nb,n) = b(1:nb,1)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)
            call lmpi_start_xfer(dq,sr_opt=sr(color))
          case(2)
            call lmpi_complete_xfer(dq,sr_opt=sr(color))
          end select

        end do pass_loop

      end do color_sweep

    end do sweeping

  end subroutine point_solve_n

!================================ POINT_SOLVE_ML =============================80
!
! Performs G-S iteration on nxn system of specified equation set
!
!=============================================================================80
  subroutine point_solve_ml(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam, n_eqns, solve_backwards, &
                            nb, dq_dim, nr, nm, n_sweeps, neqmax, res, dq,     &
                            a_diag_lu, a_off, sr, color_boundary_end, nnz0)

    use generic_gas_map, only : rf_inv

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax, nb

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end

    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in)    :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in)    :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,j1,k,kk,n,sweep,icol,istart,iend,start,end,ipass
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride
    integer :: row, col

    real(jp) :: change_sign

    real(dqp), dimension(nb) :: sum_res
    real(jp), dimension(nb,1) :: g
    real(jp), dimension(nb,1) :: b

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      sum_res(:) = 0.0_jp

      color_sweep : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(2,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            do k = 1, nb
              b(k,1) = change_sign * res(k,n)
            end do

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)
              do k = 1, nb
                do kk = 1, nb
                  b(k,1) = b(k,1) - a_off(k,kk,j)*dq(kk,icol)
                end do
              end do
            end do

! Forward U*dq

            do row=1,nb
              g(row,1) = dq(row,n)/a_diag_lu(row,row,n)
              do col=row+1,nb
                g(row,1) = g(row,1) + a_diag_lu(row,col,n)*dq(col,n)
              enddo
            enddo

! Backward L*( U*dq )

            do row=nb,2,-1
              do col=1,row-1
                g(row,1) = g(row,1) + a_diag_lu(row,col,n)*g(col,1)
              enddo
            enddo

            sum_res(1:nb) = sum_res(1:nb) + ( b(1:nb,1)-g(1:nb,1) )**2

! Forward

            do j = 2,nb
              j1 = j-1
              b(j:nb,1) = b(j:nb,1) - a_diag_lu(j:nb,j1,n)*b(j1,1)
            end do

! Backward

            do j = nb,1,-1
              if (j<nb) then
                j1 = j+1
                do k = j1,nb
                  b(j,1) = b(j,1) - a_diag_lu(j,k,n)*b(k,1)
                end do
              end if
              b(j,1) = b(j,1) * a_diag_lu(j,j,n)
            end do

! Copy the solution into the dq array

            b(1:nb,1)  = b(1:nb,1)/rf_inv

            dq(1:nb,n) = b(1:nb,1)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)
            call lmpi_start_xfer(dq,sr_opt=sr(color))
          case(2)
            call lmpi_complete_xfer(dq,sr_opt=sr(color))
          end select

        end do pass_loop

      end do color_sweep

      call monitor_rms_sr(n_eqns, nb, sum_res,          &
                            'point-res ' )

    end do sweeping

  end subroutine point_solve_ml


#ifdef HAVE_CUDA
! no comment
!================================ POINT_SOLVE_5_CUDA =========================80
!
! Performs G-S iteration on 5x5 system of specified equation set
!
!=============================================================================80

  subroutine point_solve_5_cuda(                                               &
    colored_sweeps, color_indices, max_colored_sweeps,                         &
    neq0, nia, nja, iam, jam,                                                  &
    n_eqns,                                                                    &
    solve_backwards, nb, dq_dim, nr, nm,                                       &
    n_sweeps, neqmax,                                                          &
    res, dq, a_diag_lu, a_off, sr)

    use lmpi, only : lmpi_id, lmpi_master, lmpi_nproc

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax, nb
    logical, intent(in) :: solve_backwards

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp),  dimension(nr,neq0),       intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq
    real(jp),  dimension(nm,nm,neq0),    intent(in)    :: a_diag_lu
    real(odp), dimension(nm,nm,nja),     intent(in)    :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer :: j,n,sweep,icol,istart,iend,start,end,stride
    integer :: color, loop_colored_sweeps, csta, cend, cstr
    integer :: sweep_start, sweep_end, sweep_stride
    logical :: usempi

    real(dqp), dimension(nb,1) :: sum_ddq, f_dqp, dq_dqp
    real(jp),  dimension(nb,1) :: f
    real(dp)                   :: res_sign

    real(dp) :: t1, t2, t2_all, t1_device, t2_device, t2_device_all

    integer, parameter :: number_of_devices = 1
    integer, parameter :: show_time = 1 ! 0 none, 1 summary, 2 full

  continue

    usempi = (lmpi_nproc > 1)

    csta = 1
    cend = 2
    cstr = 1
    if( solve_backwards) then
      csta = 2
      cend = 1
      cstr =-1
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    if (number_of_devices >= 1) then
      if (solve_backwards) then
         res_sign = 1.0
      else
         res_sign = -1.0
      end if
    end if
    !write(1000+lmpi_id,*)"Ndev,usempi ",lmpi_id,number_of_devices,usempi

    sweeping : do sweep = 1, n_sweeps
      sum_ddq(:,1) = 0.0_dqp
      !------------ CUDA, 1-core (either MPI or seq)
      if ((number_of_devices >= 1).and.(.not.usempi)) then

        ! write(*,*)"Calling gpu_point_solve "
        ! write(1000+lmpi_id,*)'gpu_point_solve'

        call cpu_time(t1_device)
        call gpu_point_solve(dq,nm,nja,                                        &
                  nia,neq0,dq_dim,neqmax,                                      &
                  n_sweeps,sweep_start,sweep_end,sweep_stride,                 &
                  color_indices,colored_sweeps, csta, cend, cstr,              &
                  res_sign,n_eqns)
        call cpu_time(t2_device)
        !write(*,*)"Device time= ",t2_device-t1_device," at sweep ",sweep

     else ! number_of_devices,usempi

      call cpu_time(t1)
      color_sweeps : do color = sweep_start, sweep_end, sweep_stride
        start  =  1
        end    =  2
        stride = -1
        if(color <= colored_sweeps) then
          start  = color_indices(csta,color)
          end    = color_indices(cend,color)
          stride = cstr
        endif

      !----------- CDUA, multicore (MPI)
      if ((number_of_devices >= 1).and.(usempi)) then

           call cpu_time(t1_device)

         ! call gpu_point_solve_mpi(dq,nm,nja,nia,neq0,dq_dim,neqmax,          &
         !      res_sign,n_eqns, sum_ddq, start, end)
         ! call gpu_point_solve_mpi1(dq,nm,nja,nia,neq0,dq_dim,neqmax,         &
         !      res_sign,n_eqns, sum_ddq, start, end)

         call gpu_point_solve_mpi2(a_diag_lu,dq,res,eqns,nm,nja,nia,neq0,      &
              dq_dim, neqmax,res_sign,n_eqns, sum_ddq, start, end)

         ! call gpu_point_solve_mpi3(a_diag_lu,dq,res,eqns,nm,nja,nia,neq0,    &
         !      dq_dim, neqmax,res_sign,n_eqns, sum_ddq, start, end)
           call cpu_time(t2_device)
         ! print *, "Device time = ", t2_device-t1_device, " at sweep ", sweep

     !------------ non-CUDA
     else ! number_of_devices,usempi

         !write(1000+lmpi_id,*) 'non-CUDA'
         !write(1000+lmpi_id,*)"color = ",color,"start = ",start,"end = ",end
         !  call cpu_time(t1_array)

        rhs_solve : do n = start, end, stride
          ! write(1000,*) "n= ", n, "color = ", color

          if (.not.solve_backwards) then
             f_dqp(1,1) = -res(1,n)
             f_dqp(2,1) = -res(2,n)
             f_dqp(3,1) = -res(3,n)
             f_dqp(4,1) = -res(4,n)
             f_dqp(5,1) = -res(5,n)
          else
             f_dqp(1,1) = res(1,n)
             f_dqp(2,1) = res(2,n)
             f_dqp(3,1) = res(3,n)
             f_dqp(4,1) = res(4,n)
             f_dqp(5,1) = res(5,n)
          end if

          istart = iam(n)
          iend   = iam(n+1)-1

          do j = istart,iend
            icol = jam(j)

!...Computations access a_off entries sequentially.
          ! dq_dqp(:,1) = dq(:,icol) ! Unroll for Itanium/Intel10
            dq_dqp(1,1) = dq(1,icol)
            dq_dqp(2,1) = dq(2,icol)
            dq_dqp(3,1) = dq(3,icol)
            dq_dqp(4,1) = dq(4,icol)
            dq_dqp(5,1) = dq(5,icol)

            f_dqp(1,1) = f_dqp(1,1)   - a_off(1,1,j)*dq_dqp(1,1)
            f_dqp(2,1) = f_dqp(2,1)   - a_off(2,1,j)*dq_dqp(1,1)
            f_dqp(3,1) = f_dqp(3,1)   - a_off(3,1,j)*dq_dqp(1,1)
            f_dqp(4,1) = f_dqp(4,1)   - a_off(4,1,j)*dq_dqp(1,1)
            f_dqp(5,1) = f_dqp(5,1)   - a_off(5,1,j)*dq_dqp(1,1)

            f_dqp(1,1) = f_dqp(1,1)   - a_off(1,2,j)*dq_dqp(2,1)
            f_dqp(2,1) = f_dqp(2,1)   - a_off(2,2,j)*dq_dqp(2,1)
            f_dqp(3,1) = f_dqp(3,1)   - a_off(3,2,j)*dq_dqp(2,1)
            f_dqp(4,1) = f_dqp(4,1)   - a_off(4,2,j)*dq_dqp(2,1)
            f_dqp(5,1) = f_dqp(5,1)   - a_off(5,2,j)*dq_dqp(2,1)

            f_dqp(1,1) = f_dqp(1,1)   - a_off(1,3,j)*dq_dqp(3,1)
            f_dqp(2,1) = f_dqp(2,1)   - a_off(2,3,j)*dq_dqp(3,1)
            f_dqp(3,1) = f_dqp(3,1)   - a_off(3,3,j)*dq_dqp(3,1)
            f_dqp(4,1) = f_dqp(4,1)   - a_off(4,3,j)*dq_dqp(3,1)
            f_dqp(5,1) = f_dqp(5,1)   - a_off(5,3,j)*dq_dqp(3,1)

            f_dqp(1,1) = f_dqp(1,1)   - a_off(1,4,j)*dq_dqp(4,1)
            f_dqp(2,1) = f_dqp(2,1)   - a_off(2,4,j)*dq_dqp(4,1)
            f_dqp(3,1) = f_dqp(3,1)   - a_off(3,4,j)*dq_dqp(4,1)
            f_dqp(4,1) = f_dqp(4,1)   - a_off(4,4,j)*dq_dqp(4,1)
            f_dqp(5,1) = f_dqp(5,1)   - a_off(5,4,j)*dq_dqp(4,1)

            f_dqp(1,1) = f_dqp(1,1)   - a_off(1,5,j)*dq_dqp(5,1)
            f_dqp(2,1) = f_dqp(2,1)   - a_off(2,5,j)*dq_dqp(5,1)
            f_dqp(3,1) = f_dqp(3,1)   - a_off(3,5,j)*dq_dqp(5,1)
            f_dqp(4,1) = f_dqp(4,1)   - a_off(4,5,j)*dq_dqp(5,1)
            f_dqp(5,1) = f_dqp(5,1)   - a_off(5,5,j)*dq_dqp(5,1)

          end do

! Forward...sequential access to a_diag_lu.

       !  f(:,1) = f_dqp(:,1) ! Unroll for Itanium/Intel
          f(1,1) = f_dqp(1,1)
          f(2,1) = f_dqp(2,1)
          f(3,1) = f_dqp(3,1)
          f(4,1) = f_dqp(4,1)
          f(5,1) = f_dqp(5,1)

       !  f(1,1) = f(1,1)

          f(2,1) = f(2,1) - a_diag_lu(2,1,n)*f(1,1)
          f(3,1) = f(3,1) - a_diag_lu(3,1,n)*f(1,1)
          f(4,1) = f(4,1) - a_diag_lu(4,1,n)*f(1,1)
          f(5,1) = f(5,1) - a_diag_lu(5,1,n)*f(1,1)

          f(3,1) = f(3,1) - a_diag_lu(3,2,n)*f(2,1)
          f(4,1) = f(4,1) - a_diag_lu(4,2,n)*f(2,1)
          f(5,1) = f(5,1) - a_diag_lu(5,2,n)*f(2,1)

          f(4,1) = f(4,1) - a_diag_lu(4,3,n)*f(3,1)
          f(5,1) = f(5,1) - a_diag_lu(5,3,n)*f(3,1)

          f(5,1) = f(5,1) - a_diag_lu(5,4,n)*f(4,1)

!         f(1,1) = f(1,1)
!         f(2,1) = f(2,1) - a_diag_lu(2,1,n)*f(1,1)
!         f(3,1) = f(3,1) - a_diag_lu(3,1,n)*f(1,1)      &
!                         - a_diag_lu(3,2,n)*f(2,1)
!         f(4,1) = f(4,1) - a_diag_lu(4,1,n)*f(1,1)      &
!                         - a_diag_lu(4,2,n)*f(2,1)      &
!                         - a_diag_lu(4,3,n)*f(3,1)
!         f(5,1) = f(5,1) - a_diag_lu(5,1,n)*f(1,1)      &
!                         - a_diag_lu(5,2,n)*f(2,1)      &
!                         - a_diag_lu(5,3,n)*f(3,1)      &
!                         - a_diag_lu(5,4,n)*f(4,1)

! Backward...sequential access to a_diag_lu.

          f(5,1) = f(5,1) * a_diag_lu(5,5,n)
          f(1,1) = f(1,1) - a_diag_lu(1,5,n)*f(5,1)
          f(2,1) = f(2,1) - a_diag_lu(2,5,n)*f(5,1)
          f(3,1) = f(3,1) - a_diag_lu(3,5,n)*f(5,1)
          f(4,1) = f(4,1) - a_diag_lu(4,5,n)*f(5,1)

          f(4,1) = f(4,1) * a_diag_lu(4,4,n)
          f(1,1) = f(1,1) - a_diag_lu(1,4,n)*f(4,1)
          f(2,1) = f(2,1) - a_diag_lu(2,4,n)*f(4,1)
          f(3,1) = f(3,1) - a_diag_lu(3,4,n)*f(4,1)

          f(3,1) = f(3,1) * a_diag_lu(3,3,n)
          f(1,1) = f(1,1) - a_diag_lu(1,3,n)*f(3,1)
          f(2,1) = f(2,1) - a_diag_lu(2,3,n)*f(3,1)

          f(2,1) = f(2,1) * a_diag_lu(2,2,n)
          f(1,1) = f(1,1) - a_diag_lu(1,2,n)*f(2,1)

          f(1,1) = f(1,1) * a_diag_lu(1,1,n)

!         f(5,1) = ( f(5,1)                                                  &
!                                                  ) *a_diag_lu(5,5,n)
!         f(4,1) = ( f(4,1)                                                  &
!                           - a_diag_lu(4,5,n)*f(5,1)                 &
!                                                  ) *a_diag_lu(4,4,n)
!         f(3,1) = ( f(3,1)                                                  &
!                           - a_diag_lu(3,4,n)*f(4,1)                 &
!                           - a_diag_lu(3,5,n)*f(5,1)                 &
!                                                  ) *a_diag_lu(3,3,n)
!         f(2,1) = ( f(2,1)                                                  &
!                           - a_diag_lu(2,3,n)*f(3,1)                 &
!                           - a_diag_lu(2,4,n)*f(4,1)                 &
!                           - a_diag_lu(2,5,n)*f(5,1)                 &
!                                                  ) *a_diag_lu(2,2,n)
!         f(1,1) = ( f(1,1)                                                  &
!                           - a_diag_lu(1,2,n)*f(2,1)                 &
!                           - a_diag_lu(1,3,n)*f(3,1)                 &
!                           - a_diag_lu(1,4,n)*f(4,1)                 &
!                           - a_diag_lu(1,5,n)*f(5,1)                 &
!                                                  ) *a_diag_lu(1,1,n)

! Copy the solution into the dq array...early use of f entries.

          sum_ddq(5,1)      = sum_ddq(5,1) + ( dq(5,n) - f(5,1) )**2
          dq(5,n) = f(5,1)

          sum_ddq(4,1)      = sum_ddq(4,1) + ( dq(4,n) - f(4,1) )**2
          dq(4,n) = f(4,1)

          sum_ddq(3,1)      = sum_ddq(3,1) + ( dq(3,n) - f(3,1) )**2
          dq(3,n) = f(3,1)

          sum_ddq(2,1)      = sum_ddq(2,1) + ( dq(2,n) - f(2,1) )**2
          dq(2,n) = f(2,1)

          sum_ddq(1,1)      = sum_ddq(1,1) + ( dq(1,n) - f(1,1) )**2
          dq(1,n) = f(1,1)

!         sum_ddq(1:nb)   = sum_ddq(1:nb)                      &
!                         + ( dq(1:nb,n) - f(1:nb,1) )**2
!         dq(5,n) = f(5,1)
!         dq(4,n) = f(4,1)
!         dq(3,n) = f(3,1)
!         dq(2,n) = f(2,1)
!         dq(1,n) = f(1,1)

        end do rhs_solve
        ! call cpu_time(t2_array)
        ! print *, 'rhs time = ', t2_array-t1_array
        ! write(1000+lmpi_id,*) "END RHS SOLVE"
        ! write(1000+lmpi_id,*) "..."
        ! write(1000+lmpi_id,*) "Next Color"

  end if ! ((number_of_devices >= 1).and.(usempi == 1)) then
        !write(1000+lmpi_id,*)"sum_ddq(a) ",sum_ddq

! Exchange dq across processors between colors

        call lmpi_xfer(dq,sr_opt=sr(color))

      end do color_sweeps

      call cpu_time(t2)
      !print *, "CPU TIME = ", t2-t1, " at Sweep ", sweep

    end if ! number_of_devices,usempi
           !write(1000+lmpi_id,*)'Sweep ',sweep

!   write(1000,*) "Device time = ", t2_device-t1_device, " at sweep ", sweep
    if ((lmpi_master).and.(show_time > 0)) then
       if (show_time == 1) then
          t2_all        = t2_all + (t2-t1)
          t2_device_all = t2_device_all + t2_device-t1_device
       else
          print *, "CPU time = ", t2-t1
          print *, "GPU time = ", t2_device-t1_device
       end if
    end if
!   print *, "Array conversion time = ", t2_array-t1_array

         ! write(1000+lmpi_id,*)"sweep : sum_ddq ",sweep," : ",sum_ddq
         ! write(1000+lmpi_id,*)'sum_ddq1 ',sum_ddq1

    end do sweeping
    if ((lmpi_master).and.(show_time == 1))                                    &
       print *, "CPU,GPU time = ", t2_all,t2_device_all

  end subroutine point_solve_5_cuda
#endif

! no comment
!================================ POINT_SOLVE_1 ==============================80
!
! Performs G-S iteration on 1x1 system of specified equation set
!
!=============================================================================80

  subroutine point_solve_1(colored_sweeps, color_indices, max_colored_sweeps,  &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns,                                            &
                            solve_backwards, dq_dim, nr, nm,                   &
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag_lu, a_off, sr, color_boundary_end, &
                            nnz0)


    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end,ipass
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride

    real(dqp) :: f1

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(2,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            if ( solve_backwards > 0 ) then
              f1 = -res(1,n)
            else
              f1 = res(1,n)
            end if

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)

              f1 = f1   - a_off(1,1,j)*dq(1,icol)

            end do

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)
            call lmpi_start_xfer(dq,sr_opt=sr(color))
          case(2)
            call lmpi_complete_xfer(dq,sr_opt=sr(color))
          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine point_solve_1
! no comment
!================================ PPOINT_SOLVE_1 =============================80
!
! Performs G-S iteration on 1x1 system of specified equation set
! Periodic version
!
!=============================================================================80

  subroutine ppoint_solve_1(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam, n_eqns, solve_backwards, &
                            dq_dim, nr, nm, n_sweeps, neqmax, res, dq,         &
                            a_diag_lu, a_off, sr, color_periodic_end,          &
                            color_boundary_end, nnz0)

    use periodics, only : periodic_nature, nperiodic, periodic_data

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_periodic_end
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end
    integer :: color, loop_colored_sweeps, max_ind, ipass
    integer :: sweep_start, sweep_end, sweep_stride, group
    integer :: primary_entry, periodic_end

    real(dqp) :: f1

    real(dqp), dimension(nperiodic) :: stored_f1

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      stored_f1 = 0.0_dqp

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        start        =  1
        periodic_end = -1

        if(color <= colored_sweeps) then
          start  = color_indices(1,color)
          periodic_end = color_periodic_end(color)
        endif

! First gather all contributions to stored_f from periodic points

        gather_stored_f : do n = start, periodic_end

          group = abs(periodic_nature(n))

          if ( group == 0 ) cycle gather_stored_f

          if ( solve_backwards > 0 ) then
             f1 = -res(1,n)
          else
             f1 = res(1,n)
          end if

          istart = iam(n)
          iend   = iam(n+1)-1

          do j = istart,iend
            icol = jam(j)

            f1 = f1   - a_off(1,1,j)*dq(1,icol)
          end do

          stored_f1(group) = stored_f1(group) + f1

        end do gather_stored_f

! Now solve for dq everywhere in color


        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then

            start = 1
            end   = 0

          else

            max_ind = max(color_boundary_end(color),color_periodic_end(color))

            select case(ipass)
            case(1)
              if ( max_ind == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = max_ind
              endif
            case(2)
              if ( max_ind == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = max_ind+1
                end   = color_indices(2,color)
              endif
            end select

          endif

          rhs_solve : do n = start, end

            group = 0
            if ( n <= periodic_end ) group = periodic_nature(n)

! Do not solve secondary periodic points, they will be copied from
! primary points later

            if ( group < 0 ) cycle rhs_solve

            point_is_periodic : if ( group == 0 ) then

              if ( solve_backwards > 0 ) then
                f1 = -res(1,n)
              else
                f1 = res(1,n)
              end if

              istart = iam(n)
              iend   = iam(n+1)-1

              do j = istart,iend
                icol = jam(j)

                f1 = f1   - a_off(1,1,j)*dq(1,icol)
              end do

            else point_is_periodic

              f1 = stored_f1(group)

            endif point_is_periodic

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)

            update_secondary : do n = start, periodic_end
              group = periodic_nature(n)
              if ( group >= 0 ) cycle update_secondary
              group = abs(group)
              primary_entry = periodic_data(group)%mlist(1)
              dq(1,n) = dq(1,primary_entry)
            end do update_secondary

            call lmpi_start_xfer(dq,sr_opt=sr(color))

          case(2)

            call lmpi_complete_xfer(dq,sr_opt=sr(color))

          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine ppoint_solve_1


!================================ POINT_SOLVE_2 ==============================80
!
! Performs G-S iteration on 2x2 system of specified equation set
!
!=============================================================================80

  subroutine point_solve_2(colored_sweeps, color_indices, max_colored_sweeps,  &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns,                                            &
                            solve_backwards, dq_dim, nr, nm,                   &
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag_lu, a_off, sr, color_boundary_end, &
                            nnz0)


    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end,ipass
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride

    real(dqp) :: f1, f2

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(2,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            if ( solve_backwards > 0 ) then
              f1 = -res(1,n)
              f2 = -res(2,n)
            else
              f1 = res(1,n)
              f2 = res(2,n)
            end if

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)

              f1 = f1   - a_off(1,1,j)*dq(1,icol)
              f2 = f2   - a_off(2,1,j)*dq(1,icol)

              f1 = f1   - a_off(1,2,j)*dq(2,icol)
              f2 = f2   - a_off(2,2,j)*dq(2,icol)

            end do

            f2 = f2 - a_diag_lu(2,1,n)*f1

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)
            call lmpi_start_xfer(dq,sr_opt=sr(color))
          case(2)
            call lmpi_complete_xfer(dq,sr_opt=sr(color))
          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine point_solve_2
! no comment
!================================ PPOINT_SOLVE_2 =============================80
!
! Performs G-S iteration on 2x2 system of specified equation set
! Periodic version
!
!=============================================================================80

  subroutine ppoint_solve_2(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam, n_eqns, solve_backwards, &
                            dq_dim, nr, nm, n_sweeps, neqmax, res, dq,         &
                            a_diag_lu, a_off, sr, color_periodic_end,          &
                            color_boundary_end, nnz0)

    use periodics, only : periodic_nature, nperiodic, periodic_data

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_periodic_end
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end
    integer :: color, loop_colored_sweeps, max_ind, ipass
    integer :: sweep_start, sweep_end, sweep_stride, group
    integer :: primary_entry, periodic_end

    real(dqp) :: f1,f2

    real(dqp), dimension(nperiodic) :: stored_f1
    real(dqp), dimension(nperiodic) :: stored_f2

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      stored_f1 = 0.0_dqp
      stored_f2 = 0.0_dqp

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        start        =  1
        periodic_end = -1

        if(color <= colored_sweeps) then
          start        = color_indices(1,color)
          periodic_end = color_periodic_end(color)
        endif

! First gather all contributions to stored_f from periodic points

        gather_stored_f : do n = start, periodic_end

          group = abs(periodic_nature(n))

          if ( group == 0 ) cycle gather_stored_f

          if ( solve_backwards > 0 ) then
            f1 = -res(1,n)
            f2 = -res(2,n)
          else
            f1 = res(1,n)
            f2 = res(2,n)
          end if

          istart = iam(n)
          iend   = iam(n+1)-1

          do j = istart,iend
            icol = jam(j)

            f1 = f1   - a_off(1,1,j)*dq(1,icol)
            f2 = f2   - a_off(2,1,j)*dq(1,icol)

            f1 = f1   - a_off(1,2,j)*dq(2,icol)
            f2 = f2   - a_off(2,2,j)*dq(2,icol)

          end do

          stored_f1(group) = stored_f1(group) + f1
          stored_f2(group) = stored_f2(group) + f2

        end do gather_stored_f

! Now solve for dq everywhere in color

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then

            start = 1
            end   = 0

          else

            max_ind = max(color_boundary_end(color),color_periodic_end(color))

            select case(ipass)
            case(1)
              if ( max_ind == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = max_ind
              endif
            case(2)
              if ( max_ind == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = max_ind+1
                end   = color_indices(2,color)
              endif
            end select

          endif

          rhs_solve : do n = start, end

            group = 0
            if ( n <= periodic_end ) group = periodic_nature(n)

! Do not solve secondary periodic points, they will be copied from
! primary points later

            if ( group < 0 ) cycle rhs_solve

            point_is_periodic : if ( group == 0 ) then

              if ( solve_backwards > 0 ) then
                f1 = -res(1,n)
                f2 = -res(2,n)
              else
                f1 = res(1,n)
                f2 = res(2,n)
              end if

              istart = iam(n)
              iend   = iam(n+1)-1

              do j = istart,iend
                icol = jam(j)

                f1 = f1   - a_off(1,1,j)*dq(1,icol)
                f2 = f2   - a_off(2,1,j)*dq(1,icol)

                f1 = f1   - a_off(1,2,j)*dq(2,icol)
                f2 = f2   - a_off(2,2,j)*dq(2,icol)

              end do

            else point_is_periodic

              f1 = stored_f1(group)
              f2 = stored_f2(group)

            endif point_is_periodic

! Forward...sequential access to a_diag_lu.

            f2 = f2 - a_diag_lu(2,1,n)*f1

! Backward...sequential access to a_diag_lu.

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)

            update_secondary : do n = start, periodic_end
              group = periodic_nature(n)
              if ( group >= 0 ) cycle update_secondary
              group = abs(group)
              primary_entry = periodic_data(group)%mlist(1)
              dq(1,n) = dq(1,primary_entry)
              dq(2,n) = dq(2,primary_entry)
            end do update_secondary

            call lmpi_start_xfer(dq,sr_opt=sr(color))

          case(2)

            call lmpi_complete_xfer(dq,sr_opt=sr(color))

          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine ppoint_solve_2


!================================ POINT_SOLVE_3 ==============================80
!
! Performs G-S iteration on 3x3 system of specified equation set
!
!=============================================================================80

  subroutine point_solve_3(colored_sweeps, color_indices, max_colored_sweeps,  &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns,                                            &
                            solve_backwards, dq_dim, nr, nm,                   &
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag_lu, a_off, sr, color_boundary_end, &
                            nnz0)


    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end,ipass
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride

    real(dqp) :: f1,f2,f3

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(2,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            if ( solve_backwards > 0 ) then
              f1 = -res(1,n)
              f2 = -res(2,n)
              f3 = -res(3,n)
            else
              f1 = res(1,n)
              f2 = res(2,n)
              f3 = res(3,n)
            end if

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)

              f1 = f1   - a_off(1,1,j)*dq(1,icol)
              f2 = f2   - a_off(2,1,j)*dq(1,icol)
              f3 = f3   - a_off(3,1,j)*dq(1,icol)

              f1 = f1   - a_off(1,2,j)*dq(2,icol)
              f2 = f2   - a_off(2,2,j)*dq(2,icol)
              f3 = f3   - a_off(3,2,j)*dq(2,icol)

              f1 = f1   - a_off(1,3,j)*dq(3,icol)
              f2 = f2   - a_off(2,3,j)*dq(3,icol)
              f3 = f3   - a_off(3,3,j)*dq(3,icol)

            end do

            f2 = f2 - a_diag_lu(2,1,n)*f1
            f3 = f3 - a_diag_lu(3,1,n)*f1

            f3 = f3 - a_diag_lu(3,2,n)*f2

            dq(3,n) = f3 * a_diag_lu(3,3,n)
            f1 = f1 - a_diag_lu(1,3,n)*dq(3,n)
            f2 = f2 - a_diag_lu(2,3,n)*dq(3,n)

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)
            call lmpi_start_xfer(dq,sr_opt=sr(color))
          case(2)
            call lmpi_complete_xfer(dq,sr_opt=sr(color))
          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine point_solve_3
! no comment
!================================ PPOINT_SOLVE_3 =============================80
!
! Performs G-S iteration on 3x3 system of specified equation set
! Periodic version
!
!=============================================================================80

  subroutine ppoint_solve_3(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam, n_eqns, solve_backwards, &
                            dq_dim, nr, nm, n_sweeps, neqmax, res, dq,         &
                            a_diag_lu, a_off, sr, color_periodic_end,          &
                            color_boundary_end, nnz0)

    use periodics, only : periodic_nature, nperiodic, periodic_data

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_periodic_end
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end
    integer :: color, loop_colored_sweeps, max_ind, ipass
    integer :: sweep_start, sweep_end, sweep_stride, group
    integer :: primary_entry, periodic_end

    real(dqp) :: f1,f2,f3

    real(dqp), dimension(nperiodic) :: stored_f1
    real(dqp), dimension(nperiodic) :: stored_f2
    real(dqp), dimension(nperiodic) :: stored_f3

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      stored_f1 = 0.0_dqp
      stored_f2 = 0.0_dqp
      stored_f3 = 0.0_dqp

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        start        =  1
        periodic_end = -1

        if(color <= colored_sweeps) then
          start        = color_indices(1,color)
          periodic_end = color_periodic_end(color)
        endif

! First gather all contributions to stored_f from periodic points

        gather_stored_f : do n = start, periodic_end

          group = abs(periodic_nature(n))

          if ( group == 0 ) cycle gather_stored_f

          if ( solve_backwards > 0 ) then
             f1 = -res(1,n)
             f2 = -res(2,n)
             f3 = -res(3,n)
          else
             f1 = res(1,n)
             f2 = res(2,n)
             f3 = res(3,n)
          end if

          istart = iam(n)
          iend   = iam(n+1)-1

          do j = istart,iend
            icol = jam(j)

            f1 = f1   - a_off(1,1,j)*dq(1,icol)
            f2 = f2   - a_off(2,1,j)*dq(1,icol)
            f3 = f3   - a_off(3,1,j)*dq(1,icol)

            f1 = f1   - a_off(1,2,j)*dq(2,icol)
            f2 = f2   - a_off(2,2,j)*dq(2,icol)
            f3 = f3   - a_off(3,2,j)*dq(2,icol)

            f1 = f1   - a_off(1,3,j)*dq(3,icol)
            f2 = f2   - a_off(2,3,j)*dq(3,icol)
            f3 = f3   - a_off(3,3,j)*dq(3,icol)

          end do

          stored_f1(group) = stored_f1(group) + f1
          stored_f2(group) = stored_f2(group) + f2
          stored_f3(group) = stored_f3(group) + f3

        end do gather_stored_f

! Now solve for dq everywhere in color

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then

            start = 1
            end   = 0

          else

            max_ind = max(color_boundary_end(color),color_periodic_end(color))

            select case(ipass)
            case(1)
              if ( max_ind == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = max_ind
              endif
            case(2)
              if ( max_ind == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = max_ind+1
                end   = color_indices(2,color)
              endif
            end select

          endif

          rhs_solve : do n = start, end

            group = 0
            if ( n <= periodic_end ) group = periodic_nature(n)

! Do not solve secondary periodic points, they will be copied from
! primary points later

            if ( group < 0 ) cycle rhs_solve

            point_is_periodic : if ( group == 0 ) then

              if ( solve_backwards > 0 ) then
                f1 = -res(1,n)
                f2 = -res(2,n)
                f3 = -res(3,n)
              else
                f1 = res(1,n)
                f2 = res(2,n)
                f3 = res(3,n)
              end if

              istart = iam(n)
              iend   = iam(n+1)-1

              do j = istart,iend
                icol = jam(j)

                f1 = f1   - a_off(1,1,j)*dq(1,icol)
                f2 = f2   - a_off(2,1,j)*dq(1,icol)
                f3 = f3   - a_off(3,1,j)*dq(1,icol)

                f1 = f1   - a_off(1,2,j)*dq(2,icol)
                f2 = f2   - a_off(2,2,j)*dq(2,icol)
                f3 = f3   - a_off(3,2,j)*dq(2,icol)

                f1 = f1   - a_off(1,3,j)*dq(3,icol)
                f2 = f2   - a_off(2,3,j)*dq(3,icol)
                f3 = f3   - a_off(3,3,j)*dq(3,icol)

              end do

            else point_is_periodic

              f1 = stored_f1(group)
              f2 = stored_f2(group)
              f3 = stored_f3(group)

            endif point_is_periodic

! Forward...sequential access to a_diag_lu.

            f2 = f2 - a_diag_lu(2,1,n)*f1
            f3 = f3 - a_diag_lu(3,1,n)*f1

            f3 = f3 - a_diag_lu(3,2,n)*f2

! Backward...sequential access to a_diag_lu.

            dq(3,n) = f3 * a_diag_lu(3,3,n)
            f1 = f1 - a_diag_lu(1,3,n)*dq(3,n)
            f2 = f2 - a_diag_lu(2,3,n)*dq(3,n)

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)

            update_secondary : do n = start, periodic_end
              group = periodic_nature(n)
              if ( group >= 0 ) cycle update_secondary
              group = abs(group)
              primary_entry = periodic_data(group)%mlist(1)
              dq(1,n) = dq(1,primary_entry)
              dq(2,n) = dq(2,primary_entry)
              dq(3,n) = dq(3,primary_entry)
            end do update_secondary

            call lmpi_start_xfer(dq,sr_opt=sr(color))

          case(2)

            call lmpi_complete_xfer(dq,sr_opt=sr(color))

          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine ppoint_solve_3


!================================ POINT_SOLVE_4 ==============================80
!
! Performs G-S iteration on 4x4 system of specified equation set
!
!=============================================================================80

  subroutine point_solve_4(colored_sweeps, color_indices, max_colored_sweeps,  &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns,                                            &
                            solve_backwards, dq_dim, nr, nm,                   &
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag_lu, a_off, sr, color_boundary_end, &
                            nnz0)


    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end,ipass
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride

    real(dqp) :: f1,f2,f3,f4

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(2,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            if ( solve_backwards > 0 ) then
              f1 = -res(1,n)
              f2 = -res(2,n)
              f3 = -res(3,n)
              f4 = -res(4,n)
            else
              f1 = res(1,n)
              f2 = res(2,n)
              f3 = res(3,n)
              f4 = res(4,n)
            end if

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)

              f1 = f1   - a_off(1,1,j)*dq(1,icol)
              f2 = f2   - a_off(2,1,j)*dq(1,icol)
              f3 = f3   - a_off(3,1,j)*dq(1,icol)
              f4 = f4   - a_off(4,1,j)*dq(1,icol)

              f1 = f1   - a_off(1,2,j)*dq(2,icol)
              f2 = f2   - a_off(2,2,j)*dq(2,icol)
              f3 = f3   - a_off(3,2,j)*dq(2,icol)
              f4 = f4   - a_off(4,2,j)*dq(2,icol)

              f1 = f1   - a_off(1,3,j)*dq(3,icol)
              f2 = f2   - a_off(2,3,j)*dq(3,icol)
              f3 = f3   - a_off(3,3,j)*dq(3,icol)
              f4 = f4   - a_off(4,3,j)*dq(3,icol)

              f1 = f1   - a_off(1,4,j)*dq(4,icol)
              f2 = f2   - a_off(2,4,j)*dq(4,icol)
              f3 = f3   - a_off(3,4,j)*dq(4,icol)
              f4 = f4   - a_off(4,4,j)*dq(4,icol)

            end do

! Forward...sequential access to a_diag_lu.

            f2 = f2 - a_diag_lu(2,1,n)*f1
            f3 = f3 - a_diag_lu(3,1,n)*f1
            f4 = f4 - a_diag_lu(4,1,n)*f1

            f3 = f3 - a_diag_lu(3,2,n)*f2
            f4 = f4 - a_diag_lu(4,2,n)*f2

            f4 = f4 - a_diag_lu(4,3,n)*f3

! Backward...sequential access to a_diag_lu.

            dq(4,n) = f4 * a_diag_lu(4,4,n)
            f1 = f1 - a_diag_lu(1,4,n)*dq(4,n)
            f2 = f2 - a_diag_lu(2,4,n)*dq(4,n)
            f3 = f3 - a_diag_lu(3,4,n)*dq(4,n)

            dq(3,n) = f3 * a_diag_lu(3,3,n)
            f1 = f1 - a_diag_lu(1,3,n)*dq(3,n)
            f2 = f2 - a_diag_lu(2,3,n)*dq(3,n)

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)
            call lmpi_start_xfer(dq,sr_opt=sr(color))
          case(2)
            call lmpi_complete_xfer(dq,sr_opt=sr(color))
          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine point_solve_4
! no comment
!================================ PPOINT_SOLVE_4 =============================80
!
! Performs G-S iteration on 4x4 system of specified equation set
! Periodic version
!
!=============================================================================80

  subroutine ppoint_solve_4(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam, n_eqns, solve_backwards, &
                            dq_dim, nr, nm, n_sweeps, neqmax, res, dq,         &
                            a_diag_lu, a_off, sr, color_periodic_end,          &
                            color_boundary_end, nnz0)

    use periodics, only : periodic_nature, nperiodic, periodic_data

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_periodic_end
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end
    integer :: color, loop_colored_sweeps, max_ind, ipass
    integer :: sweep_start, sweep_end, sweep_stride, group
    integer :: primary_entry, periodic_end

    real(dqp) :: f1,f2,f3,f4

    real(dqp), dimension(nperiodic) :: stored_f1
    real(dqp), dimension(nperiodic) :: stored_f2
    real(dqp), dimension(nperiodic) :: stored_f3
    real(dqp), dimension(nperiodic) :: stored_f4

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      stored_f1 = 0.0_dqp
      stored_f2 = 0.0_dqp
      stored_f3 = 0.0_dqp
      stored_f4 = 0.0_dqp

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        start        =  1
        periodic_end = -1

        if(color <= colored_sweeps) then
          start        = color_indices(1,color)
          periodic_end = color_periodic_end(color)
        endif

! First gather all contributions to stored_f from periodic points

        gather_stored_f : do n = start, periodic_end

          group = abs(periodic_nature(n))

          if ( group == 0 ) cycle gather_stored_f

          if ( solve_backwards > 0 ) then
             f1 = -res(1,n)
             f2 = -res(2,n)
             f3 = -res(3,n)
             f4 = -res(4,n)
          else
             f1 = res(1,n)
             f2 = res(2,n)
             f3 = res(3,n)
             f4 = res(4,n)
          end if

          istart = iam(n)
          iend   = iam(n+1)-1

          do j = istart,iend
            icol = jam(j)

            f1 = f1   - a_off(1,1,j)*dq(1,icol)
            f2 = f2   - a_off(2,1,j)*dq(1,icol)
            f3 = f3   - a_off(3,1,j)*dq(1,icol)
            f4 = f4   - a_off(4,1,j)*dq(1,icol)

            f1 = f1   - a_off(1,2,j)*dq(2,icol)
            f2 = f2   - a_off(2,2,j)*dq(2,icol)
            f3 = f3   - a_off(3,2,j)*dq(2,icol)
            f4 = f4   - a_off(4,2,j)*dq(2,icol)

            f1 = f1   - a_off(1,3,j)*dq(3,icol)
            f2 = f2   - a_off(2,3,j)*dq(3,icol)
            f3 = f3   - a_off(3,3,j)*dq(3,icol)
            f4 = f4   - a_off(4,3,j)*dq(3,icol)

            f1 = f1   - a_off(1,4,j)*dq(4,icol)
            f2 = f2   - a_off(2,4,j)*dq(4,icol)
            f3 = f3   - a_off(3,4,j)*dq(4,icol)
            f4 = f4   - a_off(4,4,j)*dq(4,icol)

          end do

          stored_f1(group) = stored_f1(group) + f1
          stored_f2(group) = stored_f2(group) + f2
          stored_f3(group) = stored_f3(group) + f3
          stored_f4(group) = stored_f4(group) + f4

        end do gather_stored_f

! Now solve for dq everywhere in color

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then

            start = 1
            end   = 0

          else

            max_ind = max(color_boundary_end(color),color_periodic_end(color))

            select case(ipass)
            case(1)
              if ( max_ind == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = max_ind
              endif
            case(2)
              if ( max_ind == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = max_ind+1
                end   = color_indices(2,color)
              endif
            end select

          endif

          rhs_solve : do n = start, end

            group = 0
            if ( n <= periodic_end ) group = periodic_nature(n)

! Do not solve secondary periodic points, they will be copied from
! primary points later

            if ( group < 0 ) cycle rhs_solve

            point_is_periodic : if ( group == 0 ) then

              if ( solve_backwards > 0 ) then
                f1 = -res(1,n)
                f2 = -res(2,n)
                f3 = -res(3,n)
                f4 = -res(4,n)
              else
                f1 = res(1,n)
                f2 = res(2,n)
                f3 = res(3,n)
                f4 = res(4,n)
              end if

              istart = iam(n)
              iend   = iam(n+1)-1

              do j = istart,iend
                icol = jam(j)

                f1 = f1   - a_off(1,1,j)*dq(1,icol)
                f2 = f2   - a_off(2,1,j)*dq(1,icol)
                f3 = f3   - a_off(3,1,j)*dq(1,icol)
                f4 = f4   - a_off(4,1,j)*dq(1,icol)

                f1 = f1   - a_off(1,2,j)*dq(2,icol)
                f2 = f2   - a_off(2,2,j)*dq(2,icol)
                f3 = f3   - a_off(3,2,j)*dq(2,icol)
                f4 = f4   - a_off(4,2,j)*dq(2,icol)

                f1 = f1   - a_off(1,3,j)*dq(3,icol)
                f2 = f2   - a_off(2,3,j)*dq(3,icol)
                f3 = f3   - a_off(3,3,j)*dq(3,icol)
                f4 = f4   - a_off(4,3,j)*dq(3,icol)

                f1 = f1   - a_off(1,4,j)*dq(4,icol)
                f2 = f2   - a_off(2,4,j)*dq(4,icol)
                f3 = f3   - a_off(3,4,j)*dq(4,icol)
                f4 = f4   - a_off(4,4,j)*dq(4,icol)

              end do

            else point_is_periodic

              f1 = stored_f1(group)
              f2 = stored_f2(group)
              f3 = stored_f3(group)
              f4 = stored_f4(group)

            endif point_is_periodic

! Forward...sequential access to a_diag_lu.

            f2 = f2 - a_diag_lu(2,1,n)*f1
            f3 = f3 - a_diag_lu(3,1,n)*f1
            f4 = f4 - a_diag_lu(4,1,n)*f1

            f3 = f3 - a_diag_lu(3,2,n)*f2
            f4 = f4 - a_diag_lu(4,2,n)*f2

            f4 = f4 - a_diag_lu(4,3,n)*f3

! Backward...sequential access to a_diag_lu.

            dq(4,n) = f4 * a_diag_lu(4,4,n)
            f1 = f1 - a_diag_lu(1,4,n)*dq(4,n)
            f2 = f2 - a_diag_lu(2,4,n)*dq(4,n)
            f3 = f3 - a_diag_lu(3,4,n)*dq(4,n)

            dq(3,n) = f3 * a_diag_lu(3,3,n)
            f1 = f1 - a_diag_lu(1,3,n)*dq(3,n)
            f2 = f2 - a_diag_lu(2,3,n)*dq(3,n)

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)

            update_secondary : do n = start, periodic_end
              group = periodic_nature(n)
              if ( group >= 0 ) cycle update_secondary
              group = abs(group)
              primary_entry = periodic_data(group)%mlist(1)
              dq(1,n) = dq(1,primary_entry)
              dq(2,n) = dq(2,primary_entry)
              dq(3,n) = dq(3,primary_entry)
              dq(4,n) = dq(4,primary_entry)
            end do update_secondary

            call lmpi_start_xfer(dq,sr_opt=sr(color))

          case(2)

            call lmpi_complete_xfer(dq,sr_opt=sr(color))

          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine ppoint_solve_4


!================================ POINT_SOLVE_5 ==============================80
!
! Performs G-S iteration on 5x5 system of specified equation set
!
!=============================================================================80

  subroutine point_solve_5(colored_sweeps, color_indices, max_colored_sweeps,  &
                            neq0, nia, nja, iam, jam, n_eqns, solve_backwards, &
                            dq_dim, nr, nm, n_sweeps, neqmax, res, dq,         &
                            a_diag_lu, a_off, sr, color_boundary_end, nnz0,    &
                            color_xfer_indices)

    use openacc, only : use_openacc, use_cudampi
    use lmpi,    only : lmpi_nproc

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps),     intent(in) :: color_indices
    integer, dimension(2,max_colored_sweeps), intent(in) :: color_xfer_indices
    integer, dimension(colored_sweeps),       intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end,ipass,ind1,ind2
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride

    real(dqp) :: f1,f2,f3,f4,f5

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(2,color)
              endif
            end select
          endif

!$acc parallel present(iam,jam,res,dq,a_off,a_diag_lu)
!$acc loop gang vector
          rhs_solve : do n = start, end

            if ( solve_backwards > 0 ) then
              f1 = -res(1,n)
              f2 = -res(2,n)
              f3 = -res(3,n)
              f4 = -res(4,n)
              f5 = -res(5,n)
            else
              f1 = res(1,n)
              f2 = res(2,n)
              f3 = res(3,n)
              f4 = res(4,n)
              f5 = res(5,n)
            end if

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)

              f1 = f1   - a_off(1,1,j)*dq(1,icol)
              f2 = f2   - a_off(2,1,j)*dq(1,icol)
              f3 = f3   - a_off(3,1,j)*dq(1,icol)
              f4 = f4   - a_off(4,1,j)*dq(1,icol)
              f5 = f5   - a_off(5,1,j)*dq(1,icol)

              f1 = f1   - a_off(1,2,j)*dq(2,icol)
              f2 = f2   - a_off(2,2,j)*dq(2,icol)
              f3 = f3   - a_off(3,2,j)*dq(2,icol)
              f4 = f4   - a_off(4,2,j)*dq(2,icol)
              f5 = f5   - a_off(5,2,j)*dq(2,icol)

              f1 = f1   - a_off(1,3,j)*dq(3,icol)
              f2 = f2   - a_off(2,3,j)*dq(3,icol)
              f3 = f3   - a_off(3,3,j)*dq(3,icol)
              f4 = f4   - a_off(4,3,j)*dq(3,icol)
              f5 = f5   - a_off(5,3,j)*dq(3,icol)

              f1 = f1   - a_off(1,4,j)*dq(4,icol)
              f2 = f2   - a_off(2,4,j)*dq(4,icol)
              f3 = f3   - a_off(3,4,j)*dq(4,icol)
              f4 = f4   - a_off(4,4,j)*dq(4,icol)
              f5 = f5   - a_off(5,4,j)*dq(4,icol)

              f1 = f1   - a_off(1,5,j)*dq(5,icol)
              f2 = f2   - a_off(2,5,j)*dq(5,icol)
              f3 = f3   - a_off(3,5,j)*dq(5,icol)
              f4 = f4   - a_off(4,5,j)*dq(5,icol)
              f5 = f5   - a_off(5,5,j)*dq(5,icol)

            end do

! Forward...sequential access to a_diag_lu.

            f2 = f2 - a_diag_lu(2,1,n)*f1
            f3 = f3 - a_diag_lu(3,1,n)*f1
            f4 = f4 - a_diag_lu(4,1,n)*f1
            f5 = f5 - a_diag_lu(5,1,n)*f1

            f3 = f3 - a_diag_lu(3,2,n)*f2
            f4 = f4 - a_diag_lu(4,2,n)*f2
            f5 = f5 - a_diag_lu(5,2,n)*f2

            f4 = f4 - a_diag_lu(4,3,n)*f3
            f5 = f5 - a_diag_lu(5,3,n)*f3

            f5 = f5 - a_diag_lu(5,4,n)*f4

! Backward...sequential access to a_diag_lu.

            dq(5,n) = f5 * a_diag_lu(5,5,n)
            f1 = f1 - a_diag_lu(1,5,n)*dq(5,n)
            f2 = f2 - a_diag_lu(2,5,n)*dq(5,n)
            f3 = f3 - a_diag_lu(3,5,n)*dq(5,n)
            f4 = f4 - a_diag_lu(4,5,n)*dq(5,n)

            dq(4,n) = f4 * a_diag_lu(4,4,n)
            f1 = f1 - a_diag_lu(1,4,n)*dq(4,n)
            f2 = f2 - a_diag_lu(2,4,n)*dq(4,n)
            f3 = f3 - a_diag_lu(3,4,n)*dq(4,n)

            dq(3,n) = f3 * a_diag_lu(3,3,n)
            f1 = f1 - a_diag_lu(1,3,n)*dq(3,n)
            f2 = f2 - a_diag_lu(2,3,n)*dq(3,n)

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve
!$acc end parallel

! Want to use dq(:,start:end) in this directive, but compiler won't
! let me anymore???

!$acc update host (dq) if (lmpi_nproc > 1 .and. .not.use_cudampi)

          select case(ipass)
          case(1)
            if ( use_openacc .and. use_cudampi ) then
              call lmpi_start_cudampi_xfer(color,dq,sr(color))
            else
              call lmpi_start_xfer(dq,sr_opt=sr(color))
            endif
          case(2)
            if ( use_openacc .and. use_cudampi ) then
              call lmpi_complete_cudampi_xfer(color,dq)
            else
              call lmpi_complete_xfer(dq,sr_opt=sr(color))
            endif
          end select

        end do pass_loop

        ind1 = color_xfer_indices(1,color)
        ind2 = color_xfer_indices(2,color)

! Want to use dq(:,ind1:ind2) in this directive, but compiler won't
! let me anymore???

!$acc update device (dq) if ( lmpi_nproc > 1 .and. ind1 > 0 .and.              &
!$acc                        .not.use_cudampi)

      end do color_sweeps

    end do sweeping

    if ( .false. ) write(*,*) ind1, ind2, lmpi_nproc

  end subroutine point_solve_5
! no comment
!================================ PPOINT_SOLVE_5 =============================80
!
! Performs G-S iteration on 5x5 system of specified equation set
! Periodic version
!
!=============================================================================80

  subroutine ppoint_solve_5(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam, n_eqns, solve_backwards, &
                            dq_dim, nr, nm, n_sweeps, neqmax, res, dq,         &
                            a_diag_lu, a_off, sr, color_periodic_end,          &
                            color_boundary_end, nnz0)

    use periodics, only : periodic_nature, nperiodic, periodic_data

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_periodic_end
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end
    integer :: color, loop_colored_sweeps, max_ind, ipass
    integer :: sweep_start, sweep_end, sweep_stride, group
    integer :: primary_entry, periodic_end

    real(dqp) :: f1,f2,f3,f4,f5

    real(dqp), dimension(nperiodic) :: stored_f1
    real(dqp), dimension(nperiodic) :: stored_f2
    real(dqp), dimension(nperiodic) :: stored_f3
    real(dqp), dimension(nperiodic) :: stored_f4
    real(dqp), dimension(nperiodic) :: stored_f5

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      stored_f1 = 0.0_dqp
      stored_f2 = 0.0_dqp
      stored_f3 = 0.0_dqp
      stored_f4 = 0.0_dqp
      stored_f5 = 0.0_dqp

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        start        =  1
        periodic_end = -1

        if(color <= colored_sweeps) then
          start        = color_indices(1,color)
          periodic_end = color_periodic_end(color)
        endif

! First gather all contributions to stored_f from periodic points

        gather_stored_f : do n = start, periodic_end

          group = abs(periodic_nature(n))

          if ( group == 0 ) cycle gather_stored_f

          if ( solve_backwards > 0 ) then
             f1 = -res(1,n)
             f2 = -res(2,n)
             f3 = -res(3,n)
             f4 = -res(4,n)
             f5 = -res(5,n)
          else
             f1 = res(1,n)
             f2 = res(2,n)
             f3 = res(3,n)
             f4 = res(4,n)
             f5 = res(5,n)
          end if

          istart = iam(n)
          iend   = iam(n+1)-1

          do j = istart,iend
            icol = jam(j)

            f1 = f1   - a_off(1,1,j)*dq(1,icol)
            f2 = f2   - a_off(2,1,j)*dq(1,icol)
            f3 = f3   - a_off(3,1,j)*dq(1,icol)
            f4 = f4   - a_off(4,1,j)*dq(1,icol)
            f5 = f5   - a_off(5,1,j)*dq(1,icol)

            f1 = f1   - a_off(1,2,j)*dq(2,icol)
            f2 = f2   - a_off(2,2,j)*dq(2,icol)
            f3 = f3   - a_off(3,2,j)*dq(2,icol)
            f4 = f4   - a_off(4,2,j)*dq(2,icol)
            f5 = f5   - a_off(5,2,j)*dq(2,icol)

            f1 = f1   - a_off(1,3,j)*dq(3,icol)
            f2 = f2   - a_off(2,3,j)*dq(3,icol)
            f3 = f3   - a_off(3,3,j)*dq(3,icol)
            f4 = f4   - a_off(4,3,j)*dq(3,icol)
            f5 = f5   - a_off(5,3,j)*dq(3,icol)

            f1 = f1   - a_off(1,4,j)*dq(4,icol)
            f2 = f2   - a_off(2,4,j)*dq(4,icol)
            f3 = f3   - a_off(3,4,j)*dq(4,icol)
            f4 = f4   - a_off(4,4,j)*dq(4,icol)
            f5 = f5   - a_off(5,4,j)*dq(4,icol)

            f1 = f1   - a_off(1,5,j)*dq(5,icol)
            f2 = f2   - a_off(2,5,j)*dq(5,icol)
            f3 = f3   - a_off(3,5,j)*dq(5,icol)
            f4 = f4   - a_off(4,5,j)*dq(5,icol)
            f5 = f5   - a_off(5,5,j)*dq(5,icol)

          end do

          stored_f1(group) = stored_f1(group) + f1
          stored_f2(group) = stored_f2(group) + f2
          stored_f3(group) = stored_f3(group) + f3
          stored_f4(group) = stored_f4(group) + f4
          stored_f5(group) = stored_f5(group) + f5

        end do gather_stored_f

! Now solve for dq everywhere in color

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then

            start = 1
            end   = 0

          else

            max_ind = max(color_boundary_end(color),color_periodic_end(color))

            select case(ipass)
            case(1)
              if ( max_ind == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = max_ind
              endif
            case(2)
              if ( max_ind == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = max_ind+1
                end   = color_indices(2,color)
              endif
            end select

          endif

          rhs_solve : do n = start, end

            group = 0
            if ( n <= periodic_end ) group = periodic_nature(n)

! Do not solve secondary periodic points, they will be copied from
! primary points later

            if ( group < 0 ) cycle rhs_solve

            point_is_periodic : if ( group == 0 ) then

              if ( solve_backwards > 0 ) then
                f1 = -res(1,n)
                f2 = -res(2,n)
                f3 = -res(3,n)
                f4 = -res(4,n)
                f5 = -res(5,n)
              else
                f1 = res(1,n)
                f2 = res(2,n)
                f3 = res(3,n)
                f4 = res(4,n)
                f5 = res(5,n)
              end if

              istart = iam(n)
              iend   = iam(n+1)-1

              do j = istart,iend
                icol = jam(j)

                f1 = f1   - a_off(1,1,j)*dq(1,icol)
                f2 = f2   - a_off(2,1,j)*dq(1,icol)
                f3 = f3   - a_off(3,1,j)*dq(1,icol)
                f4 = f4   - a_off(4,1,j)*dq(1,icol)
                f5 = f5   - a_off(5,1,j)*dq(1,icol)

                f1 = f1   - a_off(1,2,j)*dq(2,icol)
                f2 = f2   - a_off(2,2,j)*dq(2,icol)
                f3 = f3   - a_off(3,2,j)*dq(2,icol)
                f4 = f4   - a_off(4,2,j)*dq(2,icol)
                f5 = f5   - a_off(5,2,j)*dq(2,icol)

                f1 = f1   - a_off(1,3,j)*dq(3,icol)
                f2 = f2   - a_off(2,3,j)*dq(3,icol)
                f3 = f3   - a_off(3,3,j)*dq(3,icol)
                f4 = f4   - a_off(4,3,j)*dq(3,icol)
                f5 = f5   - a_off(5,3,j)*dq(3,icol)

                f1 = f1   - a_off(1,4,j)*dq(4,icol)
                f2 = f2   - a_off(2,4,j)*dq(4,icol)
                f3 = f3   - a_off(3,4,j)*dq(4,icol)
                f4 = f4   - a_off(4,4,j)*dq(4,icol)
                f5 = f5   - a_off(5,4,j)*dq(4,icol)

                f1 = f1   - a_off(1,5,j)*dq(5,icol)
                f2 = f2   - a_off(2,5,j)*dq(5,icol)
                f3 = f3   - a_off(3,5,j)*dq(5,icol)
                f4 = f4   - a_off(4,5,j)*dq(5,icol)
                f5 = f5   - a_off(5,5,j)*dq(5,icol)

              end do

            else point_is_periodic

              f1 = stored_f1(group)
              f2 = stored_f2(group)
              f3 = stored_f3(group)
              f4 = stored_f4(group)
              f5 = stored_f5(group)

            endif point_is_periodic

! Forward...sequential access to a_diag_lu.

            f2 = f2 - a_diag_lu(2,1,n)*f1
            f3 = f3 - a_diag_lu(3,1,n)*f1
            f4 = f4 - a_diag_lu(4,1,n)*f1
            f5 = f5 - a_diag_lu(5,1,n)*f1

            f3 = f3 - a_diag_lu(3,2,n)*f2
            f4 = f4 - a_diag_lu(4,2,n)*f2
            f5 = f5 - a_diag_lu(5,2,n)*f2

            f4 = f4 - a_diag_lu(4,3,n)*f3
            f5 = f5 - a_diag_lu(5,3,n)*f3

            f5 = f5 - a_diag_lu(5,4,n)*f4

! Backward...sequential access to a_diag_lu.

            dq(5,n) = f5 * a_diag_lu(5,5,n)
            f1 = f1 - a_diag_lu(1,5,n)*dq(5,n)
            f2 = f2 - a_diag_lu(2,5,n)*dq(5,n)
            f3 = f3 - a_diag_lu(3,5,n)*dq(5,n)
            f4 = f4 - a_diag_lu(4,5,n)*dq(5,n)

            dq(4,n) = f4 * a_diag_lu(4,4,n)
            f1 = f1 - a_diag_lu(1,4,n)*dq(4,n)
            f2 = f2 - a_diag_lu(2,4,n)*dq(4,n)
            f3 = f3 - a_diag_lu(3,4,n)*dq(4,n)

            dq(3,n) = f3 * a_diag_lu(3,3,n)
            f1 = f1 - a_diag_lu(1,3,n)*dq(3,n)
            f2 = f2 - a_diag_lu(2,3,n)*dq(3,n)

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)

            update_secondary : do n = start, periodic_end
              group = periodic_nature(n)
              if ( group >= 0 ) cycle update_secondary
              group = abs(group)
              primary_entry = periodic_data(group)%mlist(1)
              dq(1,n) = dq(1,primary_entry)
              dq(2,n) = dq(2,primary_entry)
              dq(3,n) = dq(3,primary_entry)
              dq(4,n) = dq(4,primary_entry)
              dq(5,n) = dq(5,primary_entry)
            end do update_secondary

            call lmpi_start_xfer(dq,sr_opt=sr(color))

          case(2)

            call lmpi_complete_xfer(dq,sr_opt=sr(color))

          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine ppoint_solve_5


!================================ POINT_SOLVE_6 ==============================80
!
! Performs G-S iteration on 6x6 system of specified equation set
!
!=============================================================================80

  subroutine point_solve_6(colored_sweeps, color_indices, max_colored_sweeps,  &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns,                                            &
                            solve_backwards, dq_dim, nr, nm,                   &
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag_lu, a_off, sr, color_boundary_end, &
                            nnz0)

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end,ipass
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride

    real(dqp) :: f1,f2,f3,f4,f5,f6

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(2,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            if ( solve_backwards > 0 ) then
              f1 = -res(1,n)
              f2 = -res(2,n)
              f3 = -res(3,n)
              f4 = -res(4,n)
              f5 = -res(5,n)
              f6 = -res(6,n)
            else
              f1 = res(1,n)
              f2 = res(2,n)
              f3 = res(3,n)
              f4 = res(4,n)
              f5 = res(5,n)
              f6 = res(6,n)
            end if

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)

              f1 = f1   - a_off(1,1,j)*dq(1,icol)
              f2 = f2   - a_off(2,1,j)*dq(1,icol)
              f3 = f3   - a_off(3,1,j)*dq(1,icol)
              f4 = f4   - a_off(4,1,j)*dq(1,icol)
              f5 = f5   - a_off(5,1,j)*dq(1,icol)
              f6 = f6   - a_off(6,1,j)*dq(1,icol)

              f1 = f1   - a_off(1,2,j)*dq(2,icol)
              f2 = f2   - a_off(2,2,j)*dq(2,icol)
              f3 = f3   - a_off(3,2,j)*dq(2,icol)
              f4 = f4   - a_off(4,2,j)*dq(2,icol)
              f5 = f5   - a_off(5,2,j)*dq(2,icol)
              f6 = f6   - a_off(6,2,j)*dq(2,icol)

              f1 = f1   - a_off(1,3,j)*dq(3,icol)
              f2 = f2   - a_off(2,3,j)*dq(3,icol)
              f3 = f3   - a_off(3,3,j)*dq(3,icol)
              f4 = f4   - a_off(4,3,j)*dq(3,icol)
              f5 = f5   - a_off(5,3,j)*dq(3,icol)
              f6 = f6   - a_off(6,3,j)*dq(3,icol)

              f1 = f1   - a_off(1,4,j)*dq(4,icol)
              f2 = f2   - a_off(2,4,j)*dq(4,icol)
              f3 = f3   - a_off(3,4,j)*dq(4,icol)
              f4 = f4   - a_off(4,4,j)*dq(4,icol)
              f5 = f5   - a_off(5,4,j)*dq(4,icol)
              f6 = f6   - a_off(6,4,j)*dq(4,icol)

              f1 = f1   - a_off(1,5,j)*dq(5,icol)
              f2 = f2   - a_off(2,5,j)*dq(5,icol)
              f3 = f3   - a_off(3,5,j)*dq(5,icol)
              f4 = f4   - a_off(4,5,j)*dq(5,icol)
              f5 = f5   - a_off(5,5,j)*dq(5,icol)
              f6 = f6   - a_off(6,5,j)*dq(5,icol)

              f1 = f1   - a_off(1,6,j)*dq(6,icol)
              f2 = f2   - a_off(2,6,j)*dq(6,icol)
              f3 = f3   - a_off(3,6,j)*dq(6,icol)
              f4 = f4   - a_off(4,6,j)*dq(6,icol)
              f5 = f5   - a_off(5,6,j)*dq(6,icol)
              f6 = f6   - a_off(6,6,j)*dq(6,icol)

            end do

! Forward...sequential access to a_diag_lu.

            f2 = f2 - a_diag_lu(2,1,n)*f1
            f3 = f3 - a_diag_lu(3,1,n)*f1
            f4 = f4 - a_diag_lu(4,1,n)*f1
            f5 = f5 - a_diag_lu(5,1,n)*f1
            f6 = f6 - a_diag_lu(6,1,n)*f1

            f3 = f3 - a_diag_lu(3,2,n)*f2
            f4 = f4 - a_diag_lu(4,2,n)*f2
            f5 = f5 - a_diag_lu(5,2,n)*f2
            f6 = f6 - a_diag_lu(6,2,n)*f2

            f4 = f4 - a_diag_lu(4,3,n)*f3
            f5 = f5 - a_diag_lu(5,3,n)*f3
            f6 = f6 - a_diag_lu(6,3,n)*f3

            f5 = f5 - a_diag_lu(5,4,n)*f4
            f6 = f6 - a_diag_lu(6,4,n)*f4

            f6 = f6 - a_diag_lu(6,5,n)*f5

! Backward...sequential access to a_diag_lu.

            dq(6,n) = f6 * a_diag_lu(6,6,n)
            f1 = f1 - a_diag_lu(1,6,n)*dq(6,n)
            f2 = f2 - a_diag_lu(2,6,n)*dq(6,n)
            f3 = f3 - a_diag_lu(3,6,n)*dq(6,n)
            f4 = f4 - a_diag_lu(4,6,n)*dq(6,n)
            f5 = f5 - a_diag_lu(5,6,n)*dq(6,n)

            dq(5,n) = f5 * a_diag_lu(5,5,n)
            f1 = f1 - a_diag_lu(1,5,n)*dq(5,n)
            f2 = f2 - a_diag_lu(2,5,n)*dq(5,n)
            f3 = f3 - a_diag_lu(3,5,n)*dq(5,n)
            f4 = f4 - a_diag_lu(4,5,n)*dq(5,n)

            dq(4,n) = f4 * a_diag_lu(4,4,n)
            f1 = f1 - a_diag_lu(1,4,n)*dq(4,n)
            f2 = f2 - a_diag_lu(2,4,n)*dq(4,n)
            f3 = f3 - a_diag_lu(3,4,n)*dq(4,n)

            dq(3,n) = f3 * a_diag_lu(3,3,n)
            f1 = f1 - a_diag_lu(1,3,n)*dq(3,n)
            f2 = f2 - a_diag_lu(2,3,n)*dq(3,n)

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)
            call lmpi_start_xfer(dq,sr_opt=sr(color))
          case(2)
            call lmpi_complete_xfer(dq,sr_opt=sr(color))
          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine point_solve_6
! no comment
!================================ PPOINT_SOLVE_6 =============================80
!
! Performs G-S iteration on 6x6 system of specified equation set
! Periodic version
!
!=============================================================================80

  subroutine ppoint_solve_6(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam, n_eqns, solve_backwards, &
                            dq_dim, nr, nm, n_sweeps, neqmax, res, dq,         &
                            a_diag_lu, a_off, sr, color_periodic_end,          &
                            color_boundary_end, nnz0)

    use periodics, only : periodic_nature, nperiodic, periodic_data

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_periodic_end
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end
    integer :: color, loop_colored_sweeps, max_ind, ipass
    integer :: sweep_start, sweep_end, sweep_stride, group
    integer :: primary_entry, periodic_end

    real(dqp) :: f1,f2,f3,f4,f5,f6

    real(dqp), dimension(nperiodic) :: stored_f1
    real(dqp), dimension(nperiodic) :: stored_f2
    real(dqp), dimension(nperiodic) :: stored_f3
    real(dqp), dimension(nperiodic) :: stored_f4
    real(dqp), dimension(nperiodic) :: stored_f5
    real(dqp), dimension(nperiodic) :: stored_f6

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      stored_f1 = 0.0_dqp
      stored_f2 = 0.0_dqp
      stored_f3 = 0.0_dqp
      stored_f4 = 0.0_dqp
      stored_f5 = 0.0_dqp
      stored_f6 = 0.0_dqp

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        start        =  1
        periodic_end = -1

        if(color <= colored_sweeps) then
          start        = color_indices(1,color)
          periodic_end = color_periodic_end(color)
        endif

! First gather all contributions to stored_f from periodic points

        gather_stored_f : do n = start, periodic_end

          group = abs(periodic_nature(n))

          if ( group == 0 ) cycle gather_stored_f

          if ( solve_backwards > 0 ) then
             f1 = -res(1,n)
             f2 = -res(2,n)
             f3 = -res(3,n)
             f4 = -res(4,n)
             f5 = -res(5,n)
             f6 = -res(6,n)
          else
             f1 = res(1,n)
             f2 = res(2,n)
             f3 = res(3,n)
             f4 = res(4,n)
             f5 = res(5,n)
             f6 = res(6,n)
          end if

          istart = iam(n)
          iend   = iam(n+1)-1

          do j = istart,iend
            icol = jam(j)

            f1 = f1   - a_off(1,1,j)*dq(1,icol)
            f2 = f2   - a_off(2,1,j)*dq(1,icol)
            f3 = f3   - a_off(3,1,j)*dq(1,icol)
            f4 = f4   - a_off(4,1,j)*dq(1,icol)
            f5 = f5   - a_off(5,1,j)*dq(1,icol)
            f6 = f6   - a_off(6,1,j)*dq(1,icol)

            f1 = f1   - a_off(1,2,j)*dq(2,icol)
            f2 = f2   - a_off(2,2,j)*dq(2,icol)
            f3 = f3   - a_off(3,2,j)*dq(2,icol)
            f4 = f4   - a_off(4,2,j)*dq(2,icol)
            f5 = f5   - a_off(5,2,j)*dq(2,icol)
            f6 = f6   - a_off(6,2,j)*dq(2,icol)

            f1 = f1   - a_off(1,3,j)*dq(3,icol)
            f2 = f2   - a_off(2,3,j)*dq(3,icol)
            f3 = f3   - a_off(3,3,j)*dq(3,icol)
            f4 = f4   - a_off(4,3,j)*dq(3,icol)
            f5 = f5   - a_off(5,3,j)*dq(3,icol)
            f6 = f6   - a_off(6,3,j)*dq(3,icol)

            f1 = f1   - a_off(1,4,j)*dq(4,icol)
            f2 = f2   - a_off(2,4,j)*dq(4,icol)
            f3 = f3   - a_off(3,4,j)*dq(4,icol)
            f4 = f4   - a_off(4,4,j)*dq(4,icol)
            f5 = f5   - a_off(5,4,j)*dq(4,icol)
            f6 = f6   - a_off(6,4,j)*dq(4,icol)

            f1 = f1   - a_off(1,5,j)*dq(5,icol)
            f2 = f2   - a_off(2,5,j)*dq(5,icol)
            f3 = f3   - a_off(3,5,j)*dq(5,icol)
            f4 = f4   - a_off(4,5,j)*dq(5,icol)
            f5 = f5   - a_off(5,5,j)*dq(5,icol)
            f6 = f6   - a_off(6,5,j)*dq(5,icol)

            f1 = f1   - a_off(1,6,j)*dq(6,icol)
            f2 = f2   - a_off(2,6,j)*dq(6,icol)
            f3 = f3   - a_off(3,6,j)*dq(6,icol)
            f4 = f4   - a_off(4,6,j)*dq(6,icol)
            f5 = f5   - a_off(5,6,j)*dq(6,icol)
            f6 = f6   - a_off(6,6,j)*dq(6,icol)

          end do

          stored_f1(group) = stored_f1(group) + f1
          stored_f2(group) = stored_f2(group) + f2
          stored_f3(group) = stored_f3(group) + f3
          stored_f4(group) = stored_f4(group) + f4
          stored_f5(group) = stored_f5(group) + f5
          stored_f6(group) = stored_f6(group) + f6

        end do gather_stored_f

! Now solve for dq everywhere in color

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then

            start = 1
            end   = 0

          else

            max_ind = max(color_boundary_end(color),color_periodic_end(color))

            select case(ipass)
            case(1)
              if ( max_ind == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = max_ind
              endif
            case(2)
              if ( max_ind == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = max_ind+1
                end   = color_indices(2,color)
              endif
            end select

          endif

          rhs_solve : do n = start, end

            group = 0
            if ( n <= periodic_end ) group = periodic_nature(n)

! Do not solve secondary periodic points, they will be copied from
! primary points later

            if ( group < 0 ) cycle rhs_solve

            point_is_periodic : if ( group == 0 ) then

              if ( solve_backwards > 0 ) then
                f1 = -res(1,n)
                f2 = -res(2,n)
                f3 = -res(3,n)
                f4 = -res(4,n)
                f5 = -res(5,n)
                f6 = -res(6,n)
              else
                f1 = res(1,n)
                f2 = res(2,n)
                f3 = res(3,n)
                f4 = res(4,n)
                f5 = res(5,n)
                f6 = res(6,n)
              end if

              istart = iam(n)
              iend   = iam(n+1)-1

              do j = istart,iend
                icol = jam(j)

                f1 = f1   - a_off(1,1,j)*dq(1,icol)
                f2 = f2   - a_off(2,1,j)*dq(1,icol)
                f3 = f3   - a_off(3,1,j)*dq(1,icol)
                f4 = f4   - a_off(4,1,j)*dq(1,icol)
                f5 = f5   - a_off(5,1,j)*dq(1,icol)
                f6 = f6   - a_off(6,1,j)*dq(1,icol)

                f1 = f1   - a_off(1,2,j)*dq(2,icol)
                f2 = f2   - a_off(2,2,j)*dq(2,icol)
                f3 = f3   - a_off(3,2,j)*dq(2,icol)
                f4 = f4   - a_off(4,2,j)*dq(2,icol)
                f5 = f5   - a_off(5,2,j)*dq(2,icol)
                f6 = f6   - a_off(6,2,j)*dq(2,icol)

                f1 = f1   - a_off(1,3,j)*dq(3,icol)
                f2 = f2   - a_off(2,3,j)*dq(3,icol)
                f3 = f3   - a_off(3,3,j)*dq(3,icol)
                f4 = f4   - a_off(4,3,j)*dq(3,icol)
                f5 = f5   - a_off(5,3,j)*dq(3,icol)
                f6 = f6   - a_off(6,3,j)*dq(3,icol)

                f1 = f1   - a_off(1,4,j)*dq(4,icol)
                f2 = f2   - a_off(2,4,j)*dq(4,icol)
                f3 = f3   - a_off(3,4,j)*dq(4,icol)
                f4 = f4   - a_off(4,4,j)*dq(4,icol)
                f5 = f5   - a_off(5,4,j)*dq(4,icol)
                f6 = f6   - a_off(6,4,j)*dq(4,icol)

                f1 = f1   - a_off(1,5,j)*dq(5,icol)
                f2 = f2   - a_off(2,5,j)*dq(5,icol)
                f3 = f3   - a_off(3,5,j)*dq(5,icol)
                f4 = f4   - a_off(4,5,j)*dq(5,icol)
                f5 = f5   - a_off(5,5,j)*dq(5,icol)
                f6 = f6   - a_off(6,5,j)*dq(5,icol)

                f1 = f1   - a_off(1,6,j)*dq(6,icol)
                f2 = f2   - a_off(2,6,j)*dq(6,icol)
                f3 = f3   - a_off(3,6,j)*dq(6,icol)
                f4 = f4   - a_off(4,6,j)*dq(6,icol)
                f5 = f5   - a_off(5,6,j)*dq(6,icol)
                f6 = f6   - a_off(6,6,j)*dq(6,icol)

              end do

            else point_is_periodic

              f1 = stored_f1(group)
              f2 = stored_f2(group)
              f3 = stored_f3(group)
              f4 = stored_f4(group)
              f5 = stored_f5(group)
              f6 = stored_f6(group)

            endif point_is_periodic

! Forward...sequential access to a_diag_lu.

            f2 = f2 - a_diag_lu(2,1,n)*f1
            f3 = f3 - a_diag_lu(3,1,n)*f1
            f4 = f4 - a_diag_lu(4,1,n)*f1
            f5 = f5 - a_diag_lu(5,1,n)*f1
            f6 = f6 - a_diag_lu(6,1,n)*f1

            f3 = f3 - a_diag_lu(3,2,n)*f2
            f4 = f4 - a_diag_lu(4,2,n)*f2
            f5 = f5 - a_diag_lu(5,2,n)*f2
            f6 = f6 - a_diag_lu(6,2,n)*f2

            f4 = f4 - a_diag_lu(4,3,n)*f3
            f5 = f5 - a_diag_lu(5,3,n)*f3
            f6 = f6 - a_diag_lu(6,3,n)*f3

            f5 = f5 - a_diag_lu(5,4,n)*f4
            f6 = f6 - a_diag_lu(6,4,n)*f4

            f6 = f6 - a_diag_lu(6,5,n)*f5

! Backward...sequential access to a_diag_lu.

            dq(6,n) = f6 * a_diag_lu(6,6,n)
            f1 = f1 - a_diag_lu(1,6,n)*dq(6,n)
            f2 = f2 - a_diag_lu(2,6,n)*dq(6,n)
            f3 = f3 - a_diag_lu(3,6,n)*dq(6,n)
            f4 = f4 - a_diag_lu(4,6,n)*dq(6,n)
            f5 = f5 - a_diag_lu(5,6,n)*dq(6,n)

            dq(5,n) = f5 * a_diag_lu(5,5,n)
            f1 = f1 - a_diag_lu(1,5,n)*dq(5,n)
            f2 = f2 - a_diag_lu(2,5,n)*dq(5,n)
            f3 = f3 - a_diag_lu(3,5,n)*dq(5,n)
            f4 = f4 - a_diag_lu(4,5,n)*dq(5,n)

            dq(4,n) = f4 * a_diag_lu(4,4,n)
            f1 = f1 - a_diag_lu(1,4,n)*dq(4,n)
            f2 = f2 - a_diag_lu(2,4,n)*dq(4,n)
            f3 = f3 - a_diag_lu(3,4,n)*dq(4,n)

            dq(3,n) = f3 * a_diag_lu(3,3,n)
            f1 = f1 - a_diag_lu(1,3,n)*dq(3,n)
            f2 = f2 - a_diag_lu(2,3,n)*dq(3,n)

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)

            update_secondary : do n = start, periodic_end
              group = periodic_nature(n)
              if ( group >= 0 ) cycle update_secondary
              group = abs(group)
              primary_entry = periodic_data(group)%mlist(1)
              dq(1,n) = dq(1,primary_entry)
              dq(2,n) = dq(2,primary_entry)
              dq(3,n) = dq(3,primary_entry)
              dq(4,n) = dq(4,primary_entry)
              dq(5,n) = dq(5,primary_entry)
              dq(6,n) = dq(6,primary_entry)
            end do update_secondary

            call lmpi_start_xfer(dq,sr_opt=sr(color))

          case(2)

            call lmpi_complete_xfer(dq,sr_opt=sr(color))

          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine ppoint_solve_6


!================================ POINT_SOLVE_7 ==============================80
!
! Performs G-S iteration on 7x7 system of specified equation set
!
!=============================================================================80

  subroutine point_solve_7(colored_sweeps, color_indices, max_colored_sweeps,  &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns,                                            &
                            solve_backwards, dq_dim, nr, nm,                   &
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag_lu, a_off, sr, color_boundary_end, &
                            nnz0)

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end,ipass
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride

    real(dqp) :: f1,f2,f3,f4,f5,f6,f7

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(2,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            if ( solve_backwards > 0 ) then
              f1 = -res(1,n)
              f2 = -res(2,n)
              f3 = -res(3,n)
              f4 = -res(4,n)
              f5 = -res(5,n)
              f6 = -res(6,n)
              f7 = -res(7,n)
            else
              f1 = res(1,n)
              f2 = res(2,n)
              f3 = res(3,n)
              f4 = res(4,n)
              f5 = res(5,n)
              f6 = res(6,n)
              f7 = res(7,n)
            end if

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)

              f1 = f1   - a_off(1,1,j)*dq(1,icol)
              f2 = f2   - a_off(2,1,j)*dq(1,icol)
              f3 = f3   - a_off(3,1,j)*dq(1,icol)
              f4 = f4   - a_off(4,1,j)*dq(1,icol)
              f5 = f5   - a_off(5,1,j)*dq(1,icol)
              f6 = f6   - a_off(6,1,j)*dq(1,icol)
              f7 = f7   - a_off(7,1,j)*dq(1,icol)

              f1 = f1   - a_off(1,2,j)*dq(2,icol)
              f2 = f2   - a_off(2,2,j)*dq(2,icol)
              f3 = f3   - a_off(3,2,j)*dq(2,icol)
              f4 = f4   - a_off(4,2,j)*dq(2,icol)
              f5 = f5   - a_off(5,2,j)*dq(2,icol)
              f6 = f6   - a_off(6,2,j)*dq(2,icol)
              f7 = f7   - a_off(7,2,j)*dq(2,icol)

              f1 = f1   - a_off(1,3,j)*dq(3,icol)
              f2 = f2   - a_off(2,3,j)*dq(3,icol)
              f3 = f3   - a_off(3,3,j)*dq(3,icol)
              f4 = f4   - a_off(4,3,j)*dq(3,icol)
              f5 = f5   - a_off(5,3,j)*dq(3,icol)
              f6 = f6   - a_off(6,3,j)*dq(3,icol)
              f7 = f7   - a_off(7,3,j)*dq(3,icol)

              f1 = f1   - a_off(1,4,j)*dq(4,icol)
              f2 = f2   - a_off(2,4,j)*dq(4,icol)
              f3 = f3   - a_off(3,4,j)*dq(4,icol)
              f4 = f4   - a_off(4,4,j)*dq(4,icol)
              f5 = f5   - a_off(5,4,j)*dq(4,icol)
              f6 = f6   - a_off(6,4,j)*dq(4,icol)
              f7 = f7   - a_off(7,4,j)*dq(4,icol)

              f1 = f1   - a_off(1,5,j)*dq(5,icol)
              f2 = f2   - a_off(2,5,j)*dq(5,icol)
              f3 = f3   - a_off(3,5,j)*dq(5,icol)
              f4 = f4   - a_off(4,5,j)*dq(5,icol)
              f5 = f5   - a_off(5,5,j)*dq(5,icol)
              f6 = f6   - a_off(6,5,j)*dq(5,icol)
              f7 = f7   - a_off(7,5,j)*dq(5,icol)

              f1 = f1   - a_off(1,6,j)*dq(6,icol)
              f2 = f2   - a_off(2,6,j)*dq(6,icol)
              f3 = f3   - a_off(3,6,j)*dq(6,icol)
              f4 = f4   - a_off(4,6,j)*dq(6,icol)
              f5 = f5   - a_off(5,6,j)*dq(6,icol)
              f6 = f6   - a_off(6,6,j)*dq(6,icol)
              f7 = f7   - a_off(7,6,j)*dq(6,icol)

              f1 = f1   - a_off(1,7,j)*dq(7,icol)
              f2 = f2   - a_off(2,7,j)*dq(7,icol)
              f3 = f3   - a_off(3,7,j)*dq(7,icol)
              f4 = f4   - a_off(4,7,j)*dq(7,icol)
              f5 = f5   - a_off(5,7,j)*dq(7,icol)
              f6 = f6   - a_off(6,7,j)*dq(7,icol)
              f7 = f7   - a_off(7,7,j)*dq(7,icol)

            end do

! Forward...sequential access to a_diag_lu.

            f2 = f2 - a_diag_lu(2,1,n)*f1
            f3 = f3 - a_diag_lu(3,1,n)*f1
            f4 = f4 - a_diag_lu(4,1,n)*f1
            f5 = f5 - a_diag_lu(5,1,n)*f1
            f6 = f6 - a_diag_lu(6,1,n)*f1
            f7 = f7 - a_diag_lu(7,1,n)*f1

            f3 = f3 - a_diag_lu(3,2,n)*f2
            f4 = f4 - a_diag_lu(4,2,n)*f2
            f5 = f5 - a_diag_lu(5,2,n)*f2
            f6 = f6 - a_diag_lu(6,2,n)*f2
            f7 = f7 - a_diag_lu(7,2,n)*f2

            f4 = f4 - a_diag_lu(4,3,n)*f3
            f5 = f5 - a_diag_lu(5,3,n)*f3
            f6 = f6 - a_diag_lu(6,3,n)*f3
            f7 = f7 - a_diag_lu(7,3,n)*f3

            f5 = f5 - a_diag_lu(5,4,n)*f4
            f6 = f6 - a_diag_lu(6,4,n)*f4
            f7 = f7 - a_diag_lu(7,4,n)*f4

            f6 = f6 - a_diag_lu(6,5,n)*f5
            f7 = f7 - a_diag_lu(7,5,n)*f5

            f7 = f7 - a_diag_lu(7,6,n)*f6

! Backward...sequential access to a_diag_lu.

            dq(7,n) = f7 * a_diag_lu(7,7,n)
            f1 = f1 - a_diag_lu(1,7,n)*dq(7,n)
            f2 = f2 - a_diag_lu(2,7,n)*dq(7,n)
            f3 = f3 - a_diag_lu(3,7,n)*dq(7,n)
            f4 = f4 - a_diag_lu(4,7,n)*dq(7,n)
            f5 = f5 - a_diag_lu(5,7,n)*dq(7,n)
            f6 = f6 - a_diag_lu(6,7,n)*dq(7,n)

            dq(6,n) = f6 * a_diag_lu(6,6,n)
            f1 = f1 - a_diag_lu(1,6,n)*dq(6,n)
            f2 = f2 - a_diag_lu(2,6,n)*dq(6,n)
            f3 = f3 - a_diag_lu(3,6,n)*dq(6,n)
            f4 = f4 - a_diag_lu(4,6,n)*dq(6,n)
            f5 = f5 - a_diag_lu(5,6,n)*dq(6,n)

            dq(5,n) = f5 * a_diag_lu(5,5,n)
            f1 = f1 - a_diag_lu(1,5,n)*dq(5,n)
            f2 = f2 - a_diag_lu(2,5,n)*dq(5,n)
            f3 = f3 - a_diag_lu(3,5,n)*dq(5,n)
            f4 = f4 - a_diag_lu(4,5,n)*dq(5,n)

            dq(4,n) = f4 * a_diag_lu(4,4,n)
            f1 = f1 - a_diag_lu(1,4,n)*dq(4,n)
            f2 = f2 - a_diag_lu(2,4,n)*dq(4,n)
            f3 = f3 - a_diag_lu(3,4,n)*dq(4,n)

            dq(3,n) = f3 * a_diag_lu(3,3,n)
            f1 = f1 - a_diag_lu(1,3,n)*dq(3,n)
            f2 = f2 - a_diag_lu(2,3,n)*dq(3,n)

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)
            call lmpi_start_xfer(dq,sr_opt=sr(color))
          case(2)
            call lmpi_complete_xfer(dq,sr_opt=sr(color))
          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine point_solve_7


!================================ PPOINT_SOLVE_7 =============================80
!
! Performs G-S iteration on 7x7 system of specified equation set
! Periodic version
!
!=============================================================================80

  subroutine ppoint_solve_7(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam, n_eqns, solve_backwards, &
                            dq_dim, nr, nm, n_sweeps, neqmax, res, dq,         &
                            a_diag_lu, a_off, sr, color_periodic_end,          &
                            color_boundary_end, nnz0)

    use periodics, only : periodic_nature, nperiodic, periodic_data

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia, nnz0
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_periodic_end
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    real(odp), dimension(nm,nm,nnz0), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end
    integer :: color, loop_colored_sweeps, max_ind, ipass
    integer :: sweep_start, sweep_end, sweep_stride, group
    integer :: primary_entry, periodic_end

    real(dqp) :: f1,f2,f3,f4,f5,f6,f7

    real(dqp), dimension(nperiodic) :: stored_f1
    real(dqp), dimension(nperiodic) :: stored_f2
    real(dqp), dimension(nperiodic) :: stored_f3
    real(dqp), dimension(nperiodic) :: stored_f4
    real(dqp), dimension(nperiodic) :: stored_f5
    real(dqp), dimension(nperiodic) :: stored_f6
    real(dqp), dimension(nperiodic) :: stored_f7

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    sweeping : do sweep = 1, n_sweeps

      stored_f1 = 0.0_dqp
      stored_f2 = 0.0_dqp
      stored_f3 = 0.0_dqp
      stored_f4 = 0.0_dqp
      stored_f5 = 0.0_dqp
      stored_f6 = 0.0_dqp
      stored_f7 = 0.0_dqp

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        start        =  1
        periodic_end = -1

        if(color <= colored_sweeps) then
          start        = color_indices(1,color)
          periodic_end = color_periodic_end(color)
        endif

! First gather all contributions to stored_f from periodic points

        gather_stored_f : do n = start, periodic_end

          group = abs(periodic_nature(n))

          if ( group == 0 ) cycle gather_stored_f

          if ( solve_backwards > 0 ) then
             f1 = -res(1,n)
             f2 = -res(2,n)
             f3 = -res(3,n)
             f4 = -res(4,n)
             f5 = -res(5,n)
             f6 = -res(6,n)
             f7 = -res(7,n)
          else
             f1 = res(1,n)
             f2 = res(2,n)
             f3 = res(3,n)
             f4 = res(4,n)
             f5 = res(5,n)
             f6 = res(6,n)
             f7 = res(7,n)
          end if

          istart = iam(n)
          iend   = iam(n+1)-1

          do j = istart,iend
            icol = jam(j)

            f1 = f1   - a_off(1,1,j)*dq(1,icol)
            f2 = f2   - a_off(2,1,j)*dq(1,icol)
            f3 = f3   - a_off(3,1,j)*dq(1,icol)
            f4 = f4   - a_off(4,1,j)*dq(1,icol)
            f5 = f5   - a_off(5,1,j)*dq(1,icol)
            f6 = f6   - a_off(6,1,j)*dq(1,icol)
            f7 = f7   - a_off(7,1,j)*dq(1,icol)

            f1 = f1   - a_off(1,2,j)*dq(2,icol)
            f2 = f2   - a_off(2,2,j)*dq(2,icol)
            f3 = f3   - a_off(3,2,j)*dq(2,icol)
            f4 = f4   - a_off(4,2,j)*dq(2,icol)
            f5 = f5   - a_off(5,2,j)*dq(2,icol)
            f6 = f6   - a_off(6,2,j)*dq(2,icol)
            f7 = f7   - a_off(7,2,j)*dq(2,icol)

            f1 = f1   - a_off(1,3,j)*dq(3,icol)
            f2 = f2   - a_off(2,3,j)*dq(3,icol)
            f3 = f3   - a_off(3,3,j)*dq(3,icol)
            f4 = f4   - a_off(4,3,j)*dq(3,icol)
            f5 = f5   - a_off(5,3,j)*dq(3,icol)
            f6 = f6   - a_off(6,3,j)*dq(3,icol)
            f7 = f7   - a_off(7,3,j)*dq(3,icol)

            f1 = f1   - a_off(1,4,j)*dq(4,icol)
            f2 = f2   - a_off(2,4,j)*dq(4,icol)
            f3 = f3   - a_off(3,4,j)*dq(4,icol)
            f4 = f4   - a_off(4,4,j)*dq(4,icol)
            f5 = f5   - a_off(5,4,j)*dq(4,icol)
            f6 = f6   - a_off(6,4,j)*dq(4,icol)
            f7 = f7   - a_off(7,4,j)*dq(4,icol)

            f1 = f1   - a_off(1,5,j)*dq(5,icol)
            f2 = f2   - a_off(2,5,j)*dq(5,icol)
            f3 = f3   - a_off(3,5,j)*dq(5,icol)
            f4 = f4   - a_off(4,5,j)*dq(5,icol)
            f5 = f5   - a_off(5,5,j)*dq(5,icol)
            f6 = f6   - a_off(6,5,j)*dq(5,icol)
            f7 = f7   - a_off(7,5,j)*dq(5,icol)

            f1 = f1   - a_off(1,6,j)*dq(6,icol)
            f2 = f2   - a_off(2,6,j)*dq(6,icol)
            f3 = f3   - a_off(3,6,j)*dq(6,icol)
            f4 = f4   - a_off(4,6,j)*dq(6,icol)
            f5 = f5   - a_off(5,6,j)*dq(6,icol)
            f6 = f6   - a_off(6,6,j)*dq(6,icol)
            f7 = f7   - a_off(7,6,j)*dq(6,icol)

            f1 = f1   - a_off(1,7,j)*dq(7,icol)
            f2 = f2   - a_off(2,7,j)*dq(7,icol)
            f3 = f3   - a_off(3,7,j)*dq(7,icol)
            f4 = f4   - a_off(4,7,j)*dq(7,icol)
            f5 = f5   - a_off(5,7,j)*dq(7,icol)
            f6 = f6   - a_off(6,7,j)*dq(7,icol)
            f7 = f7   - a_off(7,7,j)*dq(7,icol)

          end do

          stored_f1(group) = stored_f1(group) + f1
          stored_f2(group) = stored_f2(group) + f2
          stored_f3(group) = stored_f3(group) + f3
          stored_f4(group) = stored_f4(group) + f4
          stored_f5(group) = stored_f5(group) + f5
          stored_f6(group) = stored_f6(group) + f6
          stored_f7(group) = stored_f7(group) + f7

        end do gather_stored_f

! Now solve for dq everywhere in color

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then

            start = 1
            end   = 0

          else

            max_ind = max(color_boundary_end(color),color_periodic_end(color))

            select case(ipass)
            case(1)
              if ( max_ind == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(1,color)
                end   = max_ind
              endif
            case(2)
              if ( max_ind == 0 ) then
                start = color_indices(1,color)
                end   = color_indices(2,color)
              else
                start = max_ind+1
                end   = color_indices(2,color)
              endif
            end select

          endif

          rhs_solve : do n = start, end

            group = 0
            if ( n <= periodic_end ) group = periodic_nature(n)

! Do not solve secondary periodic points, they will be copied from
! primary points later

            if ( group < 0 ) cycle rhs_solve

            point_is_periodic : if ( group == 0 ) then

              if ( solve_backwards > 0 ) then
                f1 = -res(1,n)
                f2 = -res(2,n)
                f3 = -res(3,n)
                f4 = -res(4,n)
                f5 = -res(5,n)
                f6 = -res(6,n)
                f7 = -res(7,n)
              else
                f1 = res(1,n)
                f2 = res(2,n)
                f3 = res(3,n)
                f4 = res(4,n)
                f5 = res(5,n)
                f6 = res(6,n)
                f7 = res(7,n)
              end if

              istart = iam(n)
              iend   = iam(n+1)-1

              do j = istart,iend
                icol = jam(j)

                f1 = f1   - a_off(1,1,j)*dq(1,icol)
                f2 = f2   - a_off(2,1,j)*dq(1,icol)
                f3 = f3   - a_off(3,1,j)*dq(1,icol)
                f4 = f4   - a_off(4,1,j)*dq(1,icol)
                f5 = f5   - a_off(5,1,j)*dq(1,icol)
                f6 = f6   - a_off(6,1,j)*dq(1,icol)
                f7 = f7   - a_off(7,1,j)*dq(1,icol)

                f1 = f1   - a_off(1,2,j)*dq(2,icol)
                f2 = f2   - a_off(2,2,j)*dq(2,icol)
                f3 = f3   - a_off(3,2,j)*dq(2,icol)
                f4 = f4   - a_off(4,2,j)*dq(2,icol)
                f5 = f5   - a_off(5,2,j)*dq(2,icol)
                f6 = f6   - a_off(6,2,j)*dq(2,icol)
                f7 = f7   - a_off(7,2,j)*dq(2,icol)

                f1 = f1   - a_off(1,3,j)*dq(3,icol)
                f2 = f2   - a_off(2,3,j)*dq(3,icol)
                f3 = f3   - a_off(3,3,j)*dq(3,icol)
                f4 = f4   - a_off(4,3,j)*dq(3,icol)
                f5 = f5   - a_off(5,3,j)*dq(3,icol)
                f6 = f6   - a_off(6,3,j)*dq(3,icol)
                f7 = f7   - a_off(7,3,j)*dq(3,icol)

                f1 = f1   - a_off(1,4,j)*dq(4,icol)
                f2 = f2   - a_off(2,4,j)*dq(4,icol)
                f3 = f3   - a_off(3,4,j)*dq(4,icol)
                f4 = f4   - a_off(4,4,j)*dq(4,icol)
                f5 = f5   - a_off(5,4,j)*dq(4,icol)
                f6 = f6   - a_off(6,4,j)*dq(4,icol)
                f7 = f7   - a_off(7,4,j)*dq(4,icol)

                f1 = f1   - a_off(1,5,j)*dq(5,icol)
                f2 = f2   - a_off(2,5,j)*dq(5,icol)
                f3 = f3   - a_off(3,5,j)*dq(5,icol)
                f4 = f4   - a_off(4,5,j)*dq(5,icol)
                f5 = f5   - a_off(5,5,j)*dq(5,icol)
                f6 = f6   - a_off(6,5,j)*dq(5,icol)
                f7 = f7   - a_off(7,5,j)*dq(5,icol)

                f1 = f1   - a_off(1,6,j)*dq(6,icol)
                f2 = f2   - a_off(2,6,j)*dq(6,icol)
                f3 = f3   - a_off(3,6,j)*dq(6,icol)
                f4 = f4   - a_off(4,6,j)*dq(6,icol)
                f5 = f5   - a_off(5,6,j)*dq(6,icol)
                f6 = f6   - a_off(6,6,j)*dq(6,icol)
                f7 = f7   - a_off(7,6,j)*dq(6,icol)

                f1 = f1   - a_off(1,7,j)*dq(7,icol)
                f2 = f2   - a_off(2,7,j)*dq(7,icol)
                f3 = f3   - a_off(3,7,j)*dq(7,icol)
                f4 = f4   - a_off(4,7,j)*dq(7,icol)
                f5 = f5   - a_off(5,7,j)*dq(7,icol)
                f6 = f6   - a_off(6,7,j)*dq(7,icol)
                f7 = f7   - a_off(7,7,j)*dq(7,icol)

              end do

            else point_is_periodic

              f1 = stored_f1(group)
              f2 = stored_f2(group)
              f3 = stored_f3(group)
              f4 = stored_f4(group)
              f5 = stored_f5(group)
              f6 = stored_f6(group)
              f7 = stored_f7(group)

            endif point_is_periodic

! Forward...sequential access to a_diag_lu.

            f2 = f2 - a_diag_lu(2,1,n)*f1
            f3 = f3 - a_diag_lu(3,1,n)*f1
            f4 = f4 - a_diag_lu(4,1,n)*f1
            f5 = f5 - a_diag_lu(5,1,n)*f1
            f6 = f6 - a_diag_lu(6,1,n)*f1
            f7 = f7 - a_diag_lu(7,1,n)*f1

            f3 = f3 - a_diag_lu(3,2,n)*f2
            f4 = f4 - a_diag_lu(4,2,n)*f2
            f5 = f5 - a_diag_lu(5,2,n)*f2
            f6 = f6 - a_diag_lu(6,2,n)*f2
            f7 = f7 - a_diag_lu(7,2,n)*f2

            f4 = f4 - a_diag_lu(4,3,n)*f3
            f5 = f5 - a_diag_lu(5,3,n)*f3
            f6 = f6 - a_diag_lu(6,3,n)*f3
            f7 = f7 - a_diag_lu(7,3,n)*f3

            f5 = f5 - a_diag_lu(5,4,n)*f4
            f6 = f6 - a_diag_lu(6,4,n)*f4
            f7 = f7 - a_diag_lu(7,4,n)*f4

            f6 = f6 - a_diag_lu(6,5,n)*f5
            f7 = f7 - a_diag_lu(7,5,n)*f5

            f7 = f7 - a_diag_lu(7,6,n)*f6

! Backward...sequential access to a_diag_lu.

            dq(7,n) = f7 * a_diag_lu(7,7,n)
            f1 = f1 - a_diag_lu(1,7,n)*dq(7,n)
            f2 = f2 - a_diag_lu(2,7,n)*dq(7,n)
            f3 = f3 - a_diag_lu(3,7,n)*dq(7,n)
            f4 = f4 - a_diag_lu(4,7,n)*dq(7,n)
            f5 = f5 - a_diag_lu(5,7,n)*dq(7,n)
            f6 = f6 - a_diag_lu(6,7,n)*dq(7,n)

            dq(6,n) = f6 * a_diag_lu(6,6,n)
            f1 = f1 - a_diag_lu(1,6,n)*dq(6,n)
            f2 = f2 - a_diag_lu(2,6,n)*dq(6,n)
            f3 = f3 - a_diag_lu(3,6,n)*dq(6,n)
            f4 = f4 - a_diag_lu(4,6,n)*dq(6,n)
            f5 = f5 - a_diag_lu(5,6,n)*dq(6,n)

            dq(5,n) = f5 * a_diag_lu(5,5,n)
            f1 = f1 - a_diag_lu(1,5,n)*dq(5,n)
            f2 = f2 - a_diag_lu(2,5,n)*dq(5,n)
            f3 = f3 - a_diag_lu(3,5,n)*dq(5,n)
            f4 = f4 - a_diag_lu(4,5,n)*dq(5,n)

            dq(4,n) = f4 * a_diag_lu(4,4,n)
            f1 = f1 - a_diag_lu(1,4,n)*dq(4,n)
            f2 = f2 - a_diag_lu(2,4,n)*dq(4,n)
            f3 = f3 - a_diag_lu(3,4,n)*dq(4,n)

            dq(3,n) = f3 * a_diag_lu(3,3,n)
            f1 = f1 - a_diag_lu(1,3,n)*dq(3,n)
            f2 = f2 - a_diag_lu(2,3,n)*dq(3,n)

            dq(2,n) = f2 * a_diag_lu(2,2,n)
            f1 = f1 - a_diag_lu(1,2,n)*dq(2,n)

            dq(1,n) = f1 * a_diag_lu(1,1,n)

          end do rhs_solve

! Exchange dq across processors between colors

          select case(ipass)
          case(1)

            update_secondary : do n = start, periodic_end
              group = periodic_nature(n)
              if ( group >= 0 ) cycle update_secondary
              group = abs(group)
              primary_entry = periodic_data(group)%mlist(1)
              dq(1,n) = dq(1,primary_entry)
              dq(2,n) = dq(2,primary_entry)
              dq(3,n) = dq(3,primary_entry)
              dq(4,n) = dq(4,primary_entry)
              dq(5,n) = dq(5,primary_entry)
              dq(6,n) = dq(6,primary_entry)
              dq(7,n) = dq(7,primary_entry)
            end do update_secondary

            call lmpi_start_xfer(dq,sr_opt=sr(color))

          case(2)

            call lmpi_complete_xfer(dq,sr_opt=sr(color))

          end select

        end do pass_loop

      end do color_sweeps

    end do sweeping

  end subroutine ppoint_solve_7

end module point_solver

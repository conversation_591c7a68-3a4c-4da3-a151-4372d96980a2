


!=================================== VLFLXLS =================================80
!
! vanLeer flux limiter applied to a scalar
!
!=============================================================================80

  pure function vlflxls(grad_a, grad_b)

    use kinddefs,        only : dp
    use fun3d_constants, only : flim_llim

    real(dp), intent(in) :: grad_a, grad_b
    real(dp)             :: vlflxls

  continue

    vlflxls = (grad_a*abs(grad_b) + grad_b*abs(grad_a)) /                      &
                (abs(grad_b) + abs(grad_a) + flim_llim)

  end function vlflxls

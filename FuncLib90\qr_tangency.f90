!================================= QR_TANGENCY ===============================80
!
! Form tangency "right" state from "left" state (compressible).
!
!=============================================================================80

  pure function qr_tangency( xnorm, ynorm, znorm, face_speed, ql )

    real(dp), intent(in) :: xnorm, ynorm, znorm, face_speed

    real(dp), dimension(5), intent(in) :: ql

    real(dp), dimension(5)             :: qr_tangency

    real(dp) :: ubar

  continue

    ubar = xnorm*ql(2) + ynorm*ql(3) + znorm*ql(4) - face_speed

    qr_tangency(1) = ql(1)
    qr_tangency(2) = ql(2) - 2.0_dp*ubar*xnorm
    qr_tangency(3) = ql(3) - 2.0_dp*ubar*ynorm
    qr_tangency(4) = ql(4) - 2.0_dp*ubar*znorm
    qr_tangency(5) = ql(5)

  end function qr_tangency


pure function crow_entry( row, column, iau, ia, ja )
  integer :: crow_entry
  integer,               intent(in) :: row, column
  integer, dimension(:), intent(in) :: iau, ia, ja

  integer :: i

  continue

  crow_entry = 0

  if (row == column) then
    crow_entry = iau(row)
    return
  end if

  do i = ia(row), ia(row+1)-1
    if ( column == ja(i) ) then
      crow_entry = i
      return
    end if
  end do

end function crow_entry

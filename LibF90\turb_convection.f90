module turb_convection

  implicit none

  private

  public :: turb_resid_conv
  public :: turb_jacob_conv

contains

!================================ TURB_RESID_CONV ============================80
!
! Compressible perfect gas for arbitrary number of turbulence equations
!
!=============================================================================80

  subroutine turb_resid_conv( turbulence_model_int,                            &
                              nnodes0, nedgeloc, eptr, qturb,                  &
                              qnode, res, xn, yn, zn, ra, facespeed,           &
                              n_turb, r_start, nedgeloc_2d,                    &
                              x, y, z, gradx, grady, gradz )

    use kinddefs,            only : dp
    use info_depr,           only : twod
    use grid_motion_helpers, only : need_grid_velocity
    use turb_parameters,     only : ubar_eps, t_conv, turbulent_convection
    use turbulence_info,     only : asbm_sst

    integer,                              intent(in)    :: turbulence_model_int
    integer,                              intent(in)    :: nnodes0
    integer,                              intent(in)    :: nedgeloc
    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: nedgeloc_2d
    integer,                              intent(in)    :: r_start

    integer,  dimension(2, nedgeloc),     intent(in)    :: eptr
    real(dp), dimension(:,:),             intent(in)    :: qturb

    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),        intent(in)    :: ra
    real(dp), dimension(nedgeloc),        intent(in)    :: facespeed
    real(dp), dimension(:,:),             intent(in)    :: qnode
    real(dp), dimension(:),               intent(in)    :: x, y, z
    real(dp), dimension(:,:),             intent(in)    :: gradx, grady, gradz
    real(dp), dimension(:,:),             intent(inout) :: res

    real(dp) :: face_speed
    real(dp) :: xnorm, ynorm, znorm, area
    real(dp) :: ubar1, ubar2
    real(dp) :: uplus1, uminus1
    real(dp) :: uplus2, uminus2
    real(dp) :: xmean, ymean, zmean
    real(dp) :: turb1, turb2
    real(dp) :: x1, y1, z1
    real(dp) :: x2, y2, z2
    real(dp) :: u, v, w
    real(dp) :: flux1, flux2

    integer  :: nedge_flux_eval
    integer  :: node1
    integer  :: node2
    integer  :: n
    integer  :: r_loc, g_loc, g_start
    integer  :: i_turb
    integer  :: n_turb_conv

    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: half = 0.5_dp

    continue

    g_start = 1
    if ( turbulent_convection /= 0 ) then

      g_start = size( gradx, 1 ) - n_turb + 1

    endif

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    endif

!   uncoupled:  r_start = 0
!   i_turb   :  location in the turbulent array

    edge_loop:  do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      x1 = x(node1)
      y1 = y(node1)
      z1 = z(node1)

      x2 = x(node2)
      y2 = y(node2)
      z2 = z(node2)

      xmean = 0.5_dp*( x1 + x2 )
      ymean = 0.5_dp*( y1 + y2 )
      zmean = 0.5_dp*( z1 + z2 )

! Unit normal to dual face and area

      xnorm = xn(n)
      ynorm = yn(n)
      znorm = zn(n)
      area   = ra(n)

! Dual face speed

      face_speed = zero
      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

      if ( abs( t_conv ) < epsilon(1._dp) ) return

! First node

      u      = qnode(2,node1)
      v      = qnode(3,node1)
      w      = qnode(4,node1)
      ubar1   = xnorm*u + ynorm*v + znorm*w - face_speed

      uplus1  = half * ( ubar1 + aharten(ubar1,ubar_eps) )
      uminus1 = half * ( ubar1 - aharten(ubar1,ubar_eps) )

!     Second node

      u      = qnode(2,node2)
      v      = qnode(3,node2)
      w      = qnode(4,node2)
      ubar2 = -(xnorm*u + ynorm*v + znorm*w - face_speed)

      uplus2  = half * ( ubar2 + aharten(ubar2,ubar_eps) )
      uminus2 = half * ( ubar2 - aharten(ubar2,ubar_eps) )

!FIXME
!     The t_conv parameter should be changed to an integer array of length
!     n_turb, such that each element of t_conv determines whether computation
!     of convective terms is required for each turbulent scalar.
      if (turbulence_model_int == asbm_sst) then
        n_turb_conv = n_turb - 1 ! last asbm-sst eq. has no convection
      else
        n_turb_conv = n_turb
      endif

      turb_loop: do i_turb = 1, n_turb_conv

        g_loc = g_start + i_turb - 1 ! location in gradient array
        r_loc = r_start + i_turb - 1 ! location in residual array

        turb_order : if ( turbulent_convection == 0 ) then
          turb1 = qturb(i_turb,node1)
          turb2 = qturb(i_turb,node2)
        else
          turb1 = qturb(i_turb,node1) + gradx(g_loc,node1)*( xmean-x1 ) &
                                      + grady(g_loc,node1)*( ymean-y1 ) &
                                      + gradz(g_loc,node1)*( zmean-z1 )
          turb2 = qturb(i_turb,node2) + gradx(g_loc,node2)*( xmean-x2 ) &
                                      + grady(g_loc,node2)*( ymean-y2 ) &
                                      + gradz(g_loc,node2)*( zmean-z2 )
        endif turb_order

        flux1    = (  uplus1 * turb1 + uminus1 * turb2 )*area*t_conv
        if ( node1 <= nnodes0 )  res(r_loc,node1) = res(r_loc,node1) + flux1

        flux2    = (  uplus2 * turb2 + uminus2 * turb1 )*area*t_conv
        if ( node2 <= nnodes0 )  res(r_loc,node2) = res(r_loc,node2) + flux2

      end do turb_loop

    end do edge_loop

  end subroutine turb_resid_conv

!================================ TURB_JACOB_CONV ============================80
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
! not tightly coupled
! should match up what is used in sa-neg (Apr.2,2013: jrcarlson)
!
!=============================================================================80
  subroutine turb_jacob_conv( nnodes0,                                         &
                              nedgeloc, eptr, qnode,                           &
                              xn, yn, zn, ra, a_diag,                          &
                              a_off, fhelp, facespeed, n_turb,                 &
                              g2m, nedgeloc_2d )

    use kinddefs,            only : dp, odp
    use info_depr,           only : twod
    use grid_motion_helpers, only : need_grid_velocity
    use turb_parameters,     only : ubar_eps, t_conv
    use turbulence_info,     only : turbulence_model_int, asbm_sst

    integer, intent(in) :: n_turb
    integer, intent(in) :: nedgeloc, nedgeloc_2d
    integer, intent(in) :: nnodes0

    integer, dimension(2,nedgeloc),          intent(in) :: eptr
    integer, dimension(2,nedgeloc),          intent(in) :: fhelp
    integer, dimension(:),                   intent(in) :: g2m

    real(dp),  dimension(nedgeloc),          intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),          intent(in)    :: facespeed
    real(dp),  dimension(:,:),               intent(in)    :: qnode
    real(dp),  dimension(:,:,:),             intent(inout) :: a_diag
    real(odp), dimension(:,:,:),             intent(inout) :: a_off

    integer :: nedge_jac_eval
    integer :: n, iturb
    integer :: row
    integer :: ioff
    integer :: node1, node2
    integer :: n_turb_conv

    real(dp) :: xnorm, ynorm, znorm, area
    real(dp) :: face_speed
    real(dp) :: u, v, w, ubar1, ubar2, uminus1, uminus2, uplus1, uplus2
    real(dp) :: diag1, off1
    real(dp) :: diag2, off2

    real(dp), parameter :: half = 0.5_dp

    continue

    if ( abs( t_conv ) < epsilon(1._dp) ) return

    nedge_jac_eval = nedgeloc
    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    endif

    edge_loop:  do n = 1, nedge_jac_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Unit normal to dual face and area

      xnorm = xn(n)
      ynorm = yn(n)
      znorm = zn(n)
      area  = ra(n)

! Dual face speed

      face_speed = 0._dp

      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

! First node

      u      = qnode(2,node1)
      v      = qnode(3,node1)
      w      = qnode(4,node1)
      ubar1   = xnorm*u + ynorm*v + znorm*w - face_speed

      uplus1  = half * ( ubar1 + aharten(ubar1,ubar_eps) )
      uminus1 = half * ( ubar1 - aharten(ubar1,ubar_eps) )

!     Now do the other node (note that ubar pts in other direction)

      u      = qnode(2,node2)
      v      = qnode(3,node2)
      w      = qnode(4,node2)
      ubar2 = -(xnorm*u + ynorm*v + znorm*w - face_speed)

      uplus2  = half * ( ubar2 + aharten(ubar2,ubar_eps) )
      uminus2 = half * ( ubar2 - aharten(ubar2,ubar_eps) )

!FIXME
!     The t_conv parameter should be changed to an integer array of length
!     n_turb, such that each element of t_conv determines whether computation
!     of convective terms is required for each turbulent scalar.
      if (turbulence_model_int == asbm_sst) then
        n_turb_conv = n_turb - 1 ! last asbm-sst eq. has no convection
      else
        n_turb_conv = n_turb
      endif

      turb_loop:  do iturb = 1, n_turb_conv
!       flux1    = (  uplus1 * turb1 + uminus1 * turb2 )*area*t_conv
        diag1 = ( uplus1  )*area*t_conv
        off1  = ( uminus1 )*area*t_conv

        if (node1 <= nnodes0) then
          row = g2m(node1)
          a_diag(iturb,iturb,row) = a_diag(iturb,iturb,row) + diag1
          ioff        = fhelp(1,n)
          a_off(iturb,iturb,ioff) = a_off(iturb,iturb,ioff) + real(off1,odp)
        end if

!       flux2    = (  uplus2 * turb2 + uminus2 * turb1 )*area*t_conv
        diag2    = ( uplus2  )*area*t_conv
        off2     = ( uminus2 )*area*t_conv
        if (node2 <= nnodes0) then
          row = g2m(node2)
          a_diag(iturb,iturb,row) = a_diag(iturb,iturb,row) + diag2
          ioff        = fhelp(2,n)
          a_off(iturb,iturb,ioff) = a_off(iturb,iturb,ioff) + real(off2,odp)
        end if

      end do turb_loop

    end do edge_loop

  end subroutine turb_jacob_conv

  include 'aharten.f90'

end module turb_convection

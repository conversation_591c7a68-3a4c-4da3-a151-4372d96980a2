\section{Troubleshooting}
\label{s:help}

The goal of the \FunThreeD developers is to produce
as robust a solver as possible.
However, there are times
when the code fails to produce a converged solution.
For example, taking the square root of a negative quantity
(due to a negative density or pressure) results in a NaN.
We hope that these suggestions are helpful.
If none of the suggestions listed here remedy your problem,
please contact \funsupport.

\subsection{What if the solver has trouble starting or reports NaNs?}

Check that the freestream, reference, and boundary conditions
are specified correctly.
Visualize the solution, especially near the location of the maximum residual.
If the problem is widespread,
run the simulation again and visualize the solution a few iterations before
the problem happens.
Look for extremely large Mach numbers, low pressures, low densities,
or reversed flow at boundaries.

Examining the residual history may help to isolate the problem 
to the mean flow or turbulence model.
Lowering the CFL numbers of the meanflow and turbulence equations
can aid linear and nonlinear convergence,
see \sectionref{s:nml_nonlinear_solver_parameters}.
If the linear system is diverging
(the linear system can be examined with
the \cmd{--monitor_linear} command line option),
utilize a more dissipative flux linearization \var{flux_construction_lhs}
or the Krylov projection method
\cmd{linear_projection=.true.}, 
see \sectionref{s:nml_linear_solver_parameters}.

Try flow field initialization in the problematic region of the domain
(e.g., engine plenum), see \sectionref{s:nml_flow_initialization}. 
Start with some \cmd{first_order_iterations}
(\sectionref{s:nml_inviscid_flux_method}) or
try continuation from less challenging freestream condition
(e.g., lower angle of attack, subsonic Mach number).
The time accurate flow solver may be able to survive
initial transients better than the steady solver.

\subsection{What if the forces and moments aren't steady
            or residuals don't converge to steady-state?}

Try lowering the CFL numbers of the mean flow and turbulence model,
see \sectionref{s:nml_nonlinear_solver_parameters}.
Examining the residual history may help to isolate the problem 
to the mean flow or turbulence model.
The problem may be unsteady; 
try restarting the solution with a time-accurate simulation.

\subsection{What if the solver dies unexpectedly?}
You may need to set your shell limits to unlimited,
\begin{Verbatim}
  $ ulimit unlimited # for bash
  $ unlimit          # for c shell
\end{Verbatim}
If your operating system reports a Signal 9 or 11
and you have already tried removing shell restrictions,
you have likely hit the memory limit of your machine.
Try reducing the number of mesh points, running on more
nodes, or installing more memory on your machine.

\subsection{What if a segmentation fault occurs after ``Calling ParMetis''?}

Make sure that the the width (32 or 64 bits) of integers in \FunThreeD and
ParMETIS are consistent.
More recent implementations of MPI (e.g., OpenMPI)
internally manage the handles (i.e., communicators) differently
from previous versions of MPI.
However, ParMETIS 3.* uses an older paradigm, 
which has been updated in ParMETIS 4.*. 
Upgrade to ParMETIS 4.*. 

\subsection{What if the solver dies with an error like ``input statement requires too much data'' after echoing the wrong number of elements or nodes?}
The endianness (\sectionref{s:endianness}) 
of the grid files (\sectionref{s:grids})
may be different than \FunThreeD expects.

\subsection{What if the solver dies with an error like ``input statement requires too much data'' after echoing the number of tetrahedra and nodes for a \VGRID mesh?}
Single-segmented \VGRID grids over 20 million nodes exceed the allowable record length.
Use \code{postgrid} to save grid as a multi-segmented format
(option \var{O5} in batch mode).

\subsection{What if the solver reports that the Euler numbers differ?}\label{s:eulerdiff}

The Euler number is a global indicator of 
consistent node, element, and face connectivity.  
There is some limited evidence that suggests there may be times when it reports 
a problem that may not be an issue for the solver, 
but  the failure of the Euler number check
indicates a problem with the grid in a majority of cases.
Instructions to determine if the Euler number will impact your solution
follow this description of the Euler number.

\subsubsection{Euler Number Description}

 A valid grid is composed of four elemental volume types
 (tetrahedra, pyramids, prisms, and hexahedra)
 face-connected either to each other or one of two boundary face types
 (triangles or quadrilaterals).
 Each boundary edge connects to precisely two boundary faces.
 Two neighboring boundary faces share exactly one boundary edge.
 For each boundary face connecting to a boundary node, every
 other boundary face connecting to the same boundary node can be
 found by a path through a connected-edge/connected-face
 traverse starting from that boundary face.

 The above restrictions are meant to exclude certain topologies
 such as two spherical boundaries coming together at a point
 or two rectangular boundaries connecting along an edge.
 These restrictions are not checked explicitly
 but will cause the Euler number check described
 below to fail.

The Euler Number computed from boundary data ($EN_b$) is
\begin{equation}
    EN_b = N_b - E_b + F_b  
\end{equation}
where
\begin{equation}
 N_b \equiv \text{boundary nodes    (counted)}
\end{equation}
\begin{equation}
 E_b \equiv \text{boundary edges    (inferred from} N_{tri} \text{ and } N_{quad})
\end{equation}
\begin{equation}
 F_b \equiv \text{boundary faces    (inferred from } N_{tri} \text{ and } N_{quad})
\end{equation}
The Euler number is a characteristic number for the topology
of the boundary or boundaries. $N_{tri}$  and  $N_{quad}$ are
the number of triangular and boundary faces, respectively.

The Euler Number computed from volume data ($EN_v$) is
\begin{equation}
    EN_v = 2 ( N - E + F -C )  
\end{equation}
where
\begin{equation}
 N \equiv \text{volume nodes     (counted)}
\end{equation}
\begin{equation}
 E \equiv \text{volume edges     (counted)}
\end{equation}
\begin{equation}
 F \equiv \text{volume faces     (inferred from }C \text{ and } F_b)
\end{equation}
\begin{equation}
 C \equiv \text{volume cells     (counted)}
\end{equation}
The formula that is checked is
\begin{equation}
    EN_v - EN_b = 0
\end{equation}

 Barth\cite{barth-vki-1994-unstruct-finite-volume}
 derived this formula for tetrahedra
 and noted the formula does not hold in certain cases,
 such as two simplices that share only one common edge
 or two simplices that share only one common node.
 Barth notes that the above formulas are specific forms
 of the general Dehn-Sommerville formula, reported
 in Wikipedia to hold for simplicial polytopes and simple
 polytopes.  The pyramid is not a simple polytope.
 It can be proved by induction\cite{diskin-thomas-euler-number}
 that the formula holds for every
 valid grid as defined above.

 Try this checklist to diagnose the problem:
\begin{enumerate}
\item Check the $EN_b$ with your expectations for this case:
\begin{description}
      \item[2] for a spherical topology, simple 3D wing with symmetry, \ldots
      \item[0] for a torus, donut, \ldots
      \item[-2] for a double torus, \ldots
      \item[-4] for a triple torus, pretzel, \ldots
      \item[4] for a sphere within a sphere, \ldots
      \item[6] for two spheres within a sphere, \ldots
\end{description}

\item Ensure the number of boundary nodes $N_b$ and faces $F_b$
    reported match expected values.

\item Ensure the number of nodes $N$ and cells $C$ reported
    match expected values.

\item The difference between $EN_b$ and $EN_v$ points to inconsistencies
    in edge counts, i.e., $\delta(E) = 2(EN_b-EN_v) \neq 0 $. 
    The inequality $EN_b > EN_v$
    implies you have more edges than expected. When this occurs,
    the reported face counts will differ from an actual count.
    An error of this type would arise when there are adjacent
    faces that are inconsistent, such as a quadrilateral face
    shared between two elements that is cut into
    two triangular faces by different edges.
\end{enumerate}

\subsubsection{Determining the Impact of an Euler Number Mismatch}

A freestream residual problem localization technique is described.
However, the best practice is to not proceed
without repairing the grid to ensure $EN_b = EN_v$.
The \cmd{ignore_euler_number} namelist variable does just what the name implies,
and allows the solver to proceed. 
The \cmd{--test_freestream} option can be used as a secondary check on the mesh.
On a valid mesh, 
the solver should preserve the freestream for
an arbitrary number of iterations. 
You should run 20--50 iterations, 
which may require a lowering of the \cmd{stopping_tolerance} to 1e-20 
so the solver does not automatically stop. 
All residuals should hover around machine zero, 
and not slowly increase 
(there will be iteration-to-iteration variation in the exact number, however).

If freestream is maintained by this test, 
you could proceed with your intended computation using only
\cmd{ignore_euler_number}, but this is not recommended.
If freestream is not maintained, 
this test confirms that there is a problem with the mesh and 
the location of the max residual may give a clue as to where 
in the mesh to start looking for the problem.

!================================= FLUX_VISC =================================80
!
! Full viscous fluxes for the interior faces of cell-centered control volumes.
!
! Incoming primitive variables (rho, u, v, w, p).
!
!=============================================================================80
  pure function flux_visc(xnorm, ynorm, znorm, area, ql, qr,                   &
                          gradxl, gradyl, gradzl, gradxr, gradyr, gradzr,      &
                          amutl, amutr, dx, dy, dz)

    use flux_constants,        only : xmr, cgp, cgpt, c43, c23,                &
                                      gamma, cstar

    real(dp),               intent(in) :: xnorm, ynorm, znorm, area
    real(dp),               intent(in) :: amutl, amutr, dx, dy, dz

    real(dp), dimension(5), intent(in) :: ql, qr, gradxl, gradyl, gradzl
    real(dp), dimension(5), intent(in) :: gradxr, gradyr, gradzr

    real(dp), dimension(5) :: flux_visc

    real(dp) :: tl, tr, mul, mur, mucgp, mua, umu, vmu, wmu
    real(dp) :: tx, ty, tz, tdot, ux, vx, wx, uy, vy, wy, uz, vz, wz
    real(dp) :: ax, ay, az, e2a, e2b, e2c, e3a, e3b, e3c
    real(dp) :: e4a, e4b, e4c, e5a, e5b, e5c, ds2i, termx, termy, termz
    real(dp) :: rli, rri, cl1, cl5, cr1, cr5, de

    real(dp), dimension(4):: lgradx, lgrady, lgradz, dots

  continue

!   cstar = sutherland_constant/tref
!   xmr   = xmach/re
!   c43   = xmr*4._dp/3._dp
!   c23   = xmr*2._dp/3._dp
!   cgp   = xmr/(gm1*prandtl)
!   cgpt  = xmr/(gm1*turbulent_prandtl)

    rli = 1._dp/ql(1)
    rri = 1._dp/qr(1)

    tl = gamma*ql(5)*rli
    tr = gamma*qr(5)*rri

    mul = viscosity_law( cstar, tl )
    mur = viscosity_law( cstar, tr )

    mucgp = 0.5_dp*( cgp*(mul+mur) + cgpt*(amutl+amutr) )

    mul = mul + amutl
    mur = mur + amutr

    mua = 0.5_dp*(mul+mur)*area
    umu = 0.5_dp*(ql(2)*mul + qr(2)*mur)
    vmu = 0.5_dp*(ql(3)*mul + qr(3)*mur)
    wmu = 0.5_dp*(ql(4)*mul + qr(4)*mur)

    ds2i = 1._dp/( dx*dx + dy*dy + dz*dz )

    termx = dx*ds2i
    termy = dy*ds2i
    termz = dz*ds2i

    lgradx(2:4) = 0.5_dp*(gradxl(2:4) + gradxr(2:4))
    lgrady(2:4) = 0.5_dp*(gradyl(2:4) + gradyr(2:4))
    lgradz(2:4) = 0.5_dp*(gradzl(2:4) + gradzr(2:4))

    cl5 = 0.5_dp*gamma*rli
    cr5 = 0.5_dp*gamma*rri
    cl1 = cl5*ql(5)*rli
    cr1 = cr5*qr(5)*rri
    tx = cl5*gradxl(5) - cl1*gradxl(1) + cr5*gradxr(5) - cr1*gradxr(1)
    ty = cl5*gradyl(5) - cl1*gradyl(1) + cr5*gradyr(5) - cr1*gradyr(1)
    tz = cl5*gradzl(5) - cl1*gradzl(1) + cr5*gradzr(5) - cr1*gradzr(1)

    dots(2:4) = lgradx(2:4)*dx + lgrady(2:4)*dy + lgradz(2:4)*dz

    tdot = tx*dx + ty*dy + tz*dz

    de = qr(2) - ql(2) - dots(2)
    ux = lgradx(2) + termx*de
    uy = lgrady(2) + termy*de
    uz = lgradz(2) + termz*de

    de = qr(3) - ql(3) - dots(3)
    vx = lgradx(3)+ termx*de
    vy = lgrady(3)+ termy*de
    vz = lgradz(3)+ termz*de

    de = qr(4) - ql(4) - dots(4)
    wx = lgradx(4)+ termx*de
    wy = lgrady(4)+ termy*de
    wz = lgradz(4)+ termz*de

    de = tr - tl - tdot
    ax = tx + termx*de
    ay = ty + termy*de
    az = tz + termz*de

    e2a = c43*ux - c23*vy - c23*wz
    e2b = xmr*(uy + vx)
    e2c = xmr*(uz + wx)

    e3a = e2b
    e3b = c43*vy - c23*ux - c23*wz
    e3c = xmr*(vz + wy)

    e4a = e2c
    e4b = e3c
    e4c = c43*wz - c23*ux - c23*vy

    e5a = umu*e2a + vmu*e2b + wmu*e2c + mucgp*ax
    e5b = umu*e3a + vmu*e3b + wmu*e3c + mucgp*ay
    e5c = umu*e4a + vmu*e4b + wmu*e4c + mucgp*az

    flux_visc(1) =  0._dp
    flux_visc(2) = -mua*(xnorm*e2a + ynorm*e2b + znorm*e2c)
    flux_visc(3) = -mua*(xnorm*e3a + ynorm*e3b + znorm*e3c)
    flux_visc(4) = -mua*(xnorm*e4a + ynorm*e4b + znorm*e4c)
    flux_visc(5) = -(xnorm*e5a + ynorm*e5b + znorm*e5c)*area

  end function flux_visc

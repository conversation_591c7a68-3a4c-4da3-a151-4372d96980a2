!================================= MAPPING_COORDS_CYLINDRICAL ================80
!
! Cylindrical mapping coordinates (xie,eta,zie).
!
!=============================================================================80

  pure function coords_cylindrical_polar( xc, yc, zc, xr, yr, zr )

    real(dp), intent(in) :: xc, yc, zc, xr, yr, zr

    real(dp) :: xie, eta, zie, rc, rr, tc, tr

    real(dp), dimension(3) :: coords_cylindrical_polar

  continue

     rc = sqrt( xc**2 + zc**2 )
     rr = sqrt( xr**2 + zr**2 )

     tc = atan2( real(zc,dp) , real(xc,dp) )
     tr = atan2( real(zr,dp) , real(xr,dp) )

     xie =     rc-rr
     eta = rr*(tc-tr)
     zie =     yc-yr

    coords_cylindrical_polar(1) = xie
    coords_cylindrical_polar(2) = eta
    coords_cylindrical_polar(3) = zie

  end function coords_cylindrical_polar

!=================================== TINV_3D =================================80
!
! Compute inverse of 3x3 transformation matrix ( least square face terms )
!
!=============================================================================80

  pure function tinv_3d( ex, ey, ez, lx, ly, lz, mx, my, mz, deti )

    real(dp), intent(in)  :: ex, ey, ez, lx, ly, lz, mx, my, mz, deti

    real(dp), dimension(3,3) :: tinv_3d

    continue

    !...find inverse elements of transformation matrix
    !deti =  1.0_dp / ( ex*( ly*mz - my*lz )             &
    !                 + lx*( my*ez - ey*mz )             &
    !                 + mx*( ey*lz - ly*ez ) )

    tinv_3d(1,1) =  deti*( ly*mz - my*lz )
    tinv_3d(1,2) =  deti*( my*ez - ey*mz )
    tinv_3d(1,3) =  deti*( ey*lz - ly*ez )

    tinv_3d(2,1) = -deti*( lx*mz - mx*lz )
    tinv_3d(2,2) = -deti*( mx*ez - ex*mz )
    tinv_3d(2,3) = -deti*( ex*lz - lx*ez )

    tinv_3d(3,1) =  deti*( lx*my - mx*ly )
    tinv_3d(3,2) =  deti*( mx*ey - ex*my )
    tinv_3d(3,3) =  deti*( ex*ly - lx*ey )

    !To invoke thin-layer, or edge-based only, or
    !thin-shear-layer approximation for evidence that
    !it is an inconsistent scheme, uncomment next 4 lines.
    !tinv_3d(:,:) = 0._dp
    !tinv_3d(1,1) =  ex
    !tinv_3d(2,1) =  ey
    !tinv_3d(3,1) =  ez

  end function tinv_3d

!   vim: set filetype=fortran:
! emacs: -*- f90 -*-
!-----------------------------------------------------------------------------80

test_suite porous_model

  integer, parameter  :: dp = selected_real_kind(P=15)

  real(dp),  dimension(3,3) :: gradv
  real(dp),  dimension(3,3) :: s
  real(dp),  dimension(3,3) :: w

  real(dp), parameter       :: tol = 1.0e-8

  real(dp), parameter       :: gamma               =  1.4_dp
  real(dp), parameter       :: turbulent_prandtl   =  0.72_dp
  real(dp), parameter       :: sutherland_constant =  198.6_dp
  real(dp), parameter       :: tref                =  540.0_dp
  real(dp), parameter       :: kappa               =  0.41_dp
  real(dp), parameter       :: b                   =  5.0_dp
  real(dp), parameter       :: cmu_0               =  0.09_dp

  real(dp), parameter, dimension(3,3) :: delta =   &
            reshape((/1.,0.,0.,0.,1.,0.,0.,0.,1./),(/3,3/))

!=============================================================================80
!=============================================================================80
!=============================================================================80
test plot_states

  use fluid, only : setup_fluid_gamma, gamma, gm1, gp1
  use ddt,   only : ddt5, ddt5_identity, assignment(=), operator(+) &
                  , operator(-), operator(*), operator(/)           &
                  , operator(**), ddt_sqrt

  real(dp) :: xnorm, ynorm, znorm, area
  real(dp) :: s

  real(dp), dimension(3)   :: normal
 
  real(dp),   dimension(5) :: q
  type(ddt5), dimension(5) :: q_ddt
  type(ddt5), dimension(5) :: bc_state
  real(dp)                 :: factor, factor_p, factor_s
  real(dp)                 :: unorm
  real(dp)                 :: p0, p_domain, p_plenum, rho_unorm_1
  real(dp)                 :: rho_b, u_b, v_b, w_b, p_b

  real(dp), parameter :: one = 1.0_dp
  integer :: i, j, k, imax, jmax, kmax

  call setup_fluid_gamma

  p0    = 1.0_dp/gamma
  p_plenum   = p0
  s     = 0.99_dp

  xnorm =  0.0_dp
  ynorm =  0.0_dp
  znorm = -1.0_dp
  area  =  1.0_dp
  normal = (/xnorm,ynorm,znorm/)



write(9,'(a)') 'variables="rho_unorm_1", "p_plenum","p_domain","rho_b","p_b","unorm","rho_unorm_1/rho_b","s"'

  imax = 25
  jmax = 25
  kmax = 10

!write(9,"('zone t=',a,', i=',i0,', j=',i0,', datapacking=point')") 'map', jmax+1, kmax+1
!write(9,"('zone t=',a,', i=',i0,', j=',i0,',k=',i0,', datapacking=point')") 'map', imax+1,jmax+1, kmax+1
!write(9,"('zone t=',a,', i=',i0,', j=',i0,',k=',i0)") 'map', imax+1,jmax+1, kmax+1
  do k = 0, kmax
    factor_s = 0.30*(-0.5+float(k)/float(kmax))
    s     = 0.80 + factor_s
 write(9,'(a,f5.2,a,i0,a,i0)') 'zone,t="s=',s,'",i=',imax+1,', j=',jmax+1
!write(9,"('zone t=\"s=',f5.2,'\", i=',i0,', j=',i0)") s, imax+1, jmax+1
  do j = 0, jmax
  do i = 0, imax

    factor   = -0.5+float(j)/float(jmax)
    factor_p = 0.25*(-0.5+float(i)/float(imax))
    q(1)     =  2.0_dp
    q(2)     = -0.0_dp
    q(3)     = -0.0_dp
    q(4)     = -0.1_dp *factor
    p_domain = p0 + factor_p
    q(5)     = p_domain/gm1 + 0.5_dp*q(1)*(q(2)*q(2)+q(3)*q(3)+q(4)*q(4))
    q_ddt    = ddt5_identity(q)
    unorm    = q(2)*xnorm + q(3)*ynorm + q(4)*znorm
    rho_unorm_1 = q(1)*unorm

!   if ( p_plenum < p ) then
    if ( unorm > 0.0_dp ) then
    bc_state = porous_wall_inflow_state( q, xnorm, ynorm, znorm, s, p_plenum )

    else

    bc_state = porous_wall_outflow_state( q, xnorm, ynorm, znorm, s, p_plenum)

    end if
    rho_b = bc_state(1)%f
    u_b   = bc_state(2)%f
    v_b   = bc_state(3)%f
    w_b   = bc_state(4)%f
    p_b   = bc_state(5)%f
!  write(6,'(3i5,10(1x,f15.10))') i,j,k, &
!  rho_unorm_1, p_3-p,rho_b,p_b,unorm,rho_unorm_1/rho_b,s
   write(9,'(10(1x,f15.10))') &
   rho_unorm_1, p_plenum,p_domain,rho_b,p_b,unorm,rho_unorm_1/rho_b,s
  end do
  end do
  end do

stop
end test


!=============================================================================80
!=============================================================================80
!=============================================================================80
test test_states

  use fluid, only : setup_fluid_gamma, gamma, gm1, gp1
  use ddt,   only : ddt5, ddt5_identity, assignment(=), operator(+) &
                  , operator(-), operator(*), operator(/)           &
                  , operator(**), ddt_sqrt

  real(dp) :: p_3
  real(dp) :: xnorm, ynorm, znorm, area
  real(dp) :: s

  real(dp), dimension(3)   :: normal
 
  real(dp),   dimension(5) :: q
  type(ddt5), dimension(5) :: q_ddt
  type(ddt5), dimension(5) :: bc_state
  real(dp)                 :: p
  real(dp)                 :: unorm
! type(ddt5)               :: rho, u, v, w, p, e, unorm

  real(dp), parameter :: one = 1.0_dp

  call setup_fluid_gamma

  p_3   = 1.0_dp/gamma
  s     = 0.5_dp

  xnorm =  0.0_dp
  ynorm =  0.0_dp
  znorm = -1.0_dp
  area  =  1.0_dp
  normal = (/xnorm,ynorm,znorm/)

  q(1)  =  1.0_dp
  q(2)  = -0.0_dp
  q(3)  = -0.0_dp
  q(4)  = -0.1_dp
  p     = one/gamma
  q(5)  = p/gm1 + 0.5_dp*q(1)*(q(2)*q(2)+q(3)*q(3)+q(4)*q(4))
  q_ddt = ddt5_identity(q)
  unorm = -(q(2)*xnorm + q(3)*ynorm + q(4)*znorm)

    if ( unorm > 0.0_dp ) then
    bc_state = porous_wall_inflow_state( q, xnorm, ynorm, znorm, s, p_3 )

    write(*,*) 'inflow_state:.....'
    write(*,'(a,10(1x,f15.5))') 'unorm:............',unorm
    write(*,'(a,10(1x,f15.5))') 'bc_state(1):......',bc_state(1)
    write(*,'(a,10(1x,f15.5))') 'bc_state(2):......',bc_state(2)
    write(*,'(a,10(1x,f15.5))') 'bc_state(3):......',bc_state(3)
    write(*,'(a,10(1x,f15.5))') 'bc_state(4):......',bc_state(4)
    write(*,'(a,10(1x,f15.5))') 'bc_state(5):......',bc_state(5)

    else

    bc_state = porous_wall_outflow_state( q, xnorm, ynorm, znorm, s, p_3)

    write(*,*) 'outflow_state:....'
    write(*,'(a,10(1x,f15.5))') 'unorm:............',unorm
    write(*,'(a,10(1x,f15.5))') 'bc_state(1):......',bc_state(1)
    write(*,'(a,10(1x,f15.5))') 'bc_state(2):......',bc_state(2)
    write(*,'(a,10(1x,f15.5))') 'bc_state(3):......',bc_state(3)
    write(*,'(a,10(1x,f15.5))') 'bc_state(4):......',bc_state(4)
    write(*,'(a,10(1x,f15.5))') 'bc_state(5):......',bc_state(5)

    endif
 write(*,*)
   write(6,'(a,f15.10,a)') &
   'assert_equal_within(',bc_state(1)%f,'_dp,bc_state(1)%f, tol)'
   write(6,'(a,f15.10,a)') &
   'assert_equal_within(',bc_state(2)%f,'_dp,bc_state(2)%f, tol)'
   write(6,'(a,f15.10,a)') &
   'assert_equal_within(',bc_state(3)%f,'_dp,bc_state(3)%f, tol)'
   write(6,'(a,f15.10,a)') &
   'assert_equal_within(',bc_state(4)%f,'_dp,bc_state(4)%f, tol)'
   write(6,'(a,f15.10,a)') &
   'assert_equal_within(',bc_state(5)%f,'_dp,bc_state(5)%f, tol)'

assert_equal_within(   0.9675784173_dp,bc_state(1)%f, tol)
assert_equal_within(   0.0000000000_dp,bc_state(2)%f, tol)
assert_equal_within(   0.0000000000_dp,bc_state(3)%f, tol)
assert_equal_within(  -0.1033507964_dp,bc_state(4)%f, tol)
assert_equal_within(   0.6910332559_dp,bc_state(5)%f, tol)
 
!=============================================================================80
!=============================================================================80


  q(1)  =  1.0_dp
  q(2)  = -0.0_dp
  q(3)  = -0.0_dp
  q(4)  =  0.1_dp
  p     = one/gamma
  q(5)  = p/gm1 + 0.5_dp*q(1)*(q(2)*q(2)+q(3)*q(3)+q(4)*q(4))
  q_ddt = ddt5_identity(q)
  unorm = -(q(2)*xnorm + q(3)*ynorm + q(4)*znorm)

    if ( unorm > 0.0_dp ) then
    bc_state = porous_wall_inflow_state( q, xnorm, ynorm, znorm, s, p_3 )

    write(*,*) 'inflow_state:.....'
    write(*,'(a,10(1x,f15.5))') 'unorm:............',unorm
    write(*,'(a,10(1x,f15.5))') 'bc_state(1):......',bc_state(1)
    write(*,'(a,10(1x,f15.5))') 'bc_state(2):......',bc_state(2)
    write(*,'(a,10(1x,f15.5))') 'bc_state(3):......',bc_state(3)
    write(*,'(a,10(1x,f15.5))') 'bc_state(4):......',bc_state(4)
    write(*,'(a,10(1x,f15.5))') 'bc_state(5):......',bc_state(5)

    else

    bc_state = porous_wall_outflow_state( q, xnorm, ynorm, znorm, s, p_3)

    write(*,*) 'outflow_state:....'
    write(*,'(a,10(1x,f15.5))') 'unorm:............',unorm
    write(*,'(a,10(1x,f15.5))') 'bc_state(1):......',bc_state(1)
    write(*,'(a,10(1x,f15.5))') 'bc_state(2):......',bc_state(2)
    write(*,'(a,10(1x,f15.5))') 'bc_state(3):......',bc_state(3)
    write(*,'(a,10(1x,f15.5))') 'bc_state(4):......',bc_state(4)
    write(*,'(a,10(1x,f15.5))') 'bc_state(5):......',bc_state(5)

    endif

!=============================================================================80

!write(*,*)
!  write(6,'(a,f15.10,a)') &
!  'assert_equal_within(',bc_state(1)%f,'_dp,bc_state(1)%f, tol)'
!  write(6,'(a,f15.10,a)') &
!  'assert_equal_within(',bc_state(2)%f,'_dp,bc_state(2)%f, tol)'
!  write(6,'(a,f15.10,a)') &
!  'assert_equal_within(',bc_state(3)%f,'_dp,bc_state(3)%f, tol)'
!  write(6,'(a,f15.10,a)') &
!  'assert_equal_within(',bc_state(4)%f,'_dp,bc_state(4)%f, tol)'
!  write(6,'(a,f15.10,a)') &
!  'assert_equal_within(',bc_state(5)%f,'_dp,bc_state(5)%f, tol)'
!
!  assert_equal_within(   0.9000000000_dp,bc_state(1)%f, tol)
!  assert_equal_within(   0.6548147375_dp,bc_state(2)%f, tol)
!  assert_equal_within(   0.6548147375_dp,bc_state(3)%f, tol)
!  assert_equal_within(   0.6548147375_dp,bc_state(4)%f, tol)
!  assert_equal_within(   1.0680000000_dp,bc_state(5)%f, tol)
    write(*,*) 'Fin test states'

end test

!=============================================================================80
!=============================================================================80
!=============================================================================80
test test_jacobian_outflow

  use fluid, only : setup_fluid_gamma, gamma, gm1, gp1
  use ddt,   only : ddt5, ddt5_identity, assignment(=), operator(+) &
                  , operator(-), operator(*), operator(/)           &
                  , operator(**), ddt_sqrt

  real(dp) :: p_1
  real(dp) :: xnorm, ynorm, znorm, area
  real(dp) :: s

  real(dp), dimension(3)   :: normal
 
  real(dp),   dimension(5) :: q
  type(ddt5), dimension(5) :: flux_ddt
  real(dp)                 :: rho, u, v, w, p, u_sq, unorm
  type(ddt5), dimension(5) :: bc_state
  type(ddt5) :: rho_ddt, u_ddt, v_ddt, w_ddt, p_ddt, e_ddt, unorm_ddt

  real(dp), parameter :: one = 1.0_dp

  call setup_fluid_gamma

  s     = 0.5_dp

  xnorm =  0.0_dp
  ynorm =  0.0_dp
  znorm = -1.0_dp
  area  =  1.0_dp ! dqrt(xnorm*xnorm+ynorm*ynorm+znorm*znorm)
  normal = (/xnorm,ynorm,znorm/)

  q(1)  = 1.0_dp
  q(2)  = 0.0_dp
  q(3)  = 0.0_dp
  q(4)  = 0.1_dp
! q(5)  = 1.0_dp
    p   = 1.0_dp/gamma

  rho           = q(1)
  u             = q(2) / rho
  v             = q(3) / rho
  w             = q(4) / rho

  unorm         = u * xnorm + v * ynorm +  w * znorm
  u_sq          = u*u + v*v + w*w
  q(5)          = p /gm1  + 0.5_dp*rho*u_sq

! asq           = gamma * p_ddt / rho_ddt
! rho_unorm_sq  = ( rho_ddt * unorm_ddt )**2


!=============================================================================80
!=============================================================================80
    p_1   = 1.01_dp*p
!=============================================================================80
    bc_state = porous_wall_outflow_state( q, xnorm, ynorm, znorm, s, p_1)
     rho_ddt   = bc_state(1)
     u_ddt     = bc_state(2)
     v_ddt     = bc_state(3)
     w_ddt     = bc_state(4)
     p_ddt     = bc_state(5)
     e_ddt     = p / gm1

     unorm_ddt =  (u_ddt * xnorm + v_ddt * ynorm + w_ddt * znorm)
     flux_ddt(1)   = rho_ddt * unorm_ddt
     flux_ddt(2)   = rho_ddt * u_ddt * unorm_ddt + p_ddt * area * xnorm
     flux_ddt(3)   = rho_ddt * v_ddt * unorm_ddt + p_ddt * area * ynorm
     flux_ddt(4)   = rho_ddt * w_ddt * unorm_ddt + p_ddt * area * znorm
     flux_ddt(5)   = ( e_ddt + p_ddt ) * unorm_ddt

    write(*,*) 'flux_outflow:......'
    write(6,'(10(1x,es15.5))') flux_ddt(1:5)%f
    write(*,*)

!=============================================================================80
!=============================================================================80

write(*,*)
  write(6,'(a,f15.10,a)') &
  'assert_equal_within(',flux_ddt(1)%f,'_dp,flux_ddt(1)%f, tol)'
  write(6,'(a,f15.10,a)') &
  'assert_equal_within(',flux_ddt(2)%f,'_dp,flux_ddt(2)%f, tol)'
  write(6,'(a,f15.10,a)') &
  'assert_equal_within(',flux_ddt(3)%f,'_dp,flux_ddt(3)%f, tol)'
  write(6,'(a,f15.10,a)') &
  'assert_equal_within(',flux_ddt(4)%f,'_dp,flux_ddt(4)%f, tol)'
  write(6,'(a,f15.10,a)') &
  'assert_equal_within(',flux_ddt(5)%f,'_dp,flux_ddt(5)%f, tol)'

assert_equal_within(   0.1000000000_dp,flux_ddt(1)%f, tol)
assert_equal_within(   0.0000000000_dp,flux_ddt(2)%f, tol)
assert_equal_within(   0.0000000000_dp,flux_ddt(3)%f, tol)
assert_equal_within(  -0.7086621068_dp,flux_ddt(4)%f, tol)
assert_equal_within(   0.2540290940_dp,flux_ddt(5)%f, tol)


    write(*,*) 'Fin test jacobian outflow'

end test
!=============================================================================80
!=============================================================================80
!=============================================================================80
test test_jacobian_inflow

  use fluid, only : setup_fluid_gamma, gamma, gm1, gp1
  use ddt,   only : ddt5, ddt5_identity, assignment(=), operator(+) &
                  , operator(-), operator(*), operator(/)           &
                  , operator(**), ddt_sqrt

  real(dp) :: p3
  real(dp) :: xnorm, ynorm, znorm, area, areax, areay, areaz
  real(dp) :: ff, fm, fp

  real(dp) :: rho1
  real(dp) :: u1, du1dr, du1dm
  real(dp) :: v1, dv1dr, dv1dn
  real(dp) :: w1, dw1dr, dw1do
  real(dp) :: unorm1, dun1dr, dun1dm, dun1dn, dun1do, dun1de
  real(dp) :: u1s, du1sdr, du1sdm, du1sdn, du1sdo
  real(dp) :: p1,   dp1dr, dp1dm, dp1dn, dp1do, dp1de
  real(dp) :: a1s, das1dr, das1dm, das1dn, das1do, das1de
  real(dp) :: rm1s,       dm1sdr, dm1sdm, dm1sdn, dm1sdo, dm1sde
  real(dp) :: rm2s, dm2s, dm2sdr, dm2sdm, dm2sdn, dm2sdo, dm2sde
  real(dp) :: rm3s, dm3s, dm3sdr, dm3sdm, dm3sdn, dm3sdo, dm3sde
  real(dp) :: tt, dttdr, dttdm, dttdn, dttdo, dttde
  real(dp) :: s, phi, phi0, dphi, ddphi, rden
  real(dp) :: run2, drun2dr, drun2dm, drun2dn, drun2do, drun2de
  real(dp) :: dfddm3s, dfdr, dfdm, dfdn, dfdo, dfde
  real(dp) :: dfdrp, dfdmp, dfdnp, dfdop, dfdep

  real(dp) :: dru2mdr, dru2mdm, dru2mdn, dru2mdo, dru2mde
  real(dp) :: run, drundr, drundm, drundn, drundo, drunde
  real(dp) :: run2m, drun2mdr, drun2mdm, drun2mdn, drun2mdo, drun2mde


  real(dp) :: cf, dcfdr, dcfdm, dcfdn, dcfdo, dcfde
  real(dp) :: rnum
  real(dp) :: pb, pbr, pbm, pbn, pbo, pbe
  real(dp) :: pt, dptdr, dptdm, dptdn, dptdo, dptde

  real(dp) :: dfddm2s

  real(dp) :: t, dtdr, dtdm, dtdn, dtdo, dtde


  real(dp) :: rhob, rhobr, rhobm, rhobn, rhobo, rhobe
  real(dp) :: eb, ebr, ebm, ebn, ebo, ebe
  real(dp) :: unormb, unormbr, unormbm, unormbn, unormbo, unormbe

  real(dp) :: ub, ubr, ubm, ubn, ubo, ube
  real(dp) :: vb, vbr, vbm, vbn, vbo, vbe
  real(dp) :: wb, wbr, wbm, wbn, wbo, wbe

  real(dp), dimension(3)   :: normal
  real(dp), dimension(5)   :: flux
  real(dp), dimension(5,5) :: a_diag

  integer :: k
 
  real(dp),   dimension(5) :: q
  type(ddt5), dimension(5) :: q_ddt
  type(ddt5), dimension(5) :: flux_ddt, flux_ddt_test
  type(ddt5)               :: rho_ddt, u_ddt, v_ddt, w_ddt, p_ddt, pt_ddt
  type(ddt5)               :: u_sq_ddt, tt_ddt, rho_unorm_sq_ddt
  type(ddt5)               :: unorm_ddt, asq_ddt
  type(ddt5)               :: t_ddt, e_ddt
  type(ddt5)               :: mach_1_sq, mach_2_sq, mach_3_sq
  type(ddt5)               :: mach_factor_ddt
  type(ddt5)               :: rho_unorm_sq_m_ddt
  type(ddt5)               :: phi_ddt, cf_ddt
  type(ddt5)               :: run2_ddt, run2m_ddt
  type(ddt5), dimension(5) :: bc_state
  type(ddt5) :: rho, u, v, w, p, e, unorm

  real(dp), parameter :: one = 1.0_dp

  call setup_fluid_gamma

  p3    = 0.99_dp/gamma
  s     = 0.5_dp

  xnorm = 1.0_dp
  ynorm = 1.0_dp
  znorm = 1.0_dp
  area  = 1.0_dp ! dqrt(xnorm*xnorm+ynorm*ynorm+znorm*znorm)
  normal = (/xnorm,ynorm,znorm/)
  areax  = normal(1)*area
  areay  = normal(2)*area
  areaz  = normal(3)*area

  q(1)  = 1.0_dp
  q(2)  = 0.1_dp
  q(3)  = 0.1_dp
  q(4)  = 0.1_dp
  q(5)  = 1.0_dp
  q_ddt = ddt5_identity(q)

  rho_ddt           = q_ddt(1)
  u_ddt             = q_ddt(2) / rho_ddt
  v_ddt             = q_ddt(3) / rho_ddt
  w_ddt             = q_ddt(4) / rho_ddt
! p_ddt             = q_ddt(5)
  unorm_ddt         = u_ddt  *xnorm +  v_ddt  *ynorm +  w_ddt  *znorm
  u_sq_ddt          = u_ddt*u_ddt + v_ddt*v_ddt + w_ddt*w_ddt
  p_ddt             = gm1 * ( q_ddt(5) - 0.5_dp*rho_ddt*u_sq_ddt )
  asq_ddt           = gamma * p_ddt / rho_ddt
  mach_1_sq         = unorm_ddt**2/asq_ddt
  tt_ddt            = asq_ddt * ( 1.0_dp + 0.5_dp * gm1 * mach_1_sq )
  rho_unorm_sq_ddt  = ( rho_ddt * unorm_ddt )**2

  rho1  = q(1)
  u1    = q(2) / rho1
  du1dr = -u1 / rho1
  du1dm = 1.0_dp / rho1

  v1    = q(3) / rho1
  dv1dr = -v1 / rho1
  dv1dn = 1.0_dp / rho1

  w1    = q(4) / rho1
  dw1dr = -w1 / rho1
  dw1do = 1.0_dp / rho1

  unorm1 =  u1  *xnorm +  v1  *ynorm +  w1  *znorm

  dun1dr = du1dr*xnorm + dv1dr*ynorm + dw1dr*znorm
  dun1dm = du1dm*xnorm
  dun1dn =               dv1dn*ynorm
  dun1do =                             dw1do*znorm
  dun1de = 0.0_dp

  u1s    = u1*u1 + v1*v1 + w1*w1
  du1sdr = 2.0_dp * ( u1*du1dr + v1*dv1dr + w1*dw1dr )
  du1sdm = 2.0_dp *   u1*du1dm
  du1sdn = 2.0_dp *              v1*dv1dn
  du1sdo = 2.0_dp *                         w1*dw1do

  p1     = gm1 * ( q(5) - 0.5_dp*rho1*u1s )
  dp1dr  = -0.5_dp * gm1 * (u1s + rho1*du1sdr )
  dp1dm  = -0.5_dp * gm1 * rho1 * du1sdm
  dp1dn  = -0.5_dp * gm1 * rho1 * du1sdn
  dp1do  = -0.5_dp * gm1 * rho1 * du1sdo
  dp1de  =           gm1

  a1s    = gamma * p1 / rho1
  das1dr = gamma*dp1dr/rho1 - a1s*rho1
  das1dm = gamma*dp1dm/rho1
  das1dn = gamma*dp1dn/rho1
  das1do = gamma*dp1do/rho1
  das1de = gamma*dp1de/rho1

  rm1s   = unorm1 * unorm1 / a1s; mach_1_sq = unorm_ddt**2/asq_ddt
  dm1sdr = (2.0_dp*unorm1*dun1dr - rm1s*das1dr)/a1s
  dm1sdm = (2.0_dp*unorm1*dun1dm - rm1s*das1dm)/a1s
  dm1sdn = (2.0_dp*unorm1*dun1dn - rm1s*das1dn)/a1s
  dm1sdo = (2.0_dp*unorm1*dun1do - rm1s*das1do)/a1s
  dm1sde =                       - rm1s*das1de /a1s

  tt     = a1s * ( 1.0_dp + 0.5_dp * gm1 * rm1s )
  dttdr  = das1dr * ( 1.0_dp + 0.5_dp*gm1*rm1s ) + 0.5_dp*gm1*a1s*dm1sdr
  dttdm  = das1dm * ( 1.0_dp + 0.5_dp*gm1*rm1s ) + 0.5_dp*gm1*a1s*dm1sdm
  dttdn  = das1dn * ( 1.0_dp + 0.5_dp*gm1*rm1s ) + 0.5_dp*gm1*a1s*dm1sdn
  dttdo  = das1do * ( 1.0_dp + 0.5_dp*gm1*rm1s ) + 0.5_dp*gm1*a1s*dm1sdo
  dttde  = das1de * ( 1.0_dp + 0.5_dp*gm1*rm1s ) + 0.5_dp*gm1*a1s*dm1sde

  phi0   = 0.04137_dp/(0.0982+s) + 0.57323 + 0.005786*(1.0_dp-s)

  run2    = ( rho1 * unorm1 )**2
  drun2dr = 2.0_dp*rho1*unorm1*(rho1*dun1dr + unorm1)
  drun2dm = 2.0_dp*rho1*unorm1* rho1*dun1dm
  drun2dn = 2.0_dp*rho1*unorm1* rho1*dun1dn
  drun2do = 2.0_dp*rho1*unorm1* rho1*dun1do
  drun2de = 2.0_dp*rho1*unorm1* rho1*dun1de

  assert_real_equal( unorm1,   unorm_ddt%f )
  assert_real_equal( dun1dr,   unorm_ddt%d(1) )
  assert_real_equal( dun1dm,   unorm_ddt%d(2) )
  assert_real_equal( dun1dn,   unorm_ddt%d(3) )
  assert_real_equal( dun1do,   unorm_ddt%d(4) )
  assert_real_equal( dun1de,   unorm_ddt%d(5) )

  assert_real_equal( u1s,   u_sq_ddt%f )
  assert_real_equal( du1sdr,   u_sq_ddt%d(1) )
  assert_real_equal( du1sdm,   u_sq_ddt%d(2) )
  assert_real_equal( du1sdn,   u_sq_ddt%d(3) )
  assert_real_equal( du1sdo,   u_sq_ddt%d(4) )

  assert_real_equal( p1,   p_ddt%f )
  assert_real_equal( dp1dr,   p_ddt%d(1) )
  assert_real_equal( dp1dm,   p_ddt%d(2) )
  assert_real_equal( dp1dn,   p_ddt%d(3) )
  assert_real_equal( dp1do,   p_ddt%d(4) )
  assert_real_equal( dp1de,   p_ddt%d(5) )

  assert_real_equal( a1s,   asq_ddt%f )
  assert_real_equal( das1dr,   asq_ddt%d(1) )
  assert_real_equal( das1dm,   asq_ddt%d(2) )
  assert_real_equal( das1dn,   asq_ddt%d(3) )
  assert_real_equal( das1do,   asq_ddt%d(4) )
  assert_real_equal( das1de,   asq_ddt%d(5) )

  assert_real_equal( rm1s,   mach_1_sq%f )
  assert_real_equal( dm1sdr,   mach_1_sq%d(1) )
  assert_real_equal( dm1sdm,   mach_1_sq%d(2) )
  assert_real_equal( dm1sdn,   mach_1_sq%d(3) )
  assert_real_equal( dm1sdo,   mach_1_sq%d(4) )
  assert_real_equal( dm1sde,   mach_1_sq%d(5) )

  assert_real_equal( tt,   tt_ddt%f )
  assert_real_equal( dttdr,   tt_ddt%d(1) )
  assert_real_equal( dttdm,   tt_ddt%d(2) )
  assert_real_equal( dttdn,   tt_ddt%d(3) )
  assert_real_equal( dttdo,   tt_ddt%d(4) )
  assert_real_equal( dttde,   tt_ddt%d(5) )

  assert_real_equal( run2,   rho_unorm_sq_ddt%f )
  assert_real_equal( drun2dr,   rho_unorm_sq_ddt%d(1) )
  assert_real_equal( drun2dm,   rho_unorm_sq_ddt%d(2) )
  assert_real_equal( drun2dn,   rho_unorm_sq_ddt%d(3) )
  assert_real_equal( drun2do,   rho_unorm_sq_ddt%d(4) )
  assert_real_equal( drun2de,   rho_unorm_sq_ddt%d(5) )

  inflow_outflow:  if ( unorm1 > 0.0_dp ) then
    rm3s = rm1s
    dm3sdr = dm1sdr
    dm3sdm = dm1sdm
    dm3sdn = dm1sdn
    dm3sdo = dm1sdo
    dm3sde = dm1sde
    phi    = phi0 + 0.185*s**0.25_dp*((1.0_dp+0.5_dp*gm1)**(gamma/gm1)-1.0_dp)
    do k = 1, 10
      fm = 1.0_dp + 0.5_dp*gm1*rm3s
      ff = 0.5_dp*gp1*phi*phi*(1.0_dp-s)*(1.0_dp-s)                            &
         * ((1.0_dp+gamma*rm3s)/(1.0+gamma*phi*(1.0_dp-s)))**2                 &
         - rm3s*fm
      dfddm3s = (gamma*gp1*phi*phi*(1.0_dp-s)*(1.0_dp-s) &
              * (1.0_dp+gamma*rm3s)/(1.0_dp+gamma*phi*(1.0_dp-s))**2 &
              - 2.0_dp*fm + 1.0_dp )
      dfdr = dfddm3s*dm3sdr
      dfdm = dfddm3s*dm3sdm
      dfdn = dfddm3s*dm3sdn
      dfdo = dfddm3s*dm3sdo
      dfde = dfddm3s*dm3sde
      fp = gamma*gp1*phi*phi*(1.0_dp-s)*(1.0_dp-s) &
         * ((1.0_dp+gamma*rm3s)/(1.0_dp+gamma*phi*(1.0_dp-s))**2) &
         - 2.0_dp*fm + 1.0_dp
      dfdrp = (gamma*gamma*gp1*phi*phi*(1.0_dp-s)*(1.0_dp-s) &
                    / (1.0_dp+gamma*phi*(1.0_dp-s))**2 - gm1)*dm3sdr
      dfdmp = (gamma*gamma*gp1*phi*phi*(1.0_dp-s)*(1.0_dp-s) &
                    / (1.0_dp+gamma*phi*(1.0_dp-s))**2 - gm1)*dm3sdm
      dfdnp = (gamma*gamma*gp1*phi*phi*(1.0_dp-s)*(1.0_dp-s) &
                    / (1.0_dp+gamma*phi*(1.0_dp-s))**2 - gm1)*dm3sdn
      dfdop = (gamma*gamma*gp1*phi*phi*(1.0_dp-s)*(1.0_dp-s) &
                    / (1.0_dp+gamma*phi*(1.0_dp-s))**2 - gm1)*dm3sdo
      dfdep = (gamma*gamma*gp1*phi*phi*(1.0_dp-s)*(1.0_dp-s) &
                    / (1.0_dp+gamma*phi*(1.0_dp-s))**2 - gm1)*dm3sde
      dm3s = -ff/fp
      rm3s = rm3s + dm3s
      dm3sdr = dm3sdr - (dfdr + dfdrp*dm3s)/dfddm3s
      dm3sdm = dm3sdm - (dfdm + dfdmp*dm3s)/dfddm3s
      dm3sdn = dm3sdn - (dfdn + dfdnp*dm3s)/dfddm3s
      dm3sdo = dm3sdo - (dfdo + dfdop*dm3s)/dfddm3s
      dm3sde = dm3sde - (dfde + dfdep*dm3s)/dfddm3s
    end do

    !----------------------------------------------------------
    mach_3_sq          = newton_inflow_mach_3_ddt ( s, mach_1_sq, phi )
    !----------------------------------------------------------

    assert_equal_within( rm3s,   mach_3_sq%f,    tol )
    assert_equal_within( dm3sdr, mach_3_sq%d(1), tol )
    assert_equal_within( dm3sdm, mach_3_sq%d(2), tol )
    assert_equal_within( dm3sdn, mach_3_sq%d(3), tol )
    assert_equal_within( dm3sdo, mach_3_sq%d(4), tol )
    assert_equal_within( dm3sde, mach_3_sq%d(5), tol )

write(*,*) 'mach_3'
write(6,'(10(1x,es15.5))') mach_3_sq
write(6,'(10(1x,es15.5))') rm3s, dm3sdr, dm3sdm, dm3sdn, dm3sdo, dm3sde
write(*,*)

    mach_factor_ddt    = 1.0_dp + 0.5_dp*gm1*mach_3_sq
    rho_unorm_sq_m_ddt = gamma*gamma/tt_ddt*p3*p3*mach_3_sq*mach_factor_ddt
    run2m_ddt          = gamma*gamma/tt_ddt*p3*p3*mach_3_sq*mach_factor_ddt

    fm = 1.0_dp + 0.5_dp*gm1*rm3s
    run2m = gamma*gamma/tt*p3*p3*rm3s*fm
    dru2mdr = gamma*gamma*p3*p3/tt*((2.0_dp*fm-1.0_dp)*dm3sdr &
                                                             - rm3s/tt*fm*dttdr)
    dru2mdm = gamma*gamma*p3*p3/tt*((2.0_dp*fm-1.0_dp)*dm3sdr &
                                                             - rm3s/tt*fm*dttdm)
    dru2mdn = gamma*gamma*p3*p3/tt*((2.0_dp*fm-1.0_dp)*dm3sdr &
                                                             - rm3s/tt*fm*dttdn)
    dru2mdo = gamma*gamma*p3*p3/tt*((2.0_dp*fm-1.0_dp)*dm3sdr &
                                                             - rm3s/tt*fm*dttdo)
    dru2mde = gamma*gamma*p3*p3/tt*((2.0_dp*fm-1.0_dp)*dm3sdr &
                                                             - rm3s/tt*fm*dttde)
    assert_real_equal( run2m,   rho_unorm_sq_m_ddt%f    )
    assert_real_equal( dru2mdr, rho_unorm_sq_m_ddt%d(1) )
    assert_real_equal( dru2mdm, rho_unorm_sq_m_ddt%d(2) )
    assert_real_equal( dru2mdn, rho_unorm_sq_m_ddt%d(3) )
    assert_real_equal( dru2mdo, rho_unorm_sq_m_ddt%d(4) )
    assert_real_equal( dru2mde, rho_unorm_sq_m_ddt%d(5) )

    if ( run2 > run2m ) then
      run2_ddt = run2m_ddt
      run2    = run2m
      drun2dr = drun2mdr
      drun2dm = drun2mdm
      drun2dn = drun2mdn
      drun2do = drun2mdo
      drun2de = drun2mde
      rm2s    = 1.0_dp
      dm2sdr  = 0.0_dp
      dm2sdm  = 0.0_dp
      dm2sdn  = 0.0_dp
      dm2sdo  = 0.0_dp
      dm2sde  = 0.0_dp
    else
      run2_ddt = rho_unorm_sq_ddt
      mach_3_sq = (ddt_sqrt(1.0_dp+2.0_dp*gm1*tt_ddt*run2_ddt &
                /gamma/gamma/p3/p3)-1.0_dp)/gm1
      rm3s = (-1.0_dp+sqrt(1.0_dp+2.0_dp*tt*run2*(gm1/gamma/gamma/p3/p3)))/gm1
      dm3sdr = (tt*drun2dr + run2*dttdr)/(p3*p3*gamma*gamma) &
             / sqrt(1.0_dp+2.0_dp*gm1/gamma/gamma*tt*run2/p3/p3)
      dm3sdm = (tt*drun2dm + run2*dttdm)/(p3*p3*gamma*gamma) &
             / sqrt(1.0_dp+2.0_dp*gm1/gamma/gamma*tt*run2/p3/p3)
      dm3sdn = (tt*drun2dn + run2*dttdn)/(p3*p3*gamma*gamma) &
             / sqrt(1.0_dp+2.0_dp*gm1/gamma/gamma*tt*run2/p3/p3)
      dm3sdo = (tt*drun2do + run2*dttdo)/(p3*p3*gamma*gamma) &
             / sqrt(1.0_dp+2.0_dp*gm1/gamma/gamma*tt*run2/p3/p3)
      dm3sde = (tt*drun2de + run2*dttde)/(p3*p3*gamma*gamma) &
             / sqrt(1.0_dp+2.0_dp*gm1/gamma/gamma*tt*run2/p3/p3)

      assert_equal_within( rm3s,   mach_3_sq%f,    tol )
      assert_equal_within( dm3sdr, mach_3_sq%d(1), tol )
      assert_equal_within( dm3sdm, mach_3_sq%d(2), tol )
      assert_equal_within( dm3sdn, mach_3_sq%d(3), tol )
      assert_equal_within( dm3sdo, mach_3_sq%d(4), tol )
      assert_equal_within( dm3sde, mach_3_sq%d(5), tol )
write(6,*) 'rm3s-conditional'
write(6,'(10(1x,es15.5))') mach_3_sq
write(6,'(10(1x,es15.5))') rm3s,dm3sdr, dm3sdm, dm3sdn, dm3sdo, dm3sde
write(*,*)
      cf_ddt = run2_ddt*tt_ddt/(gamma*gamma*p3*p3)
      cf = run2*tt/(gamma*gamma*p3*p3)
      dcfdr  = (run2*dttdr + tt*drun2dr)/(gamma*gamma*p3*p3)
      dcfdm  = (run2*dttdm + tt*drun2dm)/(gamma*gamma*p3*p3)
      dcfdn  = (run2*dttdn + tt*drun2dn)/(gamma*gamma*p3*p3)
      dcfdo  = (run2*dttdo + tt*drun2do)/(gamma*gamma*p3*p3)
      dcfde  = (run2*dttde + tt*drun2de)/(gamma*gamma*p3*p3)
      assert_equal_within( cf,    cf_ddt%f,    tol )
      assert_equal_within( dcfdr, cf_ddt%d(1), tol )
      assert_equal_within( dcfdm, cf_ddt%d(2), tol )
      assert_equal_within( dcfdn, cf_ddt%d(3), tol )
      assert_equal_within( dcfdo, cf_ddt%d(4), tol )
      assert_equal_within( dcfde, cf_ddt%d(5), tol )

      rnum   = 1.0_dp + gamma*rm3s
      rm2s   = 0.0_dp
      dm2sdr = 0.0_dp
      dm2sdm = 0.0_dp
      dm2sdn = 0.0_dp
      dm2sdo = 0.0_dp
      dm2sde = 0.0_dp

      do k = 1, 10
        fm    = 1.0_dp + 0.5_dp*gm1*rm2s
        phi   = phi0+ 0.185_dp*s**0.25_dp*(fm**(gamma/gm1)-1.0_dp)
        dphi  = gamma*0.0925_dp*s**0.25_dp*fm**(1.0_dp/gm1)
        ddphi = gamma*0.04625_dp*s**0.25_dp*fm**((2.0_dp-gamma)/gm1)
        rden  = 1.0_dp + gamma*rm2s*phi*(1.0_dp-s)
        ff    = rm2s*fm*phi*phi*(1.0_dp-s)*(1.0_dp-s)*(rnum/rden)**2-cf
        dfddm2s = rnum*rnum/rden/rden*phi*phi*(1.0_dp-s)*(1.0_dp-s) &
                *((2.0_dp*fm-1.0_dp)-2.0_dp*rm2s*fm*gamma/rden*(1.0_dp-s) &
                * (phi+rm2s*dphi)+ 2/phi*dphi*rm2s*fm)
        dfdr = rnum*rnum/rden/rden*phi*phi*(1.0_dp-s)*(1.0_dp-s) &
             * 2.0_dp*rm2s*fm*gamma/rnum*dm3sdr  &
             + dfddm2s*dm2sdr - dcfdr

        fp   = rnum*rnum/rden/rden*phi*phi*(1.0_dp-s)*(1.0_dp-s) &
             *(0.5_dp*gm1*rm2s+fm*(1.0_dp-2.0_dp*gamma*(1.0_dp-s)*rm2s &
             *(phi+rm2s*dphi)/(1.0_dp+gamma*rm2s*phi*(1.0-s)) &
             + 2.0_dp*rm2s/phi*dphi))
        dfdrp = 2.0_dp*phi*(1.0_dp-s)*(1.0_dp-s)*rnum*rnum/rden/rden &
        *(dphi-gamma*phi*(phi+rm2s*dphi)*(1.0_dp-s)/rden) &
        *((2.0_dp*fm-1.0_dp)*dm2sdr+2.0_dp*rm2s*fm*gamma/rnum &
        *(dm3sdr-rnum/rden*(1.0_dp-s)*(phi+rm2s*dphi)*dm2sdr)&
        +2.0_dp/phi*dphi*dm2sdr*rm2s*fm)&
        + phi*phi*(1.0_dp-s)*(1.0_dp-s)*rnum*rnum/rden/rden &
        *(gm1*dm2sdr+2.0_dp*gamma/rnum*(2.0_dp*fm-1.0_dp)*(dm3sdr &
        -rnum/rden*(1.0_dp-s)*(phi+rm2s*dphi)*dm2sdr) &
        + 2.0_dp*gamma*(1.0_dp-s)/rden*rm2s*fm*dm2sdr*(gamma*(1.0_dp-s) &
        /rden*(phi+rm2s*dphi)**2-(2.0_dp*dphi+rm2s*ddphi)) &
        + 2.0_dp/phi*dm2sdr*(ddphi*rm2s*fm &
        +dphi*(2.0_dp*fm-1.0_dp)-dphi*dphi*rm2s*fm))

        dm2s = -ff/fp
        rm2s = rm2s + dm2s
        dm2sdr = dm2sdr - (dfdr+dfdrp*dm2s)/dfddm2s
      enddo
    endif
    !----------------------------------------------------------
    mach_2_sq = newton_inflow_mach_2_ddt( s, mach_3_sq, cf_ddt )
    !----------------------------------------------------------
write(*,*) 'mach_2'
write(6,'(10(1x,es15.5))') mach_2_sq
write(*,*)
    mach_factor_ddt = 1.0_dp + 0.5_dp*gm1*mach_2_sq
    phi_ddt         = phi0+ 0.185_dp*s**0.25_dp*(mach_factor_ddt**(gamma/gm1)-1.0_dp)
    pt_ddt          = p3 *((1.0_dp+gamma*mach_3_sq) &
                    /(1.0_dp+gamma*mach_2_sq*phi_ddt*(1.0_dp-s))) &
                    * mach_factor_ddt**(gamma/gm1)
    pt              = p3 *((1.0_dp+gamma*mach_3_sq) &
                    /(1.0_dp+gamma*mach_2_sq*phi_ddt*(1.0_dp-s))) &
                    * mach_factor_ddt**(gamma/gm1)

    dm2sdm = mach_2_sq%d(2)
    dm2sdn = mach_2_sq%d(2)
    dm2sdo = mach_2_sq%d(4)
    dm2sde = mach_2_sq%d(5)

    fm     = 1.0_dp + 0.5_dp*gm1*rm2s
    assert_real_equal(fm, mach_factor_ddt%f)

    phi    = phi0+ 0.185_dp*s**0.25_dp*(fm**(gamma/gm1)-1.0_dp)
    dphi   = gamma*0.0925_dp*s**0.25_dp*fm**(1.0_dp/gm1)

    rnum   = 1.0_dp + gamma*rm3s
    rden   = 1.0_dp + gamma*rm2s*phi*(1.0_dp-s)

    pt     = p3 *((1.0_dp+gamma*rm3s) &
           /(1.0_dp+gamma*rm2s*phi*(1.0_dp-s))) &
           * fm**(gamma/gm1)
    dptdr  = pt*(gamma/rnum*dm3sdr-gamma*(1.0_dp-s)/rden &
          * (phi+rm2s*dphi)*dm2sdr+0.5_dp*gamma/fm*dm2sdr)
    dptdm  = pt*(gamma/rnum*dm3sdm-gamma*(1.0_dp-s)/rden &
          * (phi+rm2s*dphi)*dm2sdm+0.5_dp*gamma/fm*dm2sdm)
    dptdn  = pt*(gamma/rnum*dm3sdn-gamma*(1.0_dp-s)/rden &
          * (phi+rm2s*dphi)*dm2sdn+0.5_dp*gamma/fm*dm2sdn)
    dptdo  = pt*(gamma/rnum*dm3sdo-gamma*(1.0_dp-s)/rden &
          * (phi+rm2s*dphi)*dm2sdo+0.5_dp*gamma/fm*dm2sdo)
    dptde  = pt*(gamma/rnum*dm3sde-gamma*(1.0_dp-s)/rden &
          * (phi+rm2s*dphi)*dm2sde+0.5_dp*gamma/fm*dm2sde)

    assert_real_equal( pt,    pt_ddt%f    )
    assert_real_equal( dptdr, pt_ddt%d(1) )
    assert_real_equal( dptdm, pt_ddt%d(2) )
    assert_real_equal( dptdn, pt_ddt%d(3) )
    assert_real_equal( dptdo, pt_ddt%d(4) )
    assert_real_equal( dptde, pt_ddt%d(5) )

    rm1s   = rm3s
    dm1sdr = dm3sdr
    dm1sdm = dm3sdm
    dm1sdn = dm3sdn
    dm1sdo = dm3sdo
    dm1sde = dm3sde
    cf_ddt = run2_ddt*tt_ddt/(gamma*gamma*pt_ddt*pt_ddt)
    cf     = run2*tt/(gamma*gamma*pt*pt)
    dcfdr  = (run2*dttdr+tt*drun2dr-2.0_dp*tt/pt*run2*dptdr) &
           / (gamma*gamma*pt*pt)
    dcfdm  = (run2*dttdm+tt*drun2dm-2.0_dp*tt/pt*run2*dptdm) &
           / (gamma*gamma*pt*pt)
    dcfdn  = (run2*dttdn+tt*drun2dn-2.0_dp*tt/pt*run2*dptdn) &
           / (gamma*gamma*pt*pt)
    dcfdo  = (run2*dttdo+tt*drun2do-2.0_dp*tt/pt*run2*dptdo) &
           / (gamma*gamma*pt*pt)
    dcfde  = (run2*dttde+tt*drun2de-2.0_dp*tt/pt*run2*dptde) &
           / (gamma*gamma*pt*pt)
    
    !----------------------------------------------------------
    mach_1_sq   = newton_inflow_mach_1_ddt( mach_3_sq, cf_ddt )
    !----------------------------------------------------------

write(*,*) 'mach_1'
write(6,'(10(1x,es15.5))') mach_1_sq
write(*,*)
!=============================================================================80
    rm1s        = mach_1_sq%f
    dm1sdr      = mach_1_sq%d(1)
    dm1sdm      = mach_1_sq%d(2)
    dm1sdn      = mach_1_sq%d(3)
    dm1sdo      = mach_1_sq%d(4)
    dm1sde      = mach_1_sq%d(5)

    if ( run2 > 0.0_dp ) then
      run    = sqrt(run2)
      drundr = 0.5_dp/run*drun2dr
      drundm = 0.5_dp/run*drun2dm
      drundn = 0.5_dp/run*drun2dn
      drundo = 0.5_dp/run*drun2do
      drunde = 0.5_dp/run*drun2de
    else
      run    = 0.0_dp
      drundr = 0.0_dp
      drundm = 0.0_dp
      drundn = 0.0_dp
      drundo = 0.0_dp
      drunde = 0.0_dp
    endif

!=============================================================================80
!=============================================================================80
  else inflow_outflow

   if ( run2 > 0.0_dp ) then
      run = -sqrt(run2)
      drundr = 0.5_dp / run*drun2dr
      drundm = 0.5_dp / run*drun2dm
      drundn = 0.5_dp / run*drun2dn
      drundo = 0.5_dp / run*drun2do
      drunde = 0.5_dp / run*drun2de
    else
      run    = 0.0_dp
      drundr = 0.0_dp
      drundm = 0.0_dp
      drundn = 0.0_dp
      drundo = 0.0_dp
      drunde = 0.0_dp
    endif

  endif inflow_outflow
!=============================================================================80
!=============================================================================80

  mach_factor_ddt = 1.0_dp + 0.5_dp*gm1*mach_1_sq
  p_ddt           = pt_ddt*mach_factor_ddt**(-gamma/gm1)
  t_ddt           = tt_ddt / mach_factor_ddt
  rho_ddt         = gamma*p_ddt / t_ddt
  e_ddt           = p_ddt / gm1
  unorm_ddt       = ddt_sqrt(rho_unorm_sq_ddt) / rho_ddt
  u_ddt           = unorm_ddt * xnorm
  v_ddt           = unorm_ddt * ynorm
  w_ddt           = unorm_ddt * znorm

  flux_ddt_test(1) = rho_ddt * unorm_ddt 
  flux_ddt_test(2) = rho_ddt * u_ddt * unorm_ddt + p_ddt * areax
  flux_ddt_test(3) = rho_ddt * v_ddt * unorm_ddt + p_ddt * areay
  flux_ddt_test(4) = rho_ddt * w_ddt * unorm_ddt + p_ddt * areaz
  flux_ddt_test(5) = ( e_ddt + p_ddt ) * unorm_ddt

  flux = residual_porous_wall( q, xnorm, ynorm, znorm, area, s, p3 )
  bc_state = porous_wall_inflow_state( q, xnorm, ynorm, znorm, s, p3 )
     rho   = bc_state(1)
     u     = bc_state(2)
     v     = bc_state(3)
     w     = bc_state(4)
     p     = bc_state(5)
     e     = p / gm1

     unorm =  (u * xnorm + v * ynorm + w * znorm)
     flux_ddt(1)   = rho * unorm
     flux_ddt(2)   = rho * u * unorm + p * area * xnorm
     flux_ddt(3)   = rho * v * unorm + p * area * ynorm
     flux_ddt(4)   = rho * w * unorm + p * area * znorm
     flux_ddt(5)   = ( e + p ) * unorm
write(*,*) 'flux:......'
write(6,'(10(1x,es15.5))') flux_ddt_test(1:5)%f
write(6,'(10(1x,es15.5))') flux_ddt(1:5)%f
write(6,'(10(1x,es15.5))') flux(1:5)
write(*,*)
  write(6,'(a,f15.10,a)') &
  'assert_equal_within(',flux_ddt(1)%f,'_dp,flux_ddt_test(1)%f, tol)'
  write(6,'(a,f15.10,a)') &
  'assert_equal_within(',flux_ddt(2)%f,'_dp,flux_ddt_test(2)%f, tol)'
  write(6,'(a,f15.10,a)') &
  'assert_equal_within(',flux_ddt(3)%f,'_dp,flux_ddt_test(3)%f, tol)'
  write(6,'(a,f15.10,a)') &
  'assert_equal_within(',flux_ddt(4)%f,'_dp,flux_ddt_test(4)%f, tol)'
  write(6,'(a,f15.10,a)') &
  'assert_equal_within(',flux_ddt(5)%f,'_dp,flux_ddt_test(5)%f, tol)'

  assert_equal_within(   0.3000000000_dp,flux_ddt_test(1)%f, tol)
  assert_equal_within(   0.8734119850_dp,flux_ddt_test(2)%f, tol)
  assert_equal_within(   0.8734119850_dp,flux_ddt_test(3)%f, tol)
  assert_equal_within(   0.8734119850_dp,flux_ddt_test(4)%f, tol)
  assert_equal_within(   0.4240014889_dp,flux_ddt_test(5)%f, tol)

  fm    = 1.0_dp + 0.5_dp*gm1*rm1s
  pb    = pt*fm**(-gamma/gm1)
  pbr   = pb*(dptdr/pt-0.5_dp*gamma/fm*dm1sdr)
  pbm   = pb*(dptdm/pt-0.5_dp*gamma/fm*dm1sdm)
  pbn   = pb*(dptdn/pt-0.5_dp*gamma/fm*dm1sdn)
  pbo   = pb*(dptdo/pt-0.5_dp*gamma/fm*dm1sdo)
  pbe   = pb*(dptde/pt-0.5_dp*gamma/fm*dm1sde)
  write(6,'(a,f15.10,a)') 'assert_equal_within(',pb,'_dp,p_ddt%f, tol)'

  t     = tt/fm
  dtdr  = (dttdr - 0.5_dp*gm1*tt/fm*dm1sdr)/fm
  dtdm  = (dttdm - 0.5_dp*gm1*tt/fm*dm1sdm)/fm
  dtdn  = (dttdn - 0.5_dp*gm1*tt/fm*dm1sdn)/fm
  dtdo  = (dttdo - 0.5_dp*gm1*tt/fm*dm1sdo)/fm
  dtde  = (dttde - 0.5_dp*gm1*tt/fm*dm1sde)/fm

  rhob  = gamma*pb / t
  rhobr = gamma*(pbr-pb/t*dtdr)/t
  rhobm = gamma*(pbm-pb/t*dtdm)/t
  rhobn = gamma*(pbn-pb/t*dtdn)/t
  rhobo = gamma*(pbo-pb/t*dtdo)/t
  rhobe = gamma*(pbe-pb/t*dtde)/t

  eb    = pb / gm1
  ebr   = pbr / gm1
  ebm   = pbm / gm1
  ebn   = pbn / gm1
  ebo   = pbo / gm1
  ebe   = pbe / gm1

  unormb = run / rhob
  unormbr = (drundr - run/rhob*rhobr)/rhob
  unormbm = (drundm - run/rhob*rhobm)/rhob
  unormbn = (drundn - run/rhob*rhobn)/rhob
  unormbo = (drundo - run/rhob*rhobo)/rhob
  unormbe = (drunde - run/rhob*rhobe)/rhob

  ub   = unormb*xnorm
  ubr  = unormbr*xnorm
  ubm  = unormbm*xnorm
  ubn  = unormbn*xnorm
  ubo  = unormbo*xnorm
  ube  = unormbe*xnorm

  vb   = unormb*ynorm
  vbr  = unormbr*ynorm
  vbm  = unormbm*ynorm
  vbn  = unormbn*ynorm
  vbo  = unormbo*ynorm
  vbe  = unormbe*ynorm

  wb   = unormb*znorm
  wbr  = unormbr*znorm
  wbm  = unormbm*znorm
  wbn  = unormbn*znorm
  wbo  = unormbo*znorm
  wbe  = unormbe*znorm

  assert_real_equal( pb,    p_ddt%f ) 
  assert_real_equal( pbr,   p_ddt%d(1) ) 
  assert_real_equal( pbm,   p_ddt%d(2) ) 
  assert_real_equal( pbn,   p_ddt%d(3) ) 
  assert_real_equal( pbo,   p_ddt%d(4) ) 

  assert_real_equal( t,    t_ddt%f ) 
  assert_real_equal( dtdr, t_ddt%d(1) ) 
  assert_real_equal( dtdm, t_ddt%d(2) ) 
  assert_real_equal( dtdn, t_ddt%d(3) ) 
  assert_real_equal( dtdo, t_ddt%d(4) ) 

  assert_real_equal( rhob,  rho_ddt%f ) 
  assert_real_equal( rhobr, rho_ddt%d(1) ) 
  assert_real_equal( rhobm, rho_ddt%d(2) ) 
  assert_real_equal( rhobn, rho_ddt%d(3) ) 
  assert_real_equal( rhobo, rho_ddt%d(4) ) 

  assert_real_equal( eb,  e_ddt%f ) 
  assert_real_equal( ebr, e_ddt%d(1) ) 
  assert_real_equal( ebm, e_ddt%d(2) ) 
  assert_real_equal( ebn, e_ddt%d(3) ) 
  assert_real_equal( ebo, e_ddt%d(4) ) 

  assert_real_equal( unormb,  unorm_ddt%f )
  assert_real_equal( unormbr, unorm_ddt%d(1) )
  assert_real_equal( unormbm, unorm_ddt%d(2) )
  assert_real_equal( unormbn, unorm_ddt%d(3) )
  assert_real_equal( unormbo, unorm_ddt%d(4) )

  assert_real_equal( ub,  u_ddt%f )
  assert_real_equal( ubr, u_ddt%d(1) )
  assert_real_equal( ubm, u_ddt%d(2) )
  assert_real_equal( ubn, u_ddt%d(3) )
  assert_real_equal( ubo, u_ddt%d(4) )

  assert_real_equal( vb,  v_ddt%f )
  assert_real_equal( vbr, v_ddt%d(1) )
  assert_real_equal( vbm, v_ddt%d(2) )
  assert_real_equal( vbn, v_ddt%d(3) )
  assert_real_equal( vbo, v_ddt%d(4) )

  assert_real_equal( wb,  w_ddt%f )
  assert_real_equal( wbr, w_ddt%d(1) )
  assert_real_equal( wbm, w_ddt%d(2) )
  assert_real_equal( wbn, w_ddt%d(3) )
  assert_real_equal( wbo, w_ddt%d(4) )


  a_diag(1,1) = rhob * unormbr + unormb * rhobr
  a_diag(1,2) = rhob * unormbm + unormb * rhobm
  a_diag(1,3) = rhob * unormbn + unormb * rhobn
  a_diag(1,4) = rhob * unormbo + unormb * rhobo
  a_diag(1,5) = rhob * unormbe + unormb * rhobe

  a_diag(2,1) = rhob*ub*unormbr + unormb*(rhob*ubr + ub*rhobr) + xnorm*pbr
  a_diag(2,2) = rhob*ub*unormbm + unormb*(rhob*ubm + ub*rhobm) + xnorm*pbn
  a_diag(2,3) = rhob*ub*unormbn + unormb*(rhob*ubn + ub*rhobn) + xnorm*pbn
  a_diag(2,4) = rhob*ub*unormbo + unormb*(rhob*ubo + ub*rhobo) + xnorm*pbo
  a_diag(2,5) = rhob*ub*unormbe + unormb*(rhob*ube + ub*rhobe) + xnorm*pbe

  a_diag(3,1) = rhob*vb*unormbr + unormb*(rhob*vbr + vb*rhobr) + ynorm*pbr
  a_diag(3,2) = rhob*vb*unormbm + unormb*(rhob*vbm + vb*rhobm) + ynorm*pbn
  a_diag(3,3) = rhob*vb*unormbn + unormb*(rhob*vbn + vb*rhobn) + ynorm*pbn
  a_diag(3,4) = rhob*vb*unormbo + unormb*(rhob*vbo + vb*rhobo) + ynorm*pbo
  a_diag(3,5) = rhob*vb*unormbe + unormb*(rhob*vbe + vb*rhobe) + ynorm*pbe

  a_diag(4,1) = rhob*wb*unormbr + unormb*(rhob*wbr + wb*rhobr) + znorm*pbr
  a_diag(4,2) = rhob*wb*unormbm + unormb*(rhob*wbm + wb*rhobm) + znorm*pbn
  a_diag(4,3) = rhob*wb*unormbn + unormb*(rhob*wbn + wb*rhobn) + znorm*pbn
  a_diag(4,4) = rhob*wb*unormbo + unormb*(rhob*wbo + wb*rhobo) + znorm*pbo
  a_diag(4,5) = rhob*wb*unormbe + unormb*(rhob*wbe + wb*rhobe) + znorm*pbe

  a_diag(5,1) = (eb+pb)*unormbr + unormb*(ebr + pbr)
  a_diag(5,2) = (eb+pb)*unormbm + unormb*(ebm + pbm)
  a_diag(5,3) = (eb+pb)*unormbn + unormb*(ebn + pbn)
  a_diag(5,4) = (eb+pb)*unormbo + unormb*(ebo + pbo)
  a_diag(5,5) = (eb+pb)*unormbe + unormb*(ebe + pbe)

write(*,*) 'a_diag(1,:)'
write(6,'(10(1x,es15.5))') flux_ddt(1)
write(6,'(10(1x,es15.5))') flux(1),a_diag(1,1),a_diag(1,2),a_diag(1,3),a_diag(1,4),a_diag(1,5)
write(*,*)
write(*,*) 'a_diag(2,:)'
write(6,'(10(1x,es15.5))') flux_ddt(2)
write(6,'(10(1x,es15.5))') flux(2),a_diag(2,1),a_diag(2,2),a_diag(2,3),a_diag(2,4),a_diag(2,5)
write(*,*)

! assert_real_equal( a_diag(1,1),  flux_ddt%d(1) )

write(*,*)
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(1,1),'_dp,a_diag(1,1), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(1,2),'_dp,a_diag(1,2), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(1,3),'_dp,a_diag(1,3), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(1,4),'_dp,a_diag(1,4), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(1,5),'_dp,a_diag(1,5), tol)'
write(*,*)
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(2,1),'_dp,a_diag(2,1), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(2,2),'_dp,a_diag(2,2), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(2,3),'_dp,a_diag(2,3), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(2,4),'_dp,a_diag(2,4), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(2,5),'_dp,a_diag(2,5), tol)'
write(*,*)
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(5,1),'_dp,a_diag(5,1), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(5,2),'_dp,a_diag(5,2), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(5,3),'_dp,a_diag(5,3), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(5,4),'_dp,a_diag(5,4), tol)'
  write(6,'(a,f15.10,a)') 'assert_equal_within(',a_diag(5,5),'_dp,a_diag(5,5), tol)'
write(*,*)

assert_equal_within(   0.0000000000_dp,a_diag(1,1), tol)
assert_equal_within(   1.0000000000_dp,a_diag(1,2), tol)
assert_equal_within(   1.0000000000_dp,a_diag(1,3), tol)
assert_equal_within(   1.0000000000_dp,a_diag(1,4), tol)
assert_equal_within(   0.0000000000_dp,a_diag(1,5), tol)
 
assert_equal_within(  -0.1676895747_dp,a_diag(2,1), tol)
assert_equal_within(   1.1179305323_dp,a_diag(2,2), tol)
assert_equal_within(   1.1179305323_dp,a_diag(2,3), tol)
assert_equal_within(   1.1179305323_dp,a_diag(2,4), tol)
assert_equal_within(   0.1621308092_dp,a_diag(2,5), tol)
 
assert_equal_within(  -0.4289329276_dp,a_diag(5,1), tol)
assert_equal_within(   1.4462145542_dp,a_diag(5,2), tol)
assert_equal_within(   1.4462145542_dp,a_diag(5,3), tol)
assert_equal_within(   1.4462145542_dp,a_diag(5,4), tol)
assert_equal_within(   0.4147141565_dp,a_diag(5,5), tol)


end test
!=============================================================================80
!=============================================================================80
!=============================================================================80
test test_compute_porous_plenum_pressure

  use fluid,         only : setup_fluid_gamma, gamma, gm1

  real(dp) :: actual
  real(dp) :: xmach
  real(dp) :: p0, pt0, p_min, p_avg, mdot_avg

  call setup_fluid_gamma

  xmach = 1.0_dp
  p0    = 1.0_dp / gamma
  pt0   = p0 * (1.0_dp+0.5_dp*gm1*xmach*xmach)**(gamma/gm1)

  mdot_avg = 0.00_dp
  p_avg    = p0
  p_min    = 0.90_dp*p_avg
  actual   = get_porous_plenum_pressure ( mdot_avg, p_avg, pt0, p_min )

  assert_equal_within(   p0, actual, tol)

  mdot_avg = 0.10_dp
  actual   = get_porous_plenum_pressure ( mdot_avg, p_avg, pt0, p_min )

  write(6,'(a,f15.10,a)') 'assert_equal_within(',actual,'_dp,actual, tol)'
  assert_equal_within(   0.7267917249_dp,actual, tol)

end test
!=============================================================================80
!=============================================================================80
!=============================================================================80
test test_newton_outflow_mach

  use fluid,         only : setup_fluid_gamma, gamma, gm1

  real(dp), dimension(5) :: q
  real(dp) :: mach_1_sq, mach_2_sq, mach_3_sq, s
  real(dp) :: cf, rho_unorm, rho_unorm_sq
  real(dp) :: rho_unorm_sq_m
  real(dp) :: phi, phi0, mach_factor
  real(dp) :: xnorm, ynorm, znorm, area
  real(dp) :: rho_1, p_1, p_2, p_3, pt_1, tt_1, a_1, unorm_1

  call setup_fluid_gamma

  s     = 0.5_dp
  q     = (/1.0_dp,0._dp,0._dp,-0.01_dp,0.714_dp/)

  xnorm = 0.0_dp
  ynorm = 0.0_dp
  znorm = 1.0_dp
  area  = 1.0_dp

  rho_1         = q(1)
  unorm_1       = q(2)*xnorm + q(3)*ynorm + q(4)*znorm
  p_1           = q(5)
  p_3           = 0.99_dp*q(5)

  a_1           = sqrt(gamma * p_1 / rho_1)
  mach_3_sq     = unorm_1**2 / a_1**2
  tt_1          = a_1**2 * (1.0_dp + 0.5_dp*gm1*mach_3_sq)
  phi0          = 0.04137_dp / (0.0982+s)+ 0.57323_dp + 0.005786_dp*(1.0_dp-s)

  rho_unorm_sq  = (rho_1*unorm_1)**2

  !----------------------------------------------------------------
  outflow:  if ( unorm_1 < 0.0_dp ) then

    mach_1_sq   = mach_3_sq
    mach_factor = 1.0_dp + 0.5_dp * gm1
    phi         = phi0 + 0.185_dp*(s**0.25_dp)*(mach_factor**(gamma/gm1)-1.0_dp)

    mach_1_sq   = newton_outflow_mach_1( s, mach_3_sq, phi )
  
    write(6,'(a,f15.10,a)') 'assert_equal_within(',mach_1_sq,',mach_1_sq, tol)'
    assert_equal_within(   0.0549782038,mach_1_sq, tol)

  !----------------------------------------------------------------
    mach_factor = 1.0_dp + 0.5_dp * gm1 * mach_1_sq
    rho_unorm_sq_m = gamma*gamma/tt_1*p_3*p_3*mach_1_sq*mach_factor
    if ( rho_unorm_sq > rho_unorm_sq_m ) then
      rho_unorm_sq = rho_unorm_sq_m
      mach_2_sq    = 1.0_dp
    else
      mach_1_sq = (sqrt(                                                      &
      1.0_dp + 2.0_dp * gm1/gamma/gamma*tt_1*rho_unorm_sq/p_3/p_3)-1.0_dp) / gm1
      cf            = rho_unorm_sq * tt_1 / ( gamma*gamma*p_3*p_3)
      mach_2_sq     = newton_outflow_mach_2( s, mach_1_sq, cf )

     write(6,'(a,f15.10,a)') 'assert_equal_within(',mach_2_sq,',mach_2_sq, tol)'
      assert_equal_within(   0.0009812517,mach_2_sq, tol)
    endif

  !----------------------------------------------------------------
    mach_factor = 1.0_dp + 0.5_dp * gm1 * mach_2_sq
    phi         = phi0 + 0.185_dp*(s**0.25_dp)*(mach_factor**(gamma/gm1)-1.0_dp)
    p_2 = p_3 *((1.0_dp+0.5_dp*gm1*mach_3_sq)/mach_factor)**(gamma/gm1)
    mach_3_sq   = mach_1_sq
    cf          = rho_unorm_sq * tt_1 / ( gamma*gamma * p_2*p_2 )

    mach_3_sq   = newton_outflow_mach_3( s, mach_2_sq, phi, cf )

    write(6,'(a,f15.10,a)') 'assert_equal_within(',mach_3_sq,',mach_3_sq, tol)'

    assert_equal_within(   0.0001021355,mach_3_sq, tol)

    p_1  = p_2 * ( 1.0_dp+gamma*mach_2_sq*phi*(1.0_dp-s))&
         / (1.0_dp+gamma*mach_1_sq)
    pt_1 = p_1 * ( 1.0_dp + 0.5_dp*gm1*mach_3_sq)**(gamma/gm1)
    rho_unorm = -sqrt(rho_unorm_sq)

    write(6,'(a,f15.10,a)') 'assert_equal_within(',p_1,'_dp,p_1, tol)'
    write(6,'(a,f15.10,a)') 'assert_equal_within(',pt_1,'_dp,pt_1, tol)'
    write(6,'(a,f15.10,a)') 'assert_equal_within(',rho_unorm,'_dp,rho_unorm, tol)'

assert_equal_within(   0.7066363368_dp,p_1, tol)
assert_equal_within(   0.7066868592_dp,pt_1, tol)

    assert_equal_within(  -0.0100000000_dp,rho_unorm, tol)

  end if outflow

end test

!=============================================================================80
!=============================================================================80
!=============================================================================80
test test_newton_inflow_mach

  use fluid,         only : setup_fluid_gamma, gamma, gm1

  real(dp), dimension(5) :: q
  real(dp) :: mach_1_sq, mach_2_sq, mach_3_sq, s
  real(dp) :: cf, p_3, rho_unorm, rho_unorm_sq
  real(dp) :: rho_unorm_sq_m
  real(dp) :: phi, pt1, phi0, mach_factor
  real(dp) :: xnorm, ynorm, znorm, area
  real(dp) :: rho_1, p_1, tt_1, a_1, unorm_1

  call setup_fluid_gamma

  s     = 0.5_dp
  q     = (/1.0_dp,0._dp,0._dp,0.01_dp,0.714_dp/)

  xnorm = 0.0_dp
  ynorm = 0.0_dp
  znorm = 1.0_dp
  area  = 1.0_dp

  rho_1         = q(1)
  unorm_1       = q(2)*xnorm + q(3)*ynorm + q(4)*znorm
  p_1           = q(5)
  p_3           = 0.99_dp*q(5)

  a_1           = sqrt(gamma * p_1 / rho_1)
  mach_1_sq     = unorm_1**2 / a_1**2
  tt_1          = a_1**2 * (1.0_dp + 0.5_dp*gm1*mach_1_sq)
  phi0          = 0.04137_dp / (0.0982+s)+ 0.57323_dp + 0.005786_dp*(1.0_dp-s)

  rho_unorm_sq  = (rho_1*unorm_1)**2

  !----------------------------------------------------------------
  inflow:  if ( unorm_1 > 0.0_dp ) then

    mach_3_sq   = mach_1_sq
    mach_factor = 1.0_dp + 0.5_dp * gm1
    phi         = phi0 + 0.185_dp*(s**0.25_dp)*(mach_factor**(gamma/gm1)-1.0_dp)

    mach_3_sq   = newton_inflow_mach_3( s, mach_1_sq, phi )
  
    write(6,'(a,f15.10,a)') 'assert_equal_within(',mach_3_sq,',mach_3_sq, tol)'
    assert_equal_within(0.0973978744,mach_3_sq, tol ) 

  !----------------------------------------------------------------
    mach_factor = 1.0_dp + 0.5_dp * gm1 * mach_3_sq
    rho_unorm_sq_m = gamma*gamma/tt_1*p_3*p_3*mach_3_sq*mach_factor
    if ( rho_unorm_sq > rho_unorm_sq_m ) then
      rho_unorm_sq = rho_unorm_sq_m
      mach_2_sq    = 1.0_dp
    else
      mach_3_sq = (sqrt(                                                      &
      1.0_dp + 2.0_dp * gm1/gamma/gamma*tt_1*rho_unorm_sq/p_3/p_3)-1.0_dp) / gm1
      cf            = rho_unorm_sq * tt_1 / ( gamma*gamma*p_3*p_3)
      mach_2_sq     = newton_inflow_mach_2( s, mach_3_sq, cf )

     write(6,'(a,f15.10,a)') 'assert_equal_within(',mach_2_sq,',mach_2_sq, tol)'
      assert_equal_within(   0.0009806334,mach_2_sq, tol)
    endif

  !----------------------------------------------------------------
    mach_factor = 1.0_dp + 0.5_dp * gm1 * mach_2_sq
    phi         = phi0 + 0.185_dp*(s**0.25_dp)*(mach_factor**(gamma/gm1)-1.0_dp)
    pt1         = p_3 * (( 1.0_dp+gamma*mach_3_sq)                            &
                /  ( 1.0_dp + gamma * mach_2_sq * phi * (1.0_dp-s) ))         &
                * mach_factor**(gamma/gm1)

    mach_1_sq   = mach_3_sq
    cf          = rho_unorm_sq * tt_1 / ( gamma*gamma * pt1*pt1 )
    mach_1_sq   = newton_inflow_mach_1( cf )

    write(6,'(a,f15.10,a)') 'assert_equal_within(',mach_1_sq,',mach_1_sq, tol)'

    assert_equal_within(   0.0001020069,mach_1_sq, tol)

    rho_unorm = sqrt(rho_unorm_sq)

    write(6,'(a,f15.10,a)') 'assert_equal_within(',rho_unorm,',rho_unorm, tol)'
    assert_equal_within(   0.0100000000,rho_unorm, tol)
  end if inflow

end test

!=============================================================================80
!=============================================================================80
test test_porous_residual

  use fluid,         only : setup_fluid_gamma

  real(dp), dimension(5) :: flux
  real(dp), dimension(5) :: q
  real(dp)               :: xnorm, ynorm, znorm, area, s, p_3

  call setup_fluid_gamma

  q     = (/1.0,0.,0.,0.01,0.714/)
  xnorm = 0.0_dp
  ynorm = 0.0_dp
  znorm = 1.0_dp
  area  = 1.0_dp
  s     = 0.5_dp
  p_3   = 0.95_dp

  flux = residual_porous_wall( q, xnorm, ynorm, znorm, area, s, p_3 )

    write(6,'(a,f15.10,a)') 'assert_equal_within(',flux(1),'_dp,flux(1), tol)'
    write(6,'(a,f15.10,a)') 'assert_equal_within(',flux(2),'_dp,flux(2), tol)'
    write(6,'(a,f15.10,a)') 'assert_equal_within(',flux(3),'_dp,flux(3), tol)'
    write(6,'(a,f15.10,a)') 'assert_equal_within(',flux(4),'_dp,flux(4), tol)'
    write(6,'(a,f15.10,a)') 'assert_equal_within(',flux(5),'_dp,flux(5), tol)'

    assert_equal_within(   0.0099999998_dp,flux(1), tol)
    assert_equal_within(   0.0000000000_dp,flux(2), tol)
    assert_equal_within(   0.0000000000_dp,flux(3), tol)
    assert_equal_within(   0.9500963149_dp,flux(4), tol)
    assert_equal_within(   0.0099957544_dp,flux(5), tol)

end test

!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80

end test_suite

#! /bin/bash -ue

export PATH=$TESTING_PATH:$PATH

project=inv_wing

for input_file in fun3d.nml ${project}.fgrid ${project}.mapbc ; do
  if test ! -e $input_file ; then ln -fs $SRCDIR/$input_file ; fi
done

echo CASE: coarse-grid, OM6 wing in Mach 0.7 inviscid air, 2 degrees AoA

$SRCDIR/step_01.sh > step_01.out

tol=0.5
r1_ref=0.2*10^-12
echo \ Verify that density residual is within $tol order of magnitude of $r1_ref
r1_new=$( tail -n1 ${project}_hist.dat | awk '{print $2}' | sed 's/[eE]/\*10\^/' | sed 's/+//' )
diff=$( echo "l($r1_new)/l(10) - l($r1_ref)/l(10)" | bc -l )
test $( echo "$diff > $tol || $diff < -$tol" | bc -l ) == '0' || \
  { echo "Failure: density residual ($r1_new) not within $tol order of magnitude of $r1_ref."; exit 1; }

tol=0.002
cl_ref=0.081
echo \ Verify that C_L is within $tol of $cl_ref
cl_new=$( tail -n1 ${project}_hist.dat | awk '{print $7}' | sed 's/[eE]/\*10\^/' | sed 's/+//' )
diff=$( echo "l($cl_new)/l(10) - l($cl_ref)/l(10)" | bc -l )
test $( echo "$diff > $tol || $diff < -$tol" | bc -l ) == '0' || \
  { echo "Failure: density residual ($cl_new) not within $tol of $cl_ref."; exit 1; }

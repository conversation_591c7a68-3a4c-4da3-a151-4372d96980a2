\section{Flow Solver, \nodet}

This chapter covers what is required to run an initial flow solution, 
how to restart a flow solution, 
and how to specify what outputs the solver \nodet produces.

\subsection{Flow Solver Execution}\label{s:flow-exec}

The grid and flow conditions are specified in the file
\file{fun3d.nml}; see \sectionref{s:fun3d.nml} for the file description. 
If you configured \FunThreeD without MPI, the executable is named
\file{nodet}.
If you configured \FunThreeD with MPI, the executable is named
\file{nodet_mpi}.
Configuration and installation is explained in detail
in \sectionref{c:installation}.
The executable \file{nodet} can be invoked directly from the 
command line, 
\begin{Verbatim}
  nodet [fun3d options]
\end{Verbatim}
but the MPI version \file{nodet_mpi} will need to be invoked 
within an MPI environment.
The most common method is via
\begin{Verbatim}
  [MPI run command] [MPI options] nodet_mpi [fun3d options]
\end{Verbatim}
The details of the MPI run command and MPI options will 
depend on the MPI implementation.
The MPI run command is commonly \file{mpirun} or \file{mpiexec}.
The MPI options may contain the number of processors \file{-np [n]},
a machine file \cmd{-machinefile [file]}, or no local \file{-nolocal}.
If a queuing system is used (e.g., PBS) this command will
need to be run inside an interactive job or a script. 
See your MPI documentation or system administrator 
to learn the details of your particular environment. 

If you have provided a grid with boundary conditions and \file{fun3d.nml}, 
you will then see the solver start to execute.
If an unexpected termination happens during execution,
especially during grid processing or the first iteration,
you may need to set your shell limits to unlimited,
\begin{Verbatim}
  $ ulimit unlimited # for bash
  $ unlimit          # for c shell
\end{Verbatim}
A detailed description of the output files is given below.

\subsection{Command Line Options}\label{s:clo}

These options are specified after the executable. 
The majority of the command line options are functionality under
development and there is work underway to migrate command line options to
namelists.
Namelists are the preferred input method.
Command line options
should be avoided unless they are the only way to activate the functionality
you require. 
These commands are always preceded by \cmd{--} (double minus). More than one
option may appear on the command line (each option proceeded by a \cmd{--} ). 
You can see a listing of the available command line options in any of 
the codes in the \FunThreeD suite by using the command line option 
\cmd{--help} after the executable name,

\begin{Verbatim}
  ./nodet_mpi --help
\end{Verbatim}

The options are then listed in alphabetical order, along with a short 
description and a list of any auxiliary parameters that might be needed, and 
then the code execution stops.
Specific examples of the use of command line options 
are found throughout this, and later, chapters.

\subsection{Output Files}

These are the output files produced by the flow solver, \nodet.

\subparagraph{\protect\file{[project_rootname].flow}}

This file contains the binary restart information and is read by the
solver for restart computations.
See the \var{restart_read} namelist variable in
\sectionref{s:nml_code_run_control}
to control restart behavior.

\subparagraph{\protect\file{[project_rootname]_hist.dat}}

This file contains the convergence history for the RMS residual, lift,
drag, moments, and CPU time, as well as the individual pressure and
viscous components of each force and moment.
The file is in \Tecplot format.
See \sectionref{s:nml_component_parameters} for an improved method
to track forces and moments.

\subparagraph{\protect\file{[project_rootname]_subhist.dat}}

For time accurate computations only.
This file contains the sub-iteration convergence history for the RMS
residuals.
The file is in \Tecplot format.

\subparagraph{\protect\file{[project_rootname].forces}}

This file contains a breakdown of all the forces and moments acting on
each individual boundary group.
The totals for the entire configuration are listed at the bottom.
See \sectionref{s:nml_component_parameters} for an improved method
to track forces and moments.

\subsubsection{Flow Visualization}\label{s:flowvis}
There are four basic categories of output: boundary data, 
sampling data (on entities such as planes, boxes and spheres), 
volumetric data, and slice data controlled by 
the namelists in \tab{t:solver_output_types}.

 \begin{table}[ht]
  \caption{Solver output types.}
  \label{t:solver_output_types}
  \begin{tabular}{lll}
    \textbf{Type} & \textbf{Namelist} \\
    \midrule
    domain boundaries  & \cmd{&boundary_output_variables} & 
    \sectionref{s:nml_boundary_output_variables} \\
    domain volume      & \cmd{&volume_output_variables} &
    \sectionref{s:nml_volume_output_variables} \\
    boundary slices    & \cmd{&slice_data} &
    \sectionref{s:nml_slice_data} \\
    various geometries & 
    \cmd{&sampling_parameters} and &
    \sectionref{s:nml_sampling_parameters} and \\
    &
    \cmd{&sampling_output_variables} &
    \sectionref{s:nml_sampling_output_variables} \\
    point              &
    \cmd{&sampling_parameters} and &
    \sectionref{s:nml_sampling_parameters} and \\
    &
    \cmd{&sampling_output_variables} &
    \sectionref{s:nml_sampling_output_variables} \\
  \end{tabular}
\end{table}

Each namelist has a corresponding frequency variable, 
A positive frequency will cause the output to be generated 
every frequency time step/iteration. 
A negative frequency will cause output to be written only 
at the end of a run. 
A zero frequency (the default) with produce no output.
See the corresponding namelist descriptions for details.

\subsubsection{Flow Visualization Output From Existing Solution}

If a \FunThreeD flow solution already exists, 
visualization files can be produced by setting \var{steps = 0} or 
\var{steps = 1} in the \var{&code_run_control} namelist
(\sectionref{s:nml_code_run_control}) and setting
the \var{restart_read} variable to something other than \var{'off'}. 
One iteration is required to compute gradient quantities (i.e., skin friction). 
This will allow generation of visualization output without having to repeat
the entire calculation.


module profiles

  use kinddefs, only : dp

  implicit none

  private

  public :: find_profiles, point_in_tet

  type profile_type
    integer :: npoints
    integer :: triangle_or_quad
    integer :: boundary_group
    integer :: face_number
    integer :: span_station,chrd_station

    integer, dimension(:), pointer :: element_type
    integer, dimension(:), pointer :: element_num
    integer, dimension(:), pointer :: sub_tet
    integer, dimension(:), pointer :: rank

    real(dp), dimension(:),   pointer :: x
    real(dp), dimension(:),   pointer :: y
    real(dp), dimension(:),   pointer :: z
    real(dp), dimension(:,:), pointer :: q
  end type profile_type

  type sgrid_type
    integer :: imax, jmax, kmax

    real(dp), dimension(:,:,:),   pointer :: x,y,z
    real(dp), dimension(:,:,:),   pointer :: xn,yn,zn,area
    real(dp), dimension(:,:,:,:), pointer :: q
  end type sgrid_type

  integer :: nprofiles_global, nspan, ngrid, nvalid_bcs, igeom

  integer, dimension(:), allocatable :: valid_bcs

  type(profile_type), dimension(:), pointer :: global_profile
  type(sgrid_type),   dimension(:), pointer :: sgrid

contains

!=============================== FIND_PROFILES ===============================80
!
!  If exists, load profile request, compute the profiles, and plot
!
!=============================================================================80
  subroutine find_profiles(grid,soln,flow_dir)

    use fun3d_constants,       only : my_0, my_3, my_half, my_2, my_180, pi
    use kinddefs,              only : dp
    use bc_names,              only : bc_used_for_force_calculation            &
                                    , bc_is_flow_through
    use grid_types,            only : grid_type
    use solution_types,        only : soln_type
    use lmpi,                  only : lmpi_die, lmpi_id, lmpi_nproc, lmpi_max, &
                                      lmpi_reduce, lmpi_bcast, lmpi_master,    &
                                      lmpi_max_and_maxid, lmpi_min
    use interpolate_utilities, only : point_lies_in_triangle, point_lies_in_quad
    use string_utils,          only : int_to_s
    use file_utils,            only : file_does_not_exist
    use thermo,                only : etop, ptoe
    use info_depr,             only : re
    use nml_governing_equations, only : viscous_terms

    character(*), intent(in) :: flow_dir

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln

    integer :: chord_dir, span_dir, nchord, mchord, i, j, ib, nraw
    integer ::  mraw, ind, ifc
    integer :: vertical_dir, iface, n1, n2, n3, n4, iraw, nfound, icell, ielem
    integer :: nprofiles_local, k, m, n5, n6, n7, n8, sub_tet, node
    integer :: owner, offset, temprank, proc_with_max, npoints, mask
    integer :: le_finder,nchrd_unif,nmraw,iraw_le

    integer, dimension(:), allocatable :: nprofiles_proc, temp
    integer, dimension(:), allocatable :: raw_span, raw_chrd
    integer, dimension(:), allocatable :: mindex_chrd,mindexall_chrd

    real(dp) :: minchord, maxchord, maxheight,dspan, minspan, maxspan
    real(dp) :: bndlyrthick,xarc,delta99,xminchrd
    real(dp) :: pstratio,qstretch,stretco,stretcn,totcoef,blcoef
    real(dp) :: blthickcoef,ver_stre
    real(dp) :: x1,x2,x3,x4,x5,x6,x7,x8,y1,y2,y3,y4,y5,y6,y7,y8,z1,z2,z3,z4
    real(dp) :: xtest,ytest,ztest,minx,miny,minz,maxx,maxy,maxz,xt,yt,zt,dist
    real(dp) :: vector1_x, vector1_y, vector1_z, z5, z6, z7, z8, xj, yj, zj
    real(dp) :: vector2_x, vector2_y, vector2_z, cell_vol, w1, w2, w3, w4
    real(dp) :: normal_x, normal_y, normal_z, delta, length, eta, mindist
    real(dp) :: a, b, c, d, ax, ay, az, bx, by, bz, xnorm, ynorm, znorm, area
    real(dp) :: rot_angle, xc_rot, yc_rot, zc_rot, x0, y0, z0, v1x, v1y, v1z
    real(dp) :: v2x, v2y, v2z, mask_value, big_mag, mag1, mag2, mag3


    real(dp), dimension(3)              :: big_area, area1, area2, area3
    real(dp), dimension(:), allocatable :: raw_x, raw_y, raw_z
    real(dp), dimension(:), allocatable :: xmin_chrd,xminall_chrd,xminall_chrd0
    real(dp), dimension(:), allocatable :: zmin_chrd,zmax_chrd
    real(dp), dimension(:), allocatable :: zmaxall_chrd0,zminall_chrd
    real(dp), dimension(:), allocatable :: zmaxall_chrd
    real(dp), dimension(:), allocatable :: dchord, ddchord,stachord
    real(dp), dimension(:,:), allocatable :: le_xyz

    logical :: found_face, is_candidate, found
    logical, parameter :: debug = .false.

    character(len=80) :: filename

    type(profile_type), dimension(:), pointer :: profile

  continue

! initialize to satisfy compiler complaint

    bndlyrthick = 0.0_dp

! Load profile input request if it exists, otherwise just return

    filename = trim(flow_dir) // 'profiles.input'

    if ( file_does_not_exist(filename) ) return

    open(82,file=filename,status='old')
    rewind(82)

    read(82,*) ! CHORD_DIR
    read(82,*) chord_dir, nchord, minchord, maxchord,pstratio
    read(82,*) ! SPAN_DIR
    read(82,*) span_dir, nspan, minspan, maxspan
    read(82,*) ! NPOINTS
    read(82,*) npoints, maxheight, rot_angle, blthickcoef, igeom
    read(82,*) ! MASK
    read(82,*) mask, mask_value, nvalid_bcs
    read(82,*) ! VALID_BCS
    if ( nvalid_bcs <= 0 ) then
      read(82,*) ! blank read; all bc's valid
      allocate(valid_bcs(grid%nbound)); valid_bcs = 0
      nvalid_bcs = grid%nbound
      do ib = 1, grid%nbound
        valid_bcs(ib) = ib
      end do
    else
      allocate(valid_bcs(nvalid_bcs)); valid_bcs = 0
      read(82,*) (valid_bcs(i), i = 1, nvalid_bcs)
    endif

    close(82)

! error checking on inputs

    if ( chord_dir == span_dir ) then
      write(*,*) 'Error: same direction specified for chord and span.'
      call lmpi_die
    endif

    if ( chord_dir /= 1 .and. chord_dir /= 2 .and. chord_dir /= 3 ) then
      write(*,*) 'Error: invalid chordwise direction.'
      call lmpi_die
    endif

    if ( span_dir /= 1 .and. span_dir /= 2 .and. span_dir /= 3 ) then
      write(*,*) 'Error: invalid spanwise direction.'
      call lmpi_die
    endif

    if ( maxchord < minchord ) then
      write(*,*) 'Error: maxchord <= minchord.'
      call lmpi_die
    endif

    if ( maxspan < minspan ) then
      write(*,*) 'Error: maxspan <= minspan.'
      call lmpi_die
    endif

! establish the "vertical" direction based on the chord and span directions

    vertical_dir = 0
    if ( (chord_dir == 1 .and. span_dir == 2) .or.                             &
         (chord_dir == 2 .and. span_dir == 1) ) vertical_dir = 3
    if ( (chord_dir == 2 .and. span_dir == 3) .or.                             &
         (chord_dir == 3 .and. span_dir == 2) ) vertical_dir = 1
    if ( (chord_dir == 3 .and. span_dir == 1) .or.                             &
         (chord_dir == 1 .and. span_dir == 3) ) vertical_dir = 2

! Generate a list of the raw base points requested

    if (nchord >= 0) then
        nchrd_unif=1
        mchord=0
    else
        nchrd_unif=0
        nchord=abs(nchord)
        mchord=11
    endif

    nraw = nchord*nspan
    mraw = mchord*nspan
    nmraw = max(nraw, mraw)

    allocate(raw_x(nmraw));    raw_x = my_0
    allocate(raw_y(nmraw));    raw_y = my_0
    allocate(raw_z(nmraw));    raw_z = my_0
    allocate(raw_span(nmraw)); raw_span = 0
    allocate(raw_chrd(nmraw)); raw_chrd = 0
    allocate(dchord(nspan));   dchord=my_0
    allocate(ddchord(nspan));  ddchord=my_0
    allocate(stachord(nspan)); stachord=my_0
    allocate(xmin_chrd(nspan)); xmin_chrd=my_0
    allocate(xminall_chrd(nspan)); xminall_chrd=my_0
    allocate(xminall_chrd0(nspan)); xminall_chrd0=minchord
    allocate(mindex_chrd(nspan)); mindex_chrd=0
    allocate(mindexall_chrd(nspan)); mindexall_chrd=0
    allocate(zmin_chrd(nspan)); zmin_chrd=my_0
    allocate(zmax_chrd(nspan)); zmax_chrd=my_0
    allocate(zmaxall_chrd(nspan)); zmaxall_chrd=my_0
    allocate(zminall_chrd(nspan)); zminall_chrd= my_0
    allocate(zmaxall_chrd0(nspan)); zmaxall_chrd0=my_0
    allocate(le_xyz(3,nspan)); le_xyz=my_0

    if ( nchord == 1 ) then
      dchord = my_0
    else
      dchord = (maxchord - minchord) / (nchord-1)
    endif

    if ( nspan == 1 ) then
      dspan = my_0
    else
      dspan  = (maxspan  - minspan ) / (nspan-1)
    endif

! Allocate arrays to hold any base points (and profiles) we find
! Assume we will find at most 2 per cartesian input point for now

    allocate(profile(nmraw*2))
    do i = 1, nmraw*2
      profile(i)%npoints = npoints
      allocate(profile(i)%element_type(npoints)); profile(i)%element_type = 0
      allocate(profile(i)%element_num(npoints));  profile(i)%element_num  = 0
      allocate(profile(i)%sub_tet(npoints));      profile(i)%sub_tet      = 0
      allocate(profile(i)%rank(npoints));         profile(i)%rank         = 0
      allocate(profile(i)%x(npoints));            profile(i)%x = my_0
      allocate(profile(i)%y(npoints));            profile(i)%y = my_0
      allocate(profile(i)%z(npoints));            profile(i)%z = my_0
      allocate(profile(i)%q(soln%ndim+1,npoints));profile(i)%q = my_0
    end do


! set up chordwise direction


!=== Below added by Wei to locate the leading edge at each station =============

!    write(*,*)

    stachord = minchord

    nonunif_distri : if (nchrd_unif==0) then

      ddchord = (maxchord - minchord) / (mchord-1)
      le_finder = 0

      le_detect :  do

        ind=0
        do i = 1, nmraw*2
           profile(i)%chrd_station = 10*mchord
        enddo

        select case(chord_dir)

        case(1)
          do i = 1, nspan
            do j = 1, mchord
               ind = ind + 1
               raw_x(ind) = stachord(i) + (j-1)*ddchord(i)
               raw_chrd(ind) = j
               raw_span(ind) = i
            end do
          end do
        case(2)
          do i = 1, nspan
            do j = 1, mchord
               ind = ind + 1
               raw_y(ind) = stachord(i) + (j-1)*ddchord(i)
               raw_chrd(ind) = j
               raw_span(ind) = i
            end do
          end do
        case(3)
          do i = 1, nspan
            do j = 1, mchord
               ind = ind + 1
               raw_z(ind) = stachord(i) + (j-1)*ddchord(i)
               raw_chrd(ind) = j
               raw_span(ind) = i
            end do
          end do
        case default
          write(*,*) 'Unknown chord_dir parameter.'
          call lmpi_die
        end select

! set up spanwise direction

        ind = 0
        select case(span_dir)

        case(1)
          do i = 1, nspan
            do j = 1, mchord
               ind = ind + 1
               raw_x(ind) = minspan + (i-1)*dspan
            end do
          end do
        case(2)
          do i = 1, nspan
            do j = 1, mchord
               ind = ind + 1
               raw_y(ind) = minspan + (i-1)*dspan
            end do
          end do
        case(3)
          do i = 1, nspan
            do j = 1, mchord
               ind = ind + 1
               raw_z(ind) = minspan + (i-1)*dspan
            end do
          end do
        case default
          write(*,*) 'Unknown span_dir parameter.'
          call lmpi_die
        end select

! perform any requested rotation of the cartesian grid
! No need for general profiles


! Now go find the surface elements which contain the raw points

        if ( debug )                                                           &
           filename = 'basepoints.' // trim(int_to_s(lmpi_id+1)) // '.dat'

        nfound = 0
        bound_loop0 : do ib = 1, grid%nbound
          if ( bc_is_flow_through(grid%bc(ib)%ibc)) cycle
          solid_boundary0 : if (bc_used_for_force_calculation(grid%bc(ib)%ibc) &
                               .and. validity_check(ib) ) then

! search the triangles


            tria_loop0 : do iface = 1, grid%bc(ib)%nbfacet
              local_tria0 : if ( grid%bc(ib)%face_bit(iface) == 1 ) then
                n1 = grid%bc(ib)%f2ntb(iface,1)
                n2 = grid%bc(ib)%f2ntb(iface,2)
                n3 = grid%bc(ib)%f2ntb(iface,3)
                n1 = grid%bc(ib)%ibnode(n1)
                n2 = grid%bc(ib)%ibnode(n2)
                n3 = grid%bc(ib)%ibnode(n3)

                icell = grid%bc(ib)%f2ntb(iface,4)
                ielem = grid%bc(ib)%f2ntb(iface,5)

                x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3)
                y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3)
                z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3)

                minx = min(x1,x2,x3); miny = min(y1,y2,y3); minz = min(z1,z2,z3)
                maxx = max(x1,x2,x3); maxy = max(y1,y2,y3); maxz = max(z1,z2,z3)

                raw10 : do iraw = 1, mraw

                  xtest = raw_x(iraw)
                  ytest = raw_y(iraw)
                  ztest = raw_z(iraw)

! first determine if the point is within the min and max coords of the triangle
! if so, it is a candidate
! determine the third coordinate such that the point lies in the plane of the
! triangle
! use the point_in_triangle test to make final determination
! if point is a valid base point, store it off

                  vector1_x = x2 - x1
                  vector1_y = y2 - y1
                  vector1_z = z2 - z1

                  vector2_x = x3 - x1
                  vector2_y = y3 - y1
                  vector2_z = z3 - z1

                  normal_x = vector1_y*vector2_z - vector2_y*vector1_z
                  normal_y = vector2_x*vector1_z - vector1_x*vector2_z
                  normal_z = vector1_x*vector2_y - vector2_x*vector1_y

! coefficients of plane equation Ax + By + Cz + D = 0
! are as follows

                  a = normal_x
                  b = normal_y
                  c = normal_z
                  d = - ( a*x1 + b*y1 + c*z1 )

                  is_candidate = .false.
                  select case(vertical_dir)
                  case(1)
                    if ( (ytest <= maxy .and. ytest >= miny) .and.             &
                         (ztest <= maxz .and. ztest >= minz) )                 &
                          is_candidate = .true.
                    if ( is_candidate ) then
                       xtest = - (b*ytest + c*ztest + d) / a
                       raw_x(iraw) = xtest
                    endif
                  case(2)
                    if ( (xtest <= maxx .and. xtest >= minx) .and.             &
                         (ztest <= maxz .and. ztest >= minz) )                 &
                          is_candidate = .true.
                    if ( is_candidate ) then
                       ytest = - (a*xtest + c*ztest + d) / b
                       raw_y(iraw) = ytest
                    endif
                  case(3)
                    if ( (xtest <= maxx .and. xtest >= minx) .and.             &
                         (ytest <= maxy .and. ytest >= miny) )                 &
                          is_candidate = .true.
                    if ( is_candidate ) then
                       ztest = - (a*xtest + b*ytest + d) / c
                       raw_z(iraw) = ztest
                    endif
                  case default
                    write(*,*) 'Error: vertical_dir set incorrectly:',         &
                                vertical_dir
                    call lmpi_die
                  end select

                  if ( is_candidate ) then
                     found_face =                                              &
                     point_lies_in_triangle(x1,y1,z1,x2,y2,z2,x3,y3,z3,        &
                                                    xtest,ytest,ztest)
                  else
                     found_face = .false.
                  endif

! Avoid any masked regions
                  if ( found_face .and. mask /= 0 ) then
                    select case(vertical_dir)
                    case(1)
                      if ( mask < 0 .and. xtest <= mask_value ) cycle raw10
                      if ( mask > 0 .and. xtest >= mask_value ) cycle raw10
                    case(2)
                      if ( mask < 0 .and. ytest <= mask_value ) cycle raw10
                      if ( mask > 0 .and. ytest >= mask_value ) cycle raw10
                    case(3)
                      if ( mask < 0 .and. ztest <= mask_value ) cycle raw10
                      if ( mask > 0 .and. ztest >= mask_value ) cycle raw10
                    case default
                      write(*,*)'Error: vertical_dir set incorrectly',         &
                                 vertical_dir
                      call lmpi_die
                    end select
                  endif

                  legit_face10 : if ( found_face ) then
                    nfound = nfound + 1
!    store base point information
                    if ( nfound <= 2*mraw ) then
                       profile(nfound)%span_station     = raw_span(iraw)
                       profile(nfound)%chrd_station     = raw_chrd(iraw)
                       profile(nfound)%rank(1)          = lmpi_id+1
                       profile(nfound)%x(1) = xtest
                       profile(nfound)%y(1) = ytest
                       profile(nfound)%z(1) = ztest
                    endif
                  endif legit_face10

                end do raw10

              endif local_tria0

!          write(*,*) 'check if tria_loops0 works or not,----454'

            end do tria_loop0

!    search the quads

            quad_loop0 : do iface = 1, grid%bc(ib)%nbfaceq
              local_quad0 : if ( grid%bc(ib)%face_bitq(iface) == 1 ) then
                n1 = grid%bc(ib)%f2nqb(iface,1)
                n2 = grid%bc(ib)%f2nqb(iface,2)
                n3 = grid%bc(ib)%f2nqb(iface,3)
                n4 = grid%bc(ib)%f2nqb(iface,4)
                n1 = grid%bc(ib)%ibnode(n1)
                n2 = grid%bc(ib)%ibnode(n2)
                n3 = grid%bc(ib)%ibnode(n3)
                n4 = grid%bc(ib)%ibnode(n4)

                icell = grid%bc(ib)%f2nqb(iface,5)
                ielem = grid%bc(ib)%f2nqb(iface,6)

                x1 = grid%x(n1); x2 = grid%x(n2)
                x3 = grid%x(n3); x4 = grid%x(n4)
                y1 = grid%y(n1); y2 = grid%y(n2)
                y3 = grid%y(n3); y4 = grid%y(n4)
                z1 = grid%z(n1); z2 = grid%z(n2)
                z3 = grid%z(n3); z4 = grid%z(n4)

                minx=min(x1,x2,x3,x4); miny=min(y1,y2,y3,y4)
                minz=min(z1,z2,z3,z4)
                maxx=max(x1,x2,x3,x4); maxy=max(y1,y2,y3,y4)
                maxz=max(z1,z2,z3,z4)

                raw20 : do iraw = 1, mraw

                  xtest = raw_x(iraw)
                  ytest = raw_y(iraw)
                  ztest = raw_z(iraw)

! first determine if the point is within the min and max coords of the quad
! if so, it is a candidate
! determine the third coordinate such that the point lies in the plane of the
! quad
! use the point_in_quad test to make final determination
! if point is a valid base point, store it off

                  xnorm = my_half*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
                  ynorm = my_half*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
                  znorm = my_half*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )

! coefficients of plane equation Ax + By + Cz + D = 0
! are as follows

                  a = xnorm
                  b = ynorm
                  c = znorm
                  d = - ( a*x1 + b*y1 + c*z1 )

                  is_candidate = .false.

                  select case(vertical_dir)
                  case(1)
                    if ( (ytest <= maxy .and. ytest >= miny) .and.             &
                         (ztest <= maxz .and. ztest >= minz) )                 &
                          is_candidate = .true.
                    if ( is_candidate ) then
                       xtest = - (b*ytest + c*ztest + d) / a
                       raw_x(iraw) = xtest
                    endif
                  case(2)
                    if ( (xtest <= maxx .and. xtest >= minx) .and.             &
                         (ztest <= maxz .and. ztest >= minz) )                 &
                          is_candidate = .true.
                    if ( is_candidate ) then
                       ytest = - (a*xtest + c*ztest + d) / b
                       raw_y(iraw) = ytest
                    endif
                  case(3)
                    if ( (xtest <= maxx .and. xtest >= minx) .and.             &
                         (ytest <= maxy .and. ytest >= miny) )                 &
                          is_candidate = .true.
                    if ( is_candidate ) then
                       ztest = - (a*xtest + b*ytest + d) / c
                       raw_z(iraw) = ztest
                    endif
                  case default
                    write(*,*) 'Error: vertical_dir set incorrectly:',         &
                                vertical_dir
                    call lmpi_die
                  end select

                  if ( is_candidate ) then
!                   write (*,*) le_finder,is_candidate, xtest,ytest,ztest
                    found_face =                                               &
                                 point_lies_in_quad(x1,y1,z1,x2,y2,z2,x3,y3,z3,&
                                                x4,y4,z4,xtest,ytest,ztest)
                  else
                    found_face = .false.
                  endif

! Avoid any masked regions
                  if ( found_face .and. mask /= 0 ) then
                    select case(vertical_dir)
                    case(1)
                      if ( mask < 0 .and. xtest <= mask_value ) cycle raw20
                      if ( mask > 0 .and. xtest >= mask_value ) cycle raw20
                    case(2)
                      if ( mask < 0 .and. ytest <= mask_value ) cycle raw20
                      if ( mask > 0 .and. ytest >= mask_value ) cycle raw20
                    case(3)
                      if ( mask < 0 .and. ztest <= mask_value ) cycle raw20
                      if ( mask > 0 .and. ztest >= mask_value ) cycle raw20
                    case default
                      write(*,*)'Error: vertical_dir set incorrectly',         &
                                 vertical_dir
                      call lmpi_die
                    end select
                  endif

                  legit_face20 : if ( found_face ) then
                    nfound = nfound + 1
!                if ( debug ) write(92,*) xtest,ytest,ztest
! store base point information
                    if ( nfound <= mraw ) then
                      profile(nfound)%span_station     = raw_span(iraw)
                      profile(nfound)%chrd_station     = raw_chrd(iraw)
                      profile(nfound)%rank(1)          = lmpi_id+1
                      profile(nfound)%x(1) = xtest
                      profile(nfound)%y(1) = ytest
                      profile(nfound)%z(1) = ztest
                    endif
                  endif legit_face20

                end do raw20

              endif local_quad0

            end do quad_loop0

          endif solid_boundary0

        end do bound_loop0

!    if ( debug ) close(92)

        nprofiles_local = nfound

        mindex_chrd = mchord+1  !Just initialize it using a big integer
        xmin_chrd = maxchord   !Just initialize it using a big real number
        zmax_chrd = -1.e9
        zmin_chrd =  1.e9

        do j = 1, nspan
          do i = 1, nprofiles_local
             if (profile(i)%span_station == j) then
               if (profile(i)%x(1) < xmin_chrd(j)) then
!Here it is requested I and x in the same direction
                  mindex_chrd(j)=profile(i)%chrd_station
                  xmin_chrd(j)=profile(i)%x(1)
                  zmax_chrd(j)=profile(i)%z(1)
                  zmin_chrd(j)=profile(i)%z(1)
               endif
             endif
          enddo
          call lmpi_min(mindex_chrd(j),mindexall_chrd(j))
          call lmpi_min(xmin_chrd(j),xminall_chrd(j))
          call lmpi_min(zmin_chrd(j),zminall_chrd(j))
          call lmpi_max(zmax_chrd(j),zmaxall_chrd(j))

!       if (lmpi_master) then
!          write(*,*) 'node=',lmpi_id, &
!          'ispan=',j,zminall_chrd(j),zmaxall_chrd(j),'---599'
!       endif
        enddo

! Now we need to synch up the profiles across processors

        call lmpi_bcast(mindexall_chrd)
        call lmpi_bcast(xminall_chrd)
        call lmpi_bcast(zminall_chrd)
        call lmpi_bcast(zmaxall_chrd)

        xminchrd=0.0_dp
        do j=1,nspan
          xminchrd=max(xminchrd,abs(xminall_chrd(j)-xminall_chrd0(j)))
        enddo

        le_finder=le_finder+1
        if (lmpi_master) write (*,*) 'The ',                                   &
                         le_finder,'iteration to locate LE for profile data:', &
                         'xminchord=', xminchrd

        if (xminchrd < 1.e-6_dp) exit

        xminall_chrd0=xminall_chrd
        zmaxall_chrd0=zmaxall_chrd

        do j=1,nspan
!Here assume x is in approximate streamwise direction?
           stachord(j)=raw_x((j-1)*mchord+mindexall_chrd(j)-1)
           ddchord(j)=ddchord(j)/(mchord-1)
        enddo

      enddo le_detect

!    if ( lmpi_master ) write(*,*)                                             &
!      'x-positions of leading edges have been located: ',                     &
!       nspan,(xminall_chrd(j),j=1,nspan)


! Comparing zmaxall_chrd and zmaxall_chrd0 to check which side of wing
! is considered here. Then the z-value of leading edges should be calculated
! and stored:
! zmaxall_chrd < zmaxall_chrd0:
!       Upper side, so z-value of the LE has the smallest value
! zmaxall_chrd > zmaxall_chrd0:
!       Lower side, so z-value of the LE has the largest value

      stachord=xminall_chrd

      do j=1,nspan
         le_xyz(1,j)=stachord(j)
         le_xyz(2,j)=raw_y((j-1)*mchord+mindexall_chrd(j))
         if (zmaxall_chrd(j)>zmaxall_chrd0(j)) le_xyz(3,j)=zmaxall_chrd(j)
         if (zmaxall_chrd(j)<=zmaxall_chrd0(j)) le_xyz(3,j)=zminall_chrd(j)
      enddo

      if ( lmpi_master ) then
         write(*,*) 'x-positions of leading edges have been located: ',        &
                                   nspan,(le_xyz(1,j),j=1,nspan)
         write(*,*) 'z-positions of leading edges have been located: ',        &
                                   nspan,(le_xyz(3,j),j=1,nspan)
      endif

      deallocate(ddchord,xmin_chrd,xminall_chrd0,mindex_chrd,mindexall_chrd)
      deallocate(zmaxall_chrd,zminall_chrd,zmaxall_chrd0)

    endif nonunif_distri

!=================== added by Wei Liao above=================================

! ===========================================================================
! A stretching function is used for stability grid in chordwise direction.
! Modified by Wei Liao on 11/17/2010.

! Here "pstratio" is an input parameter controling the stretching ratio
! in x-dierction. pstratio=1: even distribution; pstratio->2:larger
! stretching ratio.

!    pstratio=1.92 !uniform with 1 and 1.9... for stretching more
    qstretch=2.0

    dchord = my_0

!    if ( nchord == 1 ) then
!      dchord = my_0
!    else
!      do i=1,nspan
!        dchord(i) = (maxchord - stachord(i)) / (nchord-1)
!      enddo
!    endif

    ind = 0
    select case(chord_dir)
    case(1)
      do i = 1, nspan
        do j = 1, nchord
          ind = ind + 1
          stretco=1.0*(nchord-j)/(nchord-1)
          stretcn=1.0-tanh(qstretch*(1.-stretco))/tanh(qstretch)
          totcoef=pstratio*stretco+(1.-pstratio)*stretcn
          raw_x(ind) = maxchord + totcoef*(stachord(i)-maxchord)
!          raw_x(ind) = stachord(i) + (j-1)*dchord(i)
          raw_chrd(ind) = j
          raw_span(ind) = i
        end do
      end do
    case(2)
      do i = 1, nspan
        do j = 1, nchord
          ind = ind + 1
          stretco=1.0*(nchord-j)/(nchord-1)
          stretcn=1.0-tanh(qstretch*(1.-stretco))/tanh(qstretch)
          totcoef=pstratio*stretco+(1.-pstratio)*stretcn
          raw_y(ind) = maxchord + totcoef*(stachord(i)-maxchord)
!          raw_y(ind) = stachord(i) + (j-1)*dchord(i)
          raw_chrd(ind) = j
          raw_span(ind) = i
        end do
      end do
    case(3)
      do i = 1, nspan
        do j = 1, nchord
          ind = ind + 1

          stretco=1.0*(nchord-j)/(nchord-1)
          stretcn=1.0-tanh(qstretch*(1.-stretco))/tanh(qstretch)
          totcoef=pstratio*stretco+(1.-pstratio)*stretcn
          raw_z(ind) = maxchord + totcoef*(stachord(i)-maxchord)
!          raw_z(ind) = stachord(i) + (j-1)*dchord(i)
          raw_chrd(ind) = j
          raw_span(ind) = i
        end do
      end do
    case default
      write(*,*) 'Unknown chord_dir parameter.'
      call lmpi_die
    end select
! ==============================================================================

! set up spanwise direction

    ind = 0
    select case(span_dir)
    case(1)
      do i = 1, nspan
        do j = 1, nchord
          ind = ind + 1
          raw_x(ind) = minspan + (i-1)*dspan
        end do
      end do
    case(2)
      do i = 1, nspan
        do j = 1, nchord
          ind = ind + 1
          raw_y(ind) = minspan + (i-1)*dspan
        end do
      end do
    case(3)
      do i = 1, nspan
        do j = 1, nchord
          ind = ind + 1
          raw_z(ind) = minspan + (i-1)*dspan
        end do
      end do
    case default
      write(*,*) 'Unknown span_dir parameter.'
      call lmpi_die
    end select

! perform any requested rotation of the cartesian grid

    rot_angle = rot_angle * pi / my_180

    select case(vertical_dir)
    case(1)
      yc_rot = (minchord + maxchord) / my_2
      zc_rot = (minspan  + maxspan ) / my_2
      do i = 1, nraw
        y0 = raw_y(i)
        z0 = raw_z(i)
        v1y = y0 - yc_rot
        v1z = z0 - zc_rot
        v2y = v1y*cos(rot_angle) - v1z*sin(rot_angle)
        v2z = v1y*sin(rot_angle) + v1z*cos(rot_angle)
        raw_y(i) = yc_rot + v2y
        raw_z(i) = zc_rot + v2z
      end do
    case(2)
      xc_rot = (minchord + maxchord) / my_2
      zc_rot = (minspan  + maxspan ) / my_2
      do i = 1, nraw
        x0 = raw_x(i)
        z0 = raw_z(i)
        v1x = x0 - xc_rot
        v1z = z0 - zc_rot
        v2x = v1x*cos(rot_angle) - v1z*sin(rot_angle)
        v2z = v1x*sin(rot_angle) + v1z*cos(rot_angle)
        raw_x(i) = xc_rot + v2x
        raw_z(i) = zc_rot + v2z
      end do
    case(3)
      xc_rot = (minchord + maxchord) / my_2
      yc_rot = (minspan  + maxspan ) / my_2
      do i = 1, nraw
        x0 = raw_x(i)
        y0 = raw_y(i)
        v1x = x0 - xc_rot
        v1y = y0 - yc_rot
        v2x = v1x*cos(rot_angle) - v1y*sin(rot_angle)
        v2y = v1x*sin(rot_angle) + v1y*cos(rot_angle)
        raw_x(i) = xc_rot + v2x
        raw_y(i) = yc_rot + v2y
      end do
    case default
      write(*,*) 'Unknown vertical_dir parameter.'
      call lmpi_die
    end select

! Now go find the surface elements which contain the raw points

    if ( debug ) filename = 'basepoints.' // trim(int_to_s(lmpi_id+1)) // '.dat'

    nfound = 0
    bound_loop : do ib = 1, grid%nbound
      if ( bc_is_flow_through(grid%bc(ib)%ibc)) cycle
      solid_boundary : if (bc_used_for_force_calculation(grid%bc(ib)%ibc) .and.&
                           validity_check(ib) ) then

! search the triangles

        tria_loop : do iface = 1, grid%bc(ib)%nbfacet
          local_tria : if ( grid%bc(ib)%face_bit(iface) == 1 ) then
            n1 = grid%bc(ib)%f2ntb(iface,1)
            n2 = grid%bc(ib)%f2ntb(iface,2)
            n3 = grid%bc(ib)%f2ntb(iface,3)
            n1 = grid%bc(ib)%ibnode(n1)
            n2 = grid%bc(ib)%ibnode(n2)
            n3 = grid%bc(ib)%ibnode(n3)

            icell = grid%bc(ib)%f2ntb(iface,4)
            ielem = grid%bc(ib)%f2ntb(iface,5)

            x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3)
            y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3)
            z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3)

            minx = min(x1,x2,x3); miny = min(y1,y2,y3); minz = min(z1,z2,z3)
            maxx = max(x1,x2,x3); maxy = max(y1,y2,y3); maxz = max(z1,z2,z3)

            raw1 : do iraw = 1, nraw

              xtest = raw_x(iraw)
              ytest = raw_y(iraw)
              ztest = raw_z(iraw)

! first determine if the point is within the min and max coords of the triangle
! if so, it is a candidate
! determine the third coordinate such that the point lies in the plane of the
! triangle
! use the point_in_triangle test to make final determination
! if point is a valid base point, store it off

              vector1_x = x2 - x1
              vector1_y = y2 - y1
              vector1_z = z2 - z1

              vector2_x = x3 - x1
              vector2_y = y3 - y1
              vector2_z = z3 - z1

              normal_x = vector1_y*vector2_z - vector2_y*vector1_z
              normal_y = vector2_x*vector1_z - vector1_x*vector2_z
              normal_z = vector1_x*vector2_y - vector2_x*vector1_y

! coefficients of plane equation Ax + By + Cz + D = 0
! are as follows

              a = normal_x
              b = normal_y
              c = normal_z
              d = - ( a*x1 + b*y1 + c*z1 )

              is_candidate = .false.
              select case(vertical_dir)
              case(1)
                if ( (ytest <= maxy .and. ytest >= miny) .and.                 &
                     (ztest <= maxz .and. ztest >= minz) ) is_candidate = .true.
                if ( is_candidate ) xtest = - (b*ytest + c*ztest + d) / a
              case(2)
                if ( (xtest <= maxx .and. xtest >= minx) .and.                 &
                     (ztest <= maxz .and. ztest >= minz) ) is_candidate = .true.
                if ( is_candidate ) ytest = - (a*xtest + c*ztest + d) / b
              case(3)
                if ( (xtest <= maxx .and. xtest >= minx) .and.                 &
                     (ytest <= maxy .and. ytest >= miny) ) is_candidate = .true.
                if ( is_candidate ) ztest = - (a*xtest + b*ytest + d) / c
              case default
                write(*,*) 'Error: vertical_dir set incorrectly:', vertical_dir
                call lmpi_die
              end select

              if ( is_candidate ) then
                found_face = point_lies_in_triangle(x1,y1,z1,x2,y2,z2,x3,y3,z3,&
                                                    xtest,ytest,ztest)
              else
                found_face = .false.
              endif

              if ( found_face .and. mask /= 0 ) then  ! Avoid any masked regions
                select case(vertical_dir)
                case(1)
                  if ( mask < 0 .and. xtest <= mask_value ) cycle raw1
                  if ( mask > 0 .and. xtest >= mask_value ) cycle raw1
                case(2)
                  if ( mask < 0 .and. ytest <= mask_value ) cycle raw1
                  if ( mask > 0 .and. ytest >= mask_value ) cycle raw1
                case(3)
                  if ( mask < 0 .and. ztest <= mask_value ) cycle raw1
                  if ( mask > 0 .and. ztest >= mask_value ) cycle raw1
                case default
                  write(*,*)'Error: vertical_dir set incorrectly',vertical_dir
                  call lmpi_die
                end select
              endif

              legit_face1 : if ( found_face ) then
                nfound = nfound + 1
                if ( nfound == 1 .and. debug ) then
                  open(92,file=filename)
                  rewind(92)
                  write(92,*) 'variables="x  ","y  ","z  "'
                  write(92,*) 'zone'
                endif
                if ( debug ) write(92,*) xtest,ytest,ztest
                if ( nfound <= 2*nraw ) then

! store base point information

                  profile(nfound)%triangle_or_quad = 1
                  profile(nfound)%boundary_group   = ib
                  profile(nfound)%face_number      = iface
                  profile(nfound)%span_station     = raw_span(iraw)
                  profile(nfound)%chrd_station     = raw_chrd(iraw)
                  profile(nfound)%element_type(1)  = ielem
                  profile(nfound)%element_num(1)   = icell
                  profile(nfound)%sub_tet(1)       = 1
                  profile(nfound)%rank(1)          = lmpi_id+1
                  profile(nfound)%x(1) = xtest
                  profile(nfound)%y(1) = ytest
                  profile(nfound)%z(1) = ztest

! construct outward-pointing surface unit normal

                  ax = x2 - x1
                  ay = y2 - y1
                  az = z2 - z1

                  bx = x3 - x1
                  by = y3 - y1
                  bz = z3 - z1

                  xnorm =  my_half*(ay*bz-az*by)/my_3
                  ynorm = -my_half*(ax*bz-az*bx)/my_3
                  znorm =  my_half*(ax*by-ay*bx)/my_3

                  area = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)

                  xnorm = xnorm / area
                  ynorm = ynorm / area
                  znorm = znorm / area

! find locations of off-surface points to be used in the current profile


                  if (nchrd_unif==1) then
                     delta = maxheight / (profile(nfound)%npoints-1)
                  else
! ======================================================================
! Using Delta99=5*sqrt(x/Re_unit) to estimate the boundary layer thickness.
! Added by Wei Liao in 11/2010. Here "blthickcoef" is an input parameter.

!                     iraw_le=(raw_span(iraw)-1)*nchord+1
!                     xarc=sqrt((xtest-raw_x(iraw_le))**2+                     &
!                               (ytest-raw_y(iraw_le))**2+                     &
!                               (ztest-raw_z(iraw_le))**2)

                     iraw_le=raw_span(iraw)
                     xarc=sqrt((xtest-le_xyz(1,iraw_le))**2+                   &
                               (ytest-le_xyz(2,iraw_le))**2+                   &
                               (ztest-le_xyz(3,iraw_le))**2)

                     delta99= 5.d0*sqrt(xarc/re)

                     if (igeom==0) then
                        bndlyrthick=blthickcoef*max(maxheight,delta99)
                     else
                        blcoef = 1.d0+1.5d0*raw_chrd(iraw)/nchord
                        bndlyrthick=blthickcoef*blcoef*max(maxheight,delta99)
                     endif

                     delta = bndlyrthick / (profile(nfound)%npoints-1)

!            write(*,*) xarc,xtest,ytest,ztest,iraw_le,le_xyz(1,iraw_le),   &
!                       le_xyz(2,iraw_le),le_xyz(3,iraw_le),raw_span(iraw), &
!                       raw_chrd(iraw),re,'-1'

! =======================================================================
                  endif

                  ver_stre=1.6d0

                  do i = 2, profile(nfound)%npoints

!                    length = (i-1)*delta
                    stretco=1.0d0*(profile(nfound)%npoints-i)/                 &
                                  (profile(nfound)%npoints-1)
                    stretcn=1.0d0-tanh(qstretch*(1.d0-stretco))/tanh(qstretch)
                    totcoef=ver_stre*stretco+(1.d0-ver_stre)*stretcn
                    length = bndlyrthick - totcoef*bndlyrthick

                    profile(nfound)%x(i) = profile(nfound)%x(1) + length*xnorm
                    profile(nfound)%y(i) = profile(nfound)%y(1) + length*ynorm
                    profile(nfound)%z(i) = profile(nfound)%z(1) + length*znorm
                    if ( debug ) write(92,*) profile(nfound)%x(i),             &
                                             profile(nfound)%y(i),             &
                                             profile(nfound)%z(i)
                  end do

                else
                  write(*,*) 'Error: Found more points than assumed we would...'
                  call lmpi_die
                endif
              endif legit_face1

            end do raw1

          endif local_tria

        end do tria_loop

! search the quads

        quad_loop : do iface = 1, grid%bc(ib)%nbfaceq
          local_quad : if ( grid%bc(ib)%face_bitq(iface) == 1 ) then
            n1 = grid%bc(ib)%f2nqb(iface,1)
            n2 = grid%bc(ib)%f2nqb(iface,2)
            n3 = grid%bc(ib)%f2nqb(iface,3)
            n4 = grid%bc(ib)%f2nqb(iface,4)
            n1 = grid%bc(ib)%ibnode(n1)
            n2 = grid%bc(ib)%ibnode(n2)
            n3 = grid%bc(ib)%ibnode(n3)
            n4 = grid%bc(ib)%ibnode(n4)

            icell = grid%bc(ib)%f2nqb(iface,5)
            ielem = grid%bc(ib)%f2nqb(iface,6)

            x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3); x4 = grid%x(n4)
            y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3); y4 = grid%y(n4)
            z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3); z4 = grid%z(n4)

            minx=min(x1,x2,x3,x4); miny=min(y1,y2,y3,y4); minz=min(z1,z2,z3,z4)
            maxx=max(x1,x2,x3,x4); maxy=max(y1,y2,y3,y4); maxz=max(z1,z2,z3,z4)

            raw2 : do iraw = 1, nraw

              xtest = raw_x(iraw)
              ytest = raw_y(iraw)
              ztest = raw_z(iraw)

! first determine if the point is within the min and max coords of the quad
! if so, it is a candidate
! determine the third coordinate such that the point lies in the plane of the
! quad
! use the point_in_quad test to make final determination
! if point is a valid base point, store it off

              xnorm = my_half*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
              ynorm = my_half*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
              znorm = my_half*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )

! coefficients of plane equation Ax + By + Cz + D = 0
! are as follows

              a = xnorm
              b = ynorm
              c = znorm
              d = - ( a*x1 + b*y1 + c*z1 )

              is_candidate = .false.
              select case(vertical_dir)
              case(1)
                if ( (ytest <= maxy .and. ytest >= miny) .and.                 &
                     (ztest <= maxz .and. ztest >= minz) ) is_candidate = .true.
                if ( is_candidate ) then  !modifed by Wei to reset (raw_x())
                   xtest = - (b*ytest + c*ztest + d) / a
                   raw_x(iraw)=xtest
                endif
              case(2)
                if ( (xtest <= maxx .and. xtest >= minx) .and.                 &
                     (ztest <= maxz .and. ztest >= minz) ) is_candidate = .true.
                if ( is_candidate ) then  !modifed by Wei to reset (raw_y())
                   ytest = - (a*xtest + c*ztest + d) / b
                   raw_y(iraw)=ytest
                endif
              case(3)
                if ( (xtest <= maxx .and. xtest >= minx) .and.                 &
                     (ytest <= maxy .and. ytest >= miny) ) is_candidate = .true.
                if ( is_candidate ) then  !modifed by Wei to reset (raw_z())
                   ztest = - (a*xtest + b*ytest + d) / c
                   raw_z(iraw)=ztest
                endif
              case default
                write(*,*) 'Error: vertical_dir set incorrectly:', vertical_dir
                call lmpi_die
              end select

              if ( is_candidate ) then
                found_face = point_lies_in_quad(x1,y1,z1,x2,y2,z2,x3,y3,z3,    &
                                                x4,y4,z4,xtest,ytest,ztest)
              else
                found_face = .false.
              endif

              if ( found_face .and. mask /= 0 ) then  ! Avoid any masked regions
                select case(vertical_dir)
                case(1)
                  if ( mask < 0 .and. xtest <= mask_value ) cycle raw2
                  if ( mask > 0 .and. xtest >= mask_value ) cycle raw2
                case(2)
                  if ( mask < 0 .and. ytest <= mask_value ) cycle raw2
                  if ( mask > 0 .and. ytest >= mask_value ) cycle raw2
                case(3)
                  if ( mask < 0 .and. ztest <= mask_value ) cycle raw2
                  if ( mask > 0 .and. ztest >= mask_value ) cycle raw2
                case default
                  write(*,*)'Error: vertical_dir set incorrectly',vertical_dir
                  call lmpi_die
                end select
              endif

              legit_face2 : if ( found_face ) then
                nfound = nfound + 1
                if ( nfound == 1 .and. debug ) then
                  open(92,file=filename)
                  rewind(92)
                  write(92,*) 'variables="x  ","y  ","z  "'
                  write(92,*) 'zone'
                endif
                if ( debug ) write(92,*) xtest,ytest,ztest
                if ( nfound <= 2*nraw ) then

! store base point information

                  profile(nfound)%triangle_or_quad = 2
                  profile(nfound)%boundary_group   = ib
                  profile(nfound)%face_number      = iface
                  profile(nfound)%span_station     = raw_span(iraw)
                  profile(nfound)%chrd_station     = raw_chrd(iraw)
                  profile(nfound)%element_type(1)  = ielem
                  profile(nfound)%element_num(1)   = icell
                  profile(nfound)%sub_tet(1)       = 1
                  profile(nfound)%rank(1)          = lmpi_id+1
                  profile(nfound)%x(1) = xtest
                  profile(nfound)%y(1) = ytest
                  profile(nfound)%z(1) = ztest

                  area = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)

                  xnorm = xnorm / area
                  ynorm = ynorm / area
                  znorm = znorm / area

! find locations of off-surface points to be used in the current profile

                  if (nchrd_unif==1) then
                     delta = maxheight / (profile(nfound)%npoints-1)
                  else

! ======================================================================
! Using Delta99=5*sqrt(x/Re_unit) to estimate the boundary layer thickness.
! Added by Wei Liao in 11/2010. Here "blthickcoef" is an input parameter.

!                     iraw_le=(raw_span(iraw)-1)*nchord+1
!                     xarc=sqrt((xtest-raw_x(iraw_le))**2+                     &
!                               (ytest-raw_y(iraw_le))**2+                     &
!                               (ztest-raw_z(iraw_le))**2)

                     iraw_le=raw_span(iraw)

!                     write(*,*) '++++',lmpi_id,iraw_le

                     xarc=sqrt((xtest-le_xyz(1,iraw_le))**2+                   &
                               (ytest-le_xyz(2,iraw_le))**2+                   &
                               (ztest-le_xyz(3,iraw_le))**2)

                     delta99= 5.d0*sqrt(xarc/re)

                     if (igeom==0) then
                        bndlyrthick=blthickcoef*max(maxheight,delta99)
                     else
                        blcoef = 1.d0+1.5d0*raw_chrd(iraw)/nchord
                        bndlyrthick=blthickcoef*blcoef*max(maxheight,delta99)
                     endif

                     delta = bndlyrthick / (profile(nfound)%npoints-1)

!            write(*,*) xarc,xtest,ytest,ztest,iraw_le,le_xyz(1,iraw_le),   &
!                       le_xyz(2,iraw_le),le_xyz(3,iraw_le),raw_span(iraw), &
!                       raw_chrd(iraw),re,'-1'

! =======================================================================
                  endif

                  ver_stre = 1.6d0

                  do i = 2, profile(nfound)%npoints
                    if (nchrd_unif==1) then
                       length = (i-1)*delta
                    else
                       stretco=1.0d0*(profile(nfound)%npoints-i)/              &
                                     (profile(nfound)%npoints-1)
                       stretcn=1.0d0-tanh(qstretch*(1.d0-stretco))/            &
                                     tanh(qstretch)
                       totcoef=ver_stre*stretco+(1.d0-ver_stre)*stretcn
                       length = bndlyrthick - totcoef*bndlyrthick
                    endif

                    profile(nfound)%x(i) = profile(nfound)%x(1) + length*xnorm
                    profile(nfound)%y(i) = profile(nfound)%y(1) + length*ynorm
                    profile(nfound)%z(i) = profile(nfound)%z(1) + length*znorm
                    if ( debug ) write(92,*) profile(nfound)%x(i),             &
                                             profile(nfound)%y(i),             &
                                             profile(nfound)%z(i)
                  end do

                else
                  write(*,*) 'Error: Found more points than assumed we would...'
                  call lmpi_die
                endif
              endif legit_face2

            end do raw2

          endif local_quad

        end do quad_loop

      endif solid_boundary

    end do bound_loop

    if ( debug ) close(92)

    deallocate(raw_x,raw_y,raw_z,raw_span)

    nprofiles_local = nfound

! Now we need to synch up the profiles across processors

    call lmpi_reduce(nprofiles_local,nprofiles_global)
    call lmpi_bcast(nprofiles_global)

! Allocate an array to store how many profiles were found on each processor
! and a similar temp variable we'll need to use for reducing

    allocate(nprofiles_proc(lmpi_nproc)); nprofiles_proc = 0
    allocate(temp(lmpi_nproc)); temp = 0

! Set number of profiles on the current processor

    nprofiles_proc(lmpi_id+1) = nprofiles_local

! Synch up the list of the number of profiles on each processor

    call lmpi_reduce(nprofiles_proc,temp)
    call lmpi_bcast(temp)
    nprofiles_proc = temp
    deallocate(temp)

    if ( lmpi_master ) write(*,*) 'Number of valid profile seeds found: ',     &
                                   nprofiles_global

! Set up a global_profile variable to hold the synchronized copies of all
! profiles across all processors

    allocate(global_profile(nprofiles_global))
    do i = 1, nprofiles_global
      global_profile(i)%npoints = npoints
      allocate(global_profile(i)%element_type(npoints))
      allocate(global_profile(i)%element_num(npoints))
      allocate(global_profile(i)%sub_tet(npoints))
      allocate(global_profile(i)%rank(npoints))
      allocate(global_profile(i)%x(npoints))
      allocate(global_profile(i)%y(npoints))
      allocate(global_profile(i)%z(npoints))
      allocate(global_profile(i)%q(soln%ndim+1,npoints))
      global_profile(i)%element_type = 0
      global_profile(i)%element_num  = 0
      global_profile(i)%sub_tet      = 0
      global_profile(i)%rank         = 0
      global_profile(i)%x            = my_0
      global_profile(i)%y            = my_0
      global_profile(i)%z            = my_0
      global_profile(i)%q            = my_0
    end do

! Now copy the local profile information into the global_profile variable
! The data is offset by the number of profiles on lower-numbered processors

    offset = 0
    do i = 1, lmpi_id
      offset = offset + nprofiles_proc(i)
    end do

    deallocate(nprofiles_proc)

    do i = 1, nprofiles_local
      global_profile(i+offset)%triangle_or_quad = profile(i)%triangle_or_quad
      global_profile(i+offset)%boundary_group   = profile(i)%boundary_group
      global_profile(i+offset)%face_number      = profile(i)%face_number
      global_profile(i+offset)%span_station     = profile(i)%span_station

      global_profile(i+offset)%element_type = profile(i)%element_type
      global_profile(i+offset)%element_num  = profile(i)%element_num
      global_profile(i+offset)%sub_tet      = profile(i)%sub_tet
      global_profile(i+offset)%rank         = profile(i)%rank

      global_profile(i+offset)%x            = profile(i)%x
      global_profile(i+offset)%y            = profile(i)%y
      global_profile(i+offset)%z            = profile(i)%z
      global_profile(i+offset)%q            = profile(i)%q
    end do

! Ditch the local profile variable

    do i = 1, size(profile,1)
      deallocate(profile(i)%element_type)
      deallocate(profile(i)%element_num)
      deallocate(profile(i)%sub_tet)
      deallocate(profile(i)%rank)
      deallocate(profile(i)%x)
      deallocate(profile(i)%y)
      deallocate(profile(i)%z)
      deallocate(profile(i)%q)
    end do
    deallocate(profile)

! Synch up the rank (owner) of each profile by reducing the rank variable
! The only nonzero entry is the true owner

    do i = 1, nprofiles_global
      call lmpi_reduce(global_profile(i)%rank(1), temprank)
      call lmpi_bcast(temprank)
      global_profile(i)%rank(1) = temprank
    end do

! Synch up the global_profile variable by broadcasting the data
! from the owner's processor

    do i = 1, nprofiles_global
      owner = global_profile(i)%rank(1)-1
      call lmpi_bcast(global_profile(i)%triangle_or_quad, owner)
      call lmpi_bcast(global_profile(i)%boundary_group,   owner)
      call lmpi_bcast(global_profile(i)%face_number,      owner)
      call lmpi_bcast(global_profile(i)%span_station,     owner)
      call lmpi_bcast(global_profile(i)%element_type,     owner)
      call lmpi_bcast(global_profile(i)%element_num,      owner)
      call lmpi_bcast(global_profile(i)%sub_tet,          owner)
      call lmpi_bcast(global_profile(i)%rank,             owner)
      call lmpi_bcast(global_profile(i)%x,                owner)
      call lmpi_bcast(global_profile(i)%y,                owner)
      call lmpi_bcast(global_profile(i)%z,                owner)
      call lmpi_bcast(global_profile(i)%q,                owner)
    end do

    if ( lmpi_master ) write(*,*) 'Global profiles have been set up!'


    output_stabgrid : if ( lmpi_master ) then

      filename = 'profiles_grid.dat'

      open(92,file=filename)

      rewind(92)

      write(92,*) 'variables="x","y","z"'

      do k = 1, nprofiles_global
        write(92,'(a,a,e14.7,1x,a,e14.7,1x,a,e14.7,1x,a)')'zone t="',          &
                                                 'x=',global_profile(k)%x(1),  &
                                                 'y=',global_profile(k)%y(1),  &
                                                 'z=',global_profile(k)%z(1),'"'
        do m = 1, global_profile(k)%npoints
            write(92,'(3(1x,e25.15))') global_profile(k)%x(m),                 &
                                       global_profile(k)%y(m),                 &
                                       global_profile(k)%z(m)
        end do
      end do

      close(92)

    endif output_stabgrid




! now that we have the xyz-coordinates of all of the profile locations
! we just need to establish what elements they will interpolate from
! bear in mind the base points will all interpolate from the surface face
! they belonged to above

    elem_loop : do ielem = 1, grid%nelem

      select case(grid%elem(ielem)%type_cell)
      case('tet')

        do j = 1, grid%elem(ielem)%ncell

          n1 = grid%elem(ielem)%c2n(1,j)
          n2 = grid%elem(ielem)%c2n(2,j)
          n3 = grid%elem(ielem)%c2n(3,j)
          n4 = grid%elem(ielem)%c2n(4,j)

          x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3); x4 = grid%x(n4)
          y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3); y4 = grid%y(n4)
          z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3); z4 = grid%z(n4)

          do k = 1, nprofiles_global
            do m = 2, global_profile(k)%npoints

              if ( global_profile(k)%element_num(m) /= 0 ) cycle

              xt = global_profile(k)%x(m)
              yt = global_profile(k)%y(m)
              zt = global_profile(k)%z(m)

              found = point_in_tet(x1,x2,x3,x4,xt,y1,y2,y3,y4,yt,z1,z2,z3,z4,zt)

              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 1
                global_profile(k)%rank(m)         = lmpi_id+1
              endif

            end do
          end do

        end do

      case('hex')

        do j = 1, grid%elem(ielem)%ncell

          n1 = grid%elem(ielem)%c2n(1,j)
          n2 = grid%elem(ielem)%c2n(2,j)
          n3 = grid%elem(ielem)%c2n(3,j)
          n4 = grid%elem(ielem)%c2n(4,j)
          n5 = grid%elem(ielem)%c2n(5,j)
          n6 = grid%elem(ielem)%c2n(6,j)
          n7 = grid%elem(ielem)%c2n(7,j)
          n8 = grid%elem(ielem)%c2n(8,j)

          x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3); x4 = grid%x(n4)
          y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3); y4 = grid%y(n4)
          z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3); z4 = grid%z(n4)

          x5 = grid%x(n5); x6 = grid%x(n6); x7 = grid%x(n7); x8 = grid%x(n8)
          y5 = grid%y(n5); y6 = grid%y(n6); y7 = grid%y(n7); y8 = grid%y(n8)
          z5 = grid%z(n5); z6 = grid%z(n6); z7 = grid%z(n7); z8 = grid%z(n8)

          do k = 1, nprofiles_global
            loop2 : do m = 2, global_profile(k)%npoints

              if ( global_profile(k)%element_num(m) /= 0 ) cycle

              xt = global_profile(k)%x(m)
              yt = global_profile(k)%y(m)
              zt = global_profile(k)%z(m)

! Tet 1-3-5-4

              found = point_in_tet(x1,x3,x5,x4,xt,y1,y3,y5,y4,yt,z1,z3,z5,z4,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 1
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop2
              endif

! Tet 1-5-2-4

              found = point_in_tet(x1,x5,x2,x4,xt,y1,y5,y2,y4,yt,z1,z5,z2,z4,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 2
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop2
              endif

! Tet 2-6-4-5

              found = point_in_tet(x2,x6,x4,x5,xt,y2,y6,y4,y5,yt,z2,z6,z4,z5,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 3
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop2
              endif

! Tet 3-7-5-4

              found = point_in_tet(x3,x7,x5,x4,xt,y3,y7,y5,y4,yt,z3,z7,z5,z4,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 4
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop2
              endif

! Tet 6-7-4-5

              found = point_in_tet(x6,x7,x4,x5,xt,y6,y7,y4,y5,yt,z6,z7,z4,z5,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 5
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop2
              endif

! Tet 7-8-6-4

              found = point_in_tet(x7,x8,x6,x4,xt,y7,y8,y6,y4,yt,z7,z8,z6,z4,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 6
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop2
              endif

            end do loop2
          end do

        end do

      case('prz')

        do j = 1, grid%elem(ielem)%ncell

          n1 = grid%elem(ielem)%c2n(1,j)
          n2 = grid%elem(ielem)%c2n(2,j)
          n3 = grid%elem(ielem)%c2n(3,j)
          n4 = grid%elem(ielem)%c2n(4,j)
          n5 = grid%elem(ielem)%c2n(5,j)
          n6 = grid%elem(ielem)%c2n(6,j)

          x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3); x4 = grid%x(n4)
          y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3); y4 = grid%y(n4)
          z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3); z4 = grid%z(n4)

          x5 = grid%x(n5); x6 = grid%x(n6)
          y5 = grid%y(n5); y6 = grid%y(n6)
          z5 = grid%z(n5); z6 = grid%z(n6)

          do k = 1, nprofiles_global
            loop3 : do m = 2, global_profile(k)%npoints

              if ( global_profile(k)%element_num(m) /= 0 ) cycle

              xt = global_profile(k)%x(m)
              yt = global_profile(k)%y(m)
              zt = global_profile(k)%z(m)

! Tet 1-4-6-2

              found = point_in_tet(x1,x4,x6,x2,xt,y1,y4,y6,y2,yt,z1,z4,z6,z2,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 1
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop3
              endif

! Tet 5-4-2-6

              found = point_in_tet(x5,x4,x2,x6,xt,y5,y4,y2,y6,yt,z5,z4,z2,z6,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 2
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop3
              endif

! Tet 3-4-2-5

              found = point_in_tet(x3,x4,x2,x5,xt,y3,y4,y2,y5,yt,z3,z4,z2,z5,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 3
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop3
              endif

            end do loop3
          end do

        end do

      case('pyr')

        do j = 1, grid%elem(ielem)%ncell

          n1 = grid%elem(ielem)%c2n(1,j)
          n2 = grid%elem(ielem)%c2n(2,j)
          n3 = grid%elem(ielem)%c2n(3,j)
          n4 = grid%elem(ielem)%c2n(4,j)
          n5 = grid%elem(ielem)%c2n(5,j)

          x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3); x4 = grid%x(n4)
          y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3); y4 = grid%y(n4)
          z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3); z4 = grid%z(n4)

          x5 = grid%x(n5)
          y5 = grid%y(n5)
          z5 = grid%z(n5)

          do k = 1, nprofiles_global
            loop4 : do m = 2, global_profile(k)%npoints

              if ( global_profile(k)%element_num(m) /= 0 ) cycle

              xt = global_profile(k)%x(m)
              yt = global_profile(k)%y(m)
              zt = global_profile(k)%z(m)

! Tet 1-3-5-2

              found = point_in_tet(x1,x3,x5,x2,xt,y1,y3,y5,y2,yt,z1,z3,z5,z2,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 1
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop4
              endif

! Tet 1-3-4-5

              found = point_in_tet(x1,x3,x4,x5,xt,y1,y3,y4,y5,yt,z1,z3,z4,z5,zt)
              if ( found ) then
                global_profile(k)%element_type(m) = ielem
                global_profile(k)%element_num(m)  = j
                global_profile(k)%sub_tet(m)      = 2
                global_profile(k)%rank(m)         = lmpi_id+1
                cycle loop4
              endif

            end do loop4
          end do

        end do

      case default
        write(*,*) 'Unknown type_cell in find_profiles.'
        call lmpi_die
      end select

    end do elem_loop

! Synch up the interpolants just determined for the global_profile variable
! Do this by taking the max rank that found a particular interpolant - this
! will end up being the permanent owner for that interpolation operation.
! This is to take care of cells that straddle the partition boundaries and
! might contain profile points

    do i = 1, nprofiles_global
      do k = 1, global_profile(i)%npoints
        call lmpi_max(global_profile(i)%rank(k), temprank)
        call lmpi_bcast(temprank)
        global_profile(i)%rank(k) = temprank
      end do
    end do

! Synch up the global_profile variable by broadcasting the data
! from the owner's processor

    do i = 1, nprofiles_global
      do k = 1, global_profile(i)%npoints
        owner = global_profile(i)%rank(k)-1
        if ( owner >= 0 ) then  ! skip elements for which interpolant not found
          call lmpi_bcast(global_profile(i)%element_type(k), owner)
          call lmpi_bcast(global_profile(i)%element_num(k),  owner)
          call lmpi_bcast(global_profile(i)%sub_tet(k),      owner)
        endif
      end do
    end do

! Go back and revisit the points which were not found in an element.  Find
! the closest node on each processor
! Then reduce who had the closest node to each injection point and bcast

    do i = 1, nprofiles_global
      do k = 2, global_profile(i)%npoints
        owner = global_profile(i)%rank(k)-1

        if ( owner < 0 ) then           ! no interpolants found on any proc

          xt = global_profile(i)%x(k)
          yt = global_profile(i)%y(k)
          zt = global_profile(i)%z(k)

          mindist = huge(real(mindist,dp))
          search : do j = 1, grid%nnodes0
            xj = grid%x(j)
            yj = grid%y(j)
            zj = grid%z(j)
            dist = sqrt((xj-xt)**2 + (yj-yt)**2 + (zj-zt)**2)
            if ( dist <= mindist ) then
              global_profile(i)%element_type(k) = -j
              mindist = dist
            endif
          end do search

          mindist = -mindist   ! so we can use lmpi_max_and_maxid

          call lmpi_max_and_maxid(real(mindist,dp),proc_with_max)
          call lmpi_bcast(mindist,proc_with_max)

          mindist = -mindist   ! switch it back to physically correct

          global_profile(i)%rank(k) = proc_with_max+1
          call lmpi_bcast(global_profile(i)%element_type(k),proc_with_max)

          if ( lmpi_master ) then
            write(*,'(1x,a,e12.5,1x,e12.5,1x,e12.5)')                          &
                       'Injection point at xyz = ', global_profile(i)%x(k),    &
                                                    global_profile(i)%y(k),    &
                                                    global_profile(i)%z(k)
            write(*,'(1x,a,e12.5)') 'Injection distance = ', mindist
          endif

        endif
      end do
    end do


    if ( lmpi_master ) write(*,*)                                              &
         'Searching donor cells for profile points ... done!'

! now interpolate Q into the profile locations - only for those the current
! processor owns

    call etop(grid%nnodes01,soln%q_dof,soln%n_tot,soln%eqn_set)

    do k = 1, nprofiles_global

! first do the surface point which is special

      surf_pt : if ( global_profile(k)%rank(1) == lmpi_id+1 ) then

        select case(global_profile(k)%triangle_or_quad)
        case(1)

          ib = global_profile(k)%boundary_group
          iface = global_profile(k)%face_number

          n1 = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(iface,1))
          n2 = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(iface,2))
          n3 = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(iface,3))

          xt = global_profile(k)%x(1)
          yt = global_profile(k)%y(1)
          zt = global_profile(k)%z(1)

          x1 = grid%x(n1); y1 = grid%y(n1); z1 = grid%z(n1)
          x2 = grid%x(n2); y2 = grid%y(n2); z2 = grid%z(n2)
          x3 = grid%x(n3); y3 = grid%y(n3); z3 = grid%z(n3)

          big_area = tria_area(x1,y1,z1,x2,y2,z2,x3,y3,z3)
          big_mag = sqrt(big_area(1)**2 + big_area(2)**2 + big_area(3)**2)

          area1 = tria_area(xt,yt,zt,x2,y2,z2,x3,y3,z3)
          mag1 = sqrt(area1(1)**2 + area1(2)**2 + area1(3)**2)

          area2 = tria_area(x1,y1,z1,xt,yt,zt,x3,y3,z3)
          mag2 = sqrt(area2(1)**2 + area2(2)**2 + area2(3)**2)

          area3 = tria_area(x1,y1,z1,x2,y2,z2,xt,yt,zt)
          mag3 = sqrt(area3(1)**2 + area3(2)**2 + area3(3)**2)

          w1 = mag1/big_mag
          w2 = mag2/big_mag
          w3 = mag3/big_mag

          global_profile(k)%q(1:soln%ndim,1) = w1*soln%q_dof(1:soln%ndim,n1)   &
                                             + w2*soln%q_dof(1:soln%ndim,n2)   &
                                             + w3*soln%q_dof(1:soln%ndim,n3)

          if ( viscous_terms == 'turbulent' ) then
            global_profile(k)%q(soln%ndim+1,1) = w1*soln%amut(n1)              &
                                               + w2*soln%amut(n2)              &
                                               + w3*soln%amut(n3)
          endif

        case(2)

          ib = global_profile(k)%boundary_group
          iface = global_profile(k)%face_number

          n1 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(iface,1))
          n2 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(iface,2))
          n3 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(iface,3))
          n4 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(iface,4))

          xt = global_profile(k)%x(1)
          yt = global_profile(k)%y(1)
          zt = global_profile(k)%z(1)

          x1 = grid%x(n1); y1 = grid%y(n1); z1 = grid%z(n1)
          x2 = grid%x(n2); y2 = grid%y(n2); z2 = grid%z(n2)
          x3 = grid%x(n3); y3 = grid%y(n3); z3 = grid%z(n3)
          x4 = grid%x(n4); y4 = grid%y(n4); z4 = grid%z(n4)

          found_face = point_lies_in_triangle(x1,y1,z1,x2,y2,z2,x3,y3,z3,      &
                                              xt,yt,zt)
          if ( found_face ) then
            ifc = 1
          else
            found_face = point_lies_in_triangle(x1,y1,z1,x3,y3,z3,x4,y4,z4,    &
                                                xt,yt,zt)
            if ( found_face ) then
              ifc = 2
            else
              write(*,*) 'Error: point in quad does not appear in either tria.'
              call lmpi_die
            endif
          endif

          select case(ifc)
          case(1)   ! triangle 123

            big_area = tria_area(x1,y1,z1,x2,y2,z2,x3,y3,z3)
            big_mag = sqrt(big_area(1)**2 + big_area(2)**2 + big_area(3)**2)

            area1 = tria_area(xt,yt,zt,x2,y2,z2,x3,y3,z3)
            mag1 = sqrt(area1(1)**2 + area1(2)**2 + area1(3)**2)

            area2 = tria_area(x1,y1,z1,xt,yt,zt,x3,y3,z3)
            mag2 = sqrt(area2(1)**2 + area2(2)**2 + area2(3)**2)

            area3 = tria_area(x1,y1,z1,x2,y2,z2,xt,yt,zt)
            mag3 = sqrt(area3(1)**2 + area3(2)**2 + area3(3)**2)

            w1 = mag1/big_mag
            w2 = mag2/big_mag
            w3 = mag3/big_mag

            global_profile(k)%q(1:soln%ndim,1) = w1*soln%q_dof(1:soln%ndim,n1) &
                                               + w2*soln%q_dof(1:soln%ndim,n2) &
                                               + w3*soln%q_dof(1:soln%ndim,n3)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,1) = w1*soln%amut(n1)            &
                                                 + w2*soln%amut(n2)            &
                                                 + w3*soln%amut(n3)
            endif

          case(2)   ! triangle 134

            big_area = tria_area(x1,y1,z1,x3,y3,z3,x4,y4,z4)
            big_mag = sqrt(big_area(1)**2 + big_area(2)**2 + big_area(3)**2)

            area1 = tria_area(xt,yt,zt,x3,y3,z3,x4,y4,z4)
            mag1 = sqrt(area1(1)**2 + area1(2)**2 + area1(3)**2)

            area2 = tria_area(x1,y1,z1,xt,yt,zt,x4,y4,z4)
            mag2 = sqrt(area2(1)**2 + area2(2)**2 + area2(3)**2)

            area3 = tria_area(x1,y1,z1,x3,y3,z3,xt,yt,zt)
            mag3 = sqrt(area3(1)**2 + area3(2)**2 + area3(3)**2)

            w1 = mag1/big_mag
            w2 = mag2/big_mag
            w3 = mag3/big_mag

            global_profile(k)%q(1:soln%ndim,1) = w1*soln%q_dof(1:soln%ndim,n1) &
                                               + w2*soln%q_dof(1:soln%ndim,n3) &
                                               + w3*soln%q_dof(1:soln%ndim,n4)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,1) = w1*soln%amut(n1)            &
                                                 + w2*soln%amut(n3)            &
                                                 + w3*soln%amut(n4)
            endif

          case default
              write(*,*) 'Error: bad value of ifc in find_profiles:', ifc
              call lmpi_die
          end select

        case default
          write(*,*) 'Unknown triangle_or_quad in find_profiles.'
          call lmpi_die
        end select

      endif surf_pt

! now do the rest of the points in the profile
! break into tets for weighting with barycentric coords

      pt_loop : do m = 2, global_profile(k)%npoints

        if ( global_profile(k)%rank(m) /= lmpi_id+1 ) cycle pt_loop

        ielem = global_profile(k)%element_type(m)
        icell = global_profile(k)%element_num(m)

        xt = global_profile(k)%x(m)
        yt = global_profile(k)%y(m)
        zt = global_profile(k)%z(m)

        if ( ielem < 0 ) then  ! Take care of any injection points
          node = abs(ielem)
          global_profile(k)%q(1:soln%ndim,m) = soln%q_dof(1:soln%ndim,node)
          if ( viscous_terms == 'turbulent' )                                  &
            global_profile(k)%q(soln%ndim+1,m) = soln%amut(node)
          cycle pt_loop
        endif

        select case(grid%elem(ielem)%type_cell)
        case('tet')

          n1 = grid%elem(ielem)%c2n(1,icell)
          n2 = grid%elem(ielem)%c2n(2,icell)
          n3 = grid%elem(ielem)%c2n(3,icell)
          n4 = grid%elem(ielem)%c2n(4,icell)

          x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3); x4 = grid%x(n4)
          y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3); y4 = grid%y(n4)
          z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3); z4 = grid%z(n4)

          cell_vol = abs(tet_volume(x1,x2,x3,x4,y1,y2,y3,y4,z1,z2,z3,z4))

          w1 = abs(tet_volume(xt,x2,x3,x4,yt,y2,y3,y4,zt,z2,z3,z4))/cell_vol
          w2 = abs(tet_volume(x1,xt,x3,x4,y1,yt,y3,y4,z1,zt,z3,z4))/cell_vol
          w3 = abs(tet_volume(x1,x2,xt,x4,y1,y2,yt,y4,z1,z2,zt,z4))/cell_vol
          w4 = abs(tet_volume(x1,x2,x3,xt,y1,y2,y3,yt,z1,z2,z3,zt))/cell_vol

          global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n1)   &
                                             + w2*soln%q_dof(1:soln%ndim,n2)   &
                                             + w3*soln%q_dof(1:soln%ndim,n3)   &
                                             + w4*soln%q_dof(1:soln%ndim,n4)

          if ( viscous_terms == 'turbulent' ) then
            global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n1)              &
                                               + w2*soln%amut(n2)              &
                                               + w3*soln%amut(n3)              &
                                               + w4*soln%amut(n4)
          endif

        case('hex')

          n1 = grid%elem(ielem)%c2n(1,icell)
          n2 = grid%elem(ielem)%c2n(2,icell)
          n3 = grid%elem(ielem)%c2n(3,icell)
          n4 = grid%elem(ielem)%c2n(4,icell)
          n5 = grid%elem(ielem)%c2n(5,icell)
          n6 = grid%elem(ielem)%c2n(6,icell)
          n7 = grid%elem(ielem)%c2n(7,icell)
          n8 = grid%elem(ielem)%c2n(8,icell)

          x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3); x4 = grid%x(n4)
          y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3); y4 = grid%y(n4)
          z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3); z4 = grid%z(n4)

          x5 = grid%x(n5); x6 = grid%x(n6); x7 = grid%x(n7); x8 = grid%x(n8)
          y5 = grid%y(n5); y6 = grid%y(n6); y7 = grid%y(n7); y8 = grid%y(n8)
          z5 = grid%z(n5); z6 = grid%z(n6); z7 = grid%z(n7); z8 = grid%z(n8)

          sub_tet = global_profile(k)%sub_tet(m)

          select case(sub_tet)
          case(1)

            cell_vol = abs(tet_volume(x1,x3,x5,x4,y1,y3,y5,y4,z1,z3,z5,z4))

            w1 = abs(tet_volume(xt,x3,x5,x4,yt,y3,y5,y4,zt,z3,z5,z4))/cell_vol
            w2 = abs(tet_volume(x1,xt,x5,x4,y1,yt,y5,y4,z1,zt,z5,z4))/cell_vol
            w3 = abs(tet_volume(x1,x3,xt,x4,y1,y3,yt,y4,z1,z3,zt,z4))/cell_vol
            w4 = abs(tet_volume(x1,x3,x5,xt,y1,y3,y5,yt,z1,z3,z5,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n1) &
                                               + w2*soln%q_dof(1:soln%ndim,n3) &
                                               + w3*soln%q_dof(1:soln%ndim,n5) &
                                               + w4*soln%q_dof(1:soln%ndim,n4)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n1)            &
                                                 + w2*soln%amut(n3)            &
                                                 + w3*soln%amut(n5)            &
                                                 + w4*soln%amut(n4)
            endif

          case(2)

            cell_vol = abs(tet_volume(x1,x5,x2,x4,y1,y5,y2,y4,z1,z5,z2,z4))

            w1 = abs(tet_volume(xt,x5,x2,x4,yt,y5,y2,y4,zt,z5,z2,z4))/cell_vol
            w2 = abs(tet_volume(x1,xt,x2,x4,y1,yt,y2,y4,z1,zt,z2,z4))/cell_vol
            w3 = abs(tet_volume(x1,x5,xt,x4,y1,y5,yt,y4,z1,z5,zt,z4))/cell_vol
            w4 = abs(tet_volume(x1,x5,x2,xt,y1,y5,y2,yt,z1,z5,z2,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n1) &
                                               + w2*soln%q_dof(1:soln%ndim,n5) &
                                               + w3*soln%q_dof(1:soln%ndim,n2) &
                                               + w4*soln%q_dof(1:soln%ndim,n4)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n1)            &
                                                 + w2*soln%amut(n5)            &
                                                 + w3*soln%amut(n2)            &
                                                 + w4*soln%amut(n4)
            endif

          case(3)

            cell_vol = abs(tet_volume(x2,x6,x4,x5,y2,y6,y4,y5,z2,z6,z4,z5))

            w1 = abs(tet_volume(xt,x6,x4,x5,yt,y6,y4,y5,zt,z6,z4,z5))/cell_vol
            w2 = abs(tet_volume(x2,xt,x4,x5,y2,yt,y4,y5,z2,zt,z4,z5))/cell_vol
            w3 = abs(tet_volume(x2,x6,xt,x5,y2,y6,yt,y5,z2,z6,zt,z5))/cell_vol
            w4 = abs(tet_volume(x2,x6,x4,xt,y2,y6,y4,yt,z2,z6,z4,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n2) &
                                               + w2*soln%q_dof(1:soln%ndim,n6) &
                                               + w3*soln%q_dof(1:soln%ndim,n4) &
                                               + w4*soln%q_dof(1:soln%ndim,n5)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n2)            &
                                                 + w2*soln%amut(n6)            &
                                                 + w3*soln%amut(n4)            &
                                                 + w4*soln%amut(n5)
            endif

          case(4)

            cell_vol = abs(tet_volume(x3,x7,x5,x4,y3,y7,y5,y4,z3,z7,z5,z4))

            w1 = abs(tet_volume(xt,x7,x5,x4,yt,y7,y5,y4,zt,z7,z5,z4))/cell_vol
            w2 = abs(tet_volume(x3,xt,x5,x4,y3,yt,y5,y4,z3,zt,z5,z4))/cell_vol
            w3 = abs(tet_volume(x3,x7,xt,x4,y3,y7,yt,y4,z3,z7,zt,z4))/cell_vol
            w4 = abs(tet_volume(x3,x7,x5,xt,y3,y7,y5,yt,z3,z7,z5,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n3) &
                                               + w2*soln%q_dof(1:soln%ndim,n7) &
                                               + w3*soln%q_dof(1:soln%ndim,n5) &
                                               + w4*soln%q_dof(1:soln%ndim,n4)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n3)            &
                                                 + w2*soln%amut(n7)            &
                                                 + w3*soln%amut(n5)            &
                                                 + w4*soln%amut(n4)
            endif

          case(5)

            cell_vol = abs(tet_volume(x6,x7,x4,x5,y6,y7,y4,y5,z6,z7,z4,z5))

            w1 = abs(tet_volume(xt,x7,x4,x5,yt,y7,y4,y5,zt,z7,z4,z5))/cell_vol
            w2 = abs(tet_volume(x6,xt,x4,x5,y6,yt,y4,y5,z6,zt,z4,z5))/cell_vol
            w3 = abs(tet_volume(x6,x7,xt,x5,y6,y7,yt,y5,z6,z7,zt,z5))/cell_vol
            w4 = abs(tet_volume(x6,x7,x4,xt,y6,y7,y4,yt,z6,z7,z4,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n6) &
                                               + w2*soln%q_dof(1:soln%ndim,n7) &
                                               + w3*soln%q_dof(1:soln%ndim,n4) &
                                               + w4*soln%q_dof(1:soln%ndim,n5)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n6)            &
                                                 + w2*soln%amut(n7)            &
                                                 + w3*soln%amut(n4)            &
                                                 + w4*soln%amut(n5)
            endif

          case(6)

            cell_vol = abs(tet_volume(x7,x8,x6,x4,y7,y8,y6,y4,z7,z8,z6,z4))

            w1 = abs(tet_volume(xt,x8,x6,x4,yt,y8,y6,y4,zt,z8,z6,z4))/cell_vol
            w2 = abs(tet_volume(x7,xt,x6,x4,y7,yt,y6,y4,z7,zt,z6,z4))/cell_vol
            w3 = abs(tet_volume(x7,x8,xt,x4,y7,y8,yt,y4,z7,z8,zt,z4))/cell_vol
            w4 = abs(tet_volume(x7,x8,x6,xt,y7,y8,y6,yt,z7,z8,z6,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n7) &
                                               + w2*soln%q_dof(1:soln%ndim,n8) &
                                               + w3*soln%q_dof(1:soln%ndim,n6) &
                                               + w4*soln%q_dof(1:soln%ndim,n4)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n7)            &
                                                 + w2*soln%amut(n8)            &
                                                 + w3*soln%amut(n6)            &
                                                 + w4*soln%amut(n4)
            endif

          case default
            write(*,*) 'Unknown sub_tet in find_profiles.'
            call lmpi_die
          end select

        case('prz')

          n1 = grid%elem(ielem)%c2n(1,icell)
          n2 = grid%elem(ielem)%c2n(2,icell)
          n3 = grid%elem(ielem)%c2n(3,icell)
          n4 = grid%elem(ielem)%c2n(4,icell)
          n5 = grid%elem(ielem)%c2n(5,icell)
          n6 = grid%elem(ielem)%c2n(6,icell)

          x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3); x4 = grid%x(n4)
          y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3); y4 = grid%y(n4)
          z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3); z4 = grid%z(n4)

          x5 = grid%x(n5); x6 = grid%x(n6)
          y5 = grid%y(n5); y6 = grid%y(n6)
          z5 = grid%z(n5); z6 = grid%z(n6)

          sub_tet = global_profile(k)%sub_tet(m)

          select case(sub_tet)
          case(1)
! Tet 1-4-6-2
            cell_vol = abs(tet_volume(x1,x4,x6,x2,y1,y4,y6,y2,z1,z4,z6,z2))

            w1 = abs(tet_volume(xt,x4,x6,x2,yt,y4,y6,y2,zt,z4,z6,z2))/cell_vol
            w2 = abs(tet_volume(x1,xt,x6,x2,y1,yt,y6,y2,z1,zt,z6,z2))/cell_vol
            w3 = abs(tet_volume(x1,x4,xt,x2,y1,y4,yt,y2,z1,z4,zt,z2))/cell_vol
            w4 = abs(tet_volume(x1,x4,x6,xt,y1,y4,y6,yt,z1,z4,z6,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n1) &
                                               + w2*soln%q_dof(1:soln%ndim,n4) &
                                               + w3*soln%q_dof(1:soln%ndim,n6) &
                                               + w4*soln%q_dof(1:soln%ndim,n2)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n1)            &
                                                 + w2*soln%amut(n4)            &
                                                 + w3*soln%amut(n6)            &
                                                 + w4*soln%amut(n2)
            endif

          case(2)
! Tet 5-4-2-6
            cell_vol = abs(tet_volume(x5,x4,x2,x6,y5,y4,y2,y6,z5,z4,z2,z6))

            w1 = abs(tet_volume(xt,x4,x2,x6,yt,y4,y2,y6,zt,z4,z2,z6))/cell_vol
            w2 = abs(tet_volume(x5,xt,x2,x6,y5,yt,y2,y6,z5,zt,z2,z6))/cell_vol
            w3 = abs(tet_volume(x5,x4,xt,x6,y5,y4,yt,y6,z5,z4,zt,z6))/cell_vol
            w4 = abs(tet_volume(x5,x4,x2,xt,y5,y4,y2,yt,z5,z4,z2,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n5) &
                                               + w2*soln%q_dof(1:soln%ndim,n4) &
                                               + w3*soln%q_dof(1:soln%ndim,n2) &
                                               + w4*soln%q_dof(1:soln%ndim,n6)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n5)            &
                                                 + w2*soln%amut(n4)            &
                                                 + w3*soln%amut(n2)            &
                                                 + w4*soln%amut(n6)
            endif

          case(3)
! Tet 3-4-2-5
            cell_vol = abs(tet_volume(x3,x4,x2,x5,y3,y4,y2,y5,z3,z4,z2,z5))

            w1 = abs(tet_volume(xt,x4,x2,x5,yt,y4,y2,y5,zt,z4,z2,z5))/cell_vol
            w2 = abs(tet_volume(x3,xt,x2,x5,y3,yt,y2,y5,z3,zt,z2,z5))/cell_vol
            w3 = abs(tet_volume(x3,x4,xt,x5,y3,y4,yt,y5,z3,z4,zt,z5))/cell_vol
            w4 = abs(tet_volume(x3,x4,x2,xt,y3,y4,y2,yt,z3,z4,z2,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n3) &
                                               + w2*soln%q_dof(1:soln%ndim,n4) &
                                               + w3*soln%q_dof(1:soln%ndim,n2) &
                                               + w4*soln%q_dof(1:soln%ndim,n5)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n3)            &
                                                 + w2*soln%amut(n4)            &
                                                 + w3*soln%amut(n2)            &
                                                 + w4*soln%amut(n5)
            endif

          case default
            write(*,*) 'Unknown sub_tet in find_profiles.'
            call lmpi_die
          end select

        case('pyr')

          n1 = grid%elem(ielem)%c2n(1,icell)
          n2 = grid%elem(ielem)%c2n(2,icell)
          n3 = grid%elem(ielem)%c2n(3,icell)
          n4 = grid%elem(ielem)%c2n(4,icell)
          n5 = grid%elem(ielem)%c2n(5,icell)

          x1 = grid%x(n1); x2 = grid%x(n2); x3 = grid%x(n3); x4 = grid%x(n4)
          y1 = grid%y(n1); y2 = grid%y(n2); y3 = grid%y(n3); y4 = grid%y(n4)
          z1 = grid%z(n1); z2 = grid%z(n2); z3 = grid%z(n3); z4 = grid%z(n4)

          x5 = grid%x(n5)
          y5 = grid%y(n5)
          z5 = grid%z(n5)

          sub_tet = global_profile(k)%sub_tet(m)

          select case(sub_tet)
          case(1)
! Tet 1-3-5-2
            cell_vol = abs(tet_volume(x1,x3,x5,x2,y1,y3,y5,y2,z1,z3,z5,z2))

            w1 = abs(tet_volume(xt,x3,x5,x2,yt,y3,y5,y2,zt,z3,z5,z2))/cell_vol
            w2 = abs(tet_volume(x1,xt,x5,x2,y1,yt,y5,y2,z1,zt,z5,z2))/cell_vol
            w3 = abs(tet_volume(x1,x3,xt,x2,y1,y3,yt,y2,z1,z3,zt,z2))/cell_vol
            w4 = abs(tet_volume(x1,x3,x5,xt,y1,y3,y5,yt,z1,z3,z5,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n1) &
                                               + w2*soln%q_dof(1:soln%ndim,n3) &
                                               + w3*soln%q_dof(1:soln%ndim,n5) &
                                               + w4*soln%q_dof(1:soln%ndim,n2)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n1)            &
                                                 + w2*soln%amut(n3)            &
                                                 + w3*soln%amut(n5)            &
                                                 + w4*soln%amut(n2)
            endif

          case(2)
! Tet 1-3-4-5
            cell_vol = abs(tet_volume(x1,x3,x4,x5,y1,y3,y4,y5,z1,z3,z4,z5))

            w1 = abs(tet_volume(xt,x3,x4,x5,yt,y3,y4,y5,zt,z3,z4,z5))/cell_vol
            w2 = abs(tet_volume(x1,xt,x4,x5,y1,yt,y4,y5,z1,zt,z4,z5))/cell_vol
            w3 = abs(tet_volume(x1,x3,xt,x5,y1,y3,yt,y5,z1,z3,zt,z5))/cell_vol
            w4 = abs(tet_volume(x1,x3,x4,xt,y1,y3,y4,yt,z1,z3,z4,zt))/cell_vol

            global_profile(k)%q(1:soln%ndim,m) = w1*soln%q_dof(1:soln%ndim,n1) &
                                               + w2*soln%q_dof(1:soln%ndim,n3) &
                                               + w3*soln%q_dof(1:soln%ndim,n4) &
                                               + w4*soln%q_dof(1:soln%ndim,n5)

            if ( viscous_terms == 'turbulent' ) then
              global_profile(k)%q(soln%ndim+1,m) = w1*soln%amut(n1)            &
                                                 + w2*soln%amut(n3)            &
                                                 + w3*soln%amut(n4)            &
                                                 + w4*soln%amut(n5)
            endif

          case default
            write(*,*) 'Unknown sub_tet in find_profiles.'
            call lmpi_die
          end select

        case default
          write(*,*) 'Unknown type_cell in find_profiles.'
          call lmpi_die
        end select

      end do pt_loop

    end do

    call ptoe(grid%nnodes01,soln%q_dof,soln%n_tot,soln%eqn_set)

! Synch up the interpolated values using the owner's info and we're done

    do i = 1, nprofiles_global
      do k = 1, global_profile(i)%npoints
        owner = global_profile(i)%rank(k)-1
        do j = 1, size(global_profile(i)%q,1)
          call lmpi_bcast(global_profile(i)%q(j,k), owner)
        end do
      end do
    end do

    if ( lmpi_master )                                                         &
         write(*,*) 'Interpolation and synchup for profile points ... done!'

! finally write out a tecplot zone for each profile from the master

    output_file : if ( lmpi_master ) then

      filename = 'profiles.dat'
      open(92,file=filename)
      rewind(92)

      if ( viscous_terms == 'turbulent' ) then
        write(92,*) 'variables="x","y","z","eta","rho","u","v","w","p","mu-t"'
      else
        write(92,*) 'variables="x","y","z","eta","rho","u","v","w","p"'
      endif

      do k = 1, nprofiles_global
        write(92,'(a,a,e14.7,1x,a,e14.7,1x,a,e14.7,1x,a)')'zone t="',          &
                                                 'x=',global_profile(k)%x(1),  &
                                                 'y=',global_profile(k)%y(1),  &
                                                 'z=',global_profile(k)%z(1),'"'
        do m = 1, global_profile(k)%npoints

! compute wall distance to plot against

         if ( m == 1 ) then
            eta = my_0
         else
          eta=sqrt((global_profile(k)%x(m)-global_profile(k)%x(1))             &
                  *(global_profile(k)%x(m)-global_profile(k)%x(1))             &
                  +(global_profile(k)%y(m)-global_profile(k)%y(1))             &
                  *(global_profile(k)%y(m)-global_profile(k)%y(1))             &
                  +(global_profile(k)%z(m)-global_profile(k)%z(1))             &
                  *(global_profile(k)%z(m)-global_profile(k)%z(1)))
         endif

          if ( viscous_terms == 'turbulent' ) then
            write(92,'(10(1x,e25.15))') global_profile(k)%x(m),                &
                                        global_profile(k)%y(m),                &
                                        global_profile(k)%z(m),                &
                                        eta,                                   &
                                        (global_profile(k)%q(j,m),j=1,6)
          else
            write(92,'(9(1x,e25.15))') global_profile(k)%x(m),                 &
                                       global_profile(k)%y(m),                 &
                                       global_profile(k)%z(m),                 &
                                       eta,                                    &
                                       (global_profile(k)%q(j,m),j=1,5)
          endif

        end do
      end do

      close(92)

    endif output_file

    if ( lmpi_master ) write(*,*) 'Output global profiles ... ...'

    call crossflow()

    if ( lmpi_master ) write(*,*) 'Profile extraction done. Congratulations!'

  end subroutine find_profiles

!============================ TET_VOLUME =====================================80
!
! Computes volume of a tet
!
!=============================================================================80
  function tet_volume(x1,x2,x3,x4,y1,y2,y3,y4,z1,z2,z3,z4)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_6

    real(dp), intent(in) :: x1,x2,x3,x4,y1,y2,y3,y4,z1,z2,z3,z4

    real(dp) :: tet_volume

  continue

    tet_volume = (((y2-y1)*(z3-z1) - (y3-y1)*(z2-z1))*(x4-x1)                  &
                 -((x2-x1)*(z3-z1) - (x3-x1)*(z2-z1))*(y4-y1)                  &
                 +((x2-x1)*(y3-y1) - (x3-x1)*(y2-y1))*(z4-z1))/my_6

  end function tet_volume

!============================ POINT_IN_TET ===================================80
!
! Finds out if a point lies inside a tet
!
!=============================================================================80
  logical function point_in_tet(x1,x2,x3,x4,xt,y1,y2,y3,y4,yt,z1,z2,z3,z4,zt)

    use fun3d_constants,       only : my_0, my_1
    use interpolate_utilities, only : determ
    use kinddefs,              only : dp

    real(dp), intent(in) :: x1,x2,x3,x4
    real(dp), intent(in) :: y1,y2,y3,y4
    real(dp), intent(in) :: z1,z2,z3,z4
    real(dp), intent(in) :: xt,yt,zt

    real(dp) :: a11,a12,a13,a14
    real(dp) :: a21,a22,a23,a24
    real(dp) :: a31,a32,a33,a34
    real(dp) :: a41,a42,a43,a44
    real(dp) :: d0,d1,d2,d3,d4

  continue

    point_in_tet = .false.

! Set elements of 4x4 matrix

    a11 = x1
    a12 = y1
    a13 = z1
    a14 = my_1

    a21 = x2
    a22 = y2
    a23 = z2
    a24 = my_1

    a31 = x3
    a32 = y3
    a33 = z3
    a34 = my_1

    a41 = x4
    a42 = y4
    a43 = z4
    a44 = my_1

! Compute d0

    d0 = determ(a11,a12,a13,a14,                                               &
                a21,a22,a23,a24,                                               &
                a31,a32,a33,a34,                                               &
                a41,a42,a43,a44)

    if (d0 == my_0) write(*,*)'This cell appears to be degenerate.'

! Compute d1

    d1 = determ(xt, yt, zt, my_1,                                              &
                a21,a22,a23,a24,                                               &
                a31,a32,a33,a34,                                               &
                a41,a42,a43,a44)

    if (d1 == my_0) return

    if( ((d0 < my_0) .and. (d1 > my_0)) .or.                                   &
        ((d0 > my_0) .and. (d1 < my_0))) return

! Compute d2

    d2 = determ(a11,a12,a13,a14,                                               &
                xt, yt, zt, my_1,                                              &
                a31,a32,a33,a34,                                               &
                a41,a42,a43,a44)

    if (d2 == my_0) return

    if (((d0 < my_0) .and. (d2 > my_0)) .or.                                   &
        ((d0 > my_0) .and. (d2 < my_0))) return

! Compute d3

    d3 = determ(a11,a12,a13,a14,                                               &
                a21,a22,a23,a24,                                               &
                xt, yt, zt, my_1,                                              &
                a41,a42,a43,a44)

    if (d3 == my_0) return

    if (((d0 < my_0) .and. (d3 > my_0)) .or.                                   &
        ((d0 > my_0) .and. (d3 < my_0))) return

! Compute d4

    d4 = determ(a11,a12,a13,a14,                                               &
                a21,a22,a23,a24,                                               &
                a31,a32,a33,a34,                                               &
                xt, yt, zt, my_1)

    if (d4 == my_0) return

    if (((d0 < my_0) .and. (d4 > my_0)) .or.                                   &
        ((d0 > my_0) .and. (d4 < my_0))) return

!  Well if we made it through all those checks that means we're inside this cell

    point_in_tet = .true.

  end function point_in_tet


!============================ CROSSFLOW ======================================80
!
!  Creates a structured mesh to compute some crossflow stuff on
!
!=============================================================================80
  subroutine crossflow()

    use sort,            only : heap_sort
    use kinddefs,        only : dp
    use fun3d_constants, only : my_0
    use lmpi,            only : lmpi_master

    integer :: i, j, k, lead_dim, ind, ii, nprofiles, station, source

    integer, dimension(:), allocatable :: sort_index, span_stations, span_tag
    integer, dimension(:), allocatable :: span_sort_index, profile_number

    real(dp), dimension(:), allocatable :: xlist

  continue

! Determine how many/which spanwise stations we ended up with

    allocate(span_stations(nspan)); span_stations = 0
    allocate(span_tag(nspan));      span_tag      = 0

    ngrid = 0
    do i = 1, nprofiles_global
      if ( global_profile(i)%span_station /= 0 ) then
        if ( span_tag(global_profile(i)%span_station) == 0 ) then
          ngrid = ngrid + 1
          span_tag(global_profile(i)%span_station) = 1
          span_stations(ngrid) = global_profile(i)%span_station
        endif
      endif
    end do

    deallocate(span_tag)

    if ( lmpi_master ) write(*,*)'Crossflow: No. of span stations output', ngrid

! Sort the span stations

    allocate(span_sort_index(ngrid)); span_sort_index = 0
    call heap_sort(ngrid,span_stations,span_sort_index)

! Now go over the span stations in order and set up each 2D structured grid

    allocate(sgrid(ngrid))

    lead_dim = size(global_profile(1)%q, 1)

    span_loop : do ii = 1, ngrid

      station = span_stations(span_sort_index(ii))

      nprofiles = 0
      do j = 1, nprofiles_global
        if ( global_profile(j)%span_station==station ) nprofiles = nprofiles + 1
      end do

! First determine the order of the points from LE to TE

      sgrid(ii)%imax = nprofiles
      sgrid(ii)%jmax = global_profile(1)%npoints
      sgrid(ii)%kmax = 1

      allocate(xlist(nprofiles));          xlist          = my_0
      allocate(profile_number(nprofiles)); profile_number = 0
      allocate(sort_index(nprofiles));     sort_index     = 0

      ind = 0
      do j = 1, nprofiles_global
        if ( global_profile(j)%span_station == station ) then
          ind = ind + 1
          xlist(ind)          = global_profile(j)%x(1)
          profile_number(ind) = j
        endif
      end do

      call heap_sort(nprofiles,xlist,sort_index)

! Set up the structured grid

      allocate(sgrid(ii)%x(sgrid(ii)%imax,sgrid(ii)%jmax,sgrid(ii)%kmax))
      allocate(sgrid(ii)%y(sgrid(ii)%imax,sgrid(ii)%jmax,sgrid(ii)%kmax))
      allocate(sgrid(ii)%z(sgrid(ii)%imax,sgrid(ii)%jmax,sgrid(ii)%kmax))
      allocate(sgrid(ii)%q(lead_dim,                                           &
                           sgrid(ii)%imax,sgrid(ii)%jmax,sgrid(ii)%kmax))

      sgrid(ii)%x = my_0
      sgrid(ii)%y = my_0
      sgrid(ii)%z = my_0
      sgrid(ii)%q = my_0

      k = 1

      do i = 1, nprofiles
        ind = sort_index(i)
        source = profile_number(ind)
        do j = 1, global_profile(source)%npoints
          sgrid(ii)%x(i,j,k)   = global_profile(source)%x(j)
          sgrid(ii)%y(i,j,k)   = global_profile(source)%y(j)
          sgrid(ii)%z(i,j,k)   = global_profile(source)%z(j)
          sgrid(ii)%q(:,i,j,k) = global_profile(source)%q(:,j)
        end do
      end do

      deallocate(xlist,profile_number,sort_index)

    end do span_loop

    deallocate(span_stations,span_sort_index)

! Write out the structured grid as a Plot3D file for viewing

    if ( lmpi_master ) call write_p3d()

! Construct node normals on the structured grid

    call ijk_metrics()

! Write out in more general quad format

    if ( lmpi_master ) call write_quads()

  end subroutine crossflow


!============================ IJK_METRICS ====================================80
!
!  Computes metrics for structured grid
!
!=============================================================================80
  subroutine ijk_metrics()

    use kinddefs,        only : dp
    use fun3d_constants, only : my_4th, my_half, my_0

    integer :: i, j, k, ii

    real(dp) :: x1,y1,z1,x2,y2,z2,x3,y3,z3,x4,y4,z4
    real(dp) :: xc,yc,zc,x12,y12,z12,x23,y23,z23,x34,y34,z34,x14,y14,z14

    real(dp), dimension(3) :: a1,a2

  continue

    do ii = 1, ngrid

      allocate(sgrid(ii)%xn(sgrid(ii)%imax,sgrid(ii)%jmax,sgrid(ii)%kmax))
      allocate(sgrid(ii)%yn(sgrid(ii)%imax,sgrid(ii)%jmax,sgrid(ii)%kmax))
      allocate(sgrid(ii)%zn(sgrid(ii)%imax,sgrid(ii)%jmax,sgrid(ii)%kmax))
      allocate(sgrid(ii)%area(sgrid(ii)%imax,sgrid(ii)%jmax,sgrid(ii)%kmax))

      sgrid(ii)%xn   = my_0
      sgrid(ii)%yn   = my_0
      sgrid(ii)%zn   = my_0
      sgrid(ii)%area = my_0

      k = 1

! First get the area-weighted normals
! Take absolute value on areas because we don't care which way the
! area points, just want a positive result

      do j = 1, sgrid(ii)%jmax-1
        do i = 1, sgrid(ii)%imax-1

          x1 = sgrid(ii)%x(i,j,k)
          y1 = sgrid(ii)%y(i,j,k)
          z1 = sgrid(ii)%z(i,j,k)

          x2 = sgrid(ii)%x(i+1,j,k)
          y2 = sgrid(ii)%y(i+1,j,k)
          z2 = sgrid(ii)%z(i+1,j,k)

          x3 = sgrid(ii)%x(i+1,j+1,k)
          y3 = sgrid(ii)%y(i+1,j+1,k)
          z3 = sgrid(ii)%z(i+1,j+1,k)

          x4 = sgrid(ii)%x(i,j+1,k)
          y4 = sgrid(ii)%y(i,j+1,k)
          z4 = sgrid(ii)%z(i,j+1,k)

          xc = my_4th*(x1+x2+x3+x4)
          yc = my_4th*(y1+y2+y3+y4)
          zc = my_4th*(z1+z2+z3+z4)

          x12 = my_half*(x1+x2)
          y12 = my_half*(y1+y2)
          z12 = my_half*(z1+z2)

          x23 = my_half*(x2+x3)
          y23 = my_half*(y2+y3)
          z23 = my_half*(z2+z3)

          x34 = my_half*(x3+x4)
          y34 = my_half*(y3+y4)
          z34 = my_half*(z3+z4)

          x14 = my_half*(x1+x4)
          y14 = my_half*(y1+y4)
          z14 = my_half*(z1+z4)

          a1 = tria_area(x1,y1,z1,x14,y14,z14,xc,yc,zc)
          a2 = tria_area(x1,y1,z1,x12,y12,z12,xc,yc,zc)
          sgrid(ii)%xn(i,j,k) = sgrid(ii)%xn(i,j,k) + abs(a1(1)) + abs(a2(1))
          sgrid(ii)%yn(i,j,k) = sgrid(ii)%yn(i,j,k) + abs(a1(2)) + abs(a2(2))
          sgrid(ii)%zn(i,j,k) = sgrid(ii)%zn(i,j,k) + abs(a1(3)) + abs(a2(3))

          a1 = tria_area(x2,y2,z2,x12,y12,z12,xc,yc,zc)
          a2 = tria_area(x2,y2,z2,x23,y23,z23,xc,yc,zc)
          sgrid(ii)%xn(i+1,j,k)= sgrid(ii)%xn(i+1,j,k) + abs(a1(1)) + abs(a2(1))
          sgrid(ii)%yn(i+1,j,k)= sgrid(ii)%yn(i+1,j,k) + abs(a1(2)) + abs(a2(2))
          sgrid(ii)%zn(i+1,j,k)= sgrid(ii)%zn(i+1,j,k) + abs(a1(3)) + abs(a2(3))

          a1 = tria_area(x3,y3,z3,x23,y23,z23,xc,yc,zc)
          a2 = tria_area(x3,y3,z3,x34,y34,z34,xc,yc,zc)
          sgrid(ii)%xn(i+1,j+1,k)=sgrid(ii)%xn(i+1,j+1,k)+abs(a1(1))+abs(a2(1))
          sgrid(ii)%yn(i+1,j+1,k)=sgrid(ii)%yn(i+1,j+1,k)+abs(a1(2))+abs(a2(2))
          sgrid(ii)%zn(i+1,j+1,k)=sgrid(ii)%zn(i+1,j+1,k)+abs(a1(3))+abs(a2(3))

          a1 = tria_area(x4,y4,z4,x34,y34,z34,xc,yc,zc)
          a2 = tria_area(x4,y4,z4,x14,y14,z14,xc,yc,zc)
          sgrid(ii)%xn(i,j+1,k)= sgrid(ii)%xn(i,j+1,k) + abs(a1(1)) + abs(a2(1))
          sgrid(ii)%yn(i,j+1,k)= sgrid(ii)%yn(i,j+1,k) + abs(a1(2)) + abs(a2(2))
          sgrid(ii)%zn(i,j+1,k)= sgrid(ii)%zn(i,j+1,k) + abs(a1(3)) + abs(a2(3))

        end do
      end do

! Now get the areas and normalize

      do i = 1, sgrid(ii)%imax
        do j = 1, sgrid(ii)%jmax
          sgrid(ii)%area(i,j,k) = sqrt(sgrid(ii)%xn(i,j,k)**2                  &
                                     + sgrid(ii)%yn(i,j,k)**2                  &
                                     + sgrid(ii)%zn(i,j,k)**2)
          sgrid(ii)%xn(i,j,k) = sgrid(ii)%xn(i,j,k) / sgrid(ii)%area(i,j,k)
          sgrid(ii)%yn(i,j,k) = sgrid(ii)%yn(i,j,k) / sgrid(ii)%area(i,j,k)
          sgrid(ii)%zn(i,j,k) = sgrid(ii)%zn(i,j,k) / sgrid(ii)%area(i,j,k)
        end do
      end do

    end do

  end subroutine ijk_metrics


!============================ TRIA_AREA ======================================80
!
!  Computes normal of a triangle
!
!=============================================================================80
  function tria_area(x0,y0,z0,xl,yl,zl,xc,yc,zc)

    use fun3d_constants, only : my_half
    use kinddefs,        only : dp

    real(dp), intent(in) :: x0,y0,z0,xl,yl,zl,xc,yc,zc

    real(dp), dimension(3) :: tria_area

  continue

    tria_area(1) = my_half*( (yc-y0)*(zl-z0) - (zc-z0)*(yl-y0) )
    tria_area(2) = my_half*( (zc-z0)*(xl-x0) - (xc-x0)*(zl-z0) )
    tria_area(3) = my_half*( (xc-x0)*(yl-y0) - (yc-y0)*(xl-x0) )

  end function tria_area


!============================ VALIDITY_CHECK =================================80
!
!  See if boundary index is allowed to seed a profile
!
!=============================================================================80
  function validity_check(ibc)

    integer, intent(in) :: ibc

    logical :: validity_check

    integer :: i

  continue

    validity_check = .false.

    search : do i = 1, nvalid_bcs
      if ( ibc == valid_bcs(i) ) then
        validity_check = .true.
        exit search
      endif
    end do search

  end function validity_check

!============================ WRITE_P3D ======================================80
!
!  Writes structured mesh to Plot3D files
!
!=============================================================================80
  subroutine write_p3d()

    use info_depr,       only : xmach, alpha, re, simulation_time
    use fluid,           only : gm1
    use fun3d_constants, only : my_half

    integer :: i, j, k, iu, n, ii

  continue

! p3d expects conserved variables

    do ii = 1, ngrid
      sgrid(ii)%q(5,:,:,:)= sgrid(ii)%q(5,:,:,:)/gm1                           &
                + my_half*sgrid(ii)%q(1,:,:,:)*(sgrid(ii)%q(2,:,:,:)**2        &
                                               +sgrid(ii)%q(3,:,:,:)**2        &
                                               +sgrid(ii)%q(4,:,:,:)**2)
      sgrid(ii)%q(2,:,:,:) = sgrid(ii)%q(1,:,:,:)*sgrid(ii)%q(2,:,:,:)
      sgrid(ii)%q(3,:,:,:) = sgrid(ii)%q(1,:,:,:)*sgrid(ii)%q(3,:,:,:)
      sgrid(ii)%q(4,:,:,:) = sgrid(ii)%q(1,:,:,:)*sgrid(ii)%q(4,:,:,:)
    end do

    iu = 46

! grid file

    open(iu,file='crossflow.xyz')
    rewind(iu)
    write(iu,*) ngrid
    write(iu,*) (sgrid(ii)%imax, sgrid(ii)%jmax, sgrid(ii)%kmax, ii = 1, ngrid)
    do ii = 1, ngrid
      write(iu,*) (((sgrid(ii)%x(i,j,k),i=1,sgrid(ii)%imax),                   &
                                        j=1,sgrid(ii)%jmax),                   &
                                        k=1,sgrid(ii)%kmax),                   &
                  (((sgrid(ii)%y(i,j,k),i=1,sgrid(ii)%imax),                   &
                                        j=1,sgrid(ii)%jmax),                   &
                                        k=1,sgrid(ii)%kmax),                   &
                  (((sgrid(ii)%z(i,j,k),i=1,sgrid(ii)%imax),                   &
                                        j=1,sgrid(ii)%jmax),                   &
                                        k=1,sgrid(ii)%kmax)
    end do
    close(iu)

! q file

    open(iu,file='crossflow.q')
    rewind(iu)
    write(iu,*) ngrid  ! single block grid
    write(iu,*) (sgrid(ii)%imax, sgrid(ii)%jmax, sgrid(ii)%kmax, ii = 1, ngrid)
    do ii = 1, ngrid
      write(iu,*) xmach, alpha, re, simulation_time
      write(iu,*) ((((sgrid(ii)%q(n,i,j,k),i=1,sgrid(ii)%imax),                &
                                           j=1,sgrid(ii)%jmax),                &
                                           k=1,sgrid(ii)%kmax), n=1,5)
    end do
    close(iu)

! convert back to primitive

    do ii = 1, ngrid
      sgrid(ii)%q(2,:,:,:) = sgrid(ii)%q(2,:,:,:) / sgrid(ii)%q(1,:,:,:)
      sgrid(ii)%q(3,:,:,:) = sgrid(ii)%q(3,:,:,:) / sgrid(ii)%q(1,:,:,:)
      sgrid(ii)%q(4,:,:,:) = sgrid(ii)%q(4,:,:,:) / sgrid(ii)%q(1,:,:,:)
      sgrid(ii)%q(5,:,:,:) = gm1*(sgrid(ii)%q(5,:,:,:)                         &
                           - my_half*sgrid(ii)%q(1,:,:,:)*                     &
                                    (sgrid(ii)%q(2,:,:,:)**2                   &
                                    +sgrid(ii)%q(3,:,:,:)**2                   &
                                    +sgrid(ii)%q(4,:,:,:)**2))
    end do

  end subroutine write_p3d


!============================ WRITE_QUADS ====================================80
!
!  Writes structured mesh as general quads
!
!=============================================================================80
  subroutine write_quads()

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0
    use nml_governing_equations, only : viscous_terms

    integer :: i, j, k, iu, n, ic, nnodes, nfaces, ind, ld, ii

    real(dp), dimension(:,:,:), allocatable :: vdotn

    character(len=4)  :: c4
    character(len=80) :: formatdesc1, formatdesc2, formatdesc3

  continue

    iu = 46

    open(iu,file='crossflow.dat')
    rewind(iu)

    write(iu,*) 'title="tecplot geometry file"'
    if ( viscous_terms == 'turbulent' ) then
      write(iu,*) 'variables="x","y","z","rho","u","v","w","p","mu_t","vdotn"'
    else
      write(iu,*) 'variables="x","y","z","rho","u","v","w","p","vdotn"'
    endif

    formatdesc1 = "('zone t=',a,', i=',i6,', j=',i6,', f=feblock')"
    formatdesc2 = "(10e23.15)"
    formatdesc3 = "(4i10)"

    do ii = 1, ngrid

      write(c4,"(i4)") ii
      ic = -999
      select case (ii)
        case (0:9)
          ic = 4
        case (10:99)
          ic = 3
        case (100:999)
          ic = 2
        case (1000:9999)
          ic = 1
      end select

      nnodes = sgrid(ii)%imax*sgrid(ii)%jmax
      nfaces = (sgrid(ii)%imax-1)*(sgrid(ii)%jmax-1)

      ld = size(sgrid(ii)%q,1)

! Compute velocity normal to plane for plotting

      allocate(vdotn(sgrid(ii)%imax,sgrid(ii)%jmax,sgrid(ii)%kmax));vdotn = my_0

      do i = 1, sgrid(ii)%imax
        do j = 1, sgrid(ii)%jmax
          do k = 1, sgrid(ii)%kmax
            vdotn(i,j,k) = sgrid(ii)%q(2,i,j,k)*sgrid(ii)%xn(i,j,k)            &
                         + sgrid(ii)%q(3,i,j,k)*sgrid(ii)%yn(i,j,k)            &
                         + sgrid(ii)%q(4,i,j,k)*sgrid(ii)%zn(i,j,k)
          end do
        end do
      end do

      write(iu,formatdesc1) "plane." // c4(ic:4),nnodes,nfaces
      write(iu,formatdesc2) (((sgrid(ii)%x(i,j,k),i=1,sgrid(ii)%imax),         &
                                                  j=1,sgrid(ii)%jmax),         &
                                                  k=1,sgrid(ii)%kmax)
      write(iu,formatdesc2) (((sgrid(ii)%y(i,j,k),i=1,sgrid(ii)%imax),         &
                                                  j=1,sgrid(ii)%jmax),         &
                                                  k=1,sgrid(ii)%kmax)
      write(iu,formatdesc2) (((sgrid(ii)%z(i,j,k),i=1,sgrid(ii)%imax),         &
                                                  j=1,sgrid(ii)%jmax),         &
                                                  k=1,sgrid(ii)%kmax)
      write(iu,formatdesc2) ((((sgrid(ii)%q(n,i,j,k),i=1,sgrid(ii)%imax),      &
                                                  j=1,sgrid(ii)%jmax),         &
                                                  k=1,sgrid(ii)%kmax),n=1,ld)
      write(iu,formatdesc2) (((vdotn(i,j,k),i=1,sgrid(ii)%imax),               &
                                            j=1,sgrid(ii)%jmax),               &
                                            k=1,sgrid(ii)%kmax)

      deallocate(vdotn)

      k = 1
      ind = 1
      do j = 1, sgrid(ii)%jmax-1
        do i = 1, sgrid(ii)%imax-1
          write(iu,formatdesc3)ind,ind+1,ind+1+sgrid(ii)%imax,ind+sgrid(ii)%imax
          ind = ind+1
        end do
        ind = ind+1
      end do

    end do

    close(iu)

  end subroutine write_quads

end module profiles

!============================ ADAPTIVE_ENTROPY_FIX ===========================80
!
! Sets the switch for adaptive eigenvalue limiting
!
!=============================================================================80

  pure function adaptive_entropy_fix(rni, xnorm, ynorm, znorm, area, vol1,     &
                                     vol2, gradx1, grady1, gradz1,             &
                                     gradx2, grady2, gradz2, phi1, phi2,       &
                                     ql, qr, second, ndim, eqn_set, c_gen,     &
                                     mu)

    use kinddefs,        only : dp
    use fluid,           only : gm1, xgm1
    use info_depr,       only : ivisc
    use generic_gas_map, only : n_species, n_momx, n_momy, n_momz, n_etot
    use solution_types,  only : generic_gas

    integer,                   intent(in) :: ndim
    integer,                   intent(in) :: eqn_set
    real(dp), dimension(3,2),  intent(in) :: rni
    real(dp),                  intent(in) :: xnorm, ynorm, znorm, area
    real(dp),                  intent(in) :: vol1, vol2, phi1, phi2
    real(dp),                  intent(in) :: gradx1, grady1, gradz1
    real(dp),                  intent(in) :: gradx2, grady2, gradz2
    real(dp),                  intent(in) :: c_gen, mu
    real(dp), dimension(ndim), intent(in) :: ql, qr
    logical,                   intent(in) :: second

    real(dp) :: adaptive_entropy_fix

    real(dp) :: rho, rhol, rhor, ul, ur, vl, vr, wl, wr, pressl, pressr
    real(dp) :: q2l, q2r, enrgyl, enrgyr, hl, hr, ubarl, ubarr
    real(dp) :: wat, u, v, w, q2, h, ubar, c, c2
    real(dp) :: rx1, ry1, rz1, rx2, ry2, rz2, switchv

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_2    = 2.0_dp

    integer,  parameter :: behavior = 10
    integer,  parameter :: powerv = 4
    real(dp), parameter :: poweri = my_2
    real(dp), parameter :: laplcc = my_2

  continue

    rx1 = rni(1,1)
    ry1 = rni(2,1)
    rz1 = rni(3,1)
    rx2 = rni(1,2)
    ry2 = rni(2,2)
    rz2 = rni(3,2)

!   Get left and right state primitive variables

    rhol   = sum(ql(1:n_species))
    ul     = ql(n_momx)
    vl     = ql(n_momy)
    wl     = ql(n_momz)
    pressl = ql(n_etot)
    q2l    = ul*ul + vl*vl + wl*wl
    ubarl  = xnorm*ul + ynorm*vl + znorm*wl

    rhor   = sum(qr(1:n_species))
    ur     = qr(n_momx)
    vr     = qr(n_momy)
    wr     = qr(n_momz)
    pressr = qr(n_etot)
    q2r    = ur*ur + vr*vr + wr*wr
    ubarr  = xnorm*ur + ynorm*vr + znorm*wr

!   Compute Roe averages

    rho  = sqrt(rhol*rhor)
    wat  = rho/(rho + rhor)
    u    = ul*wat + ur*(my_1 - wat)
    v    = vl*wat + vr*(my_1 - wat)
    w    = wl*wat + wr*(my_1 - wat)
    q2   = u*u + v*v + w*w
    ubar = xnorm*u + ynorm*v + znorm*w

!   Compute the remaining needed left and right state variables:

    if ( eqn_set == generic_gas ) then
!     Assume Roe averaging not critical for this application
      c      = c_gen
      c2     = c*c
    else
      enrgyl = pressl*xgm1 + my_half*rhol*q2l
      hl     = (enrgyl + pressl)/rhol

      enrgyr = pressr*xgm1 + my_half*rhor*q2r
      hr     = (enrgyr + pressr)/rhor

      h    = hl*wat + hr*(my_1 - wat)
      c2   = gm1*(h - my_half*q2)
      c    = sqrt(c2)
    end if

!   Compute feature detection switch

    if (.not. second)then
      adaptive_entropy_fix = my_0
    else
      adaptive_entropy_fix =                                                   &
             iswch_coef(rx1,ry1,rz1,rx2,ry2,rz2,gradx1,grady1,gradz1,          &
                        gradx2,grady2,gradz2,pressl,pressr,phi1,phi2,          &
                        ubarl,ubarr,q2l,q2r,q2,c2,laplcc,poweri,behavior)
    end if

!   Compute the cell face reynolds number to make the extra dissipation vanish
!   on low Reynolds number cells faces

    if (ivisc >= 2) then
      switchv  = vswch_coef(wat,rho,q2l,ubarl,q2r,ubarr,ubar,c,vol1,vol2,      &
                              area,powerv,mu)
      adaptive_entropy_fix = max(adaptive_entropy_fix, switchv)
    end if

  end function adaptive_entropy_fix

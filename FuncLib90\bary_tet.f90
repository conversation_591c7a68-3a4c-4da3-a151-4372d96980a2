!================================== BARY_TET =================================80
!
! Barycentric coorinantes of a point in a tet defined by the coordinates of
! the pts a, b, c and d
!
!=============================================================================80

  pure function bary_tet(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz,       &
                         px, py, pz)

    use kinddefs, only : dp

    real(dp),     intent(in) :: ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz,&
                                px, py, pz

    real(dp) :: total
    real(dp), dimension(4) :: bary_tet

    continue

    bary_tet(1)=-det_3x3(px-dx,py-dy,pz-dz,bx-dx,by-dy,bz-dz,cx-dx,cy-dy,cz-dz)
    bary_tet(2)=-det_3x3(ax-dx,ay-dy,az-dz,px-dx,py-dy,pz-dz,cx-dx,cy-dy,cz-dz)
    bary_tet(3)=-det_3x3(ax-dx,ay-dy,az-dz,bx-dx,by-dy,bz-dz,px-dx,py-dy,pz-dz)
    bary_tet(4)=-det_3x3(ax-px,ay-py,az-pz,bx-px,by-py,bz-pz,cx-px,cy-py,cz-pz)

    total  = bary_tet(1)+bary_tet(2)+bary_tet(3)+bary_tet(4)

    bary_tet = bary_tet / total

  end function bary_tet

module solution_writes

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs,            only : dp
  use solution_types,      only : soln_type
  use exact_airfoil,       only : airfoil, exact_zkt_lift
  use info_depr,           only : ngrid
  use cfl_defs,          only : hanim
  use complex_functions,   only : o

  use cfl_defs, only : cfl_m_frechet, cfl_t_frechet

  implicit none

  private

  public :: write_slice_data, remove_slice_data, end_native_volume_data_write

  !beginNoComplexInterface
  public  :: techist
  public  :: solution_output
  public  :: output_global_surface_data
  public  :: fwh_global_surface_data
  public  :: output_tavg_surface_data
  public  :: write_vol_tec
  public  :: write_force
  public  :: print_status
  public  :: need_global_bndry_data
  public  :: animation_requested
  !endNoComplexInterface

  logical :: remove_slice_data = .false.

  integer :: native_unit = -1

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
  real(dp), asynchronous, dimension(:,:), allocatable :: output_data_a
#else
  real(dp), dimension(:,:), allocatable :: output_data_a
#endif

contains



!================================ TECHIST ====================================80
!
! Writes output for plotting in tecplot format
!
!=============================================================================80

  subroutine techist(grid, soln, project, iteration, flow_dir)

    use info_depr,            only : title, tightly_couple
    use nml_nonlinear_solves, only : itime
    use nml_global,           only : moving_grid
    use io,                   only : use_prior, prior_iters, prior_hist
    use string_utils,         only : sprintf, int_to_s
    use grid_types,           only : grid_type
    use system_extensions,    only : se_open
    use file_utils,           only : available_unit
    use solution_types,       only : elasticity, generic_gas
    use inviscid_flux,        only : mean_decouple

    integer,                     intent(in) :: iteration

    type(soln_type),             intent(in) :: soln
    type(grid_type),             intent(in) :: grid

    character(len=*),            intent(in)  :: project
    character(len=*),            intent(in)  :: flow_dir

    integer                       :: n, i, j, eqn, njac_hist, n_offset
    integer                       :: history_dimension, unit_nc, unit_cc
    integer                       :: additional_history_dimension
    character(len=80)             :: temp_string, filename
    character(len=80)             :: filemass, format
    character(len=800)            :: res_string
    real(dp) :: previous_walltime

    logical :: punting

    continue

    punting = .false.

! beginMakeComplexCopy

    filename = trim(flow_dir) // trim(project) // '_hist.dat'
    filemass = trim(flow_dir) // trim(project) // '_masshist.tec'

    unit_nc = available_unit()
    call se_open(unit_nc, file=filename)
    rewind(unit_nc)

    if ( grid%cc ) then
      unit_cc = available_unit()
      call se_open(unit_cc, file=filemass)
      rewind(unit_cc)
    endif

    if ( soln%eqn_set == elasticity ) then
      title = 'GridMove mixed element Convergence History'
    endif

    njac_hist = soln%njac
    if (.not. tightly_couple .and. soln%eqn_set /= generic_gas ) then
      njac_hist = soln%ndim+soln%n_turb
    endif

    if ( mean_decouple ) njac_hist = soln%ndim + 1

    ! FIXME: can write variable names on separate lines to avoid this 780 thing?
    res_string = ' "R_1"'
    do eqn = 1, njac_hist-1
      temp_string = trim(sprintf(' "R_%i0',eqn+1))//'"'
      if(len_trim(res_string) > 780) then
        write(*,*) 'ndim too big (>780) for Tecplot to handle...punting.'
        write(*,*) '...from the subroutine techist yard line.'
        write(*,*) 'len_trim(res_string)=',len_trim(res_string)
        punting = .true.
      end if
      if (.not.punting) res_string = trim(res_string) // trim(temp_string)
    end do

    write(unit_nc,'(a)') 'TITLE="' // trim(title) // '"'
    if ( grid%cc ) write(unit_cc,'(a)') 'TITLE="' // trim(title) // '"'

    if (punting) return

    if ( grid%cc ) then
      write(unit_cc,'(2a)') 'VARIABLES="CurrentIteration"',                    &
          ' "MassFlux" "EnergyFlux" "WallEnergyFlux" '
    endif

    if (itime == 0) then
      if ( soln%eqn_set == elasticity ) then
        write(unit_nc,'(6a)') 'VARIABLES="Iteration"', trim(res_string),       &
          ' "dr:rms" "dr:max" "dr:xloc" ',                                     &
          ' "dr:yloc" "dr:zloc" "Wall Time" "C1" "C2" "C3" "sr(1)" "sr(2)" ',  &
          ' "sr(3)" "C5" "C6" "C7" "C8" "C9" "C10" ',                          &
          ' "C11" "C12" "C13" "C14" "C15" "C16" "C17"'
      else
        write(unit_nc,'(6a)') 'VARIABLES="Iteration"', trim(res_string),       &
          ' "C_L" "C_D" "C_M_x" ',                                             &
          '"C_M_y" "C_M_z" "Wall Time" "C_x" "C_y" "C_z" "C_Lp" "C_Dp" ',      &
          '"C_Lv" "C_Dv" "C_M_xp" "C_M_yp" "C_M_zp" "C_M_xv" "C_M_yv" ',       &
          '"C_M_zv" "C_xp" "C_yp" "C_zp" "C_xv" "C_yv" "C_zv"'
      endif
    else
      if (moving_grid) then
        write(unit_nc,'(8a)') 'VARIABLES="Iteration"', trim(res_string),       &
            ' "C_L" "C_D" "C_M_x" ',                                           &
            '"C_M_y" "C_M_z" "Wall Time" "C_x" "C_y" "C_z" "C_Lp" "C_Dp" ',    &
            '"C_Lv" "C_Dv" "C_M_xp" "C_M_yp" "C_M_zp" "C_M_xv" "C_M_yv" ',     &
            '"C_M_zv" "C_xp" "C_yp" "C_zp" "C_xv" "C_yv" "C_zv" ',             &
            '"Simulation_Time" "Theta_x" "Theta_y" "Theta_z" "Orig_x" ',       &
            '"Orig_y" "Orig_z"'
      else
        write(unit_nc,'(7a)') 'VARIABLES="Iteration"', trim(res_string),       &
            ' "C_L" "C_D" "C_M_x" ',                                           &
            '"C_M_y" "C_M_z" "Wall Time" "C_x" "C_y" "C_z" "C_Lp" "C_Dp" ',    &
            '"C_Lv" "C_Dv" "C_M_xp" "C_M_yp" "C_M_zp" "C_M_xv" "C_M_yv" ',     &
            '"C_M_zv" "C_xp" "C_yp" "C_zp" "C_xv" "C_yv" "C_zv" ',             &
            '"Simulation_Time"'
      end if
    end if

!   set the correct history dimension for prior iterations
!   note: here we distinguish between tecplot output for moving and
!   stationary grids when running in time accurate mode; when reading
!   and writing restart files, we always set additional_history_dimension
!   to 7

    history_dimension   = 25 + njac_hist

    additional_history_dimension = 0

    if (itime > 0) then
      additional_history_dimension = 1      ! for sim time
      if (moving_grid) then                 ! for 6 DOF info
        additional_history_dimension = additional_history_dimension + 6
      end if
    end if

    history_dimension = history_dimension + additional_history_dimension

    if(use_prior == 1) then

      j = size(prior_hist,2)
      if (history_dimension > j) then

        write(*,*) 'access past end of prior_hist array...punting.'
        write(*,*) '...from the subroutine techist yard line.'
        write(*,*) '...1st dimension evidence',prior_iters,size(prior_hist,1)
        write(*,*) '...2nd dimension evidence',history_dimension,j
        punting = .true.

        if(punting) return

      end if

      write(unit_nc,'("ZONE  I=",i0," F=POINT")') iteration + prior_iters
      format = '(i0,'//int_to_s(history_dimension)//'(1x,e17.10))'
      do i = 1, prior_iters
        write(unit_nc,format) i,(real(prior_hist(i,j),dp),j=1,history_dimension)
      end do
    else
      write(unit_nc,'("ZONE  I=",i0," F=POINT")') iteration
    end if

    previous_walltime = 0._dp
    n_offset          = 0
    if ( use_prior == 1 .and. prior_iters > 0 ) then
      if(prior_iters <= size(prior_hist,1) .and. &
         njac_hist+6 <= size(prior_hist,2)) then
        previous_walltime = prior_hist(prior_iters,njac_hist+6)
        n_offset          = prior_iters
      else
        write(*,*) 'access past end of prior_hist array...punting.'
        write(*,*) '...from the subroutine techist yard line.'
        write(*,*) '...1st dimension evidence',prior_iters,size(prior_hist,1)
        write(*,*) '...2nd dimension evidence',njac_hist+6,size(prior_hist,2)
        punting = .true.
      end if
    end if

    if(punting) return

    do n = 1, iteration

      if (itime == 0) then

        if ( grid%cc ) then
          write(unit_cc,'(i0,3(1x,e15.7))') n,                                 &
           abs(real(soln%totforce(n)%mflux_integral,dp)),                      &
           abs(real(soln%totforce(n)%eflux_integral,dp)),                      &
           real(soln%totforce(n)%weflux_integral,dp)
        endif

        format = '(i0,'//int_to_s(njac_hist+25)//'(1x,e17.10))'
          write(unit_nc,format) n+n_offset,                                    &
          (real(soln%rmshist(eqn,n,1),dp   ),eqn=1,njac_hist),                 &
           real(soln%totforce(n)%cl,dp   ),                                    &
           real(soln%totforce(n)%cd,dp   ),                                    &
           real(soln%totforce(n)%cmx,dp   ),                                   &
           real(soln%totforce(n)%cmy,dp   ),                                   &
           real(soln%totforce(n)%cmz,dp   ),                                   &
           real((soln%walltime(n)+previous_walltime),dp   ),                   &
           real(soln%totforce(n)%cx,dp   ),                                    &
           real(soln%totforce(n)%cy,dp   ),                                    &
           real(soln%totforce(n)%cz,dp   ),                                    &
           real(soln%totforce(n)%clp,dp   ),                                   &
           real(soln%totforce(n)%cdp,dp   ),                                   &
           real(soln%totforce(n)%clv,dp   ),                                   &
           real(soln%totforce(n)%cdv,dp   ),                                   &
           real(soln%totforce(n)%cmxp,dp   ),                                  &
           real(soln%totforce(n)%cmyp,dp   ),                                  &
           real(soln%totforce(n)%cmzp,dp   ),                                  &
           real(soln%totforce(n)%cmxv,dp   ),                                  &
           real(soln%totforce(n)%cmyv,dp   ),                                  &
           real(soln%totforce(n)%cmzv,dp   ),                                  &
           real(soln%totforce(n)%cxp,dp   ),                                   &
           real(soln%totforce(n)%cyp,dp   ),                                   &
           real(soln%totforce(n)%czp,dp   ),                                   &
           real(soln%totforce(n)%cxv,dp   ),                                   &
           real(soln%totforce(n)%cyv,dp   ),                                   &
           real(soln%totforce(n)%czv,dp   )

      else

        if (moving_grid) then

          format = '(i0,'//int_to_s(njac_hist+32)//'(1x,e17.10))'
          write(unit_nc,format) n+n_offset,                                    &
            (real(soln%rmshist(eqn,n,1),dp   ),eqn=1,njac_hist),               &
             real(soln%totforce(n)%cl,dp   ),                                  &
             real(soln%totforce(n)%cd,dp   ),                                  &
             real(soln%totforce(n)%cmx,dp   ),                                 &
             real(soln%totforce(n)%cmy,dp   ),                                 &
             real(soln%totforce(n)%cmz,dp   ),                                 &
             real((soln%walltime(n)+previous_walltime),dp   ),                 &
             real(soln%totforce(n)%cx,dp   ),                                  &
             real(soln%totforce(n)%cy,dp   ),                                  &
             real(soln%totforce(n)%cz,dp   ),                                  &
             real(soln%totforce(n)%clp,dp   ),                                 &
             real(soln%totforce(n)%cdp,dp   ),                                 &
             real(soln%totforce(n)%clv,dp   ),                                 &
             real(soln%totforce(n)%cdv,dp   ),                                 &
             real(soln%totforce(n)%cmxp,dp   ),                                &
             real(soln%totforce(n)%cmyp,dp   ),                                &
             real(soln%totforce(n)%cmzp,dp   ),                                &
             real(soln%totforce(n)%cmxv,dp   ),                                &
             real(soln%totforce(n)%cmyv,dp   ),                                &
             real(soln%totforce(n)%cmzv,dp   ),                                &
             real(soln%totforce(n)%cxp,dp   ),                                 &
             real(soln%totforce(n)%cyp,dp   ),                                 &
             real(soln%totforce(n)%czp,dp   ),                                 &
             real(soln%totforce(n)%cxv,dp   ),                                 &
             real(soln%totforce(n)%cyv,dp   ),                                 &
             real(soln%totforce(n)%czv,dp   ),                                 &
             real(soln%simtime(n),dp   ),                                      &
             real(grid%thetax(n),dp   ),                                       &
             real(grid%thetay(n),dp   ),                                       &
             real(grid%thetaz(n),dp   ),                                       &
             real(grid%xorig(n),dp   ),                                        &
             real(grid%yorig(n),dp   ),                                        &
             real(grid%zorig(n),dp   )

        else

          format = '(i0,'//int_to_s(njac_hist+26)//'(1x,e17.10))'
          write(unit_nc,format) n+n_offset,                                    &
            (real(soln%rmshist(eqn,n,1),dp   ),eqn=1,njac_hist),               &
             real(soln%totforce(n)%cl,dp   ),                                  &
             real(soln%totforce(n)%cd,dp   ),                                  &
             real(soln%totforce(n)%cmx,dp   ),                                 &
             real(soln%totforce(n)%cmy,dp   ),                                 &
             real(soln%totforce(n)%cmz,dp   ),                                 &
             real((soln%walltime(n)+previous_walltime),dp   ),                 &
             real(soln%totforce(n)%cx,dp   ),                                  &
             real(soln%totforce(n)%cy,dp   ),                                  &
             real(soln%totforce(n)%cz,dp   ),                                  &
             real(soln%totforce(n)%clp,dp   ),                                 &
             real(soln%totforce(n)%cdp,dp   ),                                 &
             real(soln%totforce(n)%clv,dp   ),                                 &
             real(soln%totforce(n)%cdv,dp   ),                                 &
             real(soln%totforce(n)%cmxp,dp   ),                                &
             real(soln%totforce(n)%cmyp,dp   ),                                &
             real(soln%totforce(n)%cmzp,dp   ),                                &
             real(soln%totforce(n)%cmxv,dp   ),                                &
             real(soln%totforce(n)%cmyv,dp   ),                                &
             real(soln%totforce(n)%cmzv,dp   ),                                &
             real(soln%totforce(n)%cxp,dp   ),                                 &
             real(soln%totforce(n)%cyp,dp   ),                                 &
             real(soln%totforce(n)%czp,dp   ),                                 &
             real(soln%totforce(n)%cxv,dp   ),                                 &
             real(soln%totforce(n)%cyv,dp   ),                                 &
             real(soln%totforce(n)%czv,dp   ),                                 &
             real(soln%simtime(n),dp   )

        end if

      end if

    end do

    close(unit_nc)
    if ( grid%cc ) close(unit_cc)

! endMakeComplexCopy

  end subroutine techist

!================================= SOLUTION_OUTPUT ===========================80
!
! Driver routine for output of solution data for visualization/post processing
! (data output from boundaries, extracted surfaces, or volumes)
!
!=============================================================================80

  subroutine solution_output(grid, soln, time_step, nml_path, sadj)

    use grid_types,          only : grid_type
    use solution_types,      only : soln_type, compressible
    use solution_globals,    only : slice_bndry_data, get_global_bndry_data
    use boundary_slicing,    only : transform_to_moving_frame,                 &
                                    transform_to_inertial_frame
    use moving_body_types,   only : observer
    use sampling_output,     only : output_flowfield_sampling
    use sampling_main,       only : survey
    use info_depr,           only : grad_x_y_z_contents, cc_primal
    use nml_global,          only : grid_motion_only
    use nml_boundary_output, only : need_resid_bnd=>need_resid,                &
                                    need_turbresid_bnd=>need_turbresid
    use nml_volume_output,   only : need_resid_vol=>need_resid,                &
                                    need_turbresid_vol=>need_turbresid,        &
                                    need_gradients_vol=>need_gradients
    use nml_sampling_output, only : need_resid_smp=>need_resid,                &
                                    need_turbresid_smp=>need_turbresid
    use nml_sampling_output, only : need_gradients_smp=>need_gradients
    use nml_boundary_output, only : need_gradients_bnd=>need_gradients
    use lmpi_app,            only : lmpi_xfer
    use lmpi,                only : lmpi_master
    use thermo,              only : q_type, conserved_q_type, primitive_q_type,&
                                    etop, ptoe
    use turb_util,           only : turbgrad
    use grid_motion_helpers, only : transform_coord, transform_vector
    use node_avg_cc,         only : qt_avg_to_nodes
    use solve_box,           only : reference_q_read
    use solution_adj,        only : sadj_type
    use massoud,             only : write_mdo_surface_data

    integer,                                         intent(in)    :: time_step

    type(grid_type),                                 intent(inout) :: grid
    type(soln_type),                                 intent(inout) :: soln
    type(sadj_type), optional,                       intent(inout) :: sadj

    character(len=*), intent(in) :: nml_path

    logical :: compute_gradients, xfer_res, xfer_turbres, output_occurs
    logical :: adjoint_mode

    logical :: verbose = .false.

    integer :: ib, ref_frame, output_ref_frame, i

  continue

    if ( grid%origin > 1 .and. grid%cc ) return !skip agglomerated cc grids

    adjoint_mode = .false.
    if ( present(sadj) ) adjoint_mode = .true.

    if (.not. lmpi_master) verbose = .false.

    output_occurs = .false.

    if (output_global_bndry(grid%igrid,adjoint_mode) .or.                      &
        output_flowfield_sampling(adjoint_mode) .or.                           &
        output_global_volume(grid%igrid,adjoint_mode) .or.                     &
        output_slice_global_bndry() .or.                                       &
        output_fwh_global_data() .or. output_tavg_global_bndry()  .or.         &
        output_aero_loads()      .or. output_massoud()) then
      output_occurs = .true.
    end if

    if (.not. output_occurs) return

!   Transfer residual data across processor boundariess if required for output

    xfer_res     = .false.
    xfer_turbres = .false.

    if (output_global_bndry(grid%igrid,adjoint_mode)) then
      if (need_resid_bnd) then
        xfer_res = .true.
      end if
      if (need_turbresid_bnd) then
        xfer_turbres = .true.
      end if
    end if
    if (output_global_volume(grid%igrid,adjoint_mode)) then
      if (need_resid_vol) then
        xfer_res = .true.
      end if
      if (need_turbresid_vol) then
        xfer_turbres = .true.
      end if
    end if
    if (output_flowfield_sampling(adjoint_mode)) then
      if (need_resid_smp) then
        xfer_res = .true.
      end if
      if (need_turbresid_smp) then
        xfer_turbres = .true.
      end if
    end if

    if (grid_motion_only) then
      xfer_res     = .false.
      xfer_turbres = .false.
    end if

    if (xfer_res) then
      if (verbose) write(*,*) 'calling lmpi_xfer(soln%res)'
      if ( adjoint_mode ) then
        do i = 1, size(sadj%res,3)
          call lmpi_xfer(sadj%res(:,:,i))
        end do
      else
        call lmpi_xfer(soln%res)
      end if
    end if
    if (xfer_turbres) then
      if (verbose) write(*,*) 'calling lmpi_xfer(soln%turbres)'
      if ( adjoint_mode ) then
        do i = 1, size(sadj%res,3)
          call lmpi_xfer(sadj%res(:,:,i))
        end do
      else
        call lmpi_xfer(soln%turbres)
      end if
    end if

!   Convert conservative to primitive if not already in primitive form
!   Also, for cell-center path, average solution to the nodes

    ensure_qnode_primitive : if ( q_type == conserved_q_type ) then

      if ( soln%eqn_set == compressible .and. .not.(grid_motion_only) ) then
        if ( cc_primal ) then
          if (verbose) write(*,*) 'calling qt_avg_to_nodes (with pressure)'
          call qt_avg_to_nodes(grid, soln, pressure_not_temperature=.true.)
        end if
        if (verbose) write(*,*) 'calling etop'
        call etop(size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set)
      end if

    end if ensure_qnode_primitive

    if ( soln%eqn_set == compressible .and. reference_q_read ) then
      call etop(size(soln%q_dof_res0,2), soln%q_dof_res0, soln%n_q,          &
        soln%eqn_set,.true.)
    endif

!   Transform solution and grid to desired output frame of reference; the
!   transformation of velocity assumes qnode is in primitive form; the grid and
!   solution coming into this routine are assumed to be in the inertial frame;
!   transform skin friction components that were computed in the inerial frame

    ref_frame = 0

    output_ref_frame = -1  ! default to observer

    call get_output_ref_frame(output_ref_frame)

    if (output_ref_frame /= ref_frame) then

      if (verbose) write(*,*) 'calling transforms to frame: ', output_ref_frame

      if (grid_motion_only) then                      ! transform grid only

        call transform_coord(grid%x,                                           &
                             grid%y,                                           &
                             grid%z,                                           &
                             observer%inv_transform)

      else                                            ! transform grid and soln

        call transform_to_moving_frame(grid%nnodes01,                          &
                                       grid%x,                                 &
                                       grid%y,                                 &
                                       grid%z,                                 &
                                       soln%q_dof,                             &
                                       observer%inv_transform,                 &
                                       observer%body_lin_vel,                  &
                                       observer%body_ang_vel,                  &
                                       observer%xcg,                           &
                                       observer%ycg,                           &
                                       observer%zcg)

        do ib = 1,grid%nbound
          call transform_vector(soln%bcforce(ib)%cfx_t,                        &
                                soln%bcforce(ib)%cfy_t,                        &
                                soln%bcforce(ib)%cfz_t,                        &
                                observer%inv_transform)
          call transform_vector(soln%bcforce(ib)%cfx_q,                        &
                                soln%bcforce(ib)%cfy_q,                        &
                                soln%bcforce(ib)%cfz_q,                        &
                                observer%inv_transform)
        end do

      end if

      ref_frame = output_ref_frame

    end if

!   Compute Green-Gauss type gradients (for vorticity-related output) if needed

    compute_gradients = .false.

    if (output_global_bndry(grid%igrid,adjoint_mode)) then
      if (need_gradients_bnd) then
        if ((trim(grad_x_y_z_contents) /= 'turbgrad') .or. ref_frame /= 0) then
          compute_gradients = .true.
        end if
      end if
    end if
    if (output_global_volume(grid%igrid,adjoint_mode)) then
      if (need_gradients_vol) then
        if ((trim(grad_x_y_z_contents) /= 'turbgrad') .or. ref_frame /= 0) then
          compute_gradients = .true.
        end if
      end if
    end if
    if (output_flowfield_sampling(adjoint_mode)) then
      if (need_gradients_smp) then
        if ((trim(grad_x_y_z_contents) /= 'turbgrad') .or. ref_frame /= 0) then
          compute_gradients = .true.
        end if
      end if
    end if

    if (grid_motion_only) compute_gradients = .false.
    if (cc_primal)        compute_gradients = .false.

    if (compute_gradients) then
      if (verbose) write(*,*) 'calling turbgrad'
      call turbgrad(soln%eqn_set,                                              &
                    grid%nnodes0, grid%nnodes01, grid%nedgeloc, grid%eptr,     &
                    soln%q_dof,   grid%x,        grid%y,        grid%z,        &
                    grid%dxdt,    grid%dydt,     grid%dzdt,                    &
                    grid%xn,      grid%yn,       grid%zn,       grid%ra,       &
                    grid%vol,     soln%gradx,    soln%grady,    soln%gradz,    &
                    grid%nbound,  grid%bc,       grid%nedgeloc_2d,             &
                    grid%node_pairs_2d,          grid%nnodes0_2d,              &
                    grid%nelem,   grid%elem,     soln%n_tot,    soln%n_grd)
      call lmpi_xfer(soln%gradx)
      call lmpi_xfer(soln%grady)
      call lmpi_xfer(soln%gradz)
    end if

!   Gather a global image of the solution on the boundaries

    if ( need_global_bndry_data(grid%igrid, adjoint_mode) ) then
      if (verbose) write(*,*) 'calling get_global_bndry_data'
      call get_global_bndry_data(grid%nbound, grid, soln,                      &
                                 soln%global_bndry_data, ref_frame, sadj,      &
                                 adjoint_mode)
    end if

!   Output solution data on boundaries

    if ( output_global_bndry(grid%igrid,adjoint_mode) ) then
      if (verbose) write(*,*) 'calling output_global_surface_data'
       call output_global_surface_data(grid, soln, time_step, &
                                       adjoint_mode)
    end if

!   Output sampled solution (sampled on plane, sphere, box etc. geometries)

    if (output_flowfield_sampling(adjoint_mode)) then
      if (verbose) write(*,*) 'calling survey'
      call survey(grid, soln, time_step, nml_path, sadj)
    end if

!   Output solution data throughout the domain (each processor writes own file)

    if ( output_global_volume(grid%igrid,adjoint_mode) ) then
      if (verbose) write(*,*) 'calling write_vol_tec'
      call write_vol_tec(grid, soln, time_step, sadj)
    end if

!   Output Cp, Cf components at selected slices (like box5/6 codes)

    if ( output_slice_global_bndry() ) then
      if (verbose) write(*,*) 'calling output_slice_global_bndry'
      call slice_bndry_data( grid, soln, time_step, nml_path )
      call write_slice_data( grid, time_step )
    end if

!   Output unsteady aero loads for external structural model

    if ( output_aero_loads() ) then
      if (verbose) write(*,*) 'calling write_mdo_surface_data - aero_loads'
      call write_mdo_surface_data( grid, soln, time_step,                      &
                                   write_aero_data=.true. )
    end if

!   Output "massoud" data for aeroelastcs/design

    if ( output_massoud () ) then
      if (verbose) write(*,*) 'calling write_mdo_surface_data - massoud data'
      call write_mdo_surface_data( grid, soln, time_step )
    end if

!   Output time-averaged solution data on boundaries
!   Note: the time-averaged solution data is currently untouched by the
!   reference frame transformations above

    if ( output_tavg_global_bndry() ) then
      if (verbose) write(*,*) 'calling output_tavg_surface_data'
      call output_tavg_surface_data(grid, soln, time_step)
    end if

!   Convert back to inertial frame

    if (ref_frame /= 0 ) then

      if (verbose) write(*,*) 'calling transforms to inertial frame'

      if (grid_motion_only) then                      ! transform grid only

        call transform_coord(grid%x,                                           &
                             grid%y,                                           &
                             grid%z,                                           &
                             observer%transform_matrix)

      else                                            ! transform grid and soln

        call transform_to_inertial_frame(grid%nnodes01,                        &
                                         grid%x,                               &
                                         grid%y,                               &
                                         grid%z,                               &
                                         soln%q_dof,                           &
                                         observer%transform_matrix,            &
                                         observer%body_lin_vel,                &
                                         observer%body_ang_vel,                &
                                         observer%xcg,                         &
                                         observer%ycg,                         &
                                         observer%zcg)

        do ib = 1,grid%nbound
          call transform_vector(soln%bcforce(ib)%cfx_t,                        &
                               soln%bcforce(ib)%cfy_t,                         &
                               soln%bcforce(ib)%cfz_t,                         &
                               observer%transform_matrix)
          call transform_vector(soln%bcforce(ib)%cfx_q,                        &
                                soln%bcforce(ib)%cfy_q,                        &
                                soln%bcforce(ib)%cfz_q,                        &
                                observer%transform_matrix)
        end do

      end if

      ref_frame = 0

    end if

!   Output Ffowcs-Williams and Hawkings acoustic data

    if ( output_fwh_global_data() ) then
       if (verbose) write(*,*) 'calling fwh_global_surface_data'
       call fwh_global_surface_data(grid, soln, time_step)
    end if

!   Finally, convert back conservative if required; also, recompute qtavg to
!   include temperature instead of pressure

    ensure_qnode_conserved : if ( soln%eqn_set == compressible .and.         &
                                  q_type == primitive_q_type   .and.         &
                                  .not.(grid_motion_only)  ) then
      if (verbose) write(*,*) 'calling ptoe'
      call ptoe(size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set)
    end if ensure_qnode_conserved

    if ( q_type == conserved_q_type ) then
      if ( soln%eqn_set == compressible .and. .not.(grid_motion_only) ) then
        if ( cc_primal ) then
          if (verbose) write(*,*) 'calling qt_avg_to_nodes (with temperature)'
          call qt_avg_to_nodes(grid, soln)
        end if
      end if
    end if

    if ( soln%eqn_set == compressible .and. reference_q_read ) then
      call ptoe(size(soln%q_dof_res0,2), soln%q_dof_res0, soln%n_q,          &
                soln%eqn_set, .true.)
    endif

  end subroutine solution_output


!============================ GET_OUTPUT_REF_FRAME ===========================80
!
!   Sets the reference frame in which data will be output to either the observer
!   frame or the inertial frame. The user may have explicitly set the observer
!   observer to the inertial frame, or may not have set the observer at all,
!   in which case the default is the inertial frame. If the observer really
!   is the inertial frame, we want to detect this and set the output_ref_frame
!   to be zero (inertial), so we avoid unnecessary transform operations in
!   that case
!
!   The inertial frame has an identity transform matrix and no linear or
!   angular velocity components
!
!=============================================================================80

  subroutine get_output_ref_frame(output_ref_frame)

    use moving_body_types, only : observer

    integer,                 intent(out) :: output_ref_frame

    integer  :: i, j

    real(dp) :: eps

    logical  :: observer_is_inertial

  continue

    eps = epsilon(1._dp)*10._dp   ! allow a little wiggle room

!   default to observer frame

    output_ref_frame = -1

!   check transform matrix to see if it is an identity to within tolerance

    observer_is_inertial = .true.

    do i = 1,4
     if (abs(observer%transform_matrix(i,i) - 1._dp) > eps) then
       observer_is_inertial = .false.
     end if
   end do

    do i = 1,4
     do j = 1,4
       if (i == j) cycle
       if (abs(observer%transform_matrix(i,j)) > eps) then
         observer_is_inertial = .false.
       end if
     end do
   end do

!  check the linear and angular velocities of the observer

   do i = 1,3
     if (abs(observer%body_lin_vel(i)) > eps) then
       observer_is_inertial = .false.
     end if
     if (abs(observer%body_ang_vel(i)) > eps) then
       observer_is_inertial = .false.
     end if
   end do

   if (observer_is_inertial)  output_ref_frame = 0

  end subroutine get_output_ref_frame


!========================== OUTPUT_GLOBAL_SURFACE_DATA =======================80
!
!  Driver routine for output of a tecplot file on boundaries for visualizing
!  time-dependant data; selects between formatted and binary tecplot files
!  (binary files require proprietry tecplot libraries)
!
!=============================================================================80

  subroutine output_global_surface_data(grid, soln, time_step, adjoint_mode_arg)

    use info_depr,            only : xmach, simulation_time,  ntt,             &
                                     physical_timestep, twod
    use nml_nonlinear_solves, only : itime
    use nml_global,           only : boundary_animation_freq
    use grid_types,           only : grid_type
    use tecplot_io_helpers,   only : write_boundary_tec,                       &
                                     get_tec_file_extension,                   &
                                     set_boundaries_to_animate
    use nml_boundary_output, only : n_output_variables_bnd=>n_output_variables,&
                                    output_variables_bnd=>output_variables,    &
                                    max_bnd_vars, echo
    use io,                   only : prior_iters
    use fluid,                only : gamma, gm1, xgm1, xgm1g
    use lmpi,                 only : lmpi_master, lmpi_conditional_stop
    use ivals,                only : u0, v0, w0, p0
    use generic_gas_map,      only : n_momx, n_momy, n_momz, n_density, n_etot,&
                                     n_temperature_j, n_energy, n_energy_last, &
                                     n_pressure_k, n_sonic_k,                  &
                                     n_molecular_weight, n_species, n_amu_k
    use turb_gen,             only : n_turb_ke, n_dis_nutl
    use shared_gas_variables, only: spec_propv
    use solution_types,       only : compressible, generic_gas, incompressible
    use exact,                only : exact_q

    type(grid_type),        intent(inout) :: grid
    type(soln_type),        intent(inout) :: soln

    integer,                intent(in)    :: time_step

    logical, optional,      intent(in)    :: adjoint_mode_arg

    logical,                              save :: boundaries_not_set = .true.
    logical                                    :: adjoint_mode

    character(len=80)                          :: step, bndry, err_msg
    character(len=80)                          :: restart_file, species_name
    character(len=3)                           :: tec_file_extension
    character(len=256)                         :: variable_list
    character(len=256)                         :: file_title
    character(len=256)                         :: zone_title
    character(len=256)                         :: filename, bc_family

    character(len=80), dimension(max_bnd_vars),     save :: output_variables

    integer,                              save :: n_output_variables
    integer                                    :: first_bndry, last_bndry
    integer                                    :: i, ib, nface, nfacenodes
    integer                                    :: n, ns, ierr, status
    integer                                    :: n_output_bndry, n_x, n_y, n_z

    logical, save                              :: append_timestep = .true.
    logical, save, dimension(:),   allocatable :: output_bndry
    logical, save, dimension(:),   allocatable :: output_bndry_requested

    real(dp),      dimension(:,:), allocatable :: output_data
    real(dp),      dimension(:  ), allocatable :: gammae, mache2
    real(dp),      dimension(6)                :: turbfluctuations
    real(dp)                                   :: p_inf, cp_factor, time_val
    real(dp)                                   :: rec_fac

    real(dp), parameter                        :: half = 0.5_dp
    real(dp), parameter                        ::  one = 1.0_dp

    integer                       :: iii
    real(dp)                      :: x_r, y_r, z_r
    real(dp), dimension(soln%n_q) :: qexact

  continue

!   first time through, determine which variables and boundaries we will output

    if ( grid%origin > 1 .and. grid%cc ) return !skip agglomerated cc grids

    if ( present(adjoint_mode_arg) ) then
      adjoint_mode = adjoint_mode_arg
    else
      adjoint_mode = .false.
    end if

    if (boundaries_not_set) then

      allocate(output_bndry(grid%nbound))
      allocate(output_bndry_requested(grid%nbound))

      output_bndry(:) = .false.

      call set_boundaries_to_animate( echo, grid%nbound, output_bndry,         &
                                      grid%bc,     grid%nnodes01, grid%y)

      do ib = 1,grid%nbound
        output_bndry_requested(ib) = output_bndry(ib)
        if (output_bndry(ib)) then
          soln%global_bndry_data(ib)%used = .true.
        end if
      end do

      n_output_variables  = n_output_variables_bnd
      output_variables(:) = output_variables_bnd(:)

      if (boundary_animation_freq(grid%igrid) < 0) append_timestep = .false.

      boundaries_not_set = .false.

      return

    end if

    if ( soln%eqn_set == compressible ) then
      p_inf     = 1._dp/gamma
      cp_factor = gamma*xmach**2
    else
      p_inf     = 1._dp
      cp_factor = 1._dp
    end if

    ierr = 0

!   adjust output_bndry list to account for boundary output that may not
!   be possible due to insufficient memory

    n_output_bndry = 0
    do ib = 1,grid%nbound
      if (output_bndry(ib)) then
        if (.not. soln%global_bndry_data(ib)%used) output_bndry(ib) = .false.
      end if
      if (output_bndry(ib)) n_output_bndry = n_output_bndry + 1
    end do

    if (n_output_bndry == 0 .and. .not.(lmpi_master)) return

    master_writes_file : if (lmpi_master) then

!     print a message if we had to skip output due to memory limitations
!     if all requested boundaries have been skipped, bail out here

      do ib=1,grid%nbound
        if (output_bndry_requested(ib) .and. .not.(output_bndry(ib))) then
          write(*,'(a,i0,a)') ' ...skipping output of data on boundary ', ib,  &
                               ' due to insufficient memory'
        end if
      end do
      if (n_output_bndry == 0) return

!     set the file name

      if (append_timestep) then
        if ( adjoint_mode .and. itime /= 0 ) then
          write(step,"(i0)")    physical_timestep
          step = '_timestep' // trim(adjustl(step))
        else
          write(step,"(i0)")    time_step+prior_iters
          step = '_timestep' // trim(adjustl(step))
        endif
      else
        step = ''
      end if

      if (itime == 0) then
        time_val = real(time_step+prior_iters, dp)
      else
        time_val = simulation_time
      end if

      restart_file = trim(adjustl(grid%project))

      call get_tec_file_extension(tec_file_extension)

      filename = trim(adjustl(restart_file)) // '_tec_boundary' //             &
                 trim(adjustl(step)) // '.' // trim(adjustl(tec_file_extension))

      write(*,*)
      write(*,'(1x,2a)') 'Writing boundary output: ',trim(filename)
      if ( adjoint_mode .and. itime /= 0 ) then
        write(*,'(2x,3(a,i0))')                                                &
                   'Time step: ', physical_timestep,                           &
          ', Prior iterations: ', prior_iters
      else
        write(*,'(2x,3(a,i0))')                                                &
                   'Time step: ', time_step,                                   &
                       ', ntt: ', ntt,                                         &
          ', Prior iterations: ', prior_iters
      endif

!     set the file title

      file_title = 'tecplot geometry and solution file'

!     determine the id number of the first and last boundaries to be written

      first_bndry = -1
      first_bnd : do ib = 1,grid%nbound
        if (.not. output_bndry(ib)) cycle first_bnd
        first_bndry = ib
        exit first_bnd
      end do first_bnd

      last_bndry = -1
      last_bnd : do ib = 1,grid%nbound
        if (.not. output_bndry(ib)) cycle last_bnd
        last_bndry = ib
      end do last_bnd

!     create the list of variables

      variable_list = ''
      do n=1,n_output_variables
        variable_list = trim(adjustl(variable_list)) // ' ' //                 &
                        trim(adjustl(output_variables(n))) // ' '
      end do

      boundary_loop : do ib=1,grid%nbound

        if (.not. output_bndry(ib)) cycle

!       set the zone title

        write(bndry,"(i0)") ib

        bc_family = trim(grid%bc(ib)%bc_family)

        if ( bc_family == '' ) then
          zone_title = '"boundary ' // trim(adjustl(bndry)) // '"'
        else
          zone_title = '"boundary ' // trim(adjustl(bndry)) // ' ' //          &
                        trim(adjustl(bc_family)) // '"'
        end if

        nface      = soln%global_bndry_data(ib)%nface
        nfacenodes = soln%global_bndry_data(ib)%nfacenodes

        have_data_to_output : if (nface > 0) then

!         store the data to be output

          allocate(output_data(n_output_variables, nfacenodes), stat=status)

          check_memory : if (status /= 0) then
            write(*,'(2a)')                                                    &
             ' WARNING: memory allocation failed when attempting to allocate', &
             ' output_data array in output_global_surface_data'
            write(*,'(a,i0)')                                                  &
             ' ...skipping requested boundary data output for boundary ', ib
!            allocate tiny amount of memory for safety
             allocate(output_data(1,1))
          end if check_memory

          ierr = 0
          output_data(:,1) = -huge(1.0_dp)

          if (status == 0) then

            gather_output_data : do n = 1,n_output_variables

              if ( soln%eqn_set == generic_gas ) then
                if (n_species > 1) then
                  nspecies: do ns = 1,n_species
                    species_name = 'rho_'//trim(spec_propv(ns)%species)
                    if (output_variables(n) == species_name) then
                      output_data(n,:) =                                       &
                      soln%global_bndry_data(ib)%qglobal_bndry(ns,:)
                    end if
                  end do nspecies
                end if
              end if

              select case(trim(adjustl(output_variables(n))))

                case('x')
                  output_data(n,:) = soln%global_bndry_data(ib)%xglobal_bndry(:)
                  n_x              = n

                case('y')
                  output_data(n,:) = soln%global_bndry_data(ib)%yglobal_bndry(:)
                  n_y              = n

                case('z')
                  output_data(n,:) = soln%global_bndry_data(ib)%zglobal_bndry(:)
                  n_z              = n

                case('id_l2g')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%l2gglobal_bndry(:)

                case('processor_id')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%proc_idglobal_bndry(:)

                case('rho')
                  select case ( soln%eqn_set )
                  case ( compressible )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(1,:)
                  case ( incompressible )
                    output_data(n,:) = 1._dp
                  case ( generic_gas )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(n_density,:)
                  end select

                case('u')
                  select case ( soln%eqn_set )
                  case ( compressible )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(2,:)
                  case ( incompressible )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(2,:)
                  case ( generic_gas )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(n_momx,:) /     &
                      soln%global_bndry_data(ib)%qglobal_bndry(n_density,:)
                  end select

                case('v')
                  select case ( soln%eqn_set )
                  case ( compressible )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(3,:)
                  case ( incompressible )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(3,:)
                  case ( generic_gas )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(n_momy,:) /     &
                      soln%global_bndry_data(ib)%qglobal_bndry(n_density,:)
                  end select

                case('w')
                  select case ( soln%eqn_set )
                  case ( compressible )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(4,:)
                  case ( incompressible )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(4,:)
                  case ( generic_gas )
                    output_data(n,:) =                                         &
                      soln%global_bndry_data(ib)%qglobal_bndry(n_momz,:) /     &
                      soln%global_bndry_data(ib)%qglobal_bndry(n_density,:)
                  end select

                case('uavg')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%uavgglobal_bndry(:)

                case('vavg')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%vavgglobal_bndry(:)

                case('wavg')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%wavgglobal_bndry(:)

                case('p')
                  select case ( soln%eqn_set )
                  case ( compressible )
                    output_data(n,:) =                                         &
                    soln%global_bndry_data(ib)%qglobal_bndry(5,:)
                  case ( incompressible )
                    output_data(n,:) =                                         &
                    soln%global_bndry_data(ib)%qglobal_bndry(1,:)
                  case ( generic_gas )
                    output_data(n,:) =                                         &
                    soln%global_bndry_data(ib)%qglobal_bndry(n_pressure_k(1),:)
                  end select

                case('recovery_temperature')
                  select case ( soln%eqn_set )
                  case ( compressible )
                    output_data(n,:) =                                         &
                   ( (gamma*soln%global_bndry_data(ib)%qglobal_bndry(5,:)      &
                   /soln%global_bndry_data(ib)%qglobal_bndry(1,:) ) - one    ) &
                   /(half*gm1*xmach*xmach)
                  case ( incompressible )
                    output_data(n,:) = 0._dp
                  case ( generic_gas )
                    allocate(gammae(size(                                      &
                      soln%global_bndry_data(ib)%qglobal_bndry,2)))
                    allocate(mache2(size(                                      &
                      soln%global_bndry_data(ib)%qglobal_bndry,2)))
                    gammae(:) = soln%global_bndry_data(ib)%qglobal_bndry(      &
                      n_density,:)*soln%global_bndry_data(ib)%qglobal_bndry(   &
                      n_sonic_k(1),:)**2/soln%global_bndry_data(ib             &
                      )%qglobal_bndry(n_pressure_k(1),:)
                    mache2(:) = (soln%global_bndry_data(ib)%qglobal_bndry(     &
                      n_momx,:)**2 + soln%global_bndry_data(ib)%qglobal_bndry( &
                      n_momy,:)**2 + soln%global_bndry_data(ib)%qglobal_bndry( &
                      n_momz,:)**2)/(soln%global_bndry_data(ib)%qglobal_bndry( &
                      n_density,:)*soln%global_bndry_data(ib)%qglobal_bndry(   &
                      n_sonic_k(1),:))**2
!                   Assume recovery factor of sqrt(0.72)
                    rec_fac = sqrt(0.72)
                    output_data(n,:) = soln%global_bndry_data(ib               &
                      )%qglobal_bndry(n_temperature_j(1),:)*(one   + half  *   &
                      rec_fac*(gammae(:) - one  )*mache2(:))
                    deallocate(gammae)
                    deallocate(mache2)
                  end select

                case('cp')
                  if ( soln%eqn_set == compressible ) then
                    output_data(n,:) =                                         &
                    2._dp*(soln%global_bndry_data(ib)%qglobal_bndry(5,:)/p_inf &
                           - 1._dp) / cp_factor
                  else if (soln%eqn_set == incompressible ) then
                    output_data(n,:) =                                         &
                    2._dp*(soln%global_bndry_data(ib)%qglobal_bndry(1,:)/p_inf &
                           - 1._dp) / cp_factor
                  else if (soln%eqn_set == generic_gas) then
                    output_data(n,:) =                                         &
                    2._dp*(soln%global_bndry_data(ib)%qglobal_bndry(           &
                    n_pressure_k(1),:) - p0)
                  end if

                case('dp_pinf')
                  if ( soln%eqn_set == compressible ) then
                    output_data(n,:) =                                         &
                    (soln%global_bndry_data(ib)%qglobal_bndry(5,:)-p_inf)/p_inf
                  else if (soln%eqn_set == incompressible ) then
                    output_data(n,:) =                                         &
                    (soln%global_bndry_data(ib)%qglobal_bndry(1,:)-p_inf)/p_inf
                  else if (soln%eqn_set == generic_gas) then
                    output_data(n,:) =                                         &
                    (soln%global_bndry_data(ib)%qglobal_bndry(                 &
                    n_pressure_k(1),:) - p0)/p0
                  end if

                case('ptot')
                  if ( soln%eqn_set == compressible ) then
                    output_data(n,:) =                                         &
                     soln%global_bndry_data(ib)%qglobal_bndry(5,:)             &
                     * ( one + half * gm1 *                                    &
                     ( (soln%global_bndry_data(ib)%qglobal_bndry(2,:)**2       &
                      + soln%global_bndry_data(ib)%qglobal_bndry(3,:)**2       &
                      + soln%global_bndry_data(ib)%qglobal_bndry(4,:)**2)      &
                                         /                                     &
                     (gamma*soln%global_bndry_data(ib)%qglobal_bndry(5,:) /    &
                            soln%global_bndry_data(ib)%qglobal_bndry(1,:)) )   &
                     )**(xgm1g)
                  else if ( soln%eqn_set == generic_gas ) then
                    allocate(gammae(size(                                      &
                      soln%global_bndry_data(ib)%qglobal_bndry,2)))
                    allocate(mache2(size(                                      &
                      soln%global_bndry_data(ib)%qglobal_bndry,2)))
                    gammae(:) = soln%global_bndry_data(ib)%qglobal_bndry(      &
                      n_density,:)*soln%global_bndry_data(ib)%qglobal_bndry(   &
                      n_sonic_k(1),:)**2/soln%global_bndry_data(ib             &
                      )%qglobal_bndry(n_pressure_k(1),:)
                    mache2(:) = (soln%global_bndry_data(ib)%qglobal_bndry(     &
                      n_momx,:)**2 + soln%global_bndry_data(ib)%qglobal_bndry( &
                      n_momy,:)**2 + soln%global_bndry_data(ib)%qglobal_bndry( &
                      n_momz,:)**2)/(soln%global_bndry_data(ib)%qglobal_bndry( &
                      n_density,:)*soln%global_bndry_data(ib)%qglobal_bndry(   &
                      n_sonic_k(1),:))**2
                    output_data(n,:) = soln%global_bndry_data(ib               &
                      )%qglobal_bndry(n_pressure_k(1),:)*(one   + half  *      &
                      (gammae(:)-one)*mache2(:))**(gammae(:)/(gammae(:)-one))
                    deallocate(gammae)
                    deallocate(mache2)
                  endif

                case('ttot')
                  if ( soln%eqn_set == compressible ) then
                    output_data(n,:) =                                         &
                    ( gamma * soln%global_bndry_data(ib)%qglobal_bndry(5,:) /  &
                              soln%global_bndry_data(ib)%qglobal_bndry(1,:) )  &
                     * ( one + half * gm1 *                                    &
                     ( (soln%global_bndry_data(ib)%qglobal_bndry(2,:)**2       &
                      + soln%global_bndry_data(ib)%qglobal_bndry(3,:)**2       &
                      + soln%global_bndry_data(ib)%qglobal_bndry(4,:)**2)      &
                                         /                                     &
                     (gamma*soln%global_bndry_data(ib)%qglobal_bndry(5,:) /    &
                            soln%global_bndry_data(ib)%qglobal_bndry(1,:)) ) )
                  else if ( soln%eqn_set == generic_gas )then
                    allocate(gammae(size(                                      &
                      soln%global_bndry_data(ib)%qglobal_bndry,2)))
                    allocate(mache2(size(                                      &
                      soln%global_bndry_data(ib)%qglobal_bndry,2)))
                    gammae(:) = soln%global_bndry_data(ib)%qglobal_bndry(      &
                      n_density,:)*soln%global_bndry_data(ib)%qglobal_bndry(   &
                      n_sonic_k(1),:)**2/soln%global_bndry_data(ib             &
                      )%qglobal_bndry(n_pressure_k(1),:)
                    mache2(:) = (soln%global_bndry_data(ib)%qglobal_bndry(     &
                      n_momx,:)**2 + soln%global_bndry_data(ib)%qglobal_bndry( &
                      n_momy,:)**2 + soln%global_bndry_data(ib)%qglobal_bndry( &
                      n_momz,:)**2)/(soln%global_bndry_data(ib)%qglobal_bndry( &
                      n_density,:)*soln%global_bndry_data(ib)%qglobal_bndry(   &
                      n_sonic_k(1),:))**2
                    output_data(n,:) = soln%global_bndry_data(ib               &
                      )%qglobal_bndry(n_temperature_j(1),:)*(one   + half  *   &
                      (gammae(:) - one  )*mache2(:))
                    deallocate(gammae)
                    deallocate(mache2)
                  endif

                case('tt')
                  if ( soln%eqn_set == generic_gas ) then
                  output_data(n,:) =                                           &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_temperature_j(1),:)
                  end if

                case('tv')
                  if ( soln%eqn_set == generic_gas .and. n_energy > 1) then
                  output_data(n,:) =                                           &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_temperature_j(2),:)
                  end if

                case('turb_ke')
                  output_data(n,:) =                                           &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_turb_ke,:)/       &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_density,:)

                case('turb_diss')
                  output_data(n,:) =                                           &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_dis_nutl,:)/      &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_density,:)

                case('etot')
                  if ( soln%eqn_set == compressible ) then
                    output_data(n,:) =                                         &
                    (soln%global_bndry_data(ib)%qglobal_bndry(5,:) /           &
                     soln%global_bndry_data(ib)%qglobal_bndry(1,:)) * xgm1     &
                    +(soln%global_bndry_data(ib)%qglobal_bndry(2,:)**2         &
                     +soln%global_bndry_data(ib)%qglobal_bndry(3,:)**2         &
                     +soln%global_bndry_data(ib)%qglobal_bndry(4,:)**2) * half
                  end if
                  if ( soln%eqn_set == generic_gas ) then
                    output_data(n,:) =                                         &
                    soln%global_bndry_data(ib)%qglobal_bndry(n_etot,:) /       &
                    soln%global_bndry_data(ib)%qglobal_bndry(n_density,:)
                  end if

                case('htot')
                 select case ( soln%eqn_set )
                 case ( compressible )
                   output_data(n,:) =                                          &
                 ( soln%global_bndry_data(ib)%qglobal_bndry(5,:) /             &
                   soln%global_bndry_data(ib)%qglobal_bndry(1,:) )             &
                 * ( gamma *  xgm1 ) + half *                                  &
                  (soln%global_bndry_data(ib)%qglobal_bndry(2,:)**2            &
                  +soln%global_bndry_data(ib)%qglobal_bndry(3,:)**2            &
                  +soln%global_bndry_data(ib)%qglobal_bndry(4,:)**2)
                 case ( generic_gas )
                 output_data(n,:) =                                            &
                (soln%global_bndry_data(ib)%qglobal_bndry(n_etot,:) +          &
                 soln%global_bndry_data(ib)%qglobal_bndry(n_pressure_k(1),:))/ &
                 soln%global_bndry_data(ib)%qglobal_bndry(n_density,:)
                 end select

                case('ev')
                  if (soln%eqn_set == generic_gas .and. n_energy > 1) then
                  output_data(n,:) =                                           &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_energy_last,:)
                  end if

                case('mixture_mol_weight')
                  if ( soln%eqn_set == generic_gas ) then
                  output_data(n,:) =                                           &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_molecular_weight,:)
                  end if

                case('sonic')
                  if ( soln%eqn_set == generic_gas ) then
                  output_data(n,:) =                                           &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_sonic_k(1),:)
                  end if

                case('mu')
                  if ( soln%eqn_set == generic_gas ) then
                  output_data(n,:) =                                           &
                  soln%global_bndry_data(ib)%qglobal_bndry(n_amu_k(1),:)
                  end if

                case('mach')
                  select case ( soln%eqn_set )
                  case ( compressible )
                    output_data(n,:) = sqrt(                                   &
                      (soln%global_bndry_data(ib)%qglobal_bndry(2,:)**2  +     &
                       soln%global_bndry_data(ib)%qglobal_bndry(3,:)**2  +     &
                       soln%global_bndry_data(ib)%qglobal_bndry(4,:)**2) /     &
                      (gamma*soln%global_bndry_data(ib)%qglobal_bndry(5,:) /   &
                       soln%global_bndry_data(ib)%qglobal_bndry(1,:)) )
                  case ( incompressible )
                    output_data(n,:) = 0._dp
                  case ( generic_gas )
                    output_data(n,:) = sqrt                                    &
                      (soln%global_bndry_data(ib)%qglobal_bndry(n_momx,:)**2 + &
                       soln%global_bndry_data(ib)%qglobal_bndry(n_momy,:)**2 + &
                       soln%global_bndry_data(ib)%qglobal_bndry(n_momz,:)**2)/ &
                      (soln%global_bndry_data(ib)%qglobal_bndry(n_density,:)*  &
                       soln%global_bndry_data(ib)%qglobal_bndry(n_sonic_k(1),:))
                  end select

                case('temperature')
                  if ( soln%eqn_set == compressible ) then
                    output_data(n,:) =                                         &
                      gamma*soln%global_bndry_data(ib)%qglobal_bndry(5,:)      &
                           /soln%global_bndry_data(ib)%qglobal_bndry(1,:)
                  else
                    output_data(n,:) = 0._dp
                  endif

                case('entropy')
                  if ( soln%eqn_set == compressible ) then
                    output_data(n,:) =                                         &
                     log(soln%global_bndry_data(ib)%qglobal_bndry(5,:)/p_inf/  &
                     soln%global_bndry_data(ib)%qglobal_bndry(1,:)**gamma)
                  else
                    output_data(n,:) =                                         &
                      log(soln%global_bndry_data(ib)%qglobal_bndry(5,:)/p_inf)
                  end if

                case('cf_x')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%cfxglobal_bndry(:)

                case('cf_y')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%cfyglobal_bndry(:)

                case('cf_z')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%cfzglobal_bndry(:)

                case('xnormal')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%bxnglobal_bndry(:)

                case('ynormal')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%bynglobal_bndry(:)

                case('znormal')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%bznglobal_bndry(:)

                case('cq')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%cqglobal_bndry(:)

                case('heating')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%viscous_flux_bndry(4,:)

                case('q_rad')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%viscous_flux_bndry(5,:)

                case('mdot')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%viscous_flux_bndry(6,:)

                case('shear_x')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%viscous_flux_bndry(1,:)

                case('shear_y')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%viscous_flux_bndry(2,:)

                case('shear_z')
                  output_data(n,:) =                                           &
                    soln%global_bndry_data(ib)%viscous_flux_bndry(3,:)

                case('slen')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%slenglobal_bndry(:)

                case('mu_t')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%amutglobal_bndry(:)

                case('turb1')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turbglobal_bndry(1,:)

                case('turb2')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turbglobal_bndry(2,:)

                case('turb3')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turbglobal_bndry(3,:)

                case('turb4')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turbglobal_bndry(4,:)

                case('turb5')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turbglobal_bndry(5,:)

                case('turb6')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turbglobal_bndry(6,:)

                case('turb7')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turbglobal_bndry(7,:)

                case('turb_mach')
!                 if (soln%eqn_set == compressible ) then FIXME: commented code
                    output_data(n,:) = sqrt(abs(2.0_dp                         &
                  * soln%global_bndry_data(ib)%qglobal_bndry(1,:)              &
                  * soln%global_bndry_data(ib)%turbglobal_bndry(1,:)           &
                    /(gamma*soln%global_bndry_data(ib)%qglobal_bndry(5,:))))
!                 else
!                 output_data(n,:) = sqrt(2.0_dp * 1._dp                       &
!                  * soln%global_bndry_data(ib)%turbglobal_bndry(1,:)          &
!                   /(gamma*soln%global_bndry_data(ib)%qglobal_bndry(1,:)))
!                 end if

                case('iblank')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%iblankglobal_bndry(:)

                case('imesh')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%imeshglobal_bndry(:)

                case('vort_x')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%gradyglobal_bndry(4,:) - &
                           soln%global_bndry_data(ib)%gradzglobal_bndry(3,:)

                case('vort_y')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%gradzglobal_bndry(2,:) - &
                           soln%global_bndry_data(ib)%gradxglobal_bndry(4,:)

                case('vort_z')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%gradxglobal_bndry(3,:) - &
                           soln%global_bndry_data(ib)%gradyglobal_bndry(2,:)

                case('vort_mag')
                  output_data(n,:) = sqrt(                                     &
                     ( soln%global_bndry_data(ib)%gradyglobal_bndry(4,:) -     &
                       soln%global_bndry_data(ib)%gradzglobal_bndry(3,:) )**2  &
                   + ( soln%global_bndry_data(ib)%gradzglobal_bndry(2,:) -     &
                       soln%global_bndry_data(ib)%gradxglobal_bndry(4,:) )**2  &
                   + ( soln%global_bndry_data(ib)%gradxglobal_bndry(3,:) -     &
                       soln%global_bndry_data(ib)%gradyglobal_bndry(2,:) )**2 )

                case('div_vel')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%gradxglobal_bndry(2,:) + &
                           soln%global_bndry_data(ib)%gradyglobal_bndry(3,:) + &
                           soln%global_bndry_data(ib)%gradzglobal_bndry(4,:)

                case('q_criterion')
                  do i = 1,nfacenodes
                    output_data(n,i) =                                         &
                            q_criterion(soln%eqn_set, soln%n_grd,              &
                            soln%global_bndry_data(ib)%gradxglobal_bndry(:,i), &
                            soln%global_bndry_data(ib)%gradyglobal_bndry(:,i), &
                            soln%global_bndry_data(ib)%gradzglobal_bndry(:,i))
                  end do

                case ('uuprime','vvprime','wwprime','uvprime','uwprime',       &
                      'vwprime')

                  do i = 1,nfacenodes

                    turbfluctuations =                                         &
                      turb_fluctuations( soln%n_grd, soln%n_tot, soln%n_turb,  &
                            soln%eqn_set,                                      &
                            soln%global_bndry_data(ib)%qglobal_bndry(:,i),     &
                            soln%global_bndry_data(ib)%amutglobal_bndry(i),    &
                            soln%global_bndry_data(ib)%gradxglobal_bndry(:,i), &
                            soln%global_bndry_data(ib)%gradyglobal_bndry(:,i), &
                            soln%global_bndry_data(ib)%gradzglobal_bndry(:,i), &
                            soln%global_bndry_data(ib)%turbglobal_bndry(:,i))

                    select case(trim(adjustl(output_variables(n))))
                      case ('uuprime')
                        output_data(n,i) = turbfluctuations(1)
                      case ('vvprime')
                        output_data(n,i) = turbfluctuations(2)
                      case ('wwprime')
                        output_data(n,i) = turbfluctuations(3)
                      case ('uvprime')
                        output_data(n,i) = turbfluctuations(4)
                      case ('uwprime')
                        output_data(n,i) = turbfluctuations(5)
                      case ('vwprime')
                        output_data(n,i) = turbfluctuations(6)
                      case default
                    end select

                  end do

                case('yplus')
                  do i = 1,nfacenodes
                    output_data(n,i) =                                         &
                            y_plus(soln%eqn_set, soln%n_tot,                   &
                            soln%global_bndry_data(ib)%cfxglobal_bndry(i),     &
                            soln%global_bndry_data(ib)%cfyglobal_bndry(i),     &
                            soln%global_bndry_data(ib)%cfzglobal_bndry(i),     &
                            soln%global_bndry_data(ib)%qglobal_bndry(:,i),     &
                            soln%global_bndry_data(ib)%slenglobal_bndry(i))
                  end do

                case('utau_wf')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%utau_wfglobal_bndry(:)

                case('phi_wf')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%phi_wfglobal_bndry(:)

                case('mu_t_wf')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%mu_t_wfglobal_bndry(:)

                case('k_wallfunction_bc')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%k_wf_bcglobal_bndry(:)

                case('omega_wallfunction_bc')
                  output_data(n,:) =                                           &
                       soln%global_bndry_data(ib)%omega_wf_bcglobal_bndry(:)

                case('skinfr')
                  do i = 1,nfacenodes
                    output_data(n,i) =                                         &
                            skin_fric(u0, v0, w0,                              &
                            soln%global_bndry_data(ib)%cfxglobal_bndry(i),     &
                            soln%global_bndry_data(ib)%cfyglobal_bndry(i),     &
                            soln%global_bndry_data(ib)%cfzglobal_bndry(i))
                  end do

                case('turbindex')
                  do i = 1,nfacenodes
                    output_data(n,i) =                                         &
                         turb_index( soln%n_grd, soln%n_tot, soln%eqn_set,     &
                         soln%global_bndry_data(ib)%qglobal_bndry(:,i),        &
                         soln%global_bndry_data(ib)%amutoffbodyglobal_bndry(i),&
                         soln%global_bndry_data(ib)%gradxglobal_bndry(:,i),    &
                         soln%global_bndry_data(ib)%gradyglobal_bndry(:,i),    &
                         soln%global_bndry_data(ib)%gradzglobal_bndry(:,i),    &
                         soln%global_bndry_data(ib)%slenglobal_bndry(i))
                  end do

                case('volume')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%volglobal_bndry(:)

                case('res1')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%resglobal_bndry(1,:)

                case('res2')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%resglobal_bndry(2,:)

                case('res3')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%resglobal_bndry(3,:)

                case('res4')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%resglobal_bndry(4,:)

                case('res5')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%resglobal_bndry(5,:)

                case('lambda1')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%rlamglobal_bndry(1,:)

                case('lambda2')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%rlamglobal_bndry(2,:)

                case('lambda3')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%rlamglobal_bndry(3,:)

                case('lambda4')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%rlamglobal_bndry(4,:)

                case('lambda5')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%rlamglobal_bndry(5,:)

                case('lambda6')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%rlamglobal_bndry(6,:)

                case('lambda7')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%rlamglobal_bndry(7,:)

                case('de_turb1')

                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turbglobal_bndry(1,:)

                  do iii=1,size(output_data,2)
                    x_r = output_data(n_x,iii)
                    z_r = output_data(n_z,iii)
                    y_r = 0.
                    if ( .not. twod ) then
                      y_r = output_data(n_y,iii)
                    endif
                    call exact_q( soln%eqn_set, soln%n_q        &
                    , x_r, y_r, z_r, qexact, 1)
                    output_data(n,iii) = output_data(n,iii) - qexact(6)
                  enddo


                case('turres1')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turresglobal_bndry(1,:)

                case('turres2')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turresglobal_bndry(2,:)

                case('turres3')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turresglobal_bndry(3,:)

                case('turres4')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turresglobal_bndry(4,:)

                case('turres5')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turresglobal_bndry(5,:)

                case('turres6')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turresglobal_bndry(6,:)

                case('turres7')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%turresglobal_bndry(7,:)

                case('res_gcl')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%res_gclglobal_bndry(1,:)

                case('rho_tavg')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%tavg_qglobal_bndry(1,:)

                case('u_tavg')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%tavg_qglobal_bndry(2,:)

                case('v_tavg')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%tavg_qglobal_bndry(3,:)

                case('w_tavg')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%tavg_qglobal_bndry(4,:)

                case('p_tavg')
                  output_data(n,:) =                                           &
                           soln%global_bndry_data(ib)%tavg_qglobal_bndry(5,:)

                case('rho_trms')
                  output_data(n,:) = sqrt(                                     &
                       abs(soln%global_bndry_data(ib)%tavg_qglobal_bndry(6,:)  &
                    -  soln%global_bndry_data(ib)%tavg_qglobal_bndry(1,:)**2))


                case('u_trms')
                  output_data(n,:) = sqrt(                                     &
                       abs(soln%global_bndry_data(ib)%tavg_qglobal_bndry(7,:)  &
                    -  soln%global_bndry_data(ib)%tavg_qglobal_bndry(2,:)**2))

                case('v_trms')
                  output_data(n,:) = sqrt(                                     &
                       abs(soln%global_bndry_data(ib)%tavg_qglobal_bndry(8,:)  &
                    -  soln%global_bndry_data(ib)%tavg_qglobal_bndry(3,:)**2))

                case('w_trms')
                  output_data(n,:) = sqrt(                                     &
                       abs(soln%global_bndry_data(ib)%tavg_qglobal_bndry(9,:)  &
                    -  soln%global_bndry_data(ib)%tavg_qglobal_bndry(4,:)**2))

                case('p_trms')
                  output_data(n,:) = sqrt(                                     &
                       abs(soln%global_bndry_data(ib)%tavg_qglobal_bndry(10,:) &
                    -  soln%global_bndry_data(ib)%tavg_qglobal_bndry(5,:)**2))

                case default

              end select

              if (output_data(n,1) == -huge(1.0_dp)) then
                ierr = 1
                write(err_msg,*) 'output_global_surface_data: ',               &
                                 'unknown variable: '  //                      &
                                  trim(adjustl(output_variables(n)))
                exit gather_output_data
              end if

            end do gather_output_data

          end if

!         now output the data on current boundary in either ascii or binary form

          call write_boundary_tec(n_output_variables, nfacenodes, nface,       &
                                  soln%global_bndry_data(ib)%f2n, output_data, &
                                  file_title, zone_title, variable_list,       &
                                  filename, ib, first_bndry, last_bndry,       &
                                  status, time_val)

          if (allocated(output_data)) deallocate(output_data)

        end if have_data_to_output

      end do boundary_loop

    end if master_writes_file

    call lmpi_conditional_stop(ierr, err_msg)

  end subroutine output_global_surface_data

  include 'q_criterion.f90'
  include 'turb_fluctuations.f90'
  include 'y_plus.f90'
  include 'skin_fric.f90'
  include 'turb_index.f90'
    include 'viscosity_law.f90'

!========================== FWH_GLOBAL_SURFACE_DATA =======================80
!
!  Driver routine for output of an unformatted file on boundaries for
!  time-dependant data. The grid, normals, and p are output on solid surfaces.
!
!=============================================================================80

  subroutine fwh_global_surface_data(grid, soln, time_step)

    use grid_types,        only : grid_type

    type(grid_type),        intent(in)    :: grid
    type(soln_type),        intent(inout) :: soln

    integer,                intent(in)    :: time_step

    continue

    call fwh_global_surface_unf(grid, soln, time_step)

!    call fwh_global_surface_ascii(grid, soln, time_step, observer)

  end subroutine fwh_global_surface_data


!================================ WRITE_FORCE ================================80
!
! Write out the final forces
!
!=============================================================================80

  subroutine write_force(grid, soln)

    use info_depr,         only : ntt
    use bc_names,          only : viscous_solid
    use grid_types,        only : grid_type
    use system_extensions, only : se_open
    use file_utils,        only : available_unit
    use debug_defs,        only : forces_via_fluxes

    type(grid_type), intent(in) :: grid
    type(soln_type), intent(in) :: soln

    integer            :: iu
    integer            :: ib

    character(len=80)  :: filename
    character(len=300) :: format0, format1, format2

    continue

    format0 = "(1x,'Cl  = ',e14.7,3x,'Cd  = ',e14.7)"
    format1 = "(1x,'Cmx = ',e14.7,3x,'Cmy = ',e14.7,3x,'Cmz = ',e14.7)"
    format2 = "(1x,'Cx  = ',e14.7,3x,'Cy  = ',e14.7,3x,'Cz  = ',e14.7)"

! beginMakeComplexCopy

    filename = trim(grid%project) // '.forces'
    iu       = available_unit()
    call se_open(iu,file=filename)

    do ib = 1, grid%nbound

      write(iu,'(1x,a,i6)') 'FORCE SUMMARY FOR BOUNDARY ',ib
      if (.not. soln%bcforce(ib)%add_to_total) then
        write(iu,*) 'THIS BOUNDARY NOT COUNTED IN TOTAL FORCES'
      end if
      if ( grid%bc(ib)%bc_family == '' ) then
        write(iu,'(1x,a,i5)') 'Boundary type =', grid%bc(ib)%ibc
      else
        write(iu,'(1x,a,i5,2a)') 'Boundary type =', grid%bc(ib)%ibc,         &
                                  ' Family = ', trim(grid%bc(ib)%bc_family)
      endif
      write(iu,*)       '---------------------------------------------------'
      write(iu,*)       'Pressure forces'
      write(iu,format0) real(soln%bcforce(ib)%clp,dp   ),                      &
                        real(soln%bcforce(ib)%cdp,dp   )
      write(iu,format1) real(soln%bcforce(ib)%cmxp,dp   ),                     &
                        real(soln%bcforce(ib)%cmyp,dp   ),                     &
                        real(soln%bcforce(ib)%cmzp,dp   )
      write(iu,format2) real(soln%bcforce(ib)%cxp,dp   ),                      &
                        real(soln%bcforce(ib)%cyp,dp   ),                      &
                        real(soln%bcforce(ib)%czp,dp   )
      write(iu,*)       'Viscous forces'
      write(iu,format0) real(soln%bcforce(ib)%clv,dp   ),                      &
                        real(soln%bcforce(ib)%cdv,dp   )
      write(iu,format1) real(soln%bcforce(ib)%cmxv,dp   ),                     &
                        real(soln%bcforce(ib)%cmyv,dp   ),                     &
                        real(soln%bcforce(ib)%cmzv,dp   )
      write(iu,format2) real(soln%bcforce(ib)%cxv,dp   ),                      &
                        real(soln%bcforce(ib)%cyv,dp   ),                      &
                        real(soln%bcforce(ib)%czv,dp   )
      write(iu,*)       'Total forces'
      write(iu,format0) real(soln%bcforce(ib)%cl,dp   ),                       &
                        real(soln%bcforce(ib)%cd,dp   )
      write(iu,format1) real(soln%bcforce(ib)%cmx,dp   ),                      &
                        real(soln%bcforce(ib)%cmy,dp   ),                      &
                        real(soln%bcforce(ib)%cmz,dp   )
      write(iu,format2) real(soln%bcforce(ib)%cx,dp   ),                       &
                        real(soln%bcforce(ib)%cy,dp   ),                       &
                        real(soln%bcforce(ib)%cz,dp   )

      if ( ntt > 0                          .and. &
           forces_via_fluxes > 0            .and. &
           grid%bc(ib)%ibc == viscous_solid ) then
        if ( forces_via_fluxes == 1 ) then
          write(iu,*)       'Viscous forces via fluxes, strong bc and res=0'
          write(iu,*)       'Assumes inviscid boundary flux closure'
        else
          write(iu,*)       'Total forces via fluxes, strong bc, and res=0'
          write(iu,*)       'Assumes no inviscid boundary flux closure'
        endif
        write(iu,format0) real(soln%bcforce(ib)%clf,dp   ),                    &
                          real(soln%bcforce(ib)%cdf,dp   )
        write(iu,format1) real(soln%bcforce(ib)%cmxf,dp   ),                   &
                          real(soln%bcforce(ib)%cmyf,dp   ),                   &
                          real(soln%bcforce(ib)%cmzf,dp   )
        write(iu,format2) real(soln%bcforce(ib)%cxf,dp   ),                    &
                          real(soln%bcforce(ib)%cyf,dp   ),                    &
                          real(soln%bcforce(ib)%czf,dp   )
      endif
      write(iu,*)
      write(iu,*)
      write(iu,*)

    end do

    if ( ntt > 0 ) then
    write(iu,*)
    write(iu,*)       'FORCE TOTALS FOR ALL BOUNDARIES'
    write(iu,*)       '---------------------------------------------------'
    write(iu,*)       'Pressure forces'
    write(iu,format0) real(soln%totforce(ntt)%clp,dp   ),                      &
                      real(soln%totforce(ntt)%cdp,dp   )
    write(iu,format1) real(soln%totforce(ntt)%cmxp,dp   ),                     &
                      real(soln%totforce(ntt)%cmyp,dp   ),                     &
                      real(soln%totforce(ntt)%cmzp,dp   )
    write(iu,format2) real(soln%totforce(ntt)%cxp,dp   ),                      &
                      real(soln%totforce(ntt)%cyp,dp   ),                      &
                      real(soln%totforce(ntt)%czp,dp   )
    write(iu,*)       'Viscous forces'
    write(iu,format0) real(soln%totforce(ntt)%clv,dp   ),                      &
                      real(soln%totforce(ntt)%cdv,dp   )
    write(iu,format1) real(soln%totforce(ntt)%cmxv,dp   ),                     &
                      real(soln%totforce(ntt)%cmyv,dp   ),                     &
                      real(soln%totforce(ntt)%cmzv,dp   )
    write(iu,format2) real(soln%totforce(ntt)%cxv,dp   ),                      &
                      real(soln%totforce(ntt)%cyv,dp   ),                      &
                      real(soln%totforce(ntt)%czv,dp   )
    write(iu,*)       'Total forces'
    write(iu,format0) real(soln%totforce(ntt)%cl,dp   ),                       &
                      real(soln%totforce(ntt)%cd,dp   )
    write(iu,format1) real(soln%totforce(ntt)%cmx,dp   ),                      &
                      real(soln%totforce(ntt)%cmy,dp   ),                      &
                      real(soln%totforce(ntt)%cmz,dp   )
    write(iu,format2) real(soln%totforce(ntt)%cx,dp   ),                       &
                      real(soln%totforce(ntt)%cy,dp   ),                       &
                      real(soln%totforce(ntt)%cz,dp   )
    endif
    close(iu)

! endMakeComplexCopy

  end subroutine write_force


!=============================== WRITE_SLICE_DATA ============================80
!
! Dumps out Cp and Cf components at stations where the global surface data
! has been sliced
!
!=============================================================================80

  subroutine write_slice_data(grid, time_step)

    use info_depr,          only : simulation_time
    use nml_global,         only : moving_grid
    use grid_types,         only : grid_type
    use lmpi,               only : lmpi_master
    use system_extensions,  only : se_open
    use file_utils,         only : file_does_not_exist, rm, available_unit
    use solution_globals,   only : global_slice_data
    use nml_slice_data,     only : nslices, tecplot_slice_output
    use io,                 only : prior_iters

    integer,         intent(in) :: time_step
    type(grid_type), intent(in) :: grid

    character(14)  :: angle, slice_loc, time_val
    character(7)   :: c7
    character(80)  :: filename, format1, format2

    integer :: iu, section, n, ibeg, iend, loop, nloops

    logical, save      :: init = .true.

  continue

    if (.not. tecplot_slice_output) return

    write_from_master : if (lmpi_master) then

       write(*,*)
       write(*,'(2a,i5)') ' Writing sliced global boundary data to tecplot',   &
                          ' file for time step', time_step

      filename = trim(grid%project) // '_slice.dat'

      init_open : if (init) then

        if (remove_slice_data) call rm(filename)

        if ( file_does_not_exist(filename) ) then
          iu = available_unit()
          call se_open(iu,file=filename)
          write(iu,'(a)') 'title="sliced surface data"'
          write(iu,'(3a)') 'variables="x","y","z","cp","cfx","cfy","cfz"'
          close(iu)
        end if

        init = .false.

      end if init_open

      iu = available_unit()
      open(iu,file=filename,position='append')

      format1 = "('zone t=',a,', i=',i10,', f=point')"
      format2 = '(2x, 6(e23.15,1x))'

      if (simulation_time < 0._dp   ) then
        write(time_val,"(i0)") time_step+prior_iters
      else
        write(time_val,"(e14.7)") simulation_time
      end if

!     loop over all the slices

      slice_loop : do section = 1,nslices

      nloops = global_slice_data(section)%nloops

        body_loop : do loop = 1,nloops

          ibeg = global_slice_data(section)%ibeg(loop)
          iend = global_slice_data(section)%iend(loop)

          if (moving_grid) then
            write(angle,"(e14.7)") global_slice_data(section)%aux_data(1)
          end if

          write(slice_loc,"(e14.7)") global_slice_data(section)%slice_val

          write(c7,"(i7)") loop

          if (moving_grid) then
            if (global_slice_data(section)%slice_dir == 1) then
              write(iu,format1)                                                &
                    '"xslice ' // trim(adjustl(slice_loc)) //                  &
                    ' psi '    // trim(adjustl(angle))    //                   &
                    ' time '   // trim(adjustl(time_val)) //                   &
                    ' loop '   // trim(adjustl(c7))       // '"', iend-ibeg+2
            else if (global_slice_data(section)%slice_dir == 2) then
              write(iu,format1)                                                &
                    '"yslice ' // trim(adjustl(slice_loc)) //                  &
                    ' psi '    // trim(adjustl(angle))    //                   &
                    ' time '   // trim(adjustl(time_val)) //                   &
                    ' loop '   // trim(adjustl(c7))       // '"', iend-ibeg+2
            else
              write(iu,format1)                                                &
                    '"zslice ' // trim(adjustl(slice_loc)) //                  &
                    ' psi '    // trim(adjustl(angle))    //                   &
                    ' time '   // trim(adjustl(time_val)) //                   &
                    ' loop '   // trim(adjustl(c7))       // '"', iend-ibeg+2
            end if
          else
            if (global_slice_data(section)%slice_dir == 1) then
              write(iu,format1)                                                &
                    '"xslice ' // trim(adjustl(slice_loc)) //                  &
                    ' time '   // trim(adjustl(time_val)) //                   &
                    ' loop '   // trim(adjustl(c7))       // '"', iend-ibeg+2
            else if (global_slice_data(section)%slice_dir == 2) then
              write(iu,format1)                                                &
                    '"yslice ' // trim(adjustl(slice_loc)) //                  &
                    ' time '   // trim(adjustl(time_val)) //                   &
                    ' loop '   // trim(adjustl(c7))       // '"', iend-ibeg+2
            else
              write(iu,format1)                                                &
                    '"zslice ' // trim(adjustl(slice_loc)) //                  &
                    ' time '   // trim(adjustl(time_val)) //                   &
                    ' loop '   // trim(adjustl(c7))       // '"', iend-ibeg+2
            end if
          end if

          do n=ibeg,iend
            write(iu,format2) global_slice_data(section)%xs1(n),               &
                              global_slice_data(section)%ys1(n),               &
                              global_slice_data(section)%zs1(n),               &
                              global_slice_data(section)%cps1(n),              &
                              global_slice_data(section)%cfxs1(n),             &
                              global_slice_data(section)%cfys1(n),             &
                              global_slice_data(section)%cfzs1(n)
          end do

!         write last point to close the loop

          write(iu,format2) global_slice_data(section)%xs2(iend),              &
                            global_slice_data(section)%ys2(iend),              &
                            global_slice_data(section)%zs2(iend),              &
                            global_slice_data(section)%cps2(iend),             &
                            global_slice_data(section)%cfxs2(iend),            &
                            global_slice_data(section)%cfys2(iend),            &
                            global_slice_data(section)%cfzs2(iend)

        end do body_loop

      end do slice_loop

      close(iu)

    end if write_from_master

  end subroutine write_slice_data


!================================= PRINT_STATUS ==============================80
!
!  Writes current forces, residual to screen (within eighty columns!)
!
!=============================================================================80

  subroutine print_status( igrid, soln )

    use info_depr,            only : ntt, tightly_couple, cc_primal,           &
                                     complex_mode, complex_epsilon, ivisc
    use nml_nonlinear_solves, only : itime
    use lmpi,                 only : lmpi_master, lmpi_nproc, lmpi_bcast,      &
                                     lmpi_die, lmpi_reduce
    use system_extensions,    only : se_flush
    use solution_types,       only : elasticity, generic_gas, incompressible
    use ssdc_interface,       only : all_ssdc
    use sfe_interface,        only : all_sfe
    use inviscid_flux,        only : mean_decouple
    use debug_defs,           only : ntt_print
    use cfl_defs,             only : amut_max
    use gcr_defs,             only : rmsloc

    integer,         intent(in)    :: igrid
    type(soln_type), intent(inout) :: soln

    integer :: eqn, nresiduals, n_q, n_mf, n_turb, eq, i, fl

    logical       :: nan_stop

    real(dp) :: temp_time, nan, rmshistall
    real(dp) :: r_m, r_t

    character(len=80) :: f1

    logical :: old_format = .false.

  continue

old_format = .false.                  !FIXME when cc back from dead
if ( cc_primal ) old_format = .true.  !FIXME when cc back from dead

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = n_q - n_turb

! Account Wall Clock time (averaged over processors)

    call lmpi_reduce(soln%walltime(ntt), temp_time)
    call lmpi_bcast(temp_time)
    soln%walltime(ntt) = temp_time
    soln%walltime(ntt) = soln%walltime(ntt) / real(lmpi_nproc, dp)

    if ( all_ssdc ) return
    if ( all_sfe  ) return

! Write high-level convergence information to the screen

    if (tightly_couple .or. soln%eqn_set == generic_gas ) then
      nresiduals = soln%njac
    else
      nresiduals = soln%ndim + soln%n_turb
    end if

    if (mean_decouple) nresiduals = soln%ndim + 1

    screen_info: if ( lmpi_master ) then

! Header information

      write_header : if ( (ntt == 1) .and. (soln%eqn_set == elasticity) ) then

        write(6,'(a5,a23,4a13)') " Iter"," elasticity(1)-res_RMS",             &
        "     MAXIMUM","  X-location","  Y-location","  Z-location"
        write(6,'(a5,a23,4a13)') "     "," elasticity(2)-res_RMS",             &
        "     MAXIMUM","  X-location","  Y-location","  Z-location"
        write(6,'(a5,a23,4a13)') "     "," elasticity(3)-res_RMS",             &
        "     MAXIMUM","  X-location","  Y-location","  Z-location"
        write(6,'(a5,a23,4a13)') "     ","       delta:r-res_RMS",             &
        "     MAXIMUM","  X-location","  Y-location","  Z-location"

      elseif (itime == 0) then

        if (ntt == 1) then
         if ( soln%eqn_set /= generic_gas )then

           if ( .not.old_format .and. hanim .and. soln%n_q <= 7 ) then

           else
           write(6,'(a5,a23,4a13)')                                            &
           " Iter",                                                            &
           "            density_RMS",                                          &
           " density_MAX",                                                     &
           "  X-location",                                                     &
           "  Y-location",                                                     &
           "  Z-location"
           if (soln%n_turb > 0)                                                &
           write(6,'(a5,a23,4a13)')                                            &
           "     ",                                                            &
           "               turb_RMS",                                          &
           "    turb_MAX",                                                     &
           "  X-location",                                                     &
           "  Y-location",                                                     &
           "  Z-location"
           endif
         else
           write(6,'(a5,a23,a13,3a12,a3)')                                     &
           " Iter",                                                            &
           "                 L2_MAX",                                          &
           "      L1_MAX",                                                     &
           " X-location",                                                      &
           " Y-location",                                                      &
           " Z-location",                                                      &
           " Eq"
         end if
        end if

      elseif (itime > 0) then

         if ( soln%eqn_set /= generic_gas ) then
           write(6,*)
           write(6,'(a10,a18,4a13)')                                           &
           " Time-Step",                                                       &
           "       density_RMS",                                               &
           " density_MAX",                                                     &
           "  X-location",                                                     &
           "  Y-location",                                                     &
           "  Z-location"
           if (soln%n_turb > 0)                                                &
           write(6,'(a5,a23,4a13)')                                            &
           "     ",                                                            &
           "               turb_RMS",                                          &
           "    turb_MAX",                                                     &
           "  X-location",                                                     &
           "  Y-location",                                                     &
           "  Z-location"
         else
           write(6,'(a10,a18,a13,3a12,a3)')                                    &
           " Time-Step",                                                       &
           "            L2_MAX",                                               &
           "      L1_MAX",                                                     &
           " X-location",                                                      &
           " Y-location",                                                      &
           " Z-location",                                                      &
           " Eq"
         end if


      end if write_header

! Residual and location information

      if ( soln%eqn_set /= generic_gas ) then
        if ( .not.old_format .and. hanim ) then

          if ( ntt <= 10 .or. (ntt/ntt_print)*ntt_print == ntt ) then

          r_m = 0._dp
          do eq=1,n_mf
            r_m = r_m + rmsloc(eq)**2
          enddo
          r_m = sqrt( r_m / real(n_mf,dp) )

          r_t = 0._dp
          do eq=n_mf+1,n_q
            r_t = r_t + rmsloc(eq)**2
          enddo
          if ( n_turb > 0 ) &
          r_t = sqrt( r_t / real(n_turb,dp ) )

          i = max( 1, ntt-1 )
          fl = igrid
          if ( ivisc > 2 ) then
            if ( o(cfl_m_frechet(fl)) > 99000._dp .or. &
                 o(cfl_t_frechet(fl)) > 99000._dp .or. &
                 o(cfl_m_frechet(fl)) <    1.0_dp .or. &
                 o(cfl_t_frechet(fl)) <    1.0_dp ) then
              f1 = '(a,i6,a,2e9.2,2(a,e11.4)/11x,a,2e9.2,13x,a,e11.4)'
            else
              f1 = '(a,i6,a,2e9.2,2(a,e11.4)/11x,a,2f9.0,13x,a,e11.4)'
            endif
            write(6,f1) ' ntt=',ntt,' M/T:Res=',   &
            real(r_m,dp),real(r_t,dp),             &
            ' Lift=', o(soln%totforce(i)%cl),      &
            '     Drag=',  o(soln%totforce(i)%cd), &
            ' M/T:CFL=',o(cfl_m_frechet(fl)),      &
                        o(cfl_t_frechet(fl)),      &
            '     amut_max=',o(amut_max)
          else
            if ( o(cfl_m_frechet(fl)) > 99000._dp .or. &
                 o(cfl_t_frechet(fl)) > 99000._dp .or. &
                 o(cfl_m_frechet(fl)) <    1.0_dp .or. &
                 o(cfl_t_frechet(fl)) <    1.0_dp ) then
              f1 = '(a,i6,a,e9.2,2(a,e11.4),a,e9.2)'
            else
              f1 = '(a,i6,a,e9.2,2(a,e11.4),a,f9.0)'
            endif
            write(6,f1) ' ntt=',ntt,' Res=',     &
            real(r_m,dp),                        &
            ' Lift=', o(soln%totforce(i)%cl),    &
            ' Drag=',  o(soln%totforce(i)%cd),   &
            ' CFL=',o(cfl_m_frechet(fl))
          endif
          endif
        else
          write (6,'(i5, e23.15, e13.5, 3e13.5)')                              &
                ntt,                                                           &
                real(soln%rmshist( 1,ntt,1),dp   ),                            &
                real(soln%rmaxhist(1,ntt,1),dp   ),                            &
                real(soln%xlochist(1,ntt,1),dp   ),                            &
                real(soln%ylochist(1,ntt,1),dp   ),                            &
                real(soln%zlochist(1,ntt,1),dp   )
        endif
      else
        rmshistall = sum(soln%rmshist(1:nresiduals,ntt,1))
        eqn        = maxloc(soln%rmaxhist(1:nresiduals,ntt,1),1)
        write (6,'(i5, e23.15, e13.5, 3e12.4, i3)')                            &
              ntt,                                                             &
              real(rmshistall,              dp   ),                            &
              real(soln%rmaxhist(eqn,ntt,1),dp   ),                            &
              real(soln%xlochist(eqn,ntt,1),dp   ),                            &
              real(soln%ylochist(eqn,ntt,1),dp   ),                            &
              real(soln%zlochist(eqn,ntt,1),dp   ),                            &
              eqn
      end if

      if ( old_format .or. (.not.hanim .and.            &
           soln%n_turb > 0 .and. soln%eqn_set /= generic_gas ) ) then
        do eqn = nresiduals-soln%n_turb+1 , nresiduals
          write (6,'(a5, e23.15, e13.5, 3e13.5)')                              &
                "     ",                                                       &
                real(soln%rmshist(eqn,ntt,1),dp   ),                           &
                real(soln%rmaxhist(eqn,ntt,1),dp   ),                          &
                real(soln%xlochist(eqn,ntt,1),dp   ),                          &
                real(soln%ylochist(eqn,ntt,1),dp   ),                          &
                real(soln%zlochist(eqn,ntt,1),dp   )

        end do
      end if

! Force information

      if ( soln%eqn_set == elasticity ) then

        write (6,'(5x, e23.15, e13.5, 3e13.5)')                                &
          real(soln%rmshist(2,ntt,1),dp   ),                                   &
          real(soln%rmaxhist(2,ntt,1),dp   ),                                  &
          real(soln%xlochist(2,ntt,1),dp   ),                                  &
          real(soln%ylochist(2,ntt,1),dp   ),                                  &
          real(soln%zlochist(2,ntt,1),dp   )
        write (6,'(5x, e23.15, e13.5, 3e13.5)')                                &
          real(soln%rmshist(3,ntt,1),dp   ),                                   &
          real(soln%rmaxhist(3,ntt,1),dp   ),                                  &
          real(soln%xlochist(3,ntt,1),dp   ),                                  &
          real(soln%ylochist(3,ntt,1),dp   ),                                  &
          real(soln%zlochist(3,ntt,1),dp   )
        write (6,'(5x, e23.15, e13.5, 3e13.5)')                                &
          real(soln%totforce(ntt)%cl, dp   ),                                  &
          real(soln%totforce(ntt)%cd, dp   ),                                  &
          real(soln%totforce(ntt)%cmx,dp   ),                                  &
          real(soln%totforce(ntt)%cmy,dp   ),                                  &
          real(soln%totforce(ntt)%cmz,dp   )

      else

        if ( .not.hanim ) then
        write (6,'("       Lift",e23.15,"         Drag",e23.15)')              &
          real(soln%totforce(ntt)%cl,dp   ),                                   &
          real(soln%totforce(ntt)%cd,dp   )
        if ( airfoil == 1 .and. ivisc == 0 .and. &
             soln%eqn_set == incompressible ) then
          write (6,'(" Exact Lift",e23.15)') exact_zkt_lift()
        endif

        complex_pert_forces : if ( complex_mode ) then
          write (6,'("       Lift",e23.15,"         Drag",e23.15)')            &
          aimag(cmplx(soln%totforce(ntt)%cl,kind=dp))/real(complex_epsilon,dp),&
          aimag(cmplx(soln%totforce(ntt)%cd,kind=dp))/real(complex_epsilon,dp)
        end if complex_pert_forces
        endif

!       Stop if we have a NaN detected in lift

        nan_stop = .false.
        nan  = abs( real(soln%totforce(ntt)%cl,dp   ) )
        if( ((nan+nan <= nan) .and. (abs(nan) > tiny(0.0_dp   )))   .or.       &
            .not.((nan < abs(nan)) .or. (nan >= 0.0_dp   )) ) nan_stop = .true.

      endif

      call se_flush()

    end if screen_info

! Stop on NaN

    call lmpi_bcast(nan_stop)
    if(nan_stop) then
      if(lmpi_master) then
        write(*,*) 'NaN detected...stopping'
        call se_flush
      endif
      call lmpi_die
    endif

  end subroutine print_status


!========================== NEED_GLOBAL_BNDRY_DATA ===========================80
!
! Logical function to determine whether global boundary data needs to be
! collected for the current time step, for use in one or more output options
!
!=============================================================================80

  logical function need_global_bndry_data(fl, adjoint_mode)

    integer, intent(in) :: fl

    logical, intent(in) :: adjoint_mode

  continue

    need_global_bndry_data = .false.

    if ( output_global_bndry(fl, adjoint_mode) ) need_global_bndry_data = .true.
    if ( output_fwh_global_data() )     need_global_bndry_data = .true.
    if ( output_slice_global_bndry() )  need_global_bndry_data = .true.
    if ( output_aero_loads() )          need_global_bndry_data = .true.
    if ( output_tavg_global_bndry() )   need_global_bndry_data = .true.
    if ( output_massoud() )             need_global_bndry_data = .true.

  end function need_global_bndry_data


!============================= OUTPUT_GLOBAL_BNDRY ===========================80
!
! Logical function to determine whether a tecplot file containing solution
! data on one or more (global) boundaries is to be output during the
! current time step
!
!=============================================================================80

  logical function output_global_bndry(fl, adjoint_mode)

    use info_depr,            only : ntt, simulation_time, subiteration,       &
                                     physical_timestep
    use nml_nonlinear_solves, only : itime
    use nml_global,           only : boundary_animation_freq
    use io,                   only : prior_iters
    use adjoint_switches,     only : new_krylov

    integer, intent(in) :: fl

    logical, intent(in) :: adjoint_mode

    integer :: iter

  continue

    output_global_bndry = .false.

! if using the outer krylov wrapper (in adjoint), must base decisions here
! on search direction

    if ( new_krylov ) then

      iter = subiteration + prior_iters
      if ( ngrid > 1 ) iter = subiteration
      if ( adjoint_mode .and. itime /= 0 ) iter = physical_timestep

      if ( boundary_animation_freq(fl) /= 0 ) then
        if (iter/boundary_animation_freq(fl)                                   &
            *abs(boundary_animation_freq(fl))==iter) then
          output_global_bndry = .true.
        end if
      end if

    else

      iter = ntt + prior_iters
      if ( ngrid > 1 ) iter = ntt
      if ( adjoint_mode .and. itime /= 0 ) iter = physical_timestep

      if ( boundary_animation_freq(fl) /= 0 ) then
        if (iter/boundary_animation_freq(fl)                                   &
            *abs(boundary_animation_freq(fl))==iter) then
          output_global_bndry = .true.
        end if
      end if

    endif

!   to output initial state for unsteady cases

    if ( simulation_time <= 0._dp )  then
      if ( boundary_animation_freq(fl) > 0 .and. itime /= 0 ) then
        output_global_bndry = .true.
      end if
    end if

  end function output_global_bndry


!========================= OUTPUT_TAVG_GLOBAL_BNDRY ==========================80
!
! Logical function to determine whether a tecplot file containing time-averaged
! data on one or more (global) boundaries is to be output during the
! current time step
!
!=============================================================================80

  logical function output_tavg_global_bndry()

    use info_depr,            only : ntt, simulation_time
    use nml_global,           only : boundary_animation_freq_tavg
    use nml_nonlinear_solves, only : itime
    use io,                   only : prior_iters

    integer :: iter

  continue

    output_tavg_global_bndry = .false.

    iter = ntt + prior_iters

    if ( boundary_animation_freq_tavg /= 0 ) then
      if (iter/boundary_animation_freq_tavg              &
          *abs(boundary_animation_freq_tavg) == iter) then
        output_tavg_global_bndry = .true.
      end if
    end if

!   to output initial state for unsteady cases

    if ( simulation_time <= 0._dp )  then
      if ( boundary_animation_freq_tavg > 0 .and. itime /= 0 ) then
        output_tavg_global_bndry = .true.
      end if
    end if

  end function output_tavg_global_bndry


!============================ OUTPUT_GLOBAL_VOLUME ==========================80
!
! Logical function to determine whether a tecplot file containing solution
! volumetric data  is to be output during the current time step
!
!=============================================================================80

  logical function output_global_volume(fl,adjoint_mode)

    use nml_global,           only : volume_animation_freq
    use info_depr,            only : ntt, simulation_time, subiteration,       &
                                     physical_timestep
    use nml_nonlinear_solves, only : itime
    use io,                   only : prior_iters
    use adjoint_switches,     only : new_krylov

    integer, intent(in) :: fl

    logical, intent(in) :: adjoint_mode

    integer :: iter

  continue

    output_global_volume = .false.

! if using the outer krylov wrapper (in adjoint), must base decisions here
! on search direction

    if ( new_krylov ) then

      iter = subiteration + prior_iters
      if ( ngrid > 1 ) iter = subiteration
      if ( adjoint_mode .and. itime /= 0 ) iter = physical_timestep

      if ( volume_animation_freq(fl) /= 0 ) then
        if (iter/volume_animation_freq(fl)                                     &
            *abs(volume_animation_freq(fl))==iter) then
          output_global_volume = .true.
        end if
      end if

    else

      iter = ntt + prior_iters
      if ( ngrid > 1 ) iter = ntt
      if ( adjoint_mode .and. itime /= 0 ) iter = physical_timestep

      if ( volume_animation_freq(fl) /= 0 ) then
        if (iter/volume_animation_freq(fl)              &
            *abs(volume_animation_freq(fl)) == iter) then
          output_global_volume = .true.
        end if
      end if

    endif

!   to output initial state for unsteady cases

    if ( simulation_time <= 0._dp )  then
      if ( volume_animation_freq(fl) > 0 .and. itime /= 0 ) then
        output_global_volume = .true.
      end if
    end if

  end function output_global_volume


!========================== OUTPUT_SLICE_GLOBAL_BNDRY ========================80
!
! Logical function to determine whether a tecplot file containing sliced
! data on one or more (global) boundaries is to be output during the
! current time step
!
!=============================================================================80

  logical function output_slice_global_bndry()

    use info_depr,            only : ntt, simulation_time
    use nml_nonlinear_solves, only : itime
    use nml_global,           only : moving_grid, slice_freq
    use io,                   only : prior_iters

    integer :: iter

  continue

    output_slice_global_bndry = .false.

    iter = ntt + prior_iters

    if ( slice_freq /= 0 ) then
      if (iter/slice_freq*abs(slice_freq) == iter) then
        output_slice_global_bndry = .true.
      end if
    end if

!   to output initial state for unsteady cases

    if ( simulation_time <= 0._dp )  then
      if ( slice_freq > 0 .and. itime /= 0 ) then
        output_slice_global_bndry = .true.
      end if
    end if

!   cannot slice data before first grid movement step in dynamic-grid
!   cases because we may not yet have the required transform (i.e. if
!   the slicing is not carried out in the inertial frame, as is the case
!   for rotorcraft); so just disallow t=0 slcing for all dynamic-grid cases

    if (moving_grid) then
      if (simulation_time <= 0._dp) then
        output_slice_global_bndry = .false.
      end if
    end if

  end function output_slice_global_bndry


!========================= OUTPUT_FWH_GLOBAL_DATA ========================80
!
! Logical function to determine whether a tecplot file containing solution
! data on one or more (global) boundaries is to be output during the
! current time step
!
!=============================================================================80

  logical function output_fwh_global_data()

    use info_depr,             only : ntt, simulation_time
    use nml_fwh_acoustic_data, only : fwh_data_freq
    use nml_nonlinear_solves,  only : itime
    use io,                    only : prior_iters

    integer :: iter

  continue

    output_fwh_global_data = .false.

    iter = ntt + prior_iters

    if ( fwh_data_freq /= 0 ) then
      if (iter/fwh_data_freq*abs(fwh_data_freq) == iter) then
        if (ntt > 0) output_fwh_global_data = .true.
      end if
    end if

!   to output initial state for unsteady cases

    if ( simulation_time <= 0._dp )  then
      if ( fwh_data_freq > 0 .and. itime /= 0 ) then
        output_fwh_global_data = .true.
      end if
    end if

  end function output_fwh_global_data


!============================== OUTPUT_AERO_LOADS ============================80
!
! Logical function to determine whether a tecplot file containing aero loads
! on one or more (global) boundaries is to be output during the current
! time step
!
!=============================================================================80

  logical function output_aero_loads()

    use info_depr,            only : ntt, simulation_time
    use nml_nonlinear_solves, only : itime
    use nml_mdo_surface_data, only : aero_loads_output_freq
    use io,                   only : prior_iters
    use aeroelastic,          only : write_aero_loads_to_file

    integer :: iter

  continue

    output_aero_loads = .false.

    if (.not. write_aero_loads_to_file) return

    iter = ntt + prior_iters

    if (aero_loads_output_freq  /= 0) then
      if (iter/aero_loads_output_freq*abs(aero_loads_output_freq) == iter) &
          output_aero_loads = .true.
    end if

!   to output initial state for unsteady cases

    if ( simulation_time <= 0._dp )  then
      if ( aero_loads_output_freq > 0 .and. itime /= 0 ) then
        output_aero_loads = .true.
      end if
    end if

  end function output_aero_loads


!================================ OUTPUT_MASSOUD =============================80
!
! Logical function to determine whether a tecplot file containing "massoud"
! data on one or more (global) boundaries is to be output during the current
! time step
!
!=============================================================================80

  logical function output_massoud()

    use info_depr,            only : ntt, simulation_time
    use nml_nonlinear_solves, only : itime
    use nml_mdo_surface_data, only : massoud_output_freq
    use io,                   only : prior_iters
    use massoud,              only : write_massoud_file

    integer :: iter

  continue

    output_massoud = .false.

    if (.not. write_massoud_file) return

    iter = ntt + prior_iters

    if (massoud_output_freq  /= 0) then
      if (iter/massoud_output_freq*abs(massoud_output_freq) == iter)           &
          output_massoud = .true.
    end if

!   to output initial state for unsteady cases

    if ( simulation_time <= 0._dp )  then
      if ( massoud_output_freq > 0 .and. itime /= 0 ) then
        output_massoud = .true.
      end if
    end if

  end function output_massoud


!============================== ANIMATION_REQUESTED ==========================80
!
! Logical function to determine whether *some* type of animation or other
! output has been requested
!
!=============================================================================80

  logical function animation_requested(fl)

    use nml_global,           only : boundary_animation_freq,                  &
                                     volume_animation_freq, slice_freq,        &
                                     boundary_animation_freq_tavg
    use aeroelastic,          only : write_aero_loads_to_file
    use massoud,              only : write_massoud_file
    use sampling_headers,     only : number_of_geometries, sampling_frequency

    integer, intent(in) :: fl

    integer             :: sampling_sum

  continue

    animation_requested = .false.

     sampling_sum = 0
     sampling_sum = sum(abs(sampling_frequency(1:number_of_geometries)))

    if ( boundary_animation_freq(fl) /= 0     .or.                             &
         boundary_animation_freq_tavg /= 0    .or.                             &
         slice_freq /= 0                      .or.                             &
         volume_animation_freq(fl) /= 0       .or.                             &
         sampling_sum /= 0                    .or.                             &
         write_massoud_file                   .or.                             &
         write_aero_loads_to_file ) then
      animation_requested = .true.
    end if

  end function animation_requested


!=========================== FWH_GLOBAL_SURFACE_UNF ==========================80
!
!  Outputs a tecplot file on boundarie of time-dependent data
!  in binary format
!
!=============================================================================80

  subroutine fwh_global_surface_unf(grid, soln, time_step)

    use info_depr,             only : ncyc
    use nml_nonlinear_solves,  only : dt
    use grid_types,            only : grid_type
    use lmpi,                  only : lmpi_master, lmpi_conditional_stop
    use allocations,           only : my_alloc_ptr
    use file_utils,            only : file_exists, available_unit
    use boundary_slicing,      only : transform_to_moving_frame,               &
                                      transform_to_inertial_frame
    use moving_body_types,     only : moving_body
    use io,                    only : prior_iters
    use kinddefs,              only : r4
    use system_extensions,     only : se_open
    use nml_global,            only : irest
    use grid_motion_helpers,   only : get_body_id_from_bndry
    use string_utils,          only : list_to_array
    use nml_fwh_acoustic_data, only : fwh_acoustic_data_read, steps_per_period,&
                                      face_center_data, fwh_data_freq,         &
                                      data_time_variation, fwh_bndry_list,     &
                                      geom_time_variation, n_fwh_bndry,        &
                                      append_to_prior_data,                    &
                                      output_reference_frame

    type(grid_type),             intent(in)    :: grid
    type(soln_type),             intent(inout) :: soln

    integer,                     intent(in)    :: time_step

    logical                                    :: all_exist
    logical,                              save :: boundaries_not_set = .true.

    integer :: j, ib, ifixed, iu, istop, num
    integer :: nface, nbndry_to_read, ierr

    integer, dimension(:),   pointer, save :: output_fixed, fwh_data_period
    integer, dimension(:),   pointer, save :: output_frame
    integer, save                          :: nbndry_to_output
    integer, dimension(:),   pointer, save :: bndry_points
    integer, dimension(:),   pointer, save :: fwh_boundary_array

    logical, dimension(:),   pointer, save :: output_bndry
    logical                                :: echo_data

!   local variables for normals of boundary faces
    real(r4), dimension(:,:), allocatable      :: normal, xyz
    real(r4), dimension(3)                     :: normal_tri, xyz_tri
    real(r4), dimension(:),   allocatable      :: pressure_avg
    integer, dimension(3)                      :: nodes

    real(r4)                                   :: fwh_time, fwh_period, dt_in

    integer,                              save :: fwh_output_count = 0
    integer                                    :: surfp_ct, out_frame
    integer,                              save :: reference_frame

    character(len=80)                          :: filename
    character(len=1024),                  save :: title
    character(len=80)                          :: filename_p, filename_g
    character(len=32)                          :: id

    integer                                    :: iu_g
    integer                                    :: iu_p
    integer                                    :: iu_t

    real(dp),      parameter :: Po = 1.0_dp/1.4_dp

    integer,       parameter :: MAGIC_NUMBER = 42
    integer,       parameter :: FWH_VERSION = 1
    integer,       parameter :: FWH_VERSION_SUB = 0
    character(32), parameter :: UNITS = '[]'
    integer,       parameter :: GEOM = 1
    integer,       parameter :: FUNCTIONAL_FLOW_DATA = 2
    integer,       parameter :: UNSTRUCTURED = 2
    integer,       parameter :: CONSTANT = 1
    integer,       parameter :: PERIODIC = 2
    integer,       parameter :: APERIODIC = 3
    integer,       parameter :: CONST_PERIODIC = 4
    integer,       parameter :: CONST_APERIODIC = 5
    integer,       parameter :: NODE_CENTERED = 1
    integer,       parameter :: PRESSURE_DATA = 1
    integer,       parameter :: SINGLE_PRECISION = 1
    integer,       parameter :: ZERO = 0

    continue

    if (grid%cc) return    ! this routine not cc aware

    echo_data = .true.
    if (.not. lmpi_master) echo_data = .false.

!   first time through, determine which boundaries we will output for
!   animation - the user may override the default output types by
!   providing an auxilary file

    set_boundaries : if (boundaries_not_set) then

      title = trim(grid%project)
      reference_frame = 1

      call my_alloc_ptr(output_bndry,grid%nbound)    ! initializes to .false.
      call my_alloc_ptr(output_fixed,grid%nbound)    ! initializes to zero
      call my_alloc_ptr(output_frame,grid%nbound)    ! initializes to zero
      call my_alloc_ptr(bndry_points,grid%nbound)    ! initializes to zero
      call my_alloc_ptr(fwh_data_period,grid%nbound) ! initializes to zero

      bndry_points     = 0
      nbndry_to_output = 0

      istop = 0

!     legacy compatibility for now - if nml_fwh_acoustic_data has been read,
!     use the data set therein. If it has not been read, look for the old
!     "fwh_boundaries" file

      filename = 'fwh_boundaries'

      use_namelist_data : if (fwh_acoustic_data_read) then

        call list_to_array(fwh_bndry_list, fwh_boundary_array)

        if (lmpi_master) then
          if (n_fwh_bndry /= size(fwh_boundary_array)) istop = 1
        end if

        call lmpi_conditional_stop(istop,                                      &
                         'error: mismatch in n_fwh_bndry and fwh_bndry_list')

        do j=1, n_fwh_bndry

            ib = fwh_boundary_array(j)

            output_bndry(ib)     = .true.
            fwh_data_period(ib)  = steps_per_period(j)

            select case (trim(adjustl(data_time_variation(j))))

            case ('periodic')
              select case (trim(adjustl(geom_time_variation(j))))
              case ('periodic')
                output_fixed(ib) = 2
              case ('constant')
                output_fixed(ib) = 4
              case default
                 output_fixed(ib) = APERIODIC  !  as in original code
              end select

            case ('aperiodic')
              select case (trim(adjustl(geom_time_variation(j))))
              case ('aperiodic')
                output_fixed(ib) = 3 ! 0 or 3 same; original code chooses 3
              case ('constant')
                output_fixed(ib) = 5
              case default
                 output_fixed(ib) = APERIODIC  !  as in original code
              end select

            case ('constant')
              select case (trim(adjustl(geom_time_variation(j))))
              case ('constant')
                output_fixed(ib) = 1
              case default
                 output_fixed(ib) = APERIODIC  !  as in original code
              end select
            case default
!                precluded via namelist reader
            end select

            output_frame(ib) = output_reference_frame(j)

        end do

      else if ( file_exists(filename) ) then

        if (echo_data) write(*,'(a)') ' Reading fwh_boundaries file.'

!---rtb
!       for now hardwire the (legacy) face center output option (for Veer)

        face_center_data = .true.
!---rtb

        iu    = available_unit()

        call se_open(unit=iu,file=filename,status='old')

        rewind(iu)

        read(iu,*)
        read(iu,*)
        read(iu,*) nbndry_to_read

        if (echo_data) write(*,'(a,i0)') ' ...nbndry_to_read = ', nbndry_to_read

        read(iu,*)

        do j=1,nbndry_to_read

          read(iu,*) ib, ifixed, reference_frame

          if( ifixed < 1 .or. ifixed > 5 ) then
             ifixed = APERIODIC
          end if

          if( reference_frame > 3 .or. reference_frame < 1 ) then
             reference_frame = 1
          end if

          if ( echo_data ) write(*,'(a,i0,2x,i0,2x,i0)')                       &
             ' ...fwh boundary number, ifixed, reference_frame = ',            &
             ib, ifixed, reference_frame

!         check for illegal boundary number

          if (ib < 1 .or. ib > grid%nbound) then
            istop = istop + 1
            if (lmpi_master) then
              write(*,'(2a,i0)') ' Out-of-bounds boundary number specified ',  &
                          'in boundaries_to_animate file: ', ib
              if (istop == 1) then
                write(*,'(a,i0)') ' Note: valid range is 1 to',grid%nbound
                write(*,'(2a)') ' Perhaps you lumped boundaries in Party',     &
                           ' and did not account for the change in numbering'
                write(*,'(2a)') ' Or perhaps you are using mirroring',         &
                           ' and did not account for the change in numbering.'
              end if
            end if
          else
            output_bndry(ib)    = .true.
            output_fixed(ib)    = ifixed
            output_frame(ib)    = reference_frame
            fwh_data_period(ib) = steps_per_period(1)
          end if

        end do

        close(iu)

        if( istop > 0 ) then
           if (lmpi_master) write(*,*) 'Exiting... istop = ', istop
        end if

        call lmpi_conditional_stop(istop)

      else

!       have neither read in namelist nor found an fwh_boundary file: punt

        if (lmpi_master) then
          istop = 1
          write(*,*) 'Error: found neither fwh_boundaries file nor ',          &
                     'fwh_acoustic_data namelist input'
        end if

        call lmpi_conditional_stop(istop,                                      &
                             'FWH output requested but no FWH data specified')

      end if use_namelist_data

!     Determine the number of surface pressure points that will be output.
!     For quads, we will output 2 triangles.

      if (echo_data) then
        write(*,*) ! for readability
        write(*,'(a)')' FWH output surfaces'
      end if

      do ib=1,grid%nbound

        bndry_points(ib) = 0

        if (output_bndry(ib)) then

          soln%global_bndry_data(ib)%used = .true.
          nbndry_to_output = nbndry_to_output + 1

          if (echo_data) write(*,'(a,i0,a,i0)')                                &
             ' ...boundary selected to dump fwh data = ', ib, ' of ',grid%nbound

          nface = soln%global_bndry_data(ib)%nface

          if (face_center_data) then

            bndry_points(ib) = bndry_points(ib) + nface

            face_loop : do j=1,nface
              if (soln%global_bndry_data(ib)%f2n(4,j) /= 0)                    &
                  bndry_points(ib) = bndry_points(ib) + 1
            end do face_loop

          else

            bndry_points(ib) = soln%global_bndry_data(ib)%nfacenodes

          end if

          if ( echo_data ) write(*,'(a,3(i0,2x))')                             &
                 ' ...ib, nface bndry_points = ', ib, nface, bndry_points(ib)

        end if

      end do

      boundaries_not_set = .false.

      ierr = 0

      master_writes_headers : if (lmpi_master) then

        all_exist = .true.

        do ib=1,grid%nbound

          if ( (.not. output_bndry(ib)) ) cycle

          write(id, '(I3.3)') ib
          id = adjustl(id)

          filename = trim(title) // "_" // trim(id) // "_fwh_data.bin"

          all_exist = all_exist .and. file_exists(filename)

        end do

!       if the user chooses not to append to existing files, setting
!       all_exist = .false. below will prevent fwh_output_count being changed
!       in the next section from its initial value (zero), which will then
!       cause the grid and data files to be rewound, and the old data erased

        if (all_exist .and. .not. append_to_prior_data) then
           all_exist = .false.
        end if

        if (all_exist) then

          filename = trim(grid%project) // '_fwh_output_count.txt'

          if ( file_exists(filename) ) then
            iu_t = available_unit()
            call se_open(unit=iu_t,file=filename,status='old')
            read(iu_t,*)
            read(iu_t,*) fwh_output_count, dt_in, fwh_time, j, j
            dt_in = dt_in    ! to supress compiler warning
            if( fwh_output_count < 0 ) then
               write(*,*)
               write(*,*) 'Error in fwh_output_count ', fwh_output_count
               fwh_output_count = 0
            else if ( echo_data ) then
               write(*,*)
               write(*,'(a,i0)') ' Appending FWH files with count = ',         &
                                 fwh_output_count
            end if
            close(iu_t)
          else
            ierr = 1
            write(*,'(2a)') 'stopping, file not found: ', trim(filename)
          end if

        end if

        if ( fwh_output_count == 0 ) then

!         Write headers for output files

          do ib=1,grid%nbound

            if ( (.not. output_bndry(ib)) ) cycle

            nface = soln%global_bndry_data(ib)%nface

            write(id, '(I3.3)') ib
            id = adjustl(id)

            filename_p = trim(title) // "_" // trim(id) // "_fwh_data.bin"
            filename_g = trim(title) // "_" // trim(id) // "_fwh_grid.bin"

            iu_p = available_unit()
!           call se_open(unit=iu_p,file=filename_p,form='unformatted',         &
!                        access='stream',status='unknown')
            call se_open(unit=iu_p,file=filename_p,form='binary',              &
                                         status='unknown')
            iu_g = available_unit()
!           call se_open(unit=iu_g,file=filename_g,form='unformatted',         &
!                        access='stream',status='unknown')
            call se_open(unit=iu_g,file=filename_g,form='binary',              &
                                         status='unknown')

            rewind(iu_p)
            rewind(iu_g)

            write(iu_p) MAGIC_NUMBER
            write(iu_p) FWH_VERSION, FWH_VERSION_SUB
            write(iu_p) title
            write(iu_p) FUNCTIONAL_FLOW_DATA
            write(iu_p) 1
            write(iu_p) UNSTRUCTURED
            if ( output_fixed(ib) == CONST_PERIODIC ) then
              write(iu_p) PERIODIC
            else if( output_fixed(ib) == CONST_APERIODIC ) then
               write(iu_p) APERIODIC
            else
               write(iu_p) output_fixed(ib)
            endif
            write(iu_p) NODE_CENTERED
            write(iu_p) PRESSURE_DATA
            write(iu_p) output_frame(ib)
            write(iu_p) SINGLE_PRECISION
            write(iu_p) ZERO, ZERO
            write(iu_p) 1, 1

            write(iu_g) MAGIC_NUMBER
            write(iu_g) FWH_VERSION, FWH_VERSION_SUB
            write(iu_g) UNITS
            write(iu_g) title
            write(iu_g) GEOM
            write(iu_g) 1
            write(iu_g) UNSTRUCTURED
            if ( output_fixed(ib) == CONST_PERIODIC .or.                       &
                 output_fixed(ib) == CONST_APERIODIC ) then
              write(iu_g) CONSTANT
            else
              write(iu_g) output_fixed(ib)
            endif
            write(iu_g) NODE_CENTERED
            write(iu_g) SINGLE_PRECISION
            write(iu_g) ZERO, ZERO

            write(iu_p) id
            write(iu_g) id

            if (irest == 1 ) then
              num = ncyc/fwh_data_freq
            else
              num = ncyc/fwh_data_freq + 1
            end if
            fwh_period = real(dt * fwh_data_period(ib))
            if ( output_fixed(ib) == APERIODIC ) then
              write(iu_p) num ! aperiodic case, # of time steps
              write(iu_g) num
            else if( output_fixed(ib) == PERIODIC ) then
!             periodic case, period & # of time steps
              write(iu_p) fwh_period, num
              write(iu_g) fwh_period, num
            else if( output_fixed(ib) == CONST_APERIODIC ) then
              write(iu_p) num ! aperiodic case, # of time steps
            else if( output_fixed(ib) == CONST_PERIODIC ) then
              write(iu_p) fwh_period, num
            end if

            write(iu_p) bndry_points(ib) ! npoints

            write(iu_g) bndry_points(ib) ! npoints

            if (face_center_data) then

!             we don't have connectivity of face centers
              write(iu_g) ZERO             ! nfaces

            else

              write(iu_g) nface

              do j=1,nface
                if (soln%global_bndry_data(ib)%f2n(4,j) == 0) then
!                 tri face
                  write(iu_g) 3, soln%global_bndry_data(ib)%f2n(1,j),          &
                                 soln%global_bndry_data(ib)%f2n(2,j),          &
                                 soln%global_bndry_data(ib)%f2n(3,j)
                else
!                 quad face
                  write(iu_g) 4, soln%global_bndry_data(ib)%f2n(1,j),          &
                                 soln%global_bndry_data(ib)%f2n(2,j),          &
                                 soln%global_bndry_data(ib)%f2n(3,j),          &
                                 soln%global_bndry_data(ib)%f2n(4,j)
                end if
              end do

            end if

            close(iu_p)
            close(iu_g)

          end do

        end if

      end if master_writes_headers

      call lmpi_conditional_stop(ierr,'missing FWH fwh_output_count files')

      return

    end if set_boundaries

    fwh_time = real(dt * fwh_output_count * fwh_data_freq)

    output_bndry_data : do ib=1,grid%nbound

      if ( (.not. output_bndry(ib)) ) cycle

      allocate( pressure_avg(bndry_points(ib)) )

      if( fwh_output_count == 0 .or. output_fixed(ib) /= CONSTANT ) then
         allocate( xyz(bndry_points(ib), 3) )
         allocate( normal(bndry_points(ib), 3) )
       end if

      surfp_ct = 1

      nface = soln%global_bndry_data(ib)%nface

!     Convert data to the desired reference frame if not already there

      if (nface > 0) then

        if (output_frame(ib) == 1 .or. output_frame(ib) == 2) then
          out_frame = 0          ! inertial/ground-fixed frame
        else
          call get_body_id_from_bndry(ib, out_frame, ierr)
        end if

        if (soln%global_bndry_data(ib)%ref_frame == 0 .and. out_frame > 0) then

          call transform_to_moving_frame(                                      &
               soln%global_bndry_data(ib)%nfacenodes,                          &
               soln%global_bndry_data(ib)%xglobal_bndry,                       &
               soln%global_bndry_data(ib)%yglobal_bndry,                       &
               soln%global_bndry_data(ib)%zglobal_bndry,                       &
               soln%global_bndry_data(ib)%qglobal_bndry,                       &
               moving_body(out_frame)%inv_transform,                           &
               moving_body(out_frame)%body_lin_vel,                            &
               moving_body(out_frame)%body_ang_vel,                            &
               moving_body(out_frame)%xcg,                                     &
               moving_body(out_frame)%ycg,                                     &
               moving_body(out_frame)%zcg)

          soln%global_bndry_data(ib)%ref_frame = out_frame

        end if

      end if

      node_or_face_data : if (face_center_data) then

        face_centered_loop : do j=1,nface

          nodes(1) = soln%global_bndry_data(ib)%f2n(1,j)
          nodes(2) = soln%global_bndry_data(ib)%f2n(2,j)
          nodes(3) = soln%global_bndry_data(ib)%f2n(3,j)

          if ( fwh_output_count == 0 .or. output_fixed(ib) == PERIODIC         &
                                     .or. output_fixed(ib) == APERIODIC ) then

            call triangle_normal(soln%global_bndry_data(ib)%xglobal_bndry,     &
                                 soln%global_bndry_data(ib)%yglobal_bndry,     &
                                 soln%global_bndry_data(ib)%zglobal_bndry,     &
                                 nodes, xyz_tri, normal_tri)

            xyz(surfp_ct,:)    = xyz_tri(:)
            normal(surfp_ct,:) = normal_tri(:)

          end if

          pressure_avg(surfp_ct) = 1.0_dp / 3.0_dp *                           &
                      ( soln%global_bndry_data(ib)%qglobal_bndry(5,nodes(1))   &
                      + soln%global_bndry_data(ib)%qglobal_bndry(5,nodes(2))   &
                      + soln%global_bndry_data(ib)%qglobal_bndry(5,nodes(3)) ) &
                      - Po

          surfp_ct = surfp_ct + 1

          if (soln%global_bndry_data(ib)%f2n(4,j) /= 0) then
            ! Quads - split into 2 triangles

            ! triangle 1 2 3 already done
            ! Now do triangle 1 3 4

            nodes(2) = soln%global_bndry_data(ib)%f2n(3,j)
            nodes(3) = soln%global_bndry_data(ib)%f2n(4,j)

            if ( fwh_output_count == 0 .or. output_fixed(ib) == PERIODIC       &
                                       .or. output_fixed(ib) == APERIODIC ) then

              call triangle_normal(soln%global_bndry_data(ib)%xglobal_bndry,   &
                                   soln%global_bndry_data(ib)%yglobal_bndry,   &
                                   soln%global_bndry_data(ib)%zglobal_bndry,   &
                                   nodes, xyz_tri, normal_tri)

              xyz(surfp_ct,:)    = xyz_tri(:)
              normal(surfp_ct,:) = normal_tri(:)

            end if

            pressure_avg(surfp_ct) = 1.0_dp / 3.0_dp *                         &
                 ( soln%global_bndry_data(ib)%qglobal_bndry(5,nodes(1))        &
                 + soln%global_bndry_data(ib)%qglobal_bndry(5,nodes(2))        &
                 + soln%global_bndry_data(ib)%qglobal_bndry(5,nodes(3)) )      &
                 - Po

            surfp_ct = surfp_ct + 1

          end if

        end do face_centered_loop

      else

        node_centered_loop : do j=1,soln%global_bndry_data(ib)%nfacenodes

          if ( fwh_output_count == 0 .or. output_fixed(ib) == PERIODIC         &
                                     .or. output_fixed(ib) == APERIODIC ) then

            xyz(surfp_ct,1)    = soln%global_bndry_data(ib)%xglobal_bndry(j)
            xyz(surfp_ct,2)    = soln%global_bndry_data(ib)%yglobal_bndry(j)
            xyz(surfp_ct,3)    = soln%global_bndry_data(ib)%zglobal_bndry(j)

            normal(surfp_ct,1) = soln%global_bndry_data(ib)%bxnglobal_bndry(j)
            normal(surfp_ct,2) = soln%global_bndry_data(ib)%bynglobal_bndry(j)
            normal(surfp_ct,3) = soln%global_bndry_data(ib)%bznglobal_bndry(j)

          end if

          pressure_avg(surfp_ct) =                                             &
                      soln%global_bndry_data(ib)%qglobal_bndry(5,j) - Po

          surfp_ct = surfp_ct + 1

        end do node_centered_loop

      end if node_or_face_data

      master_writes_data : if (lmpi_master) then

        write(id, '(I3.3)') ib
        id = adjustl(id)

        filename_p = trim(title) // "_" // trim(id) // "_fwh_data.bin"
        filename_g = trim(title) // "_" // trim(id) // "_fwh_grid.bin"

        if ( (fwh_output_count == 0 .and. output_fixed(ib) == CONSTANT) .or.   &
                                   .not. (output_fixed(ib) == CONSTANT) ) then

          iu_p = available_unit()
!         call se_open(unit=iu_p,file=filename_p,form='unformatted',           &
!                      access='stream',status='unknown',position='append')
          call se_open(unit=iu_p,file=filename_p,form='binary',                &
                                       status='unknown',position='append')
          write(iu_p) fwh_time
          write(iu_p) pressure_avg

          close(iu_p)

         end if

         if ( fwh_output_count == 0 .or. output_fixed(ib) == PERIODIC          &
                                    .or. output_fixed(ib) == APERIODIC ) then

           iu_g = available_unit()
!          call se_open(unit=iu_g,file=filename_g,form='unformatted',          &
!                       access='stream',status='unknown',position='append')
           call se_open(unit=iu_g,file=filename_g,form='binary',               &
                                        status='unknown',position='append')

           if ( output_fixed(ib) == PERIODIC .or.                              &
                output_fixed(ib) == APERIODIC ) write(iu_g) fwh_time
           write(iu_g) xyz
           write(iu_g) normal
           close(iu_g)
         end if

      end if master_writes_data

      if( allocated(pressure_avg) ) deallocate(pressure_avg)
      if( allocated( xyz) ) deallocate(xyz)
      if( allocated( normal) ) deallocate(normal)

!     Convert data back to the inertial reference frame if not already there

      if (nface > 0) then

        if (soln%global_bndry_data(ib)%ref_frame /= 0) then

          call transform_to_inertial_frame(                                    &
               soln%global_bndry_data(ib)%nfacenodes,                          &
               soln%global_bndry_data(ib)%xglobal_bndry,                       &
               soln%global_bndry_data(ib)%yglobal_bndry,                       &
               soln%global_bndry_data(ib)%zglobal_bndry,                       &
               soln%global_bndry_data(ib)%qglobal_bndry,                       &
               moving_body(out_frame)%transform_matrix,                        &
               moving_body(out_frame)%body_lin_vel,                            &
               moving_body(out_frame)%body_ang_vel,                            &
               moving_body(out_frame)%xcg,                                     &
               moving_body(out_frame)%ycg,                                     &
               moving_body(out_frame)%zcg)

          soln%global_bndry_data(ib)%ref_frame = 0

        end if

      end if

    end do output_bndry_data

    fwh_output_count = fwh_output_count+1

    master_writes_footer : if (lmpi_master) then

      filename = trim(grid%project) // '_fwh_output_count.txt'
      iu_t     = available_unit()
      call se_open(unit=iu_t, file=filename)

      write(iu_t,*) ' count, dt, time, time_step, total_time_steps'
      write(iu_t,'(I6, 2E14.7, 2I8)') fwh_output_count,                        &
           real(dt * fwh_data_freq),                                           &
           fwh_time, time_step, time_step+prior_iters
      write(iu_t,*) ' fwh_data_freq '
      write(iu_t,*) fwh_data_freq

      write(iu_t,*) ' patch #, surface #, fwh_grid_fixed, reference_frame, pts '
      j = 0
      do ib=1,grid%nbound
        if ( (.not. output_bndry(ib)) ) cycle
        j = j + 1
        write(iu_t,'(5I8)') j, ib, output_fixed(ib), output_frame(ib),         &
                            bndry_points(ib)
      end do

      write(iu_t,*)
      write(iu_t,*) 'fwh_grid_fixed'
      write(iu_t,*) '0 or 3 = aperiodic geometry and data'
      write(iu_t,*) '1      = constant geometry and data'
      write(iu_t,*) '2      = periodic geometry and data'
      write(iu_t,*) '3 or 0 = aperiodic geometry and data'
      write(iu_t,*) '4      = constant geometry and periodic data'
      write(iu_t,*) '5      = constant geometry and aperiodic data'
      write(iu_t,*)
      write(iu_t,*) 'reference frame'
      write(iu_t,*) '1      = stationary, ground fixed frame'
      write(iu_t,*) '2      = rotating, ground fixed frame'
      write(iu_t,*) '3      = patch fixed frame'

      close(iu_t)

    end if master_writes_footer

  end subroutine fwh_global_surface_unf


!================================ TRIANGLE_NORMAL=============================80
!
!  Get the area weighted normals and the centroid of a triangle
!
!=============================================================================80

  subroutine triangle_normal(xglobal_bndry, yglobal_bndry, zglobal_bndry,      &
       nodes, xyz, normal)

    use kinddefs,                          only  : r4

    real(dp), dimension(:),          intent(in)  :: xglobal_bndry
    real(dp), dimension(:),          intent(in)  :: yglobal_bndry
    real(dp), dimension(:),          intent(in)  :: zglobal_bndry
    integer,  dimension(3),          intent(in)  :: nodes
    real(r4), dimension(3),          intent(out) :: normal, xyz

    real(dp) :: x1, x2, x3, y1, y2, y3, z1, z2, z3
    real(dp) :: ax, ay, az, bx, by, bz

    continue

    x1 = xglobal_bndry(nodes(1))
    y1 = yglobal_bndry(nodes(1))
    z1 = zglobal_bndry(nodes(1))
    x2 = xglobal_bndry(nodes(2))
    y2 = yglobal_bndry(nodes(2))
    z2 = zglobal_bndry(nodes(2))
    x3 = xglobal_bndry(nodes(3))
    y3 = yglobal_bndry(nodes(3))
    z3 = zglobal_bndry(nodes(3))

    xyz(1) = 1.0_dp / 3.0_dp * (x1 + x2 + x3)
    xyz(2) = 1.0_dp / 3.0_dp * (y1 + y2 + y3)
    xyz(3) = 1.0_dp / 3.0_dp * (z1 + z2 + z3)

    ax = (x1-x2); ay = (y1-y2); az = (z1-z2)
    bx = (x1-x3); by = (y1-y3); bz = (z1-z3)

    normal(1) = 0.5_dp*(ay*bz - by*az)
    normal(2) = 0.5_dp*(bx*az - ax*bz)
    normal(3) = 0.5_dp*(ax*by - bx*ay)

  end subroutine triangle_normal


!============================ OUTPUT_TAVG_SURFACE_DATA =======================80
!
!  Driver routine for output of a tecplot file on boundaries for visualizing
!  time-averaged data; selects between formatted and binary tecplot files
!  (binary files require proprietry tecplot libraries)
!
!=============================================================================80

  subroutine output_tavg_surface_data(grid, soln, time_step)

    use info_depr,            only : simulation_time
    use nml_nonlinear_solves, only : itime
    use nml_global,           only : boundary_animation_freq
    use grid_types,           only : grid_type
    use tecplot_io_helpers,   only : write_boundary_tec,                       &
                                     get_tec_file_extension,                   &
                                     set_boundaries_to_output
    use io,                   only : prior_iters
    use lmpi,                 only : lmpi_master

    type(grid_type),        intent(inout) :: grid
    type(soln_type),        intent(inout) :: soln

    integer,                intent(in)    :: time_step

    logical,                              save :: boundaries_not_set = .true.

    character(len=80)                          :: step, bndry
    character(len=80)                          :: restart_file
    character(len=3)                           :: tec_file_extension
    character(len=256)                         :: variable_list
    character(len=256)                         :: file_title
    character(len=256)                         :: zone_title
    character(len=256)                         :: filename

    integer                                    :: ib, n_output_variables
    integer                                    :: first_bndry, last_bndry
    integer                                    :: nface, nfacenodes, status
    integer                                    :: n_output_bndry

    logical, save                              :: append_timestep = .true.
    logical, save, dimension(:),   allocatable :: output_bndry
    logical, save, dimension(:),   allocatable :: output_bndry_requested
    logical, parameter                         :: always_output_ascii = .true.

    real(dp),      dimension(:,:), allocatable :: output_data
    real(dp)                                   :: time_val

  continue

    if ( grid%cc ) return  ! this routine not cc aware

!   first time through, determine which boundaries we will output for
!   animation

    if (boundaries_not_set) then

      allocate(output_bndry(grid%nbound))
      allocate(output_bndry_requested(grid%nbound))

      call set_boundaries_to_output(grid%nbound, output_bndry,                 &
                                    grid%bc,     grid%nnodes01,                &
                                    grid%y,      'tavg_boundaries_to_animate')

      do ib = 1,grid%nbound
        output_bndry_requested(ib) = output_bndry(ib)
        if (output_bndry(ib)) then
          soln%global_bndry_data(ib)%used = .true.
        end if
      end do

      boundaries_not_set = .false.

      if (boundary_animation_freq(grid%igrid) < 0) append_timestep = .false.

      return

    end if

!   adjust output_bndry list to account for boundary output that may not
!   be possible due to insufficient memory

    n_output_bndry = 0
    do ib = 1,grid%nbound
      if (output_bndry(ib)) then
        if (.not. soln%global_bndry_data(ib)%used) output_bndry(ib) = .false.
      end if
      if (output_bndry(ib)) n_output_bndry = n_output_bndry + 1
    end do

    if (n_output_bndry == 0 .and. .not.(lmpi_master)) return

    master_writes_file : if (lmpi_master) then

      write(*,*)
      write(*,'(a,i0)') " Writing Tecplot tavg boundary file for time step ",  &
                         time_step

!     print a message if we had to skip output due to memory limitations
!     if all requested boundaries have been skipped, bail out here

      do ib=1,grid%nbound
        if (output_bndry_requested(ib) .and. .not.(output_bndry(ib))) then
          write(*,'(a,i0,a)') ' ...skipping output of data on boundary ', ib,  &
                               ' due to insufficient memory'
        end if
      end do
      if (n_output_bndry == 0) return

!     set the file name

      if (append_timestep) then
        write(step,"(i0)")    time_step+prior_iters
        step = '_timestep' // trim(adjustl(step))
      else
        step = ''
      end if

      if (itime == 0) then
        time_val = real(time_step+prior_iters, dp)
      else
        time_val = simulation_time
      end if

      restart_file = trim(adjustl(grid%project))

      call get_tec_file_extension(tec_file_extension, always_output_ascii)

      filename = trim(adjustl(restart_file)) // '_tec_tavg_boundary' //        &
                 trim(adjustl(step)) // '.' // trim(adjustl(tec_file_extension))

!     set the file title

      file_title = 'tecplot geometry and solution file'

!     determine the id number of the first and last boundaries to be written

      first_bndry = -1
      first_bnd : do ib = 1,grid%nbound
        if (.not. output_bndry(ib)) cycle first_bnd
        first_bndry = ib
        exit first_bnd
      end do first_bnd

      last_bndry = -1
      last_bnd : do ib = 1,grid%nbound
        if (.not. output_bndry(ib)) cycle last_bnd
        last_bndry = ib
      end do last_bnd

!     create the list of variables

      variable_list = 'x y z tavg_rho tavg_p tavg_rho_rms tavg_p_rms'
      n_output_variables = 7

      boundary_loop : do ib=1,grid%nbound

        if (.not. output_bndry(ib)) cycle

!       set the zone title

        write(bndry,"(i0)") ib

        zone_title = '"boundary ' // trim(adjustl(bndry))    // '"'

        nface      =  soln%global_bndry_data(ib)%nface
        nfacenodes =  soln%global_bndry_data(ib)%nfacenodes

        have_data_to_output : if (nface > 0) then

!         store the data to be output

          allocate(output_data(n_output_variables, nfacenodes), stat=status)

          check_memory : if (status /= 0) then
            write(*,'(2a)')                                                    &
             ' WARNING: memory allocation failed when attempting to allocate', &
             ' output_data array in output_tavg_surface_data'
            write(*,'(a,i0)')                                                  &
             ' ...skipping requested boundary data output for boundary ', ib
!            allocate tiny amount of memory for safety
             allocate(output_data(1,1))
          end if check_memory

          output_data(1,:) = soln%global_bndry_data(ib)%xglobal_bndry(:)
          output_data(2,:) = soln%global_bndry_data(ib)%yglobal_bndry(:)
          output_data(3,:) = soln%global_bndry_data(ib)%zglobal_bndry(:)
!         tavg_rho
          output_data(4,:) = soln%global_bndry_data(ib)%tavg_qglobal_bndry(1,:)
!         tavg_p
          output_data(5,:) = soln%global_bndry_data(ib)%tavg_qglobal_bndry(5,:)
!         tavg_rho_rms
          output_data(6,:) = sqrt(soln%global_bndry_data(ib)%                  &
                             tavg_qglobal_bndry(6,:) - output_data(4,:)**2)
!         tavg_p_rms
          output_data(7,:) = sqrt(soln%global_bndry_data(ib)%                  &
                             tavg_qglobal_bndry(10,:) - output_data(5,:)**2)

!         now output the data on current boundary in either ascii or binary form
!         currently: always ascii via presence of always_output_ascii

          call write_boundary_tec(n_output_variables, nfacenodes, nface,       &
                                  soln%global_bndry_data(ib)%f2n, output_data, &
                                  file_title, zone_title, variable_list,       &
                                  filename, ib, first_bndry, last_bndry,       &
                                  status, time_val, always_output_ascii)

          if (allocated(output_data)) deallocate(output_data)

        end if have_data_to_output

      end do boundary_loop

    end if master_writes_file

  end subroutine output_tavg_surface_data


!================================ WRITE_VOL_TEC ==============================80
!
!  Driver routine to write  a volume tecplot file in either ascii or binary
!  format...binary requires link to tecplot libraries.
!
!  Each process writes a file for its zone; files can be "cat"'ed together or
!  read by individually by tecplot
!
!  If "turbulent_fluctuations" output is chosen, note the expression for "zk"
!  is valid for SA model only; the remainder are valid for most one and two
!  equation models
!
!=============================================================================80

  subroutine write_vol_tec( grid, soln, time_step, sadj )

    use lmpi,                 only : lmpi_conditional_stop, lmpi_id,           &
                                     lmpi_master, lmpi_synchronize, lmpi_reduce
    use system_extensions,    only : se_flush
    use exact_defs,           only : ic_exact
    use info_depr,            only : xmach, simulation_time, cc_primal, twod,  &
                                     ntt, physical_timestep, tightly_couple
    use nml_nonlinear_solves, only : itime
    use nml_global,           only : volume_animation_freq, moving_grid
    use exact,                only : exact_q
    use nml_time_avg_params , only : itime_avg
    use nml_overset_data,     only : overset_flag
    use pundit,               only : pundit_flag
    use fluid,                only : gamma, xgm1
    use grid_types,           only : grid_type
    use io,                   only : prior_iters
    use tecplot_io_helpers,   only : write_volume_tec, get_tec_file_extension
    use nml_volume_output,   only : max_tec_vars,                              &
                                    need_turb_variables_v=>need_turb_variables,&
                                    n_output_variables_v=>n_output_variables,  &
                                    output_variables_v=>output_variables,      &
                                    export_to
    use cgamma_util,          only : compute_aspect_ratio
    use lsq_defs,             only : nc_mapped_lsq
    use allocations,          only : my_alloc_ptr
    use twod_util,            only : copy_array_2d
    use solve_box,            only : reference_q_read
    use stability_defs,       only : spectral_local
    use debug_defs,           only : check_defect_correction
    use generic_gas_map,      only : n_momx, n_momy, n_momz, n_density, n_etot,&
                                     n_temperature_j, n_energy, n_energy_last, &
                                     n_pressure_k, n_sonic_k, n_turb_g,        &
                                     n_molecular_weight, n_species, n_amu_k
    use turb_gen,             only : n_turb_ke, n_dis_nutl
    use shared_gas_variables, only : spec_propv
    use global_image,         only : global_image_export_to
    use solution_adj,         only : sadj_type
    use solution_types,       only : compressible, incompressible, generic_gas

    type(grid_type),           intent(in   ) :: grid
    type(soln_type),           intent(inout) :: soln
    integer,                   intent(in   ) :: time_step
    type(sadj_type), optional, intent(in   ) :: sadj

    real(dp)           :: x, y, z, rho, u, v, w, p, entropy, mach, temperature
    real(dp)           :: tt, tv, mol_wt, etot, htot, ev, sonic, mu, velsq
    real(dp)           :: turb_ke, turb_diss
    real(dp)           :: rho_error, u_error, v_error, w_error, p_error
    real(dp)           :: vort_x, vort_y, vort_z, vort_mag, div_vel, blank, mesh
    real(dp)           :: p_inf, mu_t, qcriterion, slen, turb1, turb2, turb3,  &
                          turb4, turb5, turb6, turb7
    real(dp)           :: cp, cp_factor, vol, res1, res2, res3, res4, res5
    real(dp)           :: turres1, turres2, turres3, turres4, res_gcl
    real(dp)           :: turres5, turres6, turres7
    real(dp)           :: rho_tavg, u_tavg, v_tavg, w_tavg, p_tavg
    real(dp)           :: rho_trms, u_trms, v_trms, w_trms, p_trms
    real(dp)           :: cgamma, aspect_ratio, skip_q, spectral_rate_local
    real(dp)           :: hrlesblend, id_l2g

    integer            :: i, ielem, n, ierr, alloc_failure, n_q_dof, iflagslen
    integer            :: ns, k, step_int
    integer            :: beg_trb, end_trb
    integer,     save  :: n_output_variables, output_data_size
    integer,     save  :: totalcells, status
    integer,     save  :: allocation_failures

    logical,     save  :: init = .true.
    logical,     save  :: append_timestep = .true.
    logical,     save  :: tets_only, need_turb_variables

    real(dp),          dimension(:,:), allocatable, save :: output_data
    integer,           dimension(:),   allocatable, save :: valuelocation
    real(dp),          dimension(6)                      :: turbfluctuations
    real(dp),          dimension(soln%adim)              :: lambda
    real(dp),          dimension(n_species)              :: rho_i

    character(len=80), dimension(max_tec_vars),     save :: output_variables
    character(len=256)                                   :: variable_list
    character(len=256)                                   :: file_title
    character(len=256)                                   :: zone_title
    character(len=256)                                   :: filename
    character(len=80)                                    :: restart_file
    character(len=80)                                    :: proc_id
    character(len=80)                                    :: step, err_msg
    character(len=3)                                     :: tec_file_extension
    character(len=80)                                    :: species_name

    logical :: aspect_ratio_needed = .false.

    real(dp), dimension(:), pointer :: ar
    real(dp)                        :: time_val
    real(dp), dimension(soln%n_q)   :: egradx, egrady, egradz, q_exact

    logical :: forcing_term_div_vel, adjoint_mode

  continue

    turb1   = 0.0_dp
    turb2   = 0.0_dp
    turb3   = 0.0_dp
    turb4   = 0.0_dp
    turb5   = 0.0_dp
    turb6   = 0.0_dp
    turb7   = 0.0_dp
    mu_t    = 0.0_dp
    turres1 = 0.0_dp
    turres2 = 0.0_dp
    turres3 = 0.0_dp
    turres4 = 0.0_dp
    turres5 = 0.0_dp
    turres6 = 0.0_dp
    turres7 = 0.0_dp

    adjoint_mode = .false.
    if ( present(sadj) ) adjoint_mode = .true.

    if ( grid%origin > 1 .and. grid%cc ) return !skip agglomerated cc grids

    res_gcl = huge(1._dp)
    mesh    = huge(1._dp)
    blank   = huge(1._dp)
    id_l2g  = huge(1._dp)

!   first time through, determine which variables we will output

    initial_setup : if (init) then

      n_output_variables  = n_output_variables_v
      output_variables(:) = output_variables_v(:)
      need_turb_variables = need_turb_variables_v

      output_data_size = grid%nnodes01
      if ( cc_primal ) output_data_size = max( output_data_size, grid%ncell01 )
      allocate(output_data(n_output_variables, output_data_size), stat=status)
      allocate(valuelocation(n_output_variables))

      alloc_failure = 0

      check_memory : if (status /= 0) then
        write(*,'(2a)')                                                    &
         ' WARNING: memory allocation failed when attempting to allocate', &
         ' output_data array in write_vol_tec'
        write(*,'(a,i0)')                                                  &
         ' ...skipping requested volume data output on processor ', lmpi_id
!        allocate tiny amount of memory for safety
         allocate(output_data(1,1))
         alloc_failure = 1
         call se_flush()
         call lmpi_synchronize()
      end if check_memory

      call lmpi_reduce(alloc_failure,allocation_failures)

      tets_only  = .true.
      totalcells = 0
      do ielem = 1, grid%nelem
        totalcells = totalcells + grid%elem(ielem)%ncell
        if (grid%elem(ielem)%type_cell /= 'tet') tets_only  = .false.
      end do

      if (volume_animation_freq(grid%igrid) < 0) append_timestep = .false.

!     set indicator for cc or nodal output

      valuelocation(:) = 1   ! default to nodal locations

      if (cc_primal) then

        valuelocation(:) = 0 ! default to cc locations

!       x,y,z iblank, imesh, id_l2g and res_gcl are always at nodal locations

        do n = 1,n_output_variables

          select case(trim(adjustl(output_variables(n))))

            case('x')
              valuelocation(n) = 1

            case('y')
              valuelocation(n) = 1

            case('z')
              valuelocation(n) = 1

            case('iblank')
              valuelocation(n) = 1

            case('imesh')
              valuelocation(n) = 1

            case('res_gcl')
              valuelocation(n) = 1

            case('id_l2g')
              valuelocation(n) = 1

          end select

        end do

      end if

      init = .false.

      return

    end if initial_setup

!   only do output if we had enough memory to allocate the output_data array

    have_memory_for_output : if (status == 0) then

      ierr = 0
      output_data(:,1) = -huge(1.0_dp)

      if ( soln%eqn_set == compressible ) then
        p_inf     = 1._dp/gamma
        cp_factor = gamma*xmach**2
      else
        p_inf     = 1._dp
        cp_factor = 1._dp
      end if

      beg_trb = 1
      end_trb = soln%n_turb

      if (cc_primal) then
!       CLR: not sure about the following if n_turb=4:
        beg_trb = soln%n_q - soln%n_turb + 1
        end_trb = soln%n_q - soln%n_turb + 1
      end if

      output_data = 0._dp

      forcing_term_div_vel = .false.
      do n = 1,n_output_variables
        if( trim(adjustl(output_variables(n))) /= 'div_vel' ) cycle
        if ( ic_exact ) forcing_term_div_vel = .true.
        exit
      enddo

!     setup grid coordinate output - always at nodes

      loop_over_nodes : do i = 1, grid%nnodes01

        x        = grid%x(i)
        y        = grid%y(i)
        z        = grid%z(i)

        blank    = 1._dp
        mesh     = 0._dp
        res_gcl  = 0._dp
        id_l2g   = grid%l2g(i)

        if (overset_flag.or.pundit_flag) then
          blank = real(grid%iblank(i), dp)
          mesh  = real(grid%imesh(i), dp)
        end if

!       blank outer layer of nodes if CC to improve appearance in
!       tecplot since we don't provide tecplot with face-to-cell
!       connectivity across partitions

        if (cc_primal) then
          if (i > grid%nnodes0) blank = 0._dp
        end if

        if (moving_grid) then
          res_gcl = grid%res_gcl(1,i)
        end if

        gather_xyz_data : do n = 1,n_output_variables

          select case(trim(adjustl(output_variables(n))))

            case('x')
              output_data(n,i) = x

            case('y')
              output_data(n,i) = y

            case('z')
              output_data(n,i) = z

            case('iblank')
              output_data(n,i) = blank

            case('imesh')
              output_data(n,i) = mesh

            case('res_gcl')
              output_data(n,i) = res_gcl

            case('id_l2g')
              output_data(n,i) = id_l2g

            case default

          end select

        end do gather_xyz_data

      end do loop_over_nodes

      n_q_dof = grid%nnodes01

      if (cc_primal) then
        n_q_dof = grid%ncell01
      end if

      aspect_ratio_needed = .false.
      do n = 1,n_output_variables
        if ( trim(adjustl(output_variables(n))) /= 'aspect_ratio') cycle
        aspect_ratio_needed = .true.
        call my_alloc_ptr(ar,n_q_dof)
        exit
      enddo

      if ( aspect_ratio_needed .and. .not.cc_primal ) then
        call compute_aspect_ratio( grid%nedgeloc, grid%ra, 2, grid%eptr, &
                                   grid%x, grid%y, grid%z, ar )
        if ( twod )                                                            &
        call copy_array_2d( grid%nnodes01, grid%nnodes0_2d, grid%node_pairs_2d,&
                            ar, 1 )
      elseif ( aspect_ratio_needed ) then
        call compute_aspect_ratio( grid%nface, grid%area_face, 6, grid%fptr, &
                                   grid%xc, grid%yc, grid%zc, ar )
      endif

      aspect_ratio = 1.0_dp
      spectral_rate_local = -1._dp

      nodes_or_cells : do i = 1, n_q_dof

!       set eqn_set-independent data

        slen     = 0.0_dp
        if (grid%idistfcn /= 0) then
          slen   = grid%slen(i)
        end if

        cgamma = 0._dp
        if ( .not.cc_primal .and. nc_mapped_lsq ) then
          cgamma = grid%cgamma(i)
        elseif( cc_primal ) then
          cgamma = grid%cgamma(i)
        endif

        iflagslen = grid%iflagslen(i)

        if ( aspect_ratio_needed ) aspect_ratio = ar(i)

        skip_q = 0._dp                            !=0, solved
        if ( grid%skip_q(i) > 0 ) skip_q = 1._dp  !=1, skipped

        if ( check_defect_correction ) spectral_rate_local = spectral_local(i)

        vol = grid%volq(i)   ! volq points to either vol or cell_vol

        vort_x   = soln%grady(4,i)-soln%gradz(3,i)
        vort_y   = soln%gradz(2,i)-soln%gradx(4,i)
        vort_z   = soln%gradx(3,i)-soln%grady(2,i)
        vort_mag = sqrt(vort_x*vort_x + vort_y*vort_y + vort_z*vort_z)

        div_vel  = soln%gradx(2,i)+soln%grady(3,i)+soln%gradz(4,i)
        if ( forcing_term_div_vel ) then
          call exact_q( soln%eqn_set, soln%n_q,           &
          grid%xq(i), grid%yq(i), grid%zq(i), egradx, gradient=1)
          call exact_q( soln%eqn_set, soln%n_q,           &
          grid%xq(i), grid%yq(i), grid%zq(i), egrady, gradient=2)
          call exact_q( soln%eqn_set, soln%n_q,           &
          grid%xq(i), grid%yq(i), grid%zq(i), egradz, gradient=3)
          div_vel  = div_vel - ( egradx(2) + egrady(3) + egradz(4) )
        endif

        if ( present(sadj) ) then
          lambda(:) = sadj%rlam(1:soln%adim,i,1)
          res1     = 0.0_dp
          res2     = 0.0_dp
          res3     = 0.0_dp
          res4     = 0.0_dp
        else
          res1     = soln%res(1,i)
          res2     = soln%res(2,i)
          res3     = soln%res(3,i)
          res4     = soln%res(4,i)
        endif

        select case (soln%n_turb)

          case(1)

            if ( cc_primal ) then
              turb1   = soln%q_dof(beg_trb,i)
            else
              turb1   = soln%turb(1,i)
            end if
            turb2   = 0.0_dp
            mu_t    = soln%amut(i)
            if ( .not.tightly_couple ) then
              turres1 = soln%turbres(1,i)
            else
              turres1 = soln%res(beg_trb,i)
            endif
            turres2 = 0.0_dp

          case(2)

            if ( cc_primal ) then
              turb1   = soln%q_dof(beg_trb,i)
              turb2   = soln%q_dof(end_trb,i)
            else
              turb1   = soln%turb(1,i)
              turb2   = soln%turb(2,i)
            end if
            mu_t    = soln%amut(i)
            if ( .not.tightly_couple ) then
              turres1 = soln%turbres(1,i)
              turres2 = soln%turbres(2,i)
            else
              turres1 = soln%res(beg_trb  ,i)
              turres2 = soln%res(beg_trb+1,i)
            endif

          case(4)

            if ( cc_primal ) then
              turb1   = soln%q_dof(beg_trb,i)
              turb2   = soln%q_dof(end_trb,i)
!             CLR: do not know what to do here for 4-eqns turb
!             turb3 = ?
!             turb4 = ?
            else
              turb1   = soln%turb(1,i)
              turb2   = soln%turb(2,i)
              turb3   = soln%turb(3,i)
              turb4   = soln%turb(4,i)
            end if
            mu_t    = soln%amut(i)
            if ( .not.tightly_couple ) then
              turres1 = soln%turbres(1,i)
              turres2 = soln%turbres(2,i)
              turres3 = soln%turbres(3,i)
              turres4 = soln%turbres(4,i)
            else
              turres1 = soln%res(beg_trb  ,i)
              turres2 = soln%res(beg_trb+1,i)
!             CLR: do not know what to do here for 4-eqns turb
!             turres3 = ?
!             turres4 = ?
            endif

          case(7)

            if ( cc_primal ) then
              turb1   = soln%q_dof(beg_trb,i)
              turb2   = soln%q_dof(end_trb,i)
!             CLR: do not know what to do here for 4-eqns turb
!             turb3 = ?
!             turb4 = ?
!             turb5 = ?
!             turb6 = ?
!             turb7 = ?
            else
              turb1   = soln%turb(1,i)
              turb2   = soln%turb(2,i)
              turb3   = soln%turb(3,i)
              turb4   = soln%turb(4,i)
              turb5   = soln%turb(5,i)
              turb6   = soln%turb(6,i)
              turb7   = soln%turb(7,i)
            end if
            mu_t    = soln%amut(i)
            if ( .not.tightly_couple ) then
              turres1 = soln%turbres(1,i)
              turres2 = soln%turbres(2,i)
              turres3 = soln%turbres(3,i)
              turres4 = soln%turbres(4,i)
              turres5 = soln%turbres(5,i)
              turres6 = soln%turbres(6,i)
              turres7 = soln%turbres(7,i)
            else
              turres1 = soln%res(beg_trb  ,i)
              turres2 = soln%res(beg_trb+1,i)
!             CLR: do not know what to do here for 4-eqns turb
!             turres3 = ?
!             turres4 = ?
!             turres5 = ?
!             turres6 = ?
!             turres7 = ?
            endif


          case default

            turb1   = 0.0_dp
            turb2   = 0.0_dp
            turb3   = 0.0_dp
            turb4   = 0.0_dp
            turb5   = 0.0_dp
            turb6   = 0.0_dp
            turb7   = 0.0_dp
            mu_t    = 0.0_dp
            turres1 = 0.0_dp
            turres2 = 0.0_dp
            turres3 = 0.0_dp
            turres4 = 0.0_dp
            turres5 = 0.0_dp
            turres6 = 0.0_dp
            turres7 = 0.0_dp

        end select

!       default all time avg/rms data to zero

        rho_tavg = 0.0_dp
        u_tavg   = 0.0_dp
        v_tavg   = 0.0_dp
        w_tavg   = 0.0_dp
        p_tavg   = 0.0_dp
        rho_trms = 0.0_dp
        u_trms   = 0.0_dp
        v_trms   = 0.0_dp
        w_trms   = 0.0_dp
        p_trms   = 0.0_dp

        rho_error = 0._dp
        u_error   = 0._dp
        v_error   = 0._dp
        w_error   = 0._dp
        p_error   = 0._dp

        !INITIALIZE
        rho         = 0._dp
        u           = 0._dp
        v           = 0._dp
        w           = 0._dp
        p           = 0._dp
        cp          = 0._dp
        temperature = 0._dp
        mach        = 0._dp
        entropy     = 0._dp
        res5        = 0._dp
        ev          = 0._dp
        tv          = 0._dp
        tt          = 0._dp
        turb_ke     = 0._dp
        turb_diss   = 0._dp
        mol_wt      = 0._dp
        sonic       = 0._dp
        htot        = 0._dp
        etot        = 0._dp
        mu          = 0._dp

!       set data that depends on physics (eqn_set) type

        select case(soln%eqn_set)

          case (compressible)

            rho         = soln%q_dof(1,i)
            u           = soln%q_dof(2,i)
            v           = soln%q_dof(3,i)
            w           = soln%q_dof(4,i)
            p           = soln%q_dof(5,i)
            cp          = 2._dp*(p/p_inf - 1._dp)/cp_factor
            velsq       = u*u + v*v + w*w
            temperature = gamma*p/rho
            mach        = sqrt((velsq)/temperature)
            entropy     = log((p/p_inf)/rho**gamma)
            htot        = (p/rho)*(gamma*xgm1) + 0.5_dp*velsq
            etot        = (p/rho)*xgm1         + 0.5_dp*velsq

            if ( present(sadj) ) then
              res5        = 0.0_dp
            else
              res5        = soln%res(5,i)
            end if

            if ( ic_exact ) then
              call exact_q( soln%eqn_set, soln%n_q,                    &
                           grid%xq(i), grid%yq(i), grid%zq(i), q_exact )
              rho_error = soln%q_dof(1,i) - q_exact(1)
              u_error   = soln%q_dof(2,i) - q_exact(2)
              v_error   = soln%q_dof(3,i) - q_exact(3)
              w_error   = soln%q_dof(4,i) - q_exact(4)
              p_error   = soln%q_dof(5,i) - q_exact(5)
            elseif ( reference_q_read ) then
              rho_error = soln%q_dof(1,i) - soln%q_dof_res0(1,i)
              u_error   = soln%q_dof(2,i) - soln%q_dof_res0(2,i)
              v_error   = soln%q_dof(3,i) - soln%q_dof_res0(3,i)
              w_error   = soln%q_dof(4,i) - soln%q_dof_res0(4,i)
              p_error   = soln%q_dof(5,i) - soln%q_dof_res0(5,i)
            endif

!          time average and time rms data only for compressible path

           if (itime_avg /= 0) then
             rho_tavg  = soln%q_time_avg(1,i)
             u_tavg    = soln%q_time_avg(2,i)
             v_tavg    = soln%q_time_avg(3,i)
             w_tavg    = soln%q_time_avg(4,i)
             p_tavg    = soln%q_time_avg(5,i)
             rho_trms  = sqrt(abs(soln%q_time_avg(6,i)  - rho_tavg**2))
             u_trms    = sqrt(abs(soln%q_time_avg(7,i)  - u_tavg**2))
             v_trms    = sqrt(abs(soln%q_time_avg(8,i)  - v_tavg**2))
             w_trms    = sqrt(abs(soln%q_time_avg(9,i)  - w_tavg**2))
             p_trms    = sqrt(abs(soln%q_time_avg(10,i) - p_tavg**2))
           end if

          case (incompressible)

            rho         = 1.0_dp
            u           = soln%q_dof(2,i)
            v           = soln%q_dof(3,i)
            w           = soln%q_dof(4,i)
            p           = soln%q_dof(1,i)
            cp          = 2._dp*(p/p_inf - 1._dp)/cp_factor
            mach       = 0._dp
            entropy     = log(p/p_inf)

            if ( ic_exact ) then
              call exact_q( soln%eqn_set, soln%n_q,                    &
                           grid%xq(i), grid%yq(i), grid%zq(i), q_exact )
              u_error = soln%q_dof(2,i) - q_exact(2)
              v_error = soln%q_dof(3,i) - q_exact(3)
              w_error = soln%q_dof(4,i) - q_exact(4)
              p_error = soln%q_dof(1,i) - q_exact(1)
            elseif ( reference_q_read ) then
              u_error = soln%q_dof(2,i) - soln%q_dof_res0(2,i)
              v_error = soln%q_dof(3,i) - soln%q_dof_res0(3,i)
              w_error = soln%q_dof(4,i) - soln%q_dof_res0(4,i)
              p_error = soln%q_dof(1,i) - soln%q_dof_res0(1,i)
            endif

          case (generic_gas)

            do ns = 1,n_species
              rho_i(ns) = soln%q_dof(ns,i)
            end do

            rho         = soln%q_dof(n_density,i)
            u           = soln%q_dof(n_momx,i) / rho
            v           = soln%q_dof(n_momy,i) / rho
            w           = soln%q_dof(n_momz,i) / rho
            p           = soln%q_dof(n_pressure_k(1),i)
            tt          = soln%q_dof(n_temperature_j(1),i)
            etot        = soln%q_dof(n_etot,i) / rho
            htot        = ( soln%q_dof(n_etot,i) + p ) / rho
            sonic       = soln%q_dof(n_sonic_k(1),i)
            mol_wt      = soln%q_dof(n_molecular_weight,i)
            mach        = sqrt(u**2 + v**2 + w**2)/sonic
            mu          = soln%q_dof(n_amu_k(1),i)

            if (n_energy > 1) then
              tv        = soln%q_dof(n_temperature_j(2),i)
              ev        = soln%q_dof(n_energy_last,i)
            end if

            if (n_turb_g > 0)then
              turb_ke   = soln%q_dof(n_turb_ke,i)
              turb_diss = soln%q_dof(n_dis_nutl,i)
            end if

          case default

        end select

        qcriterion = q_criterion(soln%eqn_set, soln%n_grd, soln%gradx(:,i),  &
                                 soln%grady(:,i), soln%gradz(:,i))

        hrlesblend = hrles_blend(soln%eqn_set, soln%n_tot, soln%n_grd, slen, &
                                 soln%q_dof(:,i), soln%turb(:,i),            &
                                 soln%gradx(:,i), soln%grady(:,i),           &
                                 soln%gradz(:,i))

        if (need_turb_variables) then
          turbfluctuations =                                                 &
          turb_fluctuations(soln%n_grd, soln%n_tot, soln%n_turb,             &
                            soln%eqn_set,                                    &
                            soln%q_dof(:,i), soln%amut(i),                   &
                            soln%gradx(:,i), soln%grady(:,i),                &
                            soln%gradz(:,i), soln%turb(:,i))
        end if

        gather_output_data : do n = 1,n_output_variables

          if ( soln%eqn_set == generic_gas ) then
            if (n_species > 1) then
              nspecies: do ns = 1,n_species
                species_name = 'rho_'//trim(spec_propv(ns)%species)
                if (output_variables(n) == species_name) then
                  output_data(n,i) = rho_i(ns)
                end if
              end do nspecies
            end if
          end if

          select case(trim(adjustl(output_variables(n))))

!           note: x,y,z, iblank, imesh and res_gcl stored off above

            case('x')
            case('y')
            case('z')
            case('iblank')
            case('imesh')
            case('res_gcl')

            case('rho')
              output_data(n,i) = rho

            case('u')
              output_data(n,i) = u

            case('v')
              output_data(n,i) = v

            case('w')
              output_data(n,i) = w

            case('p')
              output_data(n,i) = p

            case('lambda1')
              output_data(n,i) = lambda(1)

            case('lambda2')
              output_data(n,i) = lambda(2)

            case('lambda3')
              output_data(n,i) = lambda(3)

            case('lambda4')
              output_data(n,i) = lambda(4)

            case('lambda5')
              output_data(n,i) = lambda(5)

            case('lambda6')
              output_data(n,i) = lambda(6)

            case('lambda7')
              output_data(n,i) = lambda(7)

            case('tt')
              output_data(n,i) = tt

            case('tv')
              output_data(n,i) = tv

            case('turb_ke')
              output_data(n,i) = turb_ke

            case('turb_diss')
              output_data(n,i) = turb_diss

            case('etot')
              output_data(n,i) = etot

            case('htot')
              output_data(n,i) = htot

            case('ev')
              output_data(n,i) = ev

            case('mixture_mol_weight')
              output_data(n,i) = mol_wt

            case('sonic')
              output_data(n,i) = sonic

            case('mu')
              output_data(n,i) = mu

            case('rho-error')
              output_data(n,i) = rho_error

            case('u-error')
              output_data(n,i) = u_error

            case('v-error')
              output_data(n,i) = v_error

            case('w-error')
              output_data(n,i) = w_error

            case('p-error')
              output_data(n,i) = p_error

            case('cp')
              output_data(n,i) = cp

            case('dp_pinf')
              output_data(n,i) = (p-p_inf)/p_inf

            case('vort_x')
              output_data(n,i) = vort_x

            case('vort_y')
              output_data(n,i) = vort_y

            case('vort_z')
              output_data(n,i) = vort_z

            case('vort_mag')
              output_data(n,i) = vort_mag

            case('div_vel')
              output_data(n,i) = div_vel

            case('q_criterion')
              output_data(n,i) = qcriterion

            case('mach')
              output_data(n,i) = mach

            case('temperature')
              output_data(n,i) = temperature

            case('entropy')
              output_data(n,i) = entropy

            case('mu_t')
              output_data(n,i) = mu_t

            case ('uuprime')
              output_data(n,i) = turbfluctuations(1)

            case ('vvprime')
              output_data(n,i) = turbfluctuations(2)

            case ('wwprime')
              output_data(n,i) = turbfluctuations(3)

            case ('uvprime')
              output_data(n,i) = turbfluctuations(4)

            case ('uwprime')
              output_data(n,i) = turbfluctuations(5)

            case ('vwprime')
              output_data(n,i) = turbfluctuations(6)

            case ('slen')
              output_data(n,i) = slen

            case ('turb1')
              output_data(n,i) = turb1

            case ('turb2')
              output_data(n,i) = turb2

            case ('turb3')
              output_data(n,i) = turb3

            case ('turb4')
              output_data(n,i) = turb4

            case ('turb5')
              output_data(n,i) = turb5

            case ('turb6')
              output_data(n,i) = turb6

            case ('turb7')
              output_data(n,i) = turb7

            case ('volume')
              output_data(n,i) = vol

            case ('res1')
              output_data(n,i) = res1

            case ('res2')
              output_data(n,i) = res2

            case ('res3')
              output_data(n,i) = res3

            case ('res4')
              output_data(n,i) = res4

            case ('res5')
              output_data(n,i) = res5

            case ('turres1')
              output_data(n,i) = turres1

            case ('turres2')
              output_data(n,i) = turres2

            case ('turres3')
              output_data(n,i) = turres3

            case ('turres4')
              output_data(n,i) = turres4

            case ('turres5')
              output_data(n,i) = turres5

            case ('turres6')
              output_data(n,i) = turres6

            case ('turres7')
              output_data(n,i) = turres7

            case ('res1_vol')
              output_data(n,i) = res1/vol

            case ('res2_vol')
              output_data(n,i) = res2/vol

            case ('res3_vol')
              output_data(n,i) = res3/vol

            case ('res4_vol')
              output_data(n,i) = res4/vol

            case ('res5_vol')
              output_data(n,i) = res5/vol

            case ('turres1_vol')
              output_data(n,i) = turres1/vol

            case ('turres2_vol')
              output_data(n,i) = turres2/vol

            case ('turres3_vol')
              output_data(n,i) = turres3/vol

            case ('turres4_vol')
              output_data(n,i) = turres4/vol

            case ('turres5_vol')
              output_data(n,i) = turres5/vol

            case ('turres6_vol')
              output_data(n,i) = turres6/vol

            case ('turres7_vol')
              output_data(n,i) = turres7/vol

            case ('rho_tavg')
              output_data(n,i) = rho_tavg

            case ('u_tavg')
              output_data(n,i) = u_tavg

            case ('v_tavg')
              output_data(n,i) = v_tavg

            case ('w_tavg')
              output_data(n,i) = w_tavg

            case ('p_tavg')
              output_data(n,i) = p_tavg

            case ('rho_trms')
              output_data(n,i) = rho_trms

            case ('u_trms')
              output_data(n,i) = u_trms

            case ('v_trms')
              output_data(n,i) = v_trms

            case ('w_trms')
              output_data(n,i) = w_trms

            case ('p_trms')
              output_data(n,i) = p_trms

            case('cgamma')
              output_data(n,i) = cgamma

            case('iflagslen')
              output_data(n,i) = iflagslen

            case('aspect_ratio')
              output_data(n,i) = aspect_ratio

            case('skip_q')
              output_data(n,i) = skip_q

            case('spectral_local')
              output_data(n,i) = spectral_rate_local

            case('hrles_blend')
              output_data(n,i) = hrlesblend

            case('reconstruction_limiter_phi1')
              output_data(n,i) = soln%phi(1,i)
            case('reconstruction_limiter_phi2')
              output_data(n,i) = soln%phi(2,i)
            case('reconstruction_limiter_phi3')
              output_data(n,i) = soln%phi(3,i)
            case('reconstruction_limiter_phi4')
              output_data(n,i) = soln%phi(4,i)
            case('reconstruction_limiter_phi5')
              output_data(n,i) = soln%phi(5,i)

            case('processor_id')
              output_data(n,i) = lmpi_id

            case default

          end select

          if ( i == 1 .and. output_data(n,1) == -huge(1.0_dp)) then
            ierr = 1
            write(err_msg,*) 'write_vol_tec: unknown variable: '  //           &
                              trim(adjustl(output_variables(n)))
            exit nodes_or_cells
          end if

        end do gather_output_data

      end do nodes_or_cells

      call lmpi_conditional_stop(ierr, err_msg)

!     set the file name

      write(proc_id,"(i0)") lmpi_id+1
      if (append_timestep) then
        if ( adjoint_mode .and. itime /= 0 ) then
          step_int = physical_timestep
          write(step,"(i0)")    step_int
          step = '_timestep' // trim(adjustl(step))
        else
          step_int = time_step + prior_iters
          write(step,"(i0)")    step_int
          step = '_timestep' // trim(adjustl(step))
        endif
      else
        step = ''
      end if

      if (itime == 0) then
        time_val = real(time_step+prior_iters, dp)
      else
        time_val = simulation_time
      end if


      non_tecplot_export : if ( export_to == 'native' ) then

        call native_volume_data_write( grid, n_output_variables,               &
                                       output_variables(1:n_output_variables), &
                                       output_data_size, output_data, step_int )

      else if ( export_to /= 'tecplot' ) then ! non_tecplot_export

        if ( export_to == 'fvuns' ) then
          do k = 1, n_output_variables
            if ( output_variables(k) == 'iblank' ) then
              if ( lmpi_master ) then
                write(*,*)'Name mangled output variable from iblank to iblankfv'
              endif
              output_variables(k)='iblankfv'
            endif
          end do
        endif

        filename = trim(adjustl(grid%project)) // "_volume" // &
                   trim(adjustl(step))
        call global_image_export_to( export_to, filename, grid,       &
          n_output_variables, output_variables(1:n_output_variables), &
          output_data_size, output_data )

        if ( export_to == 'fvuns' ) then
          do k = 1, n_output_variables
            if ( output_variables(k) == 'iblankfv' ) then
              if ( lmpi_master ) then
                write(*,*)'Name mangled output variable from iblankfv to iblank'
              endif
              output_variables(k)='iblank'
            endif
          end do
        endif

      else ! tecplot_export

        restart_file = trim(adjustl(grid%project)) // '_part'
        call get_tec_file_extension(tec_file_extension)
        filename = trim(restart_file) // trim(adjustl(proc_id)) //             &
                   "_tec_volume" // trim(adjustl(step)) //                     &
                   "." // trim(adjustl(tec_file_extension))

        file_title = 'tecplot geometry and solution file'

        write(zone_title,*) '"processor ' // trim(adjustl(proc_id)) // '"'

        variable_list = ''
        do n=1,n_output_variables
          variable_list = trim(adjustl(variable_list)) // ' ' //               &
                          trim(adjustl(output_variables(n))) // ' '
        end do

!       output the data in either ascii or binary form

        if (lmpi_master) then
          write(*,'(1x,2a)') 'Writing volume output: ',                        &
            trim(restart_file)//'*'//"_tec_volume"//trim(adjustl(step))//'.'// &
            trim(adjustl(tec_file_extension))
          if ( adjoint_mode .and. itime /= 0 ) then
            write(*,'(2x,3(a,i0))')                                            &
                       'Time step: ', physical_timestep,                       &
              ', Prior iterations: ', prior_iters
          else
            write(*,'(2x,3(a,i0))')                                            &
                       'Time step: ', time_step,                               &
                           ', ntt: ', ntt,                                     &
              ', Prior iterations: ', prior_iters
          end if
        end if

        call write_volume_tec(n_output_variables, grid%nnodes01, totalcells,   &
                            output_data, file_title, zone_title, variable_list,&
                            filename, grid%nelem, grid%elem, tets_only,        &
                            valuelocation, time_val)

      end if non_tecplot_export

    end if have_memory_for_output

    if (lmpi_master .and. allocation_failures > 0) then
      write(*,*)
      write(*,'(a,i0,2a)') " WARNING: ", allocation_failures, " processors",   &
                           " could not allocate memory for volumetric data"
      write(*,'(a)') " ...these processors will NOT write volumetric data"
    end if

    if ( aspect_ratio_needed ) deallocate(ar)

  end subroutine write_vol_tec


!======================== native_volume_data_write ===========================80
!
! image to native
!
!=============================================================================80
  subroutine native_volume_data_write( grid, n_output_variables,               &
                                       output_variables, output_data_size,     &
                                       output_data, step )

    use kinddefs,          only : dp
    use grid_types,        only : grid_type
    use file_utils,        only : available_unit
    use versions,          only : scm_id
#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
    use lmpi,              only : lmpi_master, lmpi_id, lmpi_die
    use string_utils,      only : int_to_s
#else
    use lmpi,              only : lmpi_master, lmpi_die
#endif

    integer, intent(in) :: n_output_variables, output_data_size, step

    real(dp), dimension(n_output_variables,output_data_size),                  &
                                                       intent(in) :: output_data

    character(80), dimension(n_output_variables), intent(in) :: output_variables

    type(grid_type), intent(in) :: grid

    integer :: i, j, scm_id_length

    integer, parameter :: magic_value = 305419896 ! hex(12345678): 4 bytes that
                                                  ! will read as 2018915346 if
                                                  ! file is wrong endian

    integer, dimension(n_output_variables) :: length

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
    integer, asynchronous :: step_a
#else
    integer :: step_a
#endif

    logical :: native_file_exists
    logical, save :: first_time = .true.

    character(80), dimension(n_output_variables) :: string

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
    character(len=200) :: filename
#endif

  continue

! Initialization

    initialize : if ( first_time ) then

      if ( lmpi_master ) then
        write(*,*) 'Opening asynchronous native volume data files...'
      endif

! Allocate the asynchronous variable to store the big data

      if ( grid%nnodes0 > output_data_size ) then
        if ( lmpi_master ) then
          write(*,*) 'Error: nnodes0 > output_data_size in'
          write(*,*) 'native_volume_data_write.'
        endif
        call lmpi_die
        stop
      endif

      allocate(output_data_a(n_output_variables,grid%nnodes0))

! Grab an available unit for the big data and open it

      native_unit = available_unit()

      native_file_exists = .false.

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
      filename = trim(grid%project)//'_volume_data.'//trim(int_to_s(lmpi_id+1))

      inquire(file=trim(filename),exist=native_file_exists)

      if ( native_file_exists ) then
        open(native_unit,file=trim(filename),form='unformatted',               &
             access='stream',asynchronous='yes',position='append')
      else
        open(native_unit,file=trim(filename),form='unformatted',               &
             access='stream',asynchronous='yes')
      endif
#else
      if ( lmpi_master ) then
        write(*,*) 'Compiler does not support asynchronous I/O.'
        write(*,*) 'Cannot use native volume data file format.'
      endif
      call lmpi_die
      stop
#endif

      if ( .not. native_file_exists ) then

        rewind(native_unit)

! write length of revision string and revision string

        scm_id_length = len(trim(scm_id))

        write(native_unit) magic_value, scm_id_length, trim(scm_id)

        do j = 1, n_output_variables
          string(j) = trim(output_variables(j))
          length(j) = len(trim(string(j)))
        end do

        write(native_unit) grid%nnodes0, n_output_variables
        do j = 1, n_output_variables
          write(native_unit) length(j),trim(string(j))
        end do
        write(native_unit) grid%l2g(1:grid%nnodes0)

      endif

      first_time = .false.

    endif initialize

! Wait on any previous IO to complete

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
    wait(native_unit)
#endif

! queue up the data

    step_a = step

    do j = 1, n_output_variables
      do i = 1, grid%nnodes0
        output_data_a(j,i) = output_data(j,i)
      end do
    end do

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
    write(native_unit,asynchronous='yes') step_a
    write(native_unit,asynchronous='yes') output_data_a
#else
    if ( .false. .and. step_a < 0 ) write(*,*)
#endif

  end subroutine native_volume_data_write


!======================== end_native_data_volume_write =======================80
!
! finish up image to native
!
!=============================================================================80
  subroutine end_native_volume_data_write()

    use lmpi, only : lmpi_master

  continue

    if ( native_unit > 0 ) then
      if ( lmpi_master ) then
        write(*,*) 'Closing asynchronous native volume data files...'
      endif
      close(native_unit)
    endif

  end subroutine end_native_volume_data_write

  include 'hrles_blend.f90'

end module solution_writes

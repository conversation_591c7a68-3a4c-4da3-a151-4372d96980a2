!=========================== PSWITCH =========================================80
!
!  Given pressure field and its gradient, computes a function that varies
!  from 0 to 1
!
!=============================================================================80

  pure function pswitch(x1, y1, z1, x2, y2, z2, p1, p2,                        &
                        gradpx1, gradpy1, gradpz1,                             &
                        gradpx2, gradpy2, gradpz2,                             &
                        shft, gradcc, laplcc, power,                           &
                        caller)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_1, my_half

    use info_depr,       only : pr_limiter_coeff

    character(len=4), intent(in) :: caller
    real(dp),         intent(in) :: x1, y1, z1, x2, y2, z2, p1, p2
    real(dp),         intent(in) :: gradpx1, gradpy1, gradpz1,                 &
                                    gradpx2, gradpy2, gradpz2
    real(dp),         intent(in) :: shft, gradcc, laplcc, power
    real(dp)                     :: pswitch

    real(dp) :: gradpl, gradpr, pgrad
    real(dp) :: prato, rx1, ry1, rz1, rx2, ry2, rz2,                           &
                dpx2, dpy2, dpz2, plapc, gradc, laplc

  continue

    if (caller == 'flux') then
      gradc = pr_limiter_coeff**2*gradcc
      laplc = pr_limiter_coeff*laplcc
    else
      gradc = gradcc
      laplc = laplcc
    end if

!   Normalized undivided pressure ratio

    prato  = abs(p2 - p1)

!   Construct the direction vector from nodes 1 and 2 to the interface

    rx1  = my_half*(x1+x2) - x1
    ry1  = my_half*(y1+y2) - y1
    rz1  = my_half*(z1+z2) - z1
    rx2  = my_half*(x1+x2) - x2
    ry2  = my_half*(y1+y2) - y2
    rz2  = my_half*(z1+z2) - z2

!   Construct the normalized magnitude of the undivided pressure gradient

    gradpl = sqrt(gradpx1**2+gradpy1**2+gradpz1**2) *                          &
             sqrt(rx1*rx1+ry1*ry1+rz1*rz1)

    gradpr = sqrt(gradpx2**2+gradpy2**2+gradpz2**2) *                          &
             sqrt(rx2*rx2+ry2*ry2+rz2*rz2)

    pgrad  = gradc*max(gradpl, gradpr, prato)

!   Construct the normalized magnitude of the undivided pressure Laplacian

    dpx2 = gradpx2*rx2 - gradpx1*rx1
    dpy2 = gradpy2*ry2 - gradpy1*ry1
    dpz2 = gradpz2*rz2 - gradpz1*rz1

    plapc = laplc*sqrt(dpx2*dpx2+dpy2*dpy2+dpz2*dpz2)

    pswitch = sqrt(pgrad**2 + plapc**2) / min(p1, p2)

    pswitch = (my_1-tanh(max(my_0, pswitch-shft)))**power

    pswitch = max(my_0, min(my_1, pswitch))

  end function pswitch

!
! DO NOT EDIT this file.  It was generated by the ruby script,
!
! Instead,
! 1. Edit line_jacobi_solver.template
! 2. Regenerate this file through the command below:
!               ruby LinAlg.rb line_jacobi_solver
! 3. Commit line_jacobi_solver.f90 and line_jacobi_solver.template
!
module line_jacobi_solver

  use kinddefs, only : dp, jp, dqp

  implicit none

  private

  public :: line_jacobi_ddq

contains

!================================ LINE_JACOBI_DDQ ============================80
!
! Routes the code into the appropriate line solve routine
!
!=============================================================================80
  subroutine line_jacobi_ddq( solve_backwards, nb, dq_dim, nr,                 &
                         neqmax, neq0,                                         &
                         n_lines, n_line_neq0, first_neq0, line_neq0,          &
                         res, dq, a, b, c,                                     &
                         lines, lu_offset,                                     &
                         g2m, relax_factor, force_general_path)

    integer, intent(in) :: nb, dq_dim, nr
    integer, intent(in) :: neq0, neqmax
    integer, intent(in) :: n_lines
    integer, intent(in) :: n_line_neq0

    integer, intent(in), optional :: force_general_path

    integer, dimension(n_lines+1),   intent(in) :: first_neq0
    integer, dimension(n_line_neq0), intent(in) :: line_neq0
    integer, dimension(:),           intent(in) :: g2m
    integer, dimension(n_lines),     intent(in) :: lines, lu_offset

    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c

    real(dp), intent(in) :: relax_factor

    integer, intent(in) :: solve_backwards

  continue

    if(.not. present(force_general_path)) then

      select case (nb)
      case(6)
        call line_solve_6(dq_dim, nr,  solve_backwards, neq0,                  &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        res, dq, a, b, c,                                      &
                        neqmax,nb,lines, lu_offset, g2m, relax_factor )
      case(5)
        call line_solve_5(dq_dim, nr,  solve_backwards, neq0,                  &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        res, dq, a, b, c,                                      &
                        neqmax,nb,lines, lu_offset, g2m, relax_factor )
      case(4)
        call line_solve_4(dq_dim, nr,  solve_backwards, neq0,                  &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        res, dq, a, b, c,                                      &
                        neqmax,nb,lines, lu_offset, g2m, relax_factor )
      case(3)
        call line_solve_3(dq_dim, nr,  solve_backwards, neq0,                  &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        res, dq, a, b, c,                                      &
                        neqmax,nb,lines, lu_offset, g2m, relax_factor )
      case(2)
        call line_solve_2(dq_dim, nr,  solve_backwards, neq0,                  &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        res, dq, a, b, c,                                      &
                        neqmax,nb,lines, lu_offset, g2m, relax_factor )
      case(1)
        call line_solve_1(dq_dim, nr,  solve_backwards, neq0,                  &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        res, dq, a, b, c,                                      &
                        neqmax,nb,lines, lu_offset, g2m, relax_factor )
      case default

        call line_solve_n(dq_dim, nr,  solve_backwards, neq0,                  &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        res, dq, a, b, c,                                      &
                        neqmax,nb,lines, lu_offset, g2m, relax_factor )

      end select

    else

      if(force_general_path == 1) then

!       General block line solve

        call line_solve_n(dq_dim, nr,  solve_backwards, neq0,                  &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        res, dq, a, b, c,                                      &
                        neqmax,nb,lines, lu_offset, g2m, relax_factor )
      endif

    endif

  end subroutine line_jacobi_ddq


!================================ LINE_SOLVE_N ===============================80
!
! Performs direct line solve on block tridiagonal nxn
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D.
!
!=============================================================================80
  subroutine line_solve_n(dq_dim, nr, solve_backwards, neq0,                   &
                           n_lines,n_line_neq0, first_neq0, line_neq0,         &
                           res, dq, a, b, c,                                   &
                           neqmax,nb,lines, lu_offset,                         &
                           g2m, relax_factor )

    use lmpi_app,       only : lmpi_xfer
    use fun3d_maximums, only : max_dof_in_line

    integer, intent(in) :: dq_dim, nr, neq0
    integer, intent(in) :: n_lines, n_line_neq0, neqmax
    integer, intent(in) :: nb

    real(dp), intent(in) :: relax_factor

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(:),                intent(in) :: g2m

    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c

    integer, intent(in) :: solve_backwards

    integer :: i, j, line, equation, bottom_location
    integer :: top_location, local_eqn
    integer :: il, iu, m, l, offset
    integer :: nb1, l1, l0, m1, m0, mm, mm1, mequation, n

    real(jp) :: change_sign
    real(jp) :: apv

    real(jp), dimension(nb,max_dof_in_line) :: f

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    nb1 = nb - 1

    do n = 1,n_lines

      line = lines(n)

      offset = lu_offset(line) ! Offset the indices into a,b,c

      bottom_location = first_neq0(line)
      top_location    = first_neq0(line+1) - 1

      local_eqn = 0

      loop_within_line : do j = bottom_location, top_location

        local_eqn = local_eqn + 1

        equation = line_neq0(j)
        mequation = g2m(equation)

        ! Initialize the local right hand side with the residual vector

        f(1:nb,local_eqn) = change_sign * res(1:nb,mequation)

      end do loop_within_line

      il = 1
      iu = local_eqn

      if( iu>il )then
        do m = il,iu-1
          m1 = m + 1
          l = m + offset
          l1 = l + 1
          do mm = 1,nb1
            mm1 = mm + 1
            apv = 1._jp / b(mm,mm,l)
            do i = mm1,nb
              f(i,m) = f(i,m) - f(mm,m)*b(i,mm,l)*apv
            end do
            do i = 1,nb
              f(i,m1) = f(i,m1) - f(mm,m)*a(i,mm,l1)*apv
            end do
          end do
          apv = 1._jp / b(nb,nb,l)
          mm = nb
          do i = 1,nb
            f(i,m1) = f(i,m1) - f(mm,m)*a(i,mm,l1)*apv
          end do
        end do
      end if

      m  = iu
      m0 = m - 1
      l  = iu + offset
      l0 = l - 1

      do mm = 1,nb1
        mm1 = mm + 1
        apv = 1._jp / b(mm,mm,l)
        do i = mm1,nb
          f(i,m) = f(i,m) - f(mm,m)*b(i,mm,l)*apv
        end do
      end do

      f(nb,m) = f(nb,m) / b(nb,nb,l)

      if( iu>il ) then
        do mm = nb1,1,-1
          mm1 = mm + 1
          do i = mm,1,-1
            f(i,m) = f(i,m) - b(i,mm1,l)*f(mm1,m)
          end do
          do i = nb,1,-1
            f(i,m0) = f(i,m0) - c(i,mm1,l0)*f(mm1,m)
          end do
          f(mm,m) = f(mm,m) / b(mm,mm,l)
        end do
        do m = iu-1,il+1,-1
          m0 = m - 1
          m1 = m + 1
          l  = m + offset
          l0 = l - 1
          l1 = l + 1
          mm = nb
          do i = nb,1,-1
            f(i,m) = f(i,m) - c(i,1,l)*f(1,m1)
          end do
          f(mm,m) = f(mm,m) / b(mm,mm,l)
          do mm = nb1,1,-1
            mm1 = mm + 1
            do i = mm,1,-1
              f(i,m) = f(i,m) - b(i,mm1,l)*f(mm1,m)
            end do
            do i = nb,1,-1
              f(i,m0) = f(i,m0) - c(i,mm1,l0)*f(mm1,m)
            end do
            f(mm,m) = f(mm,m) / b(mm,mm,l)
          end do
        end do
        m  = il
        l  = m + offset
        m1 = m + 1
        l1 = l + 1
        mm = nb
        do i = nb,1,-1
          f(i,m) = f(i,m) - c(i,1,l)*f(1,m1)
        end do
      else
        mm = nb
        l  = il + offset
        m  = 1
      end if

      f(mm,m) = f(mm,m) / b(mm,mm,l)

      do mm = nb1,1,-1
        mm1 = mm + 1
        do i = mm,1,-1
          f(i,m) = f(i,m) - b(i,mm1,l)*f(mm1,m)
        end do
        f(mm,m) = f(mm,m) / b(mm,mm,l)
      end do

      ! Update delta-q with delta-delta-q.

      local_eqn = 0

      fill_solution : do j = bottom_location, top_location
        local_eqn = local_eqn + 1
        equation = line_neq0(j)
        mequation = g2m(equation)
        dq(1:nb,mequation) = dq(1:nb,mequation)           &
                           + f(1:nb,local_eqn)*relax_factor
      end do fill_solution

      offset = offset + local_eqn

    end do

    call lmpi_xfer(dq)

  end subroutine line_solve_n

! no comment
!================================ LINE_SOLVE_1 ===============================80
!
! Performs direct line solve on block tridiagonal 1x1
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_1(dq_dim, nr, solve_backwards, neq0,                   &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          res, dq, a, b, c,                                    &
                          neqmax,nb,lines, lu_offset, g2m, relax_factor )

    use lmpi_app,       only : lmpi_xfer
    use fun3d_maximums, only : max_dof_in_line

    integer, intent(in) :: dq_dim, nr, neq0
    integer, intent(in) :: n_lines, n_line_neq0, neqmax
    integer, intent(in) :: nb

    real(dp), intent(in) :: relax_factor

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(:),                intent(in) :: g2m

    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c

    integer, intent(in) :: solve_backwards

    integer :: j, line, equation, bottom_location
    integer :: top_location, local_eqn, row, il, iu, il1, iqq
    integer :: ir, it, is, offset, mequation, n

    real(jp) :: change_sign

    real(jp), dimension(nb,max_dof_in_line) :: f

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    do n = 1,n_lines

      line = lines(n)

      offset = lu_offset(line) ! Offset the indices into a,b,c

      bottom_location = first_neq0(line)
      top_location    = first_neq0(line+1) - 1

      local_eqn = 0

      loop_within_line : do j = bottom_location, top_location

        local_eqn = local_eqn + 1

        equation = line_neq0(j)
        mequation = g2m(equation)

        ! Initialize the local right hand side.

        do row = 1, 1
          f(row,local_eqn) = change_sign * res(row,mequation)
        end do

      end do loop_within_line

      il = 1
      iu = local_eqn

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 1
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 1
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)
        end do
      end do backward

      ! Update delta-q with delta-delta-q.

      local_eqn = 0

      fill_solution : do j = bottom_location, top_location
        local_eqn = local_eqn + 1
        equation = line_neq0(j)
        mequation = g2m(equation)
        do row = 1, 1
          dq(row,mequation) = dq(row,mequation)            &
                            + f(row,local_eqn)*relax_factor
        end do
      end do fill_solution

      offset = offset + local_eqn

    end do

    call lmpi_xfer(dq)

  end subroutine line_solve_1

! no comment
!================================ LINE_SOLVE_2 ===============================80
!
! Performs direct line solve on block tridiagonal 2x2
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_2(dq_dim, nr, solve_backwards, neq0,                   &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          res, dq, a, b, c,                                    &
                          neqmax,nb,lines, lu_offset, g2m, relax_factor )

    use lmpi_app,       only : lmpi_xfer
    use fun3d_maximums, only : max_dof_in_line

    integer, intent(in) :: dq_dim, nr, neq0
    integer, intent(in) :: n_lines, n_line_neq0, neqmax
    integer, intent(in) :: nb

    real(dp), intent(in) :: relax_factor

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(:),                intent(in) :: g2m

    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c

    integer, intent(in) :: solve_backwards

    integer :: j, line, equation, bottom_location
    integer :: top_location, local_eqn, row, il, iu, il1, iqq
    integer :: ir, it, is, offset, mequation, n

    real(jp) :: change_sign

    real(jp), dimension(nb,max_dof_in_line) :: f

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    do n = 1,n_lines

      line = lines(n)

      offset = lu_offset(line) ! Offset the indices into a,b,c

      bottom_location = first_neq0(line)
      top_location    = first_neq0(line+1) - 1

      local_eqn = 0

      loop_within_line : do j = bottom_location, top_location

        local_eqn = local_eqn + 1

        equation = line_neq0(j)
        mequation = g2m(equation)

        ! Initialize the local right hand side.

        do row = 1, 2
          f(row,local_eqn) = change_sign * res(row,mequation)
        end do

      end do loop_within_line

      il = 1
      iu = local_eqn

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))
      f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
      f(1,is) = f(1,is)                                                    &
                        - b(1,2,offset+is)*f(2,is)

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 2
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)                     &
                          - a(row,2,offset+is)*f(2,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))
        f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
        f(1,is) = f(1,is)                                                  &
                          - b(1,2,offset+is)*f(2,is)

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 2
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)                   &
                            - c(row,2,offset+is)*f(2,it)
        end do
      end do backward

      ! Update delta-q with delta-delta-q.

      local_eqn = 0

      fill_solution : do j = bottom_location, top_location
        local_eqn = local_eqn + 1
        equation = line_neq0(j)
        mequation = g2m(equation)
        do row = 1, 2
          dq(row,mequation) = dq(row,mequation)            &
                            + f(row,local_eqn)*relax_factor
        end do
      end do fill_solution

      offset = offset + local_eqn

    end do

    call lmpi_xfer(dq)

  end subroutine line_solve_2

! no comment
!================================ LINE_SOLVE_3 ===============================80
!
! Performs direct line solve on block tridiagonal 3x3
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_3(dq_dim, nr, solve_backwards, neq0,                   &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          res, dq, a, b, c,                                    &
                          neqmax,nb,lines, lu_offset, g2m, relax_factor )

    use lmpi_app,       only : lmpi_xfer
    use fun3d_maximums, only : max_dof_in_line

    integer, intent(in) :: dq_dim, nr, neq0
    integer, intent(in) :: n_lines, n_line_neq0, neqmax
    integer, intent(in) :: nb

    real(dp), intent(in) :: relax_factor

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(:),                intent(in) :: g2m

    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c

    integer, intent(in) :: solve_backwards

    integer :: j, line, equation, bottom_location
    integer :: top_location, local_eqn, row, il, iu, il1, iqq
    integer :: ir, it, is, offset, mequation, n

    real(jp) :: change_sign

    real(jp), dimension(nb,max_dof_in_line) :: f

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    do n = 1,n_lines

      line = lines(n)

      offset = lu_offset(line) ! Offset the indices into a,b,c

      bottom_location = first_neq0(line)
      top_location    = first_neq0(line+1) - 1

      local_eqn = 0

      loop_within_line : do j = bottom_location, top_location

        local_eqn = local_eqn + 1

        equation = line_neq0(j)
        mequation = g2m(equation)

        ! Initialize the local right hand side.

        do row = 1, 3
          f(row,local_eqn) = change_sign * res(row,mequation)
        end do

      end do loop_within_line

      il = 1
      iu = local_eqn

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))
      f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
      f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                          -b(3,2,offset+is)*f(2,is))
      f(2,is) = f(2,is)                                                    &
                        - b(2,3,offset+is)*f(3,is)
      f(1,is) = f(1,is)                                                    &
                        - b(1,3,offset+is)*f(3,is)                         &
                        - b(1,2,offset+is)*f(2,is)

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 3
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)                     &
                          - a(row,2,offset+is)*f(2,ir)                     &
                          - a(row,3,offset+is)*f(3,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))
        f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
        f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                            -b(3,2,offset+is)*f(2,is))
        f(2,is) = f(2,is)                                                  &
                          - b(2,3,offset+is)*f(3,is)
        f(1,is) = f(1,is)                                                  &
                          - b(1,3,offset+is)*f(3,is)                       &
                          - b(1,2,offset+is)*f(2,is)

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 3
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)                   &
                            - c(row,2,offset+is)*f(2,it)                   &
                            - c(row,3,offset+is)*f(3,it)
        end do
      end do backward

      ! Update delta-q with delta-delta-q.

      local_eqn = 0

      fill_solution : do j = bottom_location, top_location
        local_eqn = local_eqn + 1
        equation = line_neq0(j)
        mequation = g2m(equation)
        do row = 1, 3
          dq(row,mequation) = dq(row,mequation)            &
                            + f(row,local_eqn)*relax_factor
        end do
      end do fill_solution

      offset = offset + local_eqn

    end do

    call lmpi_xfer(dq)

  end subroutine line_solve_3

! no comment
!================================ LINE_SOLVE_4 ===============================80
!
! Performs direct line solve on block tridiagonal 4x4
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_4(dq_dim, nr, solve_backwards, neq0,                   &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          res, dq, a, b, c,                                    &
                          neqmax,nb,lines, lu_offset, g2m, relax_factor )

    use lmpi_app,       only : lmpi_xfer
    use fun3d_maximums, only : max_dof_in_line

    integer, intent(in) :: dq_dim, nr, neq0
    integer, intent(in) :: n_lines, n_line_neq0, neqmax
    integer, intent(in) :: nb

    real(dp), intent(in) :: relax_factor

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(:),                intent(in) :: g2m

    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c

    integer, intent(in) :: solve_backwards

    integer :: j, line, equation, bottom_location
    integer :: top_location, local_eqn, row, il, iu, il1, iqq
    integer :: ir, it, is, offset, mequation, n

    real(jp) :: change_sign

    real(jp), dimension(nb,max_dof_in_line) :: f

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    do n = 1,n_lines

      line = lines(n)

      offset = lu_offset(line) ! Offset the indices into a,b,c

      bottom_location = first_neq0(line)
      top_location    = first_neq0(line+1) - 1

      local_eqn = 0

      loop_within_line : do j = bottom_location, top_location

        local_eqn = local_eqn + 1

        equation = line_neq0(j)
        mequation = g2m(equation)

        ! Initialize the local right hand side.

        do row = 1, 4
          f(row,local_eqn) = change_sign * res(row,mequation)
        end do

      end do loop_within_line

      il = 1
      iu = local_eqn

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))
      f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
      f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                          -b(3,2,offset+is)*f(2,is))
      f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)        &
                                          -b(4,2,offset+is)*f(2,is)        &
                                          -b(4,3,offset+is)*f(3,is))
      f(3,is) = f(3,is)                                                    &
                        - b(3,4,offset+is)*f(4,is)
      f(2,is) = f(2,is)                                                    &
                        - b(2,4,offset+is)*f(4,is)                         &
                        - b(2,3,offset+is)*f(3,is)
      f(1,is) = f(1,is)                                                    &
                        - b(1,4,offset+is)*f(4,is)                         &
                        - b(1,3,offset+is)*f(3,is)                         &
                        - b(1,2,offset+is)*f(2,is)

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 4
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)                     &
                          - a(row,2,offset+is)*f(2,ir)                     &
                          - a(row,3,offset+is)*f(3,ir)                     &
                          - a(row,4,offset+is)*f(4,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))
        f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
        f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                            -b(3,2,offset+is)*f(2,is))
        f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)      &
                                            -b(4,2,offset+is)*f(2,is)      &
                                            -b(4,3,offset+is)*f(3,is))
        f(3,is) = f(3,is)                                                  &
                          - b(3,4,offset+is)*f(4,is)
        f(2,is) = f(2,is)                                                  &
                          - b(2,4,offset+is)*f(4,is)                       &
                          - b(2,3,offset+is)*f(3,is)
        f(1,is) = f(1,is)                                                  &
                          - b(1,4,offset+is)*f(4,is)                       &
                          - b(1,3,offset+is)*f(3,is)                       &
                          - b(1,2,offset+is)*f(2,is)

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 4
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)                   &
                            - c(row,2,offset+is)*f(2,it)                   &
                            - c(row,3,offset+is)*f(3,it)                   &
                            - c(row,4,offset+is)*f(4,it)
        end do
      end do backward

      ! Update delta-q with delta-delta-q.

      local_eqn = 0

      fill_solution : do j = bottom_location, top_location
        local_eqn = local_eqn + 1
        equation = line_neq0(j)
        mequation = g2m(equation)
        do row = 1, 4
          dq(row,mequation) = dq(row,mequation)            &
                            + f(row,local_eqn)*relax_factor
        end do
      end do fill_solution

      offset = offset + local_eqn

    end do

    call lmpi_xfer(dq)

  end subroutine line_solve_4

! no comment
!================================ LINE_SOLVE_5 ===============================80
!
! Performs direct line solve on block tridiagonal 5x5
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_5(dq_dim, nr, solve_backwards, neq0,                   &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          res, dq, a, b, c,                                    &
                          neqmax,nb,lines, lu_offset, g2m, relax_factor )

    use lmpi_app,       only : lmpi_xfer
    use fun3d_maximums, only : max_dof_in_line

    integer, intent(in) :: dq_dim, nr, neq0
    integer, intent(in) :: n_lines, n_line_neq0, neqmax
    integer, intent(in) :: nb

    real(dp), intent(in) :: relax_factor

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(:),                intent(in) :: g2m

    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c

    integer, intent(in) :: solve_backwards

    integer :: j, line, equation, bottom_location
    integer :: top_location, local_eqn, row, il, iu, il1, iqq
    integer :: ir, it, is, offset, mequation, n

    real(jp) :: change_sign

    real(jp), dimension(nb,max_dof_in_line) :: f

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    do n = 1,n_lines

      line = lines(n)

      offset = lu_offset(line) ! Offset the indices into a,b,c

      bottom_location = first_neq0(line)
      top_location    = first_neq0(line+1) - 1

      local_eqn = 0

      loop_within_line : do j = bottom_location, top_location

        local_eqn = local_eqn + 1

        equation = line_neq0(j)
        mequation = g2m(equation)

        ! Initialize the local right hand side.

        do row = 1, 5
          f(row,local_eqn) = change_sign * res(row,mequation)
        end do

      end do loop_within_line

      il = 1
      iu = local_eqn

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))
      f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
      f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                          -b(3,2,offset+is)*f(2,is))
      f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)        &
                                          -b(4,2,offset+is)*f(2,is)        &
                                          -b(4,3,offset+is)*f(3,is))
      f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)        &
                                          -b(5,2,offset+is)*f(2,is)        &
                                          -b(5,3,offset+is)*f(3,is)        &
                                          -b(5,4,offset+is)*f(4,is))
      f(4,is) = f(4,is)                                                    &
                        - b(4,5,offset+is)*f(5,is)
      f(3,is) = f(3,is)                                                    &
                        - b(3,5,offset+is)*f(5,is)                         &
                        - b(3,4,offset+is)*f(4,is)
      f(2,is) = f(2,is)                                                    &
                        - b(2,5,offset+is)*f(5,is)                         &
                        - b(2,4,offset+is)*f(4,is)                         &
                        - b(2,3,offset+is)*f(3,is)
      f(1,is) = f(1,is)                                                    &
                        - b(1,5,offset+is)*f(5,is)                         &
                        - b(1,4,offset+is)*f(4,is)                         &
                        - b(1,3,offset+is)*f(3,is)                         &
                        - b(1,2,offset+is)*f(2,is)

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 5
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)                     &
                          - a(row,2,offset+is)*f(2,ir)                     &
                          - a(row,3,offset+is)*f(3,ir)                     &
                          - a(row,4,offset+is)*f(4,ir)                     &
                          - a(row,5,offset+is)*f(5,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))
        f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
        f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                            -b(3,2,offset+is)*f(2,is))
        f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)      &
                                            -b(4,2,offset+is)*f(2,is)      &
                                            -b(4,3,offset+is)*f(3,is))
        f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)      &
                                            -b(5,2,offset+is)*f(2,is)      &
                                            -b(5,3,offset+is)*f(3,is)      &
                                            -b(5,4,offset+is)*f(4,is))
        f(4,is) = f(4,is)                                                  &
                          - b(4,5,offset+is)*f(5,is)
        f(3,is) = f(3,is)                                                  &
                          - b(3,5,offset+is)*f(5,is)                       &
                          - b(3,4,offset+is)*f(4,is)
        f(2,is) = f(2,is)                                                  &
                          - b(2,5,offset+is)*f(5,is)                       &
                          - b(2,4,offset+is)*f(4,is)                       &
                          - b(2,3,offset+is)*f(3,is)
        f(1,is) = f(1,is)                                                  &
                          - b(1,5,offset+is)*f(5,is)                       &
                          - b(1,4,offset+is)*f(4,is)                       &
                          - b(1,3,offset+is)*f(3,is)                       &
                          - b(1,2,offset+is)*f(2,is)

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 5
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)                   &
                            - c(row,2,offset+is)*f(2,it)                   &
                            - c(row,3,offset+is)*f(3,it)                   &
                            - c(row,4,offset+is)*f(4,it)                   &
                            - c(row,5,offset+is)*f(5,it)
        end do
      end do backward

      ! Update delta-q with delta-delta-q.

      local_eqn = 0

      fill_solution : do j = bottom_location, top_location
        local_eqn = local_eqn + 1
        equation = line_neq0(j)
        mequation = g2m(equation)
        do row = 1, 5
          dq(row,mequation) = dq(row,mequation)            &
                            + f(row,local_eqn)*relax_factor
        end do
      end do fill_solution

      offset = offset + local_eqn

    end do

    call lmpi_xfer(dq)

  end subroutine line_solve_5

! no comment
!================================ LINE_SOLVE_6 ===============================80
!
! Performs direct line solve on block tridiagonal 6x6
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_6(dq_dim, nr, solve_backwards, neq0,                   &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          res, dq, a, b, c,                                    &
                          neqmax,nb,lines, lu_offset, g2m, relax_factor )

    use lmpi_app,       only : lmpi_xfer
    use fun3d_maximums, only : max_dof_in_line

    integer, intent(in) :: dq_dim, nr, neq0
    integer, intent(in) :: n_lines, n_line_neq0, neqmax
    integer, intent(in) :: nb

    real(dp), intent(in) :: relax_factor

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(:),                intent(in) :: g2m

    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c

    integer, intent(in) :: solve_backwards

    integer :: j, line, equation, bottom_location
    integer :: top_location, local_eqn, row, il, iu, il1, iqq
    integer :: ir, it, is, offset, mequation, n

    real(jp) :: change_sign

    real(jp), dimension(nb,max_dof_in_line) :: f

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    do n = 1,n_lines

      line = lines(n)

      offset = lu_offset(line) ! Offset the indices into a,b,c

      bottom_location = first_neq0(line)
      top_location    = first_neq0(line+1) - 1

      local_eqn = 0

      loop_within_line : do j = bottom_location, top_location

        local_eqn = local_eqn + 1

        equation = line_neq0(j)
        mequation = g2m(equation)

        ! Initialize the local right hand side.

        do row = 1, 6
          f(row,local_eqn) = change_sign * res(row,mequation)
        end do

      end do loop_within_line

      il = 1
      iu = local_eqn

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))
      f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
      f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                          -b(3,2,offset+is)*f(2,is))
      f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)        &
                                          -b(4,2,offset+is)*f(2,is)        &
                                          -b(4,3,offset+is)*f(3,is))
      f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)        &
                                          -b(5,2,offset+is)*f(2,is)        &
                                          -b(5,3,offset+is)*f(3,is)        &
                                          -b(5,4,offset+is)*f(4,is))
      f(6,is) = b(6,6,offset+is)*(f(6,is) -b(6,1,offset+is)*f(1,is)        &
                                          -b(6,2,offset+is)*f(2,is)        &
                                          -b(6,3,offset+is)*f(3,is)        &
                                          -b(6,4,offset+is)*f(4,is)        &
                                          -b(6,5,offset+is)*f(5,is))
      f(5,is) = f(5,is)                                                    &
                         - b(5,6,offset+is)*f(6,is)
      f(4,is) = f(4,is)                                                    &
                        - b(4,6,offset+is)*f(6,is)                         &
                        - b(4,5,offset+is)*f(5,is)
      f(3,is) = f(3,is)                                                    &
                        - b(3,6,offset+is)*f(6,is)                         &
                        - b(3,5,offset+is)*f(5,is)                         &
                        - b(3,4,offset+is)*f(4,is)
      f(2,is) = f(2,is)                                                    &
                        - b(2,6,offset+is)*f(6,is)                         &
                        - b(2,5,offset+is)*f(5,is)                         &
                        - b(2,4,offset+is)*f(4,is)                         &
                        - b(2,3,offset+is)*f(3,is)
      f(1,is) = f(1,is)                                                    &
                        - b(1,6,offset+is)*f(6,is)                         &
                        - b(1,5,offset+is)*f(5,is)                         &
                        - b(1,4,offset+is)*f(4,is)                         &
                        - b(1,3,offset+is)*f(3,is)                         &
                        - b(1,2,offset+is)*f(2,is)

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 6
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)                     &
                          - a(row,2,offset+is)*f(2,ir)                     &
                          - a(row,3,offset+is)*f(3,ir)                     &
                          - a(row,4,offset+is)*f(4,ir)                     &
                          - a(row,5,offset+is)*f(5,ir)                     &
                          - a(row,6,offset+is)*f(6,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))
        f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
        f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                            -b(3,2,offset+is)*f(2,is))
        f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)      &
                                            -b(4,2,offset+is)*f(2,is)      &
                                            -b(4,3,offset+is)*f(3,is))
        f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)      &
                                            -b(5,2,offset+is)*f(2,is)      &
                                            -b(5,3,offset+is)*f(3,is)      &
                                            -b(5,4,offset+is)*f(4,is))
        f(6,is) = b(6,6,offset+is)*(f(6,is) -b(6,1,offset+is)*f(1,is)      &
                                            -b(6,2,offset+is)*f(2,is)      &
                                            -b(6,3,offset+is)*f(3,is)      &
                                            -b(6,4,offset+is)*f(4,is)      &
                                            -b(6,5,offset+is)*f(5,is))
        f(5,is) = f(5,is)                                                  &
                          - b(5,6,offset+is)*f(6,is)
        f(4,is) = f(4,is)                                                  &
                          - b(4,6,offset+is)*f(6,is)                       &
                          - b(4,5,offset+is)*f(5,is)
        f(3,is) = f(3,is)                                                  &
                          - b(3,6,offset+is)*f(6,is)                       &
                          - b(3,5,offset+is)*f(5,is)                       &
                          - b(3,4,offset+is)*f(4,is)
        f(2,is) = f(2,is)                                                  &
                          - b(2,6,offset+is)*f(6,is)                       &
                          - b(2,5,offset+is)*f(5,is)                       &
                          - b(2,4,offset+is)*f(4,is)                       &
                          - b(2,3,offset+is)*f(3,is)
        f(1,is) = f(1,is)                                                  &
                          - b(1,6,offset+is)*f(6,is)                       &
                          - b(1,5,offset+is)*f(5,is)                       &
                          - b(1,4,offset+is)*f(4,is)                       &
                          - b(1,3,offset+is)*f(3,is)                       &
                          - b(1,2,offset+is)*f(2,is)

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 6
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)                   &
                            - c(row,2,offset+is)*f(2,it)                   &
                            - c(row,3,offset+is)*f(3,it)                   &
                            - c(row,4,offset+is)*f(4,it)                   &
                            - c(row,5,offset+is)*f(5,it)                   &
                            - c(row,6,offset+is)*f(6,it)
        end do
      end do backward

      ! Update delta-q with delta-delta-q.

      local_eqn = 0

      fill_solution : do j = bottom_location, top_location
        local_eqn = local_eqn + 1
        equation = line_neq0(j)
        mequation = g2m(equation)
        do row = 1, 6
          dq(row,mequation) = dq(row,mequation)            &
                            + f(row,local_eqn)*relax_factor
        end do
      end do fill_solution

      offset = offset + local_eqn

    end do

    call lmpi_xfer(dq)

  end subroutine line_solve_6

end module line_jacobi_solver

!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite turb_diffusion


  integer, parameter  ::        dp = selected_real_kind(15,307)
  integer, parameter  :: system_i8 = selected_int_kind(10)
  integer, parameter  :: system_i1 = selected_int_kind(2)

  real(dp) :: xmr, tke, omega, nu, betastar, dist
  real(dp), parameter :: tol = 1.0e-8_dp
  integer,  parameter :: traceless = 1

test test_is_model_conservative

  use turbulence_info, only : turbulence_model_is_conservative, &
                              wilcox_kw06, wilcox_kw06p, kw_des,&
                              kw_sst
  integer :: turbulence_model_int
  logical :: actual

  turbulence_model_int = wilcox_kw06
  actual = turbulence_model_is_conservative ( turbulence_model_int )
  assert_true( actual )

  turbulence_model_int = wilcox_kw06p
  actual = turbulence_model_is_conservative ( turbulence_model_int )
  assert_false( actual )

  turbulence_model_int = kw_des
  actual = turbulence_model_is_conservative ( turbulence_model_int )
  assert_false( actual )

  turbulence_model_int = kw_sst
  actual = turbulence_model_is_conservative ( turbulence_model_int )
  assert_false( actual )


end test

test test_get_diffusion_coeffiencts

  use turb_kw_const,  only : sigma_k_w88, sigma_w_w88                       &
                           , sig_k1, sig_w1, sig_k2, sig_w2 
  use turbulence_info, only : kw_sst

  integer :: turbulence_model_int
  integer, parameter :: n_turb = 2
  real(dp), dimension(n_turb) :: diff_const
  real(dp), dimension(n_turb) :: diff_const2

  turbulence_model_int = kw_sst
  call get_diffusion_coefficients ( turbulence_model_int, n_turb &
                                  , diff_const, diff_const2 )

  assert_real_equal( sig_k1,  diff_const(1) ) 
  assert_real_equal( sig_k2,  diff_const2(1) ) 
  assert_real_equal( sig_w1,  diff_const(2) ) 
  assert_real_equal( sig_w2,  diff_const2(2) ) 


end test

test test_turb_resid_diff

  use grid_types,    only : grid_type
! use soln_types,    only : soln_type
  use info_depr,     only : twod, grad_x_y_z_contents, use_edge_gradients
  use grids,         only : nullify_grid, allocate_grid, deallocate_grid
  use bc_types,      only : bcgrid_type, nullify_bc, allocate_dummy_bc      &
                          , deallocate_bc, bcsoln_type, allocate_bcsoln
  use element_types, only : elem_type
  use allocations,   only : my_alloc_ptr, my_realloc_ptr
  use element_defs,  only : nullify_elem, allocate_elem, initialize_elem    &
                          , deallocate_elem
  use element_defs,  only : max_node_per_cell, max_face_per_cell               &
                          , max_edge_per_cell 
  use element_defs,  only : local_f2n_pyr, local_f2e_pyr, local_e2n_pyr
  use element_defs,  only : local_f2n_hex, local_f2e_hex, local_e2n_hex
  use element_defs,  only : chk_norm_hex, chk_norm_pyr

  use fluid,         only : setup_fluid_gamma, setup_sutherland_constant
  use info_depr,     only : xmach, re
  use thermo,        only : q_type, primitive_q_type
  use bc_names,      only : twall
  use turbulence_info, only : model_strain_form_int, turbulence_model_int
  use debug_defs,    only : gradient_construction_rhs
  use nml_boundary_conditions, only : wall_velocity
  use turb_parameters, only : t_diff1, t_diff2
  use grid_metrics,    only : dualmetric
  use turb_util,       only : turbmultieqn_grad, kloc, wloc
  use debug_defs,      only : unified_diffusion, diff_edge_avg_t
  use turbulence_info, only : kw_lag

  integer, parameter                 :: nedgeloc = 16
  integer, parameter                 :: n_grd    = 8
  integer, parameter                 :: nnodes0  = 9
  integer, parameter                 :: nnodes01 = 9
  integer, parameter                 :: n_tot    = 5
  integer, parameter                 :: n_turb   = 3
  integer, parameter                 :: nnz01    = 10

  type(grid_type)                    :: grid
! type(soln_type)                    :: soln
  real(dp),          dimension(n_tot ,nnodes01)  :: q_dof
  real(dp),          dimension(       nnodes01)  :: amut
  real(dp),          dimension(n_turb,nnodes01)  :: turb_res
  real(dp),          dimension(n_turb,nnodes01)  :: turb
  real(dp),          dimension(n_grd ,nnodes01)  :: gradx
  real(dp),          dimension(n_grd ,nnodes01)  :: grady
  real(dp),          dimension(n_grd ,nnodes01)  :: gradz
  real(dp),          dimension(       nnodes01)  :: sst_f1
  real(dp),          dimension(10             )  :: a, b, c, d
  real(dp),          dimension(1              )  :: nu
  real(dp),          dimension(nnodes01+1     )  :: ia
  real(dp),          dimension(nnz01          )  :: ja
  real(dp),          dimension(nnz01          )  :: nzg2m, g2m
  real(dp),          dimension(n_turb         )  :: diff_const
  real(dp),          dimension(n_turb         )  :: diff_const2

! type(bcgrid_type), dimension(1)    :: bc
  type(bcsoln_type), dimension(1)    :: bcsoln
  integer                            :: ib
! integer                            :: ibc
  integer                            :: n_sta
  integer                            :: nodes_per_face
  integer                            :: face_index
  integer                            :: eqn_set

  integer                            :: ielem, icell
  integer                            :: cell
  integer                            :: node1, node2
  integer                            :: njac
  integer                            :: viscous_method
  integer                            :: i, ie, j
  integer                            :: i1, i2, edge

  character(80)                      :: grid_motion
  real(dp), dimension(1,nnodes01)    :: res_gcl


  max_face_per_cell    = 6
  max_node_per_cell    = 8
  max_edge_per_cell    = 12
  eqn_set              = 0
  cell                 = 1
  njac                 = 5
  twod                 = .false.
  turbulence_model_int = kw_lag
  kloc                 = n_grd - n_turb + 1
  wloc                 = n_grd - n_turb + 2

  call setup_fluid_gamma
  call setup_sutherland_constant

  call nullify_grid ( grid )
  grid%nnodes0  = nnodes0
  grid%nnodes01 = nnodes01
  allocate ( grid%x( grid%nnodes01 ) )
  allocate ( grid%y( grid%nnodes01 ) )
  allocate ( grid%z( grid%nnodes01 ) )
  allocate ( grid%bc(1) )
  allocate ( grid%elem(2) )

  call allocate_bcsoln   ( 9, bcsoln(1) )

  call initialize_elem ( grid%elem(1), 'pyr' )
  call initialize_elem ( grid%elem(2), 'hex' )
  call allocate_elem ( grid%elem(2), .false. )

  grid%nelem                 = 2
  grid%nedge                 = nedgeloc
  grid%nedgeloc              = nedgeloc
  grid%nedgeloc_2d           = 0
  grid%elem(1)%edge_per_cell = 8
  grid%elem(2)%edge_per_cell = 12

  allocate ( grid%vol      (   grid%nnodes01 ) ) 
  allocate ( grid%dxdt     (   grid%nnodes01 ) ) 
  allocate ( grid%dydt     (   grid%nnodes01 ) ) 
  allocate ( grid%dzdt     (   grid%nnodes01 ) ) 
  allocate ( grid%facespeed(   grid%nedgeloc ) ) 
  allocate ( grid%eptr     ( 2,grid%nedgeloc ) ) 
  allocate ( grid%xn       (   grid%nedgeloc ) )
  allocate ( grid%yn       (   grid%nedgeloc ) )
  allocate ( grid%zn       (   grid%nedgeloc ) )
  allocate ( grid%ra       (   grid%nedgeloc ) )

  grid%x(1) = 0.0_dp; grid%y(1) = 0.0_dp; grid%z(1) = 0.0_dp
  grid%x(2) = 1.0_dp; grid%y(2) = 0.0_dp; grid%z(2) = 0.0_dp
  grid%x(3) = 1.0_dp; grid%y(3) = 1.0_dp; grid%z(3) = 0.0_dp
  grid%x(4) = 0.0_dp; grid%y(4) = 1.0_dp; grid%z(4) = 0.0_dp
  grid%x(5) = 1.0_dp; grid%y(5) = 0.0_dp; grid%z(5) = 5.0e-2_dp
  grid%x(6) = 1.0_dp; grid%y(6) = 1.0_dp; grid%z(6) = 5.0e-2_dp
  grid%x(7) = 0.0_dp; grid%y(7) = 0.0_dp; grid%z(7) = 5.0e-2_dp
  grid%x(8) = 0.0_dp; grid%y(8) = 1.0_dp; grid%z(8) = 5.0e-2_dp
  grid%x(9) =-0.5_dp; grid%y(9) = 0.5_dp; grid%z(9) = 2.5e-2_dp

  ib                  = 1
  grid%nbound         = 0 ! switch to 1 to test boundary gradients
  grid%bc(ib)%ibc     = 4110 ! viscous wall function boundary
  grid%bc(ib)%nbnode  = 5 ! number of boundary nodes
  grid%bc(ib)%nbfacet = 1
  grid%bc(ib)%nbfaceq = 1

  ! account for one triangle face and one quad face
  allocate ( grid%bc(ib)%ibnode( grid%bc(ib)%nbnode) )
  allocate ( grid%bc(ib)%f2ntb ( grid%bc(ib)%nbfacet, 5 ) )
  allocate ( grid%bc(ib)%f2nqb ( grid%bc(ib)%nbfaceq, 6 ) )
  allocate ( bcsoln(ib)%mu_t_wf ( grid%bc(ib)%nbnode ) )
  allocate ( bcsoln(ib)%omega_wf ( grid%bc(ib)%nbnode ) )
  allocate ( grid%elem(1)%c2n    (  5, 1 ) )
  allocate ( grid%elem(2)%c2n    (  8, 1 ) )
  allocate ( grid%elem(1)%c2e    (  8, 1 ) )
  allocate ( grid%elem(2)%c2e    ( 12, 1 ) )
  allocate ( grid%elem(1)%e2n_2d (  4, 2 ) )
  allocate ( grid%elem(2)%e2n_2d (  4, 2 ) )

  wall_velocity(1,:) = (/0.3_dp,0.2_dp,0.1_dp/)

  face_index     = 1
  nodes_per_face = 3
  grid%bc(ib)%f2ntb(face_index,1) = 1 ! 9 ! face 1, node 1
  grid%bc(ib)%f2ntb(face_index,2) = 5 ! 4 ! face 1, node 2
  grid%bc(ib)%f2ntb(face_index,3) = 4 ! 1 ! face 1, node 3
  grid%bc(ib)%f2ntb(face_index,4) = 1 ! cell number
  grid%bc(ib)%f2ntb(face_index,5) = 1 ! element type
  grid%bc(ib)%nbfacet             = 1 ! number of tri faces

  face_index     = 1
  nodes_per_face = 4
  grid%bc(ib)%f2nqb(face_index,1) = 1 ! 1 ! face 1, node 1
  grid%bc(ib)%f2nqb(face_index,2) = 4 ! 2 ! face 1, node 2
  grid%bc(ib)%f2nqb(face_index,3) = 3 ! 3 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,4) = 2 ! 4 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,5) = 1 ! cell number
  grid%bc(ib)%f2nqb(face_index,6) = 2 ! element type
  grid%bc(ib)%nbfaceq             = 1 ! number of quad faces

  grid%bc(ib)%ibnode(1) = 1 ! boundary node
  grid%bc(ib)%ibnode(2) = 2 ! boundary node
  grid%bc(ib)%ibnode(3) = 3 ! boundary node
  grid%bc(ib)%ibnode(4) = 4 ! boundary node
  grid%bc(ib)%ibnode(5) = 9 ! boundary node

  grid%elem(1)%ncell       = 1
  cell                     = 1
  grid%elem(1)%c2n(1,cell) = 7 ! map of cell 1 nodes
  grid%elem(1)%c2n(2,cell) = 8
  grid%elem(1)%c2n(3,cell) = 4
  grid%elem(1)%c2n(4,cell) = 1
  grid%elem(1)%c2n(5,cell) = 9
  grid%elem(1)%node_per_cell = 5
  grid%elem(1)%c2e(1,cell) = 16 ! map of cell 1 edges
  grid%elem(1)%c2e(2,cell) = 15
  grid%elem(1)%c2e(3,cell) = 13
  grid%elem(1)%c2e(4,cell) = 12
  grid%elem(1)%c2e(5,cell) = 11
  grid%elem(1)%c2e(6,cell) = 14
  grid%elem(1)%c2e(7,cell) = 8
  grid%elem(1)%c2e(8,cell) = 9
  grid%elem(1)%local_f2n   = local_f2n_pyr
  grid%elem(1)%local_f2e   = local_f2e_pyr
  grid%elem(1)%local_e2n   = local_e2n_pyr
  grid%elem(1)%chk_norm    = chk_norm_pyr

  grid%elem(2)%ncell       = 1
  cell                     = 1
  grid%elem(2)%c2n(1,cell) = 1
  grid%elem(2)%c2n(2,cell) = 7
  grid%elem(2)%c2n(3,cell) = 2
  grid%elem(2)%c2n(4,cell) = 5
  grid%elem(2)%c2n(5,cell) = 4
  grid%elem(2)%c2n(6,cell) = 8
  grid%elem(2)%c2n(7,cell) = 3
  grid%elem(2)%c2n(8,cell) = 6
  grid%elem(2)%node_per_cell = 8
  grid%elem(2)%c2e(1 ,cell) = 2
  grid%elem(2)%c2e(2 ,cell) = 3
  grid%elem(2)%c2e(3 ,cell) = 1
  grid%elem(2)%c2e(4 ,cell) = 5
  grid%elem(2)%c2e(5 ,cell) = 4
  grid%elem(2)%c2e(6 ,cell) = 10
  grid%elem(2)%c2e(7 ,cell) = 7
  grid%elem(2)%c2e(8 ,cell) = 6
  grid%elem(2)%c2e(9 ,cell) = 9
  grid%elem(2)%c2e(10,cell) = 8
  grid%elem(2)%c2e(11,cell) = 11
  grid%elem(2)%c2e(12,cell) = 14
  grid%elem(2)%local_f2n   = local_f2n_hex
  grid%elem(2)%local_f2e   = local_f2e_hex
  grid%elem(2)%local_e2n   = local_e2n_hex
  grid%elem(2)%chk_norm    = chk_norm_hex

!----------------------------------------------------------------------
!------------------ allocate and compute eprt -------------------------
!----------------------------------------------------------------------

    allocate(grid%eptr(2,grid%nedge)); grid%eptr = 0
    allocate(grid%el2g(grid%nedge));   grid%el2g = 0
    allocate(grid%l2g(grid%nedge));    grid%l2g = 0
    do i = 1, grid%nnodes01
      grid%l2g(i) = i
    enddo

    do ielem = 1,grid%nelem
       i1 = grid%elem(ielem)%edge_per_cell
       i2 = grid%elem(ielem)%ncell
       allocate(grid%elem(ielem)%c2e(i1,i2)); grid%elem(ielem)%c2e = 0
    end do

    edge = 0
    do ielem  = 1,grid%nelem
       do icell = 1,grid%elem(ielem)%ncell
          loop_ie: do ie = 1,grid%elem(ielem)%edge_per_cell
             i = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
             j = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)
             node1 = min(i,j)
             node2 = max(i,j)
             do i = 1,edge
                if ((grid%eptr(1,i)==node1).and.(grid%eptr(2,i)==node2)) then 
                   grid%elem(ielem)%c2e(ie,icell) = i
                   cycle loop_ie
                end if
             end do
             edge = edge + 1
             grid%el2g(edge) = edge 
             grid%eptr(1,edge) = node1
             grid%eptr(2,edge) = node2
             grid%elem(ielem)%c2e(ie,icell) = edge 
         end do loop_ie
      end do
    end do
    grid%nedge    = edge 
    grid%nedgeloc = edge 
    grid%nedgeg   = edge 


!----------------------------------------------------------------------
!----------------------------------------------------------------------

  grid%xn(:) = 0._dp
  grid%yn(:) = 0._dp
  grid%zn(:) = 0._dp
  grid%ra(:) = 0._dp

  do ielem = 1, grid%nelem

    call dualmetric( grid%x, grid%y, grid%z, grid%dxdt, grid%dydt, grid%dzdt &
                   , grid%xn, grid%yn, grid%zn, grid%eptr                    &
                   , grid%facespeed, grid%nnodes01, grid%nedgeloc            &
                   , grid%elem(ielem)%ncell, grid%elem(ielem)%ncell          &
                   , grid%elem(ielem)%c2n, grid%elem(ielem)%c2e              &
                   , grid%elem(ielem)%node_per_cell                          &
                   , grid%elem(ielem)%edge_per_cell                          &
                   , grid%elem(ielem)%local_e2n, grid%vol, grid%ra           &
                   , grid_motion, res_gcl, ielem, grid%nelem )

  end do

!write(6,'(16(1x,f15.5))') grid%xn
!write(6,'(16(1x,f15.5))') grid%yn
!write(6,'(16(1x,f15.5))') grid%zn
!write(6,'(16(1x,f15.5))') grid%ra
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!                       Manufactured solution
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
  a(1)=0; b(1)=0; c(1)=0; d(1)=1
  a(2)=0; b(2)=0; c(2)=0; d(2)=0
  a(3)=0; b(3)=0; c(3)=0; d(3)=0
  a(4)=0; b(4)=0; c(4)=0; d(4)=0
  a(5)=1; b(5)=1; c(5)=1; d(5)=1

  a(6)=1; b(6)=0; c(6)=0; d(6)=0
  a(7)=0; b(7)=1; c(7)=0; d(7)=0
  a(8)=0; b(8)=0; c(8)=1; d(8)=0

  a(9)=0; b(9)=0; c(9)=0; d(9)=1

  do i = 1, 9
    q_dof(1,i) = a(1)*grid%x(i) + b(1)*grid%y(i) + c(1)*grid%z(i) + d(1)
    q_dof(2,i) = a(2)*grid%x(i) + b(2)*grid%y(i) + c(2)*grid%z(i) + d(2)
    q_dof(3,i) = a(3)*grid%x(i) + b(3)*grid%y(i) + c(3)*grid%z(i) + d(3)
    q_dof(4,i) = a(4)*grid%x(i) + b(4)*grid%y(i) + c(4)*grid%z(i) + d(4)
    q_dof(5,i) = a(5)*grid%x(i) + b(5)*grid%y(i) + c(5)*grid%z(i) + d(5)
    turb(1,i)  = a(6)*grid%x(i) + b(6)*grid%y(i) + c(6)*grid%z(i) + d(6)
    turb(2,i)  = a(7)*grid%x(i) + b(7)*grid%y(i) + c(7)*grid%z(i) + d(7)
    turb(3,i)  = a(8)*grid%x(i) + b(8)*grid%y(i) + c(8)*grid%z(i) + d(8)
    amut(i)    = a(9)*grid%x(i) + b(9)*grid%y(i) + c(9)*grid%z(i) + d(9)
    sst_f1(i)  = 0.00_dp
  enddo

!----------------------------------------------------------------------
  gradx = 0.0_dp
  grady = 0.0_dp
  gradz = 0.0_dp

  call turbmultieqn_grad( grid%nnodes0, grid%nnodes01, grid%nedgeloc               &
                        , grid%nedgeloc_2d, grid%nnodes0_2d                        &
                        , grid%node_pairs_2d, grid%eptr, turb, grid%x, grid%y      &
                        , grid%z, grid%xn, grid%yn, grid%zn, grid%ra, grid%vol     &
                        , gradx, grady, gradz, grid%nbound, grid%bc, n_turb, n_grd &
                        , grid%nelem, grid%elem                                    &
                        , n_tot, q_dof, turbulence_model_int )

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,1),'_dp, gradx(6,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,1),'_dp, grady(6,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,1),'_dp, gradz(6,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,2),'_dp, gradx(6,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,2),'_dp, grady(6,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,2),'_dp, gradz(6,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,3),'_dp, gradx(6,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,3),'_dp, grady(6,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,3),'_dp, gradz(6,3), tol ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,4),'_dp, gradx(6,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,4),'_dp, grady(6,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,4),'_dp, gradz(6,4), tol ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,5),'_dp, gradx(6,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,5),'_dp, grady(6,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,5),'_dp, gradz(6,5), tol ) '
  write(6,'(a,f20.10,a)') '! node6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,6),'_dp, gradx(6,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,6),'_dp, grady(6,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,6),'_dp, gradz(6,6), tol ) '
  write(6,'(a,f20.10,a)') '! node7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,7),'_dp, gradx(6,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,7),'_dp, grady(6,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,7),'_dp, gradz(6,7), tol ) '
  write(6,'(a,f20.10,a)') '! eqn 6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,8),'_dp, gradx(6,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,8),'_dp, grady(6,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,8),'_dp, gradz(6,8), tol ) '
  write(6,'(a,f20.10,a)') '! eqn 7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(7,3),'_dp, gradx(7,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(7,3),'_dp, grady(7,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(7,3),'_dp, gradz(7,3), tol ) '
  write(6,'(a,f20.10,a)') '! eqn 8'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(8,3),'_dp, gradx(8,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(8,3),'_dp, grady(8,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(8,3),'_dp, gradz(8,3), tol ) '

! node1
assert_equal_within(        0.9375000000_dp, gradx(6,1), tol ) 
assert_equal_within(       -0.0401785714_dp, grady(6,1), tol ) 
assert_equal_within(       -0.8035714286_dp, gradz(6,1), tol ) 
! node2
assert_equal_within(       -1.0000000000_dp, gradx(6,2), tol ) 
assert_equal_within(        2.0000000000_dp, grady(6,2), tol ) 
assert_equal_within(       40.0000000000_dp, gradz(6,2), tol ) 
! node3
assert_equal_within(       -1.0000000000_dp, gradx(6,3), tol ) 
assert_equal_within(       -2.0000000000_dp, grady(6,3), tol ) 
assert_equal_within(       40.0000000000_dp, gradz(6,3), tol ) 
! node4
assert_equal_within(        0.9375000000_dp, gradx(6,4), tol ) 
assert_equal_within(        0.0401785714_dp, grady(6,4), tol ) 
assert_equal_within(       -0.8035714286_dp, gradz(6,4), tol ) 
! node5
assert_equal_within(       -1.0000000000_dp, gradx(6,5), tol ) 
assert_equal_within(        2.0000000000_dp, grady(6,5), tol ) 
assert_equal_within(      -40.0000000000_dp, gradz(6,5), tol ) 
! node6
assert_equal_within(       -1.0000000000_dp, gradx(6,6), tol ) 
assert_equal_within(       -2.0000000000_dp, grady(6,6), tol ) 
assert_equal_within(      -40.0000000000_dp, gradz(6,6), tol ) 
! node7
assert_equal_within(        0.9375000000_dp, gradx(6,7), tol ) 
assert_equal_within(       -0.0401785714_dp, grady(6,7), tol ) 
assert_equal_within(        0.8035714286_dp, gradz(6,7), tol ) 
! eqn 6
assert_equal_within(        0.9375000000_dp, gradx(6,8), tol ) 
assert_equal_within(        0.0401785714_dp, grady(6,8), tol ) 
assert_equal_within(        0.8035714286_dp, gradz(6,8), tol ) 
! eqn 7
assert_equal_within(       -2.0000000000_dp, gradx(7,3), tol ) 
assert_equal_within(       -1.0000000000_dp, grady(7,3), tol ) 
assert_equal_within(       40.0000000000_dp, gradz(7,3), tol ) 
! eqn 8
assert_equal_within(        0.0000000000_dp, gradx(8,3), tol ) 
assert_equal_within(        0.0000000000_dp, grady(8,3), tol ) 
assert_equal_within(        1.0000000000_dp, gradz(8,3), tol ) 


!write(6,*) 'kloc = ', kloc
!write(6,*) 'wloc = ', wloc
!gradx(6,:) = gradx(7,:)
!grady(6,:) = grady(7,:)
!gradz(6,:) = gradz(7,:)

   do i = 6, n_grd
 write(*,*)
     write(6,'(i5,10(1x,f15.5))')  i,               &
     gradx(i,1), gradx(i,2), gradx(i,3), gradx(i,4) &
   , gradx(i,5), gradx(i,6), gradx(i,7), gradx(i,8)
     write(6,'(i5,10(1x,f15.5))')  i,               &
     grady(i,1), grady(i,2), grady(i,3), grady(i,4) &
   , grady(i,5), grady(i,6), grady(i,7), grady(i,8)
     write(6,'(i5,10(1x,f15.5))')  i,               &
     gradz(i,1), gradz(i,2), gradz(i,3), gradz(i,4) &
   , gradz(i,5), gradz(i,6), gradz(i,7), gradz(i,8)
   enddo
  write(*,*)
  write(*,*) 'Finish turbmultieqn_grad--next test is turb_resid_diff_element'
  write(*,*)

!=============================================================================80
!  3-equation edge_averaging
!=============================================================================80
! point = (/0.5_dp, 0.5_dp, 0.5_dp/)
  xmach = 1.5_dp
  re    = 0.02_dp
  model_strain_form_int = traceless
  n_sta           = 1 ! first location
  q_type          = 1 ! primitive
  eqn_set         = 0 ! compressible

  viscous_method            = 0 ! element
  diff_edge_avg_t            = 1 ! 0 ! 1
  use_edge_gradients        = .true. ! .false. ! .true.

  gradient_construction_rhs = 0
  t_diff1                   = 1
  t_diff2                   = 1
  nu(1)                     = 1.0_dp
  turb_res                  = 0.0_dp


    call get_diffusion_coefficients( turbulence_model_int, n_turb    &
                        , diff_const, diff_const2 )

      do ielem = 1, grid%nelem

       call turb_resid_diff_element ( n_sta, eqn_set, grid%nnodes0    &
               , turb, q_dof, turb_res                                &
               , grid%elem(ielem)%ncell                               &
               , grid%elem(ielem)%c2n, grid%x, grid%y, grid%z         &
               , grid%elem(ielem)%local_f2n                           &
               , grid%elem(ielem)%local_e2n                           &
               , grid%elem(ielem)%local_f2e                           &
               , grid%elem(ielem)%e2n_2d                              &
               , grid%elem(ielem)%face_per_cell                       &
               , grid%elem(ielem)%node_per_cell                       &
               , grid%elem(ielem)%edge_per_cell                       &
               , grid%elem(ielem)%type_cell                           &
               , n_turb, grid%elem(ielem)%face_2d                     &
               , grid%elem(ielem)%chk_norm                            &
               , turbulence_model_int, amut, diff_const, diff_const2  &
               , sst_f1 )

!       call turb_jacob_diff_element( n_sta, eqn_set, grid%nnodes0   &
!          , turb, q_dof, nu, a_diag, a_off  &
!          , grid%elem(ielem)%ncell          &
!          , grid%elem(ielem)%c2n            &
!          , grid%x                          &
!          , grid%y                          &
!          , grid%z                          &
!          , grid%elem(ielem)%type_cell      &
!          , grid%elem(ielem)%local_f2n      &
!          , grid%elem(ielem)%local_e2n      &
!          , grid%elem(ielem)%local_f2e      &
!          , grid%elem(ielem)%e2n_2d         &
!          , grid%elem(ielem)%face_per_cell  &
!          , grid%elem(ielem)%node_per_cell  &
!          , grid%elem(ielem)%edge_per_cell  &
!          , ia                              &
!          , ja                              &
!          , n_turb                          &
!          , grid%elem(ielem)%face_2d        &
!          , grid%elem(ielem)%chk_norm       &
!          , nzg2m                           &
!          , g2m,                            &
!          , turbulence_model                &
!          , amut,                           &
!          , diff_const                      &
!          , diff_const2                     &
!          , sst_f1 )

!       call turb_resid_diff(eqn_set, viscous_method                                &
!            , grid%nnodes0, grid%nnodes01                                          &
!            , grid%nedgeloc, grid%eptr                                             &
!            , turb, q_dof, turb_res, gradx, grady, gradz                           &
!            , grid%xn, grid%yn, grid%zn, grid%ra, ielem, grid%elem(ielem)%ncell    &
!            , grid%elem(ielem)%c2n, grid%elem(ielem)%c2e, grid%x, grid%y, grid%z   &
!            , grid%elem(ielem)%local_f2n, grid%elem(ielem)%local_e2n               &
!            , grid%elem(ielem)%local_f2e, grid%elem(ielem)%e2n_2d                  &
!            , grid%nedgeloc_2d, grid%elem(ielem)%face_per_cell                     &
!            , grid%elem(ielem)%node_per_cell                                       &
!            , grid%elem(ielem)%edge_per_cell, grid%elem(ielem)%type_cell           &
!            , n_turb, n_tot, n_grd, grid%elem(ielem)%face_2d, amut                 &
!            , sst_f1, grid%elem(ielem)%chk_norm )
      end do

! do i = 1, 9
! write(6,'(10(1x,f20.10))')  turb_res(1,i),turb_res(2,i)
! enddo

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,1),'_dp,  turb_res(1,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,1),'_dp,  turb_res(2,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,1),'_dp,  turb_res(3,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,2),'_dp,  turb_res(1,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,2),'_dp,  turb_res(2,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,2),'_dp,  turb_res(3,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,3),'_dp,  turb_res(1,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,3),'_dp,  turb_res(2,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,3),'_dp,  turb_res(3,3), tol ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,4),'_dp,  turb_res(1,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,4),'_dp,  turb_res(2,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,4),'_dp,  turb_res(3,4), tol ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,5),'_dp,  turb_res(1,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,5),'_dp,  turb_res(2,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,5),'_dp,  turb_res(3,5), tol ) '
  write(6,'(a,f20.10,a)') '! node6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,6),'_dp,  turb_res(1,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,6),'_dp,  turb_res(2,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,6),'_dp,  turb_res(3,6), tol ) '
  write(6,'(a,f20.10,a)') '! node7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,7),'_dp,  turb_res(1,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,7),'_dp,  turb_res(2,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,7),'_dp,  turb_res(3,7), tol ) '
  write(6,'(a,f20.10,a)') '! node8'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,8),'_dp,  turb_res(1,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,8),'_dp,  turb_res(2,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,8),'_dp,  turb_res(3,8), tol ) '
  write(6,'(a,f20.10,a)') '! node9'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,9),'_dp,  turb_res(1,9), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,9),'_dp,  turb_res(2,9), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,9),'_dp,  turb_res(3,9), tol ) '

! node1
assert_equal_within(       -3.3627146649_dp,  turb_res(1,1), tol ) 
assert_equal_within(       -4.4231247134_dp,  turb_res(2,1), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,1), tol ) 
! node2
assert_equal_within(        4.3577863780_dp,  turb_res(1,2), tol ) 
assert_equal_within(       -6.6164262545_dp,  turb_res(2,2), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,2), tol ) 
! node3
assert_equal_within(        7.5539262545_dp,  turb_res(1,3), tol ) 
assert_equal_within(        6.6164262545_dp,  turb_res(2,3), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,3), tol ) 
! node4
assert_equal_within(       -6.0916307491_dp,  turb_res(1,4), tol ) 
assert_equal_within(        4.5632918511_dp,  turb_res(2,4), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,4), tol ) 
! node5
assert_equal_within(        4.4976810450_dp,  turb_res(1,5), tol ) 
assert_equal_within(       -6.7960568570_dp,  turb_res(2,5), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,5), tol ) 
! node6
assert_equal_within(        7.7335568570_dp,  turb_res(1,6), tol ) 
assert_equal_within(        6.7960568570_dp,  turb_res(2,6), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,6), tol ) 
! node7
assert_equal_within(       -3.4830930736_dp,  turb_res(1,7), tol ) 
assert_equal_within(       -4.6015163469_dp,  turb_res(2,7), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,7), tol ) 
! node8
assert_equal_within(       -6.2441460543_dp,  turb_res(1,8), tol ) 
assert_equal_within(        4.7439631963_dp,  turb_res(2,8), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,8), tol ) 
! node9
assert_equal_within(       -4.9613659926_dp,  turb_res(1,9), tol ) 
assert_equal_within(       -0.2826139871_dp,  turb_res(2,9), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,9), tol ) 


  write(*,*)
  write(*,*) 'Finish part 1 test_turb_resid_diff_element'
  write(*,*)

!----------------------------------------------------------------------
!=============================================================================80
!  3-equation cell_averaging
!=============================================================================80
! point = (/0.5_dp, 0.5_dp, 0.5_dp/)
  xmach = 1.5_dp
  re    = 0.02_dp
  model_strain_form_int = traceless
  n_sta           = 1 ! first location
  q_type          = 1 ! primitive
  eqn_set         = 0 ! compressible

  viscous_method            = 0 ! element
  diff_edge_avg_t            = 0 ! 1
  use_edge_gradients        = .false. ! .true.

  gradient_construction_rhs = 0
  t_diff1                   = 1
  t_diff2                   = 1
  nu(1)                     = 1.0_dp
  turb_res                  = 0.0_dp


    call get_diffusion_coefficients( turbulence_model_int, n_turb   &
                        , diff_const, diff_const2 )

      do ielem = 1, grid%nelem

       call turb_resid_diff_element ( n_sta, eqn_set, grid%nnodes0    &
               , turb, q_dof, turb_res                                &
               , grid%elem(ielem)%ncell                               &
               , grid%elem(ielem)%c2n, grid%x, grid%y, grid%z         &
               , grid%elem(ielem)%local_f2n                           &
               , grid%elem(ielem)%local_e2n                           &
               , grid%elem(ielem)%local_f2e                           &
               , grid%elem(ielem)%e2n_2d                              &
               , grid%elem(ielem)%face_per_cell                       &
               , grid%elem(ielem)%node_per_cell                       &
               , grid%elem(ielem)%edge_per_cell                       &
               , grid%elem(ielem)%type_cell                           &
               , n_turb, grid%elem(ielem)%face_2d                     &
               , grid%elem(ielem)%chk_norm                            &
               , turbulence_model_int, amut, diff_const, diff_const2  &
               , sst_f1 )

!       call turb_jacob_diff_element( n_sta, eqn_set, grid%nnodes0   &
!          , turb, q_dof, nu, a_diag, a_off  &
!          , grid%elem(ielem)%ncell          &
!          , grid%elem(ielem)%c2n            &
!          , grid%x                          &
!          , grid%y                          &
!          , grid%z                          &
!          , grid%elem(ielem)%type_cell      &
!          , grid%elem(ielem)%local_f2n      &
!          , grid%elem(ielem)%local_e2n      &
!          , grid%elem(ielem)%local_f2e      &
!          , grid%elem(ielem)%e2n_2d         &
!          , grid%elem(ielem)%face_per_cell  &
!          , grid%elem(ielem)%node_per_cell  &
!          , grid%elem(ielem)%edge_per_cell  &
!          , ia                              &
!          , ja                              &
!          , n_turb                          &
!          , grid%elem(ielem)%face_2d        &
!          , grid%elem(ielem)%chk_norm       &
!          , nzg2m                           &
!          , g2m,                            &
!          , turbulence_model                &
!          , amut,                           &
!          , diff_const                      &
!          , diff_const2                     &
!          , sst_f1 )

!       call turb_resid_diff(eqn_set, viscous_method                                &
!            , grid%nnodes0, grid%nnodes01                                          &
!            , grid%nedgeloc, grid%eptr                                             &
!            , turb, q_dof, turb_res, gradx, grady, gradz                           &
!            , grid%xn, grid%yn, grid%zn, grid%ra, ielem, grid%elem(ielem)%ncell    &
!            , grid%elem(ielem)%c2n, grid%elem(ielem)%c2e, grid%x, grid%y, grid%z   &
!            , grid%elem(ielem)%local_f2n, grid%elem(ielem)%local_e2n               &
!            , grid%elem(ielem)%local_f2e, grid%elem(ielem)%e2n_2d                  &
!            , grid%nedgeloc_2d, grid%elem(ielem)%face_per_cell                     &
!            , grid%elem(ielem)%node_per_cell                                       &
!            , grid%elem(ielem)%edge_per_cell, grid%elem(ielem)%type_cell           &
!            , n_turb, n_tot, n_grd, grid%elem(ielem)%face_2d, amut                 &
!            , sst_f1, grid%elem(ielem)%chk_norm )
      end do

! do i = 1, 9
! write(6,'(10(1x,f20.10))')  turb_res(1,i),turb_res(2,i)
! enddo

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,1),'_dp,  turb_res(1,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,1),'_dp,  turb_res(2,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,1),'_dp,  turb_res(3,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,2),'_dp,  turb_res(1,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,2),'_dp,  turb_res(2,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,2),'_dp,  turb_res(3,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,3),'_dp,  turb_res(1,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,3),'_dp,  turb_res(2,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,3),'_dp,  turb_res(3,3), tol ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,4),'_dp,  turb_res(1,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,4),'_dp,  turb_res(2,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,4),'_dp,  turb_res(3,4), tol ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,5),'_dp,  turb_res(1,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,5),'_dp,  turb_res(2,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,5),'_dp,  turb_res(3,5), tol ) '
  write(6,'(a,f20.10,a)') '! node6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,6),'_dp,  turb_res(1,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,6),'_dp,  turb_res(2,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,6),'_dp,  turb_res(3,6), tol ) '
  write(6,'(a,f20.10,a)') '! node7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,7),'_dp,  turb_res(1,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,7),'_dp,  turb_res(2,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,7),'_dp,  turb_res(3,7), tol ) '
  write(6,'(a,f20.10,a)') '! node8'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,8),'_dp,  turb_res(1,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,8),'_dp,  turb_res(2,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,8),'_dp,  turb_res(3,8), tol ) '
  write(6,'(a,f20.10,a)') '! node9'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,9),'_dp,  turb_res(1,9), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,9),'_dp,  turb_res(2,9), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,9),'_dp,  turb_res(3,9), tol ) '

! node1
assert_equal_within(       -4.6540542921_dp,  turb_res(1,1), tol ) 
assert_equal_within(       -6.1674209752_dp,  turb_res(2,1), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,1), tol ) 
! node2
assert_equal_within(        6.0357376336_dp,  turb_res(1,2), tol ) 
assert_equal_within(       -5.0982376336_dp,  turb_res(2,2), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,2), tol ) 
! node3
assert_equal_within(        6.0357376336_dp,  turb_res(1,3), tol ) 
assert_equal_within(        5.0982376336_dp,  turb_res(2,3), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,3), tol ) 
! node4
assert_equal_within(       -4.6540542921_dp,  turb_res(1,4), tol ) 
assert_equal_within(        6.1674209752_dp,  turb_res(2,4), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,4), tol ) 
! node5
assert_equal_within(        6.0357376336_dp,  turb_res(1,5), tol ) 
assert_equal_within(       -5.0982376336_dp,  turb_res(2,5), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,5), tol ) 
! node6
assert_equal_within(        6.0357376336_dp,  turb_res(1,6), tol ) 
assert_equal_within(        5.0982376336_dp,  turb_res(2,6), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,6), tol ) 
! node7
assert_equal_within(       -4.6540542921_dp,  turb_res(1,7), tol ) 
assert_equal_within(       -6.1674209752_dp,  turb_res(2,7), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,7), tol ) 
! node8
assert_equal_within(       -4.6540542921_dp,  turb_res(1,8), tol ) 
assert_equal_within(        6.1674209752_dp,  turb_res(2,8), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,8), tol ) 
! node9
assert_equal_within(       -5.5267333662_dp,  turb_res(1,9), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(2,9), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,9), tol )

  write(*,*)
  write(*,*) 'Finish part 2 test_turb_resid_diff_element'
  write(*,*)

!----------------------------------------------------------------------
!----------------------------------------------------------------------

  call deallocate_elem ( grid%elem(1) )
! call deallocate_bc   ( bcsoln(1) )
  call deallocate_bc   ( grid%bc(1) )

  write(6,'(a,10(1x,f20.10))') 'fin test_turb_resid_diff'

end test
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80

test test_turb_resid_diff_4eqn

  use grid_types,    only : grid_type
! use soln_types,    only : soln_type
  use info_depr,     only : twod, grad_x_y_z_contents, use_edge_gradients
  use grids,         only : nullify_grid, allocate_grid, deallocate_grid
  use bc_types,      only : bcgrid_type, nullify_bc, allocate_dummy_bc      &
                          , deallocate_bc, bcsoln_type, allocate_bcsoln
  use element_types, only : elem_type
  use allocations,   only : my_alloc_ptr, my_realloc_ptr
  use element_defs,  only : nullify_elem, allocate_elem, initialize_elem    &
                          , deallocate_elem
  use element_defs,  only : max_node_per_cell, max_face_per_cell               &
                          , max_edge_per_cell 
  use element_defs,  only : local_f2n_pyr, local_f2e_pyr, local_e2n_pyr
  use element_defs,  only : local_f2n_hex, local_f2e_hex, local_e2n_hex
  use element_defs,  only : chk_norm_hex, chk_norm_pyr

  use fluid,         only : setup_fluid_gamma, setup_sutherland_constant
  use info_depr,     only : xmach, re
  use thermo,        only : q_type, primitive_q_type
  use bc_names,      only : twall
  use turbulence_info, only : model_strain_form_int, turbulence_model_int
  use debug_defs,    only : gradient_construction_rhs
  use nml_boundary_conditions, only : wall_velocity
  use turb_parameters, only : t_diff1, t_diff2
  use grid_metrics,    only : dualmetric
  use turb_util,       only : turbmultieqn_grad, kloc, wloc
  use debug_defs,      only : unified_diffusion, diff_edge_avg_t
  use turbulence_info, only : gamma_ret_sst

  integer, parameter                 :: nedgeloc = 16
  integer, parameter                 :: n_grd    = 9
  integer, parameter                 :: nnodes0  = 9
  integer, parameter                 :: nnodes01 = 9
  integer, parameter                 :: n_tot    = 5
  integer, parameter                 :: n_turb   = 4
  integer, parameter                 :: nnz01    = 10

  type(grid_type)                    :: grid
! type(soln_type)                    :: soln
  real(dp),          dimension(n_tot ,nnodes01)  :: q_dof
  real(dp),          dimension(       nnodes01)  :: amut
  real(dp),          dimension(n_turb,nnodes01)  :: turb_res
  real(dp),          dimension(n_turb,nnodes01)  :: turb
  real(dp),          dimension(n_grd ,nnodes01)  :: gradx
  real(dp),          dimension(n_grd ,nnodes01)  :: grady
  real(dp),          dimension(n_grd ,nnodes01)  :: gradz
  real(dp),          dimension(       nnodes01)  :: sst_f1
  real(dp),          dimension(11             )  :: a, b, c, d
  real(dp),          dimension(1              )  :: nu
  real(dp),          dimension(nnodes01+1     )  :: ia
  real(dp),          dimension(nnz01          )  :: ja
  real(dp),          dimension(nnz01          )  :: nzg2m, g2m
  real(dp),          dimension(n_turb         )  :: diff_const
  real(dp),          dimension(n_turb         )  :: diff_const2

! type(bcgrid_type), dimension(1)    :: bc
  type(bcsoln_type), dimension(1)    :: bcsoln
  integer                            :: ib
! integer                            :: ibc
  integer                            :: n_sta
  integer                            :: nodes_per_face
  integer                            :: face_index
  integer                            :: eqn_set

  integer                            :: ielem, icell
  integer                            :: cell
  integer                            :: node1, node2
  integer                            :: njac
  integer                            :: viscous_method
  integer                            :: i, ie, j
  integer                            :: i1, i2, edge

  character(80)                      :: grid_motion
  real(dp), dimension(1,nnodes01)    :: res_gcl


  max_face_per_cell    = 6
  max_node_per_cell    = 8
  max_edge_per_cell    = 12
  eqn_set              = 0
  cell                 = 1
  njac                 = 5
  twod                 = .false.
  turbulence_model_int =  gamma_ret_sst
  kloc                 = n_grd - n_turb + 1
  wloc                 = n_grd - n_turb + 2

  call setup_fluid_gamma
  call setup_sutherland_constant

  call nullify_grid ( grid )
  grid%nnodes0  = nnodes0
  grid%nnodes01 = nnodes01
  allocate ( grid%x( grid%nnodes01 ) )
  allocate ( grid%y( grid%nnodes01 ) )
  allocate ( grid%z( grid%nnodes01 ) )
  allocate ( grid%bc(1) )
  allocate ( grid%elem(2) )

  call allocate_bcsoln   ( 9, bcsoln(1) )

  call initialize_elem ( grid%elem(1), 'pyr' )
  call initialize_elem ( grid%elem(2), 'hex' )
  call allocate_elem ( grid%elem(2), .false. )

  grid%nelem                 = 2
  grid%nedge                 = nedgeloc
  grid%nedgeloc              = nedgeloc
  grid%nedgeloc_2d           = 0
  grid%elem(1)%edge_per_cell = 8
  grid%elem(2)%edge_per_cell = 12

  allocate ( grid%vol      (   grid%nnodes01 ) ) 
  allocate ( grid%dxdt     (   grid%nnodes01 ) ) 
  allocate ( grid%dydt     (   grid%nnodes01 ) ) 
  allocate ( grid%dzdt     (   grid%nnodes01 ) ) 
  allocate ( grid%facespeed(   grid%nedgeloc ) ) 
  allocate ( grid%eptr     ( 2,grid%nedgeloc ) ) 
  allocate ( grid%xn       (   grid%nedgeloc ) )
  allocate ( grid%yn       (   grid%nedgeloc ) )
  allocate ( grid%zn       (   grid%nedgeloc ) )
  allocate ( grid%ra       (   grid%nedgeloc ) )

  grid%x(1) = 0.0_dp; grid%y(1) = 0.0_dp; grid%z(1) = 0.0_dp
  grid%x(2) = 1.0_dp; grid%y(2) = 0.0_dp; grid%z(2) = 0.0_dp
  grid%x(3) = 1.0_dp; grid%y(3) = 1.0_dp; grid%z(3) = 0.0_dp
  grid%x(4) = 0.0_dp; grid%y(4) = 1.0_dp; grid%z(4) = 0.0_dp
  grid%x(5) = 1.0_dp; grid%y(5) = 0.0_dp; grid%z(5) = 5.0e-2_dp
  grid%x(6) = 1.0_dp; grid%y(6) = 1.0_dp; grid%z(6) = 5.0e-2_dp
  grid%x(7) = 0.0_dp; grid%y(7) = 0.0_dp; grid%z(7) = 5.0e-2_dp
  grid%x(8) = 0.0_dp; grid%y(8) = 1.0_dp; grid%z(8) = 5.0e-2_dp
  grid%x(9) =-0.5_dp; grid%y(9) = 0.5_dp; grid%z(9) = 2.5e-2_dp

  ib                  = 1
  grid%nbound         = 0 ! switch to 1 to test boundary gradients
  grid%bc(ib)%ibc     = 4000 ! viscous wall function boundary
  grid%bc(ib)%nbnode  = 5 ! number of boundary nodes
  grid%bc(ib)%nbfacet = 1
  grid%bc(ib)%nbfaceq = 1

  ! account for one triangle face and one quad face
  allocate ( grid%bc(ib)%ibnode( grid%bc(ib)%nbnode) )
  allocate ( grid%bc(ib)%f2ntb ( grid%bc(ib)%nbfacet, 5 ) )
  allocate ( grid%bc(ib)%f2nqb ( grid%bc(ib)%nbfaceq, 6 ) )
  allocate ( bcsoln(ib)%mu_t_wf ( grid%bc(ib)%nbnode ) )
  allocate ( bcsoln(ib)%omega_wf ( grid%bc(ib)%nbnode ) )
  allocate ( grid%elem(1)%c2n    (  5, 1 ) )
  allocate ( grid%elem(2)%c2n    (  8, 1 ) )
  allocate ( grid%elem(1)%c2e    (  8, 1 ) )
  allocate ( grid%elem(2)%c2e    ( 12, 1 ) )
  allocate ( grid%elem(1)%e2n_2d (  4, 2 ) )
  allocate ( grid%elem(2)%e2n_2d (  4, 2 ) )

  wall_velocity(1,:) = (/0.3_dp,0.2_dp,0.1_dp/)

  face_index     = 1
  nodes_per_face = 3
  grid%bc(ib)%f2ntb(face_index,1) = 1 ! 9 ! face 1, node 1
  grid%bc(ib)%f2ntb(face_index,2) = 5 ! 4 ! face 1, node 2
  grid%bc(ib)%f2ntb(face_index,3) = 4 ! 1 ! face 1, node 3
  grid%bc(ib)%f2ntb(face_index,4) = 1 ! cell number
  grid%bc(ib)%f2ntb(face_index,5) = 1 ! element type
  grid%bc(ib)%nbfacet             = 1 ! number of tri faces

  face_index     = 1
  nodes_per_face = 4
  grid%bc(ib)%f2nqb(face_index,1) = 1 ! 1 ! face 1, node 1
  grid%bc(ib)%f2nqb(face_index,2) = 4 ! 2 ! face 1, node 2
  grid%bc(ib)%f2nqb(face_index,3) = 3 ! 3 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,4) = 2 ! 4 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,5) = 1 ! cell number
  grid%bc(ib)%f2nqb(face_index,6) = 2 ! element type
  grid%bc(ib)%nbfaceq             = 1 ! number of quad faces

  grid%bc(ib)%ibnode(1) = 1 ! boundary node
  grid%bc(ib)%ibnode(2) = 2 ! boundary node
  grid%bc(ib)%ibnode(3) = 3 ! boundary node
  grid%bc(ib)%ibnode(4) = 4 ! boundary node
  grid%bc(ib)%ibnode(5) = 9 ! boundary node

  grid%elem(1)%ncell       = 1
  cell                     = 1
  grid%elem(1)%c2n(1,cell) = 7 ! map of cell 1 nodes
  grid%elem(1)%c2n(2,cell) = 8
  grid%elem(1)%c2n(3,cell) = 4
  grid%elem(1)%c2n(4,cell) = 1
  grid%elem(1)%c2n(5,cell) = 9
  grid%elem(1)%node_per_cell = 5
  grid%elem(1)%c2e(1,cell) = 16 ! map of cell 1 edges
  grid%elem(1)%c2e(2,cell) = 15
  grid%elem(1)%c2e(3,cell) = 13
  grid%elem(1)%c2e(4,cell) = 12
  grid%elem(1)%c2e(5,cell) = 11
  grid%elem(1)%c2e(6,cell) = 14
  grid%elem(1)%c2e(7,cell) = 8
  grid%elem(1)%c2e(8,cell) = 9
  grid%elem(1)%local_f2n   = local_f2n_pyr
  grid%elem(1)%local_f2e   = local_f2e_pyr
  grid%elem(1)%local_e2n   = local_e2n_pyr
  grid%elem(1)%chk_norm    = chk_norm_pyr

  grid%elem(2)%ncell       = 1
  cell                     = 1
  grid%elem(2)%c2n(1,cell) = 1
  grid%elem(2)%c2n(2,cell) = 7
  grid%elem(2)%c2n(3,cell) = 2
  grid%elem(2)%c2n(4,cell) = 5
  grid%elem(2)%c2n(5,cell) = 4
  grid%elem(2)%c2n(6,cell) = 8
  grid%elem(2)%c2n(7,cell) = 3
  grid%elem(2)%c2n(8,cell) = 6
  grid%elem(2)%node_per_cell = 8
  grid%elem(2)%c2e(1 ,cell) = 2
  grid%elem(2)%c2e(2 ,cell) = 3
  grid%elem(2)%c2e(3 ,cell) = 1
  grid%elem(2)%c2e(4 ,cell) = 5
  grid%elem(2)%c2e(5 ,cell) = 4
  grid%elem(2)%c2e(6 ,cell) = 10
  grid%elem(2)%c2e(7 ,cell) = 7
  grid%elem(2)%c2e(8 ,cell) = 6
  grid%elem(2)%c2e(9 ,cell) = 9
  grid%elem(2)%c2e(10,cell) = 8
  grid%elem(2)%c2e(11,cell) = 11
  grid%elem(2)%c2e(12,cell) = 14
  grid%elem(2)%local_f2n   = local_f2n_hex
  grid%elem(2)%local_f2e   = local_f2e_hex
  grid%elem(2)%local_e2n   = local_e2n_hex
  grid%elem(2)%chk_norm    = chk_norm_hex

!----------------------------------------------------------------------
!------------------ allocate and compute eprt -------------------------
!----------------------------------------------------------------------

    allocate(grid%eptr(2,grid%nedge)); grid%eptr = 0
    allocate(grid%el2g(grid%nedge));   grid%el2g = 0
    allocate(grid%l2g(grid%nedge));    grid%l2g = 0
    do i = 1, grid%nnodes01
      grid%l2g(i) = i
    enddo

    do ielem = 1,grid%nelem
       i1 = grid%elem(ielem)%edge_per_cell
       i2 = grid%elem(ielem)%ncell
       allocate(grid%elem(ielem)%c2e(i1,i2)); grid%elem(ielem)%c2e = 0
    end do

    edge = 0
    do ielem  = 1,grid%nelem
       do icell = 1,grid%elem(ielem)%ncell
          loop_ie: do ie = 1,grid%elem(ielem)%edge_per_cell
             i = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
             j = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)
             node1 = min(i,j)
             node2 = max(i,j)
             do i = 1,edge
                if ((grid%eptr(1,i)==node1).and.(grid%eptr(2,i)==node2)) then 
                   grid%elem(ielem)%c2e(ie,icell) = i
                   cycle loop_ie
                end if
             end do
             edge = edge + 1
             grid%el2g(edge) = edge 
             grid%eptr(1,edge) = node1
             grid%eptr(2,edge) = node2
             grid%elem(ielem)%c2e(ie,icell) = edge 
         end do loop_ie
      end do
    end do
    grid%nedge    = edge 
    grid%nedgeloc = edge 
    grid%nedgeg   = edge 


!----------------------------------------------------------------------
!----------------------------------------------------------------------

  grid%xn(:) = 0._dp
  grid%yn(:) = 0._dp
  grid%zn(:) = 0._dp
  grid%ra(:) = 0._dp

  do ielem = 1, grid%nelem

    call dualmetric( grid%x, grid%y, grid%z, grid%dxdt, grid%dydt, grid%dzdt &
                   , grid%xn, grid%yn, grid%zn, grid%eptr                    &
                   , grid%facespeed, grid%nnodes01, grid%nedgeloc            &
                   , grid%elem(ielem)%ncell, grid%elem(ielem)%ncell          &
                   , grid%elem(ielem)%c2n, grid%elem(ielem)%c2e              &
                   , grid%elem(ielem)%node_per_cell                          &
                   , grid%elem(ielem)%edge_per_cell                          &
                   , grid%elem(ielem)%local_e2n, grid%vol, grid%ra           &
                   , grid_motion, res_gcl, ielem, grid%nelem )

  end do

!write(6,'(16(1x,f15.5))') grid%xn
!write(6,'(16(1x,f15.5))') grid%yn
!write(6,'(16(1x,f15.5))') grid%zn
!write(6,'(16(1x,f15.5))') grid%ra
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!                       Manufactured solution
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
  a(1)=0; b(1)=0; c(1)=0; d(1)=1
  a(2)=0; b(2)=0; c(2)=0; d(2)=0
  a(3)=0; b(3)=0; c(3)=0; d(3)=0
  a(4)=0; b(4)=0; c(4)=0; d(4)=0
  a(5)=1; b(5)=1; c(5)=1; d(5)=1

  a(6)=1; b(6)=0; c(6)=0; d(6)=0
  a(7)=0; b(7)=1; c(7)=0; d(7)=0
  a(8)=0; b(8)=0; c(8)=1; d(8)=0
  a(9)=0; b(9)=0; c(9)=0; d(9)=1

  a(10)=0; b(10)=0; c(10)=0; d(10)=1

  do i = 1, 9
    q_dof(1,i) = a(1)*grid%x(i) + b(1)*grid%y(i) + c(1)*grid%z(i) + d(1)
    q_dof(2,i) = a(2)*grid%x(i) + b(2)*grid%y(i) + c(2)*grid%z(i) + d(2)
    q_dof(3,i) = a(3)*grid%x(i) + b(3)*grid%y(i) + c(3)*grid%z(i) + d(3)
    q_dof(4,i) = a(4)*grid%x(i) + b(4)*grid%y(i) + c(4)*grid%z(i) + d(4)
    q_dof(5,i) = a(5)*grid%x(i) + b(5)*grid%y(i) + c(5)*grid%z(i) + d(5)
    turb(1,i)  = a(6)*grid%x(i) + b(6)*grid%y(i) + c(6)*grid%z(i) + d(6)
    turb(2,i)  = a(7)*grid%x(i) + b(7)*grid%y(i) + c(7)*grid%z(i) + d(7)
    turb(3,i)  = a(8)*grid%x(i) + b(8)*grid%y(i) + c(8)*grid%z(i) + d(8)
    turb(4,i)  = a(9)*grid%x(i) + b(9)*grid%y(i) + c(9)*grid%z(i) + d(9)
    amut(i)    = a(10)*grid%x(i) + b(10)*grid%y(i) + c(10)*grid%z(i) + d(10)
    sst_f1(i)  = 0.00_dp
  enddo

!----------------------------------------------------------------------
  gradx = 0.0_dp
  grady = 0.0_dp
  gradz = 0.0_dp

  call turbmultieqn_grad( grid%nnodes0, grid%nnodes01, grid%nedgeloc               &
                        , grid%nedgeloc_2d, grid%nnodes0_2d                        &
                        , grid%node_pairs_2d, grid%eptr, turb, grid%x, grid%y      &
                        , grid%z, grid%xn, grid%yn, grid%zn, grid%ra, grid%vol     &
                        , gradx, grady, gradz, grid%nbound, grid%bc, n_turb, n_grd &
                        , grid%nelem, grid%elem                                    &
                        , n_tot, q_dof, turbulence_model_int )

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,1),'_dp, gradx(6,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,1),'_dp, grady(6,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,1),'_dp, gradz(6,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,2),'_dp, gradx(6,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,2),'_dp, grady(6,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,2),'_dp, gradz(6,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,3),'_dp, gradx(6,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,3),'_dp, grady(6,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,3),'_dp, gradz(6,3), tol ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,4),'_dp, gradx(6,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,4),'_dp, grady(6,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,4),'_dp, gradz(6,4), tol ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,5),'_dp, gradx(6,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,5),'_dp, grady(6,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,5),'_dp, gradz(6,5), tol ) '
  write(6,'(a,f20.10,a)') '! node6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,6),'_dp, gradx(6,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,6),'_dp, grady(6,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,6),'_dp, gradz(6,6), tol ) '
  write(6,'(a,f20.10,a)') '! node7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,7),'_dp, gradx(6,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,7),'_dp, grady(6,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,7),'_dp, gradz(6,7), tol ) '
  write(6,'(a,f20.10,a)') '! eqn 6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,8),'_dp, gradx(6,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,8),'_dp, grady(6,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,8),'_dp, gradz(6,8), tol ) '
  write(6,'(a,f20.10,a)') '! eqn 7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(7,3),'_dp, gradx(7,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(7,3),'_dp, grady(7,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(7,3),'_dp, gradz(7,3), tol ) '
  write(6,'(a,f20.10,a)') '! eqn 8'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(8,3),'_dp, gradx(8,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(8,3),'_dp, grady(8,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(8,3),'_dp, gradz(8,3), tol ) '
  write(6,'(a,f20.10,a)') '! eqn 9'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(9,3),'_dp, gradx(9,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(9,3),'_dp, grady(9,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(9,3),'_dp, gradz(9,3), tol ) '

! node1
assert_equal_within(        0.9375000000_dp, gradx(6,1), tol ) 
assert_equal_within(       -0.0401785714_dp, grady(6,1), tol ) 
assert_equal_within(       -0.8035714286_dp, gradz(6,1), tol ) 
! node2
assert_equal_within(       -1.0000000000_dp, gradx(6,2), tol ) 
assert_equal_within(        2.0000000000_dp, grady(6,2), tol ) 
assert_equal_within(       40.0000000000_dp, gradz(6,2), tol ) 
! node3
assert_equal_within(       -1.0000000000_dp, gradx(6,3), tol ) 
assert_equal_within(       -2.0000000000_dp, grady(6,3), tol ) 
assert_equal_within(       40.0000000000_dp, gradz(6,3), tol ) 
! node4
assert_equal_within(        0.9375000000_dp, gradx(6,4), tol ) 
assert_equal_within(        0.0401785714_dp, grady(6,4), tol ) 
assert_equal_within(       -0.8035714286_dp, gradz(6,4), tol ) 
! node5
assert_equal_within(       -1.0000000000_dp, gradx(6,5), tol ) 
assert_equal_within(        2.0000000000_dp, grady(6,5), tol ) 
assert_equal_within(      -40.0000000000_dp, gradz(6,5), tol ) 
! node6
assert_equal_within(       -1.0000000000_dp, gradx(6,6), tol ) 
assert_equal_within(       -2.0000000000_dp, grady(6,6), tol ) 
assert_equal_within(      -40.0000000000_dp, gradz(6,6), tol ) 
! node7
assert_equal_within(        0.9375000000_dp, gradx(6,7), tol ) 
assert_equal_within(       -0.0401785714_dp, grady(6,7), tol ) 
assert_equal_within(        0.8035714286_dp, gradz(6,7), tol ) 
! eqn 6
assert_equal_within(        0.9375000000_dp, gradx(6,8), tol ) 
assert_equal_within(        0.0401785714_dp, grady(6,8), tol ) 
assert_equal_within(        0.8035714286_dp, gradz(6,8), tol ) 
! eqn 7
assert_equal_within(       -2.0000000000_dp, gradx(7,3), tol ) 
assert_equal_within(       -1.0000000000_dp, grady(7,3), tol ) 
assert_equal_within(       40.0000000000_dp, gradz(7,3), tol ) 
! eqn 8
assert_equal_within(        0.0000000000_dp, gradx(8,3), tol ) 
assert_equal_within(        0.0000000000_dp, grady(8,3), tol ) 
assert_equal_within(        1.0000000000_dp, gradz(8,3), tol ) 
! eqn 9
assert_equal_within(       -2.0000000000_dp, gradx(9,3), tol ) 
assert_equal_within(       -2.0000000000_dp, grady(9,3), tol ) 
assert_equal_within(       40.0000000000_dp, gradz(9,3), tol ) 


!write(6,*) 'kloc = ', kloc
!write(6,*) 'wloc = ', wloc
!gradx(6,:) = gradx(7,:)
!grady(6,:) = grady(7,:)
!gradz(6,:) = gradz(7,:)

   do i = 6, n_grd
 write(*,*)
     write(6,'(i5,10(1x,f15.5))')  i,               &
     gradx(i,1), gradx(i,2), gradx(i,3), gradx(i,4) &
   , gradx(i,5), gradx(i,6), gradx(i,7), gradx(i,8)
     write(6,'(i5,10(1x,f15.5))')  i,               &
     grady(i,1), grady(i,2), grady(i,3), grady(i,4) &
   , grady(i,5), grady(i,6), grady(i,7), grady(i,8)
     write(6,'(i5,10(1x,f15.5))')  i,               &
     gradz(i,1), gradz(i,2), gradz(i,3), gradz(i,4) &
   , gradz(i,5), gradz(i,6), gradz(i,7), gradz(i,8)
   enddo

!=============================================================================80
!  3-equation edge_averaging
!=============================================================================80
! point = (/0.5_dp, 0.5_dp, 0.5_dp/)
  xmach = 1.5_dp
  re    = 0.02_dp
  model_strain_form_int = traceless
  n_sta           = 1 ! first location
  q_type          = 1 ! primitive
  eqn_set         = 0 ! compressible

  viscous_method            = 0 ! element
  diff_edge_avg_t            = 0 ! 1
  use_edge_gradients        = .false. ! .true.

  gradient_construction_rhs = 0
  t_diff1                   = 1
  t_diff2                   = 1
  nu(1)                     = 1.0_dp
  turb_res                  = 0.0_dp


    call get_diffusion_coefficients( turbulence_model_int, n_turb   &
                        , diff_const, diff_const2 )

      do ielem = 1, grid%nelem

       call turb_resid_diff_element ( n_sta, eqn_set, grid%nnodes0    &
               , turb, q_dof, turb_res                                &
               , grid%elem(ielem)%ncell                               &
               , grid%elem(ielem)%c2n, grid%x, grid%y, grid%z         &
               , grid%elem(ielem)%local_f2n                           &
               , grid%elem(ielem)%local_e2n                           &
               , grid%elem(ielem)%local_f2e                           &
               , grid%elem(ielem)%e2n_2d                              &
               , grid%elem(ielem)%face_per_cell                       &
               , grid%elem(ielem)%node_per_cell                       &
               , grid%elem(ielem)%edge_per_cell                       &
               , grid%elem(ielem)%type_cell                           &
               , n_turb, grid%elem(ielem)%face_2d                     &
               , grid%elem(ielem)%chk_norm                            &
               , turbulence_model_int, amut, diff_const, diff_const2  &
               , sst_f1 )

      end do

! do i = 1, 9
! write(6,'(10(1x,f20.10))')  turb_res(1,i),turb_res(2,i)
! enddo

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,1),'_dp,  turb_res(1,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,1),'_dp,  turb_res(2,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,1),'_dp,  turb_res(3,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(4,1),'_dp,  turb_res(4,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,2),'_dp,  turb_res(1,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,2),'_dp,  turb_res(2,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,2),'_dp,  turb_res(3,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(4,2),'_dp,  turb_res(4,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,3),'_dp,  turb_res(1,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,3),'_dp,  turb_res(2,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,3),'_dp,  turb_res(3,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(4,3),'_dp,  turb_res(4,3), tol ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,4),'_dp,  turb_res(1,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,4),'_dp,  turb_res(2,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,4),'_dp,  turb_res(3,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(4,4),'_dp,  turb_res(4,4), tol ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,5),'_dp,  turb_res(1,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,5),'_dp,  turb_res(2,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,5),'_dp,  turb_res(3,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(4,5),'_dp,  turb_res(4,5), tol ) '
  write(6,'(a,f20.10,a)') '! node6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,6),'_dp,  turb_res(1,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,6),'_dp,  turb_res(2,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,6),'_dp,  turb_res(3,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(4,6),'_dp,  turb_res(4,6), tol ) '
  write(6,'(a,f20.10,a)') '! node7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,7),'_dp,  turb_res(1,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,7),'_dp,  turb_res(2,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,7),'_dp,  turb_res(3,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(4,7),'_dp,  turb_res(4,7), tol ) '
  write(6,'(a,f20.10,a)') '! node8'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,8),'_dp,  turb_res(1,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,8),'_dp,  turb_res(2,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,8),'_dp,  turb_res(3,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(4,8),'_dp,  turb_res(4,8), tol ) '
  write(6,'(a,f20.10,a)') '! node9'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,9),'_dp,  turb_res(1,9), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,9),'_dp,  turb_res(2,9), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,9),'_dp,  turb_res(3,9), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(4,9),'_dp,  turb_res(4,9), tol ) '


! node1
assert_equal_within(       -4.3415542921_dp,  turb_res(1,1), tol ) 
assert_equal_within(       -6.6124209743_dp,  turb_res(2,1), tol ) 
assert_equal_within(     -135.8484195034_dp,  turb_res(3,1), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(4,1), tol ) 
! node2
assert_equal_within(        5.5669876336_dp,  turb_res(1,2), tol ) 
assert_equal_within(       -5.4319876330_dp,  turb_res(2,2), tol ) 
assert_equal_within(     -111.3397526723_dp,  turb_res(3,2), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(4,2), tol ) 
! node3
assert_equal_within(        5.5669876336_dp,  turb_res(1,3), tol ) 
assert_equal_within(        5.4319876330_dp,  turb_res(2,3), tol ) 
assert_equal_within(     -111.3397526723_dp,  turb_res(3,3), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(4,3), tol ) 
! node4
assert_equal_within(       -4.3415542921_dp,  turb_res(1,4), tol ) 
assert_equal_within(        6.6124209743_dp,  turb_res(2,4), tol ) 
assert_equal_within(     -135.8484195034_dp,  turb_res(3,4), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(4,4), tol ) 
! node5
assert_equal_within(        5.5669876336_dp,  turb_res(1,5), tol ) 
assert_equal_within(       -5.4319876330_dp,  turb_res(2,5), tol ) 
assert_equal_within(      111.3397526723_dp,  turb_res(3,5), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(4,5), tol ) 
! node6
assert_equal_within(        5.5669876336_dp,  turb_res(1,6), tol ) 
assert_equal_within(        5.4319876330_dp,  turb_res(2,6), tol ) 
assert_equal_within(      111.3397526723_dp,  turb_res(3,6), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(4,6), tol ) 
! node7
assert_equal_within(       -4.3415542921_dp,  turb_res(1,7), tol ) 
assert_equal_within(       -6.6124209743_dp,  turb_res(2,7), tol ) 
assert_equal_within(      135.8484195034_dp,  turb_res(3,7), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(4,7), tol ) 
! node8
assert_equal_within(       -4.3415542921_dp,  turb_res(1,8), tol ) 
assert_equal_within(        6.6124209743_dp,  turb_res(2,8), tol ) 
assert_equal_within(      135.8484195034_dp,  turb_res(3,8), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(4,8), tol ) 
! node9
assert_equal_within(       -4.9017333662_dp,  turb_res(1,9), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(2,9), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,9), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(4,9), tol ) 


  write(*,*)
  write(*,*) 'Finish part 1 test_turb_resid_diff_element 4eqn'
  write(*,*)

!----------------------------------------------------------------------
!----------------------------------------------------------------------

  call deallocate_elem ( grid%elem(1) )
! call deallocate_bc   ( bcsoln(1) )
  call deallocate_bc   ( grid%bc(1) )

  write(6,'(a,10(1x,f20.10))') 'fin test_turb_resid_diff 4eqn'

end test

!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80

test test_turb_resid_diff_sa

  use grid_types,    only : grid_type
! use soln_types,    only : soln_type
  use info_depr,     only : twod, grad_x_y_z_contents, use_edge_gradients
  use grids,         only : nullify_grid, allocate_grid, deallocate_grid
  use bc_types,      only : bcgrid_type, nullify_bc, allocate_dummy_bc      &
                          , deallocate_bc, bcsoln_type, allocate_bcsoln
  use element_types, only : elem_type
  use allocations,   only : my_alloc_ptr, my_realloc_ptr
  use element_defs,  only : nullify_elem, allocate_elem, initialize_elem    &
                          , deallocate_elem
  use element_defs,  only : max_node_per_cell, max_face_per_cell               &
                          , max_edge_per_cell 
  use element_defs,  only : local_f2n_pyr, local_f2e_pyr, local_e2n_pyr
  use element_defs,  only : local_f2n_hex, local_f2e_hex, local_e2n_hex
  use element_defs,  only : chk_norm_hex, chk_norm_pyr

  use fluid,         only : setup_fluid_gamma, setup_sutherland_constant
  use info_depr,     only : xmach, re
  use thermo,        only : q_type, primitive_q_type
  use bc_names,      only : twall
  use turbulence_info, only : model_strain_form_int, turbulence_model_int, &
                              sa_neg
  use debug_defs,    only : gradient_construction_rhs
  use nml_boundary_conditions, only : wall_velocity
  use turb_parameters, only : t_diff1, t_diff2
  use grid_metrics,    only : dualmetric
  use turb_util,       only : turbmultieqn_grad, kloc, wloc
  use debug_defs,      only : unified_diffusion, diff_edge_avg_t

  integer, parameter                 :: nedgeloc = 16
  integer, parameter                 :: n_grd    = 6
  integer, parameter                 :: nnodes0  = 9
  integer, parameter                 :: nnodes01 = 9
  integer, parameter                 :: n_tot    = 5
  integer, parameter                 :: n_turb   = 1
  integer, parameter                 :: nnz01    = 10

  type(grid_type)                    :: grid
! type(soln_type)                    :: soln
  real(dp),          dimension(n_tot ,nnodes01)  :: q_dof
  real(dp),          dimension(       nnodes01)  :: amut
  real(dp),          dimension(n_turb,nnodes01)  :: turb_res
  real(dp),          dimension(n_turb,nnodes01)  :: turb
  real(dp),          dimension(n_grd ,nnodes01)  :: gradx
  real(dp),          dimension(n_grd ,nnodes01)  :: grady
  real(dp),          dimension(n_grd ,nnodes01)  :: gradz
  real(dp),          dimension(       nnodes01)  :: sst_f1
  real(dp),          dimension(10             )  :: a, b, c, d
  real(dp),          dimension(       nnodes01)  :: nu
  real(dp),          dimension(nnodes01+1     )  :: ia
  real(dp),          dimension(nnz01          )  :: ja
  real(dp),          dimension(nnz01          )  :: nzg2m, g2m
  real(dp),          dimension(n_turb         )  :: diff_const
  real(dp),          dimension(n_turb         )  :: diff_const2

! type(bcgrid_type), dimension(1)    :: bc
  type(bcsoln_type), dimension(1)    :: bcsoln
  integer                            :: ib
! integer                            :: ibc
  integer                            :: n_sta
  integer                            :: nodes_per_face
  integer                            :: face_index
  integer                            :: eqn_set

  integer                            :: ielem, icell
  integer                            :: cell
  integer                            :: node1, node2
  integer                            :: njac
  integer                            :: viscous_method
  integer                            :: i, ie, j
  integer                            :: i1, i2, edge

  character(80)                      :: grid_motion
  real(dp), dimension(1,nnodes01)    :: res_gcl


  max_face_per_cell    = 6
  max_node_per_cell    = 8
  max_edge_per_cell    = 12
  eqn_set              = 0
  cell                 = 1
  njac                 = 5
  twod                 = .false.
  turbulence_model_int = sa_neg
  kloc                 = n_grd - n_turb + 1
  wloc                 = n_grd - n_turb + 1

  call setup_fluid_gamma
  call setup_sutherland_constant

  call nullify_grid ( grid )
  grid%nnodes0  = nnodes0
  grid%nnodes01 = nnodes01
  allocate ( grid%x( grid%nnodes01 ) )
  allocate ( grid%y( grid%nnodes01 ) )
  allocate ( grid%z( grid%nnodes01 ) )
  allocate ( grid%bc(1) )
  allocate ( grid%elem(2) )

  call allocate_bcsoln   ( 9, bcsoln(1) )

  call initialize_elem ( grid%elem(1), 'pyr' )
  call initialize_elem ( grid%elem(2), 'hex' )
  call allocate_elem ( grid%elem(2), .false. )

  grid%nelem                 = 2
  grid%nedge                 = nedgeloc
  grid%nedgeloc              = nedgeloc
  grid%nedgeloc_2d           = 0
  grid%elem(1)%edge_per_cell = 8
  grid%elem(2)%edge_per_cell = 12

  allocate ( grid%vol      (   grid%nnodes01 ) ) 
  allocate ( grid%dxdt     (   grid%nnodes01 ) ) 
  allocate ( grid%dydt     (   grid%nnodes01 ) ) 
  allocate ( grid%dzdt     (   grid%nnodes01 ) ) 
  allocate ( grid%facespeed(   grid%nedgeloc ) ) 
  allocate ( grid%eptr     ( 2,grid%nedgeloc ) ) 
  allocate ( grid%xn       (   grid%nedgeloc ) )
  allocate ( grid%yn       (   grid%nedgeloc ) )
  allocate ( grid%zn       (   grid%nedgeloc ) )
  allocate ( grid%ra       (   grid%nedgeloc ) )

  grid%x(1) = 0.0_dp; grid%y(1) = 0.0_dp; grid%z(1) = 0.0_dp
  grid%x(2) = 1.0_dp; grid%y(2) = 0.0_dp; grid%z(2) = 0.0_dp
  grid%x(3) = 1.0_dp; grid%y(3) = 1.0_dp; grid%z(3) = 0.0_dp
  grid%x(4) = 0.0_dp; grid%y(4) = 1.0_dp; grid%z(4) = 0.0_dp
  grid%x(5) = 1.0_dp; grid%y(5) = 0.0_dp; grid%z(5) = 5.0e-2_dp
  grid%x(6) = 1.0_dp; grid%y(6) = 1.0_dp; grid%z(6) = 5.0e-2_dp
  grid%x(7) = 0.0_dp; grid%y(7) = 0.0_dp; grid%z(7) = 5.0e-2_dp
  grid%x(8) = 0.0_dp; grid%y(8) = 1.0_dp; grid%z(8) = 5.0e-2_dp
  grid%x(9) =-0.5_dp; grid%y(9) = 0.5_dp; grid%z(9) = 2.5e-2_dp

  ib                  = 1
  grid%nbound         = 0 ! switch to 1 to test boundary gradients
  grid%bc(ib)%ibc     = 4110 ! viscous wall function boundary
  grid%bc(ib)%nbnode  = 5 ! number of boundary nodes
  grid%bc(ib)%nbfacet = 1
  grid%bc(ib)%nbfaceq = 1

  ! account for one triangle face and one quad face
  allocate ( grid%bc(ib)%ibnode( grid%bc(ib)%nbnode) )
  allocate ( grid%bc(ib)%f2ntb ( grid%bc(ib)%nbfacet, 5 ) )
  allocate ( grid%bc(ib)%f2nqb ( grid%bc(ib)%nbfaceq, 6 ) )
  allocate ( bcsoln(ib)%mu_t_wf ( grid%bc(ib)%nbnode ) )
  allocate ( bcsoln(ib)%omega_wf ( grid%bc(ib)%nbnode ) )
  allocate ( grid%elem(1)%c2n    (  5, 1 ) )
  allocate ( grid%elem(2)%c2n    (  8, 1 ) )
  allocate ( grid%elem(1)%c2e    (  8, 1 ) )
  allocate ( grid%elem(2)%c2e    ( 12, 1 ) )
  allocate ( grid%elem(1)%e2n_2d (  4, 2 ) )
  allocate ( grid%elem(2)%e2n_2d (  4, 2 ) )

  wall_velocity(1,:) = (/0.3_dp,0.2_dp,0.1_dp/)

  face_index     = 1
  nodes_per_face = 3
  grid%bc(ib)%f2ntb(face_index,1) = 1 ! 9 ! face 1, node 1
  grid%bc(ib)%f2ntb(face_index,2) = 5 ! 4 ! face 1, node 2
  grid%bc(ib)%f2ntb(face_index,3) = 4 ! 1 ! face 1, node 3
  grid%bc(ib)%f2ntb(face_index,4) = 1 ! cell number
  grid%bc(ib)%f2ntb(face_index,5) = 1 ! element type
  grid%bc(ib)%nbfacet             = 1 ! number of tri faces

  face_index     = 1
  nodes_per_face = 4
  grid%bc(ib)%f2nqb(face_index,1) = 1 ! 1 ! face 1, node 1
  grid%bc(ib)%f2nqb(face_index,2) = 4 ! 2 ! face 1, node 2
  grid%bc(ib)%f2nqb(face_index,3) = 3 ! 3 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,4) = 2 ! 4 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,5) = 1 ! cell number
  grid%bc(ib)%f2nqb(face_index,6) = 2 ! element type
  grid%bc(ib)%nbfaceq             = 1 ! number of quad faces

  grid%bc(ib)%ibnode(1) = 1 ! boundary node
  grid%bc(ib)%ibnode(2) = 2 ! boundary node
  grid%bc(ib)%ibnode(3) = 3 ! boundary node
  grid%bc(ib)%ibnode(4) = 4 ! boundary node
  grid%bc(ib)%ibnode(5) = 9 ! boundary node

  grid%elem(1)%ncell       = 1
  cell                     = 1
  grid%elem(1)%c2n(1,cell) = 7 ! map of cell 1 nodes
  grid%elem(1)%c2n(2,cell) = 8
  grid%elem(1)%c2n(3,cell) = 4
  grid%elem(1)%c2n(4,cell) = 1
  grid%elem(1)%c2n(5,cell) = 9
  grid%elem(1)%node_per_cell = 5
  grid%elem(1)%c2e(1,cell) = 16 ! map of cell 1 edges
  grid%elem(1)%c2e(2,cell) = 15
  grid%elem(1)%c2e(3,cell) = 13
  grid%elem(1)%c2e(4,cell) = 12
  grid%elem(1)%c2e(5,cell) = 11
  grid%elem(1)%c2e(6,cell) = 14
  grid%elem(1)%c2e(7,cell) = 8
  grid%elem(1)%c2e(8,cell) = 9
  grid%elem(1)%local_f2n   = local_f2n_pyr
  grid%elem(1)%local_f2e   = local_f2e_pyr
  grid%elem(1)%local_e2n   = local_e2n_pyr
  grid%elem(1)%chk_norm    = chk_norm_pyr

  grid%elem(2)%ncell       = 1
  cell                     = 1
  grid%elem(2)%c2n(1,cell) = 1
  grid%elem(2)%c2n(2,cell) = 7
  grid%elem(2)%c2n(3,cell) = 2
  grid%elem(2)%c2n(4,cell) = 5
  grid%elem(2)%c2n(5,cell) = 4
  grid%elem(2)%c2n(6,cell) = 8
  grid%elem(2)%c2n(7,cell) = 3
  grid%elem(2)%c2n(8,cell) = 6
  grid%elem(2)%node_per_cell = 8
  grid%elem(2)%c2e(1 ,cell) = 2
  grid%elem(2)%c2e(2 ,cell) = 3
  grid%elem(2)%c2e(3 ,cell) = 1
  grid%elem(2)%c2e(4 ,cell) = 5
  grid%elem(2)%c2e(5 ,cell) = 4
  grid%elem(2)%c2e(6 ,cell) = 10
  grid%elem(2)%c2e(7 ,cell) = 7
  grid%elem(2)%c2e(8 ,cell) = 6
  grid%elem(2)%c2e(9 ,cell) = 9
  grid%elem(2)%c2e(10,cell) = 8
  grid%elem(2)%c2e(11,cell) = 11
  grid%elem(2)%c2e(12,cell) = 14
  grid%elem(2)%local_f2n   = local_f2n_hex
  grid%elem(2)%local_f2e   = local_f2e_hex
  grid%elem(2)%local_e2n   = local_e2n_hex
  grid%elem(2)%chk_norm    = chk_norm_hex

!----------------------------------------------------------------------
!------------------ allocate and compute eprt -------------------------
!----------------------------------------------------------------------

    allocate(grid%eptr(2,grid%nedge)); grid%eptr = 0
    allocate(grid%el2g(grid%nedge));   grid%el2g = 0
    allocate(grid%l2g(grid%nedge));    grid%l2g = 0
    do i = 1, grid%nnodes01
      grid%l2g(i) = i
    enddo

    do ielem = 1,grid%nelem
       i1 = grid%elem(ielem)%edge_per_cell
       i2 = grid%elem(ielem)%ncell
       allocate(grid%elem(ielem)%c2e(i1,i2)); grid%elem(ielem)%c2e = 0
    end do

    edge = 0
    do ielem  = 1,grid%nelem
       do icell = 1,grid%elem(ielem)%ncell
          loop_ie: do ie = 1,grid%elem(ielem)%edge_per_cell
             i = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
             j = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)
             node1 = min(i,j)
             node2 = max(i,j)
             do i = 1,edge
                if ((grid%eptr(1,i)==node1).and.(grid%eptr(2,i)==node2)) then 
                   grid%elem(ielem)%c2e(ie,icell) = i
                   cycle loop_ie
                end if
             end do
             edge = edge + 1
             grid%el2g(edge) = edge 
             grid%eptr(1,edge) = node1
             grid%eptr(2,edge) = node2
             grid%elem(ielem)%c2e(ie,icell) = edge 
         end do loop_ie
      end do
    end do
    grid%nedge    = edge 
    grid%nedgeloc = edge 
    grid%nedgeg   = edge 


!----------------------------------------------------------------------
!----------------------------------------------------------------------

  grid%xn(:) = 0._dp
  grid%yn(:) = 0._dp
  grid%zn(:) = 0._dp
  grid%ra(:) = 0._dp

  do ielem = 1, grid%nelem

    call dualmetric( grid%x, grid%y, grid%z, grid%dxdt, grid%dydt, grid%dzdt &
                   , grid%xn, grid%yn, grid%zn, grid%eptr                    &
                   , grid%facespeed, grid%nnodes01, grid%nedgeloc            &
                   , grid%elem(ielem)%ncell, grid%elem(ielem)%ncell          &
                   , grid%elem(ielem)%c2n, grid%elem(ielem)%c2e              &
                   , grid%elem(ielem)%node_per_cell                          &
                   , grid%elem(ielem)%edge_per_cell                          &
                   , grid%elem(ielem)%local_e2n, grid%vol, grid%ra           &
                   , grid_motion, res_gcl, ielem, grid%nelem )

  end do

!write(6,'(16(1x,f15.5))') grid%xn
!write(6,'(16(1x,f15.5))') grid%yn
!write(6,'(16(1x,f15.5))') grid%zn
!write(6,'(16(1x,f15.5))') grid%ra
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!                       Manufactured solution
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
  a(1)=0; b(1)=0; c(1)=0; d(1)=1
  a(2)=0; b(2)=0; c(2)=0; d(2)=0
  a(3)=0; b(3)=0; c(3)=0; d(3)=0
  a(4)=0; b(4)=0; c(4)=0; d(4)=0
  a(5)=1; b(5)=1; c(5)=1; d(5)=1

  a(6)=1; b(6)=0; c(6)=0; d(6)=0

  a(7)=0; b(7)=0; c(7)=0; d(7)=1

  do i = 1, 9
    q_dof(1,i) = a(1)*grid%x(i) + b(1)*grid%y(i) + c(1)*grid%z(i) + d(1)
    q_dof(2,i) = a(2)*grid%x(i) + b(2)*grid%y(i) + c(2)*grid%z(i) + d(2)
    q_dof(3,i) = a(3)*grid%x(i) + b(3)*grid%y(i) + c(3)*grid%z(i) + d(3)
    q_dof(4,i) = a(4)*grid%x(i) + b(4)*grid%y(i) + c(4)*grid%z(i) + d(4)
    q_dof(5,i) = a(5)*grid%x(i) + b(5)*grid%y(i) + c(5)*grid%z(i) + d(5)
    turb(1,i)  = a(6)*grid%x(i) + b(6)*grid%y(i) + c(6)*grid%z(i) + d(6)
    amut(i)    = a(7)*grid%x(i) + b(7)*grid%y(i) + c(7)*grid%z(i) + d(7)
    sst_f1(i)  = 0.00_dp
  enddo

!----------------------------------------------------------------------
  gradx = 0.0_dp
  grady = 0.0_dp
  gradz = 0.0_dp

  call turbmultieqn_grad( grid%nnodes0, grid%nnodes01, grid%nedgeloc               &
                        , grid%nedgeloc_2d, grid%nnodes0_2d                        &
                        , grid%node_pairs_2d, grid%eptr, turb, grid%x, grid%y      &
                        , grid%z, grid%xn, grid%yn, grid%zn, grid%ra, grid%vol     &
                        , gradx, grady, gradz, grid%nbound, grid%bc, n_turb, n_grd &
                        , grid%nelem, grid%elem                                    &
                        , n_tot, q_dof, turbulence_model_int )

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,1),'_dp, gradx(6,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,1),'_dp, grady(6,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,1),'_dp, gradz(6,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,2),'_dp, gradx(6,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,2),'_dp, grady(6,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,2),'_dp, gradz(6,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,3),'_dp, gradx(6,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,3),'_dp, grady(6,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,3),'_dp, gradz(6,3), tol ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,4),'_dp, gradx(6,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,4),'_dp, grady(6,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,4),'_dp, gradz(6,4), tol ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,5),'_dp, gradx(6,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,5),'_dp, grady(6,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,5),'_dp, gradz(6,5), tol ) '
  write(6,'(a,f20.10,a)') '! node6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,6),'_dp, gradx(6,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,6),'_dp, grady(6,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,6),'_dp, gradz(6,6), tol ) '
  write(6,'(a,f20.10,a)') '! node7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,7),'_dp, gradx(6,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,7),'_dp, grady(6,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,7),'_dp, gradz(6,7), tol ) '
  write(6,'(a,f20.10,a)') '! eqn 6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradx(6,8),'_dp, gradx(6,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',grady(6,8),'_dp, grady(6,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',gradz(6,8),'_dp, gradz(6,8), tol ) '

! node1
assert_equal_within(        0.9375000000_dp, gradx(6,1), tol ) 
assert_equal_within(       -0.0401785714_dp, grady(6,1), tol ) 
assert_equal_within(       -0.8035714286_dp, gradz(6,1), tol ) 
! node2
assert_equal_within(       -1.0000000000_dp, gradx(6,2), tol ) 
assert_equal_within(        2.0000000000_dp, grady(6,2), tol ) 
assert_equal_within(       40.0000000000_dp, gradz(6,2), tol ) 
! node3
assert_equal_within(       -1.0000000000_dp, gradx(6,3), tol ) 
assert_equal_within(       -2.0000000000_dp, grady(6,3), tol ) 
assert_equal_within(       40.0000000000_dp, gradz(6,3), tol ) 
! node4
assert_equal_within(        0.9375000000_dp, gradx(6,4), tol ) 
assert_equal_within(        0.0401785714_dp, grady(6,4), tol ) 
assert_equal_within(       -0.8035714286_dp, gradz(6,4), tol ) 
! node5
assert_equal_within(       -1.0000000000_dp, gradx(6,5), tol ) 
assert_equal_within(        2.0000000000_dp, grady(6,5), tol ) 
assert_equal_within(      -40.0000000000_dp, gradz(6,5), tol ) 
! node6
assert_equal_within(       -1.0000000000_dp, gradx(6,6), tol ) 
assert_equal_within(       -2.0000000000_dp, grady(6,6), tol ) 
assert_equal_within(      -40.0000000000_dp, gradz(6,6), tol ) 
! node7
assert_equal_within(        0.9375000000_dp, gradx(6,7), tol ) 
assert_equal_within(       -0.0401785714_dp, grady(6,7), tol ) 
assert_equal_within(        0.8035714286_dp, gradz(6,7), tol ) 
! eqn 6
assert_equal_within(        0.9375000000_dp, gradx(6,8), tol ) 
assert_equal_within(        0.0401785714_dp, grady(6,8), tol ) 
assert_equal_within(        0.8035714286_dp, gradz(6,8), tol ) 


!write(6,*) 'kloc = ', kloc
!write(6,*) 'wloc = ', wloc
!gradx(6,:) = gradx(7,:)
!grady(6,:) = grady(7,:)
!gradz(6,:) = gradz(7,:)

   do i = 6, n_grd
 write(*,*)
     write(6,'(i5,10(1x,f15.5))')  i,               &
     gradx(i,1), gradx(i,2), gradx(i,3), gradx(i,4) &
   , gradx(i,5), gradx(i,6), gradx(i,7), gradx(i,8)
     write(6,'(i5,10(1x,f15.5))')  i,               &
     grady(i,1), grady(i,2), grady(i,3), grady(i,4) &
   , grady(i,5), grady(i,6), grady(i,7), grady(i,8)
     write(6,'(i5,10(1x,f15.5))')  i,               &
     gradz(i,1), gradz(i,2), gradz(i,3), gradz(i,4) &
   , gradz(i,5), gradz(i,6), gradz(i,7), gradz(i,8)
   enddo

!=============================================================================80
write(*,*) '1-equation edge_averaging'
!=============================================================================80
! point = (/0.5_dp, 0.5_dp, 0.5_dp/)
  xmach = 1.5_dp
  re    = 0.02_dp
  model_strain_form_int = traceless
  n_sta           = 1 ! first location
  q_type          = 1 ! primitive
  eqn_set         = 0 ! compressible

  viscous_method            = 0 ! element
  diff_edge_avg_t            = 1 ! 0 ! 1
  use_edge_gradients        = .true. ! .false. ! .true.

  gradient_construction_rhs = 0
  t_diff1                   = 1
  t_diff2                   = 1
  nu                        = 0.0_dp
  turb_res                  = 0.0_dp


    call get_diffusion_coefficients( turbulence_model_int, n_turb     &
                        , diff_const, diff_const2 )

      do ielem = 1, grid%nelem

       call turb_resid_diff_element ( n_sta, eqn_set, grid%nnodes0    &
               , turb, q_dof, turb_res                                &
               , grid%elem(ielem)%ncell                               &
               , grid%elem(ielem)%c2n, grid%x, grid%y, grid%z         &
               , grid%elem(ielem)%local_f2n                           &
               , grid%elem(ielem)%local_e2n                           &
               , grid%elem(ielem)%local_f2e                           &
               , grid%elem(ielem)%e2n_2d                              &
               , grid%elem(ielem)%face_per_cell                       &
               , grid%elem(ielem)%node_per_cell                       &
               , grid%elem(ielem)%edge_per_cell                       &
               , grid%elem(ielem)%type_cell                           &
               , n_turb, grid%elem(ielem)%face_2d                     &
               , grid%elem(ielem)%chk_norm                            &
               , turbulence_model_int, amut, diff_const, diff_const2  &
               , sst_f1 )

!       call turb_jacob_diff_element( n_sta, eqn_set, grid%nnodes0   &
!          , turb, q_dof, nu, a_diag, a_off  &
!          , grid%elem(ielem)%ncell          &
!          , grid%elem(ielem)%c2n            &
!          , grid%x                          &
!          , grid%y                          &
!          , grid%z                          &
!          , grid%elem(ielem)%type_cell      &
!          , grid%elem(ielem)%local_f2n      &
!          , grid%elem(ielem)%local_e2n      &
!          , grid%elem(ielem)%local_f2e      &
!          , grid%elem(ielem)%e2n_2d         &
!          , grid%elem(ielem)%face_per_cell  &
!          , grid%elem(ielem)%node_per_cell  &
!          , grid%elem(ielem)%edge_per_cell  &
!          , ia                              &
!          , ja                              &
!          , n_turb                          &
!          , grid%elem(ielem)%face_2d        &
!          , grid%elem(ielem)%chk_norm       &
!          , nzg2m                           &
!          , g2m,                            &
!          , turbulence_model                &
!          , amut,                           &
!          , diff_const                      &
!          , diff_const2                     &
!          , sst_f1 )

!       call turb_resid_diff(eqn_set, viscous_method                                &
!            , grid%nnodes0, grid%nnodes01                                          &
!            , grid%nedgeloc, grid%eptr                                             &
!            , turb, q_dof, turb_res, gradx, grady, gradz                           &
!            , grid%xn, grid%yn, grid%zn, grid%ra, ielem, grid%elem(ielem)%ncell    &
!            , grid%elem(ielem)%c2n, grid%elem(ielem)%c2e, grid%x, grid%y, grid%z   &
!            , grid%elem(ielem)%local_f2n, grid%elem(ielem)%local_e2n               &
!            , grid%elem(ielem)%local_f2e, grid%elem(ielem)%e2n_2d                  &
!            , grid%nedgeloc_2d, grid%elem(ielem)%face_per_cell                     &
!            , grid%elem(ielem)%node_per_cell                                       &
!            , grid%elem(ielem)%edge_per_cell, grid%elem(ielem)%type_cell           &
!            , n_turb, n_tot, n_grd, grid%elem(ielem)%face_2d, amut                 &
!            , sst_f1, grid%elem(ielem)%chk_norm )
      end do

! do i = 1, 9
! write(6,'(10(1x,f20.10))')  turb_res(1,i),turb_res(2,i)
! enddo

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,1),'_dp,  turb_res(1,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,2),'_dp,  turb_res(1,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,3),'_dp,  turb_res(1,3), tol ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,4),'_dp,  turb_res(1,4), tol ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,5),'_dp,  turb_res(1,5), tol ) '
  write(6,'(a,f20.10,a)') '! node6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,6),'_dp,  turb_res(1,6), tol ) '
  write(6,'(a,f20.10,a)') '! node7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,7),'_dp,  turb_res(1,7), tol ) '
  write(6,'(a,f20.10,a)') '! node8'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,8),'_dp,  turb_res(1,8), tol ) '
  write(6,'(a,f20.10,a)') '! node9'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,9),'_dp,  turb_res(1,9), tol ) '


! node1
assert_equal_within(       -4.9679817624_dp,  turb_res(1,1), tol ) 
! node2
assert_equal_within(        4.6930623516_dp,  turb_res(1,2), tol ) 
! node3
assert_equal_within(        9.4872481954_dp,  turb_res(1,3), tol ) 
! node4
assert_equal_within(       -9.0613354220_dp,  turb_res(1,4), tol ) 
! node5
assert_equal_within(        4.9029033030_dp,  turb_res(1,5), tol ) 
! node6
assert_equal_within(        9.7566927521_dp,  turb_res(1,6), tol ) 
! node7
assert_equal_within(       -5.1485484727_dp,  turb_res(1,7), tol ) 
! node8
assert_equal_within(       -9.2901072360_dp,  turb_res(1,8), tol ) 
! node9
assert_equal_within(       -4.4537882998_dp,  turb_res(1,9), tol ) 


  write(*,*)
  write(*,*) 'Finish part 3 test_turb_resid_diff_element_sa'
  write(*,*)

!----------------------------------------------------------------------
!=============================================================================80
write(*,*) '1-equation cell_averaging'
!=============================================================================80
! point = (/0.5_dp, 0.5_dp, 0.5_dp/)
  xmach = 1.5_dp
  re    = 0.02_dp
  model_strain_form_int = traceless
  n_sta           = 1 ! first location
  q_type          = 1 ! primitive
  eqn_set         = 0 ! compressible

  viscous_method            = 0 ! element
  diff_edge_avg_t            = 0 ! 1
  use_edge_gradients        = .false. ! .true.

  gradient_construction_rhs = 0
  t_diff1                   = 1
  t_diff2                   = 1
  nu                        = 0.0_dp
  turb_res                  = 0.0_dp


    call get_diffusion_coefficients( turbulence_model_int, n_turb     &
                        , diff_const, diff_const2 )

      do ielem = 1, grid%nelem

       call turb_resid_diff_element ( n_sta, eqn_set, grid%nnodes0    &
               , turb, q_dof, turb_res                                &
               , grid%elem(ielem)%ncell                               &
               , grid%elem(ielem)%c2n, grid%x, grid%y, grid%z         &
               , grid%elem(ielem)%local_f2n                           &
               , grid%elem(ielem)%local_e2n                           &
               , grid%elem(ielem)%local_f2e                           &
               , grid%elem(ielem)%e2n_2d                              &
               , grid%elem(ielem)%face_per_cell                       &
               , grid%elem(ielem)%node_per_cell                       &
               , grid%elem(ielem)%edge_per_cell                       &
               , grid%elem(ielem)%type_cell                           &
               , n_turb, grid%elem(ielem)%face_2d                     &
               , grid%elem(ielem)%chk_norm                            &
               , turbulence_model_int, amut, diff_const, diff_const2  &
               , sst_f1 )

!       call turb_jacob_diff_element( n_sta, eqn_set, grid%nnodes0   &
!          , turb, q_dof, nu, a_diag, a_off  &
!          , grid%elem(ielem)%ncell          &
!          , grid%elem(ielem)%c2n            &
!          , grid%x                          &
!          , grid%y                          &
!          , grid%z                          &
!          , grid%elem(ielem)%type_cell      &
!          , grid%elem(ielem)%local_f2n      &
!          , grid%elem(ielem)%local_e2n      &
!          , grid%elem(ielem)%local_f2e      &
!          , grid%elem(ielem)%e2n_2d         &
!          , grid%elem(ielem)%face_per_cell  &
!          , grid%elem(ielem)%node_per_cell  &
!          , grid%elem(ielem)%edge_per_cell  &
!          , ia                              &
!          , ja                              &
!          , n_turb                          &
!          , grid%elem(ielem)%face_2d        &
!          , grid%elem(ielem)%chk_norm       &
!          , nzg2m                           &
!          , g2m,                            &
!          , turbulence_model                &
!          , amut,                           &
!          , diff_const                      &
!          , diff_const2                     &
!          , sst_f1 )

!       call turb_resid_diff(eqn_set, viscous_method                                &
!            , grid%nnodes0, grid%nnodes01                                          &
!            , grid%nedgeloc, grid%eptr                                             &
!            , turb, q_dof, turb_res, gradx, grady, gradz                           &
!            , grid%xn, grid%yn, grid%zn, grid%ra, ielem, grid%elem(ielem)%ncell    &
!            , grid%elem(ielem)%c2n, grid%elem(ielem)%c2e, grid%x, grid%y, grid%z   &
!            , grid%elem(ielem)%local_f2n, grid%elem(ielem)%local_e2n               &
!            , grid%elem(ielem)%local_f2e, grid%elem(ielem)%e2n_2d                  &
!            , grid%nedgeloc_2d, grid%elem(ielem)%face_per_cell                     &
!            , grid%elem(ielem)%node_per_cell                                       &
!            , grid%elem(ielem)%edge_per_cell, grid%elem(ielem)%type_cell           &
!            , n_turb, n_tot, n_grd, grid%elem(ielem)%face_2d, amut                 &
!            , sst_f1, grid%elem(ielem)%chk_norm )
      end do

! do i = 1, 9
! write(6,'(10(1x,f20.10))')  turb_res(1,i),turb_res(2,i)
! enddo

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,1),'_dp,  turb_res(1,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,2),'_dp,  turb_res(1,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,3),'_dp,  turb_res(1,3), tol ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,4),'_dp,  turb_res(1,4), tol ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,5),'_dp,  turb_res(1,5), tol ) '
  write(6,'(a,f20.10,a)') '! node6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,6),'_dp,  turb_res(1,6), tol ) '
  write(6,'(a,f20.10,a)') '! node7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,7),'_dp,  turb_res(1,7), tol ) '
  write(6,'(a,f20.10,a)') '! node8'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,8),'_dp,  turb_res(1,8), tol ) '
  write(6,'(a,f20.10,a)') '! node9'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,9),'_dp,  turb_res(1,9), tol ) '

! node1
assert_equal_within(       -6.7911525743_dp,  turb_res(1,1), tol ) 
! node2
assert_equal_within(        7.2099766505_dp,  turb_res(1,2), tol ) 
! node3
assert_equal_within(        7.2099766505_dp,  turb_res(1,3), tol ) 
! node4
assert_equal_within(       -6.7911525743_dp,  turb_res(1,4), tol ) 
! node5
assert_equal_within(        7.2099766505_dp,  turb_res(1,5), tol ) 
! node6
assert_equal_within(        7.2099766505_dp,  turb_res(1,6), tol ) 
! node7
assert_equal_within(       -6.7911525743_dp,  turb_res(1,7), tol ) 
! node8
assert_equal_within(       -6.7911525743_dp,  turb_res(1,8), tol ) 
! node9
assert_equal_within(       -5.7571508955_dp,  turb_res(1,9), tol ) 


  write(*,*)
  write(*,*) 'Finish part 4 test_turb_resid_diff_element_sa'
  write(*,*)

!----------------------------------------------------------------------
!----------------------------------------------------------------------

  call deallocate_elem ( grid%elem(1) )
! call deallocate_bc   ( bcsoln(1) )
  call deallocate_bc   ( grid%bc(1) )

  write(6,'(a,10(1x,f20.10))') 'fin test_turb_resid_diff'

end test

end test_suite

DIST_SUBDIRS =

LIBCORE_DIR=@libcore_path@
LIBTURB_DIR=@libturb_path@
LIBSMEMRD_DIR=@top_builddir@/libsmemrd
LIBDDFB_DIR=@top_builddir@/libddfb
FUNCLIB_DIR=@top_builddir@/FuncLib90
LIBF90_DIR=@top_builddir@/LibF90
LIBDEFS_DIR=@top_builddir@/libdefs
LIBINIT_DIR=@top_builddir@/libinit
PHYSICS_DIR=@top_builddir@/@PHYSICS_TYPE@
PHYSICS_DEPS_DIR=@top_builddir@/PHYSICS_DEPS
ENGINESIM_DIR=@top_builddir@/enginesim/src
FUN3D_90_DIR=@top_builddir@/FUN3D_90

include Common.am

noinst_LIBRARIES = libFUN3DFlow.a

if BUILD_FCCHT_SUPPORT
bin_PROGRAMS = 
else
bin_PROGRAMS = nodet@MPI_EXT@
endif

libFUN3DFlow_a_LIBADD = 
libFUN3DFlow_a_SOURCES = $(libFUN3DFlow_SRCS)
libFUN3DFlow_a_LINK = $(F90LINK)

nodet@MPI_EXT@_LDADD = $(nodet_LDSTUFF)
nodet@MPI_EXT@_SOURCES = $(nodet_SRCS)

EXTRA_DIST = \
	fun3d.nml \
	grid_measures.input \
	moving_body.input \
	perturb.input \
	pressure_box.input \
	profiles.input \
	move_relaxation.schedule \
	remove_boundaries_from_force_totals

# Install Complex executable if it exists
install-exec-local:
	@$(COMMON_INSTALL_HOOK)
	if test -e Complex/complex_nodet@MPI_EXT@; then \
	  (cd Complex; $(MAKE) install) \
	else :; fi;

uninstall-local:
	@$(COMMON_UNINSTALL_HOOK)
	if test -e Complex/complex_nodet@MPI_EXT@; then \
	  (cd Complex; $(MAKE) uninstall) \
	else :; fi;

if BUILD_COMPLEX
DIST_SUBDIRS += Complex
endif

%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-I $(LIBDDFB_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-I $(PHYSICS_DEPS_DIR) \
	-L $(top_srcdir)/FuncLib90 \
	-L $(LIBTURB_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-I $(LIBDDFB_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-I $(PHYSICS_DEPS_DIR) \
	-L $(top_srcdir)/FuncLib90 \
	-L $(LIBTURB_DIR) > $@

#all-local:	@echo $(lib_MODULES)

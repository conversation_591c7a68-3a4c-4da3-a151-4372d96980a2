module solution_types

  use kinddefs,      only : dp, jp, odp, dqp, krp, i1, lup
  use force_types,   only : force_type
  use comprow_types, only : crow_flow
  use interp_defs,   only : sendrecv_type
  use bc_types,      only : bcsoln_type

  implicit none

  private

  public :: soln_type
  public :: eqn_group_type
  public :: gmres_data_type

  public :: global_bndry_type, global_slice_type
  public :: nullify_global_bndry_data

  public :: linear_projection_type

  public :: compressible, incompressible, generic_gas, elasticity, elliptic

 ! eqn_set enumeration

  integer, parameter :: compressible   =  0
  integer, parameter :: incompressible =  1
  integer, parameter :: generic_gas    =  2
  integer, parameter :: elasticity     = -1
  integer, parameter :: elliptic       = -2

  type soln_type

!-------------------------------------------------------------------------------
!   The global variables defined below can be set by calling :
!
!   subroutine set_global_scalars(soln, eqn_set_selected)
!
!   where soln is this derived type and eqn_set_selected is
!   an optional argument.   The descriptions below reflect
!   almost nothing in terms of consistency.  Perhaps some
!   convention will emerge at some point...otherwise
!   caveat emptor.

    integer :: neq0     ! Total on-processor Q values
    integer :: neq01    ! Total on-processor Q values

    integer :: dof0     !Total solved on-processor Q values
    integer :: dofg     !Total solved Q values

    integer :: eqn_set ! equation set path (see enumeration above)

    integer :: n_q     ! number of equations solved at each field point
                       ! =5+n_turb : PerfectGas
                       ! =4+n_turb : Incompressible
                       ! =3        : Elasticity
                       ! =1        : Elliptic
                       ! =ndim_g   : GenericGas

    integer :: n_tot   ! Number of variables in %q_dof
                       ! = ndim + number of auxiliary variables
                       ! ndim defined below
                       ! auxiliary variables varies across eqn_set paths

    integer :: ndim    ! Number of equations : first extent of %q_dof
                       ! =5      : PerfectGas     + loosely coupled turbulence
                       ! =5      : PerfectGas     + tightly coupled Spalart
                       ! =4      : Incompressible + loosely coupled turbulence
                       ! =3      : Elasticity
                       ! =1      : Elliptic
                       ! =ndim_g : GenericGas

    integer :: n_grd   ! Number of gradients in %gradx/grady/gradz
                       ! varies across eqn_set/viscous paths
                       ! increment by 1 to hold jacobian info : GenericGas

    integer :: n_turb  ! Number of turbulence equations in non GenericGas path

    integer :: adim    ! Total num of variables (used for Adjoint path %dq/%res)
                       ! leading dimension of %dq
                       ! leading dimension of %res either njac or adim
                       ! = ndim + n_turb

    integer :: njac    ! Number of jacobians
                       ! leading dimension of %dq and %res in primal path
                       ! = ndim          usually
                       ! = ndim + n_turb if tightly coupled and (eqn_set=0/1)

    logical :: triaged_residual  = .false.

    integer :: viscous_method ! treatment of viscous terms
                       ! =  0  : Greeen Gauss (requires elements)
                       ! =  1  : Edge-Based (averaged-LSQ +  edge-terms)
                       ! =  2  : Edge-Based (edge-terms-only aka thin-layer)
                       ! =  3  : Face-Based LSQ
    integer :: viscous_method_lhs !treatment of viscous terms on LHS

!-------------------------------------------------------------------------------
!   Termination of discussion about global variables.
!-------------------------------------------------------------------------------

    integer                                :: max_nnz        ! = crow%nnz0
    integer                                :: n_eqn_groups
    integer                                :: time1          ! wallclock time
    integer                                :: time2          ! wallclock time

    logical :: jacobians_updated = .false.

    integer,     dimension(:),     pointer :: node_bc        ! Strong BC at node

    real(dp),    dimension(:,:),   pointer :: q_dof          ! Variables @ dofs
    real(dp),    dimension(:,:),   pointer :: qavg           ! Variables @ nodes
    real(dp),    dimension(:,:),   pointer :: qtavg          ! T vars @ nodes
    real(dp),    dimension(:,:),   pointer :: qbc_na         ! Variables @ bc
                                                             ! node-averaging
    real(dp),    dimension(:,:),   pointer :: qt_face        ! QT bc variables
    integer(i1), dimension(:),     pointer :: qbc_na_loc     ! loc_indicator
    real(dp),    dimension(:,:,:), pointer :: enthalpy_ij
    real(dp),    dimension(:,:,:), pointer :: pressure_jac
    real(dp),    dimension(:,:),   pointer :: gradx          ! Least-Square
    real(dp),    dimension(:,:),   pointer :: grady          ! Gradients
    real(dp),    dimension(:,:),   pointer :: gradz

    real(dp),    dimension(:,:),   pointer :: vgradx         ! Velocity
    real(dp),    dimension(:,:),   pointer :: vgrady         ! Gradients
    real(dp),    dimension(:,:),   pointer :: vgradz         ! cc path

    real(dp),    dimension(:),     pointer :: cdt            ! Local time step
    real(dp),    dimension(:,:),   pointer :: phi            ! Flux limiter
    real(dp),    dimension(:),     pointer :: flux_efixc     ! Flux entrpy fix
    real(dp),    dimension(:),     pointer :: cell_re        ! cell_re
    real(dp),    dimension(:),     pointer :: dtau           ! LHS
    logical,     dimension(:,:),   pointer :: dtau_term      ! LHS
    real(dp),    dimension(:,:,:), pointer :: a_diag         ! LHS
    real(jp),    dimension(:,:,:), pointer :: a_diag_lu      ! LHS
    real(odp),   dimension(:,:,:), pointer :: a_off          ! LHS
    integer(i1), dimension(:,:),   pointer :: pivot_lu       ! LHS
    real(dp),    dimension(:,:,:), pointer :: a_turb_diag    ! LHS
    real(jp),    dimension(:,:,:), pointer :: a_turb_diag_lu ! LHS
    real(odp),   dimension(:,:,:), pointer :: a_turb_off     ! LHS
    integer(i1), dimension(:,:),   pointer :: turb_pivot_lu  ! LHS
    real(dp),    dimension(:,:,:), pointer :: a_diag_dc      ! LHS
    real(dp),    dimension(:,:,:), pointer :: a_diag_lu_dc   ! LHS
    integer(i1), dimension(:,:),   pointer :: pivot_lu_dc    ! LHS
    real(odp),   dimension(:,:),   pointer :: a_off_dc       ! LHS
    real(dp),    dimension(:,:,:), pointer :: a_diag_mean    ! LHS
    real(dp),    dimension(:,:,:), pointer :: a_diag_lu_mean ! LHS
    integer(i1), dimension(:,:),   pointer :: pivot_lu_mean  ! LHS
    real(odp),   dimension(:,:,:), pointer :: a_off_mean     ! LHS
    real(dp),    dimension(:,:),   pointer :: b              ! Right hand side
    real(dqp),   dimension(:,:),   pointer :: dq             ! Delta Q
    real(dqp),   dimension(:,:),   pointer :: dq_dc          ! Delta Q
    real(dqp),   dimension(:,:),   pointer :: dq_mean        ! Delta Q
    real(dqp),   dimension(:,:),   pointer :: dturb          ! Delta turb
    real(dp),    dimension(:,:),   pointer :: res            ! Residual
    real(dp),    dimension(:,:),   pointer :: res_dc         ! Residual
    real(dp),    dimension(:,:),   pointer :: res_mean       ! Residual

    real(dp),    dimension(:,:),   pointer :: forcing        ! Forcing term

!   Coarse grid FAS forcing function
    real(dp),    dimension(:,:),   pointer :: ff
    real(dp),    dimension(:,:),   pointer :: turbff

!   Residual weighting
    real(dp),    dimension(:),     pointer :: residual_weighting

    real(dp),    dimension(:,:),   pointer :: turbres    ! Turb Model Residual
    real(dp),    dimension(:),     pointer :: dft1, dft2 ! Turb mod
                                                         ! linearization
    real(dp),    dimension(:,:),   pointer :: turb       ! Turbulence dep vars
    real(dp),    dimension(:),     pointer :: amut       ! Turbulent mu
    logical,     dimension(:),     pointer :: wall_function_node ! what it says
    real(dp),    dimension(:,:),   pointer :: rhotauij   ! Reynolds stress
    real(dp),    dimension(:),     pointer :: sst_f1     ! Blend fcn for kw-SST
    real(dp),    dimension(:),     pointer :: sst_f2     ! Blend fcn for kw-SST
    real(dp),    dimension(:),     pointer :: crossD     ! Cross fcn for kw-SST
    real(dp),    dimension(:,:),   pointer :: qatn       ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: qatn1      ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: qatn2      ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: qatn3      ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: qatn4      ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: qatp1      ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: qatp2      ! Time-acc quantities
    real(dp),    dimension(:,:,:), pointer :: stage_res  ! Intermediate
                                                         ! stagevalues of RHS
    real(dp),    dimension(:,:),   pointer :: q_time_avg ! Time-avg q's
    real(dp),    dimension(:,:),   pointer :: qq_time_avg ! Time-avg q'q''s
    real(dp),    dimension(:,:),   pointer :: terr_flow  ! Temporal flow error
    real(dp),    dimension(:,:),   pointer :: err_vortex ! Error in solution
                                                         ! for moving vortex
    ! Exact discrete solution, i.e., q_dof at zero nonlinear residuals
    ! ...used in ICG and IR analyses
    real(dp),    dimension(:,:),   pointer :: q_dof_res0
    real(dp),    dimension(:,:),   pointer :: turb_res0  ! node-centered
    real(dp),    dimension(:),     pointer :: omega      ! Relax fact for IR
    real(dp),    dimension(:,:),   pointer :: turbatn    ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: turbatn1   ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: turbatn2   ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: turbatn3   ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: turbatn4   ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: turbatp1   ! Time-acc quantities
    real(dp),    dimension(:,:),   pointer :: turbatp2   ! Time-acc quantities
    real(dp),    dimension(:),     pointer :: amutatn1   ! Time-acc quantities
    real(dp),    dimension(:),     pointer :: amutatn2   ! Time-acc quantities
    real(dp),    dimension(:),     pointer :: amutatp1   ! Time-acc quantities
    real(dp),    dimension(:),     pointer :: amutatp2   ! Time-acc quantities
    real(dp),    dimension(:,:,:), pointer :: stage_res_turb ! Intermediate
                                                         ! stagevalues of RHS
    real(dp),    dimension(:,:),   pointer :: terr_turb  ! Temporal turb error
    real(dp),    dimension(:,:,:), pointer :: rmshist    ! residual per iter
    real(dp),    dimension(:,:,:), pointer :: rmaxhist   ! max res per iter
    real(dp),    dimension(:,:,:), pointer :: xlochist   ! max res location
    real(dp),    dimension(:,:,:), pointer :: ylochist   ! max res location
    real(dp),    dimension(:,:,:), pointer :: zlochist   ! max res location
    !...walltime is the wall clock time averaged over the processors.
    real(dp),    dimension(:),     pointer :: walltime   ! wall time
    real(dp),    dimension(:),     pointer :: alpha      ! aoa at each iter
    real(dp),    dimension(:),     pointer :: simtime    ! sim time each iter

    logical,     dimension(:),     pointer :: diag_has_been_decomposed

    type(force_type),        dimension(:), pointer :: bcforce
    type(force_type),        dimension(:), pointer :: totforce

    integer                                        :: gmres_eqn_groups_entry
    type(eqn_group_type),    dimension(:), pointer :: eqn_groups

    type(global_bndry_type), dimension(:), pointer :: global_bndry_data
    type(bcsoln_type),       dimension(:), pointer :: bcsoln

  end type soln_type

  type gmres_data_type

    integer :: n                                ! Number of equations
                                                ! (block size*grid size)
    integer :: nsearch                          ! Subspace dimension
    integer :: ileft                            ! >  0 Left preconditioning
                                                ! <= 0 Right preconditioning
    integer :: nrestarts                        ! Number of restarts
    integer :: iter_out                         ! Number of iters used by GMRES
    integer :: error_flag                       ! Return error flag
    integer :: r8work_dim                       ! Dimension of real workspace
    integer :: intwork_dim                      ! Dimension of integer workspace

    integer, dimension(:), pointer :: intwork   ! Integer workspace

    real(dp) :: tol                             ! Convergence criterion
    real(dp) :: final_err                       ! Error estimate for final soln

    real(dp), dimension(:),   pointer :: b      ! RHS vector
    real(dp), dimension(:),   pointer :: x      ! Unknowns vector
    real(dp), dimension(:),   pointer :: sb     ! Scale factors for RHS
    real(dp), dimension(:),   pointer :: sx     ! Scale factors for unknowns
    real(dp), dimension(:),   pointer :: r8work ! Real workspace
    real(dp), dimension(:,:), pointer :: matvec ! Matrix-vector product

    logical :: matvec_linear   ! Matrix-vector product using linearization

  end type gmres_data_type

  type eqn_group_type

    character(22) :: solve_type   ! 'global-gmres-newtk'
                                  ! 'point-multicolor', 'line-multicolor',
                                  ! 'gmres-newtk', 'point-downstream'
                                  ! 'ILU(0)' 'ILU(K)'

!   Sweep coloring info

    ! Number of sweeps via color_indices.
    integer :: colored_sweeps = 2

    ! Maximum number of sweeps via color_indices.
    integer :: max_colored_sweeps = 2

    ! Marks the end of periodic eqn entries for each color.
    integer, dimension(:), pointer :: color_periodic_end

    ! Marks the end of boundary eqn entries for each color.
    integer, dimension(:), pointer :: color_boundary_end

    ! Start/end indices for sweeps through the point_dofs array.
    integer, dimension(:,:), pointer :: color_indices

    ! Start/end indices for ghost transfer groups of each color.
    integer, dimension(:,:), pointer :: color_xfer_indices

    ! Send/recv information for transfer of colorings at boundary.
    type (sendrecv_type), dimension(:), pointer :: sr

!   Line-implicit info.

    integer :: n_lines ! Number of implicit lines.

    integer, dimension(:), pointer :: nonzero_above ! Off-diagonal entry in A
                                                    ! due to dof above current
                                                    ! dof

    integer, dimension(:), pointer :: nonzero_below ! Off-diagonal entry in A
                                                    ! due to dof below current
                                                    ! dof

    real(jp), dimension(:,:,:), pointer :: a ! Subdiagonal for line solve
    real(jp), dimension(:,:,:), pointer :: b ! Diagonal for line solve
    real(jp), dimension(:,:,:), pointer :: c ! Superdiagonal for line solve

    real(jp), dimension(:,:,:), pointer :: turb_a ! turbulent line solve
    real(jp), dimension(:,:,:), pointer :: turb_b ! turbulent line solve
    real(jp), dimension(:,:,:), pointer :: turb_c ! turbulent line solve

    ! Number of dofs (nodes/cells) contained in implicit lines.
    integer :: n_line_dofs

    ! Dofs (nodes/cells) comprising the implicit lines (compressed format).
    integer, dimension(:), pointer :: line_dofs

    ! Entry in line_dofs array which is the first dof in each line.
    integer, dimension(:), pointer :: first_dof

    integer, dimension(:), pointer :: lines     ! Lines to be solved.
    integer, dimension(:), pointer :: lu_offset ! Offset for lu entries.

    ! Begin ILU(K)

    real(jp),  dimension(:,:,:), pointer :: a_mat_diag
    real(lup), dimension(:,:,:), pointer :: a_mat_off
    real(jp),  dimension(:,:,:), pointer :: alu_diag
    real(lup), dimension(:,:,:), pointer :: alu_off
    real(jp),  dimension(:,:,:), pointer :: w

    integer,   dimension(:),     pointer :: jlu
    integer,   dimension(:),     pointer :: jw
    integer,   dimension(:),     pointer :: ju
    integer,   dimension(:),     pointer :: levs

    real(jp),  dimension(:,:,:), pointer :: a_mat_diag_turb
    real(lup), dimension(:,:,:), pointer :: a_mat_off_turb
    real(jp),  dimension(:,:,:), pointer :: alu_diag_turb
    real(lup), dimension(:,:,:), pointer :: alu_off_turb
    real(jp),  dimension(:,:,:), pointer :: w_turb

    integer,   dimension(:),     pointer :: jlu_turb
    integer,   dimension(:),     pointer :: jw_turb
    integer,   dimension(:),     pointer :: ju_turb
    integer,   dimension(:),     pointer :: levs_turb

    integer :: nnzilu = 0
    integer :: nnzmax = 0
    integer :: lfil   = 1
    logical :: iluk_allocated      = .false.

    integer :: nnzilu_turb = 0
    integer :: nnzmax_turb = 0
    integer :: lfil_turb   = 1
    logical :: iluk_allocated_turb = .false.

    integer :: ierr = 0

    ! End   ILU(K)

    integer :: n_eqns ! Number of point-implicit/ilu0 equations

    integer,   dimension(:), pointer :: point_dofs ! Dofs to be solved using
                                                   ! point-implicit/ilu0 schemes
    integer,   dimension(:), pointer :: iau        ! Additional comp row array
    integer,   dimension(:), pointer :: matrix_l2g ! Map between global A and

    real(jp),  dimension(:,:,:), pointer :: a_diag_prec  ! ILU(0) matrix
    real(lup), dimension(:,:,:), pointer :: a_off_prec   ! ILU(0) matrix
    real(dp),  dimension(:,:),   pointer :: precond_rhs  ! ILU(0) rhs
    real(dp),  dimension(:,:),   pointer :: precond_soln ! ILU(0) soln
    integer,   dimension(:),     pointer :: blkilu_work  ! Workspace blkilu's
    integer                              :: counter_mean = 0

    real(jp),  dimension(:,:,:), pointer :: a_turb_diag_prec  ! ILU(0) matrix
    real(lup), dimension(:,:,:), pointer :: a_turb_off_prec   ! ILU(0) matrix
    real(dp),  dimension(:,:),   pointer :: precond_turb_rhs  ! ILU(0) rhs
    real(dp),  dimension(:,:),   pointer :: precond_turb_soln ! ILU(0) soln
    integer,   dimension(:),     pointer :: blkilu_work_turb
    integer                              :: counter_turb = 0

    type(crow_flow) :: crow ! Compressed row indexing arrays for locally
                            ! numbered set of equations for ILU(0)

    type(gmres_data_type) :: gmres_data ! Stuff needed for running GMRES

  end type eqn_group_type

  type global_bndry_type


    logical                           :: used       ! need this bndry?
    integer                           :: ref_frame  ! which ref_frame
    integer                           :: nface      ! #global bndy faces
    integer                           :: nfacenodes ! #global bndy nodes
    integer                           :: nfacelocal ! #local  bndy faces

    integer,  dimension(:,:), pointer :: f2n        ! f2n map for bndy
    integer,  dimension(:,:), pointer :: localf2n   ! local f2n for bndy
    integer,  dimension(:),   pointer :: bndryl2g   ! l2g map for bndy
    integer,  dimension (:),  pointer :: nfaceproc  ! for allgatherv

!   Global boundary values

!   NOTE: Update nullify_global_bndry_data below when changing these!

    real(dp), dimension(:),   pointer :: xglobal_bndry
    real(dp), dimension(:),   pointer :: yglobal_bndry
    real(dp), dimension(:),   pointer :: zglobal_bndry
    real(dp), dimension(:),   pointer :: xat0global_bndry
    real(dp), dimension(:),   pointer :: yat0global_bndry
    real(dp), dimension(:),   pointer :: zat0global_bndry
    real(dp), dimension(:),   pointer :: cpglobal_bndry
    real(dp), dimension(:),   pointer :: cqglobal_bndry
    real(dp), dimension(:),   pointer :: cfxglobal_bndry
    real(dp), dimension(:),   pointer :: cfyglobal_bndry
    real(dp), dimension(:),   pointer :: cfzglobal_bndry
    real(dp), dimension(:,:), pointer :: qglobal_bndry
    real(dp), dimension(:),   pointer :: slenglobal_bndry
    real(dp), dimension(:),   pointer :: amutglobal_bndry
    real(dp), dimension(:),   pointer :: tauijglobal_bndry
    real(dp), dimension(:,:), pointer :: turbglobal_bndry
    real(dp), dimension(:),   pointer :: l2gglobal_bndry
    real(dp), dimension(:),   pointer :: iblankglobal_bndry
    real(dp), dimension(:),   pointer :: imeshglobal_bndry
    real(dp), dimension(:,:), pointer :: gradxglobal_bndry
    real(dp), dimension(:,:), pointer :: gradyglobal_bndry
    real(dp), dimension(:,:), pointer :: gradzglobal_bndry
    real(dp), dimension(:,:), pointer :: tavg_qglobal_bndry
    real(dp), dimension(:),   pointer :: uavgglobal_bndry
    real(dp), dimension(:),   pointer :: vavgglobal_bndry
    real(dp), dimension(:),   pointer :: wavgglobal_bndry
    real(dp), dimension(:),   pointer :: amutoffbodyglobal_bndry
    real(dp), dimension(:),   pointer :: volglobal_bndry
    real(dp), dimension(:,:), pointer :: resglobal_bndry
    real(dp), dimension(:,:), pointer :: turresglobal_bndry
    real(dp), dimension(:,:), pointer :: res_gclglobal_bndry
    real(dp), dimension(:,:), pointer :: rlamglobal_bndry    ! adjoint
    real(dp), dimension(:),   pointer :: proc_idglobal_bndry ! proc/part id
    real(dp), dimension(:,:), pointer :: viscous_flux_bndry  ! shear, htg
    real(dp), dimension(:),   pointer :: utau_wfglobal_bndry
    real(dp), dimension(:),   pointer :: phi_wfglobal_bndry
    real(dp), dimension(:),   pointer :: mu_t_wfglobal_bndry
    real(dp), dimension(:),   pointer :: k_wf_bcglobal_bndry
    real(dp), dimension(:),   pointer :: omega_wf_bcglobal_bndry
    real(dp), dimension(:),   pointer :: bxnglobal_bndry
    real(dp), dimension(:),   pointer :: bynglobal_bndry
    real(dp), dimension(:),   pointer :: bznglobal_bndry

  end type global_bndry_type

  type global_slice_type

    integer                         :: slice_dir    ! 1/2/3 x/y/z slice
    real(dp)                        :: slice_val    ! x/y/z value at slice
    real(dp)                        :: xle          ! x coord of slice le
    real(dp)                        :: yle          ! y coord of slice le
    real(dp)                        :: zle          ! z coord of slice le
    real(dp)                        :: xte          ! x coord of slice te
    real(dp)                        :: yte          ! y coord of slice te
    real(dp)                        :: zte          ! z coord of slice te
    real(dp)                        :: corner_angle ! if smaller, a corner
    integer                         :: chord_dir    ! le < te or te > le
    integer                         :: le_def       ! no. pts. to define le
    integer                         :: te_def       ! no. pts. to define te
    integer                         :: nloops       ! loops to define body
    integer                         :: nseg         ! total segment count
    integer                         :: ref_frame    ! which ref_frame
    integer                         :: id           ! id for bookkeeping
    integer                         :: group        ! which group slice is in
    integer,  dimension(:), pointer :: ibeg         ! start segment of loop
    integer,  dimension(:), pointer :: iend         ! end segment of loop
    real(dp), dimension(:), pointer :: xs1          ! x at begining of seg
    real(dp), dimension(:), pointer :: ys1          ! y at begining of seg
    real(dp), dimension(:), pointer :: zs1          ! z at begining of seg
    real(dp), dimension(:), pointer :: xs2          ! x at end of seg
    real(dp), dimension(:), pointer :: ys2          ! y at end of seg
    real(dp), dimension(:), pointer :: zs2          ! z at end of seg
    real(dp), dimension(:), pointer :: cps1         ! Cp at begining of seg
    real(dp), dimension(:), pointer :: cps2         ! Cp at end of seg
    real(dp), dimension(:), pointer :: cfxs1        ! Cfx at begining of seg
    real(dp), dimension(:), pointer :: cfxs2        ! Cfx at end of seg
    real(dp), dimension(:), pointer :: cfys1        ! Cfy at begining of seg
    real(dp), dimension(:), pointer :: cfys2        ! Cfy at end of seg
    real(dp), dimension(:), pointer :: cfzs1        ! Cfz at begining of seg
    real(dp), dimension(:), pointer :: cfzs2        ! Cfz at end of seg
    real(dp), dimension(:), pointer :: xnorm        ! face x-normal
    real(dp), dimension(:), pointer :: ynorm        ! face y-normal
    real(dp), dimension(:), pointer :: znorm        ! face z-normal
    real(dp), dimension(6)          :: aux_data     ! addt'l descriptive  data
    real(dp), dimension(3)          :: chord_vec    ! unit chord vector
    real(dp), dimension(3)          :: norm_vec     ! unit normal vector
    real(dp), dimension(3)          :: span_vec     ! unit span  vector
    real(dp)                        :: pitch        ! section geometric pitch
    real(dp)                        :: xmc          ! sectional x-moment cntr
    real(dp)                        :: ymc          ! sectional y-moment cntr
    real(dp)                        :: zmc          ! sectional z-moment cntr
    real(dp)                        :: xmc_if       ! xmc in inertial frame
    real(dp)                        :: ymc_if       ! ymc in inertial frame
    real(dp)                        :: zmc_if       ! zmc in inertial frame
    real(dp)                        :: cls          ! sectional lift coeff
    real(dp)                        :: cds          ! sectional drag coeff
    real(dp)                        :: cxs          ! sectional x-force coeff
    real(dp)                        :: cys          ! sectional y-force coeff
    real(dp)                        :: czs          ! sectional z-force coeff
    real(dp)                        :: cmxs         ! sectional x-moment coeff
    real(dp)                        :: cmys         ! sectional y-moment coeff
    real(dp)                        :: cmzs         ! sectional z-moment coeff
    real(dp)                        :: clps         ! pressure contributions
    real(dp)                        :: cdps
    real(dp)                        :: cxps
    real(dp)                        :: cyps
    real(dp)                        :: czps
    real(dp)                        :: cmxps
    real(dp)                        :: cmyps
    real(dp)                        :: cmzps
    real(dp)                        :: clvs         ! viscous contributions
    real(dp)                        :: cdvs
    real(dp)                        :: cxvs
    real(dp)                        :: cyvs
    real(dp)                        :: czvs
    real(dp)                        :: cmxvs
    real(dp)                        :: cmyvs
    real(dp)                        :: cmzvs
    real(dp)                        :: ca
    real(dp)                        :: cn
    real(dp)                        :: cs
    real(dp)                        :: cma
    real(dp)                        :: cmn
    real(dp)                        :: cms

  end type global_slice_type

  type linear_projection_type

    character(23) :: list_name = 'linear_projection' ! Namelist name

    logical  :: setup           = .false.  ! Is type setup yet?
    logical  :: hit_tolerance   = .false.  ! Have we hit tol?
    logical  :: initial_res     = .false.  ! Initial residual stored

    character(18) :: kry_meth              ! Krylov Flavor

    logical  :: frechet                   = .false. !Frechet

    logical  :: overwrite_res_dq = .false. ! Overwrite soln%res and soln%dq

    integer  :: projection_restarts = 1    ! # of proj restarts
    integer  :: krylov_dimension = 1       ! Dim: Krylov space

    integer  :: nb                         ! block size
    integer  :: dof0                       ! dof0
    integer  :: dofg                       ! dofg

    real(jp) :: termination_tolerance      ! Kickout tolerance
    real(dp) :: absolute_tolerance         ! Kickout absolute tolerance
    real(jp) :: relative_tolerance         ! Kickout relative tolerance

    logical  :: deflation = .false.        ! Deflate eigenvalues
    integer  :: N_defl                     ! Dim: Deflate space

    real(jp) :: resid_init                 ! Initial residual
    real(jp) :: resid_current              ! Current residual

    real(jp) :: cr_scalar                  ! Convergence rate

    integer  :: active_vector              ! new Krylov vect

    real(krp), dimension(:,:),   pointer :: x_vec_linear  ! update direction
    real(krp), dimension(:,:),   pointer :: r_vec_linear  ! new residual
    real(krp), dimension(:,:,:), pointer :: s_vec_linear  ! previous updates
    real(krp), dimension(:,:,:), pointer :: v_vec_linear  ! previous residuals
    real(krp), dimension(:,:,:), pointer :: ritz_vecs     ! Ritz Vectors

    real(krp), dimension(:,:),   pointer :: w_vec_linear  ! GMRES work array
    real(krp), dimension(:,:),   pointer :: x0_vec_linear ! update direction

    real(dp),  dimension(:,:),   pointer :: res_nonlinear ! res nonlinear
    real(dp),  dimension(:,:),   pointer :: res_base      ! res Frechetbase
    real(dp),  dimension(:,:),   pointer :: hes           ! H = Up-Hes Matrix
    real(dp),  dimension(:,:),   pointer :: R_hes         ! H = Q R_hes
    real(dp),  dimension(:),     pointer :: q_orth        ! QR Decomp Weights
    real(dp),  dimension(:),     pointer :: c_vec         ! GMRES Weights Vectr
    real(dp),  dimension(:),     pointer :: x_vec         ! GMRES Weights Vectr
    real(dp),  dimension(:),     pointer :: y_vec         ! GMRES Weights Vectr
    real(dp),  dimension(:),     pointer :: z_vec         ! GMRES Weights Vectr
    real(dp),  dimension(:),     pointer :: d_vec         ! GMRES Weights Vectr
    real(dp),  dimension(:,:),   pointer :: g_eig_A       ! General Eig A
    real(dp),  dimension(:,:),   pointer :: g_eig_B       ! General Eig B

  end type linear_projection_type

contains

!========================== NULLIFY_GLOBAL_BNDRY_DATA ========================80
!
! Nullifies pointers in the global_bndry_data derived type
!
!=============================================================================80

  subroutine nullify_global_bndry_data( global_bndry_data )

    type(global_bndry_type), intent(inout) :: global_bndry_data

  continue

    nullify(global_bndry_data%f2n)
    nullify(global_bndry_data%localf2n)
    nullify(global_bndry_data%bndryl2g)
    nullify(global_bndry_data%nfaceproc)
    nullify(global_bndry_data%xglobal_bndry)
    nullify(global_bndry_data%yglobal_bndry)
    nullify(global_bndry_data%zglobal_bndry)
    nullify(global_bndry_data%xat0global_bndry)
    nullify(global_bndry_data%yat0global_bndry)
    nullify(global_bndry_data%zat0global_bndry)
    nullify(global_bndry_data%cpglobal_bndry)
    nullify(global_bndry_data%cqglobal_bndry)
    nullify(global_bndry_data%cfxglobal_bndry)
    nullify(global_bndry_data%cfyglobal_bndry)
    nullify(global_bndry_data%cfzglobal_bndry)
    nullify(global_bndry_data%qglobal_bndry)
    nullify(global_bndry_data%slenglobal_bndry)
    nullify(global_bndry_data%amutglobal_bndry)
    nullify(global_bndry_data%tauijglobal_bndry)
    nullify(global_bndry_data%turbglobal_bndry)
    nullify(global_bndry_data%l2gglobal_bndry)
    nullify(global_bndry_data%iblankglobal_bndry)
    nullify(global_bndry_data%imeshglobal_bndry)
    nullify(global_bndry_data%gradxglobal_bndry)
    nullify(global_bndry_data%gradyglobal_bndry)
    nullify(global_bndry_data%gradzglobal_bndry)
    nullify(global_bndry_data%tavg_qglobal_bndry)
    nullify(global_bndry_data%uavgglobal_bndry)
    nullify(global_bndry_data%vavgglobal_bndry)
    nullify(global_bndry_data%wavgglobal_bndry)
    nullify(global_bndry_data%amutoffbodyglobal_bndry)
    nullify(global_bndry_data%volglobal_bndry)
    nullify(global_bndry_data%resglobal_bndry)
    nullify(global_bndry_data%turresglobal_bndry)
    nullify(global_bndry_data%res_gclglobal_bndry)
    nullify(global_bndry_data%rlamglobal_bndry)
    nullify(global_bndry_data%proc_idglobal_bndry)
    nullify(global_bndry_data%viscous_flux_bndry)
    nullify(global_bndry_data%utau_wfglobal_bndry)
    nullify(global_bndry_data%phi_wfglobal_bndry)
    nullify(global_bndry_data%mu_t_wfglobal_bndry)
    nullify(global_bndry_data%k_wf_bcglobal_bndry)
    nullify(global_bndry_data%omega_wf_bcglobal_bndry)
    nullify(global_bndry_data%bxnglobal_bndry)
    nullify(global_bndry_data%bynglobal_bndry)
    nullify(global_bndry_data%bznglobal_bndry)

  end subroutine nullify_global_bndry_data

end module solution_types

!=================================== SMTHLMS =================================80
!
! Smooth (differentiable) flux limiter applied to a scalar
! using the CFL3D differentiable limiter AIAA-90-0429
!
!=============================================================================80

  pure function smthlms(grad_a, grad_b, eps2)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_2, my_3

    real(dp),  intent(in) :: eps2
    real(dp),  intent(in) :: grad_a, grad_b
    real(dp)              :: smthlms

  continue

! Generalized form of the smooth limiter for kappa_umuscl=1/2 and kappa=0
! AIAA-90-0429 (1990), AIAA Journal 29(7) (1991), J<PERSON> 118 (1995)
!
!   van Albada limiter
!   smthlms = (grad_a*(grad_b**2 + eps2) + grad_b*(grad_a**2 + eps2))/         &
!             (grad_a**2 + grad_b**2 + my_2*eps2)

!   CFL3D limiter
    smthlms = (grad_b*(grad_a**2+my_2*eps2) + grad_a*(my_2*grad_b**2+eps2)) /  &
              (my_2*grad_b**2 - grad_b*grad_a + my_2*grad_a**2 + my_3*eps2)


  end function smthlms

#!/bin/bash

# Shell script that executes the equivalent of clean-stalemods via the all-local
# make rule.  This is designed to prevent issues resulting from files leaving
# .mod files behind when they are moved

listed_mods=(${@:2})
mod_ext=$1
found_mods=(*.${mod_ext})

rm_mods=()
for i in "${found_mods[@]}"; do
    skip=
    for j in "${listed_mods[@]}"; do
	#echo "first mod: $i  second mod: $j"
        [[ $i == $j ]] && { skip=1; break; }
    done
    [[ -n $skip ]] || rm_mods+=("$i")
done

for mod in ${rm_mods[@]} ; do
  mod=${mod%.${mod_ext}}
  if [[ ${mod} = "blas" ]]; then continue ; fi #blas is a program
  if [[ ${mod} = "workdefs" ]]; then continue ; fi #utils dependency issue
  if [[ ${mod} = "*" ]]; then continue ; fi    #prevents everything from getting wiped out
  echo "removing $mod objects"
  rm $mod.*
done

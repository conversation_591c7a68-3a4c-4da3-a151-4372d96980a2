#include <stdlib.h>
#include <math.h>
#ifndef __APPLE__
#include <malloc.h>
#endif

#ifdef __cplusplus
	extern "C" {
#endif

void householder(int nrow, int ncol, double *A, double *V, double *W, double *rhs);
void backsub(int nrow, int ncol, double *A, double *rhs, double *x);
void applyl(int nrow, int ncol, int irow, double *A, double *V, double *W);
void hvect(int irow, int ndim, double *x, double *v);

double fitsegmentLS (
	 long   	  *order, 
	 long   	  *ncontrol, 
	 long   	  param,
	 long   	  npts, 
	 double		 *xcoord,
	 double		 *ycoord,
	 long		 *nknot,
	 double		**knots,
	 double		**xcontrol,
	 double		**ycontrol,
         double          *segtval);

#ifdef __cplusplus
}
#endif



double fitsegmentLS (
	 long   	 *rorder, 
	 long   	 *rncontrol, 
	 long   	  param,
	 long   	  npts, 
	 double		 *xcoord,
	 double		 *ycoord,
	 long		 *nknot,
	 double		**knots,
	 double		**xcontrol,
	 double		**ycontrol,
         double          *segtval)
{
  int i,j,k,m;
  long order, ncontrol;
  double *uvals,*basis1,*basis2,*matrix,*vector1,*vector2,*rvector;
  double norm,chi2;

  order = *rorder;
  ncontrol = *rncontrol;

  /* Check inputs */
  if (order < 1) return(-1.0);

  /* Make sure the problem will be well posed */
  if (order >= npts-1) order = npts-2;
  if (ncontrol > npts) ncontrol = npts;
  if (ncontrol <= order) ncontrol = order+1;

/*  *rorder = order; Dont worry about the order for now */
  *rncontrol = ncontrol; /* Set the order and number of control points */

  *nknot = ncontrol + order-1;
  /* Allocate return array storage */
  *knots    = (double *) calloc(*nknot,  sizeof(double));
  *xcontrol = (double *) calloc(ncontrol, sizeof(double));
  (*ycontrol) = (double *) calloc(ncontrol, sizeof(double));
  /* Allocate temporary storage */
  uvals   = (double *) calloc(npts,          sizeof(double));
  basis1  = (double *) calloc(npts*ncontrol, sizeof(double));
  basis2  = (double *) calloc(npts*ncontrol, sizeof(double));
  matrix  = basis1;
  vector1 = (double *) calloc(npts-2,        sizeof(double));
  vector2 = uvals;
  rvector = (double *) calloc(npts-2,        sizeof(double));
  /* Parametrize & fill uvals list */
  switch (param) {
     case 1:          /* Uniform      */
     default:
        for (i=0; i < npts; i++)
           {
           uvals[i] = (double) i;
           segtval[i] = uvals[i];
           }
        break;
     case 3:          /* Centripetal  */
        uvals[0] = 0.0;
        for (i=1,j=1; i < npts; i++,j++)
           {
           uvals[j] = uvals[j-1] + sqrt(
                   sqrt((xcoord[i]   - xcoord[i-1])   *
                   (xcoord[i]   - xcoord[i-1])   +
                   (ycoord[i] - ycoord[i-1]) *
                   (ycoord[i] - ycoord[i-1])));
           segtval[i] = uvals[i];
           }
        break;
     case 2:          /* Chord-length */
        uvals[0] = 0.0;
        for (i=1,j=1; i < npts; i++,j++)
           {
           uvals[j] = uvals[j-1] +
              sqrt((xcoord[i]   - xcoord[i-1])   *
                   (xcoord[i]   - xcoord[i-1])   +
                   (ycoord[i] - ycoord[i-1]) *
                   (ycoord[i] - ycoord[i-1]));
           segtval[i] = uvals[i];
           }
        break;
     }
  /* Knots are equally spaced between umin & umax */
  for (i=0; i < *nknot - 2*(order-1); i++)
     (*knots)[i+order-1] = uvals[0] + (double)i/(*nknot-2*(order-1)-1) *
                                    (uvals[npts-1] - uvals[0]);
  for (i=0; i < order-1; i++) (*knots)[i] = (*knots)[order-1];
  for (i=*nknot-order+1; i < *nknot; i++) (*knots)[i] = (*knots)[*nknot-order];
  /* Compute the B-spline basis functions at each data point */
  /* Zeroth order functions first */
  for (i=0; i < npts; i++)
     {
     basis2[ncontrol*i] = 0.0;
     for (j=1; j < ncontrol; j++)
        {
        if (uvals[i] >= (*knots)[j-1] && uvals[i] < (*knots)[j])
           basis2[ncontrol*i+j] = 1.0;
        else basis2[ncontrol*i+j] = 0.0;
        }
     }
  /* Starting with zeroth order, build up to order */
  for (i=1; i <= order; i++)
     {
     /* First, copy basis2 to basis1 & clear basis2 */
     for (j=0; j < ncontrol*npts; j++)
        {
        basis1[j] = basis2[j];
        basis2[j] = 0.0;
        }
     for (j=0; j < npts; j++)
        {
        for (k=0; k < ncontrol; k++)
           {
           if (k-1 >= 0 && k+i-1 < *nknot &&
               (*knots)[k-1] != (*knots)[k+i-1])
              basis2[ncontrol*j+k] += (uvals[j]-(*knots)[k-1]) /
                    ((*knots)[k+i-1]-(*knots)[k-1]) * basis1[ncontrol*j+k];
           if (k+i < *nknot && (*knots)[k+i] != (*knots)[k])
              basis2[ncontrol*j+k] += ((*knots)[k+i]-uvals[j]) /
                    ((*knots)[k+i]-(*knots)[k]) * basis1[ncontrol*j+k+1];
           }
        }
     }
  /* For each coordinate, fill in the least-squares matrix & rhs vector */
  chi2 = 0.0;
  for (i=0,j=1; j < npts-1; i++,j++)
     {
     for (m=0; m < ncontrol-2; m++)
        matrix[(npts-2)*m+i] = basis2[ncontrol*(i+1)+m+1];
        rvector[i] = xcoord[j] -
                     basis2[ncontrol*(i+1)] *
                     xcoord[0] -
                     basis2[ncontrol*(i+2)-1] *
                     xcoord[npts-1];
     }
  /* Solve for control-point coordinates */
  householder(npts-2,ncontrol-2,matrix,vector1,vector2,rvector);
  backsub(npts-2,ncontrol-2,matrix,rvector,vector2);
  /* Put coordinates in return array */
  (*xcontrol)[0] = xcoord[0];
  for (i=0; i < ncontrol-2; i++)
     (*xcontrol)[i+1] = vector2[i];
  (*xcontrol)[ncontrol-1] = xcoord[npts-1];
  /* Get chi2, the error norm */
  norm = 0.0;
  for (i=ncontrol-2; i < npts-2; i++) norm += rvector[i]*rvector[i];
  if (npts != ncontrol) norm = sqrt(norm/(npts-ncontrol));
  if (norm > chi2) chi2 = norm;
  for (i=0,j=1; j < npts-1; i++,j++)
     {
     for (m=0; m < ncontrol-2; m++)
        matrix[(npts-2)*m+i] = basis2[ncontrol*(i+1)+m+1];
        rvector[i] = ycoord[j] -
                     basis2[ncontrol*(i+1)] *
                     ycoord[0] -
                     basis2[ncontrol*(i+2)-1] *
                     ycoord[npts-1];
     }
  /* Solve for control-point coordinates */
  householder(npts-2,ncontrol-2,matrix,vector1,vector2,rvector);
  backsub(npts-2,ncontrol-2,matrix,rvector,vector2);
  /* Put coordinates in return array */
  (*ycontrol)[0] = ycoord[0];
  for (i=0; i < ncontrol-2; i++)
     (*ycontrol)[i+1] = vector2[i];
  (*ycontrol)[ncontrol-1] = ycoord[npts-1];
  /* Get chi2, the error norm */
  norm = 0.0;
  for (i=ncontrol-2; i < npts-2; i++) norm += rvector[i]*rvector[i];
  if (npts != ncontrol) norm = sqrt(norm/(npts-ncontrol));
  if (norm > chi2) chi2 = norm;
  free(uvals);
  free(basis1);
  free(basis2);
  free(vector1);
  free(rvector);
  return(chi2);
}

void householder(int nrow, int ncol, double *A, double *V, double *W, double *rhs)
{
/* Makes an upper triangular matrix using householder transformations *
 * A is nrow x ncol, V, W, and rhs are nrow x 1                       *
 * Requires applyl and hvect                                          */

  int i;

  for (i=0; i < ncol; i++)
     {
     hvect(i, nrow, &A[i*nrow], V);
     applyl(nrow, ncol, i, A, V, W);
     applyl(nrow, 1, i, rhs, V, W);
     }
}

void backsub(int nrow, int ncol, double *A, double *rhs, double *x)
{
/* Performs back-substitution to get the vector x   *
 * A is nrow x ncol, rhs is nrow x 1, x is ncol x 1 */
  int i,j;

  for (i=ncol-1; i >= 0; i--)
     {
     x[i] = rhs[i];
     for (j=i+1; j < ncol; j++)
        {
        x[i] -= A[j*nrow+i] * x[j];
        }
     x[i] /= A[i*nrow+i];
     }
}

void applyl(int nrow, int ncol, int irow, double *A, double *V, double *W)
{
/* Applies the Householder vector to A (overwrites A) *
 * A is nrow x ncol, V and W are nrow x 1             */
  int i,j;
  double beta = 0.0;

  for (i=0; i < nrow; i++)
     {
     W[i] = 0.0;
     beta += V[i]*V[i];
     }
  beta = -2.0/beta;
  for (i=0; i < ncol; i++)
     {
     for (j=irow; j < nrow; j++) W[i] += A[i*nrow+j]*V[j];
     W[i] *= beta;
     }
  for (i=0; i < nrow; i++)
     for (j=0; j < ncol; j++) A[j*nrow+i] += V[i]*W[j];
}

void hvect(int irow, int ndim, double *x, double *v)
{
/* Gets the Householder vector *
 * x and v are ndim x 1        */
  int i;
  double vmag=0.0,sign=1.0,beta;

  for (i=0; i <= irow; i++) v[i] = 0.0;
  for (i=irow; i < ndim; i++)
     {
     v[i] = 0.0;
     vmag += x[i]*x[i];
     }
  vmag = sqrt(vmag);
  if (x[irow] < 0.0) sign = -1.0;
  beta = x[irow] + sign*vmag;
  for (i=irow+1; i < ndim; i++) v[i] = x[i]/beta;
  v[irow] = 1.0;
}

module lsq_defs

  use kinddefs, only : dp

  implicit none

  private

  public :: cc_mapped_lsq, na_mapped_lsq, nc_mapped_lsq, fit_used
  public :: cc_zero_cgamma, cgamma_tolerance, nc_zero_cgamma, fit_type
  public :: cylindrical_distance, cylindrical_testing
  public :: cylindrical_mapping, gradient_testing
  public :: slen_max_for_cgamma, skew_tolerance, ar_value_to_skip
  public :: use_nearest_node_slen0

  public :: type_solve, type_fit, lapack_blocking, lapack_uplo

!*******************************************************************************
  !Mapped least square for high-aspect-ratio curved grids.

  !Flag enabling mapped_lsq path if .true.
  logical :: cc_mapped_lsq = .false.
  logical :: na_mapped_lsq = .false.
  logical :: nc_mapped_lsq = .false.

  !Threshold to invoke cgamma contribution in mapped least square
  real(dp) :: cgamma_tolerance = 0.1_dp

  !Tests Cartesian lsq for mapped_lsq path if .true.
  logical :: cc_zero_cgamma = .false.
  logical :: nc_zero_cgamma = .false.

  logical :: use_nearest_node_slen0 = .false.

  !Use specialized distance functions to prove concept
  !of mapped least square for high-aspect-ratio curved grids, if .true.
  logical :: cylindrical_distance = .false.
  !Use specialized cylindrical mapping to prove concept
  !of mapped least square for high-aspect-ration curved grid, if .true.
  logical :: cylindrical_mapping  = .false.
  logical :: cylindrical_testing = .false.
  logical :: gradient_testing = .false.
  real(dp) :: slen_max_for_cgamma = huge(1._dp)
  real(dp) :: skew_tolerance = 30._dp
  real(dp) :: ar_value_to_skip = 2.0_dp

  character(len=40) :: type_solve      = 'choleski'
  character(len=40) :: type_fit        = 'linear'
  character(len=40) :: lapack_blocking = 'blocked'
  character(len=40) :: lapack_uplo     = 'U'

  type fit_type
    character(len=40), dimension(:), allocatable :: fit
  end type fit_type

  type(fit_type), dimension(:), allocatable :: fit_used

end module lsq_defs

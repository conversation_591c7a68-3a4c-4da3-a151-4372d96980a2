module turb_main

  use lmpi,          only : lmpi_nproc
  use lmpi,          only : lmpi_id, lmpi_master, lmpi_synchronize
  use lmpi,          only : lmpi_send, lmpi_recv
  use lmpi,          only : lmpi_conditional_stop
  use kinddefs,      only : dp, odp
  use turb_kw_const, only : verbose
  use info_depr,     only : skeleton

  use turbulence_info, only  :                                                 &
                        bsl,             menter_sst,      kw_sst,              &
         kw_sst2003,    sst_v,           sst,              sst_2003,           &
         asbm_sst,      abid_linear,     wilcox1988,       wilcox1988_v,       &
         wilcox_kw88,   wilcox_kw88p,    wilcox_kw98,      wilcox_asm,         &
         easm_ddes,     EASMko2003_S,    wilcox_kw06,      kw_des,             &
         chien,         wilcox_kw06p,    wilcox2006,       wilcox2006_v,       &
         k_kL_MEAH2013,                  wilcox_les,       kw_lag,             &
         gamma_ret_sst, WilcoxRSM_w2006, WilcoxRSM_w2006c, SSGLRR_RSM_w2012_SD,&
         SSGLRR_RSM_w2012, sst_kkl

  implicit none

  private

  public :: residual_multi_eqns
  public :: jacobian_multi_eqns

contains

!=========================== RESIDUAL_MULTI_EQNS =============================80
!
! Driver routine for residual evaluation for multi-equation models
!
!=============================================================================80

  subroutine residual_multi_eqns(grid, soln)

    use info_depr,      only : tightly_couple
    use grid_types,     only : grid_type
    use solution_types, only : soln_type

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln

  continue

    coupling : if ( tightly_couple ) then

!     not yet implemented

      call lmpi_conditional_stop(1,'tightly_couple issues:residual_multi_eqns')

    else coupling

      call turb_resid(soln%eqn_set, soln%viscous_method, grid%nnodes0,         &
                     grid%nnodes01, grid%nedgeloc, grid%eptr,                  &
                     soln%turb, soln%q_dof, soln%turbres, grid%slen,           &
                     soln%gradx, soln%grady, soln%gradz, grid%vol, grid%xn,    &
                     grid%yn, grid%zn, grid%ra, grid%x, grid%y, grid%z,        &
                     grid%nedgeloc_2d, grid%nnodes0_2d, grid%node_pairs_2d,    &
                     grid%iflagslen, grid%facespeed, soln%n_turb, soln%n_tot,  &
                     soln%n_grd, soln%amut,                                    &
                     grid%nelem, grid%elem, grid%nbound, grid%bc               &
                   , grid%dxdt, grid%dydt, grid%dzdt, soln%rhotauij            &
                   , soln%sst_f1, soln%sst_f2, soln%crossd )

    end if coupling


    if ( verbose .and. skeleton > 21 ) then
      call dump_q        ( grid, soln, 'turb_resid-q' )
      call dump_residuals( grid, soln, 'turb_resid-residuals' )
      call dump_jacobians( grid, soln, 'turb_resid-jacobians' )
      call dump_update   ( grid, soln, 'turb_resid-update' )
    endif

  end subroutine residual_multi_eqns

!=========================== JACOBIAN_MULTI_EQNS =============================80
!
! Driver routine for jacobian evaluation for multi-equation models
!
!=============================================================================80

  subroutine jacobian_multi_eqns(grid, soln, crow)

    use info_depr,      only : tightly_couple
    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(crow_flow),   intent(in)    :: crow

  continue

    coupling : if ( tightly_couple ) then

!     not yet implemented

    else coupling

      call turb_jacob(soln%eqn_set, soln%viscous_method, grid%nnodes0,         &
                     grid%nnodes01,  grid%nedgeloc, soln%max_nnz,              &
                     grid%eptr, soln%turb, soln%q_dof, grid%iflagslen,         &
                     grid%slen, soln%gradx,                                    &
                     soln%grady, soln%gradz, grid%vol, grid%xn, grid%yn,       &
                     grid%zn, grid%ra, soln%a_turb_diag, soln%a_turb_off,      &
                     crow%fhelp, grid%nedgeloc_2d, grid%nnodes0_2d,            &
                     grid%node_pairs_2d, grid%x, grid%y, grid%z, crow%nnz01,   &
                     crow%ia, crow%ja, grid%facespeed, soln%n_turb, soln%n_tot,&
                     soln%n_grd, crow%nzg2m, crow%g2m, soln%amut, grid%nelem,  &
                     grid%elem, soln%sst_f1, soln%sst_f2, soln%crossd,         &
                     soln%rhotauij )

    end if coupling

    if ( verbose .and. skeleton > 21 ) then
      call dump_q        ( grid, soln, 'turb_jacob-q' )
      call dump_residuals( grid, soln, 'turb_jacob-residuals' )
      call dump_jacobians( grid, soln, 'turb_jacob-jacobians' )
      call dump_update   ( grid, soln, 'turb_jacob-update' )
    endif

  end subroutine jacobian_multi_eqns

!================================ TURB_RESID =================================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for multi-equation models on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine turb_resid(eqn_set, viscous_method, nnodes0, nnodes01,            &
                       nedgeloc, eptr, turb, qnode, res, slen, gradx, grady,   &
                       gradz, vol, xn, yn, zn, ra, x, y, z, nedgeloc_2d,       &
                       nnodes0_2d, node_pairs_2d, iflagslen, facespeed, n_turb,&
                       n_tot, n_grd, amut, nelem, elem,                        &
                       nbound, bc, dxdt, dydt, dzdt, rhotauij, sst_f1, sst_f2, &
                       crossd )

    use bc_types,        only : bcgrid_type
    use element_types,   only : elem_type
    use info_depr,       only : twod
    use fluid,           only : gamma, sutherland_constant
    use info_depr,       only : tref, xmre
    use kinddefs,        only : dp
    use lmpi,            only : lmpi_conditional_stop
    use lmpi_app,        only : lmpi_xfer
    use solution_types,  only : compressible
    use thermo,          only : primitive_q_type, q_type
    use turb_2eqn,       only : turb_resid_2eqn
    use turb_3eqn,       only : turb_resid_3eqn
    use turb_4eqn,       only : turb_resid_4eqn
    use turb_7eqn,       only : turb_resid_7eqn
    use turb_convection, only : turb_resid_conv
    use turb_diffusion,  only : turb_resid_diff
    use turb_util,       only : kloc, wloc, trb_kloc, trb_wloc
    use turbulence_info, only : turbulence_model_int, turbulence_model
    use turb_kw_const,   only : sst_limit_cd, betastar, sig_w2
    use turb_gammaretsst_const, only : transition_4eqn_on

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: nelem
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_grd
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: nnodes0_2d

    integer,  dimension(nnodes01),           intent(in) :: iflagslen
    integer,  dimension(2,nedgeloc),         intent(in) :: eptr
    integer,  dimension(2,nnodes0_2d),       intent(in) :: node_pairs_2d

    real(dp), dimension(nnodes01),        intent(in)    :: slen
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z, vol
    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),        intent(in)    :: facespeed
    real(dp), dimension(nedgeloc),        intent(in)    :: ra
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(nnodes01),        intent(in)    :: amut
    real(dp), dimension(nnodes01),        intent(in)    :: dxdt, dydt, dzdt
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp), dimension(6,nnodes01),      intent(inout) :: rhotauij
    real(dp), dimension(nnodes01),        intent(inout) :: sst_f1
    real(dp), dimension(nnodes01),        intent(inout) :: sst_f2
    real(dp), dimension(nnodes01),        intent(inout) :: crossd

    type(elem_type), dimension(nelem),       intent(in) :: elem
    integer,                                 intent(in) :: nbound
    type(bcgrid_type),dimension(nbound),     intent(in) :: bc

    logical :: get_f2
    logical :: get_ranu
    logical :: f1_fix_4eqn

    integer :: ielem, ierr, r_start
    integer :: i, ii, node_src_eval

    real(dp) :: xmr, xmrinv, xmr2, sig_w2_inv
    real(dp) :: cstar, rho, rhoinv, temp, rnu
    real(dp) :: dist, tke, omega
    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: one  = 1.0_dp
    real(dp), parameter :: my_eps = epsilon(0.0_dp)

    continue

    ierr = 0

    select case ( eqn_set )
    case ( compressible )
      ierr = primitive_q_type - q_type
      call lmpi_conditional_stop(ierr,'q_type not primitive:  turb_resid')
    case default
      call lmpi_conditional_stop(1,'eqn_set not compressible:  turb_resid')
    end select

!-----------------------------------------------------------------------------80
!                               convection
!-----------------------------------------------------------------------------80
    r_start = 1
    call turb_resid_conv( turbulence_model_int,                              &
                          nnodes0, nedgeloc, eptr, turb,                     &
                          qnode, res, xn, yn, zn, ra, facespeed,             &
                          n_turb, r_start, nedgeloc_2d,                      &
                          x, y, z, gradx, grady, gradz )

!-----------------------------------------------------------------------------80
!                             viscous diffusion
!-----------------------------------------------------------------------------80
    ! first calculate and distribute F1 and F2 functions
    sst_f1 = zero
    sst_f2 = zero

    node_src_eval   = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

!!!!if ( turbulence_model /= 'WilcoxRSM-w2006' .and.                           &
!        turbulence_model /= 'WilcoxRSM-w2006c' ) then
!     do ii = 1, node_src_eval
!       i         = get_index( twod, node_pairs_2d, ii )
!       sst_f1(i) = blending_sst_f1 (i, eqn_set, nnodes01, turb, qnode, slen   &
!                            , gradx, grady, gradz, n_turb, n_tot, n_grd       &
!                            , turbulence_model, compressible, kloc, wloc)
!       sst_f2(i) = blending_sst_f2 (i, eqn_set, nnodes01, turb, qnode, slen   &
!                          , n_turb, n_tot, compressible, trb_kloc, trb_wloc)
!     end do
!   end if
!
!   call lmpi_xfer( sst_f1 )
!!!!call lmpi_xfer( sst_f2 )

    ! Compute the sst blending coefficients if/when they are needed
    select case ( turbulence_model_int )
     case ( gamma_ret_sst, kw_sst, kw_sst2003,                                 &
            sst_v, sst, sst_2003, menter_sst, bsl, kw_des,                     &
            wilcox_les, easm_ddes, SSGLRR_RSM_w2012_SD,                        &
            SSGLRR_RSM_w2012, sst_kkl )
       xmr    = xmre
       xmrinv = one/xmre
       xmr2   = xmre*xmre
       sig_w2_inv = one/sig_w2
       if ( eqn_set == compressible ) then
         get_ranu = .true.
         cstar = sutherland_constant / tref
       else
         get_ranu = .false.
         rho = one
         rnu = one
       end if
       if (turbulence_model_int /= gamma_ret_sst .and.                         &
           turbulence_model_int /= SSGLRR_RSM_w2012_SD  .and.                  &
           turbulence_model_int /= SSGLRR_RSM_w2012 ) then
         get_f2 = .true.
       else
         get_f2 = .false.
       end if
         if ( turbulence_model_int == sst_kkl )  get_f2 = .false.
       f1_fix_4eqn = .false.
       if (turbulence_model_int == gamma_ret_sst .and. transition_4eqn_on) then
          f1_fix_4eqn = .true.
       end if
       do ii = 1, node_src_eval
         i = get_index( twod, node_pairs_2d, ii )
         if (get_ranu) then
           rho    = qnode(1,i)
           rhoinv = one / rho
           temp   = gamma * qnode(5,i) * rhoinv
           rnu    = viscosity_law( cstar, temp ) * rhoinv
         end if
         if (turbulence_model_int == SSGLRR_RSM_w2012_SD .or.                  &
             turbulence_model_int == SSGLRR_RSM_w2012 ) then
           tke   = -0.5_dp*(turb(1,i)+turb(2,i)+turb(3,i))
           omega = turb(7,i)
         else
           tke   = turb(1,i)
           omega = turb(2,i)
         end if
         sst_f1(i) = function_sst_f1( n_grd, kloc, wloc                        &
                                    , f1_fix_4eqn                              &
                                    , xmr, xmrinv, xmr2                        &
                                    , sst_limit_cd, betastar, sig_w2_inv       &
                                    , slen(i), rnu, rho                        &
                                    , gradx(:,i), grady(:,i), gradz(:,i)       &
                                    , tke, omega, n_tot, turbulence_model_int)
! FIXME - eventually kkl should not be using blending functions, both
! f1 and f2
         if ( turbulence_model_int == sst_kkl ) sst_f1(i) = 1.0_dp

         if (get_f2) then
          dist   = max( slen(i), my_eps )
          sst_f2(i) = function_sst_f2( xmr, turb(trb_kloc,i), turb(trb_wloc,i) &
                                     , rnu, betastar, dist )
! FIXME - eventually kkl should not be using blending functions, both
! f1 and f2
          if ( turbulence_model_int == sst_kkl ) sst_f2(i) = 1.0_dp
         end if
       end do
       call lmpi_xfer( sst_f1 )
       if (get_f2) then
         call lmpi_xfer( sst_f2 )
       end if
    end select

!-----------------------------------------------------------------------------80
!                             viscous diffusion
!-----------------------------------------------------------------------------80
    ! compute contribution of viscous terms to the residuals
    do ielem = 1, nelem
      call turb_resid_diff        (eqn_set, viscous_method, nnodes0, nnodes01  &
           , nedgeloc, eptr, turb, qnode, res, gradx, grady                    &
           , gradz, xn, yn, zn, ra, ielem, elem(ielem)%ncell                   &
           , elem(ielem)%c2n,                  x, y, z                         &
           , elem(ielem)%local_f2n, elem(ielem)%local_e2n                      &
           , elem(ielem)%local_f2e, elem(ielem)%e2n_2d                         &
           , nedgeloc_2d, elem(ielem)%face_per_cell                            &
           , elem(ielem)%node_per_cell                                         &
           , elem(ielem)%edge_per_cell, elem(ielem)%type_cell                  &
           , n_turb, n_tot, n_grd, elem(ielem)%face_2d, amut                   &
           , sst_f1, elem(ielem)%chk_norm )
    end do

!-----------------------------------------------------------------------------80
!                               source terms
!-----------------------------------------------------------------------------80
    turb_model_type:  select case ( turbulence_model_int )

    case ( kw_sst, kw_sst2003, sst_v, sst, sst_2003, bsl, kw_des               &
         , wilcox_asm, wilcox2006, wilcox2006_v                                &
         , wilcox1988, wilcox1988_v, wilcox_kw88p , wilcox_kw88                &
         , EASMko2003_S                                                        &
         , wilcox_kw98                                                         &
         , wilcox_kw06, wilcox_kw06p, wilcox_les                               &
         , abid_linear, easm_ddes, chien, k_kL_MEAH2013, sst_kkl )

      call turb_resid_2eqn( turbulence_model_int,                              &
                       eqn_set, nnodes0, nnodes01,                             &
                       nedgeloc, eptr, turb, qnode, res, slen, gradx, grady,   &
                       gradz, vol, xn, yn, zn, ra, x, y, z, nedgeloc_2d,       &
                       nnodes0_2d, node_pairs_2d, iflagslen, n_turb,           &
                       n_tot, n_grd, nelem, elem,                              &
                       nbound, bc, dxdt, dydt, dzdt, rhotauij,                 &
                       sst_f1, sst_f2 )

    case ( asbm_sst, kw_lag )

      call turb_resid_3eqn( eqn_set, n_turb, n_tot, n_grd, nnodes0,            &
                            nnodes01, nnodes0_2d, node_pairs_2d, qnode,        &
                            turb, gradx, grady, gradz, x, y, z, slen,          &
                            iflagslen, vol, rhotauij, res )

    case  ( gamma_ret_sst )

      call turb_resid_4eqn(eqn_set, nnodes0, nnodes01,                         &
                           turb, qnode, res, slen, gradx, grady,               &
                           gradz, vol, nnodes0_2d, node_pairs_2d, n_turb,      &
                           n_tot, n_grd, amut, sst_f1, crossd )


    case  ( WilcoxRSM_w2006,WilcoxRSM_w2006c,SSGLRR_RSM_w2012_SD,              &
            SSGLRR_RSM_w2012 )

      call turb_resid_7eqn(eqn_set, nnodes0, nnodes01,                         &
                           turb, qnode, res, gradx, grady,                     &
                           gradz, vol, nnodes0_2d, node_pairs_2d, n_turb,      &
                           n_tot, n_grd, sst_f1, nbound, bc )

!-----------------------------------------------------------------------------80
    case default
      write(*,*)'Error in routine turb resid...unknown turbulence_model: ',    &
      turbulence_model
      call lmpi_conditional_stop(1,'unknown turbulence_model:turb_resid')

    end select turb_model_type

  end subroutine turb_resid

!================================ TURB_JACOB =================================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for multi-equation models (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine turb_jacob(eqn_set, viscous_method, nnodes0, nnodes01,            &
                       nedgeloc, max_nnz, eptr, turb, qnode, iflagslen, slen   &
                     ,            gradx,                                       &
                       grady, gradz, vol, xn, yn, zn, ra, a_diag, a_off,       &
                       fhelp, nedgeloc_2d, nnodes0_2d, node_pairs_2d,          &
                       x, y, z, nnz01, ia, ja, facespeed, n_turb, n_tot,       &
                       n_grd, nzg2m, g2m, mut, nelem, elem, sst_f1, sst_f2,    &
                       crossd, rhotauij )

    use kinddefs,        only : dp
    use element_types,   only : elem_type
    use solution_types,  only : compressible, incompressible
    use thermo,          only : primitive_q_type, q_type
    use turbulence_info, only : turbulence_model_int, turbulence_model
    use turb_2eqn,       only : turb_jacob_2eqn
    use turb_3eqn,       only : turb_jacob_3eqn
    use turb_4eqn,       only : turb_jacob_4eqn
    use turb_7eqn,       only : turb_jacob_7eqn
    use turb_convection, only : turb_jacob_conv
    use turb_diffusion,  only : turb_jacob_diff


    integer, intent(in) :: eqn_set, viscous_method, nelem
    integer, intent(in) :: n_tot, n_grd
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: nnodes0_2d
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: nnz01

    integer, dimension(2,nedgeloc),          intent(in) :: eptr
    integer, dimension(2,nedgeloc),          intent(in) :: fhelp
    integer, dimension(2,nnodes0_2d),        intent(in) :: node_pairs_2d
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(nnz01),               intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m

    integer,   dimension(nnodes01),              intent(in)    :: iflagslen
    real(dp),  dimension(nnodes01),              intent(in)    :: slen
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradz
    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z, vol
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),              intent(in)    :: facespeed
    real(dp),  dimension(nnodes01),              intent(in)    :: mut
    real(dp),  dimension(n_turb,nnodes01),       intent(inout) :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off
    real(dp),  dimension(nnodes01),              intent(inout) :: sst_f1
    real(dp),  dimension(nnodes01),              intent(inout) :: sst_f2
    real(dp),  dimension(nnodes0),               intent(inout) :: crossd
    real(dp), dimension(6,nnodes01),             intent(in)    :: rhotauij

    type(elem_type), dimension(nelem), intent(in) :: elem

    integer :: ierr
    integer :: ielem

  continue

    ierr = 0

    select case ( eqn_set )
    case ( compressible )
      ierr = primitive_q_type - q_type
      call lmpi_conditional_stop(ierr,'q_type not primitive:turb_jacob')
    case ( incompressible )
    case default
      call lmpi_conditional_stop(1,'eqn_set:turb_jacob')
    end select

!-----------------------------------------------------------------------------80
!                               convection
!-----------------------------------------------------------------------------80

    call turb_jacob_conv( nnodes0, nedgeloc, eptr, qnode,                      &
                          xn, yn, zn, ra, a_diag,                              &
                          a_off, fhelp, facespeed, n_turb,                     &
                          g2m, nedgeloc_2d )

!-----------------------------------------------------------------------------80
!                            viscous diffusion
!-----------------------------------------------------------------------------80
!   diffusion terms on non-tetrahedral element types
    viscous_method_conditional:  if ( viscous_method == 0 ) then
        element_loop:  do ielem = 1, nelem
        call turb_jacob_diff( eqn_set, nnodes0, nnodes01, max_nnz,             &
                              qnode, a_diag, a_off, elem(ielem)%ncell,         &
                              elem(ielem)%c2n,                                 &
                              x, y, z, elem(ielem)%type_cell,                  &
                              elem(ielem)%local_f2n, elem(ielem)%local_e2n,    &
                              elem(ielem)%local_f2e, elem(ielem)%e2n_2d,       &
                              elem(ielem)%face_per_cell,                       &
                              elem(ielem)%node_per_cell,                       &
                              elem(ielem)%edge_per_cell, nnz01, ia, ja, n_turb,&
                              n_tot, elem(ielem)%face_2d, nzg2m, g2m, mut,     &
                              turb, sst_f1, elem(ielem)%chk_norm )

        end do element_loop
    endif viscous_method_conditional

!-----------------------------------------------------------------------------80
!                                source terms
!-----------------------------------------------------------------------------80
    turb_model_type:  select case ( turbulence_model_int )

    case ( kw_sst, kw_sst2003, sst_v, sst, sst_2003, wilcox_asm,               &
           wilcox2006, wilcox2006_v,                                           &
           wilcox1988, wilcox1988_v, wilcox_kw88p , wilcox_kw88,               &
           EASMko2003_S,                                                       &
           wilcox_kw98,                                                        &
           wilcox_kw06, wilcox_kw06p, wilcox_les, kw_des,                      &
           abid_linear, easm_ddes, chien, k_kL_MEAH2013,                       &
           bsl, sst_kkl )

      call turb_jacob_2eqn ( turbulence_model_int,                             &
                             eqn_set, nnodes0, nnodes01,                       &
                             turb, qnode, iflagslen, slen, gradx, grady, gradz,&
                             vol, a_diag, nnodes0_2d, node_pairs_2d,           &
                             n_turb, n_tot, n_grd, g2m,                        &
                             sst_f1, sst_f2, crossd, rhotauij )

    case ( asbm_sst, kw_lag )

      call turb_jacob_3eqn( eqn_set, n_turb, n_tot, n_grd, nnodes0,            &
                            nnodes01, nnodes0_2d, node_pairs_2d, g2m,          &
                            qnode, turb, gradx, grady, gradz, vol,             &
                            iflagslen, rhotauij, a_diag )

    case ( gamma_ret_sst )

      call turb_jacob_4eqn( eqn_set, nnodes0, nnodes01,                        &
                            turb, qnode, slen, gradx, grady, gradz, vol,       &
                            a_diag, nnodes0_2d, node_pairs_2d,                 &
                            n_turb, n_tot,                                     &
                            n_grd, g2m, mut, sst_f1, crossd )

    case ( WilcoxRSM_w2006,WilcoxRSM_w2006c,SSGLRR_RSM_w2012_SD,               &
           SSGLRR_RSM_w2012 )

      call turb_jacob_7eqn(eqn_set, nnodes0, nnodes01,                         &
                       turb, qnode,                                            &
                       vol, a_diag,                                            &
                       nnodes0_2d, node_pairs_2d,                              &
                       n_turb, n_tot,                                          &
                       g2m, sst_f1)

!-----------------------------------------------------------------------------80
    case default
      write(*,*)'Error in routine turb jacob...unknown turbulence_model: ',    &
      turbulence_model
      call lmpi_conditional_stop(1,'unknown turbulence_model:turb_jacob')
    end select turb_model_type

  end subroutine turb_jacob

!============================= DUMP_Q ========================================80
!
! Writes to std out q arrya for all processors
!
!=============================================================================80

  subroutine dump_q (grid, soln, location )

    use grid_types,     only : grid_type
    use solution_types, only : soln_type

    type(grid_type),   intent(in) :: grid
    type(soln_type),   intent(in) :: soln
    character(len=*),  intent(in) :: location

     integer                                 :: i, j
     integer                                 :: n_total
     integer                                 :: proc_id
     integer                                 :: master_id
     integer                                 :: ierr
     integer                                 :: local_nodes
     integer                                 :: array_size
     integer                                 :: size2
     integer, dimension(2)                   :: isendbuf, irecvbuf
     real(dp), dimension(:,:), allocatable :: temp_send
     real(dp), dimension(:,:), allocatable :: temp_diag

  continue

    local_nodes = 0

  n_total = 8 + soln%n_turb + 1
proc_loop: do proc_id = 0, lmpi_nproc-1
  call lmpi_synchronize()
  master_id = 0
  master_needs_data:  if ( proc_id > 0 ) then

    if ( proc_id == lmpi_id ) then
      allocate(temp_send(8+soln%n_turb+1,grid%nnodes0))
      array_size  = size(temp_send)
      do i = 1, grid%nnodes0
          temp_send(1,i)   = grid%x(i)
          temp_send(2,i)   = grid%y(i)
          temp_send(3,i)   = grid%z(i)
        do j = 1, 5
          temp_send(3+j,i)   = soln%q_dof(j,i)
        enddo
        do j = 1, soln%n_turb
          temp_send(8+j,i) = soln%turb(j,i)
        enddo
        temp_send(n_total,i) = soln%amut(i)
      enddo
      isendbuf(1) = grid%nnodes0
      isendbuf(2) = size(temp_send,2)
      call lmpi_send(isendbuf ,2         ,master_id,proc_id+1*lmpi_nproc,ierr)
      call lmpi_send(temp_send,array_size,master_id,proc_id+2*lmpi_nproc,ierr)
      deallocate(temp_send)
    endif
    if ( lmpi_master ) then
      call lmpi_recv(irecvbuf,  2, proc_id, proc_id+1*lmpi_nproc, ierr)
      local_nodes = irecvbuf(1)
      size2       = irecvbuf(2)
      allocate(temp_diag(n_total,max(1,size2)))
      call lmpi_recv(temp_diag,n_total*size2,proc_id, proc_id+2*lmpi_nproc,ierr)
    endif

    if ( lmpi_master ) then
      do i = 1, local_nodes
       write(6,'(a,i3,i6,15(1x,es15.8))')    &
       location, proc_id, i, temp_diag(1:n_total,i)
      enddo
      deallocate(temp_diag)
    endif

  else master_needs_data
   if ( lmpi_master ) then
      do i = 1,grid%nnodes0
     write(6,'(a,i9,15(1x,es15.8))')                                           &
     location, i, grid%x(i), grid%y(i), grid%z(i),                             &
                  soln%q_dof(1:5,i), soln%turb(1:soln%n_turb,i), soln%amut(i)
      enddo
    endif
  endif master_needs_data
enddo proc_loop

  end subroutine dump_q

!============================= DUMP_UPDATE ===================================80
!
! Writes to std out dq and dturb arrys for all processors
!
!=============================================================================80

  subroutine dump_update (grid, soln, location )

    use grid_types,     only : grid_type
    use solution_types, only : soln_type

    type(grid_type),   intent(in) :: grid
    type(soln_type),   intent(in) :: soln
    character(len=*),  intent(in) :: location

     integer                                 :: i, j
     integer                                 :: n_grd
     integer                                 :: proc_id
     integer                                 :: master_id
     integer                                 :: ierr
     integer                                 :: local_nodes
     integer                                 :: array_size
     integer                                 :: size3
     integer, dimension(2)                   :: isendbuf, irecvbuf
     real(dp), dimension(:,:), allocatable :: temp_send
     real(dp), dimension(:,:), allocatable :: temp_diag

  continue

    local_nodes = 0

  n_grd = 5 + soln%n_turb
proc_loop: do proc_id = 0, lmpi_nproc-1
  call lmpi_synchronize()
  master_id = 0
  master_needs_data:  if ( proc_id > 0 ) then

    if ( proc_id == lmpi_id ) then
      allocate(temp_send(n_grd,grid%nnodes0))
      array_size  = size(temp_send)
      do i = 1, grid%nnodes0
        do j = 1, 5
          temp_send(j,i)   = soln%dq(j,i)
        enddo
        do j = 1, soln%n_turb
          temp_send(5+j,i) = soln%dturb(j,i)
        enddo
      enddo
      isendbuf(1) = grid%nnodes0
      isendbuf(2) = size(temp_send,2)
      call lmpi_send(isendbuf ,2         ,master_id,proc_id+1*lmpi_nproc,ierr)
      call lmpi_send(temp_send,array_size,master_id,proc_id+2*lmpi_nproc,ierr)
      deallocate(temp_send)
    endif
    if ( lmpi_master ) then
      call lmpi_recv(irecvbuf,  2, proc_id, proc_id+1*lmpi_nproc, ierr)
      local_nodes = irecvbuf(1)
      size3       = irecvbuf(2)
      allocate(temp_diag(n_grd,max(1,size3)))
      call lmpi_recv(temp_diag,n_grd*size3,proc_id,proc_id+2*lmpi_nproc,ierr)
    endif

    if ( lmpi_master ) then
      do i = 1, local_nodes
       write(6,'(a,i3,i6,15(1x,es15.8))')    &
       location, proc_id, i, temp_diag(1:n_grd,i)
      enddo
      deallocate(temp_diag)
    endif

  else master_needs_data
   if ( lmpi_master ) then
      do i = 1,grid%nnodes0
     write(6,'(a,i9,15(1x,es15.8))')    &
     location, i, soln%dq(1:5,i), soln%dturb(1:soln%n_turb,i)
      enddo
    endif
  endif master_needs_data
enddo proc_loop

  end subroutine dump_update

!============================= DUMP_RESIDUALS ================================80
!
! Writes to std out residual arrya for all processors
!
!=============================================================================80

  subroutine dump_residuals (grid, soln, location )

    use grid_types,     only : grid_type
    use solution_types, only : soln_type

    type(grid_type),   intent(in) :: grid
    type(soln_type),   intent(in) :: soln
    character(len=*),  intent(in) :: location

     integer                                 :: i, j
     integer                                 :: n_grd
     integer                                 :: proc_id
     integer                                 :: master_id
     integer                                 :: ierr
     integer                                 :: local_nodes
     integer                                 :: array_size
     integer                                 :: size3
     integer, dimension(2)                   :: isendbuf, irecvbuf
     real(dp), dimension(:,:), allocatable :: temp_send
     real(dp), dimension(:,:), allocatable :: temp_diag

  continue

    local_nodes = 0

  n_grd = 5 + soln%n_turb
proc_loop: do proc_id = 0, lmpi_nproc-1
  call lmpi_synchronize()
  master_id = 0
  master_needs_data:  if ( proc_id > 0 ) then

    if ( proc_id == lmpi_id ) then
      allocate(temp_send(n_grd,grid%nnodes0))
      array_size  = size(temp_send)
      do i = 1, grid%nnodes0
        do j = 1, 5
          temp_send(j,i)   = soln%res(j,i)
        enddo
        do j = 1, soln%n_turb
          temp_send(5+j,i) = soln%turbres(j,i)
        enddo
      enddo
      isendbuf(1) = grid%nnodes0
      isendbuf(2) = size(temp_send,2)
      call lmpi_send(isendbuf ,2         ,master_id,proc_id+1*lmpi_nproc,ierr)
      call lmpi_send(temp_send,array_size,master_id,proc_id+2*lmpi_nproc,ierr)
      deallocate(temp_send)
    endif
    if ( lmpi_master ) then
      call lmpi_recv(irecvbuf,  2, proc_id, proc_id+1*lmpi_nproc, ierr)
      local_nodes = irecvbuf(1)
      size3       = irecvbuf(2)
      allocate(temp_diag(n_grd,max(1,size3)))
      call lmpi_recv(temp_diag,n_grd*size3,proc_id,proc_id+2*lmpi_nproc,ierr)
    endif

    if ( lmpi_master ) then
      do i = 1, local_nodes
       write(6,'(a,i3,i6,11(1x,es15.8))')    &
       location, proc_id, i, temp_diag(1:n_grd,i)
      enddo
      deallocate(temp_diag)
    endif

  else master_needs_data
   if ( lmpi_master ) then
      do i = 1,grid%nnodes0
     write(6,'(a,i9,11(1x,es15.8))')    &
     location, i, soln%res(1:5,i), soln%turbres(1:soln%n_turb,i)
      enddo
    endif
  endif master_needs_data
enddo proc_loop

  end subroutine dump_residuals

!============================= JACOBIAN_KW_SST ===============================80
!
! Driver routine for jacobian evaluation for kw-SST model
!
!=============================================================================80

  subroutine dump_jacobians (grid, soln, location )

    use grid_types,     only : grid_type
    use solution_types, only : soln_type

    type(grid_type),   intent(in) :: grid
    type(soln_type),   intent(in) :: soln
    character(len=*),  intent(in) :: location

     integer                                 :: i, j, k
     integer                                 :: n_grd
     integer                                 :: proc_id
     integer                                 :: master_id
     integer                                 :: ierr
     integer                                 :: local_nodes
     integer                                 :: array_size
     integer                                 :: size3
     integer, dimension(2)                   :: isendbuf, irecvbuf
     real(dp), dimension(:,:,:), allocatable :: temp_send
     real(dp), dimension(:,:,:), allocatable :: temp_diag

  continue

    local_nodes = 0

  n_grd = 5 + soln%n_turb
proc_loop: do proc_id = 0, lmpi_nproc-1
  call lmpi_synchronize()
  master_id = 0
  master_needs_data:  if ( proc_id > 0 ) then

    if ( proc_id == lmpi_id ) then
      allocate(temp_send(n_grd,n_grd,grid%nnodes0))
      array_size  = size(temp_send)
      do i = 1, grid%nnodes0
        do j = 1, soln%njac
          do k = 1, soln%njac
            temp_send(k,j,i)   = soln%a_diag(k,j,i)
          enddo
        enddo
        do j = 1, soln%n_turb
          do k = 1, soln%n_turb
            temp_send(soln%njac+k,soln%njac+j,i) = soln%a_turb_diag(k,j,i)
          enddo
        enddo
      enddo
      isendbuf(1) = grid%nnodes0
      isendbuf(2) = size(temp_send,3)
      call lmpi_send(isendbuf ,2         ,master_id,proc_id+1*lmpi_nproc,ierr)
      call lmpi_send(temp_send,array_size,master_id,proc_id+2*lmpi_nproc,ierr)
      deallocate(temp_send)
    endif
    if ( lmpi_master ) then
      call lmpi_recv(irecvbuf,  2, proc_id, proc_id+1*lmpi_nproc, ierr)
      local_nodes = irecvbuf(1)
      size3       = irecvbuf(2)
      allocate(temp_diag(n_grd,n_grd,max(1,size3)))
      call lmpi_recv(temp_diag,n_grd*n_grd*size3,proc_id &
                    ,proc_id+2*lmpi_nproc,ierr)
    endif

    if ( lmpi_master ) then
      do i = 1, local_nodes
       write(6,'(a,i3,i6,11(1x,es15.8))')                     &
       location, proc_id, i,                                  &
        temp_diag(1,1,i), temp_diag(2,2,i), temp_diag(5,5,i), &
        temp_diag(6,6,i), temp_diag(7,7,i)
      enddo
      deallocate(temp_diag)
    endif

  else master_needs_data
   if ( lmpi_master ) then
      do i = 1,grid%nnodes0
     write(6,'(a,i9,11(1x,es15.8))')                             &
     location, i,                                                &
      soln%a_diag(1,1,i), soln%a_diag(2,2,i), soln%a_diag(5,5,i) &
    , soln%a_turb_diag(1,1,i), soln%a_turb_diag(2,2,i)
      enddo
    endif
  endif master_needs_data
enddo proc_loop

  end subroutine dump_jacobians

!============================= GET_INDEX =====================================80
!
! Switch index if 2D
!
!=============================================================================80
  pure function get_index ( twod, node_pairs_2d, ii ) result ( i )

    integer                             :: i
    logical,                 intent(in) :: twod
    integer, dimension(:,:), intent(in) :: node_pairs_2d
    integer,                 intent(in) :: ii

  continue

    if (twod) then
      i = node_pairs_2d(1,ii)
    else
      i = ii
    end if

  end function get_index

  include 'function_sst_f1.f90'
  include 'function_sst_f2.f90'
  include 'viscosity_law.f90'

end module turb_main

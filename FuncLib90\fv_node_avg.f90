!================================= FV_NODE_AVG ===============================80
!
! Full viscous compressible meanflow interior fluxes.
!
!=============================================================================80
  pure function fv_node_avg( xnorm, ynorm, znorm, area,                        &
                             qpl, qpr, amutl, amutr, qtgrad )

    use flux_constants, only : gamma, cstar

    real(dp), intent(in) :: xnorm, ynorm, znorm, area, amutl, amutr

    real(dp), dimension(5),   intent(in) :: qpl, qpr !primitive variables
    real(dp), dimension(3,5), intent(in) :: qtgrad   !T variables

    real(dp), dimension(5) :: fv_node_avg

    real(dp) :: u, v, w, t, mu, amut

  continue

    u = 0.5_dp*( qpl(2) + qpr(2) )
    v = 0.5_dp*( qpl(3) + qpr(3) )
    w = 0.5_dp*( qpl(4) + qpr(4) )
    t = 0.5_dp*( qpl(5)/qpl(1) + qpr(5)/qpr(1) )*gamma

    mu   = viscosity_law( cstar, t )
    amut = 0.5_dp*( amutl + amutr )

    fv_node_avg = tau_v( xnorm, ynorm, znorm, area, &
                         qtgrad, u, v, w, mu, amut )

  end function fv_node_avg

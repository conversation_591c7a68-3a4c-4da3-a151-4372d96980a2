module overset

  use kinddefs,          only : dp, odp
  use overset_defs,      only : debug_overset

  implicit none

  private

  public  :: blank_rhs_equation, blank_lhs_equation
  public  :: blank_rhs_turb_equation, blank_lhs_turb_equation
  public  :: blank_rhs_equation_adj, dci_sanity_checks

  contains

!================================ BLANK_RHS_EQUATION =========================80
!
! Blanks the governing equations for nodes that should not be included in
! the solution.
!
!=============================================================================80

  subroutine blank_rhs_equation(nnodes01, jac_dim, iblank, res)

    integer, intent(in) :: nnodes01, jac_dim

    integer, dimension(nnodes01), intent(in) :: iblank

    real(dp), dimension(jac_dim,nnodes01), intent(inout) :: res

    real(dp),  parameter :: my_0  = 0.0_dp

    integer :: inode, j

  continue

    if (debug_overset) write(*,*) "OVERSET: Entered blank_rhs_equation"

    node_loop: do inode = 1,nnodes01

      blank_node: if (iblank(inode) <= 0) then

        blank_residual: do j = 1,jac_dim
          res(j,inode) = my_0
        end do blank_residual

      end if blank_node

    end do node_loop

    if (debug_overset) write(*,*) "OVERSET: Leaving blank_rhs_equation"

  end subroutine blank_rhs_equation


!================================ BLANK_LHS_EQUATION =========================80
!
! Blanks the governing equations for nodes that should not be included in
! the solution.
!
!=============================================================================80

  subroutine blank_lhs_equation(nnodes0, nnodes01, jac_dim, max_nnz, iblank,   &
                                iam, a_diag, a_off, g2m)

    integer, intent(in) :: nnodes0, nnodes01, jac_dim, max_nnz

    integer, dimension(nnodes01),   intent(in) :: iblank
    integer, dimension(nnodes01+1), intent(in) :: iam
    integer, dimension(:),          intent(in) :: g2m

    real(dp),  dimension(jac_dim,jac_dim,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(jac_dim,jac_dim,max_nnz), intent(inout) :: a_off

    real(dp),  parameter :: my_0  = 0.0_dp
    real(odp), parameter :: odp_0 = 0.0_odp
    real(dp),  parameter :: my_1  = 1.0_dp

    integer :: inode, ioff, j, k, row

  continue

    if (debug_overset) write(*,*) "OVERSET: Entered blank_lhs_equation"

    node_loop: do inode = 1,nnodes0

      blank_node: if (iblank(inode) <= 0) then

        do j = 1,jac_dim
          do k = 1,jac_dim
            if (j == k) then
              a_diag(j,j,g2m(inode)) = my_1
            else
              a_diag(j,k,g2m(inode)) = my_0
            end if
          end do
        end do

        row = g2m(inode)
        do ioff = iam(row), iam(row+1)-1
          do j = 1,jac_dim
            do k = 1,jac_dim
              a_off(j,k,ioff) = odp_0
            end do
          end do
        end do

      end if blank_node

    end do node_loop

    if (debug_overset) write(*,*) "OVERSET: Leaving blank_lhs_equation"

  end subroutine blank_lhs_equation


!============================= BLANK_RHS_TURB_EQUATION =======================80
!
! Blanks the loosely coupled turbulence equations for nodes that should not
! be included in the solution. This module is identical to blank_equation,
! except for the real kind of the off diagonal.
!
!=============================================================================80

  subroutine blank_rhs_turb_equation(nnodes01, n_turb, iblank, turbres)

    integer, intent(in) :: nnodes01, n_turb

    integer, dimension(nnodes01), intent(in) :: iblank

    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turbres

    integer :: inode, j

    real(dp),  parameter :: my_0 = 0.0_dp

  continue

    if (debug_overset) write(*,*) "OVERSET: Entered blank_rhs_turb_equation"

    node_loop: do inode = 1,nnodes01

      blank_node: if (iblank(inode) <= 0) then

        blank_residual: do j = 1,n_turb
          turbres(j,inode) = my_0
        end do blank_residual

      end if blank_node

    end do node_loop

    if (debug_overset) write(*,*) "OVERSET: Leaving blank_rhs_turb_equation"

  end subroutine blank_rhs_turb_equation


!============================= BLANK_LHS_TURB_EQUATION =======================80
!
! Blanks the loosely coupled turbulence equations for nodes that should not
! be included in the solution. This module is identical to blank_equation,
! except for the real kind of the off diagonal.
!
!=============================================================================80

  subroutine blank_lhs_turb_equation(nnodes0, nnodes01, n_turb, max_nnz,       &
                                     iblank, iam, a_turb_diag, a_turb_off, g2m)

    integer, intent(in) :: nnodes0, nnodes01, n_turb, max_nnz

    integer, dimension(nnodes01),   intent(in) :: iblank
    integer, dimension(nnodes01+1), intent(in) :: iam
    integer, dimension(:),          intent(in) :: g2m

    real(dp), dimension(n_turb,n_turb,nnodes0),    intent(inout) :: a_turb_diag
    real(odp),   dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_turb_off

    integer :: inode, ioff, j, k, row

    real(dp),  parameter    :: my_0 = 0.0_dp
    real(dp),  parameter    :: my_1 = 1.0_dp

  continue

    if (debug_overset) write(*,*) "OVERSET: Entered blank_lhs_turb_equation"

    node_loop: do inode = 1,nnodes0

      blank_node: if (iblank(inode) <= 0) then

        do j = 1,n_turb
          do k = 1,n_turb
            if (j == k) then
              a_turb_diag(j,j,g2m(inode)) = my_1
            else
              a_turb_diag(j,k,g2m(inode)) = my_0
            end if
          end do
        end do

        row = g2m(inode)
        do ioff = iam(row), iam(row+1)-1
          do j = 1,n_turb
            do k = 1,n_turb
              a_turb_off(j,k,ioff) = my_0
            end do
          end do
        end do

      end if blank_node

    end do node_loop

    if (debug_overset) write(*,*) "OVERSET: Leaving blank_lhs_turb_equation"

  end subroutine blank_lhs_turb_equation


!================================ BLANK_RHS_EQUATION_ADJ =====================80
!
! Blanks the governing equations for nodes that should not be included in
! the solution.
!
! Adjoint
!
!=============================================================================80

  subroutine blank_rhs_equation_adj(iblank, res)

    integer, dimension(:), intent(in) :: iblank

    real(dp), dimension(:,:,:), intent(inout) :: res

    real(dp),  parameter :: my_0  = 0.0_dp

    integer :: inode

  continue

    if (debug_overset) write(*,*) "OVERSET: Entered blank_rhs_equation_adj"

    do inode = 1,size(res,2)
      if (iblank(inode) <= 0) res(:,inode,:) = my_0
    end do

    if (debug_overset) write(*,*) "OVERSET: Leaving blank_rhs_equation_adj"

  end subroutine blank_rhs_equation_adj

!================================= DCI_SANITY_CHECKS =========================80
!
! Performs some basic sanity checking on imesh and iblank
!
!=============================================================================80
  subroutine dci_sanity_checks(grid)

    use info_depr,         only : twod
    use nml_global,        only : moving_grid
    use grid_types,        only : grid_type
    use lmpi,              only : lmpi_reduce, lmpi_bcast, lmpi_master,        &
                                  lmpi_id, lmpi_die, lmpi_conditional_stop
    use system_extensions, only : se_flush
    use nml_grid_motion,   only : n_moving_bodies

    type(grid_type), intent(in) :: grid

    integer :: nodes_with_imesh_ne_0, i, total_nodes_with_imesh_ne_0, j
    integer :: imesh_min, imesh_max, count, elem, sum, cellnodes, node1, node2

  continue

!   Check for potential problems with the imesh data for moving grids

    check_imesh_for_dynamic_case : if (moving_grid) then

!     check that at least some points have imesh /= 0; all points with
!     imesh = 0 probably mean the user ran suggar for the static/composite
!     grid assembly without a <dynamic/> element in the <body> element
!     bodies that are intended to move.

      nodes_with_imesh_ne_0 = 0

      do i=1,grid%nnodes01
        if (grid%imesh(i) > 0) then
          nodes_with_imesh_ne_0 = nodes_with_imesh_ne_0 + 1
        end if
      end do

      call lmpi_reduce(nodes_with_imesh_ne_0, total_nodes_with_imesh_ne_0)
      call lmpi_bcast(total_nodes_with_imesh_ne_0)

      if (lmpi_master .and. total_nodes_with_imesh_ne_0 == 0) then
        write(*,'(a)') ' In subroutine dci_sanity_checks:'
        write(*,'(a)') '   Error in dci imesh data: all imesh values are zero'
        write(*,'(a)') '   this likely means when you ran SUGGAR to set up'
        write(*,'(a)') '   the composite mesh, you did not include <dynamic/>'
        write(*,'(a)') '   in the <body> element for moving bodies'
        call se_flush()
        call lmpi_die
      end if

!     check that the range of imesh values is sensible

      imesh_min =  huge(1)
      imesh_max = -huge(1)

      do i=1,grid%nnodes01
        if (grid%imesh(i) > imesh_max) imesh_max = grid%imesh(i)
        if (grid%imesh(i) < imesh_min) imesh_min = grid%imesh(i)
      end do

      if (imesh_min < 0 .or. imesh_max > n_moving_bodies) then
        write(*,'(a,2i6)') 'error in dci imesh data: mesh_min, imesh_max = ',  &
                            imesh_min, imesh_max
        write(*,'(a,i6)') ' valid range is 0 to ', n_moving_bodies
        call se_flush()
        call lmpi_die
      end if

    end if check_imesh_for_dynamic_case

!   Make sure all nodes of a cell have the same imesh value
!   If not, something is seriously wrong

    count = 0

    do elem = 1, grid%nelem
      do i=1,grid%elem(elem)%ncell
        sum = 0
        cellnodes = grid%elem(elem)%node_per_cell
        do j=1,cellnodes
          sum = sum + grid%imesh(grid%elem(elem)%c2n(j,i))
        end do
        sum = sum/cellnodes
        if (sum /= grid%imesh(grid%elem(elem)%c2n(1,i))) then
          write(lmpi_id+10,*) 'cell, c2n ',i,                                  &
               (grid%elem(elem)%c2n(j,i),j=1,cellnodes)
          write(lmpi_id+10,*) '  ',                                            &
               (grid%imesh(grid%elem(elem)%c2n(j,i)),j=1,cellnodes)
          call se_flush(lmpi_id+10)
          write(*,*) 'in init_overset: error - imesh varies in cell ',i
          call se_flush()
          count = count + 1
        end if
      end do
    end do

    call lmpi_conditional_stop(count)

!   For 2D, make sure the iblank/imesh arrays are the same on both planes

    check_2d : if (twod) then

      count = 0

      do i=1,grid%nnodes0_2d
        node1 = grid%node_pairs_2d(1,i)
        node2 = grid%node_pairs_2d(2,i)
        if (grid%iblank(node2) /= grid%iblank(node1)) then
          count = count + 1
        end if
      end do

      if (count /= 0) then
        write(*,*) 'in init_overset: error - 2D planes have different iblank'
      end if

      call lmpi_conditional_stop(count)

      if (moving_grid) then

        do i=1,grid%nnodes0_2d
          node1 = grid%node_pairs_2d(1,i)
          node2 = grid%node_pairs_2d(2,i)
          if (grid%imesh(node2) /= grid%imesh(node1)) then
            count = count + 1
          end if
        end do

        if (count /= 0) then
          write(*,*) 'in init_overset: error - 2D planes have different imesh'
        end if

      end if

    end if check_2d

  end subroutine dci_sanity_checks

end module overset

!===================================  ==================================80
!
! van Leer flux jacobians
!
! Note: this function uses conservative variables
!
! The left  jacobian is returned in vnlrFJc(:,:,1)
! The right jacobian is returned in vnlrFJc(:,:,2)
!
!=============================================================================80

  pure function vnlrFJc(xnorm, ynorm, znorm, area, face_speed, ql, qr)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_4th, my_half, my_1, my_2, my_4

    use fluid,           only : gamma, gm1, ggm1

    real(dp),               intent(in) :: xnorm, ynorm, znorm, area
    real(dp),               intent(in) :: face_speed
    real(dp), dimension(5), intent(in) :: ql, qr

    real(dp), dimension(5,5,2)         :: vnlrFJc

    real(dp) :: rho, u, v, w, p
    real(dp) :: q2, enrgy, c, c1, c2
    real(dp) :: ubar, fmach, ubp2a, ubm2a
    real(dp) :: dcdr, dcdru, dcdrv, dcdrw, dcde
    real(dp) :: dfdq1, dfdq2, dfdq3, dfdq4, dfdq5
    real(dp) :: fluxm1, fluxm2, fluxm3, fluxm4, fluxm5
    real(dp) :: fluxp1, fluxp2, fluxp3, fluxp4, fluxp5
    real(dp) :: fof, phi

  continue

!   take care of the jacobian due to the left state (dfl)

    rho   = ql(1)
    u     = ql(2)/rho
    v     = ql(3)/rho
    w     = ql(4)/rho
    q2    = u*u + v*v + w*w
    enrgy = ql(5)
    p     = gm1*(enrgy-my_half*rho*q2)
    c     = sqrt(gamma*p/rho)
    ubar  = xnorm*u + ynorm*v + znorm*w  - face_speed
    fmach = ubar / c
    ubp2a = -ubar + my_2*c

!   if subsonic calculate dfl and add contribution to A

    first_machvalue_check: if (abs(fmach) < my_1) then

!     get first order fluxes

      fluxp1 = area * my_4th * rho * c * (fmach+1)**2
      fluxp2 = fluxp1 * (xnorm*ubp2a/gamma+u)
      fluxp3 = fluxp1 * (ynorm*ubp2a/gamma+v)
      fluxp4 = fluxp1 * (znorm*ubp2a/gamma+w)
      fluxp5 = fluxp1*((-gm1*ubar*ubar                                         &
                   + my_2*gm1*ubar*c + my_2*c*c)/(gamma*gamma - my_1)          &
                   + my_half*q2 + face_speed*(-ubar + my_2*c)/gamma)

!     derivatives of speed of sound

      dcdr  =  my_half * ggm1 * (q2-enrgy/rho) / (rho*c)
      dcdru = -my_half*ggm1*u/(rho*c)
      dcdrv = -my_half*ggm1*v/(rho*c)
      dcdrw = -my_half*ggm1*w/(rho*c)
      dcde  =  my_half * ggm1 / (rho*c)

!     if subsonic calculate dfl and add contribution to Jacobian

      dfdq1 = my_4th*area*(my_1 - fmach**2)*(c + rho*dcdr)                     &
            - my_half*area*face_speed*(fmach + my_1)
      dfdq2 = my_4th*area*(my_2*xnorm*(fmach + my_1)                           &
            + rho*(my_1 - fmach**2)*dcdru)
      dfdq3 = my_4th*area*(my_2*ynorm*(fmach + my_1)                           &
            + rho*(my_1 - fmach**2)*dcdrv)
      dfdq4 = my_4th*area*(my_2*znorm*(fmach + my_1)                           &
            + rho*(my_1 - fmach**2)*dcdrw)
      dfdq5 = my_4th*area*rho*dcde*(my_1 - fmach**2)

      vnlrFJc(1,1,1) = dfdq1
      vnlrFJc(1,2,1) = dfdq2
      vnlrFJc(1,3,1) = dfdq3
      vnlrFJc(1,4,1) = dfdq4
      vnlrFJc(1,5,1) = dfdq5

      fof = fluxp2 / fluxp1
      vnlrFJc(2,1,1) = fluxp1*(my_2*xnorm/gamma*dcdr                           &
                    + xnorm*(ubar+face_speed)/gamma/rho - u/rho) + fof*dfdq1
      vnlrFJc(2,2,1) = fluxp1*(-xnorm*xnorm/gamma/rho                          &
                    + my_2*xnorm*dcdru/gamma + my_1/rho) + fof*dfdq2
      vnlrFJc(2,3,1) = fluxp1*(-xnorm*ynorm/gamma/rho                          &
                    + my_2*xnorm*dcdrv/gamma) + fof*dfdq3
      vnlrFJc(2,4,1) = fluxp1*(-xnorm*znorm/gamma/rho                          &
                    + my_2*xnorm*dcdrw/gamma) + fof*dfdq4
      vnlrFJc(2,5,1) = fluxp1*(2*xnorm*dcde/gamma)                             &
                    + fof*dfdq5

      fof = fluxp3/fluxp1
      vnlrFJc(3,1,1) = fluxp1*(my_2*ynorm/gamma*dcdr                           &
                    + ynorm*(ubar+face_speed)/gamma/rho - v/rho) + fof*dfdq1
      vnlrFJc(3,2,1) = fluxp1*(-ynorm*xnorm/gamma/rho                          &
                    + my_2*ynorm*dcdru/gamma) + fof*dfdq2
      vnlrFJc(3,3,1) = fluxp1*(-ynorm*ynorm/gamma/rho                          &
                    + my_2*ynorm*dcdrv/gamma + my_1/rho) + fof*dfdq3
      vnlrFJc(3,4,1) = fluxp1*(-ynorm*znorm/gamma/rho                          &
                    + my_2*ynorm*dcdrw/gamma) + fof*dfdq4
      vnlrFJc(3,5,1) = fluxp1*(my_2*ynorm*dcde/gamma)                          &
                    + fof*dfdq5

      fof = fluxp4/fluxp1
      vnlrFJc(4,1,1) = fluxp1*(my_2*znorm/gamma*dcdr                           &
                    + znorm*(ubar+face_speed)/gamma/rho - w/rho) + fof*dfdq1
      vnlrFJc(4,2,1) = fluxp1*(-znorm*xnorm/gamma/rho                          &
                    + my_2*znorm*dcdru/gamma) + fof*dfdq2
      vnlrFJc(4,3,1) = fluxp1*(-znorm*ynorm/gamma/rho                          &
                    + my_2*znorm*dcdrv/gamma) + fof*dfdq3
      vnlrFJc(4,4,1) = fluxp1*(-znorm*znorm/gamma/rho                          &
                    + my_2*znorm*dcdrw/gamma + my_1/rho) + fof*dfdq4
      vnlrFJc(4,5,1) = fluxp1*(my_2*znorm*dcde/gamma)                          &
                    + fof*dfdq5

      c1  = (my_2*gm1*ubar+my_4*c) / (gamma*gamma-my_1)                        &
          + my_2/gamma*face_speed
      c2  = my_2 * gm1 / (gamma*gamma-my_1) * (-ubar+c) / rho
      fof = fluxp5 / fluxp1

      vnlrFJc(5,1,1) = fluxp1*(dcdr*c1 - c2*(ubar+face_speed) - q2/rho         &
                    + face_speed*(ubar+face_speed)/gamma/rho)                  &
                    + fof*dfdq1
      vnlrFJc(5,2,1) = fluxp1*(dcdru*c1 + c2*xnorm + u/rho                     &
                    - face_speed*xnorm/gamma/rho)                              &
                    + fof*dfdq2
      vnlrFJc(5,3,1) = fluxp1*(dcdrv*c1 + c2*ynorm + v/rho                     &
                    - face_speed*ynorm/gamma/rho)                              &
                    + fof*dfdq3
      vnlrFJc(5,4,1) = fluxp1*(dcdrw*c1 + c2*znorm + w/rho                     &
                    - face_speed*znorm/gamma/rho)                              &
                    + fof*dfdq4
      vnlrFJc(5,5,1) = fluxp1*dcde*c1 + fof*dfdq5

    else if (fmach >= my_1) then

      phi = my_half * gm1 * q2
      vnlrFJc(1,1,1) = -area*face_speed
      vnlrFJc(1,2,1) = area*xnorm
      vnlrFJc(1,3,1) = area*ynorm
      vnlrFJc(1,4,1) = area*znorm
      vnlrFJc(1,5,1) = my_0

      vnlrFJc(2,1,1) = area*(xnorm*phi-u*(ubar+face_speed))
      vnlrFJc(2,2,1) = area*(xnorm*(my_2-gamma)*u+ubar)
      vnlrFJc(2,3,1) = area*(ynorm*u-xnorm*gm1*v)
      vnlrFJc(2,4,1) = area*(znorm*u-xnorm*gm1*w)
      vnlrFJc(2,5,1) = area*xnorm*gm1

      vnlrFJc(3,1,1) = area*(ynorm*phi-v*(ubar+face_speed))
      vnlrFJc(3,2,1) = area*(xnorm*v-ynorm*gm1*u)
      vnlrFJc(3,3,1) = area*(ynorm*(my_2-gamma)*v+ubar)
      vnlrFJc(3,4,1) = area*(znorm*v-ynorm*gm1*w)
      vnlrFJc(3,5,1) = area*ynorm*gm1

      vnlrFJc(4,1,1) = area*(znorm*phi-w*(ubar+face_speed))
      vnlrFJc(4,2,1) = area*(xnorm*w-znorm*gm1*u)
      vnlrFJc(4,3,1) = area*(ynorm*w-znorm*gm1*v)
      vnlrFJc(4,4,1) = area*(znorm*(my_2-gamma)*w+ubar)
      vnlrFJc(4,5,1) = area*znorm*gm1

      vnlrFJc(5,1,1) = area*(my_2*phi-gamma*enrgy/rho)*(ubar+face_speed)
      vnlrFJc(5,2,1) = area*(xnorm*(gamma*enrgy/rho-phi)-                      &
                            gm1*u*(ubar+face_speed))
      vnlrFJc(5,3,1) = area*(ynorm*(gamma*enrgy/rho-phi)-                      &
                            gm1*v*(ubar+face_speed))
      vnlrFJc(5,4,1) = area*(znorm*(gamma*enrgy/rho-phi)-                      &
                            gm1*w*(ubar+face_speed))
      vnlrFJc(5,5,1) = area*(gamma*ubar + gm1*face_speed)

    else

      vnlrFJc(1,1,1) = my_0
      vnlrFJc(1,2,1) = my_0
      vnlrFJc(1,3,1) = my_0
      vnlrFJc(1,4,1) = my_0
      vnlrFJc(1,5,1) = my_0

      vnlrFJc(2,1,1) = my_0
      vnlrFJc(2,2,1) = my_0
      vnlrFJc(2,3,1) = my_0
      vnlrFJc(2,4,1) = my_0
      vnlrFJc(2,5,1) = my_0

      vnlrFJc(3,1,1) = my_0
      vnlrFJc(3,2,1) = my_0
      vnlrFJc(3,3,1) = my_0
      vnlrFJc(3,4,1) = my_0
      vnlrFJc(3,5,1) = my_0

      vnlrFJc(4,1,1) = my_0
      vnlrFJc(4,2,1) = my_0
      vnlrFJc(4,3,1) = my_0
      vnlrFJc(4,4,1) = my_0
      vnlrFJc(4,5,1) = my_0

      vnlrFJc(5,1,1) = my_0
      vnlrFJc(5,2,1) = my_0
      vnlrFJc(5,3,1) = my_0
      vnlrFJc(5,4,1) = my_0
      vnlrFJc(5,5,1) = my_0

    end if first_machvalue_check

!   take care of the jacobian due to the right state (dfr)

    rho   = qr(1)
    u     = qr(2)/rho
    v     = qr(3)/rho
    w     = qr(4)/rho
    enrgy = qr(5)
    p     = gm1*(enrgy-my_half*rho*q2)
    q2    = u*u + v*v + w*w
    c     = sqrt(gamma*p/rho)
    ubar  = xnorm*u + ynorm*v + znorm*w - face_speed
    fmach = ubar/c
    ubm2a = -ubar - my_2*c

!   if subsonic calculate dfr and add contribution to A

    second_machvalue_check: if (abs(fmach) < my_1) then

!     get first order fluxes

      fluxm1 = -area*my_4th*rho*c*(fmach-1)**2
      fluxm2 = fluxm1 * (xnorm*ubm2a/gamma+u)
      fluxm3 = fluxm1 * (ynorm*ubm2a/gamma+v)
      fluxm4 = fluxm1 * (znorm*ubm2a/gamma+w)
      fluxm5 = fluxm1*((-gm1*ubar*ubar                                         &
                   - my_2*gm1*ubar*c + my_2*c*c)/(gamma*gamma - my_1)          &
                   + my_half*q2 + face_speed*(-ubar - my_2*c)/gamma)

!     derivatives of speed of sound

      dcdr  =  my_half*ggm1*(q2 - enrgy/rho)/(rho*c)
      dcdru = -my_half*ggm1*u/(rho*c)
      dcdrv = -my_half*ggm1*v/(rho*c)
      dcdrw = -my_half*ggm1*w/(rho*c)
      dcde  =  my_half*ggm1/(rho*c)

!     if subsonic calculate dfr and subtract contribution to A

      dfdq1 = -my_4th*area*(my_1 - fmach**2)*(c + rho*dcdr)                    &
            +  my_half*area*face_speed*(fmach - my_1)
      dfdq2 = -my_4th*area*(my_2*xnorm*(fmach - my_1)                          &
            + rho*(my_1 - fmach**2)*dcdru)
      dfdq3 = -my_4th*area*(my_2*ynorm*(fmach - my_1)                          &
            + rho*(my_1 - fmach**2)*dcdrv)
      dfdq4 = -my_4th*area*(my_2*znorm*(fmach - my_1)                          &
            + rho*(my_1 - fmach**2)*dcdrw)
      dfdq5 = -my_4th*area*rho*dcde*(my_1 - fmach**2)

      vnlrFJc(1,1,2) = dfdq1
      vnlrFJc(1,2,2) = dfdq2
      vnlrFJc(1,3,2) = dfdq3
      vnlrFJc(1,4,2) = dfdq4
      vnlrFJc(1,5,2) = dfdq5

      fof = fluxm2/fluxm1
      vnlrFJc(2,1,2) = fluxm1*(-my_2*xnorm/gamma*dcdr                          &
                    + xnorm*(ubar+face_speed)/gamma/rho - u/rho) + fof*dfdq1
      vnlrFJc(2,2,2) = fluxm1*(-xnorm*xnorm/gamma/rho                          &
                    - my_2*xnorm*dcdru/gamma + my_1/rho) + fof*dfdq2
      vnlrFJc(2,3,2) = fluxm1*(-xnorm*ynorm/gamma/rho                          &
                    - my_2*xnorm*dcdrv/gamma) + fof*dfdq3
      vnlrFJc(2,4,2) = fluxm1*(-xnorm*znorm/gamma/rho                          &
                    - my_2*xnorm*dcdrw/gamma) + fof*dfdq4
      vnlrFJc(2,5,2) = fluxm1*(-2*xnorm*dcde/gamma)                            &
                    + fof*dfdq5

      fof = fluxm3/fluxm1
      vnlrFJc(3,1,2) = fluxm1*(-my_2*ynorm/gamma*dcdr                          &
                    + ynorm*(ubar+face_speed)/gamma/rho - v/rho) + fof*dfdq1
      vnlrFJc(3,2,2) = fluxm1*(-ynorm*xnorm/gamma/rho                          &
                    - my_2*ynorm*dcdru/gamma) + fof*dfdq2
      vnlrFJc(3,3,2) = fluxm1*(-ynorm*ynorm/gamma/rho                          &
                    - my_2*ynorm*dcdrv/gamma + my_1/rho) + fof*dfdq3
      vnlrFJc(3,4,2) = fluxm1*(-ynorm*znorm/gamma/rho                          &
                    - my_2*ynorm*dcdrw/gamma) + fof*dfdq4
      vnlrFJc(3,5,2) = fluxm1*(-my_2*ynorm*dcde/gamma)                         &
                    + fof*dfdq5

      fof = fluxm4/fluxm1
      vnlrFJc(4,1,2) = fluxm1*(-my_2*znorm/gamma*dcdr                          &
                    + znorm*(ubar+face_speed)/gamma/rho - w/rho) + fof*dfdq1
      vnlrFJc(4,2,2) = fluxm1*(-znorm*xnorm/gamma/rho                          &
                    - my_2*znorm*dcdru/gamma) + fof*dfdq2
      vnlrFJc(4,3,2) = fluxm1*(-znorm*ynorm/gamma/rho                          &
                    - my_2*znorm*dcdrv/gamma) + fof*dfdq3
      vnlrFJc(4,4,2) = fluxm1*(-znorm*znorm/gamma/rho                          &
                    - my_2*znorm*dcdrw/gamma + my_1/rho) + fof*dfdq4
      vnlrFJc(4,5,2) = fluxm1*(-my_2*znorm*dcde/gamma)                         &
                    + fof*dfdq5

      c1  = (-my_2*gm1*ubar + my_4*c)/(gamma*gamma - my_1)                     &
          - my_2/gamma*face_speed
      c2  = my_2*gm1/(gamma*gamma - my_1)*(-ubar - c)/rho
      fof = fluxm5/fluxm1

      vnlrFJc(5,1,2) = fluxm1*(dcdr*c1 - c2*(ubar+face_speed) - q2/rho         &
                    + face_speed*(ubar+face_speed)/gamma/rho)                  &
                    + fof*dfdq1
      vnlrFJc(5,2,2) = fluxm1*(dcdru*c1 + c2*xnorm + u/rho                     &
                    - face_speed*xnorm/gamma/rho)                              &
                    + fof*dfdq2
      vnlrFJc(5,3,2) = fluxm1*(dcdrv*c1 + c2*ynorm + v/rho                     &
                    - face_speed*ynorm/gamma/rho)                              &
                    + fof*dfdq3
      vnlrFJc(5,4,2) = fluxm1*(dcdrw*c1 + c2*znorm + w/rho                     &
                    - face_speed*znorm/gamma/rho)                              &
                    + fof*dfdq4
      vnlrFJc(5,5,2) = fluxm1*dcde*c1 + fof*dfdq5

    else if (fmach <= -my_1) then

      phi = my_half * gm1 * q2
      vnlrFJc(1,1,2) = -area*face_speed
      vnlrFJc(1,2,2) = area*xnorm
      vnlrFJc(1,3,2) = area*ynorm
      vnlrFJc(1,4,2) = area*znorm
      vnlrFJc(1,5,2) = my_0

      vnlrFJc(2,1,2) = area*(xnorm*phi-u*(ubar+face_speed))
      vnlrFJc(2,2,2) = area*(xnorm*(my_2-gamma)*u+ubar)
      vnlrFJc(2,3,2) = area*(ynorm*u-xnorm*gm1*v)
      vnlrFJc(2,4,2) = area*(znorm*u-xnorm*gm1*w)
      vnlrFJc(2,5,2) = area*xnorm*gm1

      vnlrFJc(3,1,2) = area*(ynorm*phi-v*(ubar+face_speed))
      vnlrFJc(3,2,2) = area*(xnorm*v-ynorm*gm1*u)
      vnlrFJc(3,3,2) = area*(ynorm*(my_2-gamma)*v+ubar)
      vnlrFJc(3,4,2) = area*(znorm*v-ynorm*gm1*w)
      vnlrFJc(3,5,2) = area*ynorm*gm1

      vnlrFJc(4,1,2) = area*(znorm*phi-w*(ubar+face_speed))
      vnlrFJc(4,2,2) = area*(xnorm*w-znorm*gm1*u)
      vnlrFJc(4,3,2) = area*(ynorm*w-znorm*gm1*v)
      vnlrFJc(4,4,2) = area*(znorm*(my_2-gamma)*w+ubar)
      vnlrFJc(4,5,2) = area*znorm*gm1

      vnlrFJc(5,1,2) = area*(my_2*phi-gamma*enrgy/rho)*(ubar+face_speed)
      vnlrFJc(5,2,2) = area*(xnorm*(gamma*enrgy/rho-phi)-                      &
                            gm1*u*(ubar+face_speed))
      vnlrFJc(5,3,2) = area*(ynorm*(gamma*enrgy/rho-phi)-                      &
                            gm1*v*(ubar+face_speed))
      vnlrFJc(5,4,2) = area*(znorm*(gamma*enrgy/rho-phi)-                      &
                            gm1*w*(ubar+face_speed))
      vnlrFJc(5,5,2) = area*(gamma*ubar + gm1*face_speed)

    else

      vnlrFJc(1,1,2) = my_0
      vnlrFJc(1,2,2) = my_0
      vnlrFJc(1,3,2) = my_0
      vnlrFJc(1,4,2) = my_0
      vnlrFJc(1,5,2) = my_0

      vnlrFJc(2,1,2) = my_0
      vnlrFJc(2,2,2) = my_0
      vnlrFJc(2,3,2) = my_0
      vnlrFJc(2,4,2) = my_0
      vnlrFJc(2,5,2) = my_0

      vnlrFJc(3,1,2) = my_0
      vnlrFJc(3,2,2) = my_0
      vnlrFJc(3,3,2) = my_0
      vnlrFJc(3,4,2) = my_0
      vnlrFJc(3,5,2) = my_0

      vnlrFJc(4,1,2) = my_0
      vnlrFJc(4,2,2) = my_0
      vnlrFJc(4,3,2) = my_0
      vnlrFJc(4,4,2) = my_0
      vnlrFJc(4,5,2) = my_0

      vnlrFJc(5,1,2) = my_0
      vnlrFJc(5,2,2) = my_0
      vnlrFJc(5,3,2) = my_0
      vnlrFJc(5,4,2) = my_0
      vnlrFJc(5,5,2) = my_0

    end if second_machvalue_check

  end function vnlrFJc




!=================================== VKFLXLS =================================80
!
! Smooth (differentiable) flux limiter applied to a scalar
!
!=============================================================================80

  pure function vkflxls(grad_a, grad_b, eps2)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_2

    real(dp), intent(in) :: eps2
    real(dp), intent(in) :: grad_a, grad_b
    real(dp)             :: vkflxls

  continue

! JCP 118 120-130 (1995)

    vkflxls = ((grad_b**2 + eps2)*grad_a + my_2*grad_a**2*grad_b) /            &
              (grad_b**2 + my_2*grad_a**2 + grad_a*grad_b + eps2)

  end function vkflxls

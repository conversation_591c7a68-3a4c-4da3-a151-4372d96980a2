bin_PROGRAMS = dual@MPI_EXT@

LIBCORE_DIR=@libcore_path@
LIBTURB_DIR=@libturb_path@
LIBSMEMRD_DIR=@top_builddir@/libsmemrd
LIBDDFB_DIR=@top_builddir@/libddfb
FUNCLIB_DIR=@top_builddir@/FuncLib90
LIBF90_DIR=@top_builddir@/LibF90
LIBINIT_DIR=@top_builddir@/libinit
LIBDEFS_DIR=@top_builddir@/libdefs
PHYSICS_DIR=@top_builddir@/@PHYSICS_TYPE@
PHYSICS_DEPS_DIR=@top_builddir@/PHYSICS_DEPS
ENGINESIM_DIR=@top_builddir@/enginesim/src
LIBDESIGN_DIR=@top_builddir@/libdesign

AM_FCFLAGS = \
	$(FC_MODINC)$(LIBCORE_DIR) \
	$(FC_MODINC)$(LIBDEFS_DIR) \
	$(FC_MODINC)$(LIBTURB_DIR) \
	$(FC_MODINC)$(LIBSMEMRD_DIR) \
	$(FC_MODINC)$(LIBF90_DIR) \
	$(FC_MODINC)$(LIBINIT_DIR) \
	$(FC_MODINC)$(PHYSICS_DIR) \
	$(FC_MODINC)$(PHYSICS_DEPS_DIR) \
	$(FC_MODINC)$(LIBDESIGN_DIR) \
	$(FC_MODINC)@top_srcdir@/FuncLib90 \
	$(FC_MODINC)@top_srcdir@/libturb \
	$(FC_MODINC)@top_builddir@

dual@MPI_EXT@_LDADD = \
	$(LIBDESIGN_DIR)/libdesign.a \
	$(PHYSICS_DEPS_DIR)/libFUN3DPhysicsDeps.a \
	$(PHYSICS_DIR)/libFUN3DPhysics.a \
	$(LIBF90_DIR)/libsink.a \
	$(LIBINIT_DIR)/libinit.a \
	$(LIBDDFB_DIR)/libDDFB.a \
	$(LIBSMEMRD_DIR)/libsmemrd.a \
	$(LIBTURB_DIR)/libturb.a \
	$(LIBDEFS_DIR)/libdefs.a \
	$(ENGINESIM_DIR)/libenginesim.a

if BUILD_SSDC_SUPPORT
dual@MPI_EXT@_LDADD += -L@ssdclibrary@ -lssdc
endif

if BUILD_SFE_SUPPORT
dual@MPI_EXT@_LDADD += -L@sfelibrary@ -lsfe
endif

if BUILD_SPARSKIT_SUPPORT
dual@MPI_EXT@_LDADD += -L@sparskitlibrary@ -lskit
endif

dual@MPI_EXT@_LDADD += $(LIBCORE_DIR)/libcore.a

if BUILD_MPI
if BUILD_PARMETIS_SUPPORT
dual@MPI_EXT@_LDADD += @parmetis_ldadd@
endif
endif

if BUILD_MESHSIM_SUPPORT
dual@MPI_EXT@_LDADD += @meshsim_ldadd@
endif

if BUILD_REFINE_SUPPORT
dual@MPI_EXT@_LDADD += @refine_ldadd@
endif

if BUILD_CAPRI_SUPPORT
dual@MPI_EXT@_LDADD += -L@SDKlibrary@ -lCADGeom-CAPRI -lMeat
dual@MPI_EXT@_LDADD += -L@CAPRIlibrary@ -lcapriDyn -ldcapri
dual@MPI_EXT@_LDADD += -L@Xlibrary@ -lX11 -lm
endif

if BUILD_TECIO_SUPPORT
dual@MPI_EXT@_LDADD += @TECIOLIBS@
endif

if BUILD_KNIFE_SUPPORT
dual@MPI_EXT@_LDADD += @knife_ldadd@
endif

if BUILD_CGNS_SUPPORT
dual@MPI_EXT@_LDADD += -L@CGNSlibrary@ -lcgns
endif

if BUILD_SBOOM_SUPPORT
dual@MPI_EXT@_LDADD += -L@SBOOMlibrary@ -lsboomadjoint
endif

if BUILD_DIRTLIB_SUPPORT
if BUILD_MPI
dual@MPI_EXT@_LDADD += \
	-L@dirtlibrary@ -ldirt_mpich -lp3d
else
dual@MPI_EXT@_LDADD += \
	-L@dirtlibrary@ -ldirt -lp3d
endif
endif

if BUILD_SUGGAR_SUPPORT
if BUILD_MPI
dual@MPI_EXT@_LDADD += \
	-L@suggarlibrary@ -lsuggar_mpi -lp3d -lexpat -lpthread -lstdc++
else
dual@MPI_EXT@_LDADD += \
	-L@suggarlibrary@ -lsuggar -lp3d -lexpat -lpthread -lstdc++
endif
endif

if BUILD_DYMORE_SUPPORT
dual@MPI_EXT@_LDADD += -L@dymorelibrary@ -ldymore4
endif

if BUILD_RCAS_SDX_SUPPORT
dual@MPI_EXT@_LDADD += -L@sdxlibrary@ -lsdx
endif

if BUILD_SIXDOF_SUPPORT
dual@MPI_EXT@_LDADD += \
	-L@SIXDOFLIBS@/Motion/lib -lmo \
	-L@SIXDOFLIBS@/HT/lib -lht \
	-L@SIXDOFLIBS@/EXP/lib -lexp
endif

dual@MPI_EXT@_LDADD += @F90_EXT_LIB@ @zoltan_ldadd@

if BUILD_FORTRAN_C_INTEROP_SUPPORT
dual@MPI_EXT@_LDADD += -lstdc++
endif

dual@MPI_EXT@_SOURCES = \
	getgrad_driver.f90 \
	gmres_matvec.f90 \
	init_adjs.f90 \
	adjoint.f90 \
	radp.f90 \
	relax_adjoint.f90 \
	residual.f90 \
	residual_bc.f90 \
	residual_bc_element_based.f90 \
	residual_bc_visc.f90 \
	residual_bci.f90 \
	residual_cut.f90 \
	residual_drdq.f90 \
	residual_inviscid.f90 \
	residual_laminar.f90 \
	residual_laminari.f90 \
	residual_nonin.f90 \
	residual_turb_consol.f90 \
	residual_turbpart.f90 \
	residual_turbparti.f90

EXTRA_DIST = \
	rubber.data \
	kinematic.data \
	auxiliary.data

# remove *.mod *.fh when mod_suffix is repaired for OS X
CLEANFILES = *.$(FC_MODEXT)  mpif.h *.time *.mod *.fh *.d

dual_f90s=$(dual@MPI_EXT@_SOURCES:.F90=.f90)
dual_deps=$(dual_f90s:.f90=.d)

lib_MODULES = $(dual_f90s:.f90=.$(FC_MODEXT))

DISTCLEANFILES = $(dual_deps)

SUFFIXES = .d

%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-I $(LIBDDFB_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-I $(PHYSICS_DEPS_DIR) \
	-I $(LIBDESIGN_DIR) \
	-L $(top_srcdir)/FuncLib90 \
	-L $(LIBTURB_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-I $(LIBDDFB_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-I $(PHYSICS_DEPS_DIR) \
	-I $(LIBDESIGN_DIR) \
	-L $(top_srcdir)/FuncLib90 \
	-L $(LIBTURB_DIR) > $@

BUILT_SOURCES = $(dual_deps)

-include $(dual_deps)
include $(top_srcdir)/make.rules

CORE_LIBS = libcore.a
DEFS_LIBS = libdefs.a
TURB_LIBS = libturb.a
INIT_LIBS = libinit.a
FUN3D_F90_LIBS = libsink.a


module timeacc_coeffs

  use kinddefs, only : dp

  implicit none

  private

  public :: tcoeffn, tcoeff2, b_vec_mebdf, dt_vec
  public :: set_time_coeffn, set_time_coeff2
  public :: wf_bdf2opt, stage, t_stage, back_planes

  integer :: stage                     ! # of stages in time-advancement loop
  integer :: t_stage                   ! the CURRENT stage:1 <= t_stage <= stage
  integer :: back_planes               ! number of (good/valid) back planes

  real(dp), dimension(4) :: tcoeffn    ! bdf coeffn. for temporal schemes
  real(dp), dimension(4) :: tcoeff2    ! coeff2. for temporal error estimate
  real(dp), dimension(2) :: b_vec_mebdf! MEBDF coefficients for stage 3

  real(dp), dimension(4) :: dt_vec  ! timestep vector for variable timesteppi
                                    ! dt_vec(1) = future timestep (MEBDF dt)
                                    ! dt_vec(2) = current timestep
                                    ! dt_vec(3) = first past timestep
                                    ! dt_vec(4) = second past timestep

  real(dp) :: wf_bdf2opt = 0.48_dp  ! Weighting factor for bdf2opt

contains

!================================== SET_TIME_COEFFN ==========================80
!
! Calculate the time-dependent coefficients for the multi-step schemes
! including  1)  BDF1    2)  BDF2   3)  a: BDF2OPT   b: BDF3
!            4)  MEBDF4  ( 5) ESDIRK4
!
! The routine is applicable for constant or variable coefficients.
!
! Timesteps are as follows:
!
!              --------   n+2      <==  Super-future point for MEBDF4 (Stage 2)
!               dt_np1
!            ------------ n+1      <==  This is the target  level      --
!               dt_n                                                     |
!              --------   n+0      <==  This is the present level        |
!               dt_nm1                                                   |- BDF
!              --------   n-1                                            |
!               dt_nm2                                                   |
!              --------   n-2                                          --
!
! Note that in general   dt_np1 /= dt_n /= dt_nm1 /= dt_nm2
!
!=============================================================================80

  subroutine set_time_coeffn()

    use nml_nonlinear_solves, only : ibdf2opt, itime

    real(dp) :: t2, t3, t4, t5, numerator, denominator
    real(dp) :: dt_np1, dt_n, dt_nm1, dt_nm2
    real(dp) :: d4Bdt4_np2, d4Bdt4_np1, d4Bdt4_n, d4Bdt4_nm1, d4Bdt4_nm2

  continue

! Note that the variable "back_planes" measures the number of planes of GOOD
! data that are currently stored in the temporal registers.  If starting
! from Free stream, or Steady-State then there is only one plane of data.
! If you are changing from low-order to high-order, then progress
! through several steps (order dependent) to fill the back_plane data.

    dt_np1 = dt_vec(1)
    dt_n   = dt_vec(2)
    dt_nm1 = dt_vec(3)
    dt_nm2 = dt_vec(4)

! Account for BDF1, BDF2, BDF3, or MEBDF4 with only one back plane

    if ( (itime >= 1) .and. (itime <= 4) .and. (back_planes == 1) ) then
      tcoeffn(1) = ( 1.0_dp)/(dt_n)
      tcoeffn(2) = (-1.0_dp)/(dt_n)
      tcoeffn(3) = ( 0.0_dp)
      tcoeffn(4) = ( 0.0_dp)
    end if

! Account for BDF2, BDF3, or MEBDF4 with only two back planes

    if ( (itime >= 2) .and. (itime <= 4) .and. (back_planes == 2) ) then
      tcoeffn(1) =  (dt_nm1 + 2*dt_n)/(dt_n  *(dt_nm1+dt_n))
      tcoeffn(2) = -(dt_nm1 +   dt_n)/(dt_n  *(dt_nm1     ))
      tcoeffn(3) =  (dt_n           )/(dt_nm1*(dt_nm1+dt_n))
      tcoeffn(4) =  ( 0.0_dp)
    end if

    bdf2o_bdf3: if ( (itime == 3 .and. back_planes == 3) ) then

      tcoeffn(1) =  (dt_nm1*dt_nm2+2*dt_n  *dt_nm2+  dt_nm1*dt_nm1             &
                                 +4*dt_n  *dt_nm1+3*dt_n  *dt_n  )             &
                    /(dt_n  *(dt_nm1+dt_n  )*(dt_nm2+dt_nm1+dt_n ))
      tcoeffn(2) = -(dt_nm1*dt_nm2+  dt_n  *dt_nm2+  dt_nm1*dt_nm1             &
                                 +2*dt_n  *dt_nm1+  dt_n  *dt_n  )             &
                    /(dt_n  *(dt_nm1   )*(dt_nm2+dt_nm1              ))
      tcoeffn(3) =  (dt_n  *dt_nm2+  dt_n  *dt_nm1+  dt_n  *dt_n  )            &
                    /(dt_nm1*(dt_nm1+dt_n  )*(dt_nm2                 ))
      tcoeffn(4) = -(dt_n  *dt_nm1+  dt_n  *dt_n                  )            &
                    /(dt_nm2*(dt_nm2+dt_nm1)*(dt_nm2+dt_nm1+dt_n     ))

      bdf2o: if ( ibdf2opt == 1 ) then ! blend 2nd and 3rd-order formulae

        tcoeffn(1) =  wf_bdf2opt * tcoeffn(1) + (1.0_dp-wf_bdf2opt) *          &
                  ( (dt_nm1 + 2*dt_n)/(dt_n  *(dt_nm1+dt_n)) )
        tcoeffn(2) =  wf_bdf2opt * tcoeffn(2) + (1.0_dp-wf_bdf2opt) *          &
                  (-(dt_nm1 +   dt_n)/(dt_n  *(dt_nm1     )) )
        tcoeffn(3) =  wf_bdf2opt * tcoeffn(3) + (1.0_dp-wf_bdf2opt) *          &
                  ( (dt_n           )/(dt_nm1*(dt_nm1+dt_n)) )
        tcoeffn(4) =  wf_bdf2opt * tcoeffn(4)

      end if bdf2o

    end if bdf2o_bdf3

    mebdf4: if ( (itime == 4) .and. (back_planes == 3) ) then

      select case (t_stage)
      case (1) ! 1st-stage is BDF3
        tcoeffn(1) =  (dt_nm1*dt_nm2+2*dt_n  *dt_nm2+  dt_nm1*dt_nm1           &
                                   +4*dt_n  *dt_nm1+3*dt_n  *dt_n  )           &
                         /(dt_n  *(dt_nm1+dt_n  )*(dt_nm2+dt_nm1+dt_n ))
        tcoeffn(2) = -(dt_nm1*dt_nm2+  dt_n  *dt_nm2+  dt_nm1*dt_nm1           &
                                   +2*dt_n  *dt_nm1+  dt_n  *dt_n  )           &
                     /(dt_n  *(dt_nm1   )*(dt_nm2+dt_nm1              ))
        tcoeffn(3) =  (dt_n  *dt_nm2+  dt_n  *dt_nm1+  dt_n  *dt_n  )          &
                     /(dt_nm1*(dt_nm1+dt_n  )*(dt_nm2                 ))
        tcoeffn(4) = -(dt_n  *dt_nm1+  dt_n  *dt_n                  )          &
                     /(dt_nm2*(dt_nm2+dt_nm1)*(dt_nm2+dt_nm1+dt_n     ))

      case (2) ! 2nd-stage is BDF3 to super-future

        tcoeffn(1) =  (dt_n  *dt_nm1+2*dt_np1*dt_nm1+  dt_n  *dt_n             &
                                   +4*dt_np1*dt_n  +3*dt_np1*dt_np1)           &
                        /(dt_np1*(dt_n  +dt_np1)*(dt_nm1+dt_n  +dt_np1))
        tcoeffn(2) = -(dt_n  *dt_nm1+  dt_np1*dt_nm1+  dt_n  *dt_n             &
                                   +2*dt_np1*dt_n  +  dt_np1*dt_np1)           &
                     /(dt_np1*(dt_n     )*(dt_nm1+dt_n                ))
        tcoeffn(3) =  (dt_np1*dt_nm1+  dt_np1*dt_n  +  dt_np1*dt_np1)          &
                     /(dt_n  *(dt_n  +dt_np1)*(dt_nm1                 ))
        tcoeffn(4) = -(dt_np1*dt_n  +  dt_np1*dt_np1                )          &
                     /(dt_nm1*(dt_nm1+dt_n  )*(dt_nm1+dt_n  +dt_np1   ))

      case (3) ! 3rd-stage combines everything - BDF3 plus 4th-order correction
        tcoeffn(1) =  (dt_nm1*dt_nm2+2*dt_n  *dt_nm2+  dt_nm1*dt_nm1           &
                                   +4*dt_n  *dt_nm1+3*dt_n  *dt_n  )           &
                         /(dt_n  *(dt_nm1+dt_n  )*(dt_nm2+dt_nm1+dt_n ))
        tcoeffn(2) = -(dt_nm1*dt_nm2+  dt_n  *dt_nm2+  dt_nm1*dt_nm1           &
                                   +2*dt_n  *dt_nm1+  dt_n  *dt_n  )           &
                     /(dt_n  *(dt_nm1   )*(dt_nm2+dt_nm1              ))
        tcoeffn(3) =  (dt_n  *dt_nm2+  dt_n  *dt_nm1+  dt_n  *dt_n  )          &
                     /(dt_nm1*(dt_nm1+dt_n  )*(dt_nm2                 ))
        tcoeffn(4) = -(dt_n  *dt_nm1+  dt_n  *dt_n                  )          &
                     /(dt_nm2*(dt_nm2+dt_nm1)*(dt_nm2+dt_nm1+dt_n     ))

        t2  = dt_nm2
        t3  = dt_nm2 + dt_nm1
        t4  = dt_nm2 + dt_nm1 + dt_n
        t5  = dt_nm2 + dt_nm1 + dt_n   + dt_np1

        denominator  =(t5-t4)*(12*t4**2*t5**2-8*t3*t4*t5**2                    &
           -8*t2*t4*t5**2+4*t2*t3*t5**2-8*t3*t4**2*t5-8*t2*t4**2*t5            &
           +6*t3**2*t4*t5+10*t2*t3*t4*t5+6*t2**2*t4*t5-3*t2*t3**2*t5           &
           -3*t2**2*t3*t5+4*t2*t3*t4**2-3*t2*t3**2*t4-3*t2**2*t3*t4            &
           +2*t2**2*t3**2)

        numerator = dt_n * (dt_n  +dt_nm1) * (dt_n  +dt_nm1+dt_nm2) *          &
        (dt_nm2+dt_nm1+dt_n+dt_np1) *                                          &
        (dt_n  *(3*dt_n   + 4*dt_nm1 + 2*dt_nm2) + dt_nm1*(dt_nm1+dt_nm2)) *   &
        (dt_np1*(3*dt_np1 + 4*dt_n   + 2*dt_nm1) + dt_n  *(dt_n  +dt_nm1)) / 24

        d4Bdt4_np2 =  24/(dt_np1*(dt_n + dt_np1)*(dt_n + dt_nm1 + dt_np1)*     &
                                        (dt_n + dt_nm1 + dt_nm2 + dt_np1))
        d4Bdt4_np1 = -24/(dt_n*(dt_n + dt_nm1)*(dt_n + dt_nm1 + dt_nm2)*dt_np1)
        d4Bdt4_n   =  24/(dt_n*dt_nm1*(dt_nm1 + dt_nm2)*(dt_n + dt_np1))
       d4Bdt4_nm1 = -24/(dt_nm1*(dt_n + dt_nm1)*dt_nm2*(dt_n + dt_nm1 + dt_np1))
        d4Bdt4_nm2 =  24/(dt_nm2*(dt_nm1 + dt_nm2)*(dt_n + dt_nm1 + dt_nm2)*   &
                                          (dt_n + dt_nm1 + dt_nm2 + dt_np1))

        b_vec_mebdf(1) =         + d4Bdt4_np2 * numerator / denominator     !n+2
        b_vec_mebdf(2) =         + d4Bdt4_np1 * numerator / denominator     !old
        tcoeffn(1) =  tcoeffn(1)                                            !new
        tcoeffn(2) =  tcoeffn(2) + d4Bdt4_n   * numerator / denominator     !n
        tcoeffn(3) =  tcoeffn(3) + d4Bdt4_nm1 * numerator / denominator     !n-1
        tcoeffn(4) =  tcoeffn(4) + d4Bdt4_nm2 * numerator / denominator     !n-2

      end select

    end if mebdf4

  end subroutine set_time_coeffn

!=========================== set_time_coeff2 =================================80
!
!   Compute temporal coefficients for error estimators
!
!=============================================================================80

  subroutine set_time_coeff2()

    use nml_nonlinear_solves, only : ibdf2opt, itime

    real(dp) :: dt_n, dt_nm1, dt_nm2

  continue

! Set the bdf temporal scheme coefficients for constructing error controllers
! These coefficients reflect an alternate estimate for time advancement
! constructed from available time steps

    tcoeff2(:) = 0.0_dp

!  Account for BDF1, BDF2, BDF3, or MEBDF4 with only one plane of
!  good back_plane data

!  Note that this step is COMPLETELY BOGUS as these is no data to build a
!  reliable estimate
!                         ----------------
!  What is coded here creates an O(1) error estimate.  The the iteration
!  termination strategy
!  reverts to the hard-coded residual kickout or the max iteration kickout.

    dt_n   = dt_vec(2)
    dt_nm1 = dt_vec(3)
    dt_nm2 = dt_vec(4)

    if ( (itime>=1) .and. (itime<=4) .and. (back_planes==1) ) then ! Begin BDF1
      tcoeff2(1) =  2.0_dp/dt_n
      tcoeff2(2) = -2.0_dp/dt_n
      tcoeff2(3) =  0.0_dp
      tcoeff2(4) =  0.0_dp
    end if                                                         ! End BDF1

!  Account for BDF2, BDF3, or MEBDF4 but only two planes of good back_plane data

    if ( (itime>=2) .and. (itime<=4) .and. (back_planes==2) ) then! Begin BDF2
      tcoeff2(1) =  ( 1.0_dp)/(dt_n)
      tcoeff2(2) =  (-1.0_dp)/(dt_n)
      tcoeff2(3) =  ( 0.0_dp)
      tcoeff2(4) =  ( 0.0_dp)
    end if                                                        ! End BDF2

    if ( (itime==3) .and. (back_planes==3) ) then                 ! Begin BDF3
      if ( ibdf2opt /= 1 ) then                                   !  a: BDF2O

        tcoeff2(1) =  (dt_nm1*dt_nm2+2*dt_n  *dt_nm2+  dt_nm1*dt_nm1         &
                                   +4*dt_n  *dt_nm1+3*dt_n  *dt_n  )         &
                   /(dt_n  *(dt_nm1+dt_n  )*(dt_nm2+dt_nm1+dt_n ))
        tcoeff2(2) = -(dt_nm1*dt_nm2+  dt_n  *dt_nm2+  dt_nm1*dt_nm1         &
                                   +2*dt_n  *dt_nm1+  dt_n  *dt_n  )         &
                   /(dt_n  *(dt_nm1   )*(dt_nm2+dt_nm1              ))
        tcoeff2(3) =  (dt_n  *dt_nm2+  dt_n  *dt_nm1+  dt_n  *dt_n  )        &
                   /(dt_nm1*(dt_nm1+dt_n  )*(dt_nm2                 ))
        tcoeff2(4) = -(dt_n  *dt_nm1+  dt_n  *dt_n                  )        &
                   /(dt_nm2*(dt_nm2+dt_nm1)*(dt_nm2+dt_nm1+dt_n     ))

!       Blend 2nd and 3rd-order formulae

        tcoeff2(1) =  wf_bdf2opt * tcoeffn(1) + (1.0_dp-wf_bdf2opt) *        &
                      ( (dt_nm1 + 2*dt_n)/(dt_n  *(dt_nm1+dt_n)) )
        tcoeff2(2) =  wf_bdf2opt * tcoeffn(2) + (1.0_dp-wf_bdf2opt) *        &
                      (-(dt_nm1 +   dt_n)/(dt_n  *(dt_nm1     )) )
        tcoeff2(3) =  wf_bdf2opt * tcoeffn(3) + (1.0_dp-wf_bdf2opt) *        &
                      ( (dt_n           )/(dt_nm1*(dt_nm1+dt_n)) )
        tcoeff2(4) =  wf_bdf2opt * tcoeffn(4)

      else                                                        !  b: BDF3

        tcoeff2(1) =  (dt_nm1*dt_nm2+2*dt_n  *dt_nm2+  dt_nm1*dt_nm1         &
                                 +4*dt_n  *dt_nm1+3*dt_n  *dt_n  )           &
                   /(dt_n  *(dt_nm1+dt_n  )*(dt_nm2+dt_nm1+dt_n ))
        tcoeff2(2) = -(dt_nm1*dt_nm2+  dt_n  *dt_nm2+  dt_nm1*dt_nm1         &
                                 +2*dt_n  *dt_nm1+  dt_n  *dt_n  )           &
                   /(dt_n  *(dt_nm1   )*(dt_nm2+dt_nm1              ))
        tcoeff2(3) =  (dt_n  *dt_nm2+  dt_n  *dt_nm1+  dt_n  *dt_n  )        &
                   /(dt_nm1*(dt_nm1+dt_n  )*(dt_nm2                 ))
        tcoeff2(4) = -(dt_n  *dt_nm1+  dt_n  *dt_n                  )        &
                   /(dt_nm2*(dt_nm2+dt_nm1)*(dt_nm2+dt_nm1+dt_n     ))

      end if
    end if                                                       !  End BDF3

!   [ No coefficients needed for MEBDF4 or ESDIRK4 ]

  end subroutine set_time_coeff2

end module timeacc_coeffs

!================================ SETUP_T_INVERSE ============================80
!
!  Sets up the Jacobian matrix T-inverse=dQ/dq for converting conservative
!  linearizations to primitive
!
!=============================================================================80
  pure function setup_t_inverse(rho,u,v,w)

    use kinddefs, only : dp
    use fluid,    only : gm1

    real(dp), intent(in) :: rho, u, v, w

    real(dp), dimension(5,5) :: setup_t_inverse

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp
    real(dp), parameter :: my_2 = 2.0_dp

  continue

    setup_t_inverse(1,1) = my_1
    setup_t_inverse(1,2) = my_0
    setup_t_inverse(1,3) = my_0
    setup_t_inverse(1,4) = my_0
    setup_t_inverse(1,5) = my_0

    setup_t_inverse(2,1) = u
    setup_t_inverse(2,2) = rho
    setup_t_inverse(2,3) = my_0
    setup_t_inverse(2,4) = my_0
    setup_t_inverse(2,5) = my_0

    setup_t_inverse(3,1) = v
    setup_t_inverse(3,2) = my_0
    setup_t_inverse(3,3) = rho
    setup_t_inverse(3,4) = my_0
    setup_t_inverse(3,5) = my_0

    setup_t_inverse(4,1) = w
    setup_t_inverse(4,2) = my_0
    setup_t_inverse(4,3) = my_0
    setup_t_inverse(4,4) = rho
    setup_t_inverse(4,5) = my_0

    setup_t_inverse(5,1) = (u*u + v*v + w*w) / my_2
    setup_t_inverse(5,2) = rho*u
    setup_t_inverse(5,3) = rho*v
    setup_t_inverse(5,4) = rho*w
    setup_t_inverse(5,5) = my_1 / gm1

  end function setup_t_inverse

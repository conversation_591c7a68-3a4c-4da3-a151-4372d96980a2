!============================= CROSS_PRODUCT_X ===============================80
!
! Returns a vector cross product, given components for two vectors
!
!=============================================================================80

  pure function cross_product_x( v1x, v1y, v1z, v2x, v2y, v2z )

    use kinddefs, only : dp

    real(dp),              intent(in) :: v1x, v1y, v1z
    real(dp),              intent(in) :: v2x, v2y, v2z
    real(dp), dimension(3)            :: cross_product_x

    continue

    cross_product_x(1) = v1y*v2z - v1z*v2y
    cross_product_x(2) = v1z*v2x - v1x*v2z
    cross_product_x(3) = v1x*v2y - v1y*v2x

  end function cross_product_x

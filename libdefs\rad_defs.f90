module rad_defs

  use kinddefs, only : dp

  implicit none

  private

  public :: rads_type
  public :: deallocate_rads
  public :: rads_restrict_adapt
  public :: rads_grid_adapt_param

  type rads_type
    integer                           :: noutput
    real(dp), dimension(:,:), pointer :: adapt_param
    real(dp), dimension(:,:), pointer :: prim_linear, dual_linear, turb_linear
  end type rads_type

contains

!============================= DEALLOCATE_RADS ===============================80
!
! Deallocates the rads derived type
!
!=============================================================================80

  subroutine deallocate_rads(rads)

    type(rads_type), intent(inout) :: rads

  continue

    if (associated(rads%adapt_param))  deallocate(rads%adapt_param)
    if (associated(rads%prim_linear))  deallocate(rads%prim_linear)
    if (associated(rads%dual_linear))  deallocate(rads%dual_linear)
    if (associated(rads%turb_linear))  deallocate(rads%turb_linear)

  end subroutine deallocate_rads

!================================= RADS_GRID_ADAPT_PARAM =====================80
!
! calculated grid%adapt from rads%adapt_param(2,:) and adapt_output_tolerance
!
!=============================================================================80

  subroutine rads_grid_adapt_param(grid,adapt_param, scalar_metric, golden_file)

    use kinddefs,                only : dp
    use grid_types,              only : grid_type
    use element_defs,            only : type_tet, type_prz
    use lmpi,                    only : lmpi_master, lmpi_min, lmpi_max,       &
                                        lmpi_reduce, lmpi_bcast
    use refine_adaptation_input, only : adapt_output_tolerance, adapt_exponent,&
                                        adapt_freezebl, adapt_library,         &
                                        adapt_fixed_fraction, adapt_complexity,&
                                        adapt_twod, adapt_max_edge_growth,     &
                                        adapt_statistics
    use system_extensions,       only : se_flush


    type(grid_type),                     intent(inout) :: grid
    real(dp), dimension(1,grid%nnodes0), intent(in)    :: adapt_param
    real(dp), dimension(grid%nnodes01),  intent(out)   :: scalar_metric
    integer,                  optional,  intent(in)    :: golden_file

    integer :: node

    real(dp) :: localadapttotal, globaladapttotal
    real(dp) :: adapt_scale
    real(dp) :: adapt_max, adapt_min, adapt_avg
    integer  :: adapt_nnodes
    real(dp) :: localreal
    integer  :: localinteger

    integer  :: unfrozen_nodes, target_selection
    real(dp) :: median, average, average_in_zone, global_avg_in_zone

    real(dp) :: upper_limit
    real(dp) :: lower_limit

    integer,   dimension(grid%nnodes0) :: active_node
    integer  :: n_in_zone, total_in_zone
    integer  :: ielem, cell
    real(dp),  dimension(grid%nnodes0) :: param_in_zone

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

    character(len=3) :: active_element

    continue

    upper_limit = adapt_max_edge_growth
    lower_limit = my_1/adapt_max_edge_growth

    localadapttotal = my_0
    unfrozen_nodes = 0
    do node = 1, grid%nnodes0
      check_for_freeze :if (adapt_freezebl>0.0_dp) then
        outside_bl : if (real(grid%slen(node),dp) > real(adapt_freezebl,dp))then
          localadapttotal = localadapttotal + adapt_param(1,node)
          unfrozen_nodes = unfrozen_nodes + 1
        end if outside_bl
      else
        localadapttotal = localadapttotal + adapt_param(1,node)
        unfrozen_nodes = unfrozen_nodes + 1
      end if check_for_freeze
    end do
    call lmpi_reduce(localadapttotal,globaladapttotal)
    call lmpi_bcast(globaladapttotal)
    localinteger = unfrozen_nodes
    call lmpi_reduce(localinteger,unfrozen_nodes)
    call lmpi_bcast(unfrozen_nodes)

    average = globaladapttotal / real(unfrozen_nodes,dp)

    call parallel_selection(grid%nnodes0,adapt_param(1,:),&
      grid%nnodesg/2,median)

    if (lmpi_master) write(*,'("all median ",e13.5," median*nnodes ",e13.5)') &
      median, median * real(grid%nnodesg,dp)
    if (lmpi_master) write(*,'("all average ",e13.5," average*nnodes ",e13.5)')&
      average, average * real(grid%nnodesg,dp)

    separate_zones_for_two : if ( adapt_library == 'refine/two' ) then
      if (lmpi_master) write(*,*) "in TWO mode"

      if ( adapt_twod ) then
        active_element = type_prz
      else
        active_element = type_tet
      end if

      active_node = 1
      do ielem = 1, grid%nelem
        if ( active_element /= grid%elem(ielem)%type_cell ) then
          do cell = 1, grid%elem(ielem)%ncell
            do node = 1, grid%elem(ielem)%node_per_cell
              if ( grid%elem(ielem)%c2n(node,cell) <= grid%nnodes0 ) then
                active_node( grid%elem(ielem)%c2n(node,cell) ) = 0
              end if
            end do
          end do
        end if
      end do

! active zone

      n_in_zone = 0
      average_in_zone = 0.0_dp
      do node = 1, grid%nnodes0
        if ( 1 == active_node(node) ) then
          n_in_zone = n_in_zone + 1
          param_in_zone(n_in_zone) = adapt_param(1,node)
          average_in_zone = average_in_zone + adapt_param(1,node)
        end if
      end do

      call lmpi_reduce(n_in_zone, total_in_zone)
      call lmpi_bcast(total_in_zone)
      if (lmpi_master) &
        write(*,*) "nodes in",active_element,"zone", total_in_zone
      call parallel_selection(n_in_zone,param_in_zone,&
        total_in_zone/2,median)

      call lmpi_reduce(average_in_zone, global_avg_in_zone)
      call lmpi_bcast(global_avg_in_zone)
      average_in_zone = global_avg_in_zone / real(total_in_zone,dp)

      if (lmpi_master)                                                  &
        write(*,'(a3," median ",e13.5," median*total_in_zone ",e13.5)') &
        active_element, median, median * real(total_in_zone,dp)

      if (lmpi_master)                                                    &
        write(*,'(a3," average ",e13.5," average*total_in_zone ",e13.5)') &
        active_element, average, average * real(total_in_zone,dp)

      if ( adapt_output_tolerance < my_0 ) then
        select case (adapt_statistics)
        case ('median')
          adapt_output_tolerance = &
            abs(adapt_output_tolerance) * median * real(total_in_zone,dp)
        case ('average')
          adapt_output_tolerance =        &
            abs(adapt_output_tolerance) * &
            average_in_zone * real(total_in_zone,dp)
        end select
      end if
      adapt_scale = real(total_in_zone,dp) / adapt_output_tolerance
      scale_tet_zone : do node = 1, grid%nnodes0
        if ( 0 == active_node(node) ) cycle scale_tet_zone
        scalar_metric(node) = (adapt_scale * adapt_param(1,node))
        scalar_metric(node) = scalar_metric(node) ** adapt_exponent
        if (scalar_metric(node) < lower_limit) scalar_metric(node) = lower_limit
        if (scalar_metric(node) > upper_limit) scalar_metric(node) = upper_limit
        scalar_metric(node) = my_1/scalar_metric(node)
      end do scale_tet_zone

! prism zone

      n_in_zone = 0
      average_in_zone = 0.0_dp
      do node = 1, grid%nnodes0
        if ( 0 == active_node(node) ) then
          n_in_zone = n_in_zone + 1
          param_in_zone(n_in_zone) = adapt_param(1,node)
          average_in_zone = average_in_zone + adapt_param(1,node)
        end if
      end do

      call lmpi_reduce(n_in_zone, total_in_zone)
      call lmpi_bcast(total_in_zone)
      zone_contains_nodes : if ( total_in_zone > 0 ) then
        if (lmpi_master) &
          write(*,*) "nodes in non",active_element,"zone", total_in_zone
        call parallel_selection(n_in_zone,param_in_zone,&
          total_in_zone/2,median)

        call lmpi_reduce(average_in_zone, global_avg_in_zone)
        call lmpi_bcast(global_avg_in_zone)

        if (lmpi_master)                                                       &
        write(*,'("non ",a3," median ",e13.5," median*total_in_zone ",e13.5)') &
        active_element, median, median * real(total_in_zone,dp)

        average_in_zone = global_avg_in_zone / real(total_in_zone,dp)
        if (lmpi_master)                                                       &
      write(*,'("non ",a3," average ",e13.5," average*total_in_zone ",e13.5)') &
      active_element, average, average * real(total_in_zone,dp)
      end if zone_contains_nodes

      if ( adapt_fixed_fraction > 0.0_dp ) then
        target_selection = int( (1.0_dp-adapt_fixed_fraction) * &
                                real(total_in_zone,dp) )
        call parallel_selection(n_in_zone,param_in_zone,&
          target_selection,median)
        scale_pri_zone : do node = 1, grid%nnodes0
          if ( 1 == active_node(node) ) cycle scale_pri_zone
          if ( adapt_param(1,node) > median ) then
            scalar_metric(node) = lower_limit
          else
            scalar_metric(node) = 1.0_dp
          end if
        end do scale_pri_zone
      else
        if (lmpi_master) write(*,*) 'freeze non',active_element
        freeze_prism : do node = 1, grid%nnodes0
          if ( 0 == active_node(node) ) scalar_metric(node) = 1.0_dp
        end do freeze_prism
      end if

    else

      if ( adapt_complexity > 0.0_dp ) then
        select case (adapt_statistics)
        case ('median')
          globaladapttotal =  median * real(grid%nnodesg,dp)
          if (lmpi_master) &
            write(*,*) 'for complexity with median, reset globaladapttotal'
        end select
      end if

      if ( adapt_output_tolerance < my_0 ) then
        adapt_output_tolerance = abs(adapt_output_tolerance) * globaladapttotal
      end if

      adapt_scale = ( globaladapttotal / adapt_output_tolerance )              &
                  * ( real(grid%nnodesg,dp) / adapt_output_tolerance )
      do node = 1, grid%nnodes0
        scalar_metric(node) = (adapt_scale * adapt_param(1,node))
        scalar_metric(node) = scalar_metric(node) ** adapt_exponent
        if (scalar_metric(node) < lower_limit) scalar_metric(node) = lower_limit
        if (scalar_metric(node) > upper_limit) scalar_metric(node) = upper_limit
        scalar_metric(node) = my_1/scalar_metric(node)
      end do
    end if separate_zones_for_two

    adapt_max = -huge(1.0_dp)
    adapt_min = huge(1.0_dp)
    adapt_avg = my_0
    adapt_nnodes = 0

    do node = 1, grid%nnodes0
      adapt_avg = adapt_avg + scalar_metric(node)
      adapt_min = min(adapt_min, scalar_metric(node))
      adapt_max = max(adapt_max, scalar_metric(node))
      if (scalar_metric(node) <= my_1) adapt_nnodes = adapt_nnodes + 1
    end do
    localreal = adapt_min
    call lmpi_min(localreal, adapt_min)
    localreal = adapt_max
    call lmpi_max(localreal, adapt_max)
    localreal = adapt_avg
    call lmpi_reduce(localreal, adapt_avg)
    adapt_avg = adapt_avg / real(grid%nnodesg,dp)
    localinteger = adapt_nnodes
    call lmpi_reduce(localinteger, adapt_nnodes)
    if (lmpi_master) then
      write(*,'(a,e25.14)') 'Global adapt total           ',globaladapttotal
      write(*,'(a,e25.14)') 'Error tolerance is           ',                   &
                             adapt_output_tolerance
      write(*,'(a,e25.14)') 'Adaptation scaling factor is ',adapt_scale
      write(*,'(a,f25.14)') 'Adaptation parameter Max is  ',adapt_max
      write(*,'(a,f25.14)') 'Adaptation parameter Min is  ',adapt_min
      write(*,'(a,f25.14)') 'Adaptation parameter Avg is  ',adapt_avg
      write(*,'(2(a,i10),a)') 'Refinement requested on ',adapt_nnodes,         &
                              ' of ',grid%nnodesg, ' nodes'
      call se_flush
      if (present(golden_file)) then
        write(golden_file,'(a,e25.14)')                                        &
          '# Error tolerance is           ',adapt_output_tolerance
        write(golden_file,'(a,e25.14)')                                        &
          '# Adaptation scaling factor is ',adapt_scale
        write(golden_file,'(a,f25.14)')                                        &
          '# Adaptation parameter Max is  ',adapt_max
        write(golden_file,'(a,f25.14)')                                        &
          '# Adaptation parameter Min is  ',adapt_min
        write(golden_file,'(a,f25.14)')                                        &
          '# Adaptation parameter Avg is  ',adapt_avg
        write(golden_file,'(2(a,i10),a)')                                      &
          '# Refinement requested on ',adapt_nnodes,' of ',grid%nnodesg,' nodes'
        call se_flush(golden_file)
      end if
    end if

  end subroutine rads_grid_adapt_param

!================================= RADS_RESTRICT_ADAPT =======================80
!
! Restricts a fine grid. warning, needs a lmpi_sumnode afterwards
!
!=============================================================================80

  subroutine rads_restrict_adapt(embedrads,orig,origrads)

    use kinddefs,    only : dp
    use grid_types,  only : grid_type
    use allocations, only : my_alloc_ptr
    use lmpi,        only : lmpi_conditional_stop

    use grid_helper, only : faces_type, faces_from_grid, deallocate_faces, &
      grid_cell_unique

    type(rads_type), intent(in)    :: embedrads
    type(grid_type), intent(inout) :: orig
    type(rads_type), intent(inout) :: origrads

    integer :: leadingdim, idmn
    integer :: node, edge
    integer :: embednode, node1, node2, node3, node4
    integer :: ihex, ielem, cell

    integer :: face
    type(faces_type) :: faces

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_qrtr = 0.25_dp
    real(dp), parameter :: my_eighth = 0.125_dp

    continue

    leadingdim = size( embedrads%adapt_param, 1 )
    if (leadingdim /= embedrads%noutput )                                      &
         write(*,*)"ERROR rad_write_adapt ",leadingdim,embedrads%noutput

    origrads%noutput = embedrads%noutput
    call my_alloc_ptr(origrads%adapt_param,origrads%noutput,orig%nnodes01)

    do node = 1, orig%nnodes0
      do idmn = 1, leadingdim
        origrads%adapt_param(idmn,node) = embedrads%adapt_param(idmn,node)
      end do
    end do

    embednode = orig%nnodes0
    do edge = 1, orig%nedgeloc
      node1 = orig%eptr(1,edge)
      node2 = orig%eptr(2,edge)
      edge_orientation : if ( orig%l2g(node1)                                  &
                            < orig%l2g(node2) ) then
        node1_local :if (node1<=orig%nnodes0) then
          embednode = embednode + 1
          do idmn = 1, leadingdim
            origrads%adapt_param(idmn,node1) = origrads%adapt_param(idmn,node1)&
              + my_half * embedrads%adapt_param(idmn,embednode)
            origrads%adapt_param(idmn,node2) = origrads%adapt_param(idmn,node2)&
              + my_half * embedrads%adapt_param(idmn,embednode)
          end do
        end if node1_local
      else
        node2_local :if (node2<=orig%nnodes0) then
          embednode = embednode + 1
          do idmn = 1, leadingdim
            origrads%adapt_param(idmn,node1) = origrads%adapt_param(idmn,node1)&
              + my_half * embedrads%adapt_param(idmn,embednode)
            origrads%adapt_param(idmn,node2) = origrads%adapt_param(idmn,node2)&
              + my_half * embedrads%adapt_param(idmn,embednode)
          end do
        end if node2_local
      end if edge_orientation
    end do

    call faces_from_grid( orig, faces )

    do face = 1, faces%nface
      if ( grid_cell_unique(orig,faces%f2n(:,face)) ) then
        embednode = embednode + 1
        node1 = faces%f2n(1,face)
        node2 = faces%f2n(2,face)
        node3 = faces%f2n(3,face)
        node4 = faces%f2n(4,face)
        do idmn = 1, leadingdim
          origrads%adapt_param(idmn,node1) = origrads%adapt_param(idmn,node1)&
            + my_qrtr * embedrads%adapt_param(idmn,embednode)
          origrads%adapt_param(idmn,node2) = origrads%adapt_param(idmn,node2)&
            + my_qrtr * embedrads%adapt_param(idmn,embednode)
          origrads%adapt_param(idmn,node3) = origrads%adapt_param(idmn,node3)&
            + my_qrtr * embedrads%adapt_param(idmn,embednode)
          origrads%adapt_param(idmn,node4) = origrads%adapt_param(idmn,node4)&
            + my_qrtr * embedrads%adapt_param(idmn,embednode)
        end do
      end if
    end do

    call deallocate_faces(faces)

    ihex = 0
    do ielem = 1, orig%nelem
      if ('hex' == orig%elem(ielem)%type_cell) ihex = ielem
    end do

    if ( 0 /= ihex ) then
      do cell = 1, orig%elem(ihex)%ncell
        if ( grid_cell_unique(orig,orig%elem(ihex)%c2n(:,cell)) ) then
          embednode = embednode + 1
          do idmn = 1, leadingdim
            do node = 1, orig%elem(ihex)%node_per_cell
              node1 = orig%elem(ihex)%c2n(node,cell)
              origrads%adapt_param(idmn,node1) = &
                origrads%adapt_param(idmn,node1) &
                + my_eighth * embedrads%adapt_param(idmn,embednode)
            end do
          end do
        end if
      end do
    end if

    if (embednode /= size( embedrads%adapt_param, 2 ) ) then
      write(*,*) "rads_restrict_adapt: embed error",                           &
        embednode, size( embedrads%adapt_param, 2 )
      call lmpi_conditional_stop(1,'rad_defs:rads_restrict_adapt')
    end if

    call lmpi_conditional_stop(0,'rad_defs:rads_restrict_adapt')

  end subroutine rads_restrict_adapt

  subroutine parallel_selection(n,unsorted_values,position,value_at_position)
    use kinddefs, only : dp
    use sort,    only : heap_sort
    use lmpi, only : lmpi_master, lmpi_min, lmpi_max, lmpi_reduce, lmpi_bcast
    integer,                intent(in)    :: n
    real(dp), dimension(n), intent(in)    :: unsorted_values
    integer,                intent(in)    :: position
    real(dp),               intent(out)   :: value_at_position

    integer,  dimension(n) :: values_order
    real(dp), dimension(n) :: values

    integer :: i, iteration
    integer :: position_low, position_high
    real(dp) :: pivot_high, pivot_low
    integer :: n_smaller_than_value, current_positon
    integer :: tolerence, within_tolerence

    real(dp), parameter :: big = huge(1.0_dp)

    continue

    call heap_sort(n,unsorted_values,values_order)
    do i = 1, n
      values(i) = unsorted_values(values_order(i))
    end do

    call lmpi_reduce(n, tolerence )
    if (lmpi_master) then
      tolerence = max(2,tolerence/100)
    end if

    if ( n > 0 ) then
      call lmpi_max(values(n),pivot_high)
      call lmpi_reduce(n, position_high)
      call lmpi_min(values(1),pivot_low)
    else
      call lmpi_max(-big,pivot_high)
      call lmpi_reduce(n, position_high)
      call lmpi_min(big,pivot_low)
    end if

    position_low = 1

    if (lmpi_master) then
      write(*,'("high ",e12.5," at ",i0)')pivot_high,position_high
      write(*,'("low  ",e12.5," at ",i0)')pivot_low,position_low
    end if

    if (lmpi_master) then
      value_at_position = 0.5_dp*(pivot_low+pivot_high)
    end if
    call lmpi_bcast( value_at_position )

    iterate : do iteration = 1, 100
      n_smaller_than_value = 0
      count_smaller : do i = 1, n
        if ( values(i) < value_at_position ) then
          n_smaller_than_value = n_smaller_than_value + 1
        else
          exit count_smaller
        end if
      end do count_smaller

      call lmpi_reduce(n_smaller_than_value,current_positon)

      if (lmpi_master) then
        if ( current_positon > position ) then
          position_high = current_positon
          pivot_high = value_at_position
        else
          position_low = current_positon
          pivot_low = value_at_position
        end if
        value_at_position = 0.5_dp*(pivot_low+pivot_high)
      end if
      call lmpi_bcast( value_at_position )

      if (lmpi_master) then
        within_tolerence = 0
        if ( position_high-position_low < tolerence ) within_tolerence = 1
      end if
      call lmpi_bcast( within_tolerence )

      if ( within_tolerence == 1 ) exit iterate

    end do iterate

    if (lmpi_master) then
      write(*,*)'iteration', iteration
      write(*,'("high ",e12.5," at ",i0)')pivot_high,position_high
      write(*,'("current ",e12.5," at ",i0)')value_at_position,current_positon
      write(*,'("low  ",e12.5," at ",i0)')pivot_low,position_low
    end if

  end subroutine parallel_selection


end module rad_defs

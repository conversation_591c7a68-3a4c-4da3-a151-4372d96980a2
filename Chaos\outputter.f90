module outputter

  implicit none

  private

  public :: plot_solution, write_solution

contains

!================================= WRITE_SOLUTION ============================80
!
!   Writes the solution to disk
!
!=============================================================================80
  subroutine write_solution(description,solution,timestep)

    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use kinddefs,          only : dp
    use string_utils,      only : int_to_s

    integer, intent(in) :: timestep

    real(dp), dimension(:), intent(in) :: solution

    character(len=*), intent(in) :: description

    integer :: iu

    character(len=100) :: filename

  continue

    iu = available_unit()
    filename = trim(description) // '_solution.' // trim(int_to_s(timestep))
    call se_open(iu,file=trim(filename),form='unformatted',status='unknown',   &
                 access='stream')

    write(iu) solution

    close(iu)

  end subroutine write_solution


!================================= PLOT_SOLUTION =============================80
!
!   Plot the solution vector
!
!=============================================================================80
  subroutine plot_solution(description,solution,timestep)

    use chaos_datas,       only : gridfile
    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use kinddefs,          only : dp
    use string_utils,      only : int_to_s

    integer, intent(in) :: timestep

    real(dp), dimension(:), intent(in) :: solution

    character(len=*), intent(in) :: description

    integer :: iu, nnodesg, ntface, nqface, ntet, npyr, nprz, nhex, i, j
    integer :: totalcells, i1, i2

    integer, dimension(:),   allocatable :: ifacetag
    integer, dimension(:,:), allocatable :: if2nt, if2nq, ic2nt, ic2np, ic2nz
    integer, dimension(:,:), allocatable :: ic2nh

    real(dp), dimension(:), allocatable :: x, y, z

    character(len=100) :: filename

  continue

! Load the ascii ugrid data

    iu = available_unit()
    call se_open(iu,file=trim(gridfile),form='unformatted',status='old',       &
                 access='stream')

    read(iu) nnodesg,ntface,nqface,ntet,npyr,nprz,nhex

    allocate(x(nnodesg))
    allocate(y(nnodesg))
    allocate(z(nnodesg))
    allocate(if2nt(3,ntface))
    allocate(if2nq(4,nqface))
    allocate(ifacetag(ntface+nqface))
    allocate(ic2nt(4,ntet))
    allocate(ic2np(5,npyr))
    allocate(ic2nz(6,nprz))
    allocate(ic2nh(8,nhex))

    read(iu) (x(i),y(i),z(i),i=1,nnodesg),                                     &
             ((if2nt(j,i),j=1,3),i=1,ntface),                                  &
             ((if2nq(j,i),j=1,4),i=1,nqface),                                  &
             (ifacetag(i),i=1,ntface+nqface),                                  &
             ((ic2nt(j,i),j=1,4),i=1,ntet),                                    &
             ((ic2np(j,i),j=1,5),i=1,npyr),                                    &
             ((ic2nz(j,i),j=1,6),i=1,nprz),                                    &
             ((ic2nh(j,i),j=1,8),i=1,nhex)

    close(iu)

! Write volume Tecplot file

    iu = available_unit()
    filename = trim(description) // '_vis.' // trim(int_to_s(timestep)) //'.dat'
    call se_open(iu,file=trim(filename),form='formatted',status='unknown')

    write(iu,'(a)') 'title="tecplot geometry and solution file"'
    write(iu,'(a)') 'variables=x y z v1 v2 v3 v4 v5'

    totalcells = ntet + npyr + nprz + nhex
    write(iu,'(a,i0,a,i0,a)') 'zone t="Vol" N=',nnodesg,', E=', totalcells,    &
                              ', F=FEPOINT, ET=BRICK'
    do i = 1, nnodesg
      i1 = (i-1)*5+1
      i2 = (i-1)*5+5
      write(iu,'(10e25.15e3)') x(i),y(i),z(i),solution(i1:i2)
    end do

    do i = 1, ntet
      write(iu,'(8(1x,i0))') ic2nt(1,i), ic2nt(2,i), ic2nt(3,i), ic2nt(3,i),   &
                             ic2nt(4,i), ic2nt(4,i), ic2nt(4,i), ic2nt(4,i)
    end do

    do i = 1, nhex
      write(iu,'(8(1x,i0))') ic2nh(1,i), ic2nh(2,i), ic2nh(3,i), ic2nh(4,i),   &
                             ic2nh(5,i), ic2nh(6,i), ic2nh(7,i), ic2nh(8,i)
    end do

    do i = 1, nprz
      write(iu,'(8(1x,i0))') ic2nz(4,i), ic2nz(5,i), ic2nz(2,i), ic2nz(1,i),   &
                             ic2nz(6,i), ic2nz(6,i), ic2nz(3,i), ic2nz(3,i)
    end do

    do i = 1, npyr
      write(iu,'(8(1x,i0))') ic2np(1,i), ic2np(4,i), ic2np(5,i), ic2np(2,i),   &
                             ic2np(3,i), ic2np(3,i), ic2np(3,i), ic2np(3,i)
    end do

    close(iu)

! Free memory

    deallocate(x,y,z,if2nt,if2nq,ifacetag,ic2nt,ic2np,ic2nz,ic2nh)

  end subroutine plot_solution

end module outputter

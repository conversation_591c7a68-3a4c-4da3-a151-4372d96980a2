module cell_agglom_defs

  implicit none

  private

!....................(-1)

  public :: cell
  public :: t

!....................(0)

  public :: adj_count
  public :: agg_mynparts
  public :: agglom_f2c_org
  public :: agglom_f2c_par

!....................(1)

  public :: agg_face_f2c
  public :: cl2g_agglom
  public :: agglom_owner
  public :: par_to_seq_cell_map
  public :: seq_to_par_cell_map
  public :: agg_istart, agg_iend, rev_cell_map

!....................(2)

  public :: agglom_ncell0
  public :: agglom_ncell
  public :: agglom_ncellg

!....................(3)

  public :: eqn_group_line1
  public :: corners,  ridges,  valleys
  public :: corners0, ridges0, valleys0
  public :: cornersg, ridgesg, valleysg

!....................(4)

!....................(5)

!....................(6)

  public :: agg_boundary_flag
  public :: agg_boundary_check

!....................(7)

  public :: orphan_agglomeration
  public :: parents_minimum
  public :: parent_seed_debug
  public :: cell_prolong_debug
  public :: planar_boundaries
  public :: corner_debug
  public :: augment_ridge_boundary_flag
  public :: read_agglom_f2c
  public :: write_agglom_f2c
  public :: prolongation_type, injections
  public :: agglomerate_inviscid_order

  type :: cell
     integer,  dimension(:), pointer :: adj_list
  end type cell
  type (cell), dimension(:), pointer :: t

!....................(0)

  integer, dimension(:), allocatable :: adj_count

  integer, dimension(:), allocatable :: agg_mynparts

! Global array:
! Mapping global point (parent) to global child (agglomerate).
! Accessed locally using grid%cl2g (cell-centered local to global).
  integer, dimension(:), allocatable :: agglom_f2c_org
  integer, dimension(:), allocatable :: agglom_f2c_par

!....................(1)


  integer, dimension(:), allocatable :: agg_face_f2c
  integer, dimension(:), allocatable :: cl2g_agglom
  integer, dimension(:), allocatable :: agglom_owner
  integer, dimension(:), allocatable :: par_to_seq_cell_map
  integer, dimension(:), allocatable :: seq_to_par_cell_map
  integer, dimension(:), allocatable :: agg_istart, agg_iend, rev_cell_map

!....................(2)

  integer :: agglom_ncell0 ! Number of agglomerations owned by processor
  integer :: agglom_ncell  ! Number of agglomerated cells on processor
  integer :: agglom_ncellg ! Sum of agglom_ncell0

!....................(3)

! Parent grid information.

  integer :: eqn_group_line1               ! >0 if solving and agglomerating
                                           ! using first set of lines
  integer :: corners,  ridges,  valleys    !available on-processor values
  integer :: corners0, ridges0, valleys0   !   solved on-processor values
  integer :: cornersg, ridgesg, valleysg   !   global on-processor values

!....................(4)

!....................(5)

!....................(6)

! Agglomerated grid information.

  integer, dimension(:), allocatable :: agg_boundary_flag
  integer, dimension(:), allocatable :: agg_boundary_check

!....................(7)
! Parameters for cell_coarsening:

  integer :: orphan_agglomeration = 0 !0,1,2,3
  integer :: parents_minimum

  integer :: parent_seed_debug  = -1
  integer :: cell_prolong_debug = -1

  logical :: planar_boundaries  = .false.
  logical :: corner_debug       = .false.

  logical :: augment_ridge_boundary_flag = .false.

  logical :: read_agglom_f2c  = .false. ! Read file 'project'.agglom_f2c
  logical :: write_agglom_f2c = .false. ! Write 'project'.agglom_f2c_output

  integer :: prolongation_type = 1 !=0, injection from coarser cell
                                   !=1, linear prolongation.
                                   !=2, first linear prolongation
                                   ! (inherited from nc approach)

  integer :: injections = 0 !injections used in prolongation

  integer :: agglomerate_inviscid_order = 1 !=0, first order
                                            !=1, second order

end module cell_agglom_defs

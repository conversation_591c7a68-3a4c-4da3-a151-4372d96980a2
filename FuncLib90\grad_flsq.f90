!================================= GRAD_FLSQ ================================80
!
! Turbulence gradients using face-based-lsq.
!
!=============================================================================80

  pure function grad_flsq( lsq_mref,                                           &
                           n_s, n_e, n_tot, cell1, cell2, ncell01,             &
                           tx, ty, tz,                                         &
                           xc, yc, zc, qcell, flsq_lu, flsq_ja,                &
                           slen,                                               &
                           face, flsq_n, flsq_ni, flsq_nb, bcc )

    use lsq_constants,  only : wflsq1, flsqn_max, tf
    use bc_types,       only : bcc_type
    use lsq_types,      only : lsq_ref_type

    type(lsq_ref_type), intent(in) :: lsq_mref
    type(bcc_type), intent(in) :: bcc

    integer, intent(in) :: n_s, n_e, n_tot, cell1, cell2, ncell01
    integer, intent(in) :: face, flsq_n, flsq_ni, flsq_nb

    real(dp), intent(in) :: tx, ty, tz
    real(dp), dimension(:), intent(in) :: xc, yc, zc, slen
    real(dp), dimension(n_tot,ncell01), intent(in) :: qcell

    real(dp), dimension(4,4),     intent(in) :: flsq_lu
    integer,  dimension(flsq_ni), intent(in) :: flsq_ja

    integer :: ii, jj, cella, eq, n, fb

    real(dp) :: xiel, etal, ziel
    real(dp), dimension(3) :: fgrad
    real(dp) :: deti, ex, ey, ez
    real(dp) :: wsq, scalei, egrad, lgrad, mgrad

    real(dp), dimension(n_e-n_s+1) :: c0, c1, c2, c3, qta

    real(dp), dimension(4) :: f, lc_max
    real(dp), dimension(3,3) :: tef, trf

    real(dp), dimension(4,flsqn_max) :: lc
    real(dp), dimension(flsqn_max) :: face_wsq
    integer,  dimension(flsqn_max) :: list

    real(dp), dimension(3,n_e-n_s+1) :: grad_flsq

  continue

    n = n_e - n_s + 1

    !...Set coordinate transformation.
    trf = mapping_system( tx, ty, tz)

    scalei = flsq_lu(1,1)

    !...ex, ey, ez is unit vector along edge direction
    ex  = 0.5_dp*( xc(cell2) - xc(cell1) )*scalei
    ey  = 0.5_dp*( yc(cell2) - yc(cell1) )*scalei
    ez  = 0.5_dp*( zc(cell2) - zc(cell1) )*scalei

    deti =  1._dp / ( ex*tx + ey*ty + ez*tz )

    tef = tinv_3d( ex, ey, ez, trf(2,1), trf(2,2), trf(2,3),      &
                               trf(3,1), trf(3,2), trf(3,3), deti )

    face_wsq(1:flsq_n+2) = 1._dp
    if( abs(wflsq1-1._dp) > 1.0e-06_dp .and. (flsq_nb == 0) ) then
      face_wsq(1:flsq_n+2) = flsq_wsq( flsq_n, cell1, cell2, ncell01, &
                                       xc, yc, zc, flsq_ja )
    endif

    c0  = 0._dp
    c1  = 0._dp
    c2  = 0._dp
    c3  = 0._dp
    jj  = 0
    do ii=1,flsq_ni
      cella = flsq_ja(ii)
      jj = jj + 1
      list(jj) = cella
      lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                             xc(cella), yc(cella), zc(cella), slen(cella) )
    enddo
    jj = jj + 1
    list(jj) = cell1
    lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                           xc(cell1), yc(cell1), zc(cell1), slen(cell1) )
    jj = jj + 1
    list(jj) = cell2
    lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                           xc(cell2), yc(cell2), zc(cell2), slen(cell2) )
    do ii=bcc%flsq_ib(face),bcc%flsq_ib(face+1)-1
      fb = bcc%flsq_jb(ii)
      jj = jj + 1
      list(jj) = fb
      lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                             bcc%xface(fb), bcc%yface(fb), bcc%zface(fb), &
                             bcc%slenface(fb) )
    enddo

    lc_max = lsq_lc_max( jj, lc, face_wsq )

    do ii=1,jj

      wsq   = face_wsq(ii)
      xiel  = lc(1,ii)/lc_max(1)
      etal  = lc(2,ii)/lc_max(2)
      ziel  = lc(3,ii)/lc_max(3)

      !...turbulence variables.
      if ( ii <= flsq_ni + 2 ) then
        qta(1:n) = qcell(n_s:n_e,list(ii))
      else
        qta(1:n) = bcc%qt(n_s:n_e,list(ii))
      endif
      qta(1:n) = wsq*qta(1:n)

      c0(1:n) =  c0(1:n) +      qta(1:n)
      c1(1:n) =  c1(1:n) + xiel*qta(1:n)
      c2(1:n) =  c2(1:n) + etal*qta(1:n)
      c3(1:n) =  c3(1:n) + ziel*qta(1:n)

    enddo

! Forward...sequential access to flsq_lu.

    eq_loop : do eq=1,n

      f(1) = c0(eq)
      f(2) = c1(eq) - flsq_lu(2,1)*f(1)
      f(3) = c2(eq) - flsq_lu(3,1)*f(1)
      f(4) = c3(eq) - flsq_lu(4,1)*f(1)

      f(3) = f(3) - flsq_lu(3,2)*f(2)
      f(4) = f(4) - flsq_lu(4,2)*f(2)

      f(4) = f(4) - flsq_lu(4,3)*f(3)

! Backward...sequential access to flsq_lu.

      f(4) = f(4) * flsq_lu(4,4)
      f(2) = f(2) - flsq_lu(2,4)*f(4)
      f(3) = f(3) - flsq_lu(3,4)*f(4)

      f(3) = f(3) * flsq_lu(3,3)
      f(2) = f(2) - flsq_lu(2,3)*f(3)

      f(2) = f(2) * flsq_lu(2,2)

!     c3(eq) = f(4)
!     c2(eq) = f(3)
!     c1(eq) = f(2)
!     c0(eq) = f(1)

      !...Cartesian gradients at the face.
      fgrad = lsq_gradc( lsq_mref, f(2:4) , lc_max )

      !...gradients in face directions.
      lgrad = fgrad(1)*trf(2,1) + fgrad(2)*trf(2,2) + fgrad(3)*trf(2,3)
      mgrad = fgrad(1)*trf(3,1) + fgrad(2)*trf(3,2) + fgrad(3)*trf(3,3)

      !...directional gradients along edge.
      egrad = 0.5_dp*( qcell(n_s+eq-1,cell2)        &
                     - qcell(n_s+eq-1,cell1) )*scalei

      !...resolve gradients from edge and face.
      grad_flsq(1,eq) = tef(1,1)*egrad + tef(1,2)*lgrad + tef(1,3)*mgrad
      grad_flsq(2,eq) = tef(2,1)*egrad + tef(2,2)*lgrad + tef(2,3)*mgrad
      grad_flsq(3,eq) = tef(3,1)*egrad + tef(3,2)*lgrad + tef(3,3)*mgrad

    enddo eq_loop

  end function grad_flsq

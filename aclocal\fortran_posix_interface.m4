AC_DEFUN([AX_FORTRAN_POSIX_INTERFACE],
[AC_CACHE_CHECK([fortran provides pxf posix interface],
 ax_cv_fortran_posix_interface,
 [AC_LANG_PUSH(Fortran)
  AC_LINK_IFELSE(
  [
       program main
       integer :: status, seconds_left
       character(1000) :: string
       status = ipxfargc()
       call pxfgetarg(1,string)
       call pxfchdir(".", 1, status)
       call pxfsleep(1,seconds_left,status)
       call pxfsystem("touch temp_file_f90_unix", 24, status)
       call pxfunlink("temp_file_f90_unix", 17, status)
       call pxffastexit(0)
       end
  ],
  [ax_cv_fortran_posix_interface=yes],
  [ax_cv_fortran_posix_interface=no]
   )
  AC_LANG_POP(Fortran)
 ])
if test "$ax_cv_fortran_posix_interface" != 'no'
then
 AC_DEFINE([HAVE_PXF],[1],[fortran provides pxf posix interface])
fi
])


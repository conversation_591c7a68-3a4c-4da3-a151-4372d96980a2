module lsq_cc_test

  use kinddefs,        only : dp
  use lsq_cc,          only : clsq_lu_cc, clsq_grad_driver_cc

  implicit none

  private

  public :: test_lsq_cc_gradients

contains

!============================== TEST_GRADIENTS ===============================80
!
! Computes gradients of some test functions using least-squares
!
!=============================================================================80
  subroutine test_lsq_cc_gradients( grid, soln, crow )

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: i

    real(dp), dimension(4,grid%ncell01) :: gradx_exact, grady_exact, gradz_exact

  continue

! Evaluate the weights (not including BC augmentation)

    call clsq_lu_cc( grid, crow )

! Now formulate a linear function

    do i = 1, grid%ncell01
      soln%q_dof(1,i) = 5.0_dp*grid%xc(i)
      soln%q_dof(2,i) = 5.0_dp*grid%yc(i)
      soln%q_dof(3,i) = 5.0_dp*grid%zc(i)
      soln%q_dof(4,i) = 1.0_dp*grid%xc(i) &
                      + 2.0_dp*grid%yc(i) &
                      + 3.0_dp*grid%zc(i)

      gradx_exact(1,i) = 5.0_dp
      gradx_exact(2,i) = 0.0_dp
      gradx_exact(3,i) = 0.0_dp
      gradx_exact(4,i) = 1.0_dp

      grady_exact(1,i) = 0.0_dp
      grady_exact(2,i) = 5.0_dp
      grady_exact(3,i) = 0.0_dp
      grady_exact(4,i) = 2.0_dp

      gradz_exact(1,i) = 0.0_dp
      gradz_exact(2,i) = 0.0_dp
      gradz_exact(3,i) = 5.0_dp
      gradz_exact(4,i) = 3.0_dp
    end do

! Compute the gradient with LS (not including BC augmentation)

   call clsq_grad_driver_cc( grid, soln, crow )

! Evaluate the errors

    write(*,*) 'LINEAR FUNCTION'
    call evaluate_error(4,grid%ncell0,grid%ncell01,soln%gradx,soln%grady,      &
                        soln%gradz,gradx_exact,grady_exact,gradz_exact)

! Now formulate a quadratic function

    do i = 1, grid%ncell01
      soln%q_dof(1,i) = 5.0_dp*grid%xc(i)*grid%xc(i)
      soln%q_dof(2,i) = 5.0_dp*grid%yc(i)*grid%yc(i)
      soln%q_dof(3,i) = 5.0_dp*grid%zc(i)*grid%zc(i)
      soln%q_dof(4,i) = 1.0_dp*grid%xc(i)*grid%zc(i) &
                      + 2.0_dp*grid%yc(i)*grid%yc(i) &
                      + 3.0_dp*grid%zc(i)*grid%zc(i)

      gradx_exact(1,i) = 10.0_dp*grid%xc(i)
      gradx_exact(2,i) =  0.0_dp
      gradx_exact(3,i) =  0.0_dp
      gradx_exact(4,i) =  1.0_dp*grid%zc(i)

      grady_exact(1,i) = 0.0_dp
      grady_exact(2,i) =10.0_dp*grid%yc(i)
      grady_exact(3,i) = 0.0_dp
      grady_exact(4,i) = 4.0_dp*grid%yc(i)

      gradz_exact(1,i) = 0.0_dp
      gradz_exact(2,i) = 0.0_dp
      gradz_exact(3,i) =10.0_dp*grid%zc(i)
      gradz_exact(4,i) = 1.0_dp*grid%xc(i) + 6.0_dp*grid%zc(i)
    end do

! Compute the gradient with LS (not including augmentation)

   call lstgs_driver_cc( grid, soln, crow )

! Evaluate the errors

    write(*,*) 'QUADRATIC FUNCTION'
    call evaluate_error(4,grid%ncell0,grid%ncell01,soln%gradx,soln%grady,      &
                        soln%gradz,gradx_exact,grady_exact,gradz_exact)

  end subroutine test_lsq_cc_gradients


!============================== EVALUATE_ERROR ===============================80
!
!  Evaluates error between computed and analytic gradient functions
!
!=============================================================================80
  subroutine evaluate_error(dmn,ncell0,ncell01,gx,gy,gz,gxe,gye,gze)

    use kinddefs, only : dp

    integer, intent(in) :: ncell0, ncell01, dmn

    real(dp), dimension(dmn,ncell01), intent(inout) :: gx,gy,gz
    real(dp), dimension(dmn,ncell01), intent(in)    :: gxe,gye,gze

    integer :: i, j

    integer, dimension(dmn) :: holdx, holdy, holdz

    real(dp) :: termx, termy, termz

    real(dp), dimension(dmn) :: errx, erry, errz
    real(dp), dimension(dmn) :: maxerrx, maxerry, maxerrz

  continue

    errx(:) = 0.0_dp
    erry(:) = 0.0_dp
    errz(:) = 0.0_dp

    maxerrx(:) = -tiny(real(1.0_dp,dp))
    maxerry(:) = -tiny(real(1.0_dp,dp))
    maxerrz(:) = -tiny(real(1.0_dp,dp))

    do i = 1, ncell0
      do j = 1, dmn

        if ( abs(gxe(j,i)) < .1_dp ) then
          termx = abs(gx(j,i)-gxe(j,i))
        else
          termx = abs(gx(j,i)-gxe(j,i)) / abs(gxe(j,i))
        endif

        if ( abs(gye(j,i)) < .1_dp ) then
          termy = abs(gy(j,i)-gye(j,i))
        else
          termy = abs(gy(j,i)-gye(j,i)) / abs(gye(j,i))
        endif

        if ( abs(gze(j,i)) < .1_dp ) then
          termz = abs(gz(j,i)-gze(j,i))
        else
          termz = abs(gz(j,i)-gze(j,i)) / abs(gze(j,i))
        endif

        errx(j) = errx(j) + termx*termx
        erry(j) = erry(j) + termy*termy
        errz(j) = errz(j) + termz*termz

        if ( termx >= maxerrx(j) ) then
          maxerrx(j) = termx
          holdx(j)   = i
        endif

        if ( termy >= maxerry(j) ) then
          maxerry(j) = termy
          holdy(j)   = i
        endif

        if ( termz >= maxerrz(j) ) then
          maxerrz(j) = termz
          holdz(j)   = i
        endif

      end do
    end do

    do j = 1, dmn
      errx(j) = sqrt(errx(j)) / ncell0
      erry(j) = sqrt(erry(j)) / ncell0
      errz(j) = sqrt(errz(j)) / ncell0
      write(*,'(a,3(1x,e12.5))') 'L2-norm of Error in x y z = ',               &
                                 errx(j), erry(j), errz(j)
    end do

    do j = 1, dmn
      write(*,'(a,3(1x,i10,1x,e12.5))') 'loc, max err in x y z = ',            &
                holdx(j), maxerrx(j), holdy(j), maxerry(j), holdz(j), maxerrz(j)
    end do

  end subroutine evaluate_error

end module lsq_cc_test

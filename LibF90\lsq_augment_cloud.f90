module lsq_augment_cloud

  use cc_defs,           only : augments_max_per_node

  implicit none

  private

  public :: modify_lsq_stencil

contains

!=============================== MODIFY_LSQ_STENCIL ==========================80
!
!  Override smart augmentation stencil info provided in part files with
!  either no augmentation or full augmentation
!
!=============================================================================80
  subroutine modify_lsq_stencil(grid)

    use grid_types,        only : grid_type
    use allocations,       only : my_realloc_ptr, my_alloc_ptr
    use cc_defs,           only : ccrecon
    use lmpi,              only : lmpi_conditional_stop, lmpi_master
    use info_depr,         only : skeleton

    type(grid_type), intent(inout) :: grid

    integer :: i, n_pseudo_faces, ind, central_cell, neighbor_cell
    integer :: ii, istart, iend, min_entry, minsave, jsave, j, ierr

    integer, dimension(:),   pointer :: cell_augmentors
    integer, dimension(:,:), pointer :: pseudo_fptr

  continue

    ierr = 0

    if ( (skeleton > 0) .and. (grid%origin >= 3) ) then
      write(*,*)
      write(*,"(1x,a,i3)") 'Bypassing modification of lsq stencil&
                           &...origin=',grid%origin
    elseif( skeleton > 0 )then
      write(*,*)
      write(*,*) ' Modification of lsq stencil...igrid=',grid%igrid
    endif

    if ( grid%origin /= 1 ) return !agglomerations done elsewhere

    select case(ccrecon)
    case(-2)    ! No interior augmentation ; possible boundary augmentation

      if ( augments_max_per_node < 0 ) then
        if ( lmpi_master ) then
          write(*,*)'No augmentation (usually not possible):'
          write(*,*)'...augments_max_per_node=',augments_max_per_node
        endif
      else
        augments_max_per_node = 0
        if ( lmpi_master ) then
          write(*,*)'No interior augmentation (boundary augmentation only):'
          write(*,*)'...overriding augments_max_per_node=',augments_max_per_node
        endif
      endif

    case(-1)    ! Full augmentation

      augments_max_per_node = 1000
      if ( lmpi_master ) then
        write(*,*)'Full augmentation...'
        write(*,*)'...overriding augments_max_per_node=',augments_max_per_node
      endif

    case(0)    ! Smart augmentation

     if ( lmpi_master ) then
        write(*,*)'Smart augmentation...'
        write(*,*)'...augments_max_per_node=',augments_max_per_node
      endif

    case default

      write(*,*) 'ccrecon unknown in modify_lsq_stencil: ', ccrecon
      ierr = 1

    end select

    call lmpi_conditional_stop(ierr,' Unknown ccrecon : modify_lsq_stencil 1')

    !  Wipe out existing augmentation info

    grid%ncell_augmentors = 0
    grid%rlsq_ia = 0
    call my_realloc_ptr(grid%rlsq_ja, 1); grid%rlsq_ja = 0

    !  Find full augmentors in terms of pseudo faces

    call augment_cloud_node_neighbors(grid,n_pseudo_faces,pseudo_fptr)

    !  Count how many augmentors each local cell wound up with

    call my_alloc_ptr(cell_augmentors, grid%ncell01); cell_augmentors = 0

    do i = 1, n_pseudo_faces
      central_cell  = pseudo_fptr(1,i)
      neighbor_cell = pseudo_fptr(2,i)
      cell_augmentors(central_cell) = cell_augmentors(central_cell) + 1
    end do

    !  Set ia

    grid%rlsq_ia(1) = 1
    do i = 1, grid%ncell01
      grid%rlsq_ia(i+1) = grid%rlsq_ia(i) + cell_augmentors(i)
    end do

    !  Set total number of augmentors

    grid%ncell_augmentors = grid%rlsq_ia(grid%ncell01+1) - 1

    !  Reallocate ja

    call my_realloc_ptr(grid%rlsq_ja, grid%ncell_augmentors); grid%rlsq_ja = 0

    !  Store augmentors in ja

    cell_augmentors = 0

    do i = 1, n_pseudo_faces

      central_cell  = pseudo_fptr(1,i)
      neighbor_cell = pseudo_fptr(2,i)

      ind = grid%rlsq_ia(central_cell) + cell_augmentors(central_cell)

      if ( ind >= grid%rlsq_ia(central_cell+1) ) then
        write(*,*) 'Error tracking indices in modify_lsq_stencil'
        ierr = 1
        exit
      endif

      grid%rlsq_ja(ind) = neighbor_cell
      cell_augmentors(central_cell) = cell_augmentors(central_cell) + 1

    end do

    call lmpi_conditional_stop(ierr,' modify_lsq_stencil 2')

    deallocate(cell_augmentors,pseudo_fptr)

    !  Sort ja

    do ii = 1, grid%ncell01
      istart = grid%rlsq_ia(ii)
      iend   = grid%rlsq_ia(ii+1) - 1
      do i = istart,iend
        min_entry = grid%rlsq_ja(i)
        minsave = grid%rlsq_ja(i)
        jsave   = i
        do j = i+1,iend
          if(grid%rlsq_ja(j) < min_entry)then
            min_entry = grid%rlsq_ja(j)
            jsave = j
          end if
        end do
        grid%rlsq_ja(i) = min_entry
        grid%rlsq_ja(jsave) = minsave
      end do
    end do

  end subroutine modify_lsq_stencil


!============================== AUGMENT_CLOUD_NODE_NEIGHBORS =================80
!
!  Find a list of pseudo-faces connecting central cell with all cells
!  sharing at least one vertex with the central cell
!
!=============================================================================80
  subroutine augment_cloud_node_neighbors(grid,n_pseudo_faces,pseudo_fptr)

    use kinddefs,          only : dp
    use allocations,       only : my_alloc_ptr
    use lmpi,              only : lmpi_conditional_stop
    use grid_types,        only : grid_type
    use twod_util,         only : q_2d, yplane_2d, y_coplanar_tol
    use flux_util,         only : mark_boundaries
    use lmpi,              only : lmpi_reduce, lmpi_master, lmpi_nproc
    use element_defs,      only : type_cell_2_integer

    integer, intent(out) :: n_pseudo_faces

    type(grid_type), intent(inout) :: grid

    integer :: ielem, icell, inode, ip, i, j, k, nloc, is, ib, iostat
    integer :: edge1, edge2, edge3, edge4, local_node, node, ierr
    integer :: local_edge1, local_edge2, local_edge3, local_edge4
    integer :: candidate_cell, candidate_cell_type, tagsum, jj, iedge
    integer :: main_cell, iface, near_neighbor, near_neighbor_type
    integer :: global_cell, start_index, end_index, ineighbor
    integer :: pseudo_neighbor, irepeat, temp_count, global_candidate
    integer :: orbit_node, dsq_max_cell, cells_boundary, cells_interior
    integer :: augments_max, augments_cell, augments_node
    integer :: augments_interior, augments_boundary, augments_total
    integer :: on_proc_cells, on_proc_elems, on_proc_sims, cell1, cell2
    integer :: face

    integer, parameter :: max_count = 1000

    integer, dimension(grid%nnodes01+1)         :: locs
    integer, dimension(grid%ncell01)            :: cell_tag
    integer, dimension(max_count)               :: temp_list
    integer, dimension(grid%nedge)              :: edge_tag

    integer, dimension(:),   pointer :: locvc, locvc_type
    integer, dimension(:,:), pointer :: pseudo_fptr

    type elem_help_type
      integer, dimension(:,:), pointer :: c2c
      integer, dimension(:,:), pointer :: c2c_type
    end type elem_help_type

    type(elem_help_type), dimension(grid%nelem) :: elem_help

    real(dp) :: dsq_max, dsq, x_c, y_c, z_c

    integer, dimension(:), pointer :: boundary_flag

    logical :: cell_nodes_touch_boundary

    integer, parameter  :: iu = 77
    character (len=12) :: filename= 'debug_verify'

    logical, dimension(:), pointer :: similar
    integer, dimension(:), pointer :: tipe_cell

  continue

    ierr = 0

    call my_alloc_ptr(similar,   grid%ncell01) ;   similar(:) = .true.
    call my_alloc_ptr(tipe_cell, grid%ncell01) ; tipe_cell(:) = 0

    if( lmpi_master ) then
      open(unit=iu,file=filename,status='unknown',&
           iostat=iostat, position='append')
      if ( iostat /= 0 ) then
        write(*,*) ' TRIAGE: Problem opening debug_verify file.'
        write(*,*) ' TRIAGE: ...augment_cloud_node_neighbors.'
        write(*,*) ' TRIAGE: ...iostat=',iostat
      else
        write(*,*) ' TRIAGE: Opened debug_verify file...&
                   &augment_cloud_node_neighbors.'
      endif
      write(iu,"(1x,2(a,i10),a,L1)")                         &
      ' Element types=',grid%nelem,' Processors=',lmpi_nproc,&
      ' q_2d=',q_2d
      write(iu,*)
      write(iu,"(1x,6a)")                           &
      '        ','     ','          ','          ', &
                         '     Face-','   %-Face-'
      write(iu,"(1x,6a)")                           &
      '        ','     ','          ','          ', &
                         '   Similar','   Similar'
      write(iu,"(1x,6a)")                           &
      ' Element',' Type','     Cells','    %-Proc', &
                         '     Cells','     Cells'
      write(iu,*)
    endif

    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell
        main_cell = grid%elem(ielem)%cell_map(icell)
        tipe_cell(main_cell) =                        &
        type_cell_2_integer(grid%elem(ielem)%type_cell)
      enddo
    enddo

    cell_faces : do face = 1, grid%nface
      cell1 = grid%fptr(1,face)
      cell2 = grid%fptr(2,face)
      if ( tipe_cell(cell1) == tipe_cell(cell2) ) cycle cell_faces
      similar(cell1) = .false.
      similar(cell2) = .false.
    enddo cell_faces

    do ielem = 1, grid%nelem
      on_proc_cells = 0
      on_proc_sims  = 0
      on_proc_cell : do icell = 1, grid%elem(ielem)%ncell
         do inode = 1, grid%elem(ielem)%node_per_cell
           ip = grid%elem(ielem)%c2n(inode,icell)
           if ( ip > grid%nnodes0 ) cycle on_proc_cell
         end do
         on_proc_cells = on_proc_cells + 1
         if ( similar(grid%elem(ielem)%cell_map(icell)) ) then
           on_proc_sims = on_proc_sims + 1
         endif
      end do on_proc_cell
      i= min(1,on_proc_cells); call lmpi_reduce(i,on_proc_elems)
      i= on_proc_cells       ; call lmpi_reduce(i,on_proc_cells)
      i= on_proc_sims        ; call lmpi_reduce(i,on_proc_sims)
      if ( .not.lmpi_master ) cycle
      write(iu,"(1x,i8,2x,a,2(i10,f10.4))")              &
      ielem,grid%elem(ielem)%type_cell,                  &
      on_proc_cells,                                     &
      100._dp*real(on_proc_elems,dp)/real(lmpi_nproc,dp),&
      on_proc_sims,                                      &
      100._dp*real(on_proc_sims,dp)/real(on_proc_cells,dp)
    end do

    ! Mark boundaries of nodal mesh.

    call my_alloc_ptr( boundary_flag, grid%nnodes01 )
    boundary_flag(:) = 0
    do ib = 1,grid%nbound
      call mark_boundaries(boundary_flag,      grid%nnodes01,       &
                           grid%bc(ib)%ibc,    grid%bc(ib)%nbnode,  &
                           grid%bc(ib)%ibnode, q_2d )
    end do

! First get list of adjacent elements and their family types

    locs = 0
    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell
        do inode = 1, grid%elem(ielem)%node_per_cell
          ip = grid%elem(ielem)%c2n(inode,icell) + 1
          locs(ip) = locs(ip) + 1
        end do
      end do
    end do
    do i = 2, grid%nnodes01+1
      locs(i) = locs(i) + locs(i-1)
    end do

    nloc = locs(grid%nnodes01+1)

    call my_alloc_ptr(locvc,      nloc)
    call my_alloc_ptr(locvc_type, nloc)

    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell
         do inode = 1, grid%elem(ielem)%node_per_cell
           ip = grid%elem(ielem)%c2n(inode,icell)
           is = locs(ip) + 1
           locs(ip) = is
           locvc(is) = icell
           locvc_type(is) = ielem
         end do
      end do
    end do

    do i = grid%nnodes01+1, 2, -1
      locs(i) = locs(i-1)
    end do
    locs(1) = 0

! Allocate c2c arrays for each element

    do ielem = 1, grid%nelem
      call my_alloc_ptr(elem_help(ielem)%c2c, grid%elem(ielem)%ncell,          &
                                                 grid%elem(ielem)%face_per_cell)
      call my_alloc_ptr(elem_help(ielem)%c2c_type, grid%elem(ielem)%ncell,     &
                                                 grid%elem(ielem)%face_per_cell)
      elem_help(ielem)%c2c(:,:)      = 0
      elem_help(ielem)%c2c_type(:,:) = 0
    end do

! Initialize edge_tag array

    edge_tag = 0

! For each cell face determine the cell who shares that face and the type

    elem_loop : do ielem = 1, grid%nelem

      cell_loop : do i = 1, grid%elem(ielem)%ncell

! Find neighbor for each face

        face_loop : do j = 1, grid%elem(ielem)%face_per_cell

          local_edge1 = grid%elem(ielem)%local_f2e(j,1)
          local_edge2 = grid%elem(ielem)%local_f2e(j,2)
          local_edge3 = grid%elem(ielem)%local_f2e(j,3)
          local_edge4 = grid%elem(ielem)%local_f2e(j,4)

          edge1 = grid%elem(ielem)%c2e(local_edge1,i)
          edge2 = grid%elem(ielem)%c2e(local_edge2,i)
          edge3 = grid%elem(ielem)%c2e(local_edge3,i)
          edge4 = grid%elem(ielem)%c2e(local_edge4,i)

          edge_tag(edge1) = 1
          edge_tag(edge2) = 1
          edge_tag(edge3) = 1
          edge_tag(edge4) = 1

          local_node = grid%elem(ielem)%local_f2n(j,1)
          node = grid%elem(ielem)%c2n(local_node,i)

          do k = locs(node) + 1, locs(node+1)       !look at all tets for node
            candidate_cell = locvc(k)
            candidate_cell_type = locvc_type(k)

            if ( candidate_cell == i .and. ielem == candidate_cell_type ) cycle

            tagsum = 0
            do jj = 1, grid%elem(candidate_cell_type)%edge_per_cell
              iedge = grid%elem(candidate_cell_type)%c2e(jj,candidate_cell)
              tagsum = tagsum + edge_tag(iedge)
            end do

            if ( edge1 == edge4 ) then      ! triangle face
              if ( tagsum == 3 ) then
                elem_help(ielem)%c2c(i,j)      = candidate_cell
                elem_help(ielem)%c2c_type(i,j) = candidate_cell_type
              endif
            else                            ! quad face
              if ( tagsum == 4 ) then
                elem_help(ielem)%c2c(i,j)      = candidate_cell
                elem_help(ielem)%c2c_type(i,j) = candidate_cell_type
              endif
            endif

          end do

          edge_tag(edge1) = 0
          edge_tag(edge2) = 0
          edge_tag(edge3) = 0
          edge_tag(edge4) = 0

        end do face_loop

      end do cell_loop

    end do elem_loop

    cell_tag(:) = 0

    ! Do this twice: first time through, just count them
    ! Second time through, store them

    repeat_loop : do irepeat = 1, 2

      augments_boundary = 0
      augments_interior = 0
      cells_boundary    = 0
      cells_interior    = 0
      augments_max      = 0

      n_pseudo_faces = 0

      elem_loop2 : do ielem = 1, grid%nelem
        cell_loop2 : do icell = 1, grid%elem(ielem)%ncell

          main_cell = grid%elem(ielem)%cell_map(icell)

          ! Tag the main cell.

          cell_tag(main_cell) = 1

          x_c = grid%xq(main_cell)
          y_c = grid%yq(main_cell)
          z_c = grid%zq(main_cell)

          ! Check neighbors of each node in the main cell.

          cell_nodes_touch_boundary = .false.
          node_bc_loop : do j = 1, grid%elem(ielem)%node_per_cell

            orbit_node = grid%elem(ielem)%c2n(j,icell)
            if ( q_2d .and. abs( grid%y(orbit_node) - yplane_2d ) &
                                 > y_coplanar_tol ) cycle node_bc_loop

            if ( boundary_flag(orbit_node) == 0 ) cycle

            cell_nodes_touch_boundary = .true.

            exit node_bc_loop

          end do node_bc_loop

          augments_max = augments_max_per_node
          if ( main_cell <= grid%ncell0 ) then

            if ( cell_nodes_touch_boundary ) then
              cells_boundary = cells_boundary + 1
            else
              cells_interior = cells_interior + 1
            endif

            augments_max = augments_max_per_node
            if ( augments_max < 0 ) then
              augments_max = 0
            elseif ( cell_nodes_touch_boundary ) then
              augments_max = 1000
            elseif(similar(main_cell) ) then
              if ( tipe_cell(main_cell) == type_cell_2_integer('hex') ) then
                augments_max = 0
              endif
            endif

          endif

          ! Tag the face neighbors.

          face_loop3 : do iface = 1, grid%elem(ielem)%face_per_cell
            near_neighbor = elem_help(ielem)%c2c(icell,iface)
            if ( near_neighbor == 0 ) cycle face_loop3
            near_neighbor_type = elem_help(ielem)%c2c_type(icell,iface)
            global_cell = grid%elem(near_neighbor_type)%cell_map(near_neighbor)
            cell_tag(global_cell) = 1
          end do face_loop3

          start_index = -1
          end_index   = -2
          temp_count  = 0

          ! Loop over the neighbors of each node defining the main cell.

          augments_cell = 0
          node_loop : do j = 1, grid%elem(ielem)%node_per_cell

            if ( augments_max == 0 ) cycle node_loop

            orbit_node = grid%elem(ielem)%c2n(j,icell)
            if ( q_2d .and. abs( grid%y(orbit_node) - yplane_2d ) &
                                 > y_coplanar_tol ) cycle node_loop

            dsq_max       = 0._dp
            dsq_max_cell  = 0
            augments_node = 0

            if ( augments_max == 1 ) then

              farthest : do k = locs(orbit_node) + 1, locs(orbit_node+1)
                candidate_cell      = locvc(k)
                candidate_cell_type = locvc_type(k)
                global_candidate =                                             &
                         grid%elem(candidate_cell_type)%cell_map(candidate_cell)

                if ( cell_tag(global_candidate) > 0 ) cycle farthest

                dsq = ( grid%xq(global_candidate) - x_c )**2 &
                    + ( grid%yq(global_candidate) - y_c )**2 &
                    + ( grid%zq(global_candidate) - z_c )**2
                if ( dsq > dsq_max ) then
                  dsq_max = dsq
                  dsq_max_cell = global_candidate
                endif

              end do farthest

              if ( dsq_max == 0 ) cycle node_loop

              ! Install choice

              n_pseudo_faces = n_pseudo_faces + 1
              augments_node = augments_node + 1
              augments_cell = augments_cell + 1
              if ( irepeat == 2 ) then
                pseudo_fptr(1,n_pseudo_faces) = main_cell
                pseudo_fptr(2,n_pseudo_faces) = dsq_max_cell
              else
                temp_count = temp_count + 1
                if ( temp_count > max_count ) then
                  write(*,*) 'stop (1) in augment_cloud_node_neighbors'
                  write(*,*) 'temp_count,max_count=',temp_count,max_count
                  ierr = 1 ; exit elem_loop2
                endif
                temp_list(temp_count) = dsq_max_cell
              endif

              cell_tag(dsq_max_cell) = 1

              if ( start_index < 0 ) start_index = n_pseudo_faces
              end_index   = n_pseudo_faces

            else
              hunt : do k = locs(orbit_node) + 1, locs(orbit_node+1)
                candidate_cell      = locvc(k)
                candidate_cell_type = locvc_type(k)
                global_candidate =                                             &
                         grid%elem(candidate_cell_type)%cell_map(candidate_cell)

                if ( cell_tag(global_candidate) > 0 ) cycle hunt
                if ( augments_node == augments_max ) cycle hunt

                n_pseudo_faces = n_pseudo_faces + 1
                augments_node  = augments_node + 1
                augments_cell  = augments_cell + 1
                if ( irepeat == 2 ) then
                  pseudo_fptr(1,n_pseudo_faces) = main_cell
                  pseudo_fptr(2,n_pseudo_faces) = global_candidate
                else
                  temp_count = temp_count + 1
                  if ( temp_count > max_count ) then
                    write(*,*) 'stop (2) in augment_cloud_node_neighbors'
                    write(*,*) 'temp_count,max_count=',temp_count,max_count
                    ierr = 2 ; exit elem_loop2
                  endif
                  temp_list(temp_count) = global_candidate
                endif

                cell_tag(global_candidate) = 1

                if ( start_index < 0 ) start_index = n_pseudo_faces
                end_index   = n_pseudo_faces

              end do hunt
            end if

          end do node_loop

          ! Untag main cell.

          cell_tag(main_cell) = 0

          ! Untag primary face neighbors.

          face_loop4 : do iface = 1, grid%elem(ielem)%face_per_cell
            near_neighbor = elem_help(ielem)%c2c(icell,iface)
            if ( near_neighbor == 0 ) cycle face_loop4
            near_neighbor_type = elem_help(ielem)%c2c_type(icell,iface)
            global_cell = grid%elem(near_neighbor_type)%cell_map(near_neighbor)
            cell_tag(global_cell) = 0
          end do face_loop4

          ! Untag pseudo edge neighbors

          if ( irepeat == 2 ) then
            do ineighbor = start_index, end_index
              pseudo_neighbor = pseudo_fptr(2,ineighbor)
              cell_tag(pseudo_neighbor) = 0
            end do
          else
            do ineighbor = 1, temp_count
              pseudo_neighbor = temp_list(ineighbor)
              cell_tag(pseudo_neighbor) = 0
            end do
          endif

          if ( cell_nodes_touch_boundary ) then
            if ( main_cell <= grid%ncell0 ) &
            augments_boundary = augments_boundary + augments_cell
          else
            if ( main_cell <= grid%ncell0 ) &
            augments_interior = augments_interior + augments_cell
          endif
        end do cell_loop2

      end do elem_loop2

      call lmpi_conditional_stop(ierr,' augment_cloud_node_neighbors')

      if ( irepeat == 1 ) then
        call my_alloc_ptr(pseudo_fptr, 2, n_pseudo_faces)
      endif

    end do repeat_loop

    do ielem = 1, grid%nelem
      deallocate(elem_help(ielem)%c2c, elem_help(ielem)%c2c_type)
    end do
    deallocate(locvc,locvc_type)
    deallocate(boundary_flag)
    deallocate(similar, tipe_cell)

    i=augments_interior ; call lmpi_reduce(i,augments_interior)
    i=augments_boundary ; call lmpi_reduce(i,augments_boundary)
    i=cells_boundary    ; call lmpi_reduce(i,cells_boundary)
    i=cells_interior    ; call lmpi_reduce(i,cells_interior)
    augments_total = augments_interior + augments_boundary
    if( lmpi_master ) then
      do i=6,iu,iu-6
        write(i,*)
        write(i,*)                    ' Cell-based least squares augmentation.'
        write(i,"(1x,a,i10)")         ' ..Number of cells:'
        write(i,"(1x,a,i10,a,f12.5)") ' ....boundary-touching=',cells_boundary,&
        ' fraction=',real(cells_boundary,dp)/real(grid%ncellg,dp)
        write(i,"(1x,a,i10,a,f12.5)") ' .............interior=',cells_interior,&
        ' fraction=',real(cells_interior,dp)/real(grid%ncellg,dp)
        write(i,"(1x,a,i10,a,f12.5)") ' ................total=',grid%ncellg,&
        ' fraction=',real(cells_boundary+cells_interior,dp)/real(grid%ncellg,dp)
        write(i,"(1x,a,f10.2)") ' ..Average augmentations per cell:'
        write(i,"(1x,a,f10.2)") ' ....boundary-touching=',&
        real(augments_boundary,dp)/real(cells_boundary,dp)
        if ( cells_interior > 0 ) then
          write(i,"(1x,a,f10.2)") ' .............interior=',          &
          real(augments_interior,dp)/real(cells_interior,dp)
        endif
        write(i,"(1x,a,f10.2)") ' ................total=',&
        real(augments_total,dp)/real(grid%ncellg,dp)
        write(i,"(1x,a,f10.2)") &
        ' .Number of augmentations:'
        write(i,"(1x,a,i10,a,f12.5)")                                 &
        ' ....boundary-touching=',augments_boundary,                  &
        ' fraction=',real(augments_boundary,dp)/real(augments_total,dp)
        write(i,"(1x,a,i10,a,f12.5)")                                 &
        ' .............interior=',augments_interior,                  &
        ' fraction=',real(augments_interior,dp)/real(augments_total,dp)
        write(i,"(1x,a,i10,a,f12.5)")                                 &
        ' ................total=',augments_total,                     &
        ' fraction=',real(augments_boundary+augments_interior,dp)/    &
        real(augments_total,dp)
      enddo
    endif

    if ( lmpi_master ) close(iu)

  end subroutine augment_cloud_node_neighbors

end module lsq_augment_cloud

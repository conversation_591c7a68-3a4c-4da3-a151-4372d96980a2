module gmres_matvec

  use kinddefs, only : dp

  implicit none

  private

  public :: matvecaa_4, matvecaa_5, matvecaa_6
  public :: matvecaa_5_turbonly, matvecaa_6_turbonly

contains

!================================ MATVECAA_4 =================================80
!
! Matrix-vector product for 4x4
!
!=============================================================================80

  subroutine matvecaa_4( nnodes01, small_stencil_nnz01, ia, ja, aa, dq, tres,  &
                         adim, nfunctions, mv_extent, perform_sumnode )

    use lmpi_app, only : lmpi_sumnode

    integer,                                intent(in)    :: adim
    integer,                                intent(in)    :: nfunctions
    integer,                                intent(in)    :: mv_extent

    integer,                                intent(in)    :: nnodes01
    integer,                                intent(in)    :: small_stencil_nnz01
    integer,  dimension(nnodes01+1),        intent(in)    :: ia
    integer,  dimension(small_stencil_nnz01), intent(in)  :: ja
    real(dp), dimension(:,:,:),             intent(in)    :: aa
    real(dp), dimension(adim,nnodes01,nfunctions),    intent(in)    :: dq
    real(dp), dimension(adim,nnodes01,nfunctions),    intent(inout) :: tres

    logical, intent(in) :: perform_sumnode

    real(dp) :: dq1,dq2,dq3,dq4

    integer :: i, ii, istart, iend, icol, k

    continue

! Now do the matrix-vector product

    do k = 1, nfunctions
      do i = 1, mv_extent
        istart = ia(i)       ! Row of matrix starts here
        iend   = ia(i+1) - 1 ! Row of matrix ends here

! Loop over the columns in the matrix

        do ii = istart,iend
          icol = ja(ii)

          dq1 = dq(1,icol,k)
          dq2 = dq(2,icol,k)
          dq3 = dq(3,icol,k)
          dq4 = dq(4,icol,k)

          tres(1,i,k) = tres(1,i,k) + aa(1,1,ii)*dq1 + aa(1,2,ii)*dq2          &
                                    + aa(1,3,ii)*dq3 + aa(1,4,ii)*dq4
          tres(2,i,k) = tres(2,i,k) + aa(2,1,ii)*dq1 + aa(2,2,ii)*dq2          &
                                    + aa(2,3,ii)*dq3 + aa(2,4,ii)*dq4
          tres(3,i,k) = tres(3,i,k) + aa(3,1,ii)*dq1 + aa(3,2,ii)*dq2          &
                                    + aa(3,3,ii)*dq3 + aa(3,4,ii)*dq4
          tres(4,i,k) = tres(4,i,k) + aa(4,1,ii)*dq1 + aa(4,2,ii)*dq2          &
                                    + aa(4,3,ii)*dq3 + aa(4,4,ii)*dq4
        end do
      end do

      if ( perform_sumnode ) call lmpi_sumnode(tres(:,:,k))

    end do

  end subroutine matvecaa_4


!================================ MATVECAA_5 =================================80
!
! Matrix-vector product for 5x5
!
!=============================================================================80

  subroutine matvecaa_5( nnodes01, small_stencil_nnz01, ia, ja, aa, dq, tres,  &
                         adim, nfunctions, mv_extent, perform_sumnode )

    use lmpi_app, only : lmpi_sumnode

    integer,                                intent(in)    :: adim
    integer,                                intent(in)    :: nfunctions
    integer,                                intent(in)    :: mv_extent

    integer,                                intent(in)    :: nnodes01
    integer,                                intent(in)    :: small_stencil_nnz01
    integer, dimension(nnodes01+1),         intent(in)    :: ia
    integer, dimension(small_stencil_nnz01),intent(in)    :: ja
    real(dp), dimension(:,:,:),             intent(in)    :: aa
    real(dp), dimension(adim,nnodes01,nfunctions),    intent(in)    :: dq
    real(dp), dimension(adim,nnodes01,nfunctions),    intent(inout) :: tres

    logical, intent(in) :: perform_sumnode

    real(dp) :: dq1,dq2,dq3,dq4,dq5

    integer :: i, ii, istart, iend, icol, k

    continue

! Now do the matrix-vector product

    do k = 1, nfunctions
      do i = 1, mv_extent
        istart = ia(i)       ! Row of matrix starts here
        iend   = ia(i+1) - 1 ! Row of matrix ends here

! Loop over the columns in the matrix

        do ii = istart,iend
          icol = ja(ii)

          dq1 = dq(1,icol,k)
          dq2 = dq(2,icol,k)
          dq3 = dq(3,icol,k)
          dq4 = dq(4,icol,k)
          dq5 = dq(5,icol,k)

          tres(1,i,k) = tres(1,i,k) + aa(1,1,ii)*dq1 + aa(1,2,ii)*dq2          &
                                    + aa(1,3,ii)*dq3 + aa(1,4,ii)*dq4          &
                                    + aa(1,5,ii)*dq5
          tres(2,i,k) = tres(2,i,k) + aa(2,1,ii)*dq1 + aa(2,2,ii)*dq2          &
                                    + aa(2,3,ii)*dq3 + aa(2,4,ii)*dq4          &
                                    + aa(2,5,ii)*dq5
          tres(3,i,k) = tres(3,i,k) + aa(3,1,ii)*dq1 + aa(3,2,ii)*dq2          &
                                    + aa(3,3,ii)*dq3 + aa(3,4,ii)*dq4          &
                                    + aa(3,5,ii)*dq5
          tres(4,i,k) = tres(4,i,k) + aa(4,1,ii)*dq1 + aa(4,2,ii)*dq2          &
                                    + aa(4,3,ii)*dq3 + aa(4,4,ii)*dq4          &
                                    + aa(4,5,ii)*dq5
          tres(5,i,k) = tres(5,i,k) + aa(5,1,ii)*dq1 + aa(5,2,ii)*dq2          &
                                    + aa(5,3,ii)*dq3 + aa(5,4,ii)*dq4          &
                                    + aa(5,5,ii)*dq5
        end do
      end do

      if ( perform_sumnode ) call lmpi_sumnode(tres(:,:,k))

    end do

  end subroutine matvecaa_5


!================================ MATVECAA_6 =================================80
!
! Matrix-vector product 6x6
!
!=============================================================================80

  subroutine matvecaa_6( nnodes01,small_stencil_nnz01, ia, ja, aa, dq, tres,   &
                         adim, nfunctions, mv_extent, perform_sumnode )

    use lmpi_app, only : lmpi_sumnode

    integer,                                intent(in)    :: adim
    integer,                                intent(in)    :: nfunctions
    integer,                                intent(in)    :: mv_extent

    integer,                                intent(in)    :: nnodes01
    integer,                                intent(in)    :: small_stencil_nnz01
    integer,  dimension(nnodes01+1),        intent(in)    :: ia
    integer,  dimension(small_stencil_nnz01), intent(in)  :: ja
    real(dp), dimension(:,:,:),             intent(in)    :: aa
    real(dp), dimension(adim,nnodes01,nfunctions),    intent(in)    :: dq
    real(dp), dimension(adim,nnodes01,nfunctions),    intent(inout) :: tres

    logical, intent(in) :: perform_sumnode

    real(dp) :: dq1,dq2,dq3,dq4,dq5,dq6

    integer :: i, ii, istart, iend, icol, k

    continue

! Now do the matrix-vector product

    do k = 1, nfunctions
      do i = 1, mv_extent
        istart = ia(i)       ! Row of matrix starts here
        iend   = ia(i+1) - 1 ! Row of matrix ends here

! Loop over the columns in the matrix

        do ii = istart,iend
          icol = ja(ii)

          dq1 = dq(1,icol,k)
          dq2 = dq(2,icol,k)
          dq3 = dq(3,icol,k)
          dq4 = dq(4,icol,k)
          dq5 = dq(5,icol,k)
          dq6 = dq(6,icol,k)

          tres(1,i,k) = tres(1,i,k) + aa(1,1,ii)*dq1 + aa(1,2,ii)*dq2          &
                                    + aa(1,3,ii)*dq3 + aa(1,4,ii)*dq4          &
                                    + aa(1,5,ii)*dq5 + aa(1,6,ii)*dq6
          tres(2,i,k) = tres(2,i,k) + aa(2,1,ii)*dq1 + aa(2,2,ii)*dq2          &
                                    + aa(2,3,ii)*dq3 + aa(2,4,ii)*dq4          &
                                    + aa(2,5,ii)*dq5 + aa(2,6,ii)*dq6
          tres(3,i,k) = tres(3,i,k) + aa(3,1,ii)*dq1 + aa(3,2,ii)*dq2          &
                                    + aa(3,3,ii)*dq3 + aa(3,4,ii)*dq4          &
                                    + aa(3,5,ii)*dq5 + aa(3,6,ii)*dq6
          tres(4,i,k) = tres(4,i,k) + aa(4,1,ii)*dq1 + aa(4,2,ii)*dq2          &
                                    + aa(4,3,ii)*dq3 + aa(4,4,ii)*dq4          &
                                    + aa(4,5,ii)*dq5 + aa(4,6,ii)*dq6
          tres(5,i,k) = tres(5,i,k) + aa(5,1,ii)*dq1 + aa(5,2,ii)*dq2          &
                                    + aa(5,3,ii)*dq3 + aa(5,4,ii)*dq4          &
                                    + aa(5,5,ii)*dq5 + aa(5,6,ii)*dq6
          tres(6,i,k) = tres(6,i,k) + aa(6,1,ii)*dq1 + aa(6,2,ii)*dq2          &
                                    + aa(6,3,ii)*dq3 + aa(6,4,ii)*dq4          &
                                    + aa(6,5,ii)*dq5 + aa(6,6,ii)*dq6

        end do
      end do

      if ( perform_sumnode ) call lmpi_sumnode(tres(:,:,k))

    end do

  end subroutine matvecaa_6


!================================ MATVECAA_5_TURBONLY ========================80
!
! Matrix-vector product on only the turb residual of a 5x5
!
!=============================================================================80

  subroutine matvecaa_5_turbonly( nnodes01, small_stencil_nnz01, ia, ja, aa,   &
                                  dq, tres, adim, mv_extent, perform_sumnode,  &
                                  nfunctions )

    use lmpi_app, only : lmpi_sumnode

    integer,                                intent(in)    :: adim
    integer,                                intent(in)    :: mv_extent
    integer,                                intent(in)    :: nfunctions

    integer,                                intent(in)    :: nnodes01
    integer,                                intent(in)    :: small_stencil_nnz01
    integer,  dimension(nnodes01+1),        intent(in)    :: ia
    integer,  dimension(small_stencil_nnz01), intent(in)  :: ja
    real(dp), dimension(:,:,:),             intent(in)    :: aa
    real(dp), dimension(adim,nnodes01,nfunctions), intent(in)    :: dq
    real(dp), dimension(adim,nnodes01,nfunctions), intent(inout) :: tres

    logical, intent(in) :: perform_sumnode

    real(dp) :: dq1,dq2,dq3,dq4,dq5

    integer :: i, ii, istart, iend, icol, k

    continue

! Now do the matrix-vector product

    do k = 1, nfunctions
      do i = 1, mv_extent
        istart = ia(i)       ! Row of matrix starts here
        iend   = ia(i+1) - 1 ! Row of matrix ends here

! Loop over the columns in the matrix

        do ii = istart,iend
          icol = ja(ii)

          dq1 = dq(1,icol,k)
          dq2 = dq(2,icol,k)
          dq3 = dq(3,icol,k)
          dq4 = dq(4,icol,k)
          dq5 = dq(5,icol,k)

          tres(5,i,k)=tres(5,i,k)+aa(5,1,ii)*dq1+aa(5,2,ii)*dq2                &
                                 +aa(5,3,ii)*dq3+aa(5,4,ii)*dq4+aa(5,5,ii)*dq5

        end do
      end do

      if ( perform_sumnode ) call lmpi_sumnode(tres(5,:,k))

    end do

  end subroutine matvecaa_5_turbonly


!================================ MATVECAA_6_TURBONLY ========================80
!
! Matrix-vector product on only the turb residual of a 6x6
!
!=============================================================================80

  subroutine matvecaa_6_turbonly( nnodes01, small_stencil_nnz01, ia, ja, aa,   &
                                  dq, tres, adim, mv_extent, perform_sumnode,  &
                                  nfunctions )

    use lmpi_app, only : lmpi_sumnode

    integer,                                intent(in)    :: adim
    integer,                                intent(in)    :: mv_extent
    integer,                                intent(in)    :: nfunctions

    integer,                                intent(in)    :: nnodes01
    integer,                                intent(in)    :: small_stencil_nnz01
    integer,  dimension(nnodes01+1),          intent(in)  :: ia
    integer,  dimension(small_stencil_nnz01), intent(in)  :: ja
    real(dp), dimension(:,:,:),             intent(in)    :: aa
    real(dp), dimension(adim,nnodes01,nfunctions), intent(in)    :: dq
    real(dp), dimension(adim,nnodes01,nfunctions), intent(inout) :: tres

    logical, intent(in) :: perform_sumnode

    real(dp) :: dq1,dq2,dq3,dq4,dq5,dq6

    integer :: i, ii, istart, iend, icol, k

    continue

! Now do the matrix-vector product

    do k = 1, nfunctions
      do i = 1, mv_extent
        istart = ia(i)       ! Row of matrix starts here
        iend   = ia(i+1) - 1 ! Row of matrix ends here

! Loop over the columns in the matrix

        do ii = istart,iend
          icol = ja(ii)

          dq1 = dq(1,icol,k)
          dq2 = dq(2,icol,k)
          dq3 = dq(3,icol,k)
          dq4 = dq(4,icol,k)
          dq5 = dq(5,icol,k)
          dq6 = dq(6,icol,k)

          tres(6,i,k) = tres(6,i,k) + aa(6,1,ii)*dq1 + aa(6,2,ii)*dq2          &
                                    + aa(6,3,ii)*dq3 + aa(6,4,ii)*dq4          &
                                    + aa(6,5,ii)*dq5 + aa(6,6,ii)*dq6

        end do
      end do

      if ( perform_sumnode ) call lmpi_sumnode(tres(6,:,k))

    end do

  end subroutine matvecaa_6_turbonly

end module gmres_matvec

!====================== GET_Q_DDT ============================================80
!
! Both compressible (eqn_set = 0) and incompressible (eqn_set = 1)
! Return primitives for either q_type
!
!=============================================================================80
  pure function get_q_ddt( eqn_set, n_tot, n_turb, kloc, wloc, qnode, qturb )  &
           result ( q_ddt )

    use ddt,            only : ddt7, ddt7_identity, assignment(=),             &
                               operator(/), operator(*),                       &
                               operator(+), operator(-)
    use kinddefs,       only : dp
    use solution_types, only : compressible
    use thermo,         only : primitive_q_type, conserved_q_type, q_type

    integer,                     intent(in)  :: eqn_set
    integer,                     intent(in)  :: n_tot
    integer,                     intent(in)  :: n_turb
    integer,                     intent(in)  :: kloc
    integer,                     intent(in)  :: wloc
    real(dp), dimension(n_tot),  intent(in)  :: qnode
    real(dp), dimension(n_turb), intent(in)  :: qturb

    real(dp), dimension(n_tot+n_turb)        :: q
    real(dp), dimension(n_tot)               :: q_temp
    type(ddt7), dimension(n_tot+n_turb)      :: q_ddt

    real(dp), parameter :: one = 1.0_dp

  continue

    q_temp(1:n_tot) = qnode(1:n_tot)

    if ( eqn_set == compressible .and. q_type == primitive_q_type ) then
      q(1:5)     = q_temp(1:5)
    elseif ( eqn_set == compressible .and. q_type == conserved_q_type ) then
      q(1)       = q_temp(1)
      q(2)       = q_temp(2) / q(1)
      q(3)       = q_temp(3) / q(1)
      q(4)       = q_temp(4) / q(1)
      q(5)       = get_p( q_temp(1:5) )
    else
      q(1)       = one
      q(2:4)     = q_temp(2:4)
      q(5)       = q_temp(1)
    endif

    q(kloc)    = qturb(1)
    q(wloc)    = qturb(2)
    q_ddt      = ddt7_identity(q)

  end function get_q_ddt

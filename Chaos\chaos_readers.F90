module chaos_readers

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  implicit none

  private

  public :: read_res_vol, read_dfdq, read_drdqt, read_info, read_drdd

contains

!==================================== READ_INFO ==============================80
!
!   Reads basic information about the problem
!
!=============================================================================80
  subroutine read_info()

    use chaos_datas,       only : m0, m1, power, dt, obj_target, obj, omega,   &
                                  ntimesteps
    use file_utils,        only : available_unit
    use system_extensions, only : se_open

    integer :: iu, i

    character(len=1000) :: filename

  continue

    iu = available_unit()
    filename = '../Flow/problem_definition.data'
    call se_open(iu,file=trim(filename),form='unformatted',status='unknown',   &
                 access='stream')

    read(iu) m0, m1, power, dt, obj_target, omega

    allocate(obj(ntimesteps))

    do i = 1, ntimesteps
      read(iu) obj(i)
    end do

    close(iu)

  end subroutine read_info


!==================================== READ_METADATA ==========================80
!
!   Reads the metadata
!
!=============================================================================80
  subroutine read_metadata(set_baseline,filename,ptr)

    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use chaos_datas,       only : nnodes, ntimesteps
    use lmpi,              only : lmpi_min, lmpi_bcast, lmpi_id, lmpi_die
    use kinddefs,          only : i8

    logical, intent(in) :: set_baseline

    integer(i8), dimension(ntimesteps), intent(out) :: ptr

    character(len=*), intent(in) :: filename

    integer :: iu, j, globalmin, ntimesteps_file, nnodes0_file, nnodesg_file

  continue

    iu = available_unit()
    call se_open(iu,file=trim(filename),form='formatted',status='unknown')

    read(iu,*) ntimesteps_file, nnodes0_file, nnodesg_file

    if ( set_baseline ) then
      nnodes = nnodesg_file
      call lmpi_min(nnodes,globalmin)
      call lmpi_bcast(globalmin)
      if ( globalmin /= nnodes ) then
        write(*,*)'Error: metafile contains inconsistent nnodes: ',lmpi_id,    &
                   globalmin, nnodes, trim(filename)
        call lmpi_die
        stop
      endif
    endif

    if ( nnodesg_file /= nnodes ) then
      write(*,*) 'Error: metafile contains inconsistent nnodes:',lmpi_id,      &
                 nnodesg_file, nnodes, trim(filename)
      call lmpi_die
      stop
    endif

    if ( ntimesteps_file /= ntimesteps ) then
      write(*,*) 'Error: metafile contains incorrect no of time steps.',       &
                 lmpi_id, ntimesteps_file, trim(filename)
      call lmpi_die
      stop
    endif

    ptr(:) = -1

    do j = 1, ntimesteps
      read(iu,*) ptr(j)
    end do

    close(iu)

    if ( .false. ) write(*,*) nnodes0_file

  end subroutine read_metadata


!==================================== READ_RES_VOL ===========================80
!
!   Reads the nonlinear spatial residual and volumes
!
!=============================================================================80
  subroutine read_res_vol(timestep, time_data)

    use string_utils,      only : int_to_s
    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use chaos_datas,       only : nnodes, time_data_type, nparts, ntimesteps
    use lmpi,              only : lmpi_id, lmpi_die
    use kinddefs,          only : dp, i8

    integer, intent(in) :: timestep

    type(time_data_type), intent(inout) :: time_data

    integer :: iu, j, i, ntimesteps_file, nnodes0_file, nnodesg_file

    integer,     dimension(:), allocatable :: l2g
    integer(i8), dimension(:), allocatable :: ptr

    real(dp), dimension(:,:), allocatable :: temp

    logical :: set_baseline

    character(len=1000) :: filename

  continue

! Loop over all of the spatial partitions and grab the data for the time plane
! requested

    allocate(ptr(ntimesteps))

    spatial_partitions : do i = 1, nparts

! First open up the metadata file and grab the file pointers for each timestep

      set_baseline = .false.
      if ( i == 1 ) set_baseline = .true.

      filename = '../Flow/residual.metadata.' // trim(int_to_s(i))

      call read_metadata(set_baseline,filename,ptr)

! Now read the big data file

      iu = available_unit()
      filename = '../Flow/residual.data.' // trim(int_to_s(i))
      call se_open(iu,file=trim(filename),form='unformatted',status='unknown', &
                   access='stream')

      read(iu) ntimesteps_file, nnodes0_file, nnodesg_file

      if ( nnodesg_file /= nnodes ) then
        write(*,*) 'Error: res file contains inconsistent nnodes:', lmpi_id,   &
                   nnodesg_file, nnodes
        call lmpi_die
        stop
      endif

      if ( ntimesteps_file /= ntimesteps ) then
        write(*,*) 'Error: res file contains incorrect no of time steps.',     &
                   lmpi_id, ntimesteps_file
        call lmpi_die
        stop
      endif

! Load in this partition l2g and allocate/load global vol array if on first pass

      allocate(l2g(nnodes0_file))

      if ( i == 1 ) allocate(time_data%vol(nnodes))

      do j = 1, nnodes0_file
        read(iu) l2g(j)
        read(iu) time_data%vol(l2g(j))
      end do

! Allocate a temp to load the residual array for desired time step, skipping
! straight to the time plane of interest for our current rank

      allocate(temp(5,nnodes0_file))

#ifdef HAVE_OPEN_STREAM
      read(iu,pos=ptr(timestep)) temp(:,:)
#else
      temp = 0.0_dp ! silence compiler
#endif

      close(iu)

! Allocate global res array if on first pass

      if ( i == 1 ) allocate(time_data%res(5,nnodes))

! Put temp entries into global res array and clean up for next pass

      do j = 1, nnodes0_file
        time_data%res(:,l2g(j)) = temp(:,j)
      end do

      deallocate(l2g,temp)

    end do spatial_partitions

    deallocate(ptr)

  end subroutine read_res_vol


!==================================== READ_DFDQ ==============================80
!
!   Reads the cost function jacobian
!
!=============================================================================80
  subroutine read_dfdq(timestep, time_data)

    use string_utils,      only : int_to_s
    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use chaos_datas,       only : nnodes, time_data_type, nparts, ntimesteps
    use lmpi,              only : lmpi_id, lmpi_die
    use kinddefs,          only : dp, i8

    integer, intent(in) :: timestep

    type(time_data_type), intent(inout) :: time_data

    integer :: iu, j, i, ntimesteps_file, nnodes0_file, nnodesg_file, indx

    integer,     dimension(:), allocatable :: l2g
    integer(i8), dimension(:), allocatable :: ptr

    real(dp), dimension(:,:), allocatable :: temp

    character(len=1000) :: filename

  continue

! Loop over all of the spatial partitions and grab the data for the time plane
! requested

    allocate(ptr(ntimesteps))

    spatial_partitions : do i = 1, nparts

! First open up the metadata file and grab the file pointers for each timestep

      filename = 'dfdq.metadata.' // trim(int_to_s(i))

      call read_metadata(.false.,filename,ptr)

! Now read the big data file

      iu = available_unit()
      filename = 'dfdq.data.' // trim(int_to_s(i))
      call se_open(iu,file=trim(filename),form='unformatted',status='unknown', &
                   access='stream')

      read(iu) ntimesteps_file, nnodes0_file, nnodesg_file

      if ( nnodesg_file /= nnodes ) then
        write(*,*) 'Error: dfdq file contains inconsistent nnodes:', lmpi_id,  &
                   nnodesg_file, nnodes
        call lmpi_die
        stop
      endif

      if ( ntimesteps_file /= ntimesteps ) then
        write(*,*) 'Error: dfdq file contains incorrect no of time steps.',    &
                   lmpi_id, ntimesteps_file
        call lmpi_die
        stop
      endif

! Load in this partition l2g and allocate/load global vol array if on first pass

      allocate(l2g(nnodes0_file))

      do j = 1, nnodes0_file
        read(iu) l2g(j)
      end do

! Allocate a temp to load the residual array for desired time step, skipping
! straight to the time plane of interest for our current rank

      allocate(temp(5,nnodes0_file))

! Remember data is stored backwards in time

      indx = ntimesteps-timestep+1

#ifdef HAVE_OPEN_STREAM
      read(iu,pos=ptr(indx)) temp(:,:)
#else
      temp = 0.0_dp ! silence compiler
#endif

      close(iu)

! Allocate global res array if on first pass

      if ( i == 1 ) allocate(time_data%dfdq(5,nnodes))

! Put temp entries into global res array and clean up for next pass

      do j = 1, nnodes0_file
        time_data%dfdq(:,l2g(j)) = temp(:,j)
      end do

      deallocate(l2g,temp)

    end do spatial_partitions

    deallocate(ptr)

  end subroutine read_dfdq


!==================================== READ_DRDD ==============================80
!
!   Reads the jacobian of the residual wrt design variable
!
!=============================================================================80
  subroutine read_drdd(timestep, time_data)

    use string_utils,      only : int_to_s
    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use chaos_datas,       only : nnodes, time_data_type, nparts, ntimesteps
    use lmpi,              only : lmpi_id, lmpi_die
    use kinddefs,          only : dp, i8

    integer, intent(in) :: timestep

    type(time_data_type), intent(inout) :: time_data

    integer :: iu, j, i, ntimesteps_file, nnodes0_file, nnodesg_file, indx

    integer,     dimension(:), allocatable :: l2g
    integer(i8), dimension(:), allocatable :: ptr

    real(dp), dimension(:,:), allocatable :: temp

    character(len=1000) :: filename

  continue

! Loop over all of the spatial partitions and grab the data for the time plane
! requested

    allocate(ptr(ntimesteps))

    spatial_partitions : do i = 1, nparts

! First open up the metadata file and grab the file pointers for each timestep

      filename = 'drdd.metadata.' // trim(int_to_s(i))

      call read_metadata(.false.,filename,ptr)

! Now read the big data file

      iu = available_unit()
      filename = 'drdd.data.' // trim(int_to_s(i))
      call se_open(iu,file=trim(filename),form='unformatted',status='unknown', &
                   access='stream')

      read(iu) ntimesteps_file, nnodes0_file, nnodesg_file

      if ( nnodesg_file /= nnodes ) then
        write(*,*) 'Error: drdd file contains inconsistent nnodes:', lmpi_id,  &
                   nnodesg_file, nnodes
        call lmpi_die
        stop
      endif

      if ( ntimesteps_file /= ntimesteps ) then
        write(*,*) 'Error: drdd file contains incorrect no of time steps.',    &
                   lmpi_id, ntimesteps_file
        call lmpi_die
        stop
      endif

! Load in this partition l2g and allocate/load global vol array if on first pass

      allocate(l2g(nnodes0_file))

      do j = 1, nnodes0_file
        read(iu) l2g(j)
      end do

! Allocate a temp to load the residual array for desired time step, skipping
! straight to the time plane of interest for our current rank

      allocate(temp(5,nnodes0_file))

! Remember data is stored backwards in time

      indx = ntimesteps-timestep+1

#ifdef HAVE_OPEN_STREAM
      read(iu,pos=ptr(indx)) temp(:,:)
#else
      temp = 0.0_dp ! silence compiler
#endif

      close(iu)

! Allocate global res array if on first pass

      if ( i == 1 ) allocate(time_data%drdd(5,nnodes))

! Put temp entries into global res array and clean up for next pass

      do j = 1, nnodes0_file
        time_data%drdd(:,l2g(j)) = temp(:,j)
      end do

      deallocate(l2g,temp)

    end do spatial_partitions

    deallocate(ptr)

  end subroutine read_drdd


!==================================== READ_DRDQT =============================80
!
!   Reads the transposed jacobian matrix and also forms an untransposed copy
!
!=============================================================================80
  subroutine read_drdqt(timestep, time_data)

    use string_utils,      only : int_to_s
    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use chaos_datas,       only : nnodes, time_data_type, nparts, ntimesteps,  &
                                  jacobian_type
    use lmpi,              only : lmpi_id, lmpi_die
    use allocations,       only : my_alloc_ptr, my_realloc_ptr
    use kinddefs,          only : i8

    integer, intent(in) :: timestep

    type(time_data_type), intent(inout) :: time_data

    integer :: iu, j, i, ntimesteps_file, nnodes01_file, nnodesg_file, indx, k
    integer :: lnode, gnode, counter, cand_col, old_col, dim1, n, row, m, l

    integer, parameter :: max_blocks = 100

    integer,     dimension(:), allocatable :: l2g
    integer(i8), dimension(:), allocatable :: ptr

    character(len=1000) :: filename

    type(jacobian_type) :: jac_row

    logical :: found_it

  continue

! Loop over all of the spatial partitions and grab the data for the time plane
! requested

    allocate(ptr(ntimesteps))

    jac_row%n = 0
    allocate(jac_row%list(max_blocks))
    allocate(jac_row%blocks(5,5,max_blocks))

    spatial_partitions : do i = 1, nparts

! First open up the metadata file and grab the file pointers for each timestep

      filename = 'drdqt.metadata.' // trim(int_to_s(i))

      call read_metadata(.false.,filename,ptr)

! Now read the big data file

      iu = available_unit()
      filename = 'drdqt.data.' // trim(int_to_s(i))
      call se_open(iu,file=trim(filename),form='unformatted',status='unknown', &
                   access='stream')

      read(iu) ntimesteps_file, nnodes01_file, nnodesg_file

      if ( nnodesg_file /= nnodes ) then
        write(*,*) 'Error: drdqt file contains inconsistent nnodes:', lmpi_id, &
                   nnodesg_file, nnodes
        call lmpi_die
        stop
      endif

      if ( ntimesteps_file /= ntimesteps ) then
        write(*,*) 'Error: drdqt file contains incorrect no of time steps.',   &
                   lmpi_id, ntimesteps_file
        call lmpi_die
        stop
      endif

! Load in this partition l2g and allocate/load global vol array if on first pass

      allocate(l2g(nnodes01_file))

      do j = 1, nnodes01_file
        read(iu) l2g(j)
      end do

! Skip straight to the time plane of interest for our current rank
! Remember data is stored backwards in time

      if ( i == 1 ) then
        allocate(time_data%drdqt(nnodes))
        do j = 1, nnodes
          time_data%drdqt(j)%n = 0
        end do
      endif

! Load the first row of data at the position indicated by the pointer, then
! everything else at the current file position

      indx = ntimesteps-timestep+1

      rows_in_file : do lnode = 1, nnodes01_file

        if ( lnode == 1 ) then
#ifdef HAVE_OPEN_STREAM
          read(iu,pos=ptr(indx)) jac_row%n
#else
          jac_row%n = 0 ! silence compiler
#endif
        else
          read(iu) jac_row%n
        endif

        if ( jac_row%n > max_blocks ) then
          write(*,*) 'Error: more blocks than max_blocks.', jac_row%n
          call lmpi_die
          stop
        endif

        do k = 1, jac_row%n
          read(iu) jac_row%list(k)
          read(iu) jac_row%blocks(:,:,k)
        end do

        gnode = l2g(lnode)

! Either we already have some blocks in this row or we don't

        empty_row : if ( associated(time_data%drdqt(gnode)%list) ) then

! Already have some blocks for this row - check to see how many new blocks
! need to be added to it.  For entries that are already bookkept, add new
! contributions to them

          counter = 0

          do k = 1, jac_row%n

            cand_col = jac_row%list(k)
            found_it = .false.
            search1 : do n = 1, time_data%drdqt(gnode)%n
              old_col = time_data%drdqt(gnode)%list(n)
              if ( cand_col == old_col ) then
                found_it = .true.
                exit search1
              endif
            end do search1

            if ( found_it ) then
              time_data%drdqt(gnode)%blocks(:,:,n) =                           &
              time_data%drdqt(gnode)%blocks(:,:,n) + jac_row%blocks(:,:,k)
            else
              counter = counter + 1
            endif

          end do

! Increase the allocation of blocks by counter and add them

          add_new_blocks : if ( counter > 0 ) then

            dim1 = time_data%drdqt(gnode)%n
            call my_realloc_ptr(time_data%drdqt(gnode)%list,   dim1+counter)
            call my_realloc_ptr(time_data%drdqt(gnode)%blocks, 5,5,dim1+counter)

            do k = 1, jac_row%n
              cand_col = jac_row%list(k)
              found_it = .false.
              search2 : do n = 1, time_data%drdqt(gnode)%n
                old_col = time_data%drdqt(gnode)%list(n)
                if ( cand_col == old_col ) then
                  found_it = .true.
                  exit search2
                endif
              end do search2
              if ( .not. found_it ) then
                time_data%drdqt(gnode)%n = time_data%drdqt(gnode)%n + 1
                time_data%drdqt(gnode)%list(time_data%drdqt(gnode)%n) =        &
                                                                 jac_row%list(k)
                time_data%drdqt(gnode)%blocks(:,:,time_data%drdqt(gnode)%n) =  &
                                                           jac_row%blocks(:,:,k)
              endif
            end do

          endif add_new_blocks

        else empty_row

! No blocks for this row yet - allocate and fill

          call my_alloc_ptr(time_data%drdqt(gnode)%list,   jac_row%n)
          call my_alloc_ptr(time_data%drdqt(gnode)%blocks, 5,5,jac_row%n)

          do k = 1, jac_row%n
            time_data%drdqt(gnode)%n = time_data%drdqt(gnode)%n + 1
            time_data%drdqt(gnode)%list(time_data%drdqt(gnode)%n) =            &
                                                                 jac_row%list(k)
            time_data%drdqt(gnode)%blocks(:,:,time_data%drdqt(gnode)%n) =      &
                                                           jac_row%blocks(:,:,k)
          end do

        endif empty_row

      end do rows_in_file

      close(iu)

      deallocate(l2g)

    end do spatial_partitions

    deallocate(ptr,jac_row%list,jac_row%blocks)

! Set the primal jacobian too

    allocate(time_data%drdq(nnodes))
    do j = 1, nnodes
      time_data%drdq(j)%n = time_data%drdqt(j)%n
      allocate(time_data%drdq(j)%list(time_data%drdq(j)%n))
      allocate(time_data%drdq(j)%blocks(5,5,time_data%drdq(j)%n))
      time_data%drdq(j)%n = 0
    end do

    do j = 1, nnodes
      do k = 1, time_data%drdqt(j)%n
        row = time_data%drdqt(j)%list(k)
        time_data%drdq(row)%n = time_data%drdq(row)%n + 1
        time_data%drdq(row)%list(time_data%drdq(row)%n) = j
        do m = 1, 5
          do l = 1, 5
            time_data%drdq(row)%blocks(m,l,time_data%drdq(row)%n) =            &
            time_data%drdqt(j)%blocks(l,m,k)
          end do
        end do
      end do
    end do

! Check that everyone wound up with the right number of blocks

    do j = 1, nnodes
      if ( time_data%drdq(j)%n /= time_data%drdqt(j)%n ) then
        write(*,*) 'Error: primal jac ended up with wrong number of entries.', &
                   lmpi_id, time_data%drdq(j)%n, time_data%drdqt(j)%n
        call lmpi_die
        stop
      endif
    end do

  end subroutine read_drdqt

end module chaos_readers

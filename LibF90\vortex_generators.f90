module vortex_generators

  use nml_vortex_generator, only : nvg, vg

  implicit none

  private

  public :: setup_vgs, vgs_rhs, vgs_lhs

  logical :: init = .false.

  logical, dimension(:), pointer :: boundary_point

contains

!======================== SETUP_VGS ==========================================80
!
!   Set up data structures required for user-defined vortex generator
!   source terms
!
!                 3 _____________base b2___________ 4
!                  /                               \             ^
!                 /                                 \            |
!                /                                   \           |
!               /                                     \          |  h
!              /                                       \         |
!             /                                         \        |
!            /______________________x____________________\       |
!           1                       5                     2
!                                base b1
!
!=============================================================================80
  subroutine setup_vgs(grid)

    use grid_types,          only : grid_type
    use kinddefs,            only : dp
    use projection_distance, only : tria_dist
    use lmpi,                only : lmpi_bcast, lmpi_max_and_maxid, lmpi_die,  &
                                    lmpi_max, lmpi_master, lmpi_id, lmpi_nproc,&
                                    lmpi_reduce
    use system_extensions,   only : se_open
    use file_utils,          only : available_unit
    use ivals,               only : u0, v0, w0
    use geometry_utils,      only : is_inside_triangle
    use allocations,         only : my_alloc_ptr, my_realloc_ptr

    type(grid_type), intent(in) :: grid

    integer :: i, ib, n1, n2, n3, n4, save_tria, save_quad, itria, iquad, ipass
    integer :: save_tria_max, save_quad_max, proc_with_min, ln1, ln2, ln3, ln4
    integer :: f, n, node1, node2, global_npair, offset, j, npass

    integer, dimension(:), pointer :: npair_on_proc, temp

    real(dp) :: x0, y0, z0, x1, x2, x3, x4, y1, y2, y3, y4, z1, z2, z3, z4
    real(dp) :: dist, min_dist, xint, yint, zint, a, b, xnorm, ynorm, znorm
    real(dp) :: x_proj, y_proj, z_proj, b1, b2, xnorm_avg, ynorm_avg, znorm_avg
    real(dp) :: mag, vx, vy, vz, mid_x, mid_y, mid_z, p3x, p3y, p3z
    real(dp) :: p4x, p4y, p4z, test, xavg, yavg, zavg, length
    real(dp) :: vector1_x, vector1_y, vector1_z, ux_norm, uy_norm, uz_norm
    real(dp) :: vector2_x, vector2_y, vector2_z
    real(dp) :: normal_x, normal_y, normal_z, c, d
    real(dp) :: xe1, xe2, ye1, ye2, ze1, ze2, ux, uy, uz, s_int
    real(dp) :: tempvol, volume_sum, cx, cy, cz, dx, dy, dz, ex, ey, ez

    real(dp), dimension(3) :: tvec, bvec, nvec, ptest, p1, p2, p3
    real(dp), dimension(:), pointer :: global_xint
    real(dp), dimension(:), pointer :: global_yint
    real(dp), dimension(:), pointer :: global_zint
    real(dp), dimension(:), pointer :: tempval

    logical :: found, candidate

    logical, dimension(grid%nedgeloc) :: edge_included

    character(len=120) :: filename

  continue

    ib = 0

    if ( lmpi_master ) then
      write(*,*)
      write(*,'(a,i0,a)') ' Setting up ', nvg, ' vortex generators...'
    endif

    vg_loop1 : do i = 1, nvg

! Determine the projected locations of points 1 and 2 on the intended
! boundary patch and the outward-pointing normals at each of these
! two points

! Do two passes, one for each of the two points

      pass_loop1 : do ipass = 1, 2

        if ( ipass == 1 ) then
          ib = vg(i)%boundary_patch1
          x0 = vg(i)%p1_x_input
          y0 = vg(i)%p1_y_input
          z0 = vg(i)%p1_z_input
        else if ( ipass == 2 ) then
          ib = vg(i)%boundary_patch2
          x0 = vg(i)%p2_x_input
          y0 = vg(i)%p2_y_input
          z0 = vg(i)%p2_z_input
        endif

        min_dist = huge(1.0_dp)

        save_tria = 0
        save_quad = 0

        tri_loop : do itria = 1, grid%bc(ib)%nbfacet

          ln1 = grid%bc(ib)%f2ntb(itria,1)
          ln2 = grid%bc(ib)%f2ntb(itria,2)
          ln3 = grid%bc(ib)%f2ntb(itria,3)

          n1 = grid%bc(ib)%ibnode(ln1)
          n2 = grid%bc(ib)%ibnode(ln2)
          n3 = grid%bc(ib)%ibnode(ln3)

          x1 = grid%x(n1); y1 = grid%y(n1); z1 = grid%z(n1)
          x2 = grid%x(n2); y2 = grid%y(n2); z2 = grid%z(n2)
          x3 = grid%x(n3); y3 = grid%y(n3); z3 = grid%z(n3)

          call tria_dist( x0, y0, z0, x1, y1, z1, x2, y2, z2, x3, y3, z3,      &
                          found, dist, xint, yint, zint, a, b )

          if ( found ) then
            dist = sqrt(dist)
            if ( dist <= min_dist ) then
              min_dist  = dist
              x_proj    = xint
              y_proj    = yint
              z_proj    = zint
              save_tria = itria
              xnorm = (1.0_dp-a-b)*grid%bc(ib)%bxn(ln1)                        &
                               + a*grid%bc(ib)%bxn(ln2)                        &
                               + b*grid%bc(ib)%bxn(ln3)
              ynorm = (1.0_dp-a-b)*grid%bc(ib)%byn(ln1)                        &
                               + a*grid%bc(ib)%byn(ln2)                        &
                               + b*grid%bc(ib)%byn(ln3)
              znorm = (1.0_dp-a-b)*grid%bc(ib)%bzn(ln1)                        &
                               + a*grid%bc(ib)%bzn(ln2)                        &
                               + b*grid%bc(ib)%bzn(ln3)
            endif
          end if

        end do tri_loop

        quad_loop : do iquad = 1, grid%bc(ib)%nbfaceq

          ln1 = grid%bc(ib)%f2nqb(iquad,1)
          ln2 = grid%bc(ib)%f2nqb(iquad,2)
          ln3 = grid%bc(ib)%f2nqb(iquad,3)
          ln4 = grid%bc(ib)%f2nqb(iquad,4)

          n1 = grid%bc(ib)%ibnode(ln1)
          n2 = grid%bc(ib)%ibnode(ln2)
          n3 = grid%bc(ib)%ibnode(ln3)
          n4 = grid%bc(ib)%ibnode(ln4)

          x1 = grid%x(n1); y1 = grid%y(n1); z1 = grid%z(n1)
          x2 = grid%x(n2); y2 = grid%y(n2); z2 = grid%z(n2)
          x3 = grid%x(n3); y3 = grid%y(n3); z3 = grid%z(n3)
          x4 = grid%x(n4); y4 = grid%y(n4); z4 = grid%z(n4)

! tria 124

          call tria_dist( x0, y0, z0, x1, y1, z1, x2, y2, z2, x4, y4, z4,      &
                          found, dist, xint, yint, zint, a, b )

          if ( found ) then
            dist = sqrt(dist)
            if ( dist <= min_dist ) then
              min_dist  = dist
              x_proj    = xint
              y_proj    = yint
              z_proj    = zint
              save_quad = iquad
              xnorm = (1.0_dp-a-b)*grid%bc(ib)%bxn(ln1)                        &
                               + a*grid%bc(ib)%bxn(ln2)                        &
                               + b*grid%bc(ib)%bxn(ln4)
              ynorm = (1.0_dp-a-b)*grid%bc(ib)%byn(ln1)                        &
                               + a*grid%bc(ib)%byn(ln2)                        &
                               + b*grid%bc(ib)%byn(ln4)
              znorm = (1.0_dp-a-b)*grid%bc(ib)%bzn(ln1)                        &
                               + a*grid%bc(ib)%bzn(ln2)                        &
                               + b*grid%bc(ib)%bzn(ln4)
            endif
          end if

! tria 234

          call tria_dist( x0, y0, z0, x2, y2, z2, x3, y3, z3, x4, y4, z4,      &
                          found, dist, xint, yint, zint, a, b )

          if ( found ) then
            dist = sqrt(dist)
            if ( dist <= min_dist ) then
              min_dist  = dist
              x_proj    = xint
              y_proj    = yint
              z_proj    = zint
              save_quad = iquad
              xnorm = (1.0_dp-a-b)*grid%bc(ib)%bxn(ln2)                        &
                               + a*grid%bc(ib)%bxn(ln3)                        &
                               + b*grid%bc(ib)%bxn(ln4)
              ynorm = (1.0_dp-a-b)*grid%bc(ib)%byn(ln2)                        &
                               + a*grid%bc(ib)%byn(ln3)                        &
                               + b*grid%bc(ib)%byn(ln4)
              znorm = (1.0_dp-a-b)*grid%bc(ib)%bzn(ln2)                        &
                               + a*grid%bc(ib)%bzn(ln3)                        &
                               + b*grid%bc(ib)%bzn(ln4)
            endif
          end if

! tria 123

          call tria_dist( x0, y0, z0, x1, y1, z1, x2, y2, z2, x3, y3, z3,      &
                          found, dist, xint, yint, zint, a, b )

          if ( found ) then
            dist = sqrt(dist)
            if ( dist <= min_dist ) then
              min_dist  = dist
              x_proj    = xint
              y_proj    = yint
              z_proj    = zint
              save_quad = iquad
              xnorm = (1.0_dp-a-b)*grid%bc(ib)%bxn(ln1)                        &
                               + a*grid%bc(ib)%bxn(ln2)                        &
                               + b*grid%bc(ib)%bxn(ln3)
              ynorm = (1.0_dp-a-b)*grid%bc(ib)%byn(ln1)                        &
                               + a*grid%bc(ib)%byn(ln2)                        &
                               + b*grid%bc(ib)%byn(ln3)
              znorm = (1.0_dp-a-b)*grid%bc(ib)%bzn(ln1)                        &
                               + a*grid%bc(ib)%bzn(ln2)                        &
                               + b*grid%bc(ib)%bzn(ln3)
            endif
          end if

! tria 134

          call tria_dist( x0, y0, z0, x1, y1, z1, x3, y3, z3, x4, y4, z4,      &
                          found, dist, xint, yint, zint, a, b )

          if ( found ) then
            dist = sqrt(dist)
            if ( dist <= min_dist ) then
              min_dist  = dist
              x_proj    = xint
              y_proj    = yint
              z_proj    = zint
              save_quad = iquad
              save_tria = 0  ! zero back out since we found a better quad
              xnorm = (1.0_dp-a-b)*grid%bc(ib)%bxn(ln1)                        &
                               + a*grid%bc(ib)%bxn(ln3)                        &
                               + b*grid%bc(ib)%bxn(ln4)
              ynorm = (1.0_dp-a-b)*grid%bc(ib)%byn(ln1)                        &
                               + a*grid%bc(ib)%byn(ln3)                        &
                               + b*grid%bc(ib)%byn(ln4)
              znorm = (1.0_dp-a-b)*grid%bc(ib)%bzn(ln1)                        &
                               + a*grid%bc(ib)%bzn(ln3)                        &
                               + b*grid%bc(ib)%bzn(ln4)
            endif
          end if

        end do quad_loop

! Now reduce results across processors

        call lmpi_max(save_tria, save_tria_max)
        call lmpi_bcast(save_tria_max)
        call lmpi_max(save_quad, save_quad_max)
        call lmpi_bcast(save_quad_max)

        if ( save_tria_max == 0 .and. save_quad_max == 0 ) then
          write(*,*) 'Error: could not find face to project point to for VG ', &
                     ipass, i
          call lmpi_die
          stop
        endif

! Temporarily switch sign so we can use max function

        min_dist = -min_dist
        call lmpi_max_and_maxid(real(min_dist,dp), proc_with_min)

! Switch sign back

        min_dist = -min_dist

! Store off final location of point 1

        call lmpi_bcast(x_proj,proc_with_min)
        call lmpi_bcast(y_proj,proc_with_min)
        call lmpi_bcast(z_proj,proc_with_min)

        call lmpi_bcast(xnorm,proc_with_min)
        call lmpi_bcast(ynorm,proc_with_min)
        call lmpi_bcast(znorm,proc_with_min)

        if ( ipass == 1 ) then
          vg(i)%p1_x     = x_proj
          vg(i)%p1_y     = y_proj
          vg(i)%p1_z     = z_proj
          vg(i)%p1_xnorm = xnorm
          vg(i)%p1_ynorm = ynorm
          vg(i)%p1_znorm = znorm
        else if ( ipass == 2 ) then
          vg(i)%p2_x     = x_proj
          vg(i)%p2_y     = y_proj
          vg(i)%p2_z     = z_proj
          vg(i)%p2_xnorm = xnorm
          vg(i)%p2_ynorm = ynorm
          vg(i)%p2_znorm = znorm
        endif

      end do pass_loop1

! Next we will compute the vectors b, t, and n, then optionally reverse
! t and/or n if needed.

! If we provided planform area and height for the VG, then we will also
! determine the location of points 3, 4, and 5.

! For a triangle VG input, we will get point 3 from the user (don't care
! about 4 and 5).  We will also need to compute the VG planform area in
! this case.

! unit vector normal to base
! minus sign is to point into the domain

      xnorm_avg = 0.5_dp * ( vg(i)%p1_xnorm + vg(i)%p2_xnorm )
      ynorm_avg = 0.5_dp * ( vg(i)%p1_ynorm + vg(i)%p2_ynorm )
      znorm_avg = 0.5_dp * ( vg(i)%p1_znorm + vg(i)%p2_znorm )

      mag = sqrt( xnorm_avg**2 + ynorm_avg**2 + znorm_avg**2 )

      xnorm_avg = - xnorm_avg / mag
      ynorm_avg = - ynorm_avg / mag
      znorm_avg = - znorm_avg / mag

! Save off as unit vector b

      vg(i)%bx = xnorm_avg
      vg(i)%by = ynorm_avg
      vg(i)%bz = znorm_avg

! unit vector from p1 to p2

      vx = vg(i)%p2_x - vg(i)%p1_x
      vy = vg(i)%p2_y - vg(i)%p1_y
      vz = vg(i)%p2_z - vg(i)%p1_z

      mag = sqrt( vx**2 + vy**2 + vz**2 )

      vx = vx / mag
      vy = vy / mag
      vz = vz / mag

! This is the unit vector t

      vg(i)%tx = vx
      vg(i)%ty = vy
      vg(i)%tz = vz

! Try to align t with freestream direction

      test = u0*vg(i)%tx + v0*vg(i)%ty + w0*vg(i)%tz

      if ( test < 0.0_dp ) then
        vg(i)%tx = -vg(i)%tx
        vg(i)%ty = -vg(i)%ty
        vg(i)%tz = -vg(i)%tz
      endif

! Now n is t cross b

      tvec(1) = vg(i)%tx
      tvec(2) = vg(i)%ty
      tvec(3) = vg(i)%tz

      bvec(1) = vg(i)%bx
      bvec(2) = vg(i)%by
      bvec(3) = vg(i)%bz

      nvec = cross_product(tvec,bvec)

      vg(i)%nx = nvec(1)
      vg(i)%ny = nvec(2)
      vg(i)%nz = nvec(3)

! Try to align n with freestream direction

      test = u0*vg(i)%nx + v0*vg(i)%ny + w0*vg(i)%nz

      if ( test < 0.0_dp ) then
        vg(i)%nx = -vg(i)%nx
        vg(i)%ny = -vg(i)%ny
        vg(i)%nz = -vg(i)%nz
      endif

      find_345 : if ( trim(vg(i)%configuration) == 'area_and_height' ) then

! Compute the length of the base

        b1 = sqrt( (vg(i)%p2_x - vg(i)%p1_x)**2                                &
                 + (vg(i)%p2_y - vg(i)%p1_y)**2                                &
                 + (vg(i)%p2_z - vg(i)%p1_z)**2 )

! Since area = 0.5*(b1 + b2) * h, the length of the top base of the trapezoid is

        b2 = 2.0_dp * vg(i)%planform_area / vg(i)%height - b1

        if ( b2 <= 0.0_dp ) then
          write(*,*)'Error: combination of planform area,height,and base yields'
          write(*,*)'a negative width at the top of VG ', i
          call lmpi_die
          stop
        endif

! midpoint of b1

        mid_x = 0.5_dp * (vg(i)%p1_x + vg(i)%p2_x)
        mid_y = 0.5_dp * (vg(i)%p1_y + vg(i)%p2_y)
        mid_z = 0.5_dp * (vg(i)%p1_z + vg(i)%p2_z)

! store this off as point 5

        vg(i)%p5_x = mid_x
        vg(i)%p5_y = mid_y
        vg(i)%p5_z = mid_z

! now move along b1 in both directions by the distance b2/2

        p3x = mid_x - 0.5_dp*b2 * vx
        p3y = mid_y - 0.5_dp*b2 * vy
        p3z = mid_z - 0.5_dp*b2 * vz

        p4x = mid_x + 0.5_dp*b2 * vx
        p4y = mid_y + 0.5_dp*b2 * vy
        p4z = mid_z + 0.5_dp*b2 * vz

! now move each of these points along the normal direction by the height
! to establish the final locations of p3 and p4

        vg(i)%p3_x = p3x + vg(i)%height*xnorm_avg
        vg(i)%p3_y = p3y + vg(i)%height*ynorm_avg
        vg(i)%p3_z = p3z + vg(i)%height*znorm_avg

        vg(i)%p4_x = p4x + vg(i)%height*xnorm_avg
        vg(i)%p4_y = p4y + vg(i)%height*ynorm_avg
        vg(i)%p4_z = p4z + vg(i)%height*znorm_avg

      endif find_345

      find_3 : if ( trim(vg(i)%configuration) == 'three_points' ) then

        vg(i)%p3_x = vg(i)%p3_x_input
        vg(i)%p3_y = vg(i)%p3_y_input
        vg(i)%p3_z = vg(i)%p3_z_input

! Vector from 3 to 1

        cx = vg(i)%p1_x - vg(i)%p3_x
        cy = vg(i)%p1_y - vg(i)%p3_y
        cz = vg(i)%p1_z - vg(i)%p3_z

! Vector from 3 to 2

        dx = vg(i)%p2_x - vg(i)%p3_x
        dy = vg(i)%p2_y - vg(i)%p3_y
        dz = vg(i)%p2_z - vg(i)%p3_z

! Form cross product

        ex = cy*dz - dy*cz
        ey = dx*cz - cx*dz
        ez = cx*dy - dx*cy

        mag = sqrt(ex*ex + ey*ey + ez*ez)

        vg(i)%planform_area = 0.5_dp * mag

      endif find_3

! Finally reverse the computed directions of the t and/or n vectors as desired

      if ( vg(i)%reverse_t ) then
        vg(i)%tx = -vg(i)%tx
        vg(i)%ty = -vg(i)%ty
        vg(i)%tz = -vg(i)%tz
      endif

      if ( vg(i)%reverse_n ) then
        vg(i)%nx = -vg(i)%nx
        vg(i)%ny = -vg(i)%ny
        vg(i)%nz = -vg(i)%nz
      endif

    end do vg_loop1

! Now write out the VG geometries for plotting

    plot_em : if ( lmpi_master ) then

      f = available_unit()
      filename = trim(grid%project) // '_vg_geometry.dat'
      call se_open(f,file=trim(filename))
      rewind(f)
      write(f,*) 'Title="User-Defined Vortex Generators"'
      write(f,*) 'variables = x y z'
      do i = 1, nvg
        write(f,'(a,i0,a)') 'zone t=vg', i, ', i=4, j=1, f=feblock'
        select case(trim(vg(i)%configuration))
        case('area_and_height')
          write(f,'(4(e25.15,1x))') vg(i)%p1_x,vg(i)%p2_x,vg(i)%p3_x,vg(i)%p4_x
          write(f,'(4(e25.15,1x))') vg(i)%p1_y,vg(i)%p2_y,vg(i)%p3_y,vg(i)%p4_y
          write(f,'(4(e25.15,1x))') vg(i)%p1_z,vg(i)%p2_z,vg(i)%p3_z,vg(i)%p4_z
        case('three_points')
          write(f,'(4(e25.15,1x))') vg(i)%p1_x,vg(i)%p2_x,vg(i)%p3_x,vg(i)%p3_x
          write(f,'(4(e25.15,1x))') vg(i)%p1_y,vg(i)%p2_y,vg(i)%p3_y,vg(i)%p3_y
          write(f,'(4(e25.15,1x))') vg(i)%p1_z,vg(i)%p2_z,vg(i)%p3_z,vg(i)%p3_z
        case default
          write(*,*) 'Unknown VG configuration: ', trim(vg(i)%configuration)
          call lmpi_die
          stop
        end select
        write(f,*) '1 2 4 3'
      end do
      close(f)

      write(*,'(a,a,a)') ' Wrote ', trim(filename), '...'

      f = available_unit()
      filename = trim(grid%project) // '_vg_vectors.dat'
      call se_open(f,file=trim(filename))
      rewind(f)
      write(f,*) 'TEXT CS=FRAME, X=10, Y=10, Z=0, C=BLUE  T="Unit vector n"'
      write(f,*) 'TEXT CS=FRAME, X=10, Y=20, Z=0, C=RED   T="Unit vector b"'
      write(f,*) 'TEXT CS=FRAME, X=10, Y=30, Z=0, C=GREEN T="Unit vector t"'
      do i = 1, nvg

        select case(trim(vg(i)%configuration))
        case('area_and_height')
          xavg = 0.25_dp*(vg(i)%p1_x+vg(i)%p2_x+vg(i)%p3_x+vg(i)%p4_x)
          yavg = 0.25_dp*(vg(i)%p1_y+vg(i)%p2_y+vg(i)%p3_y+vg(i)%p4_y)
          zavg = 0.25_dp*(vg(i)%p1_z+vg(i)%p2_z+vg(i)%p3_z+vg(i)%p4_z)
        case('three_points')
          xavg = (vg(i)%p1_x+vg(i)%p2_x+vg(i)%p3_x)/3.0_dp
          yavg = (vg(i)%p1_y+vg(i)%p2_y+vg(i)%p3_y)/3.0_dp
          zavg = (vg(i)%p1_z+vg(i)%p2_z+vg(i)%p3_z)/3.0_dp
        case default
          write(*,*) 'Unknown VG configuration: ', trim(vg(i)%configuration)
          call lmpi_die
          stop
        end select

        length = 0.5_dp * sqrt ( (vg(i)%p2_x-vg(i)%p1_x)**2                    &
                               + (vg(i)%p2_y-vg(i)%p1_y)**2                    &
                               + (vg(i)%p2_z-vg(i)%p1_z)**2 )

        write(f,*) 'GEOMETRY T=LINE3D C=BLUE LT=0.4'
        write(f,*) '1'
        write(f,*) '2'
        write(f,'(3(e25.15,1x))') xavg, yavg, zavg
        write(f,'(3(e25.15,1x))') xavg+length*vg(i)%nx,                        &
                                  yavg+length*vg(i)%ny,                        &
                                  zavg+length*vg(i)%nz

        write(f,*) 'GEOMETRY T=LINE3D C=RED LT=0.4'
        write(f,*) '1'
        write(f,*) '2'
        write(f,'(3(e25.15,1x))') xavg, yavg, zavg
        write(f,'(3(e25.15,1x))') xavg+length*vg(i)%bx,                        &
                                  yavg+length*vg(i)%by,                        &
                                  zavg+length*vg(i)%bz

        write(f,*) 'GEOMETRY T=LINE3D C=GREEN LT=0.4'
        write(f,*) '1'
        write(f,*) '2'
        write(f,'(3(e25.15,1x))') xavg, yavg, zavg
        write(f,'(3(e25.15,1x))') xavg+length*vg(i)%tx,                        &
                                  yavg+length*vg(i)%ty,                        &
                                  zavg+length*vg(i)%tz

      end do
      close(f)

      write(*,'(a,a,a)') ' Wrote ', trim(filename), '...'

    endif plot_em

! Now the next step is to find the points where any edges cross each VG

    vg_loop2 : do i = 1, nvg

      vg(i)%npair = 0
      call my_alloc_ptr(vg(i)%node_pair,2,1)
      call my_alloc_ptr(vg(i)%weights,  2,1)

      volume_sum = 0.0_dp

! Depending on the VG configuration, loop over the triangles that form
! the plane of the VG:
!  configuration = 'area_and_height':
!   Triangle 1 contains points 1-3-5
!   Triangle 2 contains points 3-4-5
!   Triangle 3 contains points 2-4-5
!  configuration = 'three_points':
!   Triangle 1 contains points 1-2-3

      select case(trim(vg(i)%configuration))
      case('area_and_height')
        npass = 3
      case('three_points')
        npass = 1
      case default
        write(*,*) 'Unknown VG configuration: ', trim(vg(i)%configuration)
        call lmpi_die
        stop
      end select

      edge_included(:) = .false.

      pass_loop2 : do ipass = 1, npass

        select case(ipass)
        case(1)
          select case(trim(vg(i)%configuration))
          case('area_and_height')
            x1 = vg(i)%p1_x; x2 = vg(i)%p5_x; x3 = vg(i)%p3_x
            y1 = vg(i)%p1_y; y2 = vg(i)%p5_y; y3 = vg(i)%p3_y
            z1 = vg(i)%p1_z; z2 = vg(i)%p5_z; z3 = vg(i)%p3_z
          case('three_points')
            x1 = vg(i)%p1_x; x2 = vg(i)%p2_x; x3 = vg(i)%p3_x
            y1 = vg(i)%p1_y; y2 = vg(i)%p2_y; y3 = vg(i)%p3_y
            z1 = vg(i)%p1_z; z2 = vg(i)%p2_z; z3 = vg(i)%p3_z
          end select
        case(2)
          x1 = vg(i)%p3_x; x2 = vg(i)%p5_x; x3 = vg(i)%p4_x
          y1 = vg(i)%p3_y; y2 = vg(i)%p5_y; y3 = vg(i)%p4_y
          z1 = vg(i)%p3_z; z2 = vg(i)%p5_z; z3 = vg(i)%p4_z
        case(3)
          x1 = vg(i)%p2_x; x2 = vg(i)%p4_x; x3 = vg(i)%p5_x
          y1 = vg(i)%p2_y; y2 = vg(i)%p4_y; y3 = vg(i)%p5_y
          z1 = vg(i)%p2_z; z2 = vg(i)%p4_z; z3 = vg(i)%p5_z
        end select

! Form normal to plane of triangle

        vector1_x = x2 - x1
        vector1_y = y2 - y1
        vector1_z = z2 - z1

        vector2_x = x3 - x1
        vector2_y = y3 - y1
        vector2_z = z3 - z1

        normal_x = vector1_y*vector2_z - vector2_y*vector1_z
        normal_y = vector2_x*vector1_z - vector1_x*vector2_z
        normal_z = vector1_x*vector2_y - vector2_x*vector1_y

        mag = sqrt(normal_x**2 + normal_y**2 + normal_z**2)

        normal_x = normal_x / mag
        normal_y = normal_y / mag
        normal_z = normal_z / mag

! coefficients of plane equation Ax + By + Cz + D = 0
! are as follows

        a = normal_x
        b = normal_y
        c = normal_z
        d = - ( a*x1 + b*y1 + c*z1 )

! Loop through all edges in mesh looking for intersections with current VG

        edge_loop : do n = 1, grid%nedgeloc

          if ( edge_included(n) ) cycle edge_loop

          node1 = grid%eptr(1,n)
          node2 = grid%eptr(2,n)

          xe1 = grid%x(node1); ye1 = grid%y(node1); ze1 = grid%z(node1)
          xe2 = grid%x(node2); ye2 = grid%y(node2); ze2 = grid%z(node2)

! Find parametric equation of line segment formed by edge:
! P = P1 + s*u  where s is parameter between 0 and 1

          ux = xe2 - xe1
          uy = ye2 - ye1
          uz = ze2 - ze1

! First check if line segment is parallel to plane by testing dot product
! of normal with u

          mag = sqrt(ux**2 + uy**2 + uz**2)

          ux_norm = ux / mag
          uy_norm = uy / mag
          uz_norm = uz / mag

          test = normal_x*ux_norm + normal_y*uy_norm + normal_z*uz_norm

! So the line and the plane intersect somewhere.  Solve for the value of the
! s parameter where they intersect

          s_int = -(a*xe1 + b*ye1 + c*ze1 + d) /                               &
                  (normal_x*ux + normal_y*uy + normal_z*uz)

! If this value is outside the range 0 to 1, then there is no intersection

          if ( s_int <= 0.0_dp .or. s_int >= 1.0_dp ) cycle edge_loop

! So this is a valid intersection

          xint = xe1 + s_int*ux
          yint = ye1 + s_int*uy
          zint = ze1 + s_int*uz

! Now we just have to determine if the intersection point is in the current
! triangle

          ptest(1) = xint
          ptest(2) = yint
          ptest(3) = zint

          p1(1) = x1
          p1(2) = y1
          p1(3) = z1

          p2(1) = x2
          p2(2) = y2
          p2(3) = z2

          p3(1) = x3
          p3(2) = y3
          p3(3) = z3

          candidate = is_inside_triangle(ptest,p1,p2,p3)

          if ( .not. candidate ) cycle edge_loop

! intersection is one of interest; store the nodes and the weights

          vg(i)%npair = vg(i)%npair + 1

          call my_realloc_ptr(vg(i)%node_pair,2,vg(i)%npair)
          call my_realloc_ptr(vg(i)%weights,  2,vg(i)%npair)

          vg(i)%node_pair(1,vg(i)%npair) = node1
          vg(i)%node_pair(2,vg(i)%npair) = node2

          vg(i)%weights(1,vg(i)%npair) = 1.0_dp - s_int
          vg(i)%weights(2,vg(i)%npair) = s_int

! keep a running sum of the total volume

          volume_sum=volume_sum + vg(i)%weights(1,vg(i)%npair)*grid%vol(node1) &
                                + vg(i)%weights(2,vg(i)%npair)*grid%vol(node2)

          edge_included(n) = .true.

        end do edge_loop

      end do pass_loop2

! Sum up the volume for the whole VG and store

      call lmpi_reduce(volume_sum,tempvol)
      call lmpi_bcast(tempvol)

      vg(i)%volume = tempvol

    end do vg_loop2

! Now plot the intersection points for visualization

    if ( lmpi_master ) then
      f = available_unit()
      filename = trim(grid%project) // '_vg_source_locations.dat'
      call se_open(f,file=trim(filename))
      rewind(f)
      write(f,*) 'Title="VG Source Locations"'
      write(f,*) 'variables = x y z'
    endif

    vg_loop3 : do i = 1, nvg

      call lmpi_reduce(vg(i)%npair,global_npair)
      call lmpi_bcast(global_npair)

      call my_alloc_ptr(npair_on_proc, lmpi_nproc)
      call my_alloc_ptr(temp,          lmpi_nproc)

      npair_on_proc(lmpi_id+1) = vg(i)%npair
      call lmpi_reduce(npair_on_proc,temp)
      call lmpi_bcast(temp)
      npair_on_proc = temp
      deallocate(temp)

      offset = 0
      do j = 1, lmpi_id
        offset = offset + npair_on_proc(j)
      end do

      deallocate(npair_on_proc)

      call my_alloc_ptr(global_xint,global_npair)
      call my_alloc_ptr(global_yint,global_npair)
      call my_alloc_ptr(global_zint,global_npair)
      call my_alloc_ptr(tempval,    global_npair)

      do j = 1, vg(i)%npair
        global_xint(j+offset) = vg(i)%weights(1,j)*grid%x(vg(i)%node_pair(1,j))&
                              + vg(i)%weights(2,j)*grid%x(vg(i)%node_pair(2,j))
        global_yint(j+offset) = vg(i)%weights(1,j)*grid%y(vg(i)%node_pair(1,j))&
                              + vg(i)%weights(2,j)*grid%y(vg(i)%node_pair(2,j))
        global_zint(j+offset) = vg(i)%weights(1,j)*grid%z(vg(i)%node_pair(1,j))&
                              + vg(i)%weights(2,j)*grid%z(vg(i)%node_pair(2,j))
      end do

      call lmpi_reduce(global_xint,tempval)
      call lmpi_bcast(tempval)
      global_xint = tempval

      call lmpi_reduce(global_yint,tempval)
      call lmpi_bcast(tempval)
      global_yint = tempval

      call lmpi_reduce(global_zint,tempval)
      call lmpi_bcast(tempval)
      global_zint = tempval

      deallocate(tempval)

      if ( lmpi_master ) then
        write(f,'(a,i0,a)') 'zone t=vg', i, '_sources'
        do j = 1, global_npair
          write(f,'(3(e25.15,1x))') global_xint(j),global_yint(j),global_zint(j)
        end do
      endif

      deallocate(global_xint,global_yint,global_zint)

    end do vg_loop3

    if ( lmpi_master ) then
      close(f)
      write(*,'(a,a,a)') ' Wrote ', trim(filename), '...'
      write(*,*)
    endif

  end subroutine setup_vgs


!================================= VGS_RHS ===================================80
!
!   Add the source term due to the vortex generators to the residual vector
!
!=============================================================================80
  subroutine vgs_rhs(grid,soln)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use kinddefs,       only : dp
    use allocations,    only : my_alloc_ptr
    use lmpi,           only : lmpi_die
    use lmpi_app,       only : lmpi_xfer
    use solution_types, only : compressible, incompressible

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln

    integer :: i, j, node1, node2, inode, ib

    real(dp) :: weight1, weight2, dvolume, total_vol, rho, u, v, w, mag_u
    real(dp) :: u_dot_n, u_dot_t, u_cross_bx, u_cross_by, u_cross_bz
    real(dp) :: coeff, xmom, ymom, zmom, energy

  continue

    rho = 1.0_dp

    if ( .not. init ) then
      call my_alloc_ptr(boundary_point,grid%nnodes01)
      boundary_point(:) = .false.
      do ib = 1, grid%nbound
        do j = 1, grid%bc(ib)%nbnode
          inode = grid%bc(ib)%ibnode(j)
          if ( inode <= grid%nnodes0 ) boundary_point(inode) = .true.
        end do
      end do
      call lmpi_xfer(boundary_point)
      init = .true.
    endif

    vg_loop : do i = 1, nvg

      cut_edges : do j = 1, vg(i)%npair

        node1 = vg(i)%node_pair(1,j)
        node2 = vg(i)%node_pair(2,j)

        if ( boundary_point(node1) .and. boundary_point(node2) ) cycle cut_edges

        weight1 = vg(i)%weights(1,j)
        weight2 = vg(i)%weights(2,j)

        dvolume   = weight1*grid%vol(node1) + weight2*grid%vol(node2)
        total_vol = vg(i)%volume

        select case(soln%eqn_set)
        case(compressible)
          rho = weight1*soln%q_dof(1,node1) + weight2*soln%q_dof(1,node2)
        case(incompressible)
          rho = 1.0_dp
        case default
          write(*,*) 'Error: eqn_set not known in vgs_rhs: ', soln%eqn_set
          call lmpi_die
          stop
        end select

        u = weight1*soln%q_dof(2,node1) + weight2*soln%q_dof(2,node2)
        v = weight1*soln%q_dof(3,node1) + weight2*soln%q_dof(3,node2)
        w = weight1*soln%q_dof(4,node1) + weight2*soln%q_dof(4,node2)

        mag_u = sqrt(u*u + v*v + w*w)

        u_dot_n = u*vg(i)%nx + v*vg(i)%ny + w*vg(i)%nz

        u_cross_bx = v*vg(i)%bz - w*vg(i)%by
        u_cross_by = w*vg(i)%bx - u*vg(i)%bz
        u_cross_bz = u*vg(i)%by - v*vg(i)%bx

        u_dot_t = u*vg(i)%tx + v*vg(i)%ty + w*vg(i)%tz

! Now lets form the scalar coefficient

        coeff = vg(i)%c * vg(i)%planform_area * dvolume / total_vol * rho      &
              * u_dot_n * u_dot_t / mag_u

! Form the momentum contributions, taking into account the additional volume
! factor that arises when we integrate over the volumes

        xmom = coeff*u_cross_bx*dvolume
        ymom = coeff*u_cross_by*dvolume
        zmom = coeff*u_cross_bz*dvolume

! energy contribution (volume factor is already built into the momentum pieces)

        energy = u*xmom + v*ymom + w*zmom

! Add weighted contributions back into the residual vector

        if ( node1 <= grid%nnodes0 .and. .not. boundary_point(node1) ) then
          soln%res(2,node1) = soln%res(2,node1) + weight1*xmom
          soln%res(3,node1) = soln%res(3,node1) + weight1*ymom
          soln%res(4,node1) = soln%res(4,node1) + weight1*zmom
          if ( soln%eqn_set == compressible ) then
            soln%res(5,node1) = soln%res(5,node1) + weight1*energy
          endif
        endif

        if ( node2 <= grid%nnodes0 .and. .not. boundary_point(node2) ) then
          soln%res(2,node2) = soln%res(2,node2) + weight2*xmom
          soln%res(3,node2) = soln%res(3,node2) + weight2*ymom
          soln%res(4,node2) = soln%res(4,node2) + weight2*zmom
          if ( soln%eqn_set == compressible ) then
            soln%res(5,node2) = soln%res(5,node2) + weight2*energy
          endif
        endif

      end do cut_edges

    end do vg_loop

  end subroutine vgs_rhs


!================================= VGS_LHS ===================================80
!
!   Add the source term linearizations due to the vortex generators to the
!   jacobian matrix
!
!=============================================================================80
  subroutine vgs_lhs(grid,soln,crow)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use kinddefs,       only : dp
    use allocations,    only : my_alloc_ptr
    use lmpi,           only : lmpi_die
    use lmpi_app,       only : lmpi_xfer
    use solution_types, only : compressible, incompressible
    use comprow_types,  only : crow_flow

    type(grid_type), intent(in)    :: grid
    type(crow_flow), intent(in)    :: crow
    type(soln_type), intent(inout) :: soln

    integer :: i, j, node1, node2, inode, ib, ioff, row, column, k

    real(dp) :: weight1, weight2, dvolume, total_vol, rho, u, v, w, mag_u
    real(dp) :: u_dot_n, u_dot_t, u_cross_bx, u_cross_by, u_cross_bz
    real(dp) :: coeff, xmom, ymom, zmom
    real(dp) :: rhoq11,rhoq12,rhoq13,rhoq14,rhoq15
    real(dp) :: rhoq21,rhoq22,rhoq23,rhoq24,rhoq25
    real(dp) :: uq11,uq12,uq13,uq14,uq15
    real(dp) :: uq21,uq22,uq23,uq24,uq25
    real(dp) :: vq11,vq12,vq13,vq14,vq15
    real(dp) :: vq21,vq22,vq23,vq24,vq25
    real(dp) :: wq11,wq12,wq13,wq14,wq15
    real(dp) :: wq21,wq22,wq23,wq24,wq25
    real(dp) :: mag_uq11,mag_uq12,mag_uq13,mag_uq14,mag_uq15
    real(dp) :: mag_uq21,mag_uq22,mag_uq23,mag_uq24,mag_uq25
    real(dp) :: u_dot_nq11,u_dot_nq12,u_dot_nq13,u_dot_nq14,u_dot_nq15
    real(dp) :: u_dot_nq21,u_dot_nq22,u_dot_nq23,u_dot_nq24,u_dot_nq25
    real(dp) :: u_dot_tq11,u_dot_tq12,u_dot_tq13,u_dot_tq14,u_dot_tq15
    real(dp) :: u_dot_tq21,u_dot_tq22,u_dot_tq23,u_dot_tq24,u_dot_tq25
    real(dp) :: u_cross_bxq11,u_cross_bxq12,u_cross_bxq13,u_cross_bxq14
    real(dp) :: u_cross_bxq15
    real(dp) :: u_cross_bxq21,u_cross_bxq22,u_cross_bxq23,u_cross_bxq24
    real(dp) :: u_cross_bxq25
    real(dp) :: u_cross_byq11,u_cross_byq12,u_cross_byq13,u_cross_byq14
    real(dp) :: u_cross_byq15
    real(dp) :: u_cross_byq21,u_cross_byq22,u_cross_byq23,u_cross_byq24
    real(dp) :: u_cross_byq25
    real(dp) :: u_cross_bzq11,u_cross_bzq12,u_cross_bzq13,u_cross_bzq14
    real(dp) :: u_cross_bzq15
    real(dp) :: u_cross_bzq21,u_cross_bzq22,u_cross_bzq23,u_cross_bzq24
    real(dp) :: u_cross_bzq25
    real(dp) :: coeffq11,coeffq12,coeffq13,coeffq14,coeffq15
    real(dp) :: coeffq21,coeffq22,coeffq23,coeffq24,coeffq25
    real(dp) :: xmomq11,xmomq12,xmomq13,xmomq14,xmomq15
    real(dp) :: xmomq21,xmomq22,xmomq23,xmomq24,xmomq25
    real(dp) :: ymomq11,ymomq12,ymomq13,ymomq14,ymomq15
    real(dp) :: ymomq21,ymomq22,ymomq23,ymomq24,ymomq25
    real(dp) :: zmomq11,zmomq12,zmomq13,zmomq14,zmomq15
    real(dp) :: zmomq21,zmomq22,zmomq23,zmomq24,zmomq25
    real(dp) :: energyq11,energyq12,energyq13,energyq14,energyq15
    real(dp) :: energyq21,energyq22,energyq23,energyq24,energyq25
    real(dp) :: top
    real(dp) :: topq11,topq12,topq13,topq14,topq15
    real(dp) :: topq21,topq22,topq23,topq24,topq25

  continue

    rho=0.0_dp; u=0.0_dp; v=0.0_dp; w=0.0_dp
    rhoq11=0.0_dp; rhoq12=0.0_dp; rhoq13=0.0_dp; rhoq14=0.0_dp; rhoq15=0.0_dp;
    rhoq21=0.0_dp; rhoq22=0.0_dp; rhoq23=0.0_dp; rhoq24=0.0_dp; rhoq25=0.0_dp;
    uq11=0.0_dp; uq12=0.0_dp; uq13=0.0_dp; uq14=0.0_dp; uq15=0.0_dp;
    uq21=0.0_dp; uq22=0.0_dp; uq23=0.0_dp; uq24=0.0_dp; uq25=0.0_dp;
    vq11=0.0_dp; vq12=0.0_dp; vq13=0.0_dp; vq14=0.0_dp; vq15=0.0_dp;
    vq21=0.0_dp; vq22=0.0_dp; vq23=0.0_dp; vq24=0.0_dp; vq25=0.0_dp;
    wq11=0.0_dp; wq12=0.0_dp; wq13=0.0_dp; wq14=0.0_dp; wq15=0.0_dp;
    wq21=0.0_dp; wq22=0.0_dp; wq23=0.0_dp; wq24=0.0_dp; wq25=0.0_dp;

    if ( .not. init ) then
      call my_alloc_ptr(boundary_point,grid%nnodes01)
      boundary_point(:) = .false.
      do ib = 1, grid%nbound
        do j = 1, grid%bc(ib)%nbnode
          inode = grid%bc(ib)%ibnode(j)
          if ( inode <= grid%nnodes0 ) boundary_point(inode) = .true.
        end do
      end do
      call lmpi_xfer(boundary_point)
      init = .true.
    endif

    vg_loop : do i = 1, nvg

      cut_edges : do j = 1, vg(i)%npair

        node1 = vg(i)%node_pair(1,j)
        node2 = vg(i)%node_pair(2,j)

        if ( boundary_point(node1) .and. boundary_point(node2) ) cycle cut_edges

        weight1 = vg(i)%weights(1,j)
        weight2 = vg(i)%weights(2,j)

        dvolume   = weight1*grid%vol(node1) + weight2*grid%vol(node2)
        total_vol = vg(i)%volume

        select case(soln%eqn_set)
        case(compressible)

          rho = weight1*soln%q_dof(1,node1) + weight2*soln%q_dof(1,node2)
          u   = weight1*(soln%q_dof(2,node1)/soln%q_dof(1,node1))              &
              + weight2*(soln%q_dof(2,node2)/soln%q_dof(1,node2))
          v   = weight1*(soln%q_dof(3,node1)/soln%q_dof(1,node1))              &
              + weight2*(soln%q_dof(3,node2)/soln%q_dof(1,node2))
          w   = weight1*(soln%q_dof(4,node1)/soln%q_dof(1,node1))              &
              + weight2*(soln%q_dof(4,node2)/soln%q_dof(1,node2))

          rhoq11 = weight1
          rhoq12 = 0.0_dp
          rhoq13 = 0.0_dp
          rhoq14 = 0.0_dp
          rhoq15 = 0.0_dp

          rhoq21 = weight2
          rhoq22 = 0.0_dp
          rhoq23 = 0.0_dp
          rhoq24 = 0.0_dp
          rhoq25 = 0.0_dp

          uq11 = weight1*(-soln%q_dof(2,node1)/soln%q_dof(1,node1)             &
                                              /soln%q_dof(1,node1))
          uq12 = weight1*(1.0_dp/soln%q_dof(1,node1))
          uq13 = 0.0_dp
          uq14 = 0.0_dp
          uq15 = 0.0_dp

          vq11 = weight1*(-soln%q_dof(3,node1)/soln%q_dof(1,node1)             &
                                              /soln%q_dof(1,node1))
          vq12 = 0.0_dp
          vq13 = weight1*(1.0_dp/soln%q_dof(1,node1))
          vq14 = 0.0_dp
          vq15 = 0.0_dp

          wq11 = weight1*(-soln%q_dof(4,node1)/soln%q_dof(1,node1)             &
                                              /soln%q_dof(1,node1))
          wq12 = 0.0_dp
          wq13 = 0.0_dp
          wq14 = weight1*(1.0_dp/soln%q_dof(1,node1))
          wq15 = 0.0_dp

          uq21 = weight2*(-soln%q_dof(2,node2)/soln%q_dof(1,node2)             &
                                              /soln%q_dof(1,node2))
          uq22 = weight2*(1.0_dp/soln%q_dof(1,node2))
          uq23 = 0.0_dp
          uq24 = 0.0_dp
          uq25 = 0.0_dp

          vq21 = weight2*(-soln%q_dof(3,node2)/soln%q_dof(1,node2)             &
                                              /soln%q_dof(1,node2))
          vq22 = 0.0_dp
          vq23 = weight2*(1.0_dp/soln%q_dof(1,node2))
          vq24 = 0.0_dp
          vq25 = 0.0_dp

          wq21 = weight2*(-soln%q_dof(4,node2)/soln%q_dof(1,node2)             &
                                              /soln%q_dof(1,node2))
          wq22 = 0.0_dp
          wq23 = 0.0_dp
          wq24 = weight2*(1.0_dp/soln%q_dof(1,node2))
          wq25 = 0.0_dp

        case(incompressible)

          rho = 1.0_dp
          u   = weight1*soln%q_dof(2,node1) + weight2*soln%q_dof(2,node2)
          v   = weight1*soln%q_dof(3,node1) + weight2*soln%q_dof(3,node2)
          w   = weight1*soln%q_dof(4,node1) + weight2*soln%q_dof(4,node2)

          rhoq11 = 0.0_dp
          rhoq12 = 0.0_dp
          rhoq13 = 0.0_dp
          rhoq14 = 0.0_dp
          rhoq15 = 0.0_dp

          rhoq21 = 0.0_dp
          rhoq22 = 0.0_dp
          rhoq23 = 0.0_dp
          rhoq24 = 0.0_dp
          rhoq25 = 0.0_dp

          uq11 = 0.0_dp
          uq12 = weight1
          uq13 = 0.0_dp
          uq14 = 0.0_dp
          uq15 = 0.0_dp

          vq11 = 0.0_dp
          vq12 = 0.0_dp
          vq13 = weight1
          vq14 = 0.0_dp
          vq15 = 0.0_dp

          wq11 = 0.0_dp
          wq12 = 0.0_dp
          wq13 = 0.0_dp
          wq14 = weight1
          wq15 = 0.0_dp

          uq21 = 0.0_dp
          uq22 = weight2
          uq23 = 0.0_dp
          uq24 = 0.0_dp
          uq25 = 0.0_dp

          vq21 = 0.0_dp
          vq22 = 0.0_dp
          vq23 = weight2
          vq24 = 0.0_dp
          vq25 = 0.0_dp

          wq21 = 0.0_dp
          wq22 = 0.0_dp
          wq23 = 0.0_dp
          wq24 = weight2
          wq25 = 0.0_dp

        case default
          write(*,*) 'Error: eqn_set not known in vgs_rhs: ', soln%eqn_set
          call lmpi_die
          stop
        end select

        mag_u = sqrt(u*u + v*v + w*w)
          mag_uq11= 0.5_dp/mag_u*(2.0_dp*u*uq11 + 2.0_dp*v*vq11 + 2.0_dp*w*wq11)
          mag_uq12= 0.5_dp/mag_u*(2.0_dp*u*uq12 + 2.0_dp*v*vq12 + 2.0_dp*w*wq12)
          mag_uq13= 0.5_dp/mag_u*(2.0_dp*u*uq13 + 2.0_dp*v*vq13 + 2.0_dp*w*wq13)
          mag_uq14= 0.5_dp/mag_u*(2.0_dp*u*uq14 + 2.0_dp*v*vq14 + 2.0_dp*w*wq14)
          mag_uq15= 0.5_dp/mag_u*(2.0_dp*u*uq15 + 2.0_dp*v*vq15 + 2.0_dp*w*wq15)

          mag_uq21= 0.5_dp/mag_u*(2.0_dp*u*uq21 + 2.0_dp*v*vq21 + 2.0_dp*w*wq21)
          mag_uq22= 0.5_dp/mag_u*(2.0_dp*u*uq22 + 2.0_dp*v*vq22 + 2.0_dp*w*wq22)
          mag_uq23= 0.5_dp/mag_u*(2.0_dp*u*uq23 + 2.0_dp*v*vq23 + 2.0_dp*w*wq23)
          mag_uq24= 0.5_dp/mag_u*(2.0_dp*u*uq24 + 2.0_dp*v*vq24 + 2.0_dp*w*wq24)
          mag_uq25= 0.5_dp/mag_u*(2.0_dp*u*uq25 + 2.0_dp*v*vq25 + 2.0_dp*w*wq25)

        u_dot_n = u*vg(i)%nx + v*vg(i)%ny + w*vg(i)%nz
          u_dot_nq11 = uq11*vg(i)%nx + vq11*vg(i)%ny + wq11*vg(i)%nz
          u_dot_nq12 = uq12*vg(i)%nx + vq12*vg(i)%ny + wq12*vg(i)%nz
          u_dot_nq13 = uq13*vg(i)%nx + vq13*vg(i)%ny + wq13*vg(i)%nz
          u_dot_nq14 = uq14*vg(i)%nx + vq14*vg(i)%ny + wq14*vg(i)%nz
          u_dot_nq15 = uq15*vg(i)%nx + vq15*vg(i)%ny + wq15*vg(i)%nz

          u_dot_nq21 = uq21*vg(i)%nx + vq21*vg(i)%ny + wq21*vg(i)%nz
          u_dot_nq22 = uq22*vg(i)%nx + vq22*vg(i)%ny + wq22*vg(i)%nz
          u_dot_nq23 = uq23*vg(i)%nx + vq23*vg(i)%ny + wq23*vg(i)%nz
          u_dot_nq24 = uq24*vg(i)%nx + vq24*vg(i)%ny + wq24*vg(i)%nz
          u_dot_nq25 = uq25*vg(i)%nx + vq25*vg(i)%ny + wq25*vg(i)%nz

        u_cross_bx = v*vg(i)%bz - w*vg(i)%by
          u_cross_bxq11 = vq11*vg(i)%bz - wq11*vg(i)%by
          u_cross_bxq12 = vq12*vg(i)%bz - wq12*vg(i)%by
          u_cross_bxq13 = vq13*vg(i)%bz - wq13*vg(i)%by
          u_cross_bxq14 = vq14*vg(i)%bz - wq14*vg(i)%by
          u_cross_bxq15 = vq15*vg(i)%bz - wq15*vg(i)%by

          u_cross_bxq21 = vq21*vg(i)%bz - wq21*vg(i)%by
          u_cross_bxq22 = vq22*vg(i)%bz - wq22*vg(i)%by
          u_cross_bxq23 = vq23*vg(i)%bz - wq23*vg(i)%by
          u_cross_bxq24 = vq24*vg(i)%bz - wq24*vg(i)%by
          u_cross_bxq25 = vq25*vg(i)%bz - wq25*vg(i)%by

        u_cross_by = w*vg(i)%bx - u*vg(i)%bz
          u_cross_byq11 = wq11*vg(i)%bx - uq11*vg(i)%bz
          u_cross_byq12 = wq12*vg(i)%bx - uq12*vg(i)%bz
          u_cross_byq13 = wq13*vg(i)%bx - uq13*vg(i)%bz
          u_cross_byq14 = wq14*vg(i)%bx - uq14*vg(i)%bz
          u_cross_byq15 = wq15*vg(i)%bx - uq15*vg(i)%bz

          u_cross_byq21 = wq21*vg(i)%bx - uq21*vg(i)%bz
          u_cross_byq22 = wq22*vg(i)%bx - uq22*vg(i)%bz
          u_cross_byq23 = wq23*vg(i)%bx - uq23*vg(i)%bz
          u_cross_byq24 = wq24*vg(i)%bx - uq24*vg(i)%bz
          u_cross_byq25 = wq25*vg(i)%bx - uq25*vg(i)%bz

        u_cross_bz = u*vg(i)%by - v*vg(i)%bx
          u_cross_bzq11 = uq11*vg(i)%by - vq11*vg(i)%bx
          u_cross_bzq12 = uq12*vg(i)%by - vq12*vg(i)%bx
          u_cross_bzq13 = uq13*vg(i)%by - vq13*vg(i)%bx
          u_cross_bzq14 = uq14*vg(i)%by - vq14*vg(i)%bx
          u_cross_bzq15 = uq15*vg(i)%by - vq15*vg(i)%bx

          u_cross_bzq21 = uq21*vg(i)%by - vq21*vg(i)%bx
          u_cross_bzq22 = uq22*vg(i)%by - vq22*vg(i)%bx
          u_cross_bzq23 = uq23*vg(i)%by - vq23*vg(i)%bx
          u_cross_bzq24 = uq24*vg(i)%by - vq24*vg(i)%bx
          u_cross_bzq25 = uq25*vg(i)%by - vq25*vg(i)%bx

        u_dot_t = u*vg(i)%tx + v*vg(i)%ty + w*vg(i)%tz
          u_dot_tq11 = uq11*vg(i)%tx + vq11*vg(i)%ty + wq11*vg(i)%tz
          u_dot_tq12 = uq12*vg(i)%tx + vq12*vg(i)%ty + wq12*vg(i)%tz
          u_dot_tq13 = uq13*vg(i)%tx + vq13*vg(i)%ty + wq13*vg(i)%tz
          u_dot_tq14 = uq14*vg(i)%tx + vq14*vg(i)%ty + wq14*vg(i)%tz
          u_dot_tq15 = uq15*vg(i)%tx + vq15*vg(i)%ty + wq15*vg(i)%tz

          u_dot_tq21 = uq21*vg(i)%tx + vq21*vg(i)%ty + wq21*vg(i)%tz
          u_dot_tq22 = uq22*vg(i)%tx + vq22*vg(i)%ty + wq22*vg(i)%tz
          u_dot_tq23 = uq23*vg(i)%tx + vq23*vg(i)%ty + wq23*vg(i)%tz
          u_dot_tq24 = uq24*vg(i)%tx + vq24*vg(i)%ty + wq24*vg(i)%tz
          u_dot_tq25 = uq25*vg(i)%tx + vq25*vg(i)%ty + wq25*vg(i)%tz

! Now lets form the scalar coefficient

        top = rho*u_dot_n*u_dot_t
          topq11 = rho*(u_dot_n*u_dot_tq11 + u_dot_t*u_dot_nq11)               &
                 + u_dot_n*u_dot_t*rhoq11
          topq12 = rho*(u_dot_n*u_dot_tq12 + u_dot_t*u_dot_nq12)               &
                 + u_dot_n*u_dot_t*rhoq12
          topq13 = rho*(u_dot_n*u_dot_tq13 + u_dot_t*u_dot_nq13)               &
                 + u_dot_n*u_dot_t*rhoq13
          topq14 = rho*(u_dot_n*u_dot_tq14 + u_dot_t*u_dot_nq14)               &
                 + u_dot_n*u_dot_t*rhoq14
          topq15 = rho*(u_dot_n*u_dot_tq15 + u_dot_t*u_dot_nq15)               &
                 + u_dot_n*u_dot_t*rhoq15

          topq21 = rho*(u_dot_n*u_dot_tq21 + u_dot_t*u_dot_nq21)               &
                 + u_dot_n*u_dot_t*rhoq21
          topq22 = rho*(u_dot_n*u_dot_tq22 + u_dot_t*u_dot_nq22)               &
                 + u_dot_n*u_dot_t*rhoq22
          topq23 = rho*(u_dot_n*u_dot_tq23 + u_dot_t*u_dot_nq23)               &
                 + u_dot_n*u_dot_t*rhoq23
          topq24 = rho*(u_dot_n*u_dot_tq24 + u_dot_t*u_dot_nq24)               &
                 + u_dot_n*u_dot_t*rhoq24
          topq25 = rho*(u_dot_n*u_dot_tq25 + u_dot_t*u_dot_nq25)               &
                 + u_dot_n*u_dot_t*rhoq25

        coeff = vg(i)%c*vg(i)%planform_area*dvolume/total_vol * top/mag_u
          coeffq11 = vg(i)%c*vg(i)%planform_area*dvolume/total_vol *           &
                     (mag_u*topq11 - top*mag_uq11)/mag_u/mag_u
          coeffq12 = vg(i)%c*vg(i)%planform_area*dvolume/total_vol *           &
                     (mag_u*topq12 - top*mag_uq12)/mag_u/mag_u
          coeffq13 = vg(i)%c*vg(i)%planform_area*dvolume/total_vol *           &
                     (mag_u*topq13 - top*mag_uq13)/mag_u/mag_u
          coeffq14 = vg(i)%c*vg(i)%planform_area*dvolume/total_vol *           &
                     (mag_u*topq14 - top*mag_uq14)/mag_u/mag_u
          coeffq15 = vg(i)%c*vg(i)%planform_area*dvolume/total_vol *           &
                     (mag_u*topq15 - top*mag_uq15)/mag_u/mag_u

          coeffq21 = vg(i)%c*vg(i)%planform_area*dvolume/total_vol *           &
                     (mag_u*topq21 - top*mag_uq21)/mag_u/mag_u
          coeffq22 = vg(i)%c*vg(i)%planform_area*dvolume/total_vol *           &
                     (mag_u*topq22 - top*mag_uq22)/mag_u/mag_u
          coeffq23 = vg(i)%c*vg(i)%planform_area*dvolume/total_vol *           &
                     (mag_u*topq23 - top*mag_uq23)/mag_u/mag_u
          coeffq24 = vg(i)%c*vg(i)%planform_area*dvolume/total_vol *           &
                     (mag_u*topq24 - top*mag_uq24)/mag_u/mag_u
          coeffq25 = vg(i)%c*vg(i)%planform_area*dvolume/total_vol *           &
                     (mag_u*topq25 - top*mag_uq25)/mag_u/mag_u

! Form the momentum contributions, taking into account the additional volume
! factor that arises when we integrate over the volumes

        xmom = coeff*u_cross_bx*dvolume
          xmomq11 = (coeff*u_cross_bxq11 + u_cross_bx*coeffq11)*dvolume
          xmomq12 = (coeff*u_cross_bxq12 + u_cross_bx*coeffq12)*dvolume
          xmomq13 = (coeff*u_cross_bxq13 + u_cross_bx*coeffq13)*dvolume
          xmomq14 = (coeff*u_cross_bxq14 + u_cross_bx*coeffq14)*dvolume
          xmomq15 = (coeff*u_cross_bxq15 + u_cross_bx*coeffq15)*dvolume

          xmomq21 = (coeff*u_cross_bxq21 + u_cross_bx*coeffq21)*dvolume
          xmomq22 = (coeff*u_cross_bxq22 + u_cross_bx*coeffq22)*dvolume
          xmomq23 = (coeff*u_cross_bxq23 + u_cross_bx*coeffq23)*dvolume
          xmomq24 = (coeff*u_cross_bxq24 + u_cross_bx*coeffq24)*dvolume
          xmomq25 = (coeff*u_cross_bxq25 + u_cross_bx*coeffq25)*dvolume

        ymom = coeff*u_cross_by*dvolume
          ymomq11 = (coeff*u_cross_byq11 + u_cross_by*coeffq11)*dvolume
          ymomq12 = (coeff*u_cross_byq12 + u_cross_by*coeffq12)*dvolume
          ymomq13 = (coeff*u_cross_byq13 + u_cross_by*coeffq13)*dvolume
          ymomq14 = (coeff*u_cross_byq14 + u_cross_by*coeffq14)*dvolume
          ymomq15 = (coeff*u_cross_byq15 + u_cross_by*coeffq15)*dvolume

          ymomq21 = (coeff*u_cross_byq21 + u_cross_by*coeffq21)*dvolume
          ymomq22 = (coeff*u_cross_byq22 + u_cross_by*coeffq22)*dvolume
          ymomq23 = (coeff*u_cross_byq23 + u_cross_by*coeffq23)*dvolume
          ymomq24 = (coeff*u_cross_byq24 + u_cross_by*coeffq24)*dvolume
          ymomq25 = (coeff*u_cross_byq25 + u_cross_by*coeffq25)*dvolume

        zmom = coeff*u_cross_bz*dvolume
          zmomq11 = (coeff*u_cross_bzq11 + u_cross_bz*coeffq11)*dvolume
          zmomq12 = (coeff*u_cross_bzq12 + u_cross_bz*coeffq12)*dvolume
          zmomq13 = (coeff*u_cross_bzq13 + u_cross_bz*coeffq13)*dvolume
          zmomq14 = (coeff*u_cross_bzq14 + u_cross_bz*coeffq14)*dvolume
          zmomq15 = (coeff*u_cross_bzq15 + u_cross_bz*coeffq15)*dvolume

          zmomq21 = (coeff*u_cross_bzq21 + u_cross_bz*coeffq21)*dvolume
          zmomq22 = (coeff*u_cross_bzq22 + u_cross_bz*coeffq22)*dvolume
          zmomq23 = (coeff*u_cross_bzq23 + u_cross_bz*coeffq23)*dvolume
          zmomq24 = (coeff*u_cross_bzq24 + u_cross_bz*coeffq24)*dvolume
          zmomq25 = (coeff*u_cross_bzq25 + u_cross_bz*coeffq25)*dvolume

! energy contribution (volume factor is already built into the momentum pieces)

!       energy = u*xmom + v*ymom + w*zmom
          energyq11 = u*xmomq11 + xmom*uq11 + v*ymomq11 + ymom*vq11 + w*zmomq11&
                    + zmom*wq11
          energyq12 = u*xmomq12 + xmom*uq12 + v*ymomq12 + ymom*vq12 + w*zmomq12&
                    + zmom*wq12
          energyq13 = u*xmomq13 + xmom*uq13 + v*ymomq13 + ymom*vq13 + w*zmomq13&
                    + zmom*wq13
          energyq14 = u*xmomq14 + xmom*uq14 + v*ymomq14 + ymom*vq14 + w*zmomq14&
                    + zmom*wq14
          energyq15 = u*xmomq15 + xmom*uq15 + v*ymomq15 + ymom*vq15 + w*zmomq15&
                    + zmom*wq15

          energyq21 = u*xmomq21 + xmom*uq21 + v*ymomq21 + ymom*vq21 + w*zmomq21&
                    + zmom*wq21
          energyq22 = u*xmomq22 + xmom*uq22 + v*ymomq22 + ymom*vq22 + w*zmomq22&
                    + zmom*wq22
          energyq23 = u*xmomq23 + xmom*uq23 + v*ymomq23 + ymom*vq23 + w*zmomq23&
                    + zmom*wq23
          energyq24 = u*xmomq24 + xmom*uq24 + v*ymomq24 + ymom*vq24 + w*zmomq24&
                    + zmom*wq24
          energyq25 = u*xmomq25 + xmom*uq25 + v*ymomq25 + ymom*vq25 + w*zmomq25&
                    + zmom*wq25

! Add weighted contributions back into the residual vector

        if ( node1 <= grid%nnodes0 .and. .not. boundary_point(node1) ) then

          row = crow%g2m(node1)

          soln%a_diag(2,1,row) = soln%a_diag(2,1,row) + weight1*xmomq11
          soln%a_diag(2,2,row) = soln%a_diag(2,2,row) + weight1*xmomq12
          soln%a_diag(2,3,row) = soln%a_diag(2,3,row) + weight1*xmomq13
          soln%a_diag(2,4,row) = soln%a_diag(2,4,row) + weight1*xmomq14
          if ( soln%eqn_set == compressible ) then
            soln%a_diag(2,5,row) = soln%a_diag(2,5,row) + weight1*xmomq15
          endif

          soln%a_diag(3,1,row) = soln%a_diag(3,1,row) + weight1*ymomq11
          soln%a_diag(3,2,row) = soln%a_diag(3,2,row) + weight1*ymomq12
          soln%a_diag(3,3,row) = soln%a_diag(3,3,row) + weight1*ymomq13
          soln%a_diag(3,4,row) = soln%a_diag(3,4,row) + weight1*ymomq14
          if ( soln%eqn_set == compressible ) then
            soln%a_diag(3,5,row) = soln%a_diag(3,5,row) + weight1*ymomq15
          endif

          soln%a_diag(4,1,row) = soln%a_diag(4,1,row) + weight1*zmomq11
          soln%a_diag(4,2,row) = soln%a_diag(4,2,row) + weight1*zmomq12
          soln%a_diag(4,3,row) = soln%a_diag(4,3,row) + weight1*zmomq13
          soln%a_diag(4,4,row) = soln%a_diag(4,4,row) + weight1*zmomq14
          if ( soln%eqn_set == compressible ) then
            soln%a_diag(4,5,row) = soln%a_diag(4,5,row) + weight1*zmomq15
          endif

          if ( soln%eqn_set == compressible ) then
            soln%a_diag(5,1,row) = soln%a_diag(5,1,row) + weight1*energyq11
            soln%a_diag(5,2,row) = soln%a_diag(5,2,row) + weight1*energyq12
            soln%a_diag(5,3,row) = soln%a_diag(5,3,row) + weight1*energyq13
            soln%a_diag(5,4,row) = soln%a_diag(5,4,row) + weight1*energyq14
            soln%a_diag(5,5,row) = soln%a_diag(5,5,row) + weight1*energyq15
          endif

          ioff = 0
          search1 : do k = crow%ia(node1), crow%ia(node1+1) - 1
            column = crow%ja(k)
            if (column == node2) then
              ioff = crow%nzg2m(k)
              exit search1
            endif
          end do search1

          soln%a_off(2,1,ioff) = soln%a_off(2,1,ioff) + weight1*xmomq21
          soln%a_off(2,2,ioff) = soln%a_off(2,2,ioff) + weight1*xmomq22
          soln%a_off(2,3,ioff) = soln%a_off(2,3,ioff) + weight1*xmomq23
          soln%a_off(2,4,ioff) = soln%a_off(2,4,ioff) + weight1*xmomq24
          if ( soln%eqn_set == compressible ) then
            soln%a_off(2,5,ioff) = soln%a_off(2,5,ioff) + weight1*xmomq25
          endif

          soln%a_off(3,1,ioff) = soln%a_off(3,1,ioff) + weight1*ymomq21
          soln%a_off(3,2,ioff) = soln%a_off(3,2,ioff) + weight1*ymomq22
          soln%a_off(3,3,ioff) = soln%a_off(3,3,ioff) + weight1*ymomq23
          soln%a_off(3,4,ioff) = soln%a_off(3,4,ioff) + weight1*ymomq24
          if ( soln%eqn_set == compressible ) then
            soln%a_off(3,5,ioff) = soln%a_off(3,5,ioff) + weight1*ymomq25
          endif

          soln%a_off(4,1,ioff) = soln%a_off(4,1,ioff) + weight1*zmomq21
          soln%a_off(4,2,ioff) = soln%a_off(4,2,ioff) + weight1*zmomq22
          soln%a_off(4,3,ioff) = soln%a_off(4,3,ioff) + weight1*zmomq23
          soln%a_off(4,4,ioff) = soln%a_off(4,4,ioff) + weight1*zmomq24
          if ( soln%eqn_set == compressible ) then
            soln%a_off(4,5,ioff) = soln%a_off(4,5,ioff) + weight1*zmomq25
          endif

          if ( soln%eqn_set == compressible ) then
            soln%a_off(5,1,ioff) = soln%a_off(5,1,ioff) + weight1*energyq21
            soln%a_off(5,2,ioff) = soln%a_off(5,2,ioff) + weight1*energyq22
            soln%a_off(5,3,ioff) = soln%a_off(5,3,ioff) + weight1*energyq23
            soln%a_off(5,4,ioff) = soln%a_off(5,4,ioff) + weight1*energyq24
            soln%a_off(5,5,ioff) = soln%a_off(5,5,ioff) + weight1*energyq25
          endif

        endif

        if ( node2 <= grid%nnodes0 .and. .not. boundary_point(node2) ) then

          row = crow%g2m(node2)

          soln%a_diag(2,1,row) = soln%a_diag(2,1,row) + weight2*xmomq21
          soln%a_diag(2,2,row) = soln%a_diag(2,2,row) + weight2*xmomq22
          soln%a_diag(2,3,row) = soln%a_diag(2,3,row) + weight2*xmomq23
          soln%a_diag(2,4,row) = soln%a_diag(2,4,row) + weight2*xmomq24
          if ( soln%eqn_set == compressible ) then
            soln%a_diag(2,5,row) = soln%a_diag(2,5,row) + weight2*xmomq25
          endif

          soln%a_diag(3,1,row) = soln%a_diag(3,1,row) + weight2*ymomq21
          soln%a_diag(3,2,row) = soln%a_diag(3,2,row) + weight2*ymomq22
          soln%a_diag(3,3,row) = soln%a_diag(3,3,row) + weight2*ymomq23
          soln%a_diag(3,4,row) = soln%a_diag(3,4,row) + weight2*ymomq24
          if ( soln%eqn_set == compressible ) then
            soln%a_diag(3,5,row) = soln%a_diag(3,5,row) + weight2*ymomq25
          endif

          soln%a_diag(4,1,row) = soln%a_diag(4,1,row) + weight2*zmomq21
          soln%a_diag(4,2,row) = soln%a_diag(4,2,row) + weight2*zmomq22
          soln%a_diag(4,3,row) = soln%a_diag(4,3,row) + weight2*zmomq23
          soln%a_diag(4,4,row) = soln%a_diag(4,4,row) + weight2*zmomq24
          if ( soln%eqn_set == compressible ) then
            soln%a_diag(4,5,row) = soln%a_diag(4,5,row) + weight2*zmomq25
          endif

          if ( soln%eqn_set == compressible ) then
            soln%a_diag(5,1,row) = soln%a_diag(5,1,row) + weight2*energyq21
            soln%a_diag(5,2,row) = soln%a_diag(5,2,row) + weight2*energyq22
            soln%a_diag(5,3,row) = soln%a_diag(5,3,row) + weight2*energyq23
            soln%a_diag(5,4,row) = soln%a_diag(5,4,row) + weight2*energyq24
            soln%a_diag(5,5,row) = soln%a_diag(5,5,row) + weight2*energyq25
          endif

          ioff = 0
          search2 : do k = crow%ia(node2), crow%ia(node2+1) - 1
            column = crow%ja(k)
            if (column == node1) then
              ioff = crow%nzg2m(k)
              exit search2
            endif
          end do search2

          soln%a_off(2,1,ioff) = soln%a_off(2,1,ioff) + weight2*xmomq11
          soln%a_off(2,2,ioff) = soln%a_off(2,2,ioff) + weight2*xmomq12
          soln%a_off(2,3,ioff) = soln%a_off(2,3,ioff) + weight2*xmomq13
          soln%a_off(2,4,ioff) = soln%a_off(2,4,ioff) + weight2*xmomq14
          if ( soln%eqn_set == compressible ) then
            soln%a_off(2,5,ioff) = soln%a_off(2,5,ioff) + weight2*xmomq15
          endif

          soln%a_off(3,1,ioff) = soln%a_off(3,1,ioff) + weight2*ymomq11
          soln%a_off(3,2,ioff) = soln%a_off(3,2,ioff) + weight2*ymomq12
          soln%a_off(3,3,ioff) = soln%a_off(3,3,ioff) + weight2*ymomq13
          soln%a_off(3,4,ioff) = soln%a_off(3,4,ioff) + weight2*ymomq14
          if ( soln%eqn_set == compressible ) then
            soln%a_off(3,5,ioff) = soln%a_off(3,5,ioff) + weight2*ymomq15
          endif

          soln%a_off(4,1,ioff) = soln%a_off(4,1,ioff) + weight2*zmomq11
          soln%a_off(4,2,ioff) = soln%a_off(4,2,ioff) + weight2*zmomq12
          soln%a_off(4,3,ioff) = soln%a_off(4,3,ioff) + weight2*zmomq13
          soln%a_off(4,4,ioff) = soln%a_off(4,4,ioff) + weight2*zmomq14
          if ( soln%eqn_set == compressible ) then
            soln%a_off(4,5,ioff) = soln%a_off(4,5,ioff) + weight2*zmomq15
          endif

          if ( soln%eqn_set == compressible ) then
            soln%a_off(5,1,ioff) = soln%a_off(5,1,ioff) + weight2*energyq11
            soln%a_off(5,2,ioff) = soln%a_off(5,2,ioff) + weight2*energyq12
            soln%a_off(5,3,ioff) = soln%a_off(5,3,ioff) + weight2*energyq13
            soln%a_off(5,4,ioff) = soln%a_off(5,4,ioff) + weight2*energyq14
            soln%a_off(5,5,ioff) = soln%a_off(5,5,ioff) + weight2*energyq15
          endif

        endif

      end do cut_edges

    end do vg_loop

  end subroutine vgs_lhs

  include 'cross_product.f90'

end module vortex_generators

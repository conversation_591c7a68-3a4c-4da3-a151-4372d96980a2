!=================================== DQUMUSCL ================================80
!
! Extrapolate or interpolate and limit the gradient to the interface
!
!   Extended to the "U-MUSCL" scheme of Burg et al (AIAA 2003-3983) with
!   upwinding parameter kappa_umuscl:
!   kappa_umuscl is analogous to kappa in the usual structured-mesh upwind
!   schemes. kappa_umuscl = 0 gives the standard (baseline fun3d) unstructured
!   scheme; kappa_umuscl = 1 is central difference; kappa_umuscl = 1/2 gives
!   3rd order in one dimension if the gradients are 2nd order.
!
!=============================================================================80

  pure function dqumuscl(rx, ry, rz, gradx, grady, gradz, q1, q2,              &
                         phi, eps, ndim)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_4th, my_1, my_3, my_4
    use info_depr,       only : kappa_umuscl, epscoef
    use inviscid_flux,   only : iflim

    integer,                   intent(in) :: ndim
    real(dp),                  intent(in) :: rx, ry, rz, eps
    real(dp), dimension(ndim), intent(in) :: gradx, grady, gradz
    real(dp), dimension(ndim), intent(in) :: phi
    real(dp), dimension(ndim), intent(in) :: q1, q2
    real(dp), dimension(ndim)             :: dqumuscl

    real(dp)                              :: omega
    real(dp), dimension(ndim)             :: dqm, dqmb, dqp, dqpb

  continue

!   Compute the successive undivided gradients (dq- and dq+)

    dqm = my_4*(gradx*rx + grady*ry + gradz*rz) - (q2 - q1)
    dqp = q2 - q1

!   Construct the cell face gradients depending on the limiter type

!   Edge limited or unlimited gradients
!   N.B.: 0<= phi <= 1 is invoked independent of iflim by the pressure limiter

    if (iflim == 6) then

!     Limit the gradient using the
!     Smooth(CFl3D) differentiable gradient limiter

      dqmb = phi*vaflxlv(dqm, dqp, epscoef*eps, ndim)
      dqpb = phi*vaflxlv(dqp, dqm, epscoef*eps, ndim)

    else if (iflim == 5) then

!     Limit the gradient using the
!     Smooth(vanAlbada) differentiable gradient limiter

      dqmb = phi*smthlmv(dqm, dqp, epscoef*eps, ndim)
      dqpb = phi*smthlmv(dqp, dqm, epscoef*eps, ndim)

    else if (iflim == 4) then

!     Limit the gradients using the
!     vanLeer gradient limiter

      dqmb = vlflxlv(dqm, dqp, ndim)
      dqpb = vlflxlv(dqp, dqm, ndim)

    else if (iflim == 3) then

!     Limit the gradients using the
!     minmod gradient limiter

      omega = (my_3 - kappa_umuscl)/(my_1 - kappa_umuscl)
      dqmb  = minmodv(dqm, omega*dqp, ndim)
      dqpb  = minmodv(dqp, omega*dqm, ndim)

    else

!     Unlimited gradients or freezable gradient limiters (Barth or Venkat)

      dqmb = dqm
      dqpb = dqp

    end if

!   Higher order state reconstruction using U-MUSCL with limiting via phi
!   N.B.:     phi  = 1 is invoked by the no limiter option, iflim=0
!         0<= phi <= 1 is invoked by limiter options, iflim=1 or iflim=2
!         0<= phi <= 1 is invoked independent of iflim by the pressure limiter

    dqumuscl = phi*my_4th*((my_1-kappa_umuscl)*dqmb+(my_1+kappa_umuscl)*dqpb)

  end function dqumuscl

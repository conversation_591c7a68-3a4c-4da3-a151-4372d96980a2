!======================================= YPLUS ===============================80
!
!  Computes yplus at a point on a viscous surface
!
!=============================================================================80

  pure function y_plus( eqn_set, n_tot, cfx, cfy, cfz, qnode, slen )

    use kinddefs,       only : dp
    use info_depr,      only : re, tref
    use fluid,          only : gamma, sutherland_constant
    use solution_types, only : compressible

    real(dp)                               :: y_plus
    integer,                    intent(in) :: n_tot, eqn_set
    real(dp),                   intent(in) :: cfx, cfy, cfz
    real(dp), dimension(n_tot), intent(in) :: qnode
    real(dp),                   intent(in) :: slen

    real(dp) :: cf, mu, cstar, rho, press, temp

  continue

    cstar = sutherland_constant/tref

    if ( eqn_set == compressible ) then

      rho   = qnode(1)
      press = qnode(5)
      temp = gamma*press/rho
      mu   = (1._dp + cstar)/(temp + cstar)*temp**1.5_dp
      cf = sqrt(cfx**2 + cfy**2 + cfz**2)
      y_plus = re*slen*sqrt(rho*cf/2._dp)/mu

    else

      cf = sqrt(cfx**2 + cfy**2 + cfz**2)
      y_plus = re*slen*sqrt(cf/2._dp)

    end if

  end function y_plus

!================================= ELEMENT_JACOBIANS =========================80
!
! This routine computes the jacobians of gradients in a primal cell with respect
! to PRIMITIVE variables via Green-Gauss in 2 or 3 dimensions
!
!=============================================================================80

  pure function element_dgrad( edges_local, node_per_cell, face_per_cell,      &
                               r_node, local_f2n, e2n_2d, chk_norm )

    use info_depr,       only : twod, ivgrd
    use fun3d_constants, only : my_1, my_0, my_half, my_4th, my_6th

    integer,                              intent(in)  :: edges_local
    integer,                              intent(in)  :: node_per_cell
    integer,                              intent(in)  :: face_per_cell
    integer,  dimension(:,:),             intent(in)  :: local_f2n
    integer,  dimension(4,2),             intent(in)  :: e2n_2d
    real(dp), dimension(node_per_cell,3), intent(in)  :: r_node
    integer,  dimension(face_per_cell,face_per_cell),              &
                                          intent(in)  :: chk_norm
    real(dp), dimension(node_per_cell,3)              :: element_dgrad

    real(dp), dimension(face_per_cell) :: nxf
    real(dp), dimension(face_per_cell) :: nyf
    real(dp), dimension(face_per_cell) :: nzf

    integer :: iface, ie_local
    integer :: nn1, nn2, nn3, nn4

    real(dp) :: xavg, yavg, zavg, cell_vol_inv, cell_vol
    real(dp) :: xavg0, yavg0, zavg0
    real(dp) :: x1, x2, x3, x4, nx, nx0
    real(dp) :: y1, y2, y3, y4, ny, ny0
    real(dp) :: z1, z2, z3, z4, nz, nz0
    real(dp) :: termx, termy, termz
    real(dp) :: termx0, termy0, termz0
    real(dp) :: term0

    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp
    real(dp), parameter :: my_24th = 1.0_dp/24.0_dp
    real(dp), parameter :: my_8th  = 1.0_dp/8.0_dp

    logical :: skip_viscous_terms

    continue

!   initialization

    element_dgrad(:,:) = my_0

    skip_viscous_terms = .false.

    cell_vol = my_0

    cell_dimension : if (twod) then

      twod_edges : do ie_local = 1,edges_local

!       local node numbers of edge endpoints

        nn1 = e2n_2d(ie_local,1)
        nn2 = e2n_2d(ie_local,2)

        x1 = r_node(nn1,1)
        x2 = r_node(nn2,1)

        z1 = r_node(nn1,3)
        z2 = r_node(nn2,3)

!       edge midpoint (factor of 1/2 deferred till cell_vol
!       and jacobian terms are calculated)

        xavg = x1 + x2
        zavg = z1 + z2

!       edge normals

        nx = -(z2 - z1)
        nz =  (x2 - x1)

!       cell volume (area) contributions

        cell_vol = cell_vol + (xavg*nx + zavg*nz)*my_4th

        termx = nx*my_half
        termz = nz*my_half

!       jacobian contributions
!       note: jacobian pieces end up depending on geometry only, so
!       unlike the cell fluxes, there is no need to loop over qdim;
!       the loop is left here as commented for reference

!       do eqn = 1,qdim

!         qavg = q_node(eqn,nn1) + q_node(eqn,nn2)

!         grad_cell(eqn,1) = grad_cell(eqn,1) + termx*qavg
!         grad_cell(eqn,3) = grad_cell(eqn,3) + termz*qavg

          element_dgrad(nn1,1) = element_dgrad(nn1,1) + termx
          element_dgrad(nn1,3) = element_dgrad(nn1,3) + termz

          element_dgrad(nn2,1) = element_dgrad(nn2,1) + termx
          element_dgrad(nn2,3) = element_dgrad(nn2,3) + termz

!       end do

      end do twod_edges

    else cell_dimension

      threed_faces : do iface = 1,face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!         triangular faces of the cell

          x1 = r_node(nn1,1)
          x2 = r_node(nn2,1)
          x3 = r_node(nn3,1)

          y1 = r_node(nn1,2)
          y2 = r_node(nn2,2)
          y3 = r_node(nn3,2)

          z1 = r_node(nn1,3)
          z2 = r_node(nn2,3)
          z3 = r_node(nn3,3)

!         face normals (factor of 1/2 deferred till cell_vol
!         and jacobian terms are calculated)

          nx = (y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1)
          ny = (z2 - z1)*(x3 - x1) - (x2 - x1)*(z3 - z1)
          nz = (x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1)

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg = x1 + x2 + x3
          yavg = y1 + y2 + y3
          zavg = z1 + z2 + z3

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

!         jacobian contributions
!         note: jacobian pieces end up depending on geometry only, so
!         unlike the cell fluxes, there is no need to loop over qdim;
!         the loop is left here as commented for reference

!         do eqn = 1,qdim

!           qavg = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)

!           grad_cell(eqn,1) = grad_cell(eqn,1) + termx*qavg
!           grad_cell(eqn,2) = grad_cell(eqn,2) + termy*qavg
!           grad_cell(eqn,3) = grad_cell(eqn,3) + termz*qavg

            element_dgrad(nn1,1) = element_dgrad(nn1,1) + termx
            element_dgrad(nn1,2) = element_dgrad(nn1,2) + termy
            element_dgrad(nn1,3) = element_dgrad(nn1,3) + termz

            element_dgrad(nn2,1) = element_dgrad(nn2,1) + termx
            element_dgrad(nn2,2) = element_dgrad(nn2,2) + termy
            element_dgrad(nn2,3) = element_dgrad(nn2,3) + termz

            element_dgrad(nn3,1) = element_dgrad(nn3,1) + termx
            element_dgrad(nn3,2) = element_dgrad(nn3,2) + termy
            element_dgrad(nn3,3) = element_dgrad(nn3,3) + termz

!         end do

        else

!         quadrilateral faces of the cell

          x1 = r_node(nn1,1)
          x2 = r_node(nn2,1)
          x3 = r_node(nn3,1)
          x4 = r_node(nn4,1)

          y1 = r_node(nn1,2)
          y2 = r_node(nn2,2)
          y3 = r_node(nn3,2)
          y4 = r_node(nn4,2)

          z1 = r_node(nn1,3)
          z2 = r_node(nn2,3)
          z3 = r_node(nn3,3)
          z4 = r_node(nn4,3)

!         Use simple quadratures to evaluate gradients.  The resulting
!         volume is identical to the volume computed by averaging the
!         volumes associated with the two diagonal split choices.  The
!         quadratures for gradient are not identical, but simpler and
!         consistent across elements which share faces.

!         Define face as average plane 1-2-3-4.  This average plane passes
!         through the center of the quad and through each edge midpoint.
!         The nodes of the quad are displaced from this plane by an identical
!         amount (+-+-) confirming the plane is a least squares fit.

!         face centroid (factor of 1/4 deferred till the
!         contribution to cell_vol is calculated)

          xavg0 = x1 + x2 + x3 + x4
          yavg0 = y1 + y2 + y3 + y4
          zavg0 = z1 + z2 + z3 + z4

!         normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx0 = (y2 - y4)*(z3 - z1) - (z2 - z4)*(y3 - y1)
          ny0 = (z2 - z4)*(x3 - x1) - (x2 - x4)*(z3 - z1)
          nz0 = (x2 - x4)*(y3 - y1) - (y2 - y4)*(x3 - x1)

          term0 = xavg0*nx0 + yavg0*ny0 + zavg0*nz0

!         cell volume contributions

          cell_vol = cell_vol + term0*my_24th

          termx0 = nx0*my_8th
          termy0 = ny0*my_8th
          termz0 = nz0*my_8th

!         jacobian contributions
!         note: jacobian pieces end up depending on geometry only, so
!         unlike the cell fluxes, there is no need to loop over qdim;
!         the loop is left here as commented for reference

!         do eqn = 1,qdim

!           qavg0 = q_node(eqn,nn1) + q_node(eqn,nn2) &
!                 + q_node(eqn,nn3) + q_node(eqn,nn4)

!           grad_cell(eqn,1) = gradx_cell(eqn,1) + termx0*qavg0
!           grad_cell(eqn,1) = grady_cell(eqn,1) + termy0*qavg0
!           grad_cell(eqn,1) = gradz_cell(eqn,1) + termz0*qavg0

            element_dgrad(nn1,1) = element_dgrad(nn1,1) + termx0
            element_dgrad(nn1,2) = element_dgrad(nn1,2) + termy0
            element_dgrad(nn1,3) = element_dgrad(nn1,3) + termz0

            element_dgrad(nn2,1) = element_dgrad(nn2,1) + termx0
            element_dgrad(nn2,2) = element_dgrad(nn2,2) + termy0
            element_dgrad(nn2,3) = element_dgrad(nn2,3) + termz0

            element_dgrad(nn3,1) = element_dgrad(nn3,1) + termx0
            element_dgrad(nn3,2) = element_dgrad(nn3,2) + termy0
            element_dgrad(nn3,3) = element_dgrad(nn3,3) + termz0

            element_dgrad(nn4,1) = element_dgrad(nn4,1) + termx0
            element_dgrad(nn4,2) = element_dgrad(nn4,2) + termy0
            element_dgrad(nn4,3) = element_dgrad(nn4,3) + termz0

!         end do

          nx = nx0
          ny = ny0
          nz = nz0

        end if

        nxf(iface) = nx
        nyf(iface) = ny
        nzf(iface) = nz

      end do threed_faces

      if ( ivgrd == 1 ) then
        skip_viscous_terms = big_angle( face_per_cell, nxf, nyf, nzf, chk_norm )
      endif

    end if cell_dimension

!   need to divide the jacobain sums by the grid cell volume to give the
!   cell-average Green-Gauss jacobians

    cell_vol_inv = my_1 / cell_vol

    if ( skip_viscous_terms ) cell_vol_inv = 0._dp

    element_dgrad(:,:) = element_dgrad(:,:) * cell_vol_inv

  end function element_dgrad

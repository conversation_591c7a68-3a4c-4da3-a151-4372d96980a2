module turb_7eqn

  use lmpi,            only : lmpi_id
  use lmpi,            only : lmpi_die, lmpi_conditional_stop
  use kinddefs,        only : dp
  use turb_kw_const,   only : verbose
  use turbulence_info, only : turbulence_model_int,                            &
                              WilcoxRSM_w2006c, WilcoxRSM_w2006,               &
                              SSGLRR_RSM_w2012_SD, SSGLRR_RSM_w2012

  implicit none

  private

  public :: bc_stressomega_set_walls, turb_resid_7eqn, turb_jacob_7eqn
  public :: wall_turbulence_stressomega

  real(dp), parameter :: zero    = 0.0_dp
  real(dp), parameter :: one     = 1.0_dp

contains

!============================== TURB_RESID_4EQN ==============================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for 7-eqn models on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine turb_resid_7eqn (eqn_set, nnodes0, nnodes01,                      &
                              turb, qnode, res, gradx, grady,                  &
                              gradz, vol, nnodes0_2d, node_pairs_2d, n_turb,   &
                              n_tot, n_grd, sst_f1, nbound, bc )

    use kinddefs,       only : dp
    use info_depr,      only : xmach, re, twod
    use lmpi,           only : lmpi_conditional_stop
    use solution_types, only : compressible, incompressible
    use bc_types,       only : bcgrid_type
    use bc_names,  only : symmetry_1_strong, symmetry_2_strong,                &
                          symmetry_3_strong

    integer,                              intent(in)    :: eqn_set
    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: n_tot
    integer,                              intent(in)    :: n_grd
    integer,                              intent(in)    :: nnodes0
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: nnodes0_2d
    integer,                              intent(in)    :: nbound

    integer,  dimension(2,nnodes0_2d),    intent(in)    :: node_pairs_2d

    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp), dimension(nnodes01),        intent(in)    :: sst_f1

    type(bcgrid_type),dimension(nbound),  intent(in)    :: bc

    integer :: node_src_eval
    integer :: i, ii, ib

    real(dp) :: xmr
    real(dp) :: my_xmach

    real(dp), dimension(n_turb)         :: sourcestressomega

  continue

!   Must disallow certain axi BCs that are not correctly done for 7-eqn models
    do ib = 1, nbound
      if ( bc(ib)%ibc == symmetry_1_strong .or.  &
           bc(ib)%ibc == symmetry_2_strong .or.  &
           bc(ib)%ibc == symmetry_3_strong ) then
        call lmpi_conditional_stop(1,'axi symmetry not correct for 7-eqn')
      endif
    enddo

!   When using edge-based diffusion terms, we only need to visit this routine
!   one time
    my_xmach = zero

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = one
    case default
      call lmpi_conditional_stop(1,'turb_resid: only for in/compress pg')
    end select

    xmr   = my_xmach / re

    node_src_eval   = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

!-----------------------------------------------------------------------------80
!                    source terms
!-----------------------------------------------------------------------------80
      do ii = 1, node_src_eval

        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if
          sourcestressomega = source_stressomega ( i                           &
                               , nnodes01, n_tot, n_turb, n_grd                &
                               , vol, qnode, turb, gradx, grady, gradz         &
                               , xmr, sst_f1 )

          res(1:7,i) = res(1:7,i) - sourcestressomega(1:7)

      end do

  end subroutine turb_resid_7eqn

!============================== TURB_JACOB_4EQN ==============================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for 7-eqn models (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine turb_jacob_7eqn (eqn_set, nnodes0, nnodes01,                      &
                       turb, qnode,                                            &
                       vol, a_diag,                                            &
                       nnodes0_2d, node_pairs_2d,                              &
                       n_turb, n_tot,                                          &
                       g2m, sst_f1)

    use kinddefs,      only : dp
    use info_depr,     only : twod

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: nnodes0_2d
    integer, intent(in) :: nnodes0, nnodes01

    integer, dimension(2,nnodes0_2d),        intent(in) :: node_pairs_2d
    integer, dimension(:),                   intent(in) :: g2m

    real(dp),  dimension(nnodes01),              intent(in)    :: vol
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(dp),  dimension(nnodes01),              intent(in)    :: sst_f1

    integer :: node_src_eval
    integer :: i, ii

    real(dp),  dimension(n_turb, n_turb) :: d_source_stressomega
    integer                              :: row

!   real(dp) :: my_xmach

  continue

!   my_xmach = zero
!   select case ( eqn_set )
!   case ( compressible )
!     my_xmach = xmach
!   case ( incompressible )
!     my_xmach = one
!   case default
!     call lmpi_conditional_stop(1,'turb_jacob_7eqn: only for in/compress pg')
!   end select
!   xmr   = my_xmach / re

!   When using the edge-based terms, we only need to visit this routine
!   one time

    node_src_eval  = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

!                             source terms
!-----------------------------------------------------------------------------80
!   Next compute the source (production/destruction) terms

        source2a: do ii = 1, node_src_eval
        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if

          d_source_stressomega = jacob_source_stressomega( i, eqn_set, nnodes01&
                            ,  turb, qnode, n_turb, n_tot, sst_f1 )

          row             = g2m(i)
          a_diag(:,:,row) = a_diag(:,:,row) + vol(i)*d_source_stressomega(:,:)

        end do source2a

  end subroutine turb_jacob_7eqn


!=========================== BC_STRESSOMEGA_SET_WALLS ========================80
!
!  Sets quantities on viscous walls for RSM
!
!=============================================================================80

  subroutine bc_stressomega_set_walls(eqn_set,                                 &
                                 nnodes0, nnodes01, nbnode, ibnode,            &
                                 slen_wall, turb, qnode, n_turb, n_tot,ibc,    &
                                 omega_wf )

    use solution_types, only : compressible, incompressible

    integer, intent(in) :: nbnode, n_turb, n_tot, eqn_set
    integer, intent(in) :: nnodes0, nnodes01, ibc

    integer,     dimension(nbnode),         intent(in)    :: ibnode
    real(dp),    dimension(nbnode),         intent(in)    :: slen_wall
    real(dp),    dimension(nbnode),         intent(in)    :: omega_wf

    real(dp), dimension(n_turb,nnodes01),intent(inout) :: turb
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode

    integer :: i,inode

    real(dp), dimension(n_tot)  :: qlocal
    real(dp), dimension(n_turb) :: turb_wall

    continue

    if ( eqn_set /= incompressible .and. eqn_set /= compressible ) then
      call lmpi_conditional_stop(1,'bc_stressomega_set_walls: only in/compr pg')
    end if

    do i = 1,nbnode
      inode = ibnode(i)
      if (inode <= nnodes0) then

        qlocal = qnode(1:n_tot,inode)

        call wall_turbulence_stressomega( eqn_set, ibc, slen_wall(i), qlocal,  &
                                     turb_wall, omega_wf(i) )

        turb(1:6,inode) = turb_wall(1:6)
        turb(7  ,inode) = turb_wall(7  )

      end if
    end do

  end subroutine bc_stressomega_set_walls

!======================== WALL_TURBULENCE_STRESSOMEGA ========================80
!
!  Sets quantities on viscous walls for RSM
!
!=============================================================================80

  subroutine wall_turbulence_stressomega( eqn_set, ibc, slen_wall, qnode,      &
                                     turb_wall, omega_wf )

    use fluid,          only : gamma, sutherland_constant
    use info_depr,      only : tref, xmach, Re
    use bc_names,       only : viscous_wall_rough, viscous_wall_function
    use solution_types, only : compressible
    use turb_rsm_const, only : beta_0

    integer, intent(in) :: eqn_set, ibc

    real(dp),               intent(in)  :: slen_wall
    real(dp),               intent(in)  :: omega_wf
    real(dp), dimension(:), intent(in)  :: qnode
    real(dp), dimension(:), intent(out) :: turb_wall

    real(dp) :: dymin,rho,p
    real(dp) :: temp,rmu,cstar
    real(dp) :: my_xmach, xmr

    continue

    cstar = sutherland_constant / tref
    my_xmach = zero

    my_xmach = one
    if (eqn_set == compressible ) my_xmach = xmach

    xmr = my_xmach/Re

    dymin = slen_wall

    if (eqn_set == compressible) then
      rho  = qnode(1)
      p    = qnode(5)
      temp = gamma * p / rho
      rmu  = viscosity_law( cstar, temp )
    else
      rmu  = one
    end if
    turb_wall(1:6) = zero
    if (turbulence_model_int == WilcoxRSM_w2006c) then
!     note that turb(7) is rho*omega (so use mu rather than nu=mu/rho)
      turb_wall(7) = 60.0_dp*rmu/beta_0*(xmr/dymin)**2
    else if (turbulence_model_int == WilcoxRSM_w2006 .or.                    &
             turbulence_model_int == SSGLRR_RSM_w2012_SD .or.                &
             turbulence_model_int == SSGLRR_RSM_w2012 ) then
!     note that turb(7) is omega
      if ( ibc==viscous_wall_function ) then
        turb_wall(7) = omega_wf
      else
        turb_wall(7) = 60.0_dp*rmu/rho/beta_0*(xmr/dymin)**2
      end if
    else
      write(*,*) 'Model WilcoxRSM-w2006 or WilcoxRSM-w2006c or'
      write(*,*) 'SSGLRR-RSM-w2012-SD or SSGLRR-RSM-w2012 expected'
      call lmpi_die
    end if
!  rough wall
    if ( ibc==viscous_wall_rough ) turb_wall(7) = turb_wall(7)/100.0_dp

    if ( verbose )then
    write(6,'(i5,a,15(1x,es20.10))') lmpi_id,' wallwall',rho,p,turb_wall(7)
    endif

  end subroutine wall_turbulence_stressomega

!============================== SOURCE_STRESSOMEGA ===========================80
!
! Source terms for RSM, both compress and incompress perfect gas
!
!=============================================================================80
    function source_stressomega ( i, nnodes01, n_tot, n_turb, n_grd            &
                       , vol, qnode, turb, gradx, grady, gradz                 &
                       , xmr, sst_f1 )

    use kinddefs,       only : dp
!   use fluid,          only : gamma, sutherland_constant
!   use info_depr,      only : tref
    use debug_defs,     only : test_freestream
    use turb_rsm_const, only : alpha,beta_star,alpha_hat,beta_hat              &
                              ,gamma_hat,sigma_d0,c_1,beta_0                   &
                              ,alpha_o,beta_o,sigma_d_o,c1_o,c1star_o,c2_o     &
                              ,c3_o,c3star_o,c4_o,c5_o                         &
                              ,alpha_e,beta_e,sigma_d_e,c1_e,c1star_e,c2_e     &
                              ,c3_e,c3star_e,c4_e,c5_e

    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: n_tot
    real(dp), dimension(n_turb)                         :: source_stressomega

    integer,                              intent(in)    :: i
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: n_grd

    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp),                             intent(in)    :: xmr
    real(dp), dimension(nnodes01),        intent(in)    :: sst_f1

    real(dp)                 :: xmrinv
    real(dp)                 :: rho_conditional, rhoinv_conditional

    real(dp), dimension(3,3) :: sij0      ! strain rate tensor
    real(dp), dimension(3,3) :: sij       ! traceless strain rate tensor
    real(dp), dimension(3,3) :: gradv     ! velocity gradients
    real(dp), dimension(3,3) :: wij       ! rotation tensor
    real(dp), dimension(3,3) :: sij_hat   ! Wilcox's special strain rate
    real(dp)                 :: div

    real(dp)                 :: tke
    real(dp)                 :: ome

    real(dp), dimension(3,3) :: tau
    real(dp), dimension(3,3) :: bij,aij
    real(dp), dimension(6)   :: prdij,aikakj,as_tensor,aw_tensor
    real(dp), dimension(3)   :: dtkedx
    real(dp), dimension(3)   :: domedx

    ! vector index to tensor index translator
    integer,  parameter:: idxto33_i(6) = (/1,2,3,1,1,2/)
    integer,  parameter:: idxto33_j(6) = (/1,2,3,2,3,3/)
    ! Kronecker delta
    real(dp), parameter:: krndelta(3,3) = RESHAPE( &
         (/1.,0.,0.,0.,1.,0.,0.,0.,1./),(/3,3/))

    integer  :: m,n, icur,jcur,kcur
    real(dp) :: tsum
    real(dp) :: prod_k
    real(dp) :: prod_ome
    real(dp) :: dissip
    real(dp) :: pij
    real(dp) :: dij
    real(dp) :: pi_ij
    real(dp) :: c1term
    real(dp) :: beta
    real(dp) :: f_beta
    real(dp) :: x_w
    real(dp) :: kdotw
    real(dp) :: sd_term
    real(dp) :: eps
    real(dp) :: aklakl
    real(dp) :: aklskl
    real(dp) :: aik_term
    real(dp) :: c1_use
    real(dp) :: alpha_hat_use
    real(dp) :: beta_hat_use
    real(dp) :: gamma_hat_use
    real(dp) :: delta_hat_use
    real(dp) :: sigma_hat_use
    real(dp) :: as_term
    real(dp) :: ws_term
    real(dp) :: sij_term
    real(dp) :: pkk_aij_term
    real(dp) :: eps_aikakj_term
    real(dp) :: alpha_use
    real(dp) :: beta_use
    real(dp) :: sigma_d_use

    real(dp), parameter :: zero       = 0.0_dp
    real(dp), parameter :: half       = 0.5_dp
    real(dp), parameter :: one        = 1.0_dp
    real(dp), parameter :: two        = 2.0_dp
    real(dp), parameter :: three      = 3.0_dp
    real(dp), parameter :: twothirds  = 2.0_dp / 3.0_dp
    real(dp), parameter :: eightyfive = 85.0_dp
    real(dp), parameter :: hundred    = 100.0_dp

  continue


!---------------------------------  flow variables----------------------------80
    if (turbulence_model_int == WilcoxRSM_w2006c) then
      rho_conditional    = qnode(1,i)
      rhoinv_conditional = one/qnode(1,i)
    else if (turbulence_model_int == WilcoxRSM_w2006 .or.                    &
             turbulence_model_int == SSGLRR_RSM_w2012_SD .or.                &
             turbulence_model_int == SSGLRR_RSM_w2012 ) then
      rho_conditional    = one
      rhoinv_conditional = one
    else
      rho_conditional    = zero ! compiler warning
      rhoinv_conditional = zero ! compiler warning
      write(*,*) 'Model WilcoxRSM-w2006 or WilcoxRSM-w2006c or'
      write(*,*) 'SSGLRR-RSM-w2012-SD or SSGLRR-RSM-w2012 expected'
      call lmpi_die
    end if
    xmrinv      = one / xmr
    tke = -half*(turb(1,i)+turb(2,i)+turb(3,i))*rhoinv_conditional
    ome = turb(7,i)*rhoinv_conditional

!--------------------------------- gradients ---------------------------------80
    gradv = set_gradv( gradx(2,i), grady(2,i), gradz(2,i)                      &
                     , gradx(3,i), grady(3,i), gradz(3,i)                      &
                     , gradx(4,i), grady(4,i), gradz(4,i) )
    sij0    = get_sij( gradv )
    wij     = get_wij( gradv )
    div     = sij0(1,1)+sij0(2,2)+sij0(3,3)

    sij_hat = sij0
    sij_hat(1,1) = sij_hat(1,1) -half*div
    sij_hat(2,2) = sij_hat(2,2) -half*div
    sij_hat(3,3) = sij_hat(3,3) -half*div

    sij      = sij0
    sij(1,1) = sij(1,1) -div/three
    sij(2,2) = sij(2,2) -div/three
    sij(3,3) = sij(3,3) -div/three

    tau(1,1) = turb(1,i)*rhoinv_conditional
    tau(2,2) = turb(2,i)*rhoinv_conditional
    tau(3,3) = turb(3,i)*rhoinv_conditional
    tau(1,2) = turb(4,i)*rhoinv_conditional
    tau(1,3) = turb(5,i)*rhoinv_conditional
    tau(2,3) = turb(6,i)*rhoinv_conditional
    tau(2,1) = tau(1,2)
    tau(3,1) = tau(1,3)
    tau(3,2) = tau(2,3)

!   note: gradients are of "primitive" turb variables
    dtkedx(1)= -(gradx(n_grd-6,i)+gradx(n_grd-5,i)+gradx(n_grd-4,i))
    dtkedx(2)= -(grady(n_grd-6,i)+grady(n_grd-5,i)+grady(n_grd-4,i))
    dtkedx(3)= -(gradz(n_grd-6,i)+gradz(n_grd-5,i)+gradz(n_grd-4,i))
    dtkedx   = dtkedx * half
    domedx   = (/gradx(n_grd,i), grady(n_grd,i), gradz(n_grd,i)/)
    bij = tau

    bij(1,1) = bij(1,1) + twothirds * tke
    bij(2,2) = bij(2,2) + twothirds * tke
    bij(3,3) = bij(3,3) + twothirds * tke

    aij = zero
! calculate aij tensor
    if (tke <= zero ) then
      aij(1,1) = -twothirds
      aij(2,2) = -twothirds
      aij(3,3) = -twothirds
    else
      aij(1,1) = -turb(1,i)*rhoinv_conditional/tke-twothirds
      aij(2,2) = -turb(2,i)*rhoinv_conditional/tke-twothirds
      aij(3,3) = -turb(3,i)*rhoinv_conditional/tke-twothirds
      aij(1,2) = -turb(4,i)*rhoinv_conditional/tke
      aij(1,3) = -turb(5,i)*rhoinv_conditional/tke
      aij(2,3) = -turb(6,i)*rhoinv_conditional/tke
      aij(2,1) = aij(1,2)
      aij(3,1) = aij(1,3)
      aij(3,2) = aij(2,3)
    end if
! second invariant of a_ij (sometimes referred to as A2):
    aklakl = aij(1,1)**2 + aij(2,2)**2 + aij(3,3)**2 +                         &
             two*(aij(1,2)**2 + aij(1,3)**2 + aij(2,3)**2)
! this is a_kl*S_kl:
    aklskl = aij(1,1)*sij0(1,1) + aij(2,2)*sij0(2,2) + aij(3,3)*sij0(3,3) +    &
             two*(aij(1,2)*sij0(1,2) + aij(1,3)*sij0(1,3) + aij(2,3)*sij0(2,3))

! calculate production term, a_ik*a_kj, aS related tensor, and
! aW related tensor
    do m=1, 6
       icur= idxto33_i(m)
       jcur= idxto33_j(m)
       prdij(m) = zero
       aikakj(m)= zero
       as_tensor(m)=zero
       aw_tensor(m)=zero
       do n=1,3
          prdij(m) = tau(icur,n)*gradv(jcur,n)                                 &
                   + tau(jcur,n)*gradv(icur,n)+prdij(m)
          aikakj(m) = aikakj(m) + aij(icur,n)*aij(jcur,n)
          as_tensor(m) = as_tensor(m) +                                        &
                   aij(icur,n)*sij0(jcur,n) + aij(jcur,n)*sij0(icur,n)
          aw_tensor(m) = aw_tensor(m) +                                        &
                   aij(icur,n)*wij(jcur,n) + aij(jcur,n)*wij(icur,n)
       enddo
    enddo

    prod_k = (prdij(1)+prdij(2)+prdij(3))*half
    as_tensor(1:3) = as_tensor(1:3) - twothirds*aklskl

    eps= beta_star*ome*tke

!  Note: pressure-strain correlation (pi_ij) can be written in terms of
!  Pij and Dij, or equivalently in terms of basis tensors
!  We'll do it the latter method here, for easier future expansion
!
!  k(a_ik*S_jk + a_jk*S_ik) = -(P_ij + D_ij)/2 -4kS_ij/3
!  k(a_ik*W_jk + a_jk*W_ik) = -(P_ij - D_ij)/2
!  k*a_lm*S_lm = -P_kk/2
!
!  (the constants change, depending how it is written)
!
!  where a_ij=-R_ij/k - 2*del_ij/3  (turb(1:6) = -R_ij for primitive)
!  1: 1,1    2: 2,2   3: 3,3   4: 1,2   5: 1,3   6: 2,3
!
    do m=1,6
       icur= idxto33_i(m)
       jcur= idxto33_j(m)
       dij = zero
       pij = prdij(m)
!  Dij (needed only if write pi_ij in terms of Pij and Dij)
       do n=1,3
          dij = dij + tau(icur,n)*gradv(n,jcur) + tau(jcur,n)*gradv(n,icur)
       enddo
       pij = pij - twothirds*krndelta(icur,jcur)*prod_k
       dij = dij - twothirds*krndelta(icur,jcur)*prod_k

       aik_term = aikakj(m)
       if(m<=3) then
         aik_term = aik_term - aklakl/three
       endif

! Note: other way to write pi_ij (using Pij & Dij, not using basis tensors):
!      c1term = xmrinv*c_1*ome*bij(icur,jcur)*beta_star
!      alpha_hat_use = alpha_hat *pij
!      beta_hat_use  =  beta_hat *dij
!      gamma_hat_use =  gamma_hat*tke*sij(icur,jcur)
!      pi_ij = c1term - alpha_hat_use - beta_hat_use - gamma_hat_use

       if (turbulence_model_int == WilcoxRSM_w2006 .or. &
           turbulence_model_int == WilcoxRSM_w2006c ) then
         c1_use = c_1
         alpha_hat_use =alpha_hat+beta_hat
         beta_hat_use  =alpha_hat-beta_hat
         gamma_hat_use =4.0_dp/3.0_dp*(alpha_hat+beta_hat)-gamma_hat
         delta_hat_use =zero
         sigma_hat_use =zero
       else     ! SSGLRR-RSM-w2012-SD and SSGLRR-RSM-w2012
         c1_use        = sst_f1(i)*c1_o + (1.-sst_f1(i))*c1_e
         alpha_hat_use = sst_f1(i)*c4_o+(1.-sst_f1(i))*c4_e
         beta_hat_use  = sst_f1(i)*c5_o+(1.-sst_f1(i))*c5_e
         gamma_hat_use = sst_f1(i)*(c3_o-c3star_o*sqrt(aklakl)) +              &
                         (1.-sst_f1(i))*(c3_e-c3star_e*sqrt(aklakl))
         delta_hat_use = sst_f1(i)*c1star_o+(1.-sst_f1(i))*c1star_e
         sigma_hat_use = sst_f1(i)*c2_o+(1.-sst_f1(i))*c2_e
       end if

! pi_ij (pressure-strain) components
       c1term = xmrinv*c1_use*ome*bij(icur,jcur)*beta_star
       as_term  = alpha_hat_use*tke*as_tensor(m)
       ws_term  = beta_hat_use*tke*aw_tensor(m)
       sij_term = gamma_hat_use*tke*sij(icur,jcur)
       pkk_aij_term = -delta_hat_use*prod_k*aij(icur,jcur)
       eps_aikakj_term = xmrinv*sigma_hat_use*eps*aik_term

! pi_ij (pressure-strain) term
       pi_ij = (c1term + sij_term + as_term + ws_term +                        &
               pkk_aij_term + eps_aikakj_term)

! form source term
       dissip =  twothirds * xmrinv*eps*krndelta(icur,jcur)
       source_stressomega(m) = (-prdij(m)-pi_ij+dissip)*rho_conditional        &
                               *vol(i)
    enddo

    if (turbulence_model_int == WilcoxRSM_w2006 .or. &
    turbulence_model_int == WilcoxRSM_w2006c ) then
      alpha_use    = alpha
      beta_use     = beta_0
      sigma_d_use  = sigma_d0
      tsum = zero
      do icur = 1,3
         do jcur = 1,3
            do kcur = 1,3
               tsum = tsum + wij(icur,jcur)*sij_hat(kcur,icur)*                &
                       wij(jcur,kcur)
            enddo
         enddo
      enddo
      x_w = xmr**3 *abs( tsum/((beta_star*ome)**3))
      f_beta  = (one + eightyfive*x_w)/(one+hundred*x_w)
      beta = beta_use*f_beta
    else     ! SSGLRR-RSM-w2012-SD and SSGLRR-RSM-w2012
      alpha_use    = sst_f1(i)*alpha_o + (1.-sst_f1(i))*alpha_e
      beta_use     = sst_f1(i)*beta_o + (1.-sst_f1(i))*beta_e
      sigma_d_use  = sst_f1(i)*sigma_d_o + (1.-sst_f1(i))*sigma_d_e
      beta = beta_use
    end if

    prod_ome  = zero
    if ( tke > tiny(1.0_dp) ) then
      prod_ome  = alpha_use * ( ome / tke ) * prod_k
    end if

    dissip = xmrinv * beta * ome**2

    ! cross diffusion term
    kdotw = dtkedx(1)*domedx(1)+dtkedx(2)*domedx(2)+dtkedx(3)*domedx(3)
    sd_term  = sigma_d_use * xmr /ome * max(zero, kdotw)

    source_stressomega(7)=(prod_ome - dissip + sd_term )*rho_conditional*vol(i)

!write(6,'(a,i8,10(1x,es15.5))') 'source:...', i,turb(1:3,i),tke,ome

    if (test_freestream) then
       source_stressomega = zero
    end if

  end function source_stressomega

!========================= JACOB_SOURCE_STRESSOMEGA ==========================80
!
!
!=============================================================================80
  function jacob_source_stressomega( i, eqn_set, nnodes01                      &
                          , turb, qnode, n_turb, n_tot, sst_f1 )               &
    result ( d_source_jac)

    use kinddefs,       only : dp
    use info_depr,      only : xmach, re
    use debug_defs,     only : test_freestream
    use solution_types, only : compressible, incompressible

    use turb_rsm_const, only : beta_star, c_1, beta_0, c1_o, c1_e,             &
                               beta_o, beta_e

    integer, intent(in) :: i
    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: nnodes01

    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: sst_f1

    real(dp), dimension(n_turb, n_turb)                        :: d_source_jac

    real(dp) :: rho
    real(dp) :: tke
    real(dp) :: ome
    real(dp) :: xmr
    real(dp) :: xmrinv
    real(dp) :: c1_use
    real(dp) :: beta_use

    real(dp) :: my_xmach

    real(dp), dimension(6)   :: tij

    integer :: m

    real(dp), parameter :: zero       = 0.0_dp
    real(dp), parameter :: half       = 0.5_dp
    real(dp), parameter :: onethird   = 1.0_dp / 3.0_dp
    real(dp), parameter :: twothirds  = 2.0_dp / 3.0_dp
    real(dp), parameter :: one        = 1.0_dp
    real(dp), parameter :: two        = 2.0_dp

  continue

!   Note a lot of "exact" Jacobian coding was deleted 9/24/2013
!   around rev 68012, because it was not being used

!   primitive variables
    rho         = qnode(1,i)
    if (turbulence_model_int == WilcoxRSM_w2006c) then
      tke         = -sum(turb(1:3,i))*half/rho
      ome         = turb(7,i)/rho
      tij         = turb(:,i)/rho
    else if (turbulence_model_int == WilcoxRSM_w2006 .or.                    &
             turbulence_model_int == SSGLRR_RSM_w2012_SD .or.                &
             turbulence_model_int == SSGLRR_RSM_w2012 ) then
      tke         = -sum(turb(1:3,i))*half
      ome         = turb(7,i)
      tij         = turb(:,i)
    else
      tke = zero  ! compiler warning
      ome = zero  ! compiler warning
      write(*,*) 'Model WilcoxRSM-w2006 or WilcoxRSM-w2006c or'
      write(*,*) 'SSGLRR-RSM-w2012-SD or SSGLRR-RSM-w2012 expected'
      call lmpi_die
    end if

!   When using the edge-based terms, we only need to visit this routine
!   one time

    my_xmach = zero

!---------------------------------  equation set -----------------------------80
    select case ( eqn_set )
    case ( compressible )
      my_xmach    = xmach
    case ( incompressible )
      my_xmach    = one
    case default
      call lmpi_conditional_stop(1,'turb_jacob: only for in/compress pg')
    end select

    xmr    = my_xmach / re
    xmrinv = one /xmr

    if (turbulence_model_int == WilcoxRSM_w2006 .or. &
        turbulence_model_int == WilcoxRSM_w2006c ) then
      c1_use = c_1
    else     ! SSGLRR-RSM-w2012-SD and SSGLRR-RSM-w2012
      c1_use = sst_f1(i)*c1_o + (1.-sst_f1(i))*c1_e
    end if

    ! Do the Jacobians approximately, using
    ! destruction term and Rotta slow part of pressure-strain term.
    ! Note: originally, the "do m=1,3" loop was not linearized correctly
    ! (see commented out section below), but it sometimes worked better when
    ! run from scratch.
    ! (Ultimately, linearization may be better achieved via
    ! ddt method used elsewhere in fun3d.)
    d_source_jac = zero
    do m=1,3
        d_source_jac(m,m) = -ome*beta_star*xmrinv*c1_use*twothirds             &
                            -onethird*beta_star*ome*xmrinv
        d_source_jac(m,7) = (twothirds*(one-c1_use)*tke - tij(m)*c1_use)       &
                            *xmrinv*beta_star
    enddo
!   do m=1,3
!       d_source_jac(m,m) = -(onethird*(1.+c_1)+1.)*ome*beta_star*xmrinv
!       d_source_jac(m,7) = (twothirds*(1.+c_1)*tke - tij(m))*xmrinv*beta_star
!   enddo
    do m=4,6
        d_source_jac(m,m) = -ome*beta_star*xmrinv*c1_use
        d_source_jac(m,7) = (-tij(m))*xmrinv*beta_star*c1_use
    enddo
    !=========================================================================80
    !                Omega Equation
    !=========================================================================80
    ! dissipation term (ignores f_beta for WilcoxRSMs)
    if (turbulence_model_int == WilcoxRSM_w2006 .or. &
    turbulence_model_int == WilcoxRSM_w2006c ) then
      beta_use     = beta_0
    else     ! SSGLRR-RSM-w2012-SD and SSGLRR-RSM-w2012
      beta_use     = sst_f1(i)*beta_o + (1.-sst_f1(i))*beta_e
    end if
    d_source_jac(7,7)   = -xmrinv * beta_use *two *ome

    if (test_freestream) then
       d_source_jac = zero
    end if

    d_source_jac = -d_source_jac

  end function jacob_source_stressomega

  include 'viscosity_law.f90'
  include 'get_sij.f90'
  include 'get_wij.f90'
  include 'set_gradv.f90'

end module turb_7eqn

!================================== KEEP_TURB ================================80
!
! Decides to keep or throw away old (restart) values for turbulence depending
! on whether the new choice of turb model is closely-related to the old model
! (e.g. sa-des and sa are releated, but sa and sst are not)
!
!=============================================================================80

  pure function keep_turb_data(ivisc_file, ivisc)

    integer, intent(in) :: ivisc_file, ivisc

    logical             :: keep_turb_data

  continue

    keep_turb_data = ( ivisc_file == ivisc)

    if (((ivisc_file ==  8) .and. (ivisc == 10)) .or.                          &
        ((ivisc_file ==  8) .and. (ivisc == 12)) .or.                          &
        ((ivisc_file ==  9) .and. (ivisc ==  8)) .or.                          &
        ((ivisc_file ==  8) .and. (ivisc ==  9)) .or.                          &
        ((ivisc_file == 10) .and. (ivisc ==  8)) .or.                          &
        ((ivisc_file == 12) .and. (ivisc ==  8)) .or.                          &
        ((ivisc_file ==  6) .and. (ivisc == 60)) .or.                          &
        ((ivisc_file == 60) .and. (ivisc ==  6)) .or.                          &
        ((ivisc_file ==  6) .and. (ivisc ==  7)) .or.                          &
        ((ivisc_file ==  7) .and. (ivisc ==  6))) keep_turb_data = .true.

  end function keep_turb_data

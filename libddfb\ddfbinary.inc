! USE, INTRINSIC :: ISO_C_BINDING

INTERFACE

  FUNCTION writeBinaryDDFHeader(fname, title, nzone, solutionTime)             &
             BIND(C,NAME='writeBinaryDDFHeader')
    IMPORT
    IMPLICIT NONE
    INTEGER(C_SIZE_T)                             :: writeBinaryDDFHeader
    CHARACTER(C_CHAR), DIMENSION(*),   INTENT(IN) :: fname
    CHARACTER(C_CHAR), DIMENSION(80),  INTENT(IN) :: title
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: nzone
    REAL(C_DOUBLE), VALUE,             INTENT(IN) :: solutionTime
  END FUNCTION writeBinaryDDFHeader

  FUNCTION writeBinaryDDFZoneHeader(title, ztype, npts, nfunc, nelem)          &
             BIND(C,NAME='writeBinaryDDFZoneHeader')
    IMPORT
    IMPLICIT NONE
    INTEGER(C_SIZE_T)                             :: writeBinaryDDFZoneHeader
    CHARACTER(C_CHAR), DIMENSION(80),  INTENT(IN) :: title
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: ztype
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: npts
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: nelem
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: nfunc
  END FUNCTION writeBinaryDDFZoneHeader

  FUNCTION writeBinaryDDFPoint(xval, yval, zval, pid, setid, func)             &
             BIND(C,NAME='writeBinaryDDFPoint')
    IMPORT
    IMPLICIT NONE
    INTEGER(C_SIZE_T)                             :: writeBinaryDDFPoint
    REAL(C_DOUBLE),  VALUE,            INTENT(IN) :: xval
    REAL(C_DOUBLE),  VALUE,            INTENT(IN) :: yval
    REAL(C_DOUBLE),  VALUE,            INTENT(IN) :: zval
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: pid
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: setid
    TYPE(C_PTR),     VALUE,            INTENT(IN) :: func
  END FUNCTION writeBinaryDDFPoint

  FUNCTION writeBinaryDDFElement(npe, id, pid, setid, elem)                    &
             BIND(C,NAME='writeBinaryDDFElement')
    IMPORT
    IMPLICIT NONE
    INTEGER(C_SIZE_T)                             :: writeBinaryDDFElement
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: npe
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: id
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: pid
    INTEGER(C_LONG), VALUE,            INTENT(IN) :: setid
    TYPE(C_PTR),     VALUE,            INTENT(IN) :: elem
  END FUNCTION writeBinaryDDFElement

  FUNCTION readBinaryDDFHeader(fname, title, version, nzone, solutionTime)     &
             BIND(C,NAME='readBinaryDDFHeader')
    IMPORT
    IMPLICIT NONE
    INTEGER(C_SIZE_T)                             :: readBinaryDDFHeader
    CHARACTER(C_CHAR), DIMENSION(*),  INTENT(IN)  :: fname
    CHARACTER(C_CHAR), DIMENSION(80), INTENT(OUT) :: title
    CHARACTER(C_CHAR), DIMENSION(20), INTENT(OUT) :: version
    INTEGER(C_LONG),                  INTENT(OUT) :: nzone
    REAL(C_DOUBLE),                   INTENT(OUT) :: solutionTime
  END FUNCTION readBinaryDDFHeader

  FUNCTION readBinaryDDFZoneHeader(title, ztype, npts, nfunc, nelem)           &
             BIND(C,NAME='readBinaryDDFZoneHeader')
    IMPORT
    IMPLICIT NONE
    INTEGER(C_SIZE_T)                               :: readBinaryDDFZoneHeader
    CHARACTER(C_CHAR), DIMENSION(80), INTENT(OUT)   :: title
    INTEGER(C_LONG),                  INTENT(OUT)   :: ztype
    INTEGER(C_LONG),                  INTENT(OUT)   :: npts
    INTEGER(C_LONG),                  INTENT(OUT)   :: nfunc
    INTEGER(C_LONG),                  INTENT(OUT)   :: nelem
  END FUNCTION readBinaryDDFZoneHeader

  FUNCTION readBinaryDDFPoint(xval, yval, zval, pid, setid, func)              &
             BIND(C,NAME='readBinaryDDFPoint')
    IMPORT
    IMPLICIT NONE
    INTEGER(C_SIZE_T)                               :: readBinaryDDFPoint
    REAL(C_DOUBLE),                   INTENT(OUT)   :: xval
    REAL(C_DOUBLE),                   INTENT(OUT)   :: yval
    REAL(C_DOUBLE),                   INTENT(OUT)   :: zval
    INTEGER(C_LONG),                  INTENT(OUT)   :: pid
    INTEGER(C_LONG),                  INTENT(OUT)   :: setid
    TYPE(C_PTR),     VALUE,           INTENT(IN)    :: func
  END FUNCTION readBinaryDDFPoint

  FUNCTION readBinaryDDFElement(npe, id, pid, setid, elem)                     &
             BIND(C,NAME='readBinaryDDFElement')
    IMPORT
    IMPLICIT NONE
    INTEGER(C_SIZE_T)                               :: readBinaryDDFElement
    INTEGER(C_LONG),                  INTENT(OUT)   :: npe
    INTEGER(C_LONG),                  INTENT(OUT)   :: id
    INTEGER(C_LONG),                  INTENT(OUT)   :: pid
    INTEGER(C_LONG),                  INTENT(OUT)   :: setid
    TYPE(C_PTR),     VALUE,           INTENT(IN)    :: elem
  END FUNCTION readBinaryDDFElement

  FUNCTION readBinaryDDFRewindZone()                                           &
             BIND(C,NAME='readBinaryDDFRewindZone')
    IMPORT
    IMPLICIT NONE
    INTEGER(C_SIZE_T)                               :: readBinaryDDFRewindZone
  END FUNCTION readBinaryDDFRewindZone

  FUNCTION readBinaryDDFComplete()                                             &
             BIND(C,NAME='readBinaryDDFComplete')
    IMPORT
    IMPLICIT NONE
    INTEGER(C_SIZE_T)                               :: readBinaryDDFComplete
  END FUNCTION readBinaryDDFComplete

END INTERFACE

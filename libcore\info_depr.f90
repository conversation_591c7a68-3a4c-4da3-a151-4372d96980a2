! There used to be a module or common block or something called 'info'.
! We want everything that was in the old info to find a home in some other,
! more appropriate, module.
! So info was renamed info_depr, meaning to please move everything from
! here to somewhere else, so that this module can be deprecated (deleted).

module info_depr

  use kinddefs, only : dp

  implicit none

  private

  public :: zero_bnd_grd, embed_grid, ebv_tets
  public :: re, alpha, tref, res0, resc, xmach, xmre, beta, yaw
  public :: simulation_time, write_mesh, write_mesh_project, force_stream_file
  public :: write_mesh_freq
  public :: ivisc, ntt, ivgrd, no_unreal_recons
  public :: subiteration, ncyc, write_usm3d_flo
  public :: epscoef, use_full_update, adapt
  public :: physical_timestep
  public :: mixed, mixed_present, ns_recon, tightly_couple
  public :: complex_mode, use_full_turb_update
  public :: time_timestep_loop
  public :: lowmach_prec
  public :: partial_pivoting
  public :: prec_mach_star, adptv_entropy_fix
  public :: prec_mach_trans1, prec_mach_trans2, prec_alt, prec_ff_bc
  public :: reduce_shape_change, reduce_factor
  public :: title, twod
  public :: new_umuscl, prec_dtau_skip
  public :: kappa_umuscl
  public :: complex_to_perturb, complex_grid_point, complex_epsilon
  public :: make_tets, no_renum
  public :: pp_cmd_outformat, interleaf
  public :: pp_cmd_color, pp_cmd_wgtflag, pp_cmd_ubvec, pp_cmd_cuthill_ordering
  public :: pp_cmd_nnparts, pp_cmd_stats, pp_cmd_PM_use_proc, pp_cmd_use64
  public :: pp_cmd_skip_do_min
  public :: cc_primal, cc_clip_weights, cc_centroids
  public :: print_conditional, skeleton, testing
  public :: restart_run_parameters, version_restart_old_file
  public :: grad_x_y_z_contents, use_edge_gradients
  public :: mirror_x, mirror_y, mirror_z
  public :: version
  public :: wls_inv_terms, wls_inv_dist_scale, gg_inv_terms
  public :: kappa2, kappa4, serialize_partitioner, metis_numbering_entry
  public :: multidm_option, fixed_direction, recalc_dir_freq

  public :: ngrid
  public :: fmg_level
  public :: relaxation_work
  public :: pr_limiter, pr_limiter_coeff
  public :: wall_limit_less
  public :: track_nozzle_performance, track_group_forces
  public :: local_moment_centers

  public :: soft_init

  public :: scale_grid
  public :: scale_grid_factor_x, scale_grid_factor_y, scale_grid_factor_z

! RECONSTRUCTION MODULE

  logical :: ns_recon      = .false.   ! Use nonsimp edges in reconstruction
  logical :: fixed_direction = .true.  ! Use Cartesian directions in multidm
  integer :: recalc_dir_freq = 1       ! Freq to recalculate dir in multidm
  integer :: multidm_option  = 1       ! 1 - virtual node averaging
                                       ! 2 - weighted average of edges

! FLUX MODULE

  integer  :: ivgrd = 0               ! Using a bad mesh?
  integer  :: no_unreal_recons = 0    ! No. of unrealizable reconstructions
  real(dp) :: kappa_umuscl = -1.0_dp  ! Set < 0 to allow default checks
  ! For UMUSCL scheme; kappa_umuscl = 0 gives legacy fun3d reconstruction
  logical  :: new_umuscl = .false.    ! Use original UMUSCL formulation
  logical  :: zero_bnd_grd = .false.
  logical  :: lowmach_prec  = .false. ! Use low Mach preconditioning
  logical  :: wls_inv_terms = .false. ! Use dist wghtd LSG for inv terms
  logical  :: gg_inv_terms = .false.  ! Use Green Gauss for inv terms
  logical  :: ebv_tets = .false.      ! Use edge-based viscous terms
  real(dp) :: wls_inv_dist_scale = 1.00_dp  ! the power of the inv dist

! low speed preconditioning parameters
! beginNeverComplex
  real(dp) :: prec_mach_star   = 1.0_dp     ! nominal Mach number
  real(dp) :: prec_mach_trans1 = 1.0_dp     ! first transition Mach
  real(dp) :: prec_mach_trans2 = 1.0_dp     ! second transition Mach

  real(dp) :: kappa2 = 0.25_dp       ! coefficient in front of the 2nd diff
                                     ! for central difference
  real(dp) :: kappa4 = 0.015625_dp   ! coefficient in front of the 4th diff
                                     ! for central difference
! endNeverComplex
  integer :: prec_alt     = 0              ! preconditioning alternates
                                           ! =0, nominal ; =1, alternate
  integer :: prec_dtau_skip = 0            ! =0, apply LHS preconditioning
                                           ! =1, skip LHS preconditioning
  integer :: prec_ff_bc = 0                ! Use special farfield bc
                                           ! =0, standard ; =1, special

  logical  :: adptv_entropy_fix  = .false.  ! Entropy fix for Roe
  logical  :: pr_limiter = .false.          ! Add a pressure based limiter
                                            ! for flows with strong shocks
  logical  :: wall_limit_less = .false.     ! Turn off "h" limiters near
                                            ! walls and in subsonic flow
  real(dp) :: pr_limiter_coeff = 0.5_dp     ! nominal scaling coefficient
                                            ! for the pressure limiter
  real(dp) :: epscoef = 1.0_dp              ! nominal scaling coefficient
                                            ! for the smooth limiter

! PHYSICS/CASE

  real(dp) :: re    = 1.0_dp    ! Reynolds number
  real(dp) :: alpha = 0.0_dp    ! Angle of attack
  real(dp) :: tref  = 1.0_dp    ! Reference temperature
  real(dp) :: xmach = 0.0_dp    ! Mach number
  real(dp) :: xmre  = 0.0_dp    ! Ratio of Mach number to Reynolds number
  real(dp) :: beta  = 0.0_dp    ! Artificial compressibility parameter
  real(dp) :: yaw   = 0.0_dp    ! Yaw angle
  integer  :: ivisc = 0         ! Viscous flag

! TWOD

  logical  :: twod     =  .false.     ! Strictly for node-centered path.

! MONITOR

  real(dp) :: res0  = 1.0_dp     ! Initial residual
  real(dp) :: resc  = 1.0_dp     ! Current residual
  real(dp) :: simulation_time = -1.0_dp ! Time-accurate time
                                        ! always inititialize to < 0
                                        ! (indicative of steady state)
  integer :: ntt = 1            ! Iteration counter
  integer :: ncyc = 0           ! Number of iterations to run

! MULTIGRID

  integer :: ngrid = 1               ! Number of multigrid levels
  integer :: fmg_level          = 1  ! FMG grid level

  real(dp), dimension(2) :: relaxation_work ! Fine grid work in relaxation

! NFE grid/partitioning parameters

  logical  :: make_tets       = .false. ! Convert mixed-elements to pure tets
  logical  :: no_renum        = .true.  ! Don't use Cuthill-Mckee renumbering
  logical  :: scale_grid      = .false. ! Scales grid

  real(dp) :: scale_grid_factor_x  = 1._dp ! Scale factor for grid in x
  real(dp) :: scale_grid_factor_y  = 1._dp ! Scale factor for grid in y
  real(dp) :: scale_grid_factor_z  = 1._dp ! Scale factor for grid in z

  logical  :: mirror_x        = .false. ! Don't mirror mesh in x
  logical  :: mirror_y        = .false. ! Don't mirror mesh in y
  logical  :: mirror_z        = .false. ! Don't mirror mesh in z

  logical  :: pp_cmd_outformat = .false. ! Write partition files in ASCII
  logical  :: pp_cmd_skip_do_min = .false.! Attempt do_min (pp_metis)
  integer  :: pp_cmd_color            = 1    ! 0: no; 1: color
  integer  :: pp_cmd_cuthill_ordering = 0    ! 1: force consistent
                                             !    cuthill ordering
  integer  :: pp_cmd_wgtflag          = 2    ! 2: cell and edge (degree)
  integer  :: pp_cmd_nnparts          = 0    ! 0: use lmpi_nproc
  integer  :: pp_cmd_PM_use_proc      = 0    ! > 0: # of PEs to call PM
  integer  :: pp_cmd_stats            = 0    ! 0: no stats; 1: stats
  integer  :: pp_cmd_use64            = 0    ! 0: 32; 1: 64
  real(dp) :: pp_cmd_ubvec            = 1.01_dp

  logical  :: reduce_shape_change = .false. ! Reduce requested gridmove amount
  logical  :: serialize_partitioner = .false. ! Use Metis instead of ParMetis
  integer  :: metis_numbering_entry = 17    ! Numbering entry in Metis .h file
  real(dp) :: reduce_factor                 ! Reduction factor for above
  real(dp) :: version_restart_old_file      ! version # of input restart file

  character(80) :: title          ! Case description

  logical :: version = .false.    ! print version and stop
  integer :: interleaf = 2        ! Write partitioned files in groups

  logical :: restart_run_parameters = .false. ! read a V4 restart file to
                                              ! extract and print the meta
                                              ! and command line data used
                                              ! on the previous run, then
                                              ! stop (only for V4 restarts)

  logical :: time_timestep_loop       = .false. ! Time the actual timestep loop

  logical :: track_group_forces       = .false. ! Track grouped forces
                                                ! using parameters from a
                                                ! namelist

  logical :: local_moment_centers     = .false. ! Call the local moments forces
                                                ! routine for grouped forces.

  logical :: track_nozzle_performance = .false. ! Track nozzle performance
                                                ! using parameters from a
                                                ! namelist

  logical :: soft_init          = .false. ! Initialize interior velocities
                                          ! to zero if true
  logical :: embed_grid         = .false. ! Globally embed grid & write out
  logical :: write_mesh         = .false. ! Write final mesh
  logical :: write_mesh_freq    = .false. ! Write new mesh frequently with .flow
  logical :: force_stream_file  = .false. ! Force raw_grid to use AFLR3 stream
  character(len=256) :: write_mesh_project = '' ! Project name for new mesh file
  logical :: write_usm3d_flo    = .false. ! Write USM3D .flo solution file

! LOQUATION CONTROL

  integer :: skeleton = 0                 ! printing control flag
  logical :: print_conditional  = .false. ! printing control flag
  logical :: testing = .false.            ! = .true. creates debug_verify
                                          ! files with initial residual/
                                          ! delta-q (extra computation/IO)

! Full Newton step for flow and turbulence

  logical :: use_full_update      = .false.
  logical :: use_full_turb_update = .false.
  logical :: tightly_couple  = .false. ! Tightly couple the turb model
                                           !   (flow)

! REFINE_ADAPTATION

  logical :: adapt = .false.      ! Engage grid adaptation

! LINEARSOLVE

  logical :: partial_pivoting = .false.  ! Partial pivoting in LU

! TIME ACCURATE ADJOINT

  integer :: physical_timestep      ! Tells adjoint step we're currently at
  integer :: subiteration           ! Tells adjoint step we're currently at

! SPATIAL DISCRETIZATION - NODE_CENTERED

  logical :: mixed  = .false.        ! Mixed element flag
  logical :: mixed_present = .false. ! Whether mixed is a CLO

  logical :: use_edge_gradients = .true. ! augment cell-average gradients
                                         ! with edge-gradients to improve
                                         ! h-ellipticity on non simplicial
                                         ! elements (cell-based only)

  character(80) :: grad_x_y_z_contents ! To double check gradients

! SPATIAL DISCRETIZATION - CELL_CENTERED

  logical :: cc_primal       = .false. ! Cell-centered flag for primal grids
  logical :: cc_clip_weights = .true.  ! Clipp weights if cc node avegaging
  logical :: cc_centroids    = .false. ! Use centroids as cell-centers

! COMPLEX

  logical :: complex_mode = .false. ! Running in complex-variable mode

  integer :: complex_to_perturb = 0 ! 0=none, 1=Mach, 2=AOA, 3=Grid, 100+=source
  integer :: complex_grid_point     ! Which grid point to perturb
! beginNeverComplex
  real(dp) :: complex_epsilon       ! Perturbation size for complex
! endNeverComplex

end module info_depr

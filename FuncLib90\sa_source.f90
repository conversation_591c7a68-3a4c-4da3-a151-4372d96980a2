!================================= SA_SOURCE =================================80
!
! This routine computes the turbulent source terms for S-A.
!
!=============================================================================80

  pure function sa_source( xmre, rnu, distance, turb, vgradx, vgrady, vgradz )

    use turb_parameters, only : t_prod, t_dest
    use turb_sa_const,   only : cv1, vkar, cw2, cw3, ct3, ct4, cb1, cw1,       &
                                turb_eps, dacles_mariani

    real(dp), intent(in) :: xmre, rnu, distance, turb

    real(dp), dimension(3), intent(in) :: vgradx, vgrady, vgradz

    real(dp) :: sa_source

    real(dp) :: ux, uy, uz, vx, vy, vz, wx, wy, wz, sij_mag
    real(dp) :: chi, dest, prod, gg, rr, s, sw, bot, xmre_d2, xmre_bot
    real(dp) :: ft2, fv1, fv2, fw, vkar2, turb_plus, turb_abs, sw_limited

    real(dp), parameter :: my_00001 = 0.00001_dp
    real(dp), parameter :: my_6th   = 1._dp/6._dp

  continue

    vkar2 = vkar * vkar
    bot   = vkar2 * distance * distance

    xmre_bot = xmre/bot
    xmre_d2  = xmre/( distance**2 )

    ux = vgradx(1)
    uy = vgrady(1)
    uz = vgradz(1)
    vx = vgradx(2)
    vy = vgrady(2)
    vz = vgradz(2)
    wx = vgradx(3)
    wy = vgrady(3)
    wz = vgradz(3)

    s  = sqrt( (wy-vz)**2 + (uz-wx)**2 + (vx-uy)**2 )
    if ( s <= 1.0e-8_dp ) s = 1.0e-8_dp

    if ( dacles_mariani ) then
      sij_mag   = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                        &
                        +2._dp*(ux**2+vy**2+wz**2))
      s = s + 2._dp*min(-1.0e-8_dp,sij_mag-s)
    end if

    chi = turb /rnu

    ft2 = ct3*exp(max(-700._dp,-ct4*chi*chi))
    !...avoid Intel -fpe0 exception exp(-700._dp)=  9.859676543759771E-305

    fv1 = chi**3 / ( chi**3 + cv1**3 )
    fv2 = 1._dp - chi/( 1._dp + chi*fv1 )

!   Form modified turbulence terms (turb >= 0)

    turb_abs  = aharten( turb, turb_eps )
    turb_plus = 0.5_dp*( turb + turb_abs )

    sw = s + xmre_bot*turb_abs*fv2

    !..sw_limited is used because of roundoff in the rr term.
    !..Originally FUN3D used the limited term in f_prod but this
    !  is not needed...truncation error studies...Lisbon workshop Oct 2008.
    sw_limited = max(sw,my_00001)

    rr     = xmre_bot * turb / sw_limited
    if (rr > 10.0_dp ) rr = 10.0_dp

    gg = rr + cw2*(rr**6-rr)
    fw = gg * ((1._dp+cw3**6)/(gg**6+cw3**6))**(my_6th)

    prod = cb1*( 1._dp - ft2 )*sw*turb_abs

    dest = xmre_d2 * ( cw1*fw - cb1/vkar2*ft2 )*( turb_plus )**2

    sa_source =  t_prod*prod - t_dest*dest

  end function sa_source

\section{Grid Adaptation}\label{s:grid_adaptation}

\FunThreeD implements metric-based adaptation, 
where grid adaptation is separated into two tasks.
The first step is to construct a metric that describes the desired
size and anisotropy of the adapted grid elements.
The second step is to produce an adapted grid that is based on
this metric.

Feature-based adaptation constructs the metric based on properties of the
flow solution.
Adjoint-based adaptation constructs the metric 
from the flow and adjoint solutions to reduce
estimated errors in a specified output function.
The namelist \file{&adapt_metric_construction}
(\sectionref{s:nml_adapt_metric_construction}) for specifying
details of the metric.

\FunThreeD supports a number of grid adaptation libraries.
The namelist \file{&adapt_mechanics} (\sectionref{s:nml_adapt_mechanics})
specifies the grid adaptation library and its options.
The \refine library is distributed and installed with \FunThreeD
by default.

\subsection{Geometry Specification and Grid Freezing for \refine}

When adapting a grid with \refine,
all boundary faces must be specified as frozen or a geometry definition
mush be provided via FAUXGeom.
Use the default \var{patch_lumping='none'} in the \var{&raw_grid} namelist, 
as lumping will change boundary patch indexes making it more difficult
to specify geometry.

\subsubsection{No geometry, where the surface nodes are frozen.}

\refine cannot preserve the high aspect ratio structures within viscous layers, 
and so viscous layers must be frozen for a specified distance away from the 
surface to maintain grid quality. 
This is invoked with the \cmd{adapt_freezebl} command within the 
\cmd{&adaptation_mechanics} namelist, 
see \sectionref{s:nml_adapt_mechanics} for more details.

Additionally, specific surfaces that do not have a viscous boundary 
condition can be frozen by listing the surface numbers 
(one per line)
in a file named \file{[project_rootname].freeze}.
For example, \file{[project_rootname].freeze} that contains
\begin{Verbatim}
5
7
\end{Verbatim}
will freeze points on boundary patches 5 and 7.
This is also useful for boundary surfaces that do not have
an analytical definition handled by FAUXGeom.

\subsubsection{FAUXGeom for Planar Boundaries}

For viscous problems, where the mesh on the complex geometry of the
body is frozen, FAUXGeom
can be used to provide an analytical definition of the farfield 
boundary surfaces.
This allows adaptation to occur on the planar surfaces of the 
mesh, even when the boundary layer mesh is frozen. 
This is a particularly important capability for symmetry planes.
At present, FAUXGeom can only handle planar surfaces.

FAUXGeom reads the file \file{faux_input}. 
Here is an example file:
\begin{Verbatim}
4
 5 xplane -5.0
 3 yplane -1.0
 1 zplane  1.0
16 general_plane 2.0
   0.707 0.707 0.0
\end{Verbatim}

The first line is how many faux surfaces are being defined.
The subsequent lines have a face number, type of face, and
a distance associated with the particular geometry.
In this example,
the first faux face defined corresponds to surface 5 in the mesh
and is a $x=-5.0$ constant plane.
Faux faces are similarly defined for the $z$ and $y$ planes of surfaces 3 and 1.
Surface 16 is a plane perpendicular to a $(0.707,0.707,0.0)$ normal that
is located 2.0 away from the origin in the direction of the normal; 
the plane passes through the point $(1.414,1.414,0.0)$.

\subsection{Performing Feature-Based Adaptation}

The \var{&adapt_metric_construction} variable
\var{adapt_feature_scalar_form} defines the operator that is applied to the
\var{adapt_feature_scalar_key} to compute an adaptation intensity.
This intensity is raised to the \var{adapt_exponent} power to
produce a scaling of an isotropic element size estimate on the current grid.
The anisotropy of the metric is introduced by the
Hessian of the \var{adapt_hessian_key} variable.

Set \var{restart_read='on'} in \sectionref{s:nml_code_run_control}
to read the flow solution.
Run \nodet with the \cmd{--adapt} command line option in the directory
with the flow restart.
The result will be a new grid and interpolated solution file 
with the \cmd{adapt_project} project name.
After adaptation,
the flow solver can now be restarted with
this new grid and interpolated solution
by changing the \var{project_rootname}.

\subsection{Performing Adjoint-Based Adaptation}

Adjoint-based adaptation requires that 
a flow solution be calculated in the \file{Flow} directory
and an adjoint solution be calculated 
in the \file{Adjoint} directory.
See \sectionref{c:adjoint} for more information 
on obtaining an adjoint solution.
The adjoint solution is based on
the functional defined in \file{rubber.data}
and this is the same functional targeted for grid adaptation.

Adaptation is performed by executing \dual with the command line options
\cmd{--rad --adapt}.
The adjoint solver reads the \file{fun3d.nml} in the \file{../Flow} directory),
so this is the place to specify \file{&adapt_metric_construction} and
\file{&adapt_mechanics} options.
The freeze and FAUXGeom files are read in the current directory, 
\file{Adjoint}.
 
The result will be a new grid and interpolated solution restart file
in the \file{../Flow} directory and an interpolated adjoint restart 
in the \file{Adjoint} directory.
The project name of these new files is \cmd{adapt_project}.

% NEXT VERSION : 2D

\subsection{Scripting Grid Adaptation}

The \FunThreeD installation includes the \file{f3d} script.
To find the other components of the \FunThreeD suite,
the \file{f3d} script expects to be in the \file{bin} directory
of the \FunThreeD installation.
Don't copy or link \file{f3d} from the \file{bin} directory.
The input file \file{case_specifics} is described in \sectionref{s:casespec}.

Execute the \file{f3d} script in a directory that contains all of the
the input files (e.g., grid, \file{fun3d.nml}, \file{case_specifics}).
The script will create the required \file{Flow} and \file{Adjoint} directories
to run the case.
It has the following commands,
\begin{Verbatim}
usage: f3d <command>

 <command>       description
 ---------       -----------
 start           Start adaptation
 view            Echo a single snapshot of stdout
 watch           Watch the result of view
 shutdown        Kill all running fun3d and ruby processes
 clean           Remove output and sub directories
 function [name] write rubber.data with cost function [name]
\end{Verbatim}
The command \cmd{start} begins adaptation by launching a background
job.
The commands \cmd{view} and \cmd{watch} allow the adaptation progress to 
be monitored. 
(Use \cmd{Ctrl-C} to escape the \cmd{watch} command.)
The \cmd{shutdown} command kills all ruby (\cmd{f3d}) and \FunThreeD 
jobs.
The  \cmd{clean} command removes the \file{Flow} and \file{Adjoint}
subdirectories and the \file{output} log file.
The \cmd{function} command with argument creates the \file{rubber.data}
file to define the adjoint cost function.

\subsubsection{Input File \texttt{case\_specifics} for \texttt{f3d} Script}%
\label{s:casespec}

The \cmd{f3d} script has one input file, named \file{case_specifics}.
Here is an example
\begin{Verbatim}
root_project ''
number_of_processors 2
mpirun_command 'mpiexec'
first_iteration 1
last_iteration 10
# Any text after a number sign is a comment.
\end{Verbatim}
where the defaults are listed.
Adaptation will be performed from the first grid adaptation iteration 1 
to the last grid adaptation iteration 10.
The string in quotes next to \cmd{root_project} is the project root name.
A two digit iteration number will be appended to it.
The project name for the first adaptation will be \cmd{[root_project]01} and
the last will be \cmd{[root_project]10}.
All the files required to run \nodet and \dual 
should be provided in the current directory and
the grid filename should include
the root project name and iteration number, \cmd{[root_project]01}.
\file{Flow} and \file{Adjoint} subdirectories are
created by the script during execution, and
the input files are placed in their correct location by the script.

Command line options can be passed to the codes via,
\begin{Verbatim}
  all_cl ' '
  flo_cl ' '
  adj_cl ' '
  rad_cl ' '
\end{Verbatim}
where \cmd{all_cl} is provided to all codes,
\cmd{flo_cl} is provided to \nodet,
\cmd{adj_cl} is provided to \dual during the adjoint solve,
and \cmd{rad_cl} is provided to \dual during error estimation and adaptation.
For example, the line
\begin{Verbatim}
adj_cl ' --outer_loop_krylov '
\end{Verbatim}
turns on Krylov projection wrapping to stabilize the adjoint solve.

The main input file \file{fun3d.nml} provided in the current directory
can be modified by the following commands
\begin{Verbatim}
  all_nl['variable']= value
  flo_nl['variable']= value
  adj_nl['variable']= value
  rad_nl['variable']= value
\end{Verbatim}
where \cmd{all_nl} changes \file{fun3d.nml} for all codes,
\cmd{flo_nl} for \nodet,
\cmd{adj_nl} for \dual during the adjoint solve,
and \cmd{rad_nl} for \dual during error estimation and adaptation.
An example is
\begin{Verbatim}
adj_nl['steps']=500
adj_nl['stopping_tolerance']=1.0e-12
\end{Verbatim}
where the termination criteria of the adjoint solver
can be specified separately than the flow solver.

The \file{case_specifics} is actually executable Ruby code.
This allows values to be computed or conditionally executed, 
but also require nested quotes for character strings,
\begin{Verbatim}
rad_nl['adapt_complexity'] = 5000*iteration
number_of_processors 128 if (iteration>5)
all_nl['flux_construction'] = "'vanleer'"
\end{Verbatim}



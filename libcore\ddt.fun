!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite ddt

integer, parameter :: dp = selected_real_kind(P=15)

setup
  use kinddefs, only : dp
end setup

test ddt4_new_init
  type(ddt4) :: var_ddt

  var_ddt = ddt4_new(1.0_dp)
  assert_equal( 1.0_dp  , var_ddt%f )
  assert_equal( 0.0_dp  , var_ddt%d(1) )
  assert_equal( 0.0_dp  , var_ddt%d(2) )

  var_ddt = ddt4_new(1.0_dp, d2 = 2.0_dp)
  assert_equal( 1.0_dp  , var_ddt%f )
  assert_equal( 0.0_dp  , var_ddt%d(1) )
  assert_equal( 2.0_dp  , var_ddt%d(2) )

end test

test ddt5_new_init
  type(ddt5) :: var_ddt

  var_ddt = ddt5_new(1.0_dp)
  assert_equal( 1.0_dp  , var_ddt%f )
  assert_equal( 0.0_dp  , var_ddt%d(1) )
  assert_equal( 0.0_dp  , var_ddt%d(2) )

  var_ddt = ddt5_new(1.0_dp, d2 = 2.0_dp)
  assert_equal( 1.0_dp  , var_ddt%f )
  assert_equal( 0.0_dp  , var_ddt%d(1) )
  assert_equal( 2.0_dp  , var_ddt%d(2) )

end test

!test ddtn_new_init
!  type(ddtn) :: var_ddt
!  real(dp), dimension(:), pointer :: dd
!
!  var_ddt = ddtn_new(5, 1.0_dp)
!  assert_equal( 5       , var_ddt%n )
!  assert_equal( 1.0_dp  , var_ddt%f )
!  assert_equal( 0.0_dp  , var_ddt%d(1) )
!  assert_equal( 0.0_dp  , var_ddt%d(2) )

! allocate(dd(5))
! dd = 0.0_dp
! dd(2) = 2.0_dp
! var_ddt = ddtn_new(5, 1.0_dp, dd)
! assert_equal( 5       , var_ddt%n )
! assert_equal( 1.0_dp  , var_ddt%f )
! assert_equal( 0.0_dp  , var_ddt%d(1) )
! assert_equal( 2.0_dp  , var_ddt%d(2) )
! deallocate(dd)

!end test

test ddt4_assignment
  real(  dp) :: var
  type(ddt4) :: var_ddt

  var_ddt = 2.0_dp
  assert_equal( 2.0_dp  , var_ddt%f )
  assert_equal( 0.0_dp  , var_ddt%d(1) )
  assert_equal( 0.0_dp  , var_ddt%d(2) )

  var = var_ddt
  assert_equal( 2.0_dp  , var )

end test

test ddt5_assignment
  real(  dp) :: var
  type(ddt5) :: var_ddt

  var_ddt = 2.0_dp
  assert_equal( 2.0_dp  , var_ddt%f )
  assert_equal( 0.0_dp  , var_ddt%d(1) )
  assert_equal( 0.0_dp  , var_ddt%d(2) )

  var = var_ddt
  assert_equal( 2.0_dp  , var )

end test

test ddt5_conditionals
  real(  dp) :: r
  type(ddt5) :: d, e

  r =  0.0_dp
  d =  1.0_dp
  e = -1.0_dp

  assert_false( r > d )
  assert_true(  d > r )
  assert_true(  d > e )

  assert_true(  r < d )
  assert_false( d < r )
  assert_false( d < e )

  assert_true(  d >= r )
  assert_true(  d >= e )

  assert_false( d <= r )
  assert_false( d <= e )

end test

test ddt5_algebra

  type(ddt5) :: a_ddt, b_ddt, c_ddt

  a_ddt = 1.0_dp
  b_ddt = ddt5_new(2.0_dp, d2 = 5.0_dp)

  c_ddt = a_ddt + b_ddt
  assert_equal( 3.0_dp  , c_ddt%f )
  assert_equal( 0.0_dp  , c_ddt%d(1) )
  assert_equal( 5.0_dp  , c_ddt%d(2) )

  c_ddt = -1.0_dp + b_ddt
  assert_equal( 1.0_dp  , c_ddt%f )
  assert_equal( 0.0_dp  , c_ddt%d(1) )
  assert_equal( 5.0_dp  , c_ddt%d(2) )

  c_ddt = a_ddt - b_ddt
  assert_equal( -1.0_dp  , c_ddt%f )
  assert_equal(  0.0_dp  , c_ddt%d(1) )
  assert_equal( -5.0_dp  , c_ddt%d(2) )

  c_ddt = 2.0_dp - b_ddt
  assert_equal( -0.0_dp  , c_ddt%f )
  assert_equal(  0.0_dp  , c_ddt%d(1) )
  assert_equal( -5.0_dp  , c_ddt%d(2) )

  c_ddt = a_ddt * b_ddt
  assert_equal(  2.0_dp  , c_ddt%f )
  assert_equal(  0.0_dp  , c_ddt%d(1) )
  assert_equal(  5.0_dp  , c_ddt%d(2) )

  c_ddt = 2.0_dp * b_ddt
  assert_equal(  4.0_dp  , c_ddt%f )
  assert_equal(  0.0_dp  , c_ddt%d(1) )
  assert_equal( 10.0_dp  , c_ddt%d(2) )

  c_ddt = a_ddt / b_ddt
  assert_equal(  0.5_dp  , c_ddt%f )
  assert_equal(  0.0_dp  , c_ddt%d(1) )
  assert_equal( -1.25_dp  , c_ddt%d(2) )

  c_ddt = 2.0_dp / b_ddt
  assert_equal(  1.0_dp  , c_ddt%f )
  assert_equal(  0.0_dp  , c_ddt%d(1) )
  assert_equal( -2.5_dp  , c_ddt%d(2) )

end test

test ddt5_power

  type(ddt5) :: a_ddt, b_ddt

  a_ddt = ddt5_new(2.0_dp, d2 = 5.0_dp)

  b_ddt = a_ddt**2
  assert_equal(  4.0_dp  , b_ddt%f )
  assert_equal(  0.0_dp  , b_ddt%d(1) )
  assert_equal( 20.0_dp  , b_ddt%d(2) )

  b_ddt = a_ddt**2.0_dp
  assert_equal(  4.0_dp  , b_ddt%f )
  assert_equal(  0.0_dp  , b_ddt%d(1) )
  assert_equal( 20.0_dp  , b_ddt%d(2) )

end test

test ddt3_exp

  type(ddt3) :: a_ddt, b_ddt

  a_ddt = 2.0_dp
  a_ddt%d(1) = 3.0_dp

  b_ddt = ddt_exp(a_ddt)
  assert_equal(  exp(2.0_dp), b_ddt%f )
  assert_equal(  3.0_dp*exp(2.0_dp), b_ddt%d(1) )
  assert_equal(  0.0_dp, b_ddt%d(2) )

end test

test ddt5_abs

  type(ddt5) :: a_ddt, b_ddt

  a_ddt = ddt5_new(2.0_dp, d2 = 5.0_dp)
  b_ddt = ddt_abs(a_ddt)
  assert_equal(  2.0_dp  , b_ddt%f )
  assert_equal(  0.0_dp  , b_ddt%d(1) )
  assert_equal(  5.0_dp  , b_ddt%d(2) )

  a_ddt = ddt5_new(-2.0_dp, d2 = 5.0_dp)
  b_ddt = ddt_abs(a_ddt)
  assert_equal(  2.0_dp  , b_ddt%f )
  assert_equal(  0.0_dp  , b_ddt%d(1) )
  assert_equal( -5.0_dp  , b_ddt%d(2) )

end test

test ddt5_sign

  real(dp)   :: r
  type(ddt5) :: d
  type(ddt5) :: s

  r = 3.0_dp
  d = ddt5_new(2.0_dp, d2 = 5.0_dp)
  s = ddt_sign(r,d) ! expect abs(r)
  assert_equal(  3.0_dp  , s%f )
  assert_equal(  0.0_dp  , s%d(1) )
  assert_equal(  0.0_dp  , s%d(2) )

  r = -3.0_dp
  d = ddt5_new(2.0_dp, d2 = 5.0_dp)
  s = ddt_sign(r,d) ! expect abs(r)
  assert_equal(  3.0_dp  , s%f )
  assert_equal(  0.0_dp  , s%d(1) )
  assert_equal(  0.0_dp  , s%d(2) )

  r = 3.0_dp
  d = ddt5_new(-2.0_dp, d2 = 5.0_dp)
  s = ddt_sign(r,d) ! expect -abs(r)
  assert_equal( -3.0_dp  , s%f )
  assert_equal(  0.0_dp  , s%d(1) )
  assert_equal(  0.0_dp  , s%d(2) )

  r = -3.0_dp
  d = ddt5_new(-2.0_dp, d2 = 5.0_dp)
  s = ddt_sign(r,d) ! expect -abs(r)
  assert_equal( -3.0_dp  , s%f )
  assert_equal(  0.0_dp  , s%d(1) )
  assert_equal(  0.0_dp  , s%d(2) )

end test

test ddt5_min_max

  real(  dp) :: var
  type(ddt5) :: var_ddt, result_ddt

  var = 1.0_dp
  var_ddt = 2.0_dp
  var_ddt%d(1) =  1.0_dp

  result_ddt = ddt_min ( var, var_ddt )
  assert_equal( 1.0_dp  , result_ddt%f  )
  assert_equal( 0.0_dp  , result_ddt%d(1)  )

  result_ddt = ddt_max ( var, var_ddt )
  assert_equal( 2.0_dp  , result_ddt%f  )
  assert_equal( 1.0_dp  , result_ddt%d(1)  )

  var = 1.0_dp
  var_ddt = -2.0_dp
  var_ddt%d(1) =  3.0_dp

  result_ddt = ddt_min ( var, var_ddt )
  assert_equal( -2.0_dp  , result_ddt%f  )
  assert_equal(  3.0_dp  , result_ddt%d(1)  )

  result_ddt = ddt_max ( var, var_ddt )
  assert_equal(  1.0_dp  , result_ddt%f  )
  assert_equal(  0.0_dp  , result_ddt%d(1)  )

end test

test ddt5_identity_vector_manipulations

  real(  dp), dimension(  5) :: state
  type(ddt5), dimension(  5) :: state_ddt

  state(1)   =  1.0_dp
  state(2)   = -2.0_dp
  state(3)   =  3.0_dp
  state(4)   = -4.0_dp
  state(5)   =  5.0_dp

  state_ddt = ddt5_identity(state)

  assert_equal(  1.0_dp  , state_ddt(1)%f   )
  assert_equal( -2.0_dp  , state_ddt(2)%f   )
  assert_equal(  3.0_dp  , state_ddt(3)%f   )
  assert_equal( -4.0_dp  , state_ddt(4)%f   )
  assert_equal(  5.0_dp  , state_ddt(5)%f   )

  assert_equal(  1.0_dp  , state_ddt(1)%d(1)   )
  assert_equal(  0.0_dp  , state_ddt(1)%d(2)   )
  assert_equal(  0.0_dp  , state_ddt(1)%d(3)   )
  assert_equal(  0.0_dp  , state_ddt(1)%d(4)   )
  assert_equal(  0.0_dp  , state_ddt(1)%d(5)   )

  assert_equal(  1.0_dp  , state_ddt(2)%d(2)   )
  assert_equal(  1.0_dp  , state_ddt(3)%d(3)   )
  assert_equal(  1.0_dp  , state_ddt(4)%d(4)   )
  assert_equal(  1.0_dp  , state_ddt(5)%d(5)   )

end test

test ddt5_dot_product_manipulations

  type(ddt5)                 :: actual
  type(ddt5), dimension(  3) :: a
  type(ddt5), dimension(  3) :: b

  a         = 0.0_dp
  b         = 0.0_dp

  a(1)      =  1.0_dp
  a(2)      = -2.0_dp
  a(3)      =  3.0_dp
  a(1)%d(1) =  0.0_dp
  a(2)%d(2) =  1.0_dp
  a(3)%d(3) =  1.0_dp

  b(1)      =  1.0_dp
  b(2)      = -2.0_dp
  b(3)      =  3.0_dp
  b(1)%d(1) =  0.0_dp
  b(2)%d(2) =  1.0_dp
  b(3)%d(3) =  1.0_dp

  actual = vdot_ddt ( a, b )

  assert_equal(  14.0_dp  , actual%f    )
  assert_equal(  -4.0_dp  , actual%d(2) )
  assert_equal(   6.0_dp  , actual%d(3) )
  assert_equal(   0.0_dp  , actual%d(4) )

end test

test ddt7_dot_product_manipulations

  type(ddt7)                 :: actual
  type(ddt7), dimension(  3) :: a
  type(ddt7), dimension(  3) :: b

  a         = 0.0_dp
  b         = 0.0_dp

  a(1)      =  1.0_dp
  a(2)      = -2.0_dp
  a(3)      =  3.0_dp
  a(1)%d(1) =  0.0_dp
  a(2)%d(2) =  1.0_dp
  a(3)%d(3) =  1.0_dp

  b(1)      =  1.0_dp
  b(2)      = -2.0_dp
  b(3)      =  3.0_dp
  b(1)%d(1) =  0.0_dp
  b(2)%d(2) =  1.0_dp
  b(3)%d(3) =  1.0_dp

  actual = vdot_ddt ( a, b )

  assert_equal(  14.0_dp  , actual%f    )
  assert_equal(  -4.0_dp  , actual%d(2) )
  assert_equal(   6.0_dp  , actual%d(3) )
  assert_equal(   0.0_dp  , actual%d(4) )

end test

test ddt5_asin_manipulations

  type(ddt5)                 :: actual
  type(ddt5)                 :: a
  real(dp)                   :: pi
  real(dp)                   :: deriv

  pi     = dacos(-1.0_dp)
  a      =  0.5_dp
  a%d(1) =  1.0_dp
  a%d(2) =  0.5_dp
  a%d(3) =  0.1_dp

  deriv  = 1.0_dp / sqrt ( 1.0_dp - a%f*a%f )
  actual = ddt_asin ( a )

  assert_equal_within( (30.0_dp*pi/180.0_dp) , actual%f , 1.0e-8_dp   )
  assert_equal_within( deriv            , actual%d(1), 1.0e-8_dp )
  assert_equal_within( 1.1547005384_dp  , actual%d(1), 1.0e-8_dp )
  assert_equal_within( 0.5773502692_dp  , actual%d(2), 1.0e-8_dp )
  assert_equal_within( 0.1154700538_dp  , actual%d(3), 1.0e-8_dp )

end test

test ddt7_asin_manipulations

  type(ddt7)                 :: actual
  type(ddt7)                 :: a
  real(dp)                   :: pi
  real(dp)                   :: deriv

  pi     = dacos(-1.0_dp)
  a      =  0.5_dp
  a%d(1) =  1.0_dp
  a%d(2) =  0.5_dp
  a%d(3) =  0.1_dp

  deriv  = 1.0_dp / sqrt ( 1.0_dp - a%f*a%f )
  actual = ddt_asin ( a )

  assert_equal_within( (30.0_dp*pi/180.0_dp) , actual%f , 1.0e-8_dp   )
  assert_equal_within( deriv            , actual%d(1), 1.0e-8_dp )
  assert_equal_within( 1.1547005384_dp  , actual%d(1), 1.0e-8_dp )
  assert_equal_within( 0.5773502692_dp  , actual%d(2), 1.0e-8_dp )
  assert_equal_within( 0.1154700538_dp  , actual%d(3), 1.0e-8_dp )

end test

end test_suite

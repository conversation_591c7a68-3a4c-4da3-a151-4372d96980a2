module dalpha

  implicit none

  private

  public :: sensalpha

contains

!================================= SENSALPHA =================================80
!
! This routine is forming the sensitivity derivative dL/dX,
! where X is alpha
!
!=============================================================================80

  subroutine sensalpha(grid, soln, sadj, design, dLdx, physical_timestep,      &
                       subiteration)

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type, compressible, incompressible,  &
                                     generic_gas
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type
    use allocations,          only : my_alloc_ptr
    use nml_nonlinear_solves, only : itime
    use adjoint_switches,     only : windowing
    use bc_names,             only : farfield_riem
    use thermo,               only : etop, ptoe
    use lmpi,                 only : lmpi_die, lmpi_master, lmpi_reduce,       &
                                     lmpi_bcast
    use fun3d_constants,      only : my_0, my_1
    use forces,               only : timestep_contributes, ae_id
    use designs,              only : find_window_value
    use equiv_area,           only : ae_obj

    integer, intent(in) :: physical_timestep, subiteration

    type(grid_type),                          intent(in)    :: grid
    type(soln_type),                          intent(inout) :: soln
    type(sadj_type),                          intent(in)    :: sadj
    type(design_type),                        intent(in)    :: design
    real(dp), dimension(2,design%nfunctions), intent(out)   :: dLdX

    integer :: ib, j, k, i, ae_fcn

    real(dp) :: window_value, weight, target, power

    real(dp), dimension(2) :: tempvar

    real(dp), dimension(design%nfunctions) :: factor

    real(dp), dimension(:,:), pointer :: dfda
    real(dp), dimension(:,:), pointer :: productD

    logical :: apply_dfda

  continue

    call my_alloc_ptr(productD,2,design%nfunctions)

    dLdX     = 0.0_dp
    productD = 0.0_dp

    if ( soln%eqn_set == compressible .or. soln%eqn_set == generic_gas ) then
      call etop( size(soln%q_dof,2),soln%q_dof,soln%n_tot, soln%eqn_set )
    endif

    do ib = 1, grid%nbound
      if ( grid%bc(ib)%ibc == farfield_riem ) then
        select case(soln%eqn_set)
        case (compressible)
          call drdalpha(grid%nnodes0,grid%nnodesg,grid%bc(ib)%nbnode,          &
                        grid%bc(ib)%ibnode,grid%bc(ib)%bxn,grid%bc(ib)%byn,    &
                        grid%bc(ib)%bzn,soln%q_dof,sadj%rlam,sadj%coltag,      &
                        productD,design%nfunctions,soln%ndim,soln%adim,        &
                        grid%nnodes01,grid%bc(ib)%bfacespeed,grid%l2g)
        case (incompressible)
          call drdalphai(grid%nnodes0,grid%bc(ib)%nbnode,grid%bc(ib)%ibnode,   &
                         soln%q_dof,sadj%rlam,sadj%coltag,productD,            &
                         design%nfunctions,soln%ndim,soln%adim,grid%nnodes01,  &
                         grid%bc(ib)%bxn,grid%bc(ib)%byn,grid%bc(ib)%bzn,      &
                         grid%bc(ib)%bfacespeed)
        case (generic_gas)
          call drdalpha(grid%nnodes0,grid%nnodesg,grid%bc(ib)%nbnode,          &
                        grid%bc(ib)%ibnode,grid%bc(ib)%bxn,grid%bc(ib)%byn,    &
                        grid%bc(ib)%bzn,soln%q_dof,sadj%rlam,sadj%coltag,      &
                        productD,design%nfunctions,soln%n_tot,soln%adim,       &
                        grid%nnodes01,grid%bc(ib)%bfacespeed,grid%l2g)
        case default
          write(*,*) "drdalpha not implemented for eqn_set ",soln%eqn_set
          call lmpi_die
        end select
      endif
    end do

    if ( soln%eqn_set == compressible .or. soln%eqn_set == generic_gas ) then
      call ptoe( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
    endif

!  Now all we need to do is add the contribution
!  from the df/dX term and we'll have the answer

!  We have to flip the sign on the matrix-vector
!  product, since we're subtracting it.

    call my_alloc_ptr(dfda,2,design%nfunctions)

    select case(soln%eqn_set)
    case (compressible)
      call dfdalpha(grid,soln,design,dfda)
    case (incompressible)
      call dfdalphai(grid%nnodes01,soln%q_dof,grid%x,grid%y,grid%z,dfda,       &
                     soln%amut,grid%nbound,grid%bc,design,soln%totforce(1),    &
                     soln%bcforce,soln%ndim,grid%nelem,grid%elem)
    case (generic_gas)
      call dfdalpha(grid,soln,design,dfda)
    case default
      write(*,*) "dfdalpha not implemented for eqn_set ",soln%eqn_set
      call lmpi_die
    end select

! Contributions from equivalent area function

    ae_fcn = 0
    fcn_loop6 : do i = 1, design%nfunctions
      component_loop6 : do k = 1, design%function_data(i)%ncomponents
        weight  = design%function_data(i)%component_data(k)%weight
        target  = design%function_data(i)%component_data(k)%target
        power   = design%function_data(i)%component_data(k)%power
        if ( design%function_data(i)%component_data(k)%name == ae_id ) then
          ae_fcn = ae_fcn + 1
          call ae_obj(grid,soln,ae_fcn,weight,target,power,dfda=dfda(:,i))
        endif
      end do component_loop6
    end do fcn_loop6

! If time-dependent, we do not want the residual contribution at the initial
! state - zero it out

    if ( itime > 0 .and. physical_timestep == 1 .and. subiteration == 0 ) then
      do j = 1, design%nfunctions
        productD(:,j) = my_0
      end do
    endif

! If time-dependent, we only want cost function contributions from specified
! timesteps

    factor = my_0
    add_or_not : if ( itime == 0 ) then
      factor = my_1
    else add_or_not
      do k = 1, design%nfunctions
        apply_dfda = timestep_contributes(design%function_data(k)%timesteps,   &
                                          physical_timestep)
        if ( apply_dfda ) factor(k) = my_1

! Find any windowing factor

        if ( windowing ) then
          call find_window_value(design%function_data(k)%timesteps,            &
                                 physical_timestep,window_value)
          factor(k) = factor(k)*window_value
        endif

      end do
    endif add_or_not

! Add up the contributions

    do j = 1, design%nfunctions
      dLdX(:,j) = productD(:,j) + dfda(:,j)*factor(j)
    end do

    deallocate(dfda)
    deallocate(productD)

! Reduce the results

    fcn_loop : do j = 1, design%nfunctions
      call lmpi_reduce(dLdX(:,j),tempvar)
      call lmpi_bcast(tempvar)
      dLdX(:,j) = tempvar
      if(lmpi_master) then
        write(*,'(a,e23.14)') 'Reduced AOA derivative         = ',dLdX(1,j)
        write(67,'(a,e23.14)') 'Reduced AOA derivative         = ',dLdX(1,j)
        write(*,'(a,e23.14)') 'Reduced yaw derivative         = ',dLdX(2,j)
        write(67,'(a,e23.14)') 'Reduced yaw derivative         = ',dLdX(2,j)
      endif
    end do fcn_loop

  end subroutine sensalpha


!================================ DRDALPHA ===================================80
!
! Linearization of inviscid residual wrt alpha
!
!=============================================================================80

  subroutine drdalpha(nnodes0,nnodesg,nbnode,ibnode,bxn,byn,bzn,qnode,rlam,    &
                      coltag,dLdX,nfunctions,n_tot,adim,nnodes01,bfacespeed,l2g)

    use ivals,               only : u0,v0,w0,c0,s0
    use fluid,               only : gamma,gm1
    use info_depr,           only : alpha,yaw,xmach
    use grid_motion_helpers, only : need_grid_velocity
    use rotors,              only : rotor_flag,vinf_ratio,alternate_freestream,&
                                    vinf_input_ratio
    use kinddefs,            only : dp
    use fun3d_constants,     only : my_0
    use adjoint_switches,    only : write_for_chaos

    integer, intent(in) :: nnodes0, n_tot, adim, nbnode, nfunctions
    integer, intent(in) :: nnodes01, nnodesg

    integer, dimension(nbnode), intent(in) :: ibnode
    integer, dimension(:), intent(in) :: l2g

    real(dp), dimension(nbnode),                    intent(in)    :: bxn
    real(dp), dimension(nbnode),                    intent(in)    :: byn
    real(dp), dimension(nbnode),                    intent(in)    :: bzn
    real(dp), dimension(n_tot,nnodes01),            intent(in)    :: qnode
    real(dp), dimension(adim,nnodes01,nfunctions),  intent(in)    :: rlam
    real(dp), dimension(adim,nnodes01),             intent(in)    :: coltag
    real(dp), dimension(:),                         intent(in)    :: bfacespeed
    real(dp), dimension(2,nfunctions),              intent(inout) :: dLdX

    integer :: i,inode,j

    real(dp)    :: pi,conv,xgm1
    real(dp)    :: uout,vout,wout,cout
    real(dp)    :: uouta,vouta,wouta
    real(dp)    :: uoutb,voutb,woutb
    real(dp)    :: xnorm,ynorm,znorm
    real(dp)    :: area,rhoi,ui,vi,wi
    real(dp)    :: unormi,unormo
    real(dp)    :: unormoa,unormob,a2,a,ai
    real(dp)    :: rplus,rminus
    real(dp)    :: rplusa,rminusa
    real(dp)    :: rplusb,rminusb
    real(dp)    :: unorm,unorma,unormb,aa,ab
    real(dp)    :: u,v,w,ua,va,wa,ub,vb,wb,s
    real(dp)    :: rho,rhoa,p,pa,e,ea,rhob,pb,eb
    real(dp)    :: ubar,ubara,ubarb
    real(dp)    :: res1a,res2a,res3a,res4a,res5a
    real(dp)    :: res1b,res2b,res3b,res4b,res5b
    real(dp)    :: term1,term2,term3
    real(dp)    :: term4,term5
    real(dp)    :: face_speed

    real(dp), dimension(:,:), allocatable :: drdd

  continue

    if ( write_for_chaos ) then
      allocate(drdd(5,nnodes0))
      drdd(:,:) = 0.0_dp
    endif

    pi = 4.0_dp*atan(1.0_dp)
    conv = 180.0_dp/pi
    xgm1 = 1.0_dp/gm1

! 1) Contribution from farfield state in Riemann BC

    uout = u0
    vout = v0
    wout = w0
    cout = c0

    uouta = - xmach * sin(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)
    vouta = 0.0_dp
    wouta =   xmach * cos(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)

    uoutb = - xmach * cos(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)
    voutb = - xmach * cos(yaw/conv) / conv
    woutb = - xmach * sin(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)

    if ( rotor_flag ) then
      uouta = vinf_ratio * uouta
      vouta = vinf_ratio * vouta
      wouta = vinf_ratio * wouta

      uoutb = vinf_ratio * uoutb
      voutb = vinf_ratio * voutb
      woutb = vinf_ratio * woutb
    endif

    if ( alternate_freestream ) then
      uouta = vinf_input_ratio * uouta
      vouta = vinf_input_ratio * vouta
      wouta = vinf_input_ratio * wouta

      uoutb = vinf_input_ratio * uoutb
      voutb = vinf_input_ratio * voutb
      woutb = vinf_input_ratio * woutb
    endif

    node_loop : do i = 1,nbnode
      inode   = ibnode(i)
      if (inode <= nnodes0) then

! Calculate R+ and R-
! Then get the normal velocity and the
! speed of sound on the boundary

        xnorm   = bxn(i)
        ynorm   = byn(i)
        znorm   = bzn(i)
        area    = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)
        xnorm   = xnorm/area
        ynorm   = ynorm/area
        znorm   = znorm/area
        face_speed = my_0
        if (need_grid_velocity) then
          face_speed = bfacespeed(i)
        end if
        rhoi    = qnode(1,inode)
        ui      = qnode(2,inode)
        vi      = qnode(3,inode)
        wi      = qnode(4,inode)
        unormi  = ui*xnorm + vi*ynorm + wi*znorm - face_speed
        unormo  = uout*xnorm + vout*ynorm + wout*znorm - face_speed
        unormoa = uouta*xnorm + vouta*ynorm + wouta*znorm
        unormob = uoutb*xnorm + voutb*ynorm + woutb*znorm
        a2      = gamma*qnode(5,inode)/qnode(1,inode)
        a       = sqrt(a2)
        ai      = a
        rplus   = unormi + 2.0_dp*a/gm1
        rplusa  = 0.0_dp
        rplusb  = 0.0_dp
        rminus  = unormo - 2.0_dp*cout/gm1
        rminusa = unormoa
        rminusb = unormob

! Mods for supersonic

        if (unormi > 1.0_dp) then
          rminus  = unormi - 2.0_dp*a/gm1
          rminusa = 0.0_dp
          rminusb = 0.0_dp
        end if

        if (unormi < -1.0_dp) then
          rplus  = unormo + 2.0_dp*cout/gm1
          rplusa = unormoa
          rplusb = unormob
        end if

        unorm   = 0.5_dp*(rplus + rminus)
        unorma  = 0.5_dp*(rplusa + rminusa)
        unormb  = 0.5_dp*(rplusb + rminusb)
        a       = 0.25_dp*gm1*(rplus - rminus)
        aa      = 0.25_dp*gm1*(rplusa - rminusa)
        ab      = 0.25_dp*gm1*(rplusb - rminusb)

! If unorm > 0 this is outflow: take variables from inside
! If unorm < 0 this is inflow:  take variables from outside

        if(unorm > 0.0_dp)then
          u = qnode(2,inode) + xnorm*(unorm - unormi)
          ua = xnorm*unorma
          ub = xnorm*unormb
          v = qnode(3,inode) + ynorm*(unorm - unormi)
          va = ynorm*unorma
          vb = ynorm*unormb
          w = qnode(4,inode) + znorm*(unorm - unormi)
          wa = znorm*unorma
          wb = znorm*unormb
          s = ai*ai/(gamma*rhoi**gm1)
        else
          u = uout + xnorm*(unorm - unormo)
          ua = uouta + xnorm*(unorma - unormoa)
          ub = uoutb + xnorm*(unormb - unormob)
          v = vout + ynorm*(unorm - unormo)
          va = vouta + ynorm*(unorma - unormoa)
          vb = voutb + ynorm*(unormb - unormob)
          w = wout + znorm*(unorm - unormo)
          wa = wouta + znorm*(unorma - unormoa)
          wb = woutb + znorm*(unormb - unormob)
          s = s0
        end if
        rho  = (a*a/(gamma*s))**xgm1
        rhoa = xgm1*(a*a/(gamma*s))**(xgm1 - 1.0_dp)*2.0_dp*a*aa/gamma/s
        rhob = xgm1*(a*a/(gamma*s))**(xgm1 - 1.0_dp)*2.0_dp*a*ab/gamma/s
        p    = rho*a*a/gamma
        pa   = 1.0_dp / gamma * (rho*2.0_dp*a*aa + a*a*rhoa)
        pb   = 1.0_dp / gamma * (rho*2.0_dp*a*ab + a*a*rhob)
        e    = p/gm1 + 0.5_dp*rho*(u*u + v*v + w*w)
        ea   = pa/gm1                                                          &
             + 0.5_dp*( rho*(2.0_dp*u*ua+2.0_dp*v*va+2.0_dp*w*wa)              &
                          + (u*u + v*v + w*w)*rhoa )
        eb   = pb/gm1                                                          &
             + 0.5_dp*( rho*(2.0_dp*u*ub+2.0_dp*v*vb+2.0_dp*w*wb)              &
                          + (u*u + v*v + w*w)*rhob )
        ubar = xnorm*u + ynorm*v + znorm*w - face_speed
        ubara = xnorm*ua + ynorm*va + znorm*wa
        ubarb = xnorm*ub + ynorm*vb + znorm*wb

        res1a = area*(rho*ubara + ubar*rhoa)
        res2a = area*(rho*(u*ubara + ubar*ua) + u*ubar*rhoa + xnorm*pa)
        res3a = area*(rho*(v*ubara + ubar*va) + v*ubar*rhoa + ynorm*pa)
        res4a = area*(rho*(w*ubara + ubar*wa) + w*ubar*rhoa + znorm*pa)
        res5a = area*((e+p)*ubara + ubar*(ea+pa)) + area*face_speed*pa

        res1b = area*(rho*ubarb + ubar*rhob)
        res2b = area*(rho*(u*ubarb + ubar*ub) + u*ubar*rhob + xnorm*pb)
        res3b = area*(rho*(v*ubarb + ubar*vb) + v*ubar*rhob + ynorm*pb)
        res4b = area*(rho*(w*ubarb + ubar*wb) + w*ubar*rhob + znorm*pb)
        res5b = area*((e+p)*ubarb + ubar*(eb+pb)) + area*face_speed*pb

!  Now we can form this node's contribution to the
!  matrix-vector product in the sensitivity derivative

        do j = 1, nfunctions

          term1 = res1a * coltag(1,inode)*rlam(1,inode,j)
          term2 = res2a * coltag(2,inode)*rlam(2,inode,j)
          term3 = res3a * coltag(3,inode)*rlam(3,inode,j)
          term4 = res4a * coltag(4,inode)*rlam(4,inode,j)
          term5 = res5a * coltag(5,inode)*rlam(5,inode,j)

          dLdX(1,j) = dLdX(1,j) + term1 + term2 + term3 + term4 + term5

          term1 = res1b * coltag(1,inode)*rlam(1,inode,j)
          term2 = res2b * coltag(2,inode)*rlam(2,inode,j)
          term3 = res3b * coltag(3,inode)*rlam(3,inode,j)
          term4 = res4b * coltag(4,inode)*rlam(4,inode,j)
          term5 = res5b * coltag(5,inode)*rlam(5,inode,j)

          dLdX(2,j) = dLdX(2,j) + term1 + term2 + term3 + term4 + term5

        end do

! Store off alpha linearizations for chaos testing

        if ( write_for_chaos ) then
          drdd(1,inode) = res1a*coltag(1,inode)
          drdd(2,inode) = res2a*coltag(2,inode)
          drdd(3,inode) = res3a*coltag(3,inode)
          drdd(4,inode) = res4a*coltag(4,inode)
          drdd(5,inode) = res5a*coltag(5,inode)
        endif

      end if

    end do node_loop

! Dump data for chaos testing if desired

    if ( write_for_chaos ) then
      call write_drdd(nnodes0,nnodesg,l2g,drdd)
      deallocate(drdd)
    endif

  end subroutine drdalpha


!================================= WRITE_DRDD ================================80
!
!  Writes dR/dD for testing purposes
!
!=============================================================================80
  subroutine write_drdd(nnodes0,nnodesg,l2g,drdd)

    use info_depr,         only : physical_timestep, ncyc
    use system_extensions, only : se_open
    use string_utils,      only : int_to_s
    use file_utils,        only : available_unit
    use lmpi,              only : lmpi_id
    use kinddefs,          only : dp

    integer, intent(in) :: nnodes0, nnodesg

    integer, dimension(:), intent(in) :: l2g

    real(dp), dimension(:,:), intent(in) :: drdd

    integer :: i

    integer, save :: iu1, iu2, current_position

    character(len=1000) :: filename

    logical, save :: first_time = .true.

  continue

! I would like to use an inquire statement to get the current file position,
! but it doesnt seem to always work correctly.  Therefore I will count bytes
! manually.  Bytes are not portable file pointer units, but it seems to
! be what the Intel compiler uses.

    if ( first_time ) then

      iu1 = available_unit()
      filename = 'drdd.data.' // trim(int_to_s(lmpi_id+1))
      call se_open(iu1,file=trim(filename),form='unformatted',status='replace',&
                   access='stream')

      current_position = 1

      write(iu1) ncyc, nnodes0, nnodesg

      current_position = current_position + 12

      do i = 1, nnodes0
        write(iu1) l2g(i)
      end do

      current_position = current_position + 4*nnodes0

      iu2 = available_unit()
      filename = 'drdd.metadata.' // trim(int_to_s(lmpi_id+1))
      call se_open(iu2,file=trim(filename),form='formatted',status='replace')

      write(iu2,*) ncyc, nnodes0, nnodesg

      first_time = .false.

    endif

    write(iu2,*) current_position

    write(iu1) drdd(1:5,1:nnodes0)

    current_position = current_position + 8*5*nnodes0

    if ( physical_timestep == 1 ) then
      close(iu1)
      close(iu2)
    endif

  end subroutine write_drdd


!================================ DFDALPHA ===================================80
!
! Calculates the gradient of the cost function w.r.t. the design
! variable alpha
!
!=============================================================================80

  subroutine dfdalpha(grid,soln,design,dfda)

    use kinddefs,              only : dp
    use grid_types,            only : grid_type
    use solution_types,        only : soln_type
    use design_types,          only : design_type
    use bc_names,              only : bc_used_for_force_calculation,           &
                                      bc_has_skin_friction,                    &
                                      bc_is_flow_through
    use refgeom,               only : sref
    use forces,                only : cl_id, cd_id, clv_id, cdv_id, clcd_id,   &
                                      rotor_thrust_id
    use fun3d_constants,       only : my_1
    use custom_transforms,     only : thrust_angle
    use component_performance, only : allow_flow_through_forces

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(design_type), intent(in)    :: design

    real(dp), dimension(2,design%nfunctions),    intent(out) :: dfda

    integer :: ib, j, k

    real(dp)    :: clva, cdva, factor, clvb, cdvb, sint, cost
    real(dp)    :: cl,cd,clv,cdv,clcd,rotor_thrust
    real(dp)    :: weight,target,power,width,average,base,const

  continue

    const = 0.0_dp

    dfda = 0.0_dp

    fcn_loop : do j = 1, design%nfunctions

      bound_loop : do ib = 1, grid%nbound

        if ( bc_is_flow_through( grid%bc(ib)%ibc ) ) then
          if ( allow_flow_through_forces ) then
            call dflow_through_force_da(grid%bc(ib)%nbnode,grid%bc(ib)%ibnode, &
                                        grid%bc(ib)%nbfacet,grid%bc(ib)%f2ntb, &
                                        grid%bc(ib)%nbfaceq,grid%bc(ib)%f2nqb, &
                                        grid%nelem,grid%elem,grid%nnodes01,    &
                                        grid%x,grid%y,grid%z,soln%q_dof,       &
                                        grid%bc(ib)%face_bit,                  &
                                        grid%bc(ib)%face_bitq,soln%totforce(1),&
                                        soln%n_tot,design,soln%bcforce,dfda,j, &
                                        ib)
          endif
          cycle
        endif

        use_bc : if(bc_used_for_force_calculation( grid%bc(ib)%ibc ) ) then

          call pressurealpha(grid%nnodes01,dfda,design,grid%bc(ib)%nbnode,     &
                             grid%bc(ib)%nbfacet,    grid%bc(ib)%nbfaceq,      &
                             grid%bc(ib)%face_bit,   grid%bc(ib)%face_bitq,    &
                             grid%bc(ib)%ibnode,     grid%bc(ib)%f2ntb,        &
                             grid%bc(ib)%f2nqb,      grid%x, grid%y, grid%z,   &
                             soln%q_dof, ib, j,      grid%nbound,              &
                             soln%totforce(1),       soln%bcforce,             &
                             soln%n_tot)

          no_slip_bc : if( bc_has_skin_friction( grid%bc(ib)%ibc ) ) then

            clva = 0.0_dp
            cdva = 0.0_dp
            clvb = 0.0_dp
            cdvb = 0.0_dp

            call skinfrica(grid%nelem, grid%elem, grid%x, grid%y, grid%z,      &
                           soln%q_dof, soln%amut, clva, cdva, clvb, cdvb,      &
                           grid%nnodes01,               grid%bc(ib)%nbfacet,   &
                           grid%bc(ib)%ibnode,          grid%bc(ib)%face_bit,  &
                           grid%bc(ib)%f2ntb,           grid%bc(ib)%nbnode,    &
                           grid%bc(ib)%nbfaceq,         grid%bc(ib)%face_bitq, &
                           grid%bc(ib)%f2nqb,           soln%n_tot)

! Note that there are no alpha/beta derivatives of cx,cy,cz,cxv,cyv,czv,cmx,
! cmy,cmz,cmxv,cmyv,cmzv,propeff,powerx,powery,powerz

            component_loopv : do k = 1, design%function_data(j)%ncomponents

              if                                                               &
              (design%function_data(j)%component_data(k)%boundary_id == 0) then
                if ( .not. soln%bcforce(ib)%add_to_total ) cycle component_loopv
                cd   = soln%totforce(1)%cd
                cdv  = soln%totforce(1)%cdv
                cl   = soln%totforce(1)%cl
                clv  = soln%totforce(1)%clv
                clcd = soln%totforce(1)%clcd
                rotor_thrust = soln%totforce(1)%rotor_thrust
              else if                                                          &
              (design%function_data(j)%component_data(k)%boundary_id == ib) then
                cd   = soln%bcforce(ib)%cd
                cdv  = soln%bcforce(ib)%cdv
                cl   = soln%bcforce(ib)%cl
                clv  = soln%bcforce(ib)%clv
                clcd = 0.0_dp ! only used on boundary_id == 0
                rotor_thrust = 0.0_dp ! only used on boundary_id == 0
              else
                cycle component_loopv
              endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdv_id )
                if (.not.design%function_data(j)%averaging) base = cdv-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clv_id )
                if (.not.design%function_data(j)%averaging) base = clv-target
                const = my_1/sref
              case ( clcd_id )
                if (.not.design%function_data(j)%averaging) base = clcd-target
                const = my_1/sref
              case ( rotor_thrust_id )
                if (.not.design%function_data(j)%averaging)base = rotor_thrust &
                                                                - target
                const = my_1/sref
              case default
                cycle component_loopv
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id, cdv_id )
                dfda(1,j) = dfda(1,j) + factor*cdva
                dfda(2,j) = dfda(2,j) + factor*cdvb
              case ( cl_id, clv_id )
                dfda(1,j) = dfda(1,j) + factor*clva
                dfda(2,j) = dfda(2,j) + factor*clvb
              case ( clcd_id )
                dfda(1,j) = dfda(1,j) + factor*(1.0_dp/cd*clva - cl/cd/cd*cdva)
                dfda(2,j) = dfda(2,j) + factor*(1.0_dp/cd*clvb - cl/cd/cd*cdvb)
              case ( rotor_thrust_id )
                sint = sin(thrust_angle)
                cost = cos(thrust_angle)
                dfda(1,j) = dfda(1,j) + cost*factor*clva - sint*factor*cdva
                dfda(2,j) = dfda(2,j) + cost*factor*clvb - sint*factor*cdvb
              case default
                cycle component_loopv
              end select

            end do component_loopv

          endif no_slip_bc

        endif use_bc

      end do bound_loop

    end do fcn_loop

  end subroutine dfdalpha


!================================ PRESSUREALPHA ==============================80
!
!  This gets the derivative of pressure forces wrt alpha
!
!=============================================================================80

  subroutine pressurealpha(nnodes01,dfda,design,nbnode,nbfacet,nbfaceq,        &
                           face_bit,face_bitq,ibnode,f2ntb,f2nqb,x,y,z,qnode,  &
                           ib,ifcn,nbound,force,bcforce,n_tot)

    use info_depr,         only : alpha,yaw,xmach
    use ivals,             only : p0
    use fluid,             only : gm1,gamma
    use kinddefs,          only : dp
    use design_types,      only : design_type
    use refgeom,           only : sref,bref,cref
    use force_types,       only : force_type
    use forces,            only : cd_id, cdp_id, cl_id, clp_id, clcd_id, cx_id,&
                                  cy_id, cz_id, cxp_id, cyp_id, czp_id, cmx_id,&
                                  cmxp_id, cmy_id, cmyp_id, cmz_id, cmzp_id,   &
                                  propeff_id, powerx_id, powery_id, powerz_id, &
                                  rotor_thrust_id
    use custom_transforms, only : thrust_angle

    integer, intent(in) :: nnodes01,nbnode,n_tot,nbfacet,ib,ifcn,nbound,nbfaceq

    integer, dimension(nbnode),    intent(in) :: ibnode
    integer, dimension(nbfacet),   intent(in) :: face_bit
    integer, dimension(nbfaceq),   intent(in) :: face_bitq
    integer, dimension(nbfacet,5), intent(in) :: f2ntb
    integer, dimension(nbfaceq,6), intent(in) :: f2nqb

    real(dp), dimension(nnodes01),       intent(in) :: x,y,z
    real(dp), dimension(n_tot,nnodes01),  intent(in) :: qnode

    type(design_type),                   intent(in) :: design
    type(force_type),                    intent(in) :: force
    type(force_type), dimension(nbound), intent(in) :: bcforce

    real(dp), dimension(2,design%nfunctions),    intent(inout) :: dfda

    integer :: n,k,node1,node2,node3,node4

    real(dp)    :: pi,const
    real(dp)    :: conv,csa,sna,csy
    real(dp)    :: x1,x2,x3,x4,y1,y2,y3,y4
    real(dp)    :: z1,z2,z3,z4
    real(dp)    :: ax,ay,az,bx,by,bz
    real(dp)    :: xnorm,ynorm,znorm
    real(dp)    :: rho1,u1,v1,w1,p1
    real(dp)    :: rho2,u2,v2,w2,p2
    real(dp)    :: rho3,u3,v3,w3,p3
    real(dp)    :: rho4,u4,v4,w4,p4
    real(dp)    :: press,cp,dcx,dcy,dcz
    real(dp)    :: dsnada,dcsada,dsnyda
    real(dp)    :: dsnadb,dcsadb,dsnydb
    real(dp)    :: dcsyda,dcsydb
    real(dp)    :: weight,target,power
    real(dp)    :: cl,cd,clp,cdp,clcd,cx,cy,cz,cxp,cyp,czp,cmx,cmy,cmz
    real(dp)    :: cmxp,cmyp,cmzp,propeff,rpowerx,rpowery,rpowerz, rotor_thrust
    real(dp)    :: width, average, base, factor, sint, cost

    real(dp), parameter    :: my_haf = 0.5_dp
    real(dp), parameter    :: my_1   = 1.0_dp

  continue

    const = 0.0_dp

    pi = 4.0_dp*atan(1.0_dp)
    conv = 180.0_dp/pi

! Directions

    csa=cos(alpha/conv)
    sna=sin(alpha/conv)
    csy=cos(yaw/conv)
!   sny=sin(yaw/conv)

! Derivatives of directions

    dsnada = cos(alpha/conv) * 1.0_dp / conv
    dcsada = - sin(alpha/conv) * 1.0_dp / conv
    dsnyda = 0.0_dp
    dcsyda = 0.0_dp

    dsnadb = 0.0_dp
    dcsadb = 0.0_dp
    dsnydb = cos(yaw/conv) * 1.0_dp / conv
    dcsydb = - sin(yaw/conv) * 1.0_dp / conv

! Go accumulate the pieces now

    tri_loop : do n = 1, nbfacet
      local_face : if(face_bit(n) == 1)then
        node1 = ibnode(f2ntb(n,1))
        node2 = ibnode(f2ntb(n,2))
        node3 = ibnode(f2ntb(n,3))

        x1 = x(node1)
        y1 = y(node1)
        z1 = z(node1)

        x2 = x(node2)
        y2 = y(node2)
        z2 = z(node2)

        x3 = x(node3)
        y3 = y(node3)
        z3 = z(node3)

        ax = x2 - x1
        ay = y2 - y1
        az = z2 - z1

        bx = x3 - x1
        by = y3 - y1
        bz = z3 - z1

!  norm points outward, away from grid interior.
!  norm magnitude is area of surface triangle.

        xnorm =-0.5_dp*(ay*bz - az*by)
        ynorm = 0.5_dp*(ax*bz - az*bx)
        znorm =-0.5_dp*(ax*by - ay*bx)

        rho1  = qnode(1,node1)
        u1    = qnode(2,node1)/rho1
        v1    = qnode(3,node1)/rho1
        w1    = qnode(4,node1)/rho1
        p1    = gm1*(qnode(5,node1) - .5_dp*rho1*(u1*u1 + v1*v1 + w1*w1))
        rho2  = qnode(1,node2)
        u2    = qnode(2,node2)/rho2
        v2    = qnode(3,node2)/rho2
        w2    = qnode(4,node2)/rho2
        p2    = gm1*(qnode(5,node2) - .5_dp*rho2*(u2*u2 + v2*v2 + w2*w2))
        rho3  = qnode(1,node3)
        u3    = qnode(2,node3)/rho3
        v3    = qnode(3,node3)/rho3
        w3    = qnode(4,node3)/rho3
        p3    = gm1*(qnode(5,node3) - .5_dp*rho3*(u3*u3 + v3*v3 + w3*w3))

        press = (p1 + p2 + p3)/3.0_dp
        cp    = 2.0_dp*(press/p0-1.0_dp)/(gamma*xmach*xmach)

        dcx = cp*xnorm
        dcy = cp*ynorm
        dcz = cp*znorm

! Moment coefficients do not depend on alpha

        component_loop : do k = 1, design%function_data(ifcn)%ncomponents

          if (design%function_data(ifcn)%component_data(k)%boundary_id == 0)then
            if ( .not. bcforce(ib)%add_to_total ) cycle component_loop
            cd   = force%cd
            cdp  = force%cdp
            cl   = force%cl
            clp  = force%clp
            clcd = force%clcd
            cx   = force%cx
            cy   = force%cy
            cz   = force%cz
            cxp  = force%cxp
            cyp  = force%cyp
            czp  = force%czp
            cmx  = force%cmx
            cmy  = force%cmy
            cmz  = force%cmz
            cmxp = force%cmxp
            cmyp = force%cmyp
            cmzp = force%cmzp
            propeff = force%propeff
            rpowerx = force%powerx
            rpowery = force%powery
            rpowerz = force%powerz
            rotor_thrust = force%rotor_thrust
          else if                                                              &
          (design%function_data(ifcn)%component_data(k)%boundary_id == ib)then
            cd   = bcforce(ib)%cd
            cdp  = bcforce(ib)%cdp
            cl   = bcforce(ib)%cl
            clp  = bcforce(ib)%clp
            clcd = 0.0_dp ! only used on boundary_id == 0
            cx   = bcforce(ib)%cx
            cy   = bcforce(ib)%cy
            cz   = bcforce(ib)%cz
            cxp  = bcforce(ib)%cxp
            cyp  = bcforce(ib)%cyp
            czp  = bcforce(ib)%czp
            cmx  = bcforce(ib)%cmx
            cmy  = bcforce(ib)%cmy
            cmz  = bcforce(ib)%cmz
            cmxp = bcforce(ib)%cmxp
            cmyp = bcforce(ib)%cmyp
            cmzp = bcforce(ib)%cmzp
            propeff = 0.0_dp ! only used on boundary_id == 0
            rpowerx = bcforce(ib)%powerx
            rpowery = bcforce(ib)%powery
            rpowerz = bcforce(ib)%powerz
            rotor_thrust = 0.0_dp ! only used on boundary_id == 0
          else
            cycle component_loop
          endif

              weight  = design%function_data(ifcn)%component_data(k)%weight
              target  = design%function_data(ifcn)%component_data(k)%target
              power   = design%function_data(ifcn)%component_data(k)%power
              width   = design%function_data(ifcn)%timesteps(2) -              &
                        design%function_data(ifcn)%timesteps(1) + 1
              if ( .not.design%function_data(ifcn)%averaging ) width = 1.0_dp
              average = design%function_data(ifcn)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(ifcn)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(ifcn)%averaging) base = cd-target
                const = my_1/sref
              case ( cdp_id )
                if (.not.design%function_data(ifcn)%averaging) base = cdp-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(ifcn)%averaging) base = cl-target
                const = my_1/sref
              case ( clp_id )
                if (.not.design%function_data(ifcn)%averaging) base = clp-target
                const = my_1/sref
              case ( clcd_id )
                if (.not.design%function_data(ifcn)%averaging) base= clcd-target
                const = my_1/sref
              case ( cx_id )
                if (.not.design%function_data(ifcn)%averaging) base = cx-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(ifcn)%averaging) base = cy-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(ifcn)%averaging) base = cz-target
                const = my_1/sref
              case ( cxp_id )
                if (.not.design%function_data(ifcn)%averaging) base = cxp-target
                const = my_1/sref
              case ( cyp_id )
                if (.not.design%function_data(ifcn)%averaging) base = cyp-target
                const = my_1/sref
              case ( czp_id )
                if (.not.design%function_data(ifcn)%averaging) base = czp-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmxp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmxp-target
                const = my_1/sref/bref
              case ( cmyp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmyp-target
                const = my_1/sref/cref
              case ( cmzp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmzp-target
                const = my_1/sref/bref
              case ( propeff_id )
                if(.not.design%function_data(ifcn)%averaging)base=propeff-target
                const = my_1
              case ( powerx_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowerz-target
                const = my_1/sref
              case ( rotor_thrust_id )
                if(.not.design%function_data(ifcn)%averaging)base=rotor_thrust &
                                                                 -target
                const = my_1/sref
              case default
                cycle component_loop
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

          select case (design%function_data(ifcn)%component_data(k)%name)
          case ( cd_id, cdp_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*(dcx*(csa*dcsyda+csy*dcsada)  &
                                                - dcy*dsnyda                   &
                                                + dcz*(sna*dcsyda+csy*dsnada))
            dfda(2,ifcn) = dfda(2,ifcn) + factor*(dcx*(csa*dcsydb+csy*dcsadb)  &
                                                - dcy*dsnydb                   &
                                                + dcz*(sna*dcsydb+csy*dsnadb))
          case ( cl_id, clp_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*(-dcx*dsnada + dcz*dcsada)
            dfda(2,ifcn) = dfda(2,ifcn) + factor*(-dcx*dsnadb + dcz*dcsadb)
          case ( clcd_id )
            dfda(1,ifcn) = dfda(1,ifcn) + (cd*(-dcx*dsnada + dcz*dcsada)       &
                       - cl*(dcx*(csa*dcsyda+csy*dcsada)- dcy*dsnyda+          &
                         dcz*(sna*dcsyda+csy*dsnada)))/cd/cd*factor
            dfda(2,ifcn) = dfda(2,ifcn) + (cd*(-dcx*dsnadb + dcz*dcsadb)       &
                       - cl*(dcx*(csa*dcsydb+csy*dcsadb)- dcy*dsnydb+          &
                         dcz*(sna*dcsydb+csy*dsnadb)))/cd/cd*factor
          case ( rotor_thrust_id )
            sint = sin(thrust_angle)
            cost = cos(thrust_angle)
            dfda(1,ifcn) = dfda(1,ifcn) + cost*factor*(-dcx*dsnada+dcz*dcsada) &
                                   - sint*factor*(dcx*(csa*dcsyda+csy*dcsada)  &
                                   - dcy*dsnyda + dcz*(sna*dcsyda+csy*dsnada))
            dfda(2,ifcn) = dfda(2,ifcn) + cost*factor*(-dcx*dsnadb+dcz*dcsadb) &
                                   - sint*factor*(dcx*(csa*dcsydb+csy*dcsadb)  &
                                   - dcy*dsnydb + dcz*(sna*dcsydb+csy*dsnadb))
          case ( cx_id, cxp_id, cy_id, cyp_id, cz_id, czp_id,                  &
                 cmx_id, cmxp_id, cmy_id, cmyp_id, cmz_id, cmzp_id, propeff_id,&
                 powerx_id, powery_id, powerz_id )
            ! Nothing: do not depend on alpha/beta
          case default
          end select

        end do component_loop

      endif local_face

    end do tri_loop

    quad_loop : do n = 1, nbfaceq
      local_face2 : if(face_bitq(n) == 1)then
        node1 = ibnode(f2nqb(n,1))
        node2 = ibnode(f2nqb(n,2))
        node3 = ibnode(f2nqb(n,3))
        node4 = ibnode(f2nqb(n,4))

        x1 = x(node1)
        y1 = y(node1)
        z1 = z(node1)

        x2 = x(node2)
        y2 = y(node2)
        z2 = z(node2)

        x3 = x(node3)
        y3 = y(node3)
        z3 = z(node3)

        x4 = x(node4)
        y4 = y(node4)
        z4 = z(node4)

!       quad normal computed as 1/2 the  cross product of the 2 diagonals
!       change sign to point away from interior

        xnorm = -my_haf*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
        ynorm = -my_haf*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
        znorm = -my_haf*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )

        rho1  = qnode(1,node1)
        u1    = qnode(2,node1)/rho1
        v1    = qnode(3,node1)/rho1
        w1    = qnode(4,node1)/rho1
        p1    = gm1*(qnode(5,node1) - .5_dp*rho1*(u1*u1 + v1*v1 + w1*w1))
        rho2  = qnode(1,node2)
        u2    = qnode(2,node2)/rho2
        v2    = qnode(3,node2)/rho2
        w2    = qnode(4,node2)/rho2
        p2    = gm1*(qnode(5,node2) - .5_dp*rho2*(u2*u2 + v2*v2 + w2*w2))
        rho3  = qnode(1,node3)
        u3    = qnode(2,node3)/rho3
        v3    = qnode(3,node3)/rho3
        w3    = qnode(4,node3)/rho3
        p3    = gm1*(qnode(5,node3) - .5_dp*rho3*(u3*u3 + v3*v3 + w3*w3))
        rho4  = qnode(1,node4)
        u4    = qnode(2,node4)/rho4
        v4    = qnode(3,node4)/rho4
        w4    = qnode(4,node4)/rho4
        p4    = gm1*(qnode(5,node4) - .5_dp*rho4*(u4*u4 + v4*v4 + w4*w4))

        press = (p1 + p2 + p3 + p4)/4.0_dp
        cp    = 2.0_dp*(press/p0-1.0_dp)/(gamma*xmach*xmach)

        dcx = cp*xnorm
        dcy = cp*ynorm
        dcz = cp*znorm

! Moment coefficients do not depend on alpha

        component_loopq : do k = 1, design%function_data(ifcn)%ncomponents

          if (design%function_data(ifcn)%component_data(k)%boundary_id == 0)then
            if ( .not. bcforce(ib)%add_to_total ) cycle component_loopq
            cd   = force%cd
            cdp  = force%cdp
            cl   = force%cl
            clp  = force%clp
            clcd = force%clcd
            cx   = force%cx
            cy   = force%cy
            cz   = force%cz
            cmx  = force%cmx
            cmy  = force%cmy
            cmz  = force%cmz
            cmxp = force%cmxp
            cmyp = force%cmyp
            cmzp = force%cmzp
            propeff = force%propeff
            rpowerx = force%powerx
            rpowery = force%powery
            rpowerz = force%powerz
            rotor_thrust = force%rotor_thrust
          else if                                                              &
          (design%function_data(ifcn)%component_data(k)%boundary_id == ib)then
            cd   = bcforce(ib)%cd
            cdp  = bcforce(ib)%cdp
            cl   = bcforce(ib)%cl
            clp  = bcforce(ib)%clp
            clcd = 0.0_dp ! only used on boundary_id == 0
            cx   = bcforce(ib)%cx
            cy   = bcforce(ib)%cy
            cz   = bcforce(ib)%cz
            cmx  = bcforce(ib)%cmx
            cmy  = bcforce(ib)%cmy
            cmz  = bcforce(ib)%cmz
            cmxp = bcforce(ib)%cmxp
            cmyp = bcforce(ib)%cmyp
            cmzp = bcforce(ib)%cmzp
            propeff = 0.0_dp ! only used on boundary_id == 0
            rpowerx = bcforce(ib)%powerx
            rpowery = bcforce(ib)%powery
            rpowerz = bcforce(ib)%powerz
            rotor_thrust = 0.0_dp ! only used on boundary_id == 0
          else
            cycle component_loopq
          endif

              weight  = design%function_data(ifcn)%component_data(k)%weight
              target  = design%function_data(ifcn)%component_data(k)%target
              power   = design%function_data(ifcn)%component_data(k)%power
              width   = design%function_data(ifcn)%timesteps(2) -              &
                        design%function_data(ifcn)%timesteps(1) + 1
              if ( .not.design%function_data(ifcn)%averaging ) width = 1.0_dp
              average = design%function_data(ifcn)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(ifcn)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(ifcn)%averaging) base = cd-target
                const = my_1/sref
              case ( cdp_id )
                if (.not.design%function_data(ifcn)%averaging) base = cdp-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(ifcn)%averaging) base = cl-target
                const = my_1/sref
              case ( clp_id )
                if (.not.design%function_data(ifcn)%averaging) base = clp-target
                const = my_1/sref
              case ( clcd_id )
                if (.not.design%function_data(ifcn)%averaging) base= clcd-target
                const = my_1/sref
              case ( cx_id )
                if (.not.design%function_data(ifcn)%averaging) base = cx-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(ifcn)%averaging) base = cy-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(ifcn)%averaging) base = cz-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmxp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmxp-target
                const = my_1/sref/bref
              case ( cmyp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmyp-target
                const = my_1/sref/cref
              case ( cmzp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmzp-target
                const = my_1/sref/bref
              case ( propeff_id )
                if(.not.design%function_data(ifcn)%averaging)base=propeff-target
                const = my_1
              case ( powerx_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowerz-target
                const = my_1/sref
              case ( rotor_thrust_id )
                if(.not.design%function_data(ifcn)%averaging)base=rotor_thrust &
                                                                 -target
                const = my_1/sref
              case default
                cycle component_loopq
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

          select case (design%function_data(ifcn)%component_data(k)%name)
          case ( cd_id, cdp_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*(dcx*(csa*dcsyda+csy*dcsada)  &
                                                - dcy*dsnyda                   &
                                                + dcz*(sna*dcsyda+csy*dsnada))
            dfda(2,ifcn) = dfda(2,ifcn) + factor*(dcx*(csa*dcsydb+csy*dcsadb)  &
                                                - dcy*dsnydb                   &
                                                + dcz*(sna*dcsydb+csy*dsnadb))
          case ( cl_id, clp_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*(-dcx*dsnada + dcz*dcsada)
            dfda(2,ifcn) = dfda(2,ifcn) + factor*(-dcx*dsnadb + dcz*dcsadb)
          case ( clcd_id )
            dfda(1,ifcn) = dfda(1,ifcn) + (cd*(-dcx*dsnada + dcz*dcsada)       &
                       - cl*(dcx*(csa*dcsyda+csy*dcsada)- dcy*dsnyda+          &
                         dcz*(sna*dcsyda+csy*dsnada)))/cd/cd*factor
            dfda(2,ifcn) = dfda(2,ifcn) + (cd*(-dcx*dsnadb + dcz*dcsadb)       &
                       - cl*(dcx*(csa*dcsydb+csy*dcsadb)- dcy*dsnydb+          &
                         dcz*(sna*dcsydb+csy*dsnadb)))/cd/cd*factor
          case ( rotor_thrust_id )
            sint = sin(thrust_angle)
            cost = cos(thrust_angle)
            dfda(1,ifcn) = dfda(1,ifcn) + cost*factor*(-dcx*dsnada+dcz*dcsada) &
                                   - sint*factor*(dcx*(csa*dcsyda+csy*dcsada)  &
                                   - dcy*dsnyda + dcz*(sna*dcsyda+csy*dsnada))
            dfda(2,ifcn) = dfda(2,ifcn) + cost*factor*(-dcx*dsnadb+dcz*dcsadb) &
                                   - sint*factor*(dcx*(csa*dcsydb+csy*dcsadb)  &
                                   - dcy*dsnydb + dcz*(sna*dcsydb+csy*dsnadb))
          case ( cx_id, cxp_id, cy_id, cyp_id, cz_id, czp_id,                  &
                 cmx_id, cmxp_id, cmy_id, cmyp_id, cmz_id, cmzp_id, propeff_id,&
                 powerx_id, powery_id, powerz_id)
            ! Nothing: do not depend on alpha/beta
          case default
          end select

        end do component_loopq

      endif local_face2

    end do quad_loop

  end subroutine pressurealpha


!================================ SKINFRICA ==================================80
!
!  This gets the derivative of skin friction wrt alpha
!
!=============================================================================80
  subroutine skinfrica(nelem,elem,x,y,z,qnode,amut,                            &
                       clva,cdva,clvb,cdvb,nnodes01,nbfacet,ibnode,            &
                       face_bit,f2ntb,nbnode,nbfaceq,face_bitq,f2nqb,n_tot)

    use info_depr,        only : xmach, re, tref, alpha, yaw, twod
    use fluid,            only : gamma, gm1, sutherland_constant
    use kinddefs,         only : dp
    use element_types,    only : elem_type
    use element_defs,     only : max_face_per_cell, max_node_per_cell
    use utilities,        only : cell_gradients

    integer, intent(in) :: nnodes01, n_tot, nbfacet, nbfaceq, nbnode, nelem

    integer, dimension(nbnode),    intent(in) :: ibnode
    integer, dimension(nbfacet,5), intent(in) :: f2ntb
    integer, dimension(nbfaceq,6), intent(in) :: f2nqb
    integer, dimension(nbfacet),   intent(in) :: face_bit
    integer, dimension(nbfaceq),   intent(in) :: face_bitq

    real(dp),    intent(inout) :: clva,cdva
    real(dp),    intent(inout) :: clvb,cdvb

    real(dp), dimension(n_tot,nnodes01),    intent(in) :: qnode
    real(dp), dimension(nnodes01),         intent(in) :: x,y,z
    real(dp), dimension(nnodes01),         intent(in) :: amut

    type(elem_type), dimension(nelem), intent(in) :: elem

    integer :: n,bnode1,bnode2,bnode3,bnode4,icell,ielem,nodes_local,node
    integer :: i,i_local,edges_local,face_2d

    integer, dimension(max_node_per_cell) :: c2n_cell
    integer, dimension(max_node_per_cell) :: node_map

    real(dp)    :: nxda, nyda, nzda
    real(dp)    :: nxla, nyla, nzla
    real(dp)    :: nxdb, nydb, nzdb
    real(dp)    :: nxlb, nylb, nzlb
    real(dp)    :: pi,xmr,conv
    real(dp)    :: cstar
    real(dp)    :: rmu,ux,uy,uz
    real(dp)    :: vx,vy,vz,wx,wy,wz
    real(dp)    :: xnorm,ynorm,znorm
    real(dp)    :: termx,termy,termz
    real(dp)    :: cell_vol
    real(dp)    :: forceda,forcela
    real(dp)    :: forcedb,forcelb
    real(dp)    :: x1,y1,z1,x2,y2,z2
    real(dp)    :: x3,y3,z3,x4,y4,z4

    real(dp), dimension(max_face_per_cell)      :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)      :: u_node, v_node, w_node
    real(dp), dimension(max_node_per_cell)      :: t_node, p_node, mu_node
    real(dp), dimension(max_node_per_cell)      :: x_node, y_node, z_node
    real(dp), dimension(4,max_node_per_cell)    :: q_node
    real(dp), dimension(4)                      :: gradx_cell, grady_cell
    real(dp), dimension(4)                      :: gradz_cell

    real(dp), parameter    :: my_haf = 0.5_dp
    real(dp), parameter    :: my_1   = 1.0_dp
    real(dp), parameter    :: my_2   = 2.0_dp
    real(dp), parameter    :: my_3   = 3.0_dp
    real(dp), parameter    :: my_4   = 4.0_dp
    real(dp), parameter    :: my_180 = 180.0_dp
    real(dp), parameter    :: c43    = my_4/my_3
    real(dp), parameter    :: c23    = my_2/my_3

  continue

    pi    = acos(-1.0_dp)
    conv  = my_180 / pi
    xmr   = my_1 / xmach / Re
    cstar = sutherland_constant / tref

! Contributions from tria boundary faces

    surface_trias : do n = 1, nbfacet

      force_flag_tria : if(face_bit(n) == 1) then

        bnode1 = ibnode(f2ntb(n,1))
        bnode2 = ibnode(f2ntb(n,2))
        bnode3 = ibnode(f2ntb(n,3))

        icell = f2ntb(n,4)
        ielem = f2ntb(n,5)

!       set some loop indices and local mapping arrays depending on whether
!       we are doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (elem(ielem)%local_f2e(face_2d,1) /=                              &
              elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = elem(ielem)%edge_per_cell

        end if

!       copy c2n and local_f2n arrays from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

!       compute cell averaged viscosity by looping over the nodes in the
!       element and gathering their contributions, then average at the end

        cell_vol = 0.0_dp

        rmu = 0.0_dp

        x_node(:)   = 0.0_dp
        y_node(:)   = 0.0_dp
        z_node(:)   = 0.0_dp
        u_node(:)   = 0.0_dp
        v_node(:)   = 0.0_dp
        w_node(:)   = 0.0_dp
        p_node(:)   = 0.0_dp
        t_node(:)   = 0.0_dp
        mu_node(:)  = 0.0_dp
        q_node(:,:) = 0.0_dp

        node_loop1 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          u_node(i) = qnode(2,node)/qnode(1,node)
          v_node(i) = qnode(3,node)/qnode(1,node)
          w_node(i) = qnode(4,node)/qnode(1,node)

          p_node(i)  = gm1*( qnode(5,node) - my_haf*qnode(1,node)*             &
                           ( u_node(i)*u_node(i) + v_node(i)*v_node(i) +       &
                             w_node(i)*w_node(i) ) )

          t_node(i)  = gamma*p_node(i)/qnode(1,node)

          mu_node(i) = viscosity_law( cstar, t_node(i) ) + amut(node)

          rmu = rmu + mu_node(i)

        end do node_loop1

!       now compute cell average by dividing by the number of nodes
!       that contributed

        rmu = rmu / real(nodes_local, dp)

!       now we get this boundary face's normal

          x1 = x(bnode1)
          y1 = y(bnode1)
          z1 = z(bnode1)

          x2 = x(bnode2)
          y2 = y(bnode2)
          z2 = z(bnode2)

          x3 = x(bnode3)
          y3 = y(bnode3)
          z3 = z(bnode3)

!       - sign for outward facing normal

        xnorm = -my_haf*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
        ynorm = -my_haf*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
        znorm = -my_haf*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )

        q_node(1,:) = u_node(:)
        q_node(2,:) = v_node(:)
        q_node(3,:) = w_node(:)
        q_node(4,:) = t_node(:)

        call cell_gradients(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            4, q_node, elem(ielem)%local_f2n,                  &
                            elem(ielem)%e2n_2d, gradx_cell, grady_cell,        &
                            gradz_cell, cell_vol, nx, ny, nz)

        ux = gradx_cell(1)
        vx = gradx_cell(2)
        wx = gradx_cell(3)

        uy = grady_cell(1)
        vy = grady_cell(2)
        wy = grady_cell(3)

        uz = gradz_cell(1)
        vz = gradz_cell(2)
        wz = gradz_cell(3)

!       now compute components of stress vector acting on the face

        termx = my_2*xmr*rmu*(xnorm*(c43*ux - c23*(vy + wz)) +                 &
                              ynorm*(uy + vx)                +                 &
                              znorm*(uz + wx))

        termy = my_2*xmr*rmu*(xnorm*(uy + vx)                +                 &
                              ynorm*(c43*vy - c23*(ux + wz)) +                 &
                              znorm*(vz + wy))

        termz = my_2*xmr*rmu*(xnorm*(uz + wx)                +                 &
                              ynorm*(vz + wy)                +                 &
                              znorm*(c43*wz - c23*(ux + vy)))

!  Now dot the stress vector acting on the surface face with
!  a unit vector in the drag (lift) direction.  This is the
!  magnitude of the friction force acting on the face in the
!  drag (lift) direction

!  Find unit vectors in drag and lift directions

!       nxd =   cos(alpha/conv) * cos(yaw/conv)
!       nyd = - sin(yaw/conv)
!       nzd =   sin(alpha/conv) * cos(yaw/conv)

        nxda = -sin(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)
        nyda = 0.0_dp
        nzda = cos(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)

        nxdb = -cos(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)
        nydb = -cos(yaw/conv) / conv
        nzdb = -sin(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)

!       nxl = - sin(alpha/conv)
!       nyl = 0.0_dp
!       nzl =   cos(alpha/conv)

        nxla = - cos(alpha/conv) * 1.0_dp / conv
        nyla = 0.0_dp
        nzla = -sin(alpha/conv) * 1.0_dp / conv

        nxlb = 0.0_dp
        nylb = 0.0_dp
        nzlb = 0.0_dp

!  Now do the dot product to get the force in the drag (lift) direction

!  I think the signs are right on the following two equations, but I
!  wouldn't stake my life on it.  They've got to do with the force
!  being on the body or on the fluid.  The way they are set right now
!  gives the logical results (increase in Cd, decrease in Cl).

!       forced = - (termx*nxd + termy*nyd + termz*nzd)
!       forcel = - (termx*nxl + termy*nyl + termz*nzl)

        forceda = - (termx*nxda + termy*nyda + termz*nzda)
        forcela = - (termx*nxla + termy*nyla + termz*nzla)

        forcedb = - (termx*nxdb + termy*nydb + termz*nzdb)
        forcelb = - (termx*nxlb + termy*nylb + termz*nzlb)

!  Now add things

        cdva = cdva + forceda
        clva = clva + forcela

        cdvb = cdvb + forcedb
        clvb = clvb + forcelb

! Moment coefficients do not depend on alpha

      endif force_flag_tria

    end do surface_trias

! Contributions from quad boundary faces

    surface_quads : do n = 1, nbfaceq

      force_flag_quad : if(face_bitq(n) == 1) then

        bnode1 = ibnode(f2nqb(n,1))
        bnode2 = ibnode(f2nqb(n,2))
        bnode3 = ibnode(f2nqb(n,3))
        bnode4 = ibnode(f2nqb(n,4))

        icell = f2nqb(n,5)
        ielem = f2nqb(n,6)

!       set some loop indices and local mapping arrays depending on whether
!       we are doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (elem(ielem)%local_f2e(face_2d,1) /=                              &
              elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = elem(ielem)%edge_per_cell

        end if

!       copy c2n and local_f2n arrays from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

!       compute cell averaged viscosity by looping over the nodes in the
!       element and gathering their contributions, then average at the end

        cell_vol = 0.0_dp

        rmu = 0.0_dp

        x_node(:)   = 0.0_dp
        y_node(:)   = 0.0_dp
        z_node(:)   = 0.0_dp
        u_node(:)   = 0.0_dp
        v_node(:)   = 0.0_dp
        w_node(:)   = 0.0_dp
        p_node(:)   = 0.0_dp
        t_node(:)   = 0.0_dp
        mu_node(:)  = 0.0_dp
        q_node(:,:) = 0.0_dp

        node_loop2 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          u_node(i) = qnode(2,node)/qnode(1,node)
          v_node(i) = qnode(3,node)/qnode(1,node)
          w_node(i) = qnode(4,node)/qnode(1,node)

          p_node(i)  = gm1*( qnode(5,node) - my_haf*qnode(1,node)*             &
                           ( u_node(i)*u_node(i) + v_node(i)*v_node(i) +       &
                             w_node(i)*w_node(i) ) )

          t_node(i)  = gamma*p_node(i)/qnode(1,node)

          mu_node(i) = viscosity_law( cstar, t_node(i) ) + amut(node)

          rmu = rmu + mu_node(i)

        end do node_loop2

!       now compute cell average by dividing by the number of nodes
!       that contributed

        rmu = rmu / real(nodes_local, dp)

!       now we get this boundary face's normal

          x1 = x(bnode1)
          y1 = y(bnode1)
          z1 = z(bnode1)

          x2 = x(bnode2)
          y2 = y(bnode2)
          z2 = z(bnode2)

          x3 = x(bnode3)
          y3 = y(bnode3)
          z3 = z(bnode3)

          x4 = x(bnode4)
          y4 = y(bnode4)
          z4 = z(bnode4)

!       - sign for outward facing normal

        xnorm = -my_haf*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
        ynorm = -my_haf*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
        znorm = -my_haf*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )

        q_node(1,:) = u_node(:)
        q_node(2,:) = v_node(:)
        q_node(3,:) = w_node(:)
        q_node(4,:) = t_node(:)

        call cell_gradients(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            4, q_node, elem(ielem)%local_f2n,                  &
                            elem(ielem)%e2n_2d, gradx_cell, grady_cell,        &
                            gradz_cell, cell_vol, nx, ny, nz)

        ux = gradx_cell(1)
        vx = gradx_cell(2)
        wx = gradx_cell(3)

        uy = grady_cell(1)
        vy = grady_cell(2)
        wy = grady_cell(3)

        uz = gradz_cell(1)
        vz = gradz_cell(2)
        wz = gradz_cell(3)

!       now compute components of stress vector acting on the face

        termx = my_2*xmr*rmu*(xnorm*(c43*ux - c23*(vy + wz)) +                 &
                              ynorm*(uy + vx)                +                 &
                              znorm*(uz + wx))

        termy = my_2*xmr*rmu*(xnorm*(uy + vx)                +                 &
                              ynorm*(c43*vy - c23*(ux + wz)) +                 &
                              znorm*(vz + wy))

        termz = my_2*xmr*rmu*(xnorm*(uz + wx)                +                 &
                              ynorm*(vz + wy)                +                 &
                              znorm*(c43*wz - c23*(ux + vy)))

!  Now dot the stress vector acting on the surface face with
!  a unit vector in the drag (lift) direction.  This is the
!  magnitude of the friction force acting on the face in the
!  drag (lift) direction

!  Find unit vectors in drag and lift directions

!       nxd =   cos(alpha/conv) * cos(yaw/conv)
!       nyd = - sin(yaw/conv)
!       nzd =   sin(alpha/conv) * cos(yaw/conv)

        nxda = -sin(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)
        nyda = 0.0_dp
        nzda = cos(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)

        nxdb = -cos(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)
        nydb = -cos(yaw/conv) / conv
        nzdb = -sin(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)

!       nxl = - sin(alpha/conv)
!       nyl = 0.0_dp
!       nzl =   cos(alpha/conv)

        nxla = - cos(alpha/conv) * 1.0_dp / conv
        nyla = 0.0_dp
        nzla = -sin(alpha/conv) * 1.0_dp / conv

        nxlb = 0.0_dp
        nylb = 0.0_dp
        nzlb = 0.0_dp

!  Now do the dot product to get the force in the drag (lift) direction

!  I think the signs are right on the following two equations, but I
!  wouldn't stake my life on it.  They've got to do with the force
!  being on the body or on the fluid.  The way they are set right now
!  gives the logical results (increase in Cd, decrease in Cl).

!       forced = - (termx*nxd + termy*nyd + termz*nzd)
!       forcel = - (termx*nxl + termy*nyl + termz*nzl)

        forceda = - (termx*nxda + termy*nyda + termz*nzda)
        forcela = - (termx*nxla + termy*nyla + termz*nzla)

        forcedb = - (termx*nxdb + termy*nydb + termz*nzdb)
        forcelb = - (termx*nxlb + termy*nylb + termz*nzlb)

!  Now add things

        cdva = cdva + forceda
        clva = clva + forcela

        cdvb = cdvb + forcedb
        clvb = clvb + forcelb

! Moment coefficients do not depend on alpha

      endif force_flag_quad

    end do surface_quads

  end subroutine skinfrica


!================================ DRDALPHAI ==================================80
!
! Linearization of inviscid residual wrt alpha
!
!  Incompressible version
!
!=============================================================================80

  subroutine drdalphai(nnodes0,nbnode,ibnode,qnode,rlam,coltag,dLdX,nfunctions,&
                       ndim,adim,nnodes01,bxn,byn,bzn,bfacespeed)

    use ivals,               only : u0,v0,w0,p0
    use info_depr,           only : alpha,yaw,beta
    use grid_motion_helpers, only : need_grid_velocity
    use rotors,              only : rotor_flag,vinf_ratio,alternate_freestream,&
                                    vinf_input_ratio
    use kinddefs,            only : dp

    integer, intent(in) :: nnodes0,ndim,adim,nbnode,nfunctions,nnodes01

    integer, dimension(nbnode), intent(in) :: ibnode

    real(dp), dimension(ndim,nnodes01),            intent(in)    :: qnode
    real(dp), dimension(adim,nnodes01,nfunctions), intent(in)    :: rlam
    real(dp), dimension(adim,nnodes01),            intent(in)    :: coltag
    real(dp), dimension(nbnode),                   intent(in)    :: bxn
    real(dp), dimension(nbnode),                   intent(in)    :: byn
    real(dp), dimension(nbnode),                   intent(in)    :: bzn
    real(dp), dimension(nbnode),                   intent(in)    :: bfacespeed
    real(dp), dimension(2,nfunctions),             intent(inout) :: dLdX

    integer :: j,n,node

    real(dp) :: pi,conv,xnorm,ynorm,znorm,face_speed
    real(dp) :: p0a,u0a,v0a,w0a
    real(dp) :: p0b,u0b,v0b,w0b
    real(dp) :: area,term1,term2,term3,term4
    real(dp) :: flux1a,flux2a,flux3a,flux4a
    real(dp) :: flux1b,flux2b,flux3b,flux4b

    real(dp), dimension(4)   :: ql, qr
    real(dp), dimension(4,4) :: df_dql, df_dqr

  continue

    pi = 4.0_dp*atan(1.0_dp)
    conv = 180.0_dp/pi

    p0a = 0.0_dp
    p0b = 0.0_dp

    u0a = -sin(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)
    v0a = 0.0_dp
    w0a = cos(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)

    u0b = -cos(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)
    v0b = -cos(yaw/conv) / conv
    w0b = -sin(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)

    if ( rotor_flag ) then
      u0a = vinf_ratio * u0a
      v0a = vinf_ratio * v0a
      w0a = vinf_ratio * w0a

      u0b = vinf_ratio * u0b
      v0b = vinf_ratio * v0b
      w0b = vinf_ratio * w0b
    endif

    if ( alternate_freestream ) then
      u0a = vinf_input_ratio * u0a
      v0a = vinf_input_ratio * v0a
      w0a = vinf_input_ratio * w0a

      u0b = vinf_input_ratio * u0b
      v0b = vinf_input_ratio * v0b
      w0b = vinf_input_ratio * w0b
    endif

    loop_bnodes2 : do n = 1, nbnode

      node = ibnode(n)

      if (node <= nnodes0) then

        xnorm = bxn(n)
        ynorm = byn(n)
        znorm = bzn(n)

        area = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)

        xnorm = xnorm / area
        ynorm = ynorm / area
        znorm = znorm / area

        face_speed = 0.0_dp

        if (need_grid_velocity) then
          face_speed = bfacespeed(n)
        end if

        ql(1) = qnode(1,node)
        ql(2) = qnode(2,node)
        ql(3) = qnode(3,node)
        ql(4) = qnode(4,node)

        qr(1) = p0
        qr(2) = u0
        qr(3) = v0
        qr(4) = w0

        call dfroe_i(xnorm,ynorm,znorm,area,face_speed,beta,ql,qr,df_dql,df_dqr)

        flux1a = df_dqr(1,1)*p0a+df_dqr(1,2)*u0a+df_dqr(1,3)*v0a+df_dqr(1,4)*w0a
        flux2a = df_dqr(2,1)*p0a+df_dqr(2,2)*u0a+df_dqr(2,3)*v0a+df_dqr(2,4)*w0a
        flux3a = df_dqr(3,1)*p0a+df_dqr(3,2)*u0a+df_dqr(3,3)*v0a+df_dqr(3,4)*w0a
        flux4a = df_dqr(4,1)*p0a+df_dqr(4,2)*u0a+df_dqr(4,3)*v0a+df_dqr(4,4)*w0a

        flux1b = df_dqr(1,1)*p0b+df_dqr(1,2)*u0b+df_dqr(1,3)*v0b+df_dqr(1,4)*w0b
        flux2b = df_dqr(2,1)*p0b+df_dqr(2,2)*u0b+df_dqr(2,3)*v0b+df_dqr(2,4)*w0b
        flux3b = df_dqr(3,1)*p0b+df_dqr(3,2)*u0b+df_dqr(3,3)*v0b+df_dqr(3,4)*w0b
        flux4b = df_dqr(4,1)*p0b+df_dqr(4,2)*u0b+df_dqr(4,3)*v0b+df_dqr(4,4)*w0b

!  Now we can form this node's contribution to the
!  matrix-vector product in the sensitivity derivative

        do j = 1, nfunctions
          term1 = flux1a * coltag(1,node)*rlam(1,node,j)
          term2 = flux2a * coltag(2,node)*rlam(2,node,j)
          term3 = flux3a * coltag(3,node)*rlam(3,node,j)
          term4 = flux4a * coltag(4,node)*rlam(4,node,j)
          dLdX(1,j) = dLdX(1,j) + term1 + term2 + term3 + term4
          term1 = flux1b * coltag(1,node)*rlam(1,node,j)
          term2 = flux2b * coltag(2,node)*rlam(2,node,j)
          term3 = flux3b * coltag(3,node)*rlam(3,node,j)
          term4 = flux4b * coltag(4,node)*rlam(4,node,j)
          dLdX(2,j) = dLdX(2,j) + term1 + term2 + term3 + term4
        end do

      end if

    end do loop_bnodes2

  end subroutine drdalphai


!================================ DFDALPHAI ==================================80
!
! Calculates the gradient of the cost function w.r.t. the design
! variable alpha
!
!  Incompressible version
!
!=============================================================================80

  subroutine dfdalphai(nnodes01,qnode,x,y,z,dfda,amut,nbound,bc,design,force,  &
                       bcforce,ndim,nelem,elem)

    use kinddefs,          only : dp
    use design_types,      only : design_type
    use bc_types,          only : bcgrid_type
    use bc_names,          only : bc_used_for_force_calculation,               &
                                  bc_has_skin_friction,                        &
                                  bc_is_flow_through
    use refgeom,           only : sref
    use force_types,       only : force_type
    use forces,            only : cl_id, cd_id, clv_id, cdv_id, clcd_id,       &
                                  rotor_thrust_id
    use fun3d_constants,   only : my_1
    use element_types,     only : elem_type
    use custom_transforms, only : thrust_angle

    integer, intent(in) :: nnodes01, ndim, nbound, nelem

    real(dp), dimension(nnodes01),      intent(in) :: x,y,z
    real(dp), dimension(ndim,nnodes01), intent(in) :: qnode
    real(dp), dimension(nnodes01),      intent(in) :: amut

    type(design_type),                    intent(in) :: design
    type(force_type),                     intent(in) :: force
    type(bcgrid_type), dimension(nbound), intent(in) :: bc
    type(force_type),  dimension(nbound), intent(in) :: bcforce

    real(dp), dimension(2,design%nfunctions), intent(out) :: dfda

    type(elem_type), dimension(nelem), intent(in) :: elem

    integer :: ib, j, k

    real(dp) :: factor,sint,cost,rotor_thrust
    real(dp) :: clva,cdva,clvb,cdvb
    real(dp) :: cl,cd,clv,cdv,clcd
    real(dp) :: weight,target,power
    real(dp) :: width,average,base
    real(dp) :: const

  continue

    const = 0.0_dp

    dfda = 0.0_dp

    fcn_loop : do j = 1, design%nfunctions

      do ib = 1, nbound

        if( bc_is_flow_through( bc(ib)%ibc ) ) cycle
        if(bc_used_for_force_calculation( bc(ib)%ibc ) ) then

          call pressurealphai(nnodes01,dfda,design,                            &
                              bc(ib)%nbnode,bc(ib)%nbfacet,                    &
                              bc(ib)%face_bit,                                 &
                              bc(ib)%ibnode,bc(ib)%f2ntb,x,y,z,qnode,ib,j,     &
                              nbound,force,bcforce,ndim,bc(ib)%nbfaceq,        &
                              bc(ib)%f2nqb,bc(ib)%face_bitq)

          if( bc_has_skin_friction( bc(ib)%ibc ) ) then

            clva = 0.0_dp
            cdva = 0.0_dp
            clvb = 0.0_dp
            cdvb = 0.0_dp

            call skinfricai(x,y,z,qnode,amut,clva,cdva,clvb,cdvb,nnodes01,     &
                            bc(ib)%nbfacet,bc(ib)%ibnode,bc(ib)%face_bit,      &
                            bc(ib)%f2ntb,bc(ib)%nbnode,ndim,bc(ib)%nbfaceq,    &
                            bc(ib)%f2nqb,bc(ib)%face_bitq,nelem,elem)

! Note that there are no alpha/beta derivatives of cx,cy,cz,cxv,cyv,czv,cmx,
! cmy,cmz,cmxv,cmyv,cmzv,propeff,powerx,powery,powerz

            component_loopv : do k = 1, design%function_data(j)%ncomponents

              if(design%function_data(j)%component_data(k)%boundary_id == 0)then
                if ( .not. bcforce(ib)%add_to_total ) cycle component_loopv
                cd   = force%cd
                cdv  = force%cdv
                cl   = force%cl
                clv  = force%clv
                clcd = force%clcd
                rotor_thrust = force%rotor_thrust
              else if                                                          &
              (design%function_data(j)%component_data(k)%boundary_id == ib) then
                cd   = bcforce(ib)%cd
                cdv  = bcforce(ib)%cdv
                cl   = bcforce(ib)%cl
                clv  = bcforce(ib)%clv
                clcd = 0.0_dp ! only used on boundary_id == 0
                rotor_thrust = 0.0_dp ! only used on boundary_id == 0
              else
                cycle component_loopv
              endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdv_id )
                if (.not.design%function_data(j)%averaging) base = cdv-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clv_id )
                if (.not.design%function_data(j)%averaging) base = clv-target
                const = my_1/sref
              case ( clcd_id )
                if (.not.design%function_data(j)%averaging) base = clcd-target
                const = my_1/sref
              case ( rotor_thrust_id )
                if (.not.design%function_data(j)%averaging)base = rotor_thrust &
                                                                - target
                const = my_1/sref
              case default
                cycle component_loopv
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id, cdv_id )
                dfda(1,j) = dfda(1,j) + factor*cdva
                dfda(2,j) = dfda(2,j) + factor*cdvb
              case ( cl_id, clv_id )
                dfda(1,j) = dfda(1,j) + factor*clva
                dfda(2,j) = dfda(2,j) + factor*clvb
              case ( clcd_id )
                dfda(1,j) = dfda(1,j) + factor*(1.0_dp/cd*clva - cl/cd/cd*cdva)
                dfda(2,j) = dfda(2,j) + factor*(1.0_dp/cd*clvb - cl/cd/cd*cdvb)
              case ( rotor_thrust_id )
                sint = sin(thrust_angle)
                cost = cos(thrust_angle)
                dfda(1,j) = dfda(1,j) + cost*factor*clva - sint*factor*cdva
                dfda(2,j) = dfda(2,j) + cost*factor*clvb - sint*factor*cdvb
              case default
                cycle component_loopv
              end select

            end do component_loopv

          endif

        endif

      end do

    end do fcn_loop

  end subroutine dfdalphai


!================================ PRESSUREALPHAI =============================80
!
!  This gets the derivative of pressure forces wrt alpha
!
!=============================================================================80

  subroutine pressurealphai(nnodes01,dfda,design,nbnode,nbfacet,face_bit,      &
                            ibnode,f2ntb,x,y,z,qnode,ib,ifcn,nbound,force,     &
                            bcforce,ndim,nbfaceq,f2nqb,face_bitq)

    use info_depr,         only : alpha,yaw
    use kinddefs,          only : dp
    use design_types,      only : design_type
    use refgeom,           only : sref,bref,cref
    use force_types,       only : force_type
    use forces,            only : cd_id, cdp_id, cl_id, clp_id, clcd_id, cx_id,&
                                  cy_id, cz_id, cxp_id, cyp_id, czp_id, cmx_id,&
                                  cmxp_id, cmy_id, cmyp_id, cmz_id, cmzp_id,   &
                                  propeff_id, powerx_id, powery_id, powerz_id, &
                                  rotor_thrust_id
    use fun3d_constants,   only : my_1
    use custom_transforms, only : thrust_angle

    integer, intent(in) :: nnodes01,nbnode, ndim
    integer,                             intent(in)    :: nbfacet, ib, ifcn
    integer,                             intent(in)    :: nbound, nbfaceq

    integer,     dimension(nbnode),      intent(in)    :: ibnode
    integer,     dimension(nbfacet),     intent(in)    :: face_bit
    integer,     dimension(nbfaceq),     intent(in)    :: face_bitq
    integer,     dimension(nbfacet,5),   intent(in)    :: f2ntb
    integer,     dimension(nbfaceq,6),   intent(in)    :: f2nqb

    integer                                            :: n, k, node4
    integer                                            :: node1,node2,node3

    real(dp), dimension(nnodes01),       intent(in)    :: x,y,z
    real(dp), dimension(ndim,nnodes01),  intent(in)    :: qnode

    real(dp)                                           :: factor,pi,const
    real(dp)                                           :: width,average,base
    real(dp)                                           :: conv,csa,sna,csy
    real(dp)                                           :: x1,x2,x3,y1,y2,y3
    real(dp)                                           :: z1,z2,z3,x4,y4,z4
    real(dp)                                           :: ax,ay,az,bx,by,bz
    real(dp)                                           :: xnorm,ynorm,znorm
    real(dp)                                           :: p1,p2,p3,p4
    real(dp)                                           :: press,cp,dcx,dcy,dcz
    real(dp)                                           :: dsnada,dcsada,dsnyda
    real(dp)                                           :: dsnadb,dcsadb,dsnydb
    real(dp)                                           :: dcsyda,dcsydb

    real(dp)    :: cl,cd,clp,cdp,clcd,cx,cy,cz,cxp,cyp,czp,cmx,cmy,cmz,sint,cost
    real(dp)    :: cmxp,cmyp,cmzp,propeff,rpowerx,rpowery,rpowerz,rotor_thrust
    real(dp)    :: power,weight,target

    type(design_type),                   intent(in) :: design
    type(force_type),                    intent(in) :: force
    type(force_type), dimension(nbound), intent(in) :: bcforce

    real(dp), dimension(2,design%nfunctions),    intent(inout) :: dfda

    real(dp), parameter :: my_haf = 0.5_dp

  continue

    const = 0.0_dp

    pi = 4.0_dp*atan(1.0_dp)
    conv = 180.0_dp/pi
    csa=cos(alpha/conv)
    sna=sin(alpha/conv)
    csy=cos(yaw/conv)

      do n = 1, nbfacet
        if(face_bit(n) == 1)then
          node1 = ibnode(f2ntb(n,1))
          node2 = ibnode(f2ntb(n,2))
          node3 = ibnode(f2ntb(n,3))

          x1 = x(node1)
          y1 = y(node1)
          z1 = z(node1)

          x2 = x(node2)
          y2 = y(node2)
          z2 = z(node2)

          x3 = x(node3)
          y3 = y(node3)
          z3 = z(node3)

          ax = x2 - x1
          ay = y2 - y1
          az = z2 - z1

          bx = x3 - x1
          by = y3 - y1
          bz = z3 - z1

!  norm points outward, away from grid interior.
!  norm magnitude is area of surface triangle.

          xnorm =-0.5_dp*(ay*bz - az*by)
          ynorm = 0.5_dp*(ax*bz - az*bx)
          znorm =-0.5_dp*(ax*by - ay*bx)

          p1 = qnode(1,node1)

          p2 = qnode(1,node2)

          p3 = qnode(1,node3)

          press = (p1 + p2 + p3)/3.0_dp
          cp    = 2.0_dp*(press-1.0_dp)

          dcx = cp*xnorm
          dcy = cp*ynorm
          dcz = cp*znorm

          dsnada = cos(alpha/conv) * 1.0_dp / conv
          dcsada = - sin(alpha/conv) * 1.0_dp / conv
          dsnyda = 0.0_dp
          dcsyda = 0.0_dp

          dsnadb = 0.0_dp
          dcsadb = 0.0_dp
          dsnydb = cos(yaw/conv) * 1.0_dp / conv
          dcsydb = - sin(yaw/conv) * 1.0_dp / conv

! Moment coefficients do not depend on alpha

          component_loop : do k = 1, design%function_data(ifcn)%ncomponents

            if                                                                 &
            (design%function_data(ifcn)%component_data(k)%boundary_id == 0)then
              if ( .not. bcforce(ib)%add_to_total ) cycle component_loop
              cd   = force%cd
              cdp  = force%cdp
              cl   = force%cl
              clp  = force%clp
              clcd = force%clcd
              cx   = force%cx
              cy   = force%cy
              cz   = force%cz
              cxp  = force%cxp
              cyp  = force%cyp
              czp  = force%czp
              cmx  = force%cmx
              cmy  = force%cmy
              cmz  = force%cmz
              cmxp = force%cmxp
              cmyp = force%cmyp
              cmzp = force%cmzp
              propeff = force%propeff
              rpowerx = force%powerx
              rpowery = force%powery
              rpowerz = force%powerz
              rotor_thrust = force%rotor_thrust
            else if                                                            &
            (design%function_data(ifcn)%component_data(k)%boundary_id == ib)then
              cd   = bcforce(ib)%cd
              cdp  = bcforce(ib)%cdp
              cl   = bcforce(ib)%cl
              clp  = bcforce(ib)%clp
              clcd = 0.0_dp ! only used on boundary_id == 0
              cx   = bcforce(ib)%cx
              cy   = bcforce(ib)%cy
              cz   = bcforce(ib)%cz
              cxp  = bcforce(ib)%cxp
              cyp  = bcforce(ib)%cyp
              czp  = bcforce(ib)%czp
              cmx  = bcforce(ib)%cmx
              cmy  = bcforce(ib)%cmy
              cmz  = bcforce(ib)%cmz
              cmxp = bcforce(ib)%cmxp
              cmyp = bcforce(ib)%cmyp
              cmzp = bcforce(ib)%cmzp
              propeff = 0.0_dp ! only used on boundary_id == 0
              rpowerx = bcforce(ib)%powerx
              rpowery = bcforce(ib)%powery
              rpowerz = bcforce(ib)%powerz
              rotor_thrust = 0.0_dp ! only used on boundary_id == 0
            else
              cycle component_loop
            endif

              weight  = design%function_data(ifcn)%component_data(k)%weight
              target  = design%function_data(ifcn)%component_data(k)%target
              power   = design%function_data(ifcn)%component_data(k)%power
              width   = design%function_data(ifcn)%timesteps(2) -              &
                        design%function_data(ifcn)%timesteps(1) + 1
              if ( .not.design%function_data(ifcn)%averaging ) width = 1.0_dp
              average = design%function_data(ifcn)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(ifcn)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(ifcn)%averaging) base = cd-target
                const = my_1/sref
              case ( cdp_id )
                if (.not.design%function_data(ifcn)%averaging) base = cdp-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(ifcn)%averaging) base = cl-target
                const = my_1/sref
              case ( clp_id )
                if (.not.design%function_data(ifcn)%averaging) base = clp-target
                const = my_1/sref
              case ( clcd_id )
                if (.not.design%function_data(ifcn)%averaging) base= clcd-target
                const = my_1/sref
              case ( cx_id )
                if (.not.design%function_data(ifcn)%averaging) base = cx-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(ifcn)%averaging) base = cy-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(ifcn)%averaging) base = cz-target
                const = my_1/sref
              case ( cxp_id )
                if (.not.design%function_data(ifcn)%averaging) base = cxp-target
                const = my_1/sref
              case ( cyp_id )
                if (.not.design%function_data(ifcn)%averaging) base = cyp-target
                const = my_1/sref
              case ( czp_id )
                if (.not.design%function_data(ifcn)%averaging) base = czp-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmxp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmxp-target
                const = my_1/sref/bref
              case ( cmyp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmyp-target
                const = my_1/sref/cref
              case ( cmzp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmzp-target
                const = my_1/sref/bref
              case ( propeff_id )
                if(.not.design%function_data(ifcn)%averaging)base=propeff-target
                const = my_1
              case ( powerx_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowerz-target
                const = my_1/sref
             case ( rotor_thrust_id )
                if(.not.design%function_data(ifcn)%averaging)base=rotor_thrust &
                                                                 -target
                const = my_1/sref
              case default
                cycle component_loop
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

            select case (design%function_data(ifcn)%component_data(k)%name)
            case ( cd_id, cdp_id )
              dfda(1,ifcn) = dfda(1,ifcn) + factor*(dcx*(csa*dcsyda+csy*dcsada)&
                                               - dcy*dsnyda                    &
                                               + dcz*(sna*dcsyda+csy*dsnada))
              dfda(2,ifcn) = dfda(2,ifcn) + factor*(dcx*(csa*dcsydb+csy*dcsadb)&
                                               - dcy*dsnydb                    &
                                               + dcz*(sna*dcsydb+csy*dsnadb))
            case ( cl_id, clp_id )
              dfda(1,ifcn) = dfda(1,ifcn) + factor*(-dcx*dsnada + dcz*dcsada)
              dfda(2,ifcn) = dfda(2,ifcn) + factor*(-dcx*dsnadb + dcz*dcsadb)
            case ( clcd_id )
              dfda(1,ifcn) = dfda(1,ifcn) + (cd*(-dcx*dsnada + dcz*dcsada)     &
                         - cl*(dcx*(csa*dcsyda+csy*dcsada)- dcy*dsnyda+        &
                           dcz*(sna*dcsyda+csy*dsnada)))/cd/cd*factor
              dfda(2,ifcn) = dfda(2,ifcn) + (cd*(-dcx*dsnadb + dcz*dcsadb)     &
                         - cl*(dcx*(csa*dcsydb+csy*dcsadb)- dcy*dsnydb+        &
                           dcz*(sna*dcsydb+csy*dsnadb)))/cd/cd*factor
            case ( rotor_thrust_id )
              sint = sin(thrust_angle)
              cost = cos(thrust_angle)
              dfda(1,ifcn)= dfda(1,ifcn) + cost*factor*(-dcx*dsnada+dcz*dcsada)&
                                     - sint*factor*(dcx*(csa*dcsyda+csy*dcsada)&
                                     - dcy*dsnyda + dcz*(sna*dcsyda+csy*dsnada))
              dfda(2,ifcn)= dfda(2,ifcn) + cost*factor*(-dcx*dsnadb+dcz*dcsadb)&
                                     - sint*factor*(dcx*(csa*dcsydb+csy*dcsadb)&
                                     - dcy*dsnydb + dcz*(sna*dcsydb+csy*dsnadb))
            case ( cx_id, cxp_id, cy_id, cyp_id, cz_id, czp_id,                &
                   cmx_id, cmxp_id, cmy_id, cmyp_id, cmz_id, cmzp_id,          &
                   propeff_id, powerx_id, powery_id, powerz_id)
              ! Nothing: do not depend on alpha/beta
            case default
            end select

          end do component_loop

        endif

      end do

      do n = 1, nbfaceq
        if(face_bitq(n) == 1)then
          node1 = ibnode(f2nqb(n,1))
          node2 = ibnode(f2nqb(n,2))
          node3 = ibnode(f2nqb(n,3))
          node4 = ibnode(f2nqb(n,4))

          x1 = x(node1)
          y1 = y(node1)
          z1 = z(node1)

          x2 = x(node2)
          y2 = y(node2)
          z2 = z(node2)

          x3 = x(node3)
          y3 = y(node3)
          z3 = z(node3)

          x4 = x(node4)
          y4 = y(node4)
          z4 = z(node4)

!         quad normal computed as 1/2 the  cross product of the 2 diagonals
!         change sign to point away from interior

          xnorm = -my_haf*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
          ynorm = -my_haf*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
          znorm = -my_haf*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )

          p1 = qnode(1,node1)
          p2 = qnode(1,node2)
          p3 = qnode(1,node3)
          p4 = qnode(1,node4)

          press = (p1 + p2 + p3 + p4)/4.0_dp
          cp    = 2.0_dp*(press-1.0_dp)

          dcx = cp*xnorm
          dcy = cp*ynorm
          dcz = cp*znorm

          dsnada = cos(alpha/conv) * 1.0_dp / conv
          dcsada = - sin(alpha/conv) * 1.0_dp / conv
          dsnyda = 0.0_dp
          dcsyda = 0.0_dp

          dsnadb = 0.0_dp
          dcsadb = 0.0_dp
          dsnydb = cos(yaw/conv) * 1.0_dp / conv
          dcsydb = - sin(yaw/conv) * 1.0_dp / conv

! Moment coefficients do not depend on alpha

          component_loop2 : do k = 1, design%function_data(ifcn)%ncomponents

            if (design%function_data(ifcn)%component_data(k)%boundary_id==0)then
              if ( .not. bcforce(ib)%add_to_total ) cycle component_loop2
              cd   = force%cd
              cdp  = force%cdp
              cl   = force%cl
              clp  = force%clp
              clcd = force%clcd
              cx   = force%cx
              cy   = force%cy
              cz   = force%cz
              cxp  = force%cxp
              cyp  = force%cyp
              czp  = force%czp
              cmx  = force%cmx
              cmy  = force%cmy
              cmz  = force%cmz
              cmxp = force%cmxp
              cmyp = force%cmyp
              cmzp = force%cmzp
              propeff = force%propeff
              rpowerx = force%powerx
              rpowery = force%powery
              rpowerz = force%powerz
              rotor_thrust = force%rotor_thrust
            else if                                                            &
            (design%function_data(ifcn)%component_data(k)%boundary_id == ib)then
              cd   = bcforce(ib)%cd
              cdp  = bcforce(ib)%cdp
              cl   = bcforce(ib)%cl
              clp  = bcforce(ib)%clp
              clcd = 0.0_dp ! only used on boundary_id == 0
              cx   = bcforce(ib)%cx
              cy   = bcforce(ib)%cy
              cz   = bcforce(ib)%cz
              cxp  = bcforce(ib)%cxp
              cyp  = bcforce(ib)%cyp
              czp  = bcforce(ib)%czp
              cmx  = bcforce(ib)%cmx
              cmy  = bcforce(ib)%cmy
              cmz  = bcforce(ib)%cmz
              cmxp = bcforce(ib)%cmxp
              cmyp = bcforce(ib)%cmyp
              cmzp = bcforce(ib)%cmzp
              propeff = 0.0_dp ! only used on boundary_id == 0
              rpowerx = bcforce(ib)%powerx
              rpowery = bcforce(ib)%powery
              rpowerz = bcforce(ib)%powerz
              rotor_thrust = 0.0_dp ! only used on boundary_id == 0
            else
              cycle component_loop2
            endif

              weight  = design%function_data(ifcn)%component_data(k)%weight
              target  = design%function_data(ifcn)%component_data(k)%target
              power   = design%function_data(ifcn)%component_data(k)%power
              width   = design%function_data(ifcn)%timesteps(2) -              &
                        design%function_data(ifcn)%timesteps(1) + 1
              if ( .not.design%function_data(ifcn)%averaging ) width = 1.0_dp
              average = design%function_data(ifcn)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(ifcn)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(ifcn)%averaging) base = cd-target
                const = my_1/sref
              case ( cdp_id )
                if (.not.design%function_data(ifcn)%averaging) base = cdp-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(ifcn)%averaging) base = cl-target
                const = my_1/sref
              case ( clp_id )
                if (.not.design%function_data(ifcn)%averaging) base = clp-target
                const = my_1/sref
              case ( clcd_id )
                if (.not.design%function_data(ifcn)%averaging) base= clcd-target
                const = my_1/sref
              case ( cx_id )
                if (.not.design%function_data(ifcn)%averaging) base = cx-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(ifcn)%averaging) base = cy-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(ifcn)%averaging) base = cz-target
                const = my_1/sref
              case ( cxp_id )
                if (.not.design%function_data(ifcn)%averaging) base = cxp-target
                const = my_1/sref
              case ( cyp_id )
                if (.not.design%function_data(ifcn)%averaging) base = cyp-target
                const = my_1/sref
              case ( czp_id )
                if (.not.design%function_data(ifcn)%averaging) base = czp-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(ifcn)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmxp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmxp-target
                const = my_1/sref/bref
              case ( cmyp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmyp-target
                const = my_1/sref/cref
              case ( cmzp_id )
                if (.not.design%function_data(ifcn)%averaging)base = cmzp-target
                const = my_1/sref/bref
              case ( propeff_id )
                if(.not.design%function_data(ifcn)%averaging)base=propeff-target
                const = my_1
              case ( powerx_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if(.not.design%function_data(ifcn)%averaging)base=rpowerz-target
                const = my_1/sref
             case ( rotor_thrust_id )
                if(.not.design%function_data(ifcn)%averaging)base=rotor_thrust &
                                                                 -target
                const = my_1/sref
              case default
                cycle component_loop2
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

            select case (design%function_data(ifcn)%component_data(k)%name)
            case ( cd_id, cdp_id )
              dfda(1,ifcn) = dfda(1,ifcn) + factor*(dcx*(csa*dcsyda+csy*dcsada)&
                                              - dcy*dsnyda                     &
                                              + dcz*(sna*dcsyda+csy*dsnada))
              dfda(2,ifcn) = dfda(2,ifcn) + factor*(dcx*(csa*dcsydb+csy*dcsadb)&
                                              - dcy*dsnydb                     &
                                              + dcz*(sna*dcsydb+csy*dsnadb))
            case ( cl_id, clp_id )
              dfda(1,ifcn) = dfda(1,ifcn) + factor*(-dcx*dsnada + dcz*dcsada)
              dfda(2,ifcn) = dfda(2,ifcn) + factor*(-dcx*dsnadb + dcz*dcsadb)
            case ( clcd_id )
              dfda(1,ifcn) = dfda(1,ifcn) + (cd*(-dcx*dsnada + dcz*dcsada)     &
                         - cl*(dcx*(csa*dcsyda+csy*dcsada)- dcy*dsnyda+        &
                           dcz*(sna*dcsyda+csy*dsnada)))/cd/cd*factor
              dfda(2,ifcn) = dfda(2,ifcn) + (cd*(-dcx*dsnadb + dcz*dcsadb)     &
                         - cl*(dcx*(csa*dcsydb+csy*dcsadb)- dcy*dsnydb+        &
                           dcz*(sna*dcsydb+csy*dsnadb)))/cd/cd*factor
            case ( rotor_thrust_id )
              sint = sin(thrust_angle)
              cost = cos(thrust_angle)
              dfda(1,ifcn)= dfda(1,ifcn) + cost*factor*(-dcx*dsnada+dcz*dcsada)&
                                     - sint*factor*(dcx*(csa*dcsyda+csy*dcsada)&
                                     - dcy*dsnyda + dcz*(sna*dcsyda+csy*dsnada))
              dfda(2,ifcn)= dfda(2,ifcn) + cost*factor*(-dcx*dsnadb+dcz*dcsadb)&
                                     - sint*factor*(dcx*(csa*dcsydb+csy*dcsadb)&
                                     - dcy*dsnydb + dcz*(sna*dcsydb+csy*dsnadb))
            case ( cx_id, cxp_id, cy_id, cyp_id, cz_id, czp_id,                &
                   cmx_id, cmxp_id, cmy_id, cmyp_id, cmz_id, cmzp_id,          &
                   propeff_id, powerx_id, powery_id, powerz_id)
              ! Nothing: do not depend on alpha/beta
            case default
            end select

          end do component_loop2

        endif

      end do

  end subroutine pressurealphai


!================================ SKINFRICAI =================================80
!
!  This gets the derivative of skin friction wrt alpha
!
!=============================================================================80
  subroutine skinfricai(x,y,z,qnode,amut,clva,cdva,clvb,cdvb,nnodes01,nbfacet, &
                        ibnode,face_bit,f2ntb,nbnode,ndim,nbfaceq,f2nqb,       &
                        face_bitq,nelem,elem)

    use info_depr,     only : re, alpha, yaw, twod
    use kinddefs,      only : dp
    use element_types, only : elem_type
    use element_defs,  only : max_face_per_cell, max_node_per_cell
    use utilities,     only : cell_gradients

    integer, intent(in) :: nnodes01, ndim
    integer, intent(in) :: nbfacet, nbfaceq
    integer, intent(in) :: nbnode, nelem

    integer, dimension(nbnode),    intent(in) :: ibnode
    integer, dimension(nbfacet,5), intent(in) :: f2ntb
    integer, dimension(nbfaceq,6), intent(in) :: f2nqb
    integer, dimension(nbfacet),   intent(in) :: face_bit
    integer, dimension(nbfaceq),   intent(in) :: face_bitq

    real(dp), intent(inout) :: clva,cdva,clvb,cdvb

    real(dp), dimension(ndim,nnodes01), intent(in) :: qnode
    real(dp), dimension(nnodes01),      intent(in) :: x,y,z
    real(dp), dimension(nnodes01),      intent(in) :: amut

    type(elem_type), dimension(nelem), intent(in) :: elem

    integer :: n, ielem, bnode1, bnode2, bnode3, bnode4
    integer :: node, icell, nodes_local
    integer :: i, i_local, edges_local, face_2d

    integer, dimension(max_node_per_cell) :: c2n_cell
    integer, dimension(max_node_per_cell) :: node_map

    real(dp) :: mu_node
    real(dp) :: nxda, nyda, nzda
    real(dp) :: nxla, nyla, nzla
    real(dp) :: nxdb, nydb, nzdb
    real(dp) :: nxlb, nylb, nzlb
    real(dp) :: pi,conv,rei
    real(dp) :: rmu,ux,uy,uz
    real(dp) :: vx,vy,vz,wx,wy,wz
    real(dp) :: xnorm,ynorm,znorm
    real(dp) :: termx,termy,termz
    real(dp) :: cell_vol
    real(dp) :: forceda,forcela
    real(dp) :: forcedb,forcelb
    real(dp) :: x1,y1,z1,x2,y2,z2
    real(dp) :: x3,y3,z3,x4,y4,z4

    real(dp), dimension(max_face_per_cell)   :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)   :: u_node, v_node, w_node
    real(dp), dimension(max_node_per_cell)   :: x_node, y_node, z_node
    real(dp), dimension(3,max_node_per_cell) :: q_node
    real(dp), dimension(3)                   :: gradx_cell, grady_cell
    real(dp), dimension(3)                   :: gradz_cell

    real(dp), parameter :: my_haf = 0.50_dp
    real(dp), parameter :: my_2   = 2.00_dp

  continue

!  Some constants

    rei = 1.0_dp / re
    pi = acos(-1.0_dp)
    conv = 180.0_dp / pi

! Contributions from tri boundary faces

    surface_tris : do n = 1, nbfacet

      force_flag_tri : if(face_bit(n) == 1) then

        bnode1 = ibnode(f2ntb(n,1))
        bnode2 = ibnode(f2ntb(n,2))
        bnode3 = ibnode(f2ntb(n,3))

        icell = f2ntb(n,4)
        ielem = f2ntb(n,5)

!       set some loop indices and local mapping arrays depending on whether
!       we are doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (elem(ielem)%local_f2e(face_2d,1) /=                              &
              elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = elem(ielem)%edge_per_cell

        end if

!       copy c2n and local_f2n arrays from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

!       compute cell averaged viscosity by looping over the nodes in the
!       element and gathering their contributions, then average at the end

        cell_vol = 0.0_dp

        rmu = 0.0_dp

        x_node(:)   = 0.0_dp
        y_node(:)   = 0.0_dp
        z_node(:)   = 0.0_dp
        u_node(:)   = 0.0_dp
        v_node(:)   = 0.0_dp
        w_node(:)   = 0.0_dp
        q_node(:,:) = 0.0_dp

        node_loop1 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          u_node(i) = qnode(2,node)
          v_node(i) = qnode(3,node)
          w_node(i) = qnode(4,node)

          mu_node = 1.0_dp + amut(node)

          rmu = rmu + mu_node

        end do node_loop1

!       now compute cell average by dividing by the number of nodes
!       that contributed

        rmu = rmu / real(nodes_local, dp)

!       now we get this boundary face's normal

          x1 = x(bnode1)
          y1 = y(bnode1)
          z1 = z(bnode1)

          x2 = x(bnode2)
          y2 = y(bnode2)
          z2 = z(bnode2)

          x3 = x(bnode3)
          y3 = y(bnode3)
          z3 = z(bnode3)

!       - sign for outward facing normal

        xnorm = -my_haf*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
        ynorm = -my_haf*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
        znorm = -my_haf*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )

        q_node(1,:) = u_node(:)
        q_node(2,:) = v_node(:)
        q_node(3,:) = w_node(:)

        call cell_gradients(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            3, q_node, elem(ielem)%local_f2n,                  &
                            elem(ielem)%e2n_2d, gradx_cell, grady_cell,        &
                            gradz_cell, cell_vol, nx, ny, nz)

        ux = gradx_cell(1)
        vx = gradx_cell(2)
        wx = gradx_cell(3)

        uy = grady_cell(1)
        vy = grady_cell(2)
        wy = grady_cell(3)

        uz = gradz_cell(1)
        vz = gradz_cell(2)
        wz = gradz_cell(3)

!       now compute components of stress vector acting on the face

        termx=my_2*rei*rmu*(xnorm*2.0_dp*ux + ynorm*(uy + vx) + znorm*(uz + wx))
        termy=my_2*rei*rmu*(xnorm*(uy + vx) + ynorm*2.0_dp*vy + znorm*(vz + wy))
        termz=my_2*rei*rmu*(xnorm*(uz + wx) + ynorm*(vz + wy) + znorm*2.0_dp*wz)

!  Now dot the stress vector acting on the surface face with
!  a unit vector in the drag (lift) direction.  This is the
!  magnitude of the friction force acting on the face in the
!  drag (lift) direction

!  Find unit vectors in drag and lift directions

!       nxd =   cos(alpha/conv) * cos(yaw/conv)
!       nyd = - sin(yaw/conv)
!       nzd =   sin(alpha/conv) * cos(yaw/conv)

        nxda = -sin(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)
        nyda = 0.0_dp
        nzda = cos(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)

        nxdb = -cos(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)
        nydb = -cos(yaw/conv) / conv
        nzdb = -sin(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)

!       nxl = - sin(alpha/conv)
!       nyl = 0.0_dp
!       nzl =   cos(alpha/conv)

        nxla = - cos(alpha/conv) * 1.0_dp / conv
        nyla = 0.0_dp
        nzla = -sin(alpha/conv) * 1.0_dp / conv

        nxlb = 0.0_dp
        nylb = 0.0_dp
        nzlb = 0.0_dp

!  Now do the dot product to get the force in the drag (lift) direction

!  I think the signs are right on the following two equations, but I
!  wouldn't stake my life on it.  They've got to do with the force
!  being on the body or on the fluid.  The way they are set right now
!  gives the logical results (increase in Cd, decrease in Cl).

!       forced = - (termx*nxd + termy*nyd + termz*nzd)
!       forcel = - (termx*nxl + termy*nyl + termz*nzl)

        forceda = - (termx*nxda + termy*nyda + termz*nzda)
        forcela = - (termx*nxla + termy*nyla + termz*nzla)

        forcedb = - (termx*nxdb + termy*nydb + termz*nzdb)
        forcelb = - (termx*nxlb + termy*nylb + termz*nzlb)

!  Now add things

        cdva = cdva + forceda
        clva = clva + forcela

        cdvb = cdvb + forcedb
        clvb = clvb + forcelb

! Moment coefficients do not depend on alpha

      endif force_flag_tri

    end do surface_tris

! Contributions from quad boundary faces

    surface_quads : do n = 1, nbfaceq

      force_flag_quad : if(face_bitq(n) == 1) then

        bnode1 = ibnode(f2nqb(n,1))
        bnode2 = ibnode(f2nqb(n,2))
        bnode3 = ibnode(f2nqb(n,3))
        bnode4 = ibnode(f2nqb(n,4))

        icell = f2nqb(n,5)
        ielem = f2nqb(n,6)

!       set some loop indices and local mapping arrays depending on whether
!       we are doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (elem(ielem)%local_f2e(face_2d,1) /=                              &
              elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = elem(ielem)%edge_per_cell

        end if

!       copy c2n and local_f2n arrays from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

!       compute cell averaged viscosity by looping over the nodes in the
!       element and gathering their contributions, then average at the end

        cell_vol = 0.0_dp

        rmu = 0.0_dp

        x_node(:)   = 0.0_dp
        y_node(:)   = 0.0_dp
        z_node(:)   = 0.0_dp
        u_node(:)   = 0.0_dp
        v_node(:)   = 0.0_dp
        w_node(:)   = 0.0_dp
        q_node(:,:) = 0.0_dp

        node_loop2 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          u_node(i) = qnode(2,node)
          v_node(i) = qnode(3,node)
          w_node(i) = qnode(4,node)

          mu_node = 1.0_dp + amut(node)

          rmu = rmu + mu_node

        end do node_loop2

!       now compute cell average by dividing by the number of nodes
!       that contributed

        rmu = rmu / real(nodes_local, dp)

!       now we get this boundary face's normal

          x1 = x(bnode1)
          y1 = y(bnode1)
          z1 = z(bnode1)

          x2 = x(bnode2)
          y2 = y(bnode2)
          z2 = z(bnode2)

          x3 = x(bnode3)
          y3 = y(bnode3)
          z3 = z(bnode3)

          x4 = x(bnode4)
          y4 = y(bnode4)
          z4 = z(bnode4)

!       - sign for outward facing normal

        xnorm = -my_haf*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
        ynorm = -my_haf*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
        znorm = -my_haf*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )

        q_node(1,:) = u_node(:)
        q_node(2,:) = v_node(:)
        q_node(3,:) = w_node(:)

        call cell_gradients(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            3, q_node, elem(ielem)%local_f2n,                  &
                            elem(ielem)%e2n_2d, gradx_cell, grady_cell,        &
                            gradz_cell, cell_vol, nx, ny, nz)

        ux = gradx_cell(1)
        vx = gradx_cell(2)
        wx = gradx_cell(3)

        uy = grady_cell(1)
        vy = grady_cell(2)
        wy = grady_cell(3)

        uz = gradz_cell(1)
        vz = gradz_cell(2)
        wz = gradz_cell(3)

!       now compute components of stress vector acting on the face

        termx=my_2*rei*rmu*(xnorm*2.0_dp*ux + ynorm*(uy + vx) + znorm*(uz + wx))
        termy=my_2*rei*rmu*(xnorm*(uy + vx) + ynorm*2.0_dp*vy + znorm*(vz + wy))
        termz=my_2*rei*rmu*(xnorm*(uz + wx) + ynorm*(vz + wy) + znorm*2.0_dp*wz)

!  Now dot the stress vector acting on the surface face with
!  a unit vector in the drag (lift) direction.  This is the
!  magnitude of the friction force acting on the face in the
!  drag (lift) direction

!  Find unit vectors in drag and lift directions

!       nxd =   cos(alpha/conv) * cos(yaw/conv)
!       nyd = - sin(yaw/conv)
!       nzd =   sin(alpha/conv) * cos(yaw/conv)

        nxda = -sin(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)
        nyda = 0.0_dp
        nzda = cos(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)

        nxdb = -cos(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)
        nydb = -cos(yaw/conv) / conv
        nzdb = -sin(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)

!       nxl = - sin(alpha/conv)
!       nyl = 0.0_dp
!       nzl =   cos(alpha/conv)

        nxla = - cos(alpha/conv) * 1.0_dp / conv
        nyla = 0.0_dp
        nzla = -sin(alpha/conv) * 1.0_dp / conv

        nxlb = 0.0_dp
        nylb = 0.0_dp
        nzlb = 0.0_dp

!  Now do the dot product to get the force in the drag (lift) direction

!  I think the signs are right on the following two equations, but I
!  wouldn't stake my life on it.  They've got to do with the force
!  being on the body or on the fluid.  The way they are set right now
!  gives the logical results (increase in Cd, decrease in Cl).

!       forced = - (termx*nxd + termy*nyd + termz*nzd)
!       forcel = - (termx*nxl + termy*nyl + termz*nzl)

        forceda = - (termx*nxda + termy*nyda + termz*nzda)
        forcela = - (termx*nxla + termy*nyla + termz*nzla)

        forcedb = - (termx*nxdb + termy*nydb + termz*nzdb)
        forcelb = - (termx*nxlb + termy*nylb + termz*nzlb)

!  Now add things

        cdva = cdva + forceda
        clva = clva + forcela

        cdvb = cdvb + forcedb
        clvb = clvb + forcelb

! Moment coefficients do not depend on alpha

      endif force_flag_quad

    end do surface_quads

  end subroutine skinfricai


!============================= DFLOW_THROUGH_FORCE_DA=========================80
!
! This gets momentum flux and pressure forces for a
! flow through boundary for the general (mixed) element case
!
!=============================================================================80
  subroutine dflow_through_force_da(nbnode,ibnode,nbfacet,f2ntb,nbfaceq,f2nqb, &
                                    nelem,elem,nnodes01,x,y,z,qnode,face_bit,  &
                                    face_bitq,force,n_tot,design,bcforce,dfda, &
                                    ifcn,ib)

    use info_depr,     only : yaw, alpha
    use element_types, only : elem_type
    use forces,        only : integrate_face, cd_id, cdp_id, cdv_id, cl_id,    &
                              clp_id, clv_id, clcd_id
    use force_types,   only : force_type
    use design_types,  only : design_type
    use kinddefs,      only : dp
    use refgeom,       only : sref

    integer, intent(in) :: nnodes01, n_tot, nbnode, nbfacet, nbfaceq, nelem
    integer, intent(in) :: ifcn, ib

    integer, dimension(nbnode),    intent(in) :: ibnode
    integer, dimension(nbfacet,5), intent(in) :: f2ntb
    integer, dimension(nbfaceq,6), intent(in) :: f2nqb
    integer, dimension(nbfacet),   intent(in) :: face_bit
    integer, dimension(nbfaceq),   intent(in) :: face_bitq

    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode
    real(dp), dimension(nnodes01),       intent(in) :: x, y, z

    type(force_type),                    intent(in) :: force
    type(force_type),  dimension(:),     intent(in) :: bcforce
    type(design_type),                   intent(in) :: design
    type(elem_type),   dimension(nelem), intent(in) :: elem

    real(dp), dimension(2,design%nfunctions), intent(inout) :: dfda

    integer :: n, ielem, nface_eval, corner, k

    real(dp) :: pi, conv, dpowx, dpowy, dpowz
    real(dp) :: xmid, ymid, zmid
    real(dp) :: dvx, dvy, dvz, dpx, dpy, dpz
    real(dp) :: dmass, dpress, dpt, dtt, drho, darea
    real(dp) :: nxda,nyda,nzda,nxdb,nydb,nzdb
    real(dp) :: nxla,nyla,nzla,nxlb,nylb,nzlb
    real(dp) :: cd,cdp,cdv,cl,clp,clv,clcd
    real(dp) :: forcedva,forcedvb,forcelva,forcelvb
    real(dp) :: forcedpa,forcedpb,forcelpa,forcelpb
    real(dp) :: weight,target,power,width,base,average,const,factor

    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_180 = 180.0_dp

    integer, dimension(4) :: nodes

    character(80) :: q_state = 'conservative'

  continue

!   define some constants

    pi    = acos(-1.0_dp)
    conv  = my_180/pi

! Contributions from tria boundary faces

    nface_eval = nbfacet

    surface_trias : do n = 1, nface_eval

      force_flag_tria : if (face_bit(n) == 1) then

        do corner = 1,3
          nodes(corner) = ibnode(f2ntb(n,corner))
        enddo

        ielem = f2ntb(n,5)  ! index to type of element attached to this face

        call integrate_face( 3, nnodes01, x, y, z, n_tot, qnode, nodes        &
                          , elem(ielem)%type_cell, q_state                    &
                          , xmid, ymid, zmid                                  &
                          , dvx, dvy, dvz, dpx, dpy, dpz, dmass               &
                          , dpress, dpt, dtt, drho, darea, dpowx, dpowy, dpowz)

!       now dot the stress vector acting on the surface face with
!       a unit vector in the drag (lift) direction.  This is the
!       magnitude of the friction force acting on the face in the
!       drag (lift) direction

!       find unit vectors in drag and lift directions

!       nxd =   cos(alpha/conv) * cos(yaw/conv)
!       nyd = - sin(yaw/conv)
!       nzd =   sin(alpha/conv) * cos(yaw/conv)

        nxda = -sin(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)
        nyda = 0.0_dp
        nzda = cos(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)

        nxdb = -cos(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)
        nydb = -cos(yaw/conv) / conv
        nzdb = -sin(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)

!       nxl = - sin(alpha/conv)
!       nyl =   my_0
!       nzl =   cos(alpha/conv)

        nxla = - cos(alpha/conv) * 1.0_dp / conv
        nyla = 0.0_dp
        nzla = -sin(alpha/conv) * 1.0_dp / conv

        nxlb = 0.0_dp
        nylb = 0.0_dp
        nzlb = 0.0_dp

!       now do the dot product to get the force in the drag (lift) direction

!       forcedv = - (dvx*nxd + dvy*nyd + dvz*nzd)
          forcedva = - (dvx*nxda + dvy*nyda + dvz*nzda)
          forcedvb = - (dvx*nxdb + dvy*nydb + dvz*nzdb)
!       forcelv = - (dvx*nxl + dvy*nyl + dvz*nzl)
          forcelva = - (dvx*nxla + dvy*nyla + dvz*nzla)
          forcelvb = - (dvx*nxlb + dvy*nylb + dvz*nzlb)
!       forcedp = - (dpx*nxd + dpy*nyd + dpz*nzd)
          forcedpa = - (dpx*nxda + dpy*nyda + dpz*nzda)
          forcedpb = - (dpx*nxdb + dpy*nydb + dpz*nzdb)
!       forcelp = - (dpx*nxl + dpy*nyl + dpz*nzl)
          forcelpa = - (dpx*nxla + dpy*nyla + dpz*nzla)
          forcelpb = - (dpx*nxlb + dpy*nylb + dpz*nzlb)

        component_loop : do k = 1, design%function_data(ifcn)%ncomponents

          if (design%function_data(ifcn)%component_data(k)%boundary_id == 0)then
            cd   = force%cd
            cdp  = force%cdp
            cdv  = force%cdv
            cl   = force%cl
            clp  = force%clp
            clv  = force%clv
            clcd = force%clcd
          else if                                                              &
          (design%function_data(ifcn)%component_data(k)%boundary_id == ib)then
            cd   = bcforce(ib)%cd
            cdp  = bcforce(ib)%cdp
            cdv  = bcforce(ib)%cdv
            cl   = bcforce(ib)%cl
            clp  = bcforce(ib)%clp
            clv  = bcforce(ib)%clv
            clcd = 0.0_dp ! only used on boundary_id == 0
          else
            cycle component_loop
          endif

          weight  = design%function_data(ifcn)%component_data(k)%weight
          target  = design%function_data(ifcn)%component_data(k)%target
          power   = design%function_data(ifcn)%component_data(k)%power
          width   = design%function_data(ifcn)%timesteps(2) -                  &
                    design%function_data(ifcn)%timesteps(1) + 1
          if ( .not.design%function_data(ifcn)%averaging ) width = 1.0_dp
          average = design%function_data(ifcn)%component_data(k)%value
          base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

          select case (design%function_data(ifcn)%component_data(k)%name)
          case ( cd_id )
            if (.not.design%function_data(ifcn)%averaging) base = cd-target
            const = my_1/sref
          case ( cdp_id )
            if (.not.design%function_data(ifcn)%averaging) base = cdp-target
            const = my_1/sref
          case ( cdv_id )
            if (.not.design%function_data(ifcn)%averaging) base = cdv-target
            const = my_1/sref
          case ( cl_id )
            if (.not.design%function_data(ifcn)%averaging) base = cl-target
            const = my_1/sref
          case ( clp_id )
            if (.not.design%function_data(ifcn)%averaging) base = clp-target
            const = my_1/sref
          case ( clv_id )
            if (.not.design%function_data(ifcn)%averaging) base = clv-target
            const = my_1/sref
          case ( clcd_id )
            if (.not.design%function_data(ifcn)%averaging) base= clcd-target
            const = my_1/sref
          case default
            cycle component_loop
          end select

          factor = const*weight*power/width * base**(power-1.0_dp)

          select case (design%function_data(ifcn)%component_data(k)%name)
          case ( cd_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*(forcedva + forcedpa)
            dfda(2,ifcn) = dfda(2,ifcn) + factor*(forcedvb + forcedpb)
          case ( cdp_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*forcedpa
            dfda(2,ifcn) = dfda(2,ifcn) + factor*forcedpb
          case ( cdv_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*forcedva
            dfda(2,ifcn) = dfda(2,ifcn) + factor*forcedvb
          case ( cl_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*(forcelva + forcelpa)
            dfda(2,ifcn) = dfda(2,ifcn) + factor*(forcelvb + forcelpb)
          case ( clp_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*forcelpa
            dfda(2,ifcn) = dfda(2,ifcn) + factor*forcelpb
          case ( clv_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*forcelva
            dfda(2,ifcn) = dfda(2,ifcn) + factor*forcelvb
          case ( clcd_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*                              &
                                          (1.0_dp/cd*(forcelva + forcelpa)     &
                                          - cl/cd/cd*(forcedva + forcedpa))
            dfda(2,ifcn) = dfda(2,ifcn) + factor*                              &
                                          (1.0_dp/cd*(forcelvb + forcelpb)     &
                                          - cl/cd/cd*(forcedvb + forcedpb))
          end select

        end do component_loop

      end if force_flag_tria

    end do surface_trias

! Contributions from quad boundary faces

    nface_eval = nbfaceq

    surface_quads : do n = 1, nface_eval

      force_flag_quad : if (face_bitq(n) == 1) then

        do corner = 1,4
          nodes(corner) = ibnode(f2nqb(n,corner))
        enddo

        ielem = f2nqb(n,6)

        call integrate_face( 4, nnodes01, x, y, z, n_tot, qnode, nodes        &
                          , elem(ielem)%type_cell, q_state                    &
                          , xmid, ymid, zmid                                  &
                          , dvx, dvy, dvz, dpx, dpy, dpz, dmass               &
                          , dpress, dpt, dtt, drho, darea, dpowx, dpowy, dpowz)

!       now dot the stress vector acting on the surface face with
!       a unit vector in the drag (lift) direction.  This is the
!       magnitude of the friction force acting on the face in the
!       drag (lift) direction

!       find unit vectors in drag and lift directions

!       nxd =   cos(alpha/conv) * cos(yaw/conv)
!       nyd = - sin(yaw/conv)
!       nzd =   sin(alpha/conv) * cos(yaw/conv)

        nxda = -sin(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)
        nyda = 0.0_dp
        nzda = cos(alpha/conv) * 1.0_dp / conv * cos(yaw/conv)

        nxdb = -cos(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)
        nydb = -cos(yaw/conv) / conv
        nzdb = -sin(alpha/conv) * 1.0_dp / conv * sin(yaw/conv)

!       nxl = - sin(alpha/conv)
!       nyl =   my_0
!       nzl =   cos(alpha/conv)

        nxla = - cos(alpha/conv) * 1.0_dp / conv
        nyla = 0.0_dp
        nzla = -sin(alpha/conv) * 1.0_dp / conv

        nxlb = 0.0_dp
        nylb = 0.0_dp
        nzlb = 0.0_dp

!       now do the dot product to get the force in the drag (lift) direction

!       forcedv = - (dvx*nxd + dvy*nyd + dvz*nzd)
          forcedva = - (dvx*nxda + dvy*nyda + dvz*nzda)
          forcedvb = - (dvx*nxdb + dvy*nydb + dvz*nzdb)
!       forcelv = - (dvx*nxl + dvy*nyl + dvz*nzl)
          forcelva = - (dvx*nxla + dvy*nyla + dvz*nzla)
          forcelvb = - (dvx*nxlb + dvy*nylb + dvz*nzlb)
!       forcedp = - (dpx*nxd + dpy*nyd + dpz*nzd)
          forcedpa = - (dpx*nxda + dpy*nyda + dpz*nzda)
          forcedpb = - (dpx*nxdb + dpy*nydb + dpz*nzdb)
!       forcelp = - (dpx*nxl + dpy*nyl + dpz*nzl)
          forcelpa = - (dpx*nxla + dpy*nyla + dpz*nzla)
          forcelpb = - (dpx*nxlb + dpy*nylb + dpz*nzlb)

        component_loopq : do k = 1, design%function_data(ifcn)%ncomponents

          if (design%function_data(ifcn)%component_data(k)%boundary_id == 0)then
            cd   = force%cd
            cdp  = force%cdp
            cdv  = force%cdv
            cl   = force%cl
            clp  = force%clp
            clv  = force%clv
            clcd = force%clcd
          else if                                                              &
          (design%function_data(ifcn)%component_data(k)%boundary_id == ib)then
            cd   = bcforce(ib)%cd
            cdp  = bcforce(ib)%cdp
            cdv  = bcforce(ib)%cdv
            cl   = bcforce(ib)%cl
            clp  = bcforce(ib)%clp
            clv  = bcforce(ib)%clv
            clcd = 0.0_dp ! only used on boundary_id == 0
          else
            cycle component_loopq
          endif

          weight  = design%function_data(ifcn)%component_data(k)%weight
          target  = design%function_data(ifcn)%component_data(k)%target
          power   = design%function_data(ifcn)%component_data(k)%power
          width   = design%function_data(ifcn)%timesteps(2) -                  &
                    design%function_data(ifcn)%timesteps(1) + 1
          if ( .not.design%function_data(ifcn)%averaging ) width = 1.0_dp
          average = design%function_data(ifcn)%component_data(k)%value
          base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

          select case (design%function_data(ifcn)%component_data(k)%name)
          case ( cd_id )
            if (.not.design%function_data(ifcn)%averaging) base = cd-target
            const = my_1/sref
          case ( cdp_id )
            if (.not.design%function_data(ifcn)%averaging) base = cdp-target
            const = my_1/sref
          case ( cdv_id )
            if (.not.design%function_data(ifcn)%averaging) base = cdv-target
            const = my_1/sref
          case ( cl_id )
            if (.not.design%function_data(ifcn)%averaging) base = cl-target
            const = my_1/sref
          case ( clp_id )
            if (.not.design%function_data(ifcn)%averaging) base = clp-target
            const = my_1/sref
          case ( clv_id )
            if (.not.design%function_data(ifcn)%averaging) base = clv-target
            const = my_1/sref
          case ( clcd_id )
            if (.not.design%function_data(ifcn)%averaging) base= clcd-target
            const = my_1/sref
          case default
            cycle component_loopq
          end select

          factor = const*weight*power/width * base**(power-1.0_dp)

          select case (design%function_data(ifcn)%component_data(k)%name)
          case ( cd_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*(forcedva + forcedpa)
            dfda(2,ifcn) = dfda(2,ifcn) + factor*(forcedvb + forcedpb)
          case ( cdp_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*forcedpa
            dfda(2,ifcn) = dfda(2,ifcn) + factor*forcedpb
          case ( cdv_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*forcedva
            dfda(2,ifcn) = dfda(2,ifcn) + factor*forcedvb
          case ( cl_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*(forcelva + forcelpa)
            dfda(2,ifcn) = dfda(2,ifcn) + factor*(forcelvb + forcelpb)
          case ( clp_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*forcelpa
            dfda(2,ifcn) = dfda(2,ifcn) + factor*forcelpb
          case ( clv_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*forcelva
            dfda(2,ifcn) = dfda(2,ifcn) + factor*forcelvb
          case ( clcd_id )
            dfda(1,ifcn) = dfda(1,ifcn) + factor*                              &
                                          (1.0_dp/cd*(forcelva + forcelpa)     &
                                          - cl/cd/cd*(forcedva + forcedpa))
            dfda(2,ifcn) = dfda(2,ifcn) + factor*                              &
                                          (1.0_dp/cd*(forcelvb + forcelpb)     &
                                          - cl/cd/cd*(forcedvb + forcedpb))
          end select

        end do component_loopq

      end if force_flag_quad

    end do surface_quads

  end subroutine dflow_through_force_da

  include 'viscosity_law.f90'
  include 'dfroe_i.f90'

end module dalpha

module source_gen

  use kinddefs, only : dp, odp

  implicit none
  private
  public :: source_eqn2

contains

!============================= SOURCE_EQN2 ===================================80
!
! This routine gathers the thermochemical source terms and Jacobians.
! It adds their contributions to the residual (RHS) and linear solver (LHS)
!
!=============================================================================80

  subroutine source_eqn2( nnodes0, nnodes01, jac_flag,  x, y, z, qnode,        &
                          res, vol, a, enthalpy_ij, slen, gradx, grady, gradz, &
                          ndim, njac, n_grd, g2m, nelem, elem, ia, ja, nzg2m,  &
                          a_off)

    use generic_gas_map,           only : therm_on, n_species, n_energy,       &
                                          n_pjac, chem_on, electrons,          &
                                          n_momx, n_momy, n_momz, n_etot,      &
                                          n_cv_j, n_pressure_k, n_mom,         &
                                          n_energy_j, n_temperature_j,         &
                                          n_density, n_amu_k, n_energy_last,   &
                                          n_turb_g, n_sonic_k

    use shared_gas_variables,      only : chemical_equilibrium, spec_propv,    &
                                          i_electron

    use allocations,               only : my_alloc_ptr

    use chemical_kinetics,         only : species_source

    use thermal_relaxation,        only : thermal_source, setup_thermal_source

    use turb_gen,                  only : turb_on, n_turb_ke, n_dis_nutl,      &
                                          turb_source_gen, coupled_tke

    use thermodynamics,            only : temperature_jacobian

    use element_types,             only : elem_type
    use element_defs,              only : max_node_per_cell, max_edge_per_cell

    use inviscid_flux,             only : mean_decouple

    integer, intent(in) :: nnodes0
    integer, intent(in) :: ndim
    integer, intent(in) :: njac
    integer, intent(in) :: n_grd
    integer, intent(in) :: nnodes01
    logical, intent(in) :: jac_flag
    integer, intent(in), optional :: nelem

    integer, dimension(:), optional, intent(in) :: g2m

    real(dp), dimension(nnodes01),          intent(in)    :: x
    real(dp), dimension(nnodes01),          intent(in)    :: y
    real(dp), dimension(nnodes01),          intent(in)    :: z
    real(dp), dimension(:,:),               intent(inout) :: qnode
    real(dp), dimension(n_species,n_energy,nnodes01),                          &
                                            intent(in) :: enthalpy_ij
    real(dp), dimension(:,:),               intent(inout) :: res
    real(dp), dimension(n_grd,nnodes01),    intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),    intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),    intent(in)    :: gradz
    real(dp), dimension(nnodes01),          intent(in)    :: slen
    real(dp), dimension(njac,njac,nnodes0), intent(inout) :: a
    real(dp), dimension(nnodes01),          intent(in)    :: vol
    real(odp), dimension(:,:,:),            intent(inout), optional :: a_off
    type(elem_type), dimension(:),          intent(in), optional :: elem
    integer,   dimension(:),                intent(in), optional :: ia
    integer,   dimension(:),                intent(in), optional :: ja
    integer,   dimension(:),                intent(in), optional :: nzg2m




    real(dp), dimension(:,:),   pointer, save :: chem_source
    real(dp), dimension(:,:,:), pointer, save :: chem_source_jac
    real(dp), dimension(:,:),   pointer, save :: temperature_jac
    real(dp), dimension(:,:),   pointer, save :: therm_source
    real(dp), dimension(:,:),   pointer, save :: therm_source_factor
    real(dp), dimension(:,:,:), pointer, save :: therm_source_jac
    real(dp), dimension(:,:),   pointer, save :: elec_impct_jac
    real(dp), dimension(:),     pointer, save :: elec_impct_source
    real(dp), dimension(:),     pointer, save :: turb_src_gen
    real(dp), dimension(:,:),   pointer, save :: turb_jac_gen
    real(dp), dimension(:),     pointer, save :: res0
    real(dp), dimension(:,:),   pointer, save :: a0
    real(dp), dimension(:,:),   pointer, save :: a0_off

    real(dp)      :: xin, yin, zin
    real(dp)      :: u, v, w
    real(dp)      :: rho_turb_ke, rho_dis_nutl
    real(dp)      :: rhox, rhoy, rhoz
    real(dp)      :: tkex, tkey, tkez
    real(dp)      :: nutlx, nutly, nutlz
    real(dp)      :: rho, sqrt_rho
    real(dp)      :: dis_nutl_ovr_sqrt_rho
    real(dp)      :: tmp, tz, tza
!   real(dp)      :: resj, dresjdt

    logical, save :: init = .true.

    integer  :: i,j,k,row, n
    integer  :: inode, ioff, res_dim
    integer  :: ielem, nodes_local, edges_local, ii, ie, ie_local
    integer  :: n1_loc, n2_loc, node, i_local, nodec, column
    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

!   logical :: dbug_flag

    continue

    if(init)then
      init = .false.
      if(chem_on .or. therm_on)then
        call my_alloc_ptr(chem_source,n_species,nnodes0)
        call my_alloc_ptr(chem_source_jac,n_species,n_pjac,nnodes0)
        call my_alloc_ptr(temperature_jac,ndim,n_energy)
        call my_alloc_ptr(res0,n_species)
        call my_alloc_ptr(a0,n_species,ndim)
        call my_alloc_ptr(a0_off,n_species,ndim)
      end if
      if(therm_on)then
        call my_alloc_ptr(therm_source,n_energy,nnodes0)
        call my_alloc_ptr(therm_source_factor,n_energy,nnodes0)
        call my_alloc_ptr(therm_source_jac,n_energy,n_pjac,nnodes0)
        call my_alloc_ptr(elec_impct_source,nnodes0)
        call my_alloc_ptr(elec_impct_jac,n_pjac,nnodes0)
      end if
      if(turb_on)then
        call my_alloc_ptr(turb_src_gen,n_turb_g)
        call my_alloc_ptr(turb_jac_gen,n_turb_g,ndim)
      end if
    end if

    free_energy_minimization: if(chemical_equilibrium)then

      if (jac_flag) then ! deal with diagonal elements of jacobian
        do inode = 1, nnodes0
          row = g2m(inode)
          a0(1:n_species,1:ndim) = a(1:n_species,1:ndim,row)
          do j = 1,n_species
            if ( spec_propv(j)%replace ) cycle ! basis species equil next loop
            do i = 1,ndim
              a(j,i,row) = sum(a0(1:n_species,i)*                              &
                           spec_propv(j)%factor(1:n_species))
            end do
          end do

!         Add contributions to Jacobian from temperature dependence

          rho = qnode(n_density,inode)
          u = qnode(n_momx,inode)/rho
          v = qnode(n_momy,inode)/rho
          w = qnode(n_momz,inode)/rho

          call temperature_jacobian(rho, u, v, w,                              &
            qnode(n_temperature_j(1):n_temperature_j(n_energy),inode),         &
            qnode(n_energy_j(1):n_energy_j(n_energy),inode),                   &
            qnode(n_cv_j(1):n_cv_j(n_energy),inode),                           &
            enthalpy_ij(:,:,inode),temperature_jac(:,:),                       &
            n_momx,n_momy,n_momz,n_etot,n_turb_g,n_turb_ke,coupled_tke)

          do j = 1,n_species
            if ( j == i_electron)then
              a(j,1:n_species,row) = spec_propv(j)%factor(1:n_species)
              a(j,(n_species+1):ndim,row) = 0._dp
            else if ( spec_propv(j)%replace ) then
              do i = 1,n_species
                a(j,i,row) =  spec_propv(j)%factor(i)/                         &
                              max(1.e-10_dp,qnode(i,inode))
              end do
              do i = n_species+1, ndim
                a(j,i,row) =  0._dp
              end do
              do i = 1, ndim
                a(j,i,row) =  a(j,i,row)*vol(inode)
              end do
            end if
          end do
        end do
      else
        do inode = 1, nnodes0
          res0(1:n_species) = res(1:n_species,inode)
          do j = 1,n_species
            if ( spec_propv(j)%replace ) cycle ! basis species equil next loop
            if ( j == i_electron ) cycle ! deal with net charge neutrality later
            res(j,inode) = sum(res0(1:n_species)*                              &
                           spec_propv(j)%factor(1:n_species))
          end do
          do j = 1,n_species
            if ( j == i_electron )then
              res(j,inode) = sum(spec_propv(j)%factor(1:n_species)*            &
                             qnode(1:n_species,inode))
            else if ( spec_propv(j)%replace ) then
!             tmp = qnode(n_temperature_j(1),inode)
              tmp = max(600._dp,qnode(n_temperature_j(1),inode))
              tz = 10000._dp / tmp
              tza = log(tz)
              res(j,inode) = spec_propv(j)%ake/tz + spec_propv(j)%bke          &
                        + tza*spec_propv(j)%cke + tz*(spec_propv(j)%dke        &
                        + tz*spec_propv(j)%eke)
              do i = 1,n_species
                res(j,inode) = res(j,inode)                                    &
                             - spec_propv(j)%factor(i)*log(qnode(i,inode))
              end do
!             res(j,inode) = -res(j,inode)/((abs(res(j,inode))+1._dp)*20._dp)
              res(j,inode) = -res(j,inode)*vol(inode)*.05_dp
            end if
          end do
        end do
      end if

      if(jac_flag)then ! deal with off-diagonal changes

        element_loop: do ielem = 1,nelem
          nodes_local = elem(ielem)%node_per_cell
          edges_local = elem(ielem)%edge_per_cell
          edge_map(:) = 0
          node_map(:) = 0
          do i=1,nodes_local
            node_map(i) = i
          end do
          do i=1,edges_local
            edge_map(i)  = i
          end do
          cell_loop: do n = 1, elem(ielem)%ncell
            edge_node_loop : do ii = 1,2
              edge_loop : do ie_local = 1,edges_local
!               local edge number
                ie = edge_map(ie_local)
!               local node numbers of edge endpoints
                n1_loc = elem(ielem)%local_e2n(ie,1)
                n2_loc = elem(ielem)%local_e2n(ie,2)
!               global node numbers of edge endpoints
!               diagonal contributions
!               local (i) and global (node) numbers
                if (ii == 1) then
                  i = n1_loc
                  node = elem(ielem)%c2n(n1_loc,n)
                else
                  i = n2_loc
                  node = elem(ielem)%c2n(n2_loc,n)
                end if
                node_loop_3 : do i_local = 1, nodes_local
!                 local node number
                  i = node_map(i_local)
!                 global node number
                  nodec = elem(ielem)%c2n(i,n)
                  if (nodec == node) cycle node_loop_3
                  if ( node <= nnodes0 ) then
!                   avoid unused entries in a_off when running solver
!                   from adjoint
                    if (node > nnodes0 .and. nodec > nnodes0) cycle node_loop_3
!                   Determine location of nonzero contribution in
!                   comp row storage
                    ioff = 0
                    do k = ia(node), ia(node+1) - 1
                      column = abs(ja(k))
                      if (column == nodec) ioff = nzg2m(k)
                    end do
                    if (ioff == 0) then
                      write(6,*)                                               &
                        'error: no place to put contribution from node ',      &
                                 nodec,' to the off diagonal of node ',node
                      stop
                    end if
                    a0_off(1:n_species,1:ndim) = a_off(1:n_species,1:ndim,ioff)
                    do j = 1,n_species
                      if ( spec_propv(j)%replace .or. j == i_electron) cycle
                      do i = 1,ndim
                        a_off(j,i,ioff) = sum(a0_off(1:n_species,i)*           &
                                   spec_propv(j)%factor(1:n_species))
                      end do
                    end do
                    do j = 1,n_species
                      if ( spec_propv(j)%replace  .or. j == i_electron)then
                        a_off(j,1:ndim,ioff) = 0._odp
                      end if
                    end do
                  end if
                end do node_loop_3
              end do edge_loop
            end do edge_node_loop
          end do cell_loop
        end do element_loop
      end if

    else free_energy_minimization

!     Compute chemical (species) source terms

      species_source_terms: if(chem_on)then

        if(therm_on .and. electrons)then

          call species_source(nnodes0, qnode(1:n_species,:),                   &
             qnode(n_temperature_j(1):n_temperature_j(n_energy),:),            &
             chem_source,chem_source_jac,jac_flag,elec_impct_source,           &
             elec_impct_jac)

        else

          call species_source(nnodes0, qnode(1:n_species,:),                   &
             qnode(n_temperature_j(1):n_temperature_j(n_energy),:),            &
             chem_source,chem_source_jac,jac_flag)

        end if

        do inode = 1, nnodes0

!         If you are in a rarefied condition for species j and if the source
!         term for species j is still depleting it then lets freeze species j
          do j = 1,n_species
            if(chem_source(j,inode) < 0._dp .and.                              &
               qnode(j,inode)/qnode(n_density,inode) < 1.e-10_dp)then
              chem_source(j,inode) = 0._dp
              chem_source_jac(j,:,inode) = 0._dp
            end if
          end do

          jacobian_update: if(jac_flag)then

            row = g2m(inode)

            a(1:n_species,1:n_species,row) = a(1:n_species,1:n_species,row)    &
                          -vol(inode)*chem_source_jac(1:n_species,1:n_species, &
                          inode)

!             Add contributions to Jacobian from temperature dependence

              rho = qnode(n_density,inode)
              u = qnode(n_momx,inode)/rho
              v = qnode(n_momy,inode)/rho
              w = qnode(n_momz,inode)/rho

              call temperature_jacobian(rho, u, v, w,                          &
                qnode(n_temperature_j(1):n_temperature_j(n_energy),inode),     &
                qnode(n_energy_j(1):n_energy_j(n_energy),inode),               &
                qnode(n_cv_j(1):n_cv_j(n_energy),inode),                       &
                enthalpy_ij(:,:,inode),temperature_jac(:,:),                   &
                n_momx,n_momy,n_momz,n_etot,n_turb_g,n_turb_ke,coupled_tke)

              res_dim = ndim
              if (mean_decouple) res_dim = n_species

              do j = 1,n_energy
                do k = 1,res_dim
                  a(1:n_species,k,row) = a(1:n_species,k,row)-vol(inode)*      &
                    temperature_jac(k,j)*chem_source_jac(1:n_species,          &
                    n_species+j,inode)
                end do
              end do

              if (mean_decouple) a(:,:,row) = qnode(n_density,inode)*a(:,:,row)

          else jacobian_update

            res(1:n_species,inode) = res(1:n_species,inode)                    &
                                   - vol(inode)*chem_source(1:n_species,inode)

          end if jacobian_update
        end do

      end if species_source_terms

!     Compute thermal source terms
!     The total energy formulation used here does not have a source term for
!     the first (total energy) equation but does have source terms for
!     vibrational-electronic energy.

      thermal_source_terms: if(therm_on)then

        if( .not. jac_flag)then ! remap pressure from ptoe
          therm_source(1,1:nnodes0) = qnode(n_pressure_k(1),1:nnodes0)
          qnode(n_pressure_k(1),1:nnodes0) = qnode(n_etot,1:nnodes0)
        end if
        call setup_thermal_source(nnodes0, qnode(1:n_species,1:nnodes0),       &
           qnode(n_temperature_j(1):n_temperature_j(n_energy),1:nnodes0),      &
           qnode(n_pressure_k(1):n_pressure_k(n_mom),1:nnodes0),               &
           qnode(n_density,1:nnodes0),                                         &
           qnode(n_cv_j(1):n_cv_j(n_energy),1:nnodes0),                        &
           therm_source_factor)
        if( .not. jac_flag)then ! remap pressure from etop
          qnode(n_etot,1:nnodes0) = qnode(n_pressure_k(1),1:nnodes0)
          qnode(n_pressure_k(1),1:nnodes0) = therm_source(1,1:nnodes0)
        end if
        call thermal_source(nnodes0, chem_on,                                  &
           qnode(n_temperature_j(1):n_temperature_j(n_energy),1:nnodes0),      &
           qnode(n_energy_j(1):n_energy_j(n_energy),1:nnodes0),                &
           qnode(n_cv_j(1):n_cv_j(n_energy),1:nnodes0),                        &
           chem_source(:,1:nnodes0),chem_source_jac(:,:,1:nnodes0),            &
           therm_source,therm_source_jac,                                      &
           therm_source_factor,jac_flag,elec_impct_source(1:nnodes0),          &
           elec_impct_jac(:,1:nnodes0))

        do inode = 1, nnodes0

!       If you are in an undershoot condition for energy j and if the source
!       term for energy j is still depleting it then lets freeze energy j
          do j = 2,n_energy
            if(qnode(n_temperature_j(j),inode) < 20._dp .and.                  &
               therm_source(j,inode) < 0._dp)then
              therm_source(j,inode) = 0._dp
              therm_source_jac(j,:,inode) = 0._dp
            end if
          end do

          jacobian_update_2: if(jac_flag)then

            row = g2m(inode)

            a(n_momz+2:n_momz+n_energy,1:n_species,row) =                      &
              a(n_momz+2:n_momz+n_energy,1:n_species,row)                      &
              - vol(inode)*therm_source_jac(2:n_energy,1:n_species,inode)

!           Add contributions to Jacobian from temperature dependence

            rho = qnode(n_density,inode)
            u = qnode(n_momx,inode)/rho
            v = qnode(n_momy,inode)/rho
            w = qnode(n_momz,inode)/rho

            call temperature_jacobian(rho, u, v, w,                            &
                qnode(n_temperature_j(1):n_temperature_j(n_energy),inode),     &
                qnode(n_cv_j(1):n_cv_j(n_energy),inode),                       &
                qnode(n_energy_j(1):n_energy_j(n_energy),inode),               &
                enthalpy_ij(:,:,inode),temperature_jac(:,:),                   &
                n_momx,n_momy,n_momz,n_etot,n_turb_g,n_turb_ke,coupled_tke)

            do j = 1,n_energy
              do k = 1,ndim
                a(n_momz+2:n_momz+n_energy,k,row) =                            &
                  a(n_momz+2:n_momz+n_energy,k,row) - vol(inode)*              &
                  temperature_jac(k,j)*therm_source_jac(2:n_energy,n_species+j,&
                  inode)
              end do
            end do

          else jacobian_update_2

            res( n_etot+1:n_energy_last, inode ) =                             &
                 res( n_etot+1:n_energy_last, inode ) -                        &
                 vol( inode ) * therm_source( 2:n_energy, inode )

          end if jacobian_update_2
        end do
      end if thermal_source_terms

    end if free_energy_minimization

!   Compute turbulence model source terms

    turbulence_source_terms: if(turb_on)then
      do inode = 1, nnodes0

        xin = x(inode)
        yin = y(inode)
        zin = z(inode)

        rho = qnode(n_density,inode)

        if( .not. jac_flag)then ! using primitive variables
          u = qnode(n_momx,inode)
          v = qnode(n_momy,inode)
          w = qnode(n_momz,inode)
          rho_turb_ke  = qnode(n_density,inode)*qnode(n_turb_ke,inode)
          rho_dis_nutl = qnode(n_density,inode)*qnode(n_dis_nutl,inode)
        else
          u = qnode(n_momx,inode)/rho
          v = qnode(n_momy,inode)/rho
          w = qnode(n_momz,inode)/rho
          rho_turb_ke  = qnode(n_turb_ke,inode)
          rho_dis_nutl = qnode(n_dis_nutl,inode)
        end if

        if(n_turb_g > 1)then
          tkex = gradx(n_turb_ke,inode)
          tkey = grady(n_turb_ke,inode)
          tkez = gradz(n_turb_ke,inode)
        else
          rho_turb_ke = 0._dp
          rhox = sum(gradx(1:n_species,inode))
          rhoy = sum(grady(1:n_species,inode))
          rhoz = sum(gradz(1:n_species,inode))
          nutlx = gradx(n_dis_nutl,inode)
          nutly = grady(n_dis_nutl,inode)
          nutlz = gradz(n_dis_nutl,inode)
          sqrt_rho = sqrt(rho)
          dis_nutl_ovr_sqrt_rho = rho_dis_nutl/(rho*sqrt_rho)
!         Chain rule to get del(sqrt(rho)*nutl)/del(x,y, or z)
          tkex = sqrt_rho * nutlx + 0.5_dp*dis_nutl_ovr_sqrt_rho * rhox
          tkey = sqrt_rho * nutly + 0.5_dp*dis_nutl_ovr_sqrt_rho * rhoy
          tkez = sqrt_rho * nutlz + 0.5_dp*dis_nutl_ovr_sqrt_rho * rhoz
        end if

        call turb_source_gen(slen(inode),xin,yin,zin,                          &
                             rho,u,v,w,qnode(n_sonic_k(1),inode),              &
                gradx(n_momx,inode),grady(n_momx,inode),gradz(n_momx,inode),   &
                gradx(n_momy,inode),grady(n_momy,inode),gradz(n_momy,inode),   &
                gradx(n_momz,inode),grady(n_momz,inode),gradz(n_momz,inode),   &
                tkex,tkey,tkez,gradx(n_dis_nutl,inode),grady(n_dis_nutl,inode),&
                gradz(n_dis_nutl,inode),rho_turb_ke,rho_dis_nutl,              &
                qnode(n_amu_k(1),inode),turb_src_gen,turb_jac_gen,jac_flag)

        turb_jacobian_update: if(jac_flag)then

          row = g2m(inode)

          do k = 1,ndim
            do j = 1,n_turb_g
              a(n_turb_ke-1+j,k,row) = a(n_turb_ke-1+j,k,row)                  &
                                       - vol(inode)*turb_jac_gen(j,k)
            end do
          end do

        else turb_jacobian_update

          res(n_turb_ke:n_dis_nutl,inode) =                                    &
                                 res(n_turb_ke:n_dis_nutl,inode)               &
                               - vol(inode)*turb_src_gen(1:n_turb_g)

        end if turb_jacobian_update
      end do
    end if turbulence_source_terms

  end subroutine source_eqn2

end module source_gen

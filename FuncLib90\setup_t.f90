!================================ SETUP_T ====================================80
!
!  Sets up the Jacobian matrix T=dq/dQ for converting primitive
!  linearizations to conservative
!
!=============================================================================80
  pure function setup_t(rho,u,v,w)

    use kinddefs, only : dp
    use fluid,    only : gm1

    real(dp), intent(in) :: rho, u, v, w

    real(dp), dimension(5,5) :: setup_t

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp
    real(dp), parameter :: my_2 = 2.0_dp

  continue

    setup_t(1,1) = my_1
    setup_t(1,2) = my_0
    setup_t(1,3) = my_0
    setup_t(1,4) = my_0
    setup_t(1,5) = my_0

    setup_t(2,1) = -u/rho
    setup_t(2,2) = my_1/rho
    setup_t(2,3) = my_0
    setup_t(2,4) = my_0
    setup_t(2,5) = my_0

    setup_t(3,1) = -v/rho
    setup_t(3,2) = my_0
    setup_t(3,3) = my_1/rho
    setup_t(3,4) = my_0
    setup_t(3,5) = my_0

    setup_t(4,1) = -w/rho
    setup_t(4,2) = my_0
    setup_t(4,3) = my_0
    setup_t(4,4) = my_1/rho
    setup_t(4,5) = my_0

    setup_t(5,1) = gm1/my_2*(u*u + v*v + w*w)
    setup_t(5,2) = -gm1*u
    setup_t(5,3) = -gm1*v
    setup_t(5,4) = -gm1*w
    setup_t(5,5) = gm1

  end function setup_t

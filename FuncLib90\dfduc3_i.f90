!=================================== DFDUC3_I ================================80
!
!  Calculates the (incompressible) flux jacobians as a+|a|
!  Here |a| is averaged.
!  Also, there is a cutoff for eigenvalues but it is turned off
!  If you want to form dfp strictly from left and dfm strictly from right
!  then need to modify
!
!=============================================================================80

  subroutine dfduc3_i(xnorm, ynorm, znorm, area, face_speed, beta, ql, qr,     &
                      dfl, dfr)

    real(dp),                 intent(in)  :: xnorm, ynorm, znorm, area
    real(dp),                 intent(in)  :: face_speed, beta
    real(dp), dimension(4),   intent(in)  :: ql, qr
    real(dp), dimension(4,4), intent(out) :: dfl, dfr

    real(dp)    :: x1,x2,y1,y2,z1,z2,a11,a12,a13,a14,a21,a22,a23,a24,a31,a32
    real(dp)    :: a33,a34,a41,a42,a43,a44,c,c2,term,dot
    real(dp)    :: eig1,eig2,eig3,eig4,phi1,phi2,phi3,mag,t11,t12
    real(dp)    :: t13,t14,t21,t22,t23,t24,t31,t32,t33,t34,t41,t42,t43,t44,u
    real(dp)    :: ul,ur,ubar,ubarl,ubarr,v,vl,vr,w,wl,wr
    real(dp)    :: ti11,ti12,ti13,ti14,ti21,ti22,ti23,ti24,ti31,ti32,ti33,ti34
    real(dp)    :: ti41,ti42,ti43,ti44,fspd_half,fspd2_4th

    real(dp), parameter    :: my_0   =  0._dp
    real(dp), parameter    :: my_half = 0.5_dp
    real(dp), parameter    :: my_1   =  1._dp

  continue

    fspd_half = my_half*face_speed
    fspd2_4th = fspd_half*fspd_half

! now lets get our other 2 vectors
! for first vector, use {1,0,0} and subtract off the component
! in the direction of the face normal. if the inner product of
! {1,0,0} is close to unity, use {0,1,0}

    dot = xnorm
    if (abs(dot) < 0.95_dp) then
      x1 = my_1 - dot*xnorm
      y1 =    - dot*ynorm
      z1 =    - dot*znorm
    else
      dot = ynorm
      x1 =    - dot*xnorm
      y1 = my_1 - dot*ynorm
      z1 =    - dot*znorm
    end if

! normalize the first vector

    mag = sqrt(x1*x1 + y1*y1 + z1*z1)
    x1 = x1/mag
    y1 = y1/mag
    z1 = z1/mag

! take cross-product of normal and v1 to get v2

    x2 = ynorm*z1 - znorm*y1
    y2 = znorm*x1 - xnorm*z1
    z2 = xnorm*y1 - ynorm*x1

! variables on left

    ul    = ql(2)
    vl    = ql(3)
    wl    = ql(4)
    ubarl = xnorm*ul + ynorm*vl + znorm*wl

! variables on right

    ur    = qr(2)
    vr    = qr(3)
    wr    = qr(4)
    ubarr = xnorm*ur + ynorm*vr + znorm*wr

! now compute eigenvalues and |a| from averaged variables
! avergage variables

    u  = my_half*(ul + ur)
    v  = my_half*(vl + vr)
    w  = my_half*(wl + wr)
    ubar = xnorm*u + ynorm*v + znorm*w
    c2   = (ubar-fspd_half)*(ubar-fspd_half) + beta
    c    = sqrt(c2)

    eig1 = ubar - face_speed
    eig2 = ubar - face_speed
    eig3 = ubar - fspd_half + c
    eig4 = ubar - fspd_half - c

! put in the eigenvalue smoothing stuff

!   eigeps = .1 * (abs(ubar)+abs(c))

!   if(eig1 < eigeps)eig1 = my_half*(eig1**2/eigeps + eigeps)
!   if(eig2 < eigeps)eig2 = my_half*(eig2**2/eigeps + eigeps)
!   if(eig3 < eigeps)eig3 = my_half*(eig3**2/eigeps + eigeps)
!   if(eig4 < eigeps)eig4 = my_half*(eig4**2/eigeps + eigeps)

! components of T(inverse)

    term = my_1/(c2-fspd2_4th)

    phi1  = xnorm*beta + u*eig1
    phi2  = ynorm*beta + v*eig1
    phi3  = znorm*beta + w*eig1

    ti14  = (x2*phi2 - y2*phi1)*term
    ti24  = (y1*phi1 - x1*phi2)*term
    ti34  = (znorm*(c - fspd_half)/c*my_half)*term
    ti44  = (znorm*(c + fspd_half)/c*my_half)*term

    ti13  = (z2*phi1 - x2*phi3)*term
    ti23  = (x1*phi3 - z1*phi1)*term
    ti33  = (ynorm*(c - fspd_half)/c*my_half)*term
    ti43  = (ynorm*(c + fspd_half)/c*my_half)*term

    ti12  = (y2*phi3 - z2*phi2)*term
    ti22  = (z1*phi2 - y1*phi3)*term
    ti32  = (xnorm*(c - fspd_half)/c*my_half)*term
    ti42  = (xnorm*(c + fspd_half)/c*my_half)*term

    ti11  = (-(u*ti12 + v*ti13 + w*ti14)/beta)
    ti21  = (-(u*ti22 + v*ti23 + w*ti24)/beta)
    ti31  = (-eig4*(c - fspd_half)/(beta*c)*my_half)*term
    ti41  = (-eig3*(c + fspd_half)/(beta*c)*my_half)*term

! now get elements of T

    t11 = my_0
    t21 = x1
    t31 = y1
    t41 = z1

    t12 = my_0
    t22 = x2
    t32 = y2
    t42 = z2

    t13 = (c+fspd_half)*beta
    t23 = xnorm*beta + u*eig3
    t33 = ynorm*beta + v*eig3
    t43 = znorm*beta + w*eig3

    t14 = -(c-fspd_half)*beta
    t24 = xnorm*beta + u*eig4
    t34 = ynorm*beta + v*eig4
    t44 = znorm*beta + w*eig4

! compute t*|lambda|*t(inv)

    eig1 = area*abs(eig1)
    eig2 = area*abs(eig2)
    eig3 = area*abs(eig3)
    eig4 = area*abs(eig4)

    a11 = eig1*t11*ti11 + eig2*t12*ti21 + eig3*t13*ti31 + eig4*t14*ti41
    a12 = eig1*t11*ti12 + eig2*t12*ti22 + eig3*t13*ti32 + eig4*t14*ti42
    a13 = eig1*t11*ti13 + eig2*t12*ti23 + eig3*t13*ti33 + eig4*t14*ti43
    a14 = eig1*t11*ti14 + eig2*t12*ti24 + eig3*t13*ti34 + eig4*t14*ti44

    a21 = eig1*t21*ti11 + eig2*t22*ti21 + eig3*t23*ti31 + eig4*t24*ti41
    a22 = eig1*t21*ti12 + eig2*t22*ti22 + eig3*t23*ti32 + eig4*t24*ti42
    a23 = eig1*t21*ti13 + eig2*t22*ti23 + eig3*t23*ti33 + eig4*t24*ti43
    a24 = eig1*t21*ti14 + eig2*t22*ti24 + eig3*t23*ti34 + eig4*t24*ti44

    a31 = eig1*t31*ti11 + eig2*t32*ti21 + eig3*t33*ti31 + eig4*t34*ti41
    a32 = eig1*t31*ti12 + eig2*t32*ti22 + eig3*t33*ti32 + eig4*t34*ti42
    a33 = eig1*t31*ti13 + eig2*t32*ti23 + eig3*t33*ti33 + eig4*t34*ti43
    a34 = eig1*t31*ti14 + eig2*t32*ti24 + eig3*t33*ti34 + eig4*t34*ti44

    a41 = eig1*t41*ti11 + eig2*t42*ti21 + eig3*t43*ti31 + eig4*t44*ti41
    a42 = eig1*t41*ti12 + eig2*t42*ti22 + eig3*t43*ti32 + eig4*t44*ti42
    a43 = eig1*t41*ti13 + eig2*t42*ti23 + eig3*t43*ti33 + eig4*t44*ti43
    a44 = eig1*t41*ti14 + eig2*t42*ti24 + eig3*t43*ti34 + eig4*t44*ti44

! regular jacobians on left

    dfl(1,1) = my_0
    dfl(1,2) = area*beta*xnorm
    dfl(1,3) = area*beta*ynorm
    dfl(1,4) = area*beta*znorm

    dfl(2,1) = area*xnorm
    dfl(2,2) = area*(ubarl + xnorm*ul)
    dfl(2,3) = area*ynorm*ul
    dfl(2,4) = area*znorm*ul

    dfl(3,1) = area*ynorm
    dfl(3,2) = area*xnorm*vl
    dfl(3,3) = area*(ubarl + ynorm*vl)
    dfl(3,4) = area*znorm*vl

    dfl(4,1) = area*znorm
    dfl(4,2) = area*xnorm*wl
    dfl(4,3) = area*ynorm*wl
    dfl(4,4) = area*(ubarl + znorm*wl)

! form .5*(a + |a|)

    dfl(1,1) = my_half*(dfl(1,1) + a11)
    dfl(1,2) = my_half*(dfl(1,2) + a12)
    dfl(1,3) = my_half*(dfl(1,3) + a13)
    dfl(1,4) = my_half*(dfl(1,4) + a14)

    dfl(2,1) = my_half*(dfl(2,1) + a21)
    dfl(2,2) = my_half*(dfl(2,2) + a22)
    dfl(2,3) = my_half*(dfl(2,3) + a23)
    dfl(2,4) = my_half*(dfl(2,4) + a24)

    dfl(3,1) = my_half*(dfl(3,1) + a31)
    dfl(3,2) = my_half*(dfl(3,2) + a32)
    dfl(3,3) = my_half*(dfl(3,3) + a33)
    dfl(3,4) = my_half*(dfl(3,4) + a34)
    dfl(4,1) = my_half*(dfl(4,1) + a41)
    dfl(4,2) = my_half*(dfl(4,2) + a42)
    dfl(4,3) = my_half*(dfl(4,3) + a43)
    dfl(4,4) = my_half*(dfl(4,4) + a44)

! now get regular jacobians on right

    dfr(1,1) = my_0
    dfr(1,2) = area*beta*xnorm
    dfr(1,3) = area*beta*ynorm
    dfr(1,4) = area*beta*znorm

    dfr(2,1) = area*xnorm
    dfr(2,2) = area*(ubarr + xnorm*ur)
    dfr(2,3) = area*ynorm*ur
    dfr(2,4) = area*znorm*ur

    dfr(3,1) = area*ynorm
    dfr(3,2) = area*xnorm*vr
    dfr(3,3) = area*(ubarr + ynorm*vr)
    dfr(3,4) = area*znorm*vr

    dfr(4,1) = area*znorm
    dfr(4,2) = area*xnorm*wr
    dfr(4,3) = area*ynorm*wr
    dfr(4,4) = area*(ubarr + znorm*wr)

! form .5*(a - |a|)

    dfr(1,1) = my_half*(dfr(1,1) - a11)
    dfr(1,2) = my_half*(dfr(1,2) - a12)
    dfr(1,3) = my_half*(dfr(1,3) - a13)
    dfr(1,4) = my_half*(dfr(1,4) - a14)

    dfr(2,1) = my_half*(dfr(2,1) - a21)
    dfr(2,2) = my_half*(dfr(2,2) - a22)
    dfr(2,3) = my_half*(dfr(2,3) - a23)
    dfr(2,4) = my_half*(dfr(2,4) - a24)

    dfr(3,1) = my_half*(dfr(3,1) - a31)
    dfr(3,2) = my_half*(dfr(3,2) - a32)
    dfr(3,3) = my_half*(dfr(3,3) - a33)
    dfr(3,4) = my_half*(dfr(3,4) - a34)

    dfr(4,1) = my_half*(dfr(4,1) - a41)
    dfr(4,2) = my_half*(dfr(4,2) - a42)
    dfr(4,3) = my_half*(dfr(4,3) - a43)
    dfr(4,4) = my_half*(dfr(4,4) - a44)

  end subroutine dfduc3_i

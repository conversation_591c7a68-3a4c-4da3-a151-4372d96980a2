module agglom_defs

  use kinddefs, only : dp

  implicit none

  private

  public :: allocate_agglom, deallocate_agglom
  public :: construction
  public :: topology_pass
  public :: n_goal
  public :: min_acceptance_ratio, max_acceptance_ratio
  public :: enclosure_tolerance

  public :: agglom_f2c
  public :: orphanage
  public :: boundary_flag, boundary_ridge, ridge_mark
  public :: agg_type, sealed
  public :: check_agglomerations
  public :: boundary_agglomeration, boundary_agglomeration_line
  public :: full_line_coarsening
  public :: boundary_interior_flag
  public :: agglomerate_debug, detail, line_debug
  public :: corner_bc, corner_compressed_row

  public :: write_agglomeration_file

  public :: agglom_c2f_ia
  public :: agglom_c2f_ja

  public :: agg_line_f2c
  public :: agg_line_c2f_ia
  public :: agg_line_c2f_ja

  public :: agg_line_stack_minimum
  public :: agg_cline_dofs

  public :: symmetry_z_agglomeration

  public :: btag_type, bc_tag, agg_bc_tag

  type btag_type
   integer :: nbound                       ! # of associated boundary groups
   integer, dimension(:), pointer :: bound ! Boundary group #'s
  end type btag_type

  integer :: construction = 3
  integer :: topology_pass
  integer :: n_goal
  integer :: boundary_interior_flag
  integer :: agglomerate_debug  = -1

  logical :: check_agglomerations = .false.
  logical :: full_line_coarsening = .false.
  logical :: boundary_agglomeration = .false.
  logical :: boundary_agglomeration_line = .false.
  logical :: write_agglomeration_file = .false.

  real(dp) :: min_acceptance_ratio = 0.90_dp
  real(dp) :: max_acceptance_ratio = 1.10_dp
  real(dp) :: enclosure_tolerance = 1.0e-12_dp

  !Global array : mapping parent to child.
  integer, dimension(:), allocatable :: agglom_f2c

  !Local arrays:
  integer, dimension(:), allocatable :: sealed
  integer, dimension(:), allocatable :: agg_type
  integer, dimension(:), allocatable :: boundary_flag  ! num bcs per node
  integer, dimension(:), allocatable :: boundary_ridge

  type(btag_type), dimension(:), allocatable :: bc_tag, agg_bc_tag

  integer, dimension(:,:), allocatable :: ridge_mark   ! two ridge bcs
  integer, dimension(:,:), allocatable :: orphanage

  integer, dimension(:), allocatable :: agglom_c2f_ia
  integer, dimension(:), allocatable :: agglom_c2f_ja

  integer, dimension(:), allocatable :: agg_line_f2c
  integer, dimension(:), allocatable :: agg_line_c2f_ia
  integer, dimension(:), allocatable :: agg_line_c2f_ja

  integer, dimension(:), allocatable :: agg_line_stack_minimum
  integer, dimension(:), allocatable :: agg_cline_dofs

  type corner_compressed_row
    integer,  dimension(:), pointer :: ia
    integer,  dimension(:), pointer :: bc_number
  end type corner_compressed_row

  type(corner_compressed_row) :: corner_bc

  logical :: symmetry_z_agglomeration = .false.

  logical :: detail             = .false.

  integer :: line_debug         = 0

  contains

!============================ ALLOCATE_AGGLOM ================================80
!
! Allocate arrays for coarsening.
!
!=============================================================================80

  subroutine allocate_agglom( dof, dofg, n_line1, dof01 )

    integer, intent(in) :: dof, dofg, n_line1, dof01

    integer :: lines, i

  continue

    allocate( boundary_flag(dof)   )
              boundary_flag(:) = 0

    allocate( bc_tag(dof01) )
    do i = 1, dof01
     bc_tag(i)%nbound = 0
     nullify(bc_tag(i)%bound)
    end do

    allocate( ridge_mark(2,dof)    )
              ridge_mark(:,:) = 0

    allocate( sealed(dof)    )
              sealed(:) = 0

    allocate( agg_type(dof)   )
              agg_type(:) = 0


    allocate( agglom_f2c(dofg)  )
              agglom_f2c(:) = 0

    allocate( agglom_c2f_ia(dof+1)  )
              agglom_c2f_ia(:) = 0

    allocate( agglom_c2f_ja( dof  ) )
              agglom_c2f_ja(:) = 0


    lines = max( 1, n_line1 )
    allocate( agg_line_f2c(lines)  )
              agg_line_f2c(:) = 0

    allocate( agg_line_c2f_ia(lines+1) )
              agg_line_c2f_ia(:) = 0

    allocate( agg_line_c2f_ja(lines+1) )
              agg_line_c2f_ja(:) = 0

    allocate( agg_line_stack_minimum(lines)  )
              agg_line_stack_minimum(:) = 0

    allocate( agg_cline_dofs(lines)  )
              agg_cline_dofs(:) = 0

  end subroutine allocate_agglom

!============================ ALLOCATE_AGGLOM ================================80
!
! Deallocate arrays for coarsening.
!
!=============================================================================80

  subroutine deallocate_agglom

  continue

    deallocate( boundary_flag )
    deallocate( bc_tag )
    deallocate( ridge_mark )
    deallocate( sealed )
    deallocate( agg_type )

    deallocate( agglom_f2c )
    deallocate( agglom_c2f_ia )
    deallocate( agglom_c2f_ja )

    deallocate( agg_line_f2c )
    deallocate( agg_line_c2f_ia )
    deallocate( agg_line_c2f_ja )
    deallocate( agg_line_stack_minimum )
    deallocate( agg_cline_dofs )

    deallocate( corner_bc%ia )
    deallocate( corner_bc%bc_number )
       nullify( corner_bc%ia )
       nullify( corner_bc%bc_number )

  end subroutine deallocate_agglom

end module agglom_defs

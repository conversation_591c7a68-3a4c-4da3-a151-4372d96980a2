!================================= DSA1_TURB_ABS =============================80
!
! The variation of |turb| ( or turb * f_negative ) for S-A.
!
!=============================================================================80

  pure function dsa1_turb_abs( turb, rnu )

    real(dp), intent(in) :: turb, rnu

    real(dp) :: dsa1_turb_abs

    real(dp) :: chi, dchi

    real(dp), parameter :: cn1 = 16._dp

  continue


      !sa0_turb_abs = turb
      dsa1_turb_abs = 1._dp

      if ( turb < 0._dp ) then

        chi = turb / rnu

        dchi = 1._dp/rnu

        !sa0_turb_abs = turb*( cn1 + chi**3 )/ ( cn1 - chi**3 )

        dsa1_turb_abs = ( cn1 + chi**3 )/ ( cn1 - chi**3 ) &

        + cn1*turb*( 6._dp*chi*chi*dchi )/( cn1 - chi**3 )**2

      endif

  end function dsa1_turb_abs

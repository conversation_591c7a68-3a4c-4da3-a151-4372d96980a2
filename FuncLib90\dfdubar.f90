!======================= DFDUBAR =============================================80
!
! d(flux_convection)/d(ubar)
!
!=============================================================================80
  pure function dfdubar( eqn_set, duplus, duminus, n1,                         &
                         xnorm, ynorm, znorm, q_dof, turb_int, turb_ext )

    integer, intent(in) :: eqn_set, n1

    real(dp), intent(in) :: duplus, duminus, xnorm, ynorm, znorm

    real(dp),                 intent(in) :: turb_int, turb_ext
    real(dp), dimension(:,:), intent(in) :: q_dof

    real(dp) :: dfdu, dfdv, dfdw

    real(dp), dimension(4) :: dfdubar

  continue

    dfdu = xnorm*( duplus*turb_int + duminus*turb_ext )
    dfdv = ynorm*( duplus*turb_int + duminus*turb_ext )
    dfdw = znorm*( duplus*turb_int + duminus*turb_ext )

    dfdubar(1:4) = dqc_via_duvw( eqn_set, dfdu, dfdv, dfdw,           &
                   q_dof(1,n1), q_dof(2,n1), q_dof(3,n1), q_dof(4,n1) )

  end function dfdubar

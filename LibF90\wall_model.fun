!   vim: set filetype=fortran:
! emacs: -*- f90 -*-
!-----------------------------------------------------------------------------80

test_suite wall_model


  integer, parameter  :: dp = selected_real_kind(P=15)

  real(dp),  dimension(3,3) :: gradv
  real(dp),  dimension(3,3) :: s
  real(dp),  dimension(3,3) :: w

  real(dp), parameter       :: tol = 1.0e-8

  real(dp), parameter       :: zero                =  0.0_dp
  real(dp), parameter       :: gamma               =  1.4_dp
  real(dp), parameter       :: turbulent_prandtl   =  0.72_dp
  real(dp), parameter       :: sutherland_constant =  198.6_dp
  real(dp), parameter       :: tref                =  540.0_dp
  real(dp), parameter       :: kappa               =  0.41_dp
  real(dp), parameter       :: b                   =  5.0_dp


  real(dp), parameter, dimension(3,3) :: delta =   &
            reshape((/1.,0.,0.,0.,1.,0.,0.,0.,1./),(/3,3/))

!=============================================================================80
!=============================================================================80
!=============================================================================80
test dlr_kw_bc_routine

    real(dp) :: xmr
    real(dp) :: velocity
    real(dp) :: distance
    real(dp) :: rho
    real(dp) :: mu  
    real(dp) :: y_plus_visc
    real(dp) :: u_tau_visc
    real(dp) :: u_tau
    real(dp) :: tau_wall
    real(dp) :: uplus
    real(dp) :: dudn
    real(dp) :: k_bc
    real(dp) :: omega_bc
    real(dp) :: omega_bc_wilcox
    real(dp) :: omega_bc_log
    real(dp) :: mu_t_bc

    real(dp), parameter :: kappa = 0.41_dp
    real(dp), parameter :: b     = 5.1_dp
    real(dp), parameter :: beta1 = 0.075_dp
    real(dp), parameter :: cmu_0 = 0.09_dp

  xmr         = 0.097_dp / 2.7e+6_dp
  rho         = 1.00_dp
  mu          = 1.0023_dp

  write(*,*) '----------------------'
  write(*,*) '---test 1--dlr_kw_bc--'
  write(*,*) '----------------------'

  distance = 0.000020_dp
  velocity = 0.006_dp
  dudn     = velocity / distance
  tau_wall = mu * dudn
  u_tau_visc = sqrt(tau_wall/rho)*sqrt(xmr)

  y_plus_visc  = distance * sqrt((rho/mu) * dudn / xmr )
  uplus        = velocity / u_tau_visc

  omega_bc_wilcox  = 6.0_dp * (mu/rho) / beta1 * ( xmr / distance )**2
  omega_bc_log     = abs(u_tau_visc) * xmr / &
                   ( sqrt( cmu_0 ) * kappa * distance )

  write(6,'(a,f15.6)') 'dudn:           ', dudn
  write(6,'(a,f15.6)') 'tau_wall:       ', tau_wall
  write(6,'(a,f15.6)') 'y_plus_visc:    ', y_plus_visc
  write(6,'(a,f15.6)') 'uplus:          ', uplus
  write(6,'(a,f15.6)') 'u_tau_visc:     ', u_tau_visc
  write(6,'(a,f15.6)') 'omega_bc_wilcox:', omega_bc_wilcox

  u_tau = dlr_sa_f( xmr, velocity, distance, rho, mu, u_tau_visc)
  write(6,'(a,f15.6)') 'u_tau-dlr_sa : ', u_tau
  u_tau = dlr_kw_f( xmr, velocity, distance, rho, mu, u_tau_visc)
  write(6,'(a,f15.6)') 'u_tau-dlr_kw : ', u_tau
  assert_equal_within( 3.288354804541144E-003 , u_tau, tol )

  call dlr_kw_bc ( omega_bc_wilcox, omega_bc_log, rho,                         &
                   y_plus_visc, k_bc, omega_bc, mu_t_bc )
  write(6,'(a,f15.6)') 'k_bc      : ', k_bc
  write(6,'(a,f15.6)') 'omega_bc  : ', omega_bc
  write(6,'(a,f15.6)') 'mu_t_bc   : ', mu_t_bc

end test
!=============================================================================80
!=============================================================================80
!=============================================================================80
test dlr_sa_function

    real(dp) :: xmr
    real(dp) :: velocity
    real(dp) :: distance
    real(dp) :: rho
    real(dp) :: mu  
    real(dp) :: y_plus_visc
    real(dp) :: u_tau_visc
    real(dp) :: u_tau
    real(dp) :: tau_wall
    real(dp) :: uplus
    real(dp) :: dudn
    real(dp), parameter :: kappa = 0.41_dp
    real(dp), parameter :: b     = 5.1_dp

  xmr         = 0.097_dp / 2.7e+6_dp
  rho         = 1.00_dp
  mu          = 1.0023_dp

  write(*,*) '-------------------'
  write(*,*) '---test 1----------'
  write(*,*) '-------------------'

  distance = 0.000005_dp
  velocity = 0.003_dp
  dudn     = velocity / distance
  tau_wall = mu * dudn
  u_tau_visc = sqrt(tau_wall/rho)*sqrt(xmr)

  y_plus_visc  = distance * sqrt((rho/mu) * dudn / xmr )
  uplus        = velocity / u_tau_visc

  write(6,'(a,f15.6)') 'tau_wall:   ', tau_wall
  write(6,'(a,f15.6)') 'y_plus_visc: ', y_plus_visc
  write(6,'(a,f15.6)') 'uplus:       ', uplus

  u_tau = dlr_sa_f( xmr, velocity, distance, rho, mu, u_tau_visc)
  write(6,'(a,f15.6)') 'u_tau_visc: ', u_tau_visc
  write(6,'(a,f15.6)') 'u_tau     : ', u_tau
  assert_equal_within( 4.648133147725341E-003 , u_tau, tol )

  write(*,*) '-------------------'
  write(*,*) '---fin test 1------'
  write(*,*) '---test 2----------'
  write(*,*) '-------------------'


  distance     = 0.00012_dp
  y_plus_visc  = distance * sqrt((rho/mu) * dudn / xmr )
  uplus        = log(y_plus_visc)/kappa + 5.1_dp
  velocity     = uplus * u_tau_visc

  write(6,'(a,f15.6)') 'tau_wall:   ', tau_wall
  write(6,'(a,f15.6)') 'y_plus_visc: ', y_plus_visc
  write(6,'(a,f15.6)') 'uplus:       ', uplus

  u_tau = dlr_sa_f( xmr, velocity, distance, rho, mu, u_tau_visc)
  write(6,'(a,f15.6)') 'u_tau_visc: ', u_tau_visc
  write(6,'(a,f15.6)') 'u_tau     : ', u_tau


  assert_equal_within( 4.821358497657332E-003 , u_tau, tol )

  write(*,*) '-------------------'
  write(*,*) '---fin test 2------'
  write(*,*) '---test 3----------'
  write(*,*) '-------------------'

  distance     = 0.0005_dp
  y_plus_visc  = distance * sqrt((rho/mu) * dudn / xmr )
  uplus        = log(y_plus_visc)/kappa + 5.1_dp
  velocity     = uplus * u_tau_visc

  write(6,'(a,f15.6)') 'tau_wall:   ', tau_wall
  write(6,'(a,f15.6)') 'y_plus_visc: ', y_plus_visc
  write(6,'(a,f15.6)') 'uplus:       ', uplus

  u_tau = dlr_sa_f( xmr, velocity, distance, rho, mu, u_tau_visc)
  write(6,'(a,f15.6)') 'u_tau_visc: ', u_tau_visc
  write(6,'(a,f15.6)') 'u_tau     : ', u_tau
  assert_equal_within( 0.004648133147725341 , u_tau, tol )

  write(*,*) '-------------------'
  write(*,*) '---fin test 3------'
  write(*,*) '-------------------'

end test
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
test dlr_kw_function

    real(dp) :: xmr
    real(dp) :: velocity
    real(dp) :: distance
    real(dp) :: rho
    real(dp) :: mu  
    real(dp) :: y_plus_visc
    real(dp) :: u_tau_visc
    real(dp) :: u_tau
    real(dp) :: tau_wall
    real(dp) :: uplus
    real(dp) :: dudn
    real(dp), parameter :: kappa = 0.41_dp
    real(dp), parameter :: b     = 5.1_dp

  xmr         = 0.097_dp / 2.7e+6_dp
  rho         = 1.00_dp
  mu          = 1.0023_dp

  write(*,*) '-------------------'
  write(*,*) '---test 1---kw-----'
  write(*,*) '-------------------'

  distance = 0.000005_dp
  velocity = 0.003_dp
  dudn     = velocity / distance
  tau_wall = mu * dudn
  u_tau_visc = sqrt(tau_wall/rho)*sqrt(xmr)

  y_plus_visc  = distance * sqrt((rho/mu) * dudn / xmr )
  uplus        = velocity / u_tau_visc

  write(6,'(a,f15.6)') 'tau_wall:   ', tau_wall
  write(6,'(a,f15.6)') 'y_plus_visc: ', y_plus_visc
  write(6,'(a,f15.6)') 'uplus:       ', uplus

  u_tau = dlr_kw_f( xmr, velocity, distance, rho, mu, u_tau_visc)
  write(6,'(a,f15.6)') 'u_tau_visc: ', u_tau_visc
  write(6,'(a,f15.6)') 'u_tau     : ', u_tau
  assert_equal_within( 4.648230254870067E-003 , u_tau, tol )

  write(*,*) '-------------------'
  write(*,*) '---fin test 1------'
  write(*,*) '---test 2---kw-----'
  write(*,*) '-------------------'


  distance     = 0.00012_dp
  y_plus_visc  = distance * sqrt((rho/mu) * dudn / xmr )
  uplus        = log(y_plus_visc)/kappa + 5.1_dp
  velocity     = uplus * u_tau_visc

  write(6,'(a,f15.6)') 'tau_wall:   ', tau_wall
  write(6,'(a,f15.6)') 'y_plus_visc: ', y_plus_visc
  write(6,'(a,f15.6)') 'uplus:       ', uplus

  u_tau = dlr_kw_f( xmr, velocity, distance, rho, mu, u_tau_visc)
  write(6,'(a,f15.6)') 'u_tau_visc: ', u_tau_visc
  write(6,'(a,f15.6)') 'u_tau     : ', u_tau


  assert_equal_within( 5.079672413289204E-003 , u_tau, tol )

  write(*,*) '-------------------'
  write(*,*) '---fin test 2------'
  write(*,*) '---test 3---kw-----'
  write(*,*) '-------------------'

  distance     = 0.0005_dp
  y_plus_visc  = distance * sqrt((rho/mu) * dudn / xmr )
  uplus        = log(y_plus_visc)/kappa + 5.1_dp
  velocity     = uplus * u_tau_visc

  write(6,'(a,f15.6)') 'tau_wall:   ', tau_wall
  write(6,'(a,f15.6)') 'y_plus_visc: ', y_plus_visc
  write(6,'(a,f15.6)') 'uplus:       ', uplus

  u_tau = dlr_kw_f( xmr, velocity, distance, rho, mu, u_tau_visc)
  write(6,'(a,f15.6)') 'u_tau_visc: ', u_tau_visc
  write(6,'(a,f15.6)') 'u_tau     : ', u_tau
  assert_equal_within( 4.651582982055606E-003 , u_tau, tol )

  write(*,*) '-------------------'
  write(*,*) '---fin test 3------'
  write(*,*) '-------------------'

end test
!=============================================================================80
!=============================================================================80
!=============================================================================80
test test_pressure_jacobian_array

  use ddt, only : ddt5, assignment(=), operator(*), operator(-), operator(+)
  use ddt, only : ddt5_identity, operator(/)

  type(ddt5), dimension(5) :: f, g, h, q_ddt, eqn
  type(ddt5)               :: rho, u, v, w, p, e, ubar

  real(dp), dimension(5) :: q
  real(dp), dimension(3) :: area

  real(dp), parameter :: zero  = 0.0_dp
  real(dp), parameter :: half  = 0.5_dp
  real(dp), parameter :: one   = 1.0_dp
  real(dp), parameter :: gamma = 1.4_dp

  area  = (/5.0_dp, 5.0_dp, 5.0_dp/)

  q     = (/1.0_dp, 1.0_dp, 1.0_dp, 1.0_dp, 2.0_dp/)
  q_ddt = ddt5_identity(q)

  rho   = q_ddt(1)
  u     = q_ddt(2)
  v     = q_ddt(3)
  w     = q_ddt(4)
  p     = q_ddt(5)
  e     = (p/(gamma-one)) + half*rho*(u*u+v*v+w*w)
  ubar  = u*area(1) + v*area(2) + w*area(3)

  f(1) = zero
  f(2) = p*delta(1,1)
  f(3) = p*delta(1,2)
  f(4) = p*delta(1,2)
  f(5) = p*u

  g(1) = zero
  g(2) = p*delta(2,1)
  g(3) = p*delta(2,2)
  g(4) = p*delta(2,3)
  g(5) = p*v

  h(1) = zero
  h(2) = p*delta(3,1)
  h(3) = p*delta(3,2)
  h(4) = p*delta(3,3)
  h(5) = p*w

  eqn(1) = f(1)*area(1) + g(1)*area(2) + h(1)*area(3)
  eqn(2) = f(2)*area(1) + g(2)*area(2) + h(2)*area(3)
  eqn(3) = f(3)*area(1) + g(3)*area(2) + h(3)*area(3)
  eqn(4) = f(4)*area(1) + g(4)*area(2) + h(4)*area(3)
  eqn(5) = f(5)*area(1) + g(5)*area(2) + h(5)*area(3)

  write(6,'(10(1x,f12.5))')  eqn(1)%d(1:5)
  write(6,'(10(1x,f12.5))')  eqn(2)%d(1:5)
  write(6,'(10(1x,f12.5))')  eqn(3)%d(1:5)
  write(6,'(10(1x,f12.5))')  eqn(4)%d(1:5)
  write(6,'(10(1x,f12.5))')  eqn(5)%d(1:5)

  assert_real_equal(   0.0_dp,  eqn(5)%d(1) ) 
  assert_real_equal(  10.0_dp,  eqn(5)%d(2) ) 
  assert_real_equal(  10.0_dp,  eqn(5)%d(3) ) 
  assert_real_equal(  10.0_dp,  eqn(5)%d(4) ) 
  assert_real_equal(  15.0_dp,  eqn(5)%d(5) ) 

end test

test get_element_qp_prim

  use thermo,        only : q_type, primitive_q_type &
                          , conserved_q_type
  use ddt,           only : ddt5, assignment(=)
  use element_defs,  only : max_node_per_cell

  type(ddt5),        dimension(5,8) :: qp
  real(dp),          dimension(5,8) :: q_dof
! real(dp),          dimension(5,8) :: wall_prop
  integer,           dimension(8)   :: c2n_cell
! integer,           dimension(8)   :: node_flag
  integer,           dimension(8)   :: node_map
  integer                           :: eqn_set
  integer                           :: nodes_per_cell
  integer                           :: n_var

  max_node_per_cell = 8
  n_var             = 5
  eqn_set           = 0
! node_flag         = 1
! node_flag(1)      = 0
! node_flag(2)      = 0
! node_flag(3)      = 0
! node_flag(4)      = 0

! wall_prop  = 0.0_dp

  q_dof(1,:) = 0.5_dp

  q_dof(2,1) = 0.0001_dp
  q_dof(2,2) = 0.0001_dp
  q_dof(2,3) = 0.0001_dp
  q_dof(2,4) = 0.0001_dp
  q_dof(2,5) = 0.0010_dp
  q_dof(2,6) = 0.0010_dp
  q_dof(2,7) = 0.0010_dp
  q_dof(2,8) = 0.0010_dp

  q_dof(3,1) = 0.00010_dp
  q_dof(3,2) = 0.00010_dp
  q_dof(3,3) = 0.00010_dp
  q_dof(3,4) = 0.00010_dp
  q_dof(3,5) = 0.00015_dp
  q_dof(3,6) = 0.00015_dp
  q_dof(3,7) = 0.00015_dp
  q_dof(3,8) = 0.00015_dp

  q_dof(4,1) = 0.00010_dp
  q_dof(4,2) = 0.00010_dp
  q_dof(4,3) = 0.00010_dp
  q_dof(4,4) = 0.00010_dp
  q_dof(4,5) = 0.00015_dp
  q_dof(4,6) = 0.00015_dp
  q_dof(4,7) = 0.00015_dp
  q_dof(4,8) = 0.00015_dp

  q_dof(5,:) = 0.714_dp

  nodes_per_cell = 8
  c2n_cell       = (/1,2,3,4,5,6,7,8/)
  node_map       = (/1,2,3,4,5,6,7,8/)
  q_type         = primitive_q_type

  write(6,*) 'q_type = ', q_type
  write(6,*) 'conserved = ', conserved_q_type
  write(6,*) 'primitive = ', primitive_q_type
  qp = get_element_qp_ddt ( q_dof, c2n_cell, node_map &
                      , eqn_set, nodes_per_cell, n_var )

! write(6,'(a,10(1x,f15.5))') 'qp(1,1)= ', qp(1,1)
! write(6,'(a,10(1x,f15.5))') 'qp(2,1)= ', qp(2,1)

  assert_real_equal( 0.5,   qp(1,1)%f )
  assert_real_equal( 1.0,   qp(1,1)%d(1) )
  assert_real_equal( 0.0,   qp(1,1)%d(2) )
  assert_real_equal( 0.0,   qp(1,1)%d(3) )
  assert_real_equal( 0.0,   qp(1,1)%d(4) )
  assert_real_equal( 0.0,   qp(1,1)%d(5) )

  assert_real_equal( 0.0001,   qp(2,1)%f )
  assert_real_equal( 0.0,   qp(2,1)%d(1) )
  assert_real_equal( 1.0,   qp(2,1)%d(2) )
  assert_real_equal( 0.0,   qp(2,1)%d(3) )
  assert_real_equal( 0.0,   qp(2,1)%d(4) )
  assert_real_equal( 0.0,   qp(2,1)%d(5) )
 
  write(6,'(a,10(1x,f20.10))') 'fin get_element_qp 1'

  q_type         = conserved_q_type

  qp = get_element_qp_ddt ( q_dof, c2n_cell, node_map &
                      , eqn_set, nodes_per_cell, n_var )

  assert_real_equal( 0.5,   qp(1,1)%f )
  assert_real_equal( 1.0,   qp(1,1)%d(1) )
  assert_real_equal( 0.0,   qp(1,1)%d(2) )
  assert_real_equal( 0.0,   qp(1,1)%d(3) )
  assert_real_equal( 0.0,   qp(1,1)%d(4) )
  assert_real_equal( 0.0,   qp(1,1)%d(5) )

  assert_real_equal( 0.0002,   qp(2,1)%f )
  assert_real_equal( 0.0,   qp(2,1)%d(1) )
  assert_real_equal( 1.0,   qp(2,1)%d(2) )
  assert_real_equal( 0.0,   qp(2,1)%d(3) )
  assert_real_equal( 0.0,   qp(2,1)%d(4) )
  assert_real_equal( 0.0,   qp(2,1)%d(5) )
  write(6,'(a,10(1x,f20.10))') 'fin get_element_qp 2'

end test

test return_wall_function_bc_info

  use grids,          only : nullify_grid
  use grid_types,     only : grid_type
  use solution_types, only : soln_type
  use bc_types,       only : bcgrid_type, nullify_bc, allocate_dummy_bc      &
                           , deallocate_bc, bcsoln_type, allocate_bcsoln
  use element_types,  only : elem_type
  use allocations,    only : my_realloc_ptr
  use element_defs,   only : nullify_elem, allocate_elem, initialize_elem    &
                           , deallocate_elem

  use solution_types, only : compressible
  use thermo,         only : q_type, conserved_q_type, primitive_q_type
  use turbulence_info, only : u_tau_model, wf_bc_model
  use generic_gas_map, only : n_momx, n_momy, n_momz
  use thermo,         only : q_type, primitive_q_type
  use nml_boundary_conditions, only : wall_velocity

  use info_depr,     only : skeleton

  type(grid_type)                   :: grid
  type(soln_type)                   :: soln
  integer                           :: ib
  integer                           :: node
  integer                           :: eqn_set

  real(dp)                          :: tref
  real(dp)                          :: xmach
  real(dp)                          :: reyno
  real(dp)                          :: xmr
  real(dp)                          :: nu
  real(dp)                          :: uplus
  real(dp)                          :: k_bc, omega_bc, mu_t_bc

  eqn_set             = 0
  q_type              = primitive_q_type

  call nullify_grid ( grid )

  allocate ( grid%bc(1) )
  ib                 = 1
  grid%bc(ib)%ibc    = 4100
  grid%bc(ib)%nbnode = 4

  nullify  ( soln%bcsoln )
  allocate ( soln%bcsoln(ib))
  call allocate_bcsoln ( grid%bc(ib)%nbnode, soln%bcsoln(1) )

  allocate ( grid%bc(ib)%ibnode    ( grid%bc(ib)%nbnode ) )
  allocate ( grid%bc(ib)%slen_wall ( grid%bc(ib)%nbnode ) )

  allocate ( soln%bcsoln(ib)%u_tau    ( grid%bc(ib)%nbnode ) )
  allocate ( soln%bcsoln(ib)%v_corr   ( grid%bc(ib)%nbnode ) )
  allocate ( soln%bcsoln(ib)%yplus_wf ( grid%bc(ib)%nbnode ) )
  allocate ( soln%bcsoln(ib)%rho      ( grid%bc(ib)%nbnode ) )
  allocate ( soln%bcsoln(ib)%nu       ( grid%bc(ib)%nbnode ) )
  allocate ( soln%bcsoln(ib)%phi_wf   ( grid%bc(ib)%nbnode ) )
  allocate ( soln%bcsoln(ib)%k_wf     ( grid%bc(ib)%nbnode ) )
  allocate ( soln%bcsoln(ib)%mu_t_wf  ( grid%bc(ib)%nbnode ) )
  allocate ( soln%bcsoln(ib)%omega_wf ( grid%bc(ib)%nbnode ) )

  grid%bc(ib)%slen_wall(1) = 0.0001
  grid%bc(ib)%slen_wall(2) = 0.0002
  grid%bc(ib)%slen_wall(3) = 0.0005
  grid%bc(ib)%slen_wall(4) = 0.0010

  grid%bc(ib)%ibnode(1) = 1
  grid%bc(ib)%ibnode(2) = 2
  grid%bc(ib)%ibnode(3) = 3
  grid%bc(ib)%ibnode(4) = 4

  tref    = 540.0_dp ! [R]
  nu      = 1.0_dp
  xmach   = 0.1_dp
  reyno   = 1.0e+6
  xmr     = xmach / reyno

  uplus   = 1.0_dp
!   u_tau = yplus * nu * xmr / (0.25_dp*grid%bc(ib)%slen_wall(node))
write(*,*)

  soln%bcsoln(ib)%u_tau(1:4)    = 0.0033_dp

  soln%bcsoln(ib)%v_corr(1)   = 0.1_dp * soln%bcsoln(ib)%u_tau(1)
  soln%bcsoln(ib)%v_corr(2)   = 0.1_dp * soln%bcsoln(ib)%u_tau(2)
  soln%bcsoln(ib)%v_corr(3)   = 0.1_dp * soln%bcsoln(ib)%u_tau(3)
  soln%bcsoln(ib)%v_corr(4)   = 0.1_dp * soln%bcsoln(ib)%u_tau(4)

  soln%bcsoln(ib)%yplus_wf(1) = 1.0_dp
  soln%bcsoln(ib)%yplus_wf(2) = 5.0_dp
  soln%bcsoln(ib)%yplus_wf(3) = 10.0_dp
  soln%bcsoln(ib)%yplus_wf(4) = 20.0_dp

  soln%bcsoln(ib)%rho(1:4)    = 1.0_dp
  soln%bcsoln(ib)%nu(1:4)     = 1.0_dp
  soln%bcsoln(ib)%phi_wf(1:4) = 0.0_dp

  u_tau_model  = 'dlr_kw'
  wf_bc_model  = 'dlr'

  write(6,'(a,10(1x,f15.5))') '............wall_function_bc_info .......'
  write(6,*)

  do node = 1, grid%bc(ib)%nbnode

    call wall_function_bc_info ( grid, ib, node, xmr,                       &
              soln%bcsoln(ib)%u_tau(node),    soln%bcsoln(ib)%v_corr(node), &
              soln%bcsoln(ib)%yplus_wf(node), soln%bcsoln(ib)%rho(node),    &  
              soln%bcsoln(ib)%nu(node),       soln%bcsoln(ib)%phi_wf(node), &
              k_bc,                           omega_bc,                     &
              mu_t_bc )

        soln%bcsoln(ib)%mu_t_wf(node)  = mu_t_bc
        soln%bcsoln(ib)%k_wf(node)     = k_bc 
        soln%bcsoln(ib)%omega_wf(node) = omega_bc


  write(6,'(a,i4,10(1x,f12.7))') 'bc_info:    = ', node,   &
         grid%bc(ib)%slen_wall(node),                      &
         soln%bcsoln(ib)%u_tau(node),                      &
         soln%bcsoln(ib)%v_corr(node),                     &
         soln%bcsoln(ib)%yplus_wf(node),                   &
         soln%bcsoln(ib)%rho(node),                        &
         soln%bcsoln(ib)%nu(node),                         &
         soln%bcsoln(ib)%phi_wf(node),                     &
         soln%bcsoln(ib)%mu_t_wf(node),                    &
         soln%bcsoln(ib)%k_wf(node),                       &
         soln%bcsoln(ib)%omega_wf(node)
    enddo

  assert_real_equal( 0.0008000,   soln%bcsoln(ib)%omega_wf(1) )
  assert_real_equal( 0.0002000,   soln%bcsoln(ib)%omega_wf(2) )
  assert_real_equal( 0.0000320,   soln%bcsoln(ib)%omega_wf(3) )
  assert_real_equal( 0.0000080,   soln%bcsoln(ib)%omega_wf(4) )
! assert_real_equal( 0.330246098,   actual(2) ) ! u
! assert_real_equal( 0.0,   actual(3) )         ! v
! assert_real_equal( 0.0,   actual(4) )         ! w
! assert_real_equal( 0.714, actual(5) )         ! p
 
  call deallocate_bc   ( grid%bc(1) )

end test

test return_face_point_hex_quad_on_boundary

  use bc_types,      only : bcgrid_type, nullify_bc, allocate_dummy_bc      &   
                          , deallocate_bc
  use element_types, only : elem_type
  use allocations,   only : my_realloc_ptr
  use element_defs,  only : nullify_elem, allocate_elem, initialize_elem    &   
                          , deallocate_elem

  real(dp),          dimension(18) :: x, y, z
  type(bcgrid_type), dimension(1) :: bc
  integer                         :: nelem
  type(elem_type),   dimension(1) :: elem
  integer                         :: ib
  integer                         :: face_index
  integer                         :: face_corner
  integer                         :: nodes_per_face

  integer                         :: cell

  real(dp), dimension(3)          :: actual

  call nullify_bc ( bc(1) )
  call allocate_dummy_bc ( bc(1) )

  call nullify_elem ( elem(1) )
  call initialize_elem ( elem(1), 'hex')
  call allocate_elem ( elem(1), .false. )

  x(11) = 0.0_dp; y(11) = 0.0_dp; z(11) = 0.0_dp
  x(12) = 0.0_dp; y(12) = 0.0_dp; z(12) = 1.0_dp
  x(13) = 1.0_dp; y(13) = 0.0_dp; z(13) = 0.0_dp
  x(14) = 1.0_dp; y(14) = 0.0_dp; z(14) = 1.0_dp
  x(15) = 0.0_dp; y(15) = 1.0_dp; z(15) = 0.0_dp
  x(16) = 0.0_dp; y(16) = 1.0_dp; z(16) = 2.0_dp
  x(17) = 1.0_dp; y(17) = 1.0_dp; z(17) = 0.0_dp
  x(18) = 1.0_dp; y(18) = 1.0_dp; z(18) = 2.0_dp

  cell           = 1 
  nelem          = 1 
  ib             = 1 
  face_index     = 1 
  face_corner    = 2 
  nodes_per_face = 4 

  ! account for one face triangle
  call my_realloc_ptr( bc(ib)%ibnode,       14 ) 

  bc(ib)%f2nqb(face_index,1) = 1 ! face 1, node 1
  bc(ib)%f2nqb(face_index,2) = 2 ! face 1, node 2
  bc(ib)%f2nqb(face_index,3) = 3 ! face 1, node 3
  bc(ib)%f2nqb(face_index,4) = 4 ! face 1, node 3
  bc(ib)%f2nqb(face_index,5) = 1 ! cell number
  bc(ib)%f2nqb(face_index,6) = 1 ! element type

!  bc(ib)%ibnode(bc(1)%f2nqb(face_index,1)) = 11 ! boundary node count
!  bc(ib)%ibnode(bc(1)%f2nqb(face_index,2)) = 13 ! boundary node count
!  bc(ib)%ibnode(bc(1)%f2nqb(face_index,3)) = 14 ! boundary node count
!  bc(ib)%ibnode(bc(1)%f2nqb(face_index,4)) = 12 ! boundary node count
  bc(ib)%ibnode(bc(1)%f2nqb(face_index,1)) = 15 ! boundary node count
  bc(ib)%ibnode(bc(1)%f2nqb(face_index,2)) = 16 ! boundary node count
  bc(ib)%ibnode(bc(1)%f2nqb(face_index,3)) = 18 ! boundary node count
  bc(ib)%ibnode(bc(1)%f2nqb(face_index,4)) = 17 ! boundary node count

  elem(1)%c2n(1,cell) = 11 ! map of cell 1 nodes
  elem(1)%c2n(2,cell) = 12 
  elem(1)%c2n(3,cell) = 13 
  elem(1)%c2n(4,cell) = 14 
  elem(1)%c2n(5,cell) = 15 
  elem(1)%c2n(6,cell) = 16 
  elem(1)%c2n(7,cell) = 17 
  elem(1)%c2n(8,cell) = 18 

  actual = return_dual_volume ( x, y, z, bc, nelem, elem,    &
           ib, face_index, face_corner, nodes_per_face )

write(6,'(a,3(1x,f15.5))') 'return_dual_volume:...',actual

! assert_equal_within( 0.75, actual(1), tol )
! assert_equal_within( 1.00, actual(2), tol )
! assert_equal_within( 0.25, actual(3), tol )

  call deallocate_elem ( elem(1) )
  call deallocate_bc   ( bc(1) )

end test

end test_suite

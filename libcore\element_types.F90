! Defines the general attributes that make up a cell or element

module element_types

  use kinddefs, only : i8

  implicit none

  private

  public :: elem_type

  type elem_type
    character(len=3) :: type_cell     ! Element type identifier
    integer :: ncell0        ! # of lev0 elements of this type on partition (CC)
    integer :: ncell         ! Number of elements of this type on partition
    integer(i8) :: ncellg ! Number of elements of this type (global)
    integer :: node_per_cell ! Number of nodes in this type of element
    integer :: edge_per_cell ! Number of edges in this type of element
    integer :: face_per_cell ! Num of faces in this type of element
    integer :: face_2d       ! Which face lies on yplane_2d
    integer,  dimension(:,:), pointer :: c2n           ! cell to node pointer
    logical,  dimension(:),   pointer :: big_angle     ! big-angle indicator
    integer,  dimension(:,:), pointer :: c2e           ! cell to edge pointer
    integer,  dimension(:),   pointer :: cl2g          ! Local-to-global pointer
                                                       ! for cells on partition
    integer,  dimension(:,:), pointer :: c2c => null() ! c2c (CC)
    integer,  dimension(:),   pointer::cell_map=>null()! Concat. list pointer
    integer,  dimension(:,:), pointer :: local_e2n     ! local edge-to-node map
    integer,  dimension(:,:), pointer :: local_f2n     ! local face-to-node map
    integer,  dimension(:,:), pointer :: chk_norm      ! face angle checks
    integer,  dimension(:,:), pointer :: local_e2n_2   ! local edge-to-node map
    integer,  dimension(:,:), pointer :: local_f2e     ! local face-to-edge map
    integer,  dimension(:,:), pointer :: e2n_2d        ! edge-to-node map (2D)
  end type elem_type

end module element_types

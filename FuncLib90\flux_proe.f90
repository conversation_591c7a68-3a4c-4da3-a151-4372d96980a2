!================================= FLUX_PROE =================================80
!
! This routine computes the fluxes using <PERSON>'s approximate Riemann solver
! with low Mach number preconditioning.
!
! Default preconditioning:
! Diagonal preconditioning in density-velocity-entropy variables used by
! Turkel/Vatsa in TLNS3D and <PERSON>/<PERSON> in CFL3D
!
! Alternate preconditioning:
! Diagonal preconditioning in density-velocity-pressure variables used by
! <PERSON><PERSON><PERSON> et al.
!
! Note that this function uses primitive variables
!
! ITEMS COMPLETED FOR PRECONDIIONING:
!  (1) RHS routine with beta a smooth function of Mach number.
!  (2) Smoothly limited eigenvalues, including maximum eigenvalue scaling.
!  (3) Jacobian matrix terms (M G^-I M)*dQ/dtau with accompanying change
!      to time step for artificial time stepping.
!  (4) Pressure-velocity-temperature inflow-outflow boundary conditions.
! ITEMS REMAINING FOR PRECONDIIONING:
!  (1) Account of time accuracy.
!  (2) Allowance for other variables to be solved in update.
!
!=============================================================================80

  pure function flux_proe(rx1, ry1, rz1, rx2, ry2, rz2,                        &
                          xnorm, ynorm, znorm, area, vol1, vol2,               &
                          gradx1, grady1, gradz1, phi1,                        &
                          gradx2, grady2, gradz2, phi2,                        &
                          face_speed, term_alternate, ql, qr, second, mu)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_half, my_1, my_2

    use info_depr,       only : adptv_entropy_fix, prec_mach_star,             &
                                prec_mach_trans1, prec_mach_trans2, ivisc
    use fluid,           only : gm1

    real(dp),               intent(in) :: rx1, ry1, rz1, rx2, ry2, rz2
    real(dp),               intent(in) :: xnorm, ynorm, znorm, area
    real(dp),               intent(in) :: vol1, vol2
    real(dp),               intent(in) :: gradx1, grady1, gradz1,              &
                                          gradx2, grady2, gradz2
    real(dp),               intent(in) :: phi1, phi2
    real(dp),               intent(in) :: face_speed, mu
    real(dp),               intent(in) :: term_alternate
    real(dp), dimension(5), intent(in) :: ql, qr
    real(dp), dimension(6)             :: flux_proe

    logical,                intent(in) :: second

    real(dp) :: rhol, rhor, ul, ur, vl, vr, wl, wr, pressl, pressr
    real(dp) :: q2l, q2r, enrgyl, enrgyr, hl, hr, ubarl, ubarr
    real(dp) :: rho, wat, u, v, w, q2, c2, c, ubar, h
    real(dp) :: beta, psi, sigma, g_p, g_m, f_p, f_m, c_sigma
    real(dp) :: mach, beta_star, mach_rel
    real(dp) :: ubar_fs, ubar_fsl, ubar_fsr
    real(dp) :: switch, switchv
    real(dp) :: drho, du, dv, dw, dpress, dubar
    real(dp) :: dv1, dv2, dv3, dv4, dv5, dv6, dv7, dv8, at1

    real(dp), dimension(3) :: eig, abseig
    real(dp), dimension(5) :: t, fluxl, fluxr

    integer,  parameter :: behavior = 10
    integer,  parameter :: powerv = 4
    real(dp), parameter :: poweri = my_2
    real(dp), parameter :: laplcc = my_2

  continue

!   Get left and right state primitive variables

    rhol   = ql(1)
    ul     = ql(2)
    vl     = ql(3)
    wl     = ql(4)
    pressl = ql(5)

    rhor   = qr(1)
    ur     = qr(2)
    vr     = qr(3)
    wr     = qr(4)
    pressr = qr(5)

!   Compute the remaining needed left and right state variables:

    q2l    = ul*ul + vl*vl + wl*wl
    enrgyl = pressl/gm1 + my_half*rhol*q2l
    hl     = (enrgyl + pressl)/rhol
    ubarl  = xnorm*ul + ynorm*vl + znorm*wl

    q2r    = ur*ur + vr*vr + wr*wr
    enrgyr = pressr/gm1 + my_half*rhor*q2r
    hr     = (enrgyr + pressr)/rhor
    ubarr  = xnorm*ur + ynorm*vr + znorm*wr

!   Compute Roe averages (nominal values)

    rho  = sqrt(rhol*rhor)
    wat  = rho/(rho + rhor)
    u    = ul*wat + ur*(my_1 - wat)
    v    = vl*wat + vr*(my_1 - wat)
    w    = wl*wat + wr*(my_1 - wat)
    h    = hl*wat + hr*(my_1 - wat)
    q2   = u*u + v*v + w*w
    c2   = gm1*(h - my_half*q2)
    c    = sqrt(c2)
    ubar = xnorm*u + ynorm*v + znorm*w

!   Compute normal velocity - face speed used in eigenvalue
!   and unsplit flux contributions

    ubar_fsl = ubarl - face_speed
    ubar_fsr = ubarr - face_speed
    ubar_fs  = ubar  - face_speed

!   Compute beta based on local Mach number
!   beta = 1        M >= M_t2
!        = (M*)^2   M <= M_t1
!        = F(M)     otherwise [ i.e., M_t1 < M < M_t2 ]

    beta_star = prec_mach_star**2
    mach      = sqrt( q2/c2 )

    if (mach >= prec_mach_trans2) then
      beta = my_1
    else if (mach <= prec_mach_trans1) then
      beta = beta_star
    else
      mach_rel = (mach-prec_mach_trans1)/(prec_mach_trans2-prec_mach_trans1)
      beta = beta_star + (beta_star-my_1)*(my_2*mach_rel**2)*(-1.5_dp+mach_rel)
    end if

!   Compute preconditioning parameters

    psi     = ubar_fs*( my_1 - beta )*my_half
    sigma   = sqrt( psi**2 + beta*c2 )

    c_sigma = c/sigma
    g_p     = -(psi-sigma)
    g_m     = +(psi+sigma)
    f_p     = g_p/(beta*c)
    f_m     = g_m/(beta*c)

!   Now compute eigenvalues, eigenvectors, and strengths
!   including preconditioning parameters

    eig(1) = ubar_fs - psi + sigma
    eig(2) = ubar_fs - psi - sigma
    eig(3) = ubar_fs

!   Eigenvalue limiting either adaptive or constant

    switch = my_1

    if (adptv_entropy_fix) then

!     Compute feature detection switch

      switch = iswch_coef(rx1,ry1,rz1,rx2,ry2,rz2,gradx1,grady1,gradz1,        &
                          gradx2,grady2,gradz2,pressl,pressr,phi1,phi2,        &
                          ubarl,ubarr,q2l,q2r,q2,c2,laplcc,poweri,behavior)

      if (.not. second) switch = my_0

!     Compute the cell face reynolds number to make the extra dissipation vanish
!     on low Reynolds number cells faces

      if (ivisc >= 2) then
        switchv = vswch_coef(wat,rho,q2l,ubar,q2r,ubar,ubar,c,vol1,vol2,area,  &
                             powerv,mu)
        switch = max(switch, switchv)
      end if

    end if

!   Limit the eigenvalues

    abseig = roe_efix(q2l,q2r,ubarl,ubarr,wat,c,ubar_fs,switch,eig)

!   Primitive variable jumps

    drho   = rhor - rhol
    dpress = pressr - pressl
    du     = ur - ul
    dv     = vr - vl
    dw     = wr - wl
    dubar  = xnorm*du + ynorm*dv + znorm*dw

!   Jumps in acoustic eigenvectors times absolute eigenvalue eig1/eig2

    dv1 = abseig(1)*my_half*(f_p*dpress + rho*c*dubar)/c2
    dv2 = abseig(2)*my_half*(f_m*dpress - rho*c*dubar)/c2

!   Jumps in convective eigenvectors times absolute eigenvalue eig3

!   ...entropy

    dv3 = abseig(3)*(drho - dpress/c2)

!   ...tangential velocities (after rotations into freestream frame)

    dv4 = abseig(3)*rho*(du - dubar*xnorm)
    dv5 = abseig(3)*rho*(dv - dubar*ynorm)
    dv6 = abseig(3)*rho*(dw - dubar*znorm)

    dv7 = c_sigma*( dv1 + dv2 )
    dv8 = c_sigma*( g_m*dv1 - g_p*dv2 )

    t(1) = dv3 + dv7

    t(2) = u*t(1) + dv4 + xnorm*dv8
    t(3) = v*t(1) + dv5 + ynorm*dv8
    t(4) = w*t(1) + dv6 + znorm*dv8

    t(5) = my_half*q2*t(1) + u*dv4 + v*dv5 + w*dv6 + ubar*dv8 + c2*dv7/gm1

!   Add in the alternate preconditioning form contributions

!   ...original entropy only term

    dv3 = abseig(3)*( dpress*(+my_1 - my_1/beta) - dubar*rho*my_2*psi/beta )/c2

    dv7 = c_sigma*( dv1*( -my_1 + my_1/(beta*f_p**2) )                         &
                  + dv2*( -my_1 + my_1/(beta*f_m**2) ) )

    at1 = term_alternate*( dv3 + dv7 )

    t(1) = t(1) + at1
    t(2) = t(2) + at1*u
    t(3) = t(3) + at1*v
    t(4) = t(4) + at1*w
    t(5) = t(5) + at1*my_half*q2

!   Compute flux using variables from the left side of face

    fluxl(1) = ubar_fsl*rhol
    fluxl(2) = ubar_fsl*rhol*ul + xnorm*pressl
    fluxl(3) = ubar_fsl*rhol*vl + ynorm*pressl
    fluxl(4) = ubar_fsl*rhol*wl + znorm*pressl
    fluxl(5) = ubar_fsl*enrgyl  + ubarl*pressl

!   Compute flux using variables from the left side of face

    fluxr(1) = ubar_fsr*rhor
    fluxr(2) = ubar_fsr*rhor*ur + xnorm*pressr
    fluxr(3) = ubar_fsr*rhor*vr + ynorm*pressr
    fluxr(4) = ubar_fsr*rhor*wr + znorm*pressr
    fluxr(5) = ubar_fsr*enrgyr  + ubarr*pressr

    flux_proe(1:5) = my_half*area*(fluxl(1:5) + fluxr(1:5) - t(1:5))

    flux_proe(6) = switch

  end function flux_proe

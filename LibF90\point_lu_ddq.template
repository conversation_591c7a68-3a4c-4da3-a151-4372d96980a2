! -*- f90 -*- this turns on the emacs f90 mode

module point_lu_ddq

  use kinddefs,        only : dp, jp, system_i1

  implicit none

  private

  public :: lu_decomposition

contains


!============================= LU_DECOMPOSITION ==============================80
!
! Routes the code to the correct low-level LU routine.
!
!=============================================================================80
  subroutine lu_decomposition(neq0, a, a_lu, pivot_lu, n_eqns,                 &
                              diag_has_been_decomposed, nb, nm)

    use info_depr,    only : partial_pivoting, testing, skeleton, ntt
    use check_solves, only : lu_roundoff_check

    integer, intent(in) :: nb, nm, n_eqns, neq0

    real(dp), dimension(nm,nm,neq0), intent(in)    :: a
    real(jp), dimension(nm,nm,neq0), intent(inout) :: a_lu

    integer(system_i1), dimension(nm,neq0), intent(inout) :: pivot_lu

    logical, dimension(neq0), intent(inout) :: diag_has_been_decomposed

  continue

    if(partial_pivoting) then

      if(skeleton > 10) write(*,*) ' Using partial pivoting in point LU',&
                                  '...block_size=',nb

      select case(nb)

      case (5)
        call pp_lu_5(nm, neq0, a, a_lu, pivot_lu, n_eqns,  &
                     diag_has_been_decomposed)
      case (4)
        call pp_lu_4(nm, neq0, a, a_lu, pivot_lu, n_eqns,  &
                     diag_has_been_decomposed)
      case (3)
        call pp_lu_3(nm, neq0, a, a_lu, pivot_lu, n_eqns,  &
                     diag_has_been_decomposed)
      case (2)
        call pp_lu_2(nm, neq0, a, a_lu, pivot_lu, n_eqns,  &
                     diag_has_been_decomposed)
      case (1)
        call pp_lu_1(nm, neq0, a, a_lu, pivot_lu, n_eqns,  &
                     diag_has_been_decomposed)
      case (6)
        call pp_lu_6(nm, neq0, a, a_lu, pivot_lu, n_eqns,  &
                     diag_has_been_decomposed)
      case default
        call pp_lu_n(nm, neq0, a, a_lu, pivot_lu, n_eqns,  nb, &
                     diag_has_been_decomposed)
      end select

    else

      if(skeleton > 10) write(*,*) ' Using unpivoted point LU',&
                                  '...block_size=',nb

      select case(nb)
      case (6)
        call lu_6(nm,neq0,a, a_lu, n_eqns,diag_has_been_decomposed)
      case (5)
        call lu_5(nm,neq0,a, a_lu, n_eqns,diag_has_been_decomposed)
      case (4)
        call lu_4(nm,neq0,a, a_lu, n_eqns,diag_has_been_decomposed)
      case (3)
        call lu_3(nm,neq0,a, a_lu, n_eqns,diag_has_been_decomposed)
      case (2)
        call lu_2(nm,neq0,a, a_lu, n_eqns,diag_has_been_decomposed)
      case (1)
        call lu_1(nm,neq0,a, a_lu, n_eqns,diag_has_been_decomposed)
      case default
        !...general lu.
        call lu_n(nm,neq0,a, a_lu,n_eqns,nb,diag_has_been_decomposed)
      end select

      !...provisionally check LU for roundoff susceptibility.

      if ( lu_roundoff_check ) then

        if ( skeleton > 10 ) write(*,*)                           &
        ' lu_decomposition...lu_roundoff_check=',lu_roundoff_check
        call check_lu_roundoff(nm, nb, neq0, a_lu, n_eqns)

      elseif ( testing .and. (ntt < 2) ) then

        if ( skeleton > 10 ) write(*,*) ' Checking LU pivots for roundoff.'
        call check_lu_roundoff(nm, nb, neq0, a_lu, n_eqns)

      endif

    endif

  end subroutine lu_decomposition

!Begin ReplicateBlock 6
!=================================== LU_6 ====================================80
!
! LU decomposition of a 6x6 matrix
! ...U contains inverse of diagonal element to avoid division.
!
!=============================================================================80

  subroutine lu_6(nm, neq0, a_in, a_lu, n_eqns,                                &
                 diag_has_been_decomposed)

    integer, intent(in) :: nm, n_eqns, neq0

    real(dp), dimension(nm,nm,neq0), intent(in)    :: a_in
    real(jp), dimension(nm,nm,neq0), intent(inout) :: a_lu

    logical, dimension(neq0), intent(inout) :: diag_has_been_decomposed

    integer :: k

  continue

!$acc parallel present(a_in,a_lu)
!$acc loop gang vector
    do k = 1,n_eqns

      a_lu(:,:,k) = a_in(:,:,k)

      a_lu(1,1,k) = 1.0_dp/a_lu(1,1,k)

      a_lu(2,1,k) = a_lu(2,1,k)*a_lu(1,1,k)
      a_lu(3,1,k) = a_lu(3,1,k)*a_lu(1,1,k)
      a_lu(4,1,k) = a_lu(4,1,k)*a_lu(1,1,k)
      a_lu(5,1,k) = a_lu(5,1,k)*a_lu(1,1,k)
      a_lu(6,1,k) = a_lu(6,1,k)*a_lu(1,1,k)

      a_lu(2,2,k) =  a_lu(2,2,k) - a_lu(2,1,k)*a_lu(1,2,k)
      a_lu(2,2,k) =  1.0_dp/a_lu(2,2,k)
      a_lu(3,2,k) = (a_lu(3,2,k) - a_lu(3,1,k)*a_lu(1,2,k))*a_lu(2,2,k)
      a_lu(4,2,k) = (a_lu(4,2,k) - a_lu(4,1,k)*a_lu(1,2,k))*a_lu(2,2,k)
      a_lu(5,2,k) = (a_lu(5,2,k) - a_lu(5,1,k)*a_lu(1,2,k))*a_lu(2,2,k)
      a_lu(6,2,k) = (a_lu(6,2,k) - a_lu(6,1,k)*a_lu(1,2,k))*a_lu(2,2,k)

      a_lu(2,3,k) =  a_lu(2,3,k) - a_lu(2,1,k)*a_lu(1,3,k)
      a_lu(3,3,k) =  a_lu(3,3,k) - a_lu(3,1,k)*a_lu(1,3,k)                     &
                                 - a_lu(3,2,k)*a_lu(2,3,k)
      a_lu(3,3,k) =  1.0_dp/a_lu(3,3,k)
      a_lu(4,3,k) = (a_lu(4,3,k) - a_lu(4,1,k)*a_lu(1,3,k)                     &
                                 - a_lu(4,2,k)*a_lu(2,3,k))*a_lu(3,3,k)
      a_lu(5,3,k) = (a_lu(5,3,k) - a_lu(5,1,k)*a_lu(1,3,k)                     &
                                 - a_lu(5,2,k)*a_lu(2,3,k))*a_lu(3,3,k)
      a_lu(6,3,k) = (a_lu(6,3,k) - a_lu(6,1,k)*a_lu(1,3,k)                     &
                                 - a_lu(6,2,k)*a_lu(2,3,k))*a_lu(3,3,k)

      a_lu(2,4,k) =  a_lu(2,4,k) - a_lu(2,1,k)*a_lu(1,4,k)
      a_lu(3,4,k) =  a_lu(3,4,k) - a_lu(3,1,k)*a_lu(1,4,k)                     &
                                 - a_lu(3,2,k)*a_lu(2,4,k)
      a_lu(4,4,k) =  a_lu(4,4,k) - a_lu(4,1,k)*a_lu(1,4,k)                     &
                                 - a_lu(4,2,k)*a_lu(2,4,k)                     &
                                 - a_lu(4,3,k)*a_lu(3,4,k)
      a_lu(4,4,k) =  1.0_dp/a_lu(4,4,k)
      a_lu(5,4,k) = (a_lu(5,4,k) - a_lu(5,1,k)*a_lu(1,4,k)                     &
                                 - a_lu(5,2,k)*a_lu(2,4,k)                     &
                                 - a_lu(5,3,k)*a_lu(3,4,k))*a_lu(4,4,k)
      a_lu(6,4,k) = (a_lu(6,4,k) - a_lu(6,1,k)*a_lu(1,4,k)                     &
                                 - a_lu(6,2,k)*a_lu(2,4,k)                     &
                                 - a_lu(6,3,k)*a_lu(3,4,k))*a_lu(4,4,k)

      a_lu(2,5,k) = a_lu(2,5,k) - a_lu(2,1,k)*a_lu(1,5,k)
      a_lu(3,5,k) = a_lu(3,5,k) - a_lu(3,1,k)*a_lu(1,5,k)                      &
                                - a_lu(3,2,k)*a_lu(2,5,k)
      a_lu(4,5,k) = a_lu(4,5,k) - a_lu(4,1,k)*a_lu(1,5,k)                      &
                                - a_lu(4,2,k)*a_lu(2,5,k)                      &
                                - a_lu(4,3,k)*a_lu(3,5,k)
      a_lu(5,5,k) = a_lu(5,5,k) - a_lu(5,1,k)*a_lu(1,5,k)                      &
                                - a_lu(5,2,k)*a_lu(2,5,k)                      &
                                - a_lu(5,3,k)*a_lu(3,5,k)                      &
                                - a_lu(5,4,k)*a_lu(4,5,k)
      a_lu(5,5,k) =  1.0_dp/a_lu(5,5,k)
      a_lu(6,5,k) =(a_lu(6,5,k) - a_lu(6,1,k)*a_lu(1,5,k)                      &
                                - a_lu(6,2,k)*a_lu(2,5,k)                      &
                                - a_lu(6,3,k)*a_lu(3,5,k)                      &
                                - a_lu(6,4,k)*a_lu(4,5,k))*a_lu(5,5,k)

      a_lu(2,6,k) = a_lu(2,6,k) - a_lu(2,1,k)*a_lu(1,6,k)
      a_lu(3,6,k) = a_lu(3,6,k) - a_lu(3,1,k)*a_lu(1,6,k)                      &
                                - a_lu(3,2,k)*a_lu(2,6,k)
      a_lu(4,6,k) = a_lu(4,6,k) - a_lu(4,1,k)*a_lu(1,6,k)                      &
                                - a_lu(4,2,k)*a_lu(2,6,k)                      &
                                - a_lu(4,3,k)*a_lu(3,6,k)
      a_lu(5,6,k) = a_lu(5,6,k) - a_lu(5,1,k)*a_lu(1,6,k)                      &
                                - a_lu(5,2,k)*a_lu(2,6,k)                      &
                                - a_lu(5,3,k)*a_lu(3,6,k)                      &
                                - a_lu(5,4,k)*a_lu(4,6,k)
      a_lu(6,6,k) = a_lu(6,6,k) - a_lu(6,1,k)*a_lu(1,6,k)                      &
                                - a_lu(6,2,k)*a_lu(2,6,k)                      &
                                - a_lu(6,3,k)*a_lu(3,6,k)                      &
                                - a_lu(6,4,k)*a_lu(4,6,k)                      &
                                - a_lu(6,5,k)*a_lu(5,6,k)
      a_lu(6,6,k) =  1.0_dp/a_lu(6,6,k)

    end do
!$acc end parallel

    diag_has_been_decomposed(1:n_eqns) = .true.

  end subroutine lu_6
!End ReplicateBlock 6

!=================================== LU_N ====================================80
!
! LU decomposition of n x n matrix
! ...U contains inverse of diagonal element to avoid division.
!
!=============================================================================80

  subroutine lu_n(nm, neq0, a_in, a_lu, n_eqns, nb,                            &
                  diag_has_been_decomposed)

    integer, intent(in) :: nm, n_eqns, neq0, nb

    real(dp), dimension(nm,nm,neq0), intent(in)    :: a_in
    real(jp), dimension(nm,nm,neq0), intent(inout) :: a_lu

    logical, dimension(neq0), intent(inout) :: diag_has_been_decomposed

    integer :: j, k, k1, l

  continue

    do l = 1,n_eqns

      a_lu(:,:,l) = a_in(:,:,l)

      do k = 2,nb
        k1 = k-1
        !...store inverse of diagonal in diagonal position
        a_lu(k1,k1,l) = 1.0_dp/a_lu(k1,k1,l)
        do j = k,nb
          a_lu(j,k1,l)   = a_lu(j,k1,l) * a_lu(k1,k1,l)
          a_lu(j,k:nb,l) = a_lu(j,k:nb,l) - a_lu(j ,k1,l) * a_lu(k1,k:nb,l)
        end do
      end do

      a_lu(nb,nb,l) = 1.0_dp/a_lu(nb,nb,l)

    end do

    diag_has_been_decomposed(1:n_eqns) = .true.

  end subroutine lu_n

!=================================== PP_LU_N =================================80
!
! LU decomposition of n x n matrix with scaled partial pivoting.
! ...L has unity diagonal elements.
! ...U contains inverse of diagonal element to avoid division.
!
!=============================================================================80

  subroutine pp_lu_n(nm, neq0, a_in, a_lu, pivot_lu,                           &
                     n_eqns, nb, diag_has_been_decomposed)

    use info_depr, only : skeleton
    use lmpi, only : lmpi_die, lmpi_master, lmpi_reduce, lmpi_bcast

    integer, intent(in) :: nm, n_eqns, neq0, nb

    real(dp), dimension(nm,nm,neq0), intent(in) :: a_in

    real(jp), dimension(nm,nm,neq0), intent(inout) :: a_lu

    integer(system_i1), dimension(nm,neq0), intent(inout) :: pivot_lu

    logical, dimension(neq0), intent(inout) :: diag_has_been_decomposed

    integer :: l

    integer, dimension(4) :: i_pack, i_pack_total

    integer(system_i1) :: step, itemp, row_pivot, row, col

    real(dp) :: check, temp, tolerance

    real(dp), dimension(nm) :: sfac

    integer(system_i1), dimension(nm) :: pivot

    logical :: pivoting

  continue

    tolerance = epsilon(1.0_jp)

    i_pack(:) = 0

    do l = 1,n_eqns

      do row=1,nm
        pivot(row) = row
      enddo

      a_lu(:,:,l) = a_in(:,:,l)

      do row=1,nb
        sfac(row) = abs( a_lu(row,1,l) )
        do col=2,nb
          sfac(row) = max( sfac(row) , abs( a_lu(row,col,l) ) )
        enddo
      enddo

      do row=1,nb
        if(sfac(row) > tolerance) cycle
        i_pack(1) = i_pack(1) + 1
        exit
      enddo

      pivoting = .false.

      do step = 1,nb

        !...upper part (above diagonal)
        do row=2,step-1
          a_lu(row,step,l) = a_lu(row,step,l)                                  &
                           - sum( a_lu(row,1:row-1,l)*a_lu(1:row-1,step,l) )
        enddo

        !...lower part (below diagonal) and diagonal.
        do row=step,nb
          a_lu(row,step,l) = a_lu(row,step,l)                                  &
                           - sum( a_lu(row,1:step-1,l)*a_lu(1:step-1,step,l) )
        enddo

        !...find largest pivot element (scan rows at given column).
        row_pivot = step
        check     = abs( a_lu(step,step,l) ) / sfac(step)
        do row=step+1,nb
          temp = abs( a_lu(row,step,l) )/sfac(row)
          if(check > temp) cycle
          row_pivot = row
          check     = temp
        enddo

        if(row_pivot /= step) then
          pivoting = .true.
          !...swap rows.
          do col=1,nb
            temp             = a_lu(     step,col,l)
            a_lu(     step,col,l) = a_lu(row_pivot,col,l)
            a_lu(row_pivot,col,l) = temp
          enddo
          !...swap pivots.
          itemp            = pivot(     step)
          pivot(     step) = pivot(row_pivot)
          pivot(row_pivot) = itemp
          !...swap scales.
          temp             = sfac(     step)
          sfac(     step) = sfac(row_pivot)
          sfac(row_pivot) = temp
        endif

        if(abs(a_lu(step,step,l)) < tolerance) i_pack(2) = i_pack(2) + 1

        a_lu(step,step,l) = 1.0_dp/a_lu(step,step,l)

        !...scale lower part (below diagonal).
        do row=step+1,nb
          a_lu(row,step,l) = a_lu(row,step,l)*a_lu(step,step,l)
        enddo

      end do

      if(pivoting) i_pack(3) = i_pack(3) + 1

      pivot_lu(:,l) = pivot(:)

    end do

    diag_has_been_decomposed(1:n_eqns) = .true.

!   Diagnostics and/or stoppage.

    i_pack(4) = n_eqns
    call lmpi_reduce(i_pack, i_pack_total)
    call lmpi_bcast(         i_pack_total)

    if(i_pack_total(1) > 0) then
      if(lmpi_master) then
        write(*,*) ' Matrices encountered with row scaling below tolerance=', &
                   i_pack_total(1),' of total=',i_pack_total(4)
        write(*,*) ' ...tolerance=',tolerance
        write(*,*) ' ...Singular LU...type 1 in pp_lu_n...stopping.'
      endif
      call lmpi_die
    endif

    if(i_pack_total(2) > 0) then
      if(lmpi_master) then
        write(*,*) ' Matrices encountered with pivots below tolerance=', &
                   i_pack_total(2),' of total=',i_pack_total(4)
        write(*,*) ' ...tolerance=',tolerance
        write(*,*) ' ...Singular LU...type 2 in pp_lu_n...stopping.'
      endif
      call lmpi_die
    endif

    if((skeleton > 10).and. (i_pack_total(3) > 0)) then
      write(*,*) ' Total LU decompositions using pivoting=',i_pack_total(3),&
                   ' of total=',i_pack_total(4)
        write(*,*) ' .........Fraction of LU decompositions=',     &
                   real(i_pack_total(3),jp)/real(i_pack_total(4),jp)
      elseif(skeleton > 10) then
        write(*,*) ' No pivoting needed in LU...block_size=',nm
    endif

  end subroutine pp_lu_n

!Begin ReplicateBlock 6
!=================================== PP_LU_6 =================================80
!
! LU decomposition of 6x6 matrix with scaled partial pivoting.
! ...L has unity diagonal elements.
! ...U contains inverse of diagonal element to avoid division.
!
!=============================================================================80

  subroutine pp_lu_6(nm, neq0, a_in, a_lu, pivot_lu, n_eqns,                   &
                     diag_has_been_decomposed)

    use info_depr, only : skeleton
    use lmpi, only : lmpi_die, lmpi_master, lmpi_reduce, lmpi_bcast

    integer, intent(in) :: nm, n_eqns, neq0

    real(dp), dimension(nm,nm,neq0), intent(in)    :: a_in
    real(jp), dimension(nm,nm,neq0), intent(inout) :: a_lu

    integer(system_i1), dimension(nm,neq0), intent(inout) :: pivot_lu

    logical, dimension(neq0), intent(inout) :: diag_has_been_decomposed

    integer :: l

    integer, dimension(4) :: i_pack, i_pack_total

    integer(system_i1) :: step, itemp, row_pivot, row, col

    real(dp) :: check, temp, tolerance

    real(dp), dimension(nm,1) :: sfac

    integer(system_i1), dimension(nm) :: pivot

    logical :: pivoting

    integer(system_i1), parameter :: nb = 6_system_i1  !_6 via Ruby script

  continue

    tolerance = epsilon(1.0_jp)

    i_pack(:) = 0

    do l = 1,n_eqns

      do row=1,nm
        pivot(row) = row
      enddo

      a_lu(:,:,l) = a_in(:,:,l)

      sfac(1,1) = abs( a_lu(1,1,l) )
      sfac(2,1) = abs( a_lu(2,1,l) )
      sfac(3,1) = abs( a_lu(3,1,l) )
      sfac(4,1) = abs( a_lu(4,1,l) )
      sfac(5,1) = abs( a_lu(5,1,l) )
      sfac(6,1) = abs( a_lu(6,1,l) )

      do row=1,nb
        do col=min(2_system_i1,nb),nb
          sfac(row,1) = max( sfac(row,1) , abs( a_lu(row,col,l) ) )
        enddo
      enddo

      do row=1,nb
        if(sfac(row,1) > tolerance) cycle
        i_pack(1) = i_pack(1) + 1
        exit
      enddo

      pivoting = .false.

      step = 1

      !...skip upper part (above diagonal)

      !...skip lower part (below diagonal) and diagonal.

      !...find largest pivot element (scan rows at given column).
      row_pivot = step
      check     = abs( a_lu(step,step,l) ) / sfac(step,1)
      do row=step+1,nb
        temp = abs( a_lu(row,step,l) )/sfac(row,1)
        if(check > temp) cycle
        row_pivot = row
        check     = temp
      enddo

      if(row_pivot /= step) then
        pivoting = .true.
        !...swap rows.
        do col=1,nb
          temp               = a_lu(     step,col,l)
          a_lu(     step,col,l) = a_lu(row_pivot,col,l)
          a_lu(row_pivot,col,l) = temp
        enddo
        !...swap pivots.
        itemp            = pivot(     step)
        pivot(     step) = pivot(row_pivot)
        pivot(row_pivot) = itemp
        !...swap scales.
        temp               = sfac(     step,1)
        sfac(     step,1) = sfac(row_pivot,1)
        sfac(row_pivot,1) = temp
      endif

      if(abs(a_lu(step,step,l)) < tolerance) i_pack(2) = i_pack(2) + 1

      a_lu(step,step,l) = 1.0_dp/a_lu(step,step,l)

      !...scale lower part (below diagonal).
      a_lu(2,step,l) = a_lu(2,step,l)*a_lu(step,step,l)
      a_lu(3,step,l) = a_lu(3,step,l)*a_lu(step,step,l)
      a_lu(4,step,l) = a_lu(4,step,l)*a_lu(step,step,l)
      a_lu(5,step,l) = a_lu(5,step,l)*a_lu(step,step,l)
      a_lu(6,step,l) = a_lu(6,step,l)*a_lu(step,step,l)

!Begin SkipStep_1
      step = 2

      !...skip upper part (above diagonal)

      !...lower part (below diagonal) and diagonal.
      a_lu(2,step,l) = a_lu(2,step,l) - a_lu(2,1,l)*a_lu(1,step,l)
      a_lu(3,step,l) = a_lu(3,step,l) - a_lu(3,1,l)*a_lu(1,step,l)
      a_lu(4,step,l) = a_lu(4,step,l) - a_lu(4,1,l)*a_lu(1,step,l)
      a_lu(5,step,l) = a_lu(5,step,l) - a_lu(5,1,l)*a_lu(1,step,l)
      a_lu(6,step,l) = a_lu(6,step,l) - a_lu(6,1,l)*a_lu(1,step,l)

      !...find largest pivot element (scan rows at given column).
      row_pivot = step
      check     = abs( a_lu(step,step,l) ) / sfac(step,1)
      do row=step+1,nb
        temp = abs( a_lu(row,step,l) )/sfac(row,1)
        if(check > temp) cycle
        row_pivot = row
        check     = temp
      enddo

      if(row_pivot /= step) then
        pivoting = .true.
        !...swap rows.
        do col=1,nb
          temp               = a_lu(     step,col,l)
          a_lu(     step,col,l) = a_lu(row_pivot,col,l)
          a_lu(row_pivot,col,l) = temp
        enddo
        !...swap pivots.
        itemp            = pivot(     step)
        pivot(     step) = pivot(row_pivot)
        pivot(row_pivot) = itemp
        !...swap scales.
        temp               = sfac(     step,1)
        sfac(     step,1) = sfac(row_pivot,1)
        sfac(row_pivot,1) = temp
      endif

      if(abs(a_lu(step,step,l)) < tolerance) i_pack(2) = i_pack(2) + 1

      a_lu(step,step,l) = 1.0_dp/a_lu(step,step,l)

      !...scale lower part (below diagonal).
      a_lu(3,step,l) = a_lu(3,step,l)*a_lu(step,step,l)
      a_lu(4,step,l) = a_lu(4,step,l)*a_lu(step,step,l)
      a_lu(5,step,l) = a_lu(5,step,l)*a_lu(step,step,l)
      a_lu(6,step,l) = a_lu(6,step,l)*a_lu(step,step,l)

!Begin SkipStep_2
      step = 3

      !...upper part (above diagonal)
      a_lu(2,step,l) = a_lu(2,step,l) - a_lu(2,1,l)*a_lu(1,step,l)

      !...lower part (below diagonal) and diagonal.
      a_lu(3,step,l) = a_lu(3,step,l) &
                     - sum( a_lu(3,1:step-1,l)*a_lu(1:step-1,step,l) )
      a_lu(4,step,l) = a_lu(4,step,l) &
                     - sum( a_lu(4,1:step-1,l)*a_lu(1:step-1,step,l) )
      a_lu(5,step,l) = a_lu(5,step,l) &
                     - sum( a_lu(5,1:step-1,l)*a_lu(1:step-1,step,l) )
      a_lu(6,step,l) = a_lu(6,step,l) &
                     - sum( a_lu(6,1:step-1,l)*a_lu(1:step-1,step,l) )

      !...find largest pivot element (scan rows at given column).
      row_pivot = step
      check     = abs( a_lu(step,step,l) ) / sfac(step,1)
      do row=step+1,nb
        temp = abs( a_lu(row,step,l) )/sfac(row,1)
        if(check > temp) cycle
        row_pivot = row
        check     = temp
      enddo

      if(row_pivot /= step) then
        pivoting = .true.
        !...swap rows.
        do col=1,nb
          temp               = a_lu(     step,col,l)
          a_lu(     step,col,l) = a_lu(row_pivot,col,l)
          a_lu(row_pivot,col,l) = temp
        enddo
        !...swap pivots.
        itemp            = pivot(     step)
        pivot(     step) = pivot(row_pivot)
        pivot(row_pivot) = itemp
        !...swap scales.
        temp               = sfac(     step,1)
        sfac(     step,1) = sfac(row_pivot,1)
        sfac(row_pivot,1) = temp
      endif

      if(abs(a_lu(step,step,l)) < tolerance) i_pack(2) = i_pack(2) + 1

      a_lu(step,step,l) = 1.0_dp/a_lu(step,step,l)

      !...scale lower part (below diagonal).
      a_lu(4,step,l) = a_lu(4,step,l)*a_lu(step,step,l)
      a_lu(5,step,l) = a_lu(5,step,l)*a_lu(step,step,l)
      a_lu(6,step,l) = a_lu(6,step,l)*a_lu(step,step,l)

!Begin SkipStep_3
      step = 4

      !...upper part (above diagonal)
      a_lu(2,step,l) = a_lu(2,step,l) &
                     -      a_lu(2,  1,l)*a_lu(  1,step,l)
      a_lu(3,step,l) = a_lu(3,step,l) &
                     - sum( a_lu(3,1:2,l)*a_lu(1:2,step,l) )

      !...lower part (below diagonal) and diagonal.
      a_lu(4,step,l) = a_lu(4,step,l) &
                     - sum( a_lu(4,1:step-1,l)*a_lu(1:step-1,step,l) )
      a_lu(5,step,l) = a_lu(5,step,l) &
                     - sum( a_lu(5,1:step-1,l)*a_lu(1:step-1,step,l) )
      a_lu(6,step,l) = a_lu(6,step,l) &
                     - sum( a_lu(6,1:step-1,l)*a_lu(1:step-1,step,l) )

      !...find largest pivot element (scan rows at given column).
      row_pivot = step
      check     = abs( a_lu(step,step,l) ) / sfac(step,1)
      do row=step+1,nb
        temp = abs( a_lu(row,step,l) )/sfac(row,1)
        if(check > temp) cycle
        row_pivot = row
        check     = temp
      enddo

      if(row_pivot /= step) then
        pivoting = .true.
        !...swap rows.
        do col=1,nb
          temp               = a_lu(     step,col,l)
          a_lu(     step,col,l) = a_lu(row_pivot,col,l)
          a_lu(row_pivot,col,l) = temp
        enddo
        !...swap pivots.
        itemp            = pivot(     step)
        pivot(     step) = pivot(row_pivot)
        pivot(row_pivot) = itemp
        !...swap scales.
        temp               = sfac(     step,1)
        sfac(     step,1) = sfac(row_pivot,1)
        sfac(row_pivot,1) = temp
      endif

      if(abs(a_lu(step,step,l)) < tolerance) i_pack(2) = i_pack(2) + 1

      a_lu(step,step,l) = 1.0_dp/a_lu(step,step,l)

      !...scale lower part (below diagonal).
      a_lu(5,step,l) = a_lu(5,step,l)*a_lu(step,step,l)
      a_lu(6,step,l) = a_lu(6,step,l)*a_lu(step,step,l)

!Begin SkipStep_4
      step = 5

      !...upper part (above diagonal)
      a_lu(2,step,l) = a_lu(2,step,l) &
                     -      a_lu(2,  1,l)*a_lu(  1,step,l)
      a_lu(3,step,l) = a_lu(3,step,l) &
                     - sum( a_lu(3,1:2,l)*a_lu(1:2,step,l) )
      a_lu(4,step,l) = a_lu(4,step,l) &
                     - sum( a_lu(4,1:3,l)*a_lu(1:3,step,l) )

      !...lower part (below diagonal) and diagonal.
      a_lu(5,step,l) = a_lu(5,step,l) &
                     - sum( a_lu(5,1:step-1,l)*a_lu(1:step-1,step,l) )
      a_lu(6,step,l) = a_lu(6,step,l) &
                     - sum( a_lu(6,1:step-1,l)*a_lu(1:step-1,step,l) )

      !...find largest pivot element (scan rows at given column).
      row_pivot = step
      check     = abs( a_lu(step,step,l) ) / sfac(step,1)
      do row=step+1,nb
        temp = abs( a_lu(row,step,l) )/sfac(row,1)
        if(check > temp) cycle
        row_pivot = row
        check     = temp
      enddo

      if(row_pivot /= step) then
        pivoting = .true.
        !...swap rows.
        do col=1,nb
          temp               = a_lu(     step,col,l)
          a_lu(     step,col,l) = a_lu(row_pivot,col,l)
          a_lu(row_pivot,col,l) = temp
        enddo
        !...swap pivots.
        itemp            = pivot(     step)
        pivot(     step) = pivot(row_pivot)
        pivot(row_pivot) = itemp
        !...swap scales.
        temp               = sfac(     step,1)
        sfac(     step,1) = sfac(row_pivot,1)
        sfac(row_pivot,1) = temp
      endif

      if(abs(a_lu(step,step,l)) < tolerance) i_pack(2) = i_pack(2) + 1

      a_lu(step,step,l) = 1.0_dp/a_lu(step,step,l)

      !...scale lower part (below diagonal).
      a_lu(6,step,l) = a_lu(6,step,l)*a_lu(step,step,l)

!Begin SkipStep_5
      step = 6

      !...upper part (above diagonal)
      a_lu(2,step,l) = a_lu(2,step,l) &
                     -      a_lu(2,  1,l)*a_lu(  1,step,l)
      a_lu(3,step,l) = a_lu(3,step,l) &
                     - sum( a_lu(3,1:2,l)*a_lu(1:2,step,l) )
      a_lu(4,step,l) = a_lu(4,step,l) &
                     - sum( a_lu(4,1:3,l)*a_lu(1:3,step,l) )
      a_lu(5,step,l) = a_lu(5,step,l) &
                     - sum( a_lu(5,1:4,l)*a_lu(1:4,step,l) )

      !...lower part (below diagonal) and diagonal.
      a_lu(6,step,l) = a_lu(6,step,l) &
                     - sum( a_lu(6,1:step-1,l)*a_lu(1:step-1,step,l) )

      !...find largest pivot element (scan rows at given column).
      row_pivot = step
      check     = abs( a_lu(step,step,l) ) / sfac(step,1)
      do row=step+1,nb
        temp = abs( a_lu(row,step,l) )/sfac(row,1)
        if(check > temp) cycle
        row_pivot = row
        check     = temp
      enddo

      if(row_pivot /= step) then
        pivoting = .true.
        !...swap rows.
        do col=1,nb
          temp               = a_lu(     step,col,l)
          a_lu(     step,col,l) = a_lu(row_pivot,col,l)
          a_lu(row_pivot,col,l) = temp
        enddo
        !...swap pivots.
        itemp            = pivot(     step)
        pivot(     step) = pivot(row_pivot)
        pivot(row_pivot) = itemp
        !...swap scales.
        temp               = sfac(     step,1)
        sfac(     step,1) = sfac(row_pivot,1)
        sfac(row_pivot,1) = temp
      endif

      if(abs(a_lu(step,step,l)) < tolerance) i_pack(2) = i_pack(2) + 1

      a_lu(step,step,l) = 1.0_dp/a_lu(step,step,l)

      !...skip scale lower part (below diagonal).
!End SkipSteps

      if(pivoting) i_pack(3) = i_pack(3) + 1

      pivot_lu(:,l) = pivot(:)

    end do

    diag_has_been_decomposed(1:n_eqns) = .true.

!   Diagnostics and/or stoppage.

    i_pack(4) = n_eqns
    call lmpi_reduce(i_pack, i_pack_total)
    call lmpi_bcast(         i_pack_total)

    if(i_pack_total(1) > 0) then
      if(lmpi_master) then
        write(*,*) ' Matrices encountered with row scaling below tolerance=', &
                   i_pack_total(1),' of total=',i_pack_total(4)
        write(*,*) ' ...tolerance=',tolerance
        write(*,*) ' ...Singular LU...type 1 in pp_lu_6...stopping.'
      endif
      call lmpi_die
    endif

    if(i_pack_total(2) > 0) then
      if(lmpi_master) then
        write(*,*) ' Matrices encountered with pivots below tolerance=', &
                   i_pack_total(2),' of total=',i_pack_total(4)
        write(*,*) ' ...tolerance=',tolerance
        write(*,*) ' ...Singular LU...type 2 in pp_lu_6...stopping.'
      endif
      call lmpi_die
    endif

    if((skeleton > 10).and. (i_pack_total(3) > 0)) then
      write(*,*) ' Total LU decompositions using pivoting=',i_pack_total(3),&
                   ' of total=',i_pack_total(4)
        write(*,*) ' .........Fraction of LU decompositions=',     &
                   real(i_pack_total(3),jp)/real(i_pack_total(4),jp)
      elseif(skeleton > 10) then
        write(*,*) ' No pivoting needed in LU...block_size=',nm
    endif

  end subroutine pp_lu_6
!End ReplicateBlock 6

!================================ CHECK_LU_ROUNDOFF ==========================80
!
! Checking LU terms of LU decomposition for roundoff susceptibility.
!
!=============================================================================80

  subroutine check_lu_roundoff( nm, nb, neq0, a_lu, n_eqns )

    use lmpi, only : lmpi_master, lmpi_reduce, lmpi_bcast, lmpi_max_and_maxid, &
                     lmpi_conditional_stop
    use info_depr, only : skeleton

    integer, intent(in) :: neq0, nb, nm, n_eqns

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_lu

    integer :: j, k, m
    integer :: below_tolerance, below_tolerance_total, lu_total
    integer :: proc_with_min, eqn_min, k_min, matrix_min

    real(dp) :: check

    real(jp) :: diagonal_min, tolerance
    real(jp), dimension(nb) :: pivots
    real(jp), dimension(nb,nb) :: b_min

    logical, parameter :: debugging = .false.

    continue

      tolerance = 10._jp*epsilon(1.0_jp)

      diagonal_min    = huge(0.0_jp)
      below_tolerance = 0

      do k = 1,n_eqns

        do j = 1, nb

          if(abs(1.0_jp/a_lu(j,j,k)) < diagonal_min) then
            diagonal_min = abs(1.0_jp/a_lu(j,j,k))
            eqn_min      = j
            k_min        = k
            matrix_min   = k
            b_min(:,:)   = a_lu(:,:,k)
            do m=1,nb
              pivots(m) = 1.0_jp/a_lu(m,m,k)
            enddo
          endif
        end do

        do j = 1, nb
          if(abs(1.0_jp/a_lu(j,j,k)) < tolerance) then
            below_tolerance = below_tolerance + 1
            exit
          endif
        end do

      end do

!     Find and print minimum over processors.

      check = - real(diagonal_min,jp)
      call lmpi_max_and_maxid(real(check,jp), proc_with_min)

      call lmpi_bcast(diagonal_min, proc_with_min)
      call lmpi_bcast(       k_min, proc_with_min)
      call lmpi_bcast(     eqn_min, proc_with_min)
      call lmpi_bcast(       b_min, proc_with_min)
      call lmpi_bcast(  matrix_min, proc_with_min)

      if ( lmpi_master .and. debugging ) then
        write(*,*) ' Checking U-diagonals in LU...block_size=',nm
        write(*,*) ' ...Min abs(U-diagonal) entry=',diagonal_min
        write(*,*) ' ................on processor=',proc_with_min
        write(*,*) ' .................at equation=',eqn_min,' of',nb
        write(*,*) ' ............block size of LU=',nm
        write(*,*) ' ............at matrix number=',matrix_min
        write(*,*) ' .............at eqns() entry=',k_min
        write(*,*)
        write(*,*) ' .......corresponding LU matrix (with 1/U-diagonal) below:'
        do j=1,nb
          write(*,"(2x,i5,5e21.12/(7x,5e21.12))") j,(real(b_min(j,m),jp),m=1,nb)
        enddo
        write(*,*) ' .......Actual U-diagonal entries below:'
        do j=1,nb
          write(*,"(2x,i5,5e21.12/(7x,5e21.12))") j,real(pivots(j),jp)
        enddo
      endif

!     Print number of U-diagonals below tolerance.

      j = below_tolerance
      call lmpi_reduce(j, below_tolerance_total)
      call lmpi_bcast(below_tolerance_total)
      j = n_eqns
      call lmpi_reduce(j, lu_total)

      if ( below_tolerance_total > 0 ) then
        if ( lmpi_master ) then
          write(*,*) ' Total number of U-diagonals below tolerance=',&
                     below_tolerance_total
          write(*,*) ' .......................Tolerance=',tolerance
          write(*,*) ' ...Fortran epsilon(1.0_jp) value=',epsilon(1.0_jp)
          write(*,*) ' ...........Fraction of total LUs=',          &
                     real(below_tolerance_total,jp)/real(lu_total,jp)
        endif
        call lmpi_conditional_stop(1,'Enforced stop...check_LU_roundoff')
      else
        if ( skeleton > 10 ) then
          write(*,*) ' No U-diagonals below tolerance.'
          write(*,*) ' .......................Tolerance=',tolerance
          write(*,*) ' ...Fortran epsilon(1.0_jp) value=',epsilon(1.0_jp)
          write(*,*) ' ...Min abs(U-diagonal) entry=',diagonal_min
        endif
      endif

  end subroutine check_LU_roundoff

end module point_lu_ddq

/******************************************************************************
 *
 *      Developed By:  <PERSON>
 *                     NASA Langley Research Center
 *                     Phone:(757)864-5318
 *                     Email:<EMAIL>
 *
 *      Modifications: <PERSON>
 *
 *
 *      Developed For: NASA Langley Research Center
 *
 *      Copyright:     This material is declared a work of the U.S. Government
 *                     and is not subject to copyright protection in the
 *                     United States.
 *
 ******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "ioswap.h"
#include "ddfreader.h"

#define DATETIMESIZE 80

static FILE* private_fp=NULL;		/* File Pointer to DDF file */
static long private_nzone=0L;		/* Zone tracker */
static long private_npts=0L;		/* Number of Points tracker */
static long private_nelem=0L;		/* Number of Elements tracker */
static long private_nfunc=0L;		/* Function data tracker */
static long private_zone_offset=0L;	/* Byte offset to start of Zone data */

/*
 * Function pointer used to potentially allow setting to ioswap functions
 * if needed.
 */
size_t (*private_fread)(void *ptr, size_t size, size_t nmem, FILE *fp)=fread;


/*----------------------------------------------------------------------------*/
size_t readBinaryDDFHeader(char* name, char title[80], char version[20], long *nzone, double *solutionTime)
{
  int  magic=0x1234;
  char dateTime[DATETIMESIZE];

  if( private_fp != NULL ) {
    fprintf(stderr,"Active DDF file (finish reading or deactivate with readBinaryDDFComplete)\n");
    return 1;
  }

  if( (private_fp=fopen(name,"rb")) == NULL ) {
    fprintf(stderr,"Failed to open \"%s\" for writing\n",name);
    goto Read_Header_Error;
  }

  /* Read magic number */
  if( private_fread(&magic,sizeof(int),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to read magic number\n");
    goto Read_Header_Error;
  }

  /* File appears to need byte-swapping */
  if( magic != IOSWAP_REF ) {
    private_fread = fread_Swapped;
    fprintf(stderr,"Byte Swapping required!\n");
  }

  /* Re-read magic number */
  if( fseek(private_fp,0L,SEEK_SET) != 0 ) {
    fprintf(stderr,"Failed to rewind magic number\n");
    goto Read_Header_Error;
  }

  if( private_fread(&magic,sizeof(int),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to re-read magic number\n");
    goto Read_Header_Error;
  }

  if( magic != IOSWAP_REF ) {
    fprintf(stderr,"Bad magic number\n");
    goto Read_Header_Error;
  }

  /* Now read the DDF Header data */
  if( private_fread(title,sizeof(char),80,private_fp) != 80 ) {
    fprintf(stderr,"Failed to read title\n");
    goto Read_Header_Error;
  }

  if( private_fread(version,sizeof(char),20,private_fp) != 20 ) {
    fprintf(stderr,"Failed to read title\n");
    goto Read_Header_Error;
  }

  if( private_fread(dateTime,sizeof(char),DATETIMESIZE,private_fp) != DATETIMESIZE ) {
    fprintf(stderr,"Failed to read date and time\n");
    goto Read_Header_Error;
  }

  if( private_fread(solutionTime,sizeof(double),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to read solution time\n");
    goto Read_Header_Error;
  }

  if( private_fread(nzone,sizeof(long),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to read number of zones\n");
    goto Read_Header_Error;
  }

  private_nzone = *nzone;

  return 0;

Read_Header_Error:
  private_nzone = 0;
  if( private_fp ) {
    fclose(private_fp);
    private_fp = NULL;
  }
  return 1;
}


/*----------------------------------------------------------------------------*/
size_t readBinaryDDFZoneHeader(char title[80], long* type, long* npts, long* nfunc, long* nelem)
{
  size_t nmemb;

  if( private_fp == NULL ) {
    fprintf(stderr,"No active DDF file (activate with readBinaryDDFHeader)\n");
    goto Read_Zone_Header_Error;
  }

  private_zone_offset = ftell(private_fp);

  if( private_fread(title,sizeof(char),80,private_fp) != 80 ) {
    fprintf(stderr,"Failed to read zone title\n");
    goto Read_Zone_Header_Error;
  }

  if( private_fread(npts,sizeof(long),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to read the number of points\n");
    goto Read_Zone_Header_Error;
  }

  if( private_fread(nelem,sizeof(long),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to read the number of elements\n");
    goto Read_Zone_Header_Error;
  }

  if( private_fread(nfunc,sizeof(long),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to read the number of functions\n");
    goto Read_Zone_Header_Error;
  }

  if( private_fread(type,sizeof(long),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to read the file type\n");
    goto Read_Zone_Header_Error;
  }

  private_npts = *npts;
  private_nelem = *nelem;
  private_nfunc = *nfunc;

  return 0;

Read_Zone_Header_Error:
  if( private_fp ) {
    fclose(private_fp);
    private_fp = NULL;
  }
  return 1;
}


/*----------------------------------------------------------------------------*/
size_t readBinaryDDFPoint(double* xval, double* yval, double* zval, long* pid, long* setid, double* func)
{
  size_t nmemb;
  double point[3];
  long   pids[2];

  if( private_fp == NULL ) {
    fprintf(stderr,"No active DDF file (activate with readBinaryDDFHeader)\n");
    goto Read_Point_Error;
  }

  if( private_zone_offset == 0 ) {
    fprintf(stderr,"No active DDF Zone Header (need readBinaryDDFZoneHeader first)\n");
    goto Read_Point_Error;
  }

  if( private_npts < 0 ) {
    fprintf(stderr,"Exceeded point count for zone!\n");
    goto Read_Point_Error;
  }

  /* Coordinates */
  nmemb = 3;
  if( xval != NULL && yval != NULL && zval != NULL ) {
    if( private_fread(point,sizeof(double),nmemb,private_fp) != nmemb ) {
      fprintf(stderr,"Failed to read the point\n");
      goto Read_Point_Error;
    }
    *xval = point[0];
    *yval = point[1];
    *zval = point[2];
  } else {
    if( fseek(private_fp,nmemb*sizeof(double),SEEK_CUR) != 0 ) {
      fprintf(stderr,"Failed to skip the point\n");
      goto Read_Point_Error;
    }
  }

  /* Ids */
  nmemb = 2;
  if( pid != NULL && setid != NULL ) {
    if( private_fread(pids,sizeof(long),nmemb,private_fp) != nmemb ) {
      fprintf(stderr,"Failed to read the point ids\n");
      goto Read_Point_Error;
    }
    *pid   = pids[0];
    *setid = pids[1];
  } else {
    if( fseek(private_fp,nmemb*sizeof(long),SEEK_CUR) != 0 ) {
      fprintf(stderr,"Failed to skip the point ids\n");
      goto Read_Point_Error;
    }
  }

  /* Function */
  nmemb = private_nfunc;
  if( func != NULL ) {
    if( private_fread(func,sizeof(double),nmemb,private_fp) != nmemb) {
      fprintf(stderr,"Failed to read %ld function values\n",private_nfunc);
      goto Read_Point_Error;
    }
  } else {
    if( fseek(private_fp,nmemb*sizeof(double),SEEK_CUR) != 0 ) {
      fprintf(stderr,"Failed to skip %ld function values\n",private_nfunc);
      goto Read_Point_Error;
    }
  }

  if( --private_npts == 0 &&
      private_nelem == 0 ) {		/* Zone completed */
    if( --private_nzone == 0 ) {	/* File Completed */
      fclose(private_fp);
      private_fp = NULL;
    }
  }

  return 0;

Read_Point_Error:
  private_npts = private_nelem = 0L;
  private_zone_offset = 0L;
  if( private_fp ) {
    fclose(private_fp);
    private_fp = NULL;
  }
  return 1;
}


/*----------------------------------------------------------------------------*/
size_t readBinaryDDFElement(long* npe, long* id, long* pid, long* setid, long* elem)
{
  size_t nmemb;
  long   eids[4];

  if( private_fp == NULL ) {
    fprintf(stderr,"No active DDF file (activate with readBinaryDDFHeader)\n");
    goto Read_Element_Error;
  }

  if( private_zone_offset == 0 ) {
    fprintf(stderr,"No active DDF Zone Header (need readBinaryDDFZoneHeader first)\n");
    goto Read_Element_Error;
  }

  /* element ids */
  nmemb = 4;
  if( private_fread(eids,sizeof(long),nmemb,private_fp) != nmemb) {
    fprintf(stderr,"Failed to read the element ids\n");
    goto Read_Element_Error;
  }

  /* elements */
  nmemb = eids[0];
  if( elem != NULL ) {
    if( private_fread(elem,sizeof(long),nmemb,private_fp) != nmemb) {
      fprintf(stderr,"Failed to read the elements\n");
      goto Read_Element_Error;
    }
  } else {
    if( fseek(private_fp,nmemb*sizeof(long),SEEK_CUR) != 0 ) {
      fprintf(stderr,"Failed to skip the elements\n");
      goto Read_Element_Error;
    }
  }

  *npe   = eids[0];
  *id    = eids[1];
  *pid   = eids[2];
  *setid = eids[3];

  if( --private_nelem == 0 ) {		/* Zone completed */
    private_zone_offset = 0L;
    if( --private_nzone == 0 ) {	/* File Completed */
      fclose(private_fp);
      private_fp = NULL;
    }
  }
  return 0;

Read_Element_Error:
  private_npts = private_nelem = 0L;
  private_zone_offset = 0L;
  if( private_fp ) {
    fclose(private_fp);
    private_fp = NULL;
  }
  return 1;
}


/*----------------------------------------------------------------------------*/
size_t readBinaryDDFRewindZone()
{
  char title[80];
  long type;
  long npts;
  long nfunc;
  long nelem;

  if( private_fp == NULL ) {
    fprintf(stderr,"No active DDF file (activate with readBinaryDDFHeader)\n");
    goto Rewind_Zone_Error;
  }

  if( private_zone_offset == 0 ) {
    fprintf(stderr,"No active DDF Zone Header (need readBinaryDDFZoneHeader first)\n");
    goto Rewind_Zone_Error;
  }

  if( fseek(private_fp,private_zone_offset,SEEK_SET) != 0 ) {
    fprintf(stderr,"Failed to rewind the current zone\n");
    goto Rewind_Zone_Error;
  }

  if( readBinaryDDFZoneHeader(title,&type,&npts,&nfunc,&nelem) != 0 ) {
    fprintf(stderr,"Failed to reread zone header\n");
    goto Rewind_Zone_Error;
  }

  return 0;

Rewind_Zone_Error:
  private_npts = private_nelem = 0L;
  private_zone_offset = 0L;
  if( private_fp ) {
    fclose(private_fp);
    private_fp = NULL;
  }
  return 1;
}


/*----------------------------------------------------------------------------*/
size_t readBinaryDDFComplete()
{
  private_npts = private_nelem = 0L;
  private_zone_offset = 0L;
  if( private_fp ) {
    fclose(private_fp);
    private_fp = NULL;
  }
  return 0;
}

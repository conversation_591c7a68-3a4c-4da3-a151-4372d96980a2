


!==================================== MINMODV ================================80
!
! MinMod limiter applied to a vector
!
!=============================================================================80

  pure function minmodv(grad_a, grad_b, ndim)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_half, my_1

    integer,                   intent(in) :: ndim
    real(dp), dimension(ndim), intent(in) :: grad_a, grad_b
    real(dp), dimension(ndim)             :: minmodv

  continue

    minmodv = my_half*(sign(my_1, grad_a) + sign(my_1, grad_b)) *              &
                       min(abs(grad_a),abs(grad_b))

  end function minmodv

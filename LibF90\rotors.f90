!========= ROTOR_MODULE ======================================================80
!
!  DESCRIPTION:
!  Module for adding a rotor or propellor influence to a solution. This is
!  achieved by adding source terms distributed across an actuator disk for
!  steady-state computations or actuator blades for unsteady computations.
!  The actuator surfaces do not need to be modeled in the grid by creating
!  a boundary surface, instead an auxillary 2D structured grid is created
!  for the sources and inserted into the volume grid. The sources are then
!  associated with the nearest node in the volume grid and added to the
!  system of equations at the associated node's control volume.
!
!  At a node associated with a source the system of equations will look as
!  follows:
!                         _               _
!                 dQ     /               /
!               V -- +  /  F  * n dA -  /  F  * n dA + S = 0
!                 dt  _/    i         _/    v
!
!  where V ==> control volume,     A ==> boundary area of the control volume
!        Q ==> state vector,       F ==> inviscid and viscous flux vectors
!        t ==> time,               n ==> outward pointing normal
!        S ==> source vector
!
!  The source vector is shown below. The incompressible source is shown on
!  the left and the compressible source vector is shown on the right
!
!          | 0  |             | 0                  |
!          | fx |             | fx                 |
!     S =  | fy |         S = | fy                 |
!          | fz |             | fz                 |
!                             | fx*u + fy*v + fz*w |
!
!  where fx, fy, and fz are the force components on the rotor in the x, y,
!  and z directions, respectively and u, v, and w are the velocity components.
!  Note that the forces are normalized by the reference density, reference
!  velocity squared and the reference length squared.
!
!  Basically the rotor blades are not modeled in the grid, but act on the
!  flow by applying a force to the flow at the source locations. The
!  momentum equations see this influence as an additional force on the flow,
!  which in physical terms is the equal and opposite reaction of the blade
!  on the air. The energy equation sees this influence as a work done on the
!  air by the rotor blade. The blade forces are specified using a predefined
!  distribution of can be computed using a 2D strip theory using the local
!  flow velocity and airfoil tables. Although the blade forces can be computed
!  using the strip theory, the system is treated as a loosely coupled system,
!  meaning that the contribution to the LHS of the governing equation does
!  contain any derivatives of the blade forces with respect to the local
!  flow variables. This significantly simplifies computation of the LHS terms.
!
!  MODULE NOTES:
!  1) rotor_type is defined in the beginning of rotors and is therefore
!     treated like a common type by all of the subroutines local to this module.
!  2) rotor_flag is the only public variable and is utilized to tell the solver
!     whether the rotors subroutines should be invoked.
!  3) Throughout this routine the following convention loop control variable
!     convention is used: i -> loop over nrotor, j -> loop over nradial,
!     and k -> loop over nnormal
!
!=============================================================================80

module rotors

  use kinddefs, only : dp

  implicit none

  private
  public :: read_rotor_input,    init_rotor_source,  write_source_grid
  public :: source2node_assoc,   rotor_source_rhs,   rotor_source_lhs
  public :: advance_rotor_blade, get_rotor_id_from_ref_frame
  public :: rotor_type

  private :: init_rotor_disk,     init_rotor_blade,   set_blade_positions
  private :: init_source_force,   read_user_source1,  read_user_source2
  private :: rotate_flap2xyz,     blade_element_rhs,  blade_element_lhs

  integer, public             :: nrotor = 0             ! # of rotors
  integer, private, parameter :: iunit  = 15            ! File unit
  integer, public             :: irotwrt = 1            ! #iter rot soln write

  real(dp), private           :: force_ref              ! rhoref*Vref^2*Lref^2
  real(dp), private           :: moment_ref             ! rhoref*Vref^2*Lref^3
  real(dp), public            :: vinf_ratio = 0.0       ! Rotor Advance Ratio
  real(dp), public            :: vinf_input_ratio = 1.0 ! freestream/xmach ratio

  logical, public             :: rotor_flag = .false.   ! True if --rotor is set
  logical, public             :: alternate_freestream = .false.

  logical, public             :: use_kdtree = .false.   ! Use kd-tree for
                                                        ! actuator search
  logical, public             :: have_rotor_input = .false. ! flag rotor.input
                                                            ! file read or not
  type rotor_type

!   Basic parameters
    integer      :: rottype, loadtype       ! Type of rotor conditions to apply
    integer      :: nradial, nnormal        ! # radial, normal sources
    integer      :: swirl                   ! Adds inplane force
    real(dp)     :: tipfac                  ! Tip weighting for source distrib.
    real(dp)     :: x0, y0, z0              ! Rotor center of rotation
    real(dp)     :: vt_ratio                ! Rotor tip velocity ratio
    real(dp)     :: omega                   ! Rotor rotational velocity
    real(dp)     :: ct, cq                  ! Rotor thrust, torque coeff.
    real(dp)     :: psi0                    ! Reference azimuth angle
    real(dp)     :: psi1                    ! Current azimuth angle
    real(dp), dimension(3,3)    :: rot2xyz  ! Rotation Tensor: RotFrame -> X,Y,Z
    real(dp)     :: alpha0                  ! Tilt of the hub axis wrt inertial
    integer      :: dirrot                  ! rotation direction 1 = clockwise
                                            ! 0 = counter clockwise

!   Blade parameters
    integer      :: nblade                  ! Number of rotor blades
    real(dp)     :: rtip, rroot             ! Rotor tip, root radius
    real(dp)     :: chord                   ! Blade chord
    real(dp)     :: rfh, rlh                ! Rotor flap, lag hinge location
    real(dp)     :: rph                     ! Rotor pitch hinge location
    real(dp)     :: cla, a0                 ! Cl = cla*(a-a0)
    real(dp)     :: cd0, cd1, cd2           ! Cd = cd0 + cd1*a + cd2*a^2
    real(dp)     :: clmax, clmin            ! Cl limits
    real(dp)     :: cdmax, cdmin            ! Cd limits

!   Control parameters
    real(dp)     :: theta0, thetatw, theta1s, theta1c      ! Pitch angle

!   User-provided detailed twist distribution
    logical                         :: have_twist_distribution
    real(dp), dimension(:), pointer :: twist_distr

!   Flap angle and lead-lag angle parameters
    integer                              :: nbeta          ! # beta Harmonics
    real(dp), dimension(7)               :: beta           ! Flap angle
    integer                              :: ndelta         ! # delta Harmonics
    real(dp), dimension(7)               :: delta          ! Lead-lag angle

!   Source grid
    real(dp), dimension(:),     pointer    :: radius
    real(dp), dimension(:),     pointer    :: delrad
    real(dp), dimension(:),     pointer    :: area
    real(dp), dimension(:),     pointer    :: psi
    real(dp), dimension(:,:,:), pointer    :: x_sg, y_sg, z_sg
    real(dp), dimension(:,:,:), pointer    :: x_s,  y_s,  z_s
    real(dp), dimension(:,:,:), pointer    :: fx, fy, fz
    real(dp), dimension(:,:,:), pointer    :: alpha_eff
    real(dp), dimension(:,:,:), pointer    :: cl_local, cd_local

    integer,     dimension(:,:,:), pointer :: s2n

!   Reference blade grid
    real(dp), dimension(:,:),   pointer    :: x_s_ref, y_s_ref, z_s_ref

  end type rotor_type

  type(rotor_type), dimension(:), allocatable, public :: rotor

contains

!========= READ_ROTOR_INPUT ==================================================80
!
!  Reads rotor.input file and allocates rotor type variable.
!
!=============================================================================80

  subroutine read_rotor_input(nml_path, eqn_set)

    use lmpi,           only : lmpi_master, lmpi_bcast
    use info_depr,      only : xmach
    use solution_types, only : compressible

    character(len=*), intent(in) :: nml_path

    integer,          intent(in) :: eqn_set

    integer :: i, iostat

    real(dp) :: deg2rad
    real(dp) :: p1, p2, p3

    integer,  dimension(6) :: ipack
    real(dp), dimension(6) :: rpack

    real(dp), parameter :: my_0   =   0.0_dp
    real(dp), parameter :: my_1   =   1.0_dp
    real(dp), parameter :: my_180 = 180.0_dp

    real(dp) :: pi

    character(len=80) :: input_line

    continue

!   Set parameters
    iostat = 0
    pi = acos(-my_1)
    deg2rad = pi/my_180
    ipack = 0
    rpack = my_0

!   Open rotor.input file

    master_open_input_file : if ( lmpi_master ) then
      open(unit=iunit, file=nml_path, status='old', iostat=iostat)
      if (iostat == 0) then
        have_rotor_input = .true.
      end if
    endif master_open_input_file

    call lmpi_bcast(have_rotor_input)

!   if a rotor.input file was not found, just set up a dummy rotor of
!   size 1 (1 rotor) for now, and return

    if (.not. have_rotor_input) then
      call set_dummy_rotor()
      return
    end if

!   Read number of rotors
    if ( lmpi_master ) then
      rewind(iunit)
      !  - line 0 header
      read(iunit,'(a80)') input_line
      write(*,'(1x,a80)') input_line
      backspace(iunit)
      read(iunit,*)

      !  - line 0 data
      read(iunit,'(a80)') input_line
      write(*,'(1x,a80)') input_line
      backspace(iunit)
      read(iunit,*) nrotor, vinf_ratio, irotwrt, force_ref, moment_ref
    end if
    call lmpi_bcast(nrotor)
    call lmpi_bcast(vinf_ratio)
    call lmpi_bcast(irotwrt)
    call lmpi_bcast(force_ref)
    call lmpi_bcast(moment_ref)

    if (nrotor == 0) then
      write(*,'("WARNING: nrotor = 0 in rotor.input")')
      write(*,'("         Proceeding without a rotor influence...")')
      rotor_flag = .FALSE.
      close(iunit)
      return
    end if

!   Allocate rotor type
    allocate( rotor(nrotor) )

!   Read inputs for all rotors
    read_rotor_info: do i = 1,nrotor
      if ( lmpi_master ) then

        write(*,'(/,"read_rotor_info: Start reading data for rotor ",i2,/)') i

        !  - line 1 separator
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) ! i-th rotor description

        !  - line 1 header - Basic parameters
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 1 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) ipack(1),ipack(2),ipack(3),ipack(4),rpack(1)
      end if
      call lmpi_bcast(ipack)
      call lmpi_bcast(rpack)
      rotor(i)%rottype  = ipack(1)
      rotor(i)%loadtype = ipack(2)
      rotor(i)%nradial  = ipack(3)
      rotor(i)%nnormal  = ipack(4)
      rotor(i)%tipfac   = rpack(1)

      if ( lmpi_master ) then

        !  - line 2 header - Rotor center of rotation, Euler angles
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 2 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) rpack(1),rpack(2),rpack(3),rpack(4),rpack(5),rpack(6)
      end if
      call lmpi_bcast(rpack)
      rotor(i)%x0 = rpack(1)
      rotor(i)%y0 = rpack(2)
      rotor(i)%z0 = rpack(3)

!     Set up the rotation matrix: Rotor Ref Frame -> X,Y,Z Ref Frame
      p1 = rpack(4)*deg2rad
      p2 = rpack(5)*deg2rad
      p3 = rpack(6)*deg2rad
      rotor(i)%alpha0 = p2
      rotor(i)%rot2xyz(1,1) =  cos(p2)*cos(p3)
      rotor(i)%rot2xyz(1,2) = -cos(p2)*sin(p3)
      rotor(i)%rot2xyz(1,3) =  sin(p2)
      rotor(i)%rot2xyz(2,1) =  sin(p1)*sin(p2)*cos(p3) + cos(p1)*sin(p3)
      rotor(i)%rot2xyz(2,2) = -sin(p1)*sin(p2)*sin(p3) + cos(p1)*cos(p3)
      rotor(i)%rot2xyz(2,3) = -sin(p1)*cos(p2)
      rotor(i)%rot2xyz(3,1) = -cos(p1)*sin(p2)*cos(p3) + sin(p1)*sin(p3)
      rotor(i)%rot2xyz(3,2) =  cos(p1)*sin(p2)*sin(p3) + sin(p1)*cos(p3)
      rotor(i)%rot2xyz(3,3) =  cos(p1)*cos(p2)

      if ( lmpi_master ) then

        !  - line 3 header - Rotational Velocity, Thrust Coff, Power Coff, psi0
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 3 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
                      ! PitchHinge      DirRot
        read(iunit,*) rpack(1), rpack(2), rpack(3), rpack(4), rpack(5), ipack(1)
      end if
      call lmpi_bcast(ipack)
      call lmpi_bcast(rpack)
      rotor(i)%vt_ratio = rpack(1)
      rotor(i)%ct       = rpack(2)
      rotor(i)%cq       = rpack(3)
      rotor(i)%psi0     = rpack(4)*deg2rad
      rotor(i)%rph      = rpack(5)
      rotor(i)%dirrot   = ipack(1)

      if ( lmpi_master ) then

        !  - line 4 header - Blade Parameters
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 4 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) ipack(1),rpack(1),rpack(2),rpack(3),rpack(4),rpack(5)
      end if
      call lmpi_bcast(ipack)
      call lmpi_bcast(rpack)
      rotor(i)%nblade = ipack(1)
      rotor(i)%rtip   = rpack(1)
      rotor(i)%rroot  = rpack(2)
      rotor(i)%chord  = rpack(3)
      rotor(i)%rfh    = rpack(4)
      rotor(i)%rlh    = rpack(5)

      if ( eqn_set == compressible ) then
        rotor(i)%omega = rotor(i)%vt_ratio / rotor(i)%rtip * xmach
      else
        rotor(i)%omega = rotor(i)%vt_ratio / rotor(i)%rtip
      end if

      if ( lmpi_master ) then

        !  - line 5 header - Lift & Drag coefficients
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 5 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) rpack(1), rpack(2), rpack(3), rpack(4), rpack(5)
      end if
      call lmpi_bcast(rpack)
      rotor(i)%cla = rpack(1)
      rotor(i)%a0  = rpack(2)*deg2rad
      rotor(i)%cd0 = rpack(3)
      rotor(i)%cd1 = rpack(4)
      rotor(i)%cd2 = rpack(5)

      if ( lmpi_master ) then

        !  - line 6 header - Cl, Cd limits
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 6 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) rpack(1), rpack(2), rpack(3), rpack(4), ipack(1)
      end if
      call lmpi_bcast(rpack)
      call lmpi_bcast(ipack)
      rotor(i)%clmax = rpack(1)
      rotor(i)%clmin = rpack(2)
      rotor(i)%cdmax = rpack(3)
      rotor(i)%cdmin = rpack(4)
      rotor(i)%swirl = ipack(1)

      if ( lmpi_master ) then

        !  - line 7 header - Pitch Setting
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 7 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) rpack(1), rpack(2), rpack(3), rpack(4)
      end if
      call lmpi_bcast(rpack)
      rotor(i)%theta0  = rpack(1)*deg2rad
      rotor(i)%thetatw = rpack(2)*deg2rad / rotor(i)%rtip
      rotor(i)%theta1s = rpack(3)*deg2rad
      rotor(i)%theta1c = rpack(4)*deg2rad

      ! Twist < -360 indicates that we're going to read twist from a file
      rotor(i)%have_twist_distribution = rpack(2) < -360._dp

      if ( lmpi_master ) then

        !  - line 8 header - Flap Parameters
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 8 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) ipack(1), rpack(1), rpack(2), rpack(3)
      end if
      call lmpi_bcast(ipack)
      call lmpi_bcast(rpack)
      rotor(i)%nbeta   = ipack(1)
      rotor(i)%beta(1) = rpack(1)*deg2rad
      rotor(i)%beta(2) = rpack(2)*deg2rad
      rotor(i)%beta(3) = rpack(3)*deg2rad

      if ( lmpi_master ) then

        !  - line 9 header - 2nd and 3rd Flap Harmonics
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 9 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) rpack(1), rpack(2), rpack(3), rpack(4)
      end if
      call lmpi_bcast(rpack)
      rotor(i)%beta(4)  = rpack(1)*deg2rad
      rotor(i)%beta(5)  = rpack(2)*deg2rad
      rotor(i)%beta(6)  = rpack(3)*deg2rad
      rotor(i)%beta(7)  = rpack(4)*deg2rad

      if ( lmpi_master ) then

        !  - line 10 header - Lead-lag parameters
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 10 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) ipack(1), rpack(1), rpack(2), rpack(3)
      end if
      call lmpi_bcast(ipack)
      call lmpi_bcast(rpack)
      rotor(i)%ndelta   = ipack(1)
      rotor(i)%delta(1) = rpack(1)*deg2rad
      rotor(i)%delta(2) = rpack(2)*deg2rad
      rotor(i)%delta(3) = rpack(3)*deg2rad

      if ( lmpi_master ) then

        !  - line 11 header - 2nd and 3rd lead-lag harmonics
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*)

        !  - line 11 data
        read(iunit,'(a80)') input_line
        write(*,'(1x,a80)') input_line
        backspace(iunit)
        read(iunit,*) rpack(1), rpack(2), rpack(3), rpack(4)
      end if
      call lmpi_bcast(rpack)
      rotor(i)%delta(4)  = rpack(1)*deg2rad
      rotor(i)%delta(5)  = rpack(2)*deg2rad
      rotor(i)%delta(6)  = rpack(3)*deg2rad
      rotor(i)%delta(7)  = rpack(4)*deg2rad

      if ( lmpi_master )                                                       &
        write(*,'(/,"read_rotor_info: Finished reading data for rotor ",i2,/)')i

    end do read_rotor_info

    close(iunit)

  end subroutine read_rotor_input

!================== READ_TWIST_DISTRIBUTION ==================================80
!
! Read a twist distribution for rotor i from a file called twist_rotor<i>.dat.
! File looks like:
!   ntwist
!   r(1)       twist(1)
!   ...
!   r(ntwist)  twist(ntwist)
! Radius is in grid units, twist is in degrees.
!
! Should be called by master only
!
!=============================================================================80

  subroutine read_twist_distribution(i)

    use file_utils,            only: available_unit
    use lmpi,                  only: lmpi_master, lmpi_die
    use string_utils,          only: sprintf, MAX_STR_LEN
    use allocations,           only: my_alloc_ptr
    use interpolate_utilities, only: interp1d
    use fun3d_constants,       only: rad_from_deg

    integer, intent(in) :: i

    integer                         :: iu, istat, ntwist, j
    character(len=MAX_STR_LEN)      :: fname
    real(dp), dimension(:), pointer :: file_r, file_twist

  continue

!   Just in case...
    if (.not. lmpi_master) then
      write(*,*) 'Twist distribution should only be read by master'
      call lmpi_die
    end if

    fname = trim(sprintf('twist_rotor%i0', i))//'.dat'

!   Open the file and read the number of data points
    iu = available_unit()
    open(iu, file=trim(fname), form='formatted', status='old', iostat=istat)
    if (istat /= 0) then
      write(*,*) 'Error: ', trim(fname), ' does not exist.'
      write(*,*) 'You requested that it be read by setting ThetaTwist < -360'
      write(*,*) 'in rotor.input ... Quitting.'
      call lmpi_die
    end if
    read(iu,*) ntwist

!   Allocate space for the file data
    call my_alloc_ptr(file_r, ntwist)
    call my_alloc_ptr(file_twist, ntwist)

!   Read the points from the file and convert to radians
    do j = 1,ntwist
      read(iu,*) file_r(j), file_twist(j)
    end do
    file_twist = file_twist * rad_from_deg

    close(iu)

!   Interpolate the points in the file to the actual radial stations
    if (.not. associated(rotor(i)%twist_distr)) then
      write(*,*) 'Should have allocated twist_distr before now'
      call lmpi_die
    end if
    call interp1d(ntwist, file_r, file_twist,                                  &
                  rotor(i)%nradial, rotor(i)%radius, rotor(i)%twist_distr)

!   Clean up
    deallocate(file_r)
    deallocate(file_twist)

  end subroutine read_twist_distribution

!========= INIT_ROTOR_SOURCE =================================================80
!
!   Driver routine for initializing the rotor disk or blade source terms.
!
!=============================================================================80

  subroutine init_rotor_source( eqn_set, flow_dir )

    use allocations,    only: my_alloc_ptr
    use lmpi,           only: lmpi_master, lmpi_bcast, lmpi_die, &
                              lmpi_conditional_stop
    use info_depr,      only: xmach
    use solution_types, only: compressible

    integer, intent(in) :: eqn_set

    character(len=*), intent(in) :: flow_dir

    integer                                      :: i, error_code
    integer                                      :: na, nz, nr, nn

    continue

    error_code = 0

    rotor_loop: do i = 1, nrotor

!     Get # of radial and normal sources if using a user specified source disk
      if ( rotor(i)%loadtype == 4 ) then
        if ( rotor(i)%rottype == 1 ) then
          if ( lmpi_master ) then
            call read_user_source1( i, flow_dir )
          end if
          call lmpi_bcast( rotor(i)%nradial )
          call lmpi_bcast( rotor(i)%nnormal )
        else
          write(*,'("ERROR: Invalid rotor & load type combination.")')
          call lmpi_die
        end if
      end if

!     Set the # of radial sources and # of normal sources
      nr = rotor(i)%nradial
      nn = rotor(i)%nnormal

!     Set the # of azimuth positions and # of source grid zones
      select case ( rotor(i)%rottype )
        case (1)     ! Source disk
          na = rotor(i)%nnormal
          nz = 1
        case (2)     ! Source blades
          na = rotor(i)%nblade
          nz = rotor(i)%nblade
        case default
          write(*,'("ERROR: Invalid rotor type.")')
          call lmpi_die
      end select

!     Allocate rotor_type arrays
      call my_alloc_ptr( rotor(i)%x_s, nz, nr, nn )
      call my_alloc_ptr( rotor(i)%y_s, nz, nr, nn )
      call my_alloc_ptr( rotor(i)%z_s, nz, nr, nn )
      call my_alloc_ptr( rotor(i)%fx,  nz, nr, nn )
      call my_alloc_ptr( rotor(i)%fy,  nz, nr, nn )
      call my_alloc_ptr( rotor(i)%fz,  nz, nr, nn )
      call my_alloc_ptr( rotor(i)%s2n, nz, nr, nn )

      select case ( rotor(i)%loadtype )
        case (1:3,5,6)     ! Source grid defined by rotor.input file
          call my_alloc_ptr( rotor(i)%radius, nr )
          call my_alloc_ptr( rotor(i)%delrad, nr )
          call my_alloc_ptr( rotor(i)%area,   nr )
          call my_alloc_ptr( rotor(i)%psi,    na )
          if (rotor(i)%have_twist_distribution) then
            call my_alloc_ptr( rotor(i)%twist_distr, nr )
          end if
!         Source grid only needs to be on one processor
          if ( lmpi_master ) then
            call my_alloc_ptr( rotor(i)%x_sg, nz, nr+1, nn+1 )
            call my_alloc_ptr( rotor(i)%y_sg, nz, nr+1, nn+1 )
            call my_alloc_ptr( rotor(i)%z_sg, nz, nr+1, nn+1 )
          end if
!         Only actuator blades need a reference blade
          if ( rotor(i)%rottype == 2 ) then
            call my_alloc_ptr( rotor(i)%x_s_ref, nr, nn )
            call my_alloc_ptr( rotor(i)%y_s_ref, nr, nn )
            call my_alloc_ptr( rotor(i)%z_s_ref, nr, nn )
          end if
!         Only blade element uses effective angle of attack
          if ( rotor(i)%loadtype == 3 ) then
            call my_alloc_ptr( rotor(i)%alpha_eff, nz, nr, nn)
            call my_alloc_ptr( rotor(i)%cl_local, nz, nr, nn)
            call my_alloc_ptr( rotor(i)%cd_local, nz, nr, nn)
          end if
        case (4)       ! User specified
          continue
        case default
          write(*,'("ERROR: Invalid loading type.")')
          call lmpi_die
      end select

!     Set up source coordinates and forces
      if ( lmpi_master ) then
        select case ( rotor(i)%loadtype )
          case (1:3,5,6)
            select case ( rotor(i)%rottype )
              case (1)
                call init_rotor_disk( i )
              case (2)
                call init_rotor_blade( i )
              case default
                write(*,'("ERROR: Invalid rotor type.")')
                call lmpi_die
            end select
            call init_source_force(eqn_set, i, error_code, flow_dir )
          case (4)     ! User specified source grid
            call read_user_source2( i, flow_dir )
          case default
            write(*,'("ERROR: Invalid loading type.")')
            error_code = 1
        end select
      end if
      call lmpi_conditional_stop(error_code,'ERROR: in rotors init')

!     Adjust from tip velocity ratio to nondimensional rotational velocity
!     Needs to come after init_source_force, which assumes tip velocity ratio
      if ( eqn_set == compressible ) then
        rotor(i)%omega = rotor(i)%vt_ratio / rotor(i)%rtip * xmach
      else
        rotor(i)%omega = rotor(i)%vt_ratio / rotor(i)%rtip
      end if

!     Broadcast to all processors
      call lmpi_bcast( rotor(i)%x_s )
      call lmpi_bcast( rotor(i)%y_s )
      call lmpi_bcast( rotor(i)%z_s )
      call lmpi_bcast( rotor(i)%fx  )
      call lmpi_bcast( rotor(i)%fy  )
      call lmpi_bcast( rotor(i)%fz  )

      select case ( rotor(i)%loadtype )
        case (1:3,5,6)
          call lmpi_bcast( rotor(i)%radius )
          call lmpi_bcast( rotor(i)%delrad )
          call lmpi_bcast( rotor(i)%area   )
          call lmpi_bcast( rotor(i)%psi    )
          if (rotor(i)%have_twist_distribution) then
            call lmpi_bcast( rotor(i)%twist_distr )
          end if
        case (4)     ! User specified source grid
          continue
        case default
          write(*,'("ERROR: Invalid loading type.")')
          call lmpi_die
      end select

      if ( rotor(i)%rottype == 2 ) then
        call lmpi_bcast( rotor(i)%x_s_ref )
        call lmpi_bcast( rotor(i)%y_s_ref )
        call lmpi_bcast( rotor(i)%z_s_ref )
      end if

    end do rotor_loop

  end subroutine init_rotor_source

!========= INIT_ROTOR_DISK ===================================================80
!
!   Initializes the 2D structured rotor source disk grid.
!
!=============================================================================80

  subroutine init_rotor_disk(i)

    integer,     intent(in)                :: i

    integer                                :: j, k
    integer                                :: ibeta
    real(dp)                               :: dr, dpsi
    real(dp)                               :: b, r_proj
    real(dp)                               :: x1, y1, z1, x2, y2, z2
    real(dp)                               :: v1x, v1y, v1z
    real(dp)                               :: v2x, v2y, v2z
    real(dp)                               :: area1, area2, tot_area

    real(dp), dimension(:), allocatable    :: rad_tmp
    real(dp), dimension(:), allocatable    :: psi_tmp

    real(dp), parameter                    :: my_0    = 0.0_dp
    real(dp), parameter                    :: my_1    = 1.0_dp
    real(dp), parameter                    :: my_2    = 2.0_dp
    real(dp), parameter                    :: my_3    = 3.0_dp
    real(dp), parameter                    :: my_half = 0.5_dp
    real(dp)                               :: my_pi

    logical,     parameter                 :: debug_routine = .TRUE.

  continue

    my_pi = acos(-my_1)

    allocate( rad_tmp(rotor(i)%nradial+1) )
    allocate( psi_tmp(rotor(i)%nnormal+1) )

!   Compute radial distribution for the source grid
    rad_tmp(1) = rotor(i)%rroot
    dr = (rotor(i)%rtip - rotor(i)%rroot) / (my_1 * rotor(i)%nradial)
    do j = 2, rotor(i)%nradial
      rad_tmp(j) = rad_tmp(j-1) + dr
    end do
    rad_tmp(rotor(i)%nradial+1) = rotor(i)%rtip

    scale_radially: if (rotor(i)%tipfac > my_0) then
      do j = 2, rotor(i)%nradial
        rad_tmp(j) = rotor(i)%tipfac / (rotor(i)%rtip - rotor(i)%rroot) *      &
                     (my_2*rad_tmp(j) - rotor(i)%rtip - rotor(i)%rroot)
        rad_tmp(j) = tanh( rad_tmp(j) )
        rad_tmp(j) = my_half *(rad_tmp(j) * (rotor(i)%rtip - rotor(i)%rroot)/  &
                     tanh(rotor(i)%tipfac) + rotor(i)%rtip + rotor(i)%rroot)
      end do
    end if scale_radially

!   Compute azimuthal (normal) distribution for source grid, even spacing
    psi_tmp(1) = my_0
    dpsi = my_2*my_pi/(my_1*rotor(i)%nnormal)
    do k = 2,rotor(i)%nnormal
      psi_tmp(k) = psi_tmp(k-1) + dpsi
    end do
    psi_tmp(rotor(i)%nnormal+1) = my_2*my_pi

!   Add the flapping contribution
    do k = 1,rotor(i)%nnormal
      b = rotor(i)%beta(1)
      do ibeta = 1,rotor(i)%nbeta
        b = b + rotor(i)%beta(2*ibeta  ) * sin(ibeta*psi_tmp(k)) +             &
                rotor(i)%beta(2*ibeta+1) * cos(ibeta*psi_tmp(k))
      end do
      do j = 1,rotor(i)%nradial+1
        if ( rad_tmp(j) > rotor(i)%rfh ) then
          rotor(i)%z_sg(1,j,k) = (rad_tmp(j) - rotor(i)%rfh)*sin(b)
          r_proj = (rad_tmp(j) - rotor(i)%rfh)*cos(b) + rotor(i)%rfh
        else
          rotor(i)%z_sg(1,j,k) = my_0
          r_proj = rad_tmp(j)
        end if
        rotor(i)%x_sg(1,j,k) = r_proj*cos(psi_tmp(k))
        rotor(i)%y_sg(1,j,k) = r_proj*sin(psi_tmp(k))
!       If clockwise rotation, only need to change y coordinate
        if (rotor(i)%vt_ratio < my_0)                                          &
            rotor(i)%y_sg(1,j,k) = -rotor(i)%y_sg(1,j,k)
      end do
    end do
    do j = 1,rotor(i)%nradial+1
      rotor(i)%x_sg(1,j,rotor(i)%nnormal+1) = rotor(i)%x_sg(1,j,1)
      rotor(i)%y_sg(1,j,rotor(i)%nnormal+1) = rotor(i)%y_sg(1,j,1)
      rotor(i)%z_sg(1,j,rotor(i)%nnormal+1) = rotor(i)%z_sg(1,j,1)
    end do

!   Rotate the source gird into the xyz frame
    do j = 1,rotor(i)%nradial+1
      do k = 1,rotor(i)%nnormal+1
        x1 = rotor(i)%x_sg(1,j,k)
        y1 = rotor(i)%y_sg(1,j,k)
        z1 = rotor(i)%z_sg(1,j,k)
        rotor(i)%x_sg(1,j,k) = rotor(i)%rot2xyz(1,1) * x1 +                    &
                               rotor(i)%rot2xyz(1,2) * y1 +                    &
                               rotor(i)%rot2xyz(1,3) * z1 + rotor(i)%x0
        rotor(i)%y_sg(1,j,k) = rotor(i)%rot2xyz(2,1) * x1 +                    &
                               rotor(i)%rot2xyz(2,2) * y1 +                    &
                               rotor(i)%rot2xyz(2,3) * z1 + rotor(i)%y0
        rotor(i)%z_sg(1,j,k) = rotor(i)%rot2xyz(3,1) * x1 +                    &
                               rotor(i)%rot2xyz(3,2) * y1 +                    &
                               rotor(i)%rot2xyz(3,3) * z1 + rotor(i)%z0
      end do
    end do

!   Compute source coordinates and corresponding area
!   Point sources are placed at the centroid of each source grid cell
    tot_area = my_0
    compute_area_loop: do j = 1,rotor(i)%nradial
      do k = 1,rotor(i)%nnormal
        rotor(i)%psi(k)    = my_half * (psi_tmp(k+1) + psi_tmp(k))
        rotor(i)%radius(j) = my_half * (rad_tmp(j+1) + rad_tmp(j))
        rotor(i)%delrad(j) = rad_tmp(j+1) - rad_tmp(j)

!       Set the first set of edge vectors
        v1x = rotor(i)%x_sg(1,j+1,k) - rotor(i)%x_sg(1,j,k)
        v1y = rotor(i)%y_sg(1,j+1,k) - rotor(i)%y_sg(1,j,k)
        v1z = rotor(i)%z_sg(1,j+1,k) - rotor(i)%z_sg(1,j,k)

        v2x = rotor(i)%x_sg(1,j,k+1) - rotor(i)%x_sg(1,j,k)
        v2y = rotor(i)%y_sg(1,j,k+1) - rotor(i)%y_sg(1,j,k)
        v2z = rotor(i)%z_sg(1,j,k+1) - rotor(i)%z_sg(1,j,k)

!       Compute the first triangle centroid and area
        x1 = (rotor(i)%x_sg(1,j,k) + rotor(i)%x_sg(1,j+1,k) +                  &
              rotor(i)%x_sg(1,j,k+1)) / my_3
        y1 = (rotor(i)%y_sg(1,j,k) + rotor(i)%y_sg(1,j+1,k) +                  &
              rotor(i)%y_sg(1,j,k+1)) / my_3
        z1 = (rotor(i)%z_sg(1,j,k) + rotor(i)%z_sg(1,j+1,k) +                  &
              rotor(i)%z_sg(1,j,k+1)) / my_3
        area1 = sqrt((v1y*v2z - v2y*v1z)**my_2 + (v1z*v2x - v2z*v1x)**my_2 +   &
                     (v1x*v2y - v2x*v1y)**my_2) / my_2

!       Set the second set of edge vectors
        v1x = rotor(i)%x_sg(1,j+1,k) - rotor(i)%x_sg(1,j+1,k+1)
        v1y = rotor(i)%y_sg(1,j+1,k) - rotor(i)%y_sg(1,j+1,k+1)
        v1z = rotor(i)%z_sg(1,j+1,k) - rotor(i)%z_sg(1,j+1,k+1)

        v2x = rotor(i)%x_sg(1,j,k+1) - rotor(i)%x_sg(1,j+1,k+1)
        v2y = rotor(i)%y_sg(1,j,k+1) - rotor(i)%y_sg(1,j+1,k+1)
        v2z = rotor(i)%z_sg(1,j,k+1) - rotor(i)%z_sg(1,j+1,k+1)

!       Compute the second triangle centroid and area
        x2 = (rotor(i)%x_sg(1,j+1,k) + rotor(i)%x_sg(1,j,k+1) +                &
              rotor(i)%x_sg(1,j+1,k+1)) / my_3
        y2 = (rotor(i)%y_sg(1,j+1,k) + rotor(i)%y_sg(1,j,k+1) +                &
              rotor(i)%y_sg(1,j+1,k+1)) / my_3
        z2 = (rotor(i)%z_sg(1,j+1,k) + rotor(i)%z_sg(1,j,k+1) +                &
              rotor(i)%z_sg(1,j+1,k+1)) / my_3
        area2 = sqrt((v1y*v2z - v2y*v1z)**my_2 + (v1z*v2x - v2z*v1x)**my_2 +   &
                     (v1x*v2y - v2x*v1y)**my_2) / my_2

!       Compute source grid cell area
        rotor(i)%area(j) = area1 + area2
        tot_area = tot_area + area1 + area2

!       Compute source locations
        rotor(i)%x_s(1,j,k) = (x1*area1 + x2*area2) / rotor(i)%area(j)
        rotor(i)%y_s(1,j,k) = (y1*area1 + y2*area2) / rotor(i)%area(j)
        rotor(i)%z_s(1,j,k) = (z1*area1 + z2*area2) / rotor(i)%area(j)

      end do
    end do compute_area_loop

    if (debug_routine) then
      write(*,'("init_disk_grid:: Rotor Source Grid Check:")')
      write(*,'("-- Source grid area: ",es16.8)') tot_area
      tot_area = my_pi * (rotor(i)%rtip**my_2 - rotor(i)%rroot**my_2)
      write(*,'("-- Exact area:       ",es16.8)') tot_area
    end if

  end subroutine init_rotor_disk

!========= INIT_ROTOR_BLADE ==================================================80
!
!   Initializes the 2D structured rotor source blade grid(s).
!
!=============================================================================80

  subroutine init_rotor_blade(i)

    use lmpi,           only : lmpi_conditional_stop

    integer,     intent(in) :: i

    integer                 :: j, k, iblade
    real(dp)                :: del_psi, dradius, dchord

    real(dp), parameter     :: my_0 = 0.0_dp
    real(dp), parameter     :: my_1 = 1.0_dp
    real(dp), parameter     :: my_2 = 2.0_dp
    real(dp), parameter     :: my_4 = 4.0_dp
    real(dp)                :: my_pi, my_2pi

    logical,     parameter  :: debug_routine = .TRUE.

  continue

    if (debug_routine) write(*,'("Entering INIT_ROTOR_BLADE")')

    if ( rotor(i)%loadtype == 5 .or. rotor(i)%loadtype == 6 )   &
      call lmpi_conditional_stop(1,                             &
      "init_rotor_blade Load Type (loadtype == 5,6) not implemented" )

    my_pi  = acos(-my_1)
    my_2pi = my_2*my_pi

!   Initialize blade azimuth angles
    rotor(i)%psi(1) = rotor(i)%psi0
    if ( rotor(i)%nblade > 1 ) then
      del_psi = my_2pi / (my_1 * rotor(i)%nblade)
      do iblade = 2,rotor(i)%nblade
        rotor(i)%psi(iblade) = rotor(i)%psi(iblade-1) + del_psi
        if ( rotor(i)%psi(iblade) >= my_2pi )                                  &
          rotor(i)%psi(iblade) = rotor(i)%psi(iblade) - my_2pi
      end do
    end if

    if (debug_routine) then
      write(*,'("# of rotor blades ",i4)') rotor(i)%nblade
      do iblade = 1,rotor(i)%nblade
        write(*,'("Blade ",i4," psi = ",es16.8)') iblade, rotor(i)%psi(iblade)
      end do
    end if

!   Set up radius, delrad, area
    dradius = (rotor(i)%rtip - rotor(i)%rroot) / (my_1*rotor(i)%nradial)
    dchord  = rotor(i)%chord / (my_1*rotor(i)%nnormal)
    rotor(i)%radius(1) = rotor(i)%rroot + dradius/my_2
    rotor(i)%delrad(1) = dradius
    rotor(i)%area(1)   = dradius * dchord
    do j = 2,rotor(i)%nradial
      rotor(i)%radius(j) = rotor(i)%radius(j-1) + dradius
      rotor(i)%delrad(j) = rotor(i)%delrad(j-1)
      rotor(i)%area(j)   = rotor(i)%area(j-1)
    end do

!   Compute reference source locations
    do j = 1,rotor(i)%nradial
      do k = 1,rotor(i)%nnormal
        rotor(i)%x_s_ref(j,k) = rotor(i)%radius(j) - rotor(i)%rroot
        rotor(i)%y_s_ref(j,k) = rotor(i)%chord/my_4 - dchord*(2*k-1)/my_2
        if (rotor(i)%vt_ratio<my_0)                                            &
            rotor(i)%y_s_ref(j,k) = -rotor(i)%y_s_ref(j,k)
        rotor(i)%z_s_ref(j,k) = my_0
      end do
    end do

!   Now that we've got rotor(i)%radius, read optional user-supplied
!   twist distribution
    if (rotor(i)%have_twist_distribution) call read_twist_distribution(i)

    call set_blade_positions(i)

  end subroutine init_rotor_blade

!========= INIT_SOURCE_FORCE =================================================80
!
!   Initializes the rotor source strengths
!   Assumes omega is the tip velocity ratio
!   Called from init_rotor_source
!
!=============================================================================80

  subroutine init_source_force( eqn_set, i, error_code, flow_dir )

    use info_depr,      only : xmach
    use solution_types, only : compressible

    use string_utils,   only : sprintf
    use file_utils,     only : available_unit

    integer, intent(in)  :: eqn_set
    integer, intent(in)  :: i
    integer, intent(out) :: error_code
    character(*), intent(in) :: flow_dir

    integer                 :: h, j, k, iazimuth, izone
    integer                 :: nazimuth, nzone

    real(dp)                :: thrust, area
    real(dp)                :: azimuth, flap, lag
    real(dp)                :: dp_t, dp_t_tip

    real(dp)                :: f1, f2, f3
    real(dp)                :: sum_beta

    integer                 :: istation, nstation
    real(dp), dimension(:), allocatable :: station_radius
    real(dp), dimension(:), allocatable :: station_thrust, station_torque
    integer              :: iunit,iostat
    character(len=80)    :: inp_file
    real(dp)             :: dstation, s, ct, cq, torque

    real(dp)             :: r_h, rstar, normalization

    real(dp), parameter     :: my_0   = 0.0_dp
    real(dp), parameter     :: my_1   = 1.0_dp
    real(dp), parameter     :: my_2   = 2.0_dp
    real(dp), parameter     :: my_3   = 3.0_dp
    real(dp)                :: my_pi

    continue

    error_code = 0

    my_pi = acos(-my_1)

    read_prop_loading : if (rotor(i)%loadtype == 5) then
      inp_file = trim(sprintf('propeller_properties%i0',i)) // '.dat'
      inp_file = trim(flow_dir) // trim(inp_file)
      iunit = available_unit()
      open(unit=iunit, file=inp_file, form='formatted', &
        status='old', iostat=iostat)
      if (iostat /= 0) then
        write(*,*) "ERROR: file not found ",trim(inp_file)
        error_code = 1
        return
      end if
      read(iunit,*) nstation
      if (nstation < 2) then
        write(*,*) "ERROR: at least two stations must be provided in ",&
          trim(inp_file)
        error_code = 1
        return
      end if
      write(*,*) trim(inp_file), " has nstation=",nstation
      allocate(station_radius(nstation))
      allocate(station_thrust(nstation))
      allocate(station_torque(nstation))
      read_each_station : do istation = 1, nstation
        read(iunit,*) station_radius(istation), &
                      station_thrust(istation), &
                      station_torque(istation)
        write(*,"(i3,3(1x,f16.8))") istation, station_radius(istation), &
                                              station_thrust(istation), &
                                              station_torque(istation)
      end do read_each_station
      close(iunit)
    end if read_prop_loading

!   Compute rotor thrust and power
    if ( eqn_set == compressible ) then
      thrust= my_pi * rotor(i)%ct * (abs(rotor(i)%vt_ratio)*xmach)**my_2       &
                    * rotor(i)%rtip**my_2
!     torque= my_pi * rotor(i)%cq * (abs(rotor(i)%vt_ratio)*xmach)**my_2       &
!                   * rotor(i)%rtip**my_3
    else
      thrust= my_pi * rotor(i)%ct * (abs(rotor(i)%vt_ratio))**my_2             &
                    * rotor(i)%rtip**my_2
!     torque= my_pi * rotor(i)%cq * (abs(rotor(i)%vt_ratio))**my_2             &
!                   * rotor(i)%rtip**my_3
    end if

    get_area_nazimuth: select case ( rotor(i)%rottype )
      case (1)
        area     = my_pi * (rotor(i)%rtip**my_2 - rotor(i)%rroot**my_2)
        nazimuth = rotor(i)%nnormal
        nzone    = 1
      case (2)
        area     = rotor(i)%chord * (rotor(i)%rtip - rotor(i)%rroot)
        nazimuth = rotor(i)%nblade
        nzone    = rotor(i)%nblade
      case default
        nzone    = 1       ! to supress compiler warning
        area     = 0._dp
        nazimuth = 1
        write(*,*) "init_source_force:: invalid rotor type"
        error_code = 1
        return
    end select get_area_nazimuth

    sum_beta = my_0
    sum_flap_angle: do iazimuth = 1,nazimuth
      flap = rotor(i)%beta(1)
      do h = 1,rotor(i)%nbeta
        flap = flap + rotor(i)%beta(2*h  ) * sin(h*rotor(i)%psi(iazimuth))     &
                    + rotor(i)%beta(2*h+1) * cos(h*rotor(i)%psi(iazimuth))
      end do
      sum_beta = sum_beta + cos(flap)
    end do sum_flap_angle

    get_pressure_from_thrust: select case ( rotor(i)%rottype )
      case (1)
        dp_t     = rotor(i)%nnormal * thrust / sum_beta / area
        dp_t_tip = my_3 / my_2 * dp_t * rotor(i)%rtip *                        &
                   (rotor(i)%rtip**my_2 - rotor(i)%rroot**my_2) /              &
                   (rotor(i)%rtip**my_3 - rotor(i)%rroot**my_3)
      case (2)
        dp_t     = thrust / sum_beta / area
        dp_t_tip = my_2*dp_t*rotor(i)%rtip / (rotor(i)%rtip+rotor(i)%rroot)
      case default
        dp_t     = 0._dp   ! to supress compiler warning
        dp_t_tip = 0._dp
        write(*,*) "init_source_force:: invalid rotor type"
        error_code = 1
        return
    end select get_pressure_from_thrust

!   get_pressure_from_power, To be added in the future

!   Rotate forces into xyz coordinate system
    rotate_forces: do izone = 1,nzone
      loop_over_normal: do k = 1,rotor(i)%nnormal

!       Set the azimuth angle, flap angle, and lag angle
        select case ( rotor(i)%rottype )
          case (1)
            azimuth = rotor(i)%psi(k)
          case (2)
            azimuth = rotor(i)%psi(izone)
          case default
            error_code = 1
            return
        end select

        flap = rotor(i)%beta(1)
        do h = 1,rotor(i)%nbeta
          flap = flap + rotor(i)%beta(2*h)   * sin(h*azimuth) +                &
                        rotor(i)%beta(2*h+1) * cos(h*azimuth)
        end do

        lag = rotor(i)%delta(1)
        do h = 1,rotor(i)%ndelta
          lag = lag + rotor(i)%delta(2*h)   * sin(h*azimuth) +                 &
                      rotor(i)%delta(2*h+1) * cos(h*azimuth)
        end do

        loop_over_radius: do j = 1,rotor(i)%nradial
          select case ( rotor(i)%loadtype )
            case (1)     ! Uniform pressure jump
              f1 = my_0
              f2 = my_0
              f3 = dp_t * rotor(i)%area(j)
            case (2:3)   ! Linearly increasing pressure jump
              f1 = my_0
              f2 = my_0
              f3 = dp_t_tip*rotor(i)%area(j)*rotor(i)%radius(j)/rotor(i)%rtip
            case (5)
              outside_of_stations : if (                                      &
                rotor(i)%radius(j)/rotor(i)%rtip < station_radius(1) .or.     &
                rotor(i)%radius(j)/rotor(i)%rtip > station_radius(nstation) ) &
                  then
                f1 = my_0
                f2 = my_0
                f3 = my_0
              else
! refactor to use interp1d
                istation = 1
                find_station_pair : do while (istation < nstation .and. &
              rotor(i)%radius(j)/rotor(i)%rtip > station_radius(istation+1) )
                  istation = istation + 1
                end do find_station_pair
                dstation = station_radius(istation+1) - station_radius(istation)
                cant_interpolate : if ( dstation < 1.0e-15_dp ) then
                  s = 0.5_dp
                else
                  s = ( rotor(i)%radius(j)/rotor(i)%rtip  &
                      - station_radius(istation)) / dstation
                end if cant_interpolate
                ct = s*station_thrust(istation+1) &
                   + (my_1-s)*station_thrust(istation)
                cq = s*station_torque(istation+1) &
                   + (my_1-s)*station_torque(istation)
                if ( eqn_set == compressible ) then
                  thrust = ct * (abs(rotor(i)%vt_ratio)*xmach)**2
                  torque = cq * (abs(rotor(i)%vt_ratio)*xmach)**2
                else
                  thrust = ct * (abs(rotor(i)%vt_ratio))**2
                  torque = cq * (abs(rotor(i)%vt_ratio))**2
                end if
                f1 = my_0
                f2 = my_0
                if (rotor(i)%swirl == 1) then
                  if (rotor(i)%omega >= my_0) then
                    f2 = -torque * rotor(i)%area(j)
                  else
                    f2 =  torque * rotor(i)%area(j)
                  end if
                end if
                f3 =  thrust * rotor(i)%nnormal / sum_beta &
                             * rotor(i)%area(j)
              end if outside_of_stations
            case (6)
              r_h = rotor(i)%rroot/rotor(i)%rtip
              rstar = (rotor(i)%radius(j)/rotor(i)%rtip-r_h)/(1._dp-r_h)
              normalization = 105._dp/8._dp/(3._dp*r_h+4._dp*1_dp)/(1._dp-r_h)
              ct = rotor(i)%ct * normalization * rstar * sqrt(1._dp-rstar)
              cq = rotor(i)%cq * normalization * rstar * sqrt(1._dp-rstar) &
                / ( rstar * (1._dp-r_h) + r_h )
              if ( eqn_set == compressible ) then
                thrust = ct * (abs(rotor(i)%vt_ratio)*xmach)**2
                torque = cq * (abs(rotor(i)%vt_ratio)*xmach)**2
              else
                thrust = ct * (abs(rotor(i)%vt_ratio))**2
                torque = cq * (abs(rotor(i)%vt_ratio))**2
              end if
              f1 = my_0
              f2 = my_0
              if (rotor(i)%swirl == 1) then
                if (rotor(i)%omega >= my_0) then
                  f2 = -torque * rotor(i)%area(j)
                else
                  f2 =  torque * rotor(i)%area(j)
                end if
              end if
              f3 =  thrust * rotor(i)%nnormal / sum_beta &
                           * rotor(i)%area(j)
            case default
              write(*,'("init_source_force:: invalid load type")')
              error_code = 1
              return
          end select

          call rotate_flap2xyz(i,f1,f2,f3,azimuth,flap,lag)

          rotor(i)%fx(izone,j,k) = f1
          rotor(i)%fy(izone,j,k) = f2
          rotor(i)%fz(izone,j,k) = f3
        end do loop_over_radius

      end do loop_over_normal
    end do rotate_forces

  end subroutine init_source_force

!========= READ_USER_SOURCE1 =================================================80
!
!   Reads in the source grid dimensions for the user specified source file
!   Called from init_rotor_source
!
!   User specified sources allow for source strengths to come from another
!   code, such as a comprehensive rotor code.
!
!=============================================================================80

  subroutine read_user_source1(i, flow_dir)

    use lmpi,        only: lmpi_die

    integer, intent(in)  :: i

    character(*), intent(in) :: flow_dir

    integer              :: iostat
    character(len=80)    :: inp_file

  continue

!   Open the rotor_sourceXX.input file
    select case ( i )
     case (1:9)
      write(inp_file,'("rotor_source0",i1,".input")') i
     case (10:99)
      write(inp_file,'("rotor_source", i2,".input")') i
     case default
      write(*,'("ERROR: rotor_sourceXX.input set up for up to 99 rotors")')
      call lmpi_die
    end select

    inp_file = trim(flow_dir) // trim(inp_file)

    open(unit=iunit, file=inp_file, form='unformatted',                        &
         status='old', iostat=iostat)
    if (iostat /= 0) then
      write(*,'("ERROR: rotor_sourceXX.input not found. Quitting...")')
      call lmpi_die
    end if
    rewind(iunit)

!   Read file header
    read(iunit) rotor(i)%nradial, rotor(i)%nnormal

    close(iunit)

  end subroutine read_user_source1

!========= READ_USER_SOURCE2 =================================================80
!
!   Reads in the source locations and forces for user specified sources.
!
!=============================================================================80

  subroutine read_user_source2(i, flow_dir)

    use lmpi,        only: lmpi_die

    integer, intent(in)  :: i

    character(*), intent(in) :: flow_dir

    integer              :: j, k, nr, nn, iostat
    character(len=80)    :: inp_file

  continue

!   Open the rotor_sourceXX.input file
    select case ( i )
      case (1:9)
        write(inp_file,'("rotor_source0",i1,".input")') i
      case (10:99)
        write(inp_file,'("rotor_source", i2,".input")') i
      case default
        write(*,'("ERROR: rotor_sourceXX.input set up for up to 99 rotors")')
        call lmpi_die
    end select

    inp_file = trim(flow_dir) // trim(inp_file)

    open(unit=iunit, file=inp_file, form='unformatted',                        &
         status='old', iostat=iostat)
    if (iostat /= 0) then
      write(*,'("ERROR: rotor_sourceXX.input not found. Quitting...")')
      call lmpi_die
    end if
    rewind(iunit)

!   Read file
    read(iunit) nr, nn
    read(iunit) ((rotor(i)%x_s(1,j,k), j=1,nr), k=1,nn),                       &
                ((rotor(i)%y_s(1,j,k), j=1,nr), k=1,nn),                       &
                ((rotor(i)%z_s(1,j,k), j=1,nr), k=1,nn)
    read(iunit) ((rotor(i)%fx(1,j,k), j=1,nr), k=1,nn),                        &
                ((rotor(i)%fy(1,j,k), j=1,nr), k=1,nn),                        &
                ((rotor(i)%fz(1,j,k), j=1,nr), k=1,nn)

    close(iunit)

  end subroutine read_user_source2

!========= WRITE_SOURCE_GRID =================================================80
!
!   Writes out the source grid(s) in unformatted, multigrid, plot3d format
!
!=============================================================================80

  subroutine write_source_grid(p3d_out,ref_num)

    use lmpi,        only: lmpi_die, lmpi_master

    integer,                       intent(in)  :: p3d_out, ref_num

    integer                                    :: i, j, k, iostat
    integer                                    :: nr, nn, num_vars
    integer                                    :: izone, nzone, tot_zone
    integer                                    :: alloc_err

    integer,     dimension(:),     allocatable :: header_grid, header_soln
    real(dp), dimension(:,:),   allocatable    :: force, moment
    real(dp), dimension(3)                     :: moment_arm

    character(len=21)                          :: out_file

    real(dp), parameter                        :: my_0 = 0.0_dp

  continue

    master_write: if ( lmpi_master ) then

!     Write the force and moment summary
      write(*,'("Rotor Force Summary:")')
      write_rotor_output: do i = 1,nrotor
        nzone = 1
        if ( rotor(i)%rottype == 2 ) nzone = rotor(i)%nblade

        allocate(force(3,nzone), stat=alloc_err )
        if ( alloc_err /= 0 ) then
          write(*,*) "Failed to allocate force in write_source_grid"
          call lmpi_die
        end if
        allocate(moment(3,nzone), stat=alloc_err )
        if ( alloc_err /= 0 ) then
          write(*,*) "Failed to allocate moment in write_source_grid"
          call lmpi_die
        end if

        force = my_0
        moment = my_0
        sum_forces: do izone = 1,nzone
          do j = 1,rotor(i)%nradial
            do k = 1,rotor(i)%nnormal
              force(1,izone) = force(1,izone) + rotor(i)%fx(izone,j,k)
              force(2,izone) = force(2,izone) + rotor(i)%fy(izone,j,k)
              force(3,izone) = force(3,izone) + rotor(i)%fz(izone,j,k)

!             Compute the moment about the hub in the XYZ coordinate system
              moment_arm(1) = rotor(i)%x_s(izone,j,k) - rotor(i)%x0
              moment_arm(2) = rotor(i)%y_s(izone,j,k) - rotor(i)%y0
              moment_arm(3) = rotor(i)%z_s(izone,j,k) - rotor(i)%z0

              moment(1,izone) = moment(1,izone)                                &
                              + moment_arm(2) * rotor(i)%fz(izone,j,k)         &
                              - moment_arm(3) * rotor(i)%fy(izone,j,k)
              moment(2,izone) = moment(2,izone)                                &
                              + moment_arm(3) * rotor(i)%fx(izone,j,k)         &
                              - moment_arm(1) * rotor(i)%fz(izone,j,k)
              moment(3,izone) = moment(3,izone)                                &
                              + moment_arm(1) * rotor(i)%fy(izone,j,k)         &
                              - moment_arm(2) * rotor(i)%fx(izone,j,k)
            end do
          end do

!         Sum actuator blades in the zone 1 spot
          if (izone > 1) then
            force(:,1)  = force(:,1)  + force(:,izone)
            moment(:,1) = moment(:,1) + moment(:,izone)
          end if
        end do sum_forces

        force(:,1)  = force(:,1)  * force_ref
        moment(:,1) = moment(:,1) * moment_ref

        write(*,'("Rotor ",i2,"   Grid Forces:",3(a3,es12.4))')                &
              i, "Fx=", force(1,1),  "Fy=", force(2,1),  "Fz=", force(3,1)
        write(*,'("Rotor ",i2,"  Grid Moments:",3(a3,es12.4))')                &
              i, "Mx=", moment(1,1), "My=", moment(2,1), "Mz=", moment(3,1)

        force(:,1)  = matmul(transpose(rotor(i)%rot2xyz), force(:,1))
        moment(:,1) = matmul(transpose(rotor(i)%rot2xyz), moment(:,1))

        write(*,'("Rotor ",i2,"  Shaft Forces:",3(a3,es12.4))')                &
              i, "H =", force(1,1),  "Y =", force(2,1),  "T =", force(3,1)
        write(*,'("Rotor ",i2," Shaft Moments:",3(a3,es12.4))')                &
              i, "Mh=", moment(1,1), "My=", moment(2,1), "Q=", moment(3,1)

        deallocate(force)
        deallocate(moment)

      end do write_rotor_output

      write_p3d_output: if ( p3d_out == 1 ) then

!       Count number of zones to write.
        tot_zone = 0
        do i = 1,nrotor
          if ( rotor(i)%rottype == 1 ) then
            tot_zone = tot_zone + 1
          else
            tot_zone = tot_zone + rotor(i)%nblade
          end if
        end do

!       Set up the zone header
        allocate( header_grid(3*tot_zone), stat=alloc_err )
        if ( alloc_err /= 0 ) then
          write(*,*) "Failed to allocate header_grid in write_source_grid"
          call lmpi_die
        end if
        allocate( header_soln(4*tot_zone), stat=alloc_err )
        if ( alloc_err /= 0 ) then
          write(*,*) "Failed to allocate header_soln in write_source_grid"
          call lmpi_die
        end if

        izone = 0
        do i = 1,nrotor
          num_vars = 4
          if (rotor(i)%loadtype == 3) num_vars = 7

          if ( rotor(i)%rottype == 1 ) then
            header_grid(izone*3+1) = rotor(i)%nradial
            header_grid(izone*3+2) = rotor(i)%nnormal
            header_grid(izone*3+3) = 1
            header_soln(izone*4+1) = rotor(i)%nradial
            header_soln(izone*4+2) = rotor(i)%nnormal
            header_soln(izone*4+3) = 1
            header_soln(izone*4+4) = num_vars
            izone = izone + 1
          else
            do j = 1,rotor(i)%nblade
              header_grid(izone*3+1) = rotor(i)%nradial
              header_grid(izone*3+2) = rotor(i)%nnormal
              header_grid(izone*3+3) = 1
              header_soln(izone*4+1) = rotor(i)%nradial
              header_soln(izone*4+2) = rotor(i)%nnormal
              header_soln(izone*4+3) = 1
              header_soln(izone*4+4) = num_vars
              izone = izone + 1
            end do
          end if
        end do

!       Write grid file
        select case ( ref_num )
          case (0:9)
            write(out_file,'("source_grid_0000",i1,".p3d")') ref_num
          case (10:99)
            write(out_file,'("source_grid_000", i2,".p3d")') ref_num
          case (100:999)
            write(out_file,'("source_grid_00",  i3,".p3d")') ref_num
          case (1000:9999)
            write(out_file,'("source_grid_0",   i4,".p3d")') ref_num
          case (10000:99999)
            write(out_file,'("source_grid_",    i5,".p3d")') ref_num
          case default
            write(*,'("ERROR: ref_num too large for source_grid file")')
            call lmpi_die
        end select

        open(unit=iunit, file=out_file,                      &
             status='unknown', iostat=iostat)
        if (iostat /= 0) then
          write(*,'("ERROR: failed to create ",a21," Quitting...")') out_file
          call lmpi_die
        end if
        rewind(iunit)

!       Write header
        write(iunit,'( i10)') tot_zone
        write(iunit,'(3i10)') (header_grid(j), j=1,3*tot_zone)

!       Write coordinates
        do i = 1,nrotor
          nzone = 1
          if ( rotor(i)%rottype == 2 ) nzone = rotor(i)%nblade
          do izone = 1,nzone
            nr = rotor(i)%nradial
            nn = rotor(i)%nnormal
            write(iunit,'(10es12.4)')((rotor(i)%x_s(izone,j,k),j=1,nr),k=1,nn),&
                                    ((rotor(i)%y_s(izone,j,k),j=1,nr),k=1,nn), &
                                    ((rotor(i)%z_s(izone,j,k),j=1,nr),k=1,nn)
          end do
        end do

        close(iunit)

!       Write function file
        select case ( ref_num )
          case (0:9)
            write(out_file,'("source_data_0000",i1,".p3d")') ref_num
          case (10:99)
            write(out_file,'("source_data_000", i2,".p3d")') ref_num
          case (100:999)
            write(out_file,'("source_data_00",  i3,".p3d")') ref_num
          case (1000:9999)
            write(out_file,'("source_data_0",   i4,".p3d")') ref_num
          case (10000:99999)
            write(out_file,'("source_data_",    i5,".p3d")') ref_num
          case default
            write(*,'("ERROR: ref_num too large for source_data file")')
            call lmpi_die
        end select

        open(unit=iunit, file=out_file,                      &
               status='unknown', iostat=iostat)
        if (iostat /= 0) then
          write(*,'("ERROR: failed to create ",a21," Quitting...")') out_file
          call lmpi_die
        end if
        rewind(iunit)

!       Write header
        write(iunit,'( i10)') tot_zone
        write(iunit,'(4i10)') (header_soln(j), j=1,4*tot_zone)

        deallocate( header_grid )
        deallocate( header_soln )

!       Write force data
        rotor_data_loop: do i = 1,nrotor
          if( rotor(i)%loadtype == 4 ) cycle rotor_data_loop
          nzone = 1
          if ( rotor(i)%rottype == 2 ) nzone = rotor(i)%nblade

          nr = rotor(i)%nradial
          nn = rotor(i)%nnormal

          write_data_conditional: if (rotor(i)%loadtype == 3) then

            do izone = 1,nzone
              write(iunit,'(10es12.4)')                                        &
                           ((rotor(i)%fx(izone,j,k),        j=1,nr), k=1,nn),  &
                           ((rotor(i)%fy(izone,j,k),        j=1,nr), k=1,nn),  &
                           ((rotor(i)%fz(izone,j,k),        j=1,nr), k=1,nn),  &
                           ((rotor(i)%area(j),              j=1,nr), k=1,nn),  &
                           ((rotor(i)%alpha_eff(izone,j,k), j=1,nr), k=1,nn),  &
                           ((rotor(i)%cl_local(izone,j,k),  j=1,nr), k=1,nn),  &
                           ((rotor(i)%cd_local(izone,j,k),  j=1,nr), k=1,nn)
            end do

          else

            do izone = 1,nzone
              write(iunit,'(10es12.4)')                                        &
                           ((rotor(i)%fx(izone,j,k), j=1,nr), k=1,nn),         &
                           ((rotor(i)%fy(izone,j,k), j=1,nr), k=1,nn),         &
                           ((rotor(i)%fz(izone,j,k), j=1,nr), k=1,nn),         &
                           ((rotor(i)%area(j),       j=1,nr), k=1,nn)
            end do

          end if write_data_conditional
        end do rotor_data_loop

        close(iunit)

      end if write_p3d_output
    end if master_write

  end subroutine write_source_grid

!========= ADVANCE_ROTOR_BLADE ===============================================80
!
!   Advances the blade positions after each timestep.
!
!=============================================================================80

  subroutine advance_rotor_blade()

    use nml_nonlinear_solves, only: dt

    integer             :: i
    integer             :: iblade
    real(dp)            :: dpsi
    real(dp)            :: my_2pi

  continue

    my_2pi = 2.0_dp * acos(-1.0_dp)

    do i = 1,nrotor
      select case ( rotor(i)%rottype )
        case (2)
          dpsi = abs(rotor(i)%omega) * dt
          rotor(i)%psi0 = rotor(i)%psi0 + dpsi
          if ( rotor(i)%psi0 >= my_2pi ) rotor(i)%psi0 = rotor(i)%psi0 - my_2pi

          do iblade = 1,rotor(i)%nblade
            rotor(i)%psi(iblade) = rotor(i)%psi(iblade) + dpsi
            if ( rotor(i)%psi(iblade) >= my_2pi )                              &
              rotor(i)%psi(iblade) = rotor(i)%psi(iblade) - my_2pi
          end do
          call set_blade_positions(i)
        case default
          continue
      end select
    end do

  end subroutine advance_rotor_blade

!========= SET_BLADE_POSITIONS ===============================================80
!
!   Computes the source positions for blade sources based on the azimuth
!   position of the individual blades. This is achieved by rotating a
!   set of reference sources to the proper azimuth and then rotating into
!   the grid XYZ coordinate system.
!
!=============================================================================80

  subroutine set_blade_positions(i)

    integer,     intent(in) :: i

    integer                 :: h, j, k, iblade
    real(dp)                :: pitch, pitch_tmp, flap, lag
    real(dp)                :: x1, x2, y1, y2, z1, z2
    real(dp), parameter     :: my_0 = 0.0_dp

  continue

!   Compute blade source locations
    blade_loop: do iblade = 1,rotor(i)%nblade
      pitch_tmp=rotor(i)%theta0 + rotor(i)%theta1s*sin(rotor(i)%psi(iblade))   &
                                + rotor(i)%theta1c*cos(rotor(i)%psi(iblade))
      flap = rotor(i)%beta(1)
      do h = 1,rotor(i)%nbeta
        flap = flap + rotor(i)%beta(2*h  ) * sin(h*rotor(i)%psi(iblade))       &
                    + rotor(i)%beta(2*h+1) * cos(h*rotor(i)%psi(iblade))
      end do
      lag = rotor(i)%delta(1)
      do h = 1,rotor(i)%ndelta
        lag = lag + rotor(i)%delta(2*h  ) * sin(h*rotor(i)%psi(iblade))        &
                  + rotor(i)%delta(2*h+1) * cos(h*rotor(i)%psi(iblade))
      end do

      radial_loop: do j = 1,rotor(i)%nradial
        if (rotor(i)%have_twist_distribution) then
          pitch = pitch_tmp + rotor(i)%twist_distr(j)
        else
!         thetatw is already normalized by rtip
          pitch = pitch_tmp + rotor(i)%thetatw*rotor(i)%radius(j)
        end if
        chord_loop: do k = 1,rotor(i)%nnormal
          x1 = rotor(i)%x_s_ref(j,k)
          y1 = rotor(i)%y_s_ref(j,k)
          z1 = rotor(i)%z_s_ref(j,k)

!         Pitch the sources, involes a sign change for omega < 0
          x2 = x1 + rotor(i)%rroot - rotor(i)%rfh
          if (rotor(i)%omega >= my_0) then
            y2 = y1 * cos(pitch) - z1 * sin(pitch)
            z2 = y1 * sin(pitch) + z1 * cos(pitch)
          else
            y2 = y1 * cos(pitch) + z1 * sin(pitch)
            z2 =-y1 * sin(pitch) + z1 * cos(pitch)
          end if

!         Flap the sources, does not involve a sign change for omega < 0
          x1 = x2 * cos(flap) - z2 * sin(flap) + rotor(i)%rfh - rotor(i)%rlh
          y1 = y2
          z1 = x2 * sin(flap) + z2 * cos(flap)

!         Lag the sources, requires a sign change for omega < 0
          if (rotor(i)%omega >= my_0) then
            x2 = x1 * cos(lag) + y1 * sin(lag) + rotor(i)%rlh
            y2 =-x1 * sin(lag) + y1 * cos(lag)
          else
            x2 = x1 * cos(lag) - y1 * sin(lag) + rotor(i)%rlh
            y2 = x1 * sin(lag) + y1 * cos(lag)
          end if
          z2 = z1

!         Rotate to proper azimuth angle, requires a sign change for omega < 0
          if (rotor(i)%omega >= my_0) then
            x1 = x2*cos(rotor(i)%psi(iblade)) - y2*sin(rotor(i)%psi(iblade))
            y1 = x2*sin(rotor(i)%psi(iblade)) + y2*cos(rotor(i)%psi(iblade))
          else
            x1 = x2*cos(rotor(i)%psi(iblade)) + y2*sin(rotor(i)%psi(iblade))
            y1 =-x2*sin(rotor(i)%psi(iblade)) + y2*cos(rotor(i)%psi(iblade))
          end if
          z1 = z2

!         Rotate from rotor frame to xyz frame
          rotor(i)%x_s(iblade,j,k) = rotor(i)%rot2xyz(1,1) * x1 +              &
                                     rotor(i)%rot2xyz(1,2) * y1 +              &
                                     rotor(i)%rot2xyz(1,3) * z1 + rotor(i)%x0
          rotor(i)%y_s(iblade,j,k) = rotor(i)%rot2xyz(2,1) * x1 +              &
                                     rotor(i)%rot2xyz(2,2) * y1 +              &
                                     rotor(i)%rot2xyz(2,3) * z1 + rotor(i)%y0
          rotor(i)%z_s(iblade,j,k) = rotor(i)%rot2xyz(3,1) * x1 +              &
                                     rotor(i)%rot2xyz(3,2) * y1 +              &
                                     rotor(i)%rot2xyz(3,3) * z1 + rotor(i)%z0
        end do chord_loop
      end do radial_loop
    end do blade_loop

  end subroutine set_blade_positions

!========= SOURCE2NODE_ASSOC =================================================80
!
!  Assigns sources to grid nodes based on shortest distance.
!
!  Since all partitions are surrounded by a layer of ghost nodes, this routine
!  indirectly accounts for sources located on different partitions. If a rotor
!  source does not exist on a partition it gets associated with a ghost node.
!  Source associated with a ghost node are ignored during the solution process.
!
!  Distance is calculated as d^2; the square root just adds an extra operation
!
!  Parallel implementation notes.
!  Each processor will find the closest non-boundary xyz for each rotor xyz_s.
!  The global minimum distant and associated global node for each rotor xyz_s
!  is determined.  Each processor determines if global minimum matches it
!  local miminum. If it does not, then set to ghost node (ignored later as
!  noted above). If it matches, then if not equal to global node (rareity),
!  then note it.
!
!=============================================================================80

  subroutine source2node_assoc(nnodes0, nnodes01, x, y, z, bc, nbound, l2g,    &
                               last_time)

  use lmpi,     only : lmpi_nproc, lmpi_reduce_maxloc2, lmpi_bcast,            &
                       lmpi_master, lmpi_synchronize
  use bc_types, only : bcgrid_type
  use timings,  only : timer

    integer,                                intent(in)  :: nnodes01, nbound
    integer,                                intent(in)  :: nnodes0
    integer,           dimension(nnodes01), intent(in)  :: l2g
    real(dp),       dimension(nnodes01),    intent(in)  :: x, y, z
    type(bcgrid_type), dimension(nbound),   intent(in)  :: bc
    logical,                      optional, intent(in)  :: last_time

    integer                                       :: i, j, k, ioff
    integer                                       :: first_inode, last_inode
    integer                                       :: inode, izone, nzone
    integer                                       :: ib, ibcn

    logical, dimension(:),     allocatable        :: tag

    real(dp)                                      :: dist1, dist2, distx1
    real(dp)                                      :: xs, ys, zs
    real(dp), dimension(:,:), allocatable         :: rtemp1, rtemp2

    logical                                       :: parallel, clean_up_after
    integer                                       :: nsources
    logical                                       :: debug_routine = .false.
    logical                                       :: time_routine  = .false.

  continue

   if (time_routine) then
     call lmpi_synchronize
     if (lmpi_master) call timer('Before source2node_assoc')
   end if

   parallel = (lmpi_nproc > 1)

   clean_up_after = .false.
   if (present(last_time)) clean_up_after = last_time

   ! Tag all boundary nodes.

   allocate(tag(nnodes01))
   tag = .false.
   do ib = 1, nbound
      do ibcn = 1, bc(ib)%nbnode
         tag(bc(ib)%ibnode(ibcn)) = .true.
     end do
   end do

   ! Set first seed to a non-boundary node.
   first_inode = 0
   do i = 1,nnodes0
      if (.not.tag(i)) then
         first_inode = i
         exit
      end if
   end do
   if (first_inode == 0) write(*,*) 'Only boundary nodes!'

   if (time_routine) then
     call lmpi_synchronize
     if (lmpi_master) call timer('Tag boundary nodes')
   end if

    rotor_loop: do i = 1,nrotor
      nzone = 1
      if ( rotor(i)%rottype == 2 ) nzone = rotor(i)%nblade

      nsources = nzone*rotor(i)%nradial*rotor(i)%nnormal
      if (parallel) then
        allocate(rtemp1(2,nsources))
        rtemp1 = 1.0e10_dp
      end if

      if (time_routine) then
        call lmpi_synchronize
        if (lmpi_master) call timer('Rotor loop before search')
      end if

      search_type: if (use_kdtree) then

        call kdtree_search(nnodes0, nnodes01, x, y, z, l2g, i,                 &
                           nsources, nzone, parallel, rtemp1, clean_up_after)

      else search_type

!       Determine the closest node to each source using naive search

        last_inode = first_inode
        ioff = 0

        loop_over_normal_dir: do k = 1,rotor(i)%nnormal
          loop_over_radial_dir: do j = 1,rotor(i)%nradial
            loop_over_source_grids: do izone = 1,nzone

              ioff = ioff + 1

              xs = rotor(i)%x_s(izone,j,k)
              ys = rotor(i)%y_s(izone,j,k)
              zs = rotor(i)%z_s(izone,j,k)

              ! Use last value as initial seed
              dist1 = (xs - x(last_inode)) * (xs - x(last_inode)) +            &
                      (ys - y(last_inode)) * (ys - y(last_inode)) +            &
                      (zs - z(last_inode)) * (zs - z(last_inode))

              loop_over_nodes: do inode = 1, nnodes0

                if (.not.tag(inode)) then

                   distx1 = (xs - x(inode)) * (xs - x(inode))

                   if (distx1 < dist1) then

                      dist2 = distx1                               +           &
                           (ys - y(inode)) * (ys - y(inode))       +           &
                           (zs - z(inode)) * (zs - z(inode))

                      if ( dist2 < dist1 ) then
                         last_inode = inode
                         dist1      = dist2
                      end if

                   end if

                end if

              end do loop_over_nodes

              rotor(i)%s2n(izone,j,k) = last_inode

              if (parallel) then
                rtemp1(1,ioff) = -dist1 ! Negative for maxloc2
                rtemp1(2,ioff) = real(l2g(last_inode),dp) ! Note, l2g.
              end if

            end do loop_over_source_grids
          end do loop_over_radial_dir
        end do loop_over_normal_dir

      end if search_type

      if (time_routine) then
        call lmpi_synchronize
        if (lmpi_master) call timer('Search loop')
      end if

      if (parallel) then

         allocate(rtemp2(2,nzone*rotor(i)%nradial*rotor(i)%nnormal))
         call lmpi_reduce_maxloc2(rtemp1,rtemp2)
         call lmpi_bcast(rtemp2)

         ioff   = 0
         do k = 1,rotor(i)%nnormal
            do j = 1,rotor(i)%nradial
               do izone = 1,nzone
                  ioff = ioff + 1
                 if (rtemp2(1,ioff) /= rtemp1(1,ioff)) then
                    rotor(i)%s2n(izone,j,k) = nnodes01
                 else if (rtemp2(2,ioff) /= rtemp1(2,ioff)) then
                    write(*,*)                                                 &
                      ' WARNING: Source associated with multiple partitions.'
                    write(*,*)'mismatch = ',                                   &
                      ioff,rtemp2(1,ioff),rtemp2(2,ioff),rtemp1(2,ioff)
                 end if
               end do ! izone
            end do ! j
         end do ! k

         if (debug_routine) then
           call write_source2node_assoc(i, nzone, nsources, rtemp1)
         end if

         deallocate(rtemp1,rtemp2)

      end if ! parallel

      if (time_routine) then
        call lmpi_synchronize
        if (lmpi_master) call timer('Multiple partition check')
      end if

  end do rotor_loop

  deallocate(tag)

  end subroutine source2node_assoc

!=============================================================================80
! Write rotor(irotor)%s2n to files for debugging purposes
!=============================================================================80
  subroutine write_source2node_assoc(irotor, nzone, nsources, rtemp1)

    use lmpi,              only: lmpi_id
    use string_utils,      only: sprintf, MAX_STR_LEN
    use file_utils,        only: available_unit
    use system_extensions, only: se_open

    integer,                         intent(in) :: irotor, nzone, nsources
    real(dp), dimension(2,nsources), intent(in) :: rtemp1

    integer                    :: iu, ioff, k, j, izone
    character(len=MAX_STR_LEN) :: fname

    iu = available_unit()
    fname = sprintf('source2node.%i0', lmpi_id)
    call se_open(iu, file=fname, form='formatted')
    write(iu,*) 'Proc', lmpi_id, 'after partition check'
    write(iu,'(a)') 'zone   j   k     s2n         dist     l2g'
    ioff = 0
    do k = 1,rotor(irotor)%nnormal
      do j = 1,rotor(irotor)%nradial
        do izone = 1,nzone
          ioff = ioff + 1
          write(iu,'(i4,i4,i4,i8,e13.3,i8)') izone, j, k,                      &
            rotor(irotor)%s2n(izone,j,k), -rtemp1(1,ioff), int(rtemp1(2,ioff))
        end do
      end do
    end do
    close(iu)

  end subroutine write_source2node_assoc

!=============================================================================80
! Do actuator search with custom kd-tree, building tree if necessary
!=============================================================================80
  subroutine kdtree_search(nnodes0, nnodes01, x, y, z, l2g, irotor,            &
                           nsrcs, nzone, parallel, rtemp1, clean_up_after)

    use kdtree,      only : kdtree_type, kd_create, kd_free, kd_insert_all,    &
                            kd_search

    integer,                       intent(in)  :: nnodes0, nnodes01, nsrcs
    integer,                       intent(in)  :: nzone, irotor
    real(dp), dimension(nnodes01), intent(in)  :: x, y, z
    integer,  dimension(nnodes01), intent(in)  :: l2g
    logical,                       intent(in)  :: parallel, clean_up_after
    real(dp), dimension(2,nsrcs),  intent(out) :: rtemp1

    integer                               :: izone, irad, inorm, ioff, node
    real(dp), dimension(3)                :: xyzsrc
    real(dp), dimension(nsrcs)            :: dists
    type(kdtree_type),      pointer, save :: tree
    logical,                         save :: first_time = .true.

  continue

    need_to_build_tree: if (first_time) then

      call kd_create(tree)
      call kd_insert_all(tree, nnodes0,                                        &
                          x(1:nnodes0), y(1:nnodes0), z(1:nnodes0))
      first_time = .not. first_time

    end if need_to_build_tree

    ioff = 0
    do inorm = 1,rotor(irotor)%nnormal
      do irad = 1,rotor(irotor)%nradial
        do izone = 1,nzone
          ioff = ioff + 1
          xyzsrc(1) = rotor(irotor)%x_s(izone,irad,inorm)
          xyzsrc(2) = rotor(irotor)%y_s(izone,irad,inorm)
          xyzsrc(3) = rotor(irotor)%z_s(izone,irad,inorm)
          call kd_search(tree, xyzsrc, rotor(irotor)%s2n(izone,irad,inorm),    &
                         dists(ioff))
        end do
      end do
    end do

!   Set up the arrays we need for a multiple partition check in the
!   calling routine
    if (parallel) then
      ioff = 0
      do inorm = 1,rotor(irotor)%nnormal
        do irad = 1,rotor(irotor)%nradial
          do izone = 1,nzone
            ioff = ioff + 1
            rtemp1(1,ioff) = -dists(ioff)
            node = rotor(irotor)%s2n(izone,irad,inorm)
            rtemp1(2,ioff) = real(l2g(node), dp)
          end do
        end do
      end do
    end if

    if (clean_up_after) then
      call kd_free(tree)
    end if

  end subroutine kdtree_search

!========= ROTATE_FLAP2XYZ ===================================================80
!
! Rotates a vector, f, from the flap coordinate system to the
! XYZ coordinate system.
!
!=============================================================================80

  subroutine rotate_flap2xyz(i, f1, f2, f3, azimuth, flap, lag)

    integer,     intent(in)    :: i
    real(dp),    intent(inout) :: f1, f2, f3
    real(dp),    intent(in)    :: azimuth, flap, lag

    real(dp)                   :: f1_a, f2_a, f3_a
    real(dp)                   :: f1_r, f2_r, f3_r

  continue

!   Rotate vector from the flap coord. system to the azimuth coord. system
    f1_a = cos(flap) * f1 - sin(flap) * f3
    f2_a = f2
    f3_a = sin(flap) * f1 + cos(flap) * f3

!   Rotate vector from the azimuth coord. system to the rotor coord. system
    if (rotor(i)%omega >= 0.0_dp) then
      f1_r = cos(azimuth-lag) * f1_a - sin(azimuth-lag) * f2_a
      f2_r = sin(azimuth-lag) * f1_a + cos(azimuth-lag) * f2_a
    else
      f1_r = cos(azimuth-lag) * f1_a + sin(azimuth-lag) * f2_a
      f2_r =-sin(azimuth-lag) * f1_a + cos(azimuth-lag) * f2_a
    end if
    f3_r = f3_a

!   Rotate vector from the rotor coord. system  to the XYZ coord. system
    f1 = rotor(i)%rot2xyz(1,1) * f1_r + rotor(i)%rot2xyz(1,2) * f2_r +         &
         rotor(i)%rot2xyz(1,3) * f3_r
    f2 = rotor(i)%rot2xyz(2,1) * f1_r + rotor(i)%rot2xyz(2,2) * f2_r +         &
         rotor(i)%rot2xyz(2,3) * f3_r
    f3 = rotor(i)%rot2xyz(3,1) * f1_r + rotor(i)%rot2xyz(3,2) * f2_r +         &
         rotor(i)%rot2xyz(3,3) * f3_r

  end subroutine rotate_flap2xyz

!========= ROTATE_XYZ2FLAP ===================================================80
!
! Rotates a vector, f, from the XYZ coordinate system to the
! flap coordinate system.
!
!=============================================================================80

  subroutine rotate_xyz2flap(i, f1, f2, f3, azimuth, flap, lag)

    integer,     intent(in)    :: i
    real(dp),    intent(inout) :: f1, f2, f3
    real(dp),    intent(in)    :: azimuth, flap, lag

    real(dp)                   :: f1_a, f2_a, f3_a
    real(dp)                   :: f1_r, f2_r, f3_r

  continue

!   Rotate vector from the XYZ coord. system  to the rotor coord. system
    f1_r = rotor(i)%rot2xyz(1,1) * f1 + rotor(i)%rot2xyz(2,1) * f2 +           &
           rotor(i)%rot2xyz(3,1) * f3
    f2_r = rotor(i)%rot2xyz(1,2) * f1 + rotor(i)%rot2xyz(2,2) * f2 +           &
           rotor(i)%rot2xyz(3,2) * f3
    f3_r = rotor(i)%rot2xyz(1,3) * f1 + rotor(i)%rot2xyz(2,3) * f2 +           &
           rotor(i)%rot2xyz(3,3) * f3

!   Rotate vector from the rotor coord. system to the azimuth coord. system
    if (rotor(i)%omega >= 0.0_dp) then
      f1_a =  cos(azimuth-lag) * f1_r + sin(azimuth-lag) * f2_r
      f2_a = -sin(azimuth-lag) * f1_r + cos(azimuth-lag) * f2_r
    else
      f1_a =  cos(azimuth-lag) * f1_r - sin(azimuth-lag) * f2_r
      f2_a =  sin(azimuth-lag) * f1_r + cos(azimuth-lag) * f2_r
    end if
    f3_a =  f3_r

!   Rotate vector from the azimuth coord. system to the flap coord. system
    f1 =  cos(flap) * f1_a + sin(flap) * f3_a
    f2 =  f2_a
    f3 = -sin(flap) * f1_a + cos(flap) * f3_a

  end subroutine rotate_xyz2flap

!========= BLADE_ANGLES ======================================================80
!
! Computes the blade orientation angles: azimuth, flap, and lag
!
!=============================================================================80

  subroutine blade_angles(i, izone, k, azimuth, flap, lag)

    integer,     intent(in)  :: i, k, izone
    real(dp),    intent(out) :: azimuth, flap, lag
    integer                  :: n

  continue

!   Set the azimuth angle, flap angle, and lag angle
    if ( rotor(i)%rottype == 1) then
      azimuth = rotor(i)%psi(k)
    else
      azimuth = rotor(i)%psi(izone)
    end if

    flap = rotor(i)%beta(1)
    do n = 1,rotor(i)%nbeta
      flap = flap + rotor(i)%beta(n*2)   * sin(n*azimuth) +                    &
                    rotor(i)%beta(n*2+1) * cos(n*azimuth)
    end do

    lag = rotor(i)%delta(1)
    do n = 1,rotor(i)%ndelta
      lag = lag + rotor(i)%delta(n*2)   * sin(n*azimuth) +                     &
                  rotor(i)%delta(n*2+1) * cos(n*azimuth)
    end do

  end subroutine blade_angles

!========= ROTOR_SOURCE_RHS ==================================================80
!
! Adds the rotor source contribution to the right hand side of the equations
! Note: this routine assumes primitive varables as input.
! Called from source.f90
!
!=============================================================================80

  subroutine rotor_source_rhs(eqn_set, nnodes0,nnodes01,qnode,res, n_tot, njac)

    use solution_types, only: compressible, incompressible

    integer, intent(in) :: eqn_set
    integer, intent(in) :: nnodes0, nnodes01, n_tot, njac

    real(dp), dimension(n_tot,nnodes01),    intent(in)   :: qnode
    real(dp), dimension(njac, nnodes01),    intent(inout):: res

    integer                                              :: i, j, k
    integer                                              :: izone, inode, nzone
    real(dp)                                             :: u, v, w
    real(dp), parameter                                  :: my_0 = 0.0_dp

  continue

    rotor_loop: do i = 1,nrotor
      nzone = 1
      if ( rotor(i)%rottype == 2 ) nzone = rotor(i)%nblade

!     Compute new blade element based rotor forces
      if ( rotor(i)%loadtype == 3) then
        call blade_element_rhs(eqn_set,                                  &
                               i, nzone, nnodes0, nnodes01, qnode, n_tot)
      end if

      rotor_zone_loop: do izone = 1,nzone
        source_loop_radius: do j = 1,rotor(i)%nradial
          source_loop_normal: do k = 1,rotor(i)%nnormal
            inode = rotor(i)%s2n(izone,j,k)
            source_on_partition: if ( inode <= nnodes0 ) then
              select case (eqn_set)
                case (compressible)
                  u = qnode(2,inode)
                  v = qnode(3,inode)
                  w = qnode(4,inode)

                  res(1,inode) = res(1,inode) + my_0
                  res(2,inode) = res(2,inode) + rotor(i)%fx(izone,j,k)
                  res(3,inode) = res(3,inode) + rotor(i)%fy(izone,j,k)
                  res(4,inode) = res(4,inode) + rotor(i)%fz(izone,j,k)
                  res(5,inode) = res(5,inode) + rotor(i)%fx(izone,j,k) * u +   &
                                                rotor(i)%fy(izone,j,k) * v +   &
                                                rotor(i)%fz(izone,j,k) * w
                case (incompressible)
                  res(1,inode) = res(1,inode) + my_0
                  res(2,inode) = res(2,inode) + rotor(i)%fx(izone,j,k)
                  res(3,inode) = res(3,inode) + rotor(i)%fy(izone,j,k)
                  res(4,inode) = res(4,inode) + rotor(i)%fz(izone,j,k)
                case default
                  write(*,'("ERROR: rotor_source_rhs: invalid eqn_set option")')
              end select

            end if source_on_partition
          end do source_loop_normal
        end do source_loop_radius
      end do rotor_zone_loop
    end do rotor_loop

  end subroutine rotor_source_rhs

!========= BLADE_ELEMENT_RHS =================================================80
!
! Computes the rotor forces using a fully coupled blade element theory.
!
!=============================================================================80

  subroutine blade_element_rhs(eqn_set,                                 &
                               i, nzone, nnodes0, nnodes01, qnode, n_tot)

    use lmpi,           only: lmpi_reduce, lmpi_bcast, lmpi_die
    use solution_types, only: compressible

    integer, intent(in) :: eqn_set
    integer, intent(in) :: i, nzone, n_tot
    integer,                                 intent(in) :: nnodes0, nnodes01
    real(dp), dimension(n_tot, nnodes01),    intent(in) :: qnode

    integer                  :: j, k, n, inode, izone

    real(dp)                 :: rho, u, v, w
    real(dp)                 :: azimuth, flap, lag
    real(dp)                 :: v_rot, v_flap, v_lag
    real(dp)                 :: v_norm, v_tan, v_total
    real(dp)                 :: phi, theta, alpha_e
    real(dp)                 :: cl, cd, dlift, ddrag
    real(dp)                 :: alpha_max, alpha_min, cla_stall
    real(dp)                 :: v2, v3, f1, f2, f3

    real(dp)                 :: pi, twopi, halfpi
    real(dp), parameter      :: my_0 = 0.0_dp
    real(dp), parameter      :: my_1 = 1.0_dp
    real(dp), parameter      :: my_2 = 2.0_dp

    integer                                    :: nr, nn, alloc_err
    real(dp), dimension(:,:,:), allocatable    :: fx_temp, fy_temp, fz_temp
    real(dp), dimension(:,:,:), allocatable    :: alpha_temp
    real(dp), dimension(:,:,:), allocatable    :: cl_temp, cd_temp

  continue

    pi = acos(-my_1)
    twopi = my_2*pi
    halfpi = pi/my_2

    alpha_max = rotor(i)%clmax / rotor(i)%cla + rotor(i)%a0
    alpha_min = rotor(i)%clmin / rotor(i)%cla + rotor(i)%a0
    cla_stall = (rotor(i)%clmax - rotor(i)%clmin) / (alpha_max - alpha_min - pi)

    rotor(i)%fx        = my_0
    rotor(i)%fy        = my_0
    rotor(i)%fz        = my_0
    rotor(i)%alpha_eff = my_0
    rotor(i)%cl_local  = my_0
    rotor(i)%cd_local  = my_0

    blade_loop: do izone = 1,nzone
      normal_loop: do k = 1,rotor(i)%nnormal
        if (rotor(i)%rottype == 2 .and. k > 1) cycle normal_loop

        call blade_angles(i, izone, k, azimuth, flap, lag)

        radial_loop: do j = 1,rotor(i)%nradial
          inode = rotor(i)%s2n(izone,j,k)
          check_on_partition: if ( inode <= nnodes0 ) then
            if ( eqn_set == compressible ) then
              rho = qnode(1,inode)
            else
              rho = my_1
            end if
            u = qnode(2,inode)   ! Primitive variables are assumed
            v = qnode(3,inode)
            w = qnode(4,inode)

!           Rotate the u,v,w velocity components into flap coordinate system
            call rotate_xyz2flap(i,u,v,w,azimuth,flap,lag)
!           v1 = u
            v2 = v
            v3 = w

!           Additional velocities due to blade motions
            v_rot  = abs(rotor(i)%omega) * rotor(i)%radius(j)

            v_flap = my_0
            do n = 1,rotor(i)%nbeta
              v_flap = v_flap + n*rotor(i)%beta(n*2)  *cos(n*azimuth) -        &
                                n*rotor(i)%beta(n*2+1)*sin(n*azimuth)
            end do
            v_flap = v_flap * abs(rotor(i)%omega) * rotor(i)%radius(j)

            v_lag  = my_0
            do n = 1,rotor(i)%ndelta
              v_lag = v_lag + n*rotor(i)%delta(n*2)  *cos(n*azimuth) -         &
                              n*rotor(i)%delta(n*2+1)*sin(n*azimuth)
            end do
            v_lag = v_lag * abs(rotor(i)%omega) * rotor(i)%radius(j)

!           Define the blade section velocities
            if (rotor(i)%omega >= my_0) then
              v_norm = -v3 + v_flap
              v_tan  = -v2 + v_rot - v_lag
!             v_rad  =  v1
            else
              v_norm = -v3 + v_flap
              v_tan  =  v2 + v_rot - v_lag
!             v_rad  =  v1
            end if
            v_total = sqrt(v_norm*v_norm + v_tan*v_tan)

!           Compute the inflow angle with respect to the ref frame
            phi = atan2(v_norm,v_tan)

!           Compute the effective angle of attack
!           ... due to controls
            theta = rotor(i)%theta0 + rotor(i)%theta1s * sin(azimuth) +        &
                                      rotor(i)%theta1c * cos(azimuth)
!           ... due to twist
            if (rotor(i)%have_twist_distribution) then
              theta = theta + rotor(i)%twist_distr(j)
            else
!             Note: thetatw is normalized by rtip in read_rotor_input
              theta = theta + rotor(i)%thetatw * rotor(i)%radius(j)
            end if
            alpha_e = theta - phi
            if ( alpha_e >   pi ) alpha_e = alpha_e - twopi
            if ( alpha_e <= -pi ) alpha_e = alpha_e + twopi
            if (abs(alpha_e) > pi) then
              write(*,*) "Unexpected alpha RHS: alpha_e,theta,phi,v_norm,v_tan"
              write(*,*) alpha_e, theta, phi, v_norm, v_tan
            end if

!           Compute the lift coefficient
            if ((alpha_e >= alpha_min).and.(alpha_e <= alpha_max)) then
              cl = rotor(i)%cla * (alpha_e - rotor(i)%a0)
            elseif (alpha_e >= alpha_min+pi) then
              cl = rotor(i)%cla * (alpha_e - pi - rotor(i)%a0)
            elseif (alpha_e <= alpha_max-pi) then
              cl = rotor(i)%cla * (alpha_e + pi - rotor(i)%a0)
            elseif ((alpha_e>alpha_max).and.(alpha_e<alpha_min+pi)) then
              cl = cla_stall*(alpha_e-alpha_max) + rotor(i)%clmax
            elseif ((alpha_e<alpha_min).and.(alpha_e>alpha_max-pi)) then
              cl = cla_stall*(alpha_e-alpha_max+pi) + rotor(i)%clmax
            else
              write(*,*) "ERROR: Invalid alpha_eff range for Cl"
              cl = 0.0_dp   ! to supress compiler warning
            end if

!           Compute the drag coefficient
            if ((alpha_e >= -halfpi).and.(alpha_e <= halfpi)) then
              cd = rotor(i)%cd0 + rotor(i)%cd1 * alpha_e +                     &
                                  rotor(i)%cd2 * alpha_e**my_2
            elseif (alpha_e > halfpi) then
              cd = rotor(i)%cd0 + rotor(i)%cd1 * (alpha_e-pi) +                &
                                  rotor(i)%cd2 * (alpha_e-pi)**my_2
            elseif (alpha_e < -halfpi) then
              cd = rotor(i)%cd0 + rotor(i)%cd1 * (alpha_e+pi) +                &
                                  rotor(i)%cd2 * (alpha_e+pi)**my_2
            else
              write(*,*) "ERROR: Invalid alpha_eff range for Cd"
              cd = 0.0_dp   ! to supress compiler warning
            end if

            if (cl > rotor(i)%clmax) cl = rotor(i)%clmax
            if (cl < rotor(i)%clmin) cl = rotor(i)%clmin
            if (cd > rotor(i)%cdmax) cd = rotor(i)%cdmax
            if (cd < rotor(i)%cdmin) cd = rotor(i)%cdmin

!           Compute blade section lift and drag
            dlift = rho*(v_total**my_2)/my_2 * cl * rotor(i)%chord *       &
                    rotor(i)%delrad(j)
            ddrag = rho*(v_total**my_2)/my_2 * cd * rotor(i)%chord *       &
                    rotor(i)%delrad(j)

!           Rotate into the reference frame aligned forces
            f1 = my_0
            f2 = my_0
            if (rotor(i)%swirl == 1) then
              if (rotor(i)%omega >= my_0) then
                f2 = -dlift * sin(phi) - ddrag * cos(phi)
              else
                f2 =  dlift * sin(phi) + ddrag * cos(phi)
              end if
            end if
            f3 =  dlift * cos(phi) - ddrag * sin(phi)

            if ( rotor(i)%rottype == 1 ) then
!             Forces are time averaged by [nblades * dpsi / (2*pi)]
!             dpsi = 2*pi / nnormal, so factor is nblades / nnormal
              if ( rotor(i)%swirl == 1 ) then
                f2 = (f2 * rotor(i)%nblade) / (my_1 * rotor(i)%nnormal)
              end if
              f3 = (f3 * rotor(i)%nblade) / (my_1 * rotor(i)%nnormal)
            else
!             Forces only apply to a fraction of the chord so we divide
!             by the number of chordwise sources
              if ( rotor(i)%swirl == 1 ) then
                f2 = f2 / (my_1 * rotor(i)%nnormal)
              end if
              f3 = f3 / (my_1 * rotor(i)%nnormal)
            end if

!           Rotate forces to the grid reference frame
            call rotate_flap2xyz(i,f1,f2,f3,azimuth,flap,lag)
            rotor(i)%fx(izone,j,k) = f1
            rotor(i)%fy(izone,j,k) = f2
            rotor(i)%fz(izone,j,k) = f3
            rotor(i)%alpha_eff(izone,j,k) = alpha_e
            rotor(i)%cl_local(izone,j,k) = cl
            rotor(i)%cd_local(izone,j,k) = cd

          end if check_on_partition
        end do radial_loop
      end do normal_loop
    end do blade_loop

!   Pass blade element force data to all processors

    nr = rotor(i)%nradial
    nn = rotor(i)%nnormal

    allocate(fx_temp(nzone,nr,nn), stat=alloc_err)
    if ( alloc_err /= 0 ) then
      write(*,*) "Failed to allocate fx_temp in blade_element_rhs"
      call lmpi_die
    end if
    allocate(fy_temp(nzone,nr,nn), stat=alloc_err)
    if ( alloc_err /= 0 ) then
      write(*,*) "Failed to allocate fy_temp in blade_element_rhs"
      call lmpi_die
    end if
    allocate(fz_temp(nzone,nr,nn), stat=alloc_err)
    if ( alloc_err /= 0 ) then
      write(*,*) "Failed to allocate fz_temp in blade_element_rhs"
      call lmpi_die
    end if
    allocate(alpha_temp(nzone,nr,nn), stat=alloc_err)
    if ( alloc_err /= 0 ) then
      write(*,*) "Failed to allocate alpha_temp in blade_element_rhs"
      call lmpi_die
    end if
    allocate(cl_temp(nzone,nr,nn), stat=alloc_err)
    if ( alloc_err /= 0 ) then
      write(*,*) "Failed to allocate cl_temp in blade_element_rhs"
      call lmpi_die
    end if
    allocate(cd_temp(nzone,nr,nn), stat=alloc_err)
    if ( alloc_err /= 0 ) then
      write(*,*) "Failed to allocate cd_temp in blade_element_rhs"
      call lmpi_die
    end if

    call lmpi_reduce(rotor(i)%fx, fx_temp)
    call lmpi_bcast(fx_temp)
    call lmpi_reduce(rotor(i)%fy, fy_temp)
    call lmpi_bcast(fy_temp)
    call lmpi_reduce(rotor(i)%fz, fz_temp)
    call lmpi_bcast(fz_temp)
    call lmpi_reduce(rotor(i)%alpha_eff, alpha_temp)
    call lmpi_bcast(alpha_temp)
    call lmpi_reduce(rotor(i)%cl_local, cl_temp)
    call lmpi_bcast(cl_temp)
    call lmpi_reduce(rotor(i)%cd_local, cd_temp)
    call lmpi_bcast(cd_temp)

    rotor(i)%fx = fx_temp
    rotor(i)%fy = fy_temp
    rotor(i)%fz = fz_temp
    rotor(i)%alpha_eff = alpha_temp
    rotor(i)%cl_local = cl_temp
    rotor(i)%cd_local = cd_temp

    deallocate(fx_temp)
    deallocate(fy_temp)
    deallocate(fz_temp)
    deallocate(alpha_temp)
    deallocate(cl_temp)
    deallocate(cd_temp)

!   Enforce constant load across source blades

    fix_actuator_blade: if (rotor(i)%rottype == 2) then
      do izone = 1,nzone
        do j = 1,rotor(i)%nradial
          if (rotor(i)%nnormal > 1) then
            do k = 2,rotor(i)%nnormal
              rotor(i)%fx(izone,j,k) = rotor(i)%fx(izone,j,1)
              rotor(i)%fy(izone,j,k) = rotor(i)%fy(izone,j,1)
              rotor(i)%fz(izone,j,k) = rotor(i)%fz(izone,j,1)
              rotor(i)%alpha_eff(izone,j,k) = rotor(i)%alpha_eff(izone,j,1)
              rotor(i)%cl_local(izone,j,k) = rotor(i)%cl_local(izone,j,1)
              rotor(i)%cd_local(izone,j,k) = rotor(i)%cd_local(izone,j,1)
            end do
          end if
        end do
      end do
    end if fix_actuator_blade

  end subroutine blade_element_rhs

!========= ROTOR_SOURCE_LHS ==================================================80
!
! Adds the rotor source contribution to the right hand side of the equations.
! Note: this routine assumes conserved variables as input
! Called from source.f90
!
!=============================================================================80

  subroutine rotor_source_lhs(eqn_set,nnodes0,nnodes01,adim,                   &
                              nfunctions,qnode,n_tot,njac,fill_a_diag,         &
                              fill_adjoint_res,g2m,a_diag,rlam,coltag,res)

    use lmpi,           only: lmpi_die
    use solution_types, only: compressible, incompressible

    integer, intent(in) :: eqn_set,nnodes0,nnodes01,n_tot,njac,adim
    integer, intent(in) :: nfunctions

    integer, dimension(:), intent(in) :: g2m

    real(dp), dimension(n_tot,nnodes01),               intent(in)    :: qnode
    real(dp), dimension(adim,nnodes01,nfunctions),  optional,                  &
                                                       intent(in)    :: rlam
    real(dp), dimension(adim,nnodes01),             optional,                  &
                                                       intent(in)    :: coltag
    real(dp), dimension(njac,njac,nnodes0),         optional,                  &
                                                       intent(inout) :: a_diag
    real(dp), dimension(adim,nnodes01,nfunctions),  optional,                  &
                                                       intent(inout) :: res

    logical, intent(in) :: fill_a_diag, fill_adjoint_res

    integer :: i, izone, j, k, inode, nzone, ifcn, row

    real(dp)    :: rho, u, v, w
    real(dp)    :: dudr,  dvdr,  dwdr
    real(dp)    :: dudru, dvdrv, dwdrw
    real(dp)    :: azimuth, flap, lag

    real(dp), dimension(3,4)    :: dfdq
    real(dp), dimension(5,5)    :: a

    real(dp), parameter    :: my_0 = 0.0_dp
    real(dp), parameter    :: my_1 = 1.0_dp

  continue

! Check that necessary arguments are present for operation requested

    if ( fill_a_diag ) then
      if ( .not. present( a_diag ) ) then
        write(*,*) 'fill_a_diag requested but a_diag is not present'
        call lmpi_die
      endif
    endif

    if ( fill_adjoint_res ) then
      if ( .not. present( rlam ) ) then
        write(*,*) 'fill_adjoint_res requested but rlam is not present'
        call lmpi_die
      endif
      if ( .not. present( coltag ) ) then
        write(*,*) 'fill_adjoint_res requested but coltag is not present'
        call lmpi_die
      endif
      if ( .not. present( res ) ) then
        write(*,*) 'fill_adjoint_res requested but res is not present'
        call lmpi_die
      endif
    endif

    a = 0.0_dp

    rotor_loop: do i = 1, nrotor

      nzone = 1
      if ( rotor(i)%rottype == 2 ) nzone = rotor(i)%nblade

      rotor_zone_loop: do izone = 1, nzone
        source_loop_normal: do k = 1, rotor(i)%nnormal

          if (rotor(i)%loadtype == 3) then
            call blade_angles(i, izone, k, azimuth, flap, lag)
          endif

          source_loop_radius: do j = 1, rotor(i)%nradial
            inode = rotor(i)%s2n(izone,j,k)
            source_on_partition: if ( inode <= nnodes0 ) then

              select case ( eqn_set )
                case (compressible)
                  rho = qnode(1,inode)
                  u   = qnode(2,inode) / rho
                  v   = qnode(3,inode) / rho
                  w   = qnode(4,inode) / rho
                case (incompressible)
                  rho = my_1
                  u   = qnode(2,inode)
                  v   = qnode(3,inode)
                  w   = qnode(4,inode)
                case default
                  write(*,'("ERROR: rotor_source_lhs: invalid eqn_set option")')
                  call lmpi_die
              end select

              dfdq = my_0

              if (rotor(i)%loadtype == 3) then
                call blade_element_lhs(eqn_set,i,j,azimuth,flap,lag,rho,u,v,w, &
                                       dfdq)
              end if

!             Note: dfdq index is not the same as a_diag index
!
!             df/dq = | d(fx)/dq1   d(fx)/dq2   d(fx)/dq3   d(fx)/dq4 |
!                     | d(fy)/dq1   d(fy)/dq2   d(fy)/dq3   d(fy)/dq4 |
!                     | d(fz)/dq1   d(fz)/dq2   d(fz)/dq3   d(fz)/dq4 |

              select case ( eqn_set )
                case (compressible)
                  dudr  =   -u / rho
                  dvdr  =   -v / rho
                  dwdr  =   -w / rho
                  dudru = my_1 / rho
                  dvdrv = my_1 / rho
                  dwdrw = my_1 / rho

                  a(2,1) = dfdq(1,1)
                  a(2,2) = dfdq(1,2)
                  a(2,3) = dfdq(1,3)
                  a(2,4) = dfdq(1,4)

                  a(3,1) = dfdq(2,1)
                  a(3,2) = dfdq(2,2)
                  a(3,3) = dfdq(2,3)
                  a(3,4) = dfdq(2,4)

                  a(4,1) = dfdq(3,1)
                  a(4,2) = dfdq(3,2)
                  a(4,3) = dfdq(3,3)
                  a(4,4) = dfdq(3,4)

                  a(5,1) = rotor(i)%fx(izone,j,k) * dudr +                     &
                           rotor(i)%fy(izone,j,k) * dvdr +                     &
                           rotor(i)%fz(izone,j,k) * dwdr +                     &
                           u*dfdq(1,1) + v*dfdq(2,1) + w*dfdq(3,1)
                  a(5,2) = rotor(i)%fx(izone,j,k) * dudru +                    &
                           u*dfdq(1,2) + v*dfdq(2,2) + w*dfdq(3,2)
                  a(5,3) = rotor(i)%fy(izone,j,k) * dvdrv +                    &
                           u*dfdq(1,3) + v*dfdq(2,3) + w*dfdq(3,3)
                  a(5,4) = rotor(i)%fz(izone,j,k) * dwdrw +                    &
                           u*dfdq(1,4) + v*dfdq(2,4) + w*dfdq(3,4)

!                 No contributions to the first row and last column of a_diag

                  if ( fill_a_diag ) then
                    row = g2m(inode)
                    a_diag(2,1,row) = a_diag(2,1,row) + a(2,1)
                    a_diag(2,2,row) = a_diag(2,2,row) + a(2,2)
                    a_diag(2,3,row) = a_diag(2,3,row) + a(2,3)
                    a_diag(2,4,row) = a_diag(2,4,row) + a(2,4)

                    a_diag(3,1,row) = a_diag(3,1,row) + a(3,1)
                    a_diag(3,2,row) = a_diag(3,2,row) + a(3,2)
                    a_diag(3,3,row) = a_diag(3,3,row) + a(3,3)
                    a_diag(3,4,row) = a_diag(3,4,row) + a(3,4)

                    a_diag(4,1,row) = a_diag(4,1,row) + a(4,1)
                    a_diag(4,2,row) = a_diag(4,2,row) + a(4,2)
                    a_diag(4,3,row) = a_diag(4,3,row) + a(4,3)
                    a_diag(4,4,row) = a_diag(4,4,row) + a(4,4)

                    a_diag(5,1,row) = a_diag(5,1,row) + a(5,1)
                    a_diag(5,2,row) = a_diag(5,2,row) + a(5,2)
                    a_diag(5,3,row) = a_diag(5,3,row) + a(5,3)
                    a_diag(5,4,row) = a_diag(5,4,row) + a(5,4)
                  endif

                  if ( fill_adjoint_res ) then
                    do ifcn = 1, nfunctions
                      res(1,inode,ifcn) = res(1,inode,ifcn)                    &
                                   + a(2,1)*coltag(2,inode)*rlam(2,inode,ifcn) &
                                   + a(3,1)*coltag(3,inode)*rlam(3,inode,ifcn) &
                                   + a(4,1)*coltag(4,inode)*rlam(4,inode,ifcn) &
                                   + a(5,1)*coltag(5,inode)*rlam(5,inode,ifcn)
                      res(2,inode,ifcn) = res(2,inode,ifcn)                    &
                                   + a(2,2)*coltag(2,inode)*rlam(2,inode,ifcn) &
                                   + a(3,2)*coltag(3,inode)*rlam(3,inode,ifcn) &
                                   + a(4,2)*coltag(4,inode)*rlam(4,inode,ifcn) &
                                   + a(5,2)*coltag(5,inode)*rlam(5,inode,ifcn)
                      res(3,inode,ifcn) = res(3,inode,ifcn)                    &
                                   + a(2,3)*coltag(2,inode)*rlam(2,inode,ifcn) &
                                   + a(3,3)*coltag(3,inode)*rlam(3,inode,ifcn) &
                                   + a(4,3)*coltag(4,inode)*rlam(4,inode,ifcn) &
                                   + a(5,3)*coltag(5,inode)*rlam(5,inode,ifcn)
                      res(4,inode,ifcn) = res(4,inode,ifcn)                    &
                                   + a(2,4)*coltag(2,inode)*rlam(2,inode,ifcn) &
                                   + a(3,4)*coltag(3,inode)*rlam(3,inode,ifcn) &
                                   + a(4,4)*coltag(4,inode)*rlam(4,inode,ifcn) &
                                   + a(5,4)*coltag(5,inode)*rlam(5,inode,ifcn)
                    end do
                  endif

                case (incompressible)

!                 No contributions to the first row and first column of a_diag

                  a(2,2) = dfdq(1,2)
                  a(2,3) = dfdq(1,3)
                  a(2,4) = dfdq(1,4)

                  a(3,2) = dfdq(2,2)
                  a(3,3) = dfdq(2,3)
                  a(3,4) = dfdq(2,4)

                  a(4,2) = dfdq(3,2)
                  a(4,3) = dfdq(3,3)
                  a(4,4) = dfdq(3,4)

                  if ( fill_a_diag ) then
                    row = g2m(inode)
                    a_diag(2,2,row) = a_diag(2,2,row) + a(2,2)
                    a_diag(2,3,row) = a_diag(2,3,row) + a(2,3)
                    a_diag(2,4,row) = a_diag(2,4,row) + a(2,4)

                    a_diag(3,2,row) = a_diag(3,2,row) + a(3,2)
                    a_diag(3,3,row) = a_diag(3,3,row) + a(3,3)
                    a_diag(3,4,row) = a_diag(3,4,row) + a(3,4)

                    a_diag(4,2,row) = a_diag(4,2,row) + a(4,2)
                    a_diag(4,3,row) = a_diag(4,3,row) + a(4,3)
                    a_diag(4,4,row) = a_diag(4,4,row) + a(4,4)
                  endif

                  if ( fill_adjoint_res ) then
                    do ifcn = 1, nfunctions
                      res(2,inode,ifcn) = res(2,inode,ifcn)                    &
                                   + a(2,2)*coltag(2,inode)*rlam(2,inode,ifcn) &
                                   + a(3,2)*coltag(3,inode)*rlam(3,inode,ifcn) &
                                   + a(4,2)*coltag(4,inode)*rlam(4,inode,ifcn)
                      res(3,inode,ifcn) = res(3,inode,ifcn)                    &
                                   + a(2,3)*coltag(2,inode)*rlam(2,inode,ifcn) &
                                   + a(3,3)*coltag(3,inode)*rlam(3,inode,ifcn) &
                                   + a(4,3)*coltag(4,inode)*rlam(4,inode,ifcn)
                      res(4,inode,ifcn) = res(4,inode,ifcn)                    &
                                   + a(2,4)*coltag(2,inode)*rlam(2,inode,ifcn) &
                                   + a(3,4)*coltag(3,inode)*rlam(3,inode,ifcn) &
                                   + a(4,4)*coltag(4,inode)*rlam(4,inode,ifcn)
                    end do
                  endif

                case default
                  write(*,'("ERROR: rotor_source_lhs: invalid eqn_set option")')
                  call lmpi_die
              end select

            end if source_on_partition
          end do source_loop_radius
        end do source_loop_normal
      end do rotor_zone_loop
    end do rotor_loop

  end subroutine rotor_source_lhs

!========= BLADE_ELEMENT_LHS =================================================80
!
! Computes the jacobian of the rotor forces. This routine is necessary to
! fully couple the rotor source terms to the solver.
!
!=============================================================================80

  subroutine blade_element_lhs(eqn_set,i,j,azimuth,flap,lag,rho,u,v,w,dfdq)

    use solution_types, only: compressible

    integer,                  intent(in ) :: eqn_set
    integer,                  intent(in ) :: i, j
    real(dp),                 intent(in ) :: azimuth, flap, lag
    real(dp),                 intent(in ) :: rho, u, v, w
    real(dp), dimension(3,4), intent(out) :: dfdq

    integer                  :: n
    real(dp)                 :: v_rot, v_flap, v_lag
    real(dp)                 :: v_norm, v_tan, v_tot2
    real(dp)                 :: phi, theta, alpha_e
    real(dp)                 :: cl, cd, dlift, ddrag
    real(dp)                 :: alpha_max, alpha_min, cla_stall
    real(dp)                 :: v1, v2, v3
    real(dp)                 :: ffac

    real(dp)                 :: drdq1, drdq2, drdq3, drdq4
    real(dp)                 :: dudq1, dudq2, dudq3, dudq4
    real(dp)                 :: dvdq1, dvdq2, dvdq3, dvdq4
    real(dp)                 :: dwdq1, dwdq2, dwdq3, dwdq4

    real(dp)                 :: dv2dq1, dv2dq2, dv2dq3, dv2dq4
    real(dp)                 :: dv3dq1, dv3dq2, dv3dq3, dv3dq4

    real(dp)                 :: dvndq1, dvndq2, dvndq3, dvndq4
    real(dp)                 :: dvtdq1, dvtdq2, dvtdq3, dvtdq4
    real(dp)                 :: dvtot2dq1, dvtot2dq2, dvtot2dq3, dvtot2dq4

    real(dp)                 :: dphidq1, dphidq2, dphidq3, dphidq4
    real(dp)                 :: dadq1, dadq2, dadq3, dadq4

    real(dp)                 :: dclda, dcdda
    real(dp)                 :: dcldq1, dcldq2, dcldq3, dcldq4
    real(dp)                 :: dcddq1, dcddq2, dcddq3, dcddq4

    real(dp)                 :: dliftdq1, dliftdq2, dliftdq3, dliftdq4
    real(dp)                 :: ddragdq1, ddragdq2, ddragdq3, ddragdq4

    real(dp)                 :: df1dq1, df1dq2, df1dq3, df1dq4
    real(dp)                 :: df2dq1, df2dq2, df2dq3, df2dq4
    real(dp)                 :: df3dq1, df3dq2, df3dq3, df3dq4

    real(dp)                 :: pi, twopi, halfpi
    real(dp), parameter      :: my_0    = 0.0_dp
    real(dp), parameter      :: my_1    = 1.0_dp
    real(dp), parameter      :: my_2    = 2.0_dp
    real(dp), parameter      :: tol     = 1.0E-6_dp

  continue

    pi = acos(-my_1)
    twopi = my_2*pi
    halfpi = pi/my_2

    alpha_max = rotor(i)%clmax / rotor(i)%cla + rotor(i)%a0
    alpha_min = rotor(i)%clmin / rotor(i)%cla + rotor(i)%a0
    cla_stall = (rotor(i)%clmax - rotor(i)%clmin) / (alpha_max - alpha_min - pi)

    if ( eqn_set == compressible ) then
      drdq1 = my_1      ! d(rho)/d(q)
      drdq2 = my_0
      drdq3 = my_0
      drdq4 = my_0
      dudq1 = -u/rho    ! d(u)/d(q)
      dudq2 = my_1/rho
      dudq3 = my_0
      dudq4 = my_0
      dvdq1 = -v/rho    ! d(v)/d(q)
      dvdq2 = my_0
      dvdq3 = my_1/rho
      dvdq4 = my_0
      dwdq1 = -w/rho    ! d(w)/d(q)
      dwdq2 = my_0
      dwdq3 = my_0
      dwdq4 = my_1/rho
    else
      drdq1 = my_0      ! d(rho)/d(q) = 0 since density is constant
      drdq2 = my_0
      drdq3 = my_0
      drdq4 = my_0
      dudq1 = my_0      ! d(u)/d(q)
      dudq2 = my_1
      dudq3 = my_0
      dudq4 = my_0
      dvdq1 = my_0      ! d(v)/d(q)
      dvdq2 = my_0
      dvdq3 = my_1
      dvdq4 = my_0
      dwdq1 = my_0      ! d(w)/d(q)
      dwdq2 = my_0
      dwdq3 = my_0
      dwdq4 = my_1
    end if

!   Rotate the u,v,w velocity components into flap coordinate system
    v1 = u
    v2 = v
    v3 = w
    call rotate_xyz2flap(i,v1,v2,v3,azimuth,flap,lag)

!   Get derivatives of flap coordinate velocities
!   This only works because the rotations do not depend on the flow conditions
    call rotate_xyz2flap(i,dudq1,dvdq1,dwdq1,azimuth,flap,lag)
!   dv1dq1 = dudq1
    dv2dq1 = dvdq1
    dv3dq1 = dwdq1
    call rotate_xyz2flap(i,dudq2,dvdq2,dwdq2,azimuth,flap,lag)
!   dv1dq2 = dudq2
    dv2dq2 = dvdq2
    dv3dq2 = dwdq2
    call rotate_xyz2flap(i,dudq3,dvdq3,dwdq3,azimuth,flap,lag)
!   dv1dq3 = dudq3
    dv2dq3 = dvdq3
    dv3dq3 = dwdq3
    call rotate_xyz2flap(i,dudq4,dvdq4,dwdq4,azimuth,flap,lag)
!   dv1dq4 = dudq4
    dv2dq4 = dvdq4
    dv3dq4 = dwdq4

!   Additional velocities due to blade motions
    v_rot  = abs(rotor(i)%omega) * rotor(i)%radius(j)

    v_flap = my_0
    do n = 1,rotor(i)%nbeta
      v_flap = v_flap + n*rotor(i)%beta(n*2)  *cos(n*azimuth) -                &
                        n*rotor(i)%beta(n*2+1)*sin(n*azimuth)
    end do
    v_flap = v_flap * abs(rotor(i)%omega) * rotor(i)%radius(j)

    v_lag  = my_0
    do n = 1,rotor(i)%ndelta
      v_lag = v_lag + n*rotor(i)%delta(n*2)  *cos(n*azimuth) -                 &
                      n*rotor(i)%delta(n*2+1)*sin(n*azimuth)
    end do
    v_lag = v_lag * abs(rotor(i)%omega) * rotor(i)%radius(j)

!   Define the blade section velocities
    if (rotor(i)%omega >= my_0) then
      v_norm = -v3 + v_flap
      v_tan  = -v2 + v_rot - v_lag
!     v_rad  =  v1
    else
      v_norm = -v3 + v_flap
      v_tan  =  v2 + v_rot - v_lag
!     v_rad  =  v1
    end if
    v_tot2 = v_norm*v_norm + v_tan*v_tan

!   Blade section velocity derivatives
    dvndq1 = -dv3dq1
    dvndq2 = -dv3dq2
    dvndq3 = -dv3dq3
    dvndq4 = -dv3dq4

    if (rotor(i)%omega >= my_0) then
      dvtdq1 = -dv2dq1
      dvtdq2 = -dv2dq2
      dvtdq3 = -dv2dq3
      dvtdq4 = -dv2dq4
    else
      dvtdq1 =  dv2dq1
      dvtdq2 =  dv2dq2
      dvtdq3 =  dv2dq3
      dvtdq4 =  dv2dq4
    end if

!   dvrdq1 = dv1dq1
!   dvrdq2 = dv1dq2
!   dvrdq3 = dv1dq3
!   dvrdq4 = dv1dq4

    dvtot2dq1 = my_2*(v_norm*dvndq1 + v_tan*dvtdq1)
    dvtot2dq2 = my_2*(v_norm*dvndq2 + v_tan*dvtdq2)
    dvtot2dq3 = my_2*(v_norm*dvndq3 + v_tan*dvtdq3)
    dvtot2dq4 = my_2*(v_norm*dvndq4 + v_tan*dvtdq4)

!   Compute the inflow angle with respect to the ref frame
    phi = atan2(v_norm,v_tan)

    if (abs(v_tot2) >= tol) then
      dphidq1 = (v_tan*dvndq1 - v_norm*dvtdq1) / v_tot2
      dphidq2 = (v_tan*dvndq2 - v_norm*dvtdq2) / v_tot2
      dphidq3 = (v_tan*dvndq3 - v_norm*dvtdq3) / v_tot2
      dphidq4 = (v_tan*dvndq4 - v_norm*dvtdq4) / v_tot2
    else
!     If v_total2 is close to zero define phi as zero, d(atan(0))/dq = 1
      dphidq1 = my_1
      dphidq2 = my_1
      dphidq3 = my_1
      dphidq4 = my_1
    end if

!   Compute the effective angle of attack
!   ... due to controls
    theta = rotor(i)%theta0 + rotor(i)%theta1s * sin(azimuth) +                &
                              rotor(i)%theta1c * cos(azimuth)
!   ... due to twist
    if (rotor(i)%have_twist_distribution) then
      theta = theta + rotor(i)%twist_distr(j)
    else
!     Note: thetatw is normalized by rtip in read_rotor_input
      theta = theta + rotor(i)%thetatw * rotor(i)%radius(j)
    end if
    alpha_e = theta - phi
    if ( alpha_e >   pi ) alpha_e = alpha_e - twopi
    if ( alpha_e <= -pi ) alpha_e = alpha_e + twopi
    if (abs(alpha_e) > pi) then
      write(*,*) "Unexpected alpha LHS: alpha_e,theta,phi,v_norm,v_tan"
      write(*,*) alpha_e, theta, phi, v_norm, v_tan
    end if

    dadq1 = -dphidq1
    dadq2 = -dphidq2
    dadq3 = -dphidq3
    dadq4 = -dphidq4

!   Compute the lift coefficient
    if ((alpha_e >= alpha_min).and.(alpha_e <= alpha_max)) then
      cl = rotor(i)%cla * (alpha_e - rotor(i)%a0)
      dclda = rotor(i)%cla
    elseif (alpha_e >= alpha_min+pi) then
      cl = rotor(i)%cla * (alpha_e - pi - rotor(i)%a0)
      dclda = rotor(i)%cla
    elseif (alpha_e <= alpha_max-pi) then
      cl = rotor(i)%cla * (alpha_e + pi - rotor(i)%a0)
      dclda = rotor(i)%cla
    elseif ((alpha_e>alpha_max).and.(alpha_e<alpha_min+pi)) then
      cl = cla_stall*(alpha_e-alpha_max) + rotor(i)%clmax
      dclda = cla_stall
    elseif ((alpha_e<alpha_min).and.(alpha_e>alpha_max-pi)) then
      cl = cla_stall*(alpha_e-alpha_max+pi) + rotor(i)%clmax
      dclda = cla_stall
    else
      write(*,*) "ERROR: Invalid alpha_eff range for Cl"
      cl    = 0.0_dp   ! to supress compiler warning
      dclda = 0.0_dp
    end if

!   Compute the drag coefficient
    if ((alpha_e >= -halfpi).and.(alpha_e <= halfpi)) then
      cd = rotor(i)%cd0 + rotor(i)%cd1 * alpha_e + rotor(i)%cd2 * alpha_e**my_2
      dcdda = rotor(i)%cd1 + my_2*rotor(i)%cd2*alpha_e
    elseif (alpha_e > halfpi) then
      cd = rotor(i)%cd0 + rotor(i)%cd1*(alpha_e-pi) +                          &
                          rotor(i)%cd2*(alpha_e-pi)**my_2
      dcdda = rotor(i)%cd1 + my_2*rotor(i)%cd2*(alpha_e-pi)
    elseif (alpha_e < -halfpi) then
      cd = rotor(i)%cd0 + rotor(i)%cd1*(alpha_e+pi) +                          &
                          rotor(i)%cd2*(alpha_e+pi)**my_2
      dcdda = rotor(i)%cd1 + my_2*rotor(i)%cd2*(alpha_e+pi)
    else
      write(*,*) "ERROR: Invalid alpha_eff range for Cd"
      cd    = 0.0_dp   ! to supress compiler warning
      dcdda = 0.0_dp
    end if

    if (cl > rotor(i)%clmax) then
      cl = rotor(i)%clmax
      dclda = my_0
    end if
    if (cl < rotor(i)%clmin) then
      cl = rotor(i)%clmin
      dclda = my_0
    end if
    if (cd > rotor(i)%cdmax) then
      cd = rotor(i)%cdmax
      dcdda = my_0
    end if
    if (cd < rotor(i)%cdmin) then
      cd = rotor(i)%cdmin
      dcdda = my_0
    end if

!   Derivatives of the lift and drag coefficient
    dcldq1 = dclda * dadq1
    dcldq2 = dclda * dadq2
    dcldq3 = dclda * dadq3
    dcldq4 = dclda * dadq4

    dcddq1 = dcdda * dadq1
    dcddq2 = dcdda * dadq2
    dcddq3 = dcdda * dadq3
    dcddq4 = dcdda * dadq4

!   Compute blade section lift and drag
    dlift = rho*v_tot2/my_2 * cl * rotor(i)%chord * rotor(i)%delrad(j)
    ddrag = rho*v_tot2/my_2 * cd * rotor(i)%chord * rotor(i)%delrad(j)

!   Compute the derivatives of the lift and drag
    dliftdq1 = ((rotor(i)%chord * rotor(i)%delrad(j)) / my_2) *                &
               (drdq1*v_tot2*cl + rho*(dvtot2dq1*cl + v_tot2*dcldq1))
    dliftdq2 = ((rotor(i)%chord * rotor(i)%delrad(j)) / my_2) *                &
               (drdq2*v_tot2*cl + rho*(dvtot2dq2*cl + v_tot2*dcldq2))
    dliftdq3 = ((rotor(i)%chord * rotor(i)%delrad(j)) / my_2) *                &
               (drdq3*v_tot2*cl + rho*(dvtot2dq3*cl + v_tot2*dcldq3))
    dliftdq4 = ((rotor(i)%chord * rotor(i)%delrad(j)) / my_2) *                &
               (drdq4*v_tot2*cl + rho*(dvtot2dq4*cl + v_tot2*dcldq4))
    ddragdq1 = ((rotor(i)%chord * rotor(i)%delrad(j)) / my_2) *                &
               (drdq1*v_tot2*cd + rho*(dvtot2dq1*cd + v_tot2*dcddq1))
    ddragdq2 = ((rotor(i)%chord * rotor(i)%delrad(j)) / my_2) *                &
               (drdq2*v_tot2*cd + rho*(dvtot2dq2*cd + v_tot2*dcddq2))
    ddragdq3 = ((rotor(i)%chord * rotor(i)%delrad(j)) / my_2) *                &
               (drdq3*v_tot2*cd + rho*(dvtot2dq3*cd + v_tot2*dcddq3))
    ddragdq4 = ((rotor(i)%chord * rotor(i)%delrad(j)) / my_2) *                &
               (drdq4*v_tot2*cd + rho*(dvtot2dq4*cd + v_tot2*dcddq4))

    df1dq1 = my_0
    df1dq2 = my_0
    df1dq3 = my_0
    df1dq4 = my_0

    df2dq1 = my_0
    df2dq2 = my_0
    df2dq3 = my_0
    df2dq4 = my_0
    if ( rotor(i)%swirl == 1 ) then
      if (rotor(i)%omega >= my_0) then
        df2dq1 = -dliftdq1*sin(phi) - ddragdq1*cos(phi)  +                     &
                (-dlift   *cos(phi) + ddrag   *sin(phi)) * dphidq1
        df2dq2 = -dliftdq2*sin(phi) - ddragdq2*cos(phi)  +                     &
                (-dlift   *cos(phi) + ddrag   *sin(phi)) * dphidq2
        df2dq3 = -dliftdq3*sin(phi) - ddragdq3*cos(phi)  +                     &
                (-dlift   *cos(phi) + ddrag   *sin(phi)) * dphidq3
        df2dq4 = -dliftdq4*sin(phi) - ddragdq4*cos(phi)  +                     &
                (-dlift   *cos(phi) + ddrag   *sin(phi)) * dphidq4
      else
        df2dq1 =  dliftdq1*sin(phi) + ddragdq1*cos(phi)  +                     &
                ( dlift   *cos(phi) - ddrag   *sin(phi)) * dphidq1
        df2dq2 =  dliftdq2*sin(phi) + ddragdq2*cos(phi)  +                     &
                ( dlift   *cos(phi) - ddrag   *sin(phi)) * dphidq2
        df2dq3 =  dliftdq3*sin(phi) + ddragdq3*cos(phi)  +                     &
                ( dlift   *cos(phi) - ddrag   *sin(phi)) * dphidq3
        df2dq4 =  dliftdq4*sin(phi) + ddragdq4*cos(phi)  +                     &
                ( dlift   *cos(phi) - ddrag   *sin(phi)) * dphidq4
      end if
    end if

    df3dq1 =  dliftdq1*cos(phi) - ddragdq1*sin(phi)  +                         &
            (-dlift   *sin(phi) - ddrag   *cos(phi)) * dphidq1
    df3dq2 =  dliftdq2*cos(phi) - ddragdq2*sin(phi)  +                         &
            (-dlift   *sin(phi) - ddrag   *cos(phi)) * dphidq2
    df3dq3 =  dliftdq3*cos(phi) - ddragdq3*sin(phi)  +                         &
            (-dlift   *sin(phi) - ddrag   *cos(phi)) * dphidq3
    df3dq4 =  dliftdq4*cos(phi) - ddragdq4*sin(phi)  +                         &
            (-dlift   *sin(phi) - ddrag   *cos(phi)) * dphidq4

    if ( rotor(i)%rottype == 1 ) then
!     Forces are time averaged by [nblades * dpsi / (2*pi)]
!     dpsi = 2*pi / nnormal, so factor is nblades / nnormal
      ffac = (my_1 * rotor(i)%nblade) / (my_1 * rotor(i)%nnormal)
    else
!     Forces only apply to a fraction of the chord so we divide
!     by the number of chordwise sources
      ffac = my_1 / (my_1 * rotor(i)%nnormal)
    end if

    df2dq1 = df2dq1 * ffac
    df2dq2 = df2dq2 * ffac
    df2dq3 = df2dq3 * ffac
    df2dq4 = df2dq4 * ffac
    df3dq1 = df3dq1 * ffac
    df3dq2 = df3dq2 * ffac
    df3dq3 = df3dq3 * ffac
    df3dq4 = df3dq4 * ffac

!   Rotate derivatives back to XYZ coordinate system
!   Once again, this only works because the rotations are independent of flow
    call rotate_flap2xyz(i,df1dq1,df2dq1,df3dq1,azimuth,flap,lag)
    dfdq(1,1) = df1dq1
    dfdq(2,1) = df2dq1
    dfdq(3,1) = df3dq1
    call rotate_flap2xyz(i,df1dq2,df2dq2,df3dq2,azimuth,flap,lag)
    dfdq(1,2) = df1dq2
    dfdq(2,2) = df2dq2
    dfdq(3,2) = df3dq2
    call rotate_flap2xyz(i,df1dq3,df2dq3,df3dq3,azimuth,flap,lag)
    dfdq(1,3) = df1dq3
    dfdq(2,3) = df2dq3
    dfdq(3,3) = df3dq3
    call rotate_flap2xyz(i,df1dq4,df2dq4,df3dq4,azimuth,flap,lag)
    dfdq(1,4) = df1dq4
    dfdq(2,4) = df2dq4
    dfdq(3,4) = df3dq4

  end subroutine blade_element_lhs

!=========================== GET_ROTOR_ID_FROM_REF_FRAME =====================80
!
! Finds the rotor number and blade number given the ref frame id (body id)
!
!=============================================================================80

  subroutine get_rotor_id_from_ref_frame(frame_id, rotor_id, blade_id)

    use lmpi,                only : lmpi_master, lmpi_die
    use system_extensions,   only : se_flush

    integer,                intent(in)  :: frame_id
    integer,                intent(out) :: rotor_id, blade_id

    integer                             :: body, blade, ierr, rn

  continue

!   search the bodies attached to each rotor until we find a match

    ierr = 1

    rotor_id = 0
    blade_id = 0

    body = 0

    rotor_loop : do rn = 1, nrotor

      blade_loop : do blade = 1, rotor(rn)%nblade
        body = body + 1
        if (body == frame_id) then
          rotor_id = rn
          blade_id = blade
          ierr = 0
          exit rotor_loop
        end if
      end do blade_loop

    end do rotor_loop

    if (ierr /= 0 .and. lmpi_master) then
      write(*,*) 'Error in get_rotor_id_from_body_name: ',                     &
                 'could not find rotor id for body id', frame_id
      call se_flush()
      call lmpi_die
    end if

  end subroutine get_rotor_id_from_ref_frame

!================================= SET_DUMMY_ROTOR ===========================80
!
! Sets up a dummy rotor type of dimension 1 (1 rotor) for safety
!
!=============================================================================80

  subroutine set_dummy_rotor()

  continue

    allocate( rotor(1) )

    rotor(1)%rottype      = 0
    rotor(1)%loadtype     = 0
    rotor(1)%nradial      = 0
    rotor(1)%nnormal      = 0
    rotor(1)%swirl        = 0
    rotor(1)%tipfac       = 0.0_dp
    rotor(1)%x0           = 0.0_dp
    rotor(1)%y0           = 0.0_dp
    rotor(1)%z0           = 0.0_dp
    rotor(1)%vt_ratio     = 1.0_dp
    rotor(1)%omega        = 0.0_dp
    rotor(1)%ct           = 0.0_dp
    rotor(1)%cq           = 0.0_dp
    rotor(1)%psi0         = 0.0_dp
    rotor(1)%psi1         = 0.0_dp
    rotor(1)%rot2xyz(:,:) = 0.0_dp
    rotor(1)%rot2xyz(1,1) = 1.0_dp
    rotor(1)%rot2xyz(2,2) = 1.0_dp
    rotor(1)%rot2xyz(3,3) = 1.0_dp
    rotor(1)%alpha0       = 0.0_dp
    rotor(1)%dirrot       = 0
    rotor(1)%nblade       = 0
    rotor(1)%rtip         = 1.0_dp
    rotor(1)%rroot        = 0.0_dp
    rotor(1)%chord        = 1.0_dp
    rotor(1)%rfh          = 0.0_dp
    rotor(1)%rlh          = 0.0_dp
    rotor(1)%rph          = 0.0_dp
    rotor(1)%cla          = 0.0_dp
    rotor(1)%a0           = 0.0_dp
    rotor(1)%cd0          = 0.0_dp
    rotor(1)%cd1          = 0.0_dp
    rotor(1)%cd2          = 0.0_dp
    rotor(1)%clmax        = 0.0_dp
    rotor(1)%clmin        = 0.0_dp
    rotor(1)%cdmax        = 0.0_dp
    rotor(1)%cdmin        = 0.0_dp
    rotor(1)%theta0       = 0.0_dp
    rotor(1)%thetatw      = 0.0_dp
    rotor(1)%theta1s      = 0.0_dp
    rotor(1)%theta1c      = 0.0_dp
    rotor(1)%have_twist_distribution = .false.
    nullify(rotor(1)%twist_distr)
    rotor(1)%nbeta        = 0
    rotor(1)%beta(:)      = 0.0_dp
    rotor(1)%ndelta       = 0
    rotor(1)%delta(:)     = 0.0_dp
    nullify(rotor(1)%radius)
    nullify(rotor(1)%delrad)
    nullify(rotor(1)%area)
    nullify(rotor(1)%psi)
    nullify(rotor(1)%x_sg)
    nullify(rotor(1)%y_sg)
    nullify(rotor(1)%z_sg)
    nullify(rotor(1)%x_s)
    nullify(rotor(1)%y_s)
    nullify(rotor(1)%z_s)
    nullify(rotor(1)%fx)
    nullify(rotor(1)%fy)
    nullify(rotor(1)%fz)
    nullify(rotor(1)%alpha_eff)
    nullify(rotor(1)%cl_local)
    nullify(rotor(1)%cd_local)
    nullify(rotor(1)%s2n)
    nullify(rotor(1)%x_s_ref)
    nullify(rotor(1)%y_s_ref)
    nullify(rotor(1)%z_s_ref)

  end subroutine set_dummy_rotor

end  module rotors

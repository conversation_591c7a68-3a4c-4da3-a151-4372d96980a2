!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite sort

integer, parameter :: dp = selected_real_kind(P=15)

test set_up_lookup_1_item
  integer, dimension(1) :: l2g, sorted_global, sorted_local
  l2g(1) = 10
  call set_up_lookup(1, l2g, sorted_global, sorted_local)
  assert_equal(10,sorted_global(1))
  assert_equal(1,sorted_local(1))
end test

test set_up_lookup_2_item
  integer, dimension(2) :: l2g, sorted_global, sorted_local
  l2g(1) = 10
  l2g(2) = 20
  call set_up_lookup(2, l2g, sorted_global, sorted_local)
  assert_equal(10,sorted_global(1))
  assert_equal(20,sorted_global(2))
  assert_equal(1,sorted_local(1))
  assert_equal(2,sorted_local(2))
  l2g(1) = 20
  l2g(2) = 10
  call set_up_lookup(2, l2g, sorted_global, sorted_local)
  assert_equal(10,sorted_global(1))
  assert_equal(20,sorted_global(2))
  assert_equal(2,sorted_local(1))
  assert_equal(1,sorted_local(2))
end test

test lookup_1_item
  integer, dimension(1) :: sorted_global, sorted_local
  sorted_global(1) = 10
  sorted_local(1)  =  1
  assert_equal(0,lookup( 9,1,sorted_global,sorted_local))
  assert_equal(1,lookup(10,1,sorted_global,sorted_local))
end test

test lookup_2_item
  integer, dimension(2) :: sorted_global, sorted_local
  sorted_global(1) = 10
  sorted_global(2) = 20
  sorted_local(1)  =  2
  sorted_local(2)  =  1
  assert_equal(2,lookup(10,2,sorted_global,sorted_local))
  assert_equal(1,lookup(20,2,sorted_global,sorted_local))
end test

test binary_search_1_item
  integer, dimension(1) :: list
  list(1) = 10
  assert_equal(0,binary_search(1,list, 9))
  assert_equal(1,binary_search(1,list,10))
  assert_equal(0,binary_search(1,list,11))
end test

test binary_search_2_item
  integer, dimension(2) :: list
  list(1) = 10
  list(2) = 20
  assert_equal(1,binary_search(2,list,10))
  assert_equal(2,binary_search(2,list,20))
end test

test binary_search_3_item
  integer, dimension(3) :: list
  list(1) = 10
  list(2) = 20
  list(3) = 30
  assert_equal(1,binary_search(3,list,10))
  assert_equal(0,binary_search(3,list,15))
  assert_equal(2,binary_search(3,list,20))
  assert_equal(0,binary_search(3,list,25))
  assert_equal(3,binary_search(3,list,30))
end test

test binary_search_4_item
  integer, dimension(4) :: list
  list(1) = 10
  list(2) = 20
  list(3) = 30
  list(4) = 40
  assert_equal(1,binary_search(4,list,10))
  assert_equal(2,binary_search(4,list,20))
  assert_equal(3,binary_search(4,list,30))
  assert_equal(4,binary_search(4,list,40))
end test

test selection_1
  integer, parameter :: length = 1
  real(dp),dimension(length) :: items = (/ 3.0_dp/)
  integer :: position
  real(dp)::item

  position = 1
  call selection( length, items, position, item )
  assert_equal(3.0_dp, item )

end test

test selection_2
  integer, parameter :: length = 2
  real(dp),dimension(length) :: items = (/ 3.0_dp, 2.0_dp/)
  integer :: position
  real(dp)::item

  position = 1
  call selection( length, items, position, item )
  assert_equal(2.0_dp, item )

  position = 2
  call selection( length, items, position, item )
  assert_equal(3.0_dp, item )

end test

test selection_2s
  integer, parameter :: length = 2
  real(dp),dimension(length) :: items = (/ 2.0_dp, 3.0_dp/)
  integer :: position
  real(dp)::item

  position = 1
  call selection( length, items, position, item )
  assert_equal(2.0_dp, item )

  position = 2
  call selection( length, items, position, item )
  assert_equal(3.0_dp, item )

end test

test selection_3
  integer, parameter :: length = 3
  real(dp),dimension(length) :: items = (/ 2.0_dp, 1.0_dp, 3.0_dp/)
  integer :: position
  real(dp)::item

  position = 1
  call selection( length, items, position, item )
  assert_equal(1.0_dp, item )

  position = 2
  call selection( length, items, position, item )
  assert_equal(2.0_dp, item )

  position = 3
  call selection( length, items, position, item )
  assert_equal(3.0_dp, item )

end test

test selection_4
  integer, parameter :: length = 4
  real(dp),dimension(length) :: items = (/ 4.0_dp, 3.0_dp, 2.0_dp, 1.0_dp/)
  integer :: position
  real(dp)::item

  position = 1
  call selection( length, items, position, item )
  assert_equal(1.0_dp, item )

  position = 2
  call selection( length, items, position, item )
  assert_equal(2.0_dp, item )

  position = 3
  call selection( length, items, position, item )
  assert_equal(3.0_dp, item )

  position = 4
  call selection( length, items, position, item )
  assert_equal(4.0_dp, item )

end test

test selection_4a
  integer, parameter :: length = 4
  real(dp),dimension(length) :: items = (/ 4.0_dp, 4.0_dp, 4.0_dp, 4.0_dp/)
  integer :: position
  real(dp)::item

  position = 1
  call selection( length, items, position, item )
  assert_equal(4.0_dp, item )

  position = 2
  call selection( length, items, position, item )
  assert_equal(4.0_dp, item )

  position = 3
  call selection( length, items, position, item )
  assert_equal(4.0_dp, item )

  position = 4
  call selection( length, items, position, item )
  assert_equal(4.0_dp, item )

end test

test selection_4s
  integer, parameter :: length = 4
  real(dp),dimension(length) :: items = (/ 4.0_dp, 2.0_dp, 2.0_dp, 1.0_dp/)
  integer :: position
  real(dp)::item

  position = 1
  call selection( length, items, position, item )
  assert_equal(1.0_dp, item )

  position = 2
  call selection( length, items, position, item )
  assert_equal(2.0_dp, item )

  position = 3
  call selection( length, items, position, item )
  assert_equal(2.0_dp, item )

  position = 4
  call selection( length, items, position, item )
  assert_equal(4.0_dp, item )

end test

test reverse_1
  integer, parameter :: length = 1
  integer, dimension(length) :: items = (/ 1 /)
  call reverse( length, items)
  assert_equal( 1, items(1) )
end test
test reverse_2
  integer, parameter :: length = 2
  integer, dimension(length) :: items = (/ 2, 1 /)
  call reverse( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
end test
test reverse_3
  integer, parameter :: length = 3
  integer, dimension(length) :: items = (/ 3, 2, 1 /)
  call reverse( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
  assert_equal( 3, items(3) )
end test
test reverse_4
  integer, parameter :: length = 4
  integer, dimension(length) :: items = (/ 4, 3, 2, 1 /)
  call reverse( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
  assert_equal( 3, items(3) )
  assert_equal( 4, items(4) )
end test

test heap_sort_no_index_1
  integer, parameter :: length = 1
  integer, dimension(length) :: items = (/ 1 /)
  call heap_sort( length, items)
  assert_equal( 1, items(1) )
end test
test heap_sort_no_index_2_dn
  integer, parameter :: length = 2
  integer, dimension(length) :: items = (/ 2, 1 /)
  call heap_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
end test
test heap_sort_no_index_2_up
  integer, parameter :: length = 2
  integer, dimension(length) :: items = (/ 1, 2 /)
  call heap_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
end test
test heap_sort_no_index_3_123
  integer, parameter :: length = 3
  integer, dimension(length) :: items = (/ 1, 2, 3 /)
  call heap_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
  assert_equal( 3, items(3) )
end test
test heap_sort_no_index_3_321
  integer, parameter :: length = 3
  integer, dimension(length) :: items = (/ 3, 2, 1 /)
  call heap_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
  assert_equal( 3, items(3) )
end test
test heap_sort_no_index_3_213
  integer, parameter :: length = 3
  integer, dimension(length) :: items = (/ 2, 1, 3 /)
  call heap_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
  assert_equal( 3, items(3) )
end test
test heap_sort_with_index_3_213
  integer, parameter :: length = 3
  integer, dimension(length) :: items = (/ 2, 1, 3 /)
  integer, dimension(length) :: order
  call heap_sort( length, items, order)
  assert_equal( 2, items(1) )
  assert_equal( 1, items(2) )
  assert_equal( 3, items(3) )
  assert_equal( 2, order(1) )
  assert_equal( 1, order(2) )
  assert_equal( 3, order(3) )
end test
test heap_sort_with_index_3_654
  integer, parameter :: length = 3
  integer, dimension(length) :: items = (/ 6, 5, 4 /)
  integer, dimension(length) :: order
  call heap_sort( length, items, order)
  assert_equal( 6, items(1) )
  assert_equal( 5, items(2) )
  assert_equal( 4, items(3) )
  assert_equal( 3, order(1) )
  assert_equal( 2, order(2) )
  assert_equal( 1, order(3) )
end test

test heap_sort_no_idx_real888_1_1
  integer,  parameter :: n = 1
  real(dp), dimension(n) :: a = (/ 1.0 /)
  real(dp), dimension(n) :: b = (/ 2.0 /)
  real(dp), dimension(n) :: c = (/ 3.0 /)
  call heap_sort( n, a, b, c )
  assert_equal( 1.0, a(1) )
  assert_equal( 2.0, b(1) )
  assert_equal( 3.0, c(1) )
end test
test heap_sort_no_idx_real888_3_654
  integer,  parameter :: n = 3
  real(dp), dimension(n) :: a = (/ 6.0, 5.0, 4.0 /)
  real(dp), dimension(n) :: b = (/ 7.0, 6.0, 5.0 /)
  real(dp), dimension(n) :: c = (/ 8.0, 7.0, 6.0 /)
  call heap_sort( n, a, b, c )
  assert_equal( 4.0, a(1) )
  assert_equal( 5.0, a(2) )
  assert_equal( 6.0, a(3) )
  assert_equal( 5.0, b(1) )
  assert_equal( 6.0, b(2) )
  assert_equal( 7.0, b(3) )
  assert_equal( 6.0, c(1) )
  assert_equal( 7.0, c(2) )
  assert_equal( 8.0, c(3) )
end test

test small_sort_no_index_1
  integer, parameter :: length = 1
  integer, dimension(length) :: items = (/ 1 /)
  call small_sort( length, items)
  assert_equal( 1, items(1) )
end test
test small_sort_no_index_2_dn
  integer, parameter :: length = 2
  integer, dimension(length) :: items = (/ 2, 1 /)
  call small_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
end test
test small_sort_no_index_2_up
  integer, parameter :: length = 2
  integer, dimension(length) :: items = (/ 1, 2 /)
  call small_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
end test
test small_sort_no_index_3_123
  integer, parameter :: length = 3
  integer, dimension(length) :: items = (/ 1, 2, 3 /)
  call small_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
  assert_equal( 3, items(3) )
end test
test small_sort_no_index_3_321
  integer, parameter :: length = 3
  integer, dimension(length) :: items = (/ 3, 2, 1 /)
  call small_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
  assert_equal( 3, items(3) )
end test
test small_sort_no_index_3_213
  integer, parameter :: length = 3
  integer, dimension(length) :: items = (/ 2, 1, 3 /)
  call small_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
  assert_equal( 3, items(3) )
end test
test small_sort_no_index_4_4321
  integer, parameter :: length = 4
  integer, dimension(length) :: items = (/ 4, 3, 2, 1 /)
  call small_sort( length, items)
  assert_equal( 1, items(1) )
  assert_equal( 2, items(2) )
  assert_equal( 3, items(3) )
  assert_equal( 4, items(4) )
end test

end test_suite

\section{Design Optimization}\label{s:design}

The \FunThreeD design framework uses a gradient-based optimization
procedure.  One potential approach to obtaining the required sensitivity
derivatives is a conventional forward mode of differentiation, such
as finite-differencing, complex-variable
formulations, operator overloading, or direct differentiation.
Since the cost of these techniques scales directly with the number of
input parameters, these methods are most efficient for problems where
the number of outputs is considerably larger than the number of inputs.
For such problems, \FunThreeD provides a complex variable formulation
as described in \sectionref{s:complex-run}.  However, for most aerodynamic
design problems, the converse is true; the number of design variables
is typically much larger than the number of objective functions
and/or constraints.  In this context, an adjoint, or reverse mode of
differentiation is preferred.

\FunThreeD provides a discrete adjoint capability to efficiently determine
the sensitivities required by a gradient-based design procedure.  The
adjoint approach enables the user to compute sensitivity derivatives of an
output function with respect to an unlimited number of design variables at
a cost equivalent to a single additional flow solution.  For a general
review of sensitivity analysis techniques, see \cite{newman-sens}
and \cite{peter-sens}.

The adjoint approach used in \FunThreeD relies on discrete linearizations
of the relevant components of the flow solver.  Most of \FunThreeD's
compressible perfect gas and
incompressible capabilities are accounted for within the adjoint-based
framework.  Discretely consistent sensitivities have been demonstrated
for both steady and unsteady inviscid, laminar, and turbulent flows based
on the one-equation model of Spalart and Allmaras.  Grid topologies
may contain any combination of element types and may also contain
overset grid discretizations.  Grids may be static, non-inertial, or may
contain any combination of static, rigidly-moving, or deforming overset
component grids.  Both compressible and incompressible formulations are
available.  The most commonly-used boundary conditions are implemented
in the adjoint framework, and a broad range of objective/constraint
functions is also available.  However, the user is encouraged to
review the latest release notes or contact \funsupport to determine if
a specific analysis capability is currently supported by the adjoint
implementation.  For a detailed overview of the adjoint-based procedure
used in \FunThreeD and examples of its use for design optimization,
see \cite{nielsen-overset-adjoint} and the references contained therein.

Users are encouraged to gain extensive experience using \FunThreeD for
analysis purposes before attempting design optimization.  This experience
will aid in properly setting up optimization cases, understanding the
steps involved, and interpreting the results.

The adjoint-based algorithms are very efficient, but a typical
optimization will still require the equivalent of $\mathcal{O}(20)$ typical
analyses.  Therefore, securing sufficient computational resources is
critical to performing realistic high-fidelity design.  It should also
be noted that the various optimization packages supported by \FunThreeD
may behave very differently for a given design problem; moreover, the optimal
algorithm is generally problem-dependent.

At this time, the documentation provided here is aimed at design
optimization of steady flows.  The extension to simulations
involving unsteady flows is available for general use (e.g., see
\cite{martin-jones-phd}), but is not currently covered here.  Please
contact \funsupport if interested in using this capability.

\subsection{Objective/Constraint Functions}\label{s:obj-con-fcns}

To perform a gradient-based optimization, the user must specify at least
one objective function to quantify the merit of the configuration.  In
the \FunThreeD design infrastructure, such objective functions may take a
very general form as described here.  Note that all of the supported optimization
packages always seek to \emph{minimize} the chosen objective function.
Care should be taken to pose the objective function accordingly.
Multiple outputs may be accounted for in a variety of ways.  Constraints
may be included implicitly within the objective function(s) as penalty terms.
Explicit constraint functions may also be posed, as either equality or
inequality constraints.

Note that the primary limitation in posing the problem statement
is the general ability of the chosen optimization package to handle the
design problem posed by the user.  For example, the PORT optimization
software does not support the use of explicit constraints.  KSOPT is
the only supported optimization package that supports the use of more
than one objective function; however, \FunThreeD offers several approaches
to scalarize multiple objectives for other packages.  Multi-point design
is also supported in several forms. See \sectionref{s:mo-design} and
\sectionref{s:mp-design} for specific details on these capabilities.

The \FunThreeD flow and adjoint solvers do not distinguish between
objective functions and constraints.  The solvers themselves merely
provide function values and their sensitivities for use during the
optimization procedure.  The actual optimization packages are the only
components in the design framework that make a distinction between
objective functions and constraint functions.

\subsubsection{Terminology}

It is useful to establish some basic terminology when composing the
design problem statement.  Within the \FunThreeD design infrastructure,
the user specifies one or more \emph{component} functions based on
typical solver outputs.  These \emph{component} functions are then
combined to form a single \emph{composite} function.  Multiple \emph{component}
functions may be used to form \emph{composite} functions, and in turn, multiple
\emph{composite} functions may ultimately be specified.  The user then classifies
each \emph{composite} function, designating it either an objective
function or a constraint function.  Again, this distinction is solely
for the optimization algorithm; \FunThreeD simply evaluates and linearizes
each of the \emph{composite} functions in a generic sense and provides them
to the optimization scheme.

The adjoint formulation requires a separate adjoint solution for
each composite function.  For example, a drag-minimization problem with an
explicit lift constraint will generally require two adjoint solutions at each
step of the design procedure (one based on drag and one based on lift).  Rather
than performing separate adjoint executions
for each function, \FunThreeD's adjoint solver is implemented such that multiple
adjoint solutions may be computed simultaneously by cycling through a series
of right-hand side vectors.  In this manner, much of the computational overhead
associated with discretizing the adjoint system is amortized over the collection
of specified functions, and each additional function only increases the overall
computational cost by approximately 40\%.  See \cite{nielsen-exact-dual}
for further details on this aspect of the implementation.

\subsubsection{Functional Form}

Composite functions take the following general form in \FunThreeD:

\begin{equation} \label{eq:composite_fcn}
f_i = \sum_{j=1}^{J_i}\omega_{j} (C_{j}-C_{j}^{*})^{p_{j}}
\end{equation}
Here, the index $J_i$ corresponds to the number of individual component functions
comprising composite function $i$.  The factor $\omega_{j}$ represents
a user-specified weighting coefficient in the summation; $C_{j}$ is a \FunThreeD scalar
output quantity, $C_{j}^{*}$ is a user-specified target value for that output quantity, and
$p_{j}$ is a user-specified exponent.  The currently available \FunThreeD output functions
that may be posed as $C_{j}$ are listed in \tab{t:obj-con-keys}.  Though not explicitly
represented in Eq.~\ref{eq:composite_fcn}, 
the implementation also allows the user to only use
specific boundary contributions to $C_{j}$ and not all boundaries if desired.
This could be used to focus the optimization function on forces acting on the
wing or tail only.
Note that when composing an objective or constraint function it is often helpful to scale
the expected value to an $\mathcal{O}(1)$ quantity.  This can be readily done using the $\omega_{j}$ factor.
Additional details relevant to more complex functions are covered in \sectionref{s:fcn_specifics}.
The specific input mechanism for providing each of the component/composite function parameters
will be discussed at length in \sectionref{s:rubber.data.details}.

\begin{table}[hbtp]
  \centering\tabularfont
  \caption{Objective/constraint component function keywords.}\label{t:obj-con-keys}
  \begin{tabular}{ll}
   Keyword & Function \\
   \midrule
    \texttt{cl}, \texttt{cd} & Lift, drag coefficients \\
    \texttt{clp}, \texttt{cdp} & Lift, drag coefficients: pressure contributions \\
    \texttt{clv}, \texttt{cdv} & Lift, drag coefficients: shear contributions \\
    \texttt{cmx}, \texttt{cmy}, \texttt{cmz} & x/y/z-axis moment coefficients \\
    \texttt{cmxp}, \texttt{cmyp}, \texttt{cmzp} & x/y/z-axis moment coefficients: pressure contributions \\
    \texttt{cmxv}, \texttt{cmyv}, \texttt{cmzv} & x/y/z-axis moment coefficients: shear contributions \\
    \texttt{cx}, \texttt{cy}, \texttt{cz} & x/y/z-axis force coefficients \\
    \texttt{cxp}, \texttt{cyp}, \texttt{czp} & x/y/z-axis force coefficients: pressure contributions \\
    \texttt{cxv}, \texttt{cyv}, \texttt{czv} & x/y/z-axis force coefficients: shear contributions \\
    \texttt{powerx}, \texttt{powery}, \texttt{powerz} & x/y/z-axis power coefficients \\
    \texttt{clcd} & Lift-to-drag ratio \\
    \texttt{fom} & Rotorcraft figure of merit \\
    \texttt{propeff} & Rotorcraft propulsive efficiency  \\
    \texttt{rtr\_thrust}  & Rotorcraft thrust function \\
    \texttt{pstag}      & RMS of stagnation pressure in cutting plane disk  \\
    \texttt{boom\_targ} & Near-field $p/p_\infty$ pressure target\\
    \texttt{sboom} & Coupled \sboom ground-based noise metrics \\
    \texttt{ae}         & Supersonic equivalent area target distribution \\
    \texttt{press\_box} & RMS of pressure in user-defined box, also pointwise $dp/dt$, $d\rho /dt$ \\
    \texttt{cpstar} & Target pressure distributions \\
  \end{tabular}
\end{table}

To demonstrate the use of the general functional form
given by Eq.~\ref{eq:composite_fcn}, several
examples are given here:

\paragraph{Unconstrained Drag Minimization}
For an unconstrained problem in which the user wishes solely to
minimize drag, one potential approach might be to specify a single composite function
consisting of a single component function with $\omega_1=1.0$, $C_1=\texttt{cd}$, 
$C_1^*=0.0$, and $p_1=2$.  In this manner, the objective function is simply

\begin{equation} \label{eq:uncon_drag_min}
f=C_D^2,
\end{equation}
where the quadratic form has been chosen to provide a convex function space.

\paragraph{Drag Minimization with Lift Penalty}
To add an interior penalty term accounting for a lift equality constraint of 0.5,
one might instead use two component functions within the same single composite
function where $\omega_1=10.0$, $\omega_2=1.0$, $C_1=\texttt{cd}$,
$C_2=\texttt{cl}$, $C_1^*=0.0$, $C_2^*=0.5$, and $p_1=p_2=2$.  These parameters yield

\begin{equation}
f=10C_D^2 + (C_L-0.5)^2.
\end{equation}
In this case, any deviation of the lift coefficient from its target value of 0.5
will ``penalize'' the objective function.  The weighting parameters $\omega_j$ have
been selected based on typical magnitudes of $C_D$ and $C_L$, so as to
produce roughly equivalent contributions to the objective function.  Note that the
choice of these weighting parameters is heuristic in nature and often troublesome in practice.

\paragraph{Drag Minimization with Explicit Lift Constraint}
In this example, the lift constraint $C_L=0.5$ is instead posed as an explicit constraint for the
optimizer.  Here, two composite functions are formed, each with a single component function.
First, an objective function is specified as in Eq.~\ref{eq:uncon_drag_min} with $\omega_1=1.0$,
$C_1=\texttt{cd}$, $C_1^*=0.0$, and $p_1=2$.  As before, this yields

\begin{equation}
f_1=C_D^2.
\end{equation}
However, an additional composite function for the lift constraint is also specified with
$\omega_1=1.0$, $C_1=\texttt{cl}$, $C_1^*=0.5$, and $p_1=1$, which gives

\begin{equation}
f_2=C_L.
\end{equation}
This explicit form of the lift constraint is generally preferred in practice.

\subsection{Some Details on Specific Objective/Constraint\\Functions}\label{s:fcn_specifics}

Many of the scalar functions shown in \tab{t:obj-con-keys} and designed
to be used as the term $C_j$ in Eq.~\ref{eq:composite_fcn} are
straightforward.  For example, the keyword \texttt{cd} is
sufficient to characterize a drag-based component function.  However,
some of the scalar functions listed in \tab{t:obj-con-keys} require
the user to be aware of specific requirements and/or to provide additional
auxiliary data.  In this section, scalar functions requiring further
data and/or explanation are covered.

\subsubsection{Lift-to-Drag Ratio (Keyword: \texttt{clcd})}

This function must be specified with a \var{0} for its boundary index,
i.e., it must be applied to the entire configuration and is not available for
individual boundary patches. It is defined as
\begin{equation}
f = \frac{C_L}{C_D}.
\end{equation}

\subsubsection{Rotorcraft Figure of Merit (Keyword: \texttt{fom})}

This function is defined as

\begin{equation}
f = \frac{C_L^3}{2 C_{M_z}^2}.
\end{equation}
Note that this functional form assumes that the rotor axis of rotation is
in the $+z$ direction.  The definition also represents the square of the
traditional Figure of Merit function.  See \cite{nielsen-noninJ} for a
motivation for this modified form.  This function must be specified with
a \var{0} for its boundary index, i.e., it must be applied to the entire
configuration and is not available for individual boundary patches.

\subsubsection{Rotorcraft Propulsive Efficiency (Keyword: \texttt{propeff})}

This function is defined as

\begin{equation}
f = \frac{-C_z}{C_{M_z}}.
\end{equation}
Note that this functional form assumes that the rotor axis of rotation is
in the $+z$ direction.  The minus sign has been introduced to yield a positive
efficiency since $C_{M_z}$ is negative.  This function must be specified with
a \var{0} for its boundary index, i.e., it must be applied to the entire
configuration and is not available for individual boundary patches.

\subsubsection{Rotorcraft Thrust (Keyword: \texttt{rtr\_thrust})}

This function is defined as the force normal to the plane of a rotor system:
\begin{equation}
f = C_L \cos(\theta_T) - C_D \sin(\theta_T)
\end{equation}
where the angle from $+z$-direction $\theta_T$ in radians
is the variable \var{thrust_angle}
that must be set in the source file \file{LibF90/custom_transforms.f90}.

\subsubsection{RMS of Stagnation Pressure (Keyword: \texttt{pstag})}\label{s:pstag}

This function computes the RMS of stagnation (total) pressure in a circular
disk that passes through the grid in a specified location and orientation.
This is commonly employed to quantify engine inlet distortion.
The user must specify the variables in the \var{&pstag_function}
namelist within \file{fun3d.nml}.  See \sectionref{s:nml_pstag_function}
for details related to this namelist.  This function is only
available for compressible flows.

\subsubsection{Near-field $p/p_\infty$ Pressure Target (Keyword: \texttt{boom\_targ})}\label{s:boom-targ-details}

This function allows inverse design of near-field
pressure signatures, which is a commonly used
tactic for creating low sonic boom configurations.
This function is only available for compressible flows.
The user specifies \var{yz}-coordinate
pairs through which rays are passed parallel to the \var{x}-axis.
Off-body pressure distributions in the vicinity of an aircraft are nominally
oriented parallel to the freestream velocity vector.    
In the case of a nonzero angle of attack, the rays are rotated about a
user-specified center of rotation to align them with the freestream
direction.  The user also provides the minimum and maximum \var{x}-extent
for the rays.  A user-specified number of points are evenly distributed
along each ray and the grid element containing each point is identified.
See \sectionref{s:nml_sonic_boom} for guidance on the required namelist
inputs.

The functional form is given by

\begin{equation}
f = \sum_{i=1}^{N}\omega_{i} \left(\left.{\frac{p}{p_\infty}}\right|_i-\left.{\frac{p}{p_\infty}}\right|_i^{*}\right)^2
\end{equation}
where $p$ is the local static pressure.  The summation takes place over
all points in the rays defined by the user, and the values of $p$
are evaluated at the centroids of the enclosing elements.  The
values of $\omega_i$ and $\left.{\frac{p}{p_\infty}}\right|_i^{*}$ are user-supplied pointwise
weighting coefficients and target values of $p/p_\infty$, respectively,
which must be provided in a file named \file{pressure_target.dat}.
If this file is not present, the target values of $p/p_\infty$ are set to 1.0
and the weighting coefficients are set to 1.0.
Note that with the above functional form, the target and exponent
parameters present in Eq.~\ref{eq:composite_fcn} are usually set to
\var{0.0} and \var{1}, respectively.

A template for \file{pressure_target.dat} is typically generated
by first extracting
a set of $p/p_\infty$ distributions for a known configuration by running
the optimization driver with \var{what_to_do = 1}
in the \file{design.nml} \var{&design} namelist,
see \sectionref{s:nml_design}.
Note that the input
value \texttt{weight} must be set to \texttt{.true.} and the desired
ray extraction $(y,z)$ coordinate pairs must be specified in the \texttt{\&sonic\_boom}
namelist in \file{fun3d.nml}.  This operation produces a file named
\file{pressure_signatures.dat}, which uses the same file format
intended for the \file{pressure_target.dat} target input file.  (Note that
the file format is amenable to \Tecplot usage.)  The user
may then use the \file{pressure_signatures.dat} file to develop a
\file{pressure_target.dat} input file by modifying the existing pressures
to reflect their target values as desired.  Note that by specifying
\texttt{weight=.true.} in the \texttt{\&sonic\_boom} namelist, a column
of data representing pointwise weighting coefficients (all initially set
to \texttt{1.0}) will be provided in \file{pressure_signatures.dat}.  This
column of data is required to be present in \file{pressure_target.dat}.
The individual weights may be left as \texttt{1.0}, or they may be
modified on an individual basis to optionally weight a specific region
of the signature more or less in the final objective function.  A brief
example of this file format for a case involving two off-body signatures
is shown below.  Note that target distributions need not have the same
number of locations as, nor line up with, the eventual sampling locations
along the extraction rays.  \FunThreeD will linearly interpolate between
input target values to obtain values at the sampling locations.

{\footnotesize
\begin{verbatim}
  VARIABLES = "x", "y", "z", "p/pinf", "weight"
  zone t="Signal 1"
    -0.500E+01 0.100E-11 0.826E+00 0.110010E+01 0.100E+01
    -0.472E+01 0.100E-11 0.835E+00 0.110011E+01 0.100E+01
    -0.415E+01 0.100E-11 0.855E+00 0.110012E+01 0.100E+01
    -0.354E+01 0.100E-11 0.876E+00 0.110016E+01 0.100E+01
  zone t="Signal 2"
    -0.500E+01 0.100E-11 0.182E+01 0.102008E+01 0.100E+01
    -0.472E+01 0.100E-11 0.183E+01 0.102008E+01 0.100E+01
    -0.414E+01 0.100E-11 0.185E+01 0.102009E+01 0.100E+01
    -0.356E+01 0.100E-11 0.187E+01 0.102012E+01 0.100E+01
    -0.335E+01 0.100E-11 0.188E+01 0.105013E+01 0.100E+01
    -0.320E+01 0.100E-11 0.188E+01 0.105014E+01 0.100E+01
    -0.264E+01 0.100E-11 0.190E+01 0.105017E+01 0.100E+01
\end{verbatim}
}

\subsubsection{Coupled \sboom Ground-Based Signatures, Noise Metrics, and Equivalent Areas (Keyword: \texttt{sboom})}\label{s:sboom-design}

This option uses the adjoint capability of
the Burgers equation boom propagation code
\sboom\cite{rallabhandi-boom-extrap-burgers}
to inversely design ground pressure signatures, 
optimize a ground-based noise metric, or match equivalent area distributions.%
\cite{sriram-2011,sriram-2012,sriram-2013}
\FunThreeD must be configured and built with the \sboom library as described
in \sectionref{s:install-sboom} to use this capability.

In the coupled \FunThreeD-\sboom implementation, \FunThreeD is responsible for computing
pressure signals in the immediate vicinity of an aircraft (typically within
10 body lengths).
The \sboom tool then propagates these disturbances to the
ground using an augmented Burgers equation
that considers effects such as non-linearity, thermo-viscous
absorption, and any number of molecular relaxation phenomena
during the propagation of waveforms through the atmosphere.
In this manner, the user can directly simulate ground-based
noise metrics such as A-weighted loudness
or compute other loudness measures (e.g., Perceived Level) from the computed
ground signatures.
In a similar fashion, a
coupled adjoint problem is used to determine the discrete sensitivities of the
ground-based metrics with respect to any of \FunThreeD's typical design parameters
which may then be used to optimize the configuration.

\sboom can generate off-track signatures based on ray
theory using user input azimuthal angles.
\sboom can also predict the sonic boom signatures
in the presence of wind,
turn rate (changing heading angle), climb rate, climb angle, and
acceleration (dMach/dt). 
During maneuvering flight,
boom focusing is possible. 
The current version \sboom in not able model focusing and
will exit with an appropriate message if focusing occurs.

Equivalent area distributions are computed
with reversed augmented Burgers equation (when \var{rs}$<($\var{alt}$-$\var{hg}$)$)
or a direct conversion of off-body pressures (when \var{rs}$>($\var{alt}$-$\var{hg}$)$).
This is different than the Mach cut equivalent area matching
approach in \sectionref{s:ae-details}.
The discrete sensitivities of the difference between a
target and the computed equivalent areas are provided to \FunThreeD.
The target area is specified with the \var{target_dpress}
and \var{target_xx} variables
in the \var{&sboom} namelist.

The user must provide inputs relevant to the nearfield pressure signal
extraction (see \sectionref{s:nml_sonic_boom}) as well as parameters
specific to the \sboom library (see \sectionref{s:nml_sboom}).  Note that when
the \texttt{sboom} keyword is used as the component function name, the actual
form of the objective/constraint component function is determined entirely
within \sboom.  In this case, the values of $\omega$, $C^{*}$, and $p$ in
Eq.~\ref{eq:composite_fcn} are ignored.  This function is only available for
compressible flows.

\subsubsection{Supersonic Mach Cut Equivalent Area Distribution \\ (Keyword: \texttt{ae})}\label{s:ae-details}

This function aims to match a target Mach cut equivalent area distribution
for supersonic flows.
The Mach cut equivalent area distribution is directly computed
from surface pressures and geometry for this function.
This is a different approach than the equivalent area computation
of \sboom in \sectionref{s:sboom-design}.
The function is defined as
\begin{equation}
f = \sum_{i=1}^{N} \omega_i (L_i + V_i - A_i^{*})^2
\end{equation}
where $N$ represents the total number of longitudinal stations used to sample
the solution and geometry for the current azimuth, and $L_i$ and $V_i$ are the
lift and volume contributions, respectively, to the current equivalent area.
The term $A_i^{*}$ represents the user-supplied target equivalent area distribution.
The $\omega_i$ enables the user to locally weight individual segments of the
distribution if desired.  Note that with the above functional form, the target
and exponent parameters present in Eq.~\ref{eq:composite_fcn} are usually set to
\var{0.0} and \var{1}, respectively.  This function is only available for compressible
flows, and the configuration is assumed to align with the \var{x}-axis.

Any number of desired azimuthal (centerline/off-track) locations may be
specified and used as individual component functions.  The user must provide
the data indicated in the \texttt{\&equivalent\_area} namelist in
\file{fun3d.nml} as described in \sectionref{s:nml_equivalent_area}.  A centerline
symmetry plane may be used to reduce computational expense; in this case, the
cutting planes at each longitudinal station will be correctly accounted for on the
virtual side of the aircraft.  A file \file{ae_target.dat} must also be
provided, which describes the (optionally weighted) target equivalent area
profiles.

A template for \file{ae_target.dat} is typically generated by first extracting
a set of equivalent area distributions for a known configuration by running
the optimization driver with \var{what_to_do = 1}
in the \file{design.nml} \var{&design} namelist,
see \sectionref{s:nml_design}.
This operation will
produce a \Tecplot file \file{[project_rootname]_ae.dat} which uses the same file format
intended for the target input file \file{ae_target.dat}.  The user
may then use the \file{[project_rootname]_ae.dat} file to develop a
\file{ae_target.dat} input file by modifying the existing equivalent areas
to reflect their target values as desired.
Note that the file \file{[project_rootname]_ae.dat} contains a column of data
representing the pointwise weighting coefficients $\omega_i$ (all initially set
to \texttt{1.0}).  This
column of data is required to be present in \file{ae_target.dat}.
The individual weights may be left as \texttt{1.0}, or they may be
modified on an individual basis to optionally weight a specific region
of the distributions more or less in the final objective function.  A brief
example of this file format for a case involving three azimuthal signatures
is shown below.  Note that target distributions need not have the same
number of locations as, nor line up with, the longitudinal sampling locations.
\FunThreeD will linearly interpolate between input target values to obtain
values at the sampling locations.  Also note that in the input file
\file{ae_target.dat}, the second and third columns of the format are ignored.


{\footnotesize
\begin{verbatim}
 VARIABLES = "x", "V", "L", "Ae", "weight"
 zone t="Ae Function 1"
   -0.01000E+00  0.00000E+00  0.00000E+00  0.00000E+00  0.10000E+01
    0.13839E+01  0.25482E-01 -0.26289E-02  0.22853E-01  0.10000E+01
    0.27678E+01  0.47548E-01 -0.64155E-02  0.41133E-01  0.10000E+01
    0.41517E+01  0.76165E-01 -0.10361E-01  0.65804E-01  0.10000E+01
 zone t="Ae Function 2"
   -0.01000E+00  0.00000E+00  0.00000E+00  0.00000E+00  0.10000E+01
    0.14018E+01  0.25700E-01 -0.26610E-02  0.23039E-01  0.10000E+01
    0.28036E+01  0.48215E-01 -0.64628E-02  0.41752E-01  0.10000E+01
    0.42054E+01  0.77379E-01 -0.10358E-01  0.67020E-01  0.10000E+01
    0.56072E+01  0.11457E+00 -0.14045E-01  0.10052E+00  0.10000E+01
    0.98126E+01  0.30728E+00 -0.25726E-01  0.28156E+00  0.10000E+01
 zone t="Ae Function 3"
   -0.01000E+00  0.00000E+00  0.00000E+00  0.00000E+00  0.10000E+01
    0.14155E+01  0.26009E-01 -0.26166E-02  0.23392E-01  0.10000E+01
    0.28310E+01  0.48902E-01 -0.62883E-02  0.42614E-01  0.10000E+01
    0.42465E+01  0.78591E-01 -0.10011E-01  0.68579E-01  0.10000E+01
\end{verbatim}
}

Finally, the solver will also provide the user with a \Tecplot output file
\file{[project_rootname]_ae_cuts_i.dat} for the \var{i}th specified equivalent
area function.  These files contain the actual cross-sectional slices of
the aircraft that were generated for each azimuthal function.

\subsubsection{RMS of Pressure in User-Defined Box, Pointwise $dp/dt$, \\
$d\rho /dt$ (Keyword: \texttt{press\_box})}\label{s:press_box}

This function computes the RMS of a quantity in Cartesian region,
which is typically used to indicate a region of the flow is important
for grid adaptation or that fluctuations in a region should be minimized
in  a design.
These functions rely on the inputs associated with the
\var{&press_box_function} namelist; see \sectionref{s:nml_press_box_function}
for details.  The function may be composed of the RMS value of the pressure
within a user-defined box in the domain, or for unsteady simulations, the
time derivative of pressure or density (for compressible flows)
at a single grid point.

\subsubsection{Target Pressure Distributions (Keyword: \texttt{cpstar})}

\FunThreeD has an inverse design capability where the objective function
may be composed of target pressure distributions.  The file containing
the \texttt{j}th target distribution must be named \file{cpstar.data.j}.
However, setup is tedious, primarily due to the difficulty in specifying
pressure distributions on a three-dimensional configuration.  If this
capability is of interest, please contact \funsupport for more detailed
guidance.

\subsection{Geometry Parameterizations}\label{s:geom-param}

In order to perform shape optimization, \FunThreeD must be
provided with a set of design variables describing the geometric
shape of the configuration.  \FunThreeD is currently set up to
interface directly with geometry parameterizations provided
by MASSOUD\cite{samareh-massoud}, Bandaids\cite{samareh-bandaids}, 
or \Sculptor. MASSOUD and Bandaids are
software packages developed by Jamshid Samareh of NASA Langley
(\href{mailto:<EMAIL>}{<EMAIL>}).
Users should contact him for copies of the software; tutorial information
for these tools is available on the \FunThreeD website. These packages
allow the user to parameterize completely arbitrary shapes using a
free-form deformation technique. The packages are very efficient,
robust, and also provide analytic Jacobians of the parameterization,
which are necessary for \FunThreeD-based design. \Sculptor is a popular
commercial package developed by Optimal Solutions and also provides the
necessary data for \FunThreeD-based design.  Note that any combination of
parameterizations based on these tools may be used within the context of a
single optimization.  For example, the planform of a wing or tail surface
may be best treated using MASSOUD, while Bandaids or \Sculptor may be
most appropriate for a wing-body fillet region or a feature such as a
fuselage protuberance.  Finally, the user may also use a parameterization
scheme of their choosing; see section \sectionref{s:custom_parameterization}
for further details.

\subsubsection{Surface Grid Extraction}

To parameterize a surface grid using any of the above tools,
it must first be extracted to a \Tecplot file.  To do this, add a
\texttt{\&massoud\_output} namelist to \file{fun3d.nml} to group all of
the required boundary patches for a body to be parameterized into
a single body (see also \sectionref{s:nml_massoud_output}):

{\footnotesize
\begin{verbatim}
  &massoud_output
    n_bodies = 2                 ! parameterize 2 bodies: wing and tail
    nbndry(1) = 6                ! # of bounds that comprise wing
    boundary_list(1) = '3-8'     ! wing bounds (account for lumping!)
    nbndry(2) = 3                ! # of bounds that comprise tail
    boundary_list(2) = '9,10,12' ! tail bounds (account for lumping!)
  /
\end{verbatim}}
Note that the boundary indices shown here must reflect any patch lumping
that may have been requested in the \texttt{\&raw\_grid} namelist (see
also \sectionref{s:nml_raw_grid}).
A single iteration of the flow solver should now be executed with the
\cmd{--write_massoud_file} command line option.  This will generate a
\file{[project_rootname]_massoud_bndry#.dat} file for each of the boundary
groups present in the \texttt{\&massoud\_output} namelist.  These files
contain the information necessary to parameterize the surface grid using
any of the aforementioned tools.  See the documentation for those packages
for further instructions on how to construct the actual parameterization.

\subsubsection{Access to Executables}

If MASSOUD, \Sculptor, or a user-defined executable is being used for
parameterizations, the executable for those packages must be available
in the runtime \file{PATH}.  The executables for MASSOUD and \Sculptor
must be named \cmd{massoud} and \cmd{sculptor}, respectively.  If using
a user-defined parameterization package (see \sectionref{s:custom_parameterization}),
the executable must be named
according to the input provided in the \texttt{\&design} namelist in
\file{design.nml} (see \sectionref{s:nml_design}).  The optimization driver
supplied with \FunThreeD will attempt to call these executables if such
parameterization types are present.  If Bandaids are being used, no
additional executables must be supplied; all Bandaid evaluations are
handled internally by \FunThreeD.

\subsubsection{Notes on Using \Sculptor}

If \Sculptor is being used, \FunThreeD will invoke \Sculptor in batch
(non-GUI) mode during the course of the optimization. However, current
versions of \Sculptor will still attempt to communicate with an X server,
even when run in this fashion. If the system does not run an X server
(such as compute nodes on a cluster), then a fake X server such as
\texttt{Xvfb} is recommended. You will need to execute the fake server
prior to running the design optimization. For example, a run script may
have the following commands:

\begin{Verbatim}
  Xvfb :1 &
  export DISPLAY=:1.0 # for bash
  setenv DISPLAY :1.0 # for c shell
  [any command that uses Sculptor]
\end{Verbatim}
The syntax here may vary; if this does not allow the optimization driver
to run \Sculptor in batch mode successfully on the system, the user
should get in touch with \Sculptor support for assistance.

In addition, the parameterization of all bodies treated using
\Sculptor must be bookkept within a single set of \Sculptor input files.
For example, in the wing-tail example above, both bodies must be contained in a
single instance of \Sculptor files. Therefore, the \texttt{\&massoud\_output}
namelist described above should group all of the desired boundaries necessary
to describe the geometry(s) of interest into a single body:

\begin{Verbatim}
  &massoud_output
    n_bodies = 1                 ! wing and tail grouped into a body
    nbndry(1) = 9                ! # of tail and wing bounds
    boundary_list(1) = '3-10,12' ! wing and tail boundaries
  /
\end{Verbatim}

Each of the desired bodies may be worked on independently within Sculptor,
but they must ultimately appear as a single body to \FunThreeD.

\subsubsection{Using Other Parameterization Packages}\label{s:custom_parameterization}

\FunThreeD provides a generic interface for user-defined external geometric
parameterization packages.  The user-defined tool must provide the surface mesh
coordinates as a function of some vector of design variables for the body of
interest.  The partial derivatives of these coordinates with respect to the design
variables must also be supplied.  See \cite{nielsen-mmnp} for an example of such
an approach.

To invoke a user-defined parameterization package for one or more bodies
present in the simulation, the user must tag individual bodies appropriately
(see \sectionref{s:rubber.data.details}) and provide the executable name to
be invoked by \FunThreeD's design driver at run-time via the \texttt{\&design}
namelist in \file{design.nml} (see \sectionref{s:nml_design}).  This may be a binary
executable or simply a script that invokes other user-defined operations
that may be necessary to evaluate the parameterization and its sensitivities.

When \FunThreeD requires an evaluation of the user-defined
parameterization for a body (or its sensitivities), it will first write
a file named \file{customDV.i}, where the \file{i} suffix corresponds to
the body index for which \FunThreeD is requesting updated surface data.
The format of the \file{customDV.i} file is as shown below.

\begin{Verbatim}
#User defined design variables
#Number of DVs
         3
  1.907460000000000E+00
  0.000000000000000E+00
 -0.002469800000000E+00
\end{Verbatim}
The first two lines are comment lines, and the third specifies the total
number of design variables in the parameterization for the current
body (whether the user may have designated some active and others
inactive in \file{rubber.data}; see \sectionref{s:rubber.data.details}).
The remaining lines in the file contain the current value of each design
variable, with one value per line.

After writing the \file{customDV.i} file, \FunThreeD will then invoke
the user-specified executable command.
\FunThreeD will append a space and a single integer to this
command, where the integer corresponds to the body index
for which \FunThreeD requires an evaluation of the parameterization.
The user-provided executable (or script) must be capable of parsing
this integer command-line option in order to process the requested body.

After the external package has completed the evaluation of the parameterization
and its sensitivities, the data must be supplied to \FunThreeD via a \Tecplot
file named \file{model.tec.i.sd1}, where the integer \file{i} in the filename
represents the current body index.  The format of this file must be as follows:

{\scriptsize
\begin{verbatim}
TITLE = "PARAMETERIZATION DATA"
VARIABLES = "X" "Y" "Z" "ID" "XD1" "YD1" "ZD1"  "XD2" "YD2" "ZD2"  "XD3" "YD3" "ZD3"
ZONE T = group0, I = 4, J = 1, F=FEPOINT
0.0 1.0 0.0 235 1.234 -23.0 892.1 -23.0 82.123 -90.2 -905.2 857.12 348.2
1.0 1.0 0.0 872 4.14 -0.123 -0.324 23.13 2978.2 -0.114 -982.4 -3.22 0.1185
1.0 0.0 0.0 912 -0.34 0.938 8.45 78.23 -35.2 -0.023 8.32 -0.009 -0.92
0.0 0.0 0.0 455 9.01 -8.23 -0.456 2.56 1.21 0.0 -0.091 -1.22 0.0088
1 2 3 4
\end{verbatim}}
The trivial (and completely contrived) example shown here provides
surface mesh point locations and the corresponding sensitivities for a
single quad element parameterized by three design variables.  The title
in the first line may contain anything the user desires.  The variables
identified in line 2 represent the x-, y-, and z-coordinates for the current
surface mesh point, the node index in the global grid for the current
surface mesh point, and the sensitivity derivatives of the x-, y-, and
z-coordinates of the current surface mesh point with respect to each of
the design variables provided by \FunThreeD in \file{customDV.i} above.
The file should contain a single zone, indicated by the third line of
the example file shown.  Here, the zone title specified by \cmd{T =}
may be anything the user desires.  The \cmd{I = } value corresponds to
the number of surface mesh points for the current body, while the \cmd{J
=} value specifies the number of surface elements (triangles or quads)
contained in the surface mesh for the current body.  \FunThreeD will
only read the \cmd{I = } value; the surface mesh topology is immaterial
in this context.

In this example, the floating point values have been truncated for
readability; users are strongly encouraged to provide double-precision
values in practice.  The connectivity information at the end of the file
is not used by \FunThreeD.  It may be omitted if desired; however, it
is often instructive to load this file into \Tecplot for visualization.
In that context, the connectivity data (including the appropriate
\cmd{J =} value in the file header) will be required.

For very large surface meshes and/or parameterizations containing a
very large number of design variables, I/O of this ASCII format can
be inefficient.  There is an alternative C-binary/Fortran stream format
that may be used in its place; interested users should get in touch with
\funsupport for further details on this option.

To support execution of the user-defined parameterization tool, auxiliary
files may be provided in the \texttt{description.i} directory; the filenames
should be provided in the file \file{user_def_param_files.data} (see
\sectionref{s:user_def_param_files.details}).

\subsection{Design Optimization Directory Structure}\label{s:setup-design}

The optimization driver \cmd{opt_driver} requires a very specific
directory structure.  It can be established by running \cmd{opt_driver}
in an interactive mode with the \cmd{--setup_design} command line option.
The number of design points should be 1 for single-point design or
greater than 1 for multi-point design.

\begin{Verbatim}
  opt_driver --setup_design [number of design points]
\end{Verbatim}
This interactive command will prompt the user for several directory paths
required by the optimization, namely the paths to the \FunThreeD source
code, the configuration directory where \FunThreeD was configured and built,
and the path to the location where the design will be performed.
Here, directories should be provided as absolute paths contained in
single quotes, with trailing slashes omitted, i.e.,
\begin{Verbatim}
'/absolute/path'  
\end{Verbatim}
At the completion of this setup procedure, a summary of the files required
from the user will be echoed to the screen.  The directories
created in the specified run location are shown in \tab{t:design-dir}.
\begin{table}[hbtp]
  \centering\tabularfont
  \caption{Directory structure required for \FunThreeD-based design.}\label{t:design-dir}
  \begin{tabular}{ll}
   \file{Directory} & Description \\
   \midrule
    \file{ammo} & Location where optimization will be executed \\
    \file{description.i} & Location of all baseline input files describing design point \cmd{i} \\
    \file{model.i} & Location where analysis \& sensitivity analysis of design point \cmd{i} will be performed
  \end{tabular}
\end{table}
The \cmd{i} suffix in \texttt{description.i} and \texttt{model.i} represents the design point
index.  For single-point design, this will be \cmd{1}; for multi-point design,
this value will range from \cmd{1} to the number of user-specified design points.  The
setup procedure will populate the various directories with links to the required \FunThreeD
executables and templates for various input files described below.

\subsection{Contents of the \texttt{ammo} Directory}

The \file{ammo} directory will contain files related to the optimization
procedure itself.  This includes the \file{design.nml} input file described in
\sectionref{s:nml_design} and a link to the \cmd{opt_driver} executable.


% temporary hijack of \namelistsection
  \renewcommand{\namelistsection}[2]{\subsubsection{#1 namelist in \protect\file{design.nml}}\label{#2}}

\input{nml_design}

  \renewcommand{\namelistsection}[2]{\newpage\subsubsection{#1}\label{#2}}

\subsection{Contents of the \texttt{description.i} Directory}

The \texttt{description.i} directory serves as a repository for the
baseline files for the CFD model, the geometric parameterization,
and several other input files related to the computational model for
the \texttt{i}th design point.  These files must be set up by the user
prior to the run and will not be modified by \FunThreeD during execution.
During the initial setup procedure, templates for several input files will
be placed in this location to aid in setting up the case.  During the
actual optimization, the optimization driver will copy files from this
directory into the \texttt{model.i} directory as needed.

Any files normally required by the flow solver must be present in this
directory.  This would typically include the grid and boundary condition
files and \file{fun3d.nml}.  If the mesh uses overset grids assembled with
the Suggar++ utility, the Suggar++ DCI file must be present as well.  The
optional file \file{remove_boundaries_from_force_totals} (\sectionref{s:rbfft})
may also be present, if desired.

In addition to the files normally required by the flow solver, a number
of other files must also be present to perform the design optimization, some
of which are optional.  These are described below.

\subsubsection{Geometry Parameterization Files}

If performing shape optimization, the user must provide the relevant
parameterization files for each body in the mesh to be modified.  The
specific set of files required for each body depends on the
parameterization package(s) being used.

\paragraph{MASSOUD Parameterizations}
For MASSOUD parameterizations, the MASSOUD parameterization files should
be named \file{design.gp.j}, where \var{j} is the index of the body to
be designed.  The files specifying the values of the raw MASSOUD variables
should be named \file{design.j} for each of the bodies to be designed.
For \FunThreeD-based design, the custom design variable linking feature
of MASSOUD must be used.  If the raw MASSOUD variables are intended to
be used as-is, simply set the linking matrix as the identity matrix in
the MASSOUD \file{.usd} file.  These files specifying the design variable
linking for each body should be named \file{design.usd.j}.

The MASSOUD control file specifies the names of the files outlined
above for MASSOUD and must be provided as \file{massoud.j} for the
\texttt{j}th body.  The files listed in the MASSOUD control file must
reflect these names.  The first line of the MASSOUD control file(s) must
have a positive integer equal to the number of custom design variables.
If the intent is simply to use the raw MASSOUD variables as-is, this
value is simply the number of raw MASSOUD variables for that body.
For the in/out-of-core parameter, use in-core (${0}$).  The file name
for \Tecplot output viewing must be named \file{model.tec.j} for the
\texttt{j}th body.  The design variable grouping file specified should be
named \file{designVariableGroups.j} for the \texttt{j}th body.  The FAST
output file name can be named anything the user wishes; the \FunThreeD
tools do not use this MASSOUD output file.  Finally, the user design
variable file for the \texttt{j}th body should be named \file{customDV.j}.
In summary, a \file{massoud.j} control file for the \texttt{j}th body
should look like the following:

\begin{Verbatim}
  #MASSOUD INPUT FILE
  # runOption 0-analysis, >0-sd users dvs, -1-sd massouds    dvs
  52
  # core 0-incore solution, 1-out of core solution
  0
  # input parameterized file
  design.gp.1
  # design variable input file
  design.1
  # input sensitivity file - used for runOption > 0
  design.usd.1
  # output file grid file
  newframe.fast.1
  # output Tecplot file for viewing
  model.tec.1
  # file containing the design variables group
  designVariableGroups.1
  # user design variable file
  customDV.1
\end{Verbatim}

\paragraph{Bandaid Parameterizations}

For Bandaid parameterizations, the input files created by the Bandaid setup
tool should be named \file{bandaid.data.j} for the \texttt{j}th body.
Because Bandaid parameters behave linearly, the sensitivities contained
in these files are constant and this input is all that is required during
the course of a design.

\paragraph{\Sculptor Parameterizations}

For \Sculptor parameterizations, the user must provide
\file{[project_rootname].mdf}, \file{[project_rootname].sd1}, \file{[project_rootname].vol}, and
\file{[project_rootname].stu} files.  See the \Sculptor documentation for more
details on each of these files.  A file named \file{[project_rootname].def} must
also be provided.  An example \file{[project_rootname].def} file for a simple
two-body parameterization is shown below:

\begin{verbatim}
  set_mdf [project_rootname].mdf
  default 1 DV1-T1 0.00
  default 1 DV1-T2 0.00
  default 1 DV1-T3 0.00
  default 1 DV1-T4 0.00
  default 1 DV1-T5 0.00
  default 2 DV2-T1 0.00
  default 2 DV2-T2 0.00
  default 2 DV2-T3 0.00
  export model.tec.1
  exit
\end{verbatim}
The filename specified for the \texttt{export} command must be
\file{model.tec.1}.  The remainder of the file is dictated by the
specific parameterization developed in the \Sculptor application.

After the configuration has been parameterized using \Sculptor and all of
the appropriate files have been assembled for \FunThreeD-based design,
a copy of the original \file{[project_rootname]_massoud_bndry#.dat} file must
also be placed in the \file{description.i} directory, but with the file name
changed to \file{[project_rootname].sd1}.  
\Sculptor requires this baseline file
during the optimization.

Finally, prior to performing the design, 
the \file{[project_rootname].sd1} file must be
read into \Sculptor in GUI mode as ``Import Mesh/CFD as Tecplot Point
FE.''  Following this, the Sculptor volumes need to be imported onto
the \file{[project_rootname].sd1} file, and then the model must be saved again. Once
this is done, the command \texttt{export model.tec.1} within the
\file{[project_rootname].def} batch script will generate a \file{model.tec.1.sd1}
file as needed for \FunThreeD-based design optimization.

\paragraph{User-Defined Parameterizations}\label{s:user_def_param_files.details}

In the event that a user-defined geometric parameterization package is to
be used, the user must provide a file \file{user_def_param_files.data}.  Since
\FunThreeD will not be aware of any auxiliary files that may be needed by
the user-defined parameterization package, those files should be listed here,
with a single file name per line.  Each file named here must be provided by
the user.  At run time, \FunThreeD will move the named files to the appropriate
location prior to execution of the parameterization executable indicated by the
user in the \texttt{\&design} namelist in \file{design.nml}.  See also
\sectionref{s:custom_parameterization} and \sectionref{s:nml_design}.

\subsubsection{\texttt{rubber.data}}\label{s:rubber.data.details}

This section describes how to set up each block of the design
control file \file{rubber.data}.  The template provided in the
\path{Adjoint} directory of the source code distribution is installed
in the \cmd{description.i} directory during setup.  This file serves
as the primary control file during the course of the optimization and
stores all of the high-level information relevant to the design.  The
file is repeatedly read and updated by the various tools during the
design procedure.  A simple example of this file to be used for discussion
purposes is shown below.

{\scriptsize
\begin{verbatim}
################################################################################
########################### Design Variable Information ########################
################################################################################
Global design variables (Mach number, AOA, Yaw, Noninertial rates)
  Var Active         Value               Lower Bound            Upper Bound
 Mach    0   0.800000000000000E+00  0.000000000000000E+00  0.900000000000000E+00
  AOA    1   1.000000000000000E+00  0.000000000000000E+00  5.000000000000000E+00
  Yaw    0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
xrate    0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
yrate    0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
zrate    0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
Number of bodies
    2
Rigid motion design variables for 'wing'
  Var Active         Value               Lower Bound            Upper Bound
RotRate  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotFreq  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotAmpl  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotOrgx  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotOrgy  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotOrgz  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotVecx  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotVecy  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotVecz  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnRate  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnFreq  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnAmpl  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnVecx  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnVecy  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnVecz  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
Parameterization Scheme (Massoud=1 Bandaids=2 Sculptor=4 User-Defined=5)
    1
Number of shape variables for 'wing'
    3
Index Active         Value               Lower Bound            Upper Bound
    1    1   1.000000000000000E+00  0.000000000000000E+00  2.000000000000000E+00
    2    1   1.000000000000000E+00  0.000000000000000E+00  2.000000000000000E+00
    3    1   1.000000000000000E+00  0.000000000000000E+00  2.000000000000000E+00
Rigid motion design variables for 'tail'
  Var Active         Value               Lower Bound            Upper Bound
RotRate  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotFreq  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotAmpl  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotOrgx  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotOrgy  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotOrgz  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotVecx  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotVecy  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
RotVecz  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnRate  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnFreq  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnAmpl  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnVecx  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnVecy  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
TrnVecz  0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
Parameterization Scheme (Massoud=1 Bandaids=2 Sculptor=4 User-Defined=5)
    2
Number of shape variables for 'tail'
    2
Index Active         Value               Lower Bound            Upper Bound
    1    1   2.000000000000000E+00 -1.000000000000000E+00  5.000000000000000E+00
    2    1   2.000000000000000E+00 -1.000000000000000E+00  5.000000000000000E+00
################################################################################
############################### Function Information ###########################
################################################################################
Number of composite functions for design problem statement
    2
################################################################################
Cost function (1) or constraint (2)
    1
If constraint, lower and upper bounds
    0.0 0.0
Number of components for function   1
    1
Physical timestep interval where function is defined
    1 1
Composite function weight, target, and power
1.0 0.0 1.0
Components of function   1: boundary id (0=all)/name/value/weight/target/power
    0 cl            0.000000000000000    1.000   10.00000 2.000
Current value of function   1
   0.000000000000000
Current derivatives of function wrt global design variables
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt rigid motion design variables of body   1
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt shape design variables of body   1
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt rigid motion design variables of body   2
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt shape design variables of body   2
   0.000000000000000
   0.000000000000000
################################################################################
Cost function (1) or constraint (2)
    2
If constraint, lower and upper bounds
    -0.03 -0.01
Number of components for function   2
    1
Physical timestep interval where function is defined
    1 1
Composite function weight, target, and power
1.0 0.0 1.0
Components of function   2: boundary id (0=all)/name/value/weight/target/power
    0 cmy           0.000000000000000    1.000    0.00000 1.000
Current value of function   2
   0.000000000000000
Current derivatives of function wrt global design variables
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt rigid motion design variables of body   1
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt shape design variables of body   1
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt rigid motion design variables of body   2
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt shape design variables of body   2
   0.000000000000000
   0.000000000000000
\end{verbatim}}

\paragraph{Global Design Variable Data}

This section of \file{rubber.data} lays out global design variables for
the computation.  These include the freestream Mach number, angle of attack,
and angle of yaw.  For simulations using a noninertial reference frame, the
noninertial rotation rates about each of the three Cartesian coordinate axes
are also available as design variables.

Quantities associated with each variable are specified on their own row
in the file and have several attributes that must be set by the user.
The first column is a dummy index and is merely to assist the user in
quickly navigating through the file.  The second column is a toggle to
activate the design variable.  If this value is a \var{1}, the variable
will be allowed to change during the design.  If the value is assigned
a \var{0}, this variable will be held constant at the value specified.
For incompressible flows and mixed-element grids, the Mach number must
be declared inactive.  Similarly, for flows posed in the inertial reference
frame, the noninertial rates must be declared inactive.

The third column in the design variable block is the current value for
this design variable.  The values of any active variables in this file
will take precedence over other input decks during design. 
For example, the flow
solver will run an angle of attack of \var{1} degree in this case,
regardless of what may be specified in \texttt{fun3d.nml}.
Columns four and five specify the upper and lower bounds for the
current design variable.

\paragraph{Body-Specific Design Variable Data}

The next input following the Mach number and angle of attack entries
specifies the number of bodies for which the user has provided shape
parameterizations.  Note that not every body in the grid must
be included here.  If the wing of an aircraft is the sole focus of the
optimization, there is no need to account for other boundaries such as
the tail or fuselage here.

Following the number of bodies, there should be two blocks of design
variables for each desired body, namely a list of rigid
motion variables controlling the dynamics of the body, and a set of shape
parameters controlling the shape of the body.  The columns of inputs
are identical to those described above for Mach number and angle of attack.

The bodies present in the computation may be listed in any order; however,
the order of their appearance in this control file must match
the integer suffix on their parameterization files that are provided
in the \path{description.i} directory, as well as files such as
\file{body_grouping.data}, \file{transforms.j}, etc.

The first section for the current body specifies design variables governing
rigid body motion and is only applicable for time-dependent problems.
For optimization of steady flows and/or static geometries, the
rigid motion data is irrelevant but must be present in this file.
These variables should be set as inactive in these cases.

The next block of inputs relates to the shape parameterization for the
current body.  First, the parameterization scheme is identified by a
scalar integer.  The following values are available:  (1) MASSOUD,
(2) Bandaids, (4) \Sculptor, and (5) User-Defined.  The next input
specifies the number of parameterized shape variables on the current
body and the subsequent lines lay out the design variable information
for that body.  A row of data must be provided for every variable in
the parameterization, whether it is active or not.  (Note however, that
internally, the optimizer is only made aware of the variables marked
as active.)  If a parameterization contains 25 variables, then 25 rows
must appear in this corresponding block of \file{rubber.data}, even
if only a subset is active.  If the design variable linking feature in
MASSOUD or Bandaids has been used to create additional derived variables,
they must also appear here.  Note that the ``Active'' attribute for
shape variables may take values of not only $0$ or $1$, but also $-1$
in certain multi-point design scenarios (see \sectionref{s:mp-design}).

Care should be taken in choosing upper and lower bounds for shape
variables.  Optimizers tend to fully explore the design space, which may
result in infeasible shapes (or extreme shapes the mesh movement/solvers
cannot handle robustly).  Set these limits conservatively; one can always
restart a design with less restrictive bounds.

As noted previously, when using \Sculptor parameterizations for multiple
bodies, all such design variables must appear as a single concatenated
body in \texttt{rubber.data}.

\paragraph{Cost Function/Constraint Specification}

The first line following the design variable block specifies the total
number of composite functions to be used as objectives or constraints
for the current design point.  Multiple composite objective functions
may be specified in certain cases; see \sectionref{s:mo-design}.
Otherwise, a single composite objective function must be specified.
The example file shown here
contains a single composite objective function based on the lift
coefficient and a single explicit constraint based on the pitching moment.
Note that explicit constraints may only be specified if the optimization
package chosen in the \texttt{\&design} namelist in
\file{design.nml} supports them.

Following the scalar value specifying the total number of composite
objectives and constraints, each composite function and/or constraint
will have a block of data associated with it.  Objective functions and
constraints may be specified in any order.

The first two inputs in the composite function block specify a scalar
integer indicating how the current function is to be viewed by the
optimizer.  The two subsequent inputs represent lower and upper bounds
on the function if it is to be used as a constraint.
If the function is an objective function, the first input value should be
\var{1}, and the lower and upper bounds must be present but their values
are irrelevant.  However, if the current function is to be used as a
constraint, special attention must be paid to these inputs depending on
the optimization package being used.
\subparagraph{Constraints Using \NPSOL and \SNOPT}
If the current function is to be used as an inequality constraint, the
first input should be \var{2}, and the lower and upper bounds should be
set to their appropriate values.  If the current function is to be used
as an equality constraint, the first input should be \var{2}; however,
the lower and upper bounds should both be set equal to the desired
constraint value.

\subparagraph{Constraints Using KSOPT and \DOT}
These optimization packages assume constraint functions of the form
$f\leq0$, such that the bound of the feasibility region is implicit
in the function definition and the lower and upper bound inputs must
be present but are not used.  If the current function is intended as
an inequality constraint, the first input should be \var{2}.
If the current function is intended as an equality constraint, the
first input value should be \var{3}.
In this case, \FunThreeD's design driver will provide the
current function to the optimizer as an inequality constraint, but will
also bookkeep an equal and opposite function as an additional inequality
constraint.  In this manner, an equality constraint is achieved by only
allowing the intersection of the two inequality constraints as feasible.

\bigskip  %force an empty space here
Following the classification of the current function, the next line
states how many component functions comprise the current
composite function.  This can be any positive integer greater than
or equal to \var{1}.  Following the number of component functions,
the user must specify the physical time step interval over which the
function is to be applied.  This input is only relevant to optimization
of unsteady flows.  For steady flows, the values of these two inputs
are ignored but must be present.

The weight, target, and power to be applied to the current composite
function are specified next.  These values are only relevant when
combining multiple composite objective functions into a single global
objective function (see \sectionref{s:mo-design}).
For all other cases, these values should be specified as \var{1.0},
\var{0.0}, and \var{1.0}, respectively.

At this point, each component function that contributes to the current
composite function has a line specifying several pieces of data.
The first column is the boundary patch over which to apply the current
component.  This index corresponds directly to the boundary patches
in the CFD grid, and must reflect any patch lumping that is indicated in
the \texttt{\&raw\_grid} namelist in \texttt{fun3d.nml}
(see \sectionref{s:nml_raw_grid}).  If a component
function is to be used over the entire grid (total drag, for example),
simply put a \var{0} in this column.  Alternatively, if a single boundary
patch is to be targeted, one might apply the component function to only
that patch.  Several patches may be targeted by including a component
function for each.  The next column is the keyword for the aerodynamic
quantity to be used for the current function component.  For a list of
available keywords, see \sectionref{s:obj-con-fcns}.  The next
column contains the current value of the current function component.
This is an output value during the optimization and need not be set by
the user.  The final three columns in the row correspond to the weight,
target value, and power to be applied to the current component function
in constructing the overall composite function.

\paragraph{Current Function Value and Sensitivities}

Following the specification of the component functions, the next line
of \texttt{rubber.data} contains the current value of the composite
function.  This is an output and need not be set by the user.

The remaining lines in the current function block contain the sensitivity
derivatives with respect to all of the design variables listed in the
top half of the file.  This section is divided into derivatives with
respect to the global design variables, as well as the rigid motion
and shape design variables for each of the bodies laid out in the top
portion of the file.  These derivatives are outputs set by \FunThreeD
and not by the user.  However, a line for each design variable (both
global variables as well as body-specific variables) must be provided
in each composite function block present.  The values do not matter,
but the solvers need positions available in the file to store the current values.

\subsubsection{\texttt{ae\_target.dat} (optional)}

If the function keyword \texttt{ae} is specified
anywhere in \file{rubber.data}, the file \file{ae_target.dat} must
be present prior to performing the optimization.  This file provides
the target equivalent area distribution(s) for each of the azimuthal
locations specified in the \texttt{\&equivalent\_area} namelist in
\file{fun3d.nml} (in the same order).  See \sectionref{s:ae-details} and
\sectionref{s:nml_equivalent_area}.

\subsubsection{\texttt{body\_grouping.data} (optional)}\label{s:body_grouping.details}

This file is used to specify body grouping information.  For example,
if the objective function is the Figure of Merit $FM$ for a three-bladed
rotor, then the three blades (each typically specified as a separate
parameterized body in \file{rubber.data}) should be associated into
one group, so that sensitivity derivatives will reflect a composite
$\partial{(FM)}/\partial{D}$ for all three blades.  This capability
requires that the bodies to be associated all have identical
parameterizations (same number of design variables on each body, etc).
The format of the \file{body_grouping.data} file is as follows:

\begin{Verbatim}
  Number of groups to create
  1
  Number of bodies in group, list of bodies
  3
  1 2 3
\end{Verbatim}
The first scalar integer specifies the number of groups to create (i.e.,
one rotor).  The next set of inputs specifies the number of bodies
in each group, followed by the bodies that comprise that group (i.e.,
each of the three rotor blade bodies).

\subsubsection{\texttt{command\_line.options} (optional)}

The \texttt{command\_line.options} file specifies any command line options
to be used with the flow solver, the adjoint solver, or the MPI job launcher
(\texttt{mpirun}, \texttt{mpiexec}, \texttt{aprun}, etc).  An example of
this file is shown below.

\begin{verbatim}
  3
  1 flow
  '--rmstol 1.e-7'
  1 adjoint
  '--rmstol 1.e-3'
  2 mpirun
  '-nolocal'
  '-machinefile ../machinefile'
\end{verbatim}
The first line of the file specifies the number of
programs for which command line options are being provided.  The subsequent
line must contain an integer followed by a keyword.  The integer specifies
how many command line options are being provided for the code identified
by the keyword.  The valid keywords are \var{flow}, \var{adjoint}, and
\var{mpirun}.  This line is followed by a line for each of the command
line options provided for the code identified by the keyword.
Each command line option should appear in single quotation marks on its
own line.  The specified programs and their associated command line
options may appear in any order.

If a \texttt{-np x} option is provided as a command line option to the
MPI job launcher to specify the number of processes to run (given by the
value \var{x}), this value will
override the value specified for execution of the adjoint solver in the
\texttt{\&design} namelist in \file{design.nml} (see \sectionref{s:nml_design}).

\subsubsection{\texttt{cpstar.data.j} (optional)}

\FunThreeD has an inverse design capability where the objective function
may be composed of target pressure distributions.  The file containing
the \texttt{j}th target distribution must be named \file{cpstar.data.j}.
However, setup is tedious, primarily due to the difficulty in specifying
pressure distributions on a three-dimensional configuration.  If this
capability is of interest, please contact \funsupport for more detailed
guidance.

\subsubsection{\texttt{files\_to\_save.data} (optional)}\label{s:files_to_save}

If desired, users may indicate specific files produced by the flow and adjoint
solvers to be archived during an optimization.  For example, custom visualization
files may be produced at each design iteration (see \sectionref{s:flowvis}) to
enable animations of the design history after the optimization is complete.

To specify certain files to be archived during the course of a design execution,
the user may provide the optional file \file{files_to_save.data} in the
\file{description.i} directory.  Each line of the file consists of one of two
keywords, either ``Flow'' or ``Adjoint'', followed by the specific name of a file
to be archived.  For example, the following inputs could be used to archive \Tecplot
files containing solution data on boundaries for both the flow and adjoint solvers,
while also saving two additional \Tecplot files containing user-specified sampling
data for the flow solution:

\begin{verbatim}
Flow [project_rootname]_tec_boundary.dat
Flow [project_rootname]_tec_sampling_geom1.dat
Flow [project_rootname]_tec_sampling_geom2.dat
Adjoint [project_rootname]_tec_boundary.dat
\end{verbatim}
At the end of each function evaluation (i.e., flow solution) for the
\texttt{i}th design point, the files \file{[project_rootname]_tec_boundary.dat},
\file{[project_rootname]_tec_sampling_geom1.dat}, and \file{[project_rootname]_tec_sampling_geom2.dat}
will be stored in the \file{model.i/Flow/SaveFiles} directory, with an integer
corresponding to the current design iteration appended to each of the filenames.
Similarly, the file \file{[project_rootname]_tec_boundary.dat} will be stored in the
\file{model.i/Adjoint/SaveFiles} directory at the completion of each sensitivity
analysis (i.e., adjoint solution).

\subsubsection{\texttt{machinefile} (optional)}

If the optimization will be executed in an environment which requires an
explicit list of machines on which the MPI jobs will be executed, this
file must be present.  It should take the format required of the
particular MPI implementation being used.  If the optimization will be
executed in an automated queuing environment, the job scheduler normally
assigns the machines to be used at runtime and this file is therefore not
required.

\subsubsection{\texttt{pressure\_target.dat} (optional)}

If the function keyword \texttt{boom\_targ} is specified
anywhere in \file{rubber.data}, the file \file{pressure_target.dat} must
be present prior to performing the optimization.  This file provides
the nearfield target $p/p_\infty$ distribution(s) for each of the off-body
locations specified in the \texttt{\&sonic\_boom} namelist in
\file{fun3d.nml} (in the same order).  See \sectionref{s:boom-targ-details} and
\sectionref{s:nml_sonic_boom}.

\subsubsection{\texttt{transforms.i} (optional)}\label{s:body_transforms.details}

Since MASSOUD uses a coordinate system specific to an assumed
aircraft orientation, it is sometimes necessary to reorient a body from its
physical position to a MASSOUD-aligned coordinate system and vice-versa.
Examples might include a vertical tail or the various blades of a rotor
system.  The file describing the transform for the \cmd{i}th body 
should be included as \file{transforms.i}.
The format of a typical \file{transforms.i} file is as follows:
\begin{Verbatim}
  ROTATE 0.0 0.0 1.0 -120.0
\end{Verbatim}
This would rotate the MASSOUD parameterization for the \cmd{i}th body
by $-120$ degrees about a unit vector in the $+z$ direction.  
The commands \cmd{TRANSLATE} and \cmd{SCALE} are also available. 

\subsection{Contents of the \texttt{model.i} Directory}

Just as for the \texttt{description.i} directories, the \texttt{i} in
the \texttt{model.i} naming convention represents the design point
index.  This value is \texttt{1} for single point design or the
design point index for multi-point design.  The \texttt{model.i}
directory contains the subdirectories \file{Flow}, \file{Adjoint},
and \file{Rubberize}.  During the course of the design procedure,
\FunThreeD will evaluate the relevant parameterizations and perform
flow and adjoint solutions within these locations.  These
subdirectories are populated during the initial setup procedure with links
to the required executables from the user's \FunThreeD installation.  
Files in the \texttt{model.i} subdirectories should not be modified
by the user, although one may wish to observe various solver output files during
the course of the optimization.  All user-provided inputs are confined
to files located in the \texttt{description.i} and \texttt{ammo} directories.

\subsection{Running the Optimization}

Once all of the required inputs and files have been provided, the user
should first request a single function evaluation from the optimization
driver.  This is strongly recommended in order to identify any potential
issues in the various inputs.  To perform this check, set the value of
\texttt{what\_to\_do} in the \texttt{\&design} namelist in
\file{design.nml} to \var{1}, and
execute the optimization driver from the \texttt{ammo} subdirectory:

\begin{Verbatim}
 opt_driver > screen.output &
\end{Verbatim}
Here, the output from the optimization driver has been redirected to a file
called \texttt{screen.output}.  This file is very useful if a problem
needs to be diagnosed with the execution.  It is also good practice to
include this file with help requests to \funsupport.

At the completion of the function evaluation, the user should check for the
desired/expected result.  This is also a good opportunity to establish
reasonable values for the number of time steps to run, the residual tolerance
at which the solver should quit, and so forth.  Such run control parameters may
be set in \texttt{fun3d.nml} or via the \texttt{command\_line.options} file.

Once the function evaluation procedure has been verified, the user should
perform the same test for a sensitivity analysis by setting
\texttt{what\_to\_do} in the \texttt{\&design} namelist in
\file{design.nml} to \var{2} and re-executing
the optimization driver.  Similar checks on convergence parameters, etc
for the adjoint solver should be noted and applied to the relevant input
files.

With successful function and gradient evaluations in hand, an actual
optimization may be initiated.  The value of \texttt{what\_to\_do}
in the \texttt{\&design} namelist in \file{design.nml} should be set to
\var{3}, and the optimization driver
can be executed as before.  The user should closely monitor the screen output
as the process proceeds, especially during the first several design cycles
when input parameters may first cause problems.  The largest changes in design
variables often occur early on as well, which can cause issues with mesh
movement operations, solver convergence, and other aspects.  It is also
very useful to occasionally filter the screen output for the current function
value(s) reported at the completion of each flow solution in order to monitor
overall progress:

\begin{Verbatim}
 grep "Current value of function" screen.output
\end{Verbatim}

When an optimization completes, the optimizer will report the reason
for the termination to the screen, which may be a local minimum,
or some problem encountered during the simulation.  A summary of the
optimization is provided by each optimization package in the file(s)
noted in \tab{t:design-results}.  The final set of design variables and
function/constraint values determined by the optimizer will be available
in \file{model.i/rubber.data}.  To track the history of the optimization,
a backup of all intermediate copies of \texttt{rubber.data} are stored in
the directory \file{model.i/Rubberize/surface_history}.  Intermediate
copies of the surface grids developed during the design process are also
stored in this location as \file{model.tec.j.sd1.iter}, where \var{j} is
the body index, and \var{iter} is the design iteration.  These files may
be used to produce animations of the surface history if desired.  Using
the broad range of visualization output options for the solvers, the
user has great freedom to produce customized animations of the
design history (see \sectionref{s:files_to_save}).

\begin{table}[hbtp]
  \centering\tabularfont
  \caption{Files containing design summary from various optimization packages.}\label{t:design-results}
  \begin{tabular}{ll}
   Optimization Package & Summary File(s) \\
   \midrule
    \DOT   & dot.output \\
    KSOPT  & ksopt.output \\
    PORT   & port.output \\
    \NPSOL & npsol.printfile, npsol.summaryfile \\
    \SNOPT & snopt.printfile, snopt.summaryfile \\
  \end{tabular}
\end{table}

\subsubsection{Filesystem Latency Problems}\label{s:fs-latency}

Design optimization using some cached file systems may experience
problems due to the rapid execution of the various tools during the
design process.  In some cases, a file system lag may cause some
processes to receive older/stale versions of files during execution.
Specifying the \texttt{--sleep\_delay [seconds]} command line option 
to the \texttt{opt\_driver} executable will pause the optimization process
with a sleep duration of \texttt{seconds} between subsequent code
executions to allow the file system to perform correctly.  On older
systems, delays as large as 60 seconds are sometimes necessary; more recent
systems seem to perform considerably better and values of 5-10 seconds
are often sufficient.

\subsection{Multi-objective Design}\label{s:mo-design}

KSOPT, PORT, and \SNOPT are the only packages currently supported for
use with multi-objective design.  Details on the usage for each package
are provided below.

\subsubsection{KSOPT}

KSOPT is the only supported optimization package with explicit support
for multiple objective functions.  When using KSOPT, the user may designate
any number of composite functions as objective functions in \file{rubber.data}.

\subsubsection{PORT, \SNOPT}

The \FunThreeD design driver offers a simple approach to scalarizing
multiple user-specified objective functions for use with the PORT or
\SNOPT packages.  If multiple composite functions are specified in
\file{rubber.data}, the \FunThreeD design driver will combine them using
the weight, target, and power values specified at the \emph{composite} function level (i.e., the input
values that appear just before the \emph{component} function data is
specified in \file{rubber.data}, see \sectionref{s:rubber.data.details}).
If $N$ composite functions $f$ are labeled as objective functions in
\file{rubber.data}, the scalarized objective function $F$ to be provided
to the optimization procedure will take the form

\begin{equation}
F = \alpha_1 (f_1-f_1^*)^{p_1} + \alpha_2 (f_2-f_2^*)^{p_2} + ... + \alpha_N (f_N-f_N^*)^{p_N}
\end{equation}
where $\alpha_i$, $f_i^*$, and $p_i$ are the weight, target, and power values
associated with each \emph{composite} function in \file{rubber.data}.

\subsection{Multi-point Design}\label{s:mp-design}

The \FunThreeD design infrastructure offers several approaches to multi-point
optimization.  This refers to design problems where the user
may wish to simultaneously optimize a configuration for operations at two
different conditions --- perhaps the beginning and end of a cruise segment for
example, where the aircraft weight may be substantially different.  The user may also
wish to design for cruise and takeoff or landing (or all three).  The various
design points may be characterized by different flow conditions (i.e., speed,
angle of attack, etc), or more generally, by the geometries (and therefore grids)
at each point.  For example, one design point may consist of a cruise geometry
operating at Mach 0.8, while another design point may be a landing configuration
operating at Mach 0.2 with a high-lift system deployed.  For examples of
\FunThreeD-based multi-point design in practice, see the studies in \cite{nielsen-noninJ}
and \cite{jones-nonin}.  In these references, a tilt-rotor geometry has been optimized for
a set of several blade collective settings as well as hover and forward flight
conditions.

To perform a multi-point optimization, the user must request the desired number
of design points when setting up the directory structure where the design will
be performed (see \sectionref{s:setup-design}).  The user must populate each of
the \texttt{description.i} directories for each design point \var{i} just as
in the single-point design context.  The order of the design points does not
matter.  The value of \var{n_design_pts} in the \var{&design} namelist in
\file{design.nml} should be set appropriately.  Ultimately, \FunThreeD provides
several ways to formulate the multi-point design problem.  These approaches are
outlined below.

In general, the optimizer will be seeking a unique set of design variables to
simultaneously achieve goals at all of the design points.  For this reason,
a consistent set of design variables across all design points must be used.
This applies to the global variables Mach number and angle of attack as well as
any body-specific variables such as shape parameters.  For example, if a set of 15
thickness variables is provided for a wing shape in cruise, other design
points (again, perhaps a landing configuration as an example) must utilize
the same set of 15 thickness variables.  Moreover, the same subset of design
variables must be active at each design point.

\paragraph{Multi-valued Design Variables}
In some situations, the user may desire different optimal
values of a design variable at different design points.  For example, consider
power minimization for a rotor in hover at two different weight conditions,
where each of the two design points may have different minimum thrust coefficients
posed as constraints.  In addition to other design variables that may be present,
the user may have a shape parameter controlling the blade collective setting (blade
pitch).  However, rather than constraining the optimal blade collective to a single
unique value, the user may desire separate, optimal values for each design point.
As another example, consider a configuration with an ability to actively morph
its outer mold line.  In this case, the user may wish to determine optimal values
of the shape parameters that are unique to different design points.

To accommodate such multi-valued design variables, the user may set the
``Active'' attribute for individual shape design variables to $-1$ in \file{rubber.data}
(see \sectionref{s:rubber.data.details}).  If this is done, it must be applied
consistently for that same variable across all design points.  For variables
with this attribute, the \FunThreeD design driver will internally bookkeep separate values of the
variable for each design point.  This feature is currently only available for use
with the \SNOPT package.

\subsubsection{Linear Combination of Objective Functions}

The most straightforward approach to multi-point design is to linearly combine
individual objective functions $f_i$ from each of the $N$ design points
\var{i} into a single global objective function $f_{mp}$:

\begin{equation}
f_{mp} = \alpha_1 f_1 + \alpha_2 f_2 + \alpha_3 f_3 + ... + \alpha_N f_N
\end{equation}
To perform the optimization in this fashion, a single composite objective
function should be posed in each \file{description.i/rubber.data} file.
Each of the $\alpha_i$ weighting coefficients must be specified in
the \var{design_pt_weight(:)} array in the \var{&design} namelist in
\file{design.nml}, in the corresponding order.

This form of multi-point design is supported by PORT, \DOT, and \SNOPT.  Note
that PORT and \SNOPT will also combine multiple objective functions
within each design point as described in \sectionref{s:mo-design} if desired.
Explicit constraints can be posed at each design point when using \DOT or
\SNOPT; such constraints are each treated individually.

\subsubsection{Combination of Objective Functions using the Kreisselmeier-Steinhauser Function}

Another alternative for performing multi-point design is the approach
inherent in the KSOPT package.  In this approach, all objective functions
and constraints are combined using the Kreisselmeier-Steinhauser (KS) function.
The user is referred to \cite{wrenn-ksopt-nasa-cr} for the details of this
formulation.  Here, the \FunThreeD design driver gathers any number of objective
and constraint functions across all design points and provides them to KSOPT,
which internally constructs its KS function for the actual optimization problem.

\subsubsection{Single-Point Objective Function with Off-Design Constraint Functions}

In this approach to multi-point design, a single objective function is provided
to the optimizer, while all other functions are treated as explicit constraints.
Here, the user should designate a single composite function across all of the
\file{description.i/rubber.data} input files as an objective function.  Any other
composite functions at each design point should be designated as constraint
functions.  KSOPT, \SNOPT, and \NPSOL support this form of multi-point optimization;
\SNOPT can also construct the final objective function by linearly combining
multiple objective functions within the desired design point as described in
\sectionref{s:mo-design}.

\subsection{Optimization of Two-Dimensional Geometries}

While the \FunThreeD flow solver supports a 2D mode of operation,
this capability is not currently available from within the design
infrastructure.  Instead, the optimization must be performed as a
pseudo-3D case.  The user should provide a nominally two-dimensional grid,
with a single layer of elements in the spanwise (\var{y}) direction.
The mesh should consist of either prisms or hexahedra (or both), but
should contain no pyramids or tetrahedra.  Follow the same procedure
used for 3D cases to extract the surface grid for parameterization.
The surface should be parameterized just as for a 3D simulation; however,
the parameterization should allow no spanwise asymmetries in the geometry
to develop.  When using MASSOUD or Bandaids, this is readily accomplished
by linking the raw parameters with an equal weighting across the span
into a single set of design variables that operate in a chordwise fashion.
Note that the sidewalls should
use \texttt{symmetry\_y} boundary conditions so that only in-plane
mesh deformation occurs during the optimization.  The design may now be
executed as usual, with the 2D nature of the problem enforced implicitly
through the parameterization.

\subsection{Using a Different Optimization Package}

In a CFD-based design context, the term ``function'' implies an
evaluation of the geometric parameterization, mesh movement (both
surface and volume), a flow solution, and an evaluation of the output
function/constraint for a given set of design variables.  The file
manipulations and solver operations necessary to achieve this are not
trivial.  For users interested in using the tools as ``black boxes''
providing function data for an optimization package, a wrapper has
been provided in the \texttt{LibF90} directory of the distribution
named \file{analysis.f90}.  This module contains a subroutine called
\texttt{perform\_analysis()} which performs the extensive set of tasks
involved with producing the final desired function output.

To obtain sensitivities, the \FunThreeD package relies on a discrete
adjoint formulation.  As with function evaluations, the low-level operations
required to perform an adjoint-based sensitivity analysis are numerous.
A wrapper routine called \texttt{perform\_sensitivity\_analysis()}
in the \texttt{LibF90/sensitivity.f90} module will perform an
adjoint solution for the flow field, an adjoint solution for the mesh
movement scheme, an evaluation of the linearized geometric parameterization,
and finally produce the desired sensitivity derivatives.

The \FunThreeD design driver uses the wrappers \texttt{perform\_analysis()} and
\texttt{perform\_sensitivity\_analysis()} to greatly simplify
function and gradient evaluations when connecting to off-the-shelf
optimization packages.  If the user wishes to implement a new optimization
strategy, it is highly recommended that these wrappers be used in a
similar fashion.  A review of the existing modules in the \texttt{Design}
directory of the \FunThreeD source code distribution, which implement the
currently available optimization interfaces, is also strongly suggested.
Users may contact \funsupport for further guidance in leveraging
\FunThreeD's capabilities from within their own existing design framework.

\subsection{Implementing New Cost Functions/Constraints}

Implementation of new cost functions or constraints is not a trivial
undertaking and requires extensive modification of \FunThreeD source
code.  Experience in Fortran 2003, unstructured-grid discretizations,
development in a domain-decomposed/distributed-memory environment, and
general CFD methods are essential.  Routines to evaluate the proposed function
and linearizations of the function with respect to both the flow field
variables and grid are ultimately required.  The complex-variable form of
\FunThreeD (see \sectionref{s:complex-run}) is invaluable in verifying the
accuracy of these linearizations.  It is highly recommended that the user
contact \funsupport for guidance prior to attempting the implementation
of a new cost function or constraint.

\subsection{Forward Mode Differentiation Using Complex\\Variables}\label{s:complex-run}

The reverse, or adjoint, mode of differentiation is
primarily used for design with \FunThreeD.  A forward mode of
differentiation is also provided based on the use of complex variables
\cite{Lyness67a,LyMo67a,ComplexJ}.  This capability is useful for design
problems containing few design variables and many cost functions or
constraints.  To generate and build a complex-variable \FunThreeD executable,
see \sectionref{s:configure-complex}.

The complex-valued flow solver reads the usual real-valued grid files
and is set up to compute derivatives of every output variable with respect to Mach
number, angle of attack, shape parameters, non-inertial rotation rates, or
the $x$, $y$, or $z$ coordinate of a single grid point (others are trivial
to add).  This choice is controlled by
the file \file{perturb.input}.  A template for this file is provided in the
\path{FUN3D_90} directory and an example is also shown below.

\VerbatimInput{FUN3D_90/perturb.input}

The value of \var{PERTURB} specifies the variable for which sensitivities
will be taken with respect to.  The valid integer values are as shown above.
The input \var{EPSILON} specifies the magnitude of the imaginary perturbation
to be applied.  The recommended value is \var{1.e-50}.  If the value of
\var{PERTURB} is greater than six, the value of
\var{GRIDPOINT} specifies the grid point index to perturb.  The remaining lines
in \file{perturb.input} are not read; they are simply reminders of
the valid inputs just described. The complex-valued flow solver may then be
executed in a manner similar to the real-valued flow solver:

\begin{Verbatim}
  mpirun ./complex_nodet_mpi
\end{Verbatim}
To compute derivatives with respect to a shape parameterization
variable, the sensitivities of the parameterization must first be
evaluated in the directory \texttt{model.i/Rubberize} using the relevant
parameterization software.  The value of \var{PERTURB} should be set
to \var{3} in \texttt{perturb.input}.The complex-valued flow solver can
then be executed in the following fashion:

\begin{Verbatim}
  mpirun ./complex_nodet_mpi --dv_index [body] [dv] --snap_grid
\end{Verbatim}
Here, the values of \texttt{body} and \texttt{dv} specify the body and
design variable index in \texttt{rubber.data} to which to apply the
imaginary perturbation.  The \texttt{--snap\_grid} argument forces the
flow solver to propagate the surface sensitivities into the volume mesh
using \FunThreeD's elasticity-based deformation mechanics.

At the completion of the complex-valued flow solve, outputs will contain both
real and imaginary parts.  The imaginary part represents the sensitivity of that
output with respect to the perturbation variable that was specified in
\texttt{perturb.input}.

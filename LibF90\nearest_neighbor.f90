module nearest_neighbor

  use kinddefs,   only : dp

  implicit none

  private

  public :: Tree, Node, Pair
  public :: buildTree, destroyTree,  findNearestNodeInTree
  public :: nearest_nodes_to_subset, findWithinRadius, findNearestSurfaceNodes

! exposed for unit testing
  public :: operator(==), operator(-)
  public :: descendTree
  public :: initializetree, addnodeto

  type Node
    real(dp)    :: xCoordinate=0.0_dp
    real(dp)    :: yCoordinate=0.0_dp
    real(dp)    :: zCoordinate=0.0_dp
    integer :: id=0
  end type Node

  type Pair
    type(Node) :: rightNode,                   leftNode
    real(dp)   :: rightMaxDistance=-1.0_dp , leftMaxDistance=-1.0_dp
    logical    :: rightEmpty = .TRUE. ,        leftEmpty = .TRUE.
    integer    :: rightChild = 0,              leftChild = 0
  end type Pair

  type Tree
    integer                           :: numOfNodes
    type(Pair), dimension(:), pointer :: pair
  end type Tree

  interface operator (-)
    module procedure distanceBetween
  end interface

  interface operator (==)
    module procedure nodeComparison
  end interface

contains

!========================= NEAREST_NODES_TO_SUBSET ===========================80
!
! Description goes here.
!
!=============================================================================80

  subroutine nearest_nodes_to_subset(totalnodes,x,y,z,                         &
           subsetnodes,subset,                                                 &
           slen,ielemslen,                                                     &
           nsurfnodes,xsurf,ysurf,zsurf,                                       &
           part1,myidp1)

    integer,                          intent(in)  :: totalnodes
    real(dp), dimension(totalnodes),  intent(in)  :: x, y, z
    integer,                          intent(in)  :: subsetnodes
    integer,  dimension(subsetnodes), intent(in)  :: subset
    real(dp), dimension(totalnodes),  intent(out) :: slen
    integer,  dimension(totalnodes),  intent(out) :: ielemslen

! pparty related entities
! These variables allow for external nodes (i.e., non-subset nodes)
! to be compared to the tree.  If no external nodes, then set nsurfnodes to 0;
! all other arguments are optional.  If external nodes are present, then all
! additional arguments are needed.

    integer,                                   intent(in) :: nsurfnodes
    real(dp), dimension(nsurfnodes), optional, intent(in) :: xsurf
    real(dp), dimension(nsurfnodes), optional, intent(in) :: ysurf
    real(dp), dimension(nsurfnodes), optional, intent(in) :: zsurf

! node partition
    integer, dimension(totalnodes),  optional, intent(in) :: part1
    integer,                         optional, intent(in) :: myidp1 ! mpid+1

    real(dp), dimension(subsetnodes + nsurfnodes) :: x_surf, y_surf, z_surf

    integer :: total_nsurfnodes

    integer :: node

    continue

    total_nsurfnodes = subsetnodes + nsurfnodes

    if (subsetnodes > 0) then
      do node = 1, subsetnodes
        x_surf(node) = x(subset(node))
        y_surf(node) = y(subset(node))
        z_surf(node) = z(subset(node))
      end do
    end if

    if (nsurfnodes > 0) then
      do node = 1, nsurfnodes
        x_surf(subsetnodes+node) = xsurf(node)
        y_surf(subsetnodes+node) = ysurf(node)
        z_surf(subsetnodes+node) = zsurf(node)
      end do
      call findNearestSurfaceNodes(totalnodes,  x,  y,  z,                     &
                        total_nsurfnodes, x_surf, y_surf, z_surf,              &
                        slen,  ielemslen, part1, myidp1)
    else
      call findNearestSurfaceNodes(totalnodes,  x,  y,  z,                     &
                        total_nsurfnodes, x_surf, y_surf, z_surf,              &
                        slen,  ielemslen)
    end if

  end subroutine nearest_nodes_to_subset


!========================= findNearestSurfaceNodes ===========================80
!
! Description goes here.
!
!=============================================================================80

  subroutine findNearestSurfaceNodes (nvol, x_vol, y_vol, z_vol,               &
    nsurf, x_surf, y_surf, z_surf, slen, nodeID, part1, myidp1)

    integer,                    intent(in)  :: nvol
    real(dp), dimension(nvol),  intent(in)  :: x_vol, y_vol, z_vol
    integer,                    intent(in)  :: nsurf
    real(dp), dimension(nsurf), intent(in)  :: x_surf, y_surf, z_surf
    real(dp), dimension(nvol),  intent(out) :: slen
    integer,  dimension(nvol),  intent(out) :: nodeID
    integer,  dimension(nvol),  optional, intent(in)  :: part1
    integer,                    optional, intent(in)  :: myidp1 ! pparty

    type(Tree) :: aTree
    type(Node) :: aNode, nearestNode
    real(dp) :: distance

    integer :: i
    logical :: part1_present

    continue

    call buildTree ( aTree, nsurf, x_surf, y_surf, z_surf )

    part1_present = present(myidp1) ! pparty

    do i = 1, nvol
      if (part1_present) then  ! pparty
            if (part1(i) /= myidp1) cycle ! cycle if not volume node
      end if
      aNode%xCoordinate = x_vol(i)
      aNode%yCoordinate = y_vol(i)
      aNode%zCoordinate = z_vol(i)
      call findNearestNodeInTree( aTree, aNode, nearestNode, distance )
      slen(i) = (aNode%xCoordinate-nearestNode%xCoordinate)**2                 &
              + (aNode%yCoordinate-nearestNode%yCoordinate)**2                 &
              + (aNode%zCoordinate-nearestNode%zCoordinate)**2
      nodeID(i) = nearestNode%id
    end do

    call destroyTree( aTree )

  end subroutine findNearestSurfaceNodes

!================================ initializeTree =============================80
!
! Description goes here.
!
!=============================================================================80

  subroutine initializeTree( aTree, maxNumOfNodes )

    integer,    intent(in)    :: maxNumOfNodes
    type(Tree), intent(inout) :: aTree

    continue

    allocate(aTree%pair(maxNumOfNodes-1))
    aTree%numOfNodes = 1

  end subroutine initializeTree

!=============================== destroyTree =================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine destroyTree( aTree )

    type(Tree), intent(inout) :: aTree

    continue

    deallocate( aTree%pair )

  end subroutine destroyTree

!=========================== findNearestNodeInTree ===========================80
!
! Description goes here.
!
!=============================================================================80

  recursive subroutine findNearestNodeInTree( aTree, aNode, nearestNode,       &
                                              smallestDistance, pairIndex )
    type(Tree),           intent(in)    :: aTree
    type(Node),           intent(in)    :: aNode
    type(Node),           intent(inout) :: nearestNode
    real(dp),             intent(inout) :: smallestDistance
    integer,    optional, intent(in)    :: pairIndex

    real(dp) :: distanceToLeftNode
    real(dp) :: distanceToRightNode

    integer :: indx

    continue

    ! start at top of tree unless given a starting point
    if (.not.present(pairIndex)) then
      indx = 1
      smallestDistance = Huge(1.0_dp)
    else
      indx = pairIndex
    end if

    distanceToLeftNode = aNode - aTree%pair(indx)%leftNode

    if ( distanceToLeftNode < smallestDistance )then
      nearestNode = aTree%pair(indx)%leftNode
      smallestDistance = distanceToLeftNode
    end if

    if (aTree%pair(indx)%rightEmpty) return

    distanceToRightNode = aNode - aTree%pair(indx)%rightNode

    if ( distanceToRightNode < smallestDistance )then
      nearestNode = aTree%pair(indx)%rightNode
      smallestDistance = distanceToRightNode
    end if

    if ( descendTree( distanceToLeftNode,                                      &
                      smallestDistance,                                        &
                      aTree%pair(indx)%leftMaxDistance) )                      &
      call findNearestNodeInTree( aTree, aNode, nearestNode,                   &
                                  smallestDistance,                            &
                                  aTree%pair(indx)%leftChild )

    if ( descendTree( distanceToRightNode,                                     &
                      smallestDistance,                                        &
                      aTree%pair(indx)%rightMaxDistance) )                     &
      call findNearestNodeInTree( aTree, aNode, nearestNode,                   &
                                  smallestDistance,                            &
                                  aTree%pair(indx)%rightChild )

  end subroutine findNearestNodeInTree

!=========================== findWithinRadius ================================80
!
! Description goes here.
!
!=============================================================================80

  recursive subroutine findWithinRadius( aTree, aNode, coarse_nnodes,          &
                                         node_list, search_radius,             &
                                         within_radius, npoints, pairIndex )

    type(Tree),                        intent(in)    :: aTree
    type(Node),                        intent(in)    :: aNode
    integer,                           intent(inout) :: npoints
    integer,                           intent(in)    :: coarse_nnodes
    integer, dimension(coarse_nnodes), intent(inout) :: node_list
    integer, dimension(coarse_nnodes), intent(inout) :: within_radius
    real(dp),                          intent(in)    :: search_radius
    integer,                 optional, intent(in)    :: pairIndex

    real(dp) :: distanceToLeftNode
    real(dp) :: distanceToRightNode

    integer :: indx

    continue

    ! start at top of tree unless given a starting point
    if (.not.present(pairIndex)) then
      indx = 1
    else
      indx = pairIndex
    end if

    distanceToLeftNode = aNode - aTree%pair(indx)%leftNode

    if ( distanceToLeftNode < search_radius ) then
      npoints = npoints + 1
      node_list(npoints) = aTree%pair(indx)%leftNode%id
      within_radius(node_list(npoints)) = within_radius(node_list(npoints)) + 1
    end if

    if (aTree%pair(indx)%rightEmpty) return

    distanceToRightNode = aNode - aTree%pair(indx)%rightNode

    if ( distanceToRightNode < search_radius )then
      npoints = npoints + 1
      node_list(npoints) = aTree%pair(indx)%rightNode%id
      within_radius(node_list(npoints)) = within_radius(node_list(npoints)) + 1
    end if

    if ( descendTree( distanceToLeftNode,                                      &
                      search_radius,                                           &
                      aTree%pair(indx)%leftMaxDistance) )                      &
      call findWithinRadius( aTree, aNode, coarse_nnodes, node_list,           &
                                  search_radius,                               &
                                  within_radius,npoints,                       &
                                  aTree%pair(indx)%leftChild )

    if ( descendTree( distanceToRightNode,                                     &
                      search_radius,                                           &
                      aTree%pair(indx)%rightMaxDistance) )                     &
      call findWithinRadius( aTree, aNode, coarse_nnodes, node_list,           &
                                  search_radius,                               &
                                  within_radius,npoints,                       &
                                  aTree%pair(indx)%rightChild )

  end subroutine findWithinRadius

!================================= descendTree ===============================80
!
! Description goes here.
!
!=============================================================================80

  function descendTree( distanceBetweenNodes, bestDistance, childDistance)

    logical :: descendTree

    real(dp), intent(in) :: distanceBetweenNodes, bestDistance, childDistance

    continue

    descendTree = ( (distanceBetweenNodes <= bestDistance+childDistance)       &
                    .and. (childDistance >= 0.0_dp) )

  end function descendTree

!================================= addNodeTo =================================80
!
! Description goes here.
!
!=============================================================================80

  recursive subroutine addNodeTo( aTree, aNode, pairIndex )

    type(Tree),           intent(inout) :: aTree
    type(Node),           intent(in)    :: aNode
    integer,    optional, intent(in)    :: pairIndex

    real(dp) :: distanceToLeftNode
    real(dp) :: distanceToRightNode

    integer :: indx

    continue

    ! start at top of tree unless given a starting point
    if (present(pairIndex)) then
      indx = pairIndex
    else
      indx = 1
    end if

    if ( aTree%pair(indx)%leftEmpty ) then ! add node
      aTree%pair(indx)%leftEmpty = .FALSE.
      aTree%pair(indx)%leftNode  = aNode
    else if ( aTree%pair(indx)%rightEmpty ) then ! add node
      aTree%pair(indx)%rightEmpty = .FALSE.
      aTree%pair(indx)%rightNode  = aNode
    else
      distanceToLeftNode  = aNode - aTree%pair(indx)%leftNode
      distanceToRightNode = aNode - aTree%pair(indx)%rightNode
      choose_nearest_side : if ( distanceToLeftNode < distanceToRightNode ) then
        allocate_left : if (aTree%pair(indx)%leftChild == 0) then
          aTree%numOfNodes            = aTree%numOfNodes + 1
          aTree%pair(indx)%leftChild = aTree%numOfNodes
        end if allocate_left
        if (distanceToLeftNode > aTree%pair(indx)%LeftMaxDistance)            &
            aTree%pair(indx)%LeftMaxDistance = distanceToLeftNode
        call addNodeTo( aTree, aNode, aTree%pair(indx)%leftChild ) ! recurse
      else
        allocate_right : if (aTree%pair(indx)%rightChild == 0) then
          aTree%numOfNodes             = aTree%numOfNodes + 1
          aTree%pair(indx)%rightChild = aTree%numOfNodes
        end if allocate_right
        if (distanceToRightNode > aTree%pair(indx)%RightMaxDistance)          &
            aTree%pair(indx)%RightMaxDistance = distanceToRightNode
        call addNodeTo( aTree, aNode, aTree%pair(indx)%rightChild ) ! recurse
      end if choose_nearest_side
    end if

  end subroutine addNodeTo

!============================== distanceBetween ==============================80
!
! Description goes here.
!
!=============================================================================80

  function distanceBetween( aNode, anotherNode )

    real(dp) :: distanceBetween

    type(Node), intent(in) :: aNode, anotherNode

    continue

    distanceBetween = sqrt(( aNode%xCoordinate - anotherNode%xCoordinate )**2  &
                         + ( aNode%yCoordinate - anotherNode%yCoordinate )**2  &
                         + ( aNode%zCoordinate - anotherNode%zCoordinate )**2)

  end function distanceBetween

!============================== nodeComparison ===============================80
!
! Description goes here.
!
!=============================================================================80

  function nodeComparison( aNode, anotherNode)

    logical :: nodeComparison

    type(Node), intent(in) :: aNode, anotherNode

    continue

    if ( sqrt(aNode-anotherNode) < 2.0_dp*spacing(0.0_dp) ) then
      nodeComparison = .true.
    else
      nodeComparison = .false.
    endif

  end function nodeComparison

!================================== buildTree ================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine buildTree ( aTree, maxNumOfNodes, x, y, z )

    type(Tree),                         intent(inout) :: aTree
    integer,                            intent(in)    :: maxNumOfNodes
    real(dp), dimension(maxNumOfNodes), intent(in)    :: x
    real(dp), dimension(maxNumOfNodes), intent(in)    :: y
    real(dp), dimension(maxNumOfNodes), intent(in)    :: z

    type(Node) :: aNode

    integer :: i

    continue

    call initializeTree ( aTree, maxNumOfNodes )

    do i = 1, maxNumOfNodes
      aNode%xCoordinate = x(i)
      aNode%yCoordinate = y(i)
      aNode%zCoordinate = z(i)
      aNode%id = i
      call addNodeTo( aTree, aNode )
    end do

  end subroutine buildTree

end module nearest_neighbor

!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite string_utils

integer, parameter :: dp = selected_real_kind(P=15)

!!
! basename

test basename_nominal_behavior
  assert_equal( 'file1', basename('/path/to/file1') ) 
  assert_equal( 'file2', basename('file2.ext','.ext') ) 
  assert_equal( 'file3', basename('/path/to/file3.ext','.ext') ) 
end test

!!
! char2int

test char2int_nominal_behavior
  assert_equal( -1000, char2int(  '-1000' ) )
  assert_false( error_condition )
  assert_equal(     1, char2int(      '1' ) )
  assert_false( error_condition )
  assert_equal(     2, char2int(     '+2' ) )
  assert_false( error_condition )
  assert_equal(     3, char2int(     ' 3' ) )
  assert_false( error_condition )
  assert_equal(     4, char2int(     '4 ' ) )
  assert_false( error_condition )
  assert_equal(     5, char2int(    ' 5 ' ) )
  assert_false( error_condition )
  assert_equal(     6, char2int(    '6.8' ) )
  assert_true(  error_condition )
  assert_equal(     7, char2int( '7stuff' ) )
  assert_true(  error_condition )
  assert_equal(     0, char2int( 'stuff8' ) )
  assert_true(  error_condition )
  assert_equal(     0, char2int(  '2400000000' ) ) ! bigger than std int
  assert_true(  error_condition )
  assert_equal(     0, char2int( '-2400000000' ) ) ! smaller than std int
  assert_true(  error_condition )
  assert_equal(     0, char2int( '' ) )
  assert_true(  error_condition )
  assert_equal(     0, char2int( 'no_digits' ) )
  assert_true(  error_condition )
end test

!!
! char2real

test char2real_nominal_behavior
  assert_equal(  1, char2real(          '1' ) )
  assert_false( error_condition )
  assert_equal(  2, char2real(         '2.' ) )
  assert_false( error_condition )
  assert_equal(  3, char2real(        '+3.' ) )
  assert_false( error_condition )
  assert_equal( -4, char2real(        '-4.' ) )
  assert_false( error_condition )
  assert_equal(  5, char2real(      '5.e0 ' ) )
  assert_false( error_condition )
  assert_equal(  6, char2real(       '6.E0' ) )
  assert_false( error_condition )
  assert_equal(  7, char2real(       '7.d0' ) )
  assert_false( error_condition )
  assert_equal(  8, char2real(       '8.D0' ) )
  assert_false( error_condition )
  assert_equal(  9, char2real(      '0.9E1' ) )
  assert_false( error_condition )
  assert_equal( 10, char2real(     '1.0E+1' ) )
  assert_false( error_condition )
  assert_equal( 11, char2real(  '  110.E-1' ) )
  assert_false( error_condition )
  assert_equal( 12, char2real(  '12.0stuff' ) )
  assert_true(  error_condition )
  assert_equal(  0, char2real(  'stuff13.4' ) )
  assert_true(  error_condition )
  assert_equal(  0, char2real(           '' ) )
  assert_true(  error_condition )
  assert_equal(  0, char2real(  'no_digits' ) )
  assert_true(  error_condition )
end test

test char2real_error_codes
end test

!!
! clean

test clean_behavior
  assert_equal( 'noop', clean(            'noop' ) )
  assert_equal(   'ab', clean( 'a'//char(1)//'b' ) )
end test

!!
! compress

test compress_behavior
  assert_equal(     'noop', compress(               'noop' ) )
  assert_equal(      'abc', compress(         ' a  b   c ' ) )
  assert_equal( '.tabbed.', compress( '.	tabbed	.' ) )
end test

!!
! dirname

test dirname_nominal_behavior
  assert_equal( '/path/to', dirname('/path/to/file1') ) 
  assert_equal(        '/', dirname('/file2.ext') ) 
  assert_equal(        '.', dirname('file3.ext') ) 
end test


!!
! squeeze

test squeeze_behavior
  assert_equal(      'no op', squeeze(                     'no op' ) )
  assert_equal(     ' a b c', squeeze(                ' a  b   c ' ) )
  assert_equal( '. tabbed .', squeeze( '.	tabbed		.' ) )
end test

!!
! empty

test empty_behavior
  assert_true(  empty(          '' ) )
  assert_true(  empty(         ' ' ) )
  assert_false( empty( 'not empty' ) )
end test

!!
! geti

test geti_behavior
  assert_equal( 1, geti( 'I= 1', 'I' ) )
  assert_equal( 2, geti( 'i=2', 'I' ) )
  assert_equal( 3, geti( 'KEY =3', 'key' ) )
  assert_equal( 4, geti( 'i = 4', 'i' ) )
  assert_equal( 5, geti( 'keep KEY=5', 'key' ) )
  assert_equal( 6, geti( 'time", I=6', 'i' ) )
end test

test geti_with_stuff_after_integer
  assert_equal( 7, geti( 'A=7stuff',     'a' ) )
  assert_equal( 8, geti( ' B = 8 stuff', 'B' ) )
end test

test geti_with_no_key
  assert_equal( 0, geti( '5', 'i' ) )
end test

test geti_with_no_equals_sign
  assert_equal( 0, geti( 'i 6', 'i' ) )
end test

!!
! getreal

test getreal_behavior
  assert_equal(     3.5_dp, getreal( '"TIME = 3.5"', 'TIME' ) )
  assert_false( error_condition )
  assert_equal(     4.6_dp, getreal( '"time 4.6e+0"', 'time' ) )
  assert_false( error_condition )
  assert_equal(     0.2_dp, getreal( ' T="TIME = 0.20E+00"', 'time' ) )
  assert_false( error_condition )
  assert_equal( 2.34e-6_dp, getreal( 'rho= 2.34e-6', 'rho' ) )
  assert_false( error_condition )
  assert_equal(     1.2_dp, getreal( 'p=1.2', 'p' ) )
  assert_false( error_condition )
  assert_equal(     0.0_dp, getreal( '', '' ) )
  assert_true( error_condition )
end test

!!
! int_to_s

test int_to_s_behaves
  assert_equal(      '-1', int_to_s(      -1 ) )
  assert_equal(       '0', int_to_s(       0 ) )
  assert_equal(      '42', int_to_s(      42 ) )
  assert_equal( '1218302', int_to_s( 1218302 ) )
end test

test int_to_s_with_placeholders
  character(10) :: expect ! assert_equal cannot deal with comma in string
  assert_equal(  '999', int_to_s(     999, .true. ) )
  expect = '1,000'
  assert_equal( expect, int_to_s(    1000, .true. ) )
  expect = '10,000'
  assert_equal( expect, int_to_s(   10000, .true. ) )
  expect = '1,000,000'
  assert_equal( expect, int_to_s( 1000000, .true. ) )
  expect = '-100,000'
  assert_equal( expect, int_to_s( -100000, .true. ) )
end test

!!
! int_width

test int_width_behavior
  assert_equal(  1, int_width(       1 ) )
  assert_equal(  1, int_width(       9 ) )
  assert_equal(  1, int_width(       0 ) )
  assert_equal(  2, int_width(      -1 ) )
  assert_equal(  2, int_width(      10 ) )
  assert_equal(  2, int_width(      99 ) )
  assert_equal(  5, int_width(   -2983 ) )
  assert_equal(  6, int_width(  999999 ) )
  assert_equal(  7, int_width( 1000000 ) )
  assert_equal(  7, int_width( 9999999 ) )
  assert_equal( 10, int_width(   2**30 ) )
end test

!!
! join

test join_nominal_behavior
  assert_equal( 'abc', join( (/'a','b','c'/) ) )
  assert_equal( 'abc', join( 'a','b','c' ) )
end test

test join_trims_strings_before_joining
  assert_equal( 'abc', join( (/'a ','b ','c '/) ) )
  assert_equal( 'abc', join( 'a ','b ','c  ' ) )
end test

test join_one_item
  assert_equal( 'a', join( (/'a'/) ) )
  assert_equal( 'a', join( 'a' ) )
end test

test join_with_optional_separator
  assert_equal( 'a b c',   join( (/'a','b','c'/),  ' ' ) )
  assert_equal( 'a b c',   join( 'a','b','c', sep=' ' ) )
  assert_equal( 'a.b.c',   join( (/'a','b','c'/),  '.' ) )
  assert_equal( 'a.b.c',   join( 'a','b','c', sep='.' ) )
  assert_equal( 'a/b/c',   join( (/'a','b','c'/),  '/' ) )
  assert_equal( 'a/b/c',   join( 'a','b','c', sep='/' ) )
! FIXME: funit fails if the following are tested via assert_equal:
  assert_true( 'a,b,c'   == trim( join( (/'a','b','c'/),  ',' ) ) )
  assert_true( 'a,b,c'   == trim( join( 'a','b','c', sep=',' ) ) )
  assert_true( 'a, b, c' == trim( join( (/'a','b','c'/), ', ' ) ) )
  assert_true( 'a, b, c' == trim( join( 'a','b','c', sep=', ' ) ) )
end test

!!
! list_to_array

test list_to_array_single_number
  integer, dimension(:), pointer :: array
  call list_to_array( '1', array )
  assert_equal( 1, array(1) )
end test

test list_to_array_with_ranges_and_commas
  integer, dimension(:), pointer :: array
  call list_to_array( '1,3-5, 6', array )
  assert_equal( 5, size(array) )
  assert_equal( 1, array(1) )
  assert_equal( 3, array(2) )
  assert_equal( 4, array(3) )
  assert_equal( 5, array(4) )
  assert_equal( 6, array(5) )
end test

test list_with_spaces_to_array
  integer, dimension(:), pointer :: array
  call list_to_array( '7 8 9', array )
  assert_equal( 3, size(array) )
  assert_equal( 7, array(1) )
  assert_equal( 8, array(2) )
  assert_equal( 9, array(3) )
end test

!!
! ljust

test ljust_behavior
  assert_equal( '-1  ', ljust(   -1, 4 ) )
  assert_equal( '0   ', ljust(    0, 4 ) )
  assert_equal( '9   ', ljust(    9, 4 ) )
  assert_equal( '10  ', ljust(   10, 4 ) )
  assert_equal( '9999', ljust( 9999, 4 ) )
end test

test ljust_optional_padding
  assert_equal(  '100', ljust( 1, 3,  '0' ) )
  assert_equal( '2efe', ljust( 2, 4, 'ef' ) )
end test

test ljust_width_exceeded_gives_stars
  assert_equal( '****', ljust( 10000, 4 ) )
end test

!!
! downcase

test downcase_behavior
  integer :: i
  do i = 97,122 ! all uppercase ASCII letters
    assert_equal( achar(i), downcase(achar(i-32)) )
  end do
  assert_equal(   '1', downcase(  '1') )
  assert_equal( 'abc', downcase('ABC') )
end test

!!
! range_to_array

test range_to_array_behavior
  integer, dimension(:), pointer :: array
  call range_to_array('0', array )
  assert_equal( 0, array(1) )
  call range_to_array('3-5', array )
  assert_equal( 3, size(array) )
  assert_equal( 3, array(1) )
  assert_equal( 4, array(2) )
  assert_equal( 5, array(3) )
end test

!!
! rjust

test rjust_behavior
  assert_equal(    '1', rjust(  1, 1 ) )
  assert_equal(   '99', rjust( 99, 2 ) )
  assert_equal( '   0', rjust(  0, 4 ) )
  assert_equal( '  -1', rjust( -1, 4 ) )
end test

test rjust_optional_padding
  assert_equal( '0001', rjust(  1, 4,  '0' ) )
  assert_equal( '00-1', rjust( -1, 4,  '0' ) )
  assert_equal( 'xyx3', rjust(  3, 4, 'xy' ) )
end test

test rjust_width_exceeded_gives_stars
  assert_equal(    '*', rjust( 123, 1 ) )
  assert_equal(   '**', rjust( 123, 2 ) )
end test

!!
! split

test split_behavior_with_commas
  character(5), dimension(:), pointer :: strings
  call split( '1,2,3', strings )
  assert_equal( 3, size(strings) )
  assert_equal( '1', strings(1) )
  assert_equal( '2', strings(2) )
  assert_equal( '3', strings(3) )
end test

test split_behavior_with_spaces
  character(5), dimension(:), pointer :: strings
  call split( '1   5000    text  more', strings )
  assert_equal(      4, size(strings) )
  assert_equal(    '1', strings(1) )
  assert_equal(      1, len_trim(strings(1)) )
  assert_equal( '5000', strings(2) )
  assert_equal(      4, len_trim(strings(2)) )
  assert_equal( 'text', strings(3) )
  assert_equal(      4, len_trim(strings(3)) )
  assert_equal( 'more', strings(4) )
  assert_equal(      4, len_trim(strings(4)) )
! try another one to be check pointer allocation
  call split( '      1   4000      Blade', strings )
  assert_equal(       3, size(strings) )
  assert_equal(     '1', strings(1) )
  assert_equal(       1, len_trim(strings(1)) )
  assert_equal(  '4000', strings(2) )
  assert_equal(       4, len_trim(strings(2)) )
  assert_equal( 'Blade', strings(3) )
  assert_equal(       5, len_trim(strings(3)) )
end test

test split_behavior_with_commas_and_spaces
  character(5), dimension(:), pointer :: strings
  call split( 'a, b,  c', strings )
  assert_equal( 3, size(strings) )
  assert_equal( 'a', strings(1) )
  assert_equal( 'b', strings(2) )
  assert_equal( 'c', strings(3) )
end test

!!
! sprintf

test sprintf_trims_format_string
  assert_equal( 'trim: 0', sprintf( 'trim: %i0 ', 0 ) )
end test

test sprintf_integer_formats
  assert_equal(  'integer: 1', sprintf(   'integer: %i0', 1 ) )
  assert_equal( 'integer: 01', sprintf( 'integer: %i2.2', 1 ) )
end test

test sprintf_real_formats
  assert_equal(      'real: 1.23', sprintf(    'real: %f4.2',     1.23 ) )
  assert_equal(      'real:  1.2', sprintf(    'real: %g8.2',     1.21 ) )
  assert_equal(  'real: 1.22E+02', sprintf(   'real: %es8.2',  1.22e+2 ) )
  assert_equal( 'real: 123.0E+00', sprintf(   'real: %en9.1',  1.23e+2 ) )
end test

test sprintf_character_formats
  assert_equal( 'string: insert', sprintf( 'string: %a', 'insert' ) )
end test

!!
! strip

test strip_behavior
  assert_equal( '',            strip( '' ) )
  assert_equal( '',            strip( ' ' ) )
  assert_equal( 'noop',        strip( 'noop' ) )
  assert_equal( 'leading',     strip( ' leading' ) )
  assert_equal( 'trailing',    strip( 'trailing ' ) )
  assert_equal( 'surrounding', strip( ' surrounding ' ) )
end test

!!
! upcase

test upcase_behavior
  integer :: i
  do i = 65,90 ! all lowercase ASCII letters
    assert_equal( achar(i), upcase(achar(i+32)) )
  end do
  assert_equal(   '1', upcase(  '1') )
  assert_equal( 'ABC', upcase('abc') )
end test

end test_suite

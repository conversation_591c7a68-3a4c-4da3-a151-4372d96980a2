/* File: engine_app.c
 *
 * Abstract:
 *      This program run the MATLAB/SIMULINK model genreated C-code by
 *      by provding the time stepping and inputs to the code for the inlet
 *      total pressure and temperature, and the nozzles exit mass flow
 *
 * Usage:
 *
 *
 * <PERSON>
 * 10/01/14
 */

#include <stdio.h>
#include <stddef.h>
#include <math.h>
#include <stdlib.h>
#include "rtwtypes.h"
#include <stdexcept>

/* UNIX */
#include <dlfcn.h>
#define GETSYMBOLADDR dlsym
#define LOADLIB dlopen
#define CLOSELIB dlclose


#include "engine_app.h"
#include "engine_data.h"
#include "engine_simulator.h"
#include "engine_data.h"


void initialize_engine(void** cStruct_ptr, const char *cycle_name){

    EngineSimulator *engineSimulator = new EngineSimulator(cycle_name);

    *cStruct_ptr = (void*)engineSimulator;
    engineSimulator = NULL;
}

EngineOutput simulate_engine(
      void** cStruct_ptr, 
      double &pt_in, 
      double &TT_in,
      double &Time, 
      double &mdot_bypass,
      double &mdot_core,
      double &mdot_vce
      ){
    //--- Restore the engine simulator from the depths of fortran.
    EngineSimulator *engineSimulator = (EngineSimulator*)*cStruct_ptr;

    EngineInput input;
    input.pressure = pt_in;
    input.temperature = TT_in;
    input.time = Time;
    input.mdot_bypass = mdot_bypass;
    input.mdot_core = mdot_core;
    input.mdot_vce = mdot_vce;


    EngineOutput output = engineSimulator->step(input);
    //bc_data_type bc_data;
    //bc_data.pt_vce    = output.press_vce;
    //bc_data.pt_core   = output.press_core;
    //bc_data.pt_bypass = output.press_bypass;

    //bc_data.tt_vce    = output.temp_vce;
    //bc_data.tt_core   = output.temp_core;
    //bc_data.tt_bypass = output.temp_bypass;

    return output;
}

void finalize_engine(void** cStruct_ptr){
    //--- Restore the engine simulator from the depths of fortran.
    EngineSimulator *engineSimulator = (EngineSimulator*)*cStruct_ptr;
    delete engineSimulator;
    *cStruct_ptr = NULL;
}


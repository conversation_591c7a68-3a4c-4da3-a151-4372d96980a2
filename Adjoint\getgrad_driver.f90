module getgrad_driver

  use comprow_types, only : crow_type
  use solution_getg, only : getg_type

  implicit none

  private

  public :: steady_getgrad
  public :: echo_sensitivity_at_perturbation

  logical :: nn_crow_setup = .false.
  logical :: getg_setup    = .false.

  type(getg_type) :: getg
  type(crow_type), save :: nn_crow

contains

!================================== STEADY_GETGRAD ===========================80
!
! Runs getgrad pieces for steady problems
!
!=============================================================================80
  subroutine steady_getgrad(grid,soln,sadj,design,nml_path,dldx)

    use design_types,     only : design_type
    use kinddefs,         only : dp
    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use solution_adj,     only : sadj_type
    use dalpha,           only : sensalpha
    use dmach,            only : sensmach
    use allocations,      only : my_alloc_ptr
    use dshape,           only : sensshape, write_column_sum
    use comprow,          only : set_up_comprow
    use adjoint_switches, only : store_full_stencil, low_mem_meshsens,         &
                                 show_surface_sensitivity, get_dldx
    use solution_getg,    only : set_up_getg
    use fun3d_constants,  only : my_0
    use lmpi,             only : lmpi_master
    use grid_motion,      only : reposition_grid
    use info_depr,        only : ngrid
    use designs,          only : compute_shape_gradient, plot_surf_sens,       &
                                 compute_shape_gradient_lm
    use dshape_residual,  only : drdnoninrates

    real(dp), dimension(:,:,:), intent(out) :: dldx

    type(sadj_type),                     intent(inout) :: sadj
    type(grid_type),   dimension(ngrid), intent(inout) :: grid
    type(soln_type),                     intent(inout) :: soln
    type(design_type),                   intent(inout) :: design

    character(*), intent(in) :: nml_path

    integer :: dim1, ibody

    integer, dimension(:), pointer, save :: iflagslen_save

    real(dp), dimension(:),     pointer :: dIdmach
    real(dp), dimension(:,:),   pointer :: dIdalp, dIdrates
    real(dp), dimension(:,:,:), pointer :: colsum

    logical :: global_store_full_stencil

    logical, save :: iflagslen_save_setup = .false.

    logical, dimension(design%nbodies) :: fake_body

  continue

    fake_body = .false.

    if ( lmpi_master ) open(67,file='debug_getgrad')

! Mach derivatives

    if ( design%mach_active ) then
      call my_alloc_ptr(dIdmach, design%nfunctions)
      call sensmach(grid(1), soln, sadj, design, dIdmach, 0, 0)
      design%mach_derivative(:) = dIdmach(:)
      deallocate(dIdmach)
    endif

! AOA and yaw derivatives

    if ( design%alpha_active .or. design%yaw_active ) then
      call my_alloc_ptr(dIdalp, 2, design%nfunctions)
      call sensalpha(grid(1), soln, sadj, design, dIdalp, 0, 0)
      design%alpha_derivative(:) = dIdalp(1,:)
      design%yaw_derivative(:)   = dIdalp(2,:)
      deallocate(dIdalp)
    endif

! Noninertial rate derivatives

    if ( design%xrate_active.or.design%yrate_active.or.design%zrate_active )then
      call my_alloc_ptr(dIdrates, 3, design%nfunctions)
      call drdnoninrates(grid(1),soln,sadj,design,dIdrates)
      design%xrate_derivative(:) = dIdrates(1,:)
      design%yrate_derivative(:) = dIdrates(2,:)
      design%zrate_derivative(:) = dIdrates(3,:)
      deallocate(dIdrates)
    endif

! Shape linearizations

    shape_derivs : if ( design%shape_active .or. get_dldx .or.                 &
                        show_surface_sensitivity ) then

! Set up some data structures for grid linearizations

      if ( .not. nn_crow_setup ) then
        global_store_full_stencil = store_full_stencil
        store_full_stencil = .false.
        call set_up_comprow(soln%viscous_method, grid(1), nn_crow)
        nn_crow_setup = .true.
        store_full_stencil = global_store_full_stencil
      endif

      if ( .not. getg_setup ) then
        call set_up_getg(grid(1),nn_crow,getg,design)
        getg_setup = .true.
      endif

      getg%dvdx           = my_0
      getg%dfdx           = my_0
      getg%drdxl          = my_0
      getg%sourceterm_sum = my_0
      getg%overset_terms  = my_0

! In the event that transition is being simulated, save a baseline copy of
! the iflagslen array so we can restore it later

      if ( .not. iflagslen_save_setup ) then
        dim1 = size(grid(1)%iflagslen,1)
        call my_alloc_ptr(iflagslen_save, dim1)
        iflagslen_save = grid(1)%iflagslen
        iflagslen_save_setup = .true.
      endif

! Linearize wrt grid and form mesh adjoint RHS

      grid(1)%iflagslen = abs(grid(1)%iflagslen)
      call sensshape(grid(1),nn_crow,soln,sadj,design,getg,1)
      grid(1)%iflagslen = iflagslen_save
      call my_alloc_ptr(colsum,3,grid(1)%nnodes0,design%nfunctions)
      call write_column_sum(grid(1)%nnodes0,grid(1)%nnodes01,design%nfunctions,&
                            nn_crow%nnz01,getg%ntp,nn_crow%ia,nn_crow%ja,      &
                            getg%drdxl,getg%dfdx,getg%dvdx,getg%sourceterm_sum,&
                            getg%overset_terms,colsum)

      if ( get_dldx ) dldx = colsum

! Solve the mesh adjoint problem and evaluate the shape sensitivities

      if ( design%shape_active .or. show_surface_sensitivity ) then

        call reposition_grid(grid,'../Flow/',nml_path,.true.,colsum)

        if (show_surface_sensitivity) call plot_surf_sens(grid(1),design,colsum)

        if ( design%shape_active ) then
          body_loop : do ibody = 1, design%nbodies
            if ( low_mem_meshsens ) then
              call compute_shape_gradient_lm(ibody,design,grid(1)%nnodes0,     &
                                             grid(1)%nnodes01,grid(1)%l2g,     &
                                             colsum,.false.,fake_body)
            else
              call compute_shape_gradient(ibody,design,grid(1)%nnodes0,        &
                                          grid(1)%nnodes01,grid(1)%l2g,colsum, &
                                          .false.,fake_body)
            endif
          end do body_loop
        endif

      endif

      deallocate(colsum)

    endif shape_derivs

    if ( lmpi_master ) close(67)

  end subroutine steady_getgrad

!=============================================================================80
!
! echos data that matches complex perturbation to verify adjoint/getgrad
!
!=============================================================================80
  subroutine echo_sensitivity_at_perturbation(grid,soln,sadj,design,nml_path)

    use kinddefs,          only : dp

    use info_depr,         only : ngrid

    use grid_types,        only : grid_type
    use solution_types,    only : soln_type
    use solution_adj,      only : sadj_type
    use design_types,      only : design_type

    use system_extensions, only : se_open
    use file_utils,        only : available_unit

    use lmpi,              only : lmpi_master, lmpi_bcast, lmpi_reduce

    use info_depr,         only : complex_epsilon,                         &
                                  complex_to_perturb, complex_grid_point

    use adjoint_switches,  only : get_dldx

    type(grid_type), dimension(ngrid), intent(inout) :: grid
    type(soln_type),                   intent(inout) :: soln
    type(sadj_type),                   intent(inout) :: sadj
    type(design_type),                 intent(inout) :: design
    character(*),                      intent(in   ) :: nml_path

    real(dp) :: local_rlam, rlam_at_perturbation
    real(dp) :: local_x, x_at_perturbation
    real(dp) :: local_y, y_at_perturbation
    real(dp) :: local_z, z_at_perturbation
    real(dp), dimension(:,:,:), allocatable :: dldx
    logical :: save_state_of_get_dldx
    integer :: node

    integer :: f
    integer :: file_status

    real(dp), parameter    :: zero = 0.0_dp

    continue

    master_read_file : if ( lmpi_master ) then

      f = available_unit()

      call se_open(f,file='../Flow/perturb.input',&
        status='old',iostat=file_status)

      have_complex_perturb : if ( 0 == file_status) then
        read(f,*,iostat=file_status)
        read_first_line : if ( 0 == file_status )  then
          read(f,*,iostat=file_status) complex_to_perturb,complex_epsilon,     &
            complex_grid_point
        end if read_first_line
      end if have_complex_perturb
    end if master_read_file

    call lmpi_bcast( file_status )

    process_file_contents : if ( 0 == file_status ) then
      call lmpi_bcast(complex_to_perturb)
      call lmpi_bcast(complex_epsilon)
      call lmpi_bcast(complex_grid_point)

      local_rlam = zero
      local_x = zero
      local_y = zero
      local_z = zero
      lambda_deriv : if ( 100 < complex_to_perturb .and. &
                          200 > complex_to_perturb .and. &
                          (complex_grid_point >= 1) ) then
        find_lam_grid_point : do node = 1, grid(1)%nnodes0
          found_lam_global : if ( complex_grid_point == grid(1)%l2g(node) ) then
            local_rlam = sadj%rlam(complex_to_perturb-100,node,1)
            local_x = grid(1)%x(node)
            local_y = grid(1)%y(node)
            local_z = grid(1)%z(node)
          end if found_lam_global
        end do find_lam_grid_point
      end if lambda_deriv

      grid_xyz_deriv : if ( 7 <= complex_to_perturb .and. &
                            9 >= complex_to_perturb .and. &
                            (complex_grid_point >= 1) ) then
        allocate(dldx(3,grid(1)%nnodes0,design%nfunctions))
        save_state_of_get_dldx = get_dldx
        get_dldx = .true. ! hack to get grid sensitivities
        call steady_getgrad(grid,soln,sadj,design,nml_path,dldx)
        get_dldx = save_state_of_get_dldx
        find_xyz_grid_point : do node = 1, grid(1)%nnodes0
          found_xyz_global : if ( complex_grid_point == grid(1)%l2g(node) ) then
            local_rlam = dldx(complex_to_perturb-6,node,1)
            local_x = grid(1)%x(node)
            local_y = grid(1)%y(node)
            local_z = grid(1)%z(node)
          end if found_xyz_global
        end do find_xyz_grid_point
        deallocate(dldx)
      end if grid_xyz_deriv
      call lmpi_reduce(local_rlam, rlam_at_perturbation)
      call lmpi_reduce(local_x, x_at_perturbation)
      call lmpi_reduce(local_y, y_at_perturbation)
      call lmpi_reduce(local_z, z_at_perturbation)
      if (lmpi_master) then
        write(*,'(a,e23.15)') ' dfunc = ',rlam_at_perturbation
        write(*,'(e23.15," % adjoint at complex perturbed gridpoint")')        &
          rlam_at_perturbation
        write(*,'("xyz = ",3e23.15)')                                          &
          x_at_perturbation, y_at_perturbation, z_at_perturbation
      end if
    end if process_file_contents

  end subroutine echo_sensitivity_at_perturbation

end module getgrad_driver

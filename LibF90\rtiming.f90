module rtiming

  use kinddefs,        only : dp
  use lmpi,            only : lmpi_id, lmpi_die

  implicit none

  private

  public :: rtime, rtime_set, rtime_mark

  ! wall clock time. system_clock is a standard fortran90 routine.
  integer     :: wall_cr     = 0          ! wall count rate
  real(dp) :: wall_cr_inv = 0.0_dp  ! inverse of wall count rate
  real(dp) :: last_wdiff  = 0.0_dp  ! last wall clock difference

  integer :: ng, nq, ne, no, iunit = 175

  real(dp) :: ngi = 1.0_dp, nqi = 1.0_dp, nei = 1.0_dp, noi = 1.0_dp

  logical :: timing_file_opened = .false.

contains

!=============================== RTIME =======================================80
!
! Gets the current wall-clock time and finds the delta from the last time
! this routine was called, on a per unit basis.
!
!=============================================================================80

  subroutine rtime(location,per)

    use string_utils, only : sprintf

    character(len=*), intent(in) :: location
    integer, intent(in), optional :: per

    integer :: current_wtime

    real(dp) :: last_wdiff_per, last_wdiff_nq, last_wdiff_ne

    real(dp), save :: last_rtime

  continue

    call system_clock(current_wtime)

    if (location == 'reset') then
      call system_clock(count_rate=wall_cr)
      wall_cr_inv = 1.0_dp/wall_cr
    end if

    last_wdiff = (current_wtime-last_rtime)*wall_cr_inv
    last_rtime = current_wtime

    last_wdiff_nq = last_wdiff*nqi
    last_wdiff_ne = last_wdiff*nei

    if (location /= 'reset') then

      if( present( per ) ) then
        last_wdiff_per = last_wdiff/real ( per , dp )
        write(iunit,'(1x,i5,1x,f20.8,2(1x,e16.4),i10,e16.4,2x,a)') &
        lmpi_id, last_wdiff, last_wdiff_nq, last_wdiff_ne,         &
        per, last_wdiff_per,                                       &
        trim(adjustl(location))
      else
        write(iunit,'(1x,i5,1x,f20.8,2(1x,e16.4),2x,a)')           &
        lmpi_id, last_wdiff, last_wdiff_nq, last_wdiff_ne,         &
        trim(adjustl(location))
      endif

    else

      if( .not.timing_file_opened ) then
        open(unit=iunit,file=sprintf('rtime.p%i0',lmpi_id+1))
        timing_file_opened = .true.
      else
        write(*,*) ' Stopping...inconsistent usage in rtime.'
        call lmpi_die
      endif

    endif

    if (location /= 'Execution complete') return

    close(iunit)

  end subroutine rtime

!=============================== RTIME_SET ===================================80
!
! Set unknowns and edges.
!
!=============================================================================80

  subroutine rtime_set( ng_in, nq_in, ne_in, no_in )

    integer, intent(in) :: ng_in, nq_in, ne_in, no_in

  continue

    ng = ng_in
    nq = nq_in
    ne = ne_in
    no = no_in

    ngi = 1.0_dp/real( ng , dp )
    nqi = 1.0_dp/real( nq , dp )
    nei = 1.0_dp/real( ne , dp )
    noi = 1.0_dp/real( no , dp )

    if( .not.timing_file_opened ) then
      write(*,*) ' Stopping...inconsistent usage in rtime_step.'
      write(*,*) " ...rtime with argument 'reset' has not been called."
      call lmpi_die
    endif

    write(iunit,'("lmpi_id,ng= ",1x,i10,1x,i10)') lmpi_id,ng
    write(iunit,'("lmpi_id,nq= ",1x,i10,1x,i10)') lmpi_id,nq
    write(iunit,'("lmpi_id,ne= ",1x,i10,1x,i10)') lmpi_id,ne
    write(iunit,'("lmpi_id,no= ",1x,i10,1x,i10)') lmpi_id,no
    write(iunit,'("lmpi_id,ne/nq= ",1x,i10,1x,f10.1)') lmpi_id,nqi/nei
    write(iunit,'("lmpi_id,nq/ng= ",1x,i10,1x,f10.1)') lmpi_id,ngi/nqi
    write(iunit,'("lmpi_id,no/nq= ",1x,i10,1x,f10.1)') lmpi_id,nqi/noi

  end subroutine rtime_set

!=============================== RTIME_MARK ==================================80
!
! Set unknowns and edges.
!
!=============================================================================80

  subroutine rtime_mark(location)

    character(len=*), intent(in), optional :: location

    real(dp), save :: mark_time = 0.0_dp

    integer :: current_wtime

  continue

    call system_clock(current_wtime)

    last_wdiff = (current_wtime-mark_time)*wall_cr_inv
    mark_time = current_wtime

    if( .not.timing_file_opened ) then
      write(*,*) ' Stopping...inconsistent usage in rtime_mark.'
      write(*,*) " ...rtime with argument 'reset' has not been called."
      call lmpi_die
    endif

    if ( .not.present(location) ) then
      write(iunit,'(1x,i5,1x,f20.8,3i10,2(1x,f10.1),2x,a)') &
      lmpi_id,last_wdiff,ng,nq,ne,ngi/nqi,nqi/nei,'...MarkTime'
    else
      write(iunit,'(1x,i5,21x,3i10,2(1x,f10.1),2x,a)') &
      lmpi_id,ng,nq,ne,ngi/nqi,nqi/nei,'...MarkTimeInitialization'
    endif

  end subroutine rtime_mark

end module rtiming

module turb_diffusion5

  use lmpi,            only : lmpi_conditional_stop
  use kinddefs,        only : dp
  use turb_parameters, only : t_diff1, t_diff2, t_diff3, t_diff4
  use fun3d_constants, only : my_half, my_4th, my_6th, my_3rd

  implicit none

  private

  public :: turb_resid_diff_element5
  public :: turb_jacob_diff_element5

  ! Optimized version of diff_element5
  ! ...inlined code

contains

!======================== TURB_RESID_DIFF_ELEMENT5 ===========================80
!
! Element-based mixed element diffusion for turbulence.
! Assumes viscosity at flux-interface by edge-averaging.
!
!=============================================================================80
  subroutine turb_resid_diff_element5 ( n_sta, eqn_set, nnodes0, turb,         &
                                 qnode, res, ncell, c2n, x, y, z,              &
                                 local_f2n, local_e2n, local_f2e, e2n_2d,      &
                                 face_per_cell, node_per_cell,                 &
                                 edge_per_cell, type_cell, n_turb, face_2d,    &
                                 big_angle,                                    &
                                 diff_const, diff_const2 )

    use kinddefs,        only : dp
    use info_depr,       only : tref, xmach, re, use_edge_gradients, twod      &
                              , skeleton, grad_x_y_z_contents
    use fluid,           only : gamma, sutherland_constant
    use element_defs,    only : max_node_per_cell, max_edge_per_cell
    use debug_defs,      only : gradient_construction_rhs
    use solution_types,  only : compressible, incompressible

    integer, intent(in) :: n_sta, eqn_set
    integer, intent(in) :: n_turb
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: face_2d

    integer, dimension(node_per_cell,ncell),  intent(in) :: c2n
    integer, dimension(face_per_cell,4),      intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),      intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),      intent(in) :: local_f2e
    logical, dimension(:),                    intent(in) :: big_angle
    integer, dimension(4,2),                  intent(in) :: e2n_2d

    real(dp), dimension(:),                intent(in)    :: x, y, z
    real(dp), dimension(:,:),              intent(in)    :: turb
    real(dp), dimension(:,:),              intent(in)    :: qnode
    real(dp), dimension(:,:),              intent(inout) :: res

    character(len=3),                         intent(in) :: type_cell
    real(dp),           dimension(n_turb),    intent(in) :: diff_const
    real(dp),           dimension(n_turb),    intent(in) :: diff_const2

    integer :: n
    integer :: ie, i, ie_local, i_local
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, node
    integer :: rn1, rn2

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: xmre, xmre_s
    real(dp) :: cb20, cb2s
    real(dp) :: phi_ngradt_n1, phi_ngradt_n2
    real(dp) :: cstar
    real(dp) :: my_xmach
    real(dp) :: ex, ey, ez, disi, dai, xnf, ynf, znf
    real(dp) :: egradt, gradt_xi, phi, ngradt

    real(dp), dimension(3) :: augment_weight, da

    real(dp), dimension(max_node_per_cell,3) :: r_node
    real(dp), dimension(max_node_per_cell)   :: turb_node, aturb_node
    real(dp), dimension(max_node_per_cell)   :: rhoi_node, rnu_node
    integer,  dimension(max_node_per_cell)   :: n_node

   !real(dp), dimension(n_turb,3)                 :: grad_cell
    real(dp)  :: trbrex, trbrey, trbrez
    real(dp)  :: trbrexavg, trbreyavg
    real(dp)  :: trbrezavg

    logical :: edge_gradients

    integer :: iface
    integer :: nn1, nn2, nn3, nn4, n3_loc, n4_loc, n5_loc, n6_loc

    real(dp) :: xavg, yavg, zavg, cell_vol_inv, cell_vol
    real(dp) :: xavg0, yavg0, zavg0, tavg
    real(dp) :: x1, x2, x3, x4, nx, nx0
    real(dp) :: y1, y2, y3, y4, ny, ny0
    real(dp) :: z1, z2, z3, z4, nz, nz0
    real(dp) :: termx, termy, termz
    real(dp) :: termx0, termy0, termz0
    real(dp) :: term0, fact
    real(dp) :: xl, xm, xr, xc
    real(dp) :: yl, ym, yr, yc
    real(dp) :: zl, zm, zr, zc

    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp
    real(dp), parameter :: my_24th = 1.0_dp/24.0_dp
    real(dp), parameter :: my_8th  = 1.0_dp/8.0_dp

  continue

    !For tets in 3D or prisms in 2D, edge gradients add no new info.

                                       edge_gradients = use_edge_gradients
    if (           type_cell == 'tet') edge_gradients = .false.
    if (twod .and. type_cell == 'prz') edge_gradients = .false.

    if ( skeleton > 0 ) then
      write(*,"(1x,a,a,a)") ' Cell-based residuals of &
      &turbulent diffusion...element=',type_cell
      write(*,*) ' gradx,... via ', trim(grad_x_y_z_contents)
    endif

    cstar = sutherland_constant / tref
    my_xmach = 0._dp
    nodes_local = 0

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = 1._dp
    case default
      call lmpi_conditional_stop(1,'eqn_set:turb_resid_diff_element5')
    end select

    !...to characterize four types of turbulent diffusion.
    cb20 = t_diff3*diff_const2(1) ! t_diff3*cb2
    cb2s = t_diff4*diff_const2(1) ! t_diff4*cb2

    xmre   = my_xmach / re
    xmre_s = xmre /  diff_const(1) ! xmre / sig

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    edge_map = 0
    node_map = 0

    if ( twod ) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    else

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

    fact = 1._dp / real(node_per_cell, dp)

    diffusion_term_cell : do n = 1, ncell

      if ( big_angle(n) ) cycle !skip_viscous_terms

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

      do i = 1, node_per_cell

        node = c2n(i,n)       ! global node number

        r_node(i,1) = x(node)
        r_node(i,2) = y(node)
        r_node(i,3) = z(node)

        xc  =  xc + r_node(i,1)
        yc  =  yc + r_node(i,2)
        zc  =  zc + r_node(i,3)

      enddo

      xc  =  xc*fact
      yc  =  yc*fact
      zc  =  zc*fact

!     initialization

      trbrex          = 0._dp
      trbrey          = 0._dp
      trbrez          = 0._dp
      rhoi_node(:)    = 1._dp
      rnu_node(:)     = 1._dp

!     compute cell averages and set up some local solution arrays

      node_loop0 : do i_local = 1, nodes_local

!       local node number
        i = node_map(i_local)
!       global node number
        node = c2n(i,n)

        n_node(i) = node

        turb_node(i) = turb(1,node)

        if ( eqn_set == compressible ) then
          rhoi_node(i)= 1._dp/qnode(1,node)
          rnu_node(i) = viscosity_law( cstar, &
                        gamma*qnode(5,node)*rhoi_node(i) ) * rhoi_node(i)
        end if

        aturb_node(i) = sa0_turb_abs( turb_node(i), rnu_node(i) )

      end do node_loop0

!     get the gradients in the primal cell via Green-Gauss

   !   initialization

    trbrexavg = 0._dp
    trbreyavg = 0._dp
    trbrezavg = 0._dp

    cell_vol = 0._dp

    cell_dimension : if (twod) then

      twod_edges : do ie_local = 1,edges_local

!       local node numbers of edge endpoints

        nn1 = e2n_2d(ie_local,1)
        nn2 = e2n_2d(ie_local,2)

        x1 = r_node(nn1,1)
        x2 = r_node(nn2,1)

        z1 = r_node(nn1,3)
        z2 = r_node(nn2,3)

!       edge midpoint (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

        xavg = x1 + x2
        zavg = z1 + z2

!       edge normals

        nx = -(z2 - z1)
        nz =  (x2 - x1)

!       cell volume (area) contributions

        cell_vol = cell_vol + (xavg*nx + zavg*nz)*my_4th

        termx = nx*my_half
        termz = nz*my_half

!       gradient contributions

!        do eqn = 1,qdim
!          qavg = q_node(eqn,nn1) + q_node(eqn,nn2)
!          element_grad(eqn,1) = element_grad(eqn,1) + termx*qavg
!          element_grad(eqn,3) = element_grad(eqn,3) + termz*qavg
!        end do

          tavg = turb_node(nn1) + turb_node(nn2)

          trbrexavg = trbrexavg + tavg*termx
          trbrezavg = trbrezavg + tavg*termz

      end do twod_edges

    else cell_dimension

      threed_faces : do iface = 1,face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!         triangular faces of the cell

          x1 = r_node(nn1,1)
          x2 = r_node(nn2,1)
          x3 = r_node(nn3,1)

          y1 = r_node(nn1,2)
          y2 = r_node(nn2,2)
          y3 = r_node(nn3,2)

          z1 = r_node(nn1,3)
          z2 = r_node(nn2,3)
          z3 = r_node(nn3,3)

!         face normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx = (y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1)
          ny = (z2 - z1)*(x3 - x1) - (x2 - x1)*(z3 - z1)
          nz = (x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1)

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg = x1 + x2 + x3
          yavg = y1 + y2 + y3
          zavg = z1 + z2 + z3

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

          tavg = turb_node(nn1) + turb_node(nn2) + turb_node(nn3)

!         gradient contributions

          trbrexavg = trbrexavg + tavg*termx
          trbreyavg = trbreyavg + tavg*termy
          trbrezavg = trbrezavg + tavg*termz

        else

!         quadrilateral faces of the cell

          x1 = r_node(nn1,1)
          x2 = r_node(nn2,1)
          x3 = r_node(nn3,1)
          x4 = r_node(nn4,1)

          y1 = r_node(nn1,2)
          y2 = r_node(nn2,2)
          y3 = r_node(nn3,2)
          y4 = r_node(nn4,2)

          z1 = r_node(nn1,3)
          z2 = r_node(nn2,3)
          z3 = r_node(nn3,3)
          z4 = r_node(nn4,3)

!         Use simple quadratures to evaluate gradients.  The resulting
!         volume is identical to the volume computed by averaging the
!         volumes associated with the two diagonal split choices.  The
!         quadratures for gradient are not identical, but simpler and
!         consistent across elements which share faces.

!         Define face as average plane 1-2-3-4.  This average plane passes
!         through the center of the quad and through each edge midpoint.
!         The nodes of the quad are displaced from this plane by an identical
!         amount (+-+-) confirming the plane is a least squares fit.

!         face centroid (factor of 1/4 deferred till the
!         contribution to cell_vol is calculated)

          xavg0 = x1 + x2 + x3 + x4
          yavg0 = y1 + y2 + y3 + y4
          zavg0 = z1 + z2 + z3 + z4

!         normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx0 = (y2 - y4)*(z3 - z1) - (z2 - z4)*(y3 - y1)
          ny0 = (z2 - z4)*(x3 - x1) - (x2 - x4)*(z3 - z1)
          nz0 = (x2 - x4)*(y3 - y1) - (y2 - y4)*(x3 - x1)

          term0 = xavg0*nx0 + yavg0*ny0 + zavg0*nz0

!         cell volume contributions

          cell_vol = cell_vol + term0*my_24th

!         gradient contributions

          termx0 = nx0*my_8th
          termy0 = ny0*my_8th
          termz0 = nz0*my_8th

          tavg = turb_node(nn1) + turb_node(nn2) &
               + turb_node(nn3) + turb_node(nn4)

          trbrexavg = trbrexavg + tavg*termx0
          trbreyavg = trbreyavg + tavg*termy0
          trbrezavg = trbrezavg + tavg*termz0

          nx = nx0
          ny = ny0
          nz = nz0

        end if

      end do threed_faces

    end if cell_dimension

    cell_vol_inv = 1._dp/cell_vol


!   need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

    trbrexavg = trbrexavg * cell_vol_inv
    trbreyavg = trbreyavg * cell_vol_inv
    trbrezavg = trbrezavg * cell_vol_inv

!-----------------------------------------------------------------------------80

!     next loop over the edges in the cell and get each ones
!     contribution to the residual

      edge_loop : do ie_local = 1,edges_local

!       local edge number

        ie = edge_map(ie_local)

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)

!       global node numbers of edge endpoints

       !n1 = c2n(n1_loc,n)
       !n2 = c2n(n2_loc,n)

        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

!       get this edges' contributiuon to the dual normal and area

!       edge midpoint

        xm = (r_node(n1_loc,1) + r_node(n2_loc,1))*my_half
        ym = (r_node(n1_loc,2) + r_node(n2_loc,2))*my_half
        zm = (r_node(n1_loc,3) + r_node(n2_loc,3))*my_half

!       compute left face centroid

        if (n4_loc /= 0) then
          xl = (r_node(n1_loc,1) + r_node(n2_loc,1) + r_node(n3_loc,1) &
              + r_node(n4_loc,1))*my_4th
          yl = (r_node(n1_loc,2) + r_node(n2_loc,2) + r_node(n3_loc,2) &
              + r_node(n4_loc,2))*my_4th
          zl = (r_node(n1_loc,3) + r_node(n2_loc,3) + r_node(n3_loc,3) &
              + r_node(n4_loc,3))*my_4th
        else
          xl = (r_node(n1_loc,1) + r_node(n2_loc,1) + r_node(n3_loc,1))*my_3rd
          yl = (r_node(n1_loc,2) + r_node(n2_loc,2) + r_node(n3_loc,2))*my_3rd
          zl = (r_node(n1_loc,3) + r_node(n2_loc,3) + r_node(n3_loc,3))*my_3rd
        end if

!       compute right face centroid

        if (n6_loc /= 0) then
          xr = (r_node(n1_loc,1) + r_node(n2_loc,1) + r_node(n5_loc,1) &
              + r_node(n6_loc,1))*my_4th
          yr = (r_node(n1_loc,2) + r_node(n2_loc,2) + r_node(n5_loc,2) &
              + r_node(n6_loc,2))*my_4th
          zr = (r_node(n1_loc,3) + r_node(n2_loc,3) + r_node(n5_loc,3) &
              + r_node(n6_loc,3))*my_4th
        else
          xr = (r_node(n1_loc,1) + r_node(n2_loc,1) + r_node(n5_loc,1))*my_3rd
          yr = (r_node(n1_loc,2) + r_node(n2_loc,2) + r_node(n5_loc,2))*my_3rd
          zr = (r_node(n1_loc,3) + r_node(n2_loc,3) + r_node(n5_loc,3))*my_3rd
        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        da(1) = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        da(2) = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        da(3) = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half
!--

!       get gradients at the dual face; either take gradients for this
!       piece of the dual face to be the same as the cell-average gradient
!       computed above  (which is what the legacy FUN3D solver does for tets),
!       or combine with the edge-gradient to increase h-ellipticity on
!       non-simplicial meshes.

        include_edge_gradients : if ( edge_gradients ) then

          ! ex, ey, ez is unit vector along edge direction.

          ex   = r_node(n2_loc,1) - r_node(n1_loc,1)
          ey   = r_node(n2_loc,2) - r_node(n1_loc,2)
          ez   = r_node(n2_loc,3) - r_node(n1_loc,3)
          disi = 1._dp/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          dai = 1._dp/sqrt( da(1)**2 + da(2)**2 + da(3)**2 )
          xnf = da(1)*dai
          ynf = da(2)*dai
          znf = da(3)*dai

          augment_weight = edge_augment_weight( gradient_construction_rhs, &
                                                ex, ey, ez, xnf, ynf, znf )

            ! directional gradients along edge

              egradt = ( turb_node(n2_loc) - turb_node(n1_loc) )*disi

            !  average Green-Gauss gradient in edge direction

            gradt_xi = trbrexavg*ex + trbreyavg*ey + trbrezavg*ez

            trbrex = trbrexavg + ( egradt - gradt_xi )*augment_weight(1)
            trbrey = trbreyavg + ( egradt - gradt_xi )*augment_weight(2)
            trbrez = trbrezavg + ( egradt - gradt_xi )*augment_weight(3)

        else include_edge_gradients

          ! just use Green-Gauss cell-average gradients (this
          ! is what the baseline code does for tets)

            trbrex = trbrexavg
            trbrey = trbreyavg
            trbrez = trbrezavg

        end if include_edge_gradients

!       turbulent diffusion contribution at dual face

!       [nondimensionalization factor : Mach / Re / sigma ]*
!       [normal gradient * area] at dual face

!             Note no positivity required since cb2 > -1
              ngradt = xmre_s*( trbrex*da(1) + trbrey*da(2) &
                              + trbrez*da(3) )

              phi = t_diff1*0.5_dp*(  rnu_node(n1_loc) +   rnu_node(n2_loc) ) &
                  + t_diff2*0.5_dp*(aturb_node(n1_loc) + aturb_node(n2_loc) ) &
                  +    cb20*0.5_dp*( turb_node(n1_loc) +  turb_node(n2_loc) )

              phi_ngradt_n1 = (phi - cb2s*turb_node(n1_loc)) * ngradt
              phi_ngradt_n2 = (phi - cb2s*turb_node(n2_loc)) * ngradt

            rn1 = n_node(n1_loc)
            rn2 = n_node(n2_loc)

            if ( rn1 <= nnodes0 ) then
              res(n_sta,rn1) = res(n_sta,rn1) - phi_ngradt_n1
            end if

            if ( rn2 <= nnodes0 ) then
              res(n_sta,rn2) = res(n_sta,rn2) + phi_ngradt_n2
            end if

      end do edge_loop

    end do diffusion_term_cell

  end subroutine turb_resid_diff_element5

!========================= TURB_JACOB_DIFF_ELEMENT5 ==========================80
!
! Diffusion LHS for mixed element formulation on element basis
!
! Diffusion jacobians for turbulence models on
! mixed-element grids via cell-based integration
! Assumes viscosity at flux-interface by edge-averaging.
!
!=============================================================================80
  subroutine turb_jacob_diff_element5 ( n_sta, eqn_set, nnodes0,               &
                                 turb, qnode, a_diag, a_off,                   &
                                 ncell, c2n, x, y, z,                          &
                                 type_cell, local_f2n, local_e2n, local_f2e,   &
                                 e2n_2d, face_per_cell, node_per_cell,         &
                                 edge_per_cell, ia, ja, n_turb,                &
                                 face_2d, big_angle, nzg2m, g2m,               &
                                 diff_const, diff_const2 )

    use kinddefs,        only : dp, odp
    use info_depr,       only : tref, xmach, re, use_edge_gradients, twod      &
                              , tightly_couple
    use turb_parameters, only : t_diff1, t_diff2, t_diff3
    use fluid,           only : gamma, sutherland_constant
    use element_defs,    only : max_node_per_cell, max_edge_per_cell
    use debug_defs,      only : gradient_construction_lhs
    use solution_types,  only : compressible, incompressible

    integer, intent(in) :: n_sta, eqn_set
    integer, intent(in) :: n_turb
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: face_2d
    integer, intent(in) :: nnodes0

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    logical, dimension(:),                   intent(in) :: big_angle
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(:),                   intent(in) :: ia
    integer, dimension(:),                   intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m

    real(dp),  dimension(:),     intent(in)    :: x, y, z
    real(dp),  dimension(:,:),   intent(in)    :: turb, qnode
    real(dp),  dimension(:,:,:), intent(inout) :: a_diag
    real(odp), dimension(:,:,:), intent(inout) :: a_off

    character(len=3),                        intent(in) :: type_cell
    real(dp),            dimension(n_turb),  intent(in) :: diff_const
    real(dp),            dimension(n_turb),  intent(in) :: diff_const2

    integer :: n, row, ierr, jj, nc
    integer :: ie, i, j, ii, ie_local, i_local, j_local, ioff
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, node, nodec
    integer :: n2
    integer :: k, column

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: terma, termb, termc, ngradt
    real(dp) :: xmre, xmre_s, cb20, cb2s
    real(dp) :: cstar, rho, rhoinv
    real(dp) :: factor, my_xmach
    real(dp) :: ex, ey, ez, disi, dai, xnf, ynf, znf
    real(dp) :: dgradt_xi
    real(dp) :: termd, dphidnu, phi


    real(dp), dimension(3) :: augment_weight, da !, rc
    real(dp), dimension(5) :: dqdnu

    real(dp), dimension(max_node_per_cell) :: turb_node, aturb_node
    real(dp), dimension(max_node_per_cell) :: dturb_node
    real(dp), dimension(max_node_per_cell) :: dphidturb

    real(dp), dimension(max_node_per_cell,3)      :: r_node

   !real(dp), dimension(max_node_per_cell,3) :: dgrad_celldq
    real(dp), dimension(max_node_per_cell) :: dtrbrex, dtrbrey, dtrbrez
    real(dp), dimension(max_node_per_cell) :: dtrbrexavg, dtrbreyavg
    real(dp), dimension(max_node_per_cell) :: dtrbrezavg, dngradt
    real(dp), dimension(max_node_per_cell) :: nu_node, t_node
    integer,  dimension(max_node_per_cell) :: n_node

    logical :: edge_gradients

    real(dp), dimension(node_per_cell,node_per_cell) :: a
    real(dp), dimension(1:5,node_per_cell,node_per_cell) :: af

    integer :: iface
    integer :: nn1, nn2, nn3, nn4, n3_loc, n4_loc, n5_loc, n6_loc

    real(dp) :: xavg, yavg, zavg, cell_vol_inv, cell_vol
    real(dp) :: xavg0, yavg0, zavg0
    real(dp) :: x1, x2, x3, x4, nx, nx0
    real(dp) :: y1, y2, y3, y4, ny, ny0
    real(dp) :: z1, z2, z3, z4, nz, nz0
    real(dp) :: termx, termy, termz
    real(dp) :: termx0, termy0, termz0
    real(dp) :: term0, fact
    real(dp) :: xl, xm, xr, xc
    real(dp) :: yl, ym, yr, yc
    real(dp) :: zl, zm, zr, zc

    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp
    real(dp), parameter :: my_24th = 1.0_dp/24.0_dp
    real(dp), parameter :: my_8th  = 1.0_dp/8.0_dp

  continue

    !For tets in 3D or prisms in 2D, edge gradients add no new info.

    edge_gradients = use_edge_gradients

    if (type_cell == 'tet') edge_gradients = .false.
    if (twod .and. type_cell == 'prz') edge_gradients = .false.

    ierr  = 0
    terma = 0._dp
    termb = 0._dp
    termc = 0._dp

    cstar = sutherland_constant / tref
    my_xmach = 0._dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = 1._dp
    case default
      call lmpi_conditional_stop(1,'eqn_set:turb_jacob_diff_element5')
    end select

    !...to characterize four types of turbulent diffusion.
    cb20 = t_diff3*diff_const2(1) ! t_diff3*cb2
    cb2s = t_diff4*diff_const2(1) ! t_diff4*cb2

    xmre   = my_xmach / re
    xmre_s = xmre /  diff_const(1) ! xmre / sig

    nodes_local = 0

    ! Set some loop indicies and local mapping arrays depending
    ! on whether we are doing a 2D case or a 3D case

    edge_map     = 0
    node_map     = 0

    nodes_local = 0
    nodes_local = 0
    if ( twod ) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    else

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

    fact = 1._dp / real(node_per_cell, dp)

    diffusion_term_cell : do n = 1, ncell

      if ( big_angle(n) ) cycle !skip_viscous_terms

      a  = 0._dp
      af = 0._dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

      do i = 1, node_per_cell

        node = c2n(i,n)       ! global node number

        r_node(i,1) = x(node)
        r_node(i,2) = y(node)
        r_node(i,3) = z(node)

        xc  =  xc + r_node(i,1)
        yc  =  yc + r_node(i,2)
        zc  =  zc + r_node(i,3)

      enddo

      xc  =  xc*fact
      yc  =  yc*fact
      zc  =  zc*fact

!     compute cell averages and set up some local solution arrays

      ! initialization

      t_node(:)       = 0._dp
      nu_node(:)      = 0._dp
      rho           = 0._dp
      rhoinv        = 0._dp

      ! cell averages and set up some local solution arrays

      node_loop : do i_local = 1, nodes_local

        i = node_map(i_local) ! local node number

        node = c2n(i,n)       ! global node number

        n_node(i) = node

        rho        = 1._dp
        rhoinv     = 1._dp
        t_node(i)  = 1._dp
        nu_node(i) = 1._dp
        if ( eqn_set == compressible ) then
          rho        = qnode(1,node)
          rhoinv     = 1._dp/rho
          t_node(i)  = gamma*qnode(5,node)*rhoinv
          nu_node(i) = viscosity_law( cstar, t_node(i) ) * rhoinv
        end if

            turb_node(i) =                turb(1,node)

           aturb_node(i) =  sa0_turb_abs( turb_node(i), nu_node(i) )
           dturb_node(i) = dsa1_turb_abs( turb_node(i), nu_node(i) )

            dphidturb(i) = ( t_diff2*dturb_node(i) &
                         +    cb20                 )*0.5_dp

      end do node_loop

      ! averages over contributing nodes

      dphidnu = t_diff1*0.5_dp

      ! jacobians of the gradients in the primal cell via Green-Gauss

   !   initialization

    dtrbrexavg(:) = 0._dp
    dtrbreyavg(:) = 0._dp
    dtrbrezavg(:) = 0._dp

    cell_vol = 0._dp

    cell_dimension : if (twod) then

      twod_edges : do ie_local = 1,edges_local

!       local node numbers of edge endpoints

        nn1 = e2n_2d(ie_local,1)
        nn2 = e2n_2d(ie_local,2)

        x1 = r_node(nn1,1)
        x2 = r_node(nn2,1)

        z1 = r_node(nn1,3)
        z2 = r_node(nn2,3)

!       edge midpoint (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

        xavg = x1 + x2
        zavg = z1 + z2

!       edge normals

        nx = -(z2 - z1)
        nz =  (x2 - x1)

!       cell volume (area) contributions

        cell_vol = cell_vol + (xavg*nx + zavg*nz)*my_4th

        termx = nx*my_half
        termz = nz*my_half

!       gradient contributions

!        do eqn = 1,qdim
!          qavg = q_node(eqn,nn1) + q_node(eqn,nn2)
!          element_grad(eqn,1) = element_grad(eqn,1) + termx*qavg
!          element_grad(eqn,3) = element_grad(eqn,3) + termz*qavg
!        end do


          dtrbrexavg(nn1) = dtrbrexavg(nn1) + termx
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + termz

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + termx
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + termz

      end do twod_edges

    else cell_dimension

      threed_faces : do iface = 1,face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!         triangular faces of the cell

          x1 = r_node(nn1,1)
          x2 = r_node(nn2,1)
          x3 = r_node(nn3,1)

          y1 = r_node(nn1,2)
          y2 = r_node(nn2,2)
          y3 = r_node(nn3,2)

          z1 = r_node(nn1,3)
          z2 = r_node(nn2,3)
          z3 = r_node(nn3,3)

!         face normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx = (y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1)
          ny = (z2 - z1)*(x3 - x1) - (x2 - x1)*(z3 - z1)
          nz = (x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1)

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg = x1 + x2 + x3
          yavg = y1 + y2 + y3
          zavg = z1 + z2 + z3

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

!         gradient contributions

          dtrbrexavg(nn1) = dtrbrexavg(nn1) + termx
          dtrbreyavg(nn1) = dtrbreyavg(nn1) + termy
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + termz

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + termx
          dtrbreyavg(nn2) = dtrbreyavg(nn2) + termy
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + termz

          dtrbrexavg(nn3) = dtrbrexavg(nn3) + termx
          dtrbreyavg(nn3) = dtrbreyavg(nn3) + termy
          dtrbrezavg(nn3) = dtrbrezavg(nn3) + termz

        else

!         quadrilateral faces of the cell

          x1 = r_node(nn1,1)
          x2 = r_node(nn2,1)
          x3 = r_node(nn3,1)
          x4 = r_node(nn4,1)

          y1 = r_node(nn1,2)
          y2 = r_node(nn2,2)
          y3 = r_node(nn3,2)
          y4 = r_node(nn4,2)

          z1 = r_node(nn1,3)
          z2 = r_node(nn2,3)
          z3 = r_node(nn3,3)
          z4 = r_node(nn4,3)

!         Use simple quadratures to evaluate gradients.  The resulting
!         volume is identical to the volume computed by averaging the
!         volumes associated with the two diagonal split choices.  The
!         quadratures for gradient are not identical, but simpler and
!         consistent across elements which share faces.

!         Define face as average plane 1-2-3-4.  This average plane passes
!         through the center of the quad and through each edge midpoint.
!         The nodes of the quad are displaced from this plane by an identical
!         amount (+-+-) confirming the plane is a least squares fit.

!         face centroid (factor of 1/4 deferred till the
!         contribution to cell_vol is calculated)

          xavg0 = x1 + x2 + x3 + x4
          yavg0 = y1 + y2 + y3 + y4
          zavg0 = z1 + z2 + z3 + z4

!         normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx0 = (y2 - y4)*(z3 - z1) - (z2 - z4)*(y3 - y1)
          ny0 = (z2 - z4)*(x3 - x1) - (x2 - x4)*(z3 - z1)
          nz0 = (x2 - x4)*(y3 - y1) - (y2 - y4)*(x3 - x1)

          term0 = xavg0*nx0 + yavg0*ny0 + zavg0*nz0

!         cell volume contributions

          cell_vol = cell_vol + term0*my_24th

!         gradient contributions

          termx0 = nx0*my_8th
          termy0 = ny0*my_8th
          termz0 = nz0*my_8th

          dtrbrexavg(nn1) = dtrbrexavg(nn1) + termx0
          dtrbreyavg(nn1) = dtrbreyavg(nn1) + termy0
          dtrbrezavg(nn1) = dtrbrezavg(nn1) + termz0

          dtrbrexavg(nn2) = dtrbrexavg(nn2) + termx0
          dtrbreyavg(nn2) = dtrbreyavg(nn2) + termy0
          dtrbrezavg(nn2) = dtrbrezavg(nn2) + termz0

          dtrbrexavg(nn3) = dtrbrexavg(nn3) + termx0
          dtrbreyavg(nn3) = dtrbreyavg(nn3) + termy0
          dtrbrezavg(nn3) = dtrbrezavg(nn3) + termz0

          dtrbrexavg(nn4) = dtrbrexavg(nn4) + termx0
          dtrbreyavg(nn4) = dtrbreyavg(nn4) + termy0
          dtrbrezavg(nn4) = dtrbrezavg(nn4) + termz0

          nx = nx0
          ny = ny0
          nz = nz0

        end if

      end do threed_faces

    end if cell_dimension

    cell_vol_inv = 1._dp/cell_vol


!   need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

    dtrbrexavg(:) = dtrbrexavg(:) * cell_vol_inv
    dtrbreyavg(:) = dtrbreyavg(:) * cell_vol_inv
    dtrbrezavg(:) = dtrbrezavg(:) * cell_vol_inv
!--


      ! next loop over the edges in the cell for each one's
      ! contribution to the jacobian

      edge_loop : do ie_local = 1,edges_local

        ie = edge_map(ie_local)  ! local edge number

        n1_loc = local_e2n(ie,1) ! local node numbers of edge endpoints
        n2_loc = local_e2n(ie,2) ! local node numbers of edge endpoints

       !n1 = c2n(n1_loc,n) ! global node number of edge endpoints
       !n2 = c2n(n2_loc,n) ! global node number of edge endpoints

        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

!       get this edges' contributiuon to the dual normal and area

!       edge midpoint

        xm = (r_node(n1_loc,1) + r_node(n2_loc,1))*my_half
        ym = (r_node(n1_loc,2) + r_node(n2_loc,2))*my_half
        zm = (r_node(n1_loc,3) + r_node(n2_loc,3))*my_half

!       compute left face centroid

        if (n4_loc /= 0) then
          xl = (r_node(n1_loc,1) + r_node(n2_loc,1) + r_node(n3_loc,1) &
              + r_node(n4_loc,1))*my_4th
          yl = (r_node(n1_loc,2) + r_node(n2_loc,2) + r_node(n3_loc,2) &
              + r_node(n4_loc,2))*my_4th
          zl = (r_node(n1_loc,3) + r_node(n2_loc,3) + r_node(n3_loc,3) &
              + r_node(n4_loc,3))*my_4th
        else
          xl = (r_node(n1_loc,1) + r_node(n2_loc,1) + r_node(n3_loc,1))*my_3rd
          yl = (r_node(n1_loc,2) + r_node(n2_loc,2) + r_node(n3_loc,2))*my_3rd
          zl = (r_node(n1_loc,3) + r_node(n2_loc,3) + r_node(n3_loc,3))*my_3rd
        end if

!       compute right face centroid

        if (n6_loc /= 0) then
          xr = (r_node(n1_loc,1) + r_node(n2_loc,1) + r_node(n5_loc,1) &
              + r_node(n6_loc,1))*my_4th
          yr = (r_node(n1_loc,2) + r_node(n2_loc,2) + r_node(n5_loc,2) &
              + r_node(n6_loc,2))*my_4th
          zr = (r_node(n1_loc,3) + r_node(n2_loc,3) + r_node(n5_loc,3) &
              + r_node(n6_loc,3))*my_4th
        else
          xr = (r_node(n1_loc,1) + r_node(n2_loc,1) + r_node(n5_loc,1))*my_3rd
          yr = (r_node(n1_loc,2) + r_node(n2_loc,2) + r_node(n5_loc,2))*my_3rd
          zr = (r_node(n1_loc,3) + r_node(n2_loc,3) + r_node(n5_loc,3))*my_3rd
        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        da(1) = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_half
        da(2) = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_half
        da(3) = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_half

        !jacobians of gradients at the dual face; either take gradients
        !for this piece of the dual face to be the same as the cell-average
        !gradient computed above  (which is what the legacy FUN3D solver does
        !for tets), or combine with the edge-gradient to increase h-ellipticity
        !non-simplicial meshes.

        include_edge_gradients : if (edge_gradients) then

!         ex, ey, ez is unit vector along edge direction


          ex   = r_node(n2_loc,1) - r_node(n1_loc,1)
          ey   = r_node(n2_loc,2) - r_node(n1_loc,2)
          ez   = r_node(n2_loc,3) - r_node(n1_loc,3)
          disi = 1._dp/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          dai = 1._dp/sqrt( da(1)**2 + da(2)**2 + da(3)**2 )
          xnf = da(1)*dai
          ynf = da(2)*dai
          znf = da(3)*dai

          !  average Green-Gauss gradient in edge direction

          !gradt_xi = trbrexavg*ex + trbreyavg*ey + trbrezavg*ez

          ! directional gradients along edge

          !egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

          augment_weight = edge_augment_weight( gradient_construction_lhs, &
                                                ex, ey, ez, xnf, ynf, znf )

          !trbrex = trbrexavg + ( egradt - gradt_xi )*augment_weight(1)
          !trbrey = trbreyavg + ( egradt - gradt_xi )*augment_weight(2)
          !trbrez = trbrezavg + ( egradt - gradt_xi )*augment_weight(3)

          ! avg_term pieces; all active cell nodes contribute.

          do i_local = 1, nodes_local

            !local node number

            i = node_map(i_local) ! local node number

            dgradt_xi = dtrbrexavg(i)*ex+dtrbreyavg(i)*ey+dtrbrezavg(i)*ez

            dtrbrex(i) = dtrbrexavg(i) - dgradt_xi*augment_weight(1)
            dtrbrey(i) = dtrbreyavg(i) - dgradt_xi*augment_weight(2)
            dtrbrez(i) = dtrbrezavg(i) - dgradt_xi*augment_weight(3)

          end do

          ! edge_term pieces; only the two edge nodes contribute.

          dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*augment_weight(1)
          dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*augment_weight(1)
          dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*augment_weight(2)
          dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*augment_weight(2)
          dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*augment_weight(3)
          dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*augment_weight(3)

        else include_edge_gradients

          ! only have the unaltered, average green-gauss contributions;
          ! all active nodes in the cell contribute

            do i_local = 1, nodes_local

              i = node_map(i_local) ! local node number

              dtrbrex(i) = dtrbrexavg(i)
              dtrbrey(i) = dtrbreyavg(i)
              dtrbrez(i) = dtrbrezavg(i)

            end do

        end if include_edge_gradients

        ! Jacobian of normal gradient oriented from n1 to n2.

        ngradt = 0._dp

          dngradt(:) = xmre_s*( dtrbrex(:)*da(1) + dtrbrey(:)*da(2) &
                              + dtrbrez(:)*da(3) )

          do i_local = 1, nodes_local

            i = node_map(i_local) ! local node number

            ngradt  = ngradt + dngradt(i)*turb_node(i)
          enddo

        ! assemble final Jacobian matrices into sparse matrix form

       !edge_average1:  if ( edge_averaging ) then

         !n1 = c2n(n1_loc,n) ! global node number
         !n2 = c2n(n2_loc,n) ! global node number


              phi = t_diff1*0.5_dp*(   nu_node(n1_loc) +    nu_node(n2_loc) ) &
                  + t_diff2*0.5_dp*(aturb_node(n1_loc) + aturb_node(n2_loc) ) &
                  +    cb20*0.5_dp*( turb_node(n1_loc) +  turb_node(n2_loc) )

       !endif edge_average1

        factor = -1._dp

          edge_node_loop_sa : do ii = 1,2

          ! diagonal contributions

          if (ii == 1) then
            i = n1_loc
            node = n_node(i)      !c2n(n1_loc,n) ! global node number
            n2   = n_node(n2_loc) !c2n(n2_loc,n)
          else
            i = n2_loc
            node = n_node(i)      !c2n(n2_loc,n) ! global node number
            n2   = n_node(n1_loc) !c2n(n1_loc,n)
          end if

          !n1c = local_e2n(ie,1) ! local node numbers of edge endpoints
          !n2c = local_e2n(ie,2)

          !if ( n1 <= nnodes0 ) then
          !  res(n_sta,n1) = res(n_sta,n1) - ( phi - cb2s*turb1 )*ngradt(1)
          !end if

          !if ( n2 <= nnodes0 ) then
          !  res(n_sta,n2) = res(n_sta,n2) + ( phi - cb2s*turb2 )*ngradt(1)
          !end if

          factor = -1._dp*factor

          terma  = phi - cb2s*turb(1,node)

          termb  = -factor*( dphidturb(i) - cb2s )*ngradt

          if ( node <= nnodes0 ) then

            a(i,i) = a(i,i)                                   &
                                    - factor*terma*dngradt(i) &
                                    + termb

            if ( tightly_couple .and. eqn_set == compressible ) then

              termd  = -factor*( dphidnu )*ngradt

              nc = node
              dqdnu(1:5) = dqc_via_dnu( termd, cstar, qnode(1,nc), &
               qnode(2,nc), qnode(3,nc), qnode(4,nc), qnode(5,nc) )
              do jj=1,5
                af(jj,i,i) = af(jj,i,i) + dqdnu(jj)
              enddo

            endif
          end if

          ! off-diagonal contributions

          node_loop_2 : do j_local = 1, nodes_local

            j = node_map(j_local) ! local node number

            nodec = n_node(j) !c2n(j,n)      ! global node number

            if (nodec == node) cycle node_loop_2

            if ( node <= nnodes0 ) then

              ! avoid unused entries in a_off
              if (node > nnodes0 .and. nodec > nnodes0) cycle node_loop_2


              ! res(n1) = res(n1) - ( phi - cb2s*turb1 )*ngradt
              ! res(n2) = res(n2) + ( phi - cb2s*turb2 )*ngradt

              if ( nodec /= n2 ) then
                termc = 0._dp
              else
                termc  = -factor*( dphidturb(j)               )*ngradt
              endif

              a(i,j) = a(i,j)                                   &
                                      - factor*terma*dngradt(j) &
                                      + termc

              if ( tightly_couple .and. eqn_set == compressible ) then
              if ( nodec == n2 ) then

                nc = nodec
                dqdnu(1:5) = dqc_via_dnu( termd, cstar, qnode(1,nc), &
                 qnode(2,nc), qnode(3,nc), qnode(4,nc), qnode(5,nc) )
                do jj=1,5
                  af(jj,i,j) = af(jj,i,j) + dqdnu(jj)
                enddo

              endif
              endif
            end if

          end do node_loop_2

        end do edge_node_loop_sa

      end do edge_loop

      ! Install Jacobians into global arrays.

      outer_install : do i_local = 1, nodes_local

        i = node_map(i_local) ! local node number

        node = n_node(i) !c2n(i,n)
        if ( node <= nnodes0 ) then

          inner_install : do j_local = 1, nodes_local

            j = node_map(j_local) ! local node number

            if ( i == j ) then

              row = g2m(node)

              a_diag(n_sta,n_sta,row) = a_diag(n_sta,n_sta,row) &
                                      + a(i,j)

              if ( tightly_couple .and. eqn_set == compressible ) then

                do jj=1,5
                a_diag(n_sta,jj,row) = a_diag(n_sta,jj,row) &
                                      + af(jj,i,j)
                enddo

              endif

            else

              nodec = n_node(j) !c2n(j,n)

              ioff = 0
              search : do k = ia(node), ia(node+1) - 1
                column = ja(k)
                if (column == nodec) then
                  ioff = nzg2m(k)
                  exit search
                endif
              end do search

              a_off(n_sta,n_sta,ioff) = a_off(n_sta,n_sta,ioff) &
                                      + real(a(i,j),odp)

              if ( tightly_couple .and. eqn_set == compressible ) then

                do jj=1,5
                a_off(n_sta,jj,ioff) = a_off(n_sta,jj,ioff) &
                                      + real(af(jj,i,j),odp)
                enddo

              endif

            endif

          end do inner_install

        end if
      end do outer_install

    end do diffusion_term_cell

    call lmpi_conditional_stop(ierr,'off-diag site2:turb_jacob_diff_element5')

  end subroutine turb_jacob_diff_element5

  include 'viscosity_law.f90'
  include 'dviscosity_law.f90'
  include 'edge_augment_weight.f90'
  include 'sa0_turb_abs.f90'
  include 'dsa1_turb_abs.f90'
  include 'dqc_via_dnu.f90'

end module turb_diffusion5


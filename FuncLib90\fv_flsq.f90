!================================= FV_FLSQ ===================================80
!
! This routine computes the viscous fluxes using face-based-lsq.
! (and returns face value of velocity)
! Note: tx, ty, tz is unit normal to cell face.
!
!=============================================================================80

  pure function fv_flsq( lsq_mref, cell1, cell2, tx, ty, tz, area,             &
                         ncell01, n_tot, xc, yc, zc, qcell, amut,              &
                         flsq_lu, flsq_ja,                                     &
                         slen,                                                 &
                         face, flsq_n, flsq_ni, flsq_nb, bcc )

    use flux_constants, only : gamma, cstar
    use lsq_constants,  only : wflsq1, flsqn_max, tf
    use cc_defs,        only : lsq_face_bounded, revert_null
    use bc_types,       only : bcc_type
    use lsq_types,      only : lsq_ref_type

    type(lsq_ref_type), intent(in) :: lsq_mref
    type(bcc_type), intent(in) :: bcc

    integer, intent(in) :: face, flsq_n, flsq_ni, flsq_nb
    integer, intent(in) :: cell1, cell2, n_tot, ncell01

    real(dp),                           intent(in) :: tx, ty, tz, area

    real(dp), dimension(:),             intent(in) :: xc, yc, zc, slen
    real(dp), dimension(n_tot,ncell01), intent(in) :: qcell
    real(dp), dimension(ncell01),       intent(in) :: amut

    real(dp), dimension(4,4),            intent(in) :: flsq_lu
    integer,  dimension(flsq_ni),        intent(in) :: flsq_ja

    integer :: ii, jj, cella, eq, qteq, fb

    real(dp) :: xiel, etal, ziel
    real(dp) :: ex, ey, ez
    real(dp) :: scalei, a11, deti
    real(dp) :: muf, amutf, wsq, lgrad, mgrad, lsqdf_error
    real(dp), dimension(3) :: fgrad

    real(dp), dimension(4) :: f, c0, c1, c2, c3, lc_max
    real(dp), dimension(5) :: qta, egrad, qtmin, qtmax, qtf
    real(dp), dimension(9) :: fv_flsq
    real(dp), dimension(4,flsqn_max) :: lc
    real(dp), dimension(flsqn_max) :: face_wsq
    integer,  dimension(flsqn_max) :: list
    real(dp), dimension(3,5) :: qtgrad

    real(dp), dimension(3,3) :: tef, trf

    logical :: edge_based

  continue

    egrad(1) = 0._dp

    fv_flsq(9) = 0._dp
    edge_based  = .false.

    !...Set coordinate transformation.
    trf = mapping_system( tx, ty, tz)

    qtgrad(:,1) = 0._dp

    scalei = flsq_lu(1,1)

    !...ex, ey, ez is unit vector along edge direction
    ex  = 0.5_dp*( xc(cell2) - xc(cell1) )*scalei
    ey  = 0.5_dp*( yc(cell2) - yc(cell1) )*scalei
    ez  = 0.5_dp*( zc(cell2) - zc(cell1) )*scalei

    deti =  1._dp / ( ex*tx + ey*ty + ez*tz )

    tef = tinv_3d( ex, ey, ez, trf(2,1), trf(2,2), trf(2,3),      &
                               trf(3,1), trf(3,2), trf(3,3), deti )

    egrad(2:4) = 0.5_dp*( qcell(2:4,cell2)        &
                        - qcell(2:4,cell1) )*scalei
    egrad(  5) = 0.5_dp*gamma*( qcell(5,cell2)/qcell(1,cell2)        &
                              - qcell(5,cell1)/qcell(1,cell1) )*scalei

    !...full least squares face

    face_wsq(1:flsq_n+2) = 1._dp
    if( abs(wflsq1-1._dp) > 1.0e-06_dp  .and. ( flsq_nb == 0 ) ) then
      face_wsq(1:flsq_n+2) = flsq_wsq( flsq_n, cell1, cell2, ncell01, &
                                       xc, yc, zc, flsq_ja )
    endif

    c0  = 0._dp
    c1  = 0._dp
    c2  = 0._dp
    c3  = 0._dp
    a11 = 0._dp
    jj  = 0
    do ii=1,flsq_ni
      cella = flsq_ja(ii)
      jj = jj + 1
      list(jj) = cella
      lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                             xc(cella), yc(cella), zc(cella), slen(cella) )
    enddo
    jj = jj + 1
    list(jj) = cell1
    lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                           xc(cell1), yc(cell1), zc(cell1), slen(cell1) )
    jj = jj + 1
    list(jj) = cell2
    lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                           xc(cell2), yc(cell2), zc(cell2), slen(cell2) )
    do ii=bcc%flsq_ib(face),bcc%flsq_ib(face+1)-1
      fb = bcc%flsq_jb(ii)
      jj = jj + 1
      list(jj) = fb
      lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                             bcc%xface(fb), bcc%yface(fb), bcc%zface(fb), &
                             bcc%slenface(fb) )
    enddo

    lc_max = lsq_lc_max( jj, lc, face_wsq )

    !...construct u,v,w & T at the cell face
    !...define stencil upper and lower bounds of u,v,w, & T
    qtmin(2:5) =  huge(1._dp)
    qtmax(2:5) = -huge(1._dp)
    do ii=1,jj

      wsq   = face_wsq(ii)
      xiel  = lc(1,ii)/lc_max(1)
      etal  = lc(2,ii)/lc_max(2)
      ziel  = lc(3,ii)/lc_max(3)

      !...u,v,w,T variables.
      if ( ii <= flsq_ni + 2 ) then
        qta(2:4) = qcell(2:4,list(ii))
        qta(5)   = gamma*qcell(5,list(ii))/qcell(1,list(ii))
      else
        qta(2:5) = bcc%qt(2:5,list(ii))
      endif

      !...min/max u,v,w, & T variables.
      qtmin(2:5) = min( qtmin(2:5), qta(2:5) )
      qtmax(2:5) = max( qtmax(2:5), qta(2:5) )

      qta(2:5) = wsq*qta(2:5)

      !...face u,v,w,T variables
      c0(1:4) =  c0(1:4) +      qta(2:5)
      c1(1:4) =  c1(1:4) + xiel*qta(2:5)
      c2(1:4) =  c2(1:4) + etal*qta(2:5)
      c3(1:4) =  c3(1:4) + ziel*qta(2:5)

      a11 = a11 + wsq

    enddo

    a11 = 1._dp/a11

    ! Forward...sequential access to flsq_lu.

    Flsq_eq_loop : do eq=4,1,-1

      f(1) = c0(eq)
      f(2) = c1(eq) - flsq_lu(2,1)*f(1)
      f(3) = c2(eq) - flsq_lu(3,1)*f(1)
      f(4) = c3(eq) - flsq_lu(4,1)*f(1)

      f(3) = f(3) - flsq_lu(3,2)*f(2)
      f(4) = f(4) - flsq_lu(4,2)*f(2)

      f(4) = f(4) - flsq_lu(4,3)*f(3)

      ! Backward...sequential access to flsq_lu.

      f(4) = f(4) * flsq_lu(4,4)
      f(1) = f(1) - flsq_lu(1,4)*f(4)
      f(2) = f(2) - flsq_lu(2,4)*f(4)
      f(3) = f(3) - flsq_lu(3,4)*f(4)

      f(3) = f(3) * flsq_lu(3,3)
      f(1) = f(1) - flsq_lu(1,3)*f(3)
      f(2) = f(2) - flsq_lu(2,3)*f(3)

      f(2) = f(2) * flsq_lu(2,2)
      f(1) = f(1) - flsq_lu(1,2)*f(2)

      f(1) = f(1) * a11 !flsq_lu(1,1)

      qteq      = eq + 1
      qtf(qteq) = f(1)

      !...detect out of bound u,v,w or T
      edge_based = .false.
      if ( lsq_face_bounded ) then
        !..bounded least squares face
        if ((qtf(qteq) < qtmin(qteq)) .and. (qtmin(qteq) /= 0._dp)) then
          lsqdf_error = abs(qtf(qteq)-qtmin(qteq))/qtmin(qteq)
        elseif ((qtf(qteq) > qtmax(qteq)) .and. (qtmax(qteq) /= 0._dp)) then
          lsqdf_error = abs(qtf(qteq)-qtmax(qteq))/qtmax(qteq)
        elseif (qtf(qteq) < qtmin(qteq)) then
          lsqdf_error = abs(qtf(qteq)-qtmin(qteq))
        elseif (qtf(qteq) > qtmax(qteq)) then
          lsqdf_error = abs(qtf(qteq)-qtmax(qteq))
        else
          lsqdf_error = 0._dp
        endif
        if (lsqdf_error >= 0.01_dp) then
!         write(700+lmpi_id,*) ' qteq = ', qteq
!         write(700+lmpi_id,*) ' flsq_n = ', flsq_n
!         write(700+lmpi_id,*) ' lsqdf_error =', lsqdf_error
!         write(700+lmpi_id,*) ' qtmin(qteq) =', qtmin(qteq)
!         write(700+lmpi_id,*) ' qtf(2) =', qtf(2)
!         write(700+lmpi_id,*) ' qtf(3) =', qtf(3)
!         write(700+lmpi_id,*) ' qtf(4) =', qtf(4)
!         write(700+lmpi_id,*) ' qtf(5) =', qtf(5)
!         write(700+lmpi_id,*) ' qtmax(qteq) =', qtmax(qteq)
!         fv_flsq(9) = 1._dp + lsqdf_error
          fv_flsq(9) = 1._dp + abs(fv_flsq(9))
          edge_based = .true.
          exit ! Flsq_eq_loop
        end if
      else
        fv_flsq(9) = 0._dp
      endif

      !...Cartesian gradients at the face.
      fgrad = lsq_gradc( lsq_mref, f(2:4), lc_max )

      !...gradients in face directions.
      lgrad = fgrad(1)*trf(2,1) + fgrad(2)*trf(2,2) + fgrad(3)*trf(2,3)
      mgrad = fgrad(1)*trf(3,1) + fgrad(2)*trf(3,2) + fgrad(3)*trf(3,3)

      !...resolve gradients from edge and face.
      qtgrad(:,qteq) = tef(:,1)*egrad(qteq) + tef(:,2)*lgrad + tef(:,3)*mgrad

    enddo Flsq_eq_loop

!   For out-of-bounds interpolation, revert.

    if ( edge_based ) then

      if ( revert_null ) then
        qtgrad(:,:) = 0._dp
        fv_flsq(9)  = -fv_flsq(9)
      else
        qtgrad(1,2:5) = ex*egrad(2:5)
        qtgrad(2,2:5) = ey*egrad(2:5)
        qtgrad(3,2:5) = ez*egrad(2:5)
      endif

      qtf(2:4) = 0.5_dp*( qcell(2:4,cell1) + qcell(2:4,cell2) )

      qtf(5)   = 0.5_dp*gamma*( qcell(5,cell1)/qcell(1,cell1) &
                              + qcell(5,cell2)/qcell(1,cell2) )
    endif

    muf   = viscosity_law( cstar, qtf(5) )
    amutf = 0.5_dp*( amut(cell1) + amut(cell2) )
    fv_flsq(1:5) = tau_v( tx, ty, tz, area,                          &
                          qtgrad, qtf(2), qtf(3), qtf(4), muf, amutf )

    fv_flsq(6:8) = qtf(2:4)

  end function fv_flsq

module chaos_brute_force

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  implicit none

  private

  public :: linear_solve

contains

!===================================== LINEAR_SOLVE ==========================80
!
!   Calls GMRES
!
!=============================================================================80
  subroutine linear_solve(key)

    use lmpi,               only : lmpi_die, lmpi_master, lmpi_reduce
    use kinddefs,           only : dp
    use chaos_datas,        only : nnodes, time_data, timestep, ia, ja, iau,   &
                                   overlap, ntimesteps
    use chaos_construction, only : build_adjoint_rhs, atimesx, build_matrix,   &
                                   build_tangent_rhs, verify_check,            &
                                   postprocess_tangent,                        &
                                   postprocess_tangent_initial, boris_check,   &
                                   transfer_matrix
    use outputter,          only : plot_solution, write_solution

    integer :: its, worksize, n, key, i1, i2, i, extent

    integer, parameter :: krylov_dimension = 200
    integer, parameter :: max_matvecs      = 200

    integer, dimension(16) :: ipar

    integer, dimension(:), allocatable :: iw

    real(dp) :: res, gradient, global_gradient

    real(dp), dimension(16) :: fpar

    real(dp), dimension(:),     allocatable :: solution, wk, rhs, tangent
    real(dp), dimension(:),     allocatable :: tangent0
    real(dp), dimension(:,:,:), allocatable :: p

  continue

! Set some values and allocate some things

    n        = nnodes*5
    worksize = (n+3)*(krylov_dimension+2)                                      &
             + (krylov_dimension+1)*krylov_dimension/2

    allocate(wk(worksize))
    allocate(rhs(n))
    allocate(solution(n))

    its = 0
    res = 0.0_dp

    ipar(1) = 0                ! Initialize the solver
    ipar(2) = 2                ! Preconditioning  (0=none, 1=left, 2=right)
    ipar(3) = 2                ! Stopping criterion
    ipar(4) = worksize         ! Provided workspace size
    ipar(5) = krylov_dimension ! Size of Krylov subspace
    ipar(6) = max_matvecs      ! Max number of matvecs
    fpar(1) = 1.e-15_dp        ! Relative convergence criterion
    fpar(2) = 1.e-15_dp        ! Absolute convergence criterion
    fpar(11) = 0               ! flop count initialization

    solution = 0.0_dp  ! Set the initial guess

    if ( lmpi_master ) write(*,*)

! Form the right hand side

    select case(key)
    case(1)
      call build_adjoint_rhs(rhs)
    case(2)
      call build_tangent_rhs(rhs)
    end select

! Allocate and decompose the preconditioner

    allocate(p(5,5,size(ja,1)))

    if ( overlap ) then
      if ( timestep == 1 .or. timestep == ntimesteps ) then
        extent = 2*nnodes
      else
        extent = 3*nnodes
      endif
    else
      extent = nnodes
    endif

    allocate(iw(extent))

    call build_matrix(p)

! For now, put identity matrix into backward and forward time planes
! of overlapped matrix

!   if ( overlap ) call insert_identity(p)

    if ( overlap ) call transfer_matrix(p)

    call block5_ilu0_decompose(extent,ia,ja,iau,p,iw,2)

! Call GMRES

#ifdef HAVE_SPARSKIT
100 call gmres(n,rhs,solution,ipar,fpar,wk)
#else
100 write(*,*) 'This executable not built against SPARSKIT library: stopping.'
    call lmpi_die
    stop
#endif

    if ( ipar(7) /= its ) then
      if ( lmpi_master ) write(*,*) its, res
      its = ipar(7)
    endif

    res = fpar(5)

! Act on GMRES feedback and return - either matvec or preconditioning

    select case(ipar(1))
    case(-10)
      if ( lmpi_master ) write(*,*) 'Failure for non-numerical reasons...'
      call lmpi_die
      stop
    case(-9)
      if ( lmpi_master ) write(*,*) 'Abnormal number detected...'
      call lmpi_die
      stop
    case(-4)
      if ( lmpi_master ) then
        write(*,*) 'Invalid values of fpar(1) and fpar(2):'
        write(*,*) 'fpar(1) = ', fpar(1), 'fpar(2) = ', fpar(2)
      endif
      call lmpi_die
      stop
    case(-3)
      if ( lmpi_master ) write(*,*) 'Iterative solver is facing a breakdown.', &
                                    ipar(12)
      call lmpi_die
      stop
    case(-2)
      if ( lmpi_master ) then
        write(*,*) 'Iterative solver was not given enough work space.'
        write(*,*) 'Work space should have at least ', ipar(4), ' elements.'
      endif
      call lmpi_die
      stop
    case(-1)
      if ( lmpi_master ) write(*,*) 'Iterations exceed limit.'
    case(0)
      ! Iterative solver satisfied convergence test
    case(1)

      call atimesx(n,wk(ipar(8)),wk(ipar(9)))

      goto 100

    case(2)
      write(*,*) 'A-transpose matvec requested but not implemented!'
      call lmpi_die
      stop
    case(3,5)

!     call diagonal_preconditioner(n,wk(ipar(9)),wk(ipar(8)))

      call block5_ilu0_solve(extent,wk(ipar(9)),wk(ipar(8)),p,ja,ia,iau,nnodes)

      goto 100

    case(4,6)
      write(*,*) 'A-transpose solve requested but not implemented!'
      call lmpi_die
      stop
    case(10)
      if ( lmpi_master ) write(*,*) 'Caller requested to perform stopping test.'
      call lmpi_die
      stop
    case default
      if ( lmpi_master ) then
        write(*,*) 'Iterative solver returned unknown code: ', ipar(1)
      endif
      call lmpi_die
      stop
    end select

    if ( lmpi_master ) write(*,*) ipar(7), fpar(6)
    if ( lmpi_master ) write(*,*)

    deallocate(wk,p,iw)

! Now compute gradient

    select case(key)
    case(1)  ! adjoint

      call boris_check(n,solution)

      gradient = 0.0_dp
      do i = 1, nnodes
        i1 = (i-1)*5 + 1
        i2 = (i-1)*5 + 5
        gradient = gradient + dot_product(solution(i1:i2),time_data%drdd(:,i))
      end do
      call lmpi_reduce(gradient, global_gradient)
      if ( lmpi_master ) write(*,*) 'Adjoint gradient = ', global_gradient

      call write_solution('adjoint',solution,timestep)
      call plot_solution('adjoint',solution,timestep)

    case(2)  ! tangent

      call build_adjoint_rhs(rhs)

      gradient = dot_product(solution, rhs)
      call lmpi_reduce(gradient, global_gradient)
      if ( lmpi_master ) write(*,*) 'Tangent gradient = ', global_gradient

! Tangent postprocessing for levels 1 through N

      allocate(tangent(n))
      call postprocess_tangent(solution,tangent)
      call write_solution('tangent',tangent,timestep)
      call plot_solution('tangent',tangent,timestep)

! Master now postprocesses tangent at 0

      if ( lmpi_master ) then
        allocate(tangent0(n))
        call postprocess_tangent_initial(solution,tangent0)
        call write_solution('tangent',tangent0,0)
        call plot_solution('tangent',tangent0,0)
      else
        allocate(tangent0(1))
      endif

      call verify_check(tangent,tangent0)

      deallocate(tangent,tangent0)

    end select

    deallocate(rhs,solution)

  end subroutine linear_solve


!============================= BLOCK5_ILU0_DECOMPOSE =========================80
!
!   Perform ILU(0) on matrix composed of 5x5 sub-blocks
!
!=============================================================================80
  subroutine block5_ilu0_decompose(neq,ia,ja,iau,alu,iw,matrix_inverse)

    use kinddefs, only : dp
    use lmpi,     only : lmpi_die

    integer, intent(in) :: neq, matrix_inverse

    integer, dimension(:), intent(in)  :: ia
    integer, dimension(:), intent(in)  :: ja
    integer, dimension(:), intent(in)  :: iau
    integer, dimension(:), intent(out) :: iw

    real(dp), dimension(:,:,:), intent(inout) :: alu

    integer :: i,k,j1,j2,j,jrow,l1,l2,jj,jw,icode

    real(dp) :: u12,u13,u14,u15,u23,u24,u25,u34,u35,u45,d1,d2,d3,d4
    real(dp) :: l11,l21,l31,l41,l51,l22,l32,l42,l52
    real(dp) :: l33,l43,l53,l44,l54,l55

    real(dp), dimension(5,5) :: rhs

    real(dp), parameter :: one = 1.0_dp
    real(dp), parameter :: zer = 0.0_dp

    real(dp), parameter, dimension(5,5) :: eye =                               &
                                       reshape( source=(/one,zer,zer,zer,zer,  &
                                                        &zer,one,zer,zer,zer,  &
                                                        &zer,zer,one,zer,zer,  &
                                                        &zer,zer,zer,one,zer,  &
                                                        &zer,zer,zer,zer,one/),&
                                                shape=(/5,5/))

    real(dp), dimension(5,5) :: tmat

  continue

! initialize work vector to zero's

    do i = 1, neq
      iw(i) = 0
    enddo

!-------------- MAIN LOOP ----------------------------------

    do k = 1, neq

!------------------------ k = row number -------------------

      j1 = ia(k)
      j2 = ia(k+1)-1
      do j = j1, j2
        iw(ja(j)) = j
      enddo

      j=j1
150   jrow = ja(j)

!----------------------- Exit if diagonal element is reached.

      if (jrow >= k) goto 200

!----------------------- Compute the multiplier for jrow.

      do l1 = 1,5
        do l2 = 1,5
          tmat(l1,l2) = alu(l1,1,j)*alu(1,l2,iau(jrow))                        &
                      + alu(l1,2,j)*alu(2,l2,iau(jrow))                        &
                      + alu(l1,3,j)*alu(3,l2,iau(jrow))                        &
                      + alu(l1,4,j)*alu(4,l2,iau(jrow))                        &
                      + alu(l1,5,j)*alu(5,l2,iau(jrow))
        enddo
      enddo

      do l1 = 1,5
        alu(l1,1,j) = tmat(l1,1)
        alu(l1,2,j) = tmat(l1,2)
        alu(l1,3,j) = tmat(l1,3)
        alu(l1,4,j) = tmat(l1,4)
        alu(l1,5,j) = tmat(l1,5)
      enddo

!-----------------------performlinear combination

      do jj = iau(jrow)+1, ia(jrow+1)-1
        jw = iw(ja(jj))
        if (jw /= 0)then
          if (jw /= iau(k) )then
           do l1=1,5
             do l2=1,5
              alu(l1,l2,jw) = alu(l1,l2,jw)                                    &
                                - tmat(l1,1)*alu(1,l2,jj)                      &
                                - tmat(l1,2)*alu(2,l2,jj)                      &
                                - tmat(l1,3)*alu(3,l2,jj)                      &
                                - tmat(l1,4)*alu(4,l2,jj)                      &
                                - tmat(l1,5)*alu(5,l2,jj)
             enddo
           enddo
          else
           do l1=1,5
             do l2=1,5
              alu(l1,l2,iau(k)) = alu(l1,l2,iau(k))                            &
                                - tmat(l1,1)*alu(1,l2,jj)                      &
                                - tmat(l1,2)*alu(2,l2,jj)                      &
                                - tmat(l1,3)*alu(3,l2,jj)                      &
                                - tmat(l1,4)*alu(4,l2,jj)                      &
                                - tmat(l1,5)*alu(5,l2,jj)
             enddo
           enddo
          endif
        endif
      enddo

      j=j+1
      if (j <= j2) goto 150

!----------------------- define and store lower part

200   continue

      if (jrow /= k) goto 600

      select case(matrix_inverse)
      case(1)

        l11 = 1.e0_dp/alu(1,1,iau(k))
        u12 = alu(1,2,iau(k))*l11
        u13 = alu(1,3,iau(k))*l11
        u14 = alu(1,4,iau(k))*l11
        u15 = alu(1,5,iau(k))*l11
        l21 = alu(2,1,iau(k))
        l22 = 1.e0_dp/( ((alu(2,2,iau(k))) -l21*u12) )
        u23 = ( ((alu(2,3,iau(k))) -l21*u13) )*l22
        u24 = ( ((alu(2,4,iau(k))) -l21*u14) )*l22
        u25 = ( ((alu(2,5,iau(k))) -l21*u15) )*l22
        l31 = alu(3,1,iau(k))
        l32 = (alu(3,2,iau(k))) -l31*u12
        l33 = 1.e0_dp/( (((alu(3,3,iau(k))) -l31*u13) -l32*u23))
        u34 = ( (((alu(3,4,iau(k))) -l31*u14) -l32*u24) )*l33
        u35 = ( (((alu(3,5,iau(k))) -l31*u15) -l32*u25) )*l33
        l41 = alu(4,1,iau(k))
        l42 = (alu(4,2,iau(k))) -l41*u12
        l43 = (((alu(4,3,iau(k))) -l41*u13) -l42*u23)
        l44 = 1.e0_dp/( (((alu(4,4,iau(k))) -l41*u14) -l42*u24)-l43*u34 )
        u45 = (((alu(4,5,iau(k)) - l41*u15) - l42*u25) - l43*u35)*l44
        l51 = alu(5,1,iau(k))
        l52 = alu(5,2,iau(k)) - l51*u12
        l53 = ((alu(5,3,iau(k)) - l51*u13) - l52*u23)
        l54 = (((alu(5,4,iau(k)) - l51*u14) - l52*u24) - l53*u34)
        l55 = 1.e0_dp/((((alu(5,5,iau(k)) - l51*u15) - l52*u25) - l53*u35)     &
            - l54*u45)

        alu(1,1,iau(k)) = 1.0_dp
        alu(1,2,iau(k)) = 0.0_dp
        alu(1,3,iau(k)) = 0.0_dp
        alu(1,4,iau(k)) = 0.0_dp
        alu(1,5,iau(k)) = 0.0_dp

        alu(2,1,iau(k)) = 0.0_dp
        alu(2,2,iau(k)) = 1.0_dp
        alu(2,3,iau(k)) = 0.0_dp
        alu(2,4,iau(k)) = 0.0_dp
        alu(2,5,iau(k)) = 0.0_dp

        alu(3,1,iau(k)) = 0.0_dp
        alu(3,2,iau(k)) = 0.0_dp
        alu(3,3,iau(k)) = 1.0_dp
        alu(3,4,iau(k)) = 0.0_dp
        alu(3,5,iau(k)) = 0.0_dp

        alu(4,1,iau(k)) = 0.0_dp
        alu(4,2,iau(k)) = 0.0_dp
        alu(4,3,iau(k)) = 0.0_dp
        alu(4,4,iau(k)) = 1.0_dp
        alu(4,5,iau(k)) = 0.0_dp

        alu(5,1,iau(k)) = 0.0_dp
        alu(5,2,iau(k)) = 0.0_dp
        alu(5,3,iau(k)) = 0.0_dp
        alu(5,4,iau(k)) = 0.0_dp
        alu(5,5,iau(k)) = 1.0_dp

! 1st column

        d1 = alu(1,1,iau(k))*l11
        d2 = (alu(2,1,iau(k)) -l21*d1 )*l22
        d3 = (((alu(3,1,iau(k)))-l31*d1)-l32*d2)*l33
        d4 = (((alu(4,1,iau(k)) - l41*d1) - l42*d2) - l43*d3)*l44

        alu(5,1,iau(k))  = ((((alu(5,1,iau(k)) - l51*d1) - l52*d2) - l53*d3)   &
                        - l54*d4)*l55
        alu(4,1,iau(k)) = d4 - u45*alu(5,1,iau(k))
        alu(3,1,iau(k)) = d3 - u34*alu(4,1,iau(k)) - u35*alu(5,1,iau(k))
        alu(2,1,iau(k)) = d2 - u23*alu(3,1,iau(k)) - u24*alu(4,1,iau(k))       &
                             - u25*alu(5,1,iau(k))
        alu(1,1,iau(k)) = d1 - u12*alu(2,1,iau(k)) - u13*alu(3,1,iau(k))       &
                             - u14*alu(4,1,iau(k)) - u15*alu(5,1,iau(k))

! 2nd column

        d1 = alu(1,2,iau(k))*l11
        d2 = (alu(2,2,iau(k)) -l21*d1 )*l22
        d3 = ( ((alu(3,2,iau(k)))-l31*d1)-l32*d2)*l33
        d4 = (((alu(4,2,iau(k)) - l41*d1) - l42*d2) - l43*d3)*l44

        alu(5,2,iau(k)) = ((((alu(5,2,iau(k)) - l51*d1) - l52*d2) - l53*d3)    &
                        - l54*d4)*l55
        alu(4,2,iau(k)) = d4 - u45*alu(5,2,iau(k))
        alu(3,2,iau(k)) = d3 - u34*alu(4,2,iau(k)) - u35*alu(5,2,iau(k))
        alu(2,2,iau(k)) = d2 - u23*alu(3,2,iau(k)) - u24*alu(4,2,iau(k))       &
                             - u25*alu(5,2,iau(k))
        alu(1,2,iau(k)) = d1 - u12*alu(2,2,iau(k)) - u13*alu(3,2,iau(k))       &
                             - u14*alu(4,2,iau(k)) - u15*alu(5,2,iau(k))

! 3rd column

        d1 = alu(1,3,iau(k))*l11
        d2 = (alu(2,3,iau(k)) -l21*d1 )*l22
        d3 = ( ((alu(3,3,iau(k)))-l31*d1)-l32*d2)*l33
        d4 = (((alu(4,3,iau(k)) - l41*d1) - l42*d2) - l43*d3)*l44

        alu(5,3,iau(k)) = ((((alu(5,3,iau(k)) - l51*d1) - l52*d2) - l53*d3)    &
                                              - l54*d4)*l55
        alu(4,3,iau(k)) = d4 - u45*alu(5,3,iau(k))
        alu(3,3,iau(k)) = d3 - u34*alu(4,3,iau(k)) - u35*alu(5,3,iau(k))
        alu(2,3,iau(k)) = d2 - u23*alu(3,3,iau(k)) - u24*alu(4,3,iau(k))       &
                             - u25*alu(5,3,iau(k))
        alu(1,3,iau(k)) = d1 - u12*alu(2,3,iau(k)) - u13*alu(3,3,iau(k))       &
                             - u14*alu(4,3,iau(k)) - u15*alu(5,3,iau(k))

! 4th column

        d1 = alu(1,4,iau(k))*l11
        d2 = (alu(2,4,iau(k)) -l21*d1 )*l22
        d3 = ( ((alu(3,4,iau(k)))-l31*d1)-l32*d2)*l33
        d4 = (((alu(4,4,iau(k)) - l41*d1) - l42*d2) - l43*d3)*l44

        alu(5,4,iau(k)) = ((((alu(5,4,iau(k)) - l51*d1) - l52*d2) - l53*d3)    &
                                              - l54*d4)*l55
        alu(4,4,iau(k)) = d4 - u45*alu(5,4,iau(k))
        alu(3,4,iau(k)) = d3 - u34*alu(4,4,iau(k)) - u35*alu(5,4,iau(k))
        alu(2,4,iau(k)) = d2 - u23*alu(3,4,iau(k)) - u24*alu(4,4,iau(k))       &
                             - u25*alu(5,4,iau(k))
        alu(1,4,iau(k)) = d1 - u12*alu(2,4,iau(k)) - u13*alu(3,4,iau(k))       &
                             - u14*alu(4,4,iau(k)) - u15*alu(5,4,iau(k))

! 5th column

        d1 = alu(1,5,iau(k))*l11
        d2 = (alu(2,5,iau(k)) -l21*d1 )*l22
        d3 = ( ((alu(3,5,iau(k)))-l31*d1)-l32*d2)*l33
        d4 = (((alu(4,5,iau(k)) - l41*d1) - l42*d2) - l43*d3)*l44

        alu(5,5,iau(k)) = ((((alu(5,5,iau(k)) - l51*d1) - l52*d2) - l53*d3)    &
                                              - l54*d4)*l55
        alu(4,5,iau(k)) = d4 - u45*alu(5,5,iau(k))
        alu(3,5,iau(k)) = d3 - u34*alu(4,5,iau(k)) - u35*alu(5,5,iau(k))
        alu(2,5,iau(k)) = d2 - u23*alu(3,5,iau(k)) - u24*alu(4,5,iau(k))       &
                             - u25*alu(5,5,iau(k))
        alu(1,5,iau(k)) = d1 - u12*alu(2,5,iau(k)) - u13*alu(3,5,iau(k))       &
                             - u14*alu(4,5,iau(k)) - u15*alu(5,5,iau(k))

      case(2)

        rhs(:,:) = eye(:,:)
        call householder_inverse(alu(:,:,iau(k)),rhs,5,5)
        alu(:,:,iau(k)) = rhs(:,:)

      case default

        write(*,*) 'Unknown value of matrix_inverse: ', matrix_inverse
        call lmpi_die
        stop

      end select

!----------------------- zero all entries of iw.

      do i = j1, j2
        iw(ja(i)) = 0
      end do
    end do

    icode = 0
    return

600 icode = k
    write(*,*)' zero pvt ',icode

  end subroutine block5_ilu0_decompose


!=========================== HOUSEHOLDER_INVERSE =============================80
!
!  Householder inverse of a matrix
!
!=============================================================================80
  subroutine householder_inverse(a,rh,n,m)

    use kinddefs, only : dp

    integer, intent(in) :: n, m

    real(dp), dimension(n,n), intent(inout) :: a
    real(dp), dimension(n,m), intent(inout) :: rh

    integer :: i,j,k,p

    real(dp) :: t1, t2, t3

    real(dp), dimension(n) :: d

  continue

! First row of the inverse matrix, finds HH factorization of matrix A

    do k = 1, n-1

      t1     = sqrt(dot_product(a(k:n,k),a(k:n,k)))
      d(k)   =  -sign(t1,a(k,k))
      a(k,k) = a(k,k) - d(k)
      t2     = 1.0_dp / abs(a(k,k)*t1)

! modify k+1, k+2, ..,n col of A  using u (i.e.,the kth col of A)

      do j = k+1, n
        t3 = dot_product(a(k:n,k),a(k:n,j)) * t2
        do i = k, n
          a(i,j)=a(i,j) - a(i,k) * t3
        enddo
      enddo

! do same for RH

      t3 = dot_product(a(k:n,k),rh(k:n,1)) * t2
      do i = k, n
        rh(i,1)=rh(i,1) - a(i,k) * t3
      enddo

    enddo

    d(n)=a(n,n)

! do back substitution: first row

    do j = n, 2, -1
      rh(j,1)=rh(j,1)/d(j)
      do k = 1, j-1
        rh(k,1)=rh(k,1)-a(k,j)*rh(j,1)
      enddo
    enddo

    rh(1,1)=rh(1,1)/d(1)

    if(m > 1) then

      do p = 2, m

        do k = 1, n-1
          t2 =-a(k,k)*d(k)
          t3 = dot_product(a(k:n,k),rh(k:n,p)) / t2
          do i = k, n
            rh(i,p)=rh(i,p)-a(i,k)* t3
          enddo
        enddo

! do back substitution: rest

        do j = n, 2, -1
          rh(j,p)=rh(j,p)/d(j)
          do k = 1, j-1
            rh(k,p)=rh(k,p)-a(k,j)*rh(j,p)
          enddo
        enddo

        rh(1,p)=rh(1,p)/d(1)

      enddo

    endif

  end subroutine householder_inverse


!============================== BLOCK5_ILU0_SOLVE ============================80
!
!   Block 5x5 LU solve for ILU(0) preconditioning
!
!=============================================================================80
  subroutine block5_ilu0_solve(n,x,y,alu,jalu,ial,iau,local_cutoff)

    use kinddefs,    only : dp
    use lmpi,        only : lmpi_nproc, lmpi_send, lmpi_recv, lmpi_id,         &
                            lmpi_die, lmpi_success
    use chaos_datas, only : timestep, ntimesteps, overlap

    integer, intent(in) :: n, local_cutoff

    integer, dimension(:), intent(in) :: jalu
    integer, dimension(:), intent(in) :: ial
    integer, dimension(:), intent(in) :: iau

    real(dp), dimension(:,:,:),          intent(in)    :: alu
    real(dp), dimension(5,local_cutoff), intent(inout) :: x
    real(dp), dimension(5,local_cutoff), intent(in)    :: y

    integer :: i,m1,m2,k,jcol,ierr,istart,iend

    real(dp), dimension(5) :: t

    real(dp), dimension(5,n) :: xlocal, ylocal
    real(dp), dimension(5,local_cutoff) :: tempsend, temprecv

  continue

! First insert the input data into the appropriate segment of the larger array

    if ( overlap ) then
      if ( timestep == 1 ) then
        ylocal(1:5,1:local_cutoff) = y(1:5,1:local_cutoff)
      else
        istart = local_cutoff+1
        iend   = 2*local_cutoff
        ylocal(1:5,istart:iend) = y(1:5,1:local_cutoff)
      endif
    else
      ylocal(1:5,1:local_cutoff) = y(1:5,1:local_cutoff)
    endif

! Transfer my portion of the y vector to the processors above and below me
! if they exist

    xfer_data : if ( n > local_cutoff .and. lmpi_nproc > 1 ) then

! First let's send data backwards in time, i.e., to the rank "below" us.
! This will become y_fwd on the receiving ranks. The "special" cases here are:
!   1) the last rank will not receive anything
!   2) the first rank will not send anything

      if ( timestep /= ntimesteps ) then

        call lmpi_recv(temprecv,local_cutoff*5,lmpi_id+1,1,ierr)

        if ( ierr /= lmpi_success ) then
          write(*,*) 'Error in lmpi_recv() for ilu backward comm.', lmpi_id
          call lmpi_die
          stop
        endif

        if ( timestep == 1 ) then
          istart = local_cutoff+1
          iend   = 2*local_cutoff
          ylocal(1:5,istart:iend) = temprecv(1:5,1:local_cutoff)
        else
          istart = 2*local_cutoff+1
          iend   = 3*local_cutoff
          ylocal(1:5,istart:iend) = temprecv(1:5,1:local_cutoff)
        endif

      endif

      if ( timestep /= 1 ) then

        tempsend(1:5,1:local_cutoff) = y(1:5,1:local_cutoff)

        call lmpi_send(tempsend,local_cutoff*5,lmpi_id-1,1,ierr)

        if ( ierr /= lmpi_success ) then
          write(*,*) 'Error in lmpi_send() for ilu backward comm.', lmpi_id
          call lmpi_die
          stop
        endif

      endif

! Now let's send data forwards in time, i.e., to the rank "above" us.
! This will become y_back on the receiving ranks. The "special" cases here are:
!   1) the first rank will not receive anything
!   2) the last rank will not send anything

      if ( timestep /= 1 ) then

        call lmpi_recv(temprecv,local_cutoff*5,lmpi_id-1,1,ierr)

        if ( ierr /= lmpi_success ) then
          write(*,*) 'Error in lmpi_recv() for ilu forward comm.', lmpi_id
          call lmpi_die
          stop
        endif

        ylocal(1:5,1:local_cutoff) = temprecv(1:5,1:local_cutoff)

      endif

      if ( timestep /= ntimesteps ) then

        tempsend(1:5,1:local_cutoff) = y(1:5,1:local_cutoff)

        call lmpi_send(tempsend,local_cutoff*5,lmpi_id+1,1,ierr)

        if ( ierr /= lmpi_success ) then
          write(*,*) 'Error in lmpi_send() for ilu forward comm.', lmpi_id
          call lmpi_die
          stop
        endif

      endif

    endif xfer_data

! Now we can actually do the solve

    do i = 1, n

      m1 = ial(i)
      m2 = iau(i) -1

      xlocal(1,i) = ylocal(1,i)
      xlocal(2,i) = ylocal(2,i)
      xlocal(3,i) = ylocal(3,i)
      xlocal(4,i) = ylocal(4,i)
      xlocal(5,i) = ylocal(5,i)

      do k=m1, m2
        jcol = jalu(k)
        xlocal(1,i) = xlocal(1,i) - alu(1,1,k)*xlocal(1,jcol)                  &
                                  - alu(1,2,k)*xlocal(2,jcol)                  &
                                  - alu(1,3,k)*xlocal(3,jcol)                  &
                                  - alu(1,4,k)*xlocal(4,jcol)                  &
                                  - alu(1,5,k)*xlocal(5,jcol)
        xlocal(2,i) = xlocal(2,i) - alu(2,1,k)*xlocal(1,jcol)                  &
                                  - alu(2,2,k)*xlocal(2,jcol)                  &
                                  - alu(2,3,k)*xlocal(3,jcol)                  &
                                  - alu(2,4,k)*xlocal(4,jcol)                  &
                                  - alu(2,5,k)*xlocal(5,jcol)
        xlocal(3,i) = xlocal(3,i) - alu(3,1,k)*xlocal(1,jcol)                  &
                                  - alu(3,2,k)*xlocal(2,jcol)                  &
                                  - alu(3,3,k)*xlocal(3,jcol)                  &
                                  - alu(3,4,k)*xlocal(4,jcol)                  &
                                  - alu(3,5,k)*xlocal(5,jcol)
        xlocal(4,i) = xlocal(4,i) - alu(4,1,k)*xlocal(1,jcol)                  &
                                  - alu(4,2,k)*xlocal(2,jcol)                  &
                                  - alu(4,3,k)*xlocal(3,jcol)                  &
                                  - alu(4,4,k)*xlocal(4,jcol)                  &
                                  - alu(4,5,k)*xlocal(5,jcol)
        xlocal(5,i) = xlocal(5,i) - alu(5,1,k)*xlocal(1,jcol)                  &
                                  - alu(5,2,k)*xlocal(2,jcol)                  &
                                  - alu(5,3,k)*xlocal(3,jcol)                  &
                                  - alu(5,4,k)*xlocal(4,jcol)                  &
                                  - alu(5,5,k)*xlocal(5,jcol)
      enddo
    enddo

    do i = n, 1, -1
      m1 = iau(i) + 1
      m2 = ial(i+1) - 1
      do k=m1, m2
          jcol = jalu(k)
          xlocal(1,i) = xlocal(1,i) - alu(1,1,k)*xlocal(1,jcol)                &
                                    - alu(1,2,k)*xlocal(2,jcol)                &
                                    - alu(1,3,k)*xlocal(3,jcol)                &
                                    - alu(1,4,k)*xlocal(4,jcol)                &
                                    - alu(1,5,k)*xlocal(5,jcol)
          xlocal(2,i) = xlocal(2,i) - alu(2,1,k)*xlocal(1,jcol)                &
                                    - alu(2,2,k)*xlocal(2,jcol)                &
                                    - alu(2,3,k)*xlocal(3,jcol)                &
                                    - alu(2,4,k)*xlocal(4,jcol)                &
                                    - alu(2,5,k)*xlocal(5,jcol)
          xlocal(3,i) = xlocal(3,i) - alu(3,1,k)*xlocal(1,jcol)                &
                                    - alu(3,2,k)*xlocal(2,jcol)                &
                                    - alu(3,3,k)*xlocal(3,jcol)                &
                                    - alu(3,4,k)*xlocal(4,jcol)                &
                                    - alu(3,5,k)*xlocal(5,jcol)
          xlocal(4,i) = xlocal(4,i) - alu(4,1,k)*xlocal(1,jcol)                &
                                    - alu(4,2,k)*xlocal(2,jcol)                &
                                    - alu(4,3,k)*xlocal(3,jcol)                &
                                    - alu(4,4,k)*xlocal(4,jcol)                &
                                    - alu(4,5,k)*xlocal(5,jcol)
          xlocal(5,i) = xlocal(5,i) - alu(5,1,k)*xlocal(1,jcol)                &
                                    - alu(5,2,k)*xlocal(2,jcol)                &
                                    - alu(5,3,k)*xlocal(3,jcol)                &
                                    - alu(5,4,k)*xlocal(4,jcol)                &
                                    - alu(5,5,k)*xlocal(5,jcol)
      enddo

      t(1) = alu(1,1,iau(i))*xlocal(1,i)                                       &
           + alu(1,2,iau(i))*xlocal(2,i)                                       &
           + alu(1,3,iau(i))*xlocal(3,i)                                       &
           + alu(1,4,iau(i))*xlocal(4,i)                                       &
           + alu(1,5,iau(i))*xlocal(5,i)
      t(2) = alu(2,1,iau(i))*xlocal(1,i)                                       &
           + alu(2,2,iau(i))*xlocal(2,i)                                       &
           + alu(2,3,iau(i))*xlocal(3,i)                                       &
           + alu(2,4,iau(i))*xlocal(4,i)                                       &
           + alu(2,5,iau(i))*xlocal(5,i)
      t(3) = alu(3,1,iau(i))*xlocal(1,i)                                       &
           + alu(3,2,iau(i))*xlocal(2,i)                                       &
           + alu(3,3,iau(i))*xlocal(3,i)                                       &
           + alu(3,4,iau(i))*xlocal(4,i)                                       &
           + alu(3,5,iau(i))*xlocal(5,i)
      t(4) = alu(4,1,iau(i))*xlocal(1,i)                                       &
           + alu(4,2,iau(i))*xlocal(2,i)                                       &
           + alu(4,3,iau(i))*xlocal(3,i)                                       &
           + alu(4,4,iau(i))*xlocal(4,i)                                       &
           + alu(4,5,iau(i))*xlocal(5,i)
      t(5) = alu(5,1,iau(i))*xlocal(1,i)                                       &
           + alu(5,2,iau(i))*xlocal(2,i)                                       &
           + alu(5,3,iau(i))*xlocal(3,i)                                       &
           + alu(5,4,iau(i))*xlocal(4,i)                                       &
           + alu(5,5,iau(i))*xlocal(5,i)

      xlocal(1,i) = t(1)
      xlocal(2,i) = t(2)
      xlocal(3,i) = t(3)
      xlocal(4,i) = t(4)
      xlocal(5,i) = t(5)

    enddo

    if ( overlap ) then
      if ( timestep == 1 ) then
        x(1:5,1:local_cutoff) = xlocal(1:5,1:local_cutoff)
      else
        istart = local_cutoff+1
        iend   = 2*local_cutoff
        x(1:5,1:local_cutoff) = xlocal(1:5,istart:iend)
      endif
    else
      x(1:5,1:local_cutoff) = xlocal(1:5,1:local_cutoff)
    endif

  end subroutine block5_ilu0_solve

end module chaos_brute_force

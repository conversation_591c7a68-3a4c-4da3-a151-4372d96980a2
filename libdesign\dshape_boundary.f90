module dshape_boundary

  implicit none

  private

  public :: dboundary_metrics

contains

!============================== DBOUNDARY_METRICS ============================80
!
! This routine computes the linearizations of bxn/byn/bzn/bfacespeed wrt the
! grid coordinates
!
!=============================================================================80
  subroutine dboundary_metrics(nnodes01,nbnode,nbfacet,f2ntb,ibnode,x,y,z,     &
                               bdxdt,bdydt,bdzdt,nnodes0,bxn,byn,bzn,          &
                               bfacespeed,ntp,nbfaceq,f2nqb)

    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use info_depr,            only : physical_timestep
    use nml_nonlinear_solves, only : ibdf2opt, itime, dt
    use nml_global,           only : moving_grid
    use kinddefs,             only : dp
    use fun3d_constants,      only : my_0, my_half, my_1, my_1p5, my_2, my_3,  &
                                     my_6, my_11, my_4
    use dshape_types,         only : node_stencil

    integer, intent(in) :: nbnode,nbfacet,nnodes0,nnodes01,ntp,nbfaceq

    integer, dimension(nbnode),    intent(in) :: ibnode
    integer, dimension(nbfacet,5), intent(in) :: f2ntb
    integer, dimension(nbfaceq,6), intent(in) :: f2nqb

    real(dp), dimension(nnodes01), intent(in) :: x,y,z
    real(dp), dimension(nbnode), intent(in) :: bdxdt,bdydt,bdzdt
    real(dp), dimension(nbnode), intent(in) :: bxn,byn,bzn,bfacespeed

    integer :: i,iface,n1,n2,n3,n4,face,node1,node2,node3,cnode1,cnode2,cnode3
    integer :: nn1,nn2,nn3,ioff1,ioff2,ioff3,inode,j,k,nn,node4,cnode4,nn4,ioff4

    real(dp) :: x0, y0, z0, dxdt0, dydt0, dzdt0
    real(dp) :: dxdtc, dydtc, dzdtc
    real(dp) :: xr, yr, zr, dxdtr, dydtr, dzdtr
    real(dp) :: xl, yl, zl, dxdtl, dydtl, dzdtl
    real(dp) :: dxdtavg, dydtavg, dzdtavg
    real(dp) :: xorig1,yorig1,zorig1
    real(dp) :: xorig2,yorig2,zorig2
    real(dp) :: xorig3,yorig3,zorig3
    real(dp) :: xorig4,yorig4,zorig4
    real(dp) :: xorig,yorig,zorig
    real(dp) :: xc_ref,yc_ref,zc_ref,piece1,piece2
    real(dp) :: xorig1x1,xorig1x2,xorig1x3,xorig1x4
    real(dp) :: yorig1y1,yorig1y2,yorig1y3,yorig1y4
    real(dp) :: zorig1z1,zorig1z2,zorig1z3,zorig1z4
    real(dp) :: xorig2x1,xorig2x2,xorig2x3,xorig2x4
    real(dp) :: yorig2y1,yorig2y2,yorig2y3,yorig2y4
    real(dp) :: zorig2z1,zorig2z2,zorig2z3,zorig2z4
    real(dp) :: xorig3x1,xorig3x2,xorig3x3,xorig3x4
    real(dp) :: yorig3y1,yorig3y2,yorig3y3,yorig3y4
    real(dp) :: zorig3z1,zorig3z2,zorig3z3,zorig3z4
    real(dp) :: xorig4x1,xorig4x2,xorig4x3,xorig4x4
    real(dp) :: yorig4y1,yorig4y2,yorig4y3,yorig4y4
    real(dp) :: zorig4z1,zorig4z2,zorig4z3,zorig4z4
    real(dp) :: xorigx1,xorigx2,xorigx3,xorigx4
    real(dp) :: yorigy1,yorigy2,yorigy3,yorigy4
    real(dp) :: zorigz1,zorigz2,zorigz3,zorigz4
    real(dp) :: xc_refx1,xc_refx2,xc_refx3,xc_refx4
    real(dp) :: yc_refy1,yc_refy2,yc_refy3,yc_refy4
    real(dp) :: zc_refz1,zc_refz2,zc_refz3,zc_refz4
    real(dp) :: x0x1,x0x2,x0x3,y0y1,y0y2,y0y3,z0z1,z0z2,z0z3
    real(dp) :: x1x1,x1x2,x1x3,x2x1,x2x2,x2x3,x3x1,x3x2,x3x3
    real(dp) :: y1y1,y1y2,y1y3,y2y1,y2y2,y2y3,y3y1,y3y2,y3y3
    real(dp) :: z1z1,z1z2,z1z3,z2z1,z2z2,z2z3,z3z1,z3z2,z3z3
    real(dp) :: x4x1,x4x2,x4x3,y4y1,y4y2,y4y3
    real(dp) :: z4z1,z4z2,z4z3
    real(dp) :: xlx1,xlx2,xlx3,yly1,yly2,yly3,zlz1,zlz2,zlz3
    real(dp) :: xrx1,xrx2,xrx3,yry1,yry2,yry3,zrz1,zrz2,zrz3
    real(dp) :: x0x4,y0y4,z0z4
    real(dp) :: x1x4,y1y4,z1z4
    real(dp) :: x2x4,y2y4,z2z4
    real(dp) :: x4x4,y4y4,z4z4
    real(dp) :: xlx4,yly4,zlz4
    real(dp) :: xrx4,yry4,zrz4
    real(dp) :: areaxx1,areaxx2,areaxx3,areaxx4
    real(dp) :: areaxy1,areaxy2,areaxy3,areaxy4
    real(dp) :: areaxz1,areaxz2,areaxz3,areaxz4
    real(dp) :: areayx1,areayx2,areayx3,areayx4
    real(dp) :: areayy1,areayy2,areayy3,areayy4
    real(dp) :: areayz1,areayz2,areayz3,areayz4
    real(dp) :: areazx1,areazx2,areazx3,areazx4
    real(dp) :: areazy1,areazy2,areazy3,areazy4
    real(dp) :: areazz1,areazz2,areazz3,areazz4
    real(dp) :: bxnx1,bxnx2,bxnx3,bxny1,bxny2,bxny3,bxnz1,bxnz2,bxnz3
    real(dp) :: bynx1,bynx2,bynx3,byny1,byny2,byny3,bynz1,bynz2,bynz3
    real(dp) :: bznx1,bznx2,bznx3,bzny1,bzny2,bzny3,bznz1,bznz2,bznz3
    real(dp) :: bxnx4,bxny4,bxnz4
    real(dp) :: bynx4,byny4,bynz4
    real(dp) :: bznx4,bzny4,bznz4
    real(dp) :: areax, areay, areaz
    real(dp) :: x1,y1,z1,x2,y2,z2,x3,y3,z3,x4,y4,z4
    real(dp) :: xn_node,yn_node,zn_node,area
    real(dp) :: dxndx,dxndy,dxndz,dyndx,dyndy,dyndz,dzndx,dzndy,dzndz
    real(dp) :: dareadx,daready,dareadz

    real(dp), dimension(4)   :: xc, yc, zc
    real(dp), dimension(4)   :: xcx1,xcx2,xcx3,ycy1,ycy2,ycy3,zcz1,zcz2,zcz3
    real(dp), dimension(4)   :: xcx4,ycy4,zcz4
    real(dp), dimension(ntp) :: time_coeff
    real(dp), dimension(ntp) :: dxdtcx1,dxdtcx2,dxdtcx3,dxdtcx4
    real(dp), dimension(ntp) :: dydtcy1,dydtcy2,dydtcy3,dydtcy4
    real(dp), dimension(ntp) :: dzdtcz1,dzdtcz2,dzdtcz3,dzdtcz4
    real(dp), dimension(ntp) :: bfacespeedx1,bfacespeedx2,bfacespeedx3
    real(dp), dimension(ntp) :: bfacespeedy1,bfacespeedy2,bfacespeedy3
    real(dp), dimension(ntp) :: bfacespeedz1,bfacespeedz2,bfacespeedz3
    real(dp), dimension(ntp) :: bfacespeedx4,bfacespeedy4,bfacespeedz4
    real(dp), dimension(ntp) :: dxdt0x1,dxdt0x2,dxdt0x3,dxdt0x4
    real(dp), dimension(ntp) :: dydt0y1,dydt0y2,dydt0y3,dydt0y4
    real(dp), dimension(ntp) :: dzdt0z1,dzdt0z2,dzdt0z3,dzdt0z4
    real(dp), dimension(ntp) :: piece1x1,piece1x2,piece1x3,piece1x4
    real(dp), dimension(ntp) :: piece1y1,piece1y2,piece1y3,piece1y4
    real(dp), dimension(ntp) :: piece1z1,piece1z2,piece1z3,piece1z4
    real(dp), dimension(ntp) :: piece2x1,piece2x2,piece2x3,piece2x4
    real(dp), dimension(ntp) :: piece2y1,piece2y2,piece2y3,piece2y4
    real(dp), dimension(ntp) :: piece2z1,piece2z2,piece2z3,piece2z4
    real(dp), dimension(ntp) :: dxdtlx1,dxdtlx2,dxdtlx3,dxdtlx4
    real(dp), dimension(ntp) :: dydtly1,dydtly2,dydtly3,dydtly4
    real(dp), dimension(ntp) :: dzdtlz1,dzdtlz2,dzdtlz3,dzdtlz4
    real(dp), dimension(ntp) :: dxdtrx1,dxdtrx2,dxdtrx3,dxdtrx4
    real(dp), dimension(ntp) :: dydtry1,dydtry2,dydtry3,dydtry4
    real(dp), dimension(ntp) :: dzdtrz1,dzdtrz2,dzdtrz3,dzdtrz4
    real(dp), dimension(ntp) :: dxdtavgx1,dxdtavgx2,dxdtavgx3,dxdtavgx4
    real(dp), dimension(ntp) :: dxdtavgy1,dxdtavgy2,dxdtavgy3,dxdtavgy4
    real(dp), dimension(ntp) :: dxdtavgz1,dxdtavgz2,dxdtavgz3,dxdtavgz4
    real(dp), dimension(ntp) :: dydtavgx1,dydtavgx2,dydtavgx3,dydtavgx4
    real(dp), dimension(ntp) :: dydtavgy1,dydtavgy2,dydtavgy3,dydtavgy4
    real(dp), dimension(ntp) :: dydtavgz1,dydtavgz2,dydtavgz3,dydtavgz4
    real(dp), dimension(ntp) :: dzdtavgx1,dzdtavgx2,dzdtavgx3,dzdtavgx4
    real(dp), dimension(ntp) :: dzdtavgy1,dzdtavgy2,dzdtavgy3,dzdtavgy4
    real(dp), dimension(ntp) :: dzdtavgz1,dzdtavgz2,dzdtavgz3,dzdtavgz4
    real(dp), dimension(ntp) :: termx1,termx2,termx3,termx4
    real(dp), dimension(ntp) :: termy1,termy2,termy3,termy4
    real(dp), dimension(ntp) :: termz1,termz2,termz3,termz4
    real(dp), dimension(ntp) :: dwdx,dwdy,dwdz

    logical :: n1_found,n2_found,n3_found,n4_found

  continue

! avoid used uninitialized warnings

  x1x1=my_0; x1x2=my_0; x1x3=my_0; x1x4=my_0
  x2x1=my_0; x2x2=my_0; x2x3=my_0; x2x4=my_0
  x3x1=my_0; x3x2=my_0; x3x3=my_0
  x4x1=my_0; x4x2=my_0; x4x3=my_0; x4x4=my_0
  y1y1=my_0; y1y2=my_0; y1y3=my_0; y1y4=my_0
  y2y1=my_0; y2y2=my_0; y2y3=my_0; y2y4=my_0
  y3y1=my_0; y3y2=my_0; y3y3=my_0
  y4y1=my_0; y4y2=my_0; y4y3=my_0; y4y4=my_0
  z1z1=my_0; z1z2=my_0; z1z3=my_0; z1z4=my_0
  z2z1=my_0; z2z2=my_0; z2z3=my_0; z2z4=my_0
  z3z1=my_0; z3z2=my_0; z3z3=my_0
  z4z1=my_0; z4z2=my_0; z4z3=my_0; z4z4=my_0

  xorig=my_0; xorigx1=my_0; xorigx2=my_0; xorigx3=my_0; xorigx4=my_0
  yorig=my_0; yorigy1=my_0; yorigy2=my_0; yorigy3=my_0; yorigy4=my_0
  zorig=my_0; zorigz1=my_0; zorigz2=my_0; zorigz3=my_0; zorigz4=my_0

  xc_ref=my_0; xc_refx1=my_0; xc_refx2=my_0; xc_refx3=my_0; xc_refx4=my_0
  yc_ref=my_0; yc_refy1=my_0; yc_refy2=my_0; yc_refy3=my_0; yc_refy4=my_0
  zc_ref=my_0; zc_refz1=my_0; zc_refz2=my_0; zc_refz3=my_0; zc_refz4=my_0

! First we need to establish the stencil pattern of each boundary node
! Ie, how many nodes contribute to the normals/speed at each node

    allocate(node_stencil(nbnode))

! Assume each node has at most 20 contributors
! We'll reallocate on the fly if needed

    do i = 1, nbnode
      node_stencil(i)%n = 0
      call my_alloc_ptr(node_stencil(i)%nodes, 20)
    end do

    face_loop : do iface = 1, nbfacet
      n1 = f2ntb(iface,1)
      n2 = f2ntb(iface,2)
      n3 = f2ntb(iface,3)

! First put each node in its own list if it's not there already

      n1_found = .false.
      find_n1 : do i = 1, node_stencil(n1)%n
        if ( node_stencil(n1)%nodes(i) == n1 ) then
          n1_found = .true.
          exit find_n1
        endif
      end do find_n1
      if ( .not. n1_found ) then
        node_stencil(n1)%n = node_stencil(n1)%n + 1
        if ( node_stencil(n1)%n > size(node_stencil(n1)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n1)%nodes,node_stencil(n1)%n)
        endif
        node_stencil(n1)%nodes(node_stencil(n1)%n) = n1
      endif

      n2_found = .false.
      find_n2 : do i = 1, node_stencil(n2)%n
        if ( node_stencil(n2)%nodes(i) == n2 ) then
          n2_found = .true.
          exit find_n2
        endif
      end do find_n2
      if ( .not. n2_found ) then
        node_stencil(n2)%n = node_stencil(n2)%n + 1
        if ( node_stencil(n2)%n > size(node_stencil(n2)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n2)%nodes,node_stencil(n2)%n)
        endif
        node_stencil(n2)%nodes(node_stencil(n2)%n) = n2
      endif

      n3_found = .false.
      find_n3 : do i = 1, node_stencil(n3)%n
        if ( node_stencil(n3)%nodes(i) == n3 ) then
          n3_found = .true.
          exit find_n3
        endif
      end do find_n3
      if ( .not. n3_found ) then
        node_stencil(n3)%n = node_stencil(n3)%n + 1
        if ( node_stencil(n3)%n > size(node_stencil(n3)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n3)%nodes,node_stencil(n3)%n)
        endif
        node_stencil(n3)%nodes(node_stencil(n3)%n) = n3
      endif

! Check to see if n1 is already in n2 list; if not, add it

      n1_found = .false.
      find_n1a : do i = 1, node_stencil(n2)%n
        if ( node_stencil(n2)%nodes(i) == n1 ) then
          n1_found = .true.
          exit find_n1a
        endif
      end do find_n1a
      if ( .not. n1_found ) then
        node_stencil(n2)%n = node_stencil(n2)%n + 1
        if ( node_stencil(n2)%n > size(node_stencil(n2)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n2)%nodes,node_stencil(n2)%n)
        endif
        node_stencil(n2)%nodes(node_stencil(n2)%n) = n1
      endif

! Check to see if n1 is already in n3 list; if not, add it

      n1_found = .false.
      find_n1b : do i = 1, node_stencil(n3)%n
        if ( node_stencil(n3)%nodes(i) == n1 ) then
          n1_found = .true.
          exit find_n1b
        endif
      end do find_n1b
      if ( .not. n1_found ) then
        node_stencil(n3)%n = node_stencil(n3)%n + 1
        if ( node_stencil(n3)%n > size(node_stencil(n3)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n3)%nodes,node_stencil(n3)%n)
        endif
        node_stencil(n3)%nodes(node_stencil(n3)%n) = n1
      endif

! Check to see if n2 is already in n1 list; if not, add it

      n2_found = .false.
      find_n2a : do i = 1, node_stencil(n1)%n
        if ( node_stencil(n1)%nodes(i) == n2 ) then
          n2_found = .true.
          exit find_n2a
        endif
      end do find_n2a
      if ( .not. n2_found ) then
        node_stencil(n1)%n = node_stencil(n1)%n + 1
        if ( node_stencil(n1)%n > size(node_stencil(n1)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n1)%nodes,node_stencil(n1)%n)
        endif
        node_stencil(n1)%nodes(node_stencil(n1)%n) = n2
      endif

! Check to see if n2 is already in n3 list; if not, add it

      n2_found = .false.
      find_n2b : do i = 1, node_stencil(n3)%n
        if ( node_stencil(n3)%nodes(i) == n2 ) then
          n2_found = .true.
          exit find_n2b
        endif
      end do find_n2b
      if ( .not. n2_found ) then
        node_stencil(n3)%n = node_stencil(n3)%n + 1
        if ( node_stencil(n3)%n > size(node_stencil(n3)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n3)%nodes,node_stencil(n3)%n)
        endif
        node_stencil(n3)%nodes(node_stencil(n3)%n) = n2
      endif

! Check to see if n3 is already in n1 list; if not, add it

      n3_found = .false.
      find_n3a : do i = 1, node_stencil(n1)%n
        if ( node_stencil(n1)%nodes(i) == n3 ) then
          n3_found = .true.
          exit find_n3a
        endif
      end do find_n3a
      if ( .not. n3_found ) then
        node_stencil(n1)%n = node_stencil(n1)%n + 1
        if ( node_stencil(n1)%n > size(node_stencil(n1)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n1)%nodes,node_stencil(n1)%n)
        endif
        node_stencil(n1)%nodes(node_stencil(n1)%n) = n3
      endif

! Check to see if n3 is already in n2 list; if not, add it

      n3_found = .false.
      find_n3b : do i = 1, node_stencil(n2)%n
        if ( node_stencil(n2)%nodes(i) == n3 ) then
          n3_found = .true.
          exit find_n3b
        endif
      end do find_n3b
      if ( .not. n3_found ) then
        node_stencil(n2)%n = node_stencil(n2)%n + 1
        if ( node_stencil(n2)%n > size(node_stencil(n2)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n2)%nodes,node_stencil(n2)%n)
        endif
        node_stencil(n2)%nodes(node_stencil(n2)%n) = n3
      endif

    end do face_loop

! Next do the quad faces (I ought to write a general routine for this
! but I am too lazy)

    qface_loop : do iface = 1, nbfaceq
      n1 = f2nqb(iface,1)
      n2 = f2nqb(iface,2)
      n3 = f2nqb(iface,3)
      n4 = f2nqb(iface,4)

! First put each node in its own list if it's not there already

      n1_found = .false.
      find_n1q : do i = 1, node_stencil(n1)%n
        if ( node_stencil(n1)%nodes(i) == n1 ) then
          n1_found = .true.
          exit find_n1q
        endif
      end do find_n1q
      if ( .not. n1_found ) then
        node_stencil(n1)%n = node_stencil(n1)%n + 1
        if ( node_stencil(n1)%n > size(node_stencil(n1)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n1)%nodes,node_stencil(n1)%n)
        endif
        node_stencil(n1)%nodes(node_stencil(n1)%n) = n1
      endif

      n2_found = .false.
      find_n2q : do i = 1, node_stencil(n2)%n
        if ( node_stencil(n2)%nodes(i) == n2 ) then
          n2_found = .true.
          exit find_n2q
        endif
      end do find_n2q
      if ( .not. n2_found ) then
        node_stencil(n2)%n = node_stencil(n2)%n + 1
        if ( node_stencil(n2)%n > size(node_stencil(n2)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n2)%nodes,node_stencil(n2)%n)
        endif
        node_stencil(n2)%nodes(node_stencil(n2)%n) = n2
      endif

      n3_found = .false.
      find_n3q : do i = 1, node_stencil(n3)%n
        if ( node_stencil(n3)%nodes(i) == n3 ) then
          n3_found = .true.
          exit find_n3q
        endif
      end do find_n3q
      if ( .not. n3_found ) then
        node_stencil(n3)%n = node_stencil(n3)%n + 1
        if ( node_stencil(n3)%n > size(node_stencil(n3)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n3)%nodes,node_stencil(n3)%n)
        endif
        node_stencil(n3)%nodes(node_stencil(n3)%n) = n3
      endif

      n4_found = .false.
      find_n4q : do i = 1, node_stencil(n4)%n
        if ( node_stencil(n4)%nodes(i) == n4 ) then
          n4_found = .true.
          exit find_n4q
        endif
      end do find_n4q
      if ( .not. n4_found ) then
        node_stencil(n4)%n = node_stencil(n4)%n + 1
        if ( node_stencil(n4)%n > size(node_stencil(n4)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n4)%nodes,node_stencil(n4)%n)
        endif
        node_stencil(n4)%nodes(node_stencil(n4)%n) = n4
      endif

! Check to see if n1 is already in n2 list; if not, add it

      n1_found = .false.
      find_n12 : do i = 1, node_stencil(n2)%n
        if ( node_stencil(n2)%nodes(i) == n1 ) then
          n1_found = .true.
          exit find_n12
        endif
      end do find_n12
      if ( .not. n1_found ) then
        node_stencil(n2)%n = node_stencil(n2)%n + 1
        if ( node_stencil(n2)%n > size(node_stencil(n2)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n2)%nodes,node_stencil(n2)%n)
        endif
        node_stencil(n2)%nodes(node_stencil(n2)%n) = n1
      endif

! Check to see if n1 is already in n3 list; if not, add it

      n1_found = .false.
      find_n13 : do i = 1, node_stencil(n3)%n
        if ( node_stencil(n3)%nodes(i) == n1 ) then
          n1_found = .true.
          exit find_n13
        endif
      end do find_n13
      if ( .not. n1_found ) then
        node_stencil(n3)%n = node_stencil(n3)%n + 1
        if ( node_stencil(n3)%n > size(node_stencil(n3)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n3)%nodes,node_stencil(n3)%n)
        endif
        node_stencil(n3)%nodes(node_stencil(n3)%n) = n1
      endif

! Check to see if n1 is already in n4 list; if not, add it

      n1_found = .false.
      find_n14 : do i = 1, node_stencil(n4)%n
        if ( node_stencil(n4)%nodes(i) == n1 ) then
          n1_found = .true.
          exit find_n14
        endif
      end do find_n14
      if ( .not. n1_found ) then
        node_stencil(n4)%n = node_stencil(n4)%n + 1
        if ( node_stencil(n4)%n > size(node_stencil(n4)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n4)%nodes,node_stencil(n4)%n)
        endif
        node_stencil(n4)%nodes(node_stencil(n4)%n) = n1
      endif

! Check to see if n2 is already in n1 list; if not, add it

      n2_found = .false.
      find_n21 : do i = 1, node_stencil(n1)%n
        if ( node_stencil(n1)%nodes(i) == n2 ) then
          n2_found = .true.
          exit find_n21
        endif
      end do find_n21
      if ( .not. n2_found ) then
        node_stencil(n1)%n = node_stencil(n1)%n + 1
        if ( node_stencil(n1)%n > size(node_stencil(n1)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n1)%nodes,node_stencil(n1)%n)
        endif
        node_stencil(n1)%nodes(node_stencil(n1)%n) = n2
      endif

! Check to see if n2 is already in n3 list; if not, add it

      n2_found = .false.
      find_n23 : do i = 1, node_stencil(n3)%n
        if ( node_stencil(n3)%nodes(i) == n2 ) then
          n2_found = .true.
          exit find_n23
        endif
      end do find_n23
      if ( .not. n2_found ) then
        node_stencil(n3)%n = node_stencil(n3)%n + 1
        if ( node_stencil(n3)%n > size(node_stencil(n3)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n3)%nodes,node_stencil(n3)%n)
        endif
        node_stencil(n3)%nodes(node_stencil(n3)%n) = n2
      endif

! Check to see if n2 is already in n4 list; if not, add it

      n2_found = .false.
      find_n24 : do i = 1, node_stencil(n4)%n
        if ( node_stencil(n4)%nodes(i) == n2 ) then
          n2_found = .true.
          exit find_n24
        endif
      end do find_n24
      if ( .not. n2_found ) then
        node_stencil(n4)%n = node_stencil(n4)%n + 1
        if ( node_stencil(n4)%n > size(node_stencil(n4)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n4)%nodes,node_stencil(n4)%n)
        endif
        node_stencil(n4)%nodes(node_stencil(n4)%n) = n2
      endif

! Check to see if n3 is already in n1 list; if not, add it

      n3_found = .false.
      find_n31 : do i = 1, node_stencil(n1)%n
        if ( node_stencil(n1)%nodes(i) == n3 ) then
          n3_found = .true.
          exit find_n31
        endif
      end do find_n31
      if ( .not. n3_found ) then
        node_stencil(n1)%n = node_stencil(n1)%n + 1
        if ( node_stencil(n1)%n > size(node_stencil(n1)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n1)%nodes,node_stencil(n1)%n)
        endif
        node_stencil(n1)%nodes(node_stencil(n1)%n) = n3
      endif

! Check to see if n3 is already in n2 list; if not, add it

      n3_found = .false.
      find_n32 : do i = 1, node_stencil(n2)%n
        if ( node_stencil(n2)%nodes(i) == n3 ) then
          n3_found = .true.
          exit find_n32
        endif
      end do find_n32
      if ( .not. n3_found ) then
        node_stencil(n2)%n = node_stencil(n2)%n + 1
        if ( node_stencil(n2)%n > size(node_stencil(n2)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n2)%nodes,node_stencil(n2)%n)
        endif
        node_stencil(n2)%nodes(node_stencil(n2)%n) = n3
      endif

! Check to see if n3 is already in n4 list; if not, add it

      n3_found = .false.
      find_n34 : do i = 1, node_stencil(n4)%n
        if ( node_stencil(n4)%nodes(i) == n3 ) then
          n3_found = .true.
          exit find_n34
        endif
      end do find_n34
      if ( .not. n3_found ) then
        node_stencil(n4)%n = node_stencil(n4)%n + 1
        if ( node_stencil(n4)%n > size(node_stencil(n4)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n4)%nodes,node_stencil(n4)%n)
        endif
        node_stencil(n4)%nodes(node_stencil(n4)%n) = n3
      endif

! Check to see if n4 is already in n1 list; if not, add it

      n4_found = .false.
      find_n41 : do i = 1, node_stencil(n1)%n
        if ( node_stencil(n1)%nodes(i) == n4 ) then
          n4_found = .true.
          exit find_n41
        endif
      end do find_n41
      if ( .not. n4_found ) then
        node_stencil(n1)%n = node_stencil(n1)%n + 1
        if ( node_stencil(n1)%n > size(node_stencil(n1)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n1)%nodes,node_stencil(n1)%n)
        endif
        node_stencil(n1)%nodes(node_stencil(n1)%n) = n4
      endif

! Check to see if n4 is already in n2 list; if not, add it

      n4_found = .false.
      find_n42 : do i = 1, node_stencil(n2)%n
        if ( node_stencil(n2)%nodes(i) == n4 ) then
          n4_found = .true.
          exit find_n42
        endif
      end do find_n42
      if ( .not. n4_found ) then
        node_stencil(n2)%n = node_stencil(n2)%n + 1
        if ( node_stencil(n2)%n > size(node_stencil(n2)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n2)%nodes,node_stencil(n2)%n)
        endif
        node_stencil(n2)%nodes(node_stencil(n2)%n) = n4
      endif

! Check to see if n4 is already in n3 list; if not, add it

      n4_found = .false.
      find_n43 : do i = 1, node_stencil(n3)%n
        if ( node_stencil(n3)%nodes(i) == n4 ) then
          n4_found = .true.
          exit find_n43
        endif
      end do find_n43
      if ( .not. n4_found ) then
        node_stencil(n3)%n = node_stencil(n3)%n + 1
        if ( node_stencil(n3)%n > size(node_stencil(n3)%nodes,1) ) then
          call my_realloc_ptr(node_stencil(n3)%nodes,node_stencil(n3)%n)
        endif
        node_stencil(n3)%nodes(node_stencil(n3)%n) = n4
      endif

    end do qface_loop

! Finally reallocate each node to fit snugly and allocate/zero the reals

    do i = 1, nbnode
      call my_realloc_ptr(node_stencil(i)%nodes,node_stencil(i)%n)

      call my_alloc_ptr(node_stencil(i)%dxndx,node_stencil(i)%n)
      call my_alloc_ptr(node_stencil(i)%dxndy,node_stencil(i)%n)
      call my_alloc_ptr(node_stencil(i)%dxndz,node_stencil(i)%n)

      call my_alloc_ptr(node_stencil(i)%dyndx,node_stencil(i)%n)
      call my_alloc_ptr(node_stencil(i)%dyndy,node_stencil(i)%n)
      call my_alloc_ptr(node_stencil(i)%dyndz,node_stencil(i)%n)

      call my_alloc_ptr(node_stencil(i)%dzndx,node_stencil(i)%n)
      call my_alloc_ptr(node_stencil(i)%dzndy,node_stencil(i)%n)
      call my_alloc_ptr(node_stencil(i)%dzndz,node_stencil(i)%n)

      call my_alloc_ptr(node_stencil(i)%dwdx,node_stencil(i)%n,ntp)
      call my_alloc_ptr(node_stencil(i)%dwdy,node_stencil(i)%n,ntp)
      call my_alloc_ptr(node_stencil(i)%dwdz,node_stencil(i)%n,ntp)

      node_stencil(i)%dxndx = 0.0_dp
      node_stencil(i)%dxndy = 0.0_dp
      node_stencil(i)%dxndz = 0.0_dp

      node_stencil(i)%dyndx = 0.0_dp
      node_stencil(i)%dyndy = 0.0_dp
      node_stencil(i)%dyndz = 0.0_dp

      node_stencil(i)%dzndx = 0.0_dp
      node_stencil(i)%dzndy = 0.0_dp
      node_stencil(i)%dzndz = 0.0_dp

      node_stencil(i)%dwdx = 0.0_dp
      node_stencil(i)%dwdy = 0.0_dp
      node_stencil(i)%dwdz = 0.0_dp
    end do

! Set the time-derivative coefficient that goes with the node speed terms

    time_coeff(:) = 0.0_dp

    if ( moving_grid ) then

      if ( itime == 0 ) then
        time_coeff(1) =  my_1/dt                              !dummy steadystate
      else if ( physical_timestep == 1 .or. itime == 1 ) then !BDF1
        time_coeff(1) =  my_1/dt
        time_coeff(2) = -my_1/dt
      else if ( physical_timestep == 2 .or. itime == 2 ) then !BDF2
        time_coeff(1) =  my_1p5/dt
        time_coeff(2) = -my_2/dt
        time_coeff(3) =  my_half/dt
      else if ( itime == 3 ) then
        if ( ibdf2opt == 0 ) then                             !BDF3
          time_coeff(1) =  my_11/my_6/dt
          time_coeff(2) = -my_3/dt
          time_coeff(3) =  my_1p5/dt
          time_coeff(4) = -my_1/my_3/dt
        else                                                  !BDF2opt
          time_coeff(1) =  (0.48_dp*my_11/my_6 + 0.52_dp*my_1p5)/dt
          time_coeff(2) = (-0.48_dp*my_3       - 0.52_dp*my_2)/dt
          time_coeff(3) =  (0.48_dp*my_1p5     + 0.52_dp*my_half)/dt
          time_coeff(4) = (-0.48_dp*my_2/my_6)/dt
        endif
      endif

    endif

! Now loop over the faces and linearize the normals and speeds wrt the grid

! First the triangles

      tria_face_loop : do face = 1, nbfacet

        n1 = f2ntb(face,1)
        n2 = f2ntb(face,2)
        n3 = f2ntb(face,3)

        node1 = ibnode(n1)
        node2 = ibnode(n2)
        node3 = ibnode(n3)

        cnode1 = node1
        cnode2 = node2
        cnode3 = node3

        xorig1 = x(node1)
          xorig1x1 = my_1
          xorig1x2 = my_0
          xorig1x3 = my_0

        yorig1 = y(node1)
          yorig1y1 = my_1
          yorig1y2 = my_0
          yorig1y3 = my_0

        zorig1 = z(node1)
          zorig1z1 = my_1
          zorig1z2 = my_0
          zorig1z3 = my_0

        xorig2 = x(node2)
          xorig2x1 = my_0
          xorig2x2 = my_1
          xorig2x3 = my_0

        yorig2 = y(node2)
          yorig2y1 = my_0
          yorig2y2 = my_1
          yorig2y3 = my_0

        zorig2 = z(node2)
          zorig2z1 = my_0
          zorig2z2 = my_1
          zorig2z3 = my_0

        xorig3 = x(node3)
          xorig3x1 = my_0
          xorig3x2 = my_0
          xorig3x3 = my_1

        yorig3 = y(node3)
          yorig3y1 = my_0
          yorig3y2 = my_0
          yorig3y3 = my_1

        zorig3 = z(node3)
          zorig3z1 = my_0
          zorig3z2 = my_0
          zorig3z3 = my_1

        xc(1)=((x(node2)-xorig1) + (x(node3)-xorig1))/my_3
          xcx1(1)=(-xorig1x1-xorig1x1)/my_3
          xcx2(1)=my_1/my_3
          xcx3(1)=my_1/my_3

        yc(1)=((y(node2)-yorig1) + (y(node3)-yorig1))/my_3
          ycy1(1)=(-yorig1y1-yorig1y1)/my_3
          ycy2(1)=my_1/my_3
          ycy3(1)=my_1/my_3

        zc(1)=((z(node2)-zorig1) + (z(node3)-zorig1))/my_3
          zcz1(1)=(-zorig1z1-zorig1z1)/my_3
          zcz2(1)=my_1/my_3
          zcz3(1)=my_1/my_3

        xc(2)=((x(node1)-xorig2) + (x(node3)-xorig2))/my_3
          xcx1(2)=my_1/my_3
          xcx2(2)=(-xorig2x2-xorig2x2)/my_3
          xcx3(2)=my_1/my_3

        yc(2)=((y(node1)-yorig2) + (y(node3)-yorig2))/my_3
          ycy1(2)=my_1/my_3
          ycy2(2)=(-yorig2y2-yorig2y2)/my_3
          ycy3(2)=my_1/my_3

        zc(2)=((z(node1)-zorig2) + (z(node3)-zorig2))/my_3
          zcz1(2)=my_1/my_3
          zcz2(2)=(-zorig2z2-zorig2z2)/my_3
          zcz3(2)=my_1/my_3

        xc(3)=((x(node1)-xorig3) + (x(node2)-xorig3))/my_3
          xcx1(3)=my_1/my_3
          xcx2(3)=my_1/my_3
          xcx3(3)=(-xorig3x3-xorig3x3)/my_3

        yc(3)=((y(node1)-yorig3) + (y(node2)-yorig3))/my_3
          ycy1(3)=my_1/my_3
          ycy2(3)=my_1/my_3
          ycy3(3)=(-yorig3y3-yorig3y3)/my_3

        zc(3)=((z(node1)-zorig3) + (z(node2)-zorig3))/my_3
          zcz1(3)=my_1/my_3
          zcz2(3)=my_1/my_3
          zcz3(3)=(-zorig3z3-zorig3z3)/my_3

        dxdtc = (bdxdt(n1) + bdxdt(n2) + bdxdt(n3))/my_3
          do i = 1, ntp
            dxdtcx1(i) = time_coeff(i)/my_3
            dxdtcx2(i) = time_coeff(i)/my_3
            dxdtcx3(i) = time_coeff(i)/my_3
          end do

        dydtc = (bdydt(n1) + bdydt(n2) + bdydt(n3))/my_3
          do i = 1, ntp
            dydtcy1(i) = time_coeff(i)/my_3
            dydtcy2(i) = time_coeff(i)/my_3
            dydtcy3(i) = time_coeff(i)/my_3
          end do

        dzdtc = (bdzdt(n1) + bdzdt(n2) + bdzdt(n3))/my_3
          do i = 1, ntp
            dzdtcz1(i) = time_coeff(i)/my_3
            dzdtcz2(i) = time_coeff(i)/my_3
            dzdtcz3(i) = time_coeff(i)/my_3
          end do

        tria_node_loop : do nn = 1, 3

          bxnx1 = 0.0_dp
          bxnx2 = 0.0_dp
          bxnx3 = 0.0_dp
          bxny1 = 0.0_dp
          bxny2 = 0.0_dp
          bxny3 = 0.0_dp
          bxnz1 = 0.0_dp
          bxnz2 = 0.0_dp
          bxnz3 = 0.0_dp

          bynx1 = 0.0_dp
          bynx2 = 0.0_dp
          bynx3 = 0.0_dp
          byny1 = 0.0_dp
          byny2 = 0.0_dp
          byny3 = 0.0_dp
          bynz1 = 0.0_dp
          bynz2 = 0.0_dp
          bynz3 = 0.0_dp

          bznx1 = 0.0_dp
          bznx2 = 0.0_dp
          bznx3 = 0.0_dp
          bzny1 = 0.0_dp
          bzny2 = 0.0_dp
          bzny3 = 0.0_dp
          bznz1 = 0.0_dp
          bznz2 = 0.0_dp
          bznz3 = 0.0_dp

          do i = 1, ntp
            bfacespeedx1(i) = 0.0_dp
            bfacespeedx2(i) = 0.0_dp
            bfacespeedx3(i) = 0.0_dp

            bfacespeedy1(i) = 0.0_dp
            bfacespeedy2(i) = 0.0_dp
            bfacespeedy3(i) = 0.0_dp

            bfacespeedz1(i) = 0.0_dp
            bfacespeedz2(i) = 0.0_dp
            bfacespeedz3(i) = 0.0_dp
          end do

          select case(nn)
            case(1)
              nn1 = n1
              nn2 = n2
              nn3 = n3
              xorig = xorig1
                xorigx1 = xorig1x1
                xorigx2 = xorig1x2
                xorigx3 = xorig1x3
              yorig = yorig1
                yorigy1 = yorig1y1
                yorigy2 = yorig1y2
                yorigy3 = yorig1y3
              zorig = zorig1
                zorigz1 = zorig1z1
                zorigz2 = zorig1z2
                zorigz3 = zorig1z3
              xc_ref = xc(1)
                xc_refx1 = xcx1(1)
                xc_refx2 = xcx2(1)
                xc_refx3 = xcx3(1)
              yc_ref = yc(1)
                yc_refy1 = ycy1(1)
                yc_refy2 = ycy2(1)
                yc_refy3 = ycy3(1)
              zc_ref = zc(1)
                zc_refz1 = zcz1(1)
                zc_refz2 = zcz2(1)
                zc_refz3 = zcz3(1)
            case(2)
              nn1 = n2
              nn2 = n3
              nn3 = n1
              xorig = xorig2
                xorigx1 = xorig2x1
                xorigx2 = xorig2x2
                xorigx3 = xorig2x3
              yorig = yorig2
                yorigy1 = yorig2y1
                yorigy2 = yorig2y2
                yorigy3 = yorig2y3
              zorig = zorig2
                zorigz1 = zorig2z1
                zorigz2 = zorig2z2
                zorigz3 = zorig2z3
              xc_ref = xc(2)
                xc_refx1 = xcx1(2)
                xc_refx2 = xcx2(2)
                xc_refx3 = xcx3(2)
              yc_ref = yc(2)
                yc_refy1 = ycy1(2)
                yc_refy2 = ycy2(2)
                yc_refy3 = ycy3(2)
              zc_ref = zc(2)
                zc_refz1 = zcz1(2)
                zc_refz2 = zcz2(2)
                zc_refz3 = zcz3(2)
            case(3)
              nn1 = n3
              nn2 = n1
              nn3 = n2
              xorig = xorig3
                xorigx1 = xorig3x1
                xorigx2 = xorig3x2
                xorigx3 = xorig3x3
              yorig = yorig3
                yorigy1 = yorig3y1
                yorigy2 = yorig3y2
                yorigy3 = yorig3y3
              zorig = zorig3
                zorigz1 = zorig3z1
                zorigz2 = zorig3z2
                zorigz3 = zorig3z3
              xc_ref = xc(3)
                xc_refx1 = xcx1(3)
                xc_refx2 = xcx2(3)
                xc_refx3 = xcx3(3)
              yc_ref = yc(3)
                yc_refy1 = ycy1(3)
                yc_refy2 = ycy2(3)
                yc_refy3 = ycy3(3)
              zc_ref = zc(3)
                zc_refz1 = zcz1(3)
                zc_refz2 = zcz2(3)
                zc_refz3 = zcz3(3)
            case default
              nn1 = 0; nn2 = 0; nn3 = 0 ! avoid used uninitialized warnings
          end select

          node1 = ibnode(nn1)
          node2 = ibnode(nn2)
          node3 = ibnode(nn3)

          x1 = x(node1)
            if ( node1 == cnode1 ) then
              x1x1 = my_1
              x1x2 = my_0
              x1x3 = my_0
            else if ( node1 == cnode2 ) then
              x1x1 = my_0
              x1x2 = my_1
              x1x3 = my_0
            else if ( node1 == cnode3 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_1
            endif

          x2 = x(node2)
            if ( node2 == cnode1 ) then
              x2x1 = my_1
              x2x2 = my_0
              x2x3 = my_0
            else if ( node2 == cnode2 ) then
              x2x1 = my_0
              x2x2 = my_1
              x2x3 = my_0
            else if ( node2 == cnode3 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_1
            endif

          x3 = x(node3)
            if ( node3 == cnode1 ) then
              x3x1 = my_1
              x3x2 = my_0
              x3x3 = my_0
            else if ( node3 == cnode2 ) then
              x3x1 = my_0
              x3x2 = my_1
              x3x3 = my_0
            else if ( node3 == cnode3 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_1
            endif

          y1 = y(node1)
            if ( node1 == cnode1 ) then
              y1y1 = my_1
              y1y2 = my_0
              y1y3 = my_0
            else if ( node1 == cnode2 ) then
              y1y1 = my_0
              y1y2 = my_1
              y1y3 = my_0
            else if ( node1 == cnode3 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_1
            endif

          y2 = y(node2)
            if ( node2 == cnode1 ) then
              y2y1 = my_1
              y2y2 = my_0
              y2y3 = my_0
            else if ( node2 == cnode2 ) then
              y2y1 = my_0
              y2y2 = my_1
              y2y3 = my_0
            else if ( node2 == cnode3 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_1
            endif

          y3 = y(node3)
            if ( node3 == cnode1 ) then
              y3y1 = my_1
              y3y2 = my_0
              y3y3 = my_0
            else if ( node3 == cnode2 ) then
              y3y1 = my_0
              y3y2 = my_1
              y3y3 = my_0
            else if ( node3 == cnode3 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_1
            endif

          z1 = z(node1)
            if ( node1 == cnode1 ) then
              z1z1 = my_1
              z1z2 = my_0
              z1z3 = my_0
            else if ( node1 == cnode2 ) then
              z1z1 = my_0
              z1z2 = my_1
              z1z3 = my_0
            else if ( node1 == cnode3 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_1
            endif

          z2 = z(node2)
            if ( node2 == cnode1 ) then
              z2z1 = my_1
              z2z2 = my_0
              z2z3 = my_0
            else if ( node2 == cnode2 ) then
              z2z1 = my_0
              z2z2 = my_1
              z2z3 = my_0
            else if ( node2 == cnode3 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_1
            endif

          z3 = z(node3)
            if ( node3 == cnode1 ) then
              z3z1 = my_1
              z3z2 = my_0
              z3z3 = my_0
            else if ( node3 == cnode2 ) then
              z3z1 = my_0
              z3z2 = my_1
              z3z3 = my_0
            else if ( node3 == cnode3 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_1
            endif

          x0 = x1 - xorig
            x0x1 = x1x1 - xorigx1
            x0x2 = x1x2 - xorigx2
            x0x3 = x1x3 - xorigx3
          y0 = y1 - yorig
            y0y1 = y1y1 - yorigy1
            y0y2 = y1y2 - yorigy2
            y0y3 = y1y3 - yorigy3
          z0 = z1 - zorig
            z0z1 = z1z1 - zorigz1
            z0z2 = z1z2 - zorigz2
            z0z3 = z1z3 - zorigz3

          dxdt0 = bdxdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                dxdt0x1(i) = time_coeff(i)
                dxdt0x2(i) = my_0
                dxdt0x3(i) = my_0
              else if ( node1 == cnode2 ) then
                dxdt0x1(i) = my_0
                dxdt0x2(i) = time_coeff(i)
                dxdt0x3(i) = my_0
              else if ( node1 == cnode3 ) then
                dxdt0x1(i) = my_0
                dxdt0x2(i) = my_0
                dxdt0x3(i) = time_coeff(i)
              endif
            end do

          dydt0 = bdydt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                dydt0y1(i) = time_coeff(i)
                dydt0y2(i) = my_0
                dydt0y3(i) = my_0
              else if ( node1 == cnode2 ) then
                dydt0y1(i) = my_0
                dydt0y2(i) = time_coeff(i)
                dydt0y3(i) = my_0
              else if ( node1 == cnode3 ) then
                dydt0y1(i) = my_0
                dydt0y2(i) = my_0
                dydt0y3(i) = time_coeff(i)
              endif
            end do

          dzdt0 = bdzdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                dzdt0z1(i) = time_coeff(i)
                dzdt0z2(i) = my_0
                dzdt0z3(i) = my_0
              else if ( node1 == cnode2 ) then
                dzdt0z1(i) = my_0
                dzdt0z2(i) = time_coeff(i)
                dzdt0z3(i) = my_0
              else if ( node1 == cnode3 ) then
                dzdt0z1(i) = my_0
                dzdt0z2(i) = my_0
                dzdt0z3(i) = time_coeff(i)
              endif
            end do

          xl = ((x1-xorig) + (x3-xorig))/my_2
            xlx1 = ((x1x1-xorigx1) + (x3x1-xorigx1))/my_2
            xlx2 = ((x1x2-xorigx2) + (x3x2-xorigx2))/my_2
            xlx3 = ((x1x3-xorigx3) + (x3x3-xorigx3))/my_2

          yl = ((y1-yorig) + (y3-yorig))/my_2
            yly1 = ((y1y1-yorigy1) + (y3y1-yorigy1))/my_2
            yly2 = ((y1y2-yorigy2) + (y3y2-yorigy2))/my_2
            yly3 = ((y1y3-yorigy3) + (y3y3-yorigy3))/my_2

          zl = ((z1-zorig) + (z3-zorig))/my_2
            zlz1 = ((z1z1-zorigz1) + (z3z1-zorigz1))/my_2
            zlz2 = ((z1z2-zorigz2) + (z3z2-zorigz2))/my_2
            zlz3 = ((z1z3-zorigz3) + (z3z3-zorigz3))/my_2

          piece1 = bdxdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1x1(i) = time_coeff(i)
                piece1x2(i) = my_0
                piece1x3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1x1(i) = my_0
                piece1x2(i) = time_coeff(i)
                piece1x3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = time_coeff(i)
              endif
            end do

          piece2 = bdxdt(nn3)
            do i = 1, ntp
              if ( node3 == cnode1 ) then
                piece2x1(i) = time_coeff(i)
                piece2x2(i) = my_0
                piece2x3(i) = my_0
              else if ( node3 == cnode2 ) then
                piece2x1(i) = my_0
                piece2x2(i) = time_coeff(i)
                piece2x3(i) = my_0
              else if ( node3 == cnode3 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = time_coeff(i)
              endif
            end do

          dxdtl = (piece1 + piece2)/my_2
            do i = 1, ntp
              dxdtlx1(i) = (piece1x1(i) + piece2x1(i))/my_2
              dxdtlx2(i) = (piece1x2(i) + piece2x2(i))/my_2
              dxdtlx3(i) = (piece1x3(i) + piece2x3(i))/my_2
            end do

          piece1 = bdydt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1y1(i) = time_coeff(i)
                piece1y2(i) = my_0
                piece1y3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1y1(i) = my_0
                piece1y2(i) = time_coeff(i)
                piece1y3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = time_coeff(i)
              endif
            end do

          piece2 = bdydt(nn3)
            do i = 1, ntp
              if ( node3 == cnode1 ) then
                piece2y1(i) = time_coeff(i)
                piece2y2(i) = my_0
                piece2y3(i) = my_0
              else if ( node3 == cnode2 ) then
                piece2y1(i) = my_0
                piece2y2(i) = time_coeff(i)
                piece2y3(i) = my_0
              else if ( node3 == cnode3 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = time_coeff(i)
              endif
            end do

          dydtl = (piece1 + piece2)/my_2
            do i = 1, ntp
              dydtly1(i) = (piece1y1(i) + piece2y1(i))/my_2
              dydtly2(i) = (piece1y2(i) + piece2y2(i))/my_2
              dydtly3(i) = (piece1y3(i) + piece2y3(i))/my_2
            end do

          piece1 = bdzdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1z1(i) = time_coeff(i)
                piece1z2(i) = my_0
                piece1z3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1z1(i) = my_0
                piece1z2(i) = time_coeff(i)
                piece1z3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = time_coeff(i)
              endif
            end do

          piece2 = bdzdt(nn3)
            do i = 1, ntp
              if ( node3 == cnode1 ) then
                piece2z1(i) = time_coeff(i)
                piece2z2(i) = my_0
                piece2z3(i) = my_0
              else if ( node3 == cnode2 ) then
                piece2z1(i) = my_0
                piece2z2(i) = time_coeff(i)
                piece2z3(i) = my_0
              else if ( node3 == cnode3 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = time_coeff(i)
              endif
            end do

          dzdtl = (piece1 + piece2)/my_2
            do i = 1, ntp
              dzdtlz1(i) = (piece1z1(i) + piece2z1(i))/my_2
              dzdtlz2(i) = (piece1z2(i) + piece2z2(i))/my_2
              dzdtlz3(i) = (piece1z3(i) + piece2z3(i))/my_2
            end do

          xr = ((x1-xorig) + (x2-xorig))/my_2
            xrx1 = ((x1x1-xorigx1) + (x2x1-xorigx1))/my_2
            xrx2 = ((x1x2-xorigx2) + (x2x2-xorigx2))/my_2
            xrx3 = ((x1x3-xorigx3) + (x2x3-xorigx3))/my_2

          yr = ((y1-yorig) + (y2-yorig))/my_2
            yry1 = ((y1y1-yorigy1) + (y2y1-yorigy1))/my_2
            yry2 = ((y1y2-yorigy2) + (y2y2-yorigy2))/my_2
            yry3 = ((y1y3-yorigy3) + (y2y3-yorigy3))/my_2

          zr = ((z1-zorig) + (z2-zorig))/my_2
            zrz1 = ((z1z1-zorigz1) + (z2z1-zorigz1))/my_2
            zrz2 = ((z1z2-zorigz2) + (z2z2-zorigz2))/my_2
            zrz3 = ((z1z3-zorigz3) + (z2z3-zorigz3))/my_2

          piece1 = bdxdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1x1(i) = time_coeff(i)
                piece1x2(i) = my_0
                piece1x3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1x1(i) = my_0
                piece1x2(i) = time_coeff(i)
                piece1x3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = time_coeff(i)
              endif
            end do

          piece2 = bdxdt(nn2)
            do i = 1, ntp
              if ( node2 == cnode1 ) then
                piece2x1(i) = time_coeff(i)
                piece2x2(i) = my_0
                piece2x3(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2x1(i) = my_0
                piece2x2(i) = time_coeff(i)
                piece2x3(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = time_coeff(i)
              endif
            end do

          dxdtr = (piece1 + piece2)/my_2
            do i = 1, ntp
              dxdtrx1(i) = (piece1x1(i) + piece2x1(i))/my_2
              dxdtrx2(i) = (piece1x2(i) + piece2x2(i))/my_2
              dxdtrx3(i) = (piece1x3(i) + piece2x3(i))/my_2
            end do

          piece1 = bdydt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1y1(i) = time_coeff(i)
                piece1y2(i) = my_0
                piece1y3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1y1(i) = my_0
                piece1y2(i) = time_coeff(i)
                piece1y3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = time_coeff(i)
              endif
            end do

          piece2 = bdydt(nn2)
            do i = 1, ntp
              if ( node2 == cnode1 ) then
                piece2y1(i) = time_coeff(i)
                piece2y2(i) = my_0
                piece2y3(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2y1(i) = my_0
                piece2y2(i) = time_coeff(i)
                piece2y3(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = time_coeff(i)
              endif
            end do

          dydtr = (piece1 + piece2)/my_2
            do i = 1, ntp
              dydtry1(i) = (piece1y1(i) + piece2y1(i))/my_2
              dydtry2(i) = (piece1y2(i) + piece2y2(i))/my_2
              dydtry3(i) = (piece1y3(i) + piece2y3(i))/my_2
            end do

          piece1 = bdzdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1z1(i) = time_coeff(i)
                piece1z2(i) = my_0
                piece1z3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1z1(i) = my_0
                piece1z2(i) = time_coeff(i)
                piece1z3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = time_coeff(i)
              endif
            end do

          piece2 = bdzdt(nn2)
            do i = 1, ntp
              if ( node2 == cnode1 ) then
                piece2z1(i) = time_coeff(i)
                piece2z2(i) = my_0
                piece2z3(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2z1(i) = my_0
                piece2z2(i) = time_coeff(i)
                piece2z3(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = time_coeff(i)
              endif
            end do

          dzdtr = (piece1 + piece2)/my_2
            do i = 1, ntp
              dzdtrz1(i) = (piece1z1(i) + piece2z1(i))/my_2
              dzdtrz2(i) = (piece1z2(i) + piece2z2(i))/my_2
              dzdtrz3(i) = (piece1z3(i) + piece2z3(i))/my_2
            end do

!       triangle x0-xr-xc

          areax = my_half*( (yr-y0)*(zc_ref-z0) - (zr-z0)*(yc_ref-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0

            areaxy1 = my_half*((yry1-y0y1)*(zc_ref-z0)- (zr-z0)*(yc_refy1-y0y1))
            areaxy2 = my_half*((yry2-y0y2)*(zc_ref-z0)- (zr-z0)*(yc_refy2-y0y2))
            areaxy3 = my_half*((yry3-y0y3)*(zc_ref-z0)- (zr-z0)*(yc_refy3-y0y3))

            areaxz1 = my_half*((yr-y0)*(zc_refz1-z0z1)- (zrz1-z0z1)*(yc_ref-y0))
            areaxz2 = my_half*((yr-y0)*(zc_refz2-z0z2)- (zrz2-z0z2)*(yc_ref-y0))
            areaxz3 = my_half*((yr-y0)*(zc_refz3-z0z3)- (zrz3-z0z3)*(yc_ref-y0))

          areay = my_half*( (zr-z0)*(xc_ref-x0) - (xr-x0)*(zc_ref-z0) )
            areayx1 = my_half*((zr-z0)*(xc_refx1-x0x1)- (xrx1-x0x1)*(zc_ref-z0))
            areayx2 = my_half*((zr-z0)*(xc_refx2-x0x2)- (xrx2-x0x2)*(zc_ref-z0))
            areayx3 = my_half*((zr-z0)*(xc_refx3-x0x3)- (xrx3-x0x3)*(zc_ref-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0

            areayz1 = my_half*((zrz1-z0z1)*(xc_ref-x0)- (xr-x0)*(zc_refz1-z0z1))
            areayz2 = my_half*((zrz2-z0z2)*(xc_ref-x0)- (xr-x0)*(zc_refz2-z0z2))
            areayz3 = my_half*((zrz3-z0z3)*(xc_ref-x0)- (xr-x0)*(zc_refz3-z0z3))

          areaz = my_half*( (xr-x0)*(yc_ref-y0) - (yr-y0)*(xc_ref-x0) )
            areazx1 = my_half*((xrx1-x0x1)*(yc_ref-y0)- (yr-y0)*(xc_refx1-x0x1))
            areazx2 = my_half*((xrx2-x0x2)*(yc_ref-y0)- (yr-y0)*(xc_refx2-x0x2))
            areazx3 = my_half*((xrx3-x0x3)*(yc_ref-y0)- (yr-y0)*(xc_refx3-x0x3))

            areazy1 = my_half*((xr-x0)*(yc_refy1-y0y1)- (yry1-y0y1)*(xc_ref-x0))
            areazy2 = my_half*((xr-x0)*(yc_refy2-y0y2)- (yry2-y0y2)*(xc_ref-x0))
            areazy3 = my_half*((xr-x0)*(yc_refy3-y0y3)- (yry3-y0y3)*(xc_ref-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0

!         bxn(nn1) = bxn(nn1) - areax
            bxnx1 = bxnx1 - areaxx1
            bxnx2 = bxnx2 - areaxx2
            bxnx3 = bxnx3 - areaxx3
            bxny1 = bxny1 - areaxy1
            bxny2 = bxny2 - areaxy2
            bxny3 = bxny3 - areaxy3
            bxnz1 = bxnz1 - areaxz1
            bxnz2 = bxnz2 - areaxz2
            bxnz3 = bxnz3 - areaxz3

!         byn(nn1) = byn(nn1) - areay
            bynx1 = bynx1 - areayx1
            bynx2 = bynx2 - areayx2
            bynx3 = bynx3 - areayx3
            byny1 = byny1 - areayy1
            byny2 = byny2 - areayy2
            byny3 = byny3 - areayy3
            bynz1 = bynz1 - areayz1
            bynz2 = bynz2 - areayz2
            bynz3 = bynz3 - areayz3

!         bzn(nn1) = bzn(nn1) - areaz
            bznx1 = bznx1 - areazx1
            bznx2 = bznx2 - areazx2
            bznx3 = bznx3 - areazx3
            bzny1 = bzny1 - areazy1
            bzny2 = bzny2 - areazy2
            bzny3 = bzny3 - areazy3
            bznz1 = bznz1 - areazz1
            bznz2 = bznz2 - areazz2
            bznz3 = bznz3 - areazz3

          dxdtavg = (dxdt0 + dxdtr + dxdtc)/my_3
            do i = 1, ntp
              dxdtavgx1(i) = (dxdt0x1(i) + dxdtrx1(i) + dxdtcx1(i))/my_3
              dxdtavgx2(i) = (dxdt0x2(i) + dxdtrx2(i) + dxdtcx2(i))/my_3
              dxdtavgx3(i) = (dxdt0x3(i) + dxdtrx3(i) + dxdtcx3(i))/my_3

              dxdtavgy1(i) = my_0
              dxdtavgy2(i) = my_0
              dxdtavgy3(i) = my_0

              dxdtavgz1(i) = my_0
              dxdtavgz2(i) = my_0
              dxdtavgz3(i) = my_0
            end do

          dydtavg = (dydt0 + dydtr + dydtc)/my_3
            do i = 1, ntp
              dydtavgx1(i) = my_0
              dydtavgx2(i) = my_0
              dydtavgx3(i) = my_0

              dydtavgy1(i) = (dydt0y1(i) + dydtry1(i) + dydtcy1(i))/my_3
              dydtavgy2(i) = (dydt0y2(i) + dydtry2(i) + dydtcy2(i))/my_3
              dydtavgy3(i) = (dydt0y3(i) + dydtry3(i) + dydtcy3(i))/my_3

              dydtavgz1(i) = my_0
              dydtavgz2(i) = my_0
              dydtavgz3(i) = my_0
            end do

          dzdtavg = (dzdt0 + dzdtr + dzdtc)/my_3
            do i = 1, ntp
              dzdtavgx1(i) = my_0
              dzdtavgx2(i) = my_0
              dzdtavgx3(i) = my_0

              dzdtavgy1(i) = my_0
              dzdtavgy2(i) = my_0
              dzdtavgy3(i) = my_0

              dzdtavgz1(i) = (dzdt0z1(i) + dzdtrz1(i) + dzdtcz1(i))/my_3
              dzdtavgz2(i) = (dzdt0z2(i) + dzdtrz2(i) + dzdtcz2(i))/my_3
              dzdtavgz3(i) = (dzdt0z3(i) + dzdtrz3(i) + dzdtcz3(i))/my_3
            end do

!         term = dxdtavg*areax + dydtavg*areay + dzdtavg*areaz
          do i = 1, ntp

! area only depends on grid coords at current time level

           if ( i == 1 ) then
            termx1(i) = dxdtavg*areaxx1 + areax*dxdtavgx1(i) + dydtavg*areayx1 &
                   + areay*dydtavgx1(i) + dzdtavg*areazx1 + areaz*dzdtavgx1(i)
            termx2(i) = dxdtavg*areaxx2 + areax*dxdtavgx2(i) + dydtavg*areayx2 &
                   + areay*dydtavgx2(i) + dzdtavg*areazx2 + areaz*dzdtavgx2(i)
            termx3(i) = dxdtavg*areaxx3 + areax*dxdtavgx3(i) + dydtavg*areayx3 &
                   + areay*dydtavgx3(i) + dzdtavg*areazx3 + areaz*dzdtavgx3(i)

            termy1(i) = dxdtavg*areaxy1 + areax*dxdtavgy1(i) + dydtavg*areayy1 &
                   + areay*dydtavgy1(i) + dzdtavg*areazy1 + areaz*dzdtavgy1(i)
            termy2(i) = dxdtavg*areaxy2 + areax*dxdtavgy2(i) + dydtavg*areayy2 &
                   + areay*dydtavgy2(i) + dzdtavg*areazy2 + areaz*dzdtavgy2(i)
            termy3(i) = dxdtavg*areaxy3 + areax*dxdtavgy3(i) + dydtavg*areayy3 &
                   + areay*dydtavgy3(i) + dzdtavg*areazy3 + areaz*dzdtavgy3(i)

            termz1(i) = dxdtavg*areaxz1 + areax*dxdtavgz1(i) + dydtavg*areayz1 &
                   + areay*dydtavgz1(i) + dzdtavg*areazz1 + areaz*dzdtavgz1(i)
            termz2(i) = dxdtavg*areaxz2 + areax*dxdtavgz2(i) + dydtavg*areayz2 &
                   + areay*dydtavgz2(i) + dzdtavg*areazz2 + areaz*dzdtavgz2(i)
            termz3(i) = dxdtavg*areaxz3 + areax*dxdtavgz3(i) + dydtavg*areayz3 &
                   + areay*dydtavgz3(i) + dzdtavg*areazz3 + areaz*dzdtavgz3(i)
           else
            termx1(i)=areax*dxdtavgx1(i)+areay*dydtavgx1(i) + areaz*dzdtavgx1(i)
            termx2(i)=areax*dxdtavgx2(i)+areay*dydtavgx2(i) + areaz*dzdtavgx2(i)
            termx3(i)=areax*dxdtavgx3(i)+areay*dydtavgx3(i) + areaz*dzdtavgx3(i)

            termy1(i)=areax*dxdtavgy1(i)+areay*dydtavgy1(i) + areaz*dzdtavgy1(i)
            termy2(i)=areax*dxdtavgy2(i)+areay*dydtavgy2(i) + areaz*dzdtavgy2(i)
            termy3(i)=areax*dxdtavgy3(i)+areay*dydtavgy3(i) + areaz*dzdtavgy3(i)

            termz1(i)=areax*dxdtavgz1(i)+areay*dydtavgz1(i) + areaz*dzdtavgz1(i)
            termz2(i)=areax*dxdtavgz2(i)+areay*dydtavgz2(i) + areaz*dzdtavgz2(i)
            termz3(i)=areax*dxdtavgz3(i)+areay*dydtavgz3(i) + areaz*dzdtavgz3(i)
           endif
          end do

!         bfacespeed(nn1) = bfacespeed(nn1) - term

!         bfacespeed = -term
            do i = 1, ntp
              bfacespeedx1(i) = bfacespeedx1(i) - termx1(i)
              bfacespeedx2(i) = bfacespeedx2(i) - termx2(i)
              bfacespeedx3(i) = bfacespeedx3(i) - termx3(i)

              bfacespeedy1(i) = bfacespeedy1(i) - termy1(i)
              bfacespeedy2(i) = bfacespeedy2(i) - termy2(i)
              bfacespeedy3(i) = bfacespeedy3(i) - termy3(i)

              bfacespeedz1(i) = bfacespeedz1(i) - termz1(i)
              bfacespeedz2(i) = bfacespeedz2(i) - termz2(i)
              bfacespeedz3(i) = bfacespeedz3(i) - termz3(i)
            end do

!       triangle x0-xc-xl

          areax = my_half*( (yc_ref-y0)*(zl-z0) - (zc_ref-z0)*(yl-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0

            areaxy1 = my_half*((yc_refy1-y0y1)*(zl-z0)- (zc_ref-z0)*(yly1-y0y1))
            areaxy2 = my_half*((yc_refy2-y0y2)*(zl-z0)- (zc_ref-z0)*(yly2-y0y2))
            areaxy3 = my_half*((yc_refy3-y0y3)*(zl-z0)- (zc_ref-z0)*(yly3-y0y3))

            areaxz1 = my_half*((yc_ref-y0)*(zlz1-z0z1)- (zc_refz1-z0z1)*(yl-y0))
            areaxz2 = my_half*((yc_ref-y0)*(zlz2-z0z2)- (zc_refz2-z0z2)*(yl-y0))
            areaxz3 = my_half*((yc_ref-y0)*(zlz3-z0z3)- (zc_refz3-z0z3)*(yl-y0))

          areay = my_half*( (zc_ref-z0)*(xl-x0) - (xc_ref-x0)*(zl-z0) )
            areayx1 = my_half*((zc_ref-z0)*(xlx1-x0x1)- (xc_refx1-x0x1)*(zl-z0))
            areayx2 = my_half*((zc_ref-z0)*(xlx2-x0x2)- (xc_refx2-x0x2)*(zl-z0))
            areayx3 = my_half*((zc_ref-z0)*(xlx3-x0x3)- (xc_refx3-x0x3)*(zl-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0

            areayz1 = my_half*((zc_refz1-z0z1)*(xl-x0)- (xc_ref-x0)*(zlz1-z0z1))
            areayz2 = my_half*((zc_refz2-z0z2)*(xl-x0)- (xc_ref-x0)*(zlz2-z0z2))
            areayz3 = my_half*((zc_refz3-z0z3)*(xl-x0)- (xc_ref-x0)*(zlz3-z0z3))

          areaz = my_half*( (xc_ref-x0)*(yl-y0) - (yc_ref-y0)*(xl-x0) )
            areazx1 = my_half*((xc_refx1-x0x1)*(yl-y0)- (yc_ref-y0)*(xlx1-x0x1))
            areazx2 = my_half*((xc_refx2-x0x2)*(yl-y0)- (yc_ref-y0)*(xlx2-x0x2))
            areazx3 = my_half*((xc_refx3-x0x3)*(yl-y0)- (yc_ref-y0)*(xlx3-x0x3))

            areazy1 = my_half*((xc_ref-x0)*(yly1-y0y1)- (yc_refy1-y0y1)*(xl-x0))
            areazy2 = my_half*((xc_ref-x0)*(yly2-y0y2)- (yc_refy2-y0y2)*(xl-x0))
            areazy3 = my_half*((xc_ref-x0)*(yly3-y0y3)- (yc_refy3-y0y3)*(xl-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0

!         bxn(nn1) = bxn(nn1) - areax
            bxnx1 = bxnx1 - areaxx1
            bxnx2 = bxnx2 - areaxx2
            bxnx3 = bxnx3 - areaxx3
            bxny1 = bxny1 - areaxy1
            bxny2 = bxny2 - areaxy2
            bxny3 = bxny3 - areaxy3
            bxnz1 = bxnz1 - areaxz1
            bxnz2 = bxnz2 - areaxz2
            bxnz3 = bxnz3 - areaxz3

!         byn(nn1) = byn(nn1) - areay
            bynx1 = bynx1 - areayx1
            bynx2 = bynx2 - areayx2
            bynx3 = bynx3 - areayx3
            byny1 = byny1 - areayy1
            byny2 = byny2 - areayy2
            byny3 = byny3 - areayy3
            bynz1 = bynz1 - areayz1
            bynz2 = bynz2 - areayz2
            bynz3 = bynz3 - areayz3

!         bzn(nn1) = bzn(nn1) - areaz
            bznx1 = bznx1 - areazx1
            bznx2 = bznx2 - areazx2
            bznx3 = bznx3 - areazx3
            bzny1 = bzny1 - areazy1
            bzny2 = bzny2 - areazy2
            bzny3 = bzny3 - areazy3
            bznz1 = bznz1 - areazz1
            bznz2 = bznz2 - areazz2
            bznz3 = bznz3 - areazz3

          dxdtavg = (dxdt0 + dxdtc + dxdtl)/my_3
            do i = 1, ntp
              dxdtavgx1(i) = (dxdt0x1(i) + dxdtcx1(i) + dxdtlx1(i))/my_3
              dxdtavgx2(i) = (dxdt0x2(i) + dxdtcx2(i) + dxdtlx2(i))/my_3
              dxdtavgx3(i) = (dxdt0x3(i) + dxdtcx3(i) + dxdtlx3(i))/my_3

              dxdtavgy1(i) = my_0
              dxdtavgy2(i) = my_0
              dxdtavgy3(i) = my_0

              dxdtavgz1(i) = my_0
              dxdtavgz2(i) = my_0
              dxdtavgz3(i) = my_0
            end do

          dydtavg = (dydt0 + dydtc + dydtl)/my_3
            do i = 1, ntp
              dydtavgx1(i) = my_0
              dydtavgx2(i) = my_0
              dydtavgx3(i) = my_0

              dydtavgy1(i) = (dydt0y1(i) + dydtcy1(i) + dydtly1(i))/my_3
              dydtavgy2(i) = (dydt0y2(i) + dydtcy2(i) + dydtly2(i))/my_3
              dydtavgy3(i) = (dydt0y3(i) + dydtcy3(i) + dydtly3(i))/my_3

              dydtavgz1(i) = my_0
              dydtavgz2(i) = my_0
              dydtavgz3(i) = my_0
            end do

          dzdtavg = (dzdt0 + dzdtc + dzdtl)/my_3
            do i = 1, ntp
              dzdtavgx1(i) = my_0
              dzdtavgx2(i) = my_0
              dzdtavgx3(i) = my_0

              dzdtavgy1(i) = my_0
              dzdtavgy2(i) = my_0
              dzdtavgy3(i) = my_0

              dzdtavgz1(i) = (dzdt0z1(i) + dzdtcz1(i) + dzdtlz1(i))/my_3
              dzdtavgz2(i) = (dzdt0z2(i) + dzdtcz2(i) + dzdtlz2(i))/my_3
              dzdtavgz3(i) = (dzdt0z3(i) + dzdtcz3(i) + dzdtlz3(i))/my_3
            end do

!         term = dxdtavg*areax + dydtavg*areay + dzdtavg*areaz
          do i = 1, ntp

! area only depends on grid coords at current time level

           if ( i == 1 ) then
            termx1(i) = dxdtavg*areaxx1 + areax*dxdtavgx1(i) + dydtavg*areayx1 &
                   + areay*dydtavgx1(i) + dzdtavg*areazx1 + areaz*dzdtavgx1(i)
            termx2(i) = dxdtavg*areaxx2 + areax*dxdtavgx2(i) + dydtavg*areayx2 &
                   + areay*dydtavgx2(i) + dzdtavg*areazx2 + areaz*dzdtavgx2(i)
            termx3(i) = dxdtavg*areaxx3 + areax*dxdtavgx3(i) + dydtavg*areayx3 &
                   + areay*dydtavgx3(i) + dzdtavg*areazx3 + areaz*dzdtavgx3(i)

            termy1(i) = dxdtavg*areaxy1 + areax*dxdtavgy1(i) + dydtavg*areayy1 &
                   + areay*dydtavgy1(i) + dzdtavg*areazy1 + areaz*dzdtavgy1(i)
            termy2(i) = dxdtavg*areaxy2 + areax*dxdtavgy2(i) + dydtavg*areayy2 &
                   + areay*dydtavgy2(i) + dzdtavg*areazy2 + areaz*dzdtavgy2(i)
            termy3(i) = dxdtavg*areaxy3 + areax*dxdtavgy3(i) + dydtavg*areayy3 &
                   + areay*dydtavgy3(i) + dzdtavg*areazy3 + areaz*dzdtavgy3(i)

            termz1(i) = dxdtavg*areaxz1 + areax*dxdtavgz1(i) + dydtavg*areayz1 &
                   + areay*dydtavgz1(i) + dzdtavg*areazz1 + areaz*dzdtavgz1(i)
            termz2(i) = dxdtavg*areaxz2 + areax*dxdtavgz2(i) + dydtavg*areayz2 &
                   + areay*dydtavgz2(i) + dzdtavg*areazz2 + areaz*dzdtavgz2(i)
            termz3(i) = dxdtavg*areaxz3 + areax*dxdtavgz3(i) + dydtavg*areayz3 &
                   + areay*dydtavgz3(i) + dzdtavg*areazz3 + areaz*dzdtavgz3(i)
           else
            termx1(i)=areax*dxdtavgx1(i)+areay*dydtavgx1(i) + areaz*dzdtavgx1(i)
            termx2(i)=areax*dxdtavgx2(i)+areay*dydtavgx2(i) + areaz*dzdtavgx2(i)
            termx3(i)=areax*dxdtavgx3(i)+areay*dydtavgx3(i) + areaz*dzdtavgx3(i)

            termy1(i)=areax*dxdtavgy1(i)+areay*dydtavgy1(i) + areaz*dzdtavgy1(i)
            termy2(i)=areax*dxdtavgy2(i)+areay*dydtavgy2(i) + areaz*dzdtavgy2(i)
            termy3(i)=areax*dxdtavgy3(i)+areay*dydtavgy3(i) + areaz*dzdtavgy3(i)

            termz1(i)=areax*dxdtavgz1(i)+areay*dydtavgz1(i) + areaz*dzdtavgz1(i)
            termz2(i)=areax*dxdtavgz2(i)+areay*dydtavgz2(i) + areaz*dzdtavgz2(i)
            termz3(i)=areax*dxdtavgz3(i)+areay*dydtavgz3(i) + areaz*dzdtavgz3(i)
           endif
          end do

!         bfacespeed(nn1) = bfacespeed(nn1) - term

!         bfacespeed = -term
            do i = 1, ntp
              bfacespeedx1(i) = bfacespeedx1(i) - termx1(i)
              bfacespeedx2(i) = bfacespeedx2(i) - termx2(i)
              bfacespeedx3(i) = bfacespeedx3(i) - termx3(i)

              bfacespeedy1(i) = bfacespeedy1(i) - termy1(i)
              bfacespeedy2(i) = bfacespeedy2(i) - termy2(i)
              bfacespeedy3(i) = bfacespeedy3(i) - termy3(i)

              bfacespeedz1(i) = bfacespeedz1(i) - termz1(i)
              bfacespeedz2(i) = bfacespeedz2(i) - termz2(i)
              bfacespeedz3(i) = bfacespeedz3(i) - termz3(i)
            end do

! Now accumulate the normals and facespeeds derivatives
! Linearizations above are wrt invariant global nodes (ie, cnode1/cnode2/cnode3)

! cnode1 = ibnode(f2ntb(face,1))     ! global nodes for the face
! cnode2 = ibnode(f2ntb(face,2))     ! these are invariant within this loop
! cnode3 = ibnode(f2ntb(face,3))

! node1 == ibnode(nn1)               ! global nodes for the face
! node2 == ibnode(nn2)               ! these cycle within this loop
! node3 == ibnode(nn3)

          local_node : if ( node1 <= nnodes0 ) then

            ioff1 = -99
            ioff2 = -99
            ioff3 = -99
            do i = 1, node_stencil(nn1)%n
              inode = node_stencil(nn1)%nodes(i)
              if (inode == nn1 ) ioff1 = i
              if (inode == nn2 ) ioff2 = i
              if (inode == nn3 ) ioff3 = i
            end do

! Find and place the linearizations wrt nn1

            if ( node1 == cnode1 ) then
              node_stencil(nn1)%dxndx(ioff1) = node_stencil(nn1)%dxndx(ioff1)  &
                                                                         + bxnx1
              node_stencil(nn1)%dxndy(ioff1) = node_stencil(nn1)%dxndy(ioff1)  &
                                                                         + bxny1
              node_stencil(nn1)%dxndz(ioff1) = node_stencil(nn1)%dxndz(ioff1)  &
                                                                         + bxnz1

              node_stencil(nn1)%dyndx(ioff1) = node_stencil(nn1)%dyndx(ioff1)  &
                                                                         + bynx1
              node_stencil(nn1)%dyndy(ioff1) = node_stencil(nn1)%dyndy(ioff1)  &
                                                                         + byny1
              node_stencil(nn1)%dyndz(ioff1) = node_stencil(nn1)%dyndz(ioff1)  &
                                                                         + bynz1

              node_stencil(nn1)%dzndx(ioff1) = node_stencil(nn1)%dzndx(ioff1)  &
                                                                         + bznx1
              node_stencil(nn1)%dzndy(ioff1) = node_stencil(nn1)%dzndy(ioff1)  &
                                                                         + bzny1
              node_stencil(nn1)%dzndz(ioff1) = node_stencil(nn1)%dzndz(ioff1)  &
                                                                         + bznz1

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff1,k)=node_stencil(nn1)%dwdx(ioff1,k) &
                                                               + bfacespeedx1(k)
               node_stencil(nn1)%dwdy(ioff1,k)=node_stencil(nn1)%dwdy(ioff1,k) &
                                                               + bfacespeedy1(k)
               node_stencil(nn1)%dwdz(ioff1,k)=node_stencil(nn1)%dwdz(ioff1,k) &
                                                               + bfacespeedz1(k)
              end do
            else if ( node1 == cnode2 ) then
              node_stencil(nn1)%dxndx(ioff1) = node_stencil(nn1)%dxndx(ioff1)  &
                                                                         + bxnx2
              node_stencil(nn1)%dxndy(ioff1) = node_stencil(nn1)%dxndy(ioff1)  &
                                                                         + bxny2
              node_stencil(nn1)%dxndz(ioff1) = node_stencil(nn1)%dxndz(ioff1)  &
                                                                         + bxnz2

              node_stencil(nn1)%dyndx(ioff1) = node_stencil(nn1)%dyndx(ioff1)  &
                                                                         + bynx2
              node_stencil(nn1)%dyndy(ioff1) = node_stencil(nn1)%dyndy(ioff1)  &
                                                                         + byny2
              node_stencil(nn1)%dyndz(ioff1) = node_stencil(nn1)%dyndz(ioff1)  &
                                                                         + bynz2

              node_stencil(nn1)%dzndx(ioff1) = node_stencil(nn1)%dzndx(ioff1)  &
                                                                         + bznx2
              node_stencil(nn1)%dzndy(ioff1) = node_stencil(nn1)%dzndy(ioff1)  &
                                                                         + bzny2
              node_stencil(nn1)%dzndz(ioff1) = node_stencil(nn1)%dzndz(ioff1)  &
                                                                         + bznz2

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff1,k)=node_stencil(nn1)%dwdx(ioff1,k) &
                                                               + bfacespeedx2(k)
               node_stencil(nn1)%dwdy(ioff1,k)=node_stencil(nn1)%dwdy(ioff1,k) &
                                                               + bfacespeedy2(k)
               node_stencil(nn1)%dwdz(ioff1,k)=node_stencil(nn1)%dwdz(ioff1,k) &
                                                               + bfacespeedz2(k)
              end do
            else if ( node1 == cnode3 ) then
              node_stencil(nn1)%dxndx(ioff1) = node_stencil(nn1)%dxndx(ioff1)  &
                                                                         + bxnx3
              node_stencil(nn1)%dxndy(ioff1) = node_stencil(nn1)%dxndy(ioff1)  &
                                                                         + bxny3
              node_stencil(nn1)%dxndz(ioff1) = node_stencil(nn1)%dxndz(ioff1)  &
                                                                         + bxnz3

              node_stencil(nn1)%dyndx(ioff1) = node_stencil(nn1)%dyndx(ioff1)  &
                                                                         + bynx3
              node_stencil(nn1)%dyndy(ioff1) = node_stencil(nn1)%dyndy(ioff1)  &
                                                                         + byny3
              node_stencil(nn1)%dyndz(ioff1) = node_stencil(nn1)%dyndz(ioff1)  &
                                                                         + bynz3

              node_stencil(nn1)%dzndx(ioff1) = node_stencil(nn1)%dzndx(ioff1)  &
                                                                         + bznx3
              node_stencil(nn1)%dzndy(ioff1) = node_stencil(nn1)%dzndy(ioff1)  &
                                                                         + bzny3
              node_stencil(nn1)%dzndz(ioff1) = node_stencil(nn1)%dzndz(ioff1)  &
                                                                         + bznz3

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff1,k)=node_stencil(nn1)%dwdx(ioff1,k) &
                                                               + bfacespeedx3(k)
               node_stencil(nn1)%dwdy(ioff1,k)=node_stencil(nn1)%dwdy(ioff1,k) &
                                                               + bfacespeedy3(k)
               node_stencil(nn1)%dwdz(ioff1,k)=node_stencil(nn1)%dwdz(ioff1,k) &
                                                               + bfacespeedz3(k)
              end do
            endif

! Find and place the linearizations wrt nn2

            if ( node2 == cnode1 ) then
              node_stencil(nn1)%dxndx(ioff2) = node_stencil(nn1)%dxndx(ioff2)  &
                                                                         + bxnx1
              node_stencil(nn1)%dxndy(ioff2) = node_stencil(nn1)%dxndy(ioff2)  &
                                                                         + bxny1
              node_stencil(nn1)%dxndz(ioff2) = node_stencil(nn1)%dxndz(ioff2)  &
                                                                         + bxnz1

              node_stencil(nn1)%dyndx(ioff2) = node_stencil(nn1)%dyndx(ioff2)  &
                                                                         + bynx1
              node_stencil(nn1)%dyndy(ioff2) = node_stencil(nn1)%dyndy(ioff2)  &
                                                                         + byny1
              node_stencil(nn1)%dyndz(ioff2) = node_stencil(nn1)%dyndz(ioff2)  &
                                                                         + bynz1

              node_stencil(nn1)%dzndx(ioff2) = node_stencil(nn1)%dzndx(ioff2)  &
                                                                         + bznx1
              node_stencil(nn1)%dzndy(ioff2) = node_stencil(nn1)%dzndy(ioff2)  &
                                                                         + bzny1
              node_stencil(nn1)%dzndz(ioff2) = node_stencil(nn1)%dzndz(ioff2)  &
                                                                         + bznz1

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff2,k)=node_stencil(nn1)%dwdx(ioff2,k) &
                                                               + bfacespeedx1(k)
               node_stencil(nn1)%dwdy(ioff2,k)=node_stencil(nn1)%dwdy(ioff2,k) &
                                                               + bfacespeedy1(k)
               node_stencil(nn1)%dwdz(ioff2,k)=node_stencil(nn1)%dwdz(ioff2,k) &
                                                               + bfacespeedz1(k)
              end do
            else if ( node2 == cnode2 ) then
              node_stencil(nn1)%dxndx(ioff2) = node_stencil(nn1)%dxndx(ioff2)  &
                                                                         + bxnx2
              node_stencil(nn1)%dxndy(ioff2) = node_stencil(nn1)%dxndy(ioff2)  &
                                                                         + bxny2
              node_stencil(nn1)%dxndz(ioff2) = node_stencil(nn1)%dxndz(ioff2)  &
                                                                         + bxnz2

              node_stencil(nn1)%dyndx(ioff2) = node_stencil(nn1)%dyndx(ioff2)  &
                                                                         + bynx2
              node_stencil(nn1)%dyndy(ioff2) = node_stencil(nn1)%dyndy(ioff2)  &
                                                                         + byny2
              node_stencil(nn1)%dyndz(ioff2) = node_stencil(nn1)%dyndz(ioff2)  &
                                                                         + bynz2

              node_stencil(nn1)%dzndx(ioff2) = node_stencil(nn1)%dzndx(ioff2)  &
                                                                         + bznx2
              node_stencil(nn1)%dzndy(ioff2) = node_stencil(nn1)%dzndy(ioff2)  &
                                                                         + bzny2
              node_stencil(nn1)%dzndz(ioff2) = node_stencil(nn1)%dzndz(ioff2)  &
                                                                         + bznz2

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff2,k)=node_stencil(nn1)%dwdx(ioff2,k) &
                                                               + bfacespeedx2(k)
               node_stencil(nn1)%dwdy(ioff2,k)=node_stencil(nn1)%dwdy(ioff2,k) &
                                                               + bfacespeedy2(k)
               node_stencil(nn1)%dwdz(ioff2,k)=node_stencil(nn1)%dwdz(ioff2,k) &
                                                               + bfacespeedz2(k)
              end do
            else if ( node2 == cnode3 ) then
              node_stencil(nn1)%dxndx(ioff2) = node_stencil(nn1)%dxndx(ioff2)  &
                                                                         + bxnx3
              node_stencil(nn1)%dxndy(ioff2) = node_stencil(nn1)%dxndy(ioff2)  &
                                                                         + bxny3
              node_stencil(nn1)%dxndz(ioff2) = node_stencil(nn1)%dxndz(ioff2)  &
                                                                         + bxnz3

              node_stencil(nn1)%dyndx(ioff2) = node_stencil(nn1)%dyndx(ioff2)  &
                                                                         + bynx3
              node_stencil(nn1)%dyndy(ioff2) = node_stencil(nn1)%dyndy(ioff2)  &
                                                                         + byny3
              node_stencil(nn1)%dyndz(ioff2) = node_stencil(nn1)%dyndz(ioff2)  &
                                                                         + bynz3

              node_stencil(nn1)%dzndx(ioff2) = node_stencil(nn1)%dzndx(ioff2)  &
                                                                         + bznx3
              node_stencil(nn1)%dzndy(ioff2) = node_stencil(nn1)%dzndy(ioff2)  &
                                                                         + bzny3
              node_stencil(nn1)%dzndz(ioff2) = node_stencil(nn1)%dzndz(ioff2)  &
                                                                         + bznz3

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff2,k)=node_stencil(nn1)%dwdx(ioff2,k) &
                                                               + bfacespeedx3(k)
               node_stencil(nn1)%dwdy(ioff2,k)=node_stencil(nn1)%dwdy(ioff2,k) &
                                                               + bfacespeedy3(k)
               node_stencil(nn1)%dwdz(ioff2,k)=node_stencil(nn1)%dwdz(ioff2,k) &
                                                               + bfacespeedz3(k)
              end do
            endif

! Find and place the linearizations wrt nn3

            if ( node3 == cnode1 ) then
              node_stencil(nn1)%dxndx(ioff3) = node_stencil(nn1)%dxndx(ioff3)  &
                                                                         + bxnx1
              node_stencil(nn1)%dxndy(ioff3) = node_stencil(nn1)%dxndy(ioff3)  &
                                                                         + bxny1
              node_stencil(nn1)%dxndz(ioff3) = node_stencil(nn1)%dxndz(ioff3)  &
                                                                         + bxnz1

              node_stencil(nn1)%dyndx(ioff3) = node_stencil(nn1)%dyndx(ioff3)  &
                                                                         + bynx1
              node_stencil(nn1)%dyndy(ioff3) = node_stencil(nn1)%dyndy(ioff3)  &
                                                                         + byny1
              node_stencil(nn1)%dyndz(ioff3) = node_stencil(nn1)%dyndz(ioff3)  &
                                                                         + bynz1

              node_stencil(nn1)%dzndx(ioff3) = node_stencil(nn1)%dzndx(ioff3)  &
                                                                         + bznx1
              node_stencil(nn1)%dzndy(ioff3) = node_stencil(nn1)%dzndy(ioff3)  &
                                                                         + bzny1
              node_stencil(nn1)%dzndz(ioff3) = node_stencil(nn1)%dzndz(ioff3)  &
                                                                         + bznz1

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff3,k)=node_stencil(nn1)%dwdx(ioff3,k) &
                                                               + bfacespeedx1(k)
               node_stencil(nn1)%dwdy(ioff3,k)=node_stencil(nn1)%dwdy(ioff3,k) &
                                                               + bfacespeedy1(k)
               node_stencil(nn1)%dwdz(ioff3,k)=node_stencil(nn1)%dwdz(ioff3,k) &
                                                               + bfacespeedz1(k)
              end do
            else if ( node3 == cnode2 ) then
              node_stencil(nn1)%dxndx(ioff3) = node_stencil(nn1)%dxndx(ioff3)  &
                                                                         + bxnx2
              node_stencil(nn1)%dxndy(ioff3) = node_stencil(nn1)%dxndy(ioff3)  &
                                                                         + bxny2
              node_stencil(nn1)%dxndz(ioff3) = node_stencil(nn1)%dxndz(ioff3)  &
                                                                         + bxnz2

              node_stencil(nn1)%dyndx(ioff3) = node_stencil(nn1)%dyndx(ioff3)  &
                                                                         + bynx2
              node_stencil(nn1)%dyndy(ioff3) = node_stencil(nn1)%dyndy(ioff3)  &
                                                                         + byny2
              node_stencil(nn1)%dyndz(ioff3) = node_stencil(nn1)%dyndz(ioff3)  &
                                                                         + bynz2

              node_stencil(nn1)%dzndx(ioff3) = node_stencil(nn1)%dzndx(ioff3)  &
                                                                         + bznx2
              node_stencil(nn1)%dzndy(ioff3) = node_stencil(nn1)%dzndy(ioff3)  &
                                                                         + bzny2
              node_stencil(nn1)%dzndz(ioff3) = node_stencil(nn1)%dzndz(ioff3)  &
                                                                         + bznz2

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff3,k)=node_stencil(nn1)%dwdx(ioff3,k) &
                                                               + bfacespeedx2(k)
               node_stencil(nn1)%dwdy(ioff3,k)=node_stencil(nn1)%dwdy(ioff3,k) &
                                                               + bfacespeedy2(k)
               node_stencil(nn1)%dwdz(ioff3,k)=node_stencil(nn1)%dwdz(ioff3,k) &
                                                               + bfacespeedz2(k)
              end do
            else if ( node3 == cnode3 ) then
              node_stencil(nn1)%dxndx(ioff3) = node_stencil(nn1)%dxndx(ioff3)  &
                                                                         + bxnx3
              node_stencil(nn1)%dxndy(ioff3) = node_stencil(nn1)%dxndy(ioff3)  &
                                                                         + bxny3
              node_stencil(nn1)%dxndz(ioff3) = node_stencil(nn1)%dxndz(ioff3)  &
                                                                         + bxnz3

              node_stencil(nn1)%dyndx(ioff3) = node_stencil(nn1)%dyndx(ioff3)  &
                                                                         + bynx3
              node_stencil(nn1)%dyndy(ioff3) = node_stencil(nn1)%dyndy(ioff3)  &
                                                                         + byny3
              node_stencil(nn1)%dyndz(ioff3) = node_stencil(nn1)%dyndz(ioff3)  &
                                                                         + bynz3

              node_stencil(nn1)%dzndx(ioff3) = node_stencil(nn1)%dzndx(ioff3)  &
                                                                         + bznx3
              node_stencil(nn1)%dzndy(ioff3) = node_stencil(nn1)%dzndy(ioff3)  &
                                                                         + bzny3
              node_stencil(nn1)%dzndz(ioff3) = node_stencil(nn1)%dzndz(ioff3)  &
                                                                         + bznz3

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff3,k)=node_stencil(nn1)%dwdx(ioff3,k) &
                                                               + bfacespeedx3(k)
               node_stencil(nn1)%dwdy(ioff3,k)=node_stencil(nn1)%dwdy(ioff3,k) &
                                                               + bfacespeedy3(k)
               node_stencil(nn1)%dwdz(ioff3,k)=node_stencil(nn1)%dwdz(ioff3,k) &
                                                               + bfacespeedz3(k)
              end do
            endif

          endif local_node

        end do tria_node_loop

      end do tria_face_loop

! Now the quads

      quad_face_loop : do face = 1, nbfaceq

        n1 = f2nqb(face,1)
        n2 = f2nqb(face,2)
        n3 = f2nqb(face,3)
        n4 = f2nqb(face,4)

        node1 = ibnode(n1)
        node2 = ibnode(n2)
        node3 = ibnode(n3)
        node4 = ibnode(n4)

        cnode1 = node1
        cnode2 = node2
        cnode3 = node3
        cnode4 = node4

        xorig1 = x(node1)
          xorig1x1 = my_1
          xorig1x2 = my_0
          xorig1x3 = my_0
          xorig1x4 = my_0

        yorig1 = y(node1)
          yorig1y1 = my_1
          yorig1y2 = my_0
          yorig1y3 = my_0
          yorig1y4 = my_0

        zorig1 = z(node1)
          zorig1z1 = my_1
          zorig1z2 = my_0
          zorig1z3 = my_0
          zorig1z4 = my_0

        xorig2 = x(node2)
          xorig2x1 = my_0
          xorig2x2 = my_1
          xorig2x3 = my_0
          xorig2x4 = my_0

        yorig2 = y(node2)
          yorig2y1 = my_0
          yorig2y2 = my_1
          yorig2y3 = my_0
          yorig2y4 = my_0

        zorig2 = z(node2)
          zorig2z1 = my_0
          zorig2z2 = my_1
          zorig2z3 = my_0
          zorig2z4 = my_0

        xorig3 = x(node3)
          xorig3x1 = my_0
          xorig3x2 = my_0
          xorig3x3 = my_1
          xorig3x4 = my_0

        yorig3 = y(node3)
          yorig3y1 = my_0
          yorig3y2 = my_0
          yorig3y3 = my_1
          yorig3y4 = my_0

        zorig3 = z(node3)
          zorig3z1 = my_0
          zorig3z2 = my_0
          zorig3z3 = my_1
          zorig3z4 = my_0

        xorig4 = x(node4)
          xorig4x1 = my_0
          xorig4x2 = my_0
          xorig4x3 = my_0
          xorig4x4 = my_1

        yorig4 = y(node4)
          yorig4y1 = my_0
          yorig4y2 = my_0
          yorig4y3 = my_0
          yorig4y4 = my_1

        zorig4 = z(node4)
          zorig4z1 = my_0
          zorig4z2 = my_0
          zorig4z3 = my_0
          zorig4z4 = my_1

!    face center (avg of points on face) referenced to local origins

        xc(1) = ((x(node1)-xorig1) + (x(node2)-xorig1) + (x(node3)-xorig1)     &
               + (x(node4)-xorig1))/4._dp
          xcx1(1) = (-my_1-my_1-my_1)/my_4
          xcx2(1) = my_1/my_4
          xcx3(1) = my_1/my_4
          xcx4(1) = my_1/my_4

        yc(1) = ((y(node1)-yorig1) + (y(node2)-yorig1) + (y(node3)-yorig1)     &
               + (y(node4)-yorig1))/4._dp
          ycy1(1) = (-my_1-my_1-my_1)/my_4
          ycy2(1) = my_1/my_4
          ycy3(1) = my_1/my_4
          ycy4(1) = my_1/my_4

        zc(1) = ((z(node1)-zorig1) + (z(node2)-zorig1) + (z(node3)-zorig1)     &
               + (z(node4)-zorig1))/4._dp
          zcz1(1) = (-my_1-my_1-my_1)/my_4
          zcz2(1) = my_1/my_4
          zcz3(1) = my_1/my_4
          zcz4(1) = my_1/my_4

        xc(2) = ((x(node1)-xorig2) + (x(node2)-xorig2) + (x(node3)-xorig2)     &
               + (x(node4)-xorig2))/4._dp
          xcx1(2) = my_1/my_4
          xcx2(2) = (-my_1-my_1-my_1)/my_4
          xcx3(2) = my_1/my_4
          xcx4(2) = my_1/my_4

        yc(2) = ((y(node1)-yorig2) + (y(node2)-yorig2) + (y(node3)-yorig2)     &
               + (y(node4)-yorig2))/4._dp
          ycy1(2) = my_1/my_4
          ycy2(2) = (-my_1-my_1-my_1)/my_4
          ycy3(2) = my_1/my_4
          ycy4(2) = my_1/my_4

        zc(2) = ((z(node1)-zorig2) + (z(node2)-zorig2) + (z(node3)-zorig2)     &
               + (z(node4)-zorig2))/4._dp
          zcz1(2) = my_1/my_4
          zcz2(2) = (-my_1-my_1-my_1)/my_4
          zcz3(2) = my_1/my_4
          zcz4(2) = my_1/my_4

        xc(3) = ((x(node1)-xorig3) + (x(node2)-xorig3) + (x(node3)-xorig3)     &
               + (x(node4)-xorig3))/4._dp
          xcx1(3) = my_1/my_4
          xcx2(3) = my_1/my_4
          xcx3(3) = (-my_1-my_1-my_1)/my_4
          xcx4(3) = my_1/my_4

        yc(3) = ((y(node1)-yorig3) + (y(node2)-yorig3) + (y(node3)-yorig3)     &
               + (y(node4)-yorig3))/4._dp
          ycy1(3) = my_1/my_4
          ycy2(3) = my_1/my_4
          ycy3(3) = (-my_1-my_1-my_1)/my_4
          ycy4(3) = my_1/my_4

        zc(3) = ((z(node1)-zorig3) + (z(node2)-zorig3) + (z(node3)-zorig3)     &
               + (z(node4)-zorig3))/4._dp
          zcz1(3) = my_1/my_4
          zcz2(3) = my_1/my_4
          zcz3(3) = (-my_1-my_1-my_1)/my_4
          zcz4(3) = my_1/my_4

        xc(4) = ((x(node1)-xorig4) + (x(node2)-xorig4) + (x(node3)-xorig4)     &
               + (x(node4)-xorig4))/4._dp
          xcx1(4) = my_1/my_4
          xcx2(4) = my_1/my_4
          xcx3(4) = my_1/my_4
          xcx4(4) = (-my_1-my_1-my_1)/my_4

        yc(4) = ((y(node1)-yorig4) + (y(node2)-yorig4) + (y(node3)-yorig4)     &
               + (y(node4)-yorig4))/4._dp
          ycy1(4) = my_1/my_4
          ycy2(4) = my_1/my_4
          ycy3(4) = my_1/my_4
          ycy4(4) = (-my_1-my_1-my_1)/my_4

        zc(4) = ((z(node1)-zorig4) + (z(node2)-zorig4) + (z(node3)-zorig4)     &
               + (z(node4)-zorig4))/4._dp
          zcz1(4) = my_1/my_4
          zcz2(4) = my_1/my_4
          zcz3(4) = my_1/my_4
          zcz4(4) = (-my_1-my_1-my_1)/my_4


        dxdtc = (bdxdt(n1) + bdxdt(n2) + bdxdt(n3) + bdxdt(n4))/4._dp
          do i = 1, ntp
            dxdtcx1(i) = time_coeff(i)/my_4
            dxdtcx2(i) = time_coeff(i)/my_4
            dxdtcx3(i) = time_coeff(i)/my_4
            dxdtcx4(i) = time_coeff(i)/my_4
          end do

        dydtc = (bdydt(n1) + bdydt(n2) + bdydt(n3) + bdydt(n4))/4._dp
          do i = 1, ntp
            dydtcy1(i) = time_coeff(i)/my_4
            dydtcy2(i) = time_coeff(i)/my_4
            dydtcy3(i) = time_coeff(i)/my_4
            dydtcy4(i) = time_coeff(i)/my_4
          end do

        dzdtc = (bdzdt(n1) + bdzdt(n2) + bdzdt(n3) + bdzdt(n4))/4._dp
          do i = 1, ntp
            dzdtcz1(i) = time_coeff(i)/my_4
            dzdtcz2(i) = time_coeff(i)/my_4
            dzdtcz3(i) = time_coeff(i)/my_4
            dzdtcz4(i) = time_coeff(i)/my_4
          end do

        quad_node_loop : do nn = 1, 4

          bxnx1 = 0.0_dp
          bxnx2 = 0.0_dp
          bxnx3 = 0.0_dp
          bxnx4 = 0.0_dp
          bxny1 = 0.0_dp
          bxny2 = 0.0_dp
          bxny3 = 0.0_dp
          bxny4 = 0.0_dp
          bxnz1 = 0.0_dp
          bxnz2 = 0.0_dp
          bxnz3 = 0.0_dp
          bxnz4 = 0.0_dp

          bynx1 = 0.0_dp
          bynx2 = 0.0_dp
          bynx3 = 0.0_dp
          bynx4 = 0.0_dp
          byny1 = 0.0_dp
          byny2 = 0.0_dp
          byny3 = 0.0_dp
          byny4 = 0.0_dp
          bynz1 = 0.0_dp
          bynz2 = 0.0_dp
          bynz3 = 0.0_dp
          bynz4 = 0.0_dp

          bznx1 = 0.0_dp
          bznx2 = 0.0_dp
          bznx3 = 0.0_dp
          bznx4 = 0.0_dp
          bzny1 = 0.0_dp
          bzny2 = 0.0_dp
          bzny3 = 0.0_dp
          bzny4 = 0.0_dp
          bznz1 = 0.0_dp
          bznz2 = 0.0_dp
          bznz3 = 0.0_dp
          bznz4 = 0.0_dp

          do i = 1, ntp
            bfacespeedx1(i) = 0.0_dp
            bfacespeedx2(i) = 0.0_dp
            bfacespeedx3(i) = 0.0_dp
            bfacespeedx4(i) = 0.0_dp

            bfacespeedy1(i) = 0.0_dp
            bfacespeedy2(i) = 0.0_dp
            bfacespeedy3(i) = 0.0_dp
            bfacespeedy4(i) = 0.0_dp

            bfacespeedz1(i) = 0.0_dp
            bfacespeedz2(i) = 0.0_dp
            bfacespeedz3(i) = 0.0_dp
            bfacespeedz4(i) = 0.0_dp
          end do

          select case(nn)
            case(1)
              nn1 = n1
              nn2 = n2
              nn3 = n3
              nn4 = n4
              xorig = xorig1
                xorigx1 = xorig1x1
                xorigx2 = xorig1x2
                xorigx3 = xorig1x3
                xorigx4 = xorig1x4
              yorig = yorig1
                yorigy1 = yorig1y1
                yorigy2 = yorig1y2
                yorigy3 = yorig1y3
                yorigy4 = yorig1y4
              zorig = zorig1
                zorigz1 = zorig1z1
                zorigz2 = zorig1z2
                zorigz3 = zorig1z3
                zorigz4 = zorig1z4
              xc_ref = xc(1)
                xc_refx1 = xcx1(1)
                xc_refx2 = xcx2(1)
                xc_refx3 = xcx3(1)
                xc_refx4 = xcx4(1)
              yc_ref = yc(1)
                yc_refy1 = ycy1(1)
                yc_refy2 = ycy2(1)
                yc_refy3 = ycy3(1)
                yc_refy4 = ycy4(1)
              zc_ref = zc(1)
                zc_refz1 = zcz1(1)
                zc_refz2 = zcz2(1)
                zc_refz3 = zcz3(1)
                zc_refz4 = zcz4(1)
            case(2)
              nn1 = n2
              nn2 = n3
              nn3 = n4
              nn4 = n1
              xorig = xorig2
                xorigx1 = xorig2x1
                xorigx2 = xorig2x2
                xorigx3 = xorig2x3
                xorigx4 = xorig2x4
              yorig = yorig2
                yorigy1 = yorig2y1
                yorigy2 = yorig2y2
                yorigy3 = yorig2y3
                yorigy4 = yorig2y4
              zorig = zorig2
                zorigz1 = zorig2z1
                zorigz2 = zorig2z2
                zorigz3 = zorig2z3
                zorigz4 = zorig2z4
              xc_ref = xc(2)
                xc_refx1 = xcx1(2)
                xc_refx2 = xcx2(2)
                xc_refx3 = xcx3(2)
                xc_refx4 = xcx4(2)
              yc_ref = yc(2)
                yc_refy1 = ycy1(2)
                yc_refy2 = ycy2(2)
                yc_refy3 = ycy3(2)
                yc_refy4 = ycy4(2)
              zc_ref = zc(2)
                zc_refz1 = zcz1(2)
                zc_refz2 = zcz2(2)
                zc_refz3 = zcz3(2)
                zc_refz4 = zcz4(2)
            case(3)
              nn1 = n3
              nn2 = n4
              nn3 = n1
              nn4 = n2
              xorig = xorig3
                xorigx1 = xorig3x1
                xorigx2 = xorig3x2
                xorigx3 = xorig3x3
                xorigx4 = xorig3x4
              yorig = yorig3
                yorigy1 = yorig3y1
                yorigy2 = yorig3y2
                yorigy3 = yorig3y3
                yorigy4 = yorig3y4
              zorig = zorig3
                zorigz1 = zorig3z1
                zorigz2 = zorig3z2
                zorigz3 = zorig3z3
                zorigz4 = zorig3z4
              xc_ref = xc(3)
                xc_refx1 = xcx1(3)
                xc_refx2 = xcx2(3)
                xc_refx3 = xcx3(3)
                xc_refx4 = xcx4(3)
              yc_ref = yc(3)
                yc_refy1 = ycy1(3)
                yc_refy2 = ycy2(3)
                yc_refy3 = ycy3(3)
                yc_refy4 = ycy4(3)
              zc_ref = zc(3)
                zc_refz1 = zcz1(3)
                zc_refz2 = zcz2(3)
                zc_refz3 = zcz3(3)
                zc_refz4 = zcz4(3)
            case(4)
              nn1 = n4
              nn2 = n1
              nn3 = n2
              nn4 = n3
              xorig = xorig4
                xorigx1 = xorig4x1
                xorigx2 = xorig4x2
                xorigx3 = xorig4x3
                xorigx4 = xorig4x4
              yorig = yorig4
                yorigy1 = yorig4y1
                yorigy2 = yorig4y2
                yorigy3 = yorig4y3
                yorigy4 = yorig4y4
              zorig = zorig4
                zorigz1 = zorig4z1
                zorigz2 = zorig4z2
                zorigz3 = zorig4z3
                zorigz4 = zorig4z4
              xc_ref = xc(4)
                xc_refx1 = xcx1(4)
                xc_refx2 = xcx2(4)
                xc_refx3 = xcx3(4)
                xc_refx4 = xcx4(4)
              yc_ref = yc(4)
                yc_refy1 = ycy1(4)
                yc_refy2 = ycy2(4)
                yc_refy3 = ycy3(4)
                yc_refy4 = ycy4(4)
              zc_ref = zc(4)
                zc_refz1 = zcz1(4)
                zc_refz2 = zcz2(4)
                zc_refz3 = zcz3(4)
                zc_refz4 = zcz4(4)
            case default
              nn1 = 0; nn2 = 0; nn3 = 0; nn4 = 0 ! avoid uninitialized warnings
          end select

          node1 = ibnode(nn1)
          node2 = ibnode(nn2)
          node3 = ibnode(nn3)
          node4 = ibnode(nn4)

          x1 = x(node1)
            if ( node1 == cnode1 ) then
              x1x1 = my_1
              x1x2 = my_0
              x1x3 = my_0
              x1x4 = my_0
            else if ( node1 == cnode2 ) then
              x1x1 = my_0
              x1x2 = my_1
              x1x3 = my_0
              x1x4 = my_0
            else if ( node1 == cnode3 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_1
              x1x4 = my_0
            else if ( node1 == cnode4 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_0
              x1x4 = my_1
            endif

          x2 = x(node2)
            if ( node2 == cnode1 ) then
              x2x1 = my_1
              x2x2 = my_0
              x2x3 = my_0
              x2x4 = my_0
            else if ( node2 == cnode2 ) then
              x2x1 = my_0
              x2x2 = my_1
              x2x3 = my_0
              x2x4 = my_0
            else if ( node2 == cnode3 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_1
              x2x4 = my_0
            else if ( node2 == cnode4 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_0
              x2x4 = my_1
            endif

          x3 = x(node3)
            if ( node3 == cnode1 ) then
              x3x1 = my_1
              x3x2 = my_0
              x3x3 = my_0
            else if ( node3 == cnode2 ) then
              x3x1 = my_0
              x3x2 = my_1
              x3x3 = my_0
            else if ( node3 == cnode3 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_1
            else if ( node3 == cnode4 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_0
            endif

          x4 = x(node4)
            if ( node4 == cnode1 ) then
              x4x1 = my_1
              x4x2 = my_0
              x4x3 = my_0
              x4x4 = my_0
            else if ( node4 == cnode2 ) then
              x4x1 = my_0
              x4x2 = my_1
              x4x3 = my_0
              x4x4 = my_0
            else if ( node4 == cnode3 ) then
              x4x1 = my_0
              x4x2 = my_0
              x4x3 = my_1
              x4x4 = my_0
            else if ( node4 == cnode4 ) then
              x4x1 = my_0
              x4x2 = my_0
              x4x3 = my_0
              x4x4 = my_1
            endif








          y1 = y(node1)
            if ( node1 == cnode1 ) then
              y1y1 = my_1
              y1y2 = my_0
              y1y3 = my_0
              y1y4 = my_0
            else if ( node1 == cnode2 ) then
              y1y1 = my_0
              y1y2 = my_1
              y1y3 = my_0
              y1y4 = my_0
            else if ( node1 == cnode3 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_1
              y1y4 = my_0
            else if ( node1 == cnode4 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_0
              y1y4 = my_1
            endif

          y2 = y(node2)
            if ( node2 == cnode1 ) then
              y2y1 = my_1
              y2y2 = my_0
              y2y3 = my_0
              y2y4 = my_0
            else if ( node2 == cnode2 ) then
              y2y1 = my_0
              y2y2 = my_1
              y2y3 = my_0
              y2y4 = my_0
            else if ( node2 == cnode3 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_1
              y2y4 = my_0
            else if ( node2 == cnode4 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_0
              y2y4 = my_1
            endif

          y3 = y(node3)
            if ( node3 == cnode1 ) then
              y3y1 = my_1
              y3y2 = my_0
              y3y3 = my_0
            else if ( node3 == cnode2 ) then
              y3y1 = my_0
              y3y2 = my_1
              y3y3 = my_0
            else if ( node3 == cnode3 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_1
            else if ( node3 == cnode4 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_0
            endif

          y4 = y(node4)
            if ( node4 == cnode1 ) then
              y4y1 = my_1
              y4y2 = my_0
              y4y3 = my_0
              y4y4 = my_0
            else if ( node4 == cnode2 ) then
              y4y1 = my_0
              y4y2 = my_1
              y4y3 = my_0
              y4y4 = my_0
            else if ( node4 == cnode3 ) then
              y4y1 = my_0
              y4y2 = my_0
              y4y3 = my_1
              y4y4 = my_0
            else if ( node4 == cnode4 ) then
              y4y1 = my_0
              y4y2 = my_0
              y4y3 = my_0
              y4y4 = my_1
            endif









          z1 = z(node1)
            if ( node1 == cnode1 ) then
              z1z1 = my_1
              z1z2 = my_0
              z1z3 = my_0
              z1z4 = my_0
            else if ( node1 == cnode2 ) then
              z1z1 = my_0
              z1z2 = my_1
              z1z3 = my_0
              z1z4 = my_0
            else if ( node1 == cnode3 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_1
              z1z4 = my_0
            else if ( node1 == cnode4 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_0
              z1z4 = my_1
            endif

          z2 = z(node2)
            if ( node2 == cnode1 ) then
              z2z1 = my_1
              z2z2 = my_0
              z2z3 = my_0
              z2z4 = my_0
            else if ( node2 == cnode2 ) then
              z2z1 = my_0
              z2z2 = my_1
              z2z3 = my_0
              z2z4 = my_0
            else if ( node2 == cnode3 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_1
              z2z4 = my_0
            else if ( node2 == cnode4 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_0
              z2z4 = my_1
            endif

          z3 = z(node3)
            if ( node3 == cnode1 ) then
              z3z1 = my_1
              z3z2 = my_0
              z3z3 = my_0
            else if ( node3 == cnode2 ) then
              z3z1 = my_0
              z3z2 = my_1
              z3z3 = my_0
            else if ( node3 == cnode3 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_1
            else if ( node3 == cnode4 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_0
            endif

          z4 = z(node4)
            if ( node4 == cnode1 ) then
              z4z1 = my_1
              z4z2 = my_0
              z4z3 = my_0
              z4z4 = my_0
            else if ( node4 == cnode2 ) then
              z4z1 = my_0
              z4z2 = my_1
              z4z3 = my_0
              z4z4 = my_0
            else if ( node4 == cnode3 ) then
              z4z1 = my_0
              z4z2 = my_0
              z4z3 = my_1
              z4z4 = my_0
            else if ( node4 == cnode4 ) then
              z4z1 = my_0
              z4z2 = my_0
              z4z3 = my_0
              z4z4 = my_1
            endif




          x0 = x1 - xorig
            x0x1 = x1x1 - xorigx1
            x0x2 = x1x2 - xorigx2
            x0x3 = x1x3 - xorigx3
            x0x4 = x1x4 - xorigx4
          y0 = y1 - yorig
            y0y1 = y1y1 - yorigy1
            y0y2 = y1y2 - yorigy2
            y0y3 = y1y3 - yorigy3
            y0y4 = y1y4 - yorigy4
          z0 = z1 - zorig
            z0z1 = z1z1 - zorigz1
            z0z2 = z1z2 - zorigz2
            z0z3 = z1z3 - zorigz3
            z0z4 = z1z4 - zorigz4

          dxdt0 = bdxdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                dxdt0x1(i) = time_coeff(i)
                dxdt0x2(i) = my_0
                dxdt0x3(i) = my_0
                dxdt0x4(i) = my_0
              else if ( node1 == cnode2 ) then
                dxdt0x1(i) = my_0
                dxdt0x2(i) = time_coeff(i)
                dxdt0x3(i) = my_0
                dxdt0x4(i) = my_0
              else if ( node1 == cnode3 ) then
                dxdt0x1(i) = my_0
                dxdt0x2(i) = my_0
                dxdt0x3(i) = time_coeff(i)
                dxdt0x4(i) = my_0
              else if ( node1 == cnode4 ) then
                dxdt0x1(i) = my_0
                dxdt0x2(i) = my_0
                dxdt0x3(i) = my_0
                dxdt0x4(i) = time_coeff(i)
              endif
            end do

          dydt0 = bdydt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                dydt0y1(i) = time_coeff(i)
                dydt0y2(i) = my_0
                dydt0y3(i) = my_0
                dydt0y4(i) = my_0
              else if ( node1 == cnode2 ) then
                dydt0y1(i) = my_0
                dydt0y2(i) = time_coeff(i)
                dydt0y3(i) = my_0
                dydt0y4(i) = my_0
              else if ( node1 == cnode3 ) then
                dydt0y1(i) = my_0
                dydt0y2(i) = my_0
                dydt0y3(i) = time_coeff(i)
                dydt0y4(i) = my_0
              else if ( node1 == cnode4 ) then
                dydt0y1(i) = my_0
                dydt0y2(i) = my_0
                dydt0y3(i) = my_0
                dydt0y4(i) = time_coeff(i)
              endif
            end do

          dzdt0 = bdzdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                dzdt0z1(i) = time_coeff(i)
                dzdt0z2(i) = my_0
                dzdt0z3(i) = my_0
                dzdt0z4(i) = my_0
              else if ( node1 == cnode2 ) then
                dzdt0z1(i) = my_0
                dzdt0z2(i) = time_coeff(i)
                dzdt0z3(i) = my_0
                dzdt0z4(i) = my_0
              else if ( node1 == cnode3 ) then
                dzdt0z1(i) = my_0
                dzdt0z2(i) = my_0
                dzdt0z3(i) = time_coeff(i)
                dzdt0z4(i) = my_0
              else if ( node1 == cnode4 ) then
                dzdt0z1(i) = my_0
                dzdt0z2(i) = my_0
                dzdt0z3(i) = my_0
                dzdt0z4(i) = time_coeff(i)
              endif
            end do

          xl = ((x1-xorig) + (x4-xorig))/my_2
            xlx1 = ((x1x1-xorigx1) + (x4x1-xorigx1))/my_2
            xlx2 = ((x1x2-xorigx2) + (x4x2-xorigx2))/my_2
            xlx3 = ((x1x3-xorigx3) + (x4x3-xorigx3))/my_2
            xlx4 = ((x1x4-xorigx4) + (x4x4-xorigx4))/my_2

          yl = ((y1-yorig) + (y4-yorig))/my_2
            yly1 = ((y1y1-yorigy1) + (y4y1-yorigy1))/my_2
            yly2 = ((y1y2-yorigy2) + (y4y2-yorigy2))/my_2
            yly3 = ((y1y3-yorigy3) + (y4y3-yorigy3))/my_2
            yly4 = ((y1y4-yorigy4) + (y4y4-yorigy4))/my_2

          zl = ((z1-zorig) + (z4-zorig))/my_2
            zlz1 = ((z1z1-zorigz1) + (z4z1-zorigz1))/my_2
            zlz2 = ((z1z2-zorigz2) + (z4z2-zorigz2))/my_2
            zlz3 = ((z1z3-zorigz3) + (z4z3-zorigz3))/my_2
            zlz4 = ((z1z4-zorigz4) + (z4z4-zorigz4))/my_2

          piece1 = bdxdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1x1(i) = time_coeff(i)
                piece1x2(i) = my_0
                piece1x3(i) = my_0
                piece1x4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1x1(i) = my_0
                piece1x2(i) = time_coeff(i)
                piece1x3(i) = my_0
                piece1x4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = time_coeff(i)
                piece1x4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = my_0
                piece1x4(i) = time_coeff(i)
              endif
            end do

          piece2 = bdxdt(nn4)
            do i = 1, ntp
              if ( node4 == cnode1 ) then
                piece2x1(i) = time_coeff(i)
                piece2x2(i) = my_0
                piece2x3(i) = my_0
                piece2x4(i) = my_0
              else if ( node4 == cnode2 ) then
                piece2x1(i) = my_0
                piece2x2(i) = time_coeff(i)
                piece2x3(i) = my_0
                piece2x4(i) = my_0
              else if ( node4 == cnode3 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = time_coeff(i)
                piece2x4(i) = my_0
              else if ( node4 == cnode4 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = my_0
                piece2x4(i) = time_coeff(i)
              endif
            end do

          dxdtl = (piece1 + piece2)/my_2
            do i = 1, ntp
              dxdtlx1(i) = (piece1x1(i) + piece2x1(i))/my_2
              dxdtlx2(i) = (piece1x2(i) + piece2x2(i))/my_2
              dxdtlx3(i) = (piece1x3(i) + piece2x3(i))/my_2
              dxdtlx4(i) = (piece1x4(i) + piece2x4(i))/my_2
            end do

          piece1 = bdydt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1y1(i) = time_coeff(i)
                piece1y2(i) = my_0
                piece1y3(i) = my_0
                piece1y4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1y1(i) = my_0
                piece1y2(i) = time_coeff(i)
                piece1y3(i) = my_0
                piece1y4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = time_coeff(i)
                piece1y4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = my_0
                piece1y4(i) = time_coeff(i)
              endif
            end do

          piece2 = bdydt(nn4)
            do i = 1, ntp
              if ( node4 == cnode1 ) then
                piece2y1(i) = time_coeff(i)
                piece2y2(i) = my_0
                piece2y3(i) = my_0
                piece2y4(i) = my_0
              else if ( node4 == cnode2 ) then
                piece2y1(i) = my_0
                piece2y2(i) = time_coeff(i)
                piece2y3(i) = my_0
                piece2y4(i) = my_0
              else if ( node4 == cnode3 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = time_coeff(i)
                piece2y4(i) = my_0
              else if ( node4 == cnode4 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = my_0
                piece2y4(i) = time_coeff(i)
              endif
            end do

          dydtl = (piece1 + piece2)/my_2
            do i = 1, ntp
              dydtly1(i) = (piece1y1(i) + piece2y1(i))/my_2
              dydtly2(i) = (piece1y2(i) + piece2y2(i))/my_2
              dydtly3(i) = (piece1y3(i) + piece2y3(i))/my_2
              dydtly4(i) = (piece1y4(i) + piece2y4(i))/my_2
            end do

          piece1 = bdzdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1z1(i) = time_coeff(i)
                piece1z2(i) = my_0
                piece1z3(i) = my_0
                piece1z4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1z1(i) = my_0
                piece1z2(i) = time_coeff(i)
                piece1z3(i) = my_0
                piece1z4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = time_coeff(i)
                piece1z4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = my_0
                piece1z4(i) = time_coeff(i)
              endif
            end do

          piece2 = bdzdt(nn4)
            do i = 1, ntp
              if ( node4 == cnode1 ) then
                piece2z1(i) = time_coeff(i)
                piece2z2(i) = my_0
                piece2z3(i) = my_0
                piece2z4(i) = my_0
              else if ( node4 == cnode2 ) then
                piece2z1(i) = my_0
                piece2z2(i) = time_coeff(i)
                piece2z3(i) = my_0
                piece2z4(i) = my_0
              else if ( node4 == cnode3 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = time_coeff(i)
                piece2z4(i) = my_0
              else if ( node4 == cnode4 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = my_0
                piece2z4(i) = time_coeff(i)
              endif
            end do

          dzdtl = (piece1 + piece2)/my_2
            do i = 1, ntp
              dzdtlz1(i) = (piece1z1(i) + piece2z1(i))/my_2
              dzdtlz2(i) = (piece1z2(i) + piece2z2(i))/my_2
              dzdtlz3(i) = (piece1z3(i) + piece2z3(i))/my_2
              dzdtlz4(i) = (piece1z4(i) + piece2z4(i))/my_2
            end do

          xr = ((x1-xorig) + (x2-xorig))/my_2
            xrx1 = ((x1x1-xorigx1) + (x2x1-xorigx1))/my_2
            xrx2 = ((x1x2-xorigx2) + (x2x2-xorigx2))/my_2
            xrx3 = ((x1x3-xorigx3) + (x2x3-xorigx3))/my_2
            xrx4 = ((x1x4-xorigx4) + (x2x4-xorigx4))/my_2

          yr = ((y1-yorig) + (y2-yorig))/my_2
            yry1 = ((y1y1-yorigy1) + (y2y1-yorigy1))/my_2
            yry2 = ((y1y2-yorigy2) + (y2y2-yorigy2))/my_2
            yry3 = ((y1y3-yorigy3) + (y2y3-yorigy3))/my_2
            yry4 = ((y1y4-yorigy4) + (y2y4-yorigy4))/my_2

          zr = ((z1-zorig) + (z2-zorig))/my_2
            zrz1 = ((z1z1-zorigz1) + (z2z1-zorigz1))/my_2
            zrz2 = ((z1z2-zorigz2) + (z2z2-zorigz2))/my_2
            zrz3 = ((z1z3-zorigz3) + (z2z3-zorigz3))/my_2
            zrz4 = ((z1z4-zorigz4) + (z2z4-zorigz4))/my_2

          piece1 = bdxdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1x1(i) = time_coeff(i)
                piece1x2(i) = my_0
                piece1x3(i) = my_0
                piece1x4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1x1(i) = my_0
                piece1x2(i) = time_coeff(i)
                piece1x3(i) = my_0
                piece1x4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = time_coeff(i)
                piece1x4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = my_0
                piece1x4(i) = time_coeff(i)
              endif
            end do

          piece2 = bdxdt(nn2)
            do i = 1, ntp
              if ( node2 == cnode1 ) then
                piece2x1(i) = time_coeff(i)
                piece2x2(i) = my_0
                piece2x3(i) = my_0
                piece2x4(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2x1(i) = my_0
                piece2x2(i) = time_coeff(i)
                piece2x3(i) = my_0
                piece2x4(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = time_coeff(i)
                piece2x4(i) = my_0
              else if ( node2 == cnode4 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = my_0
                piece2x4(i) = time_coeff(i)
              endif
            end do

          dxdtr = (piece1 + piece2)/my_2
            do i = 1, ntp
              dxdtrx1(i) = (piece1x1(i) + piece2x1(i))/my_2
              dxdtrx2(i) = (piece1x2(i) + piece2x2(i))/my_2
              dxdtrx3(i) = (piece1x3(i) + piece2x3(i))/my_2
              dxdtrx4(i) = (piece1x4(i) + piece2x4(i))/my_2
            end do

          piece1 = bdydt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1y1(i) = time_coeff(i)
                piece1y2(i) = my_0
                piece1y3(i) = my_0
                piece1y4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1y1(i) = my_0
                piece1y2(i) = time_coeff(i)
                piece1y3(i) = my_0
                piece1y4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = time_coeff(i)
                piece1y4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = my_0
                piece1y4(i) = time_coeff(i)
              endif
            end do

          piece2 = bdydt(nn2)
            do i = 1, ntp
              if ( node2 == cnode1 ) then
                piece2y1(i) = time_coeff(i)
                piece2y2(i) = my_0
                piece2y3(i) = my_0
                piece2y4(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2y1(i) = my_0
                piece2y2(i) = time_coeff(i)
                piece2y3(i) = my_0
                piece2y4(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = time_coeff(i)
                piece2y4(i) = my_0
              else if ( node2 == cnode4 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = my_0
                piece2y4(i) = time_coeff(i)
              endif
            end do

          dydtr = (piece1 + piece2)/my_2
            do i = 1, ntp
              dydtry1(i) = (piece1y1(i) + piece2y1(i))/my_2
              dydtry2(i) = (piece1y2(i) + piece2y2(i))/my_2
              dydtry3(i) = (piece1y3(i) + piece2y3(i))/my_2
              dydtry4(i) = (piece1y4(i) + piece2y4(i))/my_2
            end do

          piece1 = bdzdt(nn1)
            do i = 1, ntp
              if ( node1 == cnode1 ) then
                piece1z1(i) = time_coeff(i)
                piece1z2(i) = my_0
                piece1z3(i) = my_0
                piece1z4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1z1(i) = my_0
                piece1z2(i) = time_coeff(i)
                piece1z3(i) = my_0
                piece1z4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = time_coeff(i)
                piece1z4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = my_0
                piece1z4(i) = time_coeff(i)
              endif
            end do

          piece2 = bdzdt(nn2)
            do i = 1, ntp
              if ( node2 == cnode1 ) then
                piece2z1(i) = time_coeff(i)
                piece2z2(i) = my_0
                piece2z3(i) = my_0
                piece2z4(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2z1(i) = my_0
                piece2z2(i) = time_coeff(i)
                piece2z3(i) = my_0
                piece2z4(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = time_coeff(i)
                piece2z4(i) = my_0
              else if ( node2 == cnode4 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = my_0
                piece2z4(i) = time_coeff(i)
              endif
            end do

          dzdtr = (piece1 + piece2)/my_2
            do i = 1, ntp
              dzdtrz1(i) = (piece1z1(i) + piece2z1(i))/my_2
              dzdtrz2(i) = (piece1z2(i) + piece2z2(i))/my_2
              dzdtrz3(i) = (piece1z3(i) + piece2z3(i))/my_2
              dzdtrz4(i) = (piece1z4(i) + piece2z4(i))/my_2
            end do

!       triangle x0-xr-xc

          areax = my_half*( (yr-y0)*(zc_ref-z0) - (zr-z0)*(yc_ref-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0
            areaxx4 = my_0

            areaxy1 = my_half*((yry1-y0y1)*(zc_ref-z0)- (zr-z0)*(yc_refy1-y0y1))
            areaxy2 = my_half*((yry2-y0y2)*(zc_ref-z0)- (zr-z0)*(yc_refy2-y0y2))
            areaxy3 = my_half*((yry3-y0y3)*(zc_ref-z0)- (zr-z0)*(yc_refy3-y0y3))
            areaxy4 = my_half*((yry4-y0y4)*(zc_ref-z0)- (zr-z0)*(yc_refy4-y0y4))

            areaxz1 = my_half*((yr-y0)*(zc_refz1-z0z1)- (zrz1-z0z1)*(yc_ref-y0))
            areaxz2 = my_half*((yr-y0)*(zc_refz2-z0z2)- (zrz2-z0z2)*(yc_ref-y0))
            areaxz3 = my_half*((yr-y0)*(zc_refz3-z0z3)- (zrz3-z0z3)*(yc_ref-y0))
            areaxz4 = my_half*((yr-y0)*(zc_refz4-z0z4)- (zrz4-z0z4)*(yc_ref-y0))

          areay = my_half*( (zr-z0)*(xc_ref-x0) - (xr-x0)*(zc_ref-z0) )
            areayx1 = my_half*((zr-z0)*(xc_refx1-x0x1)- (xrx1-x0x1)*(zc_ref-z0))
            areayx2 = my_half*((zr-z0)*(xc_refx2-x0x2)- (xrx2-x0x2)*(zc_ref-z0))
            areayx3 = my_half*((zr-z0)*(xc_refx3-x0x3)- (xrx3-x0x3)*(zc_ref-z0))
            areayx4 = my_half*((zr-z0)*(xc_refx4-x0x4)- (xrx4-x0x4)*(zc_ref-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0
            areayy4 = my_0

            areayz1 = my_half*((zrz1-z0z1)*(xc_ref-x0)- (xr-x0)*(zc_refz1-z0z1))
            areayz2 = my_half*((zrz2-z0z2)*(xc_ref-x0)- (xr-x0)*(zc_refz2-z0z2))
            areayz3 = my_half*((zrz3-z0z3)*(xc_ref-x0)- (xr-x0)*(zc_refz3-z0z3))
            areayz4 = my_half*((zrz4-z0z4)*(xc_ref-x0)- (xr-x0)*(zc_refz4-z0z4))

          areaz = my_half*( (xr-x0)*(yc_ref-y0) - (yr-y0)*(xc_ref-x0) )
            areazx1 = my_half*((xrx1-x0x1)*(yc_ref-y0)- (yr-y0)*(xc_refx1-x0x1))
            areazx2 = my_half*((xrx2-x0x2)*(yc_ref-y0)- (yr-y0)*(xc_refx2-x0x2))
            areazx3 = my_half*((xrx3-x0x3)*(yc_ref-y0)- (yr-y0)*(xc_refx3-x0x3))
            areazx4 = my_half*((xrx4-x0x4)*(yc_ref-y0)- (yr-y0)*(xc_refx4-x0x4))

            areazy1 = my_half*((xr-x0)*(yc_refy1-y0y1)- (yry1-y0y1)*(xc_ref-x0))
            areazy2 = my_half*((xr-x0)*(yc_refy2-y0y2)- (yry2-y0y2)*(xc_ref-x0))
            areazy3 = my_half*((xr-x0)*(yc_refy3-y0y3)- (yry3-y0y3)*(xc_ref-x0))
            areazy4 = my_half*((xr-x0)*(yc_refy4-y0y4)- (yry4-y0y4)*(xc_ref-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0
            areazz4 = my_0

!         bxn(nn1) = bxn(nn1) - areax
            bxnx1 = bxnx1 - areaxx1
            bxnx2 = bxnx2 - areaxx2
            bxnx3 = bxnx3 - areaxx3
            bxnx4 = bxnx4 - areaxx4
            bxny1 = bxny1 - areaxy1
            bxny2 = bxny2 - areaxy2
            bxny3 = bxny3 - areaxy3
            bxny4 = bxny4 - areaxy4
            bxnz1 = bxnz1 - areaxz1
            bxnz2 = bxnz2 - areaxz2
            bxnz3 = bxnz3 - areaxz3
            bxnz4 = bxnz4 - areaxz4

!         byn(nn1) = byn(nn1) - areay
            bynx1 = bynx1 - areayx1
            bynx2 = bynx2 - areayx2
            bynx3 = bynx3 - areayx3
            bynx4 = bynx4 - areayx4
            byny1 = byny1 - areayy1
            byny2 = byny2 - areayy2
            byny3 = byny3 - areayy3
            byny4 = byny4 - areayy4
            bynz1 = bynz1 - areayz1
            bynz2 = bynz2 - areayz2
            bynz3 = bynz3 - areayz3
            bynz4 = bynz4 - areayz4

!         bzn(nn1) = bzn(nn1) - areaz
            bznx1 = bznx1 - areazx1
            bznx2 = bznx2 - areazx2
            bznx3 = bznx3 - areazx3
            bznx4 = bznx4 - areazx4
            bzny1 = bzny1 - areazy1
            bzny2 = bzny2 - areazy2
            bzny3 = bzny3 - areazy3
            bzny4 = bzny4 - areazy4
            bznz1 = bznz1 - areazz1
            bznz2 = bznz2 - areazz2
            bznz3 = bznz3 - areazz3
            bznz4 = bznz4 - areazz4

          dxdtavg = (dxdt0 + dxdtr + dxdtc)/my_3
            do i = 1, ntp
              dxdtavgx1(i) = (dxdt0x1(i) + dxdtrx1(i) + dxdtcx1(i))/my_3
              dxdtavgx2(i) = (dxdt0x2(i) + dxdtrx2(i) + dxdtcx2(i))/my_3
              dxdtavgx3(i) = (dxdt0x3(i) + dxdtrx3(i) + dxdtcx3(i))/my_3
              dxdtavgx4(i) = (dxdt0x4(i) + dxdtrx4(i) + dxdtcx4(i))/my_3

              dxdtavgy1(i) = my_0
              dxdtavgy2(i) = my_0
              dxdtavgy3(i) = my_0
              dxdtavgy4(i) = my_0

              dxdtavgz1(i) = my_0
              dxdtavgz2(i) = my_0
              dxdtavgz3(i) = my_0
              dxdtavgz4(i) = my_0
            end do

          dydtavg = (dydt0 + dydtr + dydtc)/my_3
            do i = 1, ntp
              dydtavgx1(i) = my_0
              dydtavgx2(i) = my_0
              dydtavgx3(i) = my_0
              dydtavgx4(i) = my_0

              dydtavgy1(i) = (dydt0y1(i) + dydtry1(i) + dydtcy1(i))/my_3
              dydtavgy2(i) = (dydt0y2(i) + dydtry2(i) + dydtcy2(i))/my_3
              dydtavgy3(i) = (dydt0y3(i) + dydtry3(i) + dydtcy3(i))/my_3
              dydtavgy4(i) = (dydt0y4(i) + dydtry4(i) + dydtcy4(i))/my_3

              dydtavgz1(i) = my_0
              dydtavgz2(i) = my_0
              dydtavgz3(i) = my_0
              dydtavgz4(i) = my_0
            end do

          dzdtavg = (dzdt0 + dzdtr + dzdtc)/my_3
            do i = 1, ntp
              dzdtavgx1(i) = my_0
              dzdtavgx2(i) = my_0
              dzdtavgx3(i) = my_0
              dzdtavgx4(i) = my_0

              dzdtavgy1(i) = my_0
              dzdtavgy2(i) = my_0
              dzdtavgy3(i) = my_0
              dzdtavgy4(i) = my_0

              dzdtavgz1(i) = (dzdt0z1(i) + dzdtrz1(i) + dzdtcz1(i))/my_3
              dzdtavgz2(i) = (dzdt0z2(i) + dzdtrz2(i) + dzdtcz2(i))/my_3
              dzdtavgz3(i) = (dzdt0z3(i) + dzdtrz3(i) + dzdtcz3(i))/my_3
              dzdtavgz4(i) = (dzdt0z4(i) + dzdtrz4(i) + dzdtcz4(i))/my_3
            end do

!         term = dxdtavg*areax + dydtavg*areay + dzdtavg*areaz
          do i = 1, ntp

! area only depends on grid coords at current time level

           if ( i == 1 ) then
            termx1(i) = dxdtavg*areaxx1 + areax*dxdtavgx1(i) + dydtavg*areayx1 &
                   + areay*dydtavgx1(i) + dzdtavg*areazx1 + areaz*dzdtavgx1(i)
            termx2(i) = dxdtavg*areaxx2 + areax*dxdtavgx2(i) + dydtavg*areayx2 &
                   + areay*dydtavgx2(i) + dzdtavg*areazx2 + areaz*dzdtavgx2(i)
            termx3(i) = dxdtavg*areaxx3 + areax*dxdtavgx3(i) + dydtavg*areayx3 &
                   + areay*dydtavgx3(i) + dzdtavg*areazx3 + areaz*dzdtavgx3(i)
            termx4(i) = dxdtavg*areaxx4 + areax*dxdtavgx4(i) + dydtavg*areayx4 &
                   + areay*dydtavgx4(i) + dzdtavg*areazx4 + areaz*dzdtavgx4(i)

            termy1(i) = dxdtavg*areaxy1 + areax*dxdtavgy1(i) + dydtavg*areayy1 &
                   + areay*dydtavgy1(i) + dzdtavg*areazy1 + areaz*dzdtavgy1(i)
            termy2(i) = dxdtavg*areaxy2 + areax*dxdtavgy2(i) + dydtavg*areayy2 &
                   + areay*dydtavgy2(i) + dzdtavg*areazy2 + areaz*dzdtavgy2(i)
            termy3(i) = dxdtavg*areaxy3 + areax*dxdtavgy3(i) + dydtavg*areayy3 &
                   + areay*dydtavgy3(i) + dzdtavg*areazy3 + areaz*dzdtavgy3(i)
            termy4(i) = dxdtavg*areaxy4 + areax*dxdtavgy4(i) + dydtavg*areayy4 &
                   + areay*dydtavgy4(i) + dzdtavg*areazy4 + areaz*dzdtavgy4(i)

            termz1(i) = dxdtavg*areaxz1 + areax*dxdtavgz1(i) + dydtavg*areayz1 &
                   + areay*dydtavgz1(i) + dzdtavg*areazz1 + areaz*dzdtavgz1(i)
            termz2(i) = dxdtavg*areaxz2 + areax*dxdtavgz2(i) + dydtavg*areayz2 &
                   + areay*dydtavgz2(i) + dzdtavg*areazz2 + areaz*dzdtavgz2(i)
            termz3(i) = dxdtavg*areaxz3 + areax*dxdtavgz3(i) + dydtavg*areayz3 &
                   + areay*dydtavgz3(i) + dzdtavg*areazz3 + areaz*dzdtavgz3(i)
            termz4(i) = dxdtavg*areaxz4 + areax*dxdtavgz4(i) + dydtavg*areayz4 &
                   + areay*dydtavgz4(i) + dzdtavg*areazz4 + areaz*dzdtavgz4(i)
           else
            termx1(i)=areax*dxdtavgx1(i)+areay*dydtavgx1(i) + areaz*dzdtavgx1(i)
            termx2(i)=areax*dxdtavgx2(i)+areay*dydtavgx2(i) + areaz*dzdtavgx2(i)
            termx3(i)=areax*dxdtavgx3(i)+areay*dydtavgx3(i) + areaz*dzdtavgx3(i)
            termx4(i)=areax*dxdtavgx4(i)+areay*dydtavgx4(i) + areaz*dzdtavgx4(i)

            termy1(i)=areax*dxdtavgy1(i)+areay*dydtavgy1(i) + areaz*dzdtavgy1(i)
            termy2(i)=areax*dxdtavgy2(i)+areay*dydtavgy2(i) + areaz*dzdtavgy2(i)
            termy3(i)=areax*dxdtavgy3(i)+areay*dydtavgy3(i) + areaz*dzdtavgy3(i)
            termy4(i)=areax*dxdtavgy4(i)+areay*dydtavgy4(i) + areaz*dzdtavgy4(i)

            termz1(i)=areax*dxdtavgz1(i)+areay*dydtavgz1(i) + areaz*dzdtavgz1(i)
            termz2(i)=areax*dxdtavgz2(i)+areay*dydtavgz2(i) + areaz*dzdtavgz2(i)
            termz3(i)=areax*dxdtavgz3(i)+areay*dydtavgz3(i) + areaz*dzdtavgz3(i)
            termz4(i)=areax*dxdtavgz4(i)+areay*dydtavgz4(i) + areaz*dzdtavgz4(i)
           endif
          end do

!         bfacespeed(nn1) = bfacespeed(nn1) - term

!         bfacespeed = -term
            do i = 1, ntp
              bfacespeedx1(i) = bfacespeedx1(i) - termx1(i)
              bfacespeedx2(i) = bfacespeedx2(i) - termx2(i)
              bfacespeedx3(i) = bfacespeedx3(i) - termx3(i)
              bfacespeedx4(i) = bfacespeedx4(i) - termx4(i)

              bfacespeedy1(i) = bfacespeedy1(i) - termy1(i)
              bfacespeedy2(i) = bfacespeedy2(i) - termy2(i)
              bfacespeedy3(i) = bfacespeedy3(i) - termy3(i)
              bfacespeedy4(i) = bfacespeedy4(i) - termy4(i)

              bfacespeedz1(i) = bfacespeedz1(i) - termz1(i)
              bfacespeedz2(i) = bfacespeedz2(i) - termz2(i)
              bfacespeedz3(i) = bfacespeedz3(i) - termz3(i)
              bfacespeedz4(i) = bfacespeedz4(i) - termz4(i)
            end do

!       triangle x0-xc-xl

          areax = my_half*( (yc_ref-y0)*(zl-z0) - (zc_ref-z0)*(yl-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0
            areaxx4 = my_0

            areaxy1 = my_half*((yc_refy1-y0y1)*(zl-z0)- (zc_ref-z0)*(yly1-y0y1))
            areaxy2 = my_half*((yc_refy2-y0y2)*(zl-z0)- (zc_ref-z0)*(yly2-y0y2))
            areaxy3 = my_half*((yc_refy3-y0y3)*(zl-z0)- (zc_ref-z0)*(yly3-y0y3))
            areaxy4 = my_half*((yc_refy4-y0y4)*(zl-z0)- (zc_ref-z0)*(yly4-y0y4))

            areaxz1 = my_half*((yc_ref-y0)*(zlz1-z0z1)- (zc_refz1-z0z1)*(yl-y0))
            areaxz2 = my_half*((yc_ref-y0)*(zlz2-z0z2)- (zc_refz2-z0z2)*(yl-y0))
            areaxz3 = my_half*((yc_ref-y0)*(zlz3-z0z3)- (zc_refz3-z0z3)*(yl-y0))
            areaxz4 = my_half*((yc_ref-y0)*(zlz4-z0z4)- (zc_refz4-z0z4)*(yl-y0))

          areay = my_half*( (zc_ref-z0)*(xl-x0) - (xc_ref-x0)*(zl-z0) )
            areayx1 = my_half*((zc_ref-z0)*(xlx1-x0x1)- (xc_refx1-x0x1)*(zl-z0))
            areayx2 = my_half*((zc_ref-z0)*(xlx2-x0x2)- (xc_refx2-x0x2)*(zl-z0))
            areayx3 = my_half*((zc_ref-z0)*(xlx3-x0x3)- (xc_refx3-x0x3)*(zl-z0))
            areayx4 = my_half*((zc_ref-z0)*(xlx4-x0x4)- (xc_refx4-x0x4)*(zl-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0
            areayy4 = my_0

            areayz1 = my_half*((zc_refz1-z0z1)*(xl-x0)- (xc_ref-x0)*(zlz1-z0z1))
            areayz2 = my_half*((zc_refz2-z0z2)*(xl-x0)- (xc_ref-x0)*(zlz2-z0z2))
            areayz3 = my_half*((zc_refz3-z0z3)*(xl-x0)- (xc_ref-x0)*(zlz3-z0z3))
            areayz4 = my_half*((zc_refz4-z0z4)*(xl-x0)- (xc_ref-x0)*(zlz4-z0z4))

          areaz = my_half*( (xc_ref-x0)*(yl-y0) - (yc_ref-y0)*(xl-x0) )
            areazx1 = my_half*((xc_refx1-x0x1)*(yl-y0)- (yc_ref-y0)*(xlx1-x0x1))
            areazx2 = my_half*((xc_refx2-x0x2)*(yl-y0)- (yc_ref-y0)*(xlx2-x0x2))
            areazx3 = my_half*((xc_refx3-x0x3)*(yl-y0)- (yc_ref-y0)*(xlx3-x0x3))
            areazx4 = my_half*((xc_refx4-x0x4)*(yl-y0)- (yc_ref-y0)*(xlx4-x0x4))

            areazy1 = my_half*((xc_ref-x0)*(yly1-y0y1)- (yc_refy1-y0y1)*(xl-x0))
            areazy2 = my_half*((xc_ref-x0)*(yly2-y0y2)- (yc_refy2-y0y2)*(xl-x0))
            areazy3 = my_half*((xc_ref-x0)*(yly3-y0y3)- (yc_refy3-y0y3)*(xl-x0))
            areazy4 = my_half*((xc_ref-x0)*(yly4-y0y4)- (yc_refy4-y0y4)*(xl-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0
            areazz4 = my_0

!         bxn(nn1) = bxn(nn1) - areax
            bxnx1 = bxnx1 - areaxx1
            bxnx2 = bxnx2 - areaxx2
            bxnx3 = bxnx3 - areaxx3
            bxnx4 = bxnx4 - areaxx4
            bxny1 = bxny1 - areaxy1
            bxny2 = bxny2 - areaxy2
            bxny3 = bxny3 - areaxy3
            bxny4 = bxny4 - areaxy4
            bxnz1 = bxnz1 - areaxz1
            bxnz2 = bxnz2 - areaxz2
            bxnz3 = bxnz3 - areaxz3
            bxnz4 = bxnz4 - areaxz4

!         byn(nn1) = byn(nn1) - areay
            bynx1 = bynx1 - areayx1
            bynx2 = bynx2 - areayx2
            bynx3 = bynx3 - areayx3
            bynx4 = bynx4 - areayx4
            byny1 = byny1 - areayy1
            byny2 = byny2 - areayy2
            byny3 = byny3 - areayy3
            byny4 = byny4 - areayy4
            bynz1 = bynz1 - areayz1
            bynz2 = bynz2 - areayz2
            bynz3 = bynz3 - areayz3
            bynz4 = bynz4 - areayz4

!         bzn(nn1) = bzn(nn1) - areaz
            bznx1 = bznx1 - areazx1
            bznx2 = bznx2 - areazx2
            bznx3 = bznx3 - areazx3
            bznx4 = bznx4 - areazx4
            bzny1 = bzny1 - areazy1
            bzny2 = bzny2 - areazy2
            bzny3 = bzny3 - areazy3
            bzny4 = bzny4 - areazy4
            bznz1 = bznz1 - areazz1
            bznz2 = bznz2 - areazz2
            bznz3 = bznz3 - areazz3
            bznz4 = bznz4 - areazz4

          dxdtavg = (dxdt0 + dxdtc + dxdtl)/my_3
            do i = 1, ntp
              dxdtavgx1(i) = (dxdt0x1(i) + dxdtcx1(i) + dxdtlx1(i))/my_3
              dxdtavgx2(i) = (dxdt0x2(i) + dxdtcx2(i) + dxdtlx2(i))/my_3
              dxdtavgx3(i) = (dxdt0x3(i) + dxdtcx3(i) + dxdtlx3(i))/my_3
              dxdtavgx4(i) = (dxdt0x4(i) + dxdtcx4(i) + dxdtlx4(i))/my_3

              dxdtavgy1(i) = my_0
              dxdtavgy2(i) = my_0
              dxdtavgy3(i) = my_0
              dxdtavgy4(i) = my_0

              dxdtavgz1(i) = my_0
              dxdtavgz2(i) = my_0
              dxdtavgz3(i) = my_0
              dxdtavgz4(i) = my_0
            end do

          dydtavg = (dydt0 + dydtc + dydtl)/my_3
            do i = 1, ntp
              dydtavgx1(i) = my_0
              dydtavgx2(i) = my_0
              dydtavgx3(i) = my_0
              dydtavgx4(i) = my_0

              dydtavgy1(i) = (dydt0y1(i) + dydtcy1(i) + dydtly1(i))/my_3
              dydtavgy2(i) = (dydt0y2(i) + dydtcy2(i) + dydtly2(i))/my_3
              dydtavgy3(i) = (dydt0y3(i) + dydtcy3(i) + dydtly3(i))/my_3
              dydtavgy4(i) = (dydt0y4(i) + dydtcy4(i) + dydtly4(i))/my_3

              dydtavgz1(i) = my_0
              dydtavgz2(i) = my_0
              dydtavgz3(i) = my_0
              dydtavgz4(i) = my_0
            end do

          dzdtavg = (dzdt0 + dzdtc + dzdtl)/my_3
            do i = 1, ntp
              dzdtavgx1(i) = my_0
              dzdtavgx2(i) = my_0
              dzdtavgx3(i) = my_0
              dzdtavgx4(i) = my_0

              dzdtavgy1(i) = my_0
              dzdtavgy2(i) = my_0
              dzdtavgy3(i) = my_0
              dzdtavgy4(i) = my_0

              dzdtavgz1(i) = (dzdt0z1(i) + dzdtcz1(i) + dzdtlz1(i))/my_3
              dzdtavgz2(i) = (dzdt0z2(i) + dzdtcz2(i) + dzdtlz2(i))/my_3
              dzdtavgz3(i) = (dzdt0z3(i) + dzdtcz3(i) + dzdtlz3(i))/my_3
              dzdtavgz4(i) = (dzdt0z4(i) + dzdtcz4(i) + dzdtlz4(i))/my_3
            end do

!         term = dxdtavg*areax + dydtavg*areay + dzdtavg*areaz
          do i = 1, ntp

! area only depends on grid coords at current time level

           if ( i == 1 ) then
            termx1(i) = dxdtavg*areaxx1 + areax*dxdtavgx1(i) + dydtavg*areayx1 &
                   + areay*dydtavgx1(i) + dzdtavg*areazx1 + areaz*dzdtavgx1(i)
            termx2(i) = dxdtavg*areaxx2 + areax*dxdtavgx2(i) + dydtavg*areayx2 &
                   + areay*dydtavgx2(i) + dzdtavg*areazx2 + areaz*dzdtavgx2(i)
            termx3(i) = dxdtavg*areaxx3 + areax*dxdtavgx3(i) + dydtavg*areayx3 &
                   + areay*dydtavgx3(i) + dzdtavg*areazx3 + areaz*dzdtavgx3(i)
            termx4(i) = dxdtavg*areaxx4 + areax*dxdtavgx4(i) + dydtavg*areayx4 &
                   + areay*dydtavgx4(i) + dzdtavg*areazx4 + areaz*dzdtavgx4(i)

            termy1(i) = dxdtavg*areaxy1 + areax*dxdtavgy1(i) + dydtavg*areayy1 &
                   + areay*dydtavgy1(i) + dzdtavg*areazy1 + areaz*dzdtavgy1(i)
            termy2(i) = dxdtavg*areaxy2 + areax*dxdtavgy2(i) + dydtavg*areayy2 &
                   + areay*dydtavgy2(i) + dzdtavg*areazy2 + areaz*dzdtavgy2(i)
            termy3(i) = dxdtavg*areaxy3 + areax*dxdtavgy3(i) + dydtavg*areayy3 &
                   + areay*dydtavgy3(i) + dzdtavg*areazy3 + areaz*dzdtavgy3(i)
            termy4(i) = dxdtavg*areaxy4 + areax*dxdtavgy4(i) + dydtavg*areayy4 &
                   + areay*dydtavgy4(i) + dzdtavg*areazy4 + areaz*dzdtavgy4(i)

            termz1(i) = dxdtavg*areaxz1 + areax*dxdtavgz1(i) + dydtavg*areayz1 &
                   + areay*dydtavgz1(i) + dzdtavg*areazz1 + areaz*dzdtavgz1(i)
            termz2(i) = dxdtavg*areaxz2 + areax*dxdtavgz2(i) + dydtavg*areayz2 &
                   + areay*dydtavgz2(i) + dzdtavg*areazz2 + areaz*dzdtavgz2(i)
            termz3(i) = dxdtavg*areaxz3 + areax*dxdtavgz3(i) + dydtavg*areayz3 &
                   + areay*dydtavgz3(i) + dzdtavg*areazz3 + areaz*dzdtavgz3(i)
            termz4(i) = dxdtavg*areaxz4 + areax*dxdtavgz4(i) + dydtavg*areayz4 &
                   + areay*dydtavgz4(i) + dzdtavg*areazz4 + areaz*dzdtavgz4(i)
           else
            termx1(i)=areax*dxdtavgx1(i)+areay*dydtavgx1(i) + areaz*dzdtavgx1(i)
            termx2(i)=areax*dxdtavgx2(i)+areay*dydtavgx2(i) + areaz*dzdtavgx2(i)
            termx3(i)=areax*dxdtavgx3(i)+areay*dydtavgx3(i) + areaz*dzdtavgx3(i)
            termx4(i)=areax*dxdtavgx4(i)+areay*dydtavgx4(i) + areaz*dzdtavgx4(i)

            termy1(i)=areax*dxdtavgy1(i)+areay*dydtavgy1(i) + areaz*dzdtavgy1(i)
            termy2(i)=areax*dxdtavgy2(i)+areay*dydtavgy2(i) + areaz*dzdtavgy2(i)
            termy3(i)=areax*dxdtavgy3(i)+areay*dydtavgy3(i) + areaz*dzdtavgy3(i)
            termy4(i)=areax*dxdtavgy4(i)+areay*dydtavgy4(i) + areaz*dzdtavgy4(i)

            termz1(i)=areax*dxdtavgz1(i)+areay*dydtavgz1(i) + areaz*dzdtavgz1(i)
            termz2(i)=areax*dxdtavgz2(i)+areay*dydtavgz2(i) + areaz*dzdtavgz2(i)
            termz3(i)=areax*dxdtavgz3(i)+areay*dydtavgz3(i) + areaz*dzdtavgz3(i)
            termz4(i)=areax*dxdtavgz4(i)+areay*dydtavgz4(i) + areaz*dzdtavgz4(i)
           endif
          end do

!         bfacespeed(nn1) = bfacespeed(nn1) - term

!         bfacespeed = -term
            do i = 1, ntp
              bfacespeedx1(i) = bfacespeedx1(i) - termx1(i)
              bfacespeedx2(i) = bfacespeedx2(i) - termx2(i)
              bfacespeedx3(i) = bfacespeedx3(i) - termx3(i)
              bfacespeedx4(i) = bfacespeedx4(i) - termx4(i)

              bfacespeedy1(i) = bfacespeedy1(i) - termy1(i)
              bfacespeedy2(i) = bfacespeedy2(i) - termy2(i)
              bfacespeedy3(i) = bfacespeedy3(i) - termy3(i)
              bfacespeedy4(i) = bfacespeedy4(i) - termy4(i)

              bfacespeedz1(i) = bfacespeedz1(i) - termz1(i)
              bfacespeedz2(i) = bfacespeedz2(i) - termz2(i)
              bfacespeedz3(i) = bfacespeedz3(i) - termz3(i)
              bfacespeedz4(i) = bfacespeedz4(i) - termz4(i)
            end do

! Now accumulate the normals and facespeeds derivatives
! Linearizations above are wrt invariant global nodes (ie, cnode1/cnode2/cnode3)

! cnode1 = ibnode(f2ntb(face,1))     ! global nodes for the face
! cnode2 = ibnode(f2ntb(face,2))     ! these are invariant within this loop
! cnode3 = ibnode(f2ntb(face,3))
! cnode4 = ibnode(f2ntb(face,4))

! node1 == ibnode(nn1)               ! global nodes for the face
! node2 == ibnode(nn2)               ! these cycle within this loop
! node3 == ibnode(nn3)
! node4 == ibnode(nn4)

          local_node2 : if ( node1 <= nnodes0 ) then

            ioff1 = -99
            ioff2 = -99
            ioff3 = -99
            ioff4 = -99
            do i = 1, node_stencil(nn1)%n
              inode = node_stencil(nn1)%nodes(i)
              if (inode == nn1 ) ioff1 = i
              if (inode == nn2 ) ioff2 = i
              if (inode == nn3 ) ioff3 = i
              if (inode == nn4 ) ioff4 = i
            end do

! Find and place the linearizations wrt nn1

            if ( node1 == cnode1 ) then
              node_stencil(nn1)%dxndx(ioff1) = node_stencil(nn1)%dxndx(ioff1)  &
                                                                         + bxnx1
              node_stencil(nn1)%dxndy(ioff1) = node_stencil(nn1)%dxndy(ioff1)  &
                                                                         + bxny1
              node_stencil(nn1)%dxndz(ioff1) = node_stencil(nn1)%dxndz(ioff1)  &
                                                                         + bxnz1

              node_stencil(nn1)%dyndx(ioff1) = node_stencil(nn1)%dyndx(ioff1)  &
                                                                         + bynx1
              node_stencil(nn1)%dyndy(ioff1) = node_stencil(nn1)%dyndy(ioff1)  &
                                                                         + byny1
              node_stencil(nn1)%dyndz(ioff1) = node_stencil(nn1)%dyndz(ioff1)  &
                                                                         + bynz1

              node_stencil(nn1)%dzndx(ioff1) = node_stencil(nn1)%dzndx(ioff1)  &
                                                                         + bznx1
              node_stencil(nn1)%dzndy(ioff1) = node_stencil(nn1)%dzndy(ioff1)  &
                                                                         + bzny1
              node_stencil(nn1)%dzndz(ioff1) = node_stencil(nn1)%dzndz(ioff1)  &
                                                                         + bznz1

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff1,k)=node_stencil(nn1)%dwdx(ioff1,k) &
                                                               + bfacespeedx1(k)
               node_stencil(nn1)%dwdy(ioff1,k)=node_stencil(nn1)%dwdy(ioff1,k) &
                                                               + bfacespeedy1(k)
               node_stencil(nn1)%dwdz(ioff1,k)=node_stencil(nn1)%dwdz(ioff1,k) &
                                                               + bfacespeedz1(k)
              end do
            else if ( node1 == cnode2 ) then
              node_stencil(nn1)%dxndx(ioff1) = node_stencil(nn1)%dxndx(ioff1)  &
                                                                         + bxnx2
              node_stencil(nn1)%dxndy(ioff1) = node_stencil(nn1)%dxndy(ioff1)  &
                                                                         + bxny2
              node_stencil(nn1)%dxndz(ioff1) = node_stencil(nn1)%dxndz(ioff1)  &
                                                                         + bxnz2

              node_stencil(nn1)%dyndx(ioff1) = node_stencil(nn1)%dyndx(ioff1)  &
                                                                         + bynx2
              node_stencil(nn1)%dyndy(ioff1) = node_stencil(nn1)%dyndy(ioff1)  &
                                                                         + byny2
              node_stencil(nn1)%dyndz(ioff1) = node_stencil(nn1)%dyndz(ioff1)  &
                                                                         + bynz2

              node_stencil(nn1)%dzndx(ioff1) = node_stencil(nn1)%dzndx(ioff1)  &
                                                                         + bznx2
              node_stencil(nn1)%dzndy(ioff1) = node_stencil(nn1)%dzndy(ioff1)  &
                                                                         + bzny2
              node_stencil(nn1)%dzndz(ioff1) = node_stencil(nn1)%dzndz(ioff1)  &
                                                                         + bznz2

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff1,k)=node_stencil(nn1)%dwdx(ioff1,k) &
                                                               + bfacespeedx2(k)
               node_stencil(nn1)%dwdy(ioff1,k)=node_stencil(nn1)%dwdy(ioff1,k) &
                                                               + bfacespeedy2(k)
               node_stencil(nn1)%dwdz(ioff1,k)=node_stencil(nn1)%dwdz(ioff1,k) &
                                                               + bfacespeedz2(k)
              end do
            else if ( node1 == cnode3 ) then
              node_stencil(nn1)%dxndx(ioff1) = node_stencil(nn1)%dxndx(ioff1)  &
                                                                         + bxnx3
              node_stencil(nn1)%dxndy(ioff1) = node_stencil(nn1)%dxndy(ioff1)  &
                                                                         + bxny3
              node_stencil(nn1)%dxndz(ioff1) = node_stencil(nn1)%dxndz(ioff1)  &
                                                                         + bxnz3

              node_stencil(nn1)%dyndx(ioff1) = node_stencil(nn1)%dyndx(ioff1)  &
                                                                         + bynx3
              node_stencil(nn1)%dyndy(ioff1) = node_stencil(nn1)%dyndy(ioff1)  &
                                                                         + byny3
              node_stencil(nn1)%dyndz(ioff1) = node_stencil(nn1)%dyndz(ioff1)  &
                                                                         + bynz3

              node_stencil(nn1)%dzndx(ioff1) = node_stencil(nn1)%dzndx(ioff1)  &
                                                                         + bznx3
              node_stencil(nn1)%dzndy(ioff1) = node_stencil(nn1)%dzndy(ioff1)  &
                                                                         + bzny3
              node_stencil(nn1)%dzndz(ioff1) = node_stencil(nn1)%dzndz(ioff1)  &
                                                                         + bznz3

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff1,k)=node_stencil(nn1)%dwdx(ioff1,k) &
                                                               + bfacespeedx3(k)
               node_stencil(nn1)%dwdy(ioff1,k)=node_stencil(nn1)%dwdy(ioff1,k) &
                                                               + bfacespeedy3(k)
               node_stencil(nn1)%dwdz(ioff1,k)=node_stencil(nn1)%dwdz(ioff1,k) &
                                                               + bfacespeedz3(k)
              end do
            else if ( node1 == cnode4 ) then
              node_stencil(nn1)%dxndx(ioff1) = node_stencil(nn1)%dxndx(ioff1)  &
                                                                         + bxnx4
              node_stencil(nn1)%dxndy(ioff1) = node_stencil(nn1)%dxndy(ioff1)  &
                                                                         + bxny4
              node_stencil(nn1)%dxndz(ioff1) = node_stencil(nn1)%dxndz(ioff1)  &
                                                                         + bxnz4

              node_stencil(nn1)%dyndx(ioff1) = node_stencil(nn1)%dyndx(ioff1)  &
                                                                         + bynx4
              node_stencil(nn1)%dyndy(ioff1) = node_stencil(nn1)%dyndy(ioff1)  &
                                                                         + byny4
              node_stencil(nn1)%dyndz(ioff1) = node_stencil(nn1)%dyndz(ioff1)  &
                                                                         + bynz4

              node_stencil(nn1)%dzndx(ioff1) = node_stencil(nn1)%dzndx(ioff1)  &
                                                                         + bznx4
              node_stencil(nn1)%dzndy(ioff1) = node_stencil(nn1)%dzndy(ioff1)  &
                                                                         + bzny4
              node_stencil(nn1)%dzndz(ioff1) = node_stencil(nn1)%dzndz(ioff1)  &
                                                                         + bznz4

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff1,k)=node_stencil(nn1)%dwdx(ioff1,k) &
                                                               + bfacespeedx4(k)
               node_stencil(nn1)%dwdy(ioff1,k)=node_stencil(nn1)%dwdy(ioff1,k) &
                                                               + bfacespeedy4(k)
               node_stencil(nn1)%dwdz(ioff1,k)=node_stencil(nn1)%dwdz(ioff1,k) &
                                                               + bfacespeedz4(k)
              end do
            endif

! Find and place the linearizations wrt nn2

            if ( node2 == cnode1 ) then
              node_stencil(nn1)%dxndx(ioff2) = node_stencil(nn1)%dxndx(ioff2)  &
                                                                         + bxnx1
              node_stencil(nn1)%dxndy(ioff2) = node_stencil(nn1)%dxndy(ioff2)  &
                                                                         + bxny1
              node_stencil(nn1)%dxndz(ioff2) = node_stencil(nn1)%dxndz(ioff2)  &
                                                                         + bxnz1

              node_stencil(nn1)%dyndx(ioff2) = node_stencil(nn1)%dyndx(ioff2)  &
                                                                         + bynx1
              node_stencil(nn1)%dyndy(ioff2) = node_stencil(nn1)%dyndy(ioff2)  &
                                                                         + byny1
              node_stencil(nn1)%dyndz(ioff2) = node_stencil(nn1)%dyndz(ioff2)  &
                                                                         + bynz1

              node_stencil(nn1)%dzndx(ioff2) = node_stencil(nn1)%dzndx(ioff2)  &
                                                                         + bznx1
              node_stencil(nn1)%dzndy(ioff2) = node_stencil(nn1)%dzndy(ioff2)  &
                                                                         + bzny1
              node_stencil(nn1)%dzndz(ioff2) = node_stencil(nn1)%dzndz(ioff2)  &
                                                                         + bznz1

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff2,k)=node_stencil(nn1)%dwdx(ioff2,k) &
                                                               + bfacespeedx1(k)
               node_stencil(nn1)%dwdy(ioff2,k)=node_stencil(nn1)%dwdy(ioff2,k) &
                                                               + bfacespeedy1(k)
               node_stencil(nn1)%dwdz(ioff2,k)=node_stencil(nn1)%dwdz(ioff2,k) &
                                                               + bfacespeedz1(k)
              end do
            else if ( node2 == cnode2 ) then
              node_stencil(nn1)%dxndx(ioff2) = node_stencil(nn1)%dxndx(ioff2)  &
                                                                         + bxnx2
              node_stencil(nn1)%dxndy(ioff2) = node_stencil(nn1)%dxndy(ioff2)  &
                                                                         + bxny2
              node_stencil(nn1)%dxndz(ioff2) = node_stencil(nn1)%dxndz(ioff2)  &
                                                                         + bxnz2

              node_stencil(nn1)%dyndx(ioff2) = node_stencil(nn1)%dyndx(ioff2)  &
                                                                         + bynx2
              node_stencil(nn1)%dyndy(ioff2) = node_stencil(nn1)%dyndy(ioff2)  &
                                                                         + byny2
              node_stencil(nn1)%dyndz(ioff2) = node_stencil(nn1)%dyndz(ioff2)  &
                                                                         + bynz2

              node_stencil(nn1)%dzndx(ioff2) = node_stencil(nn1)%dzndx(ioff2)  &
                                                                         + bznx2
              node_stencil(nn1)%dzndy(ioff2) = node_stencil(nn1)%dzndy(ioff2)  &
                                                                         + bzny2
              node_stencil(nn1)%dzndz(ioff2) = node_stencil(nn1)%dzndz(ioff2)  &
                                                                         + bznz2

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff2,k)=node_stencil(nn1)%dwdx(ioff2,k) &
                                                               + bfacespeedx2(k)
               node_stencil(nn1)%dwdy(ioff2,k)=node_stencil(nn1)%dwdy(ioff2,k) &
                                                               + bfacespeedy2(k)
               node_stencil(nn1)%dwdz(ioff2,k)=node_stencil(nn1)%dwdz(ioff2,k) &
                                                               + bfacespeedz2(k)
              end do
            else if ( node2 == cnode3 ) then
              node_stencil(nn1)%dxndx(ioff2) = node_stencil(nn1)%dxndx(ioff2)  &
                                                                         + bxnx3
              node_stencil(nn1)%dxndy(ioff2) = node_stencil(nn1)%dxndy(ioff2)  &
                                                                         + bxny3
              node_stencil(nn1)%dxndz(ioff2) = node_stencil(nn1)%dxndz(ioff2)  &
                                                                         + bxnz3

              node_stencil(nn1)%dyndx(ioff2) = node_stencil(nn1)%dyndx(ioff2)  &
                                                                         + bynx3
              node_stencil(nn1)%dyndy(ioff2) = node_stencil(nn1)%dyndy(ioff2)  &
                                                                         + byny3
              node_stencil(nn1)%dyndz(ioff2) = node_stencil(nn1)%dyndz(ioff2)  &
                                                                         + bynz3

              node_stencil(nn1)%dzndx(ioff2) = node_stencil(nn1)%dzndx(ioff2)  &
                                                                         + bznx3
              node_stencil(nn1)%dzndy(ioff2) = node_stencil(nn1)%dzndy(ioff2)  &
                                                                         + bzny3
              node_stencil(nn1)%dzndz(ioff2) = node_stencil(nn1)%dzndz(ioff2)  &
                                                                         + bznz3

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff2,k)=node_stencil(nn1)%dwdx(ioff2,k) &
                                                               + bfacespeedx3(k)
               node_stencil(nn1)%dwdy(ioff2,k)=node_stencil(nn1)%dwdy(ioff2,k) &
                                                               + bfacespeedy3(k)
               node_stencil(nn1)%dwdz(ioff2,k)=node_stencil(nn1)%dwdz(ioff2,k) &
                                                               + bfacespeedz3(k)
              end do
            else if ( node2 == cnode4 ) then
              node_stencil(nn1)%dxndx(ioff2) = node_stencil(nn1)%dxndx(ioff2)  &
                                                                         + bxnx4
              node_stencil(nn1)%dxndy(ioff2) = node_stencil(nn1)%dxndy(ioff2)  &
                                                                         + bxny4
              node_stencil(nn1)%dxndz(ioff2) = node_stencil(nn1)%dxndz(ioff2)  &
                                                                         + bxnz4

              node_stencil(nn1)%dyndx(ioff2) = node_stencil(nn1)%dyndx(ioff2)  &
                                                                         + bynx4
              node_stencil(nn1)%dyndy(ioff2) = node_stencil(nn1)%dyndy(ioff2)  &
                                                                         + byny4
              node_stencil(nn1)%dyndz(ioff2) = node_stencil(nn1)%dyndz(ioff2)  &
                                                                         + bynz4

              node_stencil(nn1)%dzndx(ioff2) = node_stencil(nn1)%dzndx(ioff2)  &
                                                                         + bznx4
              node_stencil(nn1)%dzndy(ioff2) = node_stencil(nn1)%dzndy(ioff2)  &
                                                                         + bzny4
              node_stencil(nn1)%dzndz(ioff2) = node_stencil(nn1)%dzndz(ioff2)  &
                                                                         + bznz4

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff2,k)=node_stencil(nn1)%dwdx(ioff2,k) &
                                                               + bfacespeedx4(k)
               node_stencil(nn1)%dwdy(ioff2,k)=node_stencil(nn1)%dwdy(ioff2,k) &
                                                               + bfacespeedy4(k)
               node_stencil(nn1)%dwdz(ioff2,k)=node_stencil(nn1)%dwdz(ioff2,k) &
                                                               + bfacespeedz4(k)
              end do
            endif

! Find and place the linearizations wrt nn3

            if ( node3 == cnode1 ) then
              node_stencil(nn1)%dxndx(ioff3) = node_stencil(nn1)%dxndx(ioff3)  &
                                                                         + bxnx1
              node_stencil(nn1)%dxndy(ioff3) = node_stencil(nn1)%dxndy(ioff3)  &
                                                                         + bxny1
              node_stencil(nn1)%dxndz(ioff3) = node_stencil(nn1)%dxndz(ioff3)  &
                                                                         + bxnz1

              node_stencil(nn1)%dyndx(ioff3) = node_stencil(nn1)%dyndx(ioff3)  &
                                                                         + bynx1
              node_stencil(nn1)%dyndy(ioff3) = node_stencil(nn1)%dyndy(ioff3)  &
                                                                         + byny1
              node_stencil(nn1)%dyndz(ioff3) = node_stencil(nn1)%dyndz(ioff3)  &
                                                                         + bynz1

              node_stencil(nn1)%dzndx(ioff3) = node_stencil(nn1)%dzndx(ioff3)  &
                                                                         + bznx1
              node_stencil(nn1)%dzndy(ioff3) = node_stencil(nn1)%dzndy(ioff3)  &
                                                                         + bzny1
              node_stencil(nn1)%dzndz(ioff3) = node_stencil(nn1)%dzndz(ioff3)  &
                                                                         + bznz1

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff3,k)=node_stencil(nn1)%dwdx(ioff3,k) &
                                                               + bfacespeedx1(k)
               node_stencil(nn1)%dwdy(ioff3,k)=node_stencil(nn1)%dwdy(ioff3,k) &
                                                               + bfacespeedy1(k)
               node_stencil(nn1)%dwdz(ioff3,k)=node_stencil(nn1)%dwdz(ioff3,k) &
                                                               + bfacespeedz1(k)
              end do
            else if ( node3 == cnode2 ) then
              node_stencil(nn1)%dxndx(ioff3) = node_stencil(nn1)%dxndx(ioff3)  &
                                                                         + bxnx2
              node_stencil(nn1)%dxndy(ioff3) = node_stencil(nn1)%dxndy(ioff3)  &
                                                                         + bxny2
              node_stencil(nn1)%dxndz(ioff3) = node_stencil(nn1)%dxndz(ioff3)  &
                                                                         + bxnz2

              node_stencil(nn1)%dyndx(ioff3) = node_stencil(nn1)%dyndx(ioff3)  &
                                                                         + bynx2
              node_stencil(nn1)%dyndy(ioff3) = node_stencil(nn1)%dyndy(ioff3)  &
                                                                         + byny2
              node_stencil(nn1)%dyndz(ioff3) = node_stencil(nn1)%dyndz(ioff3)  &
                                                                         + bynz2

              node_stencil(nn1)%dzndx(ioff3) = node_stencil(nn1)%dzndx(ioff3)  &
                                                                         + bznx2
              node_stencil(nn1)%dzndy(ioff3) = node_stencil(nn1)%dzndy(ioff3)  &
                                                                         + bzny2
              node_stencil(nn1)%dzndz(ioff3) = node_stencil(nn1)%dzndz(ioff3)  &
                                                                         + bznz2

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff3,k)=node_stencil(nn1)%dwdx(ioff3,k) &
                                                               + bfacespeedx2(k)
               node_stencil(nn1)%dwdy(ioff3,k)=node_stencil(nn1)%dwdy(ioff3,k) &
                                                               + bfacespeedy2(k)
               node_stencil(nn1)%dwdz(ioff3,k)=node_stencil(nn1)%dwdz(ioff3,k) &
                                                               + bfacespeedz2(k)
              end do
            else if ( node3 == cnode3 ) then
              node_stencil(nn1)%dxndx(ioff3) = node_stencil(nn1)%dxndx(ioff3)  &
                                                                         + bxnx3
              node_stencil(nn1)%dxndy(ioff3) = node_stencil(nn1)%dxndy(ioff3)  &
                                                                         + bxny3
              node_stencil(nn1)%dxndz(ioff3) = node_stencil(nn1)%dxndz(ioff3)  &
                                                                         + bxnz3

              node_stencil(nn1)%dyndx(ioff3) = node_stencil(nn1)%dyndx(ioff3)  &
                                                                         + bynx3
              node_stencil(nn1)%dyndy(ioff3) = node_stencil(nn1)%dyndy(ioff3)  &
                                                                         + byny3
              node_stencil(nn1)%dyndz(ioff3) = node_stencil(nn1)%dyndz(ioff3)  &
                                                                         + bynz3

              node_stencil(nn1)%dzndx(ioff3) = node_stencil(nn1)%dzndx(ioff3)  &
                                                                         + bznx3
              node_stencil(nn1)%dzndy(ioff3) = node_stencil(nn1)%dzndy(ioff3)  &
                                                                         + bzny3
              node_stencil(nn1)%dzndz(ioff3) = node_stencil(nn1)%dzndz(ioff3)  &
                                                                         + bznz3

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff3,k)=node_stencil(nn1)%dwdx(ioff3,k) &
                                                               + bfacespeedx3(k)
               node_stencil(nn1)%dwdy(ioff3,k)=node_stencil(nn1)%dwdy(ioff3,k) &
                                                               + bfacespeedy3(k)
               node_stencil(nn1)%dwdz(ioff3,k)=node_stencil(nn1)%dwdz(ioff3,k) &
                                                               + bfacespeedz3(k)
              end do
            else if ( node3 == cnode4 ) then
              node_stencil(nn1)%dxndx(ioff3) = node_stencil(nn1)%dxndx(ioff3)  &
                                                                         + bxnx4
              node_stencil(nn1)%dxndy(ioff3) = node_stencil(nn1)%dxndy(ioff3)  &
                                                                         + bxny4
              node_stencil(nn1)%dxndz(ioff3) = node_stencil(nn1)%dxndz(ioff3)  &
                                                                         + bxnz4

              node_stencil(nn1)%dyndx(ioff3) = node_stencil(nn1)%dyndx(ioff3)  &
                                                                         + bynx4
              node_stencil(nn1)%dyndy(ioff3) = node_stencil(nn1)%dyndy(ioff3)  &
                                                                         + byny4
              node_stencil(nn1)%dyndz(ioff3) = node_stencil(nn1)%dyndz(ioff3)  &
                                                                         + bynz4

              node_stencil(nn1)%dzndx(ioff3) = node_stencil(nn1)%dzndx(ioff3)  &
                                                                         + bznx4
              node_stencil(nn1)%dzndy(ioff3) = node_stencil(nn1)%dzndy(ioff3)  &
                                                                         + bzny4
              node_stencil(nn1)%dzndz(ioff3) = node_stencil(nn1)%dzndz(ioff3)  &
                                                                         + bznz4

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff3,k)=node_stencil(nn1)%dwdx(ioff3,k) &
                                                               + bfacespeedx4(k)
               node_stencil(nn1)%dwdy(ioff3,k)=node_stencil(nn1)%dwdy(ioff3,k) &
                                                               + bfacespeedy4(k)
               node_stencil(nn1)%dwdz(ioff3,k)=node_stencil(nn1)%dwdz(ioff3,k) &
                                                               + bfacespeedz4(k)
              end do
            endif

! Find and place the linearizations wrt nn4

            if ( node4 == cnode1 ) then
              node_stencil(nn1)%dxndx(ioff4) = node_stencil(nn1)%dxndx(ioff4)  &
                                                                         + bxnx1
              node_stencil(nn1)%dxndy(ioff4) = node_stencil(nn1)%dxndy(ioff4)  &
                                                                         + bxny1
              node_stencil(nn1)%dxndz(ioff4) = node_stencil(nn1)%dxndz(ioff4)  &
                                                                         + bxnz1

              node_stencil(nn1)%dyndx(ioff4) = node_stencil(nn1)%dyndx(ioff4)  &
                                                                         + bynx1
              node_stencil(nn1)%dyndy(ioff4) = node_stencil(nn1)%dyndy(ioff4)  &
                                                                         + byny1
              node_stencil(nn1)%dyndz(ioff4) = node_stencil(nn1)%dyndz(ioff4)  &
                                                                         + bynz1

              node_stencil(nn1)%dzndx(ioff4) = node_stencil(nn1)%dzndx(ioff4)  &
                                                                         + bznx1
              node_stencil(nn1)%dzndy(ioff4) = node_stencil(nn1)%dzndy(ioff4)  &
                                                                         + bzny1
              node_stencil(nn1)%dzndz(ioff4) = node_stencil(nn1)%dzndz(ioff4)  &
                                                                         + bznz1

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff4,k)=node_stencil(nn1)%dwdx(ioff4,k) &
                                                               + bfacespeedx1(k)
               node_stencil(nn1)%dwdy(ioff4,k)=node_stencil(nn1)%dwdy(ioff4,k) &
                                                               + bfacespeedy1(k)
               node_stencil(nn1)%dwdz(ioff4,k)=node_stencil(nn1)%dwdz(ioff4,k) &
                                                               + bfacespeedz1(k)
              end do
            else if ( node4 == cnode2 ) then
              node_stencil(nn1)%dxndx(ioff4) = node_stencil(nn1)%dxndx(ioff4)  &
                                                                         + bxnx2
              node_stencil(nn1)%dxndy(ioff4) = node_stencil(nn1)%dxndy(ioff4)  &
                                                                         + bxny2
              node_stencil(nn1)%dxndz(ioff4) = node_stencil(nn1)%dxndz(ioff4)  &
                                                                         + bxnz2

              node_stencil(nn1)%dyndx(ioff4) = node_stencil(nn1)%dyndx(ioff4)  &
                                                                         + bynx2
              node_stencil(nn1)%dyndy(ioff4) = node_stencil(nn1)%dyndy(ioff4)  &
                                                                         + byny2
              node_stencil(nn1)%dyndz(ioff4) = node_stencil(nn1)%dyndz(ioff4)  &
                                                                         + bynz2

              node_stencil(nn1)%dzndx(ioff4) = node_stencil(nn1)%dzndx(ioff4)  &
                                                                         + bznx2
              node_stencil(nn1)%dzndy(ioff4) = node_stencil(nn1)%dzndy(ioff4)  &
                                                                         + bzny2
              node_stencil(nn1)%dzndz(ioff4) = node_stencil(nn1)%dzndz(ioff4)  &
                                                                         + bznz2

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff4,k)=node_stencil(nn1)%dwdx(ioff4,k) &
                                                               + bfacespeedx2(k)
               node_stencil(nn1)%dwdy(ioff4,k)=node_stencil(nn1)%dwdy(ioff4,k) &
                                                               + bfacespeedy2(k)
               node_stencil(nn1)%dwdz(ioff4,k)=node_stencil(nn1)%dwdz(ioff4,k) &
                                                               + bfacespeedz2(k)
              end do
            else if ( node4 == cnode3 ) then
              node_stencil(nn1)%dxndx(ioff4) = node_stencil(nn1)%dxndx(ioff4)  &
                                                                         + bxnx3
              node_stencil(nn1)%dxndy(ioff4) = node_stencil(nn1)%dxndy(ioff4)  &
                                                                         + bxny3
              node_stencil(nn1)%dxndz(ioff4) = node_stencil(nn1)%dxndz(ioff4)  &
                                                                         + bxnz3

              node_stencil(nn1)%dyndx(ioff4) = node_stencil(nn1)%dyndx(ioff4)  &
                                                                         + bynx3
              node_stencil(nn1)%dyndy(ioff4) = node_stencil(nn1)%dyndy(ioff4)  &
                                                                         + byny3
              node_stencil(nn1)%dyndz(ioff4) = node_stencil(nn1)%dyndz(ioff4)  &
                                                                         + bynz3

              node_stencil(nn1)%dzndx(ioff4) = node_stencil(nn1)%dzndx(ioff4)  &
                                                                         + bznx3
              node_stencil(nn1)%dzndy(ioff4) = node_stencil(nn1)%dzndy(ioff4)  &
                                                                         + bzny3
              node_stencil(nn1)%dzndz(ioff4) = node_stencil(nn1)%dzndz(ioff4)  &
                                                                         + bznz3

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff4,k)=node_stencil(nn1)%dwdx(ioff4,k) &
                                                               + bfacespeedx3(k)
               node_stencil(nn1)%dwdy(ioff4,k)=node_stencil(nn1)%dwdy(ioff4,k) &
                                                               + bfacespeedy3(k)
               node_stencil(nn1)%dwdz(ioff4,k)=node_stencil(nn1)%dwdz(ioff4,k) &
                                                               + bfacespeedz3(k)
              end do
            else if ( node4 == cnode4 ) then
              node_stencil(nn1)%dxndx(ioff4) = node_stencil(nn1)%dxndx(ioff4)  &
                                                                         + bxnx4
              node_stencil(nn1)%dxndy(ioff4) = node_stencil(nn1)%dxndy(ioff4)  &
                                                                         + bxny4
              node_stencil(nn1)%dxndz(ioff4) = node_stencil(nn1)%dxndz(ioff4)  &
                                                                         + bxnz4

              node_stencil(nn1)%dyndx(ioff4) = node_stencil(nn1)%dyndx(ioff4)  &
                                                                         + bynx4
              node_stencil(nn1)%dyndy(ioff4) = node_stencil(nn1)%dyndy(ioff4)  &
                                                                         + byny4
              node_stencil(nn1)%dyndz(ioff4) = node_stencil(nn1)%dyndz(ioff4)  &
                                                                         + bynz4

              node_stencil(nn1)%dzndx(ioff4) = node_stencil(nn1)%dzndx(ioff4)  &
                                                                         + bznx4
              node_stencil(nn1)%dzndy(ioff4) = node_stencil(nn1)%dzndy(ioff4)  &
                                                                         + bzny4
              node_stencil(nn1)%dzndz(ioff4) = node_stencil(nn1)%dzndz(ioff4)  &
                                                                         + bznz4

              do k = 1, ntp
               node_stencil(nn1)%dwdx(ioff4,k)=node_stencil(nn1)%dwdx(ioff4,k) &
                                                               + bfacespeedx4(k)
               node_stencil(nn1)%dwdy(ioff4,k)=node_stencil(nn1)%dwdy(ioff4,k) &
                                                               + bfacespeedy4(k)
               node_stencil(nn1)%dwdz(ioff4,k)=node_stencil(nn1)%dwdz(ioff4,k) &
                                                               + bfacespeedz4(k)
              end do
            endif

          endif local_node2

        end do quad_node_loop

      end do quad_face_loop

! Now that we have the derivatives of the edge normals and dimensional
! facespeeds, we can compute the derivatives of the edge areas and ultimately
! the W-hat derivatives.  Then ditch the derivatives of the facespeeds.

    do i = 1, nbnode

      if ( ibnode(i) > nnodes0 ) cycle

      xn_node = bxn(i)
      yn_node = byn(i)
      zn_node = bzn(i)

      area = sqrt(xn_node*xn_node + yn_node*yn_node + zn_node*zn_node)

      call my_alloc_ptr(node_stencil(i)%dwhatdx, node_stencil(i)%n, ntp)
      call my_alloc_ptr(node_stencil(i)%dwhatdy, node_stencil(i)%n, ntp)
      call my_alloc_ptr(node_stencil(i)%dwhatdz, node_stencil(i)%n, ntp)

      do j = 1, node_stencil(i)%n
        dxndx = node_stencil(i)%dxndx(j)
        dxndy = node_stencil(i)%dxndy(j)
        dxndz = node_stencil(i)%dxndz(j)

        dyndx = node_stencil(i)%dyndx(j)
        dyndy = node_stencil(i)%dyndy(j)
        dyndz = node_stencil(i)%dyndz(j)

        dzndx = node_stencil(i)%dzndx(j)
        dzndy = node_stencil(i)%dzndy(j)
        dzndz = node_stencil(i)%dzndz(j)

        do k = 1, ntp
          dwdx(k) = node_stencil(i)%dwdx(j,k)
          dwdy(k) = node_stencil(i)%dwdy(j,k)
          dwdz(k) = node_stencil(i)%dwdz(j,k)
        end do

        dareadx = 1.0_dp/area*(xn_node*dxndx + yn_node*dyndx + zn_node*dzndx)
        daready = 1.0_dp/area*(xn_node*dxndy + yn_node*dyndy + zn_node*dzndy)
        dareadz = 1.0_dp/area*(xn_node*dxndz + yn_node*dyndz + zn_node*dzndz)

! The area does not depend on the grid coordinates at time levels other than
! the current one - zero them out if not on current time level

        do k = 1, ntp
          if ( k == 1 ) then
            node_stencil(i)%dwhatdx(j,k) = (dwdx(k) -bfacespeed(i)*dareadx)/area
            node_stencil(i)%dwhatdy(j,k) = (dwdy(k) -bfacespeed(i)*daready)/area
            node_stencil(i)%dwhatdz(j,k) = (dwdz(k) -bfacespeed(i)*dareadz)/area
          else
            node_stencil(i)%dwhatdx(j,k) = dwdx(k)/area
            node_stencil(i)%dwhatdy(j,k) = dwdy(k)/area
            node_stencil(i)%dwhatdz(j,k) = dwdz(k)/area
          endif
        end do
      end do

      deallocate(node_stencil(i)%dwdx, node_stencil(i)%dwdy)
      deallocate(node_stencil(i)%dwdz)

    end do

  end subroutine dboundary_metrics

end module dshape_boundary

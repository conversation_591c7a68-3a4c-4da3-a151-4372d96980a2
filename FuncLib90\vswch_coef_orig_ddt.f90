

!============================== VSWCH_COEF_ORIG ==============================80
!
!  Computes the viscous switching coefficient used turn off the inviscid
!  switching coefficient on cell faces that have small cell reynolds numbers
!
!=============================================================================80

  pure function vswch_coef_orig_ddt(rhol, rhor,                                &
                                    q2l, q2r, chalf, vol1, vol2, area,         &
                                    power, MU_face)

    use kinddefs,        only : dp
    use ddt,             only : ddt5, assignment(=),                           &
      ddt_min, ddt_max, ddt_sqrt,                                              &
      operator(+), operator(-), operator(*), operator(/), operator(**),        &
      operator(<=)
    use fun3d_constants, only : my_0, my_1, my_half

    integer,  intent(in) :: power

    type(ddt5), intent(in) :: rhol, rhor
    type(ddt5), intent(in) :: q2l, q2r, chalf
    real(dp),   intent(in) :: vol1, vol2, area
    real(dp),   intent(in) :: MU_face

    type(ddt5)             :: vswch_coef_orig_ddt

    type(ddt5) :: RO_face, U_face, L_face, RE_face

    real(dp), parameter :: RE_min  =    50.0_dp
    real(dp), parameter :: RE_max  =   500.0_dp

  continue

    RO_face = my_half*(rhol + rhor)
    U_face  = my_half*(my_half*(ddt_sqrt(q2l)+ddt_sqrt(q2r))+chalf)
    L_face  = my_half*(vol1 + vol2)/area
    RE_face = RO_face*U_face*L_face/MU_face
    vswch_coef_orig_ddt  = my_1-ddt_max(my_0, ddt_min(my_1, (RE_face-RE_min)/ &
                                           (RE_max-RE_min)))**power

  end function vswch_coef_orig_ddt

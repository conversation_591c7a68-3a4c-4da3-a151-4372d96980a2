include ../Common.am

cmplxlib_LIBRARIES = libcore.a

libcore_a_SOURCES =
libcore_a_LIBADD =
libcore_a_LINK = $(F90LINK)

nodist_libcore_a_SOURCES = $(libcore_SRCS)

BUILT_SOURCES += $(nodist_libcore_a_SOURCES)

CLEANFILES += $(BUILT_SOURCES)

%.f90: $(top_srcdir)/libcore/%.f90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.F90: $(top_srcdir)/libcore/%.F90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ > $@

# Install Fortran module files alongside the library
ax_lib_CMPLX_MODULES = $(libcore_SRCS:.f90=.mod)
lib_CMPLX_MODULES = $(ax_lib_CMPLX_MODULES:.F90=.mod)

cmplxlibdir = "$(libdir)/Complex"

libCMPLXMODULES_INSTALL = $(INSTALL_DATA)
install-exec-local:
	@$(NORMAL_INSTALL)
	test -z "$(cmplxlibdir)" || $(mkdir_p) "$(DESTDIR)$(cmplxlibdir)"
	@list="*.$(FC_MODEXT)"; for p in $$list; do \
	  if test -f $$p; then \
	    f=$(am__strip_dir) \
	    echo " $(libCMPLXMODULES_INSTALL) '$$p' '$(DESTDIR)$(cmplxlibdir)/$$f'";\
	    $(libCMPLXMODULES_INSTALL) "$$p" "$(DESTDIR)$(cmplxlibdir)/$$f"; \
	  else :; fi; \
	done
	@$(POST_INSTALL)

uninstall-local:
	@$(NORMAL_UNINSTALL)
	@list="*.$(FC_MODEXT)"; for p in $$list; do \
	  p=$(am__strip_dir) \
	  echo " rm -f '$(DESTDIR)$(cmplxlibdir)/$$p'"; \
	  rm -f "$(DESTDIR)$(cmplxlibdir)/$$p"; \
	done

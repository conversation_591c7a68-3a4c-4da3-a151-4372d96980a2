
! helps grid module by adding functionality

module grid_helper

  implicit none

  private

  public :: create_part
  public :: create_test_g2l
  public :: lookupg2l, findglobalentry
  public :: level0global
  public :: uniqueg2l
  public :: grid_reset_lmpi_xfer
  public :: grid_canonic_cell
  public :: grid_cell_unique

  public :: grid_maximum_imesh
  public :: grid_imesh_order
  public :: grid_contiguous_imesh

  public :: grid_contiguous_twod

  public :: max_tet_dot_product
  public :: gridpositivevolumetet
  public :: gridtetvolume

  public :: n2c_from_grid, deallocate_n2c, n2c_cell_with_face
  public :: n2c_type
  type n2c_type
    integer, dimension(:), pointer :: first_for => null()
    integer, dimension(:,:), pointer :: elem_and_cell => null()
  end type n2c_type

  public :: faces_from_grid, faces_find, deallocate_faces
  public :: faces_type
  type faces_type
    integer :: nface, nfaceg
    integer, dimension(:,:), pointer :: f2n
    integer, dimension(:),   pointer :: l2g
    integer :: emptyn2f
    integer, dimension(:),   pointer :: firstn2f
    integer, dimension(:,:), pointer :: n2f
  end type faces_type

  public :: allocate_edges, edges_find, edges_add_uniquely, deallocate_edges
  public :: edges_type
  type edges_type
    integer :: nedge
    integer, dimension(:,:), pointer :: e2n
    integer :: emptyn2e
    integer, dimension(:),   pointer :: firstn2e
    integer, dimension(:,:), pointer :: n2e
  end type edges_type

contains

!=============================================================================80
!
!=============================================================================80

  subroutine deallocate_n2c( n2c )

    type(n2c_type), intent(inout) :: n2c

    continue

    if (associated(n2c%first_for)) then
      deallocate( n2c%first_for )
      nullify( n2c%first_for )
    end if

    if (associated(n2c%elem_and_cell)) then
      deallocate( n2c%elem_and_cell )
      nullify( n2c%elem_and_cell )
    end if

  end subroutine deallocate_n2c

!=============================================================================80
!
!=============================================================================80

  subroutine n2c_from_grid( grid, n2c )

    use grid_types, only : grid_type

    type(grid_type), intent(in) :: grid
    type(n2c_type), intent(out) :: n2c

    integer :: ielem, icell, inode, node, comprow_index

    integer :: nnodes

    continue

    nnodes = grid%nnodes01

    allocate( n2c%first_for(nnodes+1) )
    do node = 1, nnodes+1
      n2c%first_for(node) = 0
    end do

    count_elemement_groups : do ielem = 1, grid%nelem
      count_cells : do icell = 1, grid%elem(ielem)%ncell
        count_cell_nodes : do inode = 1, grid%elem(ielem)%node_per_cell
          node = grid%elem(ielem)%c2n(inode,icell)
          if (node <= nnodes) &
            n2c%first_for(node) = n2c%first_for(node) + 1
        end do count_cell_nodes
      end do count_cells
    end do count_elemement_groups

    n2c%first_for(1) = n2c%first_for(1) + 1
    overfill_comprow : do node = 1, nnodes
      n2c%first_for(node+1) = &
        n2c%first_for(node+1) + n2c%first_for(node)
    end do overfill_comprow

    allocate( n2c%elem_and_cell( 2, n2c%first_for(nnodes+1)-1 ) )

    fill_elemement_groups : do ielem = 1, grid%nelem
      fill_cells : do icell = 1, grid%elem(ielem)%ncell
        fill_cell_nodes : do inode = 1, grid%elem(ielem)%node_per_cell
          node = grid%elem(ielem)%c2n(inode,icell)
          if (node <= nnodes) then
            n2c%first_for(node) = n2c%first_for(node) - 1
            comprow_index = n2c%first_for(node)
            n2c%elem_and_cell( 1, comprow_index ) = ielem
            n2c%elem_and_cell( 2, comprow_index ) = icell
          end if
        end do fill_cell_nodes
      end do fill_cells
    end do fill_elemement_groups

  end subroutine n2c_from_grid

!============================ find_cell_with_face ============================80
!
!=============================================================================80

  subroutine n2c_cell_with_face ( embed, n2c, nodes, elem, cell )

    use grid_types,           only : grid_type

    type(grid_type),                      intent(in)    :: embed
    type(n2c_type),                       intent(in)    :: n2c
    integer, dimension(:),                intent(in)    :: nodes
    integer,                              intent(out)   :: elem, cell

    integer :: comprow_index, cell_node, face_node, node, nfound

    continue

    cells_surrounding_node1 :do comprow_index = n2c%first_for(nodes(1)), &
                                                n2c%first_for(nodes(1)+1)-1
      elem = n2c%elem_and_cell(1,comprow_index)
      cell = n2c%elem_and_cell(2,comprow_index)
      nfound = 0
      do cell_node = 1, embed%elem(elem)%node_per_cell
        node = embed%elem(elem)%c2n(cell_node,cell)
        do face_node = 1, size(nodes,1)
          if ( node == nodes(face_node) ) nfound = nfound + 1
        end do
      end do
      if (nfound == size(nodes,1) ) then
        return
      end if
    end do cells_surrounding_node1

    write(*,*) ' ERROR: n2c_cell_with_face: cell not found'
    elem = 0
    cell = 0

  end subroutine n2c_cell_with_face

!=============================================================================80
!
!=============================================================================80

  subroutine deallocate_faces( faces )

    type(faces_type), intent(inout) :: faces

    continue

    if (associated(faces%f2n)) then
      deallocate( faces%f2n )
      nullify( faces%f2n )
    end if

    if (associated(faces%l2g)) then
      deallocate( faces%l2g )
      nullify( faces%l2g )
    end if

    if (associated(faces%firstn2f)) then
      deallocate( faces%firstn2f )
      nullify( faces%firstn2f )
    end if

    if (associated(faces%n2f)) then
      deallocate( faces%n2f )
      nullify( faces%n2f )
    end if

  end subroutine deallocate_faces

!=============================================================================80
!
!=============================================================================80

  function faces_find(faces, nodes)
    integer :: faces_find
    type(faces_type), intent(in) :: faces
    integer, dimension(4), intent(in) :: nodes
    integer :: target_node, canidate_node, n2f, face, nfound
    continue
    faces_find = 0
    n2f = faces%firstn2f(nodes(1))
    do while ( n2f > 0 )
      face = faces%n2f(1,n2f)
      nfound = 0
      each_target_node : do target_node = 1,4
        each_canidate_node : do canidate_node = 1,4
          if ( nodes( target_node ) == faces%f2n(canidate_node,face) ) then
            nfound = nfound + 1
            cycle each_target_node
          end if
        end do each_canidate_node
      end do each_target_node
      got_it : if ( 4 == nfound ) then
        faces_find = face
        return
      end if got_it
      n2f = faces%n2f(2,n2f)
    end do
  end function faces_find

  subroutine faces_add_uniquely(faces, nodes, global)
    use allocations,     only : my_realloc_ptr
    type(faces_type), intent(inout) :: faces
    integer, dimension(4), intent(in) :: nodes
    integer, intent(in) :: global
    integer :: nold, face
    continue
    face = faces_find(faces,nodes)
    if ( face > 0 ) return ! already have it
    nold = size(faces%f2n,2)
    need_more_f2n : if ( faces%nface >= nold ) then
      call my_realloc_ptr( faces%f2n, 4, nold+10000 )
      call my_realloc_ptr( faces%l2g, nold+10000 )
    end if need_more_f2n
    faces%nface = faces%nface + 1
    faces%f2n(:,faces%nface) = nodes
    faces%l2g(faces%nface) = global
    call faces_register(faces, nodes(1), faces%nface)
    call faces_register(faces, nodes(2), faces%nface)
    call faces_register(faces, nodes(3), faces%nface)
    if (nodes(1) /= nodes(4)) call faces_register(faces, nodes(4), faces%nface)
  end subroutine faces_add_uniquely

  subroutine faces_register(faces, node, face)
    use allocations,     only : my_realloc_ptr
    type(faces_type), intent(inout) :: faces
    integer, intent(in) :: node, face
    integer :: nold, n2f
    continue
    need_more_n2f : if ( 0 == faces%emptyn2f ) then
      nold = size(faces%n2f,2)
      call my_realloc_ptr( faces%n2f, 2, nold+40000 )
      do n2f = nold+1, size(faces%n2f,2)
        faces%n2f(1,n2f) = 0
        faces%n2f(2,n2f) = n2f+1
      end do
      faces%n2f(2,size(faces%n2f,2)) = 0
      faces%emptyn2f = nold+1
    end if need_more_n2f

    n2f = faces%emptyn2f
    faces%emptyn2f = faces%n2f(2,n2f)

    faces%n2f(1,n2f) = face
    faces%n2f(2,n2f) = faces%firstn2f(node)
    faces%firstn2f(node) = n2f

  end subroutine faces_register

  subroutine faces_from_grid( grid, faces )
    use grid_types, only : grid_type
    type(grid_type), intent(inout) :: grid
    type(faces_type), intent(out) :: faces

    logical, parameter :: quads_only = .true.

    continue

    call create_test_g2l(grid)
    if ( associated(grid%part) ) deallocate(grid%part)
    allocate(grid%part(grid%nnodes01))
    call create_part(grid%nnodes0, grid%nnodes01, grid%l2g, grid%part)

    call faces_unique_local( grid, faces, quads_only )
    call faces_push_local_faces( grid, faces )
    call faces_fill_missing_ghosts( grid, faces, quads_only )

    deallocate(grid%part)         ; grid%part => null()
    deallocate(grid%sortedglobal) ; grid%sortedglobal => null()
    deallocate(grid%sortedlocal)  ; grid%sortedlocal  => null()

  end subroutine faces_from_grid

  subroutine faces_unique_local( grid, faces,quads_only )
    use allocations,     only : my_alloc_ptr
    use grid_types, only : grid_type
    use lmpi, only : lmpi_master, lmpi_id, lmpi_nproc, &
                     lmpi_bcast, lmpi_gather

    type(grid_type), intent(inout) :: grid
    type(faces_type), intent(out) :: faces
    logical,          intent(in)  :: quads_only

    integer :: ielem, icell, iface, node, n2f
    integer, dimension(4) :: nodes

    integer, dimension(:), allocatable :: first_face_on_proc, unique_on_proc
    integer :: processor
    integer :: face

    continue

    faces%nface = 0
    call my_alloc_ptr( faces%f2n,4,10000 )
    call my_alloc_ptr( faces%l2g,10000 )
    faces%f2n = 0
    call my_alloc_ptr( faces%firstn2f, grid%nnodes01 )
    do node = 1, grid%nnodes01
      faces%firstn2f(node) = 0
    end do
    call my_alloc_ptr( faces%n2f,2,40000 )
    do n2f = 1, size(faces%n2f,2)
      faces%n2f(1,n2f) = 0
      faces%n2f(2,n2f) = n2f+1
    end do
    faces%n2f(2,size(faces%n2f,2)) = 0
    faces%emptyn2f = 1

    count_elemement_groups : do ielem = 1, grid%nelem
      count_cells : do icell = 1, grid%elem(ielem)%ncell
        count_cell_faces : do iface = 1, grid%elem(ielem)%face_per_cell
          nodes = grid%elem(ielem)%local_f2n(iface,1:4)
          if ( quads_only .and. (nodes(1) == nodes(4)) ) cycle count_cell_faces
          nodes = grid%elem(ielem)%c2n(nodes,icell)
          if ( grid_cell_unique(grid,nodes) ) then
            call faces_add_uniquely(faces,nodes,0)
          end if
        end do count_cell_faces
      end do count_cells
    end do count_elemement_groups

    allocate( unique_on_proc( lmpi_nproc ) )
    allocate( first_face_on_proc( lmpi_nproc ) )
    unique_on_proc = 0
    first_face_on_proc = 0

    call lmpi_gather(faces%nface,unique_on_proc)

    if (lmpi_master) then
      faces%nfaceg = sum(unique_on_proc)
      first_face_on_proc(1) = 0
      do processor = 2, lmpi_nproc
        first_face_on_proc(processor)=&
          first_face_on_proc(processor-1)+unique_on_proc(processor-1)
      end do
    end if
    call lmpi_bcast(faces%nfaceg)
    call lmpi_bcast(first_face_on_proc)

    do face = 1, faces%nface
      faces%l2g(face) = first_face_on_proc(lmpi_id+1) + face
    end do

    deallocate( unique_on_proc )
    deallocate( first_face_on_proc )

  end subroutine faces_unique_local

  subroutine faces_push_local_faces( grid, faces )

    use grid_types, only : grid_type
    use lmpi, only : lmpi_id, lmpi_nproc, lmpi_alltoall, lmpi_alltoallv
    use sort, only : lookup, set_up_lookup

    type(grid_type), intent(inout) :: grid
    type(faces_type), intent(inout) :: faces

    integer :: node
    integer, dimension(4) :: nodes

    integer :: face, processor

    integer :: my_part, destination, previous_node, previous_part
    integer, dimension(:), allocatable :: message_size_1, message_size_2
    integer, dimension(:), allocatable :: next_position
    integer :: message_total_1, message_total_2
    integer, dimension(:,:), allocatable :: message_body_1, message_body_2

    integer, dimension(:), allocatable :: sorted_global
    integer, dimension(:), allocatable :: sorted_local

    continue

    my_part = lmpi_id + 1

    allocate(message_size_1(lmpi_nproc))
    allocate(message_size_2(lmpi_nproc))
    message_size_1 = 0
    message_size_2 = 0

    count_face : do face = 1, faces%nface
      count_face_node : do node = 1, 4
        destination = grid%part(faces%f2n(node,face))
        count_has_destination : if (destination /= my_part) then
          count_prev_node : do previous_node = 1, node - 1
            previous_part = grid%part(faces%f2n(previous_node,face))
            if ( destination == previous_part ) cycle count_face_node
          end do count_prev_node
          message_size_1(destination)=message_size_1(destination)+1
        end if count_has_destination
      end do count_face_node
    end do count_face

    call lmpi_alltoall( message_size_1, message_size_2 )

    message_total_1 = sum( message_size_1 )
    message_total_2 = sum( message_size_2 )

    allocate( message_body_1(5, message_total_1) )
    allocate( message_body_2(5, message_total_2) )

    allocate(next_position(lmpi_nproc+1))

    next_position = 0
    next_position(1) = 1
    calc_next_position : do processor = 1, lmpi_nproc
      next_position(processor+1) = next_position(processor) &
                                 + message_size_1(processor)
    end do calc_next_position

    fill_face : do face = 1, faces%nface
      fill_face_node : do node = 1, 4
        destination = grid%part(faces%f2n(node,face))
        fill_has_destination : if (destination /= my_part) then
          fill_prev_node : do previous_node = 1, node - 1
            previous_part = grid%part(faces%f2n(previous_node,face))
            if ( destination == previous_part ) cycle fill_face_node
          end do fill_prev_node
          message_body_1(1:4,next_position(destination)) = &
            grid%l2g(faces%f2n(1:4,face))
           message_body_1(5,next_position(destination)) = faces%l2g(face)
          next_position(destination) = next_position(destination) +1
        end if fill_has_destination
      end do fill_face_node
    end do fill_face

    deallocate(next_position)

    call lmpi_alltoallv( message_body_1, message_size_1, &
                         message_body_2, message_size_2 )

    deallocate(message_size_1)
    deallocate(message_size_2)
    deallocate(message_body_1)

    allocate(sorted_global(grid%nnodes01))
    allocate(sorted_local(grid%nnodes01))
    call set_up_lookup( grid%nnodes01, grid%l2g, sorted_global, sorted_local )

    insert_face : do face = 1, message_total_2
      insert_node : do node = 1, 4
        nodes(node) = lookup(message_body_2(node,face), &
          grid%nnodes01, sorted_global, sorted_local)
      end do insert_node
      call faces_add_uniquely(faces,nodes,message_body_2(5,face))
    end do insert_face

    deallocate(sorted_global)
    deallocate(sorted_local)

    deallocate(message_body_2)

  end subroutine faces_push_local_faces

!=============================================================================80
!
!=============================================================================80

  subroutine deallocate_edges( edges )

    type(edges_type), intent(inout) :: edges

    continue

    if (associated(edges%e2n)) then
      deallocate( edges%e2n )
      nullify( edges%e2n )
    end if

    if (associated(edges%firstn2e)) then
      deallocate( edges%firstn2e )
      nullify( edges%firstn2e )
    end if

    if (associated(edges%n2e)) then
      deallocate( edges%n2e )
      nullify( edges%n2e )
    end if

  end subroutine deallocate_edges

!=============================================================================80
!
!=============================================================================80

  function edges_find(edges, nodes)
    integer :: edges_find
    type(edges_type), intent(in) :: edges
    integer, dimension(2), intent(in) :: nodes
    integer :: n2e, edge
    continue
    edges_find = 0
    n2e = edges%firstn2e(nodes(1))
    do while ( n2e > 0 )
      edge = edges%n2e(1,n2e)
      got_it : if ( ( nodes(1) == edges%e2n(1,edge) .and.  &
                      nodes(2) == edges%e2n(2,edge) ) .or. &
                    ( nodes(2) == edges%e2n(1,edge) .and.  &
                      nodes(1) == edges%e2n(2,edge) ) ) then
        edges_find = edge
        return
      end if got_it
      n2e = edges%n2e(2,n2e)
    end do
  end function edges_find

  subroutine edges_add_uniquely(edges, nodes)
    use allocations,     only : my_realloc_ptr
    type(edges_type), intent(inout) :: edges
    integer, dimension(2), intent(in) :: nodes
    integer :: nold, edge
    continue
    edge = edges_find(edges,nodes)
    if ( edge > 0 ) return ! already have it
    nold = size(edges%e2n,2)
    need_more_f2n : if ( edges%nedge >= nold ) then
      call my_realloc_ptr( edges%e2n, 2, nold+1000000 )
    end if need_more_f2n
    edges%nedge = edges%nedge + 1
    edges%e2n(:,edges%nedge) = nodes
    call edges_register(edges, nodes(1), edges%nedge)
    call edges_register(edges, nodes(2), edges%nedge)
  end subroutine edges_add_uniquely

  subroutine edges_register(edges, node, edge)
    use allocations,     only : my_realloc_ptr
    type(edges_type), intent(inout) :: edges
    integer, intent(in) :: node, edge
    integer :: nold, n2e
    continue
    need_more_n2e : if ( 0 == edges%emptyn2e ) then
      nold = size(edges%n2e,2)
      call my_realloc_ptr( edges%n2e, 2, nold+2000000 )
      do n2e = nold+1, size(edges%n2e,2)
        edges%n2e(1,n2e) = 0
        edges%n2e(2,n2e) = n2e+1
      end do
      edges%n2e(2,size(edges%n2e,2)) = 0
      edges%emptyn2e = nold+1
    end if need_more_n2e

    n2e = edges%emptyn2e
    edges%emptyn2e = edges%n2e(2,n2e)

    edges%n2e(1,n2e) = edge
    edges%n2e(2,n2e) = edges%firstn2e(node)
    edges%firstn2e(node) = n2e

  end subroutine edges_register

  subroutine allocate_edges( edges, nnodes )
    use allocations,     only : my_alloc_ptr
    type(edges_type), intent(out) :: edges
    integer, intent(in) :: nnodes
    integer :: node, n2e
    continue

    edges%nedge = 0
    call my_alloc_ptr( edges%e2n,2,100000 )
    edges%e2n = 0
    call my_alloc_ptr( edges%firstn2e, nnodes )
    do node = 1, nnodes
      edges%firstn2e(node) = 0
    end do
    call my_alloc_ptr( edges%n2e,2,200000 )
    do n2e = 1, size(edges%n2e,2)
      edges%n2e(1,n2e) = 0
      edges%n2e(2,n2e) = n2e+1
    end do
    edges%n2e(2,size(edges%n2e,2)) = 0
    edges%emptyn2e = 1

  end subroutine allocate_edges

!=============================== GRID_CELL_UNIQUE_NODE =======================80
!
! Ensures that the queried cell is not in the over-lap region between partitions
!
!=============================================================================80

  function grid_cell_unique_node( grid, cell_nodes )

    use grid_types, only : grid_type

    integer                            :: grid_cell_unique_node
    type(grid_type),        intent(in) :: grid
    integer,  dimension(:), intent(in) :: cell_nodes

    integer :: smallest_local, smallest_global, node

    continue

    smallest_local = cell_nodes(1)
    smallest_global = grid%l2g(cell_nodes(1))
    do node = 2, size(cell_nodes,1)
      if ( grid%l2g(cell_nodes(node)) < smallest_global ) then
        smallest_local = cell_nodes(node)
        smallest_global = grid%l2g(cell_nodes(node))
      end if
    end do

    grid_cell_unique_node = smallest_local

  end function grid_cell_unique_node

!========================= FACE_FILL_MISSING_GHOSES ==========================80
!=============================================================================80

  subroutine faces_fill_missing_ghosts( grid, faces, quads_only )

    use grid_types, only : grid_type
    use lmpi,       only : lmpi_nproc, lmpi_conditional_stop,                  &
                           lmpi_alltoall, lmpi_alltoallv
    use sort,       only : lookup, set_up_lookup

    type(grid_type),  intent(inout) :: grid
    type(faces_type), intent(inout) :: faces
    logical,          intent(in   ) :: quads_only

    integer, dimension(:),   allocatable :: next_position
    integer, dimension(:,:), allocatable :: message_body_local
    integer, dimension(:,:), allocatable :: message_body_1,  message_body_2
    integer, dimension(:),   allocatable :: message_size_1,  message_size_2
    integer                              :: message_total_1, message_total_2

    integer, dimension(:), allocatable :: sorted_global, sorted_local

    integer, dimension(4) :: nodes
    integer               :: node

    integer :: face, processor, request, destination, ielem, icell, iface

    continue

    allocate( message_size_1(lmpi_nproc) ) ; message_size_1 = 0
    allocate( message_size_2(lmpi_nproc) ) ; message_size_2 = 0

    count_elemement_groups : do ielem = 1, grid%nelem
      count_cells : do icell = 1, grid%elem(ielem)%ncell
        count_cell_faces : do iface = 1, grid%elem(ielem)%face_per_cell
          nodes = grid%elem(ielem)%local_f2n(iface,1:4)
          if ( quads_only .and. (nodes(1) == nodes(4)) ) cycle count_cell_faces
          nodes = grid%elem(ielem)%c2n(nodes,icell)
          if ( 0 == faces_find(faces, nodes) ) then
            destination = grid%part(grid_cell_unique_node(grid,nodes))
            message_size_1(destination)=message_size_1(destination)+1
          end if
        end do count_cell_faces
      end do count_cells
    end do count_elemement_groups

    call lmpi_alltoall( message_size_1, message_size_2 )

    message_total_1 = sum( message_size_1 )
    message_total_2 = sum( message_size_2 )

    allocate( message_body_1( 5, message_total_1 ) )
    allocate( message_body_2( 5, message_total_2 ) )
    allocate( message_body_local( 5, message_total_1 ) )

    allocate( next_position(lmpi_nproc+1) ) ; next_position = 0

    next_position(1) = 1
    calc_next_position : do processor = 1, lmpi_nproc
      next_position(processor+1) = next_position(processor) &
                                 + message_size_1(processor)
    end do calc_next_position

    fill_elemement_groups : do ielem = 1, grid%nelem
      fill_cells : do icell = 1, grid%elem(ielem)%ncell
        fill_cell_faces : do iface = 1, grid%elem(ielem)%face_per_cell
          nodes = grid%elem(ielem)%local_f2n(iface,1:4)
          if ( quads_only .and. (nodes(1) == nodes(4)) ) cycle fill_cell_faces
          nodes = grid%elem(ielem)%c2n(nodes,icell)
          if ( 0 == faces_find(faces, nodes) ) then
            destination = grid%part(grid_cell_unique_node(grid,nodes))
            message_body_1(1:4,next_position(destination)) = grid%l2g(nodes)
            message_body_1(5,next_position(destination)) = 0
            message_body_local(1:4,next_position(destination)) = nodes
            message_body_local(5,next_position(destination)) = 0
            next_position(destination) = next_position(destination) +1
          end if
        end do fill_cell_faces
      end do fill_cells
    end do fill_elemement_groups

    deallocate( next_position )

    call lmpi_alltoallv( message_body_1, message_size_1, &
                         message_body_2, message_size_2 )

    allocate( sorted_global(grid%nnodes01), sorted_local(grid%nnodes01) )
    call set_up_lookup( grid%nnodes01, grid%l2g, sorted_global, sorted_local )

    find_face : do request = 1, message_total_2
      translate_node : do node = 1, 4
        nodes(node) = lookup(message_body_2(node,request), &
          grid%nnodes01, sorted_global, sorted_local)
      end do translate_node
      face = faces_find(faces, nodes)
      requested_face_missing : if ( 0 == face ) then
        write(*,*) 'face that should be local unique not found on part'
        call lmpi_conditional_stop(1, 'grid_helper:faces_push_local_faces')
      end if requested_face_missing
      message_body_2(5,request) = faces%l2g(face)
    end do find_face
    call lmpi_conditional_stop(0, 'grid_helper:faces_push_local_faces')

    deallocate( sorted_global, sorted_local )

    call lmpi_alltoallv( message_body_2, message_size_2, &
                         message_body_1, message_size_1 )

    deallocate( message_size_1, message_size_2 , message_body_2 )

    insert_face : do request = 1, message_total_1
      call faces_add_uniquely(faces, &
        message_body_local(1:4,request), message_body_1(5,request))
    end do insert_face

    deallocate( message_body_1 )

  end subroutine faces_fill_missing_ghosts

!=============================== CREATE_PART =================================80
!=============================================================================80

  subroutine create_part(nnodes0,nnodes01,l2g,part)

    use sort, only : set_up_lookup, lookup
    use lmpi, only : lmpi_id, lmpi_nproc, lmpi_bcast, lmpi_max

    integer, intent(in)                       :: nnodes0, nnodes01
    integer, dimension(nnodes01), intent(in)  :: l2g
    integer, dimension(nnodes01), intent(out) :: part

    integer :: processor, myid
    integer :: i, localnode, globalnode
    integer :: ghostnodes

    integer, dimension(:), allocatable :: ghostpart
    integer, dimension(:), allocatable :: sorted_global, sorted_local

    continue

    allocate( sorted_global(nnodes0), sorted_local(nnodes0) )
    call set_up_lookup( nnodes0, l2g, sorted_global, sorted_local )

    myid = lmpi_id+1

    do localnode = 1, nnodes0
      part(localnode) = myid
    end do

    do processor = 1, lmpi_nproc
      ghostnodes = nnodes01-nnodes0
      call lmpi_bcast(ghostnodes,processor-1)
      positive_ghostnodes : if ( ghostnodes > 0 ) then
        allocate(ghostpart(ghostnodes))
        client_node_request : if (processor == myid ) then
          do i = 1, ghostnodes
            localnode = i + nnodes0
            globalnode = l2g(localnode)
            ghostpart(i) = globalnode
          end do
        end if client_node_request
        call lmpi_bcast(ghostpart,processor-1)
        servers_lookup_nodes : if (processor /= myid ) then
          do i = 1, ghostnodes
            globalnode = ghostpart(i)
            localnode = lookup(globalnode, nnodes0, sorted_global, sorted_local)
            ghostpart(i) = -1
            if (localnode>0 .and. localnode <= nnodes0) &
              ghostpart(i) = myid
          end do
        else
          ghostpart = -1
        end if servers_lookup_nodes
        call lmpi_max(ghostpart,part(nnodes0+1:nnodes01),processor-1)
        deallocate(ghostpart)
      end if positive_ghostnodes
    end do

    deallocate( sorted_global, sorted_local )

  end subroutine create_part

!============================== CREATE_TEST_G2L ==============================80
!
!=============================================================================80

  subroutine create_test_g2l( grid )

    use grid_types,  only : grid_type
    use allocations, only : my_alloc_ptr
    use sort,        only : heap_sort

    type(grid_type), intent(inout) :: grid

    integer :: nnodes, size_allocated, i, localnode

    continue

    nnodes = grid%nnodes01
    size_allocated = size(grid%l2g,1)
    grid%nsorted = min(nnodes,size_allocated)
    if ( associated(grid%sortedglobal) ) deallocate(grid%sortedglobal)
    call my_alloc_ptr(grid%sortedglobal,size_allocated)
    if ( associated(grid%sortedlocal) ) deallocate(grid%sortedlocal)
    call my_alloc_ptr(grid%sortedlocal,size_allocated)

    do i = 1, grid%nsorted
      grid%sortedglobal(i) = grid%l2g(i)
    end do
    call heap_sort(grid%nsorted,grid%sortedglobal,grid%sortedlocal)
    do i = 1, grid%nsorted
      grid%sortedglobal(i) = grid%l2g(grid%sortedlocal(i))
    end do

    do i = 1, grid%nsorted
      localnode = lookupg2l(grid%l2g(i),grid)
      if (i /= localnode)                                                      &
        write(*,*) "create_test_g2l: lookupg2l failed: ",i,localnode,grid%l2g(i)
    end do

  end subroutine create_test_g2l

!=============================== lookupg2l ===================================80
!
! Inverse lookup from sorted list with index map (passthrough to sort's lookup)
!
!=============================================================================80
  elemental &
  function lookupg2l( global_index, grid )

    use grid_types, only : grid_type
    use sort,       only : lookup

    integer                     :: lookupg2l
    integer,         intent(in) :: global_index
    type(grid_type), intent(in) :: grid

    continue

    lookupg2l = lookup( global_index,                                          &
                        grid%nsorted, grid%sortedglobal, grid%sortedlocal )

  end function lookupg2l

!=============================== findglobalentry =============================80
!
! Binary search for ascending sorted list (passthrough to sort's binary_search)
!
!=============================================================================80

  function findglobalentry( grid, node )

    use grid_types, only : grid_type
    use sort,       only : binary_search

    integer                     :: findglobalentry
    type(grid_type), intent(in) :: grid
    integer,         intent(in) :: node

    continue

    findglobalentry = binary_search( grid%nsorted, grid%sortedglobal, node )

  end function findglobalentry

!============================== level0global =================================80
!
!=============================================================================80

  function level0global(grid,globalnode)
    use grid_types,      only : grid_type

    logical                     :: level0global
    type(grid_type), intent(in) :: grid
    integer,         intent(in) :: globalnode

    integer :: localnode

    continue

    localnode = lookupg2l(globalnode,grid)

    level0global = ( localnode > 0 .and. localnode <= grid%nnodes0 )

  end function level0global

!============================== uniqueg2l ====================================80
!
!=============================================================================80

  function uniqueg2l(grid,globalnode)
    use grid_types,      only : grid_type

    integer                        :: uniqueg2l
    type(grid_type), intent(inout) :: grid
    integer,         intent(in)    :: globalnode

    integer :: localnode
    integer :: maxnode
    integer :: indx
    integer :: insertpoint

    continue

    localnode = lookupg2l(globalnode,grid)

    node_exists : if ( localnode > 0 ) then
      uniqueg2l = localnode
      return
    end if node_exists

    reuse_removed_node : if (grid%firstemptynode > 0 ) then
      localnode = grid%firstemptynode
      grid%firstemptynode = -grid%l2g(localnode)
    else
      maxnode = size(grid%l2g,1)

      if (grid%nnodes01 >= maxnode) then
        maxnode = maxnode + 5000
        call grid_realloc_node_vectors(grid, maxnode)
      end if

      grid%nnodes01 = grid%nnodes01 + 1
      localnode = grid%nnodes01
    end if reuse_removed_node

    grid%l2g(localnode) = globalnode

    insertpoint = grid%nsorted+1
    find_insert_point : do indx = 1, grid%nsorted
      if (grid%sortedglobal(indx) > globalnode) then
        insertpoint = indx
        exit find_insert_point
      end if
    end do find_insert_point

    slide_global_up : do indx = grid%nsorted, insertpoint, -1
      grid%sortedglobal(indx+1) = grid%sortedglobal(indx)
    end do slide_global_up
    slide_local_up : do indx = grid%nsorted, insertpoint, -1
      grid%sortedlocal(indx+1) = grid%sortedlocal(indx)
    end do slide_local_up

    grid%nsorted = grid%nsorted + 1
    grid%sortedglobal(insertpoint) = globalnode
    grid%sortedlocal(insertpoint) = localnode

    uniqueg2l = localnode

  end function uniqueg2l

!============================== grid_realloc_node_vectors ====================80
!
!=============================================================================80

  subroutine grid_realloc_node_vectors(grid, newsize)
    use grid_types,      only : grid_type
    use allocations,     only : my_realloc_ptr

    type(grid_type), intent(inout) :: grid
    integer,         intent(in)    :: newsize

    continue

    if (associated(grid%l2g)) call my_realloc_ptr(grid%l2g,newsize)
    if (associated(grid%firstn2c)) call my_realloc_ptr(grid%firstn2c,newsize)
    if (associated(grid%x)) call my_realloc_ptr(grid%x,newsize)
    if (associated(grid%y)) call my_realloc_ptr(grid%y,newsize)
    if (associated(grid%z)) call my_realloc_ptr(grid%z,newsize)
    if (associated(grid%sortedglobal))                                     &
      call my_realloc_ptr(grid%sortedglobal,newsize)
    if (associated(grid%sortedlocal))                                      &
      call my_realloc_ptr(grid%sortedlocal,newsize)
    if (associated(grid%part)) call my_realloc_ptr(grid%part,newsize)

  end subroutine grid_realloc_node_vectors

!============================ grid_reset_lmpi_xfer ===========================80
!
!=============================================================================80

  subroutine grid_reset_lmpi_xfer (grid)

    use grid_types,           only : grid_type
    use lmpi,                 only : lmpi_id, lmpi_nproc, lmpi_master,         &
                                     lmpi_reduce, lmpi_bcast,                  &
                                     lmpi_conditional_stop
    use lmpi_app,             only : lmpi_reset_sendrecv, lmpi_test_xfer,      &
                                     lmpi_sendreceive

    type(grid_type),                              intent(inout) :: grid

    integer :: lastlocalnode, lastghostnode

    integer, dimension(lmpi_nproc,lmpi_nproc) :: messagesize, temp
    integer :: clientid, serverid
    integer :: localnode, globalnode
    integer :: processor, istop
    integer :: i, indx

    integer, dimension(lmpi_nproc+1) :: clientindex
    integer, dimension(:),   allocatable :: clientnode
    integer :: clientsize

    integer, dimension(lmpi_nproc+1) :: serverindex
    integer, dimension(:),   allocatable :: servernode
    integer :: serversize

    integer, dimension(lmpi_nproc) :: counter

    logical, parameter :: verbose = .false.

    continue

    if ( lmpi_nproc == 1 ) then
      if (verbose .and. lmpi_master) write(*,*)" nproc == 1, skipping..."
      return
    end if

    lastlocalnode = grid%nnodes0
    lastghostnode = grid%nnodes01

    clientid = lmpi_id + 1
    messagesize = 0
    do localnode = lastlocalnode + 1, lastghostnode
      serverid = grid%part(localnode)
      messagesize(clientid,serverid) = messagesize(clientid,serverid) + 1
    end do

    call lmpi_reduce(messagesize, temp)
    if (lmpi_master) messagesize = temp
    call lmpi_bcast(messagesize)

    if (verbose .and. lmpi_master) then
      write(*,'(a,20i6)') "  server    ",(i,i=1, lmpi_nproc)
      do clientid = 1, lmpi_nproc
        write(*,'(a,i3,20i6)') "  client ", clientid, messagesize(clientid,:)
      end do
    end if

    clientid = lmpi_id + 1
    clientsize = sum(messagesize(clientid,:))
    clientindex(1) = 1
    do processor = 1, lmpi_nproc
      clientindex(processor+1) = clientindex(processor)                        &
                               + messagesize(clientid,processor)
    end do
    allocate(clientnode(clientsize))

    serverid = lmpi_id + 1
    serversize = sum(messagesize(:,serverid))
    serverindex(1) = 1
    do processor = 1, lmpi_nproc
      serverindex(processor+1) = serverindex(processor)                        &
                               + messagesize(processor,serverid)
    end do
    allocate(servernode(serversize))

    counter = 0
    do localnode = lastlocalnode + 1, lastghostnode
      globalnode = grid%l2g(localnode)
      serverid = grid%part(localnode)
      clientnode(clientindex(serverid)+counter(serverid)) = globalnode
      counter(serverid) = counter(serverid) + 1
    end do

    istop = 0
    clientid = lmpi_id + 1
    do serverid = 1, lmpi_nproc
      if ( counter(serverid) /= messagesize(clientid,serverid) ) then
        istop = 1
        write(*,*)" proc ",lmpi_id, " bad message length ",                  &
          counter(serverid), messagesize(clientid,serverid)
      endif
    end do
    call lmpi_conditional_stop(istop)

    if (verbose .and. lmpi_master) write(*,*)" send node requests to servers..."
    call lmpi_sendreceive(clientnode, clientindex, servernode, serverindex)

    if (verbose .and. lmpi_master) &
      write(*,*)" convert global index to local index..."
    do indx = 1, clientsize
      clientnode(indx) = lookupg2l(clientnode(indx),grid)
    end do
    do indx = 1, serversize
      servernode(indx) = lookupg2l(servernode(indx),grid)
    end do

    istop = 0
    client_g2l_test : do indx = 1, clientsize
      if (clientnode(indx) == 0) then
        write(*,*) lmpi_id, " client g2l error ", indx
        istop = 1
        exit client_g2l_test
      end if
    end do client_g2l_test
    server_g2l_test : do indx = 1, serversize
      if (servernode(indx) == 0) then
        write(*,*) lmpi_id, " server g2l error ", indx
        istop = 1
        exit server_g2l_test
      end if
    end do server_g2l_test
    client_setting_test : do indx = 1, clientsize
      if (clientnode(indx) <= lastlocalnode) then
        write(*,*) lmpi_id, " client setting local node ", indx,            &
          clientnode(indx)
        istop = 1
        exit client_setting_test
      end if
    end do client_setting_test
    server_sending_test : do indx = 1, serversize
      if (servernode(indx) > lastlocalnode) then
        write(*,*) lmpi_id, " server sending ghost node ", indx,            &
          servernode(indx)
        istop = 1
        exit server_sending_test
      end if
    end do server_sending_test
    call lmpi_conditional_stop(istop)

    if (verbose .and. lmpi_master) &
      write(*,*)" reset lmpi_xfer send, recieve pairs..."
    call lmpi_reset_sendrecv( 1,                                             &
      serverindex, servernode, clientindex, clientnode)

    if (verbose .and. lmpi_master) write(*,*)" test lmpi_xfer..."
    call lmpi_test_xfer( lastlocalnode, lastghostnode, grid%l2g )

    deallocate(clientnode)
    deallocate(servernode)

  end subroutine grid_reset_lmpi_xfer

!=============================== grid_canonic_cell ===========================80
!=============================================================================80

  subroutine grid_canonic_cell(grid,original,canonical)

    use grid_types,           only : grid_type

    type(grid_type),        intent(in)  :: grid
    integer,  dimension(:), intent(in)  :: original
    integer,  dimension(:), intent(out) :: canonical

    integer,  dimension(4) :: global

    continue

    non_tets : if ( size(original,1) /= 4) then
      canonical = original
      return
    end if non_tets

    global = grid%l2g(original)

    canonical = 0 ! in case nodes are numbered improperly

    global_node_1_lowest : if ( global(1) < global(2) .and. &
                                global(1) < global(3) .and. &
                                global(1) < global(4) ) then
      canonical(1) = original(1)
      if ( global(2) < global(3) .and. &
           global(2) < global(4) ) then
        canonical(2) = original(2)
        canonical(3) = original(3)
        canonical(4) = original(4)
        return
      end if
      if ( global(3) < global(2) .and. &
           global(3) < global(4) ) then
        canonical(2) = original(3)
        canonical(3) = original(4)
        canonical(4) = original(2)
        return
      end if
      if ( global(4) < global(2) .and. &
           global(4) < global(3) ) then
        canonical(2) = original(4)
        canonical(3) = original(2)
        canonical(4) = original(3)
        return
      end if
    end if global_node_1_lowest

    global_node_2_lowest : if ( global(2) < global(1) .and. &
                                global(2) < global(3) .and. &
                                global(2) < global(4) ) then
      canonical(1) = original(2)
      if ( global(1) < global(3) .and. &
           global(1) < global(4) ) then
        canonical(2) = original(1)
        canonical(3) = original(4)
        canonical(4) = original(3)
        return
      end if
      if ( global(3) < global(1) .and. &
           global(3) < global(4) ) then
        canonical(2) = original(3)
        canonical(3) = original(1)
        canonical(4) = original(4)
        return
      end if
      if ( global(4) < global(1) .and. &
           global(4) < global(3) ) then
        canonical(2) = original(4)
        canonical(3) = original(3)
        canonical(4) = original(1)
        return
      end if
    end if global_node_2_lowest

    global_node_3_lowest : if ( global(3) < global(1) .and. &
                                global(3) < global(2) .and. &
                                global(3) < global(4) ) then
      canonical(1) = original(3)
      if ( global(1) < global(2) .and. &
           global(1) < global(4) ) then
        canonical(2) = original(1)
        canonical(3) = original(2)
        canonical(4) = original(4)
        return
      end if
      if ( global(2) < global(1) .and. &
           global(2) < global(4) ) then
        canonical(2) = original(2)
        canonical(3) = original(4)
        canonical(4) = original(1)
        return
      end if
      if ( global(4) < global(1) .and. &
           global(4) < global(2) ) then
        canonical(2) = original(4)
        canonical(3) = original(1)
        canonical(4) = original(2)
        return
      end if
    end if global_node_3_lowest

    global_node_4_lowest : if ( global(4) < global(1) .and. &
                                global(4) < global(2) .and. &
                                global(4) < global(3) ) then
      canonical(1) = original(4)
      if ( global(1) < global(2) .and. &
           global(1) < global(3) ) then
        canonical(2) = original(1)
        canonical(3) = original(3)
        canonical(4) = original(2)
        return
      end if
      if ( global(2) < global(1) .and. &
           global(2) < global(3) ) then
        canonical(2) = original(2)
        canonical(3) = original(1)
        canonical(4) = original(3)
        return
      end if
      if ( global(3) < global(1) .and. &
           global(3) < global(2) ) then
        canonical(2) = original(3)
        canonical(3) = original(2)
        canonical(4) = original(1)
        return
      end if
    end if global_node_4_lowest

    write(*,*) 'error grid_canonic_cell nodes can not be sorted',global

  end subroutine grid_canonic_cell

!=============================== grid_cell_unique ============================80
!=============================================================================80

  function grid_cell_unique(grid,cell_nodes)

    use grid_types,           only : grid_type

    logical :: grid_cell_unique

    type(grid_type),        intent(in)  :: grid
    integer,  dimension(:), intent(in)  :: cell_nodes

    integer :: smallest_local, smallest_global, node

    continue

    smallest_local = cell_nodes(1)
    smallest_global = grid%l2g(cell_nodes(1))
    do node = 2, size(cell_nodes,1)
      if ( grid%l2g(cell_nodes(node)) < smallest_global ) then
        smallest_local = cell_nodes(node)
        smallest_global = grid%l2g(cell_nodes(node))
      end if
    end do

    grid_cell_unique = ( smallest_local > 0  .and. &
                         smallest_local <= grid%nnodes0 )

  end function grid_cell_unique

!=============================== grid_maximum_imesh ==========================80
!=============================================================================80

  subroutine grid_maximum_imesh(grid, maximum_imesh)

    use grid_types,           only : grid_type
    use lmpi,                 only : lmpi_max, lmpi_bcast

    type(grid_type),        intent(in)  :: grid

    integer, intent(out) :: maximum_imesh
    integer :: local_maximum
    integer :: node

    continue

    maximum_imesh = 0

    do node = 1, grid%nnodes0
      maximum_imesh = max( maximum_imesh, grid%imesh(node) )
    end do
    local_maximum = maximum_imesh
    call lmpi_max(local_maximum, maximum_imesh)
    call lmpi_bcast(maximum_imesh)

  end subroutine grid_maximum_imesh

!=============================== grid_imesh_order ============================80
!=============================================================================80

  subroutine grid_imesh_order(grid, maximum_imesh)

    use grid_types,           only : grid_type
    use lmpi,                 only : lmpi_min, lmpi_bcast, lmpi_master
    use sort,                 only : heap_sort
    use overset_defs,         only : imesh_order

    type(grid_type),        intent(in)  :: grid

    integer, intent(in) :: maximum_imesh

    integer, dimension(maximum_imesh+1) :: min_glob_index
    integer :: local_minimum
    integer :: node, mesh

    continue

    do mesh = 0, maximum_imesh
      imesh_order(mesh+1) = mesh+1
      min_glob_index(mesh+1) = grid%nnodesg

      do node = 1, grid%nnodes0
        if ( grid%imesh(node) == mesh ) then
          min_glob_index(mesh+1) = min( min_glob_index(mesh+1), grid%l2g(node) )
        end if
      end do
      local_minimum = min_glob_index(mesh+1)
      call lmpi_min(local_minimum, min_glob_index(mesh+1))
      call lmpi_bcast(min_glob_index(mesh+1))
    end do

    call heap_sort(maximum_imesh+1,min_glob_index,imesh_order)

    do mesh = 0, maximum_imesh
      imesh_order(mesh+1) = imesh_order(mesh+1) - 1
      if (lmpi_master) then
        write(*,*) 'Comp# of Mesh #', mesh+1, ' is ', imesh_order(mesh+1)
      end if
    end do

  end subroutine grid_imesh_order

!=============================== grid_contiguous_imesh =======================80
!=============================================================================80

  subroutine grid_contiguous_imesh(grid)

    use grid_types,           only : grid_type
    use lmpi,                 only : lmpi_id, lmpi_nproc,                      &
                                     lmpi_allgather, lmpi_conditional_stop
    use lmpi_app,             only : lmpi_xfer
    use overset_defs,         only : imesh_order

    type(grid_type),        intent(inout)  :: grid

    integer :: maximum_imesh

    integer :: local_component_nodes
    integer, dimension(lmpi_nproc) :: component_nodes_per_proc
    integer, dimension(lmpi_nproc) :: processor_offset

    integer :: node, processor, partid, completed_nodes
    integer :: c, component, local_unique

    continue

    partid = lmpi_id+1

    call grid_maximum_imesh(grid, maximum_imesh)

    completed_nodes = 0

    do c = 1, maximum_imesh+1

! Retrieve imesh order of original mesh
      component = imesh_order(c)

      local_component_nodes = 0
      do node = 1, grid%nnodes0
        if (grid%imesh(node) == component ) then
          local_component_nodes = local_component_nodes + 1
        end if
      end do

      call lmpi_allgather(local_component_nodes,component_nodes_per_proc)

      processor_offset = 0
      do processor = 2, lmpi_nproc
        processor_offset(processor) = processor_offset(processor-1) &
                          + component_nodes_per_proc(processor-1)
      end do

      local_unique = processor_offset(partid) + completed_nodes

      do node = 1, grid%nnodes0
        if (grid%imesh(node) == component )then
          local_unique = local_unique + 1
          grid%l2g(node) = local_unique
        end if
      end do

      completed_nodes = completed_nodes              &
                      + processor_offset(lmpi_nproc) &
                      + component_nodes_per_proc(lmpi_nproc)

    end do

    if ( grid%nnodesg /= completed_nodes ) &
      call lmpi_conditional_stop(1,'grid_contiguous_imesh, global miscount')

    call lmpi_conditional_stop(0,'grid_contiguous_imesh, global miscount')

    call lmpi_xfer(grid%l2g)

  end subroutine grid_contiguous_imesh

!================================ grid_contiguous_twod =======================80
!=============================================================================80

  subroutine grid_contiguous_twod(grid)

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use lmpi,                 only : lmpi_id, lmpi_nproc,                      &
                                     lmpi_allgather, lmpi_conditional_stop
    use lmpi_app,             only : lmpi_xfer

    type(grid_type),        intent(inout)  :: grid

    integer :: local_yplane_nodes
    integer, dimension(lmpi_nproc) :: yplane_nodes_per_proc
    integer, dimension(lmpi_nproc) :: processor_offset

    integer :: node, processor, partid, completed_nodes
    integer :: p, local_unique

    real(dp) :: yplane, ydiff
    real(dp), dimension(2) :: iplane_order
    real(dp), parameter :: my_zero = 0.0_dp
    real(dp), parameter :: my_one  = 1.0_dp
    real(dp), parameter :: my_tol  = 1.0e-15_dp

    continue

    partid = lmpi_id+1

    completed_nodes = 0

    iplane_order(1) = my_zero
    iplane_order(2) = my_one

    do p = 1, 2

      yplane = iplane_order(p)

      local_yplane_nodes = 0
      do node = 1, grid%nnodes0
        ydiff = abs( abs(grid%y(node)) - yplane )
        if ( ydiff < my_tol ) then
          local_yplane_nodes = local_yplane_nodes + 1
        end if
      end do

      call lmpi_allgather(local_yplane_nodes,yplane_nodes_per_proc)

      processor_offset = 0
      do processor = 2, lmpi_nproc
        processor_offset(processor) = processor_offset(processor-1) &
                          + yplane_nodes_per_proc(processor-1)
      end do

      local_unique = processor_offset(partid) + completed_nodes

      do node = 1, grid%nnodes0
        ydiff = abs( abs(grid%y(node)) - yplane )
        if ( ydiff < my_tol )then
          local_unique = local_unique + 1
          grid%l2g(node) = local_unique
        end if
      end do

      completed_nodes = completed_nodes              &
                      + processor_offset(lmpi_nproc) &
                      + yplane_nodes_per_proc(lmpi_nproc)

    end do

    if ( grid%nnodesg /= completed_nodes ) &
      call lmpi_conditional_stop(1,'grid_contiguous_twod, global miscount')

    call lmpi_conditional_stop(0,'grid_contiguous_twod, global miscount')

    call lmpi_xfer(grid%l2g)

  end subroutine grid_contiguous_twod

!=================================== max_tet_dot_product =====================80
!
! the maximum tet face normal dot product
!
!=============================================================================80

  function max_tet_dot_product(node1, node2, node3, node4, nnodes, x, y, z)

    use kinddefs,        only : dp

    real(dp)                                        :: max_tet_dot_product
    integer,                          intent(in)    :: node1,node2,node3,node4
    integer,                          intent(in)    :: nnodes
    real(dp), dimension(nnodes),      intent(in)    :: x,y,z

    real(dp)    :: x1, x2, x3, x4
    real(dp)    :: y1, y2, y3, y4
    real(dp)    :: z1, z2, z3, z4

    real(dp)    :: nx1, nx2, nx3, nx4
    real(dp)    :: ny1, ny2, ny3, ny4
    real(dp)    :: nz1, nz2, nz3, nz4

    real(dp)    :: rlen1, xnorm1, ynorm1, znorm1
    real(dp)    :: rlen2, xnorm2, ynorm2, znorm2
    real(dp)    :: rlen3, xnorm3, ynorm3, znorm3
    real(dp)    :: rlen4, xnorm4, ynorm4, znorm4

    real(dp)    :: dot, maxdot

    real(dp), parameter    :: my_haf = 0.5_dp

    continue

    x1 = x(node1)
    x2 = x(node2)
    x3 = x(node3)
    x4 = x(node4)

    y1 = y(node1)
    y2 = y(node2)
    y3 = y(node3)
    y4 = y(node4)

    z1 = z(node1)
    z2 = z(node2)
    z3 = z(node3)
    z4 = z(node4)

! Lets get outward normals (nx_i is for the face opposite node i)

    nx1 = my_haf*((y2 - y4)*(z3 - z4) - (y3 - y4)*(z2 - z4))
    ny1 = my_haf*((z2 - z4)*(x3 - x4) - (z3 - z4)*(x2 - x4))
    nz1 = my_haf*((x2 - x4)*(y3 - y4) - (x3 - x4)*(y2 - y4))

    nx2 = my_haf*((y3 - y4)*(z1 - z4) - (y1 - y4)*(z3 - z4))
    ny2 = my_haf*((z3 - z4)*(x1 - x4) - (z1 - z4)*(x3 - x4))
    nz2 = my_haf*((x3 - x4)*(y1 - y4) - (x1 - x4)*(y3 - y4))

    nx3 = my_haf*((y1 - y4)*(z2 - z4) - (y2 - y4)*(z1 - z4))
    ny3 = my_haf*((z1 - z4)*(x2 - x4) - (z2 - z4)*(x1 - x4))
    nz3 = my_haf*((x1 - x4)*(y2 - y4) - (x2 - x4)*(y1 - y4))

    nx4 = -nx1 -nx2 -nx3
    ny4 = -ny1 -ny2 -ny3
    nz4 = -nz1 -nz2 -nz3

    rlen1 = sqrt(nx1*nx1 + ny1*ny1 + nz1*nz1)
    xnorm1 = nx1/rlen1
    ynorm1 = ny1/rlen1
    znorm1 = nz1/rlen1

    rlen2 = sqrt(nx2*nx2 + ny2*ny2 + nz2*nz2)
    xnorm2 = nx2/rlen2
    ynorm2 = ny2/rlen2
    znorm2 = nz2/rlen2

    rlen3 = sqrt(nx3*nx3 + ny3*ny3 + nz3*nz3)
    xnorm3 = nx3/rlen3
    ynorm3 = ny3/rlen3
    znorm3 = nz3/rlen3

    rlen4 = sqrt(nx4*nx4 + ny4*ny4 + nz4*nz4)
    xnorm4 = nx4/rlen4
    ynorm4 = ny4/rlen4
    znorm4 = nz4/rlen4

    dot = xnorm1*xnorm2 + ynorm1*ynorm2 + znorm1*znorm2
    maxdot = dot
    dot = xnorm1*xnorm3 + ynorm1*ynorm3 + znorm1*znorm3
    maxdot = max(dot,maxdot)
    dot = xnorm1*xnorm4 + ynorm1*ynorm4 + znorm1*znorm4
    maxdot = max(dot,maxdot)
    dot = xnorm2*xnorm3 + ynorm2*ynorm3 + znorm2*znorm3
    maxdot = max(dot,maxdot)
    dot = xnorm2*xnorm4 + ynorm2*ynorm4 + znorm2*znorm4
    maxdot = max(dot,maxdot)
    dot = xnorm3*xnorm4 + ynorm3*ynorm4 + znorm3*znorm4
    maxdot = max(dot,maxdot)

    max_tet_dot_product = maxdot

  end function max_tet_dot_product

!=============================================================================80
! make sure that a tet has positive vol
!=============================================================================80
   function gridpositivevolumetet (grid, n1, n2, n3, n4)

    use kinddefs,        only : dp
    use grid_types,      only : grid_type

    logical                     :: gridpositivevolumetet
    type(grid_type), intent(in) :: grid

    integer,         intent(in) :: n1, n2, n3, n4

    real(dp), parameter    :: my_tiny = tiny(0.0_dp)

    continue

    gridpositivevolumetet = ( gridtetvolume (grid, n1, n2, n3, n4) > my_tiny )

  end function gridpositivevolumetet

!=============================================================================80
! volume of a tet
!=============================================================================80
   function gridtetvolume (grid, n1, n2, n3, n4)

    use kinddefs,        only : dp
    use grid_types,      only : grid_type

    real(dp)                    :: gridtetvolume
    type(grid_type), intent(in) :: grid

    integer,         intent(in) :: n1, n2, n3, n4

    real(dp)    :: ax,ay,az,bx,by,bz,cx,cy,cz
    real(dp)    :: temp

    real(dp),parameter    :: six = 6.0_dp

    continue

    ax = grid%x(n2) - grid%x(n1)
    ay = grid%y(n2) - grid%y(n1)
    az = grid%z(n2) - grid%z(n1)

    bx = grid%x(n3) - grid%x(n1)
    by = grid%y(n3) - grid%y(n1)
    bz = grid%z(n3) - grid%z(n1)

    cx = grid%x(n4) - grid%x(n1)
    cy = grid%y(n4) - grid%y(n1)
    cz = grid%z(n4) - grid%z(n1)

    temp = (ay*bz - by*az)*cx - (ax*bz - bx*az)*cy + (ax*by - bx*ay)*cz

    gridtetvolume = temp / six

  end function gridtetvolume

end module grid_helper

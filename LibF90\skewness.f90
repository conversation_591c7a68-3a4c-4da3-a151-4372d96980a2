module skewness

  use kinddefs,        only : dp, system_i1, system_i2
  use lmpi,            only : lmpi_max, lmpi_master, lmpi_reduce
  use trig_utils,      only : skew_degrees

  implicit none

  private

  public :: set_cell_skewness, set_cell_skewness_nc, check_face_skewness

contains

!=========================== CHECK_FACE_SKEWNESS =============================80
!
! Determine face-based LSQ stencil for cell-centered.
! Invoked by Party.     _gg=> global_grid.
!
!=============================================================================80
  subroutine check_face_skewness( nface, ncell0, ncell01, fptr,                &
                                  xn_face, yn_face, zn_face, xc, yc, zc )

    integer, intent(in) :: nface, ncell0, ncell01

    integer, dimension(6,nface),     intent(in) :: fptr

    real(dp), dimension(nface), intent(in) :: xn_face, yn_face, zn_face

    real(dp), dimension(ncell01), intent(in) :: xc, yc, zc

    integer :: n, cell1, cell2, icheck, ncellg, nfaceg
    integer(system_i2) :: iskew

    integer, dimension(22) :: n_exceed, n_temp, degree_check

    real(dp) :: ex, ey, ez, dsi, skew, skew_min, skew_max, term
    real(dp) :: skew_m90

  continue

    call set_degree_check( degree_check )

    n_exceed(:) = 0

    skew_max = -huge(1._dp)
    skew_min = +huge(1._dp)
    skew_m90 = +huge(1._dp)
    face_loop: do n = 1, nface

      cell1 = fptr(1,n)
      cell2 = fptr(2,n)

!     ex, ey, ez is unit vector along edge direction

      ex  = xc(cell2) - xc(cell1)
      ey  = yc(cell2) - yc(cell1)
      ez  = zc(cell2) - zc(cell1)
      dsi = 1.0_dp/sqrt( ex**2 + ey**2 + ez**2 )

      ex  = ex*dsi
      ey  = ey*dsi
      ez  = ez*dsi

      skew = skew_degrees( xn_face(n)*ex + yn_face(n)*ey + zn_face(n)*ez )

      skew_max = max( skew_max , skew )
      skew_min = min( skew_min , skew )
      skew_m90 = min( skew_m90 , abs( skew - 90._dp ) )

      iskew = nint( real(skew,dp) ) !..real for complexification.

      do icheck=1,22
        if ( iskew <= degree_check(icheck) ) cycle
        n_exceed(icheck) = n_exceed(icheck) + 1
      enddo

    end do face_loop

    skew = skew_max ; call lmpi_max( skew, skew_max )
    skew =-skew_min ; call lmpi_max( skew, skew_min ) ; skew_min = -skew_min
    skew =-skew_m90 ; call lmpi_max( skew, skew_m90 ) ; skew_m90 = -skew_m90

    n_temp=n_exceed ; call lmpi_reduce(n_temp,n_exceed)

    n=ncell0   ; call lmpi_reduce(n,ncellg)
    n=nface    ; call lmpi_reduce(n,nfaceg)

    if ( .not. lmpi_master ) return

    write(*,*)
    write(*,*) ' FLSQ:  Checking face skewness:'
    write(*,*) ' FLSQ:                  cells=',ncellg
    write(*,*) ' FLSQ:                  faces=',nfaceg

    write(*,*)
    write(*,"(1x,a,2f20.11)") ' FLSQ:SKEW  .................skew_min(deg)=',&
                                                            skew_min
    write(*,"(1x,a,2f20.11)") ' FLSQ:SKEW  .................skew_max(deg)=',&
                                                            skew_max
    write(*,"(1x,a,2f20.11)") ' FLSQ:SKEW  ...closest approach to 90(deg)=',&
                                                            skew_m90
    write(*,*)

    term = 100._dp/real(nfaceg,dp)
    if ( abs( skew_max ) > 1.0e-12_dp ) then
      do icheck=1,22
        write(*,"(1x,a,f10.3,a,i3,a,i10)")                                     &
        ' FLSQ:SKEW  .......% of faces=', term*real(n_exceed(icheck),dp),      &
        ' above skew angle =',degree_check(icheck),                            &
        ' deg faces=',n_exceed(icheck)
      enddo
    endif

  end subroutine check_face_skewness

!=========================== CHECK_FACE_SKEWNESS =============================80
!
! Determine face-based LSQ stencil for cell-centered.
! Invoked by Party.     _gg=> global_grid.
!
!=============================================================================80
  subroutine set_flsq_flag_lhs( nface, ncell01, fptr,                          &
                          xn_face, yn_face, zn_face, xc, yc, zc, flsq_flag_lhs )

    use cc_defs,only : allowable_skew_edge_based

    integer, intent(in) :: nface, ncell01

    integer, dimension(6,nface),     intent(in) :: fptr

    real(dp), dimension(nface), intent(in) :: xn_face, yn_face, zn_face

    real(dp), dimension(ncell01), intent(in) :: xc, yc, zc

    integer(kind=system_i1), dimension(nface), intent(inout) :: flsq_flag_lhs

    integer :: n, cell1, cell2
    integer(system_i2) :: iskew

    real(dp) :: ex, ey, ez, dsi, skew

  continue

    face_loop: do n = 1, nface

      cell1 = fptr(1,n)
      cell2 = fptr(2,n)

!     ex, ey, ez is unit vector along edge direction

      ex  = xc(cell2) - xc(cell1)
      ey  = yc(cell2) - yc(cell1)
      ez  = zc(cell2) - zc(cell1)
      dsi = 1.0_dp/sqrt( ex**2 + ey**2 + ez**2 )

      ex  = ex*dsi
      ey  = ey*dsi
      ez  = ez*dsi

      skew = skew_degrees( xn_face(n)*ex + yn_face(n)*ey + zn_face(n)*ez )

      iskew = nint( real(skew,dp) ) !..real for complexification.

      if ( iskew <= allowable_skew_edge_based ) flsq_flag_lhs(n) = 1

    end do face_loop

  end subroutine set_flsq_flag_lhs

!================================= SET_CELL_SKEWNESS =========================80
!
! Set skewness indicator for a cell by checking faces.
!
!=============================================================================80

  subroutine set_cell_skewness( grid )

    use grid_types,      only : grid_type
    use cc_defs,         only : allowable_skew_edge_based
    use check_face_lsq,  only : report_flsq

    type(grid_type), intent(inout) :: grid

  continue

    call set_skewness( grid%nface, grid%ncell0, grid%ncell01, grid%fptr,       &
                       grid%xn_face, grid%yn_face, grid%zn_face,               &
                       grid%xc, grid%yc, grid%zc, grid%cell_skewness )

    call check_face_skewness( grid%nface, grid%ncell0,                  &
                              grid%ncell01, grid%fptr,                  &
                              grid%xn_face, grid%yn_face, grid%zn_face, &
                              grid%xc, grid%yc, grid%zc )

    grid%flsq_flag_lhs(:) = 0 !...default LHS edge-based linearization.

    if ( grid%origin /= 1 ) return

    if ( allowable_skew_edge_based > 0 ) then
      call set_flsq_flag_lhs( grid%nface, grid%ncell01, grid%fptr,         &
                            grid%xn_face, grid%yn_face, grid%zn_face,      &
                            grid%xc, grid%yc, grid%zc,  grid%flsq_flag_lhs )

      call report_flsq( grid, -10, .true. )

    endif

  end subroutine set_cell_skewness

!================================= SET_SKEWNESS ==============================80
!
! Set skewness indicator for a cell by checking faces.
!
!=============================================================================80

  subroutine set_skewness( nface, ncell0, ncell01, fptr,                       &
                     xn_face, yn_face, zn_face, xc, yc, zc, cell_skewness )

    integer, intent(in) :: nface, ncell0, ncell01

    integer, dimension(6,nface),     intent(in) :: fptr

    real(dp), dimension(nface), intent(in) :: xn_face, yn_face, zn_face

    real(dp), dimension(ncell01), intent(in) :: xc, yc, zc

    integer(system_i2), dimension(:), intent(inout) :: cell_skewness

    integer :: n, cell1, cell2, n_total, icheck
    integer(system_i2) :: iskew

    integer, dimension(22) :: n_exceed, n_temp, degree_check

    real(dp) :: ex, ey, ez, dsi, skew, skew_min, skew_max
    real(dp) :: skew_m90

    logical, parameter :: debugging = .false.

  continue

    cell_skewness(:) = 0
    skew_max = -huge(1._dp)
    skew_min = +huge(1._dp)
    skew_m90 = +huge(1._dp)
    face_loop: do n = 1, nface

      cell1 = fptr(1,n)
      cell2 = fptr(2,n)

!     ex, ey, ez is unit vector along edge direction

      ex  = xc(cell2) - xc(cell1)
      ey  = yc(cell2) - yc(cell1)
      ez  = zc(cell2) - zc(cell1)
      dsi = 1.0_dp/sqrt( ex**2 + ey**2 + ez**2 )

      ex  = ex*dsi
      ey  = ey*dsi
      ez  = ez*dsi

      skew = skew_degrees( xn_face(n)*ex + yn_face(n)*ey + zn_face(n)*ez )

      skew_max = max( skew_max , skew )
      skew_min = min( skew_min , skew )
      skew_m90 = min( skew_m90 , abs( skew - 90._dp ) )

      iskew = nint( real(skew,dp) ) !..real for complexification.

      cell_skewness(cell1) = max ( cell_skewness(cell1), iskew )

      cell_skewness(cell2) = max ( cell_skewness(cell2), iskew )

    end do face_loop

    call set_degree_check( degree_check )

    do icheck=1,22
      n_exceed(icheck) = 0
      cell_loop: do n = 1, ncell0
        if ( cell_skewness(n) <= degree_check(icheck) ) cycle
        n_exceed(icheck) = n_exceed(icheck) + 1
      enddo cell_loop
    enddo

    skew = skew_max ; call lmpi_max( skew, skew_max )
    skew =-skew_min ; call lmpi_max( skew, skew_min ) ; skew_min = -skew_min
    skew =-skew_m90 ; call lmpi_max( skew, skew_m90 ) ; skew_m90 = -skew_m90
    n_temp=n_exceed ; call lmpi_reduce(n_temp,n_exceed)
    n=ncell0  ; call lmpi_reduce(n,n_total)
    if ( lmpi_master ) then
      write(*,*)
      write(*,"(1x,a,2f20.11)") ' SKEWNESS  .................skew_min(deg)=',&
                                                             skew_min
      write(*,"(1x,a,2f20.11)") ' SKEWNESS  .................skew_max(deg)=',&
                                                             skew_max
      write(*,"(1x,a,2f20.11)") ' SKEWNESS  ...closest approach to 90(deg)=',&
                                                             skew_m90
      write(*,*)
      if ( abs( skew_max ) > 1.0e-12_dp ) then
        do icheck=1,22
          write(*,"(1x,a,f10.3,a,i3,a,i10)")                                &
                               ' SKEWNESS  .......% of total cells=',       &
                       100._dp*real(n_exceed(icheck),dp)/real(n_total,dp),  &
                               ' above skew angle =',degree_check(icheck),  &
                               ' deg cells=',n_exceed(icheck)
        enddo
      endif
    endif

    if ( .not.debugging ) return
    write(81,"(1x,1x,a,3(18x,a),a)") 'cell','xc','yc','zc',' skew'
    do n = 1, ncell01
      write(81,"(1x,i5,3f20.10,i5)") n,xc(n),yc(n),zc(n),cell_skewness(n)
    end do

  end subroutine set_skewness

!================================= SET_CELL_SKEWNESS_NC ======================80
!
! Set skewness indicator for a cell by checking faces for node-centered scheme.
!
!=============================================================================80

  subroutine set_cell_skewness_nc( grid )

    use grid_types,      only : grid_type
    use info_depr,       only : twod

    type(grid_type), intent(inout) :: grid

    integer :: nedge

  continue

    nedge = grid%nedgeloc
    if ( twod .and. grid%origin == 1 ) nedge = grid%nedgeloc_2d

    call set_skewness_nc( grid%project, nedge, grid%nnodes0, grid%nnodes01,   &
                          grid%eptr, grid%xn, grid%yn, grid%zn,               &
                          grid%x , grid%y , grid%z , grid%slen,               &
                          grid%cell_skewness )

  end subroutine set_cell_skewness_nc

!================================= SET_SKEWNESS_NC ===========================80
!
! Set skewness indicator for a cell by checking faces for node-centered scheme.
!
!=============================================================================80

  subroutine set_skewness_nc( project, nedge, nnodes0, nnodes01, eptr,         &
                              xn, yn, zn, x, y, z, slen, node_skewness )

    use lmpi,       only : lmpi_id

    character(len=*), intent(in) :: project

    integer, intent(in) :: nedge, nnodes0, nnodes01

    integer, dimension(2,nedge),     intent(in) :: eptr

    real(dp), dimension(nedge), intent(in) :: xn, yn, zn

    real(dp), dimension(nnodes01), intent(in) :: x, y, z, slen

    integer(system_i2), dimension(:), intent(inout) :: node_skewness

    integer :: n, node1, node2, n_total, icheck, inversions
    integer(system_i2) :: iskew, iskew_max

    integer, dimension(22) :: n_exceed, n_temp, degree_check

    real(dp) :: ex, ey, ez, dsi, skew, skew_min, skew_max
    real(dp) :: skew_m90, slen_m90

    logical, parameter :: debugging = .false.

  continue

    node_skewness(:) = 0
    skew_max = -huge(1._dp)
    skew_min = +huge(1._dp)
    skew_m90 = +huge(1._dp)
    slen_m90 = +huge(1._dp)
    iskew_max = -1
    inversions= 0
    edge_loop: do n = 1, nedge

      node1 = eptr(1,n)
      node2 = eptr(2,n)

!     ex, ey, ez is unit vector along edge direction

      ex  = x(node2) - x(node1)
      ey  = y(node2) - y(node1)
      ez  = z(node2) - z(node1)
      dsi = 1.0_dp/sqrt( ex**2 + ey**2 + ez**2 )

      ex  = ex*dsi
      ey  = ey*dsi
      ez  = ez*dsi

      skew = skew_degrees( xn(n)*ex + yn(n)*ey + zn(n)*ez )

      skew_max = max( skew_max , skew )
      skew_min = min( skew_min , skew )
      skew_m90 = min( skew_m90 , abs( skew - 90._dp ) )

      if ( skew > 90._dp + 1.0e-12_dp ) then
        slen_m90 = min( slen_m90 , slen(node1) )
        slen_m90 = min( slen_m90 , slen(node2) )
        inversions = inversions + 1
      endif

      iskew = nint( real(skew,dp) ) !..real for complexification.

      node_skewness(node1) = max ( node_skewness(node1), iskew )

      node_skewness(node2) = max ( node_skewness(node2), iskew )

      iskew_max = max( iskew_max, iskew )

    end do edge_loop

    if ( iskew_max > 90 ) then
      write(*,"(1x,a,e20.12,a,i0)") &
      ' SKEWNESS: Min slen for all skew>90=',slen_m90,' lmpi_id=',lmpi_id
      write(*,"(1x,a,i20,a,i0)")    &
      ' SKEWNESS: ........Total inversions=',inversions,' lmpi_id=',lmpi_id
    endif
    if ( iskew_max > 90 ) then
      call tecplot_inversions( project, nedge, eptr, xn, yn, zn, x, y, z, &
                               slen, inversions, node_skewness, iskew_max )
    endif

    call set_degree_check( degree_check )

    do icheck=1,22
      n_exceed(icheck) = 0
      node_loop: do n = 1, nnodes0
        if ( node_skewness(n) <= degree_check(icheck) ) cycle
        n_exceed(icheck) = n_exceed(icheck) + 1
      enddo node_loop
    enddo

    skew = skew_max ; call lmpi_max( skew, skew_max )
    skew =-skew_min ; call lmpi_max( skew, skew_min ) ; skew_min = -skew_min
    skew =-skew_m90 ; call lmpi_max( skew, skew_m90 ) ; skew_m90 = -skew_m90
    n_temp=n_exceed ; call lmpi_reduce(n_temp,n_exceed)
    n=nnodes0  ; call lmpi_reduce(n,n_total)
    n=inversions ; call lmpi_reduce(n,inversions)
    if ( lmpi_master ) then
      write(*,*)
      write(*,"(1x,a,2f20.11)") ' SKEWNESS  .................skew_min(deg)=',&
                                                             skew_min
      write(*,"(1x,a,2f20.11)") ' SKEWNESS  .................skew_max(deg)=',&
                                                             skew_max
      write(*,"(1x,a,2f20.11)") ' SKEWNESS  ...closest approach to 90(deg)=',&
                                                             skew_m90
     if ( inversions > 0 )                                                   &
      write(*,"(1x,a,i0)")      ' SKEWNESS  ...WARNING......INVERTED_EDGES=',&
                                                             inversions
      write(*,*)
      if ( abs( skew_max ) > 1.0e-12_dp ) then
        do icheck=1,22
          write(*,"(1x,a,f10.3,a,i3,a,i10)")                                &
                               ' SKEWNESS  .......% of total nodes=',       &
                       100._dp*real(n_exceed(icheck),dp)/real(n_total,dp),  &
                               ' above skew angle =',degree_check(icheck),  &
                               ' deg nodes=',n_exceed(icheck)
        enddo
      endif
    endif

    if ( .not.debugging ) return
    write(81,"(1x,1x,a,3(18x,a),a)") 'node','x','y','z',' skew'
    do n = 1, nnodes01
      write(81,"(1x,i5,3f20.10,i5)") n,x(n),y(n),z(n),node_skewness(n)
    end do

  end subroutine set_skewness_nc

!===========================  SET_DEGREE_CHECK ===============================80
!
! Set degrees to check skewness.
!
!=============================================================================80
  subroutine set_degree_check( degree_check )

    integer, intent(out), dimension(:) :: degree_check

    integer :: n, n1

  continue

    degree_check(1) = 10
    do n=2,8
      degree_check(n) = degree_check(n-1) + 10
    enddo
    degree_check( 9) = 85
    degree_check(10) = 87
    degree_check(11) = 89
    do n=12,22
      n1 = 11 - ( n - 12 )
      degree_check(n) = 90 + ( 90 - degree_check(n1) )
    enddo

  end subroutine set_degree_check

!=============================== TECPLOT_INVERSIONS ==========================80
!
! Write Tecplot file composed of the points where the grid is inverted.
!
!=============================================================================80

  subroutine tecplot_inversions( project, nedge, eptr, xn, yn, zn, x, y, z, &
                                 slen, inversions, node_skewness, iskew_max )

    use lmpi,         only : lmpi_id
    use file_utils,   only : rm
    use string_utils, only : int_to_s

    character(len=*), intent(in) :: project

    integer, intent(in) :: nedge

    integer, dimension(2,nedge), intent(in) :: eptr

    real(dp), dimension(nedge), intent(in) :: xn, yn, zn

    real(dp), dimension(:), intent(in) :: x, y, z, slen

    integer, intent(in) :: inversions

    integer(system_i2), dimension(:), intent(in) :: node_skewness

    integer(system_i2), intent(in) :: iskew_max

    integer :: n, node1, node2, iu = 56

    real(dp) :: ex, ey, ez, dsi, skew, dot, ds

    character (len=256) :: filename


  continue

    if ( inversions > 0 ) write(*,"(1x,a,i5,a,i0)")     &
    ' SKEWNESS: Neighborhood of skew(deg)=',iskew_max,' lmpi_id=',lmpi_id

    filename = trim(project) // '_inverted_edges_p'
    filename = trim(filename) // trim(int_to_s(lmpi_id)) // '_tec.dat'

    if ( inversions > 0 ) then
      open(unit=iu, file=filename,status='unknown')
      rewind(iu)
      write(iu,'(a)') 'title="Inversions (Average points in edge)"'
      write(iu,'(2a)') 'variables="x", "y", "z", "slen" "skew" "dot" "ds"'
    else
      call rm( trim(filename) )
      return
    endif

    write(iu,"(a,a,i10)") 'zone T=InvertedEdges',' I=',inversions

    edge_loop: do n = 1, nedge

      node1 = eptr(1,n)
      node2 = eptr(2,n)

!     ex, ey, ez is unit vector along edge direction

      ex  = x(node2) - x(node1)
      ey  = y(node2) - y(node1)
      ez  = z(node2) - z(node1)
      dsi = 1.0_dp/sqrt( ex**2 + ey**2 + ez**2 )

      ex  = ex*dsi
      ey  = ey*dsi
      ez  = ez*dsi

      dot  = xn(n)*ex + yn(n)*ey + zn(n)*ez
      skew = skew_degrees( dot )

      if ( skew < 90._dp + 1.0e-12_dp ) cycle

      ex  = x(node2) - x(node1)
      ey  = y(node2) - y(node1)
      ez  = z(node2) - z(node1)
      ds  = sqrt( ex**2 + ey**2 + ez**2 )

      write(iu,"(3e20.10,4e10.2)")                    &
      0.5_dp*( x(node2) + x(node1) ),                 &
      0.5_dp*( y(node2) + y(node1) ),                 &
      0.5_dp*( z(node2) + z(node1) ),                 &
      0.5_dp*( slen(node2) + slen(node1) ),           &
      skew, dot, ds

      if ( node_skewness(node1) /= iskew_max ) cycle

      write(*,"(1x,a,i10,2f20.12)") ' SKEWNESS    edge,skew,ds=',&
      n,skew,1._dp/dsi,' lmpi_id=',lmpi_id
      write(*,"(1x,a,i10,4f20.12,a,i0)") ' SKEWNESS node,x,y,z,slen=',&
      node1,x(node1),y(node1),z(node1),slen(node2),' lmpi_id=',lmpi_id
      write(*,"(1x,a,i10,4f20.12,a,i0)") ' SKEWNESS node,x,y,z,slen=',&
      node2,x(node2),y(node2),z(node2),slen(node2),' lmpi_id=',lmpi_id

    end do edge_loop

    close(iu)

  end subroutine tecplot_inversions

end module skewness

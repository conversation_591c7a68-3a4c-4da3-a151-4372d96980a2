!============================== PUNDIT_MODULE =============================80
!
! This module provides routines for computing overset connectivity using
! Pundit
!
! Pundit is developed under DoD funding for HPC institute for Rotorcraft
! Aeromechanics (HIARMS) and can be available after necessary clearance
! procedures from the developer <PERSON>araman
!
!==========================================================================80

module pundit

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

#ifdef HAVE_PUNDIT
  use kinddefs, only : dp
#endif

  implicit none

  private

  public :: pundit_initialize, pundit_register_data
  public :: pundit_perform_connectivity, pundit_solution_update
  public :: pundit_write_output, pundit_flag

  logical :: pundit_flag = .false.  ! Enables overset grids using pundit

#ifdef HAVE_PUNDIT
  integer :: nv, ntetra, npyra, nprizm, nhexa, nwbc, nobc, nvar, nvart

  integer, dimension(:), pointer :: iblank
  integer, dimension(:), pointer :: bodytag

  integer, dimension(:),   pointer :: obcnode, wbcnode
  integer, dimension(:,:), pointer :: tet_c2n, pyr_c2n, prz_c2n, hex_c2n

  real(dp), dimension(5) :: scal

  real(dp), dimension(:),   pointer :: xv
  real(dp), dimension(:,:), pointer :: q
  real(dp), dimension(:,:), pointer :: turb

  logical, parameter :: verbose = .false.
#endif

contains


#ifdef HAVE_PUNDIT
!================================= PUNDIT_INITIALIZE =========================80
!
! Pass the communicator to pundit
!
!=============================================================================80

  subroutine pundit_initialize()

    use lmpi,            only : lmpi_master, lmpi_bcast
    use suggar_info,     only : world_comm
    use punditInterface, only : punditInitialize, pundit_read_params
    use file_utils,      only : file_exists

    logical :: pundit_file_exists

  continue

   call punditInitialize(world_comm)

! See if there is an input.pundit file present
! If so, instruct PUNDIT to read it in

   if ( lmpi_master ) pundit_file_exists = file_exists('input.pundit')
   call lmpi_bcast(pundit_file_exists)

   if ( pundit_file_exists ) then
     if ( lmpi_master ) then
       write(*,*) 'Found an input.pundit file...loading parameters.'
     endif
     call pundit_read_params()
   endif

   if ( lmpi_master .and. verbose ) write(*,*) 'Pundit initialized...'

  end subroutine pundit_initialize


!=============================== PUNDIT_REGISTER_DATA ========================80
!
! Register the partitioned grid data with pundit
!
!=============================================================================80

  subroutine pundit_register_data(grid,soln)

    use grid_types,      only : grid_type
    use solution_types,  only : soln_type
    use kinddefs,        only : dp
    use file_utils,      only : available_unit
    use lmpi,            only : lmpi_id, lmpi_die, lmpi_reduce, lmpi_bcast,    &
                                lmpi_min, lmpi_max, lmpi_master
    use punditInterface, only : set_nb_grid_data, reset_nb_grid_data
    use allocations,     only : my_alloc_ptr

    type(grid_type),  intent(inout) :: grid
    type(soln_type),  intent(inout) :: soln

    integer :: i,j,ios,f,min_node,hold_index,k,gn,value,global_count,nfound
    integer :: min_imesh, max_imesh, min_imesh_g, max_imesh_g

    character(len=128) :: imesh_filename

    logical, save :: init = .true.

    logical, parameter :: debug = .false.

  continue

    set_or_reset : if ( init ) then

      nv    = grid%nnodes01
      nvar  = soln%n_tot
      nvart = soln%n_turb

      call my_alloc_ptr(xv,3*nv)

      do i = 1, nv
        xv(3*i-2) = grid%x(i)
        xv(3*i-1) = grid%y(i)
        xv(3*i)   = grid%z(i)
      enddo

!  need to provide project_imesh.* files
!  questions:
!    - numbering scheme should start at 1, order of meshes does not matter
!    - pundit uses this numbering scheme for iblank:
!        1 solve point
!        0 hole point
!       -1 fringe point
!       orphans are not explicitly labeled; must do that on the FUN3D side


! load imesh data

      grid%imesh = -99
      nfound = 0
      min_imesh =  huge(min_imesh)
      max_imesh = -huge(max_imesh)

      imesh_filename = trim(grid%project) // '.imesh'
      f = available_unit()
      open(unit=f,file=trim(imesh_filename),form='unformatted',                &
           status='unknown',iostat=ios)

      if (ios == 0) then

        reader : do i = 1, grid%nnodesg
          read(f) gn, value
          search : do j = 1, grid%nnodes01
            if ( gn == grid%l2g(j) ) then
              grid%imesh(j) = value
              nfound = nfound + 1
              min_imesh = min(value,min_imesh)
              max_imesh = max(value,max_imesh)
              exit search
            endif
          end do search
          if ( nfound == grid%nnodes01 ) exit reader
        end do reader

        close(f)

      endif

! error checking

      do i = 1, grid%nnodes01
        if ( grid%imesh(i) == -99 ) then
          write(*,*) 'error in assigning imesh:', lmpi_id, i, grid%l2g(i)
          call lmpi_die
          stop
        endif
      end do

! write min and max imesh

      call lmpi_min(min_imesh,min_imesh_g) ; call lmpi_bcast(min_imesh_g)
      call lmpi_max(max_imesh,max_imesh_g) ; call lmpi_bcast(max_imesh_g)

      if ( lmpi_master ) then
        write(*,*) 'Min value of imesh in FUN3D numbering: ', min_imesh_g
        write(*,*) 'Max value of imesh in FUN3D numbering: ', max_imesh_g
      endif

! associate iblank and allocate bodytag

      iblank => grid%iblank
      allocate(bodytag(nv))

! Pundit requires 1-based values of imesh

      bodytag = grid%imesh + 1

! get element connectivity
! trim down so that cells only come from one processor uniquely

! First count them

      ntetra = 0
      npyra  = 0
      nprizm = 0
      nhexa  = 0

      do i = 1, grid%nelem

        do j = 1, grid%elem(i)%ncell

          min_node = huge(min_node)
          hold_index =  -99

          do k = 1, grid%elem(i)%node_per_cell
            if ( grid%l2g(grid%elem(i)%c2n(k,j)) < min_node ) then
              min_node = grid%l2g(grid%elem(i)%c2n(k,j))
              hold_index = k
            endif
          end do

          if ( hold_index == -99 ) then
            write(*,*) 'Error finding min global node'
            call lmpi_die
            stop
          endif

          if ( grid%elem(i)%c2n(hold_index,j) <= grid%nnodes0 ) then
            select case(grid%elem(i)%type_cell)
            case('tet')
              ntetra = ntetra + 1
            case('pyr')
              npyra  = npyra  + 1
            case('prz')
              nprizm = nprizm + 1
            case('hex')
              nhexa  = nhexa  + 1
            end select
          endif

        end do

      end do

! check to make sure we have the right number

      do i = 1, grid%nelem
        select case(grid%elem(i)%type_cell)
        case('tet')
          call lmpi_reduce(ntetra,global_count); call lmpi_bcast(global_count)
          if ( global_count /= grid%elem(i)%ncellg ) then
            write(*,*) 'Incorrect number of tets found:',                      &
                       global_count, grid%elem(i)%ncellg
            call lmpi_die
            stop
          endif
        case('pyr')
          call lmpi_reduce(npyra,global_count); call lmpi_bcast(global_count)
          if ( global_count /= grid%elem(i)%ncellg ) then
            write(*,*) 'Incorrect number of pyrs found:',                      &
                       global_count, grid%elem(i)%ncellg
            call lmpi_die
            stop
          endif
        case('prz')
          call lmpi_reduce(nprizm,global_count); call lmpi_bcast(global_count)
          if ( global_count /= grid%elem(i)%ncellg ) then
            write(*,*) 'Incorrect number of przs found:',                      &
                       global_count, grid%elem(i)%ncellg
            call lmpi_die
            stop
          endif
        case('hex')
          call lmpi_reduce(nhexa,global_count); call lmpi_bcast(global_count)
          if ( global_count /= grid%elem(i)%ncellg ) then
            write(*,*) 'Incorrect number of hexs found:',                      &
                       global_count, grid%elem(i)%ncellg
            call lmpi_die
            stop
          endif
        end select
      end do

! now allocate and fill

      call my_alloc_ptr(tet_c2n,4,ntetra)
      call my_alloc_ptr(pyr_c2n,5,npyra)
      call my_alloc_ptr(prz_c2n,6,nprizm)
      call my_alloc_ptr(hex_c2n,8,nhexa)

      ntetra = 0
      npyra  = 0
      nprizm = 0
      nhexa  = 0

      do i = 1, grid%nelem

        do j = 1, grid%elem(i)%ncell

          min_node = huge(min_node)
          hold_index =  -99

          do k = 1, grid%elem(i)%node_per_cell
            if ( grid%l2g(grid%elem(i)%c2n(k,j)) < min_node ) then
              min_node = grid%l2g(grid%elem(i)%c2n(k,j))
              hold_index = k
            endif
          end do

          if ( hold_index == -99 ) then
            write(*,*) 'Error finding min global node'
            call lmpi_die
            stop
          endif

          if ( grid%elem(i)%c2n(hold_index,j) <= grid%nnodes0 ) then
            select case(grid%elem(i)%type_cell)
            case('tet')
              ntetra = ntetra + 1
              tet_c2n(:,ntetra) = grid%elem(i)%c2n(:,j)
            case('pyr')
              npyra  = npyra  + 1
              pyr_c2n(:,npyra)  = grid%elem(i)%c2n(:,j)
            case('prz')
              nprizm = nprizm + 1
              prz_c2n(:,nprizm) = grid%elem(i)%c2n(:,j)
            case('hex')
              nhexa  = nhexa  + 1
              hex_c2n(:,nhexa)  = grid%elem(i)%c2n(:,j)
            end select
          endif

        end do

      end do

! get boundary node information

      nobc = 0  ! number of nodes on overset boundaries
      nwbc = 0  ! number of nodes on wall boundaries

! first count them to get sizes

      do i = 1, grid%nbound
        if ( grid%bc(i)%ibc == -1 ) then
          nobc = nobc + grid%bc(i)%nbnode
        else if ( abs(grid%bc(i)%ibc)/1000 == 3 .or.                           &
                  abs(grid%bc(i)%ibc)/1000 == 4 .or.                           &
                  abs(grid%bc(i)%ibc)/1000 == 7 ) then
          nwbc = nwbc + grid%bc(i)%nbnode
        endif
      enddo

! now allocate for data

      call my_alloc_ptr(obcnode,nobc)
      call my_alloc_ptr(wbcnode,nwbc)

! now populate them

      nobc = 0
      nwbc = 0

      do i = 1, grid%nbound
        if ( grid%bc(i)%ibc == -1 )  then
          do j = 1, grid%bc(i)%nbnode
            nobc = nobc + 1
            obcnode(nobc) = grid%bc(i)%ibnode(j)
          enddo
        else if ( abs(grid%bc(i)%ibc)/1000 == 3 .or.                           &
                  abs(grid%bc(i)%ibc)/1000 == 4 .or.                           &
                  abs(grid%bc(i)%ibc)/1000 == 7 ) then
          do j = 1, grid%bc(i)%nbnode
            nwbc = nwbc + 1
            wbcnode(nwbc) = grid%bc(i)%ibnode(j)
          enddo
        endif
      enddo

! register grid data with pundit

      if ( debug ) then
        write(*,*) 'Calling set_nb_grid_data'
        write(1000+lmpi_id) 'nv = ', nv
        do i = 1, 3*nv
          write(1000+lmpi_id) xv(i)
        end do
        write(1000+lmpi_id) 'start of iblank, bodytag'
        do i = 1, nv
          write(1000+lmpi_id) iblank(i), bodytag(i)
        end do
        write(1000+lmpi_id) 'nobc, nwbc = ', nobc, nwbc
        write(1000+lmpi_id) 'start of wbcnode'
        do i = 1, nwbc
          write(1000+lmpi_id) wbcnode(i)
        end do
        write(1000+lmpi_id) 'start of obcnode'
        do i = 1, nobc
          write(1000+lmpi_id) obcnode(i)
        end do
        write(1000+lmpi_id) 'ntetra, npyra, nprizm, nhexa = ', ntetra, npyra,&
                                                                 nprizm, nhexa
        write(1000+lmpi_id) 'start of tet_c2n'
        do i = 1, ntetra
          write(1000+lmpi_id) tet_c2n(:,i)
        end do
        write(1000+lmpi_id) 'start of pyr_c2n'
        do i = 1, npyra
          write(1000+lmpi_id) pyr_c2n(:,i)
        end do
        write(1000+lmpi_id) 'start of prz_c2n'
        do i = 1, nprizm
          write(1000+lmpi_id) prz_c2n(:,i)
        end do
        write(1000+lmpi_id) 'start of hex_c2n'
        do i = 1, nhexa
          write(1000+lmpi_id) hex_c2n(:,i)
        end do
        close(1000+lmpi_id)
      endif
      call set_nb_grid_data(xv,iblank,bodytag,wbcnode,obcnode,                 &
                            tet_c2n,pyr_c2n,prz_c2n,hex_c2n,                   &
                            nv,ntetra,npyra,nprizm,nhexa,nobc,nwbc)

! associate solution variables

      q    => soln%q_dof
      turb => soln%turb

! solution scaling=1 since Q is from the same code base

      scal = 1.0_dp

      init = .false.

    else set_or_reset

      do i = 1, nv
        xv(3*i-2) = grid%x(i)
        xv(3*i-1) = grid%y(i)
        xv(3*i)   = grid%z(i)
      enddo

      call reset_nb_grid_data(xv,iblank,bodytag,nv)

    endif set_or_reset

  end subroutine pundit_register_data


!=============================== PUNDIT_PERFORM_CONNECTIVITY =================80
!
! perform domain connectivity
!
!=============================================================================80

  subroutine pundit_perform_connectivity()

    use lmpi,            only : lmpi_master
    use punditInterface, only : performConnectivity
    use lmpi_app,        only : lmpi_xfer

    integer :: i

  continue

    if ( lmpi_master .and. verbose) write(*,*) 'pundit: performing domain ',   &
                                               'connectivity'

    call performConnectivity()

! update the iblank values at the orphans since pundit does not do this

    do i = 1, nobc
      if ( iblank(obcnode(i)) == 1 ) iblank(obcnode(i)) = -2
    end do
    call lmpi_xfer(iblank)

    if ( lmpi_master .and. verbose ) write(*,*) 'pundit: finished domain ',    &
                                                'connectivity'

  end subroutine pundit_perform_connectivity


!=============================== PUNDIT_SOLUTION_UPDATE ======================80
!
!  update the overset solution
!
!=============================================================================80

  subroutine pundit_solution_update(grid,soln,crow,key)

    use lmpi,            only : lmpi_master, lmpi_die
    use punditInterface, only : updateSolution, setDataBuffers
    use lmpi_app,        only : lmpi_xfer
    use solution_types,  only : soln_type
    use grid_types,      only : grid_type
    use kinddefs,        only : dp
    use comprow_types,   only : crow_flow

    integer, intent(in) :: key

    type(grid_type), intent(in)    :: grid
    type(crow_flow), intent(in)    :: crow
    type(soln_type), intent(inout) :: soln

    integer :: i, j, neighbor, nneighbors

    real(dp), dimension(soln%n_tot)  :: sum_neighbors
    real(dp), dimension(soln%n_turb) :: sum_neighborst

  continue

    if ( lmpi_master .and. verbose ) write(*,*) 'pundit: performing solution ',&
                                                'update'

    select case(key)
    case(1)
      call setDataBuffers(nvar)
      call updateSolution(q,scal,nvar,nv)
      call lmpi_xfer(soln%q_dof)
    case(2)
      call setDataBuffers(nvart)
      call updateSolution(turb,scal,nvart,nv)
      call lmpi_xfer(soln%turb)
    case(3)
      call setDataBuffers(nvar)
      call updateSolution(q,scal,nvar,nv)
      call lmpi_xfer(soln%q_dof)
      call setDataBuffers(nvart)
      call updateSolution(turb,scal,nvart,nv)
      call lmpi_xfer(soln%turb)
    case default
      write(*,*) 'invalid key in pundit_solution_update()', key
      call lmpi_die
      stop
    end select

! need to update the holes/orphans since pundit does not do this

    do i = 1, grid%nnodes0
      if ( grid%iblank(i) == 0 .or. grid%iblank(i) == -2 ) then
        sum_neighbors(:) = 0.0_dp
        sum_neighborst(:) = 0.0_dp
        do neighbor = crow%ia(i), crow%ia(i+1)-1
          j = abs(crow%ja(neighbor))
          if ( key == 1 ) then
            sum_neighbors(:) = sum_neighbors(:) + soln%q_dof(:,j)
          else if ( key == 2 ) then
            sum_neighborst(:) = sum_neighborst(:) + soln%turb(:,j)
          else if ( key == 3 ) then
            sum_neighbors(:) = sum_neighbors(:) + soln%q_dof(:,j)
            sum_neighborst(:) = sum_neighborst(:) + soln%turb(:,j)
          endif
        end do
        nneighbors = crow%ia(i+1) - crow%ia(i)
        if ( key == 1 ) then
          soln%q_dof(:,i) = sum_neighbors(:) / (1.0_dp * nneighbors)
        else if ( key == 2 ) then
          soln%turb(:,i) = sum_neighborst(:) / (1.0_dp * nneighbors)
        else if ( key == 3 ) then
          soln%q_dof(:,i) = sum_neighbors(:) / (1.0_dp * nneighbors)
          soln%turb(:,i) = sum_neighborst(:) / (1.0_dp * nneighbors)
        endif
      endif
    end do

    if ( key == 1 ) then
      call lmpi_xfer(soln%q_dof)
    else if ( key == 2 ) then
      call lmpi_xfer(soln%turb)
    else if ( key == 3 ) then
      call lmpi_xfer(soln%q_dof)
      call lmpi_xfer(soln%turb)
    endif

    if ( lmpi_master .and. verbose ) write(*,*) 'pundit: finished solution ',  &
                                                'update'

  end subroutine pundit_solution_update


!=============================== PUNDIT_WRITE_OUTPUT =========================80
!
!  write the solution for debugging
!
!=============================================================================80

  subroutine pundit_write_output()

    use lmpi,            only : lmpi_master, lmpi_id
!   use punditInterface, only : punditWriteData

  continue

   if ( lmpi_master .and. verbose ) write(*,*) 'pundit: writing tecplot vis ', &
                                               'files'

!  call punditWriteData(lmpi_id)

   if ( lmpi_master .and. verbose ) write(*,*) 'pundit: finished writing ',    &
                                               'tecplot vis files'

  end subroutine pundit_write_output


#else

!================================= PUNDIT_INITIALIZE =========================80
!
! Pass the communicator to pundit
!
!=============================================================================80

  subroutine pundit_initialize()

  continue

    write(*,*) 'Do not have PUNDIT configured.'

  end subroutine pundit_initialize


!=============================== PUNDIT_REGISTER_DATA ========================80
!
! Register the partitioned grid data with pundit
!
!=============================================================================80

  subroutine pundit_register_data(grid,soln)

    use grid_types,      only : grid_type
    use solution_types,  only : soln_type

    type(grid_type),  intent(inout) :: grid
    type(soln_type),  intent(inout) :: soln

  continue

    if ( .false. ) write(*,*) grid%nnodes0, soln%n_tot

    write(*,*) 'Do not have PUNDIT configured.'

  end subroutine pundit_register_data


!=============================== PUNDIT_PERFORM_CONNECTIVITY =================80
!
! perform domain connectivity
!
!=============================================================================80

  subroutine pundit_perform_connectivity()

  continue

    write(*,*) 'Do not have PUNDIT configured.'

  end subroutine pundit_perform_connectivity


!=============================== PUNDIT_SOLUTION_UPDATE ======================80
!
!  update the overset solution
!
!=============================================================================80

  subroutine pundit_solution_update(grid,soln,crow,key)

    use solution_types,  only : soln_type
    use grid_types,      only : grid_type
    use comprow_types,   only : crow_flow

    integer, intent(in) :: key

    type(grid_type), intent(in)    :: grid
    type(crow_flow), intent(in)    :: crow
    type(soln_type), intent(inout) :: soln

  continue

    if ( .false. ) write(*,*) grid%nnodes0, soln%n_tot, crow%ia(1), key

    write(*,*) 'Do not have PUNDIT configured.'

  end subroutine pundit_solution_update


!=============================== PUNDIT_WRITE_OUTPUT =========================80
!
!  write the solution for debugging
!
!=============================================================================80

  subroutine pundit_write_output()

  continue

    write(*,*) 'Do not have PUNDIT configured.'

  end subroutine pundit_write_output

#endif

end module pundit

#! /usr/bin/env ruby

class MissingOptionDescription < RuntimeError; end
class MissingVariableDefault < RuntimeError; end
class MissingNamelistDeclaration < RuntimeError; end
class MissingNamelistDocumentationBlock < RuntimeError; end

class String
  def rm_blank_lines
    self.gsub(/^\n/,'')
  end
  def rm_comment_prefix
    self.gsub(/^! */,'')
  end
  def rm_comment_prefix!
    self.gsub!(/^! */,'')
  end
  def rm_dp_kind!
    self.gsub!(/_dp/,'')
  end
  def strip_array_literals!
    self.gsub!(/\(\/ */,'')
    self.gsub!(/ *\/\)/,'')
  end
  def strip_parentheses
    self.gsub(/\(.*?\)$/,'')
  end
  def escape_underscores
    self.gsub(/_/,'\_')
  end
  def escape_braces
    self.gsub(/\{/,'\{').gsub(/\}/,'\}')
  end
  def mod_desc
    self.rm_blank_lines.rm_comment_prefix
  end
  def namelist_names
    matches = self.scan(/namelist *\/ *([\w,\{\}]+) *\//).flatten.uniq
    raise MissingNamelistDeclaration if matches.empty?
    matches
  end
  def namelist_documentation_block(nml_name)
    match = self.match(/^! *begin +namelist &#{nml_name.escape_braces}\n(.*?\n)^! *end +namelist/im)
    if match then
      match.captures.first
    else
      raise MissingNamelistDocumentationBlock,
      "could not find '! begin namelist &#{nml_name}' / '! end namelist' pair"
    end
  end
  def name_default_desc
    declaration, description = self.rm_blank_lines.split("\n",2)
    found_declaration = declaration.match(/ *(.*?) *= *(.*)/)
    if found_declaration then
      varname, default = found_declaration.captures
    else
      raise MissingVariableDefault,
            "Could not find default assignment in: '#{declaration}'"
    end
    default.strip_array_literals!
    default.rm_dp_kind!
    description.rm_comment_prefix!
    [ varname, default, description ]
  end
  def opt_desc
    option, description = self.rm_blank_lines.split("\n",2)
    option = option.match(/\( *(.*?) *\)/)[1]
    option = option.split(/, *'/)
    option = option.map{ |e| e.sub(/^('|)/,'`') }
    option = option.map{ |e| e.sub(/('|)$/,"'") }
    description = description.split(/^[^!]/,2).first
    description.rm_comment_prefix!
    if description.empty? then
      raise MissingOptionDescription,
            "no description found for option #{option}"
    end
    [ option, description ]
  end
  def find_opts
    result = self.split(/^ *case/)
    result.slice!(0)
    result
  end
end

if __FILE__ == $PROGRAM_NAME then

  require 'erb'
  require 'optparse'

  # Process command line options
  options = {}
  OptionParser.new do |opts|
    opts.banner = "Usage: extract_namelists.rb [options]"

    opts.on("--path path", "Source search path") do |v|
      options[:path] = v
    end

    opts.on("--file file", "Source file name") do |v|
      options[:file] = v
    end

  end.parse!

# p options

  NmlVar = Struct.new( :name, :stripped_name, :default, :desc, :options )
  VarOpt = Struct.new( :names, :desc )

  template = ERB.new <<'EOF', nil, '-'
%% == DO NOT EDIT THIS FILE ==
%% Generated from <%= f90path %>

\namelistsection{\&<%= nml_name.escape_underscores.escape_braces %>}%
                {s:nml_<%= nml_name %>}

<%= overview -%>

\begin{Verbatim}
&<%= nml_name -%>
<% mx = 0 ; vars.each{ |var| mx = [mx, var.name.length].max } -%>
<% vars.each do |var| %>
<%= "  #{var.name.ljust(mx)} = #{var.default}" -%>
<% end %>
/
\end{Verbatim}

\begin{namelist}

<% vars.each do |var| -%>
<%   next if var.desc.match(/\[multiline initialization\]/) %>
<%= "  \\item[\\cmd{#{var.name} = #{var.default}}]\n" %>
<%=    var.desc.gsub(/^/,' '*4) %>
<%   var.options.each do |opt| -%>
<%= "    \\cmd{#{opt.names.first}} #{opt.desc.gsub(/^/,' '*6)}" %>
<%   end -%>
<% end -%>
\end{namelist}
EOF

#  puts "have path" if options[:path]
#  puts "have file" if options[:file]
  unless (options[:file]).nil?
    FILES = [options[:file]]
  else
    FILES = %w[
               component_performance.f90
               eqn_groups.f90
               grid_motion.F90
               nml_grid_motion.f90
               nml_adapt_mechanics.f90
               nml_adapt_metric_construction.f90
               nml_aeroelastic_modal_data.f90
               nml_body_transform.f90
               nml_boundary_conditions.f90
               nml_boundary_output.f90
               nml_code_run_control.f90
               nml_design.f90
               nml_equivalent_area.f90
               nml_flow_initialization.f90
               nml_force_moment_integ.f90
               nml_fwh_acoustic_data.f90
               nml_global.f90
               nml_governing_equations.f90
               nml_grid_transform.f90
               nml_inviscid_flux.f90
               nml_linear_systems.f90
               nml_mdo_surface_data.f90
               nml_noninertial_reference_frame.f90
               nml_nonlinear_solves.f90
               nml_overset_data.f90
               nml_periodicity.f90
               nml_project.f90
               nml_press_box_function.f90
               nml_pstag_function.f90
               nml_raw_grid.f90
               nml_reference_physical.f90
               nml_rotor_data.f90
               nml_sampling_output.f90
               nml_sboom.f90
               nml_slice_data.f90
               nml_sonic_boom.f90
               nml_special_parameters.f90
               nml_three_d_trans.f90
               nml_time_avg_params.f90
               nml_two_d_trans.f90
               nml_turbulence.f90
               nml_turbulent_diffusion_models.f90
               nml_version_number.f90
               nml_volume_output.f90
               nml_vortex_generator.f90
               sampling_main.f90
               nml_sixdof.f90
              ]
  end

  unless (options[:path]).nil?
    search = options[:path] + "/*/{#{FILES*','}}"
  else
    search = "../../*/{#{FILES*','}}"
  end
  Dir[search].each do |f90path|
    next if f90path =~ /Complex/ # ignore Complexified source
    puts "parsing #{f90path}"
    contents = File.read f90path
    begin
      nml_names = contents.namelist_names
    rescue
      next
    end
    nml_names.each do |nml_name|
      nml_blk = contents.namelist_documentation_block(nml_name)
      overview = nml_blk.match(/(.*?)^ /im)[1].mod_desc
      vars = []
      nml_blk.scan(/^ +.*?^!.*?(?=^ *\n)/m).each do |var_section|
        name, default, desc = var_section.name_default_desc
        stripped_name = name.strip_parentheses
        next if desc.match(/\[hidden\]/)
        vars << NmlVar.new( name, stripped_name, default, desc, [] )
      end
      vars.each do |var|
        opt_blk = contents.
                 scan(/^ *select case *\( *#{var.stripped_name}(?:|\(.*?\)) *\)\n(.*?)^ *case default/im).
               first
        if ( opt_blk.nil? ) then
          opt_blk = ""
        else
          opt_blk = opt_blk.join
        end
        opts = opt_blk.find_opts
        opts.each do |opt|
          names, desc = opt.opt_desc
          next if desc.match(/\[hidden\]/)
          var.options << VarOpt.new( names, desc )
        end
      end # vars
      texfile = 'nml_' + nml_name + '.tex'
      File.chmod(0644,texfile) if File.exists? texfile
      File.open( texfile, 'w' ) do |f|
         f.puts template.result(binding)
         f.chmod(0444)
      end
    end # nml_names
  end
end

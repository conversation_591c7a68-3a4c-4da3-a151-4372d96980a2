  module sampling_headers

  use kinddefs,            only : dp
  use fun3d_maximums,      only : max_geom, max_pnts
  use nml_sampling_output, only : max_smp_vars
  use sampling_types,      only : sample_type

  implicit none

  integer, parameter     :: max_node = 8

! namelist parameters

  logical                :: verbose
  integer                :: number_of_geometries  ! number of slice geometries
  logical                :: crinkle
  logical                :: nodal
  logical                :: fwh_formatted
  logical                :: asynchronous_fwh = .false.
! integer, parameter     :: max_geom  = 100       ! max number of geometries
! integer, parameter     :: max_pnts  = 10000     ! max number of points
! integer, parameter     :: max_lines = 1000000
  character(len=14  ), dimension(max_geom) :: plot
  character(len=80  ), dimension(max_geom) :: label
  character(len=1024), dimension(max_geom) :: variable_list
  character(len=80  ), dimension(max_geom) :: type_of_geometry
  character(len=80  ), dimension(max_geom) :: type_of_data
  integer,             dimension(max_geom) :: sampling_frequency
  logical,             dimension(max_geom) :: sampling_final_write
  logical,             dimension(max_geom) :: sampling_strands
  logical,             dimension(max_geom) :: append_history
  character(len=80  ), dimension(max_geom) :: move_with_body
  character(len=1024), dimension(max_geom) :: patch_list
  integer,             dimension(max_geom) :: patch_list_count

! Point sampling information
  integer, dimension(max_geom)             :: number_of_points  ! points
  real(dp), dimension(3,max_geom,max_pnts) :: points
  integer,  dimension(max_geom)            :: sunit_pts
  logical, save                            :: snap_output_xyz
  real(dp)                                 :: dist_tolerance
  real(dp), dimension(3,max_geom)          :: boundary_point
  real(dp), dimension(max_geom)            :: cf
  real(dp), dimension(max_geom)            :: re_tau
  real(dp), dimension(max_geom)            :: u_tau
  real(dp), dimension(max_geom)            :: rnuw
  integer,  dimension(max_geom)            :: boundary
  logical,  dimension(max_geom), save      :: need_wall_data
  real(dp)                                 :: reference_length

! Schlieren information
  character(len=1),    dimension(  max_geom) :: schlieren_aspect
  integer,             dimension(  max_geom) :: number_of_lines
  integer,             dimension(  max_geom) :: number_of_rows
  integer,             dimension(  max_geom) :: number_of_columns
  real(dp),            dimension(  max_geom) :: window_height
  real(dp),            dimension(  max_geom) :: window_width
  real(dp),            dimension(3,max_geom) :: window_center
  real(dp),            dimension(3,max_geom) :: model_center
  real(dp),            dimension(3,max_geom) :: window_normal
  logical,             dimension(  max_geom) :: plot_lines
  character(len=1024), dimension(  max_geom) :: blanking_list
  integer,             dimension(  max_geom) :: blanking_list_count
  logical                                    :: make_shadow

! Volume sampling geometry information
  real(dp),            dimension(3,max_geom) :: box_lower_corner
  real(dp),            dimension(3,max_geom) :: box_upper_corner
  real(dp),            dimension(3,max_geom) :: plane_center
  real(dp),            dimension(3,max_geom) :: plane_normal
  real(dp),            dimension(3,max_geom) :: sphere_center
  real(dp),            dimension(  max_geom) :: sphere_radius
  real(dp),            dimension(3,max_geom) :: cylinder_face1
  real(dp),            dimension(3,max_geom) :: cylinder_face2
  real(dp),            dimension(  max_geom) :: cylinder_radius
  real(dp),            dimension(3,max_geom) :: cone_face1
  real(dp),            dimension(3,max_geom) :: cone_face2
  real(dp),            dimension(  max_geom) :: cone_radius1
  real(dp),            dimension(  max_geom) :: cone_radius2
  real(dp),            dimension(3,max_geom) :: circle_center
  real(dp),            dimension(3,max_geom) :: circle_normal
  real(dp),            dimension(  max_geom) :: circle_radius
  real(dp),            dimension(3,max_geom) :: p1_line
  real(dp),            dimension(3,max_geom) :: p2_line
  real(dp),            dimension(3,max_geom) :: corner1
  real(dp),            dimension(3,max_geom) :: corner2
  real(dp),            dimension(3,max_geom) :: corner3
  real(dp),            dimension(3,max_geom) :: corner4

! Isosurface information
  real(dp), dimension(  max_geom)          :: isosurf_value
  character(len=80), dimension(max_geom)   :: isosurf_variable
  real(dp), dimension(  max_geom)          :: isosurf_dist_threshold
  logical,  dimension(  max_geom)          :: isosurf_box
  real(dp), dimension(  max_geom)          :: x_range_lower
  real(dp), dimension(  max_geom)          :: x_range_upper
  real(dp), dimension(  max_geom)          :: y_range_lower
  real(dp), dimension(  max_geom)          :: y_range_upper
  real(dp), dimension(  max_geom)          :: z_range_lower
  real(dp), dimension(  max_geom)          :: z_range_upper
! parameters for sampling of boundaries
  character(len=1024)                       :: boundary_list
  logical                                   :: default_boundary


  logical, dimension(max_geom), save       :: init_template           = .true.
  logical, dimension(max_geom), save       :: init_blanking_list      = .true.
  logical, dimension(max_geom), save       :: init_patch_list         = .true.
  logical, dimension(max_geom), save       :: have_sampling_points    = .false.

  type(sample_type), dimension(:), allocatable :: sample


  logical :: sampling_parameters_read = .false.

  real(dp), dimension(:,:), allocatable :: data_p
  integer , dimension(:,:), allocatable :: global_node1
  integer , dimension(:,:), allocatable :: global_node2
  integer,  dimension(:,:), allocatable :: c2f

  real(dp), dimension(:,:), allocatable ::    q_master
  integer , dimension(  :), allocatable :: edge_master
  integer                               :: edge_count_master

  integer , dimension(max_geom,max_pnts), save :: p2c_local_cells
  integer , dimension(max_geom,max_pnts), save :: p2c_local_elem
  integer , dimension(max_geom,max_pnts), save :: p2c_local_face
  integer , dimension(max_geom,max_pnts), save :: p2c_local_bdy
  integer , dimension(max_geom,max_pnts), save :: point_is_found
  integer , dimension(max_geom,max_pnts), save :: point_is_found_global
  integer , dimension(max_geom,max_pnts), save :: lost_point_cell
  integer , dimension(max_geom,max_pnts), save :: lost_point_elem

    logical,                                   save :: need_turb_variables
    logical,                                   save :: need_gradients
    logical,                                   save :: need_slen
    logical,                                   save :: init            = .true.
    logical, dimension(max_geom),              save :: append_timestep = .true.
    logical, dimension(max_geom),              save :: init_write      = .true.
    logical, dimension(max_geom),              save :: have_points     = .false.
    character(len=80), dimension(max_smp_vars), save :: output_variables
    integer,                                   save :: n_output_variables
    character(len=80), dimension(7),           save :: schlieren_variables
    integer,                                   save :: n_schlieren_variables
    character(len=80)                               :: project_name
    character(len=80)                               :: geo_name



! These are all the combinations of 4 edge cuts for a hexahedral
! Refer to element_defs for an edge map - 3 orientations, 3
! primary faces, 3 4-point cuts per orientation/face.

  integer, parameter, dimension ( 13, 36 ) :: hex_cut_edge_map1 =              &
  reshape((/                                                                   &
     354, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0                                   &
   ,4674, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0                                   &
   , 202, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0                                   &
   ,6224, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1                                   &
   ,4674, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1                                   &
   ,5188, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1                                   &
   ,4896, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1                                   &
   ,4674, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1                                   &
   ,4744, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1                                   &
   ,2578, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0                                   &
   ,4674, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1                                   &
   ,1542, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0                                   &
   ,4896, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0                                   &
   , 424, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0                                   &
   , 354, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0                                   &
   ,3456, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0                                   &
   , 424, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0                                   &
   , 404, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0                                   &
   ,4744, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1                                   &
   , 424, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0                                   &
   , 202, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0                                   &
   ,3112, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0                                   &
   , 424, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0                                   &
   ,  60, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0                                   &
   ,5188, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0                                   &
   ,3092, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0                                   &
   ,1542, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0                                   &
   ,3456, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0                                   &
   ,3092, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0                                   &
   ,3112, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1, 0                                   &
   ,6224, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1                                   &
   ,3092, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0                                   &
   ,2578, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0                                   &
   , 404, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0                                   &
   ,3092, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0                                   &
   ,  60, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0                                   &
     /),(/13, 36/))

  integer, parameter, dimension ( 13, 36 ) :: hex_cut_edge_map2 =              &
  reshape((/                                                                   &
     354, 0, 0, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0                                   &
   ,4674, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1                                   &
   , 202, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0                                   &
   ,6224, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 1                                   &
   ,4674, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1                                   &
   ,5188, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1                                   &
   ,4896, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0, 0, 0                                   &
   ,4674, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0                                   &
   ,4744, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0, 0, 0                                   &
   ,2578, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0                                   &
   ,4674, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1                                   &
   ,1542, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0                                   &
   ,4896, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1                                   &
   , 424, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0                                   &
   , 354, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0                                   &
   ,3456, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 1, 0                                   &
   , 424, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0                                   &
   , 404, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0                                   &
   ,4744, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1                                   &
   , 424, 0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0                                   &
   , 202, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 0                                   &
   ,3112, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0                                   &
   , 424, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0                                   &
   ,  60, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0                                   &
   ,5188, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1                                   &
   ,3092, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0                                   &
   ,1542, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0                                   &
   ,3456, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0                                   &
   ,3092, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0                                   &
   ,3112, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0                                   &
   ,6224, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1                                   &
   ,3092, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0                                   &
   ,2578, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0                                   &
   , 404, 0, 1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0                                   &
   ,3092, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0                                   &
   ,  60, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0                                   &
     /),(/13, 36/))

! These are all the combinations of 4 edge cuts for a pyramid
! Refer to element_defs for an edge map

  integer, parameter, dimension (  9, 16 ) :: pyr_cut_edge_map1 =              &
  reshape((/                                                                   &
     326, 1, 1, 0, 0, 0, 0, 0, 1                                               &
    , 30, 1, 1, 0, 1, 0, 0, 0, 0                                               &
    ,172, 0, 1, 1, 0, 1, 0, 0, 0                                               &
    , 30, 1, 1, 1, 0, 0, 0, 0, 0                                               &
    ,344, 0, 0, 1, 1, 0, 1, 0, 0                                               &
    , 30, 0, 1, 1, 1, 0, 0, 0, 0                                               &
    ,178, 1, 0, 0, 1, 0, 0, 1, 0                                               &
    , 30, 1, 0, 1, 1, 0, 0, 0, 0                                               &
    ,178, 1, 0, 0, 0, 1, 0, 1, 0                                               &
    ,172, 0, 1, 0, 0, 1, 0, 1, 0                                               &
    ,354, 0, 0, 0, 0, 1, 1, 0, 1                                               &
    ,344, 0, 0, 1, 0, 0, 1, 0, 1                                               &
    ,172, 0, 0, 1, 0, 1, 0, 1, 0                                               &
    ,178, 0, 0, 0, 1, 1, 0, 1, 0                                               &
    ,344, 0, 0, 0, 1, 0, 1, 0, 1                                               &
    ,326, 1, 0, 0, 0, 0, 1, 0, 1                                               &
     /),(/9, 16/))

  integer, parameter, dimension (  9, 16 ) :: pyr_cut_edge_map2 =              &
  reshape((/                                                                   &
     326, 0, 1, 0, 0, 0, 1, 0, 1                                               &
    , 30, 0, 1, 1, 1, 0, 0, 0, 0                                               &
    ,172, 0, 0, 1, 0, 1, 0, 1, 0                                               &
    , 30, 1, 0, 1, 1, 0, 0, 0, 0                                               &
    ,344, 0, 0, 0, 1, 0, 1, 0, 1                                               &
    , 30, 1, 1, 0, 1, 0, 0, 0, 0                                               &
    ,178, 1, 0, 0, 0, 1, 0, 1, 0                                               &
    , 30, 1, 1, 1, 0, 0, 0, 0, 0                                               &
    ,178, 1, 0, 0, 1, 0, 0, 1, 0                                               &
    ,172, 0, 1, 1, 0, 0, 0, 1, 0                                               &
    ,354, 1, 0, 0, 0, 1, 0, 0, 1                                               &
    ,344, 0, 0, 1, 1, 0, 0, 0, 1                                               &
    ,172, 0, 1, 1, 0, 1, 0, 0, 0                                               &
    ,178, 1, 0, 0, 1, 1, 0, 0, 0                                               &
    ,344, 0, 0, 1, 1, 0, 1, 0, 0                                               &
    ,326, 1, 1, 0, 0, 0, 1, 0, 0                                               &
     /),(/9, 16/))

! These are all the combinations of 4 edge cuts for a prism
! Refer to element_defs for an edge map

  integer, parameter, dimension ( 10, 11 ) :: prz_cut_4edge_map1 =             &
  reshape((/                                                                   &
      86, 1, 1, 0, 1, 0, 0, 0, 0, 0                                            &
    ,554, 1, 0, 1, 0, 1, 0, 0, 0, 0                                            &
    ,960, 0, 0, 0, 0, 0, 1, 0, 1, 1                                            &
    ,404, 0, 1, 0, 1, 0, 0, 0, 1, 0                                            &
    , 60, 0, 1, 1, 1, 0, 0, 0, 0, 0                                            &
    , 60, 0, 1, 1, 0, 1, 0, 0, 0, 0                                            &
    ,424, 0, 0, 1, 0, 1, 0, 0, 1, 0                                            &
    ,424, 0, 0, 1, 0, 0, 0, 1, 1, 0                                            &
    ,404, 0, 1, 0, 0, 0, 0, 1, 1, 0                                            &
    ,588, 0, 1, 1, 0, 0, 1, 0, 0, 0                                            &
    ,624, 0, 0, 0, 1, 1, 0, 0, 0, 1                                            &
     /),(/10, 11/))

  integer, parameter, dimension ( 10, 11 ) :: prz_cut_4edge_map2 =             &
  reshape((/                                                                   &
      86, 0, 1, 0, 1, 0, 1, 0, 0, 0                                            &
    ,554, 0, 0, 1, 0, 1, 0, 0, 0, 1                                            &
    ,960, 0, 0, 0, 0, 0, 1, 1, 0, 1                                            &
    ,404, 0, 0, 0, 1, 0, 0, 1, 1, 0                                            &
    , 60, 0, 0, 1, 1, 1, 0, 0, 0, 0                                            &
    , 60, 0, 1, 0, 1, 1, 0, 0, 0, 0                                            &
    ,424, 0, 0, 0, 0, 1, 0, 1, 1, 0                                            &
    ,424, 0, 0, 1, 0, 1, 0, 1, 0, 0                                            &
    ,404, 0, 1, 0, 1, 0, 0, 1, 0, 0                                            &
    ,588, 0, 0, 1, 0, 0, 1, 0, 0, 1                                            &
    ,624, 0, 0, 0, 1, 0, 1, 0, 0, 1                                            &
     /),(/10, 11/))

  public :: sample
  public :: max_node

  public :: verbose
  public :: number_of_geometries
  public :: crinkle
  public :: nodal
  public :: fwh_formatted
  public :: asynchronous_fwh
! integer, parameter     :: max_geom  = 100       ! max number of geometries
! integer, parameter     :: max_pnts  = 10000     ! max number of points
! integer, parameter     :: max_lines = 1000000
  public :: plot
  public :: label
  public :: variable_list
  public :: type_of_geometry
  public :: type_of_data
  public :: sampling_frequency
  public :: sampling_final_write
  public :: sampling_strands
  public :: move_with_body
  public :: patch_list
  public :: patch_list_count

  public :: number_of_points  ! points
  public :: points
  public :: sunit_pts
  public :: snap_output_xyz
  public :: dist_tolerance
  public :: boundary
  public :: boundary_point
  public :: cf
  public :: re_tau
  public :: u_tau
  public :: rnuw
  public :: need_wall_data
  public :: reference_length

  public :: schlieren_aspect
  public :: number_of_lines
  public :: number_of_rows
  public :: number_of_columns
  public :: window_height
  public :: window_width
  public :: window_center
  public :: model_center
  public :: window_normal
  public :: plot_lines
  public :: blanking_list
  public :: blanking_list_count
  public :: make_shadow

  public :: box_lower_corner
  public :: box_upper_corner
  public :: plane_center
  public :: plane_normal
  public :: sphere_center
  public :: sphere_radius
  public :: cylinder_face1
  public :: cylinder_face2
  public :: cylinder_radius
  public :: cone_face1
  public :: cone_face2
  public :: cone_radius1
  public :: cone_radius2
  public :: circle_center
  public :: circle_normal
  public :: circle_radius
  public :: p1_line
  public :: p2_line
  public :: corner1
  public :: corner2
  public :: corner3
  public :: corner4

! Isosurface information
  public :: isosurf_value
  public :: isosurf_variable
  public :: isosurf_dist_threshold
  public :: isosurf_box
  public :: x_range_lower
  public :: x_range_upper
  public :: y_range_lower
  public :: y_range_upper
  public :: z_range_lower
  public :: z_range_upper
  public :: boundary_list
  public :: default_boundary
! public :: default_boundary_set

  public :: init_template
  public :: init_blanking_list
  public :: init_patch_list
  public :: have_sampling_points
  public :: append_history

  public :: sampling_parameters_read

  public :: data_p
! public(dp), dimension(:,:), allocatable :: data_p1
! public(dp), dimension(:,:), allocatable :: data_p2
! public(dp), dimension(:,:), allocatable :: data_p3
  public :: global_node1
  public :: global_node2
  public :: c2f
! public :: global_node_map

  public ::    q_master
  public :: edge_master
  public :: edge_count_master
  public :: p2c_local_cells
  public :: p2c_local_elem
  public :: p2c_local_face
  public :: p2c_local_bdy
  public :: point_is_found
  public :: point_is_found_global
  public :: lost_point_cell
  public :: lost_point_elem

  public :: need_turb_variables
  public :: need_gradients
  public :: need_slen
  public :: init
  public :: append_timestep
  public :: init_write
  public :: have_points
  public :: output_variables
  public :: n_output_variables
  public :: schlieren_variables
  public :: n_schlieren_variables
  public :: project_name
  public :: geo_name

  public  :: hex_cut_edge_map1
  public  :: hex_cut_edge_map2
  public  :: pyr_cut_edge_map1
  public  :: pyr_cut_edge_map2
  public  :: prz_cut_4edge_map1
  public  :: prz_cut_4edge_map2

end module sampling_headers

module projection_distance

  implicit none

  private

  public :: tria_dist, edge_dist

contains

!================================== TRIA_DIST ================================80
!
! Find the projected distance from the volume node (point 0) to a triangle on
! tthe boundary. the logical "found" is returned .true. f the projection lies
! lies within the triangle, otherwise it is returned as .false.
!
! The method used to calculate the minimum distance from a field point to the
! interior of a triangle is described in Section 4 of the
! following reference:
!
!   Jamshid <PERSON>, Unstructured Grids on NURBS Surfaces , 11th AIAA
!   Applied Aerodynamics Conference, Monterey, California, AIAA-93-3454, August
!   9-11, 1993
!
! That reference also describs an non-linear, iterative scheme for
! quadralaterals. An alternative approach is used here in which quads are
! broken into triangles and the linear eqns for triangles are used in each
! piece. This should eliminate the issue of whether the projection is not
! found inside the quad because it doesn't really, or was it due to a
! convergence issue.
!
!=============================================================================80

  subroutine tria_dist( x0, y0, z0, x1, y1, z1, x2, y2, z2, x3, y3, z3,        &
                        found, dist, xloc, yloc, zloc, a, b )

    use kinddefs,        only : dp

    logical, intent(out) :: found

    real(dp),    intent(in)  :: x0, y0, z0, x1, y1, z1
    real(dp),    intent(in)  :: x2, y2, z2, x3, y3, z3
    real(dp),    intent(out) :: dist
    real(dp),    intent(out) :: xloc, yloc, zloc, a, b

    real(dp), dimension(2,3)               :: d
    real(dp)                               :: dx,   dy,  dz, dx2, dy2, dz2
    real(dp)                               :: dx3, dy3, dz3
    real(dp)                               :: c11, c22, c12, rj

    continue

!   initialize

    dist  = huge(1.0_dp)
    found = .false.

    dx = x0 - x1
    dy = y0 - y1
    dz = z0 - z1

    dx2 = x2 - x1
    dy2 = y2 - y1
    dz2 = z2 - z1

    dx3 = x3 - x1
    dy3 = y3 - y1
    dz3 = z3 - z1

    c11 = dx2*dx2 + dy2*dy2 + dz2*dz2
    c22 = dx3*dx3 + dy3*dy3 + dz3*dz3
    c12 = dx2*dx3 + dy2*dy3 + dz2*dz3

    rj  = c11*c22 - c12*c12

    d(1,1) = (c22*dx2 - c12*dx3)/rj
    d(1,2) = (c22*dy2 - c12*dy3)/rj
    d(1,3) = (c22*dz2 - c12*dz3)/rj

    d(2,1) = (c11*dx3 - c12*dx2)/rj
    d(2,2) = (c11*dy3 - c12*dy2)/rj
    d(2,3) = (c11*dz3 - c12*dz2)/rj

    a = d(1,1)*dx + d(1,2)*dy + d(1,3)*dz
    b = d(2,1)*dx + d(2,2)*dy + d(2,3)*dz

    if ((a >= 0._dp).and.(b >= 0._dp).and.((a + b) <= 1._dp)) then

      found = .true.

!     if a and b are both positive and the sum is <= 1 then this is it

!     now we know that our location is a linear combination of our vectors

      xloc = x1 + a*dx2 + b*dx3
      yloc = y1 + a*dy2 + b*dy3
      zloc = z1 + a*dz2 + b*dz3

      dist = (xloc - x0)**2 + (yloc - y0)**2 + (zloc - z0)**2

    end if

  end subroutine tria_dist


!================================== EDGE_DIST ================================80
!
!  find the projected distance from the volume node (point 0) to each edge of
!  the face (defined by points 1-3 for triangles, 1-4 for quadralaterals)
!
!  note that the edge id given to the nearest edge must be consistent with that
!  described for array iflagslen in subroutine calc_turb_dist_fcn
!
!=============================================================================80

  subroutine edge_dist( type_face, x0, y0, z0, x1, y1, z1, x2, y2, z2,         &
                        x3, y3, z3, x4, y4, z4, dist, iedge )

    use kinddefs,        only : dp

    character(len=4), intent(in)  :: type_face
    integer,          intent(out) :: iedge
    real(dp),         intent(in)  :: x0, y0, z0, x1, y1, z1, x2, y2, z2
    real(dp),         intent(in)  :: x3, y3, z3, x4, y4, z4
    real(dp),         intent(out) :: dist

    real(dp)                               :: top, bottom, a
    real(dp)                               :: dist1, dist2, dist3, dist4
    real(dp)                               :: xloc, yloc, zloc

    continue

    select case (type_face)

      case ('tria')

!       check along edge 1-2

        bottom = (x2-x1)*(x2-x1)+(y2-y1)*(y2-y1)+(z2-z1)*(z2-z1)
        top    = (x0-x1)*(x2-x1)+(y0-y1)*(y2-y1)+(z0-z1)*(z2-z1)
        a = top/bottom
        dist1 = huge(1.0_dp)
        if((a >= 0._dp).and.(a <= 1._dp))then
          xloc = x1 + a*(x2 - x1)
          yloc = y1 + a*(y2 - y1)
          zloc = z1 + a*(z2 - z1)
          dist1 = (xloc-x0)**2 + (yloc-y0)**2 + (zloc-z0)**2
        end if

!       check along edge 2-3

        bottom = (x3-x2)*(x3-x2)+(y3-y2)*(y3-y2)+(z3-z2)*(z3-z2)
        top    = (x0-x2)*(x3-x2)+(y0-y2)*(y3-y2)+(z0-z2)*(z3-z2)
        a = top/bottom
        dist2 =  huge(1.0_dp)
        if((a >= 0._dp).and.(a <= 1._dp))then
          xloc = x2 + a*(x3 - x2)
          yloc = y2 + a*(y3 - y2)
          zloc = z2 + a*(z3 - z2)
          dist2 = (xloc-x0)**2 + (yloc-y0)**2 + (zloc-z0)**2
        end if

!       check along edge 3-1

        bottom = (x1-x3)*(x1-x3)+(y1-y3)*(y1-y3)+(z1-z3)*(z1-z3)
        top    = (x0-x3)*(x1-x3)+(y0-y3)*(y1-y3)+(z0-z3)*(z1-z3)
        dist3 =  huge(1.0_dp)
        a = top/bottom
        if((a >= 0._dp).and.(a <= 1._dp))then
          xloc = x3 + a*(x1 - x3)
          yloc = y3 + a*(y1 - y3)
          zloc = z3 + a*(z1 - z3)
          dist3 = (xloc-x0)**2 + (yloc-y0)**2 + (zloc-z0)**2
        end if

!       find smallest distance

        dist = min(dist1, dist2, dist3)

!       flag the edge with the smallest distance

        if (dist1 <= dist2 .and. dist1 <= dist3) then
          iedge = 2
        else if (dist2 <= dist1 .and. dist2 <= dist3) then
          iedge = 3
        else if (dist3 <= dist1 .and. dist3 <= dist2) then
          iedge = 4
        end if

      case ('quad')

!       check along edge 1-2

        bottom = (x2-x1)*(x2-x1)+(y2-y1)*(y2-y1)+(z2-z1)*(z2-z1)
        top    = (x0-x1)*(x2-x1)+(y0-y1)*(y2-y1)+(z0-z1)*(z2-z1)
        a = top/bottom
        dist1 = huge(1.0_dp)
        if((a >= 0._dp).and.(a <= 1._dp))then
          xloc = x1 + a*(x2 - x1)
          yloc = y1 + a*(y2 - y1)
          zloc = z1 + a*(z2 - z1)
          dist1 = (xloc-x0)**2 + (yloc-y0)**2 + (zloc-z0)**2
        end if

!       check along edge 2-3

        bottom = (x3-x2)*(x3-x2)+(y3-y2)*(y3-y2)+(z3-z2)*(z3-z2)
        top    = (x0-x2)*(x3-x2)+(y0-y2)*(y3-y2)+(z0-z2)*(z3-z2)
        a = top/bottom
        dist2 =  huge(1.0_dp)
        if((a >= 0._dp).and.(a <= 1._dp))then
          xloc = x2 + a*(x3 - x2)
          yloc = y2 + a*(y3 - y2)
          zloc = z2 + a*(z3 - z2)
          dist2 = (xloc-x0)**2 + (yloc-y0)**2 + (zloc-z0)**2
        end if

!       check along edge 3-4

        bottom = (x4-x3)*(x4-x3)+(y4-y3)*(y4-y3)+(z4-z3)*(z4-z3)
        top    = (x0-x3)*(x4-x3)+(y0-y3)*(y4-y3)+(z0-z3)*(z4-z3)
        dist3 =  huge(1.0_dp)
        a = top/bottom
        if((a >= 0._dp).and.(a <= 1._dp))then
          xloc = x3 + a*(x4 - x3)
          yloc = y3 + a*(y4 - y3)
          zloc = z3 + a*(z4 - z3)
          dist3 = (xloc-x0)**2 + (yloc-y0)**2 + (zloc-z0)**2
        end if

!       check along edge 4-1

        bottom = (x1-x4)*(x1-x4)+(y1-y4)*(y1-y4)+(z1-z4)*(z1-z4)
        top    = (x0-x4)*(x1-x4)+(y0-y4)*(y1-y4)+(z0-z4)*(z1-z4)
        dist4 =  huge(1.0_dp)
        a = top/bottom
        if((a >= 0._dp).and.(a <= 1._dp))then
          xloc = x4 + a*(x1 - x4)
          yloc = y4 + a*(y1 - y4)
          zloc = z4 + a*(z1 - z4)
          dist4 = (xloc-x0)**2 + (yloc-y0)**2 + (zloc-z0)**2
        end if

!       find smallest distance

        dist = min(dist1, dist2, dist3, dist4)

!       flag the edge with the smallest distance

        if (dist1 <= dist2 .and. dist1 <= dist3 .and. dist1 <= dist4) then
          iedge = 6
        else if (dist2 <= dist1 .and. dist2 <= dist3 .and. dist2 <= dist4) then
          iedge = 7
        else if (dist3 <= dist1 .and. dist3 <= dist2 .and. dist3 <= dist4) then
          iedge = 8
        else if (dist4 <= dist1 .and. dist4 <= dist2 .and. dist4 <= dist3) then
          iedge = 9
        end if

      case default

        write(*,*) 'error in face_dist...face type ',type_face,' not supported'
        stop

    end select

  end subroutine edge_dist

end module projection_distance

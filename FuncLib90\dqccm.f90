!=================================== DQCCM ===================================80
!
! Extrapolate or interpolate and limit the gradient to the interface for the
! Cell Centered Scheme
!
!   Extended to the "U-MUSCL" scheme of Burg et al (AIAA 2003-3983) with
!   upwinding parameter kappa_umuscl:
!   kappa_umuscl is analogous to kappa in the usual structured-mesh upwind
!   schemes. kappa_umuscl = 0 gives the standard (baseline fun3d) unstructured
!   scheme; kappa_umuscl = 1 is central difference; kappa_umuscl = 1/2 gives
!   3rd order in one dimension if the gradients are 2nd order.
!
!=============================================================================80

  pure function dqccm(rx, ry, rz, gradx, grady, gradz, q1, q2, phi,            &
                      eps, epscoef, kappa_umuscl,                              &
                      usage, iflim, omega, ndim)

    use kinddefs,        only : dp

    integer,                   intent(in) :: iflim, usage, ndim

    real(dp),                  intent(in) :: rx, ry, rz
    real(dp),                  intent(in) :: eps, epscoef, omega
    real(dp),                  intent(in) :: kappa_umuscl
    real(dp), dimension(ndim), intent(in) :: gradx, grady, gradz
    real(dp), dimension(ndim), intent(in) :: phi
    real(dp), dimension(ndim), intent(in) :: q1, q2

    real(dp), dimension(ndim)             :: dqccm

    real(dp), dimension(ndim)             :: dqm, dqms, dqmb, dqp, dqps, dqpb

    real(dp), parameter  :: one = 1._dp, four = 4._dp, p4=1._dp/4._dp

  continue

!   Compute the successive undivided gradients (dq- and dq+)

    dqm = four*(gradx*rx + grady*ry + gradz*rz) - (q2 - q1)
    dqp = q2 - q1

!   Construct the cell face gradients depending on the limiter type

!   Edge limited or unlimited gradients
!   N.B.: 0<= phi <= 1 is invoked independent of iflim by the pressure limiter

    dqpb = dqp

    if (iflim == 6) then

!     Higher order state reconstruction using the
!     smooth(CFl3D) differentiable gradient and [pressure] limiter

      dqmb = smthlmv(dqm, dqp, epscoef*eps, ndim)
      dqpb = smthlmv(dqp, dqm, epscoef*eps, ndim)

    else if (iflim == 5) then

!     Higher order state reconstruction using the
!     smooth(vanAlbada) differentiable gradient and [pressure] limiter

      dqmb = vaflxlv(dqm, dqp, epscoef*eps, ndim)
      dqpb = vaflxlv(dqp, dqm, epscoef*eps, ndim)

    else if (iflim == 4) then

!     Limit the gradients using the
!     vanLeer gradient limiter

      dqmb = vlflxlv(dqm, dqp, ndim)
      if (usage == 2) dqpb = vlflxlv(dqp, dqm, ndim)

    else if (iflim == 3) then

!     Limit the gradients using the
!     minmod gradient limiter

      dqms = omega*dqm
      dqps = omega*dqp
      dqmb = minmodv(dqm, dqps, ndim)
      if (usage == 2) dqpb = minmodv(dqp, dqms, ndim)

    else

!       Unlimited gradients or freezable gradient limiters (Barth or Venkat)

      dqmb = dqm
      dqpb = dqp

    end if

!   Higher order state reconstruction using U-MUSCL with limiting via phi
!   N.B.:     phi  = 1 is invoked by the no limiter option, iflim=0
!         0<= phi <= 1 is invoked by limiter options, iflim=1 or iflim=2
!         0<= phi <= 1 is invoked independent of iflim by the pressure limiter

    dqccm = phi*p4*((one-kappa_umuscl)*dqmb + (one+kappa_umuscl)*dqpb)

  end function dqccm

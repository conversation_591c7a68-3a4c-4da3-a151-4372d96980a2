module fluid

  use kinddefs, only : dp

  implicit none

  private

  public :: gamma
  public :: ggm1
  public :: gm1
  public :: xgm
  public :: xgm1
  public :: xg2m1
  public :: gm1g, xgm1g
  public :: gp1
  public :: gp1g
  public :: ideal_gas_constant
  public :: sutherland_constant
  public :: turbinfinity
  public :: prandtl

  !beginNoComplexInterface
  public :: setup_incomp_fluid
  public :: setup_fluid_gamma
  public :: setup_sutherland_constant
  public :: setup_turbulence_infinity
  !endNoComplexInterface

  real(dp) :: gamma = -1.0_dp   ! gamma (set <0 for init check)
  real(dp) :: ggm1              ! gamma times (gamma-1)
  real(dp) :: gm1               ! gamma-1
  real(dp) :: xg2m1             ! 1/(gamma*gamma-1)
  real(dp) :: xgm               ! 1/gamma
  real(dp) :: xgm1              ! 1/(gamma-1)
  real(dp) :: gm1g              ! (gamma-1) over gamma
  real(dp) :: xgm1g             ! gamma over (gamma-1)
  real(dp) :: gp1               ! gamma+1
  real(dp) :: gp1g              ! (gamma+1) over gamma

  real(dp) :: ideal_gas_constant
  real(dp) :: prandtl             = 0.72_dp ! Prandtl number
  real(dp) :: sutherland_constant = -1.0_dp ! Sutherland constant, deg R
                                                  ! (set <0 for init check)

  real(dp), dimension(4) :: turbinfinity !turbulence at infinity

  contains

!============================== SETUP_FLUID_GAMMA ============================80
!
! sets gamma to 1.4 or optional argument and computes all its friends
!
!=============================================================================80

  subroutine setup_fluid_gamma(optional_gamma)

    use lmpi,              only : lmpi_master
    use system_extensions, only : floats_not_equal

    real(dp), optional, intent(in) :: optional_gamma

    real(dp), parameter :: my_1   = 1.0_dp

    continue

    ideal_gas_constant = 287.1_dp

    gamma = 1.4_dp

    if (present(optional_gamma)) then
      if (lmpi_master.and.floats_not_equal(gamma,optional_gamma)) then
        write(*,'(a,f0.3,a,f0.3)')                                             &
        ' NOTE: The ratio of specific heats (gamma) has been changed from ',   &
        real(gamma), ' to ', real(optional_gamma)
      end if
      gamma = optional_gamma
    end if

    gm1   = gamma - my_1
    xgm   = my_1/gamma
    xgm1  = my_1/gm1
    xg2m1 = my_1/(gamma*gamma - my_1)
    gp1   = gamma + my_1
    gm1g  = gm1/gamma
    xgm1g = gamma/gm1
    gp1g  = gp1/gamma
    ggm1  = gamma*gm1

  end subroutine setup_fluid_gamma

!============================== SETUP_INCOMP_FLUID ===========================80
!
! sets gamma and all its friends to 1 for incompressible flow
!
!=============================================================================80

  subroutine setup_incomp_fluid()

  continue

    gamma = 1.0_dp
    gm1   = 1.0_dp
    xgm   = 1.0_dp
    xgm1  = 1.0_dp
    xg2m1 = 1.0_dp
    gp1   = 1.0_dp
    gm1g  = 1.0_dp
    gp1g  = 1.0_dp
    ggm1  = 1.0_dp

  end subroutine setup_incomp_fluid

!=========================== SETUP_SUTHERLAND_CONSTANT =======================80
!
! sets Sutherland constant, sutherland_constant, for use in Sutherland's Law
! for the variation of molecular viscosity with temperature:
!
! mu = (1 + sutherland_constant/Tref)/(T + sutherland_constant/Tref) * T**3/2
!
!=============================================================================80

  subroutine setup_sutherland_constant(optional_cs)

    use lmpi, only : lmpi_master

    real(dp), optional, intent(in) :: optional_cs

    continue

    if (present(optional_cs)) then
      sutherland_constant = optional_cs
      if (lmpi_master) write(*,'(2(a,f7.2))')                                  &
        ' Setting Sutherland constant =  ',real(sutherland_constant,dp),       &
        ' rather than default value of ',198.6_dp
    else
      sutherland_constant = 198.6_dp
    end if

  end subroutine setup_sutherland_constant

!=========================== SETUP_TURBULENCE_INFINITY =======================80
!
! sets turbulence values at infinity
!
!=============================================================================80

  subroutine setup_turbulence_infinity(turb1, turb2, turb3, turb4)

    real(dp), optional, intent(in) :: turb1, turb2, turb3, turb4

    continue

    if ( present(turb1) ) then
      turbinfinity(1) = turb1
    end if

    if ( present(turb2) ) then
      turbinfinity(2) = turb2
    end if

    if ( present(turb3) ) then
      turbinfinity(3) = turb3
    end if

    if ( present(turb4) ) then
      turbinfinity(4) = turb4
    end if

  end subroutine setup_turbulence_infinity

end module fluid

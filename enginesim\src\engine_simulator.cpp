#include "engine_simulator.h"

EngineSimulator::EngineSimulator(std::string libraryName) 
    : library(libraryName.c_str()) {
    library.model_initialize(1);
}

EngineSimulator::~EngineSimulator(){
    library.model_terminate();
}

EngineOutput EngineSimulator::step( EngineInput data ){
    
    library.model_in->P_in = data.pressure;
    library.model_in->T_in = data.temperature;
    library.model_in->Time = data.time;
    library.model_in->mdot_bypass = data.mdot_bypass;
    library.model_in->mdot_core = data.mdot_core;
    library.model_in->mdot_vce = data.mdot_vce;

    library.model_step();

    EngineOutput output;
    output.press_bypass = library.model_out->P_bypass;
    output.press_core   = library.model_out->P_core;
    output.press_vce    = library.model_out->P_vce;

    output.temp_bypass = library.model_out->T_bypass;
    output.temp_core   = library.model_out->T_core;
    output.temp_vce    = library.model_out->T_vce;

    return output;
}

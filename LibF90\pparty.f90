! Routines used for partitioning cells, nodes, edges and boundaries

module pparty

  use kinddefs,   only : dp, system_i1, i8
  use local_grid, only : pp_nsize, pp_nhead, pp_ntail
  use lmpi,       only : lmpi_id,    lmpi_nproc,  lmpi_min,    lmpi_max,       &
                         lmpi_bcast, lmpi_master, lmpi_reduce,                 &
                         lmpi_integer1_vector_bcast, lmpi_conditional_stop
  use system_extensions, only : se_open, se_wall_time
  implicit none

  private

  public :: node_cell_chopper, bc_chopper, pparty_convert_g2l, writegrid_sm
  public :: node_cell_chopper_cc

    type elem1_cc_type
      integer :: npc, ncell0, ncell01
      integer, dimension(:),   pointer :: cl2g
      integer, dimension(:,:), pointer :: c2n
    end type elem1_cc_type

    type elem2_cc_type
      integer :: sz, ct
      integer, dimension(:),   pointer :: cl2g
      integer, dimension(:,:), pointer :: c2n
    end type elem2_cc_type

    integer,  parameter :: bsize = 8
    real(dp), parameter :: bsize_inv = 1._dp / 8._dp

contains

!=============================== NODE_CELL_CHOPPER ===========================80
!
! Partitions the nodes and cells, and gather the cells
!
!=============================================================================80

  subroutine node_cell_chopper(grid)

    use pparty_metis,         only : metis_data
    use grid_types,           only : grid_type
    use sort,                 only : heap_sort

    type(grid_type),      intent(inout) :: grid

    integer :: maxnodes, minnodes, inode, my_count
    integer :: nnodes0, nnodes1, current_size, i,j,k,m,ielem
    integer :: npc, s2, my_s2, ioff, ipe, icell, ict, ierr
    integer(i8) :: ncellg_i8

    integer, dimension(:), allocatable :: my_counts_all, nodelist0
    integer, dimension(:), allocatable :: temp1, tcl2g, tempca, tempda
    integer, dimension(:), allocatable :: t1_ind
    integer, dimension(:,:), allocatable :: temp2a, temp2b, temp2c, tc2n

    integer :: word1, word2, word3, word4, word5, word6, word7, word8, tag_size
    integer :: j1, j2, j3, j4, j5, j6, j7, j8
    logical :: b1, b2, b3, b4, b5, b6, b7, b8, is32
    integer(system_i1), dimension(:), allocatable :: tag,tagc,tag1

    logical :: verboset = .false.

  continue

!----------------------------------
!
! level 0 node partitions.
! Use metis_data to create level 0 node list.
!
! Each processor has 1-nsize partitions
!  0 : 1-100; 1 : 101-200 ... n : n01-n00
!
! Every processor counts its ipe entries, nnodes0 = determined
! Then each metis_data is bcast'ed and nodelist0 is saved.
! Ordering is ensured.
!
!----------------------------------

    ncellg_i8 = sum(grid%elem(1:grid%nelem)%ncellg)
    is32 = (ncellg_i8 <= 2147483647_i8)
    if (lmpi_id == 0)                                                          &
      write(*,*)'    ... Constructing partition node sets for level-0...',     &
                         ncellg_i8,is32

!   fill the level-0 node list.

    ! Count nodes for each ipe

    allocate(my_counts_all(lmpi_nproc)); my_counts_all = 0

    if (lmpi_nproc > 1) then
       do i = 1,pp_nsize(lmpi_id)
          ipe = metis_data(i)
          my_counts_all(ipe) = my_counts_all(ipe) + 1
       end do
    else
       my_counts_all(1) = pp_nsize(lmpi_id)
    end if

    do ipe = 1,lmpi_nproc
       call lmpi_reduce(my_counts_all(ipe),my_count)
       call lmpi_bcast(my_count)
       if (lmpi_id+1 == ipe)  then
          nnodes0 = my_count
          allocate(nodelist0(nnodes0))
          nodelist0 = 0
       end if
    end do
    deallocate(my_counts_all)

   if (verboset) then
       call lmpi_max(nnodes0,maxnodes)
       call lmpi_min(nnodes0,minnodes)

       if(lmpi_id == 0)then
         write(*,*)"The max number of level-0 nodes in a partition is ",maxnodes
         write(*,*)"The min number of level-0 nodes in a partition is ",minnodes
       end if
   end if

! Fill the nodelist0 array on each processor with the global nodes that make
! up the partitions

    ioff     = 0
    my_count = 0
    do ipe = 0,lmpi_nproc-1
      if (verboset.and.lmpi_master) then
         if (mod(ipe,250)==0) write(*,*)'    ... loop0 ',ipe
      end if
      if (lmpi_id == ipe) k = pp_nsize(lmpi_id)
      call lmpi_bcast(k,ipe)
      allocate(temp1(k)); temp1 = 1
      if ((lmpi_id == ipe).and.(lmpi_nproc > 1)) temp1 = metis_data
      call lmpi_bcast(temp1,ipe)

      do i = 1,k
         if (temp1(i) == lmpi_id+1) then
            my_count = my_count + 1
            nodelist0(my_count) = ioff + i
         end if
      end do

      if (lmpi_nproc > 1) deallocate(temp1)

      ioff = ioff + k

    end do
    nnodes0 = my_count

    if (verboset.and.lmpi_master) call se_wall_time('    ... Time for loop0 ')

! we can now ditch the partition vector

    if (lmpi_nproc > 1) deallocate(metis_data)

!----------------------------------
! level 01 cell partitions using level 0 nodes.
! MEMORY: replace tag with bit arrays.
!
! Each processor bcasts its nodelist0; all PEs tag the appropriate local cells.
! A count is determined and the cells are gathered; knowing the cells on each
! processor are in order.
!----------------------------------

   tag_size  = ceiling(grid%nnodesg*bsize_inv)
   allocate(tag(tag_size)); tag = 0

   ! Tag all level0 nodes
   do i = 1,nnodes0
      word1 = ceiling(nodelist0(i)*bsize_inv)
      tag(word1) = ibset(tag(word1), mod(nodelist0(i),bsize))
   end do

   elem_loop : do ielem = 1,grid%nelem

     npc    = grid%elem(ielem)%node_per_cell
     my_s2  = grid%elem(ielem)%ncell

     if (my_s2 > 0) then
       allocate(tagc(my_s2)); tagc = 0
       ict = 0

! if the lowest-numbered node in the cell is on my current processor, tag it
! (uniquely assign each cell to a processor)

       do i = 1,my_s2
         j = minval(grid%elem(ielem)%c2n(:,i))
         if ((j >= pp_nhead(lmpi_id)).and.(j <= pp_ntail(lmpi_id))) then
           tagc(i) = 1
           ict = ict + 1
         end if
       end do

! load the tagged cells into temp arrays and empty out elem%c2n and elem%cl2g

       if (allocated(temp2a)) deallocate(temp2a)
       if (allocated(tempca)) deallocate(tempca)
       allocate(temp2a(npc,ict))
       allocate(tempca(ict))
       j = 0
       do i = 1,my_s2
         if (tagc(i)==1) then
           j = j + 1
           temp2a(:,j) = grid%elem(ielem)%c2n(:,i)
           tempca(j) = grid%elem(ielem)%cl2g(i)
         end if
       end do
       deallocate(tagc)
       my_s2 = ict
     end if

     deallocate(grid%elem(ielem)%c2n)
     nullify(grid%elem(ielem)%c2n)
     deallocate(grid%elem(ielem)%cl2g)
     nullify(grid%elem(ielem)%cl2g)

! Figure out the total number of cells being bookkept globally (fringe cells
! are counted multiple times so this number is more than the strict number of
! cells in the global mesh)

     call lmpi_reduce(my_s2*1_i8,ncellg_i8)
     call lmpi_bcast(ncellg_i8)

     current_size = nint((ncellg_i8 / lmpi_nproc*1_i8) * 1.5)
     if (current_size < 100000) current_size = min(100000_i8,ncellg_i8)

! allocate temp arrays to hold c2n and cl2g

     grid%elem(ielem)%ncell = 0
     allocate(tc2n(npc,current_size));  tc2n = 0
     allocate(tcl2g(current_size));     tcl2g = 0

! broadcast c2n and cl2g to the processors where they need to eventually end up

     k = 0
     proc_loop : do ipe = 0,lmpi_nproc-1

       if (verboset.and.lmpi_master) then
         if (mod(ipe,250)==0) write(*,*)'    ... loop2 ',ipe
       end if

       if (lmpi_id == ipe) s2 = my_s2
       call lmpi_bcast(s2,ipe)

       if (s2 > 0) then
         allocate(temp2b(npc,s2)); temp2b = 0
         allocate(tempda(s2));     tempda = 0
         if (ipe == lmpi_id) then
           temp2b(:,1:s2) = temp2a(:,:)
           tempda(1:s2)   = tempca(:)
           deallocate(temp2a)
           deallocate(tempca)
         end if
         call lmpi_bcast(tempda,ipe)
         call lmpi_bcast(temp2b,ipe)

         allocate(tag1(s2)); tag1 = 0
         ict = 0
         do icell = 1,s2
            j1 = temp2b(1,icell)
            j2 = temp2b(2,icell)
            j3 = temp2b(3,icell)
            j4 = temp2b(4,icell)
            word1 = int((j1+7)/bsize); b1 = btest(tag(word1),mod(j1,bsize))
            word2 = int((j2+7)/bsize); b2 = btest(tag(word2),mod(j2,bsize))
            word3 = int((j3+7)/bsize); b3 = btest(tag(word3),mod(j3,bsize))
            word4 = int((j4+7)/bsize); b4 = btest(tag(word4),mod(j4,bsize))
            if (b1.or.b2.or.b3.or.b4) then
               tag1(icell) = 1
               ict = ict + 1
            elseif (npc > 4) then
               j5 = temp2b(5,icell)
               word5 = int((j5+7)/bsize); b5 = btest(tag(word5),mod(j5,bsize))
               b6 = .false.; b7 = .false.; b8 = .false.
               if (npc /= 5) then
                  j6 = temp2b(6,icell)
                  word6 = int((j6+7)/bsize)
                  b6 = btest(tag(word6),mod(j6,bsize))
               end if
               if (npc == 8) then
                  j7 = temp2b(7,icell)
                  j8 = temp2b(8,icell)
                  word7 = int((j7+7)/bsize)
                  b7 = btest(tag(word7),mod(j7,bsize))
                  word8 = int((j8+7)/bsize)
                  b8 = btest(tag(word8),mod(j8,bsize))
               end if
               if (b5.or.b6.or.b7.or.b8) then
                  tag1(icell) = 1
                  ict = ict + 1
               end if
            end if
         end do

         if (ict > 0) then
            if (k+ict > current_size) then

               m = current_size
               current_size = current_size + current_size/2
               if (current_size < k+ict) current_size = k+ict
               ! c2n
               allocate(temp2c(npc,m))
               temp2c = tc2n
               deallocate(tc2n)
               allocate(tc2n(npc,current_size)); tc2n = 0
               tc2n(1:npc,1:m) = temp2c(1:npc,1:m)
               deallocate(temp2c)

               ! cl2g
               allocate(temp1(m))
               temp1 = tcl2g
               deallocate(tcl2g)
               allocate(tcl2g(current_size)); tcl2g = 0
               tcl2g(1:m) = temp1(1:m)
               deallocate(temp1)
            end if

            do icell = 1,s2
               if (tag1(icell) == 1)  then
                 k = k + 1
                 tc2n(1:npc,k) = temp2b(1:npc,icell)
                 tcl2g(k)      = tempda(icell)
               end if
            end do     ! icell
         end if

         deallocate(tag1)
         deallocate(temp2b)
         deallocate(tempda)
       end if

     end do proc_loop

! set the final value of elem%ncell

     grid%elem(ielem)%ncell = k

     if (is32) then ! 32 bit
        allocate(t1_ind(k)); t1_ind = 0
        call heap_sort(k,tcl2g,t1_ind)
        allocate(tempda(k))
        tempda = tcl2g(t1_ind)
        deallocate(tcl2g)

        ierr = 0
        do i = 1,k-1
           if (tempda(i) == tempda(i+1)) then
              ierr = 1
              exit
           end if
        end do
        call lmpi_max(ierr,i)
        call lmpi_bcast(i)
        call lmpi_conditional_stop(i,"Internal error: duplicate cl2g")

   ! set the values of elem%c2n and elem%cl2g

        allocate(grid%elem(ielem)%cl2g(k))
        grid%elem(ielem)%cl2g(1:k) = tempda(1:k)
        deallocate(tempda)

        allocate(grid%elem(ielem)%c2n(npc,k))
        do i = 1,k
           grid%elem(ielem)%c2n(1:npc,i) = tc2n(1:npc,t1_ind(i))
        end do
        deallocate(tc2n)
        deallocate(t1_ind)
    else ! 64-bit
        allocate(grid%elem(ielem)%cl2g(k))
        grid%elem(ielem)%cl2g(1:k) = tcl2g(1:k)
        deallocate(tcl2g)

        allocate(grid%elem(ielem)%c2n(npc,k))
        grid%elem(ielem)%c2n(1:npc,1:k) = tc2n(1:npc,1:k)
        deallocate(tc2n)
    end if

   end do elem_loop ! do ielem

   if (verboset.and.lmpi_master) call se_wall_time('    ... Time for loop2 ')

!----------------------------------
! level 1 nodes using local cells01 (in new c2n)
! Count the non-level0 nodes in the level01 entries.

   tag = 0

   ! Tag all nodes from cells
   do ielem = 1,grid%nelem
     if (grid%elem(ielem)%ncell > 0) then
       do icell = 1,grid%elem(ielem)%ncell
         do i = 1,size(grid%elem(ielem)%c2n,1)
            inode = grid%elem(ielem)%c2n(i,icell)
            word1 = ceiling(inode*bsize_inv)
            tag(word1) = ibset(tag(word1), mod(inode,bsize))
            !tag(inode) = 1
         end do
       end do
     end if
   end do

   ! untag level0 -- leaving only level1
   do i = 1,nnodes0
      inode = nodelist0(i)
      word1 = ceiling(inode*bsize_inv)
      tag(word1) = ibclr(tag(word1), mod(inode,bsize))
      !tag(nodelist0(i)) = 0
   end do

   nnodes1 = 0
   do i = 1,grid%nnodesg
      word1 = ceiling(i*bsize_inv)
      b1 = btest(tag(word1), mod(i,bsize))
      if (b1) nnodes1 = nnodes1 + 1
      !if (tag(i)==1) nnodes1 = nnodes1 + 1
   end do

! fill up grid%l2g for the level-0 nodes and ditch nodelist0

   deallocate( grid%l2g )
   allocate(grid%l2g(nnodes0+nnodes1))
   grid%l2g(1:nnodes0) = nodelist0
   deallocate(nodelist0)

   my_count = 0
   do i = 1,grid%nnodesg
      word1 = ceiling(i*bsize_inv)
      b1 = btest(tag(word1), mod(i,bsize))
      if (b1) then
      !if (tag(i)==1) then
         my_count = my_count + 1
         grid%l2g(nnodes0+my_count) = i
      end if
   end do
   deallocate(tag)

   if (verboset.and.lmpi_master)                                               &
      call se_wall_time('    ... node_cell_chopper: tag nodes ')

! set some grid% scalars

   grid%nnodes0  = nnodes0
   grid%nnodes01 = nnodes0 + nnodes1

   call bcast_xyz(grid)

   if (verboset.and.lmpi_master)                                               &
      call se_wall_time('    ... END node_cell_chopper ')

 end subroutine node_cell_chopper


!=============================== NODE_CELL_CHOPPER_CC ========================80
!
! Partitions the cells and nodes, and gather the cells
!
!=============================================================================80

  subroutine node_cell_chopper_cc(grid)

    use pparty_metis, only : metis_data, elem3_c2c
    use grid_types,   only : grid_type
    use sort,         only : heap_sort, binary_search,                         &
                             growable_lookup_type, growable_lookup_extend
    use local_grid,   only : cc_csize

    type(grid_type), intent(inout) :: grid

    integer :: i,j,k,ielem, jelem,npc, ioff, ipe, csize, node, inode, pmax
    integer :: icell, ki, i1, i2, i12, fpc, gcell1
    integer :: ct, eval_ct, ict, my_count, my_ct, max_fpc, ncells0, ncells1
    integer :: all_faces, interior_faces, iface, my_max_fpc
    integer :: jsize, ioff4(4), count4(0:lmpi_nproc-1)

    integer, dimension(:), allocatable :: my_counts_all, celllist0, cellind0
    integer, dimension(:), allocatable :: temp0, temp1, temp_cl2g, pdata
    integer, dimension(:), allocatable :: eval, ind, indx, which_pe, my_pdata
    integer, dimension(:,:), allocatable :: temp2

    integer(system_i1), dimension(:), allocatable::tagc,tag0,tag1,cellele0

    logical :: verboset = .false.

    type(elem1_cc_type), dimension(:), allocatable :: elem1
    type(elem2_cc_type), dimension(:), allocatable :: elem2
    type(growable_lookup_type) :: clist1

  continue

    grid%ncell0  = 0
    grid%ncell01 = 0

!----------------------------------
! level 0 cell partitions.
! Use metis_data to create level 0 cell list.

    if (lmpi_master)                                                           &
      write(*,*)'    ... Constructing partition cell sets for level-0...'

    allocate(my_counts_all(lmpi_nproc)); my_counts_all = 0
    if (lmpi_nproc > 1) then
       do i = 1,cc_csize(lmpi_id)
          my_counts_all(metis_data(i)) = my_counts_all(metis_data(i)) + 1
       end do
    else
       my_counts_all(1) = size(metis_data)
    end if

    allocate(temp1(0:lmpi_nproc-1)); temp1 = 0
    call lmpi_reduce(my_counts_all,temp1)
    deallocate(my_counts_all)
    call lmpi_bcast(temp1)
    grid%ncell0 = temp1(lmpi_id)
    allocate(celllist0(grid%ncell0)); celllist0 = 0
    allocate(cellind0(grid%ncell0));  cellind0  = 0

    if ((verboset).and.(lmpi_master)) then
     write(*,*)"The max number of level-0 cells per partition is ",maxval(temp1)
     write(*,*)"The min number of level-0 cells per partition is ",minval(temp1)
    end if
    deallocate(temp1)

! Fill the celllist0

    ioff     = 0
    my_count = 0
    do ipe = 0,lmpi_nproc-1
       csize = cc_csize(ipe)
       allocate(temp1(csize)); temp1 = 0
       if (lmpi_id == ipe) temp1(1:csize) = metis_data(1:csize)
       call lmpi_bcast(temp1,ipe)

       do i = 1,csize
          if (temp1(i) == lmpi_id+1) then
             my_count = my_count + 1
             celllist0(my_count) = i + ioff
          end if
       end do
       deallocate(temp1)
       ioff = ioff + csize
    end do
    if (my_count/=grid%ncell0)                                                 &
       write(*,*)"MISMATCH nc0 ",lmpi_id,grid%ncell0,my_count

! deallocate the partition vector

    deallocate(metis_data)

    if (verboset.and.lmpi_master) call se_wall_time('    ... Time for loop0 ')

! Determine the type of each level0 cell

    allocate(cellele0(grid%ncell0)); cellele0 = 0
    allocate(tagc(grid%ncell0)); tagc = 0 ! REMOVE tagc
    do ipe = 0,lmpi_nproc-1
       if (lmpi_id == ipe) then
          j = sum(grid%elem(1:grid%nelem)%ncell)
          allocate(temp0(j)); temp0 = 0
          allocate(tag0(j));  tag0  = 0
          ict = 0
          do ielem = 1,grid%nelem
             if (grid%elem(ielem)%ncell > 0) then
                ! If lowest-numbered node in the cell on ipe, tag it
                do i = 1,grid%elem(ielem)%ncell
                   j = minval(grid%elem(ielem)%c2n(:,i))
                   if((j >= pp_nhead(lmpi_id)).and.(j <= pp_ntail(lmpi_id)))then
                     ict = ict + 1
                     tag0(ict) = ielem
                     temp0(ict) = grid%elem(ielem)%cl2g(i)
                  end if
                end do
             end if
          end do
          if (ict == 0) deallocate(temp0,tag0)
       end if
       call lmpi_bcast(ict,ipe)
       if (ict > 0) then
          allocate(tag1(ict));   tag1 = 0
          allocate(temp1(ict));  temp1  = 0 ! gcell
          if (lmpi_id == ipe) then
             tag1 (1:ict) = tag0 (1:ict)
             temp1(1:ict) = temp0(1:ict)
             deallocate(temp0,tag0)
          end if
          call lmpi_bcast(temp1,ipe)
          call lmpi_integer1_vector_bcast(tag1,ipe)
          do i = 1,ict
             k = binary_search(grid%ncell0,celllist0,temp1(i))
             if (k > 0) cellele0(k) = tag1(i)
          end do
          deallocate(temp1,tag1)
       end if
    end do

! Based on cell numbers, determine how many are of which element and allocate.
! (Will need to reallocate for level01, but storage for level0, for now).

    allocate(temp1(grid%nelem)); temp1 = 0
    do i = 1,grid%ncell0
       ielem = cellele0(i)
       temp1(ielem) = temp1(ielem) + 1
       cellind0(i) = temp1(ielem)
    end do
    allocate(elem1(grid%nelem))
    do ielem = 1,grid%nelem
       npc = grid%elem(ielem)%node_per_cell
       j =  temp1(ielem)
       elem1(ielem)%ncell0  = j
       elem1(ielem)%ncell01 = 0
       allocate(elem1(ielem)%c2n(npc,j)); elem1(ielem)%c2n  = 0
       allocate(elem1(ielem)%cl2g(j));    elem1(ielem)%cl2g = 0
    end do
    do i = 1,grid%ncell0
       ielem = cellele0(i)
       elem1(ielem)%cl2g(cellind0(i)) = celllist0(i)
    end do
    deallocate(temp1)
    deallocate(cellind0)

!----------------------------------
! Gather level01 cells using celllist0

    do ipe = 0,lmpi_nproc-1
       do ielem = 1,grid%nelem
          npc = grid%elem(ielem)%node_per_cell
          ict = 0
          if (lmpi_id == ipe) then
             if (grid%elem(ielem)%ncell > 0) then
                ! If lowest-numbered node in the cell on ipe, tag it
                allocate(temp0(grid%elem(ielem)%ncell)); temp0 = 0
                do i = 1,grid%elem(ielem)%ncell
                   j = minval(grid%elem(ielem)%c2n(:,i))
                   if((j >= pp_nhead(lmpi_id)).and.(j <= pp_ntail(lmpi_id)))then
                      !write(25000+lmpi_id,'(1x,i0,1x,8(1x,i0,1x))')           &
                      !  grid%elem(ielem)%cl2g(i),grid%elem(ielem)%c2n(:,i)
                     ict = ict + 1
                     temp0(ict) = i
                   end if
                end do
                if (ict == 0) deallocate(temp0)
             end if
          end if
          call lmpi_bcast(ict,ipe)
          if (ict > 0) then
             allocate(temp1(ict));     temp1 = 0 ! gcell
             allocate(temp2(npc,ict)); temp2 = 0 ! c2n
             if (lmpi_id == ipe) then
                do i = 1,ict
                   j = temp0(i)
                   temp1(i) = grid%elem(ielem)%cl2g(j)
                   temp2(1:npc,i) = grid%elem(ielem)%c2n(1:npc,j)
                      !write(26000+lmpi_id,'(1x,i0,1x,8(1x,i0,1x))')           &
                      !  temp1(i), temp2(1:npc,i)
                end do
                deallocate(temp0)
             end if
             call lmpi_bcast(temp1,ipe)
             call lmpi_bcast(temp2,ipe)
             do i = 1,ict
                k=binary_search(elem1(ielem)%ncell0,elem1(ielem)%cl2g,temp1(i))
                if (k > 0) elem1(ielem)%c2n(1:npc,k) = temp2(1:npc,i)
             end do
             deallocate(temp1,temp2)
          end if
       end do
    end do

! dump elem1
!   do ielem = 1,grid%nelem
!      if (elem1(ielem)%ncell0 > 0) then
!         npc = grid%elem(ielem)%node_per_cell
!         do icell = 1,elem1(ielem)%ncell0
!            write(27000+lmpi_id,'(1x,i0,1x,8(1x,i0,1x))')                     &
!              elem1(ielem)%cl2g(icell), elem1(ielem)%c2n(1:npc,icell)
!         end do
!      end if
!   end do

!------------------------------------------------------------
    allocate(tag0(grid%nnodesg)); tag0 = 0 ! TBD DANA Use bits
    ki = sum(elem1(:)%ncell0)
    allocate(grid%cl2g(ki)); grid%cl2g = 0
    i = 0
    do ielem = 1,grid%nelem
       npc = grid%elem(ielem)%node_per_cell
       if (elem1(ielem)%ncell0 > 0) then
          do icell = 1,elem1(ielem)%ncell0
             i = i + 1
             grid%cl2g(i) = elem1(ielem)%cl2g(icell)
             do inode = 1,npc
                node = elem1(ielem)%c2n(inode,icell)
                tag0(node) = 1
             end do
          end do
       end if
    end do
    ict = 0
    do i = 1,grid%nnodesg
       if (tag0(i) == 1) ict = ict + 1
    end do
    call heap_sort(ki,grid%cl2g)

    allocate(elem2(grid%nelem))
    do ielem = 1,grid%nelem
       j = nint(grid%elem(ielem)%ncellg*1._dp/lmpi_nproc*1._dp)
       if (j < 1000) j = 1000
       elem2(ielem)%sz = j
       elem2(ielem)%ct = 0
       npc = grid%elem(ielem)%node_per_cell
       nullify(elem2(ielem)%c2n)
       allocate(elem2(ielem)%c2n(npc,j)); elem2(ielem)%c2n  = 0
       nullify(elem2(ielem)%cl2g)
       allocate(elem2(ielem)%cl2g(j));    elem2(ielem)%cl2g = 0
    end do
    call growable_lookup_extend(0,0,clist1)
    do ipe = 0,lmpi_nproc-1
       do ielem = 1,grid%nelem
          npc = grid%elem(ielem)%node_per_cell
          ict = 0
          if (lmpi_id == ipe) then
             if (grid%elem(ielem)%ncell > 0) then
                ! If lowest-numbered node in the cell on ipe, collect it
                allocate(temp0(grid%elem(ielem)%ncell)); temp0 = 0
                do i = 1,grid%elem(ielem)%ncell
                   j = minval(grid%elem(ielem)%c2n(:,i))
                   if((j >= pp_nhead(lmpi_id)).and.(j <= pp_ntail(lmpi_id)))then
                      !write(25000+lmpi_id,'(1x,i0,1x,8(1x,i0,1x))')           &
                      !  grid%elem(ielem)%cl2g(i),grid%elem(ielem)%c2n(:,i)
                     ict = ict + 1
                     temp0(ict) = i
                   end if
                end do
                if (ict == 0) deallocate(temp0)
             end if
          end if
          call lmpi_bcast(ict,ipe)
          if (ict > 0) then
             allocate(temp1(ict));     temp1 = 0 ! gcell
             allocate(temp2(npc,ict)); temp2 = 0 ! c2n
             if (lmpi_id == ipe) then
                do i = 1,ict
                   j = temp0(i)
                   temp1(i) = grid%elem(ielem)%cl2g(j)
                   temp2(1:npc,i) = grid%elem(ielem)%c2n(1:npc,j)
                      !write(26000+lmpi_id,'(1x,i0,1x,8(1x,i0,1x))')           &
                      !  temp1(i), temp2(1:npc,i)
                end do
                deallocate(temp0)
             end if
             call lmpi_bcast(temp1,ipe)
             call lmpi_bcast(temp2,ipe)
             out1a: do i = 1,ict
                do j = 1,npc
                   node = temp2(j,i)
                   if (tag0(node)==1) then
                      k = binary_search(ki,grid%cl2g,temp1(i))
                      if (k == 0) then
                        !write(35000+lmpi_id,*) temp1(i),ielem
                         call growable_lookup_extend(temp1(i),1,clist1)
                         if (elem2(ielem)%ct == elem2(ielem)%sz)               &
                            call extend_elem2_cc(grid%nelem,ielem,elem2)
                         elem2(ielem)%ct = elem2(ielem)%ct + 1
                         elem2(ielem)%cl2g(elem2(ielem)%ct) = temp1(i)
                         elem2(ielem)%c2n(1:npc,elem2(ielem)%ct)=temp2(1:npc,i)
                         cycle out1a
                      end if
                   end if
                end do
             end do out1a
             deallocate(temp1,temp2)
          end if
          ! Update elem2
       end do
    end do
    call growable_lookup_extend(-1,-1,clist1)
    grid%ncell01 = grid%ncell0 + clist1%sorted_ct
    allocate(temp_cl2g(grid%ncell0)); temp_cl2g = grid%cl2g
    deallocate(grid%cl2g)
    allocate(grid%cl2g(grid%ncell01))
    grid%cl2g(1:grid%ncell0) = temp_cl2g(1:grid%ncell0)
    deallocate(temp_cl2g)
    if (associated(clist1%sorted)) then
       grid%cl2g(grid%ncell0+1:grid%ncell01) = clist1%sorted(1:clist1%sorted_ct)
       deallocate(clist1%sorted); nullify(clist1%sorted)
    end if
    clist1%sorted_ct = 0

! Now combine elem2 with elem1, separation shown by ncell0, ncell01

    do ielem = 1,grid%nelem
       npc = grid%elem(ielem)%node_per_cell
       if (elem2(ielem)%ct > 0) then
          i1 = elem1(ielem)%ncell0
          i2 = elem2(ielem)%ct
          i12 = i1+i2
          elem1(ielem)%ncell01 = i12

          if (i1 > 0) then
             allocate(temp1(i1))
             temp1(1:i1) = elem1(ielem)%cl2g(1:i1)
             deallocate(elem1(ielem)%cl2g)
             allocate(temp2(npc,i1))
             temp2(1:npc,1:i1) = elem1(ielem)%c2n(1:npc,1:i1)
             deallocate(elem1(ielem)%c2n)
          end if
          allocate(elem1(ielem)%cl2g(i12))
          allocate(elem1(ielem)%c2n(npc,i12))
          if (i1 > 0) then
             elem1(ielem)%cl2g(1:i1) = temp1(1:i1)
             deallocate(temp1)
             elem1(ielem)%c2n(1:npc,1:i1) = temp2(1:npc,1:i1)
             deallocate(temp2)
          end if
          elem1(ielem)%cl2g(     i1+1:i12) = elem2(ielem)%cl2g(     1:i2)
          elem1(ielem)%c2n(1:npc,i1+1:i12) = elem2(ielem)%c2n(1:npc,1:i2)
          deallocate(elem2(ielem)%cl2g); nullify(elem2(ielem)%cl2g)
          deallocate(elem2(ielem)%c2n);  nullify(elem2(ielem)%c2n)
          elem2(ielem)%sz = 0; elem2(ielem)%ct = 0
       else
         elem1(ielem)%ncell01 = elem1(ielem)%ncell0
       end if
     ! do icell = 1,elem1(ielem)%ncell01
     !    write(37000+lmpi_id,'(1x,i0,1x,8(1x,i0,1x))')                        &
     !      elem1(ielem)%cl2g(icell), elem1(ielem)%c2n(1:npc,icell)
     ! end do
    end do
    deallocate(elem2)
    deallocate(tag0)

    allocate(tag0(grid%nnodesg)); tag0 = 0 ! TBD DANA Use bits
    do ielem = 1,grid%nelem
       npc = grid%elem(ielem)%node_per_cell
       if (elem1(ielem)%ncell0 > 0) then
          do icell = 1,elem1(ielem)%ncell0
             do inode = 1,npc
                tag0(elem1(ielem)%c2n(inode,icell)) = 1
             end do
          end do
       end if
    end do
    grid%nnodes0 = 0
    do i = 1,grid%nnodesg
       if (tag0(i) == 1) grid%nnodes0 = grid%nnodes0 + 1
    end do
    allocate(temp0(grid%nnodes0)); temp0 = 0
    j = 0
    do i = 1,grid%nnodesg
       if (tag0(i) == 1) then
          j = j + 1
          temp0(j) = i
       end if
    end do

    do ielem = 1,grid%nelem
       npc = grid%elem(ielem)%node_per_cell
       if (elem1(ielem)%ncell01 > 0) then
          do icell = elem1(ielem)%ncell0+1,elem1(ielem)%ncell01
             do inode = 1,npc
                tag0(elem1(ielem)%c2n(inode,icell)) = 1
             end do
          end do
       end if
    end do
    grid%nnodes01 = 0
    do i = 1,grid%nnodesg
       if (tag0(i) == 1) grid%nnodes01 = grid%nnodes01 + 1
    end do
    allocate(grid%l2g(grid%nnodes01)); grid%l2g = 0
    grid%l2g(1:grid%nnodes0) = temp0(1:grid%nnodes0)
    deallocate(temp0)
    do i = 1,grid%nnodes0
       tag0(grid%l2g(i)) = 0
    end do
    j = grid%nnodes0
    do i = 1,grid%nnodesg
       if (tag0(i) == 1) then
          j = j + 1
          grid%l2g(j) = i
       end if
    end do
    deallocate(tag0)

!   do i = 1,grid%nnodes01
!      write(39000+lmpi_id,*) grid%l2g(i)
!   end do
!   write(*,*)"N0,1,01",lmpi_id,                                               &
!     grid%nnodes0,grid%nnodes01,grid%nnodes01-grid%nnodes0

   if (verboset.and.lmpi_master) call se_wall_time('    ... Time for loop2 ')

   call bcast_xyz(grid)

 ! do i = 1,grid%nnodes01
 !    write(40000+lmpi_id,'(1x,i0,1x,3(F20.12,1x))')                           &
 !      grid%l2g(i),grid%x(i),grid%y(i),grid%z(i)
 ! end do

! Deallocate old grid cell info, and move/deallocate elem1 info.

   do ielem = 1,grid%nelem
      if (grid%elem(ielem)%ncell > 0) then
         deallocate(grid%elem(ielem)%c2n)
         deallocate(grid%elem(ielem)%cl2g)
         grid%elem(ielem)%ncell  = 0
      end if
   end do

   do ielem = 1,grid%nelem
      grid%elem(ielem)%ncell0 = elem1(ielem)%ncell0
      grid%elem(ielem)%ncell  = elem1(ielem)%ncell01
      if (grid%elem(ielem)%ncell > 0) then
         npc = grid%elem(ielem)%node_per_cell
         allocate(grid%elem(ielem)%c2n(npc,grid%elem(ielem)%ncell))
         grid%elem(ielem)%c2n = elem1(ielem)%c2n
         deallocate(elem1(ielem)%c2n)
         allocate(grid%elem(ielem)%cl2g(grid%elem(ielem)%ncell))
         grid%elem(ielem)%cl2g = elem1(ielem)%cl2g
         deallocate(elem1(ielem)%cl2g)
      end if
   end do

!--------- C2C --------------
! Allocate storage for c2c

    do ielem = 1,grid%nelem
       if (grid%elem(ielem)%ncell > 0) then
          fpc = grid%elem(ielem)%face_per_cell
          allocate(grid%elem(ielem)%c2c(fpc,grid%elem(ielem)%ncell))
          grid%elem(ielem)%c2c = 0
       end if
    end do

! Sort temp cl2g (for matching passed c2c info)

   eval_ct = sum(grid%elem(:)%ncell)
   allocate(eval(eval_ct)); eval = 0
   allocate(indx(eval_ct)); indx = 0
   eval_ct = 0
   do ielem = 1,grid%nelem
      do icell = 1,grid%elem(ielem)%ncell
         eval_ct = eval_ct + 1
         eval(eval_ct) = grid%elem(ielem)%cl2g(icell)
         indx(eval_ct) = icell
      end do
   end do
   allocate(ind(eval_ct)); ind  = 0
   call heap_sort(eval_ct,eval,ind)
   allocate(temp_cl2g(eval_ct))
   temp_cl2g = eval; eval = temp_cl2g(ind)
   temp_cl2g = indx; indx = temp_cl2g(ind)
   deallocate(temp_cl2g,ind)

! Count how many c2c per element to pass

    my_max_fpc = 0
    do ielem = 1,grid%nelem
      fpc =  grid%elem(ielem)%face_per_cell
      if ((elem3_c2c(ielem)%ncell > 0).and.(fpc > my_max_fpc)) my_max_fpc = fpc
    end do
    my_ct = sum(elem3_c2c(:)%ncell)

! Pass, examine and store values.

    do ipe = 0,lmpi_nproc-1
       if (lmpi_id == ipe) then
          ict     = my_ct
          max_fpc = my_max_fpc
          ioff4   = 0
          ioff4(1:grid%nelem) = elem3_c2c(:)%ncell
          if (grid%nelem > 1) then
             do i = 2,grid%nelem
                ioff4(i) = ioff4(i-1) + ioff4(i)
             end do
          end if
          !write(*,*)" IOFF4 ",ioff4
       end if
       call lmpi_bcast(ict,    ipe)
       call lmpi_bcast(max_fpc,ipe)
       call lmpi_bcast(ioff4,  ipe)
       if (ict == 0) cycle
       allocate(temp0(ict));         temp0 = 0 ! cl2g
       allocate(temp2(max_fpc,ict)); temp2 = 0 ! c2c
       if (lmpi_id == ipe) then
          ct  = 0
          do ielem = 1,grid%nelem
             fpc = grid%elem(ielem)%face_per_cell
             do icell = 1,elem3_c2c(ielem)%ncell
                ct = ct + 1
                temp0(ct) = elem3_c2c(ielem)%cl2g(icell)
                temp2(1:fpc,ct) = elem3_c2c(ielem)%c2c(1:fpc,icell)
             end do
          end do
       end if
       call lmpi_bcast(temp0,ipe)
       call lmpi_bcast(temp2,ipe)
       jelem = 1
       fpc = grid%elem(jelem)%face_per_cell
       do i = 1,ict
          if (i > ioff4(jelem)) then ! determine elem and fpc
             do j = jelem,grid%nelem
                if (i <= ioff4(j)) then
                   jelem = j
                   fpc = grid%elem(j)%face_per_cell
                   exit
                end if
             end do
          end if
          k = binary_search(eval_ct,eval,temp0(i))
          if (k > 0) grid%elem(jelem)%c2c(1:fpc,indx(k)) = temp2(1:fpc,i)
       end do ! ict
       deallocate(temp0,temp2)
    end do ! ipe
    deallocate(indx)

! Debug
!   do ielem = 1,grid%nelem
!      fpc = grid%elem(ielem)%face_per_cell
!      do icell = 1,grid%elem(ielem)%ncell
!         write(470000+lmpi_id,'(1x,i0,1x,8(i0,1x))')                          &
!           grid%elem(ielem)%cl2g(icell), grid%elem(ielem)%c2c(:,icell)
!     end do
!   end do

! Compute grid%nface, grid%nbface0, grid%nfaceg

    all_faces = 0
    interior_faces = 0
    do ielem = 1,grid%nelem
       fpc = grid%elem(ielem)%face_per_cell
       all_faces = all_faces + fpc*grid%elem(ielem)%ncell0
       do icell = 1,grid%elem(ielem)%ncell0
          do iface = 1,fpc
             gcell1 = grid%elem(ielem)%c2c(iface,icell)
             if (gcell1 > 0) interior_faces = interior_faces + 1
          end do
       end do
    end do
    grid%nface = interior_faces/2
    grid%nbface0 = all_faces-interior_faces
    call lmpi_reduce(interior_faces,grid%nfaceg)
    call lmpi_bcast(grid%nfaceg)
    grid%nfaceg = grid%nfaceg/2
    ! write(*,*)"FACES0: all,inter ",lmpi_id, interior_faces, grid%nface

    ! write(77000+lmpi_id,*)"sz(celllist0) ",size(celllist0)
    ! write(77000+lmpi_id,*)"e(NC  ) ",grid%elem(:)%ncell
    ! write(77000+lmpi_id,*)"e(nc0 ) ",grid%elem(:)%ncell0
    ! write(77000+lmpi_id,*)"sz(recvindx) ",                                   &
    !   sum(grid%elem(:)%ncell) - sum(grid%elem(:)%ncell0)
    ! write(77000+lmpi_id,*)'recv by elem'
    ! do ielem = 1,grid%nelem
    !    write(77000+lmpi_id,*) grid%elem(ielem)%ncell-grid%elem(ielem)%ncell0
    ! end do

! Determine the pe for level1 cells ()

    ncells1 = 0
    do ielem = 1,grid%nelem
       ncells1 = ncells1 + (grid%elem(ielem)%ncell-grid%elem(ielem)%ncell0)
    end do
    ! write(*,*)"NCELLS1 (1) ",lmpi_id,ncells1
    allocate(temp1(ncells1)); temp1 = 0
    ncells1 = 0
    do ielem = 1,grid%nelem
       if (grid%elem(ielem)%ncell > grid%elem(ielem)%ncell0) then
          do icell = grid%elem(ielem)%ncell0+1,grid%elem(ielem)%ncell
             ncells1 = ncells1 + 1
             temp1(ncells1) = grid%elem(ielem)%cl2g(icell)
          end do
       end if
    end do
    ! write(*,*)"NCELLS1 (2) ",lmpi_id,ncells1
    allocate(which_pe(ncells1)); which_pe = lmpi_nproc

    ncells0 = size(celllist0)
    allocate(my_pdata(ncells0))
    my_pdata = celllist0
    call lmpi_max(ncells0,pmax)
    call lmpi_bcast(pmax)
    allocate(pdata(pmax))
    do ipe = 0,lmpi_nproc-1
       if (lmpi_id == ipe) jsize = ncells0
       call lmpi_bcast(jsize,ipe)
       pdata = 0
       if (lmpi_id == ipe) pdata(1:jsize) = my_pdata(1:jsize)
       call lmpi_bcast(pdata,ipe)
       do i = 1,ncells1
          if (which_pe(i) == lmpi_nproc) then
             j = binary_search(jsize,pdata,temp1(i))
             if (j > 0) which_pe(i) = ipe
          end if
       end do
    end do
    deallocate(pdata)
    deallocate(my_pdata)

    count4 = 0
    do i = 1,ncells1
       j = which_pe(i)
       count4(j) = count4(j) + 1
    end do

    !write(*,*)"SUM(count4) ",lmpi_id,sum(count4),count4
    ! do i = 1,ncells1
    !   write(133000+lmpi_id,'(1x,i0,1x,i0,1x)') temp1(i),which_pe(i)
    !end do
    !do i = 1,ncells0
    !   write(134000+lmpi_id,'(1x,i0,1x,i0,1x)') celllist0(i),lmpi_id
    !end do

    ncells0 = sum(grid%elem(1:grid%nelem)%ncell0)
    allocate(temp0(ncells0))
    ncells0 = 0
    do ielem = 1,grid%nelem
       do icell = 1,grid%elem(ielem)%ncell0
          ncells0 = ncells0 + 1
          temp0(ncells0) = grid%elem(ielem)%cl2g(icell)
       end do
    end do
    ! write(*,*)"NCELLS0 ",lmpi_id,ncells0
    ! write(*,*)"NN0 ",lmpi_id,grid%elem(1:grid%nelem)%ncell0
    ! do i = 1,ncells0
    !   write(60+lmpi_id,*) temp0(i)
    ! end do
    deallocate(temp0)

! Deallocate

    deallocate(celllist0,temp1,which_pe,eval,cellele0)

 end subroutine node_cell_chopper_cc

!============================== extend_elem2_cc ==============================80
!
! Extend elem2 cc. TBD consolidate with pp_metis extend_elem2.
!
!=============================================================================80

  subroutine extend_elem2_cc(g_nelem,ielem,elem2)

    integer,                                 intent(in)    :: g_nelem, ielem
    type(elem2_cc_type), dimension(g_nelem), intent(inout) :: elem2

    integer :: npc, old_size, new_size
    integer, dimension(:),   allocatable :: temp1
    integer, dimension(:,:), allocatable :: temp2

    continue

      old_size = elem2(ielem)%sz
      new_size = nint(old_size * 1.10)
      elem2(ielem)%sz = new_size
      ! write(*,*)"EXTEND ",lmpi_id,old_size,new_size

      allocate(temp1(old_size)); temp1 = 0
      temp1(1:old_size) = elem2(ielem)%cl2g(1:old_size)
      deallocate(elem2(ielem)%cl2g); nullify(elem2(ielem)%cl2g)
      allocate(elem2(ielem)%cl2g(new_size)); elem2(ielem)%cl2g = 0
      elem2(ielem)%cl2g(1:old_size) = temp1(1:old_size)
      deallocate(temp1)

      npc = size(elem2(ielem)%c2n,1)
      allocate(temp2(npc,old_size)); temp2 = 0
      temp2(1:npc,1:old_size) = elem2(ielem)%c2n(1:npc,1:old_size)
      deallocate(elem2(ielem)%c2n); nullify(elem2(ielem)%c2n)
      allocate(elem2(ielem)%c2n(npc,new_size)); elem2(ielem)%c2n = 0
      elem2(ielem)%c2n(1:npc,1:old_size) = temp2(1:npc,1:old_size)
      deallocate(temp2)
      ! write(*,*)"EXTEND ",lmpi_id,ielem,elem2(ielem)%ct,elem2(ielem)%sz

  end subroutine extend_elem2_cc

!=============================== BCAST_XYZ ===================================80
!
! Bcast xyz.
!
!=============================================================================80

  subroutine bcast_xyz( grid )

    use grid_types, only : grid_type
    use sort,       only : heap_sort

    type(grid_type), intent(inout) :: grid

    integer :: i, is, ie, last, ipe

    integer,  dimension(:), allocatable :: t1, t1_ind
    real(dp), dimension(:), allocatable :: tx,ty,tz, sx,sy,sz

  continue

    allocate(t1    (grid%nnodes01))
    allocate(t1_ind(grid%nnodes01))
    call heap_sort(grid%nnodes01,grid%l2g,t1_ind)
    t1 = grid%l2g(t1_ind)

    is = pp_nsize(lmpi_id)
    allocate(sx(is)); sx = grid%x(1:is); deallocate(grid%x)
    allocate(sy(is)); sy = grid%y(1:is); deallocate(grid%y)
    allocate(sz(is)); sz = grid%z(1:is); deallocate(grid%z)

    allocate(grid%x(grid%nnodes01))
    allocate(grid%y(grid%nnodes01))
    allocate(grid%z(grid%nnodes01))

    last = 1
    do ipe = 0,lmpi_nproc-1

       is = pp_nhead(ipe)
       ie = pp_ntail(ipe)

       allocate(tx(is:ie))
       allocate(ty(is:ie))
       allocate(tz(is:ie))

       if (lmpi_id == ipe) then
          tx = sx; ty = sy; tz = sz
          deallocate(sx,sy,sz)
       end if

       call lmpi_bcast(tx,ipe)
       call lmpi_bcast(ty,ipe)
       call lmpi_bcast(tz,ipe)

       if (last <= grid%nnodes01) then
          do i = is,ie
             if (i == t1(last)) then
                grid%x(t1_ind(last)) = tx(i)
                grid%y(t1_ind(last)) = ty(i)
                grid%z(t1_ind(last)) = tz(i)
                last = last + 1
                if (last > grid%nnodes01) exit
             end if
          end do
       end if

       deallocate(tx,ty,tz)

   end do

   if (allocated(t1))     deallocate(t1)
   if (allocated(t1_ind)) deallocate(t1_ind)

  end subroutine bcast_xyz

!================================== BC_CHOPPER ===============================80
!
! Partitions the boundaries face bits.
!
! Note ibnodes and nf2ntb are still global numbers (not c2n_map or l2g).
!
!=============================================================================80

  subroutine bc_chopper(grid)

    use grid_types, only : grid_type

    type(grid_type), intent(inout) ::  grid

    logical  :: b1
    integer  :: i, word1, tag_size, iface, node
    integer,    dimension(:), allocatable :: tag0

    continue

!   Tag all level0 nodes

    tag_size  = ceiling(grid%nnodesg*bsize_inv)
    allocate(tag0(tag_size)); tag0 = 0

    do i = 1,grid%nnodes0
       word1 = ceiling(grid%l2g(i)*bsize_inv)
       tag0(word1) = ibset(tag0(word1), mod(grid%l2g(i),bsize))
    end do

    do i=1, grid%nbound

      if (grid%bc(i)%nbfacet > 0) then

        if (.not.associated(grid%bc(i)%face_bit))                              &
           allocate(grid%bc(i)%face_bit(grid%bc(i)%nbfacet))
        grid%bc(i)%face_bit = 0

        do iface = 1,grid%bc(i)%nbfacet
           node = grid%bc(i)%ibnode(grid%bc(i)%f2ntb(iface,1))
           word1 = ceiling(node*bsize_inv)
           b1 = btest(tag0(word1), mod(node,bsize))
           if (b1) grid%bc(i)%face_bit(iface) = 1
        end do
      else
        allocate(grid%bc(i)%face_bit(1)); grid%bc(i)%face_bit = 0
      end if

      if (grid%bc(i)%nbfaceq > 0) then

        allocate(grid%bc(i)%face_bitq(grid%bc(i)%nbfaceq))
        grid%bc(i)%face_bitq = 0

        do iface = 1,grid%bc(i)%nbfaceq
           node = grid%bc(i)%ibnode(grid%bc(i)%f2nqb(iface,1))
           word1 = ceiling(node*bsize_inv)
           b1 = btest(tag0(word1), mod(node,bsize))
           if (b1) grid%bc(i)%face_bitq(iface) = 1
        end do
      else
        allocate(grid%bc(i)%face_bitq(1)); grid%bc(i)%face_bitq = 0
      end if

    end do ! nbound

    deallocate(tag0)

  end subroutine bc_chopper


!=============================== pparty_convert_g2l ==========================80
!
! Convert all global node numbers to local number (l2g)
!
!=============================================================================80

  subroutine pparty_convert_g2l(grid)

    use grid_types, only : grid_type
    use sort,       only : binary_search, heap_sort

    type(grid_type), intent(inout) :: grid

! local

    integer :: ielem, icell, inode, node, ibound, nbnode
    integer :: i,j,k,n01

    integer, dimension(:), allocatable :: t1, t1_ind

   continue

! Mapping of l2g

    n01     = grid%nnodes01

    allocate(t1    (n01)); t1 = grid%l2g
    allocate(t1_ind(n01))
    call heap_sort(n01,t1,t1_ind)
    t1 = grid%l2g(t1_ind)

! c2n

    do ielem = 1,grid%nelem
       do icell = 1,grid%elem(ielem)%ncell
          do inode = 1,grid%elem(ielem)%node_per_cell
             node = grid%elem(ielem)%c2n(inode,icell)
             j = binary_search(n01,t1,node)
             grid%elem(ielem)%c2n(inode,icell) = t1_ind(j)
          end do
       end do
    end do

! eptr

    do i = 1,grid%nedge
      !write(4000+lmpi_id,*) grid%eptr(1,i),grid%eptr(2,i)
       j = grid%eptr(1,i); grid%eptr(1,i) = t1_ind(binary_search(n01,t1,j))
       j = grid%eptr(2,i); grid%eptr(2,i) = t1_ind(binary_search(n01,t1,j))
    end do

! Boundaries related node numbers: ibnode. Not faces, which are localized.

    if (grid%nbound > 0) then
       do ibound = 1,grid%nbound
          nbnode = grid%bc(ibound)%nbnode
          if (nbnode > 0) then
             do i = 1,nbnode
                k = binary_search(n01,t1,grid%bc(ibound)%ibnode(i))
              ! write(33000+lmpi_id,'(i0,1x,i0,":",3(i0,1x))') &
              !  ibound,i,j,k,t1_ind(k)
                grid%bc(ibound)%ibnode(i) = t1_ind(k)
             end do
          end if
       end do ! ibound
    end if

    deallocate(t1,t1_ind)

  end subroutine pparty_convert_g2l

!================================ WRITEGRID_SM ===============================80
!
! Write out partitioned grid files
!
!=============================================================================80

  subroutine writegrid_sm(grid,outformat,name)

    use grid_types,        only : grid_type
    use party_lmpi,        only : party_lmpi_writeme_sm
    use pparty_io,         only : writeme_headvol_sm, writeme_boundary_sm,     &
                                  writeme_cc_sm
    use info_depr,         only : cc_primal
    use string_utils,      only : sprintf, max_str_len

    type(grid_type), intent(inout) :: grid
    logical,         intent(in)    :: outformat
    character(*),    intent(in), optional :: name

    integer     :: ielem, iunit
    logical     :: l_opened
! beginNeverComplex
    real(dp)    :: version
! endNeverComplex

  character (max_str_len) :: part_name
  logical :: tet_only

  continue

    !write(*,*)"ENTER writegrid_sm ",lmpi_id,grid%nbfaceg
    iunit = 2000+lmpi_id

    if (.not.outformat) then
       part_name = sprintf( trim(grid%project)//'_part.%i0', lmpi_id+1)
    else
       if (.not.present(name)) then
          part_name = sprintf( trim(grid%project)//'_part.%i0', lmpi_id+1)
       else
          part_name = sprintf( trim(grid%project)//'_'//trim(name)//'.%i0',    &
                      lmpi_id+1)
       end if
    end if

    if (outformat) then
     call se_open(iunit,file=part_name)
    else
     call se_open(iunit,file=part_name,form='unformatted')
    end if

!   default to version 3 style, unless mixed elements are used/forced

    version = 3.0_dp
    tet_only = .true.

    do ielem = 1,grid%nelem
      if (grid%elem(ielem)%type_cell /= 'tet') then
         tet_only = .false.
         version = 4.0_dp
      end if
    end do

!   Modify version number to check for cc_primal.

    if (cc_primal) version = version + 0.1_dp

    if (tet_only) then
       call writeme_headvol_sm(iunit, grid, outformat, lmpi_nproc)
    else
       call writeme_headvol(iunit,lmpi_id+1,lmpi_nproc,grid,outformat,         &
            cc_primal,0,0) ! TBD CC
    end if

    if (outformat) write(iunit,*)'------------ B4 party_lmpi_writeme_sm'
    call party_lmpi_writeme_sm(iunit, grid, outformat, lmpi_nproc)
    if (outformat) write(iunit,*)'------------ Af party_lmpi_writeme_sm'

    if (outformat) write(iunit,*)'------------ B4 writeme_boundary_sm'
    call writeme_boundary_sm(iunit, grid, outformat, version)
    if (outformat) write(iunit,*)'------------ AF writeme_boundary_sm'
    if (grid%cc) call writeme_cc_sm(iunit, outformat, grid)

    inquire(unit=iunit,OPENED=l_opened)
    if (l_opened) close(iunit)

  end subroutine writegrid_sm

!========================= WRITEME_HEADVOL ===================================80
!
! Write grid part file header and volume
!
!=============================================================================80

  subroutine writeme_headvol(unit,ipart,nparts,grid,outformat,                 &
                             cc_primal, ncell_augmentorsg,  nface_augmentorsg)

    use grid_types, only : grid_type
    use info_depr,  only : mixed, twod
    use party_lmpi,    only : ppdb_part
    use pparty_io,   only : writeme_vol_level2

    integer,         intent(in) :: unit,ipart,nparts
    logical,         intent(in) :: outformat, cc_primal
    type(grid_type), intent(in) :: grid

    integer, intent(in) :: ncell_augmentorsg,  nface_augmentorsg

    integer :: tempcell2, ielem, nedgeg_i4, ncellg_i4

    real(dp) :: version

    character(len=80) :: iformatdesc, rformatdesc, eformatdesc, lformatdesc

    integer :: nnodes1
    logical :: logical_dummy = .false.

  continue

    iformatdesc = '(i10)'
    rformatdesc = '(f5.2)'
    eformatdesc = '(a10,6i10)'
    lformatdesc = '(l5)'

!   default to version 3 format for now; version 4 only for other than tets

    version = 3.0_dp

    if (grid%nelem > 1) version = 4.0_dp

    do ielem = 1,grid%nelem
      if (grid%elem(ielem)%type_cell /= 'tet') version = 4.0_dp
    end do

!   force version 4 format if mixed = .true.

    if (mixed) version = 4.0_dp

!   Modify version number to check for cc_primal.

    if (cc_primal) version = version + 0.1_dp

    if (outformat) then

      if (ppdb_part) then
         write(unit,*)'beg writeme_header_and_volume ',cc_primal
         write(*,*)   'Writing grid partition in formatted (ASCII)'
      end if

      write (unit,rformatdesc) version

    else

      write (unit) version

    end if

    nedgeg_i4 = grid%nedgeg

    if (cc_primal) then
      if ( outformat ) then
        write(unit,*)  grid%nface,   grid%ncell0, grid%ncell01,           &
                       grid%nbface0, grid%ncellg, grid%nfaceg,            &
                       grid%nbfaceg,                                      &
                       grid%ncell_augmentors, grid%nface_augmentors,      &
                       ncell_augmentorsg,     nface_augmentorsg,          &
                       logical_dummy
      else
        write(unit)  grid%nface,   grid%ncell0, grid%ncell01,             &
                     grid%nbface0, grid%ncellg, grid%nfaceg,              &
                     grid%nbfaceg,                                        &
                     grid%ncell_augmentors, grid%nface_augmentors,        &
                     ncell_augmentorsg,     nface_augmentorsg,            &
                     logical_dummy
      endif
    end if

!   write basic dimensioning data

    file_version : if (nint(real(version,dp)) > 3) then

      formatted_new : if (outformat) then

        write (unit,lformatdesc) twod

        nnodes1 = grid%nnodes01 - grid%nnodes0

        write (unit,iformatdesc)                                               &
             ipart,         nparts,                                            &
             grid%nnodesg,  nedgeg_i4,                                         &
             grid%nbound,   grid%nelem,                                        &
             grid%idistfcn,                                                    &
             grid%nnodes0,  nnodes1,         grid%nnodes01,                    &
             grid%nedgeloc, grid%nedge,      grid%nedgeloc_2d

        do ielem = 1,grid%nelem
          tempcell2 = 0
          ncellg_i4 = grid%elem(ielem)%ncellg
          write(unit, eformatdesc)                                             &
                      grid%elem(ielem)%type_cell,                              &
                      grid%elem(ielem)%ncell,                                  &
                      tempcell2,                                               &
                      ncellg_i4
        end do

      else

        write (unit) twod

        nnodes1 = grid%nnodes01 - grid%nnodes0
        write (unit)                                                           &
             ipart,         nparts,                                            &
             grid%nnodesg,  nedgeg_i4,                                         &
             grid%nbound,   grid%nelem,                                        &
             grid%idistfcn,                                                    &
             grid%nnodes0,  nnodes1,         grid%nnodes01,                    &
             grid%nedgeloc, grid%nedge,      grid%nedgeloc_2d

        do ielem = 1,grid%nelem
          tempcell2 = 0
          ncellg_i4 = grid%elem(ielem)%ncellg
          write(unit)                                                          &
                      grid%elem(ielem)%type_cell,                              &
                      grid%elem(ielem)%ncell,                                  &
                      tempcell2,                                               &
                      ncellg_i4
        end do

      end if formatted_new

    else

      formatted_old : if (outformat) then

        tempcell2 = 0
        ncellg_i4 = grid%elem(ielem)%ncellg
        nnodes1 = grid%nnodes01 - grid%nnodes0
        write (unit,iformatdesc)                                               &
             ipart,         nparts,                                            &
             grid%nnodesg,  ncellg_i4,  nedgeg_i4,                             &
             grid%nbound,                                                      &
             grid%idistfcn,                                                    &
             grid%nnodes0,  nnodes1, grid%nnodes01,                            &
             grid%elem(1)%ncell,    tempcell2,                                 &
             grid%nedgeloc, grid%nedge,                                        &
             1,             1

      else

        tempcell2 = 0
        ncellg_i4 = grid%elem(ielem)%ncellg
        nnodes1 = grid%nnodes01 - grid%nnodes0
        write (unit)                                                           &
             ipart,         nparts,                                            &
             grid%nnodesg,  ncellg_i4,  nedgeg_i4,                             &
             grid%nbound,                                                      &
             grid%idistfcn,                                                    &
             grid%nnodes0,  nnodes1, grid%nnodes01,                            &
             grid%elem(1)%ncell,    tempcell2,                                 &
             grid%nedgeloc, grid%nedge,                                        &
             1,             1

      end if formatted_old

    end if file_version

!   write mesh data

    nedgeg_i4 = grid%nedge
    call writeme_vol_level2( unit,                                             &
         grid%nnodes01, grid%ncell01,                                          &
         nedgeg_i4,     grid%nedgeloc,                                         &
         grid%eptr,                                                            &
         grid%nelem,    grid%elem,                                             &
         grid%x,        grid%y,        grid%z,                                 &
         grid%idistfcn, grid%l2g,      grid%el2g,     outformat,      version)

      if (ppdb_part.and.outformat) write(unit,*)'end writeme_header_and_volume'

  end subroutine writeme_headvol

end module pparty

module reconstruction

  use kinddefs,        only : dp
  use twod_util,       only : yplane_2d, y_coplanar_tol
  use generic_gas_map, only : n_momx, n_momy, n_momz
  use bc_types,        only : bcgrid_type
  use info_depr,       only : wls_inv_dist_scale
  use lmpi,            only : lmpi_conditional_stop

  implicit none

  private

  public :: sumgs, sumgs_periodic, lstgs, lstgs_sym, lstgs_periodic
  public :: green_gauss
  public :: sumgs_2d, lstgs_2d, symmetry_tol
  public :: lstgs_2d_turb, lstgs_turb
  public :: lstgs_nobound, lstgs_vect, get_vector_grad
  public :: sumgs_nobound
  public :: timlim, timlimi, venlim, no_lim, gsblim
  public :: n_pseudo_faces, pseudo_fptr
  public :: pressure_limiter_coeff
  public :: do_not_delete_here_for_r_vec

  integer :: nedge_ns = 0                      ! # of non-simple edges
  integer :: n_pseudo_faces = 0                ! # of pseudo faces

  integer, dimension(:,:), pointer :: eptr_ns     ! edge ptr for non-simple edge
  integer, dimension(:,:), pointer :: pseudo_fptr ! pseudo face ptr

  logical :: ns_edges_initialized = .false.

  real(dp)    :: weight_factor
  real(dp)    :: symmetry_tol = 1.0e-08_dp

contains

!================================== SUMGS ====================================80
!
! Gets the weights for calculating gradients using least squares
!
!=============================================================================80
  subroutine sumgs(nnodes0,nnodes01,nedgeloc,eptr,symmetry,x,y,z,r11,r12,r13,  &
                   r22,r23,r33,nnz01,ia,ja,weighted)

    use info_depr,  only : ns_recon
    use debug_defs, only : symmetry_bcs

    integer,                                  intent(in)  :: nnodes0, nnodes01
    integer,                                  intent(in)  :: nedgeloc
    integer,                        optional, intent(in)  :: nnz01
    integer,  dimension(2,nedgeloc),          intent(in)  :: eptr
    integer,  dimension(nnodes01),            intent(in)  :: symmetry
    integer,  dimension(:),         optional, intent(in)  :: ia, ja
    real(dp), dimension(nnodes01),            intent(in)  :: x, y, z
    real(dp), dimension(nnodes0),             intent(out) :: r11, r12, r13
    real(dp), dimension(nnodes0),             intent(out) :: r22, r23, r33
    logical,                        optional, intent(in)  :: weighted

    integer  :: i, n, node1, node2

    real(dp) :: dx, dy, dz, t11, t12, t13, t22, t23, t33
    real(dp) :: weight, w2

    real(dp), parameter    :: my_0 = 0.0_dp
    real(dp), parameter    :: my_1 = 1.0_dp

  continue

!   Set distance weighting factor

    weight_factor = my_0
    if (present( weighted )) then
      if(weighted) weight_factor = my_1
    endif

!   If using non-simple connections in the reconstruction, go out and
!   find these pseudo "edges" if we haven't already

    setup_pseudo_edges : if (ns_recon .and. ( .not. ns_edges_initialized )) then
      if ( .not. present(nnz01) .or.                                           &
           .not. present(ia)    .or.                                           &
           .not. present(ja) ) then
        write(*,*) 'Not enough arguments provided for sumgs'
        call lmpi_conditional_stop(1)
      endif
      call find_pseudo_edges(nnodes01, nnz01, nedgeloc, ia, ja, eptr)
      ns_edges_initialized = .true.
    endif setup_pseudo_edges

!   Initialize all the rij to 0.0

    do i = 1,nnodes0
      r11(i) = my_0
      r12(i) = my_0
      r13(i) = my_0
      r22(i) = my_0
      r23(i) = my_0
      r33(i) = my_0
    end do

!   Now loop over the edges and accumulate the r's

    scan_edges : do n = 1, nedgeloc + nedge_ns

      if ( n <= nedgeloc ) then
        node1 = eptr(1,n)
        node2 = eptr(2,n)
      else
        node1 = eptr_ns(1,n-nedgeloc)
        node2 = eptr_ns(2,n-nedgeloc)
      endif

      dx = x(node2) - x(node1)
      dy = y(node2) - y(node1)
      dz = z(node2) - z(node1)

      weight = reconstruct_weight(dx, dy, dz, weight_factor, wls_inv_dist_scale)
      w2 = weight*weight

      if(node1 <= nnodes0) then
        t11 = dx*dx*w2
        t12 = dx*dy*w2
        t13 = dx*dz*w2
        t22 = dy*dy*w2
        t23 = dy*dz*w2
        t33 = dz*dz*w2

        if ( symmetry_bcs .and. (symmetry(node1) /= 0))                       &
          call sumgs_sym(symmetry(node1),dx,dy,dz,t11,t12,t13,t22,t23,t33)

        r11(node1) = r11(node1) + t11
        r12(node1) = r12(node1) + t12
        r13(node1) = r13(node1) + t13
        r22(node1) = r22(node1) + t22
        r23(node1) = r23(node1) + t23
        r33(node1) = r33(node1) + t33
      endif

      dx = -dx
      dy = -dy
      dz = -dz

      if(node2 <= nnodes0) then
        t11 =  dx*dx*w2
        t12 =  dx*dy*w2
        t13 =  dx*dz*w2
        t22 =  dy*dy*w2
        t23 =  dy*dz*w2
        t33 =  dz*dz*w2

        if ( symmetry_bcs .and. (symmetry(node2) /= 0))                       &
          call sumgs_sym(symmetry(node2),dx,dy,dz,t11,t12,t13,t22,t23,t33)

        r11(node2) = r11(node2) + t11
        r12(node2) = r12(node2) + t12
        r13(node2) = r13(node2) + t13
        r22(node2) = r22(node2) + t22
        r23(node2) = r23(node2) + t23
        r33(node2) = r33(node2) + t33
      endif

    enddo scan_edges

    do i = 1,nnodes0
      r11(i) = sqrt( r11(i)                                 )
      r12(i) = r12(i)/r11(i)
      r13(i) = r13(i)/r11(i)
      r22(i) = sqrt( r22(i) - r12(i)*r12(i)                 )
      r23(i) = (r23(i) - r12(i) * r13(i)) / r22(i)
      r33(i) = sqrt( r33(i) - r13(i)*r13(i) - r23(i)*r23(i) )
    end do

  end subroutine sumgs

!=============================== SUMGS_sym ===================================80
!
! Returns the contributions to r11, r12, r13, r22, r23, r33 using symmetry
!
!=============================================================================80
  subroutine sumgs_sym(symmetry, dx, dy, dz, t11, t12, t13, t22, t23, t33 )

    integer,  intent(in)    :: symmetry
    real(dp), intent(in)    :: dx, dy, dz
    real(dp), intent(inout) :: t11, t12, t13, t22, t23, t33

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_2 = 2.0_dp
    real(dp), parameter :: my_4 = 4.0_dp

  continue

    if(symmetry == 100 .and. (abs(dx) > symmetry_tol)) then

      t11 = my_2*t11
      t12 = my_0
      t13 = my_0
      t22 = my_2*t22
      t23 = my_2*t23
      t33 = my_2*t33

    elseif(symmetry == 010 .and. (abs(dy) > symmetry_tol)) then

      t11 = my_2*t11
      t12 = my_0
      t13 = my_2*t13
      t22 = my_2*t22
      t23 = my_0
      t33 = my_2*t33

    elseif(symmetry == 001 .and. (abs(dz) > symmetry_tol)) then

      t11 = my_2*t11
      t12 = my_2*t12
      t13 = my_0
      t22 = my_2*t22
      t23 = my_0
      t33 = my_2*t33

    elseif(symmetry == 110 .and. ((abs(dx) > symmetry_tol)      &
                           .and.  (abs(dy) > symmetry_tol))) then

      t11 = my_4*t11
      t12 = my_0
      t13 = my_0
      t22 = my_4*t22
      t23 = my_0
      t33 = my_4*t33

    elseif(symmetry == 101 .and. ((abs(dx) > symmetry_tol)      &
                           .and.  (abs(dz) > symmetry_tol))) then

      t11 = my_4*t11
      t12 = my_0
      t13 = my_0
      t22 = my_4*t22
      t23 = my_0
      t33 = my_4*t33


    elseif(symmetry == 011 .and. ((abs(dy) > symmetry_tol)      &
                           .and.  (abs(dz) > symmetry_tol))) then

      t11 = my_4*t11
      t12 = my_0
      t13 = my_0
      t22 = my_4*t22
      t23 = my_0
      t33 = my_4*t33

    elseif( ( (symmetry == 110) .or. (symmetry == 101) ) &
      .and. (abs(dx) > symmetry_tol) ) then

      t11 = my_2*t11
      t12 = my_0
      t13 = my_0
      t22 = my_2*t22
      t23 = my_2*t23
      t33 = my_2*t33

    elseif( ( (symmetry == 110) .or. (symmetry == 011) ) &
      .and. (abs(dy) > symmetry_tol) ) then

      t11 = my_2*t11
      t12 = my_0
      t13 = my_2*t13
      t22 = my_2*t22
      t23 = my_0
      t33 = my_2*t33

    elseif( ( (symmetry == 101) .or. (symmetry == 011) ) &
      .and. (abs(dz) > symmetry_tol) ) then

      t11 = my_2*t11
      t12 = my_2*t12
      t13 = my_0
      t22 = my_2*t22
      t23 = my_0
      t33 = my_2*t33

    endif

  end subroutine sumgs_sym

!================================== SUMGS_PERIODIC ===========================80
!
! Gets the weights for calculating gradients using least squares
! Periodic version
!
!=============================================================================80
  subroutine sumgs_periodic(nnodes0,nnodes01,nedgeloc,eptr,x,y,z,r11,r12,r13,  &
                            r22,r23,r33,weighted)

    use periodics,   only : nperiodic, periodic_data
    use lmpi_app,    only : lmpi_xfer

    integer, intent(in) :: nnodes0, nnodes01, nedgeloc

    integer, dimension(2,nedgeloc), intent(in) :: eptr

    real(dp), dimension(nnodes01),    intent(in)  :: x, y, z
    real(dp), dimension(nnodes0),     intent(out) :: r11,r12,r13
    real(dp), dimension(nnodes0),     intent(out) :: r22,r23,r33

    logical, intent(in), optional :: weighted

    integer :: i, n, node1, node2, node, j

    integer, dimension(nnodes01) :: periodic_tag

    real(dp) :: dx, dy, dz, t11, t12, t13, t22, t23, t33
    real(dp) :: weight, w2

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

  continue

! Set up a tag for the periodic nodes

    periodic_tag = 0

    do i = 1, nperiodic
      node = periodic_data(i)%list(1)
      periodic_tag(node) =  1
      do j = 2, periodic_data(i)%n
        node = periodic_data(i)%list(j)
        periodic_tag(node) =  -1
      end do
    end do

    call lmpi_xfer(periodic_tag)

!   Set distance weighting factor

    weight_factor = my_0
    if (present( weighted )) then
      if(weighted) weight_factor = my_1
    endif

!   Initialize all the rij to 0.0

    do i = 1,nnodes0
      r11(i) = my_0
      r12(i) = my_0
      r13(i) = my_0
      r22(i) = my_0
      r23(i) = my_0
      r33(i) = my_0
    end do

!   Now loop over the edges and accumulate the r's

    scan_edges : do n = 1, nedgeloc

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      dx = x(node2) - x(node1)
      dy = y(node2) - y(node1)
      dz = z(node2) - z(node1)

      weight = reconstruct_weight(dx, dy, dz, weight_factor, wls_inv_dist_scale)
      w2 = weight*weight

! Only allow contributions to secondary plane nodes if they arise
! from connections to interior points

      if(node1 <= nnodes0) then
        if (.not.(periodic_tag(node1) < 0 .and. periodic_tag(node2) /= 0)) then
          t11 =  dx*dx*w2
          t12 =  dx*dy*w2
          t13 =  dx*dz*w2
          t22 =  dy*dy*w2
          t23 =  dy*dz*w2
          t33 =  dz*dz*w2

          r11(node1) = r11(node1) + t11
          r12(node1) = r12(node1) + t12
          r13(node1) = r13(node1) + t13
          r22(node1) = r22(node1) + t22
          r23(node1) = r23(node1) + t23
          r33(node1) = r33(node1) + t33
        endif
      endif

      dx = -dx
      dy = -dy
      dz = -dz

      if(node2 <= nnodes0) then
        if (.not.(periodic_tag(node2) < 0 .and. periodic_tag(node1) /= 0)) then
          t11 =  dx*dx*w2
          t12 =  dx*dy*w2
          t13 =  dx*dz*w2
          t22 =  dy*dy*w2
          t23 =  dy*dz*w2
          t33 =  dz*dz*w2

          r11(node2) = r11(node2) + t11
          r12(node2) = r12(node2) + t12
          r13(node2) = r13(node2) + t13
          r22(node2) = r22(node2) + t22
          r23(node2) = r23(node2) + t23
          r33(node2) = r33(node2) + t33
        endif
      endif

    enddo scan_edges

! Now we have to gather the results on the periodic planes

    do i = 1, nperiodic

      t11 = 0.0_dp
      t12 = 0.0_dp
      t13 = 0.0_dp
      t22 = 0.0_dp
      t23 = 0.0_dp
      t33 = 0.0_dp

      do j = 1, periodic_data(i)%n
        node = periodic_data(i)%list(j)
        t11 = t11 + r11(node)
        t12 = t12 + r12(node)
        t13 = t13 + r13(node)
        t22 = t22 + r22(node)
        t23 = t23 + r23(node)
        t33 = t33 + r33(node)
      end do

      do j = 1, periodic_data(i)%n
        node = periodic_data(i)%list(j)
        r11(node) = t11
        r12(node) = t12
        r13(node) = t13
        r22(node) = t22
        r23(node) = t23
        r33(node) = t33
      end do

    end do

    do i = 1,nnodes0
      r11(i) = sqrt(r11(i))
      r12(i) = r12(i)/r11(i)
      r13(i) = r13(i)/r11(i)
      r22(i) = sqrt( r22(i) - r12(i)*r12(i) )
      r23(i) = (r23(i) - r12(i) * r13(i)) / r22(i)
      r33(i) = sqrt( r33(i) - r13(i)*r13(i) - r23(i)*r23(i) )
    end do

  end subroutine sumgs_periodic

!================================== SUMGS_NOBOUND ============================80
!
! Gets the weights for calculating gradients using least squares
!
!=============================================================================80
  subroutine sumgs_nobound(nnodes0,nnodes01,nedgeloc,eptr,x,y,z,               &
                           r11,r12,r13,r22,r23,r33,bctag)

    integer,                         intent(in)  :: nnodes0, nnodes01
    integer,                         intent(in)  :: nedgeloc
    integer,  dimension(2,nedgeloc), intent(in)  :: eptr
    real(dp), dimension(nnodes01),   intent(in)  :: x, y, z
    real(dp), dimension(nnodes0),    intent(out) :: r11, r12, r13
    real(dp), dimension(nnodes0),    intent(out) :: r22, r23, r33
    integer,  dimension(nnodes01),   intent(in)  :: bctag

    integer :: i, n, node1, node2

    real(dp) :: dx, dy, dz, weight, w2

    real(dp), parameter :: my_0 = 0.0_dp

    real(dp), parameter :: zero_tol = 1.0e-32_dp

  continue

! Set distance weighting factor (assume unweighted)

   !weight_factor = my_0

! Initialize all the rij to 0.0

    do i = 1,nnodes0
      r11(i) = my_0
      r12(i) = my_0
      r13(i) = my_0
      r22(i) = my_0
      r23(i) = my_0
      r33(i) = my_0
    end do

! Now loop over the edges and accumulate the r's

    scan_edges_1 : do n = 1, nedgeloc

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      if ( bctag(node1) == 1 ) cycle scan_edges_1
      if ( bctag(node2) == 1 ) cycle scan_edges_1

      dx = x(node2) - x(node1)
      dy = y(node2) - y(node1)
      dz = z(node2) - z(node1)

      w2 = 1._dp

      if(node1 <= nnodes0) then
        r11(node1) = r11(node1) + dx*dx*w2
        r12(node1) = r12(node1) + dx*dy*w2
        r13(node1) = r13(node1) + dx*dz*w2
      endif

      dx = -dx
      dy = -dy
      dz = -dz

      if(node2 <= nnodes0) then
        r11(node2) = r11(node2) + dx*dx*w2
        r12(node2) = r12(node2) + dx*dy*w2
        r13(node2) = r13(node2) + dx*dz*w2
      endif

    end do scan_edges_1

! Now calculate ||x|| = r11 by taking the square root
! Also divide r12 and r13 by ||x||

    do i = 1,nnodes0
      if ( bctag(i) == 0 .and. abs(r11(i))>zero_tol ) then
        r11(i) = sqrt(r11(i))
        r12(i) = r12(i)/r11(i)
        r13(i) = r13(i)/r11(i)
      end if
    end do

! Now calculate r22 and r23

    scan_edges_2 : do n = 1, nedgeloc

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      if ( bctag(node1) == 1 ) cycle scan_edges_2
      if ( bctag(node2) == 1 ) cycle scan_edges_2

      dx = x(node2) - x(node1)
      dy = y(node2) - y(node1)
      dz = z(node2) - z(node1)

      weight = 1._dp

      dx = weight*dx
      dy = weight*dy
      dz = weight*dz

      if( node1 <= nnodes0 ) then
        if ( abs(r11(node1)) > zero_tol ) then
          r22(node1) = r22(node1) +    (dy - dx*r12(node1)/r11(node1))**2
          r23(node1) = r23(node1) + dz*(dy - dx*r12(node1)/r11(node1))
        end if
      end if

      dx = -dx
      dy = -dy
      dz = -dz

      if( node2 <= nnodes0 ) then
        if ( abs(r11(node2)) > zero_tol ) then
          r22(node2) = r22(node2) +    (dy - dx*r12(node2)/r11(node2))**2
          r23(node2) = r23(node2) + dz*(dy - dx*r12(node2)/r11(node2))
        end if
      end if

    end do scan_edges_2

! Now finish getting r22 and r23

    do i = 1,nnodes0
      if ( bctag(i) == 0 .and. abs(r22(i))>zero_tol ) then
        r22(i) = sqrt(r22(i))
        r23(i) = r23(i)/r22(i)
      end if
    end do

! Now all we have to do is get r33

    scan_edges_3 : do n = 1, nedgeloc

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      if ( bctag(node1) == 1 ) cycle scan_edges_3
      if ( bctag(node2) == 1 ) cycle scan_edges_3

      dx = x(node2) - x(node1)
      dy = y(node2) - y(node1)
      dz = z(node2) - z(node1)

      weight = 1._dp

      dx = weight*dx
      dy = weight*dy
      dz = weight*dz

      if( node1 <= nnodes0 ) then
        if ( abs(r11(node1))>zero_tol .and. abs(r22(node1))>zero_tol ) then
          r33(node1) = r33(node1) +          (dz - dx*r13(node1)/r11(node1)    &
                     - r23(node1)/r22(node1)*(dy - dx*r12(node1)/r11(node1)))**2
        end if
      end if

      dx = -dx
      dy = -dy
      dz = -dz

      if( node2 <= nnodes0 ) then
        if ( abs(r11(node2))>zero_tol .and. abs(r22(node2))>zero_tol ) then
          r33(node2) = r33(node2) +          (dz - dx*r13(node2)/r11(node2)    &
                     - r23(node2)/r22(node2)*(dy - dx*r12(node2)/r11(node2)))**2
        end if
      end if

    end do scan_edges_3

! Now just get the magnitude of r33

    do i = 1,nnodes0
      if ( bctag(i) == 0 ) then
        r33(i) = sqrt(r33(i))
      endif
    end do

  end subroutine sumgs_nobound

!================================== SUMGS_2D =================================80
!
! Gets the weights for calculating gradients using least squares in 2D
!
!=============================================================================80
  subroutine sumgs_2d(nnodes0,nnodes01,nedgeloc,eptr,symmetry,x,z,             &
                      r11,r12,r22,nedgeloc_2d,weighted)

    use debug_defs, only : symmetry_bcs

    integer,                         intent(in)  :: nnodes0, nnodes01
    integer,                         intent(in)  :: nedgeloc
    integer,                         intent(in)  :: nedgeloc_2d
    integer,  dimension(2,nedgeloc), intent(in)  :: eptr
    integer,  dimension(nnodes01),   intent(in)  :: symmetry

    real(dp), dimension(nnodes01),   intent(in)  :: x, z
    real(dp), dimension(nnodes0),    intent(out) :: r11,r12,r22

    logical, optional,               intent(in)  :: weighted

    integer  :: i, n
    integer  :: node1, node2

    real(dp) :: dx, dz
    real(dp) :: t11, t12, t22
    real(dp) :: weight, w2

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

  continue

! Set distance weighting factor

    weight_factor = my_0
    if (present( weighted )) then
      if(weighted) weight_factor = my_1
    endif

! Initialize all the rij to 0.0

    do i = 1,nnodes0
      r11(i) = my_0
      r12(i) = my_0
      r22(i) = my_0
    end do

! Now loop over the edges and accumulate the r's

    if ( .not.symmetry_bcs ) then

      do n = 1, nedgeloc_2d

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        dx = x(node2) - x(node1)
        dz = z(node2) - z(node1)

        weight = reconstruct_weight(dx, my_0, dz,                              &
                                    weight_factor, wls_inv_dist_scale)
        w2 = weight*weight

        if(node1 <= nnodes0) then
          r11(node1) = r11(node1) + dx*dx*w2
          r12(node1) = r12(node1) + dx*dz*w2
        endif

        dx = -dx
        dz = -dz

        if(node2 <= nnodes0) then
          r11(node2) = r11(node2) + dx*dx*w2
          r12(node2) = r12(node2) + dx*dz*w2
        endif

      end do

! Now calculate ||x|| = r11 by taking the square root
! Also divide r12 by ||x||

      do i = 1,nnodes0

        if (r11(i) > 0._dp) then
          r11(i) = sqrt(r11(i))
          r12(i) = r12(i)/r11(i)
        end if

      end do

! Now calculate r22

      do n = 1, nedgeloc_2d

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        dx = x(node2) - x(node1)
        dz = z(node2) - z(node1)

        weight = reconstruct_weight(dx, my_0, dz,                              &
                                    weight_factor, wls_inv_dist_scale)
        dx = weight*dx
        dz = weight*dz

        if(node1 <= nnodes0) then
          r22(node1) = r22(node1) + (dz - dx*r12(node1)/r11(node1))**2
        endif

        dx = -dx
        dz = -dz

        if(node2 <= nnodes0) then
          r22(node2) = r22(node2) + (dz - dx*r12(node2)/r11(node2))**2
        endif

      end do

! Now finish getting r22

      do i = 1,nnodes0
        r22(i) = sqrt(r22(i))
      end do

    else

!   Now loop over the edges and accumulate the r's

      scan_edges_1 : do n = 1, nedgeloc_2d

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        dx = x(node2) - x(node1)
        dz = z(node2) - z(node1)

        if(node1 <= nnodes0) then
          call sumgs_2d_1(symmetry(node1), dx,  dz, t11, t12 )
          r11(node1) = r11(node1) + t11
          r12(node1) = r12(node1) + t12
        endif

        dx = -dx
        dz = -dz

        if(node2 <= nnodes0) then
          call sumgs_2d_1(symmetry(node2), dx,  dz, t11, t12 )
          r11(node2) = r11(node2) + t11
          r12(node2) = r12(node2) + t12
        endif

      end do scan_edges_1

!   Now calculate ||x|| = r11 by taking the square root
!   Also divide r12 and r13 by ||x||
!   Store 1/||x|| in r11 (reset points not in first plane)

      do i = 1,nnodes0
        if(r11(i) > my_0) then
          r11(i) = my_1/sqrt(r11(i))
          r12(i) = r12(i)*r11(i)
        else
          r11(i) = my_1
          r12(i) = my_0
        endif
      end do

!   Now calculate r22

      scan_edges_2 : do n = 1, nedgeloc_2d

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        dx = x(node2) - x(node1)
        dz = z(node2) - z(node1)

        if(node1 <= nnodes0) then
          t11  = r11(node1)
          t12  = r12(node1)
          call sumgs_2d_2(symmetry(node1), dx,  dz, t11, t12, t22 )
          r22(node1) = r22(node1) + t22
        endif

        dx = -dx
        dz = -dz

        if(node2 <= nnodes0) then
          t11  = r11(node2)
          t12  = r12(node2)
          call sumgs_2d_2(symmetry(node2), dx,  dz, t11, t12, t22 )
          r22(node2) = r22(node2) + t22
        endif

      end do scan_edges_2

!   Now finish getting r22
!   Store 1/||x|| in r22 (reset points not in first plane)

      do i = 1,nnodes0
        if(r22(i) > my_0) then
          r22(i) = my_1/sqrt(r22(i))
        else
          r22(i) = my_1
        endif

      end do

! reinvert these because it was very confusing --emlr

      do i = 1,nnodes0
        r11(i) = my_1/r11(i)
        r22(i) = my_1/r22(i)
      end do

    endif

  end subroutine sumgs_2d

!=============================== SUMGS_2D_1  =================================80
!
! Returns the contributions to r11 and r12 using symmetry.
! Note: Every point to be treated has at least y-symmetry.
!
!=============================================================================80
  subroutine sumgs_2d_1( symmetry, dx,  dz, r11, r12 )

    integer,  intent(in)  :: symmetry
    real(dp), intent(in)  :: dx, dz
    real(dp), intent(out) :: r11, r12

    real(dp) :: weight, w2, xx, xz

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_2 = 2.0_dp
    real(dp), parameter :: my_4 = 4.0_dp

  continue

    weight = reconstruct_weight(dx, my_0, dz, weight_factor, wls_inv_dist_scale)
    w2 = weight*weight

    xx = dx*dx*w2
    xz = dx*dz*w2

    r11 = xx
    r12 = xz

    if(symmetry == 110 .and. (abs(dx) > symmetry_tol)) then

!     r11 = r11 + xx
!     r12 = r12 - xz

      r11 = my_2*xx
      r12 = my_0

    elseif(symmetry == 011 .and. (abs(dz) > symmetry_tol)) then

!     r11 = r11 + xx
!     r12 = r12 - xz

      r11 = my_2*xx
      r12 = my_0

    elseif(symmetry == 111 .and. ((abs(dx) > symmetry_tol)      &
                           .and.  (abs(dz) > symmetry_tol))) then

!     r11 = r11 + xx
!     r12 = r12 - xz

!     r11 = r11 + xx
!     r12 = r12 - xz

!     r11 = r11 + xx
!     r12 = r12 + xz

      r11 = my_4*xx
      r12 = my_0

    elseif(symmetry == 111) then

      r11 = my_2*xx
      r12 = my_0

    endif

  end subroutine sumgs_2d_1


!=============================== SUMGS_2D_2 ==================================80
!
! Returns the contributions to r22 using symmetry.
! Note: Every point to be treated has at least y-symmetry.
!
!=============================================================================80
  subroutine sumgs_2d_2(symmetry, dx,  dz, r11, r12, r22 )

    integer,  intent(in)  :: symmetry
    real(dp), intent(in)  :: dx, dz
    real(dp), intent(in)  :: r11, r12
    real(dp), intent(out) :: r22

    real(dp) :: weight, w2, zz, xr1

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_2 = 2.0_dp
    real(dp), parameter :: my_4 = 4.0_dp

  continue

    weight = reconstruct_weight(dx, my_0, dz, weight_factor, wls_inv_dist_scale)
    w2 = weight*weight

    zz = dz*dz*w2
    xr1 = dx*weight*r12*r11

    r22 = ( dz*weight - xr1 )**2

    if(symmetry == 110 .and. (abs(dx) > symmetry_tol)) then

!     r22 = r22 + ( dz + xr1 )**2

      r22 = my_2*( zz + xr1*xr1 )

    elseif(symmetry == 011 .and. (abs(dz) > symmetry_tol)) then

!     r22 = r22 + (-dz - xr1 )**2

      r22 = my_2*( zz + xr1*xr1 )

    elseif(symmetry == 111 .and. ((abs(dx) > symmetry_tol)      &
                           .and.  (abs(dz) > symmetry_tol))) then

!     r22 = r22 + ( dz + xr1 )**2

!     r22 = r22 + (-dz - xr1 )**2

!     r22 = r22 + (-dz + xr1 )**2

      r22 = my_4*( zz + xr1*xr1 )

     elseif(symmetry == 111) then

      r22 = my_2*( zz + xr1*xr1 )

    endif

  end subroutine sumgs_2d_2

!==================================== LSTGS ==================================80
!
! Calculates the gradients at the nodes using weighted least squares
!
! This subroutine solves using Gram-Schmidt
!
! Note: for compressible, edge-based NS:
!          n_grd = n_tot + n_turb
!       otherwise:
!          n_grd = n_tot
!
!=============================================================================80
  subroutine lstgs(turb_grad_flag,                                             &
                   nnodes0,nnodes01,nedgeloc,eptr,symmetry,qnode,              &
                   gradx,grady,gradz,x,y,z,                                    &
                   r11,r12,r13,r22,r23,r33,                                    &
                   n_tot,n_grd,turb,n_turb,eqn_set,ndim,weighted )

    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations
    use solution_types,       only : generic_gas

    integer, intent(in) :: turb_grad_flag, nnodes0, nnodes01, n_tot, n_grd
    integer, intent(in) :: n_turb, nedgeloc
    integer, intent(in) :: eqn_set, ndim

    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    integer,  dimension(nnodes01),        intent(in)  :: symmetry
    real(dp), dimension(n_tot,nnodes01),  intent(in)  :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)  :: turb
    real(dp), dimension(nnodes01),        intent(in)  :: x,y,z
    real(dp), dimension(nnodes0),         intent(in)  :: r11,r12,r13
    real(dp), dimension(nnodes0),         intent(in)  :: r22,r23,r33
    real(dp), dimension(n_grd,nnodes01),  intent(out) :: gradx,grady,gradz
    logical,  optional,                   intent(in)  :: weighted

    integer :: j, n, node1, node2, nedge_eval, my_ntt
    integer :: n_vec, nqq

    real(dp)                   :: dx, dy, dz, weight
    real(dp), dimension(3)     :: terms
    real(dp), dimension(n_grd) :: dqq, contx, conty, contz

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

  continue

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

    if ( eqn_set == generic_gas ) then
      n_vec = ndim
      nqq   = ndim
    else
      n_vec = n_tot
      nqq   = n_grd
    end if

!   Set distance weighting factor

    weight_factor = my_0
    if (present( weighted )) then
      if(weighted) weight_factor = my_1
    endif

!   Zero out the gradients

    gradx(:,:) = my_0
    grady(:,:) = my_0
    gradz(:,:) = my_0

!   Initialize dqq

    dqq(:) = my_0

    nedge_eval = nedgeloc + nedge_ns

!   If second order, loop over all the faces accumulate sums

    check_second_order : if(my_ntt > first_order_iterations) then

      scan_edges : do n = 1, nedge_eval

        if ( n <= nedgeloc ) then
          node1 = eptr(1,n)
          node2 = eptr(2,n)
        else
          node1 = eptr_ns(1,n-nedgeloc)
          node2 = eptr_ns(2,n-nedgeloc)
        endif

        dx = x(node2) - x(node1)
        dy = y(node2) - y(node1)
        dz = z(node2) - z(node1)

        weight = reconstruct_weight(dx,dy,dz, weight_factor, wls_inv_dist_scale)

        dx = weight*dx
        dy = weight*dy
        dz = weight*dz

        dqq(1:n_vec) = weight*(qnode(1:n_vec,node2) - qnode(1:n_vec,node1))

!       turb gradients if needed

        if ( turb_grad_flag > 0 ) then

              do j=1,n_turb
                dqq(n_tot+j) = weight*( turb(j,node2) - turb(j,node1) )
              end do

        end if

        if (node1 <= nnodes0) then

          terms(:) = lstgs_func(dx,         dy,         dz,                    &
                                r11(node1), r12(node1), r13(node1),            &
                                r22(node1), r23(node1), r33(node1))
          contx(:) = terms(1)
          conty(:) = terms(2)
          contz(:) = terms(3)

          call lstgs_sym(symmetry(node1),                                      &
                         dx,         dy,         dz,                           &
                         r11(node1), r12(node1), r13(node1),                   &
                         r22(node1), r23(node1), r33(node1),                   &
                         nqq, contx(1:nqq), conty(1:nqq), contz(1:nqq) )

          gradx(1:nqq,node1) = gradx(1:nqq,node1) + contx(1:nqq)*dqq(1:nqq)
          grady(1:nqq,node1) = grady(1:nqq,node1) + conty(1:nqq)*dqq(1:nqq)
          gradz(1:nqq,node1) = gradz(1:nqq,node1) + contz(1:nqq)*dqq(1:nqq)

        end if

!       Now do the other node

        dqq(1:nqq) = -dqq(1:nqq)
        dx = -dx
        dy = -dy
        dz = -dz

        if (node2 <= nnodes0) then

          terms(:) = lstgs_func(dx,         dy,         dz,                    &
                                r11(node2), r12(node2), r13(node2),            &
                                r22(node2), r23(node2), r33(node2))
          contx(:) = terms(1)
          conty(:) = terms(2)
          contz(:) = terms(3)

          call lstgs_sym(symmetry(node2),                                      &
                         dx,         dy,         dz,                           &
                         r11(node2), r12(node2), r13(node2),                   &
                         r22(node2), r23(node2), r33(node2),                   &
                         nqq, contx(1:nqq), conty(1:nqq), contz(1:nqq) )

          gradx(1:nqq,node2) = gradx(1:nqq,node2) + contx(1:nqq)*dqq(1:nqq)
          grady(1:nqq,node2) = grady(1:nqq,node2) + conty(1:nqq)*dqq(1:nqq)
          gradz(1:nqq,node2) = gradz(1:nqq,node2) + contz(1:nqq)*dqq(1:nqq)

        end if

      end do scan_edges

    end if check_second_order

  end subroutine lstgs

!=============================== LSTGS_SYM ===================================80
!
! Returns some contributions to gradx, grady, gradz using symmetry
! For twod, set dy = r12 = r13 = zero, and r22 = 1 to prevent division by zero
!
!=============================================================================80
  subroutine lstgs_sym(symmetry,  dx,  dy,  dz,                  &
                                  r11, r12, r13,                 &
                                  r22, r23, r33,                 &
                                  dim_grad, gradx, grady, gradz )

    use info_depr, only : twod

    integer,  intent(in)  :: symmetry
    integer,  intent(in)  :: dim_grad

    real(dp), intent(in)  :: dx, dy, dz
    real(dp), intent(in)  :: r11, r12, r13
    real(dp), intent(in)  :: r22, r23, r33

    real(dp), dimension(dim_grad), intent(inout) :: gradx
    real(dp), dimension(dim_grad), intent(inout) :: grady
    real(dp), dimension(dim_grad), intent(inout) :: gradz

    real(dp)                    :: w11, w22, w33
    real(dp)                    :: r11inv, r22inv
    real(dp)                    :: r12r11, r13r11, r23r22, rmess
    real(dp)                    :: termx, termy, termz
    real(dp)                    :: xt1, xt2, xt3, t1, t2
    real(dp)                    :: coef1, coef2

    integer,  parameter         :: terms = 4
    real(dp), dimension(terms)  :: xf, yf, zf
    real(dp), dimension(terms)  :: cx, cy, cz

    integer                     :: eqn

    real(dp), parameter         :: my_0 = 0.0_dp
    real(dp), parameter         :: my_1 = 1.0_dp

  continue

    !...symmetric and 3 antisymmetric variations

    cx(:) = my_0
    cy(:) = my_0
    cz(:) = my_0

    xf = my_1
    yf = my_1
    zf = my_1

    xf(2) =-my_1
    yf(3) =-my_1
    zf(4) =-my_1

    if(twod) yf = my_0

    r11inv = my_1/r11
    r22inv = my_1/r22

    w11  = r11inv*r11inv
    w22  = r22inv*r22inv
    w33  = my_1/(r33*r33)
    r12r11 = r12*r11inv
    r13r11 = r13*r11inv
    r23r22 = r23*r22inv
    rmess  = (r12r11*r23r22 - r13r11)*w33

    xt1 = dx*r12r11
    xt2 = dx*r13r11
    xt3 = dx*w11

    t1  = w22*r12r11
    t2  = w33*r23r22

    if(symmetry == 100 .and. (abs(dx) > symmetry_tol)) then

      coef1 =  dy + xt1
      coef2 =  dz + xt2 - r23r22*coef1
      termx = -xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*xf(:)
      cy(:) = cy(:) + termy*xf(:)
      cz(:) = cz(:) + termz*xf(:)

    elseif(symmetry == 010 .and. (abs(dy) > symmetry_tol)) then

      coef1 = -dy - xt1
      coef2 =  dz - xt2 - r23r22*coef1
      termx =  xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*yf(:)
      cy(:) = cy(:) + termy*yf(:)
      cz(:) = cz(:) + termz*yf(:)

    elseif(symmetry == 001 .and. (abs(dz) > symmetry_tol)) then

      coef1 =  dy - xt1
      coef2 = -dz - xt2 - r23r22*coef1
      termx =  xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*zf(:)
      cy(:) = cy(:) + termy*zf(:)
      cz(:) = cz(:) + termz*zf(:)

    elseif(symmetry == 110 .and. ((abs(dx) > symmetry_tol)      &
                           .and.  (abs(dy) > symmetry_tol))) then

      coef1 =  dy + xt1
      coef2 =  dz + xt2 - r23r22*coef1
      termx = -xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*xf(:)
      cy(:) = cy(:) + termy*xf(:)
      cz(:) = cz(:) + termz*xf(:)

      coef1 = -dy - xt1
      coef2 =  dz - xt2 - r23r22*coef1
      termx =  xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*yf(:)
      cy(:) = cy(:) + termy*yf(:)
      cz(:) = cz(:) + termz*yf(:)

      coef1 = -dy + xt1
      coef2 =  dz + xt2 - r23r22*coef1
      termx = -xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*xf(:)*yf(:)
      cy(:) = cy(:) + termy*xf(:)*yf(:)
      cz(:) = cz(:) + termz*xf(:)*yf(:)

    elseif(symmetry == 101 .and. ((abs(dx) > symmetry_tol)      &
                           .and.  (abs(dz) > symmetry_tol))) then

      coef1 =  dy + xt1
      coef2 =  dz + xt2 - r23r22*coef1
      termx = -xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*xf(:)
      cy(:) = cy(:) + termy*xf(:)
      cz(:) = cz(:) + termz*xf(:)

      coef1 =  dy - xt1
      coef2 = -dz - xt2 - r23r22*coef1
      termx =  xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*zf(:)
      cy(:) = cy(:) + termy*zf(:)
      cz(:) = cz(:) + termz*zf(:)

      coef1 =  dy + xt1
      coef2 = -dz + xt2 - r23r22*coef1
      termx = -xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*xf(:)*zf(:)
      cy(:) = cy(:) + termy*xf(:)*zf(:)
      cz(:) = cz(:) + termz*xf(:)*zf(:)

    elseif(symmetry == 011 .and. ((abs(dy) > symmetry_tol)      &
                           .and.  (abs(dz) > symmetry_tol))) then

      coef1 = -dy - xt1
      coef2 =  dz - xt2 - r23r22*coef1
      termx =  xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*yf(:)
      cy(:) = cy(:) + termy*yf(:)
      cz(:) = cz(:) + termz*yf(:)

      coef1 =  dy - xt1
      coef2 = -dz - xt2 - r23r22*coef1
      termx =  xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*zf(:)
      cy(:) = cy(:) + termy*zf(:)
      cz(:) = cz(:) + termz*zf(:)

      coef1 = -dy - xt1
      coef2 = -dz - xt2 - r23r22*coef1
      termx =  xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*yf(:)*zf(:)
      cy(:) = cy(:) + termy*yf(:)*zf(:)
      cz(:) = cz(:) + termz*yf(:)*zf(:)

    elseif( ( (symmetry == 110) .or. (symmetry == 101) ) &
      .and. (abs(dx) > symmetry_tol) ) then

      coef1 =  dy + xt1
      coef2 =  dz + xt2 - r23r22*coef1
      termx = -xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*xf(:)
      cy(:) = cy(:) + termy*xf(:)
      cz(:) = cz(:) + termz*xf(:)

    elseif( ( (symmetry == 110) .or. (symmetry == 011) ) &
      .and. (abs(dy) > symmetry_tol) ) then

      coef1 = -dy - xt1
      coef2 =  dz - xt2 - r23r22*coef1
      termx =  xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*yf(:)
      cy(:) = cy(:) + termy*yf(:)
      cz(:) = cz(:) + termz*yf(:)

    elseif( ( (symmetry == 101) .or. (symmetry == 011) ) &
      .and. (abs(dz) > symmetry_tol) ) then

      coef1 =  dy - xt1
      coef2 = -dz - xt2 - r23r22*coef1
      termx =  xt3 - t1*coef1 + rmess*coef2
      termy =  w22*coef1 - t2*coef2
      termz =  w33*coef2

      cx(:) = cx(:) + termx*zf(:)
      cy(:) = cy(:) + termy*zf(:)
      cz(:) = cz(:) + termz*zf(:)

! symmetry == 111 came from 2D routine
    elseif(symmetry == 111 .and. ((abs(dx) > symmetry_tol)      &
          .and.  (abs(dz) > symmetry_tol))) then

      coef1 =  dz + xt2       !reflect ghost point delta-x
      termx = -xt3 + rmess*coef1
      termz =  w33*coef1

      cx(:) = cx(:) + termx*xf(:)
      cz(:) = cz(:) + termz*xf(:)

      coef1 = -dz - xt2       !reflect ghost point delta-z
      termx =  xt3 + rmess*coef1
      termz =  w33*coef1

      cx(:) = cx(:) + termx*zf(:)
      cz(:) = cz(:) + termz*zf(:)

      coef1 = -dz + xt2       !reflect ghost point delta-x/z
      termx = -xt3 + rmess*coef1
      termz =  w33*coef1

      cx(:) = cx(:) + termx*xf(:)*zf(:)
      cz(:) = cz(:) + termz*xf(:)*zf(:)

    elseif(symmetry == 111 .and. (abs(dx) > symmetry_tol)) then

      coef1 =  dz + xt2
      termx = -xt3 + rmess*coef1
      termz =  w33*coef1

      cx(:) = cx(:) + termx*xf(:)
      cz(:) = cz(:) + termz*xf(:)

    elseif(symmetry == 111 .and. (abs(dz) > symmetry_tol)) then

      coef1 = -dz - xt2
      termx =  xt3 + rmess*coef1
      termz =  w33*coef1

      cx(:) = cx(:) + termx*zf(:)
      cz(:) = cz(:) + termz*zf(:)

    endif

!   Split contribution according to:
!   Antisymmetric components for n_momx:n_momz
!   Symmetric components for everyone else

    do eqn = 1, dim_grad
      antisymmetric_momentum_terms : if ( eqn == n_momx .or. &
                                          eqn == n_momy .or. &
                                          eqn == n_momz ) then
        gradx(eqn) = gradx(eqn) + cx(2+(eqn-n_momx))
        grady(eqn) = grady(eqn) + cy(2+(eqn-n_momx))
        gradz(eqn) = gradz(eqn) + cz(2+(eqn-n_momx))
      else
        gradx(eqn) =  gradx(eqn) + cx(1)
        grady(eqn) =  grady(eqn) + cy(1)
        gradz(eqn) =  gradz(eqn) + cz(1)
      end if antisymmetric_momentum_terms
    end do

  end subroutine lstgs_sym

!==================================== LSTGS_PERIODIC =========================80
!
! Calculates the gradients at the nodes using weighted least squares
!
! This subroutine solves using Gram-Schmidt
!
! Note: for compressible, edge-based NS:
!          n_grd = n_tot + n_turb
!       otherwise:
!          n_grd = n_tot
!
!  Periodic version
!
!=============================================================================80
  subroutine lstgs_periodic(nnodes0,nnodes01,nedgeloc,eptr,qnode,gradx,grady,  &
                            gradz,x,y,z,r11,r12,r13,r22,r23,r33,n_tot,n_grd,   &
                            eqn_set,ndim,weighted)

    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations
    use periodics,            only : nperiodic, periodic_data
    use lmpi_app,             only : lmpi_xfer
    use solution_types,       only : generic_gas

    integer, intent(in) :: nnodes0, nnodes01, n_tot, n_grd, nedgeloc
    integer, intent(in) :: eqn_set, ndim

    integer, dimension(2,nedgeloc),    intent(in) :: eptr

    real(dp), dimension(n_tot,nnodes01),     intent(in)  :: qnode
    real(dp), dimension(nnodes01),           intent(in)  :: x,y,z
    real(dp), dimension(nnodes0),            intent(in)  :: r11,r12,r13
    real(dp), dimension(nnodes0),            intent(in)  :: r22,r23,r33
    real(dp), dimension(n_grd,nnodes01),     intent(out) :: gradx,grady,gradz

    logical, intent(in), optional :: weighted

    integer :: i,n,node1,node2,my_ntt
    integer :: n_vec, nqq, node, j

    integer, dimension(nnodes01) :: periodic_tag

    real(dp) :: dx, dy, dz, weight

    real(dp), dimension(n_grd) :: dqq, gx, gy, gz
    real(dp), dimension(3)     :: term

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

  continue

    if ( eqn_set == generic_gas ) then
      n_vec = ndim
      nqq   = ndim
    else
      n_vec = n_tot
      nqq   = n_grd
    end if

! Set up a tag for the periodic nodes

    periodic_tag = 0

    do i = 1, nperiodic
      node = periodic_data(i)%list(1)
      periodic_tag(node) =  1
      do j = 2, periodic_data(i)%n
        node = periodic_data(i)%list(j)
        periodic_tag(node) =  -1
      end do
    end do

    call lmpi_xfer(periodic_tag)

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

!   Set distance weighting factor

    weight_factor = my_0
    if (present( weighted )) then
      if(weighted) weight_factor = my_1
    endif

!   Zero out the gradients

    gradx(:,:) = my_0
    grady(:,:) = my_0
    gradz(:,:) = my_0

!   Initialize dqq

    dqq(:) = my_0

!   If second order, loop over all the faces accumulate sums

    check_second_order : if (my_ntt > first_order_iterations) then

      scan_edges : do n = 1, nedgeloc

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        dx = x(node2) - x(node1)
        dy = y(node2) - y(node1)
        dz = z(node2) - z(node1)

        weight = reconstruct_weight(dx,dy,dz, weight_factor, wls_inv_dist_scale)

        dx = weight*dx
        dy = weight*dy
        dz = weight*dz

        dqq(1:n_vec) = weight*(qnode(1:n_vec,node2) - qnode(1:n_vec,node1))

! Only allow contributions to secondary plane nodes if they arise
! from connections to interior points

        n1_loc : if (node1 <= nnodes0) then
          if (.not.(periodic_tag(node1) < 0 .and. periodic_tag(node2) /= 0))then

            term(:) = lstgs_func(dx,         dy,         dz,                   &
                                 r11(node1), r12(node1), r13(node1),           &
                                 r22(node1), r23(node1), r33(node1))

            gradx(1:nqq,node1) = gradx(1:nqq,node1) + term(1)*dqq(1:nqq)
            grady(1:nqq,node1) = grady(1:nqq,node1) + term(2)*dqq(1:nqq)
            gradz(1:nqq,node1) = gradz(1:nqq,node1) + term(3)*dqq(1:nqq)

          endif
        end if n1_loc

!       Now do the other node

        dqq(1:nqq) = -dqq(1:nqq)
        dx = -dx
        dy = -dy
        dz = -dz

        n2_loc : if (node2 <= nnodes0) then
          if (.not.(periodic_tag(node2) < 0 .and. periodic_tag(node1) /= 0))then

            term(:) = lstgs_func(dx,         dy,         dz,                   &
                                 r11(node2), r12(node2), r13(node2),           &
                                 r22(node2), r23(node2), r33(node2))

            gradx(1:nqq,node2) = gradx(1:nqq,node2) + term(1)*dqq(1:nqq)
            grady(1:nqq,node2) = grady(1:nqq,node2) + term(2)*dqq(1:nqq)
            gradz(1:nqq,node2) = gradz(1:nqq,node2) + term(3)*dqq(1:nqq)

          endif
        end if n2_loc

      end do scan_edges

! Now we have to gather the results on the periodic planes

      do i = 1, nperiodic

        gx(1:nqq) = 0.0_dp
        gy(1:nqq) = 0.0_dp
        gz(1:nqq) = 0.0_dp

        do j = 1, periodic_data(i)%n
          node = periodic_data(i)%list(j)
          gx(1:nqq) = gx(1:nqq) + gradx(1:nqq,node)
          gy(1:nqq) = gy(1:nqq) + grady(1:nqq,node)
          gz(1:nqq) = gz(1:nqq) + gradz(1:nqq,node)
        end do

        do j = 1, periodic_data(i)%n
          node = periodic_data(i)%list(j)
          gradx(1:nqq,node) = gx(1:nqq)
          grady(1:nqq,node) = gy(1:nqq)
          gradz(1:nqq,node) = gz(1:nqq)
        end do

      end do

    end if check_second_order

  end subroutine lstgs_periodic

!================================== LSTGS_NOBOUND ============================80
!
! Calculates the Gradients at the nodes using weighted least squares
!
! This subroutine solves using Gram-Schmidt
!
! Skips boundary nodes
!=============================================================================80
  subroutine lstgs_nobound(turb_grad_flag,                                     &
                           nnodes0,nnodes01,nedgeloc,eptr,                     &
                           qnode,gradx,grady,gradz,x,y,z,                      &
                           r11,r12,r13,r22,r23,r33,bctag,                      &
                           n_tot,n_grd,turb,n_turb)

    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations

    integer,                              intent(in)  :: turb_grad_flag
    integer,                              intent(in)  :: nnodes0,nnodes01
    integer,                              intent(in)  :: n_tot, n_grd
    integer,                              intent(in)  :: nedgeloc
    integer,                              intent(in)  :: n_turb
    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    real(dp), dimension(n_tot,nnodes01),  intent(in)  :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)  :: turb
    real(dp), dimension(n_grd,nnodes01),  intent(out) :: gradx,grady,gradz
    real(dp), dimension(nnodes01),        intent(in)  :: x,y,z
    real(dp), dimension(nnodes0),         intent(in)  :: r11,r12,r13
    real(dp), dimension(nnodes0),         intent(in)  :: r22,r23,r33
    integer,  dimension(nnodes01),        intent(in)  :: bctag

    integer :: j, n, my_ntt, node1, node2, nedge_eval

    real(dp), dimension(n_grd) :: dqq

    real(dp) :: dx, dy, dz, weight
    real(dp), dimension(3) :: term

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

    real(dp), parameter :: zero_tol = 1.0e-32_dp

  continue

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

! Set distance weighting factor (assume unweighted)

   !weight_factor = my_0

! Zero out the gradients

    gradx(:,:) = my_0
    grady(:,:) = my_0
    gradz(:,:) = my_0

! Initialize dqq

    dqq(:) = my_0

    nedge_eval = nedgeloc + nedge_ns

! If second order, loop over all the faces accumulate sums

    check_second_order : if (my_ntt > first_order_iterations) then

      scan_edges : do n = 1, nedge_eval

        if ( n <= nedgeloc ) then
          node1 = eptr(1,n)
          node2 = eptr(2,n)
        else
          node1 = eptr_ns(1,n-nedgeloc)
          node2 = eptr_ns(2,n-nedgeloc)
        endif

        if ( bctag(node1) == 1 ) cycle scan_edges
        if ( bctag(node2) == 1 ) cycle scan_edges

        dx = x(node2) - x(node1)
        dy = y(node2) - y(node1)
        dz = z(node2) - z(node1)

        weight = my_1

        dx = weight*dx
        dy = weight*dy
        dz = weight*dz

        dqq(1:n_tot) = weight*(qnode(1:n_tot,node2) - qnode(1:n_tot,node1))

!       turb gradients if needed

        if ( turb_grad_flag > 0 ) then

              do j=1,n_turb
                dqq(n_tot+j) =  weight*( turb(j,node2) - turb(j,node1) )
              end do

        end if

        if ( node1 <= nnodes0 ) then
          if ( abs(r11(node1)) > zero_tol .and.                             &
               abs(r22(node1)) > zero_tol .and.                             &
               abs(r33(node1)) > zero_tol ) then

            term(:) = lstgs_func(dx,         dy,         dz,                   &
                                 r11(node1), r12(node1), r13(node1),           &
                                 r22(node1), r23(node1), r33(node1))

            gradx(1:n_grd,node1) = gradx(1:n_grd,node1) + term(1)*dqq(1:n_grd)
            grady(1:n_grd,node1) = grady(1:n_grd,node1) + term(2)*dqq(1:n_grd)
            gradz(1:n_grd,node1) = gradz(1:n_grd,node1) + term(3)*dqq(1:n_grd)
          end if
        end if

!     Now do the other node
        dqq = -dqq
        dx = -dx
        dy = -dy
        dz = -dz

        if ( node2 <= nnodes0 ) then
          if ( abs(r11(node2)) > zero_tol .and.                             &
               abs(r22(node2)) > zero_tol .and.                             &
               abs(r33(node2)) > zero_tol ) then

            term(:) = lstgs_func(dx,         dy,         dz,                   &
                                 r11(node2), r12(node2), r13(node2),           &
                                 r22(node2), r23(node2), r33(node2))

            gradx(1:n_grd,node2) = gradx(1:n_grd,node2) + term(1)*dqq(1:n_grd)
            grady(1:n_grd,node2) = grady(1:n_grd,node2) + term(2)*dqq(1:n_grd)
            gradz(1:n_grd,node2) = gradz(1:n_grd,node2) + term(3)*dqq(1:n_grd)

          end if
        end if

      end do scan_edges

    end if check_second_order

  end subroutine lstgs_nobound

!=================================== LSTGS_2D ================================80
!
! Calculates the gradients at the nodes using weighted least squares
!
! This subroutine solves using Gram-Schmidt
!
! Note: for compressible, edge-based NS:
!         n_grd = n_tot + n_turb
!       otherwise:
!         n_grd = n_tot
!
!=============================================================================80
  subroutine lstgs_2d(turb_grad_flag,                                          &
                      nnodes0,nnodes01,nedgeloc,eptr,symmetry, qnode,          &
                      gradx,grady,gradz,x,z,                                   &
                      r11,r12,r22,nedgeloc_2d,                                 &
                      nnodes0_2d,node_pairs_2d,n_tot,n_grd,turb,n_turb,        &
                      eqn_set,ndim,                                            &
                      weighted)

    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use debug_defs,           only : symmetry_bcs
    use inviscid_flux,        only : first_order_iterations
    use solution_types,       only : generic_gas

    integer,                              intent(in)  :: turb_grad_flag
    integer,                              intent(in)  :: eqn_set, ndim
    integer,                              intent(in)  :: nnodes0
    integer,                              intent(in)  :: nnodes01
    integer,                              intent(in)  :: n_tot, n_grd
    integer,                              intent(in)  :: nedgeloc
    integer,                              intent(in)  :: n_turb
    integer,                              intent(in)  :: nedgeloc_2d
    integer,                              intent(in)  :: nnodes0_2d
    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    integer,  dimension(2,nnodes0_2d),    intent(in)  :: node_pairs_2d
    integer,  dimension(nnodes01),        intent(in)  :: symmetry
    real(dp), dimension(n_tot,nnodes01),  intent(in)  :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)  :: turb
    real(dp), dimension(n_grd,nnodes01),  intent(out) :: gradx, grady, gradz
    real(dp), dimension(nnodes01),        intent(in)  :: x, z
    real(dp), dimension(nnodes0),         intent(in)  :: r11, r12, r22
    logical,  optional,                   intent(in)  :: weighted

    real(dp), dimension(n_grd) :: dqq
    real(dp), dimension(n_grd) :: contx, conty, contz

    real(dp) :: dx, dz
    real(dp) :: weight
    real(dp), dimension(3) :: term

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

    integer :: i, j, n, node1, node2, nedge_eval, my_ntt, n_vec, nqq

  continue

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

    if ( eqn_set == generic_gas ) then
      n_vec = ndim
      nqq   = ndim
    else
      n_vec = n_tot
      nqq   = n_grd
    end if

! Set distance weighting factor

    weight_factor = my_0
    if (present( weighted )) then
      if(weighted) weight_factor = my_1
    endif

! Zero out the gradients (note: grady must be zeroed out as well)

    gradx(:,:) = my_0
    grady(:,:) = my_0
    gradz(:,:) = my_0

! Initialize dqq

    dqq(:) = my_0

    nedge_eval = nedgeloc_2d

! If second order, loop over all the faces accumulate sums

    check_second_order : if (my_ntt > first_order_iterations) then

      if (.not.symmetry_bcs) then

        do n = 1, nedge_eval

          node1 = eptr(1,n)
          node2 = eptr(2,n)

          dx = x(node2) - x(node1)
          dz = z(node2) - z(node1)

          weight = reconstruct_weight(dx, my_0, dz,                          &
                                      weight_factor, wls_inv_dist_scale)

          dx = weight*dx
          dz = weight*dz

          dqq(1:n_vec) = weight*(qnode(1:n_vec,node2) - qnode(1:n_vec,node1))

!         turb gradients if needed

          if ( turb_grad_flag > 0 ) then

                do j=1,n_turb
                  dqq(n_tot+j) =  weight*( turb(j,node2) - turb(j,node1) )
                end do

          end if

          if (node1 <= nnodes0) then

            term(:) = lstgs_func(dx,         my_0, dz,         &
                                 r11(node1), my_0, r12(node1), &
                                 my_1,       my_0, r22(node1))
            gradx(1:nqq,node1) = gradx(1:nqq,node1) + term(1)*dqq(1:nqq)
            gradz(1:nqq,node1) = gradz(1:nqq,node1) + term(3)*dqq(1:nqq)

          end if

!         Now do the other node

          dqq(1:nqq) = -dqq(1:nqq)
          dx = -dx
          dz = -dz

          if (node2 <= nnodes0) then

            term(:) = lstgs_func(dx,         my_0, dz,         &
                                 r11(node2), my_0, r12(node2), &
                                 my_1,       my_0, r22(node2))
            gradx(1:nqq,node2) = gradx(1:nqq,node2) + term(1)*dqq(1:nqq)
            gradz(1:nqq,node2) = gradz(1:nqq,node2) + term(3)*dqq(1:nqq)

          end if

        end do

      else

        scan_edges : do n = 1, nedge_eval

          node1 = eptr(1,n)
          node2 = eptr(2,n)

          dx = x(node2) - x(node1)
          dz = z(node2) - z(node1)

          weight = reconstruct_weight(dx, my_0, dz,                            &
                                      weight_factor, wls_inv_dist_scale)

          dx = weight*dx
          dz = weight*dz

          dqq(1:n_vec) = weight*(qnode(1:n_vec,node2) - qnode(1:n_vec,node1))

!         turb gradients if needed

          if ( turb_grad_flag > 0 ) then

                do j=1,n_turb
                  dqq(n_tot+j) = weight*( turb(j,node2) - turb(j,node1) )
                end do

          end if

          if (node1 <= nnodes0) then
            term(:) = lstgs_func(dx,         my_0, dz,         &
                                 r11(node1), my_0, r12(node1), &
                                 my_1,       my_0, r22(node1))

            contx(:) = term(1)
            conty(:) = term(2)
            contz(:) = term(3)

            call lstgs_sym(symmetry(node1),                                    &
                           dx,         my_0, dz,                               &
                           r11(node1), my_0, r12(node1),                       &
                           my_1,       my_0, r22(node1),                       &
                           nqq, contx(1:nqq), conty(1:nqq), contz(1:nqq) )

            gradx(1:nqq,node1) = gradx(1:nqq,node1) + contx(1:nqq)*dqq(1:nqq)
            gradz(1:nqq,node1) = gradz(1:nqq,node1) + contz(1:nqq)*dqq(1:nqq)
          end if

!         Now do the other node

          dqq(1:nqq) = -dqq(1:nqq)
          dx = -dx
          dz = -dz

          if (node2 <= nnodes0) then

            term(:) = lstgs_func(dx,         my_0, dz,         &
                                 r11(node2), my_0, r12(node2), &
                                 my_1,       my_0, r22(node2))

            contx(:) = term(1)
            conty(:) = term(2)
            contz(:) = term(3)

            call lstgs_sym(symmetry(node2),                                    &
                           dx,         my_0, dz,                               &
                           r11(node2), my_0, r12(node2),                       &
                           my_1,       my_0, r22(node2),                       &
                           nqq, contx(1:nqq), conty(1:nqq), contz(1:nqq) )

            gradx(1:nqq,node2) = gradx(1:nqq,node2) + contx(1:nqq)*dqq(1:nqq)
            gradz(1:nqq,node2) = gradz(1:nqq,node2) + contz(1:nqq)*dqq(1:nqq)

          end if

        end do scan_edges

      endif

!     Copy gradients to opposite plane

      do i = 1,nnodes0_2d

        node1 = node_pairs_2d(1,i)
        node2 = node_pairs_2d(2,i)

        gradx(1:nqq,node2) = gradx(1:nqq,node1)
        gradz(1:nqq,node2) = gradz(1:nqq,node1)

      end do

    end if check_second_order

  end subroutine lstgs_2d

!=================================== LSTGS_VECT ==============================80
!
! Calculates the Gradients at the nodes using weighted least squares
! This subroutine solves using Gram-Schmidt
! for a vector only.
!
!=============================================================================80
  subroutine lstgs_vect(nnodes0,nnodes01,nedgeloc,eptr,symmetry,               &
                        qnode,gradx,grady,gradz,x,y,z,                         &
                        r11,r12,r13,r22,r23,r33)

    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations

    integer,                         intent(in)  :: nnodes0, nnodes01
    integer,                         intent(in)  :: nedgeloc
    integer,  dimension(2,nedgeloc), intent(in)  :: eptr
    integer,  dimension(nnodes01),   intent(in)  :: symmetry
    real(dp), dimension(nnodes01),   intent(in)  :: qnode
    real(dp), dimension(nnodes01),   intent(out) :: gradx, grady, gradz
    real(dp), dimension(nnodes01),   intent(in)  :: x, y, z
    real(dp), dimension(nnodes0),    intent(in)  :: r11, r12, r13
    real(dp), dimension(nnodes0),    intent(in)  :: r22, r23, r33

    integer :: i, n, my_ntt, node1, node2

    real(dp) :: dx, dy, dz, weight, dqq
    real(dp), dimension(3) :: term

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

  continue

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

! Set distance weighting factor (assume unweighted)

   !weight_factor = my_0

! Zero out the gradients

    do i = 1,nnodes01
      gradx(i) = my_0
      grady(i) = my_0
      gradz(i) = my_0
    end do

! If second order, loop over all the faces accumulate sums

    check_second_order : if (my_ntt > first_order_iterations)then

      scan_edges : do n = 1, nedgeloc

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        dx = x(node2) - x(node1)
        dy = y(node2) - y(node1)
        dz = z(node2) - z(node1)

        weight = my_1

        dx = weight*dx
        dy = weight*dy
        dz = weight*dz

        dqq = weight*( qnode(node2) - qnode(node1) )

        if(node1 <= nnodes0) then
          term(:) = lstgs_func(dx,         dy,         dz,                     &
                               r11(node1), r12(node1), r13(node1),             &
                               r22(node1), r23(node1), r33(node1))

          call lstgs_sym(symmetry(node1),                                      &
                         dx,         dy,         dz,                           &
                         r11(node1), r12(node1), r13(node1),                   &
                         r22(node1), r23(node1), r33(node1),                   &
                         1, term(1), term(2), term(3) )

          gradx(node1) = gradx(node1) + term(1)*dqq
          grady(node1) = grady(node1) + term(2)*dqq
          gradz(node1) = gradz(node1) + term(3)*dqq
        endif

        ! Now do the other node

        dqq = -dqq
        dx = -dx
        dy = -dy
        dz = -dz

        if(node2 <= nnodes0) then
          term(:) = lstgs_func(dx,         dy,         dz,                     &
                               r11(node2), r12(node2), r13(node2),             &
                               r22(node2), r23(node2), r33(node2))

          call lstgs_sym(symmetry(node2),                                      &
                         dx,         dy,         dz,                           &
                         r11(node2), r12(node2), r13(node2),                   &
                         r22(node2), r23(node2), r33(node2),                   &
                         1, term(1), term(2), term(3) )

          gradx(node2) = gradx(node2) + term(1)*dqq
          grady(node2) = grady(node2) + term(2)*dqq
          gradz(node2) = gradz(node2) + term(3)*dqq
        endif

      end do scan_edges

    end if check_second_order

  end subroutine lstgs_vect

!=============================================================================80
!
! use sumgs and lstgs_vect to calcluate magnitude of the gradient of a vector
!
! written for use with adapt_out, to clean up that code.
! key_var comes into routine with the global, reordered node numbering
!
!=============================================================================80
  subroutine get_vector_grad(key_var, gradx, grady, gradz, nnodes0, nnodes01,  &
                             x, y, z,                                          &
                             nedgeloc, eptr, symmetry )

    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations
    use lmpi_app,             only : lmpi_xfer
    use cut_gradient,         only : cut_lstgs_vect, cut_sumgs
    use cut_types,            only : cut_cell_activated

    integer,                         intent(in)  :: nnodes0, nnodes01, nedgeloc
    integer,  dimension(2,nedgeloc), intent(in)  :: eptr
    integer,  dimension(nnodes01),   intent(in)  :: symmetry
    real(dp), dimension(nnodes01),   intent(in)  :: x, y, z
    real(dp), dimension(nnodes01),   intent(in)  :: key_var
    real(dp), dimension(nnodes01),   intent(out) :: gradx, grady, gradz

    real(dp), dimension(nnodes0)  :: r11, r12, r13, r22, r23, r33

    integer :: status

  continue

    if (cut_cell_activated) then
      call cut_sumgs( nnodes0,nnodes01,x,y,z,  &
                      r11,r12,r13,r22,r23,r33, &
                      nedgeloc, eptr, status )
      call lmpi_conditional_stop(status)
    else
      call sumgs(nnodes0,  nnodes01,  nedgeloc,   eptr, symmetry,              &
               x,       y,       z,                                            &
               r11,     r12,     r13,                                          &
               r22,     r23,     r33)
    end if

!   this is to make sure that the gradients are computed
    if (itime == 0) then
      ntt = 1
    else
      pseudo_sub = 1
    end if
    first_order_iterations = 0

    if (cut_cell_activated) then
      call  cut_lstgs_vect( nnodes0,nnodes01,x,y,z,                            &
                        r11,r12,r13,r22,r23,r33,                               &
                        key_var,                                               &
                        gradx,grady,gradz,                                     &
                        nedgeloc, eptr)
    else
      call lstgs_vect(nnodes0,  nnodes01,  nedgeloc,   eptr, symmetry,         &
                      key_var,                                                 &
                      gradx,   grady,   gradz,                                 &
                      x,       y,        z,                                    &
                      r11,     r12,      r13,                                  &
                      r22,     r23,      r33)
    end if

    call lmpi_xfer(gradx)
    call lmpi_xfer(grady)
    call lmpi_xfer(gradz)

  end subroutine get_vector_grad

!=============================== Q_MINMAX_SYMMETRY ===========================80
!
! Corrects q (min_max) at symmetry planes for antisymmetric components.
!
!=============================================================================80
  subroutine q_minmax_symmetry(ibc, nbnode, ibnode, nnodes01,                  &
                               ndim, qmin, qmax)

    use bc_names, only : symmetry_x, symmetry_y, symmetry_z

    integer,                            intent(in)    :: ibc, nbnode
    integer,                            intent(in)    :: nnodes01, ndim
    integer,  dimension(nbnode),        intent(in)    :: ibnode
    real(dp), dimension(ndim,nnodes01), intent(inout) :: qmin, qmax

    real(dp) :: term

    integer :: n, node

  continue

    select case (ibc)

    case (symmetry_x)

      do n = 1,nbnode
        node = ibnode(n)
        term = max ( abs(qmin(n_momx,node)) , abs(qmax(n_momx,node)) )
        qmin(n_momx,node) = - term
        qmax(n_momx,node) = + term
      end do

    case (symmetry_y)

      do n = 1,nbnode
        node = ibnode(n)
        term = max ( abs(qmin(n_momy,node)) , abs(qmax(n_momy,node)) )
        qmin(n_momy,node) = - term
        qmax(n_momy,node) = + term
      end do

    case (symmetry_z)

      do n = 1,nbnode
        node = ibnode(n)
        term = max ( abs(qmin(n_momz,node)) , abs(qmax(n_momz,node)) )
        qmin(n_momz,node) = - term
        qmax(n_momz,node) = + term
      end do

    end select

  end subroutine q_minmax_symmetry

!================================= TIMLIM ====================================80
!
! Flux Limiter
!
!=============================================================================80
  subroutine timlim(nnodes0,nnodes01,nedgeloc,qnode,phi,                       &
                    gradx,grady,gradz,x,y,z,vol,eptr,nedgeloc_2d,              &
                    n_grd,n_tot,ndim,nbound,bc,                                &
                    cgamma, slenxn, slenyn, slenzn, slen, jag )

    use inviscid_flux,        only : first_order_iterations,                   &
                                     freeze_limiter_iteration
    use info_depr,            only : ntt, twod, new_umuscl
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use twod_util,            only : q_2d
    use lsq_constants,        only : mlsq, tf, cg_tol
    use lsq_defs,             only : nc_mapped_lsq

    integer,                              intent(in)    :: nnodes0, nnodes01
    integer,                              intent(in)    :: nedgeloc, nedgeloc_2d
    integer,                              intent(in)    :: n_grd, ndim
    integer,                              intent(in)    :: n_tot, nbound
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_grd,nnodes01),  intent(inout) :: phi
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx, grady, gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(nnodes01),        intent(in)    :: cgamma
    real(dp), dimension(nnodes01),        intent(in)    :: slenxn
    real(dp), dimension(nnodes01),        intent(in)    :: slenyn
    real(dp), dimension(nnodes01),        intent(in)    :: slenzn
    real(dp), dimension(nnodes01),        intent(in)    :: slen
    integer,  dimension(2,nedgeloc),      intent(in)    :: eptr
    integer,  dimension(:),               intent(in)    :: jag
    type(bcgrid_type), dimension(nbound), intent(in)   :: bc

    ! temporary automatic arrays to form limiter
    real(dp), dimension(n_grd,nnodes01) :: qmin
    real(dp), dimension(n_grd,nnodes01) :: qmax

    ! saved copy of limiter to permit freezing
    real(dp), dimension(n_grd,nnodes01) :: phi_original

    ! limiter blending

    integer :: i, n, my_ntt, node1, node2, nedge_lim_eval

    real(dp) :: qi, temp, phit

    real(dp), dimension(3,2) :: rni

    real(dp), parameter :: my_1 = 1.0_dp
    real(dp), parameter :: my_4 = 4.0_dp

  continue

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

! store the current value of the limiter
! if we're going to potentially freeze it

    set_up_to_freeze_limiter : if ( freeze_limiter_iteration >= 0 ) then
      phi_original = phi
    endif set_up_to_freeze_limiter

! 2D vs 3D parameters

    if (twod) then
      nedge_lim_eval = nedgeloc_2d
    else
      nedge_lim_eval = nedgeloc
    end if

! First loop over the edges and find the maximum and minimum

    do i = 1,nnodes01
      qmax(1:ndim,i) = qnode(1:ndim,i)
      qmin(1:ndim,i) = qnode(1:ndim,i)
      phi(1:ndim,i) = my_1
    end do

    if (my_ntt < first_order_iterations) return

    scan_edges_1 : do n = 1, nedge_lim_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! First check node1

      qmax(1:ndim,node1) = max(qmax(1:ndim,node1),qnode(1:ndim,node2))
      qmin(1:ndim,node1) = min(qmin(1:ndim,node1),qnode(1:ndim,node2))

! Now for node2

      qmax(1:ndim,node2) = max(qmax(1:ndim,node2),qnode(1:ndim,node1))
      qmin(1:ndim,node2) = min(qmin(1:ndim,node2),qnode(1:ndim,node1))

    end do scan_edges_1

    do n = 1,nbound
      call q_minmax_symmetry( bc(n)%ibc, bc(n)%nbnode, bc(n)%ibnode,      &
                              nnodes01, ndim, qmin, qmax )
    enddo

! Now we have found the max and min of surrounding nodes
! so let do the extrapolation to the face and "limit"
! the gradient so no new extrema are produced

    scan_edges_2 : do n = 1, nedge_lim_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Compute the interface location of either the dual interface or the
! mapped interface

      rni = r_vec(nnodes01, node1, node2, q_2d, mlsq, tf, cg_tol,        &
                  x, y, z, cgamma, slen, slenxn, slenyn,                 &
                  slenzn, nc_mapped_lsq, .true.)

      loop_elements : do i = 1,ndim

! First do node 1

        if (.not. new_umuscl) then
          qi = qnode(i,node1) + gradx(i,node1)*rni(1,1)                  &
                              + grady(i,node1)*rni(2,1)                  &
                              + gradz(i,node1)*rni(3,1)
        else
          qi = qnode(i,node1) + (gradx(i,node1)*rni(1,1) +               &
                                 grady(i,node1)*rni(2,1) +               &
                                 gradz(i,node1)*rni(3,1))*my_4           &
             - (qnode(i,node2)-qnode(i,node1))
        end if

! Now check to see if this needs limiting

        temp = my_1
        if (qi > qnode(i,node1)) then
          temp = (qmax(i,node1) - qnode(i,node1)) / (qi - qnode(i,node1))
          phit = min(my_1,temp)
          phi(i,node1) = min(phi(i,node1),phit)
        else if  (qi < qnode(i,node1))  then
          temp = (qmin(i,node1) - qnode(i,node1)) / (qi - qnode(i,node1))
          phit = min(my_1,temp)
          phi(i,node1) = min(phi(i,node1),phit)
        end if

! Now for node 2

        if (.not. new_umuscl) then
          qi = qnode(i,node2) + gradx(i,node2)*rni(1,2)                  &
                              + grady(i,node2)*rni(2,2)                  &
                              + gradz(i,node2)*rni(3,2)
        else
          qi = qnode(i,node2) + (gradx(i,node2)*rni(1,2) +               &
                                 grady(i,node2)*rni(2,2) +               &
                                 gradz(i,node2)*rni(3,2))*my_4           &
             - (qnode(i,node1)-qnode(i,node2))
        end if

! Now check to see if this need limiting

        if (qi > qnode(i,node2)) then
          temp = (qmax(i,node2) - qnode(i,node2)) / (qi - qnode(i,node2))
          phit = min(my_1,temp)
          phi(i,node2) = min(phi(i,node2),phit)
        else if (qi < qnode(i,node2)) then
          temp = (qmin(i,node2) - qnode(i,node2)) / (qi - qnode(i,node2))
          phit = min(my_1,temp)
          phi(i,node2) = min(phi(i,node2),phit)
        end if

      end do loop_elements
    end do scan_edges_2

! Now if we want to freeze the limiter, evaluate the reconstruction using the
! original limiter.  If the reconstruction is successful, keep the original
! phi.  However, if it fails, use the new value of phi computed above.  In
! this way, we hope that the limiter will be frozen throughout the majority
! of the field, while it adjusts maybe occasionally here or there to keep the
! reconstruction from failing.

    set_phi : if ( freeze_limiter_iteration >=0 .and. &
                   ntt > freeze_limiter_iteration ) then

      call test_reconstruct_frozen_lim(nnodes0,nnodes01,nedgeloc_2d,nedgeloc,  &
                                       eptr,x,y,z,gradx,grady,gradz,qnode,     &
                                       vol,phi_original,phi,n_grd,n_tot,ndim,  &
                                       cgamma,slenxn,slenyn,slenzn,slen, jag )

! Now load phi with the original values, except for the ones
! that were reevaluated above

      phi = phi_original
    endif set_phi

  end subroutine timlim


!=============================== TIMLIMI =====================================80
!
! Flux Limiter
!
!=============================================================================80
  subroutine timlimi(nnodes01,nedgeloc,qnode,phi,                              &
                     gradx,grady,gradz,x,y,z,eptr,nedgeloc_2d,ndim,            &
                     n_grd,n_tot,nbound,bc,                                    &
                     cgamma, slenxn, slenyn, slenzn, slen )

    use inviscid_flux,        only : first_order_iterations
    use info_depr,            only : ntt, twod
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use twod_util,            only : q_2d
    use lsq_constants,        only : mlsq, tf, cg_tol
    use lsq_defs,             only : nc_mapped_lsq

    integer,                              intent(in)  :: nnodes01, nedgeloc
    integer,                              intent(in)  :: nedgeloc_2d, ndim
    integer,                              intent(in)  :: n_grd, n_tot, nbound
    real(dp), dimension(n_tot,nnodes01),  intent(in)  :: qnode
    real(dp), dimension(n_grd,nnodes01),  intent(out) :: phi
    real(dp), dimension(n_grd,nnodes01),  intent(in)  :: gradx,grady,gradz
    real(dp), dimension(nnodes01),        intent(in)  :: x, y, z
    real(dp), dimension(nnodes01),        intent(in)  :: cgamma
    real(dp), dimension(nnodes01),        intent(in)  :: slenxn
    real(dp), dimension(nnodes01),        intent(in)  :: slenyn
    real(dp), dimension(nnodes01),        intent(in)  :: slenzn
    real(dp), dimension(nnodes01),        intent(in)  :: slen
    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    type(bcgrid_type), dimension(nbound), intent(in)  :: bc

    ! temporary automatic arrays to form limiter
    real(dp), dimension(n_grd,nnodes01) :: qmin
    real(dp), dimension(n_grd,nnodes01) :: qmax

    integer :: i, n, my_ntt, node1, node2, nedge_lim_eval

    real(dp) :: q1, q2, q3, q4, temp, phit

    real(dp), dimension(3,2) :: rni

    real(dp), parameter :: my_1   = 1.0_dp

  continue

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

! 2D vs 3D parameters

    if (twod) then
      nedge_lim_eval = nedgeloc_2d
    else
      nedge_lim_eval = nedgeloc
    end if

! First loop over the edges and find the maximum and minimum

    do i = 1,nnodes01
      qmax(1,i) = qnode(1,i)
      qmax(2,i) = qnode(2,i)
      qmax(3,i) = qnode(3,i)
      qmax(4,i) = qnode(4,i)

      qmin(1,i) = qnode(1,i)
      qmin(2,i) = qnode(2,i)
      qmin(3,i) = qnode(3,i)
      qmin(4,i) = qnode(4,i)

      phi(1,i) = my_1
      phi(2,i) = my_1
      phi(3,i) = my_1
      phi(4,i) = my_1
    end do

    if (my_ntt < first_order_iterations) return

    scan_edges_1 : do n = 1, nedge_lim_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! First check node1

      qmax(1,node1) = max(qmax(1,node1),qnode(1,node2))
      qmax(2,node1) = max(qmax(2,node1),qnode(2,node2))
      qmax(3,node1) = max(qmax(3,node1),qnode(3,node2))
      qmax(4,node1) = max(qmax(4,node1),qnode(4,node2))

      qmin(1,node1) = min(qmin(1,node1),qnode(1,node2))
      qmin(2,node1) = min(qmin(2,node1),qnode(2,node2))
      qmin(3,node1) = min(qmin(3,node1),qnode(3,node2))
      qmin(4,node1) = min(qmin(4,node1),qnode(4,node2))

! Now for node2

      qmax(1,node2) = max(qmax(1,node2),qnode(1,node1))
      qmax(2,node2) = max(qmax(2,node2),qnode(2,node1))
      qmax(3,node2) = max(qmax(3,node2),qnode(3,node1))
      qmax(4,node2) = max(qmax(4,node2),qnode(4,node1))

      qmin(1,node2) = min(qmin(1,node2),qnode(1,node1))
      qmin(2,node2) = min(qmin(2,node2),qnode(2,node1))
      qmin(3,node2) = min(qmin(3,node2),qnode(3,node1))
      qmin(4,node2) = min(qmin(4,node2),qnode(4,node1))

    end do scan_edges_1

    do n = 1,nbound
      call q_minmax_symmetry( bc(n)%ibc, bc(n)%nbnode, bc(n)%ibnode,      &
                              nnodes01, ndim, qmin, qmax )
    enddo

! Now we have found the max and min of surrounding nodes
! so let do the extrapolation to the face and "limit"
! the gradient so no new extrema are produced

    scan_edges_2 : do n = 1, nedge_lim_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Compute the interface location of either the dual interface or the
! mapped interface

      rni = r_vec(nnodes01, node1, node2, q_2d, mlsq, tf, cg_tol,        &
                  x, y, z, cgamma, slen, slenxn, slenyn,                 &
                  slenzn, nc_mapped_lsq, .true.)

! First do node 1

      q1 = qnode(1,node1) + gradx(1,node1)*rni(1,1)                      &
                          + grady(1,node1)*rni(2,1)                      &
                          + gradz(1,node1)*rni(3,1)
      q2 = qnode(2,node1) + gradx(2,node1)*rni(1,1)                      &
                          + grady(2,node1)*rni(2,1)                      &
                          + gradz(2,node1)*rni(3,1)
      q3 = qnode(3,node1) + gradx(3,node1)*rni(1,1)                      &
                          + grady(3,node1)*rni(2,1)                      &
                          + gradz(3,node1)*rni(3,1)
      q4 = qnode(4,node1) + gradx(4,node1)*rni(1,1)                      &
                          + grady(4,node1)*rni(2,1)                      &
                          + gradz(4,node1)*rni(3,1)

! Now check to see if this needs limiting

      if (q1 > qnode(1,node1)) then
        temp = (qmax(1,node1) - qnode(1,node1)) / (q1 - qnode(1,node1))
        phit = min(my_1,temp)
        phi(1,node1) = min(phi(1,node1),phit)
      else if  (q1 < qnode(1,node1))  then
        temp = (qmin(1,node1) - qnode(1,node1)) / (q1 - qnode(1,node1))
        phit = min(my_1,temp)
        phi(1,node1) = min(phi(1,node1),phit)
      end if

      if (q2 > qnode(2,node1)) then
        temp = (qmax(2,node1) - qnode(2,node1)) / (q2 - qnode(2,node1))
        phit = min(my_1,temp)
        phi(2,node1) = min(phi(2,node1),phit)
      else if (q2 < qnode(2,node1)) then
        temp = (qmin(2,node1) - qnode(2,node1)) / (q2 - qnode(2,node1))
        phit = min(my_1,temp)
        phi(2,node1) = min(phi(2,node1),phit)
      end if

      if (q3 > qnode(3,node1)) then
        temp = (qmax(3,node1) - qnode(3,node1)) / (q3 - qnode(3,node1))
        phit = min(my_1,temp)
        phi(3,node1) = min(phi(3,node1),phit)
      else if (q3 < qnode(3,node1)) then
        temp = (qmin(3,node1) - qnode(3,node1)) / (q3 - qnode(3,node1))
        phit = min(my_1,temp)
        phi(3,node1) = min(phi(3,node1),phit)
      end if

      if (q4 > qnode(4,node1)) then
        temp = (qmax(4,node1) - qnode(4,node1)) / (q4 - qnode(4,node1))
        phit = min(my_1,temp)
        phi(4,node1) = min(phi(4,node1),phit)
      else if(q4 < qnode(4,node1))then
        temp = (qmin(4,node1) - qnode(4,node1)) / (q4 - qnode(4,node1))
        phit = min(my_1,temp)
        phi(4,node1) = min(phi(4,node1),phit)
      end if

! Now for node 2

      q1 = qnode(1,node2) + gradx(1,node2)*rni(1,2)                      &
                          + grady(1,node2)*rni(2,2)                      &
                          + gradz(1,node2)*rni(3,2)
      q2 = qnode(2,node2) + gradx(2,node2)*rni(1,2)                      &
                          + grady(2,node2)*rni(2,2)                      &
                          + gradz(2,node2)*rni(3,2)
      q3 = qnode(3,node2) + gradx(3,node2)*rni(1,2)                      &
                          + grady(3,node2)*rni(2,2)                      &
                          + gradz(3,node2)*rni(3,2)
      q4 = qnode(4,node2) + gradx(4,node2)*rni(1,2)                      &
                          + grady(4,node2)*rni(2,2)                      &
                          + gradz(4,node2)*rni(3,2)

! Now check to see if this need limiting

      if (q1 > qnode(1,node2)) then
        temp = (qmax(1,node2) - qnode(1,node2)) / (q1 - qnode(1,node2))
        phit = min(my_1,temp)
        phi(1,node2) = min(phi(1,node2),phit)
      else if (q1 < qnode(1,node2)) then
        temp = (qmin(1,node2) - qnode(1,node2)) / (q1 - qnode(1,node2))
        phit = min(my_1,temp)
        phi(1,node2) = min(phi(1,node2),phit)
      end if

      if (q2 > qnode(2,node2)) then
        temp = (qmax(2,node2) - qnode(2,node2)) / (q2 - qnode(2,node2))
        phit = min(my_1,temp)
        phi(2,node2) = min(phi(2,node2),phit)
      else if (q2 < qnode(2,node2)) then
        temp = (qmin(2,node2) - qnode(2,node2)) / (q2 - qnode(2,node2))
        phit = min(my_1,temp)
        phi(2,node2) = min(phi(2,node2),phit)
      end if

      if (q3 > qnode(3,node2)) then
        temp = (qmax(3,node2) - qnode(3,node2)) / (q3 - qnode(3,node2))
        phit = min(my_1,temp)
        phi(3,node2) = min(phi(3,node2),phit)
      else if (q3 < qnode(3,node2)) then
        temp = (qmin(3,node2) - qnode(3,node2)) / (q3 - qnode(3,node2))
        phit = min(my_1,temp)
        phi(3,node2) = min(phi(3,node2),phit)
      end if

      if (q4 > qnode(4,node2)) then
        temp = (qmax(4,node2) - qnode(4,node2)) / (q4 - qnode(4,node2))
        phit = min(my_1,temp)
        phi(4,node2) = min(phi(4,node2),phit)
      else if (q4 < qnode(4,node2)) then
        temp = (qmin(4,node2) - qnode(4,node2)) / (q4 - qnode(4,node2))
        phit = min(my_1,temp)
        phi(4,node2) = min(phi(4,node2),phit)
      end if

    end do scan_edges_2

  end subroutine timlimi


!================================= GSBLIM ====================================80
!
!                    Generalized Stencil Based Limiter
!
! Tim Barths node centered stencil based flux limiter generalized to use the
! minmod, van Leer, smooth (van Albada), smooth(CFL3D) or smooth(Venkat)
! limiter functions.
! Where:
!   iflim = 13 : TVD minmod
!   iflim = 14 : TVD van Leer
!   iflim = 15 : TVB smooth ala van Albada
!   iflim = 16 : TVB smooth ala CFL3D
!   iflim = 17 : TVB smooth ala Venkat
!
!=============================================================================80
  subroutine gsblim(nnodes0, nnodes01, nedgeloc, qnode, phi,                   &
                    gradx, grady, gradz, x, y, z, vol, xn, yn, zn, ra,         &
                    eptr, nedgeloc_2d, n_grd, n_tot, eqn_set, ndim,            &
                    nbound, bc,                                                &
                    cgamma, slenxn, slenyn, slenzn, slen, jag )

    use fun3d_constants,         only : my_0, my_half, my_3rd, my_4th,         &
                                        my_1, my_2, my_3, my_4, my_6, pi
    use fluid,                   only : gamma, gm1, sutherland_constant
    use inviscid_flux,           only : first_order_iterations, iflim
    use info_depr,               only : ntt, twod, new_umuscl, kappa_umuscl,   &
                                        epscoef, ivisc, tref, xmach, re,       &
                                        pr_limiter_coeff, wall_limit_less
    use nml_nonlinear_solves,    only : itime
    use timeacc,                 only : pseudo_sub
    use inviscid_flux,           only : freeze_limiter_iteration
    use generic_gas_map,         only : n_etot, n_sonic_k, n_amu_k, n_species, &
                                        n_momx, n_momy, n_momz, n_density
    use twod_util,               only : q_2d
    use lsq_constants,           only : mlsq, tf, cg_tol
    use lsq_defs,                only : nc_mapped_lsq
    use solution_types,          only : generic_gas
    use nml_governing_equations, only : viscous_terms

    integer,                              intent(in)    :: nnodes0
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: nedgeloc
    integer,                              intent(in)    :: nedgeloc_2d
    integer,                              intent(in)    :: n_grd, n_tot
    integer,                              intent(in)    :: ndim
    integer,                              intent(in)    :: eqn_set
    integer,                              intent(in)    :: nbound
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_grd,nnodes01),  intent(inout) :: phi
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx, grady, gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(nnodes01),        intent(in)    :: cgamma
    real(dp), dimension(nnodes01),        intent(in)    :: slenxn
    real(dp), dimension(nnodes01),        intent(in)    :: slenyn
    real(dp), dimension(nnodes01),        intent(in)    :: slenzn
    real(dp), dimension(nnodes01),        intent(in)    :: slen
    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn, ra
    integer,  dimension(2,nedgeloc),      intent(in)    :: eptr
    integer,  dimension(:),               intent(in)    :: jag
    type(bcgrid_type), dimension(nbound), intent(in)    :: bc

    ! temporary automatic arrays to form limiter
    real(dp), dimension(n_grd,nnodes01) :: qmin
    real(dp), dimension(n_grd,nnodes01) :: qmax
    real(dp), dimension(nnodes01)       :: nodemach

    ! saved copy of limiter to permit freezing
    real(dp), dimension(n_grd,nnodes01) :: phi_original

    ! automated pressure limiter
    real(dp), dimension(nedgeloc) :: fl_coeff
    real(dp), dimension(nnodes01) :: pcoefn

    integer  :: i, n, k, my_ntt, node1, node2, nedge_lim_eval

    real(dp) :: temp, pcoef, pr_limiter_coeff_g

    real(dp) :: dqm, dqp, omega, eps, my_fact1, my_fact2, my_exp
    real(dp) :: cstar, xmr, c_gen, mu
    real(dp) :: rho, u, v, w, press, ut2, enrgy, Ht, sos2, q_pos_def
    real(dp) :: rho1, press1, t1, a1, u1, diam1, mu1, Re1, vcoef1
    real(dp) :: rho2, press2, t2, a2, u2, diam2, mu2, Re2, vcoef2
    real(dp) :: machf1, machf2, fmsmn1, fmsmn2, pcofn1, pcofn2

    real(dp), dimension(3,2) :: rni
    real(dp), dimension(:), allocatable :: gqr, dq, q1, q2

    integer,  parameter :: powervi = 4
    real(dp), parameter :: powervr = 35.0_dp/50.0_dp
    real(dp), parameter :: Re_min  = 35.0_dp
    real(dp), parameter :: dRe     = 30.0_dp

    real(dp), parameter :: powerir      = 0.25_dp
    real(dp), parameter :: phi_min_mach = 0.35_dp
    real(dp), parameter :: dphi_mach    = 0.65_dp
    real(dp), parameter :: plc_min_mach = 0.35_dp
    real(dp), parameter :: dplc_mach    = 0.15_dp

  continue

    allocate(gqr(ndim))
    allocate(dq(ndim))
    allocate(q1(ndim))
    allocate(q2(ndim))

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

    my_exp   = my_3rd
    my_fact1 = my_6
    my_fact2 = my_3
    if (twod) then
      my_exp   = my_half
      my_fact1 = my_4
      my_fact2 = my_1
    end if

    if (ivisc >= 2) then
      cstar = sutherland_constant/tref
      xmr  = xmach/re
    else
      cstar = my_0
      xmr   = my_0
    end if

    omega = my_2

! Store the current value of the limiter
! if we're going to potentially freeze it

    if ( freeze_limiter_iteration >= 0 ) then
      phi_original = phi
    end if

! 2D vs 3D parameters

    if (twod) then
      nedge_lim_eval = nedgeloc_2d
    else
      nedge_lim_eval = nedgeloc
    end if

! Loop over the cells and initialize phi to 1

    do i = 1,nnodes01
      phi(1:ndim,i) = my_1
    end do

! if not second order leave

    if (my_ntt < first_order_iterations) return

! Figure out if we're going to keep track of which edge determines
! phi for each node (needed for adjoint)

!   if ( present(phi) ) monitor_phi_contribs = .true.

! Loop over the cells and initialize qmin qnd qmax and compte the Mach no.

    set_up_loop: do i = 1,nnodes01

      qmax(1:ndim,i) = qnode(1:ndim,i)
      qmin(1:ndim,i) = qnode(1:ndim,i)

      if(n_species == 1)then
        rho   = qnode(1,i)
      else
        rho   = sum(qnode(1:n_species,i))
      end if
      u     = qnode(n_momx,i)
      v     = qnode(n_momy,i)
      w     = qnode(n_momz,i)
      press = qnode(n_etot,i)
      ut2   = u*u + v*v + w*w
      if ( eqn_set == generic_gas ) then
        sos2  = qnode(n_sonic_k(1),i)**2
      else
        enrgy = press/gm1 + my_half*rho*ut2
        Ht    = (enrgy + press)/rho
        sos2  = gm1*(Ht-my_half*ut2)
      end if

      nodemach(i) = sqrt(ut2/sos2)

    end do set_up_loop

! Find the q max and min of the edge neighbor node part of
! the gradient stencil

    qmin_qmax_loop: do n = 1, nedge_lim_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Check node1

      qmax(1:ndim,node1) = max(qmax(1:ndim,node1),qnode(1:ndim,node2))
      qmin(1:ndim,node1) = min(qmin(1:ndim,node1),qnode(1:ndim,node2))

! Check node2

      qmax(1:ndim,node2) = max(qmax(1:ndim,node2),qnode(1:ndim,node1))
      qmin(1:ndim,node2) = min(qmin(1:ndim,node2),qnode(1:ndim,node1))

    end do qmin_qmax_loop

! Fix symmetry bc nodes

    do n = 1,nbound
      call q_minmax_symmetry( bc(n)%ibc, bc(n)%nbnode, bc(n)%ibnode,           &
                              nnodes01, ndim, qmin, qmax )
    end do

! Now we have found the max and min of surrounding nodes
! so lets do the extrapolation to the face and "limit" the gradient
! based on those bounds such that no new extrema are produced

    gradient_limiter_loop: do n = 1, nedge_lim_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Compute the edge midpoint coordinates

! Compute the interface location of either the dual interface or the
! mapped interface

      rni = r_vec(nnodes01, node1, node2, q_2d, mlsq, tf, cg_tol, x, y, z,     &
                  cgamma, slen, slenxn, slenyn, slenzn,                        &
                  nc_mapped_lsq, .true.)

! Compute the node 1 limiter coefficient

      eps = epscoef*(my_fact2*(my_fact1*vol(node1)/pi)**my_exp)**3

! Reconstruct to the node 1 side of the interface

      gqr(1:ndim) = gradx(1:ndim,node1)*rni(1,1) +                             &
                    grady(1:ndim,node1)*rni(2,1) +                             &
                    gradz(1:ndim,node1)*rni(3,1)
      q1(1:ndim)  = qnode(1:ndim,node1) + gqr(1:ndim)

! Compute the limiter coefficient array at node1 using
! generalization of the Barth dual based limiting approach

      do k = 1,ndim

        if (q1(k) /= qnode(k,node1)) then

          if (q1(k) > qnode(k,node1)) then
            dqp = qmax(k,node1) - qnode(k,node1)
          else if (q1(k) < qnode(k,node1))  then
            dqp = qmin(k,node1) - qnode(k,node1)
          end if

          dqm = q1(k) - qnode(k,node1)
          dqp = my_half*dqp

          if (iflim == 13) then
            temp = minmods(dqm, omega*dqp) / dqm
          else if (iflim == 14) then
            temp = vlflxls(dqm, dqp) / dqm
          else if (iflim == 15) then
            temp = vaflxls(dqm, dqp, eps) / dqm
          else if (iflim == 16) then
            temp = smthlms(dqm, dqp, eps) / dqm
          else if (iflim == 17) then
            temp = vkflxls(dqm, my_2*dqp, eps) / dqm
          else
            temp = my_0
            write(*,*) 'Error: in reconstruction::gsblim: unknown iflim:', iflim
            call lmpi_conditional_stop(1)
          end if

          phi(k,node1) = max(my_0, min(phi(k,node1), min(my_1,temp)))

        end if

      end do

! Compute the node 2 limiter coefficient

      eps = epscoef*(my_fact2*(my_fact1*vol(node2)/pi)**my_exp)**3

! Reconstruct to the node 2 side of the interface

      gqr(1:ndim) = gradx(1:ndim,node2)*rni(1,2) +                             &
                    grady(1:ndim,node2)*rni(2,2) +                             &
                    gradz(1:ndim,node2)*rni(3,2)
      q2(1:ndim)  = qnode(1:ndim,node2) + gqr(1:ndim)

! Compute the limiter coefficient array at node2 using
! generalization of the Barth dual based limiting approach

      do k = 1, ndim

        if (q2(k) /= qnode(k,node2)) then

          if (q2(k) > qnode(k,node2)) then
            dqp = qmax(k,node2) - qnode(k,node2)
          else if (q2(k) < qnode(k,node2))  then
            dqp = qmin(k,node2) - qnode(k,node2)
          end if

          dqm = q2(k) - qnode(k,node2)
          dqp = my_half*dqp

          if (iflim == 13) then
            temp = minmods(dqm, omega*dqp) / dqm
          else if (iflim == 14) then
            temp = vlflxls(dqm, dqp) / dqm
          else if (iflim == 15) then
            temp = vaflxls(dqm, dqp, eps) / dqm
          else if (iflim == 16) then
            temp = smthlms(dqm, dqp, eps) / dqm
          else if (iflim == 17) then
            temp = vkflxls(dqm, my_2*dqp, eps) / dqm
          else
            temp = my_0
            write(*,*) 'Error: in reconstruction::gsblim: unknown iflim:', iflim
            call lmpi_conditional_stop(1)
          end if

          phi(k,node2) = max(my_0, min(phi(k,node2), min(my_1,temp)))

        end if

      end do

    end do gradient_limiter_loop

! Modifiy the gradient limiter based on the local Mach no., Reynolds no. and
! the pressure gradient and laplacian

    call pressure_limiter_coeff(n_tot, nnodes01, nnodes0, nedgeloc, eptr,      &
                                qnode, fl_coeff, eqn_set)

!   Loop over all the edges and calculate the pressure limiter effect on phi

    pcoefn = my_1
    pressure_limiter_loop: do n = 1, nedge_lim_eval

!     Get location of the nodes on opposite sides of the dual interface

      node1 = eptr(1,n)
      node2 = eptr(2,n)

!     Compute the normalized undivided pressure ratio and
!     pressure Laplacian based limiter

      pr_limiter_coeff_g = pr_limiter_coeff
      pr_limiter_coeff = fl_coeff(n)

      if ( eqn_set == generic_gas ) then
        c_gen = my_half*(qnode(n_sonic_k(1),node1) + qnode(n_sonic_k(1),node2))
        if ( ivisc >= 2 ) then
          mu1 = qnode(n_amu_k(1),node1)
          mu2 = qnode(n_amu_k(1),node2)
        else
          mu1 = my_0
          mu2 = my_0
        end if
      else
        c_gen = my_0
        if(ivisc >= 2)then
          xmr = xmach/re
          mu1   = viscosity_law(cstar,gamma*qnode(5,node1)/qnode(1,node1))*xmr
          mu2   = viscosity_law(cstar,gamma*qnode(5,node2)/qnode(1,node2))*xmr
        else
          mu1 = my_0
          mu2 = my_0
        end if
      end if
      mu = my_half*(mu1 + mu2)
      pcoef=pressure_limiter(x(node1),y(node1),z(node1),                       &
                             x(node2),y(node2),z(node2),                       &
                             xn(n),yn(n),zn(n),ra(n),vol(node1),vol(node2),    &
                             qnode(1:ndim,node1),qnode(1:ndim,node2),          &
                             gradx(n_etot,node1),grady(n_etot,node1),          &
                             gradz(n_etot,node1),gradx(n_etot,node2),          &
                             grady(n_etot,node2),gradz(n_etot,node2),          &
                             ndim,  eqn_set, mu, gm1, viscous_terms, powervi,  &
                             my_0,my_1,my_2,my_1,0,c_gen)
      pr_limiter_coeff = pr_limiter_coeff_g

!     Use the min of the limiter coefficients

      pcoefn(node1) = max(my_0, min(pcoef, pcoefn(node1)))
      pcoefn(node2) = max(my_0, min(pcoef, pcoefn(node2)))

    end do pressure_limiter_loop

!   Modify the limiter coefficients as a function of
!   the Mach no. and cell Reynolds no. at the nodes

    if (wall_limit_less) then

      mach_no_dependency_loop: do n = 1, nedge_lim_eval

        node1 = eptr(1,n)
        node2 = eptr(2,n)

!       Constrain the gradient and pressure limiter coeficients
!       in the subsonic portion of the flow

        if (nodemach(node1) < my_1) then
          machf1 = max(my_0,min(my_1,(nodemach(node1)-phi_min_mach)/dphi_mach))
          fmsmn1 = my_half*(cos(machf1*pi)+my_1)
          phi(:,node1) = max(fmsmn1**powerir, phi(:,node1))

          machf1 = max(my_0,min(my_1,(nodemach(node1)-plc_min_mach)/dplc_mach))
          pcofn1 = my_half*(cos(machf1*pi)+my_1)
          pcoefn(node1) = max(pcofn1, pcoefn(node1))
        end if

        if (nodemach(node2) < my_1) then
          machf2 = max(my_0,min(my_1,(nodemach(node2)-phi_min_mach)/dphi_mach))
          fmsmn2 = my_half*(cos(machf2*pi)+my_1)
          phi(:,node2) = max(fmsmn2**powerir, phi(:,node2))

          machf2 = max(my_0,min(my_1,(nodemach(node2)-plc_min_mach)/dplc_mach))
          pcofn2 = my_half*(cos(machf2*pi)+my_1)
          pcoefn(node2) = max(pcofn2, pcoefn(node2))
        end if

      end do mach_no_dependency_loop

    end if

!   Compute the composite limiter using the
!   min of the pressure and the gradient limiters

    composite_limiter_loop: do n = 1, nedge_lim_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      do k = 1, ndim
        phi(k,node1) = min(pcoefn(node1),phi(k,node1))
        phi(k,node2) = min(pcoefn(node2),phi(k,node2))
      end do

    end do composite_limiter_loop

!   Constrain the limiter coefficients when the cell Reynolds number is small

    if (wall_limit_less .and. ivisc >= 2) then
      cell_Re_no_dependency_loop: do n = 1, nedge_lim_eval

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        if ( eqn_set == generic_gas ) then
          rho1    = qnode(n_density,node1)
          a1      = sqrt(qnode(n_sonic_k(1),node1))
          mu1     = qnode(n_amu_k(1),node1)
        else
          rho1    = qnode(1,node1)
          press1  = qnode(5,node1)
          t1      = gamma*press1/rho1
          a1      = sqrt(t1)
          mu1     = viscosity_law( cstar, t1 )*xmr
        end if
        u1      = nodemach(node1)*a1
        diam1   = (my_fact1*vol(node1)/pi)**my_exp
        Re1     = rho1*u1*diam1/mu1
        vcoef1  = max(my_0, min(my_1, (Re1-Re_min)/dRe))
        vcoef1  = my_half*(cos(vcoef1*pi)+my_1)
        phi(1:ndim,node1) = max(vcoef1**powervr, phi(1:ndim,node1))

        if ( eqn_set == generic_gas ) then
          rho2    = qnode(n_density,node2)
          a2      = sqrt(qnode(n_sonic_k(1),node2))
          mu2     = qnode(n_amu_k(1),node2)
        else
          rho2    = qnode(1,node2)
          press2  = qnode(5,node2)
          t2      = gamma*press2/rho2
          a2      = sqrt(t2)
          mu2     = viscosity_law( cstar, t2 )*xmr
        end if
        u2      = nodemach(node2)*a2
        diam2   = (my_fact1*vol(node2)/pi)**my_exp
        Re2     = rho2*u2*diam2/mu2
        vcoef2  = max(my_0, min(my_1, (Re2-Re_min)/dRe))
        vcoef2  = my_half*(cos(vcoef2*pi)+my_1)
        phi(1:ndim,node2) = max(vcoef2**powervr, phi(1:ndim,node2))

      end do cell_Re_no_dependency_loop
    end if

! Enforce realizability based on the result of the reconstructed
! density and pressure

    real_recon_loop: do n = 1, nedge_lim_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

!     Compute the interface location of either the dual interface or the
!     mapped interface

      rni = r_vec(nnodes01, node1, node2, q_2d, mlsq, tf, cg_tol, x, y, z,     &
                  cgamma, slen, slenxn, slenyn, slenzn,                        &
                  nc_mapped_lsq, .true.)

      do k = 1, n_etot
        if(k>=n_momx .and. k<=n_momz)cycle
        gqr(k) = gradx(k,node1)*rni(1,1) +                                     &
                 grady(k,node1)*rni(2,1) +                                     &
                 gradz(k,node1)*rni(3,1)
        dq(k)  = qnode(k,node2) - qnode(k,node1)
        if (.not. new_umuscl) then
          dq(k) = (my_1-kappa_umuscl)*gqr(k) + kappa_umuscl*my_half*dq(k)
        else
          dq(k) = my_4th*((my_1-kappa_umuscl)*(my_4*gqr(k)-dq(k)) +            &
                          (my_1+kappa_umuscl)*dq(k))
        end if
        q1(k) = qnode(k,node1) + phi(k,node1)*dq(k)

        gqr(k) = gradx(k,node2)*rni(1,2) +                                     &
                 grady(k,node2)*rni(2,2) +                                     &
                 gradz(k,node2)*rni(3,2)
        dq(k)  = qnode(k,node1) - qnode(k,node2)
        if (.not. new_umuscl) then
          dq(k) = (my_1-kappa_umuscl)*gqr(k) + kappa_umuscl*my_half*dq(k)
        else
          dq(k) = my_4th*((my_1-kappa_umuscl)*(my_4*gqr(k)-dq(k)) +            &
                          (my_1+kappa_umuscl)*dq(k))
        end if
        q2(k) = qnode(k,node2) + phi(k,node2)*dq(k)
      end do

      if ( eqn_set == generic_gas ) then
        q_pos_def = min(minval(q1(1:n_species)),q1(n_etot),                    &
                        minval(q2(1:n_species)),q2(n_etot))
      else
        q_pos_def = min(q1(1),q1(n_etot),q2(1),q2(n_etot))
      end if
      if ( node1 <= nnodes0) then
        if ( q_pos_def < my_0 ) phi(1:ndim,node1) = my_0
      end if
      if ( node2 <= nnodes0 ) then
        if ( q_pos_def < my_0 ) phi(1:ndim,node2) = my_0
      end if

    end do real_recon_loop

! Now if we want to freeze the limiter, evaluate the reconstruction using the
! original limiter.  If the reconstruction is successful, keep the original
! phi.  However, if it fails, use the new value of phi computed above.  In
! this way, we hope that the limiter will be frozen throughout the majority
! of the field, while it adjusts maybe occasionally here or there to keep the
! reconstruction from failing.

    frozen_limiter: if ( freeze_limiter_iteration >=0 .and. &
                         ntt > freeze_limiter_iteration ) then

      call test_reconstruct_frozen_lim(nnodes0,nnodes01,nedgeloc_2d,nedgeloc,  &
                                       eptr,x,y,z,gradx,grady,gradz,qnode,     &
                                       vol,phi_original,phi,n_grd,n_tot,ndim,  &
                                     cgamma, slenxn, slenyn, slenzn, slen, jag )

! Now load phi with the original values, except for the ones
! that were reevaluated above

      phi = phi_original

    end if frozen_limiter
    deallocate(gqr)
    deallocate(dq)
    deallocate(q1)
    deallocate(q2)

  end subroutine gsblim


!=================================== VENLIM ==================================80
!
! Venkat's flux Limiter
!
!=============================================================================80
  subroutine venlim(nnodes0,nnodes01,nedgeloc,qnode,phi,                       &
                    gradx,grady,gradz,x,y,z,vol,eptr,nedgeloc_2d,n_grd,        &
                    n_tot,ndim,nbound,bc,                                      &
                    cgamma, slenxn, slenyn, slenzn, slen, jag)

    use inviscid_flux,        only : first_order_iterations,                   &
                                     freeze_limiter_iteration
    use info_depr,            only : ntt, twod, new_umuscl, epscoef
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use twod_util,            only : q_2d
    use lsq_constants,        only : mlsq, tf, cg_tol
    use lsq_defs,             only : nc_mapped_lsq

    integer,                              intent(in)    :: nnodes0, nnodes01
    integer,                              intent(in)    :: nedgeloc
    integer,                              intent(in)    :: nedgeloc_2d
    integer,                              intent(in)    :: n_grd, n_tot
    integer,                              intent(in)    :: nbound, ndim
    integer, dimension(2,nedgeloc),       intent(in)    :: eptr
    integer, dimension(:),                intent(in)    :: jag
    type(bcgrid_type), dimension(nbound), intent(in)    :: bc
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_grd,nnodes01),  intent(inout) :: phi
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx,grady,gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(nnodes01),        intent(in)    :: cgamma
    real(dp), dimension(nnodes01),        intent(in)    :: slenxn
    real(dp), dimension(nnodes01),        intent(in)    :: slenyn
    real(dp), dimension(nnodes01),        intent(in)    :: slenzn
    real(dp), dimension(nnodes01),        intent(in)    :: slen

    ! temporary automatic arrays to form limiter and
    ! saved copy of limiter to permit freezing
    real(dp), dimension(:,:), allocatable :: qmin,qmax,phi_original

    integer :: i, n, my_ntt, node1, node2, nedge_lim_eval

    real(dp) :: pi, diam, eps, proj, sproj
    real(dp) :: dm, delp, gmx, f1, gmn, f2, gm
    real(dp) :: my_exp, my_fact1, my_fact2

    real(dp), dimension(3,2) :: rni

    real(dp), parameter :: my_haf   = 0.5_dp
    real(dp), parameter :: my_1     = 1.0_dp
    real(dp), parameter :: my_2     = 2.0_dp
    real(dp), parameter :: my_3     = 3.0_dp
    real(dp), parameter :: my_4     = 4.0_dp
    real(dp), parameter :: my_6     = 6.0_dp
    real(dp), parameter :: my_3rd   = my_1/my_3
    real(dp), parameter :: my_1em12 = 1.0e-12_dp

    continue

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

! store the current value of the limiter
! if we're going to potentially freeze it

    set_up_to_freeze_limiter : if ( freeze_limiter_iteration >= 0 ) then
      allocate(phi_original(n_grd,nnodes01))
      phi_original = phi
    endif set_up_to_freeze_limiter

! 2D vs 3D parameters

    if (twod) then
      nedge_lim_eval = nedgeloc_2d
      my_exp   = my_haf
      my_fact1 = my_4
      my_fact2 = my_1
    else
      nedge_lim_eval = nedgeloc
      my_exp   = my_3rd
      my_fact1 = my_6
      my_fact2 = my_3
    end if

! First loop over the edges and find the maximum and minimum

    phi(1:ndim,1:nnodes01) = my_1

    if (my_ntt < first_order_iterations) then
       if (freeze_limiter_iteration >= 0) deallocate(phi_original)
       return
    end if

    allocate(qmin(ndim,nnodes01))
    qmin(1:ndim,1:nnodes01) = qnode(1:ndim,1:nnodes01)
    allocate(qmax(ndim,nnodes01))
    qmax(1:ndim,1:nnodes01) = qmin

    scan_edges_1 : do n = 1, nedge_lim_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! First check node1

      qmax(1:ndim,node1) = max(qmax(1:ndim,node1),qnode(1:ndim,node2))
      qmin(1:ndim,node1) = min(qmin(1:ndim,node1),qnode(1:ndim,node2))

! Now for node2

      qmax(1:ndim,node2) = max(qmax(1:ndim,node2),qnode(1:ndim,node1))
      qmin(1:ndim,node2) = min(qmin(1:ndim,node2),qnode(1:ndim,node1))

    end do scan_edges_1

    do n = 1,nbound
      call q_minmax_symmetry( bc(n)%ibc, bc(n)%nbnode, bc(n)%ibnode,         &
        nnodes01, ndim, qmin, qmax )
    enddo

! Now we have found the max and min of surrounding nodes
! so let do the extrapolation to the face and "limit"
! the gradient so no new extrema are produced

    pi = acos(-1.0_dp)

    scan_edges_2 : do n = 1, nedge_lim_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Compute the interface location of either the dual interface or the
! mapped interface

      rni = r_vec(nnodes01, node1, node2, q_2d, mlsq, tf, cg_tol, x, y, z, &
                  cgamma, slen, slenxn, slenyn, slenzn,                    &
                  nc_mapped_lsq, .true.)

! First do node 2
! estimate the local grid size


!     Compute the interface location of either the dual interface or the
!     mapped interface

      loop_elements : do i = 1,ndim

        diam = (my_fact1*vol(node2)/pi)**my_exp
        eps  = (my_fact2*diam)**3 * epscoef
        proj  = gradx(i,node2)*rni(1,2) + grady(i,node2)*rni(2,2) +          &
                gradz(i,node2)*rni(3,2)
        if (new_umuscl) proj = my_4*proj - (qnode(i,node1)-qnode(i,node2))
        sproj = sign(my_1,proj)
        dm    = sproj*(abs(proj) + my_1em12)
        delp  = qmax(i,node2) - qnode(i,node2)
        gmx   = ((delp**2 +eps)*dm+my_2*dm**2*delp)/(delp**2+my_2*dm**2 +    &
                dm*delp+eps)
        f1    = gmx/dm
        delp  = qmin(i,node2)-qnode(i,node2)
        gmn   = ((delp**2+eps)*dm+my_2*dm**2*delp)/(delp**2+my_2*dm**2 +     &
                dm*delp+eps)
        f2    = gmn/dm
        gm    = ((sproj+my_1)*f1 + (my_1-sproj)*f2)*my_haf

        phi(i,node2) = min(phi(i,node2),gm)

! Now do the other node

        diam  = (my_fact1*vol(node1)/pi)**my_exp
        eps   = (my_fact2*diam)**3 * epscoef

        proj  = gradx(i,node1)*rni(1,1) + grady(i,node1)*rni(2,1) +          &
                gradz(i,node1)*rni(3,1)
        if (new_umuscl) proj = my_4*proj - (qnode(i,node2)-qnode(i,node1))
        sproj = sign(my_1,proj)
        dm    = sproj*(abs(proj) + my_1em12)
        delp  = qmax(i,node1) - qnode(i,node1)
        gmx   = ((delp**2+eps)*dm+my_2*dm**2*delp)/(delp**2+my_2*dm**2 +     &
                dm*delp+eps)
        f1    = gmx/dm
        delp  = qmin(i,node1)-qnode(i,node1)
        gmn   = ((delp**2+eps)*dm+my_2*dm**2*delp)/(delp**2+my_2*dm**2 +     &
                dm*delp+eps)
        f2    = gmn/dm
        gm    = ((sproj+my_1)*f1 + (my_1-sproj)*f2)*my_haf

        phi(i,node1) = min(phi(i,node1),gm)

      end do loop_elements

    end do scan_edges_2

    deallocate(qmin,qmax)

! Now if we want to freeze the limiter, evaluate the reconstruction using the
! original limiter.  If the reconstruction is successful, keep the original
! phi.  However, if it fails, use the new value of phi computed above.  In
! this way, we hope that the limiter will be frozen throughout the majority
! of the field, while it adjusts maybe occasionally here or there to keep the
! reconstruction from failing.

    set_phi : if ( freeze_limiter_iteration >=0 .and. &
                   ntt > freeze_limiter_iteration ) then

      call test_reconstruct_frozen_lim(nnodes0,nnodes01,nedgeloc_2d,nedgeloc,  &
                                       eptr,x,y,z,gradx,grady,gradz,qnode,     &
                                       vol,phi_original,phi,n_grd,n_tot,ndim,  &
                                     cgamma, slenxn, slenyn, slenzn, slen, jag )

! Now load phi with the original values, except for the ones
! that were reevaluated above

      phi = phi_original
    endif set_phi
    if (freeze_limiter_iteration >=0) deallocate(phi_original)

  end subroutine venlim


!===================== TEST_RECONSTRUCTION_WITH_FROZEN_LIM ===================80
!
!  Test reconstruction with frozen limiter.  If reconstruction fails, replace
!  local values with updated limiter.
!
!=============================================================================80
  subroutine test_reconstruct_frozen_lim(nnodes0,nnodes01,nedgeloc_2d,nedgeloc,&
                                         eptr,x,y,z,gradx,grady,gradz,qnode,   &
                                         vol,phi,phi_new,n_grd,n_tot,ndim,     &
                                     cgamma, slenxn, slenyn, slenzn, slen, jag )

    use kinddefs,      only : dp
    use lmpi,          only : lmpi_reduce, lmpi_bcast, lmpi_master
    use inviscid_flux, only : flux_construction

    integer,                             intent(in)    :: nnodes0, nnodes01
    integer,                             intent(in)    :: nedgeloc_2d, nedgeloc
    integer,                             intent(in)    :: n_grd, n_tot, ndim
    integer,  dimension(2,nedgeloc),     intent(in)    :: eptr
    integer,  dimension(:),              intent(in)    :: jag
    real(dp), dimension(n_grd,nnodes01), intent(inout) :: phi
    real(dp), dimension(n_grd,nnodes01), intent(in)    :: phi_new
    real(dp), dimension(nnodes01),       intent(in)    :: x, y, z
    real(dp), dimension(n_grd,nnodes01), intent(in)    :: gradx, grady, gradz
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(nnodes01),       intent(in)    :: vol
    real(dp), dimension(nnodes01),       intent(in)    :: cgamma
    real(dp), dimension(nnodes01),       intent(in)    :: slenxn
    real(dp), dimension(nnodes01),       intent(in)    :: slenyn
    real(dp), dimension(nnodes01),       intent(in)    :: slenzn
    real(dp), dimension(nnodes01),       intent(in)    :: slen

    ! automatic array to mark nodes that require the phi_new limiter
    logical, dimension(nnodes0) :: node_tag

    integer :: i, bad_node_sum, global_bad_node_sum

  continue

! Reset the node tag to false

    node_tag = .false.

! Test the higher order reconstruction

    select case ( flux_construction )
    case ('vanleer','roe','dldfss','hllc','aufs')
      call test_frozen_limiter(nnodes0,nnodes01,nedgeloc_2d,nedgeloc,          &
                               eptr,x,y,z,gradx,grady,gradz,qnode,             &
                               vol,phi,node_tag,n_grd,n_tot,ndim,              &
                               cgamma, slenxn, slenyn, slenzn, slen, jag )

    case default
      write(*,*) 'Uncoded option in test_reconstruct_frozen_limiter'
      write(*,*) ' ...flux_construction=',flux_construction
      call lmpi_conditional_stop(1,'test_reconstruct_frozen_lim')
    end select

! Count up the number of nodes that went "bad" and reset their limiter

    bad_node_sum = 0
    global_bad_node_sum = 0

    do i = 1, nnodes0
      if ( node_tag(i) ) then
        bad_node_sum = bad_node_sum + 1
        phi(1:ndim,i) = phi_new(1:ndim,i)
        phi(1:ndim,i) = phi_new(1:ndim,i)
      endif
    end do

    call lmpi_reduce(bad_node_sum, global_bad_node_sum)
    call lmpi_bcast(global_bad_node_sum)

    if ( lmpi_master .and. global_bad_node_sum > 0 ) then
      write(*,*) 'This many nodes needed their limiter replaced: ',            &
                 global_bad_node_sum
    endif

  end subroutine test_reconstruct_frozen_lim

!=============================== TEST_FROZEN_LIMITER =========================80
!
!  Test higher order reconstructed states with frozen limiter
!
!=============================================================================80
  subroutine test_frozen_limiter(nnodes0,nnodes01,nedgeloc_2d,nedgeloc,        &
                                 eptr,x,y,z,gradx,grady,gradz,qnode,           &
                                 vol,phi,node_tag,n_grd,n_tot,ndim,            &
                                 cgamma, slenxn, slenyn, slenzn, slen, jag )

    use kinddefs,             only : dp
    use fun3d_constants,      only : my_0, my_3rd, my_1, my_3, my_4, my_6, pi
    use info_depr,            only : ntt, twod
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations, iflim
    use twod_util,            only : q_2d
    use lsq_constants,        only : mlsq, tf, cg_tol
    use lsq_defs,             only : nc_mapped_lsq

    integer,                             intent(in)    :: nnodes0, nnodes01
    integer,                             intent(in)    :: nedgeloc_2d, nedgeloc
    integer,                             intent(in)    :: n_grd, n_tot, ndim
    integer,  dimension(2,nedgeloc),     intent(in)    :: eptr
    integer,  dimension(:),              intent(in)    :: jag

    real(dp), dimension(n_grd,nnodes01), intent(in)    :: phi
    real(dp), dimension(nnodes01),       intent(in)    :: x, y, z
    real(dp), dimension(n_grd,nnodes01), intent(in)    :: gradx,grady,gradz
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(nnodes01),       intent(in)    :: vol
    real(dp), dimension(nnodes01),       intent(in)    :: cgamma
    real(dp), dimension(nnodes01),       intent(in)    :: slenxn
    real(dp), dimension(nnodes01),       intent(in)    :: slenyn
    real(dp), dimension(nnodes01),       intent(in)    :: slenzn
    real(dp), dimension(nnodes01),       intent(in)    :: slen
    logical,  dimension(nnodes0),        intent(inout) :: node_tag

    integer :: n, my_ntt, nedge_flux_eval, node1, node2

    real(dp) :: my_exp, my_fact1, my_fact2, diam1, diam2, eps1, eps2

    real(dp), dimension(3,2) :: rni
    real(dp), dimension(ndim+1)   :: ql, qr

    logical :: second
    integer :: central1, central2

  continue

! Loop over edges and test the reconstruction using the frozen limiter values

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

    second = .true.
    if (my_ntt <= first_order_iterations) second = .false.

    my_exp   = my_3rd
    my_fact1 = my_6
    my_fact2 = my_3
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    else
      nedge_flux_eval = nedgeloc
      my_fact1 = my_4
      my_fact2 = my_1
    end if

!   Loop over all the edges
!   (in the 2D case, this loop contains edges on only one y=constant plane)

    edge_loop: do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

!     Compute the smooth limiter epsilons if needed

      eps1 = my_1
      eps2 = my_1
      if ((iflim == 15) .or. (iflim == 16) .or. (iflim == 17)) then
        diam1 = (my_fact1*vol(node1)/pi)**my_exp
        diam2 = (my_fact1*vol(node2)/pi)**my_exp
        eps1  = (my_fact2*diam1)**3
        eps2  = (my_fact2*diam2)**3
      end if

!     Compute the interface location of either the dual interface or the
!     mapped interface

      rni = r_vec(nnodes01, node1, node2, q_2d, mlsq, tf, cg_tol, x, y, z,     &
                  cgamma, slen, slenxn, slenyn, slenzn,                        &
                  nc_mapped_lsq, .true.)

      central1 = 0
      central2 = 0
      if ( jag(node1) /= 0 .and. second ) central1 = 1
      if ( jag(node2) /= 0 .and. second ) central2 = 1

!     Compute the reconstructed primitive variables at the interface

      ql = qf(rni(1,1), rni(2,1), rni(3,1),                                    &
              gradx(1:ndim,node1), grady(1:ndim,node1), gradz(1:ndim,node1),   &
              qnode(1:ndim,node1), qnode(1:ndim,node2),                        &
              central1, phi(1:ndim,node1), eps1, second, ndim)

      qr = qf(rni(1,2), rni(2,2), rni(3,2),                                    &
              gradx(1:ndim,node2), grady(1:ndim,node2), gradz(1:ndim,node2),   &
              qnode(1:ndim,node2), qnode(1:ndim,node1),                        &
              central2, phi(1:ndim,node2), eps2, second, ndim)

!     Check for the occurance of reconstruction realizability violations
!     If they occur then tag the node for re-evaluation of the limiter

      if ( node1 <= nnodes0 ) then
        if ( (ql(ndim+1) /= my_0) .or. (qr(ndim+1) /= my_0) )                  &
              node_tag(node1) = .true.
      end if
      if ( node2 <= nnodes0 ) then
        if ( (ql(ndim+1) /= my_0) .or. (qr(ndim+1) /= my_0) )                  &
              node_tag(node2) = .true.
      end if

    end do edge_loop

  end subroutine test_frozen_limiter

!==================================== NO_LIM =================================80
!
! Initialize flux limiter to no limits
!
!=============================================================================80
  subroutine no_lim( nnodes01, phi, n_grd )

    integer,                              intent(in)  :: nnodes01, n_grd
    real(dp),  dimension(n_grd,nnodes01), intent(out) :: phi

    real(dp), parameter :: my_1 = 1.0_dp

  continue

    phi(:,:) = my_1

  end subroutine no_lim

!============================ FIND_PSEUDO_EDGES ==============================80
!
!  Constructs pseudo-edges for non-simply connected nodes
!
!=============================================================================80
  subroutine find_pseudo_edges(nnodes01, nnz01, nedgeloc, ia, ja, eptr)

    use allocations, only : my_alloc_ptr

    integer,                        intent(in) :: nnodes01, nnz01, nedgeloc
    integer, dimension(nnodes01+1), intent(in) :: ia
    integer, dimension(nnz01),      intent(in) :: ja
    integer, dimension(2,nedgeloc), intent(in) :: eptr

    integer :: i, j, k, node1, node2, jstart, jend, jhold, kstart, kend, khold
    integer :: nedge_ns_test, ns_node

    integer, dimension(:), pointer :: ia_work, ja_work

  continue

    call my_alloc_ptr(ia_work, nnodes01+1)
    call my_alloc_ptr(ja_work, nnz01)

! Make copies of ia,ja that we can fiddle with

    do i = 1, nnodes01+1
      ia_work(i) = ia(i)
    end do

    do i = 1, nnz01
      ja_work(i) = abs( ja(i) )
    end do

! Go through the simple edges and set corresponding elements of ja to zero

    do i = 1, nedgeloc

      node1 = eptr(1,i)
      node2 = eptr(2,i)

! Knock out node1's offdiagonal

      jstart = ia_work(node1)
      jend   = ia_work(node1+1)-1
      jhold = -99
      search_row1 : do j = jstart, jend
        if ( ja_work(j) == node2 ) then
          jhold = j
          exit search_row1
        endif
      end do search_row1
      if ( jhold == -99 ) then
        write(*,*) 'Failed to find offdiagonal for node1 in sumgs'
        call lmpi_conditional_stop(1)
      else
        ja_work(jhold) = 0
      endif

! Knock out node2's offdiagonal

      jstart = ia_work(node2)
      jend   = ia_work(node2+1)-1
      jhold = -99
      search_row2 : do j = jstart, jend
        if ( ja_work(j) == node1 ) then
          jhold = j
          exit search_row2
        endif
      end do search_row2
      if ( jhold == -99 ) then
        write(*,*) 'Failed to find offdiagonal for node2 in sumgs'
        call lmpi_conditional_stop(1)
      else
        ja_work(jhold) = 0
      endif
    end do

! Now the remaining nonzero entries in ja_work are due to non-simple connections
! Using this, we can determine the number of edges.  The number of edges SHOULD
! turn out to be exactly half the number of nonzero entries remaining.

    nedge_ns_test = 0
    do i = 1, nnodes01
      jstart = ia_work(i)
      jend   = ia_work(i+1)-1
      do j = jstart, jend
        if ( ja_work(j) > 0 ) nedge_ns_test = nedge_ns_test + 1
      end do
    end do
    nedge_ns_test = nedge_ns_test / 2

! Count em the hard way too

    do i = 1, nnodes01
      jstart = ia_work(i)
      jend   = ia_work(i+1)-1
      do j = jstart, jend
        if ( ja_work(j) > 0 ) then
          ns_node = ja_work(j)
          kstart = ia_work(ns_node)
          kend   = ia_work(ns_node+1)-1
          khold = -99
          search_k1 : do k = kstart, kend
            if ( ja_work(k) == i ) then
              khold = k
              exit search_k1
            endif
          end do search_k1
          if ( khold == -99 ) then
            write(*,*) 'Failed to find matching entry1 in sumgs'
            call lmpi_conditional_stop(1)
          else
            nedge_ns = nedge_ns + 1
            ja_work(j)     = -ja_work(j)
            ja_work(khold) = -ja_work(khold)
          endif
        endif
      end do
    end do

! Make sure there aren't any positive ja_work's left

    do i = 1, nnz01
      if ( ja_work(i) > 0 ) then
        write(*,*) 'Orphan edge in sumgs'
        call lmpi_conditional_stop(1)
      endif
    end do

! Compare the two totals

    if ( nedge_ns_test /= nedge_ns ) then
      write(*,*) 'Two tests did not match in sumgs'
      call lmpi_conditional_stop(1)
    endif

! Allocate the pseudo-edge pointers and set the ja_work's
! back to positive

    call my_alloc_ptr(eptr_ns, 2, nedge_ns)

    ja_work = -ja_work

! Now go back and store the edge pointers this time

    nedge_ns = 0

    do i = 1, nnodes01
      jstart = ia_work(i)
      jend   = ia_work(i+1)-1
      do j = jstart, jend
        if ( ja_work(j) > 0 ) then
          ns_node = ja_work(j)
          kstart = ia_work(ns_node)
          kend   = ia_work(ns_node+1)-1
          khold = -99
          search_k2 : do k = kstart, kend
            if ( ja_work(k) == i ) then
              khold = k
              exit search_k2
            endif
          end do search_k2
          if ( khold == -99 ) then
            write(*,*) 'Failed to find matching entry2 in sumgs'
            call lmpi_conditional_stop(1)
          else
            nedge_ns = nedge_ns + 1
            eptr_ns(1,nedge_ns) = i
            eptr_ns(2,nedge_ns) = ns_node
            ja_work(j)     = -ja_work(j)
            ja_work(khold) = -ja_work(khold)
          endif
        endif
      end do
    end do

! Make sure there aren't any positive ja_work's left

    do i = 1, nnz01
      if ( ja_work(i) > 0 ) then
        write(*,*) 'Orphan edge in sumgs'
        call lmpi_conditional_stop(1)
      endif
    end do

! Compare the two totals once more

    if ( nedge_ns_test /= nedge_ns ) then
      write(*,*) 'Two tests did not match in sumgs second time through'
      call lmpi_conditional_stop(1)
    endif

! That should do it for our new pseudo edge pointers

    deallocate(ia_work,ja_work)

  end subroutine find_pseudo_edges


!================================= GREEN_GAUSS ===============================80
!
! Calculates gradients using Green-Gauss
!
!=============================================================================80
  subroutine green_gauss(turb_grad_flag,                                       &
                         nnodes0, nnodes01, nedgeloc, eptr, qnode, x, y, z,    &
                         xn, yn, zn, ra, vol, gradx, grady, gradz, nbound, bc, &
                         nedgeloc_2d, node_pairs_2d, nnodes0_2d, turb, n_turb, &
                         nelem, elem, n_tot, n_grd, eqn_set, ndim)

    use info_depr,      only : twod
    use bc_names,       only : bc_ignore_2d
    use element_types,  only : elem_type
    use solution_types, only : generic_gas

    integer,                              intent(in)  :: turb_grad_flag
    integer,                              intent(in)  :: nnodes0, nnodes01
    integer,                              intent(in)  :: nedgeloc, n_turb
    integer,                              intent(in)  :: n_tot, n_grd
    integer,                              intent(in)  :: nedgeloc_2d
    integer,                              intent(in)  :: nelem
    integer,                              intent(in)  :: eqn_set, ndim
    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    integer,                              intent(in)  :: nnodes0_2d
    integer,  dimension(2,nnodes0_2d),    intent(in)  :: node_pairs_2d
    real(dp), dimension(n_tot,nnodes01),  intent(in)  :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)  :: turb
    real(dp), dimension(n_grd,nnodes01),  intent(out) :: gradx,grady,gradz
    real(dp), dimension(nnodes01),        intent(in)  :: x, y, z
    real(dp), dimension(nedgeloc),        intent(in)  :: xn, yn, zn, ra
    real(dp), dimension(nnodes01),        intent(in)  :: vol
    integer,                              intent(in)  :: nbound
    type(bcgrid_type), dimension(nbound), intent(in)  :: bc
    type(elem_type),   dimension(nelem),  intent(in)  :: elem

    integer :: i, ib, j, n, node1, node2, n_vec, nqq

    integer :: bc_closure = 0  ! 0...face-based closure
                               ! 1...node-based closure
                               ! 0 is better at corners

    real(dp), dimension(n_grd) :: qavg

    real(dp) :: area, xnorm, ynorm, znorm

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_haf = 0.5_dp

  continue

    if ( eqn_set == generic_gas ) then
      n_vec = ndim
      nqq   = ndim
    else
      n_vec = n_tot
      nqq   = n_grd
    end if

! zero out the gradients

    gradx(:,:) = my_0
    grady(:,:) = my_0
    gradz(:,:) = my_0

    twod_mode : if (twod) then

      edge_loop_2d : do n = 1, nedgeloc_2d

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        area  = ra(n)
        xnorm = area * xn(n)
        znorm = area * zn(n)

        qavg(1:n_vec) = my_haf * (qnode(1:n_vec,node2)+qnode(1:n_vec,node1))

!       turb gradients if needed

        if ( turb_grad_flag > 0 ) then

            do j=1,n_turb
              qavg(n_tot+j) = my_haf * (turb(j,node2)+turb(j,node1))
            end do

        end if

        if (node1 <= nnodes0) then
          gradx(1:nqq,node1) = gradx(1:nqq,node1) + xnorm*qavg(1:nqq)
          gradz(1:nqq,node1) = gradz(1:nqq,node1) + znorm*qavg(1:nqq)
        end if

        if (node2 <= nnodes0) then
          gradx(1:nqq,node2) = gradx(1:nqq,node2) - xnorm*qavg(1:nqq)
          gradz(1:nqq,node2) = gradz(1:nqq,node2) - znorm*qavg(1:nqq)
        end if

      end do edge_loop_2d

    else twod_mode

      edge_loop_3d : do n = 1, nedgeloc

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        area  = ra(n)
        xnorm = area * xn(n)
        ynorm = area * yn(n)
        znorm = area * zn(n)

        qavg(1:n_vec) = my_haf * (qnode(1:n_vec,node2)+qnode(1:n_vec,node1))

!       turb gradients if needed

        if ( turb_grad_flag > 0 ) then

            do j=1,n_turb
              qavg(n_tot+j) = my_haf * (turb(j,node2)+turb(j,node1))
            end do

        end if

        if (node1 <= nnodes0) then
          gradx(1:nqq,node1) = gradx(1:nqq,node1) + xnorm*qavg(1:nqq)
          grady(1:nqq,node1) = grady(1:nqq,node1) + ynorm*qavg(1:nqq)
          gradz(1:nqq,node1) = gradz(1:nqq,node1) + znorm*qavg(1:nqq)
        end if

        if (node2 <= nnodes0) then
          gradx(1:nqq,node2) = gradx(1:nqq,node2) - xnorm*qavg(1:nqq)
          grady(1:nqq,node2) = grady(1:nqq,node2) - ynorm*qavg(1:nqq)
          gradz(1:nqq,node2) = gradz(1:nqq,node2) - znorm*qavg(1:nqq)
        end if

      end do edge_loop_3d

    end if twod_mode

    do ib = 1,nbound

      if (twod .and. bc_ignore_2d(bc(ib)%ibc)) cycle

!     choose between closing off boundaries with face-based method or
!     node-based method

      if (bc_closure == 0) then
        call bc_green_gauss(turb_grad_flag,                                    &
                            nnodes0,nnodes01,qnode,x,y,z,gradx,grady,gradz,    &
                            bc(ib)%nbnode,bc(ib)%ibnode,bc(ib)%nbfacet,        &
                            bc(ib)%f2ntb,bc(ib)%nbfaceq,bc(ib)%f2nqb,turb,     &
                            n_turb,nelem,elem,n_tot,n_grd,n_vec,nqq)
      else
        call bc_green_gauss2(turb_grad_flag,                                   &
                             nnodes0,nnodes01,qnode,gradx,grady,gradz,         &
                             bc(ib)%nbnode,bc(ib)%ibnode,bc(ib)%bxn,bc(ib)%byn,&
                             bc(ib)%bzn,turb,n_turb,n_tot,n_grd,n_vec,nqq)
      end if

    end do

    if (twod) then

      do i = 1,nnodes0_2d

        node1 = node_pairs_2d(1,i)
        node2 = node_pairs_2d(2,i)

        gradx(1:nqq,node1) = gradx(1:nqq,node1) / vol(node1)
        gradz(1:nqq,node1) = gradz(1:nqq,node1) / vol(node1)

        gradx(1:nqq,node2) = gradx(1:nqq,node1)
        gradz(1:nqq,node2) = gradz(1:nqq,node1)

      end do

    else

      do i = 1,nnodes01
        gradx(1:nqq,i) = gradx(1:nqq,i) / vol(i)
        grady(1:nqq,i) = grady(1:nqq,i) / vol(i)
        gradz(1:nqq,i) = gradz(1:nqq,i) / vol(i)
      end do

    end if

  end subroutine green_gauss

!=============================== BC_GREEN_GAUSS ==============================80
!
! This routine closes off the boundaries for the Green-Gauss gradients
! (face based variant)
!
!=============================================================================80
  subroutine bc_green_gauss(turb_grad_flag,                                    &
                            nnodes0, nnodes01, qnode, x, y, z, gradx, grady,   &
                            gradz, nbnode, ibnode, nbfacet, f2ntb, nbfaceq,    &
                            f2nqb, turb, n_turb, nelem,elem,n_tot,n_grd,       &
                            n_vec, nqq)

    use info_depr,     only : twod
    use grid_metrics,  only : dual_area_quad
    use element_types, only : elem_type
    use nml_noninertial_reference_frame, only : noninertial

    integer,                              intent(in)    :: turb_grad_flag
    integer,                              intent(in)    :: nbnode, n_tot
    integer,                              intent(in)    :: n_grd, n_vec, nqq
    integer,                              intent(in)    :: nbfacet, nbfaceq
    integer,                              intent(in)    :: nnodes0, nnodes01
    integer,                              intent(in)    :: nelem, n_turb
    integer,  dimension(nbnode),          intent(in)    :: ibnode
    integer,  dimension(nbfacet,5),       intent(in)    :: f2ntb
    integer,  dimension(nbfaceq,6),       intent(in)    :: f2nqb
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_grd,nnodes01),  intent(inout) :: gradx, grady, gradz
    type(elem_type), dimension(nelem),    intent(in)    :: elem

    integer :: j, n, node1, node2, node3, node4, neighbor, ielem

    character(len=3) :: element_type

    real(dp) :: c56, c16

    real(dp) :: ax, ay, az, bx, by, bz

    real(dp), dimension(n_grd,4) :: qnd
    real(dp), dimension(n_grd)   :: qavg

    real(dp) :: x1, x2, x3, y1, y2, y3, z1, z2, z3

    real(dp) :: xnorm, ynorm, znorm

    real(dp), dimension(4) :: xnorm_q, ynorm_q, znorm_q

    real(dp), parameter :: my_haf = 0.5_dp
    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_3   = 3.0_dp
    real(dp), parameter :: my_6   = 6.0_dp
    real(dp), parameter :: my_8   = 8.0_dp
    real(dp), parameter :: c68    = my_6/my_8
    real(dp), parameter :: c18    = my_1/my_8
    real(dp), parameter :: my_5   = 5.0_dp

  continue

    twod_mode : if (twod) then

!     close off gradient evaluation on triangular faces

      loop_tris_1 : do n = 1, nbfacet

        node1 = ibnode(f2ntb(n,1))
        node2 = ibnode(f2ntb(n,2))
        node3 = ibnode(f2ntb(n,3))

        qnd(1:n_vec,1) = qnode(1:n_vec,node1)
        qnd(1:n_vec,2) = qnode(1:n_vec,node2)
        qnd(1:n_vec,3) = qnode(1:n_vec,node3)

!       turb gradients if needed

        if ( turb_grad_flag > 0 ) then

            do j=1,n_turb
              qnd(n_tot+j,1) = turb(j,node1)
              qnd(n_tot+j,2) = turb(j,node2)
              qnd(n_tot+j,3) = turb(j,node3)
            end do

        end if

!       norm points away from grid interior
!       norm magnitude is 1/3 of surface triangle area
!       and is the same at all 3 nodes

        x1 = x(node1)
        y1 = y(node1)
        z1 = z(node1)

        x2 = x(node2)
        y2 = y(node2)
        z2 = z(node2)

        x3 = x(node3)
        y3 = y(node3)
        z3 = z(node3)

        ax = x2 - x1
        ay = y2 - y1
        az = z2 - z1

        bx = x3 - x1
        by = y3 - y1
        bz = z3 - z1

        xnorm = -my_haf*(ay*bz-az*by)/my_3
        znorm = -my_haf*(ax*by-ay*bx)/my_3

        qavg(1:nqq) = c68*qnd(1:nqq,1) + c18*(qnd(1:nqq,2)+qnd(1:nqq,3))

        if (node1 <= nnodes0) then
          gradx(1:nqq,node1) = gradx(1:nqq,node1) + xnorm*qavg(1:nqq)
          gradz(1:nqq,node1) = gradz(1:nqq,node1) + znorm*qavg(1:nqq)
        end if

        qavg(1:nqq) = c68*qnd(1:nqq,2) + c18*(qnd(1:nqq,1)+qnd(1:nqq,3))

        if (node2 <= nnodes0) then
          gradx(1:nqq,node2) = gradx(1:nqq,node2) + xnorm*qavg(1:nqq)
          gradz(1:nqq,node2) = gradz(1:nqq,node2) + znorm*qavg(1:nqq)
        end if

        qavg(1:nqq) = c68*qnd(1:nqq,3) + c18*(qnd(1:nqq,1)+qnd(1:nqq,2))

        if (node3 <= nnodes0) then
          gradx(1:nqq,node3) = gradx(1:nqq,node3) + xnorm*qavg(1:nqq)
          gradz(1:nqq,node3) = gradz(1:nqq,node3) + znorm*qavg(1:nqq)
        end if

      end do loop_tris_1

!     close off gradient evaluation on quadrilateral faces

      loop_quads_1 : do n = 1, nbfaceq

        node1 = ibnode(f2nqb(n,1))
        node2 = ibnode(f2nqb(n,2))
        node3 = ibnode(f2nqb(n,3))
        node4 = ibnode(f2nqb(n,4))

        qnd(1:n_vec,1) = qnode(1:n_vec,node1)
        qnd(1:n_vec,2) = qnode(1:n_vec,node2)
        qnd(1:n_vec,3) = qnode(1:n_vec,node3)
        qnd(1:n_vec,4) = qnode(1:n_vec,node4)

!       turb gradients if needed

        if ( turb_grad_flag > 0 ) then

            do j=1,n_turb
              qnd(n_tot+j,1) = turb(j,node1)
              qnd(n_tot+j,2) = turb(j,node2)
              qnd(n_tot+j,3) = turb(j,node3)
              qnd(n_tot+j,4) = turb(j,node4)
            end do

        end if

!       get dual norm contributions at each node of the quad face

        call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,noninertial,&
                            xnorm_q,ynorm_q,znorm_q)

        ielem        = f2nqb(n,6)
        element_type = elem(ielem)%type_cell

        if(element_type == 'prz') then
          c56 = my_5/my_6
          c16 = my_1/my_6
        else
          c56 = my_1
          c16 = my_0
        endif

!       Find neighbor of node 1

        neighbor = 2
        if(abs(y(node3)-yplane_2d) < y_coplanar_tol) neighbor = 3
        if(abs(y(node4)-yplane_2d) < y_coplanar_tol) neighbor = 4

        qavg(1:nqq) = c56*qnd(1:nqq,1) + c16*qnd(1:nqq,neighbor)
        xnorm         = xnorm_q(1)
        ynorm         = ynorm_q(1)
        znorm         = znorm_q(1)

        if (node1 <= nnodes0) then
          gradx(1:nqq,node1) = gradx(1:nqq,node1) + xnorm*qavg(1:nqq)
          gradz(1:nqq,node1) = gradz(1:nqq,node1) + znorm*qavg(1:nqq)
        end if

!       Find neighbor of node 2

        neighbor = 3
        if(abs(y(node1)-yplane_2d) < y_coplanar_tol) neighbor = 1
        if(abs(y(node4)-yplane_2d) < y_coplanar_tol) neighbor = 4

        qavg(1:nqq) = c56*qnd(1:nqq,2) + c16*qnd(1:nqq,neighbor)
        xnorm         = xnorm_q(2)
        znorm         = znorm_q(2)

        if (node2 <= nnodes0) then
          gradx(1:nqq,node2) = gradx(1:nqq,node2) + xnorm*qavg(1:nqq)
          gradz(1:nqq,node2) = gradz(1:nqq,node2) + znorm*qavg(1:nqq)
        end if

!       Find neighbor of node 3

        neighbor = 4
        if(abs(y(node1)-yplane_2d) < y_coplanar_tol) neighbor = 1
        if(abs(y(node2)-yplane_2d) < y_coplanar_tol) neighbor = 2

        qavg(1:nqq) = c56*qnd(1:nqq,3) + c16*qnd(1:nqq,neighbor)
        xnorm         = xnorm_q(3)
        znorm         = znorm_q(3)

        if (node3 <= nnodes0) then
          gradx(1:nqq,node3) = gradx(1:nqq,node3) + xnorm*qavg(1:nqq)
          gradz(1:nqq,node3) = gradz(1:nqq,node3) + znorm*qavg(1:nqq)
        end if

!       Find neighbor of node 4

        neighbor = 1
        if(abs(y(node2)-yplane_2d) < y_coplanar_tol) neighbor = 2
        if(abs(y(node3)-yplane_2d) < y_coplanar_tol) neighbor = 3

        qavg(1:nqq) = c56*qnd(1:nqq,4) + c16*qnd(1:nqq,neighbor)
        xnorm         = xnorm_q(4)
        znorm         = znorm_q(4)

        if (node4 <= nnodes0) then
          gradx(1:nqq,node4) = gradx(1:nqq,node4) + xnorm*qavg(1:nqq)
          gradz(1:nqq,node4) = gradz(1:nqq,node4) + znorm*qavg(1:nqq)
        end if

      end do loop_quads_1

    else twod_mode

!     close off gradient evaluation on triangular faces

      loop_tris_2 : do n = 1, nbfacet

        node1 = ibnode(f2ntb(n,1))
        node2 = ibnode(f2ntb(n,2))
        node3 = ibnode(f2ntb(n,3))

        qnd(1:n_vec,1) = qnode(1:n_vec,node1)
        qnd(1:n_vec,2) = qnode(1:n_vec,node2)
        qnd(1:n_vec,3) = qnode(1:n_vec,node3)

!       turb gradients if needed

        if ( turb_grad_flag > 0 ) then

            do j=1,n_turb
              qnd(n_tot+j,1) = turb(j,node1)
              qnd(n_tot+j,2) = turb(j,node2)
              qnd(n_tot+j,3) = turb(j,node3)
            end do

        end if

!       norm points away from grid interior
!       norm magnitude is 1/3 of surface triangle area
!       and is the same at all 3 nodes

        x1 = x(node1)
        x2 = x(node2)
        x3 = x(node3)

        y1 = y(node1)
        y2 = y(node2)
        y3 = y(node3)

        z1 = z(node1)
        z2 = z(node2)
        z3 = z(node3)

        ax = x2 - x1
        ay = y2 - y1
        az = z2 - z1

        bx = x3 - x1
        by = y3 - y1
        bz = z3 - z1

        xnorm = -my_haf*(ay*bz-az*by)/my_3
        ynorm =  my_haf*(ax*bz-az*bx)/my_3
        znorm = -my_haf*(ax*by-ay*bx)/my_3

        qavg(1:nqq) = c68*qnd(1:nqq,1) + c18*(qnd(1:nqq,2)+qnd(1:nqq,3))

        if (node1 <= nnodes0) then
          gradx(1:nqq,node1) = gradx(1:nqq,node1) + xnorm*qavg(1:nqq)
          grady(1:nqq,node1) = grady(1:nqq,node1) + ynorm*qavg(1:nqq)
          gradz(1:nqq,node1) = gradz(1:nqq,node1) + znorm*qavg(1:nqq)
        end if

        qavg(1:nqq) = c68*qnd(1:nqq,2) + c18*(qnd(1:nqq,1)+qnd(1:nqq,3))

        if (node2 <= nnodes0) then
          gradx(1:nqq,node2) = gradx(1:nqq,node2) + xnorm*qavg(1:nqq)
          grady(1:nqq,node2) = grady(1:nqq,node2) + ynorm*qavg(1:nqq)
          gradz(1:nqq,node2) = gradz(1:nqq,node2) + znorm*qavg(1:nqq)
        end if

        qavg(1:nqq) = c68*qnd(1:nqq,3) + c18*(qnd(1:nqq,1)+qnd(1:nqq,2))

        if (node3 <= nnodes0) then
          gradx(1:nqq,node3) = gradx(1:nqq,node3) + xnorm*qavg(1:nqq)
          grady(1:nqq,node3) = grady(1:nqq,node3) + ynorm*qavg(1:nqq)
          gradz(1:nqq,node3) = gradz(1:nqq,node3) + znorm*qavg(1:nqq)
        end if

      end do loop_tris_2

!     close off gradient evaluation on quadralateral faces

      loop_quads_2 : do n = 1, nbfaceq

        node1 = ibnode(f2nqb(n,1))
        node2 = ibnode(f2nqb(n,2))
        node3 = ibnode(f2nqb(n,3))
        node4 = ibnode(f2nqb(n,4))

        qnd(1:n_vec,1) = qnode(1:n_vec,node1)
        qnd(1:n_vec,2) = qnode(1:n_vec,node2)
        qnd(1:n_vec,3) = qnode(1:n_vec,node3)
        qnd(1:n_vec,4) = qnode(1:n_vec,node4)

!       turb gradients if needed

        if ( turb_grad_flag > 0 ) then

            do j=1,n_turb
              qnd(n_tot+j,1) = turb(j,node1)
              qnd(n_tot+j,2) = turb(j,node2)
              qnd(n_tot+j,3) = turb(j,node3)
              qnd(n_tot+j,4) = turb(j,node4)
            end do

        end if

!       get dual norm contributions at each node of the quad face

        call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,noninertial,&
                            xnorm_q,ynorm_q,znorm_q)

        qavg(1:nqq)   = qnd(1:nqq,1)
        xnorm         = xnorm_q(1)
        ynorm         = ynorm_q(1)
        znorm         = znorm_q(1)

        if (node1 <= nnodes0) then
          gradx(1:nqq,node1) = gradx(1:nqq,node1) + xnorm*qavg(1:nqq)
          grady(1:nqq,node1) = grady(1:nqq,node1) + ynorm*qavg(1:nqq)
          gradz(1:nqq,node1) = gradz(1:nqq,node1) + znorm*qavg(1:nqq)
        end if

        qavg(1:nqq)   = qnd(1:nqq,2)
        xnorm         = xnorm_q(2)
        ynorm         = ynorm_q(2)
        znorm         = znorm_q(2)

        if (node2 <= nnodes0) then
          gradx(1:nqq,node2) = gradx(1:nqq,node2) + xnorm*qavg(1:nqq)
          grady(1:nqq,node2) = grady(1:nqq,node2) + ynorm*qavg(1:nqq)
          gradz(1:nqq,node2) = gradz(1:nqq,node2) + znorm*qavg(1:nqq)
        end if

        qavg(1:nqq)   = qnd(1:nqq,3)
        xnorm         = xnorm_q(3)
        ynorm         = ynorm_q(3)
        znorm         = znorm_q(3)

        if (node3 <= nnodes0) then
          gradx(1:nqq,node3) = gradx(1:nqq,node3) + xnorm*qavg(1:nqq)
          grady(1:nqq,node3) = grady(1:nqq,node3) + ynorm*qavg(1:nqq)
          gradz(1:nqq,node3) = gradz(1:nqq,node3) + znorm*qavg(1:nqq)
        end if

        qavg(1:nqq)   = qnd(1:nqq,4)
        xnorm         = xnorm_q(4)
        ynorm         = ynorm_q(4)
        znorm         = znorm_q(4)

        if (node4 <= nnodes0) then
          gradx(1:nqq,node4) = gradx(1:nqq,node4) + xnorm*qavg(1:nqq)
          grady(1:nqq,node4) = grady(1:nqq,node4) + ynorm*qavg(1:nqq)
          gradz(1:nqq,node4) = gradz(1:nqq,node4) + znorm*qavg(1:nqq)
        end if

      end do loop_quads_2

    end if twod_mode

  end subroutine bc_green_gauss

!=============================== BC_GREEN_GAUSS2 =============================80
!
! This routine closes off the boundaries for the Green-Gauss gradients
! (node based variant)
!
!=============================================================================80
  subroutine bc_green_gauss2(turb_grad_flag,                                   &
                             nnodes0, nnodes01, qnode, gradx, grady, gradz,    &
                             nbnode, ibnode, bxn, byn, bzn, turb, n_turb,      &
                             n_tot, n_grd, n_vec, nqq)

    use info_depr, only : twod

    integer,                              intent(in)    :: turb_grad_flag
    integer,                              intent(in)    :: nbnode, n_turb
    integer,                              intent(in)    :: n_tot, n_grd
    integer,                              intent(in)    :: n_vec, nqq
    integer,                              intent(in)    :: nnodes0, nnodes01
    integer,  dimension(nbnode),          intent(in)    :: ibnode
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_grd,nnodes01),  intent(inout) :: gradx,grady,gradz
    real(dp), dimension(nbnode),          intent(in)    :: bxn, byn, bzn

    integer :: j, n, node

    real(dp), dimension(n_grd) :: qavg

  continue

    twod_mode : if (twod) then

      twod_bndry_loop : do n = 1, nbnode

        node = ibnode(n)

        qavg(1:n_vec) = qnode(1:n_vec,node)

!       turb gradients if needed

        if ( turb_grad_flag > 0 ) then

            do j=1,n_turb
              qavg(n_tot+j) = turb(j,node)
            end do

        end if

        if (node <= nnodes0) then
          gradx(1:nqq,node) = gradx(1:nqq,node) + bxn(n)*qavg(1:nqq)
          gradz(1:nqq,node) = gradz(1:nqq,node) + bzn(n)*qavg(1:nqq)
        end if

      end do twod_bndry_loop

    else

      threed_bndry_loop : do n = 1, nbnode

        node = ibnode(n)

        qavg(1:n_vec) = qnode(1:n_vec,node)

!       turb gradients if needed

        if ( turb_grad_flag > 0 ) then

            do j=1,n_turb
              qavg(n_tot+j) = turb(j,node)
            end do

        end if

        if (node <= nnodes0) then
          gradx(1:nqq,node) = gradx(1:nqq,node) + bxn(n)*qavg(1:nqq)
          grady(1:nqq,node) = grady(1:nqq,node) + byn(n)*qavg(1:nqq)
          gradz(1:nqq,node) = gradz(1:nqq,node) + bzn(n)*qavg(1:nqq)
        end if

      end do threed_bndry_loop

    end if twod_mode

  end subroutine bc_green_gauss2

!============================= PRESSURE_LIMITER_COEFF ========================80
!
! This routine computes the local pressure limiter coefficient as a function
! of the maximum Mach number that occurs in the nodes spanned by the higher
! order reconstruction stencil
!
!=============================================================================80
  subroutine pressure_limiter_coeff(n_tot, nnodes01, nnodes0, nedgeloc, eptr,  &
                                    qnode, pl_coeff, eqn_set)

    use kinddefs,        only : system_r8
    use fun3d_constants, only : my_0, my_half, my_1, pi
    use fluid,           only : gamma
    use info_depr,       only : pr_limiter_coeff
    use lmpi,            only : lmpi_max, lmpi_bcast
    use lmpi_app,        only : lmpi_maxnode, lmpi_xfer
    use generic_gas_map, only : n_momx, n_momy, n_momz, n_sonic_k
    use solution_types,  only : generic_gas

    integer,                             intent(in)  :: n_tot
    integer,                             intent(in)  :: eqn_set
    integer,                             intent(in)  :: nnodes01
    integer,                             intent(in)  :: nnodes0
    integer,                             intent(in)  :: nedgeloc
    integer,  dimension(2,nedgeloc),     intent(in)  :: eptr
    real(dp), dimension(n_tot,nnodes01), intent(in)  :: qnode
    real(dp), dimension(nedgeloc),       intent(out) :: pl_coeff

    integer  :: n1, n2, i

    real(dp) :: rho, u, v, w, q2, p
    real(dp) :: machn1, machn2, n1mach, n2mach, edge_mach
    real(dp) :: max_mach_p, max_mach_g, pl_coeff_min
    real(system_r8) :: real_max_mach_p, real_max_mach_g ! avoid complexification

    real(dp), dimension(nnodes01) :: max_stencil_mach

    real(dp), parameter :: upper_mach = 8.0_dp
    real(dp), parameter :: pl_coeff_max_l = my_half
    real(dp), parameter :: pl_coeff_max_g = pl_coeff_max_l

  continue

!   Loop over the nodes making up the higher order stencl used to reconstruct
!   the data at the interface on an edge

!   Initialize the level 1 values of max_stencil_mach to zero

    max_stencil_mach = my_0
    max_mach_p = my_0

!   Set the max Mach number to the local Mach number at each node

    do i = 1, nnodes0
      u   = qnode(n_momx,i)
      v   = qnode(n_momy,i)
      w   = qnode(n_momz,i)
      q2  = u*u + v*v + w*w
      if ( eqn_set == generic_gas ) then
        max_stencil_mach(i) = sqrt(q2)/qnode(n_sonic_k(1),i)
      else
        rho = qnode(1,i)
        p   = qnode(5,i)
        max_stencil_mach(i) = sqrt(q2*rho/(gamma*p))
      end if
      max_mach_p = max(max_mach_p, max_stencil_mach(i))
    end do

! Determine and communicate the global max Mach number

    real_max_mach_p = max_mach_p
    call lmpi_max(real_max_mach_p, real_max_mach_g)
    max_mach_g = real_max_mach_g
    call lmpi_bcast(max_mach_g)

! Determine the minumum allowable pressure limiter coefficient

    pl_coeff_min = max(my_0, pl_coeff_max_g*my_half*(my_1 +                   &
                       cos(pi*(my_1 + max_mach_g/upper_mach))))
    pl_coeff_min = pr_limiter_coeff*pl_coeff_min

!   Loop over the edges to find the max Mach number of the stencil nodes that
!   are on-processor

    do i = 1, nedgeloc

      n1 = eptr(1,i)
      n2 = eptr(2,i)

      machn1 = max_stencil_mach(n1)
      machn2 = max_stencil_mach(n2)

      u   = qnode(n_momx,n1)
      v   = qnode(n_momy,n1)
      w   = qnode(n_momz,n1)
      q2  = u*u + v*v + w*w

      if ( eqn_set == generic_gas ) then
        n1mach = sqrt(q2)/qnode(n_sonic_k(1),n1)
      else
        rho = qnode(1,n1)
        p   = qnode(5,n1)
        n1mach = sqrt(q2*rho/(gamma*p))
      end if

      u   = qnode(n_momx,n2)
      v   = qnode(n_momy,n2)
      w   = qnode(n_momz,n2)
      q2  = u*u + v*v + w*w

      if ( eqn_set == generic_gas ) then
        n2mach = sqrt(q2)/qnode(n_sonic_k(1),n2)
      else
        rho = qnode(1,n2)
        p   = qnode(5,n2)
        n2mach = sqrt(q2*rho/(gamma*p))
      end if

!     Find the max Mach numbers

      if ( n2 <= nnodes0 ) then
        if (machn1 > n2mach ) max_stencil_mach(n2) = n1mach
      end if
      if ( n1 <= nnodes0 ) then
        if ( machn2 > n1mach ) max_stencil_mach(n1) = n2mach
      end if

    end do

! Look across processor boundaries to find the max of the local node and any
! off processor nodes that connect to it

    call lmpi_maxnode(max_stencil_mach)

! Communicate the max Mach number to the ghost nodes on adjacent processors

    call lmpi_xfer(max_stencil_mach)

!   Compute the limiter scaling coefficient based on the max edge stencil
!   Mach number

    pl_coeff = my_0
    do i = 1, nedgeloc
      n1 = eptr(1,i)
      n2 = eptr(2,i)
      edge_mach = max(max_stencil_mach(n1), max_stencil_mach(n2))
      pl_coeff(i) = pl_coeff_min
      pl_coeff(i) = max(my_0, pl_coeff_max_l*my_half*(my_1 +                   &
                        cos(pi*(my_1 + edge_mach/upper_mach))))
      if (edge_mach >= upper_mach) pl_coeff(i) = pl_coeff_max_l
      pl_coeff(i) = max(pl_coeff_min, pl_coeff(i))
    end do

  end subroutine pressure_limiter_coeff

!==================================== LSTGS_TURB =============================80
!
! Calculates the gradients at the nodes using weighted least squares
!
! This subroutine solves using Gram-Schmidt
!
! Note: turbulence gradients in  last n_turb locations.
!       turbulence variables in first n_turb locations.
!
!=============================================================================80
  subroutine lstgs_turb(nnodes0,nnodes01,nedgeloc,eptr,symmetry,               &
                   gradx,grady,gradz,x,y,z,                                    &
                   r11,r12,r13,r22,r23,r33,                                    &
                   turb, n_turb, weighted )

    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: n_turb, nedgeloc

    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    integer,  dimension(nnodes01),        intent(in)  :: symmetry
    real(dp), dimension(:,:),             intent(in)  :: turb
    real(dp), dimension(nnodes01),        intent(in)  :: x,y,z
    real(dp), dimension(nnodes0),         intent(in)  :: r11,r12,r13
    real(dp), dimension(nnodes0),         intent(in)  :: r22,r23,r33
    real(dp), dimension(:,:),           intent(inout) :: gradx,grady,gradz
    logical,  optional,                   intent(in)  :: weighted

    integer :: j, n, node1, node2, nedge_eval, nq1, nq2, t

    real(dp)                   :: dx, dy, dz, weight
    real(dp), dimension(3)     :: terms
    real(dp), dimension(size(gradx,1)) :: dqq, contx, conty, contz

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

  continue

    nq2   = size(gradx,1)
    nq1   = nq2 - n_turb + 1

!   Set distance weighting factor

    weight_factor = my_0
    if (present( weighted )) then
      if(weighted) weight_factor = my_1
    endif

!   Zero out the gradients

    gradx(nq1:nq2,:) = my_0
    grady(nq1:nq2,:) = my_0
    gradz(nq1:nq2,:) = my_0

!   Initialize dqq

    dqq(:) = my_0

    nedge_eval = nedgeloc + nedge_ns

    scan_edges : do n = 1, nedge_eval

      if ( n <= nedgeloc ) then
        node1 = eptr(1,n)
        node2 = eptr(2,n)
      else
        node1 = eptr_ns(1,n-nedgeloc)
        node2 = eptr_ns(2,n-nedgeloc)
      endif

      dx = x(node2) - x(node1)
      dy = y(node2) - y(node1)
      dz = z(node2) - z(node1)

      weight = reconstruct_weight(dx,dy,dz, weight_factor, wls_inv_dist_scale)

      dx = weight*dx
      dy = weight*dy
      dz = weight*dz

      do j=nq1,nq2
        t = j - nq1 + 1
        dqq(j) = weight*( turb(t,node2) - turb(t,node1) )
      end do

      if (node1 <= nnodes0) then

        terms(:) = lstgs_func(dx,         dy,         dz,                    &
                              r11(node1), r12(node1), r13(node1),            &
                              r22(node1), r23(node1), r33(node1))
        contx(:) = terms(1)
        conty(:) = terms(2)
        contz(:) = terms(3)

        call lstgs_sym(symmetry(node1),                                      &
                       dx,         dy,         dz,                           &
                       r11(node1), r12(node1), r13(node1),                   &
                       r22(node1), r23(node1), r33(node1),                   &
                       nq2, contx(1:nq2), conty(1:nq2), contz(1:nq2) )

        gradx(nq1:nq2,node1) = gradx(nq1:nq2,node1) + &
                               contx(nq1:nq2)*dqq(nq1:nq2)
        grady(nq1:nq2,node1) = grady(nq1:nq2,node1) + &
                               conty(nq1:nq2)*dqq(nq1:nq2)
        gradz(nq1:nq2,node1) = gradz(nq1:nq2,node1) + &
                               contz(nq1:nq2)*dqq(nq1:nq2)

      end if

!     Now do the other node

      dqq(nq1:nq2) = -dqq(nq1:nq2)
      dx = -dx
      dy = -dy
      dz = -dz

      if (node2 <= nnodes0) then

        terms(:) = lstgs_func(dx,         dy,         dz,                    &
                              r11(node2), r12(node2), r13(node2),            &
                              r22(node2), r23(node2), r33(node2))
        contx(nq1:nq2) = terms(1)
        conty(nq1:nq2) = terms(2)
        contz(nq1:nq2) = terms(3)

        call lstgs_sym(symmetry(node2),                                      &
                       dx,         dy,         dz,                           &
                       r11(node2), r12(node2), r13(node2),                   &
                       r22(node2), r23(node2), r33(node2),                   &
                       nq2, contx(1:nq2), conty(1:nq2), contz(1:nq2) )

        gradx(nq1:nq2,node2) = gradx(nq1:nq2,node2) + &
                               contx(nq1:nq2)*dqq(nq1:nq2)
        grady(nq1:nq2,node2) = grady(nq1:nq2,node2) + &
                               conty(nq1:nq2)*dqq(nq1:nq2)
        gradz(nq1:nq2,node2) = gradz(nq1:nq2,node2) + &
                               contz(nq1:nq2)*dqq(nq1:nq2)

      end if

    end do scan_edges

  end subroutine lstgs_turb

!=================================== LSTGS_2D ================================80
!
! Calculates the gradients at the nodes using weighted least squares
!
! This subroutine solves using Gram-Schmidt
!
! Note: turbulence gradients in  last n_turb locations.
!       turbulence variables in first n_turb locations.
!
!=============================================================================80
  subroutine lstgs_2d_turb(                                                    &
                      nnodes0,nnodes01,nedgeloc,eptr,symmetry,                 &
                      gradx,grady,gradz,x,z,                                   &
                      r11,r12,r22,nedgeloc_2d,                                 &
                      nnodes0_2d,node_pairs_2d, turb,                          &
                      n_turb, weighted)

    use debug_defs,           only : symmetry_bcs

    integer,                              intent(in)  :: nnodes0
    integer,                              intent(in)  :: nnodes01
    integer,                              intent(in)  :: nedgeloc
    integer,                              intent(in)  :: n_turb
    integer,                              intent(in)  :: nedgeloc_2d
    integer,                              intent(in)  :: nnodes0_2d
    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    integer,  dimension(2,nnodes0_2d),    intent(in)  :: node_pairs_2d
    integer,  dimension(nnodes01),        intent(in)  :: symmetry
    real(dp), dimension(:,:),             intent(in)  :: turb
    real(dp), dimension(:,:),           intent(inout) :: gradx, grady, gradz
    real(dp), dimension(nnodes01),        intent(in)  :: x, z
    real(dp), dimension(nnodes0),         intent(in)  :: r11, r12, r22
    logical,  optional,                   intent(in)  :: weighted

    real(dp), dimension(size(gradx,1)) :: dqq
    real(dp), dimension(size(gradx,1)) :: contx, conty, contz

    real(dp) :: dx, dz

    real(dp) :: weight
    real(dp), dimension(3) :: term

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

    integer :: i, j, n, node1, node2, nedge_eval, nq1, nq2, t

  continue

    nq2 = size(gradx,1)
    nq1 = nq2 - n_turb + 1

! Set distance weighting factor

    weight_factor = my_0
    if (present( weighted )) then
      if(weighted) weight_factor = my_1
    endif

! Zero out the gradients (note: grady must be zeroed out as well)

    gradx(nq1:nq2,:) = my_0
    grady(nq1:nq2,:) = my_0
    gradz(nq1:nq2,:) = my_0

! Initialize dqq

    dqq(:) = my_0

    nedge_eval = nedgeloc_2d

    if (.not.symmetry_bcs) then

      do n = 1, nedge_eval

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        dx = x(node2) - x(node1)
        dz = z(node2) - z(node1)

        weight = reconstruct_weight(dx, my_0, dz,                          &
                                    weight_factor, wls_inv_dist_scale)

        dx = weight*dx
        dz = weight*dz

        do j=nq1,nq2
           t = j - nq1 + 1
          dqq(j) =  weight*( turb(t,node2) - turb(t,node1) )
        end do

        if (node1 <= nnodes0) then

          term(:) = lstgs_func(dx,         my_0, dz,         &
                               r11(node1), my_0, r12(node1), &
                               my_1,       my_0, r22(node1))
          gradx(nq1:nq2,node1) = gradx(nq1:nq2,node1) + term(1)*dqq(nq1:nq2)
          gradz(nq1:nq2,node1) = gradz(nq1:nq2,node1) + term(3)*dqq(nq1:nq2)

        end if

!       Now do the other node

        dqq(nq1:nq2) = -dqq(nq1:nq2)
        dx = -dx
        dz = -dz

        if (node2 <= nnodes0) then

          term(:) = lstgs_func(dx,         my_0, dz,         &
                               r11(node2), my_0, r12(node2), &
                               my_1,       my_0, r22(node2))
          gradx(nq1:nq2,node2) = gradx(nq1:nq2,node2) + term(1)*dqq(nq1:nq2)
          gradz(nq1:nq2,node2) = gradz(nq1:nq2,node2) + term(3)*dqq(nq1:nq2)

        end if

      end do

    else

      scan_edges : do n = 1, nedge_eval

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        dx = x(node2) - x(node1)
        dz = z(node2) - z(node1)

        weight = reconstruct_weight(dx, my_0, dz,                            &
                                    weight_factor, wls_inv_dist_scale)

        dx = weight*dx
        dz = weight*dz

        do j=nq1,nq2
          t = j - nq1 + 1
          dqq(j) = weight*( turb(t,node2) - turb(t,node1) )
        end do

        if (node1 <= nnodes0) then
          term(:) = lstgs_func(dx,         my_0, dz,         &
                               r11(node1), my_0, r12(node1), &
                               my_1,       my_0, r22(node1))

          contx(:) = term(1)
          conty(:) = term(2)
          contz(:) = term(3)

          call lstgs_sym(symmetry(node1),                                    &
                         dx,         my_0, dz,                               &
                         r11(node1), my_0, r12(node1),                       &
                         my_1,       my_0, r22(node1),                       &
                         nq2, contx(1:nq2), conty(1:nq2), contz(1:nq2) )

          gradx(nq1:nq2,node1) = gradx(nq1:nq2,node1) + &
                                 contx(nq1:nq2)*dqq(nq1:nq2)
          gradz(nq1:nq2,node1) = gradz(nq1:nq2,node1) + &
                                 contz(nq1:nq2)*dqq(nq1:nq2)
        end if

!       Now do the other node

        dqq(nq1:nq2) = -dqq(nq1:nq2)
        dx = -dx
        dz = -dz

        if (node2 <= nnodes0) then

          term(:) = lstgs_func(dx,         my_0, dz,         &
                               r11(node2), my_0, r12(node2), &
                               my_1,       my_0, r22(node2))

          contx(:) = term(1)
          conty(:) = term(2)
          contz(:) = term(3)

          call lstgs_sym(symmetry(node2),                                    &
                         dx,         my_0, dz,                               &
                         r11(node2), my_0, r12(node2),                       &
                         my_1,       my_0, r22(node2),                       &
                         nq2, contx(1:nq2), conty(1:nq2), contz(1:nq2) )

          gradx(nq1:nq2,node2) = gradx(nq1:nq2,node2) + &
                                 contx(nq1:nq2)*dqq(nq1:nq2)
          gradz(nq1:nq2,node2) = gradz(nq1:nq2,node2) + &
                                 contz(nq1:nq2)*dqq(nq1:nq2)

        end if

      end do scan_edges

    endif

!     Copy gradients to opposite plane

    do i = 1,nnodes0_2d

      node1 = node_pairs_2d(1,i)
      node2 = node_pairs_2d(2,i)

      gradx(nq1:nq2,node2) = gradx(nq1:nq2,node1)
      gradz(nq1:nq2,node2) = gradz(nq1:nq2,node1)

    end do

  end subroutine lstgs_2d_turb


!   Statements to include the functions that are to be inlined.
!   This is necessary because not all compilers can inline
!   functions that are in a different module.
!   N.B.: The order of the statements must reflect
!         how they are nested in the routine(s)
!         they are invoked from.

  subroutine do_not_delete_here_for_r_vec
    use lsq_types, only : lsq_ref_type
    type(lsq_ref_type) :: dummy
     continue
     if (.false.) then
       dummy%tr(1,1)=0.0_dp
       write(*,*) 'lsq_ref_type',dummy%tr(1,1)
     endif
  end subroutine

  include 'viscosity_law.f90'
  include 'minmods.f90'
  include 'vlflxls.f90'
  include 'vaflxls.f90'
  include 'smthlms.f90'
  include 'vkflxls.f90'
  include 'pressure_limiter.f90'
  include 'pswitch.f90'
  include 'vswch_coef.f90'
  include 'vswch_coef_orig.f90'
  include 'qf.f90'
  include 'dq.f90'
  include 'dqumuscl.f90'
  include 'minmodv.f90'
  include 'vlflxlv.f90'
  include 'vaflxlv.f90'
  include 'smthlmv.f90'
  include 'r_vec.f90'
  include 'lsq_map_ref.f90'
  include 'lsq_coords.f90'
  include 'tinverse.f90'
  include 'mapping_system.f90'
  include 'mapping_coords.f90'
  include 'coords_cylindrical_polar.f90'
  include 'reconstruct_weight.f90'
  include 'lstgs_func.f90'

end module reconstruction

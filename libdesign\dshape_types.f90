module dshape_types

  use kinddefs, only : dp

  implicit none

  private

  public :: node_stencil, node_stencil_type

  type node_stencil_type
    integer :: n

    integer, dimension(:), pointer :: nodes

    real(dp), dimension(:),   pointer :: dxndx,dxndy,dxndz
    real(dp), dimension(:),   pointer :: dyndx,dyndy,dyndz
    real(dp), dimension(:),   pointer :: dzndx,dzndy,dzndz
    real(dp), dimension(:,:), pointer :: dwdx,dwdy,dwdz
    real(dp), dimension(:,:), pointer :: dwhatdx,dwhatdy,dwhatdz
  end type node_stencil_type

  type(node_stencil_type), dimension(:), allocatable :: node_stencil

end module dshape_types

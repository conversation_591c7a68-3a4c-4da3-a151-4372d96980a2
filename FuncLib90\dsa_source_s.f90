!================================= DSA_SOURCE_S ==============================80
!
! Jacobians wrt gradient of S-A source term input from velocity field.
!
!=============================================================================80

  pure function dsa_source_s( ux, uy, uz, vx, vy, vz, wx, wy, wz )
                            !  1   2   3   4   5   6   7   8   9

    use turb_sa_const,   only : dacles_mariani

    real(dp), intent(in) :: ux, uy, uz, vx, vy, vz, wx, wy, wz

    real(dp), dimension(9) :: dsa_source_s

    real(dp) :: s, sij_mag, term

  continue

    s  = sqrt( (wy-vz)**2 + (uz-wx)**2 + (vx-uy)**2 )

    dsa_source_s(:) = 0._dp

    if ( s <= 1.0e-8_dp ) then

      s = 1.0e-8_dp

    else

      term = 1._dp/s
      dsa_source_s(8) = term*(wy-vz)
      dsa_source_s(6) =-term*(wy-vz)
      dsa_source_s(3) = term*(uz-wx)
      dsa_source_s(7) =-term*(uz-wx)
      dsa_source_s(4) = term*(vx-uy)
      dsa_source_s(2) =-term*(vx-uy)

    endif

    if ( dacles_mariani ) then

      sij_mag = sqrt( (wy+vz)**2 + (uz+wx)**2 + (vx+uy)**2 &
                    + 2._dp*(ux**2+vy**2+wz**2) )

      if ( sij_mag-s < -1.0e-8_dp ) then

!       s = s + 2._dp*min( -1.0e-8_dp, sij_mag-s )

        term = 2._dp/sij_mag
        dsa_source_s(8) = -dsa_source_s(8) + term*(wy+vz)
        dsa_source_s(6) = -dsa_source_s(6) + term*(wy+vz)
        dsa_source_s(3) = -dsa_source_s(3) + term*(uz+wx)
        dsa_source_s(7) = -dsa_source_s(7) + term*(uz+wx)
        dsa_source_s(4) = -dsa_source_s(4) + term*(vx+uy)
        dsa_source_s(2) = -dsa_source_s(2) + term*(vx+uy)

        dsa_source_s(1) = -dsa_source_s(1) + 2._dp*term*(ux)
        dsa_source_s(5) = -dsa_source_s(5) + 2._dp*term*(vy)
        dsa_source_s(9) = -dsa_source_s(9) + 2._dp*term*(wz)
      endif

    end if

  end function dsa_source_s


module timestep_gen

  use kinddefs, only : dp

  implicit none

  private
  public :: deltat2_gen

contains

!================================ DELTAT2_GEN  ===============================80
!
! Calculate a time step for each cell
! Note that this routine assumes conservative variables
!
!=============================================================================80

  subroutine deltat2_gen( nnodes0, nnodes01, nedgeloc, qnode, cdt, vol,        &
                          xn, yn, zn, ra, eptr, nbound, bc,                    &
                          nedgeloc_2d, nnodes0_2d, node_pairs_2d, n_tot )

    use info_depr,            only : twod
    use nml_nonlinear_solves, only : use_local_dt
    use generic_gas_map,      only : n_density, n_species, n_sonic_k
    use bc_types,             only : bcgrid_type
    use bc_names,             only : symmetry_x, symmetry_y, symmetry_z,       &
                                     bc_ignore_2d

    integer,                              intent(in)  :: nbound, n_tot
    integer,                              intent(in)  :: nedgeloc
    integer,                              intent(in)  :: nedgeloc_2d
    integer,                              intent(in)  :: nnodes0_2d
    integer,                              intent(in)  :: nnodes0
    integer,                              intent(in)  :: nnodes01
    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    integer,  dimension(2,nnodes0_2d),    intent(in)  :: node_pairs_2d
    real(dp), dimension(nnodes0),         intent(out) :: cdt
    real(dp), dimension(nnodes01),        intent(in)  :: vol
    real(dp), dimension(nedgeloc),        intent(in)  :: ra, xn, yn, zn
    real(dp), dimension(n_tot,nnodes01),  intent(in)  :: qnode
    type(bcgrid_type), dimension(nbound), intent(in)  :: bc

    integer :: i, ib, n, node1, node2, nedge_dt_eval

    real(dp) :: area, c1, c2, rho1, rho2, term, u1, u2, v1, v2, w1, w2
    real(dp) :: xnorm, ynorm, znorm

    real(dp), parameter :: my_1 = 1.0
    real(dp), parameter :: my_0 = 0.0

    continue

!   Set number of edges to evaluate interior time step data

    if (twod) then
      nedge_dt_eval = nedgeloc_2d
    else
      nedge_dt_eval = nedgeloc
    end if

!   If local time steping, loop over faces
!   and calculate time step as cdt = V/(sum(|u.n| +c.area)
!   This is time step for cfl=1. We will multiply by cfl number later

!   First loop over nodes and zero out cdt

    local_dt: if (use_local_dt) then

      do i = 1,nnodes0
        cdt(i) = my_0
      end do

      edge_loop: do n = 1,nedge_dt_eval

        node1 = eptr(1,n)
        node2 = eptr(2,n)

!       get normal to face

        xnorm = xn(n)
        ynorm = yn(n)
        znorm = zn(n)
        area  = ra(n)

        xnorm = xnorm*area
        ynorm = ynorm*area
        znorm = znorm*area

!       time step based on momenta set 1

        rho1 = qnode(n_density,node1)
        u1   = qnode(n_species+1,node1)/rho1
        v1   = qnode(n_species+2,node1)/rho1
        w1   = qnode(n_species+3,node1)/rho1
        c1   = qnode(n_sonic_k(1),node1)

        rho2 = qnode(n_density,node2)
        u2   = qnode(n_species+1,node2)/rho2
        v2   = qnode(n_species+2,node2)/rho2
        w2   = qnode(n_species+3,node2)/rho2
        c2   = qnode(n_sonic_k(1),node2)

!       get average values on face

        term = max((abs(u1*xnorm + v1*ynorm + w1*znorm) + c1*area),            &
                   (abs(u2*xnorm + v2*ynorm + w2*znorm) + c2*area))
        if (node1 <= nnodes0) cdt(node1) = cdt(node1) + term
        if (node2 <= nnodes0) cdt(node2) = cdt(node2) + term

      end do edge_loop

      boundary_loop : do ib = 1, nbound
        if (twod .and. bc_ignore_2d(bc(ib)%ibc)) cycle boundary_loop
        if((bc(ib)%ibc == symmetry_x) .or.               &
           (bc(ib)%ibc == symmetry_y) .or.               &
           (bc(ib)%ibc == symmetry_z)) cycle boundary_loop

        call bc_deltat2_gen(nnodes0, nnodes01, bc(ib)%nbnode, bc(ib)%ibnode,   &
                            qnode, cdt, bc(ib)%bxn, bc(ib)%byn, bc(ib)%bzn,    &
                            n_tot)
      end do boundary_loop

!     Now cdt has sum(|u.n| + c*area)

      if (twod) then

        do n = 1,nnodes0_2d
          node1 = node_pairs_2d(1,n)
          node2 = node_pairs_2d(2,n)
          cdt(node1) = vol(node1)/cdt(node1)
          cdt(node2) = cdt(node1)
        end do

      else

        do i = 1,nnodes0
          cdt(i) = vol(i)/cdt(i)
        end do

      end if

    else local_dt

!     If not doing local time stepping just set cdt=1

      do i = 1,nnodes0
        cdt(i) = my_1
      end do

    end if local_dt

  end subroutine deltat2_gen

!================================ BC_DELTAT2_GEN =============================80
!
! Computes boundary contribution to dt (node based)
!
!=============================================================================80

  subroutine bc_deltat2_gen( nnodes0, nnodes01, nbnode, ibnode, qnode, cdt,    &
                             bxn, byn, bzn, n_tot )

    use generic_gas_map, only : n_momx, n_momy, n_momz, n_density,  n_sonic_k

    integer,                             intent(in)    :: nbnode, nnodes0, n_tot
    integer,                             intent(in)    :: nnodes01
    integer,  dimension(nbnode),         intent(in)    :: ibnode
    real(dp), dimension(nbnode),         intent(in)    :: bxn, byn, bzn
    real(dp), dimension(nnodes0),        intent(inout) :: cdt
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode

    integer :: i, inode

    real(dp) :: vn, area, c, rho, u, v, w, xnorm, ynorm, znorm

    continue

    do i = 1,nbnode
      inode = ibnode(i)

      if (inode <= nnodes0) then
        rho = qnode(n_density, inode)
        u   = qnode(n_momx, inode)/rho
        v   = qnode(n_momy, inode)/rho
        w   = qnode(n_momz, inode)/rho
        c   = qnode(n_sonic_k(1), inode)

!       Get the normal

        xnorm = bxn(i)
        ynorm = byn(i)
        znorm = bzn(i)
        area  = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)

        vn = abs(xnorm*u + ynorm*v + znorm*w) + c*area
        cdt(inode) = cdt(inode) + vn
      end if

    end do

  end subroutine bc_deltat2_gen

end module timestep_gen
